"""
Base AI Provider - Abstract interface for all AI providers
Ensures consistent behavior across Google, Anthropic, OpenAI, Ollama, etc.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging
import asyncio


@dataclass
class AIResponse:
    """Standardized AI response format across all providers"""
    model: str
    content: str
    confidence: float = 0.0
    reasoning: str = ""

    # Token usage
    input_tokens: int = 0
    output_tokens: int = 0
    total_tokens: int = 0

    # Metadata
    provider: str = ""
    request_id: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    latency_ms: float = 0.0

    # Raw response for debugging
    raw_response: Dict[str, Any] = field(default_factory=dict)

    # Error handling
    error: Optional[str] = None
    success: bool = True

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'model': self.model,
            'content': self.content,
            'confidence': self.confidence,
            'reasoning': self.reasoning,
            'input_tokens': self.input_tokens,
            'output_tokens': self.output_tokens,
            'total_tokens': self.total_tokens,
            'provider': self.provider,
            'request_id': self.request_id,
            'created_at': self.created_at.isoformat(),
            'latency_ms': self.latency_ms,
            'error': self.error,
            'success': self.success
        }


class BaseAIProvider(ABC):
    """
    Abstract base class for all AI providers

    All providers (Google, Anthropic, OpenAI, Ollama) must implement this interface
    """

    def __init__(
        self,
        credential_manager,
        cost_tracker=None,
        logger: logging.Logger = None
    ):
        """
        Initialize provider

        Args:
            credential_manager: CredentialManager instance
            cost_tracker: CostTracker instance (optional)
            logger: Logger instance
        """
        self.credential_manager = credential_manager
        self.cost_tracker = cost_tracker
        self.logger = logger or logging.getLogger(__name__)

        self.provider_name = self._get_provider_name()

    @abstractmethod
    def _get_provider_name(self) -> str:
        """Return provider name (e.g., 'google', 'anthropic')"""
        pass

    @abstractmethod
    async def call(
        self,
        model: str,
        prompt: str,
        temperature: float = 0.1,
        max_tokens: int = 8192,
        **kwargs
    ) -> AIResponse:
        """
        Call AI model with prompt

        Args:
            model: Model name (e.g., 'gemini-2.5-pro')
            prompt: The prompt text
            temperature: Temperature (0.0-1.0)
            max_tokens: Maximum tokens to generate
            **kwargs: Provider-specific parameters

        Returns:
            AIResponse with standardized format

        Raises:
            Exception: If call fails
        """
        pass

    async def call_with_retry(
        self,
        model: str,
        prompt: str,
        retries: int = 3,
        **kwargs
    ) -> AIResponse:
        """
        Call with automatic retry on failure

        Args:
            model: Model name
            prompt: The prompt
            retries: Number of retries
            **kwargs: Additional parameters

        Returns:
            AIResponse

        Raises:
            Exception: If all retries fail
        """
        last_error = None

        for attempt in range(retries):
            try:
                self.logger.info(f"Calling {self.provider_name}/{model} (attempt {attempt + 1}/{retries})")

                response = await self.call(model, prompt, **kwargs)

                if response.success:
                    return response
                else:
                    last_error = response.error
                    self.logger.warning(f"Call failed: {last_error}")

            except Exception as e:
                last_error = str(e)
                self.logger.error(f"Call error: {e}")

                # Check if it's a rate limit error
                if self._is_rate_limit_error(e):
                    self.logger.warning(f"Rate limit hit, rotating key")
                    self.credential_manager.rotate_key(self.provider_name)

                # Wait before retry (exponential backoff)
                if attempt < retries - 1:
                    wait_time = 2 ** attempt  # 1s, 2s, 4s, etc.
                    self.logger.info(f"Waiting {wait_time}s before retry...")
                    await asyncio.sleep(wait_time)

        # All retries failed
        raise Exception(f"All {retries} attempts failed. Last error: {last_error}")

    @abstractmethod
    def _is_rate_limit_error(self, error: Exception) -> bool:
        """
        Check if error is a rate limit error

        Args:
            error: Exception to check

        Returns:
            True if rate limit error
        """
        pass

    @abstractmethod
    def parse_response(self, raw_response: Any) -> AIResponse:
        """
        Parse provider-specific response into standardized format

        Args:
            raw_response: Raw response from provider API

        Returns:
            Standardized AIResponse
        """
        pass

    def _track_usage(
        self,
        model: str,
        input_tokens: int,
        output_tokens: int,
        task: str = "general"
    ):
        """
        Track token usage and cost

        Args:
            model: Model name
            input_tokens: Input tokens used
            output_tokens: Output tokens generated
            task: Task type (for categorization)
        """
        if self.cost_tracker:
            self.cost_tracker.record_usage(
                model=model,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                task=task
            )

    def _check_budget(self, model: str, estimated_tokens: int) -> bool:
        """
        Check if request would exceed budget

        Args:
            model: Model name
            estimated_tokens: Estimated tokens for request

        Returns:
            True if within budget, False otherwise
        """
        if self.cost_tracker:
            return self.cost_tracker.check_budget(model, estimated_tokens)
        return True  # No tracker, allow all

    def validate_connection(self) -> bool:
        """
        Validate that provider is accessible

        Returns:
            True if can connect, False otherwise
        """
        try:
            # Check credentials exist
            if not self.credential_manager.has_credentials(self.provider_name):
                self.logger.error(f"No credentials for {self.provider_name}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Connection validation failed: {e}")
            return False

    def get_credential(self) -> Optional[str]:
        """Get API credential for this provider"""
        return self.credential_manager.get_credential(self.provider_name)

    def get_endpoint(self) -> Optional[str]:
        """Get API endpoint for this provider"""
        return self.credential_manager.get_endpoint(self.provider_name)

    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(provider={self.provider_name})"


class ProviderFactory:
    """
    Factory for creating provider instances

    Usage:
        factory = ProviderFactory(credential_manager, cost_tracker)
        provider = factory.create_provider('google')
        response = await provider.call('gemini-2.5-pro', 'Hello')
    """

    def __init__(self, credential_manager, cost_tracker=None, logger: logging.Logger = None):
        """Initialize factory"""
        self.credential_manager = credential_manager
        self.cost_tracker = cost_tracker
        self.logger = logger or logging.getLogger(__name__)

        self._providers: Dict[str, BaseAIProvider] = {}

    def create_provider(self, provider_name: str) -> BaseAIProvider:
        """
        Create or get cached provider instance

        Args:
            provider_name: Provider name (google, anthropic, openai, ollama)

        Returns:
            Provider instance

        Raises:
            ValueError: If provider not supported
        """
        # Return cached if exists
        if provider_name in self._providers:
            return self._providers[provider_name]

        # Import and create provider
        if provider_name == 'google':
            from .google_provider import GoogleProvider
            provider = GoogleProvider(
                self.credential_manager,
                self.cost_tracker,
                self.logger
            )
        elif provider_name == 'anthropic':
            from .anthropic_provider import AnthropicProvider
            provider = AnthropicProvider(
                self.credential_manager,
                self.cost_tracker,
                self.logger
            )
        elif provider_name == 'openai':
            from .openai_provider import OpenAIProvider
            provider = OpenAIProvider(
                self.credential_manager,
                self.cost_tracker,
                self.logger
            )
        elif provider_name == 'ollama':
            from .ollama_provider import OllamaProvider
            provider = OllamaProvider(
                self.credential_manager,
                self.cost_tracker,
                self.logger
            )
        else:
            raise ValueError(f"Unsupported provider: {provider_name}")

        # Cache and return
        self._providers[provider_name] = provider
        return provider

    def get_provider_for_model(self, model_config) -> BaseAIProvider:
        """
        Get provider instance for a model config

        Args:
            model_config: ModelConfig instance

        Returns:
            Provider instance
        """
        return self.create_provider(model_config.provider)
