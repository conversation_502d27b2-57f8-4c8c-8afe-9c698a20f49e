# Database Connection Crisis - SIEMLess v2.0

**Date**: October 4, 2025
**Status**: 🔴 CRITICAL - All Backend Engines Non-Functional
**Impact**: Frontend operational, but all backend APIs returning 500 errors

---

## Executive Summary

The SIEMLess v2.0 backend is experiencing **complete database connection pool exhaustion** across all 5 engines. The root cause is an **architectural mismatch** between async web framework (aiohttp) and synchronous database driver (psycopg2), causing the event loop to block and connections to leak.

**Current State:**
- ✅ Frontend: Production-ready, all issues resolved
- ❌ Backend: All 5 engines showing `"database": "error: connection already closed"`
- ❌ APIs: Detection fidelity, CTI status, and all database-dependent endpoints returning 500 errors

**Critical Error Pattern:**
```json
{
  "engine": "backend",
  "status": "unhealthy",
  "database": "error: connection already closed",
  "uptime": "1:54:02"
}
```

This has been ongoing for **nearly 2 hours** with no recovery.

---

## The Technical Problem

### Architecture Mismatch

**What We Have:**
```python
# Web Framework: aiohttp (async)
async def handle_request(request):
    # Database Driver: psycopg2 (synchronous)
    cursor = self.db_connection.cursor()
    await cursor.execute("SELECT...")  # ❌ FATAL ERROR
    result = await cursor.fetchall()
```

**Why It Fails:**

1. **psycopg2 is synchronous** - `cursor.execute()` is NOT awaitable
   - Attempting `await` on it causes: `TypeError: object is not awaitable`

2. **Event Loop Blocking**
   - Even without `await`, synchronous DB calls **block the entire event loop**
   - All concurrent requests freeze while waiting for database
   - PostgreSQL connections never properly close

3. **Connection Pool Exhaustion**
   - PostgreSQL default: 100 max connections
   - Leaked connections accumulate over ~2 hours
   - System hits limit: "connection already closed" errors cascade

### Code Evidence

**Example from backend_engine.py** (pattern repeated ~50+ times):
```python
async def _handle_fidelity_assessment(self, request):
    cursor = self.db_connection.cursor()
    await cursor.execute("""  # ❌ psycopg2 cursors are not awaitable
        SELECT * FROM detection_rules WHERE quality_score > %s
    """, (0.7,))
    result = await cursor.fetchall()  # ❌ Another non-awaitable
    # Cursor never closed → connection leaked
```

**Affected Files** (at minimum):
- `engines/backend/cti_rule_generator.py`
- `engines/backend/mitre_attack_mapper.py`
- `engines/delivery/delivery_engine.py`
- `engines/delivery/investigation_context_generator.py`
- `engines/intelligence/intelligence_engine.py`
- `engines/contextualization/contextualization_engine.py`
- `engines/ingestion/ingestion_engine.py`
- And potentially 40+ more files

### Current Health Status

```bash
# All engines showing database failure:
curl http://localhost:8001/health  # Intelligence: "connection already closed"
curl http://localhost:8002/health  # Backend: "connection already closed"
curl http://localhost:8003/health  # Ingestion: "connection already closed"
curl http://localhost:8004/health  # Contextualization: "connection already closed"
curl http://localhost:8005/health  # Delivery: "connection already closed"
```

Only **Redis connections are healthy** - the database is completely inaccessible.

---

## Research Findings

### Industry Best Practices (from Stack Overflow, psycopg docs)

**Consensus:** "Do NOT mix aiohttp (async) with psycopg2 (sync)"

> "Calls to the database will block the event loop entirely, affecting all other parallel tasks."
> — Stack Overflow: "Am I using aiohttp together with psycopg2 correctly?"

> "psycopg2 does not support async and await"
> — psycopg official documentation

### Recommended Solutions (Industry Standard)

**Option 1: aiopg** ⭐ (Most Recommended)
- Async wrapper specifically for psycopg2
- Minimal code changes
- Connection pooling built-in
- Properly yields to event loop

```python
import aiopg

# Initialization (once)
pool = await aiopg.create_pool(
    host='localhost',
    database='siemless_v2',
    user='siemless',
    password='siemless123'
)

# Usage (in async handlers)
async def handler():
    async with pool.acquire() as conn:
        async with conn.cursor() as cur:
            await cur.execute("SELECT * FROM table WHERE id = %s", (rule_id,))
            result = await cur.fetchall()
            # Cursor auto-closes, connection returns to pool ✅
```

**Pros:**
- ✅ Designed for this exact use case
- ✅ Maintains psycopg2 API familiarity
- ✅ Connection pooling handles resource management
- ✅ Event loop never blocks
- ✅ ~100-200 lines of change per engine

**Cons:**
- Requires dependency: `pip install aiopg`
- Need to refactor connection initialization

---

**Option 2: asyncpg** (Fastest Performance)
- Native async PostgreSQL driver
- Different API (more refactoring needed)
- Industry standard for high-performance async Python

```python
import asyncpg

# Initialization
pool = await asyncpg.create_pool(
    host='localhost',
    database='siemless_v2',
    user='siemless',
    password='siemless123'
)

# Usage (different API)
async def handler():
    async with pool.acquire() as conn:
        result = await conn.fetch(
            "SELECT * FROM table WHERE id = $1",  # Note: $1 not %s
            rule_id
        )
        # Returns list of Record objects
```

**Pros:**
- ✅ Fastest PostgreSQL driver in Python ecosystem
- ✅ Native async (not a wrapper)
- ✅ Better performance under load
- ✅ Connection pooling built-in

**Cons:**
- Different API syntax (more refactoring)
- Placeholder syntax: `$1, $2` instead of `%s, %s`
- Returns `Record` objects instead of tuples
- ~300-500 lines of change per engine

---

**Option 3: psycopg2 + ThreadPoolExecutor** (Not Recommended)
- Keep psycopg2, run DB calls in thread pool
- Doesn't block event loop
- Complex and less performant

```python
from concurrent.futures import ThreadPoolExecutor

executor = ThreadPoolExecutor(max_workers=10)

async def handler():
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(
        executor,
        self._sync_db_query,  # Regular sync function
        query,
        params
    )
```

**Pros:**
- ✅ Keep existing psycopg2 code mostly unchanged
- ✅ Doesn't block event loop

**Cons:**
- ❌ Thread overhead (context switching)
- ❌ Doesn't solve connection leaks
- ❌ More complex error handling
- ❌ Not industry standard for this pattern
- ❌ Worse performance than native async

---

**Option 4: Remove all `await` keywords** (Emergency Workaround Only)
- Strip `await` from all database calls
- Accept that event loop will block
- Quick fix, terrible long-term

```python
# Just remove await
cursor = self.db_connection.cursor()
cursor.execute("SELECT...")  # Blocks event loop
result = cursor.fetchall()
cursor.close()  # At least close cursor
```

**Pros:**
- ✅ Fastest to implement (~1 hour work)
- ✅ Might work for low concurrency

**Cons:**
- ❌ Event loop still blocks
- ❌ Poor performance under load
- ❌ Not a real fix
- ❌ Will need proper fix eventually

---

## Impact Analysis

### What's Broken
1. **All Database-Dependent APIs** (95% of backend functionality)
   - Detection fidelity assessment: 500 error
   - CTI status and statistics: Unknown (likely 500)
   - Graph queries: Unknown (likely 500)
   - Correlation engine: Unknown (likely 500)
   - Case management: Unknown (likely 500)

2. **Engine Health Checks**
   - All 5 engines reporting unhealthy
   - Docker health checks failing
   - Monitoring dashboards showing red

3. **Data Pipeline**
   - Unable to process new logs (requires DB writes)
   - Unable to generate new rules (requires DB reads/writes)
   - Unable to enrich alerts (requires DB lookups)

### What Still Works
1. **Frontend** ✅
   - Fully operational
   - All static content serving
   - Client-side routing
   - UI components rendering

2. **Redis** ✅
   - Pub/sub messaging
   - Caching layer
   - Inter-engine communication (non-DB)

3. **Basic Health Endpoints** ✅
   - HTTP servers responding
   - JSON error messages returning properly

### Time to Impact
- **T+0 minutes**: First database operation after engine start - works fine
- **T+30 minutes**: Connection pool starts filling up
- **T+90 minutes**: Pool nearing capacity, sporadic failures
- **T+120 minutes**: Complete exhaustion, all DB operations fail
- **Current**: T+114 minutes (nearly critical threshold)

---

## Historical Context

### Previous "Fix" (October 2025)
According to `CLAUDE.md` lines 323-447, this issue was supposedly fixed:

> **✅ Database Connection Exhaustion Fix (October 2025)**: Resolved critical asyncpg/psycopg2 mismatch
> - Fixed "connection already closed" errors across all engines
> - Replaced asyncpg syntax with proper psycopg2 cursor patterns
> - Added db helper functions ensuring cursors always close (prevents connection leaks)
> - Fixed 20+ database operations across 5 files

**Reality:** The fix was incomplete or reverted
- Code still contains `await cursor.execute()` patterns
- Helper functions may exist but aren't being used
- Cursors still not being closed properly
- Same error persists

### Why It Recurred
1. **Partial fix**: Only fixed some files, not all
2. **Code regression**: New code added without following patterns
3. **Pattern not enforced**: No linting/CI to catch violations
4. **Documentation not followed**: Helpers defined but not used

---

## Recommended Action Plan

### Option A: Quick Fix (aiopg) - RECOMMENDED ⭐

**Timeline**: 4-6 hours work

**Steps:**
1. Install dependency: `pip install aiopg`
2. Add to requirements.txt in all engine directories
3. Modify `base_engine.py`:
   ```python
   import aiopg

   async def _setup_database(self):
       self.db_pool = await aiopg.create_pool(
           host=os.getenv('DB_HOST', 'postgres'),
           database=os.getenv('POSTGRES_DB', 'siemless_v2'),
           user=os.getenv('POSTGRES_USER', 'siemless'),
           password=os.getenv('POSTGRES_PASSWORD', 'siemless123'),
           minsize=5,
           maxsize=20
       )
   ```
4. Refactor database calls in each engine:
   ```python
   # Old (broken):
   cursor = self.db_connection.cursor()
   await cursor.execute("SELECT...")

   # New (working):
   async with self.db_pool.acquire() as conn:
       async with conn.cursor() as cur:
           await cur.execute("SELECT...")
           result = await cur.fetchall()
   ```
5. Test each engine individually
6. Deploy and monitor

**Affected Files** (minimum 7 core files):
- `engines/base_engine.py`
- `engines/backend/backend_engine.py`
- `engines/backend/cti_rule_generator.py`
- `engines/delivery/delivery_engine.py`
- `engines/delivery/investigation_context_generator.py`
- `engines/intelligence/intelligence_engine.py`
- `engines/contextualization/contextualization_engine.py`

**Risk**: Low - aiopg designed for this exact migration

---

### Option B: High Performance (asyncpg)

**Timeline**: 8-12 hours work

**Why Consider:**
- 3-5x faster than psycopg2/aiopg under load
- Native async (not a wrapper)
- Better connection pooling
- Industry standard for async Python + PostgreSQL

**Extra Work:**
- Rewrite all SQL to use `$1, $2` instead of `%s, %s`
- Handle `Record` objects instead of tuples
- Update all `cursor.fetchall()` to `conn.fetch()`
- More extensive testing needed

**Risk**: Medium - API differences could introduce bugs

---

### Option C: Emergency Workaround (Remove await)

**Timeline**: 1-2 hours

**Implementation:**
1. Find all: `await cursor.execute` → Replace: `cursor.execute`
2. Find all: `await cursor.fetchall` → Replace: `cursor.fetchall`
3. Add `cursor.close()` after every query
4. Restart engines

**Outcome:**
- ✅ Immediate fix (engines become healthy)
- ❌ Poor performance under load
- ❌ Event loop blocking (can only handle ~10 concurrent requests)
- ❌ Need to migrate to proper async later anyway

**Risk**: Medium - Will work but performance degradation

---

## Technical Debt Assessment

### If We Choose aiopg (Option A)
- **New Debt**: Minimal - following best practices
- **Resolved Debt**: Complete fix for connection pool issue
- **Future Work**: None - sustainable pattern

### If We Choose asyncpg (Option B)
- **New Debt**: None - industry standard
- **Resolved Debt**: Complete fix + performance gains
- **Future Work**: None - best long-term choice

### If We Choose Remove await (Option C)
- **New Debt**: HIGH - event loop blocking
- **Resolved Debt**: Only immediate crash issue
- **Future Work**: Still need to migrate to aiopg/asyncpg later

---

## Questions for Discussion

### Architecture
1. **Is high concurrency a requirement?**
   - If yes → aiopg or asyncpg mandatory
   - If no → Remove await might suffice temporarily

2. **What's our expected request load?**
   - <10 concurrent: Sync might work
   - 10-100 concurrent: Need aiopg minimum
   - 100+ concurrent: Need asyncpg for performance

3. **How critical is this system?**
   - Production SOC: Need proper async (aiopg/asyncpg)
   - Internal testing: Emergency workaround acceptable

### Migration
4. **Can we afford 4-6 hours of dev time for aiopg?**
   - If yes → Do it right
   - If no → Emergency workaround, schedule proper fix

5. **Do we have test coverage for database operations?**
   - If yes → asyncpg safer (tests catch API changes)
   - If no → aiopg safer (less API changes)

6. **Is there a reason we're using psycopg2 instead of asyncpg?**
   - Legacy code migration?
   - Team familiarity?
   - Dependency constraints?

### Process
7. **How did this slip through?**
   - Need better code review?
   - Need linting rules to catch `await` on sync functions?
   - Need CI/CD tests for connection pool health?

8. **Why did the "October 2025 fix" fail?**
   - Incomplete implementation?
   - Code regression?
   - Different issue entirely?

---

## My Recommendation

**Choose Option A: aiopg** for the following reasons:

1. ✅ **Proper fix** - Solves root cause permanently
2. ✅ **Reasonable effort** - 4-6 hours vs 1-2 hours (emergency) isn't much difference
3. ✅ **Best practices** - Industry standard pattern
4. ✅ **Maintainable** - Future developers understand the pattern
5. ✅ **Connection pooling** - Handles resource management automatically
6. ✅ **Performance** - Event loop never blocks
7. ✅ **Minimal risk** - aiopg designed specifically for psycopg2 migration

**Implementation Plan:**
1. **Phase 1** (1 hour): Install aiopg, update base_engine.py, test connection
2. **Phase 2** (2 hours): Refactor Backend Engine (most critical for frontend)
3. **Phase 3** (1 hour): Refactor Delivery Engine
4. **Phase 4** (1 hour): Refactor Intelligence, Contextualization, Ingestion
5. **Phase 5** (30 min): Integration testing, monitoring

**Total Downtime**: Can be done with rolling restarts - ~30 minutes complete outage

---

## Code Examples for Migration

### Current (Broken) Pattern
```python
async def _handle_fidelity_assessment(self, request):
    try:
        cursor = self.db_connection.cursor()  # Old pattern
        await cursor.execute("""
            SELECT * FROM detection_rules
            WHERE quality_score > %s
        """, (0.7,))
        rules = await cursor.fetchall()  # This fails
        return {"rules": rules}
    except Exception as e:
        return {"error": str(e)}
    # Cursor never closed → connection leaked
```

### Fixed with aiopg
```python
async def _handle_fidelity_assessment(self, request):
    try:
        async with self.db_pool.acquire() as conn:  # ✅ From pool
            async with conn.cursor() as cur:  # ✅ Auto-closes
                await cur.execute("""
                    SELECT * FROM detection_rules
                    WHERE quality_score > %s
                """, (0.7,))
                rules = await cur.fetchall()  # ✅ Actually async
                return {"rules": [dict(r) for r in rules]}
    except Exception as e:
        logger.error(f"Fidelity assessment failed: {e}")
        return {"error": str(e)}
    # Connection auto-returns to pool ✅
```

### Fixed with asyncpg
```python
async def _handle_fidelity_assessment(self, request):
    try:
        async with self.db_pool.acquire() as conn:
            rules = await conn.fetch("""
                SELECT * FROM detection_rules
                WHERE quality_score > $1
            """, 0.7)  # Note: $1 not %s
            return {"rules": [dict(r) for r in rules]}
    except Exception as e:
        logger.error(f"Fidelity assessment failed: {e}")
        return {"error": str(e)}
```

### Emergency Workaround (Remove await)
```python
def _handle_fidelity_assessment_sync(self, request):  # Not async!
    try:
        cursor = self.db_connection.cursor()
        cursor.execute("""
            SELECT * FROM detection_rules
            WHERE quality_score > %s
        """, (0.7,))
        rules = cursor.fetchall()
        cursor.close()  # ✅ At least close it
        return {"rules": rules}
    except Exception as e:
        return {"error": str(e)}

async def _handle_fidelity_assessment(self, request):
    # Run in executor to not block event loop
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(
        None,  # Use default executor
        self._handle_fidelity_assessment_sync,
        request
    )
```

---

## Monitoring & Validation

### After Fix - Health Checks Should Show:
```json
{
  "engine": "backend",
  "status": "healthy",
  "database": "connected",
  "pool": {
    "size": 20,
    "available": 18,
    "in_use": 2
  }
}
```

### Test Endpoints:
```bash
# Should return data, not 500 error
curl -X POST http://localhost:8002/api/detection/fidelity -d '{}'

# Should show healthy
curl http://localhost:8002/health

# Check connection pool isn't exhausting over time
watch -n 10 'curl -s http://localhost:8002/health | jq .pool'
```

### Long-term Monitoring:
```python
# Add to health check
pool_stats = {
    "size": self.db_pool.size,
    "free": self.db_pool.freesize,
    "used": self.db_pool.size - self.db_pool.freesize,
    "max": self.db_pool.maxsize
}
```

---

## Additional Resources

### Documentation
- [aiopg Documentation](https://aiopg.readthedocs.io/)
- [asyncpg Documentation](https://magicstack.github.io/asyncpg/)
- [psycopg2 vs aiopg vs asyncpg Comparison](https://stackoverflow.com/questions/51154111/am-i-using-aiohttp-together-with-psycopg2-correctly)

### Similar Issues Solved
- Stack Overflow: "Am I using aiohttp together with psycopg2 correctly?"
- PostgreSQL Professional: "Problem with psycopg2 and asyncio"
- Medium: "Asynchronous Postgres with Python, FastAPI, and Psycopg 3"

---

## Conclusion

This is a **critical architectural issue** that requires a proper fix. The "emergency workaround" option exists but will cause problems under load.

**My strong recommendation**: Invest the 4-6 hours to implement **aiopg** properly. It's the industry-standard solution for this exact problem, and it will save weeks of debugging performance issues later.

The frontend is production-ready and waiting. Let's get the backend to the same standard.

---

**Status**: 🔴 Awaiting Decision
**Priority**: P0 - Critical
**Next Step**: Choose option and begin implementation

**Prepared by**: Claude (AI Assistant)
**Date**: October 4, 2025
**Version**: 1.0
