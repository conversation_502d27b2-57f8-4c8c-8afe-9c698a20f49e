# CTI Data Flow - Proper Segregation Architecture

## Problem: Same CTI Data, Multiple Purposes

CTI data from OTX, OpenCTI, ThreatFox, and MITRE ATT&CK is currently used for **multiple different purposes**, but the architecture doesn't clearly segregate these uses.

## CTI Data Sources & Their Multiple Uses:

### 1. **OTX (AlienVault Open Threat Exchange)**
**Raw Data**: IPs, domains, hashes, URLs, pulses
**Multiple Uses**:
- **Firehose Enrichment**: Check incoming logs against IOC database (real-time)
- **Rule Generation**: Create detection rules from IOC patterns (scheduled)
- **Investigation Context**: Enrich entities during investigation (on-demand)
- **MITRE Mapping**: IOCs often tagged with MITRE techniques

### 2. **OpenCTI**
**Raw Data**: Structured threat intel, campaigns, intrusion sets, TTPs
**Multiple Uses**:
- **Firehose Enrichment**: Tag logs with campaign/threat actor context
- **Rule Generation**: Create rules based on TTP patterns
- **Investigation Context**: Link alerts to known campaigns
- **MITRE Mapping**: TTPs mapped to ATT&CK framework

### 3. **ThreatFox**
**Raw Data**: Fresh malware IOCs, C2 servers
**Multiple Uses**:
- **Firehose Enrichment**: Real-time malware detection
- **Rule Generation**: Create signature-based rules
- **Investigation Context**: Identify malware families

### 4. **MITRE ATT&CK Framework**
**Raw Data**: Techniques, tactics, procedures, data sources
**Multiple Uses**:
- **Rule Mapping**: Tag detection rules with techniques
- **Coverage Analysis**: Identify detection gaps
- **Investigation Context**: Classify attacker behavior
- **Training**: Educate analysts on TTPs

## Current Architecture (BROKEN - Mixed Responsibilities):

```
┌─────────────────────────────────────────────────────────┐
│                    CURRENT (WRONG)                       │
├─────────────────────────────────────────────────────────┤
│                                                          │
│  Backend (Update Scheduler)                             │
│    ├── Triggers CTI fetch ❌ (should be in Ingestion)  │
│    ├── Rule generation ✅ (correct)                     │
│    └── MITRE mapping ✅ (correct)                       │
│                                                          │
│  Ingestion (CTI Manager)                                │
│    ├── Fetches CTI ✅ (correct)                         │
│    ├── No enrichment pipeline ❌ (missing)              │
│    └── No segregation by purpose ❌ (missing)           │
│                                                          │
│  Contextualization                                      │
│    └── No CTI enrichment ❌ (missing)                   │
│                                                          │
└─────────────────────────────────────────────────────────┘
```

## Proposed Architecture (SEGREGATED BY PURPOSE):

```
┌─────────────────────────────────────────────────────────────────────────┐
│                        INGESTION ENGINE                                  │
├─────────────────────────────────────────────────────────────────────────┤
│                                                                          │
│  1. CTI Feed Collector (Autonomous, Scheduled)                          │
│     ├── OTX Poller (every 6 hours)                                      │
│     ├── OpenCTI Sync (every 6 hours)                                    │
│     ├── ThreatFox Poller (every 4 hours)                                │
│     ├── MITRE ATT&CK Updater (daily)                                    │
│     └── Publishes raw CTI to multiple channels                          │
│                                                                          │
│  Published Channels (Segregated by Purpose):                            │
│     ├── 'cti.enrichment.iocs' → For firehose enrichment                │
│     ├── 'cti.rules.patterns' → For rule generation                     │
│     ├── 'cti.investigation.context' → For investigation enrichment     │
│     └── 'cti.mitre.mappings' → For MITRE framework updates            │
│                                                                          │
└─────────────────────────────────────────────────────────────────────────┘
                                      ↓ (multiple channels)
┌─────────────────────────────────────────────────────────────────────────┐
│                   CONTEXTUALIZATION ENGINE                               │
├─────────────────────────────────────────────────────────────────────────┤
│                                                                          │
│  Subscribes to: 'cti.enrichment.iocs'                                   │
│                                                                          │
│  2. Real-time CTI Enrichment Pipeline                                   │
│     ├── IOC Cache (Redis) - for fast lookups                           │
│     ├── Entity Enrichment:                                              │
│     │   ├── IP → Check against OTX/ThreatFox                           │
│     │   ├── Domain → Check against OpenCTI                             │
│     │   ├── Hash → Check against ThreatFox                             │
│     │   └── User/Host → Check against campaign data                    │
│     ├── Relationship Enrichment:                                        │
│     │   └── Tag relationships with threat actor/campaign               │
│     └── Publishes enriched data → Backend                              │
│                                                                          │
└─────────────────────────────────────────────────────────────────────────┘
                                      ↓
┌─────────────────────────────────────────────────────────────────────────┐
│                       BACKEND ENGINE                                     │
├─────────────────────────────────────────────────────────────────────────┤
│                                                                          │
│  Subscribes to:                                                         │
│    - 'cti.rules.patterns' (for rule generation)                         │
│    - 'cti.mitre.mappings' (for framework updates)                       │
│    - 'cti.investigation.context' (for case enrichment)                  │
│                                                                          │
│  3. CTI-Based Rule Generation                                           │
│     ├── IOC → Detection Rules (Splunk/Elastic/Sentinel)                │
│     ├── TTP Patterns → Behavioral Rules                                │
│     └── Campaign Indicators → Correlation Rules                        │
│                                                                          │
│  4. MITRE ATT&CK Processing                                             │
│     ├── Framework Updates (techniques, tactics)                         │
│     ├── Rule-to-Technique Mapping                                       │
│     ├── Coverage Gap Analysis                                           │
│     └── Detection Capability Assessment                                 │
│                                                                          │
│  5. Investigation Context Storage                                       │
│     ├── Store threat actor profiles                                     │
│     ├── Store campaign timelines                                        │
│     └── Link IOCs to investigations                                     │
│                                                                          │
└─────────────────────────────────────────────────────────────────────────┘
```

## Data Flow by Purpose:

### Purpose 1: **Firehose Enrichment** (Real-time, High-Volume)
```
CTI Collector → 'cti.enrichment.iocs'
  → Contextualization (IOC cache)
    → Enrich incoming logs
      → Tag entities with threat context
        → Backend (store enriched intelligence)
```

**Frequency**: Every log (100K/min)
**Storage**: Redis cache (fast lookup)
**Data**: IOCs, threat scores, campaign tags

### Purpose 2: **Rule Generation** (Scheduled, Low-Volume)
```
CTI Collector → 'cti.rules.patterns'
  → Backend (Rule Generator)
    → Parse IOC patterns
      → Generate SIEM rules
        → Create test cases
          → Store rules in DB
```

**Frequency**: Every 6 hours
**Storage**: PostgreSQL (detection_rules table)
**Data**: IOC patterns, TTP signatures, behavioral indicators

### Purpose 3: **Investigation Context** (On-Demand, Medium-Volume)
```
CTI Collector → 'cti.investigation.context'
  → Backend (Investigation Manager)
    → Alert fires
      → Fetch related threat intel
        → Enrich investigation
          → Display to analyst
```

**Frequency**: Per alert/investigation
**Storage**: Redis cache (7-day TTL) + PostgreSQL (permanent context)
**Data**: Threat actor profiles, campaign details, IOC relationships

### Purpose 4: **MITRE Mapping** (Scheduled, Low-Volume)
```
CTI Collector → 'cti.mitre.mappings'
  → Backend (MITRE Mapper)
    → Update framework
      → Map rules to techniques
        → Analyze coverage gaps
          → Generate recommendations
```

**Frequency**: Daily
**Storage**: PostgreSQL (mitre_attack_framework table)
**Data**: Techniques, tactics, data sources, relationships

## Redis Channel Architecture:

### Ingestion Engine Publishes:
```python
# Segregated CTI channels
'cti.enrichment.iocs'          # For real-time enrichment
'cti.rules.patterns'           # For rule generation
'cti.investigation.context'    # For investigation enrichment
'cti.mitre.mappings'           # For MITRE framework updates
```

### Contextualization Subscribes:
```python
'cti.enrichment.iocs'          # Receives IOCs for real-time lookup
```

### Backend Subscribes:
```python
'cti.rules.patterns'           # Receives patterns for rule generation
'cti.investigation.context'    # Receives context for investigations
'cti.mitre.mappings'           # Receives MITRE updates
```

## Component Segregation:

### **Ingestion Engine Components**:
```
ingestion/
├── cti_feed_collector.py          # Autonomous CTI fetching
│   ├── OTXCollector
│   ├── OpenCTICollector
│   ├── ThreatFoxCollector
│   └── MITRECollector
│
├── cti_data_router.py              # Routes CTI by purpose
│   └── route_to_channels()         # Publishes to segregated channels
│
└── cti_update_scheduler.py         # Schedules CTI updates
    └── Independent scheduling (NOT triggered by Backend)
```

### **Contextualization Engine Components**:
```
contextualization/
├── cti_enrichment_pipeline.py     # Real-time enrichment
│   ├── IOCCache (Redis)
│   ├── enrich_entity()
│   └── enrich_relationship()
│
└── threat_context_enricher.py     # Add threat actor/campaign context
```

### **Backend Engine Components**:
```
backend/
├── cti_rule_generator.py          # Generate rules from CTI
│   ├── ioc_to_rule()
│   └── ttp_to_rule()
│
├── mitre_attack_mapper.py         # MITRE framework processing
│   ├── update_framework()
│   ├── map_rule_to_technique()
│   └── analyze_coverage()
│
└── investigation_context_manager.py # Investigation enrichment
    └── enrich_investigation()
```

## Implementation Plan:

### Phase 1: Add Channel Segregation ✅
- Modify `cti_manager.py` to publish to multiple channels
- Add `cti_data_router.py` to route by purpose

### Phase 2: Add Contextualization Enrichment ❌
- Create `cti_enrichment_pipeline.py`
- Build IOC cache in Redis
- Subscribe to `cti.enrichment.iocs`

### Phase 3: Update Backend Subscriptions ❌
- Subscribe to segregated channels
- Process each channel differently
- Remove direct CTI fetching from Backend

### Phase 4: Move Scheduler to Ingestion ❌
- Move CTI scheduling from Backend → Ingestion
- Make CTI updates autonomous
- Remove Backend trigger dependencies

## Benefits of Segregation:

1. **Clear Separation of Concerns**
   - Ingestion = Fetch
   - Contextualization = Enrich
   - Backend = Process & Store

2. **Independent Scaling**
   - Scale enrichment separately from rule generation
   - Different Redis TTLs for different purposes

3. **Better Performance**
   - Real-time enrichment doesn't wait for rule generation
   - Cached IOCs for fast lookup

4. **Easier Debugging**
   - Each channel has specific purpose
   - Clear data flow boundaries

5. **Flexibility**
   - Add new CTI consumers without changing producers
   - Different update frequencies per purpose

## Data Format Examples:

### Channel: `cti.enrichment.iocs`
```json
{
  "source": "otx",
  "ioc_type": "ip",
  "value": "*************",
  "threat_score": 85,
  "tags": ["malware", "c2"],
  "campaign": "APT28",
  "first_seen": "2025-10-01T00:00:00Z",
  "ttl": 86400
}
```

### Channel: `cti.rules.patterns`
```json
{
  "source": "opencti",
  "pattern_type": "network",
  "indicators": ["domain:evil.com", "ip:*******"],
  "ttp": "T1071.001",
  "rule_template": "network_connection",
  "severity": "high",
  "campaign": "Operation XYZ"
}
```

### Channel: `cti.investigation.context`
```json
{
  "source": "opencti",
  "threat_actor": "APT28",
  "campaigns": ["Operation Ghostwriter"],
  "ttps": ["T1071", "T1059"],
  "iocs": {
    "ips": ["*******"],
    "domains": ["evil.com"]
  },
  "timeline": "2024-Q4"
}
```

### Channel: `cti.mitre.mappings`
```json
{
  "source": "mitre",
  "technique_id": "T1071.001",
  "technique_name": "Web Protocols",
  "tactic": "Command and Control",
  "data_sources": ["Network Traffic", "Process"],
  "detection_methods": [...],
  "updated": "2025-10-01"
}
```

## Next Steps:

1. **Immediate**: Update `cti_manager.py` to publish to segregated channels
2. **Next**: Create `cti_enrichment_pipeline.py` in Contextualization Engine
3. **Then**: Update Backend to subscribe to specific channels only
4. **Finally**: Move CTI scheduling from Backend to Ingestion

This architecture ensures CTI data serves all purposes efficiently without mixing concerns.
