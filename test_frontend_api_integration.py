"""
Test Frontend-Backend API Integration
Tests all API endpoints that the frontend uses
"""

import requests
import json
import time
from datetime import datetime

# Engine ports
DELIVERY_PORT = 8005
BACKEND_PORT = 8002
INGESTION_PORT = 8003
CONTEXTUALIZATION_PORT = 8004
INTELLIGENCE_PORT = 8001

BASE_URLS = {
    'delivery': f'http://localhost:{DELIVERY_PORT}',
    'backend': f'http://localhost:{BACKEND_PORT}',
    'ingestion': f'http://localhost:{INGESTION_PORT}',
    'contextualization': f'http://localhost:{CONTEXTUALIZATION_PORT}',
    'intelligence': f'http://localhost:{INTELLIGENCE_PORT}'
}

def test_endpoint(engine, method, path, data=None, params=None, description=""):
    """Test a single endpoint"""
    url = f"{BASE_URLS[engine]}{path}"

    print(f"\n{'='*80}")
    print(f"Testing: {description}")
    print(f"  {method} {engine}:{path}")
    if params:
        print(f"  Params: {params}")
    if data:
        print(f"  Body: {json.dumps(data, indent=2)[:200]}...")

    try:
        if method == 'GET':
            response = requests.get(url, params=params, timeout=5)
        elif method == 'POST':
            response = requests.post(url, json=data, timeout=5)
        elif method == 'PUT':
            response = requests.put(url, json=data, timeout=5)
        elif method == 'PATCH':
            response = requests.patch(url, json=data, timeout=5)
        elif method == 'DELETE':
            response = requests.delete(url, timeout=5)
        else:
            print(f"  ❌ Unknown method: {method}")
            return False

        print(f"  Status: {response.status_code}")

        if response.status_code == 200:
            try:
                result = response.json()
                print(f"  [OK] SUCCESS")
                print(f"  Response keys: {list(result.keys())}")
                return True
            except:
                print(f"  [OK] SUCCESS (non-JSON response)")
                return True
        else:
            print(f"  [WARN] Non-200 status")
            try:
                error = response.json()
                print(f"  Error: {error}")
            except:
                print(f"  Error: {response.text[:200]}")
            return False

    except requests.exceptions.ConnectionError:
        print(f"  [ERR] CONNECTION ERROR - Engine not running on port {BASE_URLS[engine].split(':')[-1]}")
        return False
    except requests.exceptions.Timeout:
        print(f"  [ERR] TIMEOUT - No response within 5 seconds")
        return False
    except Exception as e:
        print(f"  [ERR] ERROR: {str(e)}")
        return False

def main():
    """Run all API integration tests"""

    print("\n" + "="*80)
    print("FRONTEND-BACKEND API INTEGRATION TEST")
    print("Testing all endpoints used by frontend components")
    print("="*80)

    results = {
        'passed': 0,
        'failed': 0,
        'skipped': 0
    }

    # ============================================
    # 1. ENGINE HEALTH CHECKS
    # ============================================
    print("\n" + "#"*80)
    print("# 1. ENGINE HEALTH CHECKS")
    print("#"*80)

    engines_up = []
    for engine_name, base_url in BASE_URLS.items():
        if test_endpoint(engine_name, 'GET', '/health', description=f"{engine_name.capitalize()} Engine Health"):
            results['passed'] += 1
            engines_up.append(engine_name)
        else:
            results['failed'] += 1

    print(f"\n[OK] Engines Running: {', '.join(engines_up)}")
    print(f"[ERR] Engines Down: {', '.join([e for e in BASE_URLS.keys() if e not in engines_up])}")

    # ============================================
    # 2. ALERT & INVESTIGATION APIs (Delivery Engine)
    # ============================================
    print("\n" + "#"*80)
    print("# 2. ALERT & INVESTIGATION APIs")
    print("#"*80)

    if 'delivery' in engines_up:
        # Alert Queue Widget endpoints
        if test_endpoint('delivery', 'GET', '/api/alerts', params={'status': 'open', 'limit': 10},
                        description="List Alerts (AlertQueueWidget)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

        # Alert Investigation Screen endpoints
        if test_endpoint('delivery', 'GET', '/api/alerts', params={'limit': 1},
                        description="Get Single Alert"):
            results['passed'] += 1

            # Try to get enrichment status (will 404 if no alert, but tests endpoint)
            test_endpoint('delivery', 'GET', '/api/alerts/test_alert_123/enrichment',
                         description="Get Enrichment Status (poll endpoint)")
            test_endpoint('delivery', 'GET', '/api/alerts/test_alert_123/correlation',
                         description="Get Correlation Results (poll endpoint)")
            test_endpoint('delivery', 'GET', '/api/alerts/test_alert_123/context',
                         description="Get Investigation Context (Investigation Screen)")
        else:
            results['failed'] += 1

    # ============================================
    # 3. RULE MANAGEMENT APIs
    # ============================================
    print("\n" + "#"*80)
    print("# 3. RULE MANAGEMENT APIs")
    print("#"*80)

    if 'delivery' in engines_up:
        # Rule Library Widget
        if test_endpoint('delivery', 'GET', '/api/rules', params={'limit': 10},
                        description="List Rules (RuleLibraryWidget)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

    if 'backend' in engines_up:
        # Pending Rules Widget
        if test_endpoint('backend', 'GET', '/api/pending-rules', params={'limit': 10},
                        description="List Pending Rules (PendingRulesWidget)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

        # Rule Performance Widget
        if test_endpoint('backend', 'GET', '/api/rules/performance', params={'period': '7d'},
                        description="Get Rule Performance (RulePerformanceWidget)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

        # CTI Plugin Status Widget
        if test_endpoint('backend', 'GET', '/api/cti/status',
                        description="Get CTI Plugin Status (CTIPluginStatus)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

        if test_endpoint('backend', 'GET', '/api/cti/statistics',
                        description="Get CTI Statistics (CTIPluginStatus)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

    # ============================================
    # 4. DASHBOARD & MONITORING APIs
    # ============================================
    print("\n" + "#"*80)
    print("# 4. DASHBOARD & MONITORING APIs")
    print("#"*80)

    if 'delivery' in engines_up:
        if test_endpoint('delivery', 'GET', '/api/dashboard/overview',
                        description="Dashboard Overview (DashboardOverview)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

        if test_endpoint('delivery', 'GET', '/api/dashboard/stats',
                        description="Dashboard Stats (DashboardOverview)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

        if test_endpoint('delivery', 'GET', '/api/system/status',
                        description="System Status (DashboardOverview)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

        if test_endpoint('delivery', 'GET', '/api/system/engines',
                        description="Engine Status (DashboardOverview)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

    # ============================================
    # 5. ENTITY & GRAPH APIs
    # ============================================
    print("\n" + "#"*80)
    print("# 5. ENTITY & GRAPH APIs")
    print("#"*80)

    if 'delivery' in engines_up:
        if test_endpoint('delivery', 'GET', '/api/entities', params={'limit': 10},
                        description="List Entities (EntityExplorer)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

    if 'backend' in engines_up:
        if test_endpoint('backend', 'GET', '/api/graph/stats',
                        description="Graph Statistics (RelationshipGraph)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

    # ============================================
    # 6. CASE & WORKFLOW APIs
    # ============================================
    print("\n" + "#"*80)
    print("# 6. CASE & WORKFLOW APIs")
    print("#"*80)

    if 'delivery' in engines_up:
        if test_endpoint('delivery', 'GET', '/api/cases', params={'status': 'active'},
                        description="List Cases (InvestigationWorkspace)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

        if test_endpoint('delivery', 'GET', '/api/workflows',
                        description="List Workflows (InvestigationWorkspace)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

    # ============================================
    # 7. PATTERN MANAGEMENT APIs
    # ============================================
    print("\n" + "#"*80)
    print("# 7. PATTERN MANAGEMENT APIs")
    print("#"*80)

    if 'delivery' in engines_up:
        if test_endpoint('delivery', 'GET', '/api/patterns', params={'limit': 10},
                        description="List Patterns (PatternLibrary)"):
            results['passed'] += 1
        else:
            results['failed'] += 1

    # ============================================
    # FINAL RESULTS
    # ============================================
    print("\n" + "="*80)
    print("INTEGRATION TEST RESULTS")
    print("="*80)
    print(f"[OK] Passed:  {results['passed']}")
    print(f"[ERR] Failed:  {results['failed']}")
    print(f"[SKIP] Skipped: {results['skipped']}")
    print(f"[INFO] Total:   {results['passed'] + results['failed'] + results['skipped']}")

    if results['failed'] == 0:
        print("\n[SUCCESS] ALL TESTS PASSED! Frontend-Backend integration is ready!")
        return 0
    else:
        print(f"\n[WARNING] {results['failed']} tests failed. Check engine connectivity and endpoint implementations.")
        return 1

if __name__ == '__main__':
    exit(main())
