"""
Check what's actually stored in SIEMLess database
vs what's available in Elastic Cloud
"""

import psycopg2
from datetime import datetime

DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'siemless_v2',
    'user': 'siemless',
    'password': 'siemless123'
}

print("="*80)
print("DATABASE STORAGE ANALYSIS")
print("="*80)

conn = psycopg2.connect(**DB_CONFIG)
cursor = conn.cursor()

# Check all tables
print("\n[1] Checking database tables...")
cursor.execute("""
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'public'
    ORDER BY table_name
""")

tables = [row[0] for row in cursor.fetchall()]
print(f"[OK] Found {len(tables)} tables")

# Key tables for log/data storage
storage_tables = [
    'entities',
    'relationships',
    'ingestion_logs',
    'warm_storage',
    'detection_rules',
    'cases',
    'events',
    'sessions'
]

print("\n[2] Storage-related tables:")
for table in storage_tables:
    if table in tables:
        cursor.execute(f"SELECT COUNT(*) FROM {table}")
        count = cursor.fetchone()[0]
        print(f"  [OK] {table}: {count:,} rows")
    else:
        print(f"  [--] {table}: Not found")

# Check ingestion_logs specifically
print("\n[3] Ingestion Logs Detail...")
if 'ingestion_logs' in tables:
    cursor.execute("SELECT * FROM ingestion_logs LIMIT 0")
    columns = [desc[0] for desc in cursor.description]
    print(f"  Columns: {columns}")

    cursor.execute("SELECT COUNT(*), MIN(created_at), MAX(created_at) FROM ingestion_logs")
    count, min_date, max_date = cursor.fetchone()
    if count > 0:
        print(f"  Total logs: {count:,}")
        print(f"  Date range: {min_date} to {max_date}")

        # Check sources
        cursor.execute("""
            SELECT source_name, COUNT(*) as count
            FROM ingestion_logs
            GROUP BY source_name
            ORDER BY count DESC
        """)

        print(f"\n  [Log Sources]")
        for source, count in cursor.fetchall():
            print(f"    {source}: {count:,} logs")
else:
    print("  [WARN] ingestion_logs table does not exist")

# Check warm_storage
print("\n[4] Warm Storage Detail...")
if 'warm_storage' in tables:
    cursor.execute("SELECT * FROM warm_storage LIMIT 0")
    columns = [desc[0] for desc in cursor.description]
    print(f"  Columns: {columns}")

    cursor.execute("SELECT COUNT(*) FROM warm_storage")
    count = cursor.fetchone()[0]
    print(f"  Total records: {count:,}")

    if count > 0:
        # Sample data
        cursor.execute("SELECT * FROM warm_storage LIMIT 3")
        print(f"\n  [Sample Records]")
        for row in cursor.fetchall():
            print(f"    {row[:3]}...")  # First 3 columns
else:
    print("  [WARN] warm_storage table does not exist")

# Check entities - source attribution
print("\n[5] Entity Source Attribution...")
cursor.execute("SELECT * FROM entities LIMIT 0")
columns = [desc[0] for desc in cursor.description]

if 'properties' in columns:
    print("  Checking if entities track their source vendor...")

    # Sample entity properties
    cursor.execute("""
        SELECT entity_type, entity_value, properties
        FROM entities
        WHERE properties IS NOT NULL
        LIMIT 5
    """)

    print(f"\n  [Sample Entities with Properties]")
    for entity_type, value, props in cursor.fetchall():
        print(f"    {entity_type}: {value}")
        print(f"      Properties: {props}")

# Check if there's a log storage table we're missing
print("\n[6] Looking for log/event storage tables...")
log_related = [t for t in tables if 'log' in t.lower() or 'event' in t.lower() or 'storage' in t.lower()]

print(f"  Found {len(log_related)} log/event related tables:")
for table in log_related:
    cursor.execute(f"SELECT COUNT(*) FROM {table}")
    count = cursor.fetchone()[0]
    print(f"    {table}: {count:,} rows")

# Summary
print("\n" + "="*80)
print("STORAGE ARCHITECTURE ANALYSIS")
print("="*80)

print("""
Current SIEMLess Architecture (Lightweight):

  1. ELASTIC CLOUD (External)
     - Stores: 6.95 BILLION raw security logs
     - Vendors: TippingPoint, ThreatLocker, Fortinet, Palo Alto, CrowdStrike, etc.
     - Purpose: Complete log archive, queryable on-demand
     - Cost: ~$X/month for Elastic storage

  2. SIEMLESS DATABASE (Local PostgreSQL)
     - Stores: INTELLIGENCE EXTRACTED from logs
     - What's stored:
       * Entities (34 rows): IPs, hosts, users, processes
       * Relationships (12 rows): entity-to-entity mappings
       * Detection rules
       * Cases/investigations
       * NOT storing raw logs (by design)
     - Purpose: Fast relationship queries, investigation context
     - Cost: Minimal (local database)

KEY INSIGHT:
  SIEMLess does NOT duplicate Elastic's 6.95B logs into PostgreSQL.
  Instead, it:
    1. Queries Elastic on-demand (via context plugins)
    2. Extracts INTELLIGENCE (entities, relationships)
    3. Stores only the extracted intelligence locally
    4. References back to Elastic for raw log details

This is the "Lightweight Architecture" from CLAUDE.md:
  - 98.4% storage reduction (447 MB -> 7 MB)
  - Query Elastic when needed
  - Store only relationships and insights
""")

# Check if we have any vendor-specific data
print("\n[7] Vendor-Specific Data Check...")

vendors_to_check = ['tippingpoint', 'threatlocker', 'fortinet', 'palo', 'crowdstrike']

for vendor in vendors_to_check:
    # Check entities
    cursor.execute("""
        SELECT COUNT(*) FROM entities
        WHERE LOWER(entity_value) LIKE %s
           OR LOWER(properties::text) LIKE %s
    """, (f'%{vendor}%', f'%{vendor}%'))

    entity_count = cursor.fetchone()[0]

    if entity_count > 0:
        print(f"  [FOUND] {vendor}: {entity_count} entities reference this vendor")
    else:
        print(f"  [--] {vendor}: No entities in database (data in Elastic only)")

cursor.close()
conn.close()

print("\n" + "="*80)
print("CONCLUSION")
print("="*80)

print("""
The 6.95 BILLION logs are NOT in the SIEMLess PostgreSQL database.
They are in Elastic Cloud, where they belong.

SIEMLess architecture:
  ✅ Elastic = Log archive (6.95B events, all vendors)
  ✅ PostgreSQL = Intelligence layer (34 entities, 12 relationships)
  ✅ Context Plugins = Query Elastic on-demand
  ✅ Contextualization = Extract intelligence from queried logs

To get vendor data INTO SIEMLess database:
  1. Query Elastic (already working)
  2. Send results to Contextualization engine
  3. Extract entities/relationships
  4. Persist to PostgreSQL

Next step: Demonstrate this flow with TippingPoint/ThreatLocker data!
""")
