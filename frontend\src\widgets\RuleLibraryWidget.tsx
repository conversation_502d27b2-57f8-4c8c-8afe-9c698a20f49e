import React, { useEffect, useState } from 'react'
import { AgGridReact } from 'ag-grid-react'
import { ColDef, GridReadyEvent } from 'ag-grid-community'
import 'ag-grid-community/styles/ag-grid.css'
import 'ag-grid-community/styles/ag-theme-alpine.css'
import { useRuleStore } from '../stores/ruleStore'
import { wsClient } from '../api/client'
import { DetectionRule } from '../api/types/api'
import {
  Shield, Search, Filter, FolderPlus, Upload, Download,
  Edit, Trash2, Play, Pause, Archive, Tag, MoreVertical,
  CheckCircle, XCircle, RefreshCw, ChevronRight, Folder
} from 'lucide-react'

/**
 * Rule Library Widget
 *
 * Purpose: Manage existing detection rulesets
 *
 * Features:
 * - AG-Grid with virtual scrolling for thousands of rules
 * - Folder organization with tree view
 * - Tag-based categorization
 * - MITRE ATT&CK mapping
 * - Bulk actions (enable/disable, move, delete)
 * - Import/Export (Sigma, JSON, YAML)
 * - Performance metrics per rule
 * - Real-time status updates
 */

const RuleLibraryWidget: React.FC = () => {
  const {
    rules,
    selectedRule,
    folders,
    tags,
    ruleFilters,
    loading,
    error,
    fetchRules,
    fetchFolders,
    fetchTags,
    selectRule,
    updateRule,
    deleteRule,
    bulkUpdateRuleStatus,
    bulkMoveRules,
    bulkDeleteRules,
    setRuleFilters
  } = useRuleStore()

  const [gridApi, setGridApi] = useState<any>(null)
  const [selectedRuleIds, setSelectedRuleIds] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)
  const [showFolderPanel, setShowFolderPanel] = useState(true)
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null)

  const [filters, setFilters] = useState({
    status: '',
    rule_type: '',
    severity: '',
    mitre_tactic: '',
    search: ''
  })

  // Initial load
  useEffect(() => {
    fetchRules()
    fetchFolders()
    fetchTags()
  }, [fetchRules, fetchFolders, fetchTags])

  // WebSocket updates
  useEffect(() => {
    const handleRuleUpdate = (data: any) => {
      if (data.type === 'rule.updated' || data.type === 'rule.created') {
        fetchRules()
      }
    }

    wsClient.on('rule.updated', handleRuleUpdate)
    wsClient.on('rule.created', handleRuleUpdate)

    return () => {
      wsClient.off('rule.updated', handleRuleUpdate)
      wsClient.off('rule.created', handleRuleUpdate)
    }
  }, [fetchRules])

  const onGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api)
  }

  const onSelectionChanged = () => {
    if (!gridApi) return
    const selectedRows = gridApi.getSelectedRows()
    setSelectedRuleIds(selectedRows.map((r: DetectionRule) => r.rule_id))
  }

  const columnDefs: ColDef[] = [
    {
      headerCheckboxSelection: true,
      checkboxSelection: true,
      width: 50,
      pinned: 'left'
    },
    {
      field: 'enabled',
      headerName: '',
      width: 50,
      cellRenderer: (params: any) => (
        <div className="flex items-center h-full">
          {params.value ? (
            <CheckCircle className="text-green-500" size={16} />
          ) : (
            <XCircle className="text-gray-400" size={16} />
          )}
        </div>
      ),
      pinned: 'left'
    },
    {
      field: 'rule_name',
      headerName: 'Rule Name',
      width: 300,
      cellRenderer: (params: any) => (
        <div className="cursor-pointer hover:text-blue-600" onClick={() => selectRule(params.data.rule_id)}>
          <div className="font-medium">{params.value}</div>
          {params.data.description && (
            <div className="text-xs text-gray-500 truncate">{params.data.description}</div>
          )}
        </div>
      )
    },
    {
      field: 'severity',
      headerName: 'Severity',
      width: 110,
      cellRenderer: (params: any) => {
        const colors: Record<string, string> = {
          critical: 'bg-red-100 text-red-700 border-red-300',
          high: 'bg-orange-100 text-orange-700 border-orange-300',
          medium: 'bg-yellow-100 text-yellow-700 border-yellow-300',
          low: 'bg-blue-100 text-blue-700 border-blue-300',
          info: 'bg-gray-100 text-gray-700 border-gray-300'
        }
        return (
          <span className={`px-2 py-0.5 rounded text-xs font-medium border ${colors[params.value] || colors.info}`}>
            {params.value?.toUpperCase()}
          </span>
        )
      }
    },
    {
      field: 'rule_type',
      headerName: 'Type',
      width: 100,
      cellRenderer: (params: any) => (
        <span className="text-xs font-mono">{params.value?.toUpperCase()}</span>
      )
    },
    {
      field: 'mitre_techniques',
      headerName: 'MITRE',
      width: 120,
      cellRenderer: (params: any) => (
        <div className="flex flex-wrap gap-1">
          {params.value?.slice(0, 2).map((tech: string, idx: number) => (
            <span key={idx} className="px-1.5 py-0.5 bg-purple-100 text-purple-700 text-xs rounded">
              {tech}
            </span>
          ))}
          {params.value?.length > 2 && (
            <span className="text-xs text-gray-500">+{params.value.length - 2}</span>
          )}
        </div>
      )
    },
    {
      field: 'source',
      headerName: 'Source',
      width: 120,
      cellRenderer: (params: any) => {
        const icons: Record<string, string> = {
          cti_generated: '🤖',
          imported: '📥',
          manual: '✍️'
        }
        return (
          <div className="flex items-center gap-1">
            <span>{icons[params.value] || '📄'}</span>
            <span className="text-xs">{params.value?.replace('_', ' ')}</span>
          </div>
        )
      }
    },
    {
      field: 'tags',
      headerName: 'Tags',
      width: 180,
      cellRenderer: (params: any) => (
        <div className="flex flex-wrap gap-1">
          {params.value?.slice(0, 3).map((tag: string, idx: number) => (
            <span key={idx} className="px-1.5 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">
              {tag}
            </span>
          ))}
          {params.value?.length > 3 && (
            <span className="text-xs text-gray-500">+{params.value.length - 3}</span>
          )}
        </div>
      )
    },
    {
      field: 'created_at',
      headerName: 'Created',
      width: 120,
      cellRenderer: (params: any) => (
        <span className="text-xs text-gray-600">
          {new Date(params.value).toLocaleDateString()}
        </span>
      )
    },
    {
      headerName: 'Actions',
      width: 100,
      pinned: 'right',
      cellRenderer: (params: any) => (
        <div className="flex items-center gap-1 h-full">
          <button
            onClick={() => selectRule(params.data.rule_id)}
            className="p-1 hover:bg-gray-100 rounded"
            title="View Details"
          >
            <ChevronRight size={16} />
          </button>
          <button
            onClick={async () => {
              await updateRule(params.data.rule_id, { enabled: !params.data.enabled })
              fetchRules()
            }}
            className="p-1 hover:bg-gray-100 rounded"
            title={params.data.enabled ? 'Disable' : 'Enable'}
          >
            {params.data.enabled ? <Pause size={16} /> : <Play size={16} />}
          </button>
          <button
            onClick={async () => {
              if (confirm(`Delete rule "${params.data.rule_name}"?`)) {
                await deleteRule(params.data.rule_id)
              }
            }}
            className="p-1 hover:bg-red-100 text-red-600 rounded"
            title="Delete"
          >
            <Trash2 size={16} />
          </button>
        </div>
      )
    }
  ]

  const handleBulkEnable = async () => {
    await bulkUpdateRuleStatus(selectedRuleIds, true)
    setSelectedRuleIds([])
  }

  const handleBulkDisable = async () => {
    await bulkUpdateRuleStatus(selectedRuleIds, false)
    setSelectedRuleIds([])
  }

  const handleBulkDelete = async () => {
    if (confirm(`Delete ${selectedRuleIds.length} rules?`)) {
      await bulkDeleteRules(selectedRuleIds)
      setSelectedRuleIds([])
    }
  }

  const handleBulkMove = async () => {
    const folderId = prompt('Enter folder ID to move rules to:')
    if (folderId) {
      await bulkMoveRules(selectedRuleIds, folderId)
      setSelectedRuleIds([])
    }
  }

  const applyFilters = () => {
    const finalFilters = { ...filters }
    if (selectedFolder) {
      finalFilters['folder'] = selectedFolder
    }
    setRuleFilters(finalFilters)
    fetchRules(finalFilters)
    setShowFilters(false)
  }

  const handleExport = async () => {
    const format = prompt('Export format (sigma, json, yaml):') as 'sigma' | 'json' | 'yaml'
    if (!format) return

    const ruleService = (await import('../api/services/ruleService')).default
    const blob = await ruleService.exportRules(
      selectedRuleIds.length > 0 ? selectedRuleIds : rules.map(r => r.rule_id),
      format
    )

    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `rules_export.${format}`
    a.click()
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <Shield className="text-blue-500" size={20} />
              Rule Library
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              Manage detection rules across all sources
            </p>
          </div>

          <div className="flex items-center gap-2">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={16} />
              <input
                type="text"
                placeholder="Search rules..."
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                onKeyDown={(e) => e.key === 'Enter' && applyFilters()}
                className="pl-9 pr-3 py-1.5 border rounded w-64 text-sm focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Filters */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`p-2 rounded hover:bg-gray-100 ${showFilters ? 'bg-gray-100' : ''}`}
            >
              <Filter size={18} />
            </button>

            {/* Bulk Actions */}
            {selectedRuleIds.length > 0 && (
              <div className="flex items-center gap-2 px-3 py-1.5 bg-blue-50 border border-blue-200 rounded">
                <span className="text-sm text-blue-700 font-medium">
                  {selectedRuleIds.length} selected
                </span>
                <button
                  onClick={handleBulkEnable}
                  className="p-1 hover:bg-blue-100 rounded"
                  title="Enable All"
                >
                  <Play size={16} className="text-green-600" />
                </button>
                <button
                  onClick={handleBulkDisable}
                  className="p-1 hover:bg-blue-100 rounded"
                  title="Disable All"
                >
                  <Pause size={16} className="text-orange-600" />
                </button>
                <button
                  onClick={handleBulkMove}
                  className="p-1 hover:bg-blue-100 rounded"
                  title="Move to Folder"
                >
                  <Folder size={16} className="text-blue-600" />
                </button>
                <button
                  onClick={handleBulkDelete}
                  className="p-1 hover:bg-blue-100 rounded"
                  title="Delete All"
                >
                  <Trash2 size={16} className="text-red-600" />
                </button>
              </div>
            )}

            {/* Import/Export */}
            <button
              className="px-3 py-1.5 bg-white border rounded hover:bg-gray-50 text-sm flex items-center gap-1"
              onClick={() => {/* TODO: Import modal */}}
            >
              <Upload size={16} />
              Import
            </button>
            <button
              onClick={handleExport}
              className="px-3 py-1.5 bg-white border rounded hover:bg-gray-50 text-sm flex items-center gap-1"
            >
              <Download size={16} />
              Export
            </button>

            <button
              onClick={() => fetchRules()}
              disabled={loading.rules}
              className="p-2 rounded hover:bg-gray-100 disabled:opacity-50"
            >
              <RefreshCw size={18} className={loading.rules ? 'animate-spin' : ''} />
            </button>
          </div>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
            <div className="grid grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                  className="w-full px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All</option>
                  <option value="active">Active</option>
                  <option value="disabled">Disabled</option>
                  <option value="archived">Archived</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Rule Type
                </label>
                <select
                  value={filters.rule_type}
                  onChange={(e) => setFilters({ ...filters, rule_type: e.target.value })}
                  className="w-full px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All</option>
                  <option value="sigma">Sigma</option>
                  <option value="splunk">Splunk</option>
                  <option value="elastic">Elastic</option>
                  <option value="sentinel">Sentinel</option>
                  <option value="qradar">QRadar</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Severity
                </label>
                <select
                  value={filters.severity}
                  onChange={(e) => setFilters({ ...filters, severity: e.target.value })}
                  className="w-full px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All</option>
                  <option value="critical">Critical</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                  <option value="info">Info</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  MITRE Tactic
                </label>
                <select
                  value={filters.mitre_tactic}
                  onChange={(e) => setFilters({ ...filters, mitre_tactic: e.target.value })}
                  className="w-full px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All</option>
                  <option value="initial-access">Initial Access</option>
                  <option value="execution">Execution</option>
                  <option value="persistence">Persistence</option>
                  <option value="privilege-escalation">Privilege Escalation</option>
                  <option value="defense-evasion">Defense Evasion</option>
                  <option value="credential-access">Credential Access</option>
                  <option value="discovery">Discovery</option>
                  <option value="lateral-movement">Lateral Movement</option>
                  <option value="collection">Collection</option>
                  <option value="exfiltration">Exfiltration</option>
                  <option value="command-and-control">Command and Control</option>
                  <option value="impact">Impact</option>
                </select>
              </div>
            </div>

            <div className="mt-3 flex justify-end gap-2">
              <button
                onClick={() => setShowFilters(false)}
                className="px-3 py-1.5 text-gray-600 hover:bg-gray-200 rounded text-sm"
              >
                Cancel
              </button>
              <button
                onClick={applyFilters}
                className="px-3 py-1.5 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
              >
                Apply Filters
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Folder Panel */}
        {showFolderPanel && (
          <div className="w-64 bg-white border-r overflow-y-auto">
            <div className="p-4 border-b flex items-center justify-between">
              <h3 className="font-semibold flex items-center gap-2">
                <Folder size={16} />
                Folders
              </h3>
              <button
                onClick={() => {/* TODO: Create folder modal */}}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <FolderPlus size={16} />
              </button>
            </div>

            <div className="p-2">
              <button
                onClick={() => {
                  setSelectedFolder(null)
                  setRuleFilters({})
                  fetchRules()
                }}
                className={`w-full text-left px-3 py-2 rounded hover:bg-gray-100 flex items-center gap-2 ${
                  !selectedFolder ? 'bg-blue-50 text-blue-600' : ''
                }`}
              >
                <Folder size={16} />
                <span>All Rules ({rules.length})</span>
              </button>

              {loading.folders ? (
                <div className="p-4 text-center text-gray-400">
                  <RefreshCw className="animate-spin mx-auto" size={20} />
                </div>
              ) : (
                folders.map((folder) => (
                  <button
                    key={folder.folder_id}
                    onClick={() => {
                      setSelectedFolder(folder.folder_id)
                      setRuleFilters({ folder: folder.folder_id })
                      fetchRules({ folder: folder.folder_id })
                    }}
                    className={`w-full text-left px-3 py-2 rounded hover:bg-gray-100 flex items-center justify-between ${
                      selectedFolder === folder.folder_id ? 'bg-blue-50 text-blue-600' : ''
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <Folder size={16} />
                      <span className="text-sm">{folder.name}</span>
                    </div>
                    <span className="text-xs text-gray-500">{folder.rule_count}</span>
                  </button>
                ))
              )}
            </div>

            <div className="p-4 border-t">
              <h3 className="font-semibold mb-2 flex items-center gap-2 text-sm">
                <Tag size={14} />
                Common Tags
              </h3>
              <div className="flex flex-wrap gap-1">
                {tags.slice(0, 10).map((tag, idx) => (
                  <button
                    key={idx}
                    onClick={() => {
                      setRuleFilters({ tags: [tag] })
                      fetchRules({ tags: [tag] })
                    }}
                    className="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded hover:bg-blue-200"
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* AG-Grid */}
        <div className="flex-1 ag-theme-alpine">
          {error.rules ? (
            <div className="flex items-center justify-center h-full text-red-600">
              <div className="text-center">
                <XCircle className="mx-auto mb-2" size={32} />
                <p>{error.rules}</p>
              </div>
            </div>
          ) : (
            <AgGridReact
              rowData={rules}
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              onSelectionChanged={onSelectionChanged}
              rowSelection="multiple"
              suppressRowClickSelection={true}
              animateRows={true}
              pagination={true}
              paginationPageSize={50}
              defaultColDef={{
                resizable: true,
                sortable: true,
                filter: true
              }}
            />
          )}
        </div>
      </div>
    </div>
  )
}

export default RuleLibraryWidget
