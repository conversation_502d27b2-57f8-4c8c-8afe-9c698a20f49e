import React, { useState } from 'react';
import { Alert, CorrelationData, RelatedEvent } from '../../types/investigation';
import '../../styles/CorrelationTab.css';

interface CorrelationTabProps {
  alert: Alert;
  correlation: CorrelationData | null;
}

export const CorrelationTab: React.FC<CorrelationTabProps> = ({ alert, correlation }) => {
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set());
  const [sortField, setSortField] = useState<'timestamp' | 'severity'>('timestamp');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  if (!correlation?.correlation) {
    return (
      <div className="no-correlation">
        <div className="no-correlation-icon">🔗</div>
        <h3>No Correlation Data Available</h3>
        <p>Correlation analysis has not been performed for this alert.</p>
        <p className="hint">Trigger correlation to find related events across sources and time windows.</p>
      </div>
    );
  }

  const { related_events = [], summary, time_window, mitre_chain = [] } = correlation.correlation;

  const toggleEventExpansion = (eventId: string) => {
    const newExpanded = new Set(expandedEvents);
    if (newExpanded.has(eventId)) {
      newExpanded.delete(eventId);
    } else {
      newExpanded.add(eventId);
    }
    setExpandedEvents(newExpanded);
  };

  const handleSort = (field: 'timestamp' | 'severity') => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('desc');
    }
  };

  const sortedEvents = [...related_events].sort((a, b) => {
    let comparison = 0;
    if (sortField === 'timestamp') {
      comparison = new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
    } else {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      comparison = (severityOrder[a.severity as keyof typeof severityOrder] || 0) -
                   (severityOrder[b.severity as keyof typeof severityOrder] || 0);
    }
    return sortOrder === 'asc' ? comparison : -comparison;
  });

  const getEventDistribution = () => {
    const byType: Record<string, number> = {};
    const bySource: Record<string, number> = {};

    related_events.forEach(event => {
      byType[event.event_type] = (byType[event.event_type] || 0) + 1;
      bySource[event.source] = (bySource[event.source] || 0) + 1;
    });

    return { byType, bySource };
  };

  const { byType, bySource } = getEventDistribution();

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const getCorrelationColor = (score: number) => {
    if (score >= 0.7) return '#dc2626';
    if (score >= 0.4) return '#ea580c';
    return '#65a30d';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return '#dc2626';
      case 'high': return '#ea580c';
      case 'medium': return '#d97706';
      case 'low': return '#65a30d';
      default: return '#6b7280';
    }
  };

  const exportTimeline = () => {
    const data = {
      alert_id: alert.alert_id,
      time_window,
      related_events: sortedEvents,
      mitre_chain,
      summary
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `correlation_timeline_${alert.alert_id}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="correlation-tab">
      {/* Time Window Header */}
      <div className="correlation-header">
        <div className="time-window-card">
          <h3>📅 Analysis Time Window</h3>
          <div className="time-window-details">
            <div className="time-detail">
              <span className="label">Start:</span>
              <span className="value">{new Date(time_window.start).toLocaleString()}</span>
            </div>
            <div className="time-detail">
              <span className="label">End:</span>
              <span className="value">{new Date(time_window.end).toLocaleString()}</span>
            </div>
            <div className="time-detail">
              <span className="label">Duration:</span>
              <span className="value">{formatDuration(time_window.duration_ms)}</span>
            </div>
          </div>
        </div>

        <div className="correlation-score-card" style={{ borderColor: getCorrelationColor(summary.correlation_score) }}>
          <h3>🎯 Correlation Score</h3>
          <div className="score-display">
            <div className="score-value" style={{ color: getCorrelationColor(summary.correlation_score) }}>
              {(summary.correlation_score * 100).toFixed(1)}%
            </div>
            <div className="score-label">
              {summary.correlation_score >= 0.7 ? 'High Correlation' :
               summary.correlation_score >= 0.4 ? 'Medium Correlation' : 'Low Correlation'}
            </div>
          </div>
          <div className="score-details">
            <div className="score-row">
              <span className="label">Confidence:</span>
              <span className="value">{summary.confidence}%</span>
            </div>
            <div className="score-row">
              <span className="label">Related Events:</span>
              <span className="value">{summary.total_related_events}</span>
            </div>
            <div className="score-row">
              <span className="label">Unique Sources:</span>
              <span className="value">{summary.unique_sources}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Event Distribution */}
      <div className="distribution-section">
        <div className="distribution-card">
          <h4>📊 Events by Type</h4>
          <div className="distribution-bars">
            {Object.entries(byType).map(([type, count]) => (
              <div key={type} className="distribution-bar">
                <span className="bar-label">{type}</span>
                <div className="bar-container">
                  <div
                    className="bar-fill"
                    style={{
                      width: `${(count / related_events.length) * 100}%`,
                      background: '#3b82f6'
                    }}
                  />
                  <span className="bar-count">{count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="distribution-card">
          <h4>🔌 Events by Source</h4>
          <div className="distribution-bars">
            {Object.entries(bySource).map(([source, count]) => (
              <div key={source} className="distribution-bar">
                <span className="bar-label">{source}</span>
                <div className="bar-container">
                  <div
                    className="bar-fill"
                    style={{
                      width: `${(count / related_events.length) * 100}%`,
                      background: '#8b5cf6'
                    }}
                  />
                  <span className="bar-count">{count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* MITRE Chain */}
      {mitre_chain.length > 0 && (
        <div className="mitre-chain-section">
          <h4>🎯 MITRE ATT&CK Technique Chain</h4>
          <div className="mitre-chain">
            {mitre_chain.map((technique, index) => (
              <React.Fragment key={technique.technique_id}>
                <div className="mitre-technique">
                  <div className="technique-id">{technique.technique_id}</div>
                  <div className="technique-name">{technique.technique_name}</div>
                  <div className="technique-tactic">{technique.tactic}</div>
                </div>
                {index < mitre_chain.length - 1 && (
                  <div className="chain-arrow">→</div>
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      )}

      {/* Related Events Table */}
      <div className="events-section">
        <div className="events-header">
          <h4>📋 Related Events ({sortedEvents.length})</h4>
          <button className="export-btn" onClick={exportTimeline}>
            📥 Export Timeline
          </button>
        </div>

        <div className="events-table">
          <div className="table-header">
            <div className="header-cell timestamp-cell" onClick={() => handleSort('timestamp')}>
              Timestamp {sortField === 'timestamp' && (sortOrder === 'asc' ? '↑' : '↓')}
            </div>
            <div className="header-cell severity-cell" onClick={() => handleSort('severity')}>
              Severity {sortField === 'severity' && (sortOrder === 'asc' ? '↑' : '↓')}
            </div>
            <div className="header-cell type-cell">Event Type</div>
            <div className="header-cell source-cell">Source</div>
            <div className="header-cell description-cell">Description</div>
            <div className="header-cell action-cell">Actions</div>
          </div>

          <div className="table-body">
            {sortedEvents.map(event => (
              <div key={event.event_id} className="event-row">
                <div className="event-main">
                  <div className="event-cell timestamp-cell">
                    {new Date(event.timestamp).toLocaleString()}
                  </div>
                  <div className="event-cell severity-cell">
                    <span
                      className="severity-badge"
                      style={{ background: getSeverityColor(event.severity) }}
                    >
                      {event.severity.toUpperCase()}
                    </span>
                  </div>
                  <div className="event-cell type-cell">{event.event_type}</div>
                  <div className="event-cell source-cell">{event.source}</div>
                  <div className="event-cell description-cell">{event.description}</div>
                  <div className="event-cell action-cell">
                    <button
                      className="expand-btn"
                      onClick={() => toggleEventExpansion(event.event_id)}
                    >
                      {expandedEvents.has(event.event_id) ? '▼' : '▶'}
                    </button>
                  </div>
                </div>

                {expandedEvents.has(event.event_id) && (
                  <div className="event-details">
                    <div className="detail-section">
                      <h5>Event Details</h5>
                      <div className="detail-grid">
                        <div className="detail-row">
                          <span className="detail-label">Event ID:</span>
                          <span className="detail-value">{event.event_id}</span>
                        </div>
                        <div className="detail-row">
                          <span className="detail-label">Matching Fields:</span>
                          <span className="detail-value">
                            {event.matching_fields?.join(', ') || 'N/A'}
                          </span>
                        </div>
                        <div className="detail-row">
                          <span className="detail-label">Correlation Reason:</span>
                          <span className="detail-value">{event.correlation_reason}</span>
                        </div>
                      </div>
                    </div>

                    {event.raw_event && (
                      <div className="detail-section">
                        <h5>Raw Event Data</h5>
                        <pre className="raw-event-data">
                          {JSON.stringify(event.raw_event, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Summary Footer */}
      {summary.key_findings && summary.key_findings.length > 0 && (
        <div className="summary-footer">
          <h4>🔍 Key Findings</h4>
          <ul className="findings-list">
            {summary.key_findings.map((finding, index) => (
              <li key={index} className="finding-item">{finding}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
