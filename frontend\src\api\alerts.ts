import apiClient from './client'

export interface Alert {
  alert_id: string
  title: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  timestamp: string
  entities?: {
    ips?: string[]
    users?: string[]
    hosts?: string[]
  }
  status: 'open' | 'investigating' | 'closed'
  priority?: string
  type?: string
  message?: string
}

export interface AlertListParams {
  status?: 'open' | 'investigating' | 'closed' | 'all'
  limit?: number
}

export interface AlertListResponse {
  alerts: Alert[]
  count: number
}

export const alertAPI = {
  /**
   * Get list of alerts
   * GET /api/alerts?status=open&limit=50
   */
  getAlerts: (params?: AlertListParams) =>
    apiClient.get<AlertListResponse>('/alerts', { params }),

  /**
   * Send a new alert
   * POST /api/alerts
   */
  sendAlert: (data: Partial<Alert>) =>
    apiClient.post('/alerts', data),

  /**
   * Get alert history
   * GET /api/alerts/history
   */
  getAlertHistory: () =>
    apiClient.get('/alerts/history'),
}

export default alertAPI
