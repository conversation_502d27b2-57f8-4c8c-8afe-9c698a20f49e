/**
 * Case API Service
 * Handles all case-related API calls
 */

import apiClient from '../client'
import type {
  Case,
  CaseDetail,
  CaseFilters,
  PaginatedResponse,
  APIResponse,
  CreateCaseRequest,
  UpdateCaseRequest,
  Evidence
} from '../../types/api'

export const caseService = {
  /**
   * Get paginated list of cases
   */
  async getCases(
    filters?: CaseFilters,
    page: number = 1,
    pageSize: number = 50
  ): Promise<PaginatedResponse<Case>> {
    const response = await apiClient.get<PaginatedResponse<Case>>(
      '/cases',
      {
        params: {
          ...filters,
          page,
          page_size: pageSize
        }
      }
    )
    return response.data
  },

  /**
   * Get detailed case information
   */
  async getCaseDetail(caseId: string): Promise<CaseDetail> {
    const response = await apiClient.get<APIResponse<CaseDetail>>(
      `/api/cases/${caseId}`
    )
    return response.data.data
  },

  /**
   * Create new case
   */
  async createCase(caseData: CreateCaseRequest): Promise<Case> {
    const response = await apiClient.post<APIResponse<Case>>(
      '/cases',
      caseData
    )
    return response.data.data
  },

  /**
   * Update case
   */
  async updateCase(
    caseId: string,
    updates: UpdateCaseRequest
  ): Promise<Case> {
    const response = await apiClient.put<APIResponse<Case>>(
      `/api/cases/${caseId}`,
      updates
    )
    return response.data.data
  },

  /**
   * Close case
   */
  async closeCase(caseId: string): Promise<Case> {
    const response = await apiClient.delete<APIResponse<Case>>(
      `/api/cases/${caseId}`
    )
    return response.data.data
  },

  /**
   * Add evidence to case
   */
  async addEvidence(
    caseId: string,
    evidence: Partial<Evidence>
  ): Promise<Evidence> {
    const response = await apiClient.post<APIResponse<Evidence>>(
      `/api/cases/${caseId}/evidence`,
      evidence
    )
    return response.data.data
  }
}
