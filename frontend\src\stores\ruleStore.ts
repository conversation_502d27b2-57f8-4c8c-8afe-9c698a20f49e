import { create } from 'zustand'
import ruleService from '../api/services/ruleService'
import {
  DetectionRule,
  PendingRule,
  RulePerformance,
  RuleTestResult,
  RuleFolder,
  RulePerformanceSummary,
  PaginatedResponse
} from '../api/types/api'

interface RuleFilters {
  status?: string
  rule_type?: string
  folder?: string
  tags?: string[]
  severity?: string
  mitre_tactic?: string
  mitre_technique?: string
  search?: string
}

interface PendingRuleFilters {
  cti_source?: string
  quality_min?: number
  indicator_type?: string
}

interface RuleState {
  // Pending Rules (CTI-generated)
  pendingRules: PendingRule[]
  selectedPendingRule: PendingRule | null
  pendingFilters: PendingRuleFilters
  pendingPagination: {
    page: number
    pageSize: number
    totalItems: number
    totalPages: number
  }

  // Detection Rules (Active/Existing)
  rules: DetectionRule[]
  selectedRule: DetectionRule | null
  ruleFilters: RuleFilters
  rulePagination: {
    page: number
    pageSize: number
    totalItems: number
    totalPages: number
  }

  // Rule Performance
  rulePerformance: Map<string, RulePerformance>
  performanceSummary: RulePerformanceSummary | null

  // Organization
  folders: RuleFolder[]
  tags: string[]

  // Loading States
  loading: {
    pendingRules: boolean
    rules: boolean
    pendingDetail: boolean
    ruleDetail: boolean
    approve: boolean
    reject: boolean
    performance: boolean
    folders: boolean
    bulkAction: boolean
  }

  // Error States
  error: {
    pendingRules: string | null
    rules: string | null
    pendingDetail: string | null
    ruleDetail: string | null
    approve: string | null
    reject: string | null
    performance: string | null
    folders: string | null
    bulkAction: string | null
  }

  // Actions - Pending Rules
  fetchPendingRules: (filters?: PendingRuleFilters) => Promise<void>
  selectPendingRule: (pendingId: string) => Promise<void>
  approvePendingRule: (pendingId: string, edits?: any) => Promise<boolean>
  rejectPendingRule: (pendingId: string, reason?: string) => Promise<boolean>
  updatePendingRule: (pendingId: string, updates: any) => Promise<boolean>
  bulkApprovePendingRules: (pendingIds: string[]) => Promise<number>

  // Actions - Detection Rules
  fetchRules: (filters?: RuleFilters) => Promise<void>
  selectRule: (ruleId: string) => Promise<void>
  createRule: (rule: any) => Promise<DetectionRule | null>
  updateRule: (ruleId: string, updates: any) => Promise<boolean>
  deleteRule: (ruleId: string) => Promise<boolean>
  importRules: (importRequest: any) => Promise<number>
  bulkUpdateRuleStatus: (ruleIds: string[], enabled: boolean) => Promise<number>
  bulkMoveRules: (ruleIds: string[], folderId: string) => Promise<number>
  bulkDeleteRules: (ruleIds: string[]) => Promise<number>

  // Actions - Performance
  fetchRulePerformance: (ruleId: string, period?: string) => Promise<void>
  fetchPerformanceSummary: (period?: string) => Promise<void>
  updateRulePerformance: (ruleId: string, alertId: string, verdict: string) => Promise<void>

  // Actions - Organization
  fetchFolders: () => Promise<void>
  fetchTags: () => Promise<void>
  createFolder: (name: string, description?: string) => Promise<RuleFolder | null>
  addRuleTags: (ruleId: string, tags: string[]) => Promise<void>
  removeRuleTags: (ruleId: string, tags: string[]) => Promise<void>

  // Utility Actions
  setPendingFilters: (filters: PendingRuleFilters) => void
  setRuleFilters: (filters: RuleFilters) => void
  clearErrors: () => void
}

export const useRuleStore = create<RuleState>((set, get) => ({
  // Initial State - Pending Rules
  pendingRules: [],
  selectedPendingRule: null,
  pendingFilters: {},
  pendingPagination: {
    page: 1,
    pageSize: 20,
    totalItems: 0,
    totalPages: 0
  },

  // Initial State - Detection Rules
  rules: [],
  selectedRule: null,
  ruleFilters: {},
  rulePagination: {
    page: 1,
    pageSize: 50,
    totalItems: 0,
    totalPages: 0
  },

  // Initial State - Performance
  rulePerformance: new Map(),
  performanceSummary: null,

  // Initial State - Organization
  folders: [],
  tags: [],

  // Initial State - Loading
  loading: {
    pendingRules: false,
    rules: false,
    pendingDetail: false,
    ruleDetail: false,
    approve: false,
    reject: false,
    performance: false,
    folders: false,
    bulkAction: false
  },

  // Initial State - Errors
  error: {
    pendingRules: null,
    rules: null,
    pendingDetail: null,
    ruleDetail: null,
    approve: null,
    reject: null,
    performance: null,
    folders: null,
    bulkAction: null
  },

  // ===========================
  // Pending Rules Actions
  // ===========================

  fetchPendingRules: async (filters?: PendingRuleFilters) => {
    set(state => ({
      loading: { ...state.loading, pendingRules: true },
      error: { ...state.error, pendingRules: null }
    }))

    try {
      const finalFilters = filters || get().pendingFilters
      const response = await ruleService.getPendingRules(finalFilters)

      set({
        pendingRules: response.items,
        pendingPagination: {
          page: response.page,
          pageSize: response.page_size,
          totalItems: response.total_items,
          totalPages: response.total_pages
        },
        pendingFilters: finalFilters,
        loading: { ...get().loading, pendingRules: false }
      })
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, pendingRules: false },
        error: { ...state.error, pendingRules: error.message || 'Failed to load pending rules' }
      }))
    }
  },

  selectPendingRule: async (pendingId: string) => {
    set(state => ({
      loading: { ...state.loading, pendingDetail: true },
      error: { ...state.error, pendingDetail: null }
    }))

    try {
      const pendingRule = await ruleService.getPendingRule(pendingId)
      set({
        selectedPendingRule: pendingRule,
        loading: { ...get().loading, pendingDetail: false }
      })
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, pendingDetail: false },
        error: { ...state.error, pendingDetail: error.message || 'Failed to load pending rule' }
      }))
    }
  },

  approvePendingRule: async (pendingId: string, edits?: any) => {
    set(state => ({
      loading: { ...state.loading, approve: true },
      error: { ...state.error, approve: null }
    }))

    try {
      const approvedRule = await ruleService.approvePendingRule(pendingId, edits)

      // Remove from pending list
      set(state => ({
        pendingRules: state.pendingRules.filter(r => r.pending_id !== pendingId),
        selectedPendingRule: null,
        loading: { ...state.loading, approve: false }
      }))

      // Refresh rules list if loaded
      if (get().rules.length > 0) {
        get().fetchRules()
      }

      return true
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, approve: false },
        error: { ...state.error, approve: error.message || 'Failed to approve rule' }
      }))
      return false
    }
  },

  rejectPendingRule: async (pendingId: string, reason?: string) => {
    set(state => ({
      loading: { ...state.loading, reject: true },
      error: { ...state.error, reject: null }
    }))

    try {
      await ruleService.rejectPendingRule(pendingId, reason)

      // Remove from pending list
      set(state => ({
        pendingRules: state.pendingRules.filter(r => r.pending_id !== pendingId),
        selectedPendingRule: null,
        loading: { ...state.loading, reject: false }
      }))

      return true
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, reject: false },
        error: { ...state.error, reject: error.message || 'Failed to reject rule' }
      }))
      return false
    }
  },

  updatePendingRule: async (pendingId: string, updates: any) => {
    set(state => ({
      loading: { ...state.loading, pendingDetail: true },
      error: { ...state.error, pendingDetail: null }
    }))

    try {
      const updatedRule = await ruleService.updatePendingRule(pendingId, updates)

      set(state => ({
        selectedPendingRule: updatedRule,
        pendingRules: state.pendingRules.map(r =>
          r.pending_id === pendingId ? updatedRule : r
        ),
        loading: { ...state.loading, pendingDetail: false }
      }))

      return true
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, pendingDetail: false },
        error: { ...state.error, pendingDetail: error.message || 'Failed to update pending rule' }
      }))
      return false
    }
  },

  bulkApprovePendingRules: async (pendingIds: string[]) => {
    set(state => ({
      loading: { ...state.loading, bulkAction: true },
      error: { ...state.error, bulkAction: null }
    }))

    try {
      const result = await ruleService.bulkApprovePendingRules(pendingIds)

      // Remove approved rules from pending list
      set(state => ({
        pendingRules: state.pendingRules.filter(r => !pendingIds.includes(r.pending_id)),
        loading: { ...state.loading, bulkAction: false }
      }))

      // Refresh rules list if loaded
      if (get().rules.length > 0) {
        get().fetchRules()
      }

      return result.approved
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, bulkAction: false },
        error: { ...state.error, bulkAction: error.message || 'Failed to bulk approve rules' }
      }))
      return 0
    }
  },

  // ===========================
  // Detection Rules Actions
  // ===========================

  fetchRules: async (filters?: RuleFilters) => {
    set(state => ({
      loading: { ...state.loading, rules: true },
      error: { ...state.error, rules: null }
    }))

    try {
      const finalFilters = filters || get().ruleFilters
      const response = await ruleService.getRules(finalFilters)

      set({
        rules: response.items,
        rulePagination: {
          page: response.page,
          pageSize: response.page_size,
          totalItems: response.total_items,
          totalPages: response.total_pages
        },
        ruleFilters: finalFilters,
        loading: { ...get().loading, rules: false }
      })
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, rules: false },
        error: { ...state.error, rules: error.message || 'Failed to load rules' }
      }))
    }
  },

  selectRule: async (ruleId: string) => {
    set(state => ({
      loading: { ...state.loading, ruleDetail: true },
      error: { ...state.error, ruleDetail: null }
    }))

    try {
      const rule = await ruleService.getRule(ruleId)
      set({
        selectedRule: rule,
        loading: { ...get().loading, ruleDetail: false }
      })

      // Also fetch performance data for this rule
      get().fetchRulePerformance(ruleId)
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, ruleDetail: false },
        error: { ...state.error, ruleDetail: error.message || 'Failed to load rule' }
      }))
    }
  },

  createRule: async (rule: any) => {
    set(state => ({
      loading: { ...state.loading, ruleDetail: true },
      error: { ...state.error, ruleDetail: null }
    }))

    try {
      const newRule = await ruleService.createRule(rule)

      set(state => ({
        rules: [newRule, ...state.rules],
        selectedRule: newRule,
        loading: { ...state.loading, ruleDetail: false }
      }))

      return newRule
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, ruleDetail: false },
        error: { ...state.error, ruleDetail: error.message || 'Failed to create rule' }
      }))
      return null
    }
  },

  updateRule: async (ruleId: string, updates: any) => {
    set(state => ({
      loading: { ...state.loading, ruleDetail: true },
      error: { ...state.error, ruleDetail: null }
    }))

    try {
      const updatedRule = await ruleService.updateRule(ruleId, updates)

      set(state => ({
        selectedRule: updatedRule,
        rules: state.rules.map(r => r.rule_id === ruleId ? updatedRule : r),
        loading: { ...state.loading, ruleDetail: false }
      }))

      return true
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, ruleDetail: false },
        error: { ...state.error, ruleDetail: error.message || 'Failed to update rule' }
      }))
      return false
    }
  },

  deleteRule: async (ruleId: string) => {
    set(state => ({
      loading: { ...state.loading, ruleDetail: true },
      error: { ...state.error, ruleDetail: null }
    }))

    try {
      await ruleService.deleteRule(ruleId)

      set(state => ({
        rules: state.rules.filter(r => r.rule_id !== ruleId),
        selectedRule: state.selectedRule?.rule_id === ruleId ? null : state.selectedRule,
        loading: { ...state.loading, ruleDetail: false }
      }))

      return true
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, ruleDetail: false },
        error: { ...state.error, ruleDetail: error.message || 'Failed to delete rule' }
      }))
      return false
    }
  },

  importRules: async (importRequest: any) => {
    set(state => ({
      loading: { ...state.loading, rules: true },
      error: { ...state.error, rules: null }
    }))

    try {
      const result = await ruleService.importRules(importRequest)

      // Refresh rules list
      get().fetchRules()

      return result.imported
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, rules: false },
        error: { ...state.error, rules: error.message || 'Failed to import rules' }
      }))
      return 0
    }
  },

  bulkUpdateRuleStatus: async (ruleIds: string[], enabled: boolean) => {
    set(state => ({
      loading: { ...state.loading, bulkAction: true },
      error: { ...state.error, bulkAction: null }
    }))

    try {
      const result = await ruleService.bulkUpdateRuleStatus(ruleIds, enabled)

      // Update rules in state
      set(state => ({
        rules: state.rules.map(r =>
          ruleIds.includes(r.rule_id) ? { ...r, enabled } : r
        ),
        loading: { ...state.loading, bulkAction: false }
      }))

      return result.updated
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, bulkAction: false },
        error: { ...state.error, bulkAction: error.message || 'Failed to update rule status' }
      }))
      return 0
    }
  },

  bulkMoveRules: async (ruleIds: string[], folderId: string) => {
    set(state => ({
      loading: { ...state.loading, bulkAction: true },
      error: { ...state.error, bulkAction: null }
    }))

    try {
      const result = await ruleService.bulkMoveRules(ruleIds, folderId)

      // Update rules in state
      set(state => ({
        rules: state.rules.map(r =>
          ruleIds.includes(r.rule_id) ? { ...r, folder: folderId } : r
        ),
        loading: { ...state.loading, bulkAction: false }
      }))

      return result.moved
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, bulkAction: false },
        error: { ...state.error, bulkAction: error.message || 'Failed to move rules' }
      }))
      return 0
    }
  },

  bulkDeleteRules: async (ruleIds: string[]) => {
    set(state => ({
      loading: { ...state.loading, bulkAction: true },
      error: { ...state.error, bulkAction: null }
    }))

    try {
      const result = await ruleService.bulkDeleteRules(ruleIds)

      // Remove rules from state
      set(state => ({
        rules: state.rules.filter(r => !ruleIds.includes(r.rule_id)),
        loading: { ...state.loading, bulkAction: false }
      }))

      return result.deleted
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, bulkAction: false },
        error: { ...state.error, bulkAction: error.message || 'Failed to delete rules' }
      }))
      return 0
    }
  },

  // ===========================
  // Performance Actions
  // ===========================

  fetchRulePerformance: async (ruleId: string, period?: string) => {
    set(state => ({
      loading: { ...state.loading, performance: true },
      error: { ...state.error, performance: null }
    }))

    try {
      const performance = await ruleService.getRulePerformance(ruleId, period as any)

      set(state => {
        const newPerformance = new Map(state.rulePerformance)
        newPerformance.set(ruleId, performance)

        return {
          rulePerformance: newPerformance,
          loading: { ...state.loading, performance: false }
        }
      })
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, performance: false },
        error: { ...state.error, performance: error.message || 'Failed to load performance' }
      }))
    }
  },

  fetchPerformanceSummary: async (period?: string) => {
    set(state => ({
      loading: { ...state.loading, performance: true },
      error: { ...state.error, performance: null }
    }))

    try {
      const summary = await ruleService.getRulePerformanceSummary(period as any)

      set({
        performanceSummary: summary,
        loading: { ...get().loading, performance: false }
      })
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, performance: false },
        error: { ...state.error, performance: error.message || 'Failed to load performance summary' }
      }))
    }
  },

  updateRulePerformance: async (ruleId: string, alertId: string, verdict: string) => {
    try {
      const updatedPerformance = await ruleService.updateRulePerformance(
        ruleId,
        alertId,
        verdict as any
      )

      set(state => {
        const newPerformance = new Map(state.rulePerformance)
        newPerformance.set(ruleId, updatedPerformance)

        return { rulePerformance: newPerformance }
      })
    } catch (error: any) {
      console.error('Failed to update rule performance:', error)
    }
  },

  // ===========================
  // Organization Actions
  // ===========================

  fetchFolders: async () => {
    set(state => ({
      loading: { ...state.loading, folders: true },
      error: { ...state.error, folders: null }
    }))

    try {
      const folders = await ruleService.getRuleFolders()

      set({
        folders,
        loading: { ...get().loading, folders: false }
      })
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, folders: false },
        error: { ...state.error, folders: error.message || 'Failed to load folders' }
      }))
    }
  },

  fetchTags: async () => {
    try {
      const tags = await ruleService.getRuleTags()
      set({ tags })
    } catch (error: any) {
      console.error('Failed to load tags:', error)
    }
  },

  createFolder: async (name: string, description?: string) => {
    set(state => ({
      loading: { ...state.loading, folders: true },
      error: { ...state.error, folders: null }
    }))

    try {
      const newFolder = await ruleService.createRuleFolder(name, description)

      set(state => ({
        folders: [...state.folders, newFolder],
        loading: { ...state.loading, folders: false }
      }))

      return newFolder
    } catch (error: any) {
      set(state => ({
        loading: { ...state.loading, folders: false },
        error: { ...state.error, folders: error.message || 'Failed to create folder' }
      }))
      return null
    }
  },

  addRuleTags: async (ruleId: string, tags: string[]) => {
    try {
      const updatedRule = await ruleService.addRuleTags(ruleId, tags)

      set(state => ({
        selectedRule: state.selectedRule?.rule_id === ruleId ? updatedRule : state.selectedRule,
        rules: state.rules.map(r => r.rule_id === ruleId ? updatedRule : r)
      }))

      // Refresh tags list
      get().fetchTags()
    } catch (error: any) {
      console.error('Failed to add tags:', error)
    }
  },

  removeRuleTags: async (ruleId: string, tags: string[]) => {
    try {
      const updatedRule = await ruleService.removeRuleTags(ruleId, tags)

      set(state => ({
        selectedRule: state.selectedRule?.rule_id === ruleId ? updatedRule : state.selectedRule,
        rules: state.rules.map(r => r.rule_id === ruleId ? updatedRule : r)
      }))
    } catch (error: any) {
      console.error('Failed to remove tags:', error)
    }
  },

  // ===========================
  // Utility Actions
  // ===========================

  setPendingFilters: (filters: PendingRuleFilters) => {
    set({ pendingFilters: filters })
  },

  setRuleFilters: (filters: RuleFilters) => {
    set({ ruleFilters: filters })
  },

  clearErrors: () => {
    set({
      error: {
        pendingRules: null,
        rules: null,
        pendingDetail: null,
        ruleDetail: null,
        approve: null,
        reject: null,
        performance: null,
        folders: null,
        bulkAction: null
      }
    })
  }
}))
