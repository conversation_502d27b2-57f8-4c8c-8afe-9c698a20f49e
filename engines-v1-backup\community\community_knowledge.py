#!/usr/bin/env python3
"""
SIEMLess v2.0 Community Knowledge Engine

Continuously harvests open-source security intelligence from:
- GitHub repositories (log parsers, MITRE mappings, use cases)
- MITRE ATT&CK framework (official updates)
- Community threat intel feeds
- User-configured sources

Philosophy: Turn the entire open-source security community into our pattern library
"""

import asyncio
import os
import sys
import json
import requests
import yaml
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging
import hashlib
import re
from dataclasses import dataclass, asdict

# Add shared modules to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'shared'))

from shared.base import BaseEngine
from shared.logging import get_logger
from shared.security.credential_manager import get_siem_credentials

@dataclass
class CommunitySource:
    """Configuration for a community knowledge source"""
    source_id: str
    source_type: str  # 'github', 'mitre', 'feeds', 'custom_api'
    url: str
    description: str
    update_frequency: int  # hours
    last_updated: Optional[datetime] = None
    enabled: bool = True
    extraction_config: Dict[str, Any] = None
    metadata: Dict[str, Any] = None

@dataclass
class KnowledgeItem:
    """A piece of knowledge extracted from community sources"""
    item_id: str
    source_id: str
    knowledge_type: str  # 'log_pattern', 'use_case', 'mitre_technique', 'ioc_feed'
    title: str
    content: Dict[str, Any]
    confidence: float
    extracted_at: datetime
    source_url: str
    tags: List[str] = None
    mitre_techniques: List[str] = None
    metadata: Dict[str, Any] = None

class CommunityKnowledgeEngine(BaseEngine):
    """
    Community Knowledge Engine - Harvests open-source security intelligence

    Capabilities:
    1. GitHub repository scraping for log parsers and use cases
    2. MITRE ATT&CK framework updates
    3. Community threat intelligence feeds
    4. User-configurable source management
    5. Pattern validation and crystallization
    """

    def __init__(self):
        super().__init__('community_knowledge', '2.0.0')

        # Community sources
        self.sources: Dict[str, CommunitySource] = {}
        self.knowledge_cache: Dict[str, KnowledgeItem] = {}

        # GitHub API configuration
        self.github_token = None  # Will be loaded from credentials
        self.github_api_base = "https://api.github.com"

        # MITRE ATT&CK configuration
        self.mitre_api_base = "https://raw.githubusercontent.com/mitre/cti"
        self.mitre_version = "master"  # Can be updated to specific releases

        # Update tracking
        self.last_global_update = None
        self.update_in_progress = False

        self.logger.info("Community Knowledge Engine initialized")

    async def initialize(self):
        """Initialize the engine with default community sources"""

        # Load GitHub credentials if available
        try:
            github_creds = await get_siem_credentials('github')
            self.github_token = github_creds.get('GITHUB_TOKEN')
            if self.github_token:
                self.logger.info("GitHub API token loaded for enhanced rate limits")
        except Exception as e:
            self.logger.warning(f"No GitHub credentials found, using public API: {e}")

        # Register default community sources
        await self._register_default_sources()

        # Start background update scheduler
        asyncio.create_task(self._update_scheduler())

    async def _register_default_sources(self):
        """Register default high-value community sources"""

        default_sources = [
            # MITRE ATT&CK Framework (Official)
            CommunitySource(
                source_id='mitre_attack',
                source_type='mitre',
                url='https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json',
                description='Official MITRE ATT&CK Enterprise Framework',
                update_frequency=24,  # Daily updates
                extraction_config={
                    'extract_techniques': True,
                    'extract_tactics': True,
                    'extract_groups': True,
                    'extract_software': True
                }
            ),

            # Sigma Rules Repository (Community Log Patterns)
            CommunitySource(
                source_id='sigma_rules',
                source_type='github',
                url='https://api.github.com/repos/SigmaHQ/sigma/contents/rules',
                description='Community Sigma detection rules',
                update_frequency=12,  # Twice daily
                extraction_config={
                    'file_extensions': ['.yml', '.yaml'],
                    'extract_patterns': True,
                    'validate_sigma': True
                }
            ),

            # Elastic Security Rules
            CommunitySource(
                source_id='elastic_rules',
                source_type='github',
                url='https://api.github.com/repos/elastic/detection-rules/contents/rules',
                description='Elastic Security detection rules',
                update_frequency=12,
                extraction_config={
                    'file_extensions': ['.toml'],
                    'extract_patterns': True,
                    'vendor': 'elastic'
                }
            ),

            # Splunk Security Content
            CommunitySource(
                source_id='splunk_security',
                source_type='github',
                url='https://api.github.com/repos/splunk/security_content/contents/detections',
                description='Splunk security detection content',
                update_frequency=12,
                extraction_config={
                    'file_extensions': ['.yml'],
                    'extract_patterns': True,
                    'vendor': 'splunk'
                }
            ),

            # YARA Rules (Malware Detection)
            CommunitySource(
                source_id='yara_rules',
                source_type='github',
                url='https://api.github.com/repos/Yara-Rules/rules/contents',
                description='Community YARA malware detection rules',
                update_frequency=24,
                extraction_config={
                    'file_extensions': ['.yar', '.yara'],
                    'extract_patterns': True,
                    'rule_type': 'yara'
                }
            ),

            # OSSEC Rules (Host-based Detection)
            CommunitySource(
                source_id='ossec_rules',
                source_type='github',
                url='https://api.github.com/repos/ossec/ossec-hids/contents/etc/rules',
                description='OSSEC host-based detection rules',
                update_frequency=24,
                extraction_config={
                    'file_extensions': ['.xml'],
                    'extract_patterns': True,
                    'rule_type': 'ossec'
                }
            )
        ]

        for source in default_sources:
            self.sources[source.source_id] = source
            self.logger.info(f"Registered community source: {source.source_id}")

    async def add_user_source(self, source_config: Dict[str, Any]) -> bool:
        """
        Allow users to add custom community sources

        Args:
            source_config: User-provided source configuration

        Returns:
            True if source was added successfully
        """
        try:
            # Validate required fields
            required_fields = ['source_id', 'source_type', 'url', 'description']
            for field in required_fields:
                if field not in source_config:
                    raise ValueError(f"Missing required field: {field}")

            # Create source object
            source = CommunitySource(
                source_id=source_config['source_id'],
                source_type=source_config['source_type'],
                url=source_config['url'],
                description=source_config['description'],
                update_frequency=source_config.get('update_frequency', 24),
                extraction_config=source_config.get('extraction_config', {}),
                metadata={'user_added': True, 'added_at': datetime.utcnow().isoformat()}
            )

            # Validate source accessibility
            if await self._validate_source(source):
                self.sources[source.source_id] = source
                self.logger.info(f"Added user source: {source.source_id}")

                # Trigger immediate update for new source
                await self._update_source(source)
                return True
            else:
                raise ValueError("Source validation failed - URL not accessible")

        except Exception as e:
            self.logger.error(f"Failed to add user source: {e}")
            return False

    async def _validate_source(self, source: CommunitySource) -> bool:
        """Validate that a source is accessible and returns expected content"""
        try:
            headers = {}
            if source.source_type == 'github' and self.github_token:
                headers['Authorization'] = f'token {self.github_token}'

            async with aiohttp.ClientSession() as session:
                async with session.get(source.url, headers=headers) as response:
                    if response.status == 200:
                        content = await response.text()
                        # Basic validation - ensure we get expected content
                        return len(content) > 100  # Minimum content threshold
                    return False

        except Exception as e:
            self.logger.error(f"Source validation failed for {source.source_id}: {e}")
            return False

    async def _update_scheduler(self):
        """Background scheduler for updating community sources"""
        while True:
            try:
                if not self.update_in_progress:
                    self.update_in_progress = True
                    await self._update_all_sources()
                    self.update_in_progress = False

                # Sleep for 1 hour before next check
                await asyncio.sleep(3600)

            except Exception as e:
                self.logger.error(f"Update scheduler error: {e}")
                self.update_in_progress = False
                await asyncio.sleep(3600)

    async def _update_all_sources(self):
        """Update all community sources that are due for refresh"""

        current_time = datetime.utcnow()
        updated_count = 0

        for source_id, source in self.sources.items():
            if not source.enabled:
                continue

            # Check if update is due
            if (source.last_updated is None or
                current_time - source.last_updated >= timedelta(hours=source.update_frequency)):

                try:
                    await self._update_source(source)
                    source.last_updated = current_time
                    updated_count += 1

                except Exception as e:
                    self.logger.error(f"Failed to update source {source_id}: {e}")

        if updated_count > 0:
            self.logger.info(f"Updated {updated_count} community sources")

            # Submit new knowledge to Librarian for validation
            await self._submit_to_librarian()

    async def _update_source(self, source: CommunitySource):
        """Update a specific community source"""

        self.logger.info(f"Updating community source: {source.source_id}")

        if source.source_type == 'github':
            await self._update_github_source(source)
        elif source.source_type == 'mitre':
            await self._update_mitre_source(source)
        elif source.source_type == 'feeds':
            await self._update_feed_source(source)
        elif source.source_type == 'custom_api':
            await self._update_custom_api_source(source)
        else:
            self.logger.warning(f"Unknown source type: {source.source_type}")

    async def _update_github_source(self, source: CommunitySource):
        """Update a GitHub repository source"""

        headers = {'User-Agent': 'SIEMLess-Community-Engine/2.0'}
        if self.github_token:
            headers['Authorization'] = f'token {self.github_token}'

        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(source.url, headers=headers) as response:
                    if response.status != 200:
                        raise Exception(f"GitHub API returned {response.status}")

                    content = await response.json()

                    # Process repository contents
                    knowledge_items = []

                    if isinstance(content, list):
                        # Directory listing
                        for item in content:
                            if item['type'] == 'file':
                                file_knowledge = await self._extract_github_file(
                                    session, item, source, headers
                                )
                                if file_knowledge:
                                    knowledge_items.extend(file_knowledge)
                    else:
                        # Single file
                        file_knowledge = await self._extract_github_file(
                            session, content, source, headers
                        )
                        if file_knowledge:
                            knowledge_items.extend(file_knowledge)

                    # Store extracted knowledge
                    for item in knowledge_items:
                        self.knowledge_cache[item.item_id] = item

                    self.logger.info(f"Extracted {len(knowledge_items)} items from {source.source_id}")

        except Exception as e:
            self.logger.error(f"GitHub source update failed for {source.source_id}: {e}")

    async def _extract_github_file(self, session, file_info: Dict, source: CommunitySource, headers: Dict) -> List[KnowledgeItem]:
        """Extract knowledge from a GitHub file"""

        knowledge_items = []
        config = source.extraction_config or {}

        # Check file extension
        file_name = file_info['name']
        file_ext = Path(file_name).suffix.lower()

        allowed_extensions = config.get('file_extensions', ['.yml', '.yaml', '.json', '.toml'])
        if file_ext not in allowed_extensions:
            return knowledge_items

        try:
            # Download file content
            async with session.get(file_info['download_url'], headers=headers) as response:
                if response.status != 200:
                    return knowledge_items

                content = await response.text()

                # Parse based on file type
                if file_ext in ['.yml', '.yaml']:
                    parsed_content = yaml.safe_load(content)
                elif file_ext == '.json':
                    parsed_content = json.loads(content)
                elif file_ext == '.toml':
                    import tomllib
                    parsed_content = tomllib.loads(content)
                else:
                    parsed_content = {'raw_content': content}

                # Extract knowledge based on content type
                if config.get('extract_patterns', False):
                    knowledge_item = await self._extract_detection_pattern(
                        file_name, parsed_content, source, file_info['html_url']
                    )
                    if knowledge_item:
                        knowledge_items.append(knowledge_item)

        except Exception as e:
            self.logger.debug(f"Failed to extract from {file_name}: {e}")

        return knowledge_items

    async def _extract_detection_pattern(self, filename: str, content: Dict, source: CommunitySource, url: str) -> Optional[KnowledgeItem]:
        """Extract detection pattern from file content"""

        try:
            # Generate unique ID
            content_hash = hashlib.md5(json.dumps(content, sort_keys=True).encode()).hexdigest()
            item_id = f"{source.source_id}_{content_hash[:8]}"

            # Extract key information
            title = content.get('title', content.get('name', filename))
            description = content.get('description', '')

            # Extract MITRE techniques
            mitre_techniques = []
            if 'mitre' in content:
                mitre_data = content['mitre']
                if isinstance(mitre_data, dict):
                    technique = mitre_data.get('technique_id', mitre_data.get('technique'))
                    if technique:
                        mitre_techniques.append(technique)
                elif isinstance(mitre_data, list):
                    mitre_techniques.extend([t.get('technique_id', t) for t in mitre_data if t])

            # Determine confidence based on source reputation
            confidence = 0.8  # High confidence for established repos
            if source.source_id in ['sigma_rules', 'elastic_rules']:
                confidence = 0.9
            elif 'community' in source.description.lower():
                confidence = 0.7

            # Create knowledge item
            knowledge_item = KnowledgeItem(
                item_id=item_id,
                source_id=source.source_id,
                knowledge_type='detection_pattern',
                title=title,
                content={
                    'raw_pattern': content,
                    'pattern_type': source.extraction_config.get('rule_type', 'sigma'),
                    'description': description,
                    'filename': filename
                },
                confidence=confidence,
                extracted_at=datetime.utcnow(),
                source_url=url,
                tags=content.get('tags', []),
                mitre_techniques=mitre_techniques,
                metadata={
                    'source_repo': source.url,
                    'file_extension': Path(filename).suffix
                }
            )

            return knowledge_item

        except Exception as e:
            self.logger.error(f"Pattern extraction failed for {filename}: {e}")
            return None

    async def _update_mitre_source(self, source: CommunitySource):
        """Update MITRE ATT&CK framework data"""

        try:
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.get(source.url) as response:
                    if response.status != 200:
                        raise Exception(f"MITRE API returned {response.status}")

                    mitre_data = await response.json()

                    # Extract techniques, tactics, groups, software
                    knowledge_items = []

                    for obj in mitre_data.get('objects', []):
                        if obj.get('type') == 'attack-pattern':  # Techniques
                            knowledge_item = await self._extract_mitre_technique(obj, source)
                            if knowledge_item:
                                knowledge_items.append(knowledge_item)

                        elif obj.get('type') == 'intrusion-set':  # Groups
                            knowledge_item = await self._extract_mitre_group(obj, source)
                            if knowledge_item:
                                knowledge_items.append(knowledge_item)

                    # Store extracted knowledge
                    for item in knowledge_items:
                        self.knowledge_cache[item.item_id] = item

                    self.logger.info(f"Updated MITRE ATT&CK: {len(knowledge_items)} items")

        except Exception as e:
            self.logger.error(f"MITRE source update failed: {e}")

    async def _extract_mitre_technique(self, technique_data: Dict, source: CommunitySource) -> Optional[KnowledgeItem]:
        """Extract MITRE technique information"""

        try:
            external_refs = technique_data.get('external_references', [])
            mitre_ref = next((ref for ref in external_refs if ref.get('source_name') == 'mitre-attack'), None)

            if not mitre_ref:
                return None

            technique_id = mitre_ref.get('external_id', '')

            knowledge_item = KnowledgeItem(
                item_id=f"mitre_{technique_id}",
                source_id=source.source_id,
                knowledge_type='mitre_technique',
                title=f"{technique_id}: {technique_data.get('name', '')}",
                content={
                    'technique_id': technique_id,
                    'name': technique_data.get('name', ''),
                    'description': technique_data.get('description', ''),
                    'kill_chain_phases': technique_data.get('kill_chain_phases', []),
                    'platforms': technique_data.get('x_mitre_platforms', []),
                    'data_sources': technique_data.get('x_mitre_data_sources', [])
                },
                confidence=1.0,  # Official MITRE data
                extracted_at=datetime.utcnow(),
                source_url=mitre_ref.get('url', ''),
                mitre_techniques=[technique_id]
            )

            return knowledge_item

        except Exception as e:
            self.logger.error(f"MITRE technique extraction failed: {e}")
            return None

    async def _submit_to_librarian(self):
        """Submit new knowledge items to Librarian for validation and crystallization"""

        # Prepare knowledge for submission
        new_items = []
        for item_id, item in self.knowledge_cache.items():
            # Only submit items that haven't been validated yet
            if not item.metadata.get('submitted_to_librarian', False):
                new_items.append(item)
                item.metadata['submitted_to_librarian'] = True

        if not new_items:
            return

        # Submit to Librarian via message queue
        for item in new_items:
            message = {
                'type': 'pattern_submission',
                'source_engine': 'community_knowledge',
                'submission_data': {
                    'pattern_type': item.knowledge_type,
                    'pattern_content': item.content,
                    'confidence': item.confidence,
                    'source_info': {
                        'source_id': item.source_id,
                        'source_url': item.source_url,
                        'community_source': True
                    },
                    'mitre_techniques': item.mitre_techniques,
                    'metadata': item.metadata
                }
            }

            await self.send_message('librarian', message)

        self.logger.info(f"Submitted {len(new_items)} community knowledge items to Librarian")

    # BaseEngine abstract method implementations
    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Process incoming messages"""

        message_type = message.get('type')

        if message_type == 'add_source':
            # User wants to add a new community source
            source_config = message.get('source_config', {})
            success = await self.add_user_source(source_config)
            return {
                'status': 'success' if success else 'error',
                'message': 'Source added successfully' if success else 'Failed to add source'
            }

        elif message_type == 'list_sources':
            # Return available community sources
            sources_info = []
            for source_id, source in self.sources.items():
                sources_info.append({
                    'source_id': source_id,
                    'description': source.description,
                    'enabled': source.enabled,
                    'last_updated': source.last_updated.isoformat() if source.last_updated else None,
                    'knowledge_count': len([k for k in self.knowledge_cache.values() if k.source_id == source_id])
                })

            return {
                'status': 'success',
                'sources': sources_info
            }

        elif message_type == 'force_update':
            # Force update of specific source or all sources
            source_id = message.get('source_id')
            if source_id and source_id in self.sources:
                await self._update_source(self.sources[source_id])
                return {'status': 'success', 'message': f'Updated source: {source_id}'}
            else:
                await self._update_all_sources()
                return {'status': 'success', 'message': 'Updated all sources'}

        elif message_type == 'get_knowledge':
            # Return knowledge items with optional filtering
            knowledge_type = message.get('knowledge_type')
            source_id = message.get('source_id')

            filtered_items = []
            for item in self.knowledge_cache.values():
                if knowledge_type and item.knowledge_type != knowledge_type:
                    continue
                if source_id and item.source_id != source_id:
                    continue
                filtered_items.append(asdict(item))

            return {
                'status': 'success',
                'knowledge_items': filtered_items,
                'total_count': len(filtered_items)
            }

        return {'status': 'error', 'message': f'Unknown message type: {message_type}'}

    def get_capabilities(self) -> Dict[str, Any]:
        """Return engine capabilities"""
        return {
            'engine_type': 'community_knowledge',
            'capabilities': [
                'github_repository_scraping',
                'mitre_attack_updates',
                'community_pattern_harvesting',
                'user_configurable_sources',
                'automatic_pattern_validation',
                'knowledge_crystallization'
            ],
            'supported_sources': [
                'github',
                'mitre',
                'feeds',
                'custom_api'
            ],
            'knowledge_types': [
                'detection_pattern',
                'mitre_technique',
                'use_case',
                'ioc_feed',
                'log_parser'
            ]
        }

    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate engine configuration"""
        # Basic validation - can be enhanced
        return True


if __name__ == "__main__":
    engine = CommunityKnowledgeEngine()
    asyncio.run(engine.run())