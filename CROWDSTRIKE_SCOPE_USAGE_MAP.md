# CrowdStrike API Scope Usage Map

## Overview
We have 7 CrowdStrike API scopes. Each scope serves a **specific purpose** in our architecture to avoid confusion, duplication, and loops.

---

## 1. **INTEL_READ** (`intel:read`)
**Purpose:** CTI (Cyber Threat Intelligence) Feed
**Used By:** CTI Manager → Backend Engine → Rule Generation
**Integration:** `crowdstrike_cti_integration.py`

### What It Does:
- Fetches threat intelligence indicators (IPs, domains, file hashes)
- Threat actor information and malware families
- Published threat intelligence reports from CrowdStrike

### Data Flow:
```
Ingestion (CTI Manager)
  → Backend (store as IOCs in entities table)
  → Intelligence (generate detection rules)
  → Delivery (show in CTI feed widget)
```

### API Endpoints:
- `/intel/queries/indicators/v1` - Query indicators
- `/intel/entities/indicators/GET/v1` - Get indicator details
- `/intel/queries/reports/v1` - Query reports

### Use Case:
"What are the latest malicious IPs from CrowdStrike's threat intel?"

---

## 2. **IOCS_READ** (`iocs:read`)
**Purpose:** CTI (Cyber Threat Intelligence) Feed - CUSTOM IOCs
**Used By:** CTI Manager → Backend Engine → Rule Generation
**Integration:** `crowdstrike_cti_integration.py`

### What It Does:
- Fetches **customer-specific** IOCs (your org's custom indicators)
- Different from INTEL_READ which is CrowdStrike's public threat intel
- Your security team's custom blocklists

### Data Flow:
```
Ingestion (CTI Manager)
  → Backend (store as IOCs in entities table)
  → Intelligence (generate detection rules)
  → Delivery (show in CTI feed widget)
```

### API Endpoints:
- `/indicators/queries/iocs/v1` - Query custom IOCs
- `/indicators/entities/iocs/v1` - Get IOC details

### Use Case:
"What custom IOCs has our team added to CrowdStrike?"

---

## 3. **HOSTS_READ** (`hosts:read`)
**Purpose:** INVESTIGATION CONTEXT - Asset Inventory
**Used By:** Investigation Context System (On-Demand)
**Integration:** `ingestion_engine._pull_crowdstrike_host()` (NEW)

### What It Does:
- Queries device/host information by IP address
- Returns hostname, OS version, last seen, user, domain
- Asset inventory lookup for investigation

### Data Flow:
```
Delivery (analyst clicks alert with IP *************)
  → Redis: ingestion.pull_context
  → Ingestion (query CrowdStrike Hosts API)
  → Contextualization (extract hostname, user, OS)
  → Backend (store enriched entities)
  → Delivery (display on investigation screen)
```

### API Endpoints:
- `/devices/queries/devices-scroll/v1` - Query devices by IP/hostname
- `/devices/entities/devices/v1` - Get device details

### Use Case:
"What device is *************? Who owns it? What OS?"

---

## 4. **ALERTS_READ** (`alerts:read`)
**Purpose:** CORRELATION - CrowdStrike Alert Correlation
**Used By:** Correlation Engine (Future)
**Integration:** NOT YET IMPLEMENTED

### What It Does:
- Fetches CrowdStrike Falcon alerts
- Cross-correlate with Elastic alerts
- Show if CrowdStrike also saw suspicious activity

### Data Flow (Planned):
```
Delivery (analyst investigating alert)
  → Backend (correlation.check_crowdstrike_alerts)
  → Ingestion (query CrowdStrike Alerts API)
  → Contextualization (merge with Elastic alert)
  → Backend (create correlation link)
  → Delivery (display: "CrowdStrike also detected X")
```

### API Endpoints:
- `/alerts/queries/alerts/v1` - Query alerts
- `/alerts/entities/alerts/v1` - Get alert details

### Use Case:
"Did CrowdStrike Falcon also trigger an alert for this activity?"

---

## 5. **DETECTIONS_READ** (`detections:read`)
**Purpose:** CORRELATION - EDR Detection Correlation
**Used By:** Correlation Engine (Future)
**Integration:** NOT YET IMPLEMENTED

### What It Does:
- Fetches CrowdStrike Falcon **detections** (more detailed than alerts)
- Includes MITRE ATT&CK techniques, command lines, process trees
- Enriches investigation with EDR telemetry

### Data Flow (Planned):
```
Delivery (analyst investigating process execution)
  → Backend (correlation.get_crowdstrike_detection)
  → Ingestion (query CrowdStrike Detections API)
  → Contextualization (extract process tree, command line)
  → Backend (enrich alert with EDR data)
  → Delivery (display full process genealogy)
```

### API Endpoints:
- `/detects/queries/detects/v1` - Query detections
- `/detects/entities/summaries/GET/v1` - Get detection details

### Use Case:
"Show me the full process tree and MITRE techniques from CrowdStrike EDR"

---

## 6. **EVENT_STREAMS_READ** (`event-streams:read`)
**Purpose:** LOG INGESTION - Real-Time EDR Telemetry
**Used By:** Normal Log Ingestion Pipeline (Future)
**Integration:** NOT YET IMPLEMENTED

### What It Does:
- Real-time stream of EDR events (process execution, network, file, registry)
- Replaces polling - continuous event feed
- Most detailed telemetry available

### Data Flow (Planned):
```
Ingestion (subscribe to event stream)
  → Contextualization (extract entities from each event)
  → Backend (store intelligence)
  → Delivery (queryable for investigations)
```

### API Endpoints:
- `/sensors/entities/datafeed/v2` - Event stream subscription
- `/sensors/entities/datafeed-actions/v1` - Stream management

### Use Case:
"Continuously ingest all CrowdStrike EDR events into SIEMLess"

---

## 7. **INCIDENTS_READ** (`incidents:read`)
**Purpose:** CORRELATION - Incident Correlation
**Used By:** Correlation Engine (Future)
**Integration:** NOT YET IMPLEMENTED

### What It Does:
- Fetches CrowdStrike Falcon **incidents** (grouped detections)
- Shows if activity is part of a larger incident CrowdStrike identified
- Incident state, severity, tactics, objectives

### Data Flow (Planned):
```
Delivery (analyst investigating alert)
  → Backend (correlation.check_crowdstrike_incident)
  → Ingestion (query CrowdStrike Incidents API)
  → Contextualization (link alert to incident)
  → Backend (create incident relationship)
  → Delivery (display: "Part of Incident #12345 - Ransomware Campaign")
```

### API Endpoints:
- `/incidents/queries/incidents/v1` - Query incidents
- `/incidents/entities/incidents/GET/v1` - Get incident details

### Use Case:
"Is this alert part of a larger CrowdStrike incident?"

---

## Architecture Summary

### Current Implementation (✅ Working):
1. **INTEL_READ** - CTI feed for threat intelligence indicators
2. **IOCS_READ** - CTI feed for custom IOCs
3. **HOSTS_READ** - Investigation context for asset lookup (JUST ADDED)

### Next Priority (🔄 In Progress):
4. **HOSTS_READ** - Complete implementation with Contextualization → Backend → Delivery

### Future Implementation (⏳ Planned):
5. **ALERTS_READ** - Correlation with CrowdStrike alerts
6. **DETECTIONS_READ** - Correlation with detailed EDR detections
7. **EVENT_STREAMS_READ** - Real-time EDR log ingestion
8. **INCIDENTS_READ** - Correlation with CrowdStrike incidents

---

## Critical Rules to Avoid Confusion

### Rule 1: CTI vs Context vs Correlation
- **CTI (INTEL + IOCS)**: Background feed → stored in entities → used for rule generation
- **Context (HOSTS)**: On-demand lookup → enriches specific investigation → not continuously polled
- **Correlation (ALERTS + DETECTIONS + INCIDENTS)**: Cross-reference → links our alerts with CrowdStrike's

### Rule 2: No Loops
- CTI Manager pulls INTEL/IOCS → Backend stores → Never loops back to CTI Manager
- Investigation pulls HOSTS → Contextualization extracts → Backend stores → Delivery displays → Done
- Correlation checks ALERTS/DETECTIONS → Backend links → Delivery shows → No recursive queries

### Rule 3: Clear Triggers
- **CTI**: Time-based (hourly/daily background updates)
- **Context**: User-triggered (analyst clicks alert)
- **Correlation**: Event-triggered (new alert arrives, check if CrowdStrike has related data)

---

## Environment Variables Needed

```bash
# All CrowdStrike scopes use same auth
CROWDSTRIKE_CLIENT_ID=your_client_id
CROWDSTRIKE_CLIENT_SECRET=your_client_secret

# No separate credentials needed - scope access controlled by API key permissions
```

---

## Integration File Map

| Scope | Purpose | File | Status |
|-------|---------|------|--------|
| INTEL_READ | CTI | `crowdstrike_cti_integration.py` | ✅ Working |
| IOCS_READ | CTI | `crowdstrike_cti_integration.py` | ✅ Working |
| HOSTS_READ | Context | `ingestion_engine.py` (new method) | 🔄 In Progress |
| ALERTS_READ | Correlation | Not yet created | ⏳ Planned |
| DETECTIONS_READ | Correlation | Not yet created | ⏳ Planned |
| EVENT_STREAMS_READ | Ingestion | Not yet created | ⏳ Planned |
| INCIDENTS_READ | Correlation | Not yet created | ⏳ Planned |

---

## Current Task: Complete HOSTS_READ Integration

We're implementing the investigation context system that uses HOSTS_READ to answer:
"What device is this IP address, and what context do we have about it?"

This is DIFFERENT from:
- CTI feeds (INTEL/IOCS) - Those run in background continuously
- Normal log ingestion - That pulls security events, not asset inventory

We're building an **on-demand asset lookup** system triggered when analysts click alerts.
