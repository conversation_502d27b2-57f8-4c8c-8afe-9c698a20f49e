# Delivery Engine - Complete Implementation Guide

## Table of Contents
1. [Overview](#overview)
2. [Case Management System](#case-management-system)
3. [Alert Orchestration](#alert-orchestration)
4. [Dashboard & Visualization](#dashboard--visualization)
5. [Notification Channels](#notification-channels)
6. [Workflow Automation](#workflow-automation)
7. [Integration Patterns](#integration-patterns)
8. [Frontend Implementation](#frontend-implementation)

---

## Overview

The Delivery Engine is the **action and presentation layer** of SIEMLess. It transforms intelligence into outcomes by:
- Creating and managing security cases
- Orchestrating response workflows
- Delivering alerts through appropriate channels
- Providing visualizations and dashboards
- Automating remediation actions

### Core Responsibilities

```python
DELIVERY_ENGINE_FUNCTIONS = {
    'case_management': 'Track investigations from detection to resolution',
    'alert_routing': 'Send right alerts to right people at right time',
    'visualization': 'Present intelligence in consumable format',
    'automation': 'Execute response actions automatically',
    'reporting': 'Generate compliance and executive reports',
    'integration': 'Connect with ticketing, chat, and SOAR platforms'
}
```

---

## Case Management System

### 1. Case Lifecycle Management

#### Case Creation
```python
class CaseManager:
    def create_case(self, alert_data):
        """Create a new security case from alert"""
        case = {
            'case_id': self.generate_case_id(),
            'title': self.generate_title(alert_data),
            'severity': self.calculate_severity(alert_data),
            'priority': self.determine_priority(alert_data),
            'status': 'NEW',
            'created_at': datetime.utcnow(),
            'assigned_to': self.auto_assign(alert_data),
            'sla': self.calculate_sla(alert_data),
            'entities': alert_data['entities'],
            'evidence': [],
            'timeline': [],
            'actions_taken': [],
            'related_cases': self.find_related_cases(alert_data)
        }

        # Enrich case with context
        case['context'] = {
            'affected_assets': self.identify_affected_assets(alert_data),
            'business_impact': self.assess_business_impact(alert_data),
            'attack_stage': self.determine_attack_stage(alert_data),
            'mitre_mapping': self.map_to_mitre(alert_data)
        }

        # Create initial timeline entry
        case['timeline'].append({
            'timestamp': datetime.utcnow(),
            'event': 'Case created',
            'actor': 'System',
            'details': f"Case created from alert {alert_data['alert_id']}"
        })

        return case

    def calculate_severity(self, alert_data):
        """Calculate case severity based on multiple factors"""
        severity_score = 0

        # Factor 1: Alert severity
        severity_map = {'critical': 40, 'high': 30, 'medium': 20, 'low': 10}
        severity_score += severity_map.get(alert_data['severity'], 0)

        # Factor 2: Asset criticality
        if alert_data.get('asset_criticality') == 'business_critical':
            severity_score += 30

        # Factor 3: Data sensitivity
        if alert_data.get('data_classification') in ['confidential', 'restricted']:
            severity_score += 20

        # Factor 4: Threat actor sophistication
        if alert_data.get('threat_actor_tier') in ['apt', 'organized_crime']:
            severity_score += 10

        # Map score to severity level
        if severity_score >= 70:
            return 'CRITICAL'
        elif severity_score >= 50:
            return 'HIGH'
        elif severity_score >= 30:
            return 'MEDIUM'
        else:
            return 'LOW'
```

#### Case Assignment Logic
```python
def auto_assign(self, alert_data):
    """Intelligently assign cases to analysts"""

    # Check for specific expertise requirements
    if 'malware' in alert_data['tags']:
        return self.get_available_analyst('malware_specialist')
    elif 'insider_threat' in alert_data['tags']:
        return self.get_available_analyst('insider_threat')
    elif alert_data['severity'] == 'CRITICAL':
        return self.get_available_analyst('tier3')

    # Load balancing for general cases
    workload = self.get_analyst_workload()

    # Find analyst with lowest workload
    available_analysts = [a for a in workload if a['status'] == 'available']
    if available_analysts:
        return min(available_analysts, key=lambda x: x['active_cases'])['analyst_id']

    # Escalate if no one available
    return 'soc_manager'
```

### 2. Case Investigation Workflow

```python
class InvestigationWorkflow:
    def __init__(self):
        self.stages = [
            'initial_triage',
            'evidence_collection',
            'analysis',
            'containment',
            'eradication',
            'recovery',
            'lessons_learned'
        ]

    def execute_stage(self, case, stage):
        """Execute specific investigation stage"""
        if stage == 'initial_triage':
            return self.initial_triage(case)
        elif stage == 'evidence_collection':
            return self.collect_evidence(case)
        elif stage == 'analysis':
            return self.analyze_evidence(case)
        # ... other stages

    def initial_triage(self, case):
        """Perform initial case triage"""
        triage_results = {
            'is_true_positive': None,
            'requires_escalation': False,
            'immediate_actions': [],
            'investigation_plan': []
        }

        # Automated checks
        checks = [
            self.verify_ioc_reputation(case),
            self.check_user_legitimacy(case),
            self.validate_against_baseline(case),
            self.correlate_with_threat_intel(case)
        ]

        # Determine if true positive
        confidence = sum(check['confidence'] for check in checks) / len(checks)
        triage_results['is_true_positive'] = confidence > 0.7

        # Determine if escalation needed
        if case['severity'] == 'CRITICAL' or case['context']['asset_criticality'] == 'business_critical':
            triage_results['requires_escalation'] = True

        # Suggest immediate actions
        if triage_results['is_true_positive']:
            triage_results['immediate_actions'] = [
                'Isolate affected systems',
                'Disable compromised accounts',
                'Block malicious IPs at firewall'
            ]

        return triage_results
```

### 3. Evidence Management

```python
class EvidenceCollector:
    def collect_evidence(self, case):
        """Automatically collect and preserve evidence"""
        evidence = {
            'logs': [],
            'artifacts': [],
            'network_captures': [],
            'memory_dumps': [],
            'screenshots': [],
            'chain_of_custody': []
        }

        # Collect relevant logs
        time_window = self.calculate_time_window(case)
        evidence['logs'] = self.collect_logs(
            case['entities'],
            time_window['start'],
            time_window['end']
        )

        # Collect artifacts from endpoints
        for host in case['context']['affected_assets']:
            evidence['artifacts'].extend(
                self.collect_host_artifacts(host)
            )

        # Capture network traffic if ongoing
        if case['status'] == 'ACTIVE_INCIDENT':
            evidence['network_captures'] = self.start_packet_capture(
                case['entities']
            )

        # Document chain of custody
        evidence['chain_of_custody'].append({
            'timestamp': datetime.utcnow(),
            'action': 'Evidence collected',
            'collector': 'Automated System',
            'hash': self.calculate_evidence_hash(evidence)
        })

        return evidence

    def collect_host_artifacts(self, hostname):
        """Collect artifacts from specific host"""
        artifacts = []

        # Define what to collect based on OS
        if self.is_windows(hostname):
            artifact_paths = [
                'C:\\Windows\\Prefetch\\*',
                'C:\\Windows\\System32\\winevt\\Logs\\*',
                'HKEY_LOCAL_MACHINE\\Software\\Microsoft\\Windows\\CurrentVersion\\Run',
                'C:\\Users\\<USER>\\AppData\\Local\\Temp\\*'
            ]
        else:
            artifact_paths = [
                '/var/log/*',
                '/tmp/*',
                '/home/<USER>/.bash_history',
                '/etc/crontab'
            ]

        for path in artifact_paths:
            artifacts.append({
                'path': path,
                'hostname': hostname,
                'collected_at': datetime.utcnow(),
                'data': self.retrieve_artifact(hostname, path)
            })

        return artifacts
```

---

## Alert Orchestration

### 1. Intelligent Alert Routing

```python
class AlertRouter:
    def __init__(self):
        self.routing_rules = self.load_routing_rules()
        self.escalation_matrix = self.load_escalation_matrix()

    def route_alert(self, alert):
        """Route alert based on context and rules"""
        routing_decision = {
            'recipients': [],
            'channels': [],
            'priority': 'normal',
            'require_ack': False
        }

        # Determine recipients based on alert type
        if alert['severity'] == 'CRITICAL':
            routing_decision['recipients'].extend([
                'soc_manager',
                'on_call_engineer',
                'ciso'
            ])
            routing_decision['channels'] = ['phone', 'sms', 'email', 'slack']
            routing_decision['priority'] = 'immediate'
            routing_decision['require_ack'] = True

        elif 'data_breach' in alert['tags']:
            routing_decision['recipients'].extend([
                'data_protection_officer',
                'legal_team',
                'ciso',
                'public_relations'
            ])
            routing_decision['channels'] = ['email', 'dashboard', 'teams']
            routing_decision['priority'] = 'high'

        elif alert['category'] == 'compliance':
            routing_decision['recipients'].extend([
                'compliance_team',
                'audit_team'
            ])
            routing_decision['channels'] = ['email', 'dashboard']
            routing_decision['priority'] = 'normal'

        else:
            # Standard routing
            routing_decision['recipients'] = ['soc_queue']
            routing_decision['channels'] = ['dashboard']
            routing_decision['priority'] = 'normal'

        # Apply custom routing rules
        for rule in self.routing_rules:
            if self.rule_matches(alert, rule):
                routing_decision = self.apply_rule(routing_decision, rule)

        return routing_decision

    def rule_matches(self, alert, rule):
        """Check if alert matches routing rule"""
        for condition in rule['conditions']:
            field = condition['field']
            operator = condition['operator']
            value = condition['value']

            alert_value = alert.get(field)

            if operator == 'equals' and alert_value != value:
                return False
            elif operator == 'contains' and value not in alert_value:
                return False
            elif operator == 'greater_than' and alert_value <= value:
                return False

        return True
```

### 2. Alert Aggregation & Deduplication

```python
class AlertAggregator:
    def __init__(self):
        self.alert_buffer = []
        self.aggregation_window = 60  # seconds

    def process_alert(self, alert):
        """Process and potentially aggregate alert"""

        # Check for similar alerts
        similar_alerts = self.find_similar_alerts(alert)

        if similar_alerts:
            # Aggregate instead of creating new alert
            aggregated = self.aggregate_alerts(similar_alerts + [alert])
            return self.create_aggregated_alert(aggregated)
        else:
            # New unique alert
            self.alert_buffer.append(alert)
            return alert

    def find_similar_alerts(self, alert):
        """Find alerts that should be aggregated"""
        similar = []

        for buffered_alert in self.alert_buffer:
            # Check if within time window
            time_diff = (alert['timestamp'] - buffered_alert['timestamp']).seconds
            if time_diff > self.aggregation_window:
                continue

            # Check similarity criteria
            similarity_score = self.calculate_similarity(alert, buffered_alert)
            if similarity_score > 0.8:
                similar.append(buffered_alert)

        return similar

    def calculate_similarity(self, alert1, alert2):
        """Calculate similarity score between alerts"""
        score = 0
        weights = {
            'source_ip': 0.3,
            'destination_ip': 0.3,
            'event_type': 0.2,
            'user': 0.1,
            'host': 0.1
        }

        for field, weight in weights.items():
            if alert1.get(field) == alert2.get(field):
                score += weight

        return score

    def create_aggregated_alert(self, alerts):
        """Create single alert from multiple similar alerts"""
        return {
            'type': 'aggregated',
            'count': len(alerts),
            'first_seen': min(a['timestamp'] for a in alerts),
            'last_seen': max(a['timestamp'] for a in alerts),
            'severity': max(a['severity'] for a in alerts),
            'unique_sources': list(set(a.get('source_ip') for a in alerts if a.get('source_ip'))),
            'unique_targets': list(set(a.get('destination_ip') for a in alerts if a.get('destination_ip'))),
            'alert_ids': [a['alert_id'] for a in alerts]
        }
```

---

## Dashboard & Visualization

### 1. Executive Dashboard

```python
class ExecutiveDashboard:
    def generate_metrics(self):
        """Generate executive-level security metrics"""
        return {
            'security_posture': {
                'overall_score': self.calculate_security_score(),
                'trend': self.calculate_trend('7d'),
                'risk_areas': self.identify_risk_areas()
            },
            'incident_metrics': {
                'active_incidents': self.count_active_incidents(),
                'mttr': self.calculate_mttr(),
                'incidents_by_severity': self.group_by_severity(),
                'top_threat_actors': self.get_top_threat_actors()
            },
            'coverage_metrics': {
                'mitre_coverage': self.calculate_mitre_coverage(),
                'asset_visibility': self.calculate_asset_visibility(),
                'detection_gaps': self.identify_detection_gaps()
            },
            'business_impact': {
                'prevented_loss': self.calculate_prevented_loss(),
                'downtime_avoided': self.calculate_downtime_avoided(),
                'compliance_status': self.get_compliance_status()
            }
        }

    def calculate_security_score(self):
        """Calculate overall security posture score"""
        factors = {
            'patch_compliance': self.get_patch_compliance_rate() * 20,
            'detection_coverage': self.get_detection_coverage() * 25,
            'incident_response': self.get_incident_response_score() * 20,
            'vulnerability_management': self.get_vuln_management_score() * 15,
            'user_training': self.get_training_completion_rate() * 10,
            'configuration_compliance': self.get_config_compliance() * 10
        }

        return sum(factors.values())
```

### 2. SOC Operations Dashboard

```python
class SOCDashboard:
    def generate_operational_view(self):
        """Generate SOC operational dashboard"""
        return {
            'queue_status': {
                'new_cases': self.count_new_cases(),
                'in_progress': self.count_in_progress(),
                'pending_closure': self.count_pending_closure(),
                'sla_at_risk': self.identify_sla_risks()
            },
            'analyst_workload': {
                'by_analyst': self.get_workload_by_analyst(),
                'by_shift': self.get_workload_by_shift(),
                'skill_distribution': self.get_skill_distribution()
            },
            'real_time_activity': {
                'active_threats': self.get_active_threats(),
                'ongoing_attacks': self.get_ongoing_attacks(),
                'suspicious_behaviors': self.get_suspicious_behaviors()
            },
            'performance_metrics': {
                'alerts_processed': self.count_processed_alerts('24h'),
                'false_positive_rate': self.calculate_fp_rate(),
                'automation_rate': self.calculate_automation_rate(),
                'detection_accuracy': self.calculate_detection_accuracy()
            }
        }
```

### 3. Threat Hunting Dashboard

```python
class ThreatHuntingDashboard:
    def generate_hunt_view(self):
        """Generate threat hunting dashboard"""
        return {
            'active_hunts': {
                'campaigns': self.get_active_campaigns(),
                'hypotheses': self.get_hunt_hypotheses(),
                'findings': self.get_recent_findings()
            },
            'threat_landscape': {
                'trending_ttps': self.get_trending_ttps(),
                'emerging_threats': self.get_emerging_threats(),
                'threat_actor_activity': self.get_actor_activity()
            },
            'hunt_analytics': {
                'coverage_map': self.generate_coverage_map(),
                'detection_opportunities': self.find_detection_gaps(),
                'high_value_targets': self.identify_hvt()
            },
            'ioc_tracking': {
                'active_iocs': self.count_active_iocs(),
                'ioc_hits': self.get_ioc_hits('7d'),
                'expired_iocs': self.get_expired_iocs()
            }
        }
```

---

## Notification Channels

### 1. Multi-Channel Notification System

```python
class NotificationManager:
    def __init__(self):
        self.channels = {
            'email': EmailChannel(),
            'slack': SlackChannel(),
            'teams': TeamsChannel(),
            'sms': SMSChannel(),
            'phone': PhoneChannel(),
            'webhook': WebhookChannel(),
            'dashboard': DashboardChannel()
        }

    def send_notification(self, alert, routing_decision):
        """Send notifications through specified channels"""
        results = []

        for channel_name in routing_decision['channels']:
            channel = self.channels.get(channel_name)
            if channel:
                # Format message for channel
                message = self.format_for_channel(alert, channel_name)

                # Send notification
                result = channel.send(
                    recipients=routing_decision['recipients'],
                    message=message,
                    priority=routing_decision['priority']
                )

                results.append({
                    'channel': channel_name,
                    'status': result['status'],
                    'delivered_to': result.get('delivered_to', [])
                })

                # Handle acknowledgment requirement
                if routing_decision['require_ack']:
                    self.track_acknowledgment(alert['alert_id'], channel_name)

        return results

    def format_for_channel(self, alert, channel):
        """Format alert message for specific channel"""
        if channel == 'email':
            return self.format_email(alert)
        elif channel == 'slack':
            return self.format_slack(alert)
        elif channel == 'sms':
            return self.format_sms(alert)
        # ... other channels
```

### 2. Channel Implementations

```python
class SlackChannel:
    def send(self, recipients, message, priority):
        """Send alert via Slack"""
        webhook_url = self.get_webhook_url()

        # Build Slack message with blocks
        slack_message = {
            'text': message['summary'],
            'blocks': [
                {
                    'type': 'header',
                    'text': {
                        'type': 'plain_text',
                        'text': message['title']
                    }
                },
                {
                    'type': 'section',
                    'fields': [
                        {'type': 'mrkdwn', 'text': f'*Severity:* {message["severity"]}'},
                        {'type': 'mrkdwn', 'text': f'*Time:* {message["timestamp"]}'},
                        {'type': 'mrkdwn', 'text': f'*Case ID:* {message["case_id"]}'},
                        {'type': 'mrkdwn', 'text': f'*Affected:* {message["affected_assets"]}'}
                    ]
                },
                {
                    'type': 'section',
                    'text': {
                        'type': 'mrkdwn',
                        'text': message['details']
                    }
                },
                {
                    'type': 'actions',
                    'elements': [
                        {
                            'type': 'button',
                            'text': {'type': 'plain_text', 'text': 'View Case'},
                            'url': message['case_url']
                        },
                        {
                            'type': 'button',
                            'text': {'type': 'plain_text', 'text': 'Acknowledge'},
                            'action_id': 'acknowledge_alert',
                            'value': message['alert_id']
                        }
                    ]
                }
            ]
        }

        # Add @mentions for critical alerts
        if priority == 'immediate':
            slack_message['text'] = f"<!channel> {slack_message['text']}"

        # Send to Slack
        response = requests.post(webhook_url, json=slack_message)

        return {
            'status': 'success' if response.status_code == 200 else 'failed',
            'delivered_to': recipients
        }
```

---

## Workflow Automation

### 1. Response Playbooks

```python
class PlaybookEngine:
    def __init__(self):
        self.playbooks = self.load_playbooks()

    def execute_playbook(self, case, playbook_name):
        """Execute automated response playbook"""
        playbook = self.playbooks.get(playbook_name)
        if not playbook:
            return {'error': 'Playbook not found'}

        execution = {
            'playbook': playbook_name,
            'case_id': case['case_id'],
            'started_at': datetime.utcnow(),
            'steps': [],
            'status': 'running'
        }

        for step in playbook['steps']:
            try:
                # Check conditions
                if not self.check_conditions(case, step.get('conditions', [])):
                    continue

                # Execute action
                result = self.execute_action(step['action'], case)

                execution['steps'].append({
                    'step_name': step['name'],
                    'action': step['action'],
                    'result': result,
                    'executed_at': datetime.utcnow()
                })

                # Check for branching
                if result.get('branch'):
                    next_steps = step.get('branches', {}).get(result['branch'], [])
                    playbook['steps'].extend(next_steps)

            except Exception as e:
                execution['steps'].append({
                    'step_name': step['name'],
                    'error': str(e),
                    'failed_at': datetime.utcnow()
                })

                if step.get('critical', False):
                    execution['status'] = 'failed'
                    break

        execution['completed_at'] = datetime.utcnow()
        execution['status'] = 'completed' if execution['status'] != 'failed' else 'failed'

        return execution

    def execute_action(self, action, case):
        """Execute specific playbook action"""
        action_type = action['type']

        if action_type == 'isolate_host':
            return self.isolate_host(action['parameters']['hostname'])
        elif action_type == 'disable_account':
            return self.disable_account(action['parameters']['username'])
        elif action_type == 'block_ip':
            return self.block_ip(action['parameters']['ip_address'])
        elif action_type == 'collect_forensics':
            return self.collect_forensics(action['parameters'])
        elif action_type == 'run_query':
            return self.run_query(action['parameters']['query'])
        # ... more actions
```

### 2. Automated Remediation

```python
class RemediationEngine:
    def auto_remediate(self, case):
        """Automatically remediate based on case type"""
        remediation_actions = []

        # Determine remediation strategy
        if case['category'] == 'malware':
            remediation_actions = [
                {'action': 'isolate_infected_hosts', 'params': case['affected_assets']},
                {'action': 'kill_malicious_processes', 'params': case['malicious_processes']},
                {'action': 'quarantine_files', 'params': case['malicious_files']},
                {'action': 'block_c2_communications', 'params': case['c2_indicators']},
                {'action': 'force_password_reset', 'params': case['compromised_accounts']}
            ]

        elif case['category'] == 'data_exfiltration':
            remediation_actions = [
                {'action': 'block_outbound_traffic', 'params': case['exfil_destinations']},
                {'action': 'revoke_access', 'params': case['compromised_accounts']},
                {'action': 'enable_dlp_rules', 'params': case['sensitive_data']},
                {'action': 'notify_legal', 'params': case['data_classification']}
            ]

        elif case['category'] == 'account_compromise':
            remediation_actions = [
                {'action': 'disable_account', 'params': case['compromised_account']},
                {'action': 'terminate_sessions', 'params': case['active_sessions']},
                {'action': 'reset_password', 'params': case['compromised_account']},
                {'action': 'revoke_tokens', 'params': case['api_tokens']},
                {'action': 'enable_mfa', 'params': case['compromised_account']}
            ]

        # Execute remediation actions
        results = []
        for action in remediation_actions:
            result = self.execute_remediation(action)
            results.append(result)

            # Stop if critical action fails
            if not result['success'] and action.get('critical', False):
                break

        return results
```

---

## Integration Patterns

### 1. SOAR Integration

```python
class SOARIntegration:
    def __init__(self, soar_platform):
        self.platform = soar_platform
        self.api_client = self.initialize_client()

    def create_soar_incident(self, case):
        """Create incident in SOAR platform"""
        incident = {
            'name': case['title'],
            'severity': self.map_severity(case['severity']),
            'status': 'new',
            'type': self.map_incident_type(case['category']),
            'description': case['description'],
            'artifacts': self.convert_entities_to_artifacts(case['entities']),
            'custom_fields': {
                'siemless_case_id': case['case_id'],
                'detection_source': 'SIEMLess',
                'kill_chain_stage': case['attack_stage']
            }
        }

        # Create incident
        response = self.api_client.create_incident(incident)

        # Add tasks
        for task in self.generate_tasks(case):
            self.api_client.add_task(response['incident_id'], task)

        # Trigger playbook
        if case['severity'] in ['CRITICAL', 'HIGH']:
            self.api_client.run_playbook(
                response['incident_id'],
                self.select_playbook(case)
            )

        return response
```

### 2. Ticketing System Integration

```python
class TicketingIntegration:
    def create_ticket(self, case):
        """Create ticket in ticketing system"""
        ticket = {
            'title': f"[Security] {case['title']}",
            'priority': self.map_priority(case['priority']),
            'category': 'Security Incident',
            'description': self.format_description(case),
            'assignee': case['assigned_to'],
            'watchers': self.determine_watchers(case),
            'custom_fields': {
                'security_severity': case['severity'],
                'affected_systems': ', '.join(case['affected_assets']),
                'mitre_techniques': ', '.join(case['mitre_mapping'])
            }
        }

        # Add attachments
        for evidence in case['evidence']:
            ticket['attachments'].append({
                'filename': evidence['name'],
                'content': evidence['data']
            })

        return self.ticket_api.create(ticket)
```

---

## Frontend Implementation

### 1. React Components Structure

```javascript
// Main Dashboard Component
const SecurityDashboard = () => {
    const [cases, setCases] = useState([]);
    const [alerts, setAlerts] = useState([]);
    const [metrics, setMetrics] = useState({});

    useEffect(() => {
        // Connect to WebSocket for real-time updates
        const ws = new WebSocket('ws://localhost:8005/ws');

        ws.onmessage = (event) => {
            const data = JSON.parse(event.data);

            switch(data.type) {
                case 'new_alert':
                    setAlerts(prev => [data.alert, ...prev]);
                    break;
                case 'case_update':
                    updateCase(data.case);
                    break;
                case 'metrics_update':
                    setMetrics(data.metrics);
                    break;
            }
        };

        // Initial data load
        fetchCases();
        fetchAlerts();
        fetchMetrics();

        return () => ws.close();
    }, []);

    return (
        <div className="dashboard">
            <MetricsBar metrics={metrics} />
            <div className="content-grid">
                <AlertQueue alerts={alerts} />
                <CaseList cases={cases} />
                <ThreatMap />
            </div>
        </div>
    );
};

// Case Management Component
const CaseView = ({ caseId }) => {
    const [caseData, setCaseData] = useState(null);
    const [timeline, setTimeline] = useState([]);
    const [actions, setActions] = useState([]);

    const handleAction = async (action) => {
        const response = await api.executeAction(caseId, action);
        if (response.success) {
            addToTimeline(action, response);
            refreshCase();
        }
    };

    return (
        <div className="case-view">
            <CaseHeader case={caseData} />
            <div className="case-content">
                <EntityGraph entities={caseData?.entities} />
                <Timeline events={timeline} />
                <Evidence items={caseData?.evidence} />
                <ActionPanel
                    actions={actions}
                    onAction={handleAction}
                />
            </div>
        </div>
    );
};
```

### 2. Real-time Updates

```javascript
class RealtimeManager {
    constructor() {
        this.ws = null;
        this.subscribers = new Map();
        this.reconnectInterval = 5000;
    }

    connect() {
        this.ws = new WebSocket('ws://localhost:8005/realtime');

        this.ws.onopen = () => {
            console.log('Connected to realtime updates');
            this.subscribeToChannels();
        };

        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
        };

        this.ws.onclose = () => {
            console.log('Disconnected from realtime updates');
            setTimeout(() => this.connect(), this.reconnectInterval);
        };
    }

    subscribe(channel, callback) {
        if (!this.subscribers.has(channel)) {
            this.subscribers.set(channel, new Set());
        }
        this.subscribers.get(channel).add(callback);
    }

    handleMessage(message) {
        const { channel, data } = message;
        const callbacks = this.subscribers.get(channel) || [];

        callbacks.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('Error in realtime callback:', error);
            }
        });
    }
}
```

### 3. Visualization Components

```javascript
// D3.js Entity Relationship Graph
const EntityGraph = ({ entities, relationships }) => {
    const svgRef = useRef();

    useEffect(() => {
        if (!entities || !relationships) return;

        const svg = d3.select(svgRef.current);

        // Create force simulation
        const simulation = d3.forceSimulation(entities)
            .force('link', d3.forceLink(relationships).id(d => d.id))
            .force('charge', d3.forceManyBody().strength(-300))
            .force('center', d3.forceCenter(width / 2, height / 2));

        // Draw links
        const links = svg.selectAll('.link')
            .data(relationships)
            .enter().append('line')
            .attr('class', 'link')
            .style('stroke', d => getRelationshipColor(d.type));

        // Draw nodes
        const nodes = svg.selectAll('.node')
            .data(entities)
            .enter().append('g')
            .attr('class', 'node')
            .call(d3.drag()
                .on('start', dragstarted)
                .on('drag', dragged)
                .on('end', dragended));

        nodes.append('circle')
            .attr('r', d => getNodeSize(d.type))
            .style('fill', d => getNodeColor(d.type));

        nodes.append('text')
            .text(d => d.label)
            .attr('x', 12)
            .attr('y', 3);

        // Update positions on simulation tick
        simulation.on('tick', () => {
            links
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y);

            nodes
                .attr('transform', d => `translate(${d.x},${d.y})`);
        });

    }, [entities, relationships]);

    return <svg ref={svgRef} width={800} height={600} />;
};
```

---

## Metrics & KPIs

### Delivery Engine Performance Metrics

```python
DELIVERY_METRICS = {
    'case_management': {
        'cases_created_per_hour': 50,
        'average_case_resolution_time': '4 hours',
        'sla_compliance_rate': 0.95,
        'escalation_rate': 0.15
    },
    'alert_delivery': {
        'alerts_routed_per_second': 1000,
        'delivery_success_rate': 0.999,
        'average_delivery_latency': '200ms',
        'channel_availability': 0.999
    },
    'automation': {
        'playbooks_executed': 500,
        'automation_success_rate': 0.92,
        'manual_intervention_required': 0.08,
        'average_playbook_duration': '3 minutes'
    },
    'user_experience': {
        'dashboard_load_time': '1.2 seconds',
        'api_response_time': '150ms',
        'user_satisfaction_score': 4.5,
        'feature_adoption_rate': 0.78
    }
}
```

---

## Conclusion

The Delivery Engine transforms SIEMLess intelligence into actionable outcomes through:

1. **Intelligent Case Management**: Automated creation, assignment, and tracking
2. **Smart Alert Routing**: Context-aware delivery to the right people
3. **Rich Visualizations**: Interactive dashboards and real-time updates
4. **Workflow Automation**: Playbook execution and auto-remediation
5. **Seamless Integration**: Connect with existing security tools

This ensures that the intelligence extracted by Contextualization and Correlation engines results in rapid, effective security responses.