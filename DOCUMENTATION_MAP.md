# SIEMLess v2.0 - Documentation Map

## 📍 You Are Here: DEVELOPMENT Repository
This is where all development happens. Full history, test files, and working documents are preserved here.

## 🗺️ Complete Documentation Map

### 📂 Repository Structure
```
siemless_v2/ (DEVELOPMENT - You are here)
│
├── 📝 Core Documentation (ACTIVE - Synced to Production)
│   ├── README.md                        ✅ Main overview
│   ├── ARCHITECTURE.md                  ✅ System architecture
│   ├── DEPLOYMENT_ARCHITECTURE.md       ✅ Docker setup
│   ├── QUICK_START_GUIDE.md            ✅ Getting started
│   ├── TROUBLESHOOTING_GUIDE.md        ✅ Common issues
│   └── ADVANCED_WIDGETS_DOCUMENTATION.md ✅ Frontend specs
│
├── 🔧 Dev-Only Documentation (NOT synced)
│   ├── CLAUDE.md                        ⚠️ AI context (DEV ONLY)
│   ├── PROJECT_INDEX.md                 ⚠️ Complete map (DEV ONLY)
│   ├── DOCUMENTATION_MAP.md             ⚠️ This file (DEV ONLY)
│   └── sync_to_production.*             ⚠️ Sync scripts (DEV ONLY)
│
├── 📁 documents/
│   ├── Active Technical Docs            ✅ (Synced)
│   │   ├── AUTHENTICATION_IMPLEMENTATION.md
│   │   ├── DATABASE_SCHEMA.md
│   │   ├── SYSTEM_FLOWS.md
│   │   └── FRONTEND_WIDGETS_SPECIFICATION.md
│   │
│   └── archive/                         ❌ (Git-ignored, NOT synced)
│       ├── *_STATUS.md                  Old status reports
│       ├── *_REPORT.md                  Progress reports
│       ├── *_COMPLETE.md                Completion docs
│       └── [22+ historical documents]   Working/temp docs
│
└── siemless-production/ (PRODUCTION - Separate repo)
    ├── All active code
    ├── Active documentation only
    ├── LICENSE (Proprietary)
    ├── README_COLLABORATORS.md
    └── NO development history
```

## 📋 Quick Reference

### What's in Production Repository?
✅ **INCLUDED:**
- All source code (engines/, frontend/)
- Active documentation
- Docker configurations
- Pattern libraries
- LICENSE file
- README_COLLABORATORS.md

❌ **EXCLUDED:**
- Git history
- Test files (test_*.py, test_*.js)
- Development context (CLAUDE.md, PROJECT_INDEX.md)
- Archive folder
- Status/Report documents
- This documentation map

### How to Know What's Current?
1. **Check PROJECT_INDEX.md** - Lists all active documentation
2. **Check .gitignore** - Shows what's excluded from tracking
3. **Check documents/archive/** - Contains old/working documents

### Documentation Status Legend
- ✅ **Active** - Current, maintained, synced to production
- ⚠️ **Dev-Only** - Development context, not for production
- ❌ **Archived** - Historical, not synced, git-ignored
- 📝 **Working** - In progress, may become active

## 🔄 Keeping Repositories in Sync

### Development Workflow
1. **All work happens here** (siemless_v2)
2. **Test thoroughly** using test files
3. **Update documentation** as needed
4. **Run sync script** to update production

### Sync Commands
```bash
# Windows
sync_to_production.bat

# Linux/Mac/Git Bash
./sync_to_production.sh
```

### What Gets Updated
- Code changes automatically sync
- Active documentation syncs
- Dev-only files stay here
- Archive never syncs

## 📊 Documentation Statistics

### Current State (December 2024)
- **Active Docs**: 11 core + 7 technical = 18 total
- **Archived Docs**: 22+ (git-ignored)
- **Dev-Only Docs**: 4 (CLAUDE, PROJECT_INDEX, MAP, sync scripts)
- **Total Documentation**: ~40 files

### Documentation Health
- ✅ All core docs updated for production
- ✅ Archive cleaned and ignored
- ✅ Clear separation between dev/prod
- ✅ Sync mechanism in place

## 🎯 Key Files for Navigation

1. **Start Here**: PROJECT_INDEX.md (complete system map)
2. **For Setup**: QUICK_START_GUIDE.md
3. **For Issues**: TROUBLESHOOTING_GUIDE.md
4. **For Development**: CLAUDE.md (AI context)
5. **For Architecture**: ARCHITECTURE.md
6. **For Deployment**: DEPLOYMENT_ARCHITECTURE.md

## 💡 Tips

- **Before major changes**: Check PROJECT_INDEX.md
- **Before syncing**: Review what's in production vs dev
- **For new features**: Update relevant active documentation
- **For experiments**: Use archive folder (git-ignored)
- **For collaboration**: Use production repo

---

**Remember**: Development happens HERE, production is a clean mirror for collaborators.