/**
 * Entities Tab - Enriched Entity Display Styles
 */

.entities-tab {
  max-width: 1400px;
  margin: 0 auto;
}

/* No Enrichment State */
.no-enrichment {
  text-align: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.no-enrichment-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.no-enrichment h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.no-enrichment p {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0;
}

.no-enrichment .hint {
  font-size: 13px;
  font-style: italic;
  color: #9ca3af;
  margin-top: 12px;
}

/* Entities Header */
.entities-header {
  margin-bottom: 24px;
}

.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 8px 16px;
  background: #ffffff;
  color: #6b7280;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.filter-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #111827;
}

.filter-btn.active {
  background: #3b82f6;
  color: #ffffff;
  border-color: #3b82f6;
}

.filter-btn.malicious {
  border-color: #fca5a5;
  color: #dc2626;
}

.filter-btn.malicious:hover {
  background: #fef2f2;
  border-color: #dc2626;
}

.filter-btn.malicious.active {
  background: #dc2626;
  color: #ffffff;
  border-color: #dc2626;
}

/* Entities List */
.entities-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.no-entities {
  text-align: center;
  padding: 40px 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  color: #6b7280;
}

/* Entity Card */
.entity-card {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  border-left: 4px solid;
  overflow: hidden;
  transition: all 0.2s;
}

.entity-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.entity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.entity-title {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.entity-icon {
  font-size: 24px;
}

.entity-value {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  font-family: 'Courier New', monospace;
}

.entity-type-badge {
  padding: 2px 8px;
  background: #eff6ff;
  color: #3b82f6;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: 8px;
}

.threat-badge {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 0.3px;
}

/* Entity Body */
.entity-body {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.enrichment-section {
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.enrichment-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.enrichment-section.threat-section {
  background: #fef2f2;
  border-color: #fecaca;
}

.enrichment-section.threat-section h4 {
  color: #dc2626;
}

.enrichment-data {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  font-size: 13px;
}

.data-row .label {
  color: #6b7280;
  font-weight: 500;
  min-width: 150px;
}

.data-row .value {
  color: #111827;
  font-weight: 500;
  text-align: right;
  flex: 1;
}

.value.ip-type {
  text-transform: uppercase;
  font-size: 11px;
  padding: 2px 6px;
  background: #eff6ff;
  color: #3b82f6;
  border-radius: 3px;
}

.value.privilege-level {
  text-transform: uppercase;
  font-size: 11px;
  padding: 2px 6px;
  background: #fef3c7;
  color: #d97706;
  border-radius: 3px;
}

.value.criticality-critical {
  color: #dc2626;
  font-weight: 700;
}

.value.criticality-high {
  color: #ea580c;
  font-weight: 700;
}

.value.criticality-medium {
  color: #d97706;
  font-weight: 600;
}

.value.criticality-low {
  color: #10b981;
  font-weight: 500;
}

.no-data-inline {
  font-size: 13px;
  color: #9ca3af;
  font-style: italic;
  padding: 8px 0;
}

/* Threat Sources */
.threat-source {
  padding: 12px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #fecaca;
  margin-bottom: 8px;
}

.threat-source:last-child {
  margin-bottom: 0;
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.source-name {
  font-weight: 600;
  font-size: 13px;
  color: #dc2626;
}

.source-verdict {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  padding: 2px 8px;
  background: #dc2626;
  color: #ffffff;
  border-radius: 3px;
  letter-spacing: 0.5px;
}

.source-description {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
  margin-bottom: 6px;
}

.source-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  font-size: 11px;
  padding: 2px 8px;
  background: #f3f4f6;
  color: #374151;
  border-radius: 3px;
  border: 1px solid #d1d5db;
}

/* Risk Indicators */
.risk-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #ffffff;
  border-radius: 4px;
  border-left: 3px solid #dc2626;
  font-size: 13px;
  color: #374151;
  margin-bottom: 6px;
}

.risk-indicator:last-child {
  margin-bottom: 0;
}

.risk-icon {
  font-size: 14px;
  flex-shrink: 0;
}

/* Unusual Activities */
.unusual-activities {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 8px;
}

.unusual-activity {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #fef3c7;
  border-radius: 4px;
  border-left: 3px solid #d97706;
  font-size: 13px;
  color: #78350f;
}

.warning-icon {
  font-size: 14px;
  flex-shrink: 0;
}

/* Entity Actions */
.entity-actions {
  display: flex;
  gap: 8px;
  padding: 16px 20px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  flex-wrap: wrap;
}

.action-btn {
  padding: 8px 16px;
  background: #ffffff;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.action-btn.block-btn {
  background: #fef2f2;
  color: #dc2626;
  border-color: #fca5a5;
}

.action-btn.block-btn:hover {
  background: #fecaca;
  border-color: #dc2626;
}

/* Responsive */
@media (max-width: 768px) {
  .entity-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .data-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .data-row .label {
    min-width: auto;
  }

  .data-row .value {
    text-align: left;
  }

  .entity-actions {
    flex-direction: column;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }

  .source-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }
}

/* Print Styles */
@media print {
  .filter-buttons,
  .entity-actions {
    display: none;
  }

  .entity-card {
    page-break-inside: avoid;
    border: 1px solid #000000;
  }

  .entity-body {
    padding: 12px;
  }
}
