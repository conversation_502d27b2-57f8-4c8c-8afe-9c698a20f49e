# Plugin Creation Guide: Manual vs AI-Generated

## Two Approaches for Creating Context Plugins

---

## Approach 1: Manual with Templates ⚡ (FAST)

### When to Use:
- You have clear API documentation
- You understand the vendor's API structure
- You want full control over implementation
- Quick turnaround needed (30-60 minutes)

### Process:

**Step 1**: Choose template based on vendor type
- EDR/Endpoint: CrowdStrike, SentinelOne, Carbon Black → Use EDR template
- Identity: Active Directory, Okta, Azure AD → Use Identity template
- Vulnerability: Tenable, Qualys, Rapid7 → Use Vulnerability template
- Network: Palo Alto, Fortinet, Cisco → Use Network template

**Step 2**: Copy template from [PLUGIN_TEMPLATES.md](cci:7://file:///c:/Users/<USER>/Documents/siemless_v2/engines/ingestion/PLUGIN_TEMPLATES.md:0:0-0:0)

**Step 3**: Replace placeholders
```python
# Replace these:
{SOURCE_NAME}     → SentinelOne
{SourceName}      → SentinelOne (class name)
{source_name}     → sentinelone (lowercase)
{provider}        → sentinelone
```

**Step 4**: Update API endpoints
```python
# Example for SentinelOne:
f'{self.console_url}/web/api/v2.1/agents'           # Assets
f'{self.console_url}/web/api/v2.1/threats'          # Detections
f'{self.console_url}/web/api/v2.1/threats/timeline' # Incidents
```

**Step 5**: Map vendor fields to standard fields
```python
# SentinelOne → Standard
'computerName'              → 'hostname'
'networkInterfaces[0].inet' → 'local_ip'
'osName'                    → 'os_version'
'lastLoggedInUserName'      → 'last_login_user'
```

**Step 6**: Register plugin in [ingestion_engine.py](cci:7://file:///c:/Users/<USER>/Documents/siemless_v2/engines/ingestion/ingestion_engine.py:0:0-0:0)
```python
if os.getenv('SENTINELONE_API_TOKEN'):
    sentinelone_plugin = SentinelOneContextPlugin({
        'enabled': True,
        'api_token': os.getenv('SENTINELONE_API_TOKEN'),
        'console_url': os.getenv('SENTINELONE_CONSOLE_URL')
    })
    self.context_manager.register_plugin(sentinelone_plugin)
```

**Step 7**: Test
```bash
python test_my_plugin.py
```

### Pros:
✅ Fast (30-60 minutes)
✅ Full control over implementation
✅ Easy to debug
✅ No AI dependencies

### Cons:
❌ Manual field mapping required
❌ Need to read API docs carefully
❌ Repetitive for similar vendors

### Time Estimate: 30-60 minutes per plugin

---

## Approach 2: AI-Generated 🤖 (SMART)

### When to Use:
- Complex API with many endpoints
- Want automatic field mapping
- Have Swagger/OpenAPI documentation
- Want to generate multiple similar plugins
- Learning new vendor API

### Process:

**Step 1**: Prepare inputs
```python
from plugin_generator import PluginGenerator

generator = PluginGenerator()
```

**Step 2**: Choose generation method

**Method A: From API docs URL**
```python
code = await generator.generate_plugin(
    source_name='SentinelOne',
    api_docs_url='https://usea1-partners.sentinelone.net/api-doc/overview',
    categories=['asset', 'detection', 'incident']
)
```

**Method B: From Swagger/OpenAPI**
```python
code = await generator.generate_from_swagger(
    source_name='Tenable',
    swagger_url='https://cloud.tenable.com/api-docs',
    categories=['asset', 'vulnerability']
)
```

**Method C: From sample responses**
```python
code = await generator.generate_plugin(
    source_name='CustomVendor',
    categories=['asset', 'detection'],
    sample_responses={
        'asset': '''{"agents": [{"name": "host1", "ip": "***********"}]}''',
        'detection': '''{"threats": [{"id": "123", "severity": "high"}]}'''
    }
)
```

**Step 3**: Save and review
```python
generator.save_plugin(code, 'sentinelone_context_plugin.py')

# Review the generated code
# Make any necessary adjustments
```

**Step 4**: Register plugin (same as manual)

**Step 5**: Test
```bash
python test_my_plugin.py
```

### Pros:
✅ Automatic field mapping
✅ Handles complex APIs automatically
✅ Generates production-ready code
✅ Learns from API documentation
✅ Consistent code structure

### Cons:
❌ Requires Intelligence Engine running
❌ Takes longer to generate (2-5 minutes)
❌ May need manual review/adjustments
❌ Dependent on AI understanding of docs

### Time Estimate: 10-15 minutes per plugin (+ 2-5 min AI generation)

---

## Comparison Table

| Feature | Manual Template | AI-Generated |
|---------|----------------|--------------|
| **Setup Time** | 30-60 min | 10-15 min |
| **Code Quality** | High (if done carefully) | High (auto-generated) |
| **Field Mapping** | Manual | Automatic |
| **API Learning** | Required | Optional |
| **Customization** | Full control | Review + adjust |
| **Dependencies** | None | Intelligence Engine |
| **Reproducibility** | Medium | High |
| **Batch Creation** | Slow (manual each time) | Fast (automate) |
| **Complex APIs** | Time-consuming | Handles well |
| **Documentation** | Need to read carefully | AI parses for you |

---

## Recommended Workflow

### For First-Time Plugin Creation:
1. **Use AI-Generated** to understand the vendor's API structure
2. **Review generated code** to learn field mappings
3. **Test and validate**
4. **Use template** for similar vendors in the future

### For Similar Vendors:
1. **Use Manual Template** (faster once you understand the pattern)
2. Copy from existing similar plugin
3. Adjust endpoints and field mappings
4. Test

### For Complex Vendors with Many Endpoints:
1. **Use AI-Generated from Swagger**
2. Let AI handle endpoint discovery
3. Review and test generated code

### For Rapid Prototyping:
1. **Use AI with sample responses**
2. Paste a few sample API responses
3. Get working plugin in minutes
4. Refine later

---

## Example: Creating SentinelOne Plugin

### Manual Approach:

```bash
# Step 1: Copy EDR template
cp PLUGIN_TEMPLATES.md sentinelone_context_plugin.py

# Step 2: Edit file
# - Replace {SOURCE_NAME} with SentinelOne
# - Update API endpoints
# - Map fields

# Step 3: Test
python test_sentinelone_plugin.py

# Total time: ~45 minutes
```

### AI Approach:

```python
# Step 1: Run generator
from plugin_generator import PluginGenerator

generator = PluginGenerator()
code = await generator.generate_plugin(
    source_name='SentinelOne',
    api_docs_url='https://usea1-partners.sentinelone.net/api-doc/overview',
    categories=['asset', 'detection', 'incident']
)

# Step 2: Save
generator.save_plugin(code, 'sentinelone_context_plugin.py')

# Step 3: Review and test
python test_sentinelone_plugin.py

# Total time: ~12 minutes (+ 3 min AI generation)
```

---

## Field Mapping Quick Reference

### Common Vendor Field Names → Standard Fields

**Hostname/Computer:**
```
computerName, computer_name, hostname, host, device_name, machineName
  → 'hostname'
```

**IP Address:**
```
ipAddress, ip, ip_address, local_ip, networkInterfaces[0].inet, inet
  → 'local_ip'
```

**OS Version:**
```
osName, os, os_version, operatingSystem, osType, platform
  → 'os_version'
```

**User:**
```
lastLoggedInUserName, last_user, logged_in_user, current_user, userName
  → 'last_login_user'
```

**Detection ID:**
```
id, threat_id, alert_id, detection_id, incident_id
  → 'detection_id' or 'alert_id'
```

**Severity:**
```
severity, threat_level, risk_score, classification, priority
  → 'severity' (normalize to: critical/high/medium/low)
```

**MITRE Techniques:**
```
mitre_techniques, techniques, attack_techniques, ttps, tactics
  → 'mitre_techniques' (array of strings like ['T1055', 'T1021'])
```

---

## Testing Your Plugin

### Test Script Template:

```python
# test_my_plugin.py

import asyncio
from my_plugin import MyContextPlugin
from context_source_plugin import create_context_query

async def test():
    # Initialize
    plugin = MyContextPlugin({
        'enabled': True,
        'api_token': 'your_test_token',
        'base_url': 'https://api.vendor.com'
    })

    print(f"Testing {plugin.get_source_name()} plugin...")

    # Test 1: Credential validation
    print("\n1. Testing credentials...")
    valid = await plugin.validate_credentials()
    print(f"   Credentials: {'✅ Valid' if valid else '❌ Invalid'}")

    if not valid:
        return

    # Test 2: Asset query
    print("\n2. Testing asset query...")
    query = create_context_query('ip', '***********00', ['asset'])
    results = await plugin.query_context(query)
    print(f"   Asset results: {len(results)}")
    for r in results:
        print(f"   - {r.data.get('hostname', 'N/A')} ({r.data.get('local_ip', 'N/A')})")

    # Test 3: Detection query
    print("\n3. Testing detection query...")
    query = create_context_query('hostname', 'workstation-42', ['detection'])
    results = await plugin.query_context(query)
    print(f"   Detection results: {len(results)}")
    for r in results:
        print(f"   - {r.data.get('detection_id', 'N/A')} - {r.data.get('severity', 'N/A')}")

    print("\n✅ All tests passed!")

if __name__ == "__main__":
    asyncio.run(test())
```

Run: `python test_my_plugin.py`

---

## Next Steps After Plugin Creation

1. **Register in ingestion_engine.py**
2. **Add environment variables to .env**
3. **Rebuild Docker container**
```bash
docker-compose up -d --build --no-deps ingestion_engine
```
4. **Test end-to-end**
```bash
# Trigger context pull via Redis
docker-compose exec redis redis-cli
PUBLISH ingestion.pull_context '{"request_id":"test-123","query_type":"ip","query_value":"***********00","categories":["asset"]}'

# Check logs
docker-compose logs -f ingestion_engine | grep "test-123"
```
5. **Verify entity extraction in Contextualization Engine**
6. **Check results in Delivery Engine**

---

## Plugin Library (Future)

We can create a community library of plugins:

```
plugins/
├── crowdstrike/         ✅ Complete (7 scopes)
├── sentinelone/         🔄 AI-generated template ready
├── carbon_black/        📝 Manual template ready
├── microsoft_defender/  📝 Manual template ready
├── elastic_security/    📝 Manual template ready
├── okta/               📝 Manual template ready
├── active_directory/   📝 Manual template ready
├── tenable/            📝 Manual template ready
├── qualys/             📝 Manual template ready
├── palo_alto/          📝 Manual template ready
└── fortinet/           📝 Manual template ready
```

Each plugin:
- ~100-300 lines of code
- Tested with real API
- Documented field mappings
- Example queries included

---

## Summary

**Choose Manual** when:
- You know the API well
- Want fast iteration
- Similar to existing plugin

**Choose AI** when:
- Complex API
- Multiple similar vendors
- Have good documentation
- Want automatic field mapping

**Best Practice**: Use AI for first plugin of a type, then use template for similar vendors.

Both approaches produce production-ready plugins in under an hour!
