#!/usr/bin/env python3
"""
SIEMLess v2.0 Database Setup Script

This script sets up the PostgreSQL database schema for encrypted credential storage
and initializes the cryptographic infrastructure for API key management.
"""

import asyncio
import os
import sys
import json
from datetime import datetime
from typing import Dict, Any

# Database imports
import asyncpg
from asyncpg import Connection

# Add shared modules to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shared'))

# Crypto service import
try:
    from shared.security.encryption import CryptoService
except ImportError:
    print("Error: Shared crypto service not found. Using standalone implementation.")
    # Fallback to inline minimal crypto service
    import secrets
    import base64
    from cryptography.hazmat.primitives.ciphers.aead import AESGCM

    class CryptoService:
        def __init__(self):
            self.master_key = self._get_master_key()

        def _get_master_key(self):
            master_key_str = os.environ.get("SIEMLESS_MASTER_KEY")
            if master_key_str:
                return base64.b64decode(master_key_str)
            return secrets.token_bytes(32)

        def generate_client_keypair(self):
            # Simplified for v2 - will use AES-only for now
            return {
                'public_key': secrets.token_bytes(32),
                'private_key': secrets.token_bytes(32)
            }

        def encrypt_credentials(self, credentials: Dict[str, Any]) -> bytes:
            aesgcm = AESGCM(self.master_key)
            nonce = secrets.token_bytes(12)
            plaintext = json.dumps(credentials).encode('utf-8')
            ciphertext = aesgcm.encrypt(nonce, plaintext, None)
            return nonce + ciphertext

async def create_database_schema(conn: Connection):
    """Create the v2 database schema for credential storage"""

    print("[SETUP] Creating v2 database schema...")

    # Create schema
    await conn.execute("CREATE SCHEMA IF NOT EXISTS siemless_v2")

    # Create clients table
    await conn.execute("""
        CREATE TABLE IF NOT EXISTS siemless_v2.clients (
            client_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            client_name VARCHAR(255) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            is_active BOOLEAN DEFAULT TRUE,
            metadata JSONB DEFAULT '{}'
        )
    """)

    # Create environments table
    await conn.execute("""
        CREATE TABLE IF NOT EXISTS siemless_v2.environments (
            environment_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            client_id UUID REFERENCES siemless_v2.clients(client_id) ON DELETE CASCADE,
            environment_name VARCHAR(255) NOT NULL,
            environment_type VARCHAR(50) DEFAULT 'production',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            is_active BOOLEAN DEFAULT TRUE,
            configuration JSONB DEFAULT '{}'
        )
    """)

    # Create encryption keys table
    await conn.execute("""
        CREATE TABLE IF NOT EXISTS siemless_v2.encryption_keys (
            key_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            client_id UUID REFERENCES siemless_v2.clients(client_id) ON DELETE CASCADE,
            environment_id UUID REFERENCES siemless_v2.environments(environment_id) ON DELETE CASCADE,
            public_key BYTEA NOT NULL,
            encrypted_private_key BYTEA NOT NULL,
            algorithm VARCHAR(50) DEFAULT 'aes256gcm',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            is_active BOOLEAN DEFAULT TRUE,
            key_purpose VARCHAR(100) DEFAULT 'credential_encryption'
        )
    """)

    # Create credentials table
    await conn.execute("""
        CREATE TABLE IF NOT EXISTS siemless_v2.credentials (
            credential_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            client_id UUID REFERENCES siemless_v2.clients(client_id) ON DELETE CASCADE,
            environment_id UUID REFERENCES siemless_v2.environments(environment_id) ON DELETE CASCADE,
            key_id UUID REFERENCES siemless_v2.encryption_keys(key_id) ON DELETE CASCADE,
            provider_type VARCHAR(100) NOT NULL,  -- 'ai_provider', 'cti_provider', 'siem_provider'
            provider_name VARCHAR(100) NOT NULL,  -- 'anthropic', 'openai', 'opencti', etc.
            credential_name VARCHAR(100) NOT NULL, -- 'default', 'production', etc.
            encrypted_data BYTEA NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            is_active BOOLEAN DEFAULT TRUE,
            metadata JSONB DEFAULT '{}'
        )
    """)

    # Create audit log table
    await conn.execute("""
        CREATE TABLE IF NOT EXISTS siemless_v2.credential_audit_log (
            audit_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            client_id UUID REFERENCES siemless_v2.clients(client_id) ON DELETE CASCADE,
            environment_id UUID REFERENCES siemless_v2.environments(environment_id) ON DELETE CASCADE,
            credential_id UUID REFERENCES siemless_v2.credentials(credential_id) ON DELETE CASCADE,
            action VARCHAR(50) NOT NULL,  -- 'create', 'read', 'update', 'delete'
            performed_by VARCHAR(255),
            performed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            details JSONB DEFAULT '{}'
        )
    """)

    # Create indexes for performance
    await conn.execute("CREATE INDEX IF NOT EXISTS idx_clients_active ON siemless_v2.clients(is_active)")
    await conn.execute("CREATE INDEX IF NOT EXISTS idx_environments_client ON siemless_v2.environments(client_id)")
    await conn.execute("CREATE INDEX IF NOT EXISTS idx_credentials_client_env ON siemless_v2.credentials(client_id, environment_id)")
    await conn.execute("CREATE INDEX IF NOT EXISTS idx_credentials_provider ON siemless_v2.credentials(provider_type, provider_name)")
    await conn.execute("CREATE INDEX IF NOT EXISTS idx_audit_credential ON siemless_v2.credential_audit_log(credential_id)")

    print("[OK] Database schema created successfully")

async def create_default_client(conn: Connection) -> tuple:
    """Create default client and environment"""

    print("[SETUP] Creating default client and environment...")

    # Insert default client
    client_result = await conn.fetchrow("""
        INSERT INTO siemless_v2.clients (client_name, metadata)
        VALUES ('SIEMLess Default Client', '{"description": "Default client for v2 platform"}')
        ON CONFLICT DO NOTHING
        RETURNING client_id
    """)

    if not client_result:
        # Client already exists, get it
        client_result = await conn.fetchrow("""
            SELECT client_id FROM siemless_v2.clients WHERE client_name = 'SIEMLess Default Client'
        """)

    client_id = client_result['client_id']

    # Insert default environment
    env_result = await conn.fetchrow("""
        INSERT INTO siemless_v2.environments (client_id, environment_name, environment_type)
        VALUES ($1, 'production', 'production')
        ON CONFLICT DO NOTHING
        RETURNING environment_id
    """, client_id)

    if not env_result:
        # Environment already exists, get it
        env_result = await conn.fetchrow("""
            SELECT environment_id FROM siemless_v2.environments
            WHERE client_id = $1 AND environment_name = 'production'
        """, client_id)

    environment_id = env_result['environment_id']

    print(f"[OK] Client ID: {client_id}")
    print(f"[OK] Environment ID: {environment_id}")

    return str(client_id), str(environment_id)

async def setup_encryption_keys(conn: Connection, client_id: str, environment_id: str):
    """Set up encryption keys for the client"""

    print("[SETUP] Setting up encryption keys...")

    crypto_service = CryptoService()

    # Generate keypair
    keypair = crypto_service.generate_client_keypair()

    # For simplified v2, we'll use the same key for encryption
    public_key = keypair['public_key']
    private_key = keypair['private_key']

    # Insert encryption key
    key_result = await conn.fetchrow("""
        INSERT INTO siemless_v2.encryption_keys
        (client_id, environment_id, public_key, encrypted_private_key, algorithm)
        VALUES ($1, $2, $3, $4, 'aes256gcm')
        ON CONFLICT DO NOTHING
        RETURNING key_id
    """, client_id, environment_id, public_key, private_key)

    if not key_result:
        # Key already exists, get it
        key_result = await conn.fetchrow("""
            SELECT key_id FROM siemless_v2.encryption_keys
            WHERE client_id = $1 AND environment_id = $2 AND is_active = TRUE
        """, client_id, environment_id)

    key_id = key_result['key_id']

    print(f"[OK] Encryption key ID: {key_id}")
    return str(key_id)

async def migrate_api_keys(conn: Connection, client_id: str, environment_id: str, key_id: str):
    """Migrate API keys from environment variables to encrypted database storage"""

    print("[SETUP] Migrating API keys to encrypted storage...")

    crypto_service = CryptoService()

    # API key mappings from environment
    api_keys = {
        'ai_provider': {
            'anthropic': {
                'ANTHROPIC_API_KEY': os.environ.get('ANTHROPIC_API_KEY', '')
            },
            'openai': {
                'OPENAI_API_KEY': os.environ.get('OPENAI_API_KEY', '')
            },
            'google': {
                'GEMINI_API_KEY': os.environ.get('GEMINI_API_KEY', '')
            }
        },
        'cti_provider': {
            'opencti': {
                'OPENCTI_URL': os.environ.get('OPENCTI_URL', ''),
                'OPENCTI_TOKEN': os.environ.get('OPENCTI_TOKEN', '')
            },
            'otx': {
                'OTX_API_KEY': os.environ.get('OTX_API_KEY', '')
            }
        },
        'siem_provider': {
            'crowdstrike': {
                'CROWDSTRIKE_CLIENT_ID': os.environ.get('CROWDSTRIKE_CLIENT_ID', ''),
                'CROWDSTRIKE_CLIENT_SECRET': os.environ.get('CROWDSTRIKE_CLIENT_SECRET', ''),
                'CROWDSTRIKE_BASE_URL': os.environ.get('CROWDSTRIKE_BASE_URL', '')
            },
            'elasticsearch': {
                'ELASTIC_CLOUD_ID': os.environ.get('ELASTIC_CLOUD_ID', ''),
                'ELASTIC_API_KEY': os.environ.get('ELASTIC_API_KEY', '')
            }
        }
    }

    credentials_created = 0

    for provider_type, providers in api_keys.items():
        for provider_name, credentials in providers.items():
            # Filter out empty credentials
            filtered_creds = {k: v for k, v in credentials.items() if v}

            if not filtered_creds:
                print(f"[SKIP] No credentials found for {provider_type}/{provider_name}")
                continue

            # Encrypt credentials
            encrypted_data = crypto_service.encrypt_credentials(filtered_creds)

            # Insert into database
            await conn.execute("""
                INSERT INTO siemless_v2.credentials
                (client_id, environment_id, key_id, provider_type, provider_name,
                 credential_name, encrypted_data, metadata)
                VALUES ($1, $2, $3, $4, $5, 'default', $6, $7)
                ON CONFLICT DO NOTHING
            """, client_id, environment_id, key_id, provider_type, provider_name,
                encrypted_data, json.dumps({'migrated_from': 'v1_env', 'key_count': len(filtered_creds)}))

            # Log the migration
            await conn.execute("""
                INSERT INTO siemless_v2.credential_audit_log
                (client_id, environment_id, action, performed_by, details)
                VALUES ($1, $2, 'create', 'system_migration', $3)
            """, client_id, environment_id, json.dumps({
                'provider_type': provider_type,
                'provider_name': provider_name,
                'credential_count': len(filtered_creds),
                'migration_source': 'v1_environment_variables'
            }))

            credentials_created += 1
            print(f"[OK] Migrated {len(filtered_creds)} keys for {provider_type}/{provider_name}")

    print(f"[OK] Successfully migrated {credentials_created} credential sets")

async def verify_migration(conn: Connection, client_id: str, environment_id: str):
    """Verify the migration was successful"""

    print("[VERIFY] Checking migration results...")

    # Count credentials
    credential_count = await conn.fetchval("""
        SELECT COUNT(*) FROM siemless_v2.credentials
        WHERE client_id = $1 AND environment_id = $2 AND is_active = TRUE
    """, client_id, environment_id)

    # Get credential summary
    credential_summary = await conn.fetch("""
        SELECT provider_type, provider_name, credential_name,
               created_at, metadata->>'key_count' as key_count
        FROM siemless_v2.credentials
        WHERE client_id = $1 AND environment_id = $2 AND is_active = TRUE
        ORDER BY provider_type, provider_name
    """, client_id, environment_id)

    print(f"[OK] Total credentials stored: {credential_count}")
    print("\n[SUMMARY] Credential breakdown:")

    for cred in credential_summary:
        print(f"  - {cred['provider_type']}/{cred['provider_name']}: {cred['key_count']} keys")

    return credential_count > 0

async def main():
    """Main setup function"""

    print("[SECURE] SIEMLess v2.0 Database & Credential Migration")
    print("=" * 60)

    # Database connection parameters - use v1 database for now
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': os.environ.get('POSTGRES_DB', 'siemless_db'),  # v1 database name
        'user': os.environ.get('POSTGRES_USER', 'test'),          # v1 user
        'password': os.environ.get('POSTGRES_PASSWORD', 'test123') # v1 password
    }

    try:
        # Connect to database
        print(f"[CONNECT] Connecting to PostgreSQL: {db_config['host']}:{db_config['port']}/{db_config['database']}")
        conn = await asyncpg.connect(**db_config)

        # Step 1: Create database schema
        await create_database_schema(conn)

        # Step 2: Create default client and environment
        client_id, environment_id = await create_default_client(conn)

        # Step 3: Set up encryption keys
        key_id = await setup_encryption_keys(conn, client_id, environment_id)

        # Step 4: Migrate API keys
        await migrate_api_keys(conn, client_id, environment_id, key_id)

        # Step 5: Verify migration
        success = await verify_migration(conn, client_id, environment_id)

        if success:
            print("\n[SUCCESS] MIGRATION COMPLETED SUCCESSFULLY!")
            print(f"Client ID: {client_id}")
            print(f"Environment ID: {environment_id}")
            print(f"Encryption Key ID: {key_id}")
            print("\n[SECURE] All API keys are now encrypted and stored in the database")
        else:
            print("\n[ERROR] MIGRATION FAILED - No credentials were stored")
            return 1

    except Exception as e:
        print(f"\n[CRITICAL ERROR] MIGRATION ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1

    finally:
        if 'conn' in locals():
            await conn.close()

    return 0

if __name__ == "__main__":
    import asyncio
    sys.exit(asyncio.run(main()))