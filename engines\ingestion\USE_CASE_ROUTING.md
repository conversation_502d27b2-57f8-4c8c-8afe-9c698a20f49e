# Use Case Routing in V2.0 Separated Engines

## V0.7 → V2.0 Architecture Mapping

### V0.7 Monolithic Flow:
```
Log → Ingestor Pattern → Use Case → Enrichment → Alert
         (parse)         (detect)     (context)   (deliver)
```

### V2.0 Separated Engine Flow:
```
Log → Ingestion Engine → Pattern Matcher → Route by Use Case Type
         ↓                    ↓                      ↓
    Parse & Extract      Match Pattern         Route to Engine
         ↓                    ↓                      ↓
    [Entities]          [Pattern Type]        [Specialized Processing]
```

## Use Case Routing Implementation

### 1. Pattern Types Define Use Cases
Each pattern in the pattern_library has a `pattern_type` that maps to v0.7 use cases:

```python
pattern_types = {
    'authentication': 'security_use_case',
    'network_anomaly': 'threat_detection_use_case',
    'data_exfiltration': 'dlp_use_case',
    'compliance_violation': 'compliance_use_case',
    'performance_degradation': 'operations_use_case'
}
```

### 2. Use Case-Based Routing Rules

```python
async def route_by_use_case(self, log: Dict, pattern_match: Dict):
    """Route logs to appropriate engines based on use case"""

    pattern_type = pattern_match['pattern_type']

    # Security Use Cases → Intelligence + Contextualization
    if pattern_type in ['authentication', 'privilege_escalation', 'malware']:
        self.publish_message('intelligence.security_analysis', {
            'log': log,
            'pattern': pattern_match,
            'priority': 'high'
        })

        self.publish_message('contextualization.security_context', {
            'entities': pattern_match['entities'],
            'threat_indicators': True
        })

    # Compliance Use Cases → Backend + Delivery
    elif pattern_type in ['compliance_violation', 'audit_event']:
        self.publish_message('backend.compliance_storage', {
            'log': log,
            'retention': 'long_term',
            'immutable': True
        })

        self.publish_message('delivery.compliance_alert', {
            'violation_type': pattern_type,
            'severity': 'medium',
            'recipients': ['compliance-team']
        })

    # Threat Detection Use Cases → Full Pipeline
    elif pattern_type in ['network_anomaly', 'data_exfiltration']:
        # Intelligence for threat analysis
        self.publish_message('intelligence.threat_analysis', log)

        # Context for entity relationships
        self.publish_message('contextualization.entity_graph', {
            'entities': pattern_match['entities'],
            'depth': 3  # Look 3 hops for relationships
        })

        # Backend for IOC storage
        self.publish_message('backend.ioc_storage', {
            'indicators': pattern_match['entities']
        })

        # Delivery for SOC alerts
        self.publish_message('delivery.soc_case', {
            'case_type': 'threat_detection',
            'auto_assign': True
        })
```

### 3. Ingestor Pattern Mapping

V0.7 ingestor patterns become specialized parsers in the Ingestion Engine:

```python
class IngestionEngine:
    def __init__(self):
        # V0.7 Ingestor Patterns → V2.0 Source Configs
        self.source_parsers = {
            'crowdstrike': CrowdStrikeParser(),  # Knows CrowdStrike format
            'elasticsearch': ElasticParser(),     # Handles ES documents
            'palo_alto': PaloAltoParser(),       # Parses PA logs
            'fortinet': FortinetParser()         # Fortinet specific
        }

    async def parse_by_source(self, log: Dict, source_type: str):
        """Apply source-specific parsing (v0.7 ingestor pattern)"""
        parser = self.source_parsers.get(source_type)
        if parser:
            return parser.parse(log)
        return self._generic_parse(log)
```

### 4. Enrichment → Contextualization Engine

V0.7 enrichment becomes the Contextualization Engine's responsibility:

```python
# V0.7 Monolithic Enrichment
def enrich_log(log):
    log['geoip'] = lookup_geoip(log['ip'])
    log['user_info'] = lookup_ad(log['username'])
    log['threat_intel'] = check_threatfeeds(log['hash'])
    return log

# V2.0 Separated Enrichment
class ContextualizationEngine:
    async def process_message(self, message):
        if message['channel'] == 'contextualization.enrich_entities':
            entities = message['data']['entities']

            # Each enrichment publishes results separately
            for entity in entities:
                if entity['type'] == 'ip_address':
                    self.publish_message('contextualization.geoip_result',
                                       await self.geoip_lookup(entity))

                elif entity['type'] == 'username':
                    self.publish_message('contextualization.user_context',
                                       await self.ad_lookup(entity))

                elif entity['type'] == 'file_hash':
                    self.publish_message('contextualization.threat_intel',
                                       await self.threatfeed_check(entity))
```

## Benefits of Separation

### 1. Independent Scaling
- High-volume compliance logs? Scale Backend Engine
- Complex threat analysis? Scale Intelligence Engine
- Lots of enrichment? Scale Contextualization Engine

### 2. Failure Isolation
- Contextualization down? Logs still stored
- Intelligence slow? Pattern matching continues
- Delivery issues? Data still processed

### 3. Use Case Specialization
- Security use cases get deep analysis
- Compliance use cases get guaranteed storage
- Operations use cases get performance metrics

### 4. Cost Optimization
- Route only security events to expensive AI
- Simple compliance logs skip Intelligence Engine
- Known-good patterns bypass enrichment

## Example: Failed Login Use Case

### V0.7 Flow:
```python
# All in one function
def process_failed_login(log):
    parsed = parse_windows_event(log)          # Ingestor pattern
    if detect_brute_force(parsed):             # Use case logic
        enriched = enrich_user_context(parsed)  # Enrichment
        create_alert(enriched)                  # Delivery
```

### V2.0 Flow:
```python
# Ingestion Engine
pattern_match = self.pattern_matcher.match(log)
# Returns: pattern_type='failed_authentication'

# Route by use case
self.publish_message('contextualization.enrich_entities', {
    'entities': [{'type': 'username', 'value': 'admin'}]
})

self.publish_message('intelligence.behavior_analysis', {
    'pattern': 'failed_authentication',
    'count': self.get_recent_count(username='admin')
})

# Contextualization Engine (async)
user_context = await self.ad_lookup('admin')
self.publish_message('delivery.user_risk_update', user_context)

# Intelligence Engine (async)
if self.is_brute_force_pattern(data):
    self.publish_message('delivery.security_alert', {
        'type': 'brute_force_detected',
        'confidence': 0.95
    })

# Delivery Engine (async)
case = self.create_case(alert_data)
self.assign_to_analyst(case)
self.send_notifications(case)
```

## Configuration for Use Cases

Add to `ingestion_engine.py`:

```python
USE_CASE_ROUTING = {
    'authentication': ['contextualization', 'intelligence'],
    'network_traffic': ['backend'],  # Just store
    'threat_detection': ['intelligence', 'contextualization', 'delivery'],
    'compliance': ['backend', 'delivery'],
    'performance': ['backend']
}
```

This allows dynamic routing based on pattern type, reducing unnecessary processing and costs.