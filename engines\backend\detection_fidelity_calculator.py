"""
Detection Fidelity Calculator
Calculates real-time detection confidence based on available log sources
"""

import json
from typing import Dict, List, Optional, Set
from datetime import datetime, timedelta
import logging
import math

class DetectionFidelityCalculator:
    """
    Calculate detection fidelity for attacks based on log source quality and coverage
    """

    def __init__(self, quality_engine=None, logger=None):
        self.quality_engine = quality_engine
        self.logger = logger or logging.getLogger(__name__)

        # MITRE ATT&CK coverage mapping
        self.technique_coverage = self._initialize_technique_coverage()

        # Attack chain requirements
        self.attack_chains = self._initialize_attack_chains()

        # Fidelity cache
        self.fidelity_cache = {}
        self.cache_ttl = 300  # 5 minutes

    def calculate_detection_fidelity(self, attack_types, available_sources):
        """Main entry point for detection fidelity calculation

        This is the primary method called by the backend engine.
        """
        # Handle both single attack and multiple attacks
        if isinstance(attack_types, str):
            attack_types = [attack_types]

        results = {
            'attack_fidelity': {},
            'overall_confidence': 0
        }

        for attack_type in attack_types:
            result = self.calculate_attack_chain_fidelity(attack_type, available_sources)
            results['attack_fidelity'][attack_type] = result

        # Calculate overall confidence
        if results['attack_fidelity']:
            confidences = [r.get('confidence', 0) for r in results['attack_fidelity'].values()]
            results['overall_confidence'] = sum(confidences) / len(confidences) if confidences else 0

        return results

    def calculate_fidelity_for_attacks(self, attack_types, available_sources):
        """Alias for calculate_detection_fidelity for backward compatibility"""
        return self.calculate_detection_fidelity(attack_types, available_sources)

    def _initialize_technique_coverage(self) -> Dict:
        """Initialize MITRE ATT&CK technique coverage by log source"""
        return {
            # Initial Access
            'T1566': {  # Phishing
                'name': 'Phishing',
                'sources': {
                    'email_gateway': 90,
                    'endpoint_edr': 70,
                    'web_proxy': 60,
                    'dns_logs': 40
                }
            },
            'T1078': {  # Valid Accounts
                'name': 'Valid Accounts',
                'sources': {
                    'identity_logs': 95,
                    'vpn_logs': 85,
                    'cloud_logs': 80,
                    'endpoint_edr': 60
                }
            },

            # Execution
            'T1059': {  # Command and Scripting Interpreter
                'name': 'Command and Scripting Interpreter',
                'sources': {
                    'endpoint_edr': 95,
                    'sysmon': 90,
                    'powershell_logs': 95,
                    'windows_events': 60,
                    'auditd': 80
                }
            },
            'T1047': {  # Windows Management Instrumentation
                'name': 'Windows Management Instrumentation',
                'sources': {
                    'endpoint_edr': 90,
                    'sysmon': 85,
                    'windows_events': 40
                }
            },

            # Persistence
            'T1547': {  # Boot or Logon Autostart Execution
                'name': 'Boot or Logon Autostart Execution',
                'sources': {
                    'endpoint_edr': 90,
                    'sysmon': 85,
                    'windows_events': 60,
                    'file_integrity': 70
                }
            },
            'T1053': {  # Scheduled Task/Job
                'name': 'Scheduled Task/Job',
                'sources': {
                    'endpoint_edr': 95,
                    'windows_events': 75,
                    'sysmon': 80,
                    'auditd': 80
                }
            },

            # Privilege Escalation
            'T1055': {  # Process Injection
                'name': 'Process Injection',
                'sources': {
                    'endpoint_edr': 95,
                    'sysmon': 80,
                    'windows_events': 30
                }
            },
            'T1068': {  # Exploitation for Privilege Escalation
                'name': 'Exploitation for Privilege Escalation',
                'sources': {
                    'endpoint_edr': 85,
                    'windows_events': 40,
                    'patch_management': 60
                }
            },

            # Defense Evasion
            'T1070': {  # Indicator Removal on Host
                'name': 'Indicator Removal on Host',
                'sources': {
                    'endpoint_edr': 90,
                    'sysmon': 75,
                    'windows_events': 60,
                    'file_integrity': 70
                }
            },
            'T1562': {  # Impair Defenses
                'name': 'Impair Defenses',
                'sources': {
                    'endpoint_edr': 95,
                    'windows_events': 70,
                    'sysmon': 80
                }
            },

            # Credential Access
            'T1003': {  # OS Credential Dumping
                'name': 'OS Credential Dumping',
                'sources': {
                    'endpoint_edr': 95,
                    'sysmon': 75,
                    'windows_events': 60,
                    'identity_logs': 50
                }
            },
            'T1110': {  # Brute Force
                'name': 'Brute Force',
                'sources': {
                    'identity_logs': 95,
                    'windows_events': 85,
                    'network_logs': 70,
                    'firewall_logs': 60
                }
            },

            # Lateral Movement
            'T1021': {  # Remote Services
                'name': 'Remote Services',
                'sources': {
                    'network_logs': 85,
                    'identity_logs': 90,
                    'endpoint_edr': 85,
                    'windows_events': 70,
                    'firewall_logs': 75
                }
            },
            'T1570': {  # Lateral Tool Transfer
                'name': 'Lateral Tool Transfer',
                'sources': {
                    'network_logs': 75,
                    'endpoint_edr': 90,
                    'dlp': 80,
                    'file_integrity': 60
                }
            },

            # Collection
            'T1074': {  # Data Staged
                'name': 'Data Staged',
                'sources': {
                    'endpoint_edr': 75,
                    'file_integrity': 60,
                    'dlp': 80
                }
            },
            'T1114': {  # Email Collection
                'name': 'Email Collection',
                'sources': {
                    'email_logs': 90,
                    'endpoint_edr': 80,
                    'cloud_logs': 85
                }
            },

            # Command and Control
            'T1071': {  # Application Layer Protocol
                'name': 'Application Layer Protocol',
                'sources': {
                    'network_ids': 90,
                    'proxy_logs': 85,
                    'dns_logs': 75,
                    'netflow': 60,
                    'firewall_logs': 70
                }
            },
            'T1095': {  # Non-Application Layer Protocol
                'name': 'Non-Application Layer Protocol',
                'sources': {
                    'network_ids': 85,
                    'firewall_logs': 70,
                    'netflow': 65
                }
            },

            # Exfiltration
            'T1041': {  # Exfiltration Over C2 Channel
                'name': 'Exfiltration Over C2 Channel',
                'sources': {
                    'network_ids': 85,
                    'dlp': 90,
                    'proxy_logs': 80,
                    'netflow': 65
                }
            },
            'T1048': {  # Exfiltration Over Alternative Protocol
                'name': 'Exfiltration Over Alternative Protocol',
                'sources': {
                    'dns_logs': 90,
                    'email_gateway': 85,
                    'network_ids': 80
                }
            }
        }

    def _initialize_attack_chains(self) -> Dict:
        """Initialize attack chain definitions"""
        return {
            'ransomware': {
                'name': 'Ransomware Attack',
                'stages': [
                    {'techniques': ['T1566', 'T1078'], 'weight': 0.15},  # Initial Access
                    {'techniques': ['T1059', 'T1047'], 'weight': 0.20},  # Execution
                    {'techniques': ['T1055', 'T1068'], 'weight': 0.15},  # Privilege Escalation
                    {'techniques': ['T1070', 'T1562'], 'weight': 0.10},  # Defense Evasion
                    {'techniques': ['T1074'], 'weight': 0.20},           # Collection
                    {'techniques': ['T1071'], 'weight': 0.20}            # C2
                ],
                'critical_techniques': ['T1059', 'T1074']
            },
            'lateral_movement': {
                'name': 'Lateral Movement',
                'stages': [
                    {'techniques': ['T1078'], 'weight': 0.20},           # Valid Accounts
                    {'techniques': ['T1003', 'T1110'], 'weight': 0.30},  # Credential Access
                    {'techniques': ['T1021', 'T1570'], 'weight': 0.40},  # Lateral Movement
                    {'techniques': ['T1071'], 'weight': 0.10}            # C2
                ],
                'critical_techniques': ['T1021', 'T1003']
            },
            'data_exfiltration': {
                'name': 'Data Exfiltration',
                'stages': [
                    {'techniques': ['T1078'], 'weight': 0.15},           # Access
                    {'techniques': ['T1074', 'T1114'], 'weight': 0.35},  # Collection
                    {'techniques': ['T1041', 'T1048'], 'weight': 0.50}   # Exfiltration
                ],
                'critical_techniques': ['T1041', 'T1048']
            },
            'apt_campaign': {
                'name': 'Advanced Persistent Threat',
                'stages': [
                    {'techniques': ['T1566', 'T1078'], 'weight': 0.10},  # Initial Access
                    {'techniques': ['T1059'], 'weight': 0.15},           # Execution
                    {'techniques': ['T1547', 'T1053'], 'weight': 0.15},  # Persistence
                    {'techniques': ['T1055', 'T1068'], 'weight': 0.15},  # Privilege Escalation
                    {'techniques': ['T1003'], 'weight': 0.15},           # Credential Access
                    {'techniques': ['T1021'], 'weight': 0.15},           # Lateral Movement
                    {'techniques': ['T1074'], 'weight': 0.10},           # Collection
                    {'techniques': ['T1041'], 'weight': 0.05}            # Exfiltration
                ],
                'critical_techniques': ['T1547', 'T1003', 'T1021']
            }
        }

    def calculate_technique_coverage(self, technique_id: str, available_sources: List[str]) -> Dict:
        """Calculate detection coverage for a specific MITRE technique"""

        if technique_id not in self.technique_coverage:
            return {
                'technique': technique_id,
                'coverage': 0,
                'detected_by': [],
                'missing_sources': []
            }

        technique = self.technique_coverage[technique_id]
        coverage_scores = []
        detected_by = []
        missing_sources = []

        # Map available sources to technique coverage
        for source in available_sources:
            # Normalize source name to match coverage mapping
            normalized_source = self._normalize_source_name(source)

            for coverage_source, score in technique['sources'].items():
                if normalized_source in coverage_source or coverage_source in normalized_source:
                    coverage_scores.append(score)
                    detected_by.append({'source': source, 'confidence': score})
                    break

        # Find missing high-value sources
        for coverage_source, score in technique['sources'].items():
            if score >= 80:  # High-value sources
                source_found = False
                for source in available_sources:
                    normalized = self._normalize_source_name(source)
                    if normalized in coverage_source or coverage_source in normalized:
                        source_found = True
                        break
                if not source_found:
                    missing_sources.append({'source': coverage_source, 'value': score})

        # Calculate combined coverage (with diminishing returns)
        if coverage_scores:
            # Sort scores in descending order
            coverage_scores.sort(reverse=True)

            # Apply diminishing returns formula
            combined_coverage = coverage_scores[0]
            for i, score in enumerate(coverage_scores[1:], 1):
                weight = 1 / (i + 1)  # Diminishing weight
                combined_coverage += score * weight * (1 - combined_coverage / 100)

            combined_coverage = min(combined_coverage, 98)  # Cap at 98%
        else:
            combined_coverage = 0

        return {
            'technique': technique_id,
            'name': technique['name'],
            'coverage': round(combined_coverage, 1),
            'detected_by': detected_by,
            'missing_sources': missing_sources,
            'recommendation': self._generate_technique_recommendation(combined_coverage, missing_sources)
        }

    def calculate_attack_chain_fidelity(self, attack_type: str, available_sources: List[str]) -> Dict:
        """Calculate detection fidelity for an entire attack chain"""

        # Check cache (convert dicts to string for sorting)
        if isinstance(available_sources, list) and available_sources and isinstance(available_sources[0], dict):
            source_names = [s.get('name', str(s)) for s in available_sources]
            cache_key = f"{attack_type}:{','.join(sorted(source_names))}"
        else:
            cache_key = f"{attack_type}:{','.join(sorted(available_sources))}"
        if cache_key in self.fidelity_cache:
            cached_result = self.fidelity_cache[cache_key]
            if (datetime.utcnow() - cached_result['calculated_at']).seconds < self.cache_ttl:
                return cached_result['result']

        if attack_type not in self.attack_chains:
            return {
                'attack_type': attack_type,
                'fidelity': 0,
                'error': 'Unknown attack type'
            }

        attack_chain = self.attack_chains[attack_type]
        stage_fidelities = []
        technique_coverage = {}
        critical_coverage = []

        # Calculate coverage for each stage
        for stage in attack_chain['stages']:
            stage_coverages = []

            for technique_id in stage['techniques']:
                coverage = self.calculate_technique_coverage(technique_id, available_sources)
                technique_coverage[technique_id] = coverage
                stage_coverages.append(coverage['coverage'])

                # Track critical techniques
                if technique_id in attack_chain['critical_techniques']:
                    critical_coverage.append(coverage['coverage'])

            # Calculate stage fidelity (best coverage among techniques)
            stage_fidelity = max(stage_coverages) if stage_coverages else 0
            stage_fidelities.append({
                'techniques': stage['techniques'],
                'weight': stage['weight'],
                'fidelity': stage_fidelity
            })

        # Calculate overall fidelity (weighted average)
        overall_fidelity = sum(s['fidelity'] * s['weight'] for s in stage_fidelities)

        # Apply critical technique penalty
        if critical_coverage and min(critical_coverage) < 50:
            penalty = (50 - min(critical_coverage)) / 100
            overall_fidelity *= (1 - penalty)

        # Apply source quality bonus from quality engine
        if self.quality_engine:
            quality_assessment = self.quality_engine.assess_correlation_capability(available_sources)
            quality_bonus = quality_assessment['composite_score'] / 500  # Max 20% bonus
            overall_fidelity *= (1 + quality_bonus)

        overall_fidelity = min(overall_fidelity, 98)  # Cap at 98%

        # Determine confidence level
        confidence_level = self._determine_confidence_level(overall_fidelity)

        # Generate recommendations
        recommendations = self._generate_chain_recommendations(
            attack_chain,
            technique_coverage,
            overall_fidelity
        )

        result = {
            'attack_type': attack_type,
            'attack_name': attack_chain['name'],
            'overall_fidelity': round(overall_fidelity, 1),
            'confidence_level': confidence_level,
            'stage_breakdown': stage_fidelities,
            'critical_technique_coverage': {
                tech: technique_coverage[tech]['coverage']
                for tech in attack_chain['critical_techniques']
            },
            'weakest_stage': min(stage_fidelities, key=lambda x: x['fidelity']),
            'recommendations': recommendations,
            'sources_used': available_sources,
            'calculated_at': datetime.utcnow().isoformat()
        }

        # Cache result
        self.fidelity_cache[cache_key] = {
            'result': result,
            'calculated_at': datetime.utcnow()
        }

        return result

    def calculate_environment_fidelity(self, available_sources: List[str]) -> Dict:
        """Calculate overall detection fidelity for the environment"""

        # Calculate fidelity for all attack types
        attack_fidelities = {}
        for attack_type in self.attack_chains.keys():
            fidelity = self.calculate_attack_chain_fidelity(attack_type, available_sources)
            attack_fidelities[attack_type] = fidelity['overall_fidelity']

        # Calculate technique coverage
        all_techniques = set()
        covered_techniques = {}

        for technique_id in self.technique_coverage.keys():
            all_techniques.add(technique_id)
            coverage = self.calculate_technique_coverage(technique_id, available_sources)
            if coverage['coverage'] > 0:
                covered_techniques[technique_id] = coverage['coverage']

        # Calculate statistics
        technique_coverage_rate = len(covered_techniques) / len(all_techniques) * 100 if all_techniques else 0
        avg_technique_coverage = sum(covered_techniques.values()) / len(covered_techniques) if covered_techniques else 0
        avg_attack_fidelity = sum(attack_fidelities.values()) / len(attack_fidelities) if attack_fidelities else 0

        # Identify top gaps
        technique_gaps = []
        for technique_id in all_techniques:
            if technique_id not in covered_techniques or covered_techniques.get(technique_id, 0) < 50:
                technique = self.technique_coverage[technique_id]
                technique_gaps.append({
                    'technique': technique_id,
                    'name': technique['name'],
                    'current_coverage': covered_techniques.get(technique_id, 0)
                })

        # Sort gaps by importance (techniques used in multiple attack chains)
        technique_importance = {}
        for attack in self.attack_chains.values():
            for stage in attack['stages']:
                for tech in stage['techniques']:
                    technique_importance[tech] = technique_importance.get(tech, 0) + 1

        technique_gaps.sort(key=lambda x: (technique_importance.get(x['technique'], 0), -x['current_coverage']), reverse=True)

        return {
            'overall_fidelity_score': round(avg_attack_fidelity, 1),
            'technique_coverage_rate': round(technique_coverage_rate, 1),
            'average_technique_coverage': round(avg_technique_coverage, 1),
            'attack_fidelities': attack_fidelities,
            'total_techniques': len(all_techniques),
            'covered_techniques': len(covered_techniques),
            'high_confidence_techniques': len([c for c in covered_techniques.values() if c >= 80]),
            'medium_confidence_techniques': len([c for c in covered_techniques.values() if 50 <= c < 80]),
            'low_confidence_techniques': len([c for c in covered_techniques.values() if c < 50]),
            'critical_gaps': technique_gaps[:10],
            'assessment': self._generate_environment_assessment(avg_attack_fidelity, technique_coverage_rate),
            'improvement_priority': self._generate_improvement_priority(technique_gaps, attack_fidelities)
        }

    def _normalize_source_name(self, source: str) -> str:
        """Normalize source name for matching"""
        # Map common source names to coverage categories
        mappings = {
            'crowdstrike': 'endpoint_edr',
            'sentinelone': 'endpoint_edr',
            'carbon_black': 'endpoint_edr',
            'defender': 'endpoint_edr',
            'sysmon': 'sysmon',
            'windows_security': 'windows_events',
            'windows_events': 'windows_events',
            'active_directory': 'identity_logs',
            'azure_ad': 'identity_logs',
            'okta': 'identity_logs',
            'paloalto': 'firewall_logs',
            'fortinet': 'firewall_logs',
            'zeek': 'network_ids',
            'suricata': 'network_ids',
            'cloudtrail': 'cloud_logs',
            'azure_monitor': 'cloud_logs'
        }

        lower_source = source.lower()
        for key, value in mappings.items():
            if key in lower_source:
                return value

        return lower_source

    def _determine_confidence_level(self, fidelity: float) -> str:
        """Determine confidence level based on fidelity score"""
        if fidelity >= 90:
            return "VERY HIGH"
        elif fidelity >= 75:
            return "HIGH"
        elif fidelity >= 60:
            return "MEDIUM"
        elif fidelity >= 40:
            return "LOW"
        else:
            return "VERY LOW"

    def _generate_technique_recommendation(self, coverage: float, missing_sources: List) -> str:
        """Generate recommendation for technique coverage"""
        if coverage >= 90:
            return "Excellent coverage for this technique"
        elif coverage >= 70:
            return f"Good coverage, consider adding: {missing_sources[0]['source']}" if missing_sources else "Good coverage"
        elif coverage >= 50:
            top_missing = ', '.join([m['source'] for m in missing_sources[:2]])
            return f"Moderate coverage, add: {top_missing}" if missing_sources else "Moderate coverage"
        else:
            top_missing = ', '.join([m['source'] for m in missing_sources[:3]])
            return f"Poor coverage, urgently add: {top_missing}" if missing_sources else "Poor coverage, need better sources"

    def _generate_chain_recommendations(self, attack_chain: Dict, technique_coverage: Dict, fidelity: float) -> List[str]:
        """Generate recommendations for attack chain coverage"""
        recommendations = []

        if fidelity < 50:
            recommendations.append("CRITICAL: Detection capability is severely limited")

        # Check critical techniques
        for critical_tech in attack_chain['critical_techniques']:
            if critical_tech in technique_coverage:
                coverage = technique_coverage[critical_tech]['coverage']
                if coverage < 70:
                    tech_name = self.technique_coverage[critical_tech]['name']
                    recommendations.append(f"Improve detection for critical technique: {tech_name} (current: {coverage}%)")

        # Find weakest techniques
        weak_techniques = [
            (tech_id, cov['coverage'])
            for tech_id, cov in technique_coverage.items()
            if cov['coverage'] < 60
        ]

        if weak_techniques:
            weakest = min(weak_techniques, key=lambda x: x[1])
            tech_name = self.technique_coverage[weakest[0]]['name']
            recommendations.append(f"Weakest coverage: {tech_name} ({weakest[1]}%)")

        if fidelity >= 80:
            recommendations.append("Consider adding deception technology for early warning")

        return recommendations[:5]

    def _generate_environment_assessment(self, avg_fidelity: float, coverage_rate: float) -> str:
        """Generate overall environment assessment"""
        if avg_fidelity >= 85 and coverage_rate >= 80:
            return "EXCELLENT: Comprehensive detection capability with high confidence"
        elif avg_fidelity >= 70 and coverage_rate >= 65:
            return "GOOD: Strong detection capability with some gaps"
        elif avg_fidelity >= 55 and coverage_rate >= 50:
            return "MODERATE: Acceptable detection with significant room for improvement"
        elif avg_fidelity >= 40 and coverage_rate >= 35:
            return "POOR: Limited detection capability, major improvements needed"
        else:
            return "CRITICAL: Severely limited detection, immediate action required"

    def _generate_improvement_priority(self, gaps: List, attack_fidelities: Dict) -> List[str]:
        """Generate prioritized improvement recommendations"""
        priorities = []

        # Find weakest attack type
        weakest_attack = min(attack_fidelities.items(), key=lambda x: x[1])
        if weakest_attack[1] < 70:
            priorities.append(f"Improve {weakest_attack[0]} detection (current: {weakest_attack[1]}%)")

        # Top technique gaps
        if gaps:
            top_gap = gaps[0]
            priorities.append(f"Add detection for {top_gap['name']}")

        # Source recommendations based on gaps
        source_recommendations = set()
        for gap in gaps[:5]:
            technique = self.technique_coverage.get(gap['technique'], {})
            for source, score in technique.get('sources', {}).items():
                if score >= 80:
                    source_recommendations.add(source)

        if source_recommendations:
            priorities.append(f"Consider adding: {', '.join(list(source_recommendations)[:3])}")

        return priorities[:5]


# Example usage
if __name__ == "__main__":
    # Create calculator
    calculator = DetectionFidelityCalculator()

    # Test with different source configurations
    test_configs = [
        {
            'name': 'Basic Setup',
            'sources': ['windows_events', 'firewall', 'active_directory']
        },
        {
            'name': 'Standard Setup',
            'sources': ['windows_events', 'sysmon', 'active_directory', 'paloalto', 'office365']
        },
        {
            'name': 'Advanced Setup',
            'sources': ['crowdstrike', 'zeek', 'active_directory', 'cloudtrail', 'paloalto', 'splunk']
        }
    ]

    for config in test_configs:
        print(f"\n{'='*60}")
        print(f"Configuration: {config['name']}")
        print(f"Sources: {', '.join(config['sources'])}")
        print('='*60)

        # Calculate environment fidelity
        env_fidelity = calculator.calculate_environment_fidelity(config['sources'])
        print(f"\nOverall Fidelity Score: {env_fidelity['overall_fidelity_score']}%")
        print(f"Technique Coverage: {env_fidelity['covered_techniques']}/{env_fidelity['total_techniques']} ({env_fidelity['technique_coverage_rate']}%)")
        print(f"Assessment: {env_fidelity['assessment']}")

        # Show attack-specific fidelity
        print("\nAttack Detection Confidence:")
        for attack, fidelity in env_fidelity['attack_fidelities'].items():
            print(f"  {attack}: {fidelity}%")

        # Show priorities
        print("\nImprovement Priorities:")
        for i, priority in enumerate(env_fidelity['improvement_priority'], 1):
            print(f"  {i}. {priority}")

    # Test specific attack chain
    print(f"\n{'='*60}")
    print("Detailed Ransomware Detection Analysis")
    print('='*60)

    sources = ['crowdstrike', 'active_directory', 'paloalto', 'zeek']
    ransomware_fidelity = calculator.calculate_attack_chain_fidelity('ransomware', sources)

    print(f"\nRansomware Detection Fidelity: {ransomware_fidelity['overall_fidelity']}%")
    print(f"Confidence Level: {ransomware_fidelity['confidence_level']}")
    print("\nCritical Technique Coverage:")
    for tech, coverage in ransomware_fidelity['critical_technique_coverage'].items():
        print(f"  {tech}: {coverage}%")

    print("\nRecommendations:")
    for rec in ransomware_fidelity['recommendations']:
        print(f"  - {rec}")