"""
Consensus Engine Module
Handles AI consensus validation and multi-model response analysis
"""

import json
import uuid
from typing import Dict, Any, List
from datetime import datetime
import logging


# Database helper functions (psycopg2 pattern)
def db_execute(connection, query: str, *params):
    """Helper to execute non-query commands with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    cursor.close()  # CRITICAL: Always close to prevent leaks


def db_fetchone(connection, query: str, *params):
    """Helper to fetch a single row with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    result = cursor.fetchone()
    cursor.close()
    return result


def db_fetchall(connection, query: str, *params):
    """Helper to fetch all rows with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    results = cursor.fetchall()
    cursor.close()
    return results


class ConsensusEngine:
    """Manages AI consensus validation and response analysis"""

    def __init__(self, ai_model_manager, cost_tracker, db_connection, logger: logging.Logger = None):
        self.ai_model_manager = ai_model_manager
        self.cost_tracker = cost_tracker
        self.db_connection = db_connection
        self.logger = logger or logging.getLogger(__name__)

        # Consensus Configuration
        self.consensus_threshold = 0.80

    async def get_ai_consensus(self, pattern_data: Dict[str, Any], models: List[str]) -> Dict[str, Any]:
        """Get consensus from multiple AI models"""
        responses = []

        for model_tier in models:
            try:
                # Call AI model
                response = await self.ai_model_manager.call_ai_model(model_tier, pattern_data)
                responses.append(response)

                # Track costs through cost tracker
                model_info = self.ai_model_manager.get_model_info(model_tier)
                self.cost_tracker.track_request(model_tier, model_info['cost_per_request'])

            except Exception as e:
                self.logger.error(f"AI model {model_tier} error: {e}")

        # Calculate consensus
        consensus = self._calculate_consensus(responses)
        return consensus

    def _calculate_consensus(self, responses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate consensus from multiple AI responses"""
        if not responses:
            return {'consensus': False, 'confidence': 0.0}

        # Calculate average confidence
        avg_confidence = sum(r['confidence'] for r in responses) / len(responses)

        # Determine consensus
        consensus_reached = avg_confidence >= self.consensus_threshold

        return {
            'consensus': consensus_reached,
            'confidence': avg_confidence,
            'num_models': len(responses),
            'responses': responses,
            'reasoning': f"Consensus from {len(responses)} models"
        }

    def store_consensus_result(self, result_id: str, result: Dict[str, Any]):
        """Store consensus result in database"""
        try:
            db_execute(
                self.db_connection,
                """
                INSERT INTO intelligence_consensus_results (result_id, consensus_data, created_at)
                VALUES (%s, %s, %s)
                """,
                result_id, json.dumps(result), datetime.utcnow()
            )
        except Exception as e:
            self.logger.error(f"Failed to store consensus result: {e}")

    def get_consensus_result(self, result_id: str) -> Dict[str, Any]:
        """Retrieve consensus result from database"""
        try:
            result = db_fetchone(
                self.db_connection,
                """
                SELECT consensus_data, created_at FROM intelligence_consensus_results
                WHERE result_id = %s
                """,
                result_id
            )

            if result:
                return {
                    'result_id': result_id,
                    'consensus_data': json.loads(result['consensus_data']),
                    'created_at': result['created_at']
                }

        except Exception as e:
            self.logger.error(f"Failed to retrieve consensus result {result_id}: {e}")

        return None

    def analyze_consensus_quality(self, consensus_result: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the quality of consensus results"""
        responses = consensus_result.get('responses', [])

        if not responses:
            return {'quality': 'poor', 'issues': ['No responses available']}

        issues = []

        # Check for variance in confidence scores
        confidences = [r.get('confidence', 0) for r in responses]
        confidence_variance = max(confidences) - min(confidences)

        if confidence_variance > 0.3:
            issues.append(f"High confidence variance: {confidence_variance:.2f}")

        # Check for model agreement
        if len(responses) < 2:
            issues.append("Insufficient models for proper consensus")

        # Calculate overall quality
        quality = 'excellent' if not issues else 'good' if len(issues) <= 1 else 'poor'

        return {
            'quality': quality,
            'confidence_variance': confidence_variance,
            'num_models': len(responses),
            'avg_confidence': sum(confidences) / len(confidences) if confidences else 0,
            'issues': issues
        }

    def get_consensus_statistics(self) -> Dict[str, Any]:
        """Get consensus statistics from database"""
        try:
            # Get basic counts
            stats = db_fetchone(
                self.db_connection,
                """
                SELECT
                    COUNT(*) as total_consensus,
                    AVG(CAST(consensus_data->>'confidence' AS FLOAT)) as avg_confidence,
                    COUNT(CASE WHEN CAST(consensus_data->>'consensus' AS BOOLEAN) = true THEN 1 END) as successful_consensus
                FROM intelligence_consensus_results
                WHERE created_at > NOW() - INTERVAL '7 days'
                """
            )

            # Get daily breakdown
            daily_stats = db_fetchall(
                self.db_connection,
                """
                SELECT
                    DATE(created_at) as date,
                    COUNT(*) as daily_count,
                    AVG(CAST(consensus_data->>'confidence' AS FLOAT)) as daily_avg_confidence
                FROM intelligence_consensus_results
                WHERE created_at > NOW() - INTERVAL '7 days'
                GROUP BY DATE(created_at)
                ORDER BY date DESC
                """
            )

            return {
                'total_consensus': stats['total_consensus'] if stats else 0,
                'avg_confidence': stats['avg_confidence'] if stats else 0,
                'successful_consensus': stats['successful_consensus'] if stats else 0,
                'success_rate': (stats['successful_consensus'] / stats['total_consensus'] * 100)
                               if stats and stats['total_consensus'] > 0 else 0,
                'daily_breakdown': daily_stats
            }

        except Exception as e:
            self.logger.error(f"Failed to get consensus statistics: {e}")
            return {
                'total_consensus': 0,
                'avg_confidence': 0,
                'successful_consensus': 0,
                'success_rate': 0,
                'daily_breakdown': []
            }

    def cleanup_old_consensus_results(self, days_to_keep: int = 30):
        """Clean up old consensus results"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                DELETE FROM intelligence_consensus_results
                WHERE created_at < NOW() - INTERVAL '%s days'
            """, (days_to_keep,))

            deleted_count = cursor.rowcount
            cursor.close()  # Fixed cursor leak
            if deleted_count > 0:
                self.logger.info(f"Cleaned up {deleted_count} old consensus results")

        except Exception as e:
            self.logger.error(f"Failed to cleanup consensus results: {e}")

    def validate_consensus_threshold(self, new_threshold: float) -> bool:
        """Validate a new consensus threshold"""
        if not isinstance(new_threshold, (int, float)):
            return False

        if new_threshold < 0.0 or new_threshold > 1.0:
            return False

        return True

    def update_consensus_threshold(self, new_threshold: float):
        """Update the consensus threshold"""
        if self.validate_consensus_threshold(new_threshold):
            old_threshold = self.consensus_threshold
            self.consensus_threshold = new_threshold
            self.logger.info(f"Updated consensus threshold from {old_threshold} to {new_threshold}")
        else:
            raise ValueError(f"Invalid consensus threshold: {new_threshold}. Must be between 0.0 and 1.0")
