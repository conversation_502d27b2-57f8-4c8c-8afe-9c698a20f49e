"""
Pattern Manager Mo<PERSON>le
Handles pattern crystallization, validation, storage, and management
"""

import json
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging
import asyncio

# Database helper functions (psycopg2 pattern)
def db_execute(connection, query: str, *params):
    """Helper to execute non-query commands with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    connection.commit()
    cursor.close()  # CRITICAL: Always close to prevent leaks


def db_fetchone(connection, query: str, *params):
    """Helper to fetch a single row with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    result = cursor.fetchone()
    cursor.close()
    return result


def db_fetchall(connection, query: str, *params):
    """Helper to fetch all rows with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    results = cursor.fetchall()
    cursor.close()
    return results


class PatternManager:
    """Manages pattern crystallization, validation, and storage"""

    def __init__(self, db_connection, ai_model_manager, logger: logging.Logger = None):
        self.db_connection = db_connection
        self.ai_model_manager = ai_model_manager
        self.logger = logger or logging.getLogger(__name__)

        # Pattern Management
        self.pattern_library = {}
        self.confidence_threshold = 0.70

    async def crystallize_pattern(self, ai_insights: Dict[str, Any]) -> Dict[str, Any]:
        """Crystallize AI insights into deterministic pattern"""
        # Convert AI insights into regex pattern or deterministic rule
        crystallized = {
            'type': 'regex',
            'pattern': self._extract_regex_from_insights(ai_insights),
            'confidence': ai_insights.get('confidence', 0),
            'source_models': ai_insights.get('responses', []),
            'created_at': datetime.utcnow().isoformat(),
            'cost_to_create': self._calculate_creation_cost(ai_insights)
        }

        return crystallized

    def _extract_regex_from_insights(self, insights: Dict[str, Any]) -> str:
        """Extract regex pattern from AI insights (mock implementation)"""
        # This would contain sophisticated pattern extraction logic
        return r".*"  # Placeholder

    async def validate_pattern(self, pattern: Dict[str, Any], validation_type: str) -> Dict[str, Any]:
        """Validate pattern using AI model"""
        # Select model based on validation importance
        model_tier = 'low_cost' if validation_type == 'critical' else 'free'

        # Call AI for validation
        validation = await self.ai_model_manager.call_ai_model(model_tier, {
            'pattern': pattern,
            'task': 'validation'
        })

        return validation

    async def analyze_unknown_pattern(self, log_sample: str, source_type: str) -> Dict[str, Any]:
        """Analyze unknown pattern using AI"""
        # Use high-quality model for unknown pattern analysis
        analysis = await self.ai_model_manager.call_ai_model('high_quality', {
            'log_sample': log_sample,
            'source_type': source_type,
            'task': 'pattern_discovery'
        })

        return analysis

    def store_crystallized_pattern(self, pattern_id: str, pattern: Dict[str, Any]):
        """Store crystallized pattern in pattern library"""
        try:
            db_execute(
                self.db_connection,
                """
                INSERT INTO pattern_library (pattern_id, pattern_data, pattern_type, created_at)
                VALUES (%s, %s, %s, %s)
                ON CONFLICT (pattern_id) DO UPDATE SET pattern_data = %s, updated_at = %s
                """,
                pattern_id, json.dumps(pattern), 'crystallized', datetime.utcnow(),
                json.dumps(pattern), datetime.utcnow()
            )

            # Also store in local cache
            self.pattern_library[pattern_id] = pattern

        except Exception as e:
            self.logger.error(f"Failed to store crystallized pattern: {e}")

    def store_entity_pattern(self, pattern_id: str, pattern: Dict[str, Any]):
        """Store entity pattern in library"""
        try:
            db_execute(
                self.db_connection,
                """
                INSERT INTO pattern_library (pattern_id, pattern_data, pattern_type, created_at)
                VALUES (%s, %s, %s, %s)
                """,
                pattern_id, json.dumps(pattern), 'entity', datetime.utcnow()
            )

            self.pattern_library[pattern_id] = pattern

        except Exception as e:
            self.logger.error(f"Failed to store entity pattern: {e}")

    def generate_pattern_id(self, pattern: Dict[str, Any]) -> str:
        """Generate unique pattern ID"""
        return f"pattern_{hash(str(pattern)) % 1000000}"

    def calculate_cost_savings(self, pattern: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate cost savings from crystallization"""
        creation_cost = pattern.get('cost_to_create', 0)
        estimated_uses = 1000  # Estimated future uses
        cost_per_use = 0.002   # Average cost per AI call

        total_savings = (estimated_uses * cost_per_use) - creation_cost
        roi_percentage = (total_savings / creation_cost * 100) if creation_cost > 0 else float('inf')

        return {
            'creation_cost': creation_cost,
            'estimated_savings': total_savings,
            'roi_percentage': roi_percentage,
            'break_even_uses': int(creation_cost / cost_per_use) if cost_per_use > 0 else 0
        }

    def _calculate_creation_cost(self, insights: Dict[str, Any]) -> float:
        """Calculate cost to create pattern"""
        # Sum up costs from all AI model calls used
        total_cost = 0.0
        responses = insights.get('responses', [])

        for response in responses:
            model_name = response.get('model', '')
            for tier, model_info in self.ai_model_manager.ai_models.items():
                if model_info['name'] in model_name:
                    total_cost += model_info['cost_per_request']
                    break

        return total_cost

    async def cleanup_patterns(self):
        """Periodic cleanup of old patterns"""
        try:
            # Clean up patterns older than 30 days with low usage
            cursor = self.db_connection.cursor()
            cursor.execute("""
                DELETE FROM pattern_library
                WHERE created_at < NOW() - INTERVAL '30 days'
                AND usage_count < 5
            """)

            deleted_count = cursor.rowcount
            cursor.close()  # Close cursor after getting rowcount

            if deleted_count > 0:
                self.logger.info(f"Cleaned up {deleted_count} unused patterns")

        except Exception as e:
            self.logger.error(f"Pattern cleanup error: {e}")

    async def maintain_library(self):
        """Maintain pattern library performance"""
        try:
            # Update pattern usage statistics
            db_execute(
                self.db_connection,
                """
                UPDATE pattern_library
                SET last_used = NOW()
                WHERE pattern_id IN (
                    SELECT DISTINCT pattern_id FROM pattern_usage_log
                    WHERE created_at > NOW() - INTERVAL '1 hour'
                )
                """
            )

            # Optimize frequently used patterns
            frequent_patterns = db_fetchall(
                self.db_connection,
                """
                SELECT pattern_id, usage_count FROM pattern_library
                WHERE usage_count > 100
                ORDER BY usage_count DESC
                LIMIT 100
                """
            )

            for pattern_row in frequent_patterns:
                pattern_id = pattern_row['pattern_id']
                if pattern_id not in self.pattern_library:
                    # Load into cache
                    pattern_data = db_fetchone(
                        self.db_connection,
                        "SELECT pattern_data FROM pattern_library WHERE pattern_id = %s",
                        pattern_id
                    )
                    if pattern_data:
                        self.pattern_library[pattern_id] = json.loads(pattern_data['pattern_data'])

        except Exception as e:
            self.logger.error(f"Library maintenance error: {e}")

    def get_pattern(self, pattern_id: str) -> Optional[Dict[str, Any]]:
        """Get pattern from library or database"""
        # Check local cache first
        if pattern_id in self.pattern_library:
            return self.pattern_library[pattern_id]

        # Query database
        try:
            result = db_fetchone(
                self.db_connection,
                "SELECT pattern_data FROM pattern_library WHERE pattern_id = %s",
                pattern_id
            )

            if result:
                pattern_data = json.loads(result['pattern_data'])
                # Cache for future use
                self.pattern_library[pattern_id] = pattern_data
                return pattern_data

        except Exception as e:
            self.logger.error(f"Failed to get pattern {pattern_id}: {e}")

        return None

    def list_patterns(self, pattern_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """List patterns with optional type filter"""
        try:
            if pattern_type:
                return db_fetchall(
                    self.db_connection,
                    """
                    SELECT pattern_id, pattern_type, created_at, usage_count
                    FROM pattern_library
                    WHERE pattern_type = %s
                    ORDER BY usage_count DESC, created_at DESC
                    """,
                    pattern_type
                )
            else:
                return db_fetchall(
                    self.db_connection,
                    """
                    SELECT pattern_id, pattern_type, created_at, usage_count
                    FROM pattern_library
                    ORDER BY usage_count DESC, created_at DESC
                    """
                )

        except Exception as e:
            self.logger.error(f"Failed to list patterns: {e}")
            return []