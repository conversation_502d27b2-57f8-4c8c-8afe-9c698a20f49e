# Use Case Context Engine Dockerfile
FROM siemless-v2-base:latest

# Copy Use Case Context Engine specific code
COPY engines/use_case_context/ /app/engines/use_case_context/



# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD redis-cli -h $REDIS_HOST ping || exit 1

# Set the command to run Use Case Context Engine
CMD ["python", "engines/use_case_context/use_case_context.py"]
