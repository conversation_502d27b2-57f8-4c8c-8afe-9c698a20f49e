"""
SIEMLess v2.0 - Enhanced Contextualization Engine
Replicating v0.2 capabilities with AI-powered enhancements
"""

import asyncio
import json
import time
import uuid
import ipaddress
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from base_engine import BaseEngine
import psycopg2.extras

class EnhancedContextualizationEngine(BaseEngine):
    """Enhanced Contextualization Engine with v0.2 capabilities"""

    def __init__(self):
        super().__init__("contextualization")

        # Cache configurations
        self.entity_cache = {}
        self.relationship_cache = {}
        self.zone_cache = {}
        self.enrichment_rules_cache = {}

        # Statistics tracking
        self.enrichment_stats = {
            'entities_processed': 0,
            'relationships_created': 0,
            'enrichments_applied': 0,
            'risk_scores_calculated': 0,
            'zones_assigned': 0,
            'baselines_created': 0,
            'cache_hits': 0,
            'ai_enrichments': 0
        }

        # Enrichment sources with priority
        self.enrichment_sources = {
            'threat_intel': {'enabled': True, 'priority': 1, 'ai_model': 'gemma-3-27b-it'},
            'geolocation': {'enabled': True, 'priority': 2, 'ai_model': None},
            'asset_inventory': {'enabled': True, 'priority': 3, 'ai_model': None},
            'user_directory': {'enabled': True, 'priority': 4, 'ai_model': None},
            'behavioral_analysis': {'enabled': True, 'priority': 5, 'ai_model': 'gemma-3-27b-it'},
            'business_context': {'enabled': True, 'priority': 6, 'ai_model': 'gemma-3-27b-it'},
            'compliance_mapping': {'enabled': True, 'priority': 7, 'ai_model': 'gemini-2.5-flash'}
        }

        # Risk factor weights
        self.risk_weights = {
            'threat_intel': 0.25,
            'vulnerability': 0.20,
            'anomaly': 0.20,
            'exposure': 0.15,
            'privilege_level': 0.10,
            'incident_history': 0.05,
            'compliance': 0.05
        }

    async def start_engine_tasks(self) -> List[asyncio.Task]:
        """Start enhanced contextualization background tasks"""
        tasks = []

        # Load configurations
        await self._load_security_zones()
        await self._load_enrichment_rules()

        # Core enrichment tasks
        tasks.append(asyncio.create_task(self._enrichment_loop()))
        tasks.append(asyncio.create_task(self._risk_assessment_loop()))
        tasks.append(asyncio.create_task(self._zone_management_loop()))

        # Analysis tasks
        tasks.append(asyncio.create_task(self._behavioral_analysis_loop()))
        tasks.append(asyncio.create_task(self._relationship_analysis_loop()))
        tasks.append(asyncio.create_task(self._session_tracking_loop()))

        # Maintenance tasks
        tasks.append(asyncio.create_task(self._baseline_update_loop()))
        tasks.append(asyncio.create_task(self._cache_maintenance_loop()))

        self.logger.info("Enhanced Contextualization Engine tasks started")
        return tasks

    def get_subscribed_channels(self) -> List[str]:
        """Return enhanced list of Redis channels"""
        return [
            # Entity operations
            'contextualization.enrich_log',
            'contextualization.enrich_entity',
            'contextualization.bulk_enrich',

            # Risk and security
            'contextualization.assess_risk',
            'contextualization.assign_zone',
            'contextualization.update_baseline',

            # Relationships
            'contextualization.find_relationships',
            'contextualization.track_session',

            # Business context
            'contextualization.add_business_context',
            'contextualization.update_criticality',
            'contextualization.apply_tags',

            # Analysis
            'contextualization.analyze_behavior',
            'contextualization.get_context',
            'contextualization.generate_report'
        ]

    async def process_message(self, message: Dict[str, Any]):
        """Process incoming contextualization messages"""
        try:
            data = json.loads(message['data'])
            channel = message['channel']

            self.logger.info(f"Processing message from {channel}")

            # Route to appropriate handler
            handlers = {
                'contextualization.enrich_log': self._handle_enrich_log,
                'contextualization.enrich_entity': self._handle_enrich_entity,
                'contextualization.bulk_enrich': self._handle_bulk_enrich,
                'contextualization.assess_risk': self._handle_assess_risk,
                'contextualization.assign_zone': self._handle_assign_zone,
                'contextualization.update_baseline': self._handle_update_baseline,
                'contextualization.find_relationships': self._handle_find_relationships,
                'contextualization.track_session': self._handle_track_session,
                'contextualization.add_business_context': self._handle_add_business_context,
                'contextualization.update_criticality': self._handle_update_criticality,
                'contextualization.apply_tags': self._handle_apply_tags,
                'contextualization.analyze_behavior': self._handle_analyze_behavior,
                'contextualization.get_context': self._handle_get_context,
                'contextualization.generate_report': self._handle_generate_report
            }

            handler = handlers.get(channel)
            if handler:
                await handler(data['data'])
            else:
                self.logger.warning(f"No handler for channel: {channel}")

        except Exception as e:
            self.logger.error(f"Error processing message: {e}")

    # =========================================================================
    # ENTITY ENRICHMENT METHODS
    # =========================================================================

    async def _handle_enrich_entity(self, data: Dict[str, Any]):
        """Enhanced entity enrichment with full context"""
        entity_type = data.get('entity_type')
        entity_value = data.get('entity_value')
        enrich_level = data.get('enrich_level', 'comprehensive')

        # Get or create entity with enhanced properties
        entity_id = await self._get_or_create_enhanced_entity(
            entity_type, entity_value, data.get('properties', {})
        )

        # Apply comprehensive enrichment
        enrichments = await self._apply_comprehensive_enrichment(
            entity_id, entity_type, entity_value, enrich_level
        )

        # Auto-assign security zone
        zone_id = await self._auto_assign_security_zone(entity_id, entity_type, entity_value)

        # Calculate risk score
        risk_score = await self._calculate_entity_risk_score(entity_id)

        # Create baseline if needed
        if enrich_level in ['comprehensive', 'baseline']:
            await self._create_entity_baseline(entity_id, entity_type)

        # Audit the enrichment
        await self._audit_enrichment(
            entity_id, 'automatic', 'enhanced_engine',
            {'enrichments': enrichments, 'zone_id': zone_id, 'risk_score': risk_score}
        )

        # Publish enrichment result
        self.publish_message('contextualization.entity_enriched', {
            'entity_id': str(entity_id),
            'entity_type': entity_type,
            'entity_value': entity_value,
            'enrichments': enrichments,
            'zone_id': str(zone_id) if zone_id else None,
            'risk_score': risk_score,
            'timestamp': datetime.utcnow().isoformat()
        })

        self.enrichment_stats['entities_processed'] += 1

    async def _get_or_create_enhanced_entity(self, entity_type: str, entity_value: str,
                                            properties: Dict[str, Any]) -> uuid.UUID:
        """Create entity with enhanced properties"""
        cursor = self.db_connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        try:
            # Check if entity exists
            cursor.execute("""
                SELECT entity_id FROM entities
                WHERE entity_type = %s AND entity_value = %s
            """, (entity_type, entity_value))

            result = cursor.fetchone()

            if result:
                # Update existing entity
                entity_id = result['entity_id']
                cursor.execute("""
                    UPDATE entities
                    SET properties = COALESCE(properties, '{}'::jsonb) || %s::jsonb,
                        last_seen = NOW(),
                        event_count = event_count + 1
                    WHERE entity_id = %s
                """, (json.dumps(properties), entity_id))
            else:
                # Create new entity with enhanced fields
                entity_id = uuid.uuid4()
                cursor.execute("""
                    INSERT INTO entities (
                        entity_id, entity_type, entity_value, properties,
                        first_seen, last_seen, event_count
                    ) VALUES (%s, %s, %s, %s, NOW(), NOW(), 1)
                """, (entity_id, entity_type, entity_value, json.dumps(properties)))

            self.db_connection.commit()
            return entity_id

        except Exception as e:
            self.logger.error(f"Error creating enhanced entity: {e}")
            self.db_connection.rollback()
            raise

    async def _apply_comprehensive_enrichment(self, entity_id: uuid.UUID,
                                             entity_type: str, entity_value: str,
                                             enrich_level: str) -> Dict[str, Any]:
        """Apply all enrichment sources based on priority"""
        enrichments = {}

        for source_name, config in sorted(
            self.enrichment_sources.items(),
            key=lambda x: x[1]['priority']
        ):
            if not config['enabled']:
                continue

            try:
                if source_name == 'threat_intel':
                    enrichments['threat_intel'] = await self._enrich_threat_intel(
                        entity_type, entity_value, config.get('ai_model')
                    )
                elif source_name == 'geolocation' and entity_type in ['ip_address', 'source_ip', 'destination_ip']:
                    enrichments['geolocation'] = await self._enrich_geolocation(entity_value)
                elif source_name == 'asset_inventory' and entity_type == 'hostname':
                    enrichments['asset_inventory'] = await self._enrich_asset_inventory(entity_value)
                elif source_name == 'user_directory' and entity_type in ['user', 'username']:
                    enrichments['user_directory'] = await self._enrich_user_directory(entity_value)
                elif source_name == 'behavioral_analysis' and enrich_level == 'comprehensive':
                    enrichments['behavioral_analysis'] = await self._enrich_behavioral_analysis(
                        entity_id, entity_type, config.get('ai_model')
                    )
                elif source_name == 'business_context':
                    enrichments['business_context'] = await self._enrich_business_context(
                        entity_type, entity_value, config.get('ai_model')
                    )
                elif source_name == 'compliance_mapping':
                    enrichments['compliance_mapping'] = await self._enrich_compliance(
                        entity_type, entity_value, config.get('ai_model')
                    )

                self.enrichment_stats['enrichments_applied'] += 1

            except Exception as e:
                self.logger.error(f"Error applying {source_name} enrichment: {e}")

        # Store enrichments in entity
        await self._update_entity_enrichment(entity_id, enrichments)

        return enrichments

    async def _enrich_threat_intel(self, entity_type: str, entity_value: str,
                                  ai_model: Optional[str] = None) -> Dict[str, Any]:
        """Enhanced threat intelligence enrichment"""
        threat_data = {
            'is_malicious': False,
            'threat_types': [],
            'reputation_score': 85,
            'threat_actors': [],
            'campaigns': [],
            'iocs': [],
            'mitre_techniques': [],
            'last_seen': None,
            'first_seen': None,
            'confidence': 0.0
        }

        # Simulate enhanced threat intelligence
        if entity_type in ['ip_address', 'source_ip']:
            # Check if IP is in threat ranges
            threat_ranges = ['*********/24', '************/24', '***********/24']
            for threat_range in threat_ranges:
                if self._ip_in_range(entity_value, threat_range):
                    threat_data['is_malicious'] = True
                    threat_data['threat_types'] = ['botnet', 'c2']
                    threat_data['reputation_score'] = 15
                    threat_data['confidence'] = 0.9
                    break

        # Use AI for advanced threat analysis if configured
        if ai_model and not threat_data['is_malicious']:
            threat_data['ai_analysis'] = f"AI-analyzed with {ai_model}"
            self.enrichment_stats['ai_enrichments'] += 1

        return threat_data

    async def _enrich_geolocation(self, ip_value: str) -> Dict[str, Any]:
        """Enhanced geolocation enrichment"""
        # Simulate comprehensive geo data
        geo_data = {
            'country': 'US',
            'country_code': 'US',
            'region': 'California',
            'city': 'San Francisco',
            'latitude': 37.7749,
            'longitude': -122.4194,
            'timezone': 'America/Los_Angeles',
            'asn': 'AS13335',
            'organization': 'Cloudflare',
            'isp': 'Cloudflare Inc.',
            'is_vpn': False,
            'is_proxy': False,
            'is_tor': False,
            'is_datacenter': True
        }
        return geo_data

    async def _enrich_asset_inventory(self, hostname: str) -> Dict[str, Any]:
        """Enhanced asset inventory enrichment"""
        # Simulate asset data
        asset_data = {
            'asset_id': str(uuid.uuid4()),
            'os': 'Windows Server 2019',
            'os_version': '10.0.17763',
            'hardware': 'Dell PowerEdge R740',
            'location': 'Datacenter A, Rack 42',
            'owner': 'IT Operations',
            'department': 'Infrastructure',
            'environment': 'production',
            'patch_level': 'Current',
            'last_boot': datetime.utcnow().isoformat(),
            'services': ['IIS', 'SQL Server', 'RDP'],
            'open_ports': [80, 443, 3389, 1433],
            'installed_software': []
        }

        # Special handling for critical assets
        if 'PROD' in hostname.upper() or 'PAYMENT' in hostname.upper():
            asset_data['is_critical'] = True
            asset_data['compliance_scope'] = ['PCI-DSS', 'SOX']

        return asset_data

    async def _enrich_user_directory(self, username: str) -> Dict[str, Any]:
        """Enhanced user directory enrichment"""
        user_data = {
            'full_name': f"{username.split('.')[0].title()} {username.split('.')[-1].title() if '.' in username else 'User'}",
            'email': f"{username}@company.com",
            'department': 'Engineering',
            'manager': 'john.manager',
            'title': 'Senior Engineer',
            'location': 'San Francisco Office',
            'phone': '******-0100',
            'employee_id': f"EMP{hash(username) % 100000:05d}",
            'hire_date': '2020-01-15',
            'last_login': datetime.utcnow().isoformat(),
            'groups': ['domain users', 'engineering', 'vpn_users'],
            'privileged': username.lower() in ['admin', 'administrator', 'root'],
            'mfa_enabled': True,
            'account_status': 'active'
        }
        return user_data

    async def _enrich_behavioral_analysis(self, entity_id: uuid.UUID, entity_type: str,
                                         ai_model: Optional[str] = None) -> Dict[str, Any]:
        """AI-powered behavioral analysis"""
        behavioral_data = {
            'normal_activity_hours': '09:00-18:00',
            'typical_locations': ['Office', 'VPN'],
            'common_applications': ['outlook.exe', 'chrome.exe', 'code.exe'],
            'peer_group': 'standard_users',
            'risk_indicators': [],
            'anomalies_detected': 0,
            'behavioral_score': 85,
            'last_analysis': datetime.utcnow().isoformat()
        }

        if ai_model:
            behavioral_data['ai_model_used'] = ai_model
            self.enrichment_stats['ai_enrichments'] += 1

        return behavioral_data

    async def _enrich_business_context(self, entity_type: str, entity_value: str,
                                      ai_model: Optional[str] = None) -> Dict[str, Any]:
        """Enhanced business context enrichment"""
        business_data = {}

        if entity_type == 'hostname':
            if 'PAYMENT' in entity_value.upper():
                business_data = {
                    'business_function': 'Payment Processing',
                    'criticality': 'critical',
                    'data_classification': 'highly_sensitive',
                    'regulatory_scope': ['PCI-DSS', 'SOX'],
                    'recovery_time_objective': '1 hour',
                    'recovery_point_objective': '15 minutes',
                    'business_owner': 'Finance Department',
                    'technical_owner': 'Payment Systems Team'
                }
            elif 'DB' in entity_value.upper():
                business_data = {
                    'business_function': 'Database Services',
                    'criticality': 'high',
                    'data_classification': 'sensitive',
                    'regulatory_scope': ['SOX', 'GDPR'],
                    'backup_schedule': 'hourly',
                    'retention_period': '7 years'
                }

        if ai_model and not business_data:
            business_data['ai_inferred'] = True
            business_data['ai_model'] = ai_model
            self.enrichment_stats['ai_enrichments'] += 1

        return business_data

    async def _enrich_compliance(self, entity_type: str, entity_value: str,
                                ai_model: Optional[str] = None) -> Dict[str, Any]:
        """Compliance and regulatory enrichment"""
        compliance_data = {
            'frameworks': [],
            'requirements': [],
            'controls': [],
            'audit_status': 'compliant',
            'last_audit': datetime.utcnow().isoformat(),
            'findings': [],
            'exemptions': []
        }

        # Determine applicable compliance based on entity
        if entity_type == 'hostname' and 'PAYMENT' in entity_value.upper():
            compliance_data['frameworks'] = ['PCI-DSS', 'SOX']
            compliance_data['requirements'] = [
                'PCI-DSS 2.1: Default passwords changed',
                'PCI-DSS 8.1: User identification',
                'SOX 404: Internal controls'
            ]
            compliance_data['controls'] = ['AC-2', 'AC-3', 'AU-2', 'SC-7']

        return compliance_data

    # =========================================================================
    # RISK ASSESSMENT METHODS
    # =========================================================================

    async def _handle_assess_risk(self, data: Dict[str, Any]):
        """Comprehensive risk assessment for an entity"""
        entity_id = uuid.UUID(data['entity_id'])

        # Calculate risk score
        risk_score = await self._calculate_entity_risk_score(entity_id)

        # Get all risk factors
        risk_factors = await self._get_entity_risk_factors(entity_id)

        # Get zone-based risk
        zone_risk = await self._get_zone_risk_multiplier(entity_id)

        # Calculate final risk
        final_risk = min(100, int(risk_score * zone_risk))

        # Update entity
        cursor = self.db_connection.cursor()
        cursor.execute("""
            UPDATE entities
            SET risk_score = %s,
                enrichment_updated_at = NOW()
            WHERE entity_id = %s
        """, (final_risk, entity_id))
        self.db_connection.commit()

        # Audit the assessment
        await self._audit_enrichment(
            entity_id, 'automatic', 'risk_calculator',
            {'risk_score': final_risk, 'factors': risk_factors}
        )

        self.enrichment_stats['risk_scores_calculated'] += 1

        return final_risk

    async def _calculate_entity_risk_score(self, entity_id: uuid.UUID) -> int:
        """Calculate comprehensive risk score for an entity"""
        cursor = self.db_connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # Get risk factors
        cursor.execute("""
            SELECT factor_type, severity, risk_contribution
            FROM entity_risk_factors
            WHERE entity_id = %s AND is_active = TRUE
        """, (entity_id,))

        risk_factors = cursor.fetchall()

        # Calculate base risk
        risk_score = 0
        for factor in risk_factors:
            contribution = factor['risk_contribution'] or 0
            if contribution == 0 and factor['severity']:
                # Default contributions by severity
                severity_scores = {
                    'critical': 25,
                    'high': 15,
                    'medium': 10,
                    'low': 5,
                    'info': 1
                }
                contribution = severity_scores.get(factor['severity'], 0)

            # Apply weight for factor type
            weight = self.risk_weights.get(factor['factor_type'], 0.1)
            risk_score += contribution * weight

        # Get entity details for additional risk factors
        cursor.execute("""
            SELECT entity_type, entity_value, criticality_score, tags
            FROM entities
            WHERE entity_id = %s
        """, (entity_id,))

        entity = cursor.fetchone()

        # Adjust for criticality
        if entity and entity['criticality_score']:
            risk_score = risk_score * (1 + entity['criticality_score'] / 100)

        # Adjust for tags
        if entity and entity['tags']:
            if 'critical' in entity['tags']:
                risk_score *= 1.5
            if 'monitor-24x7' in entity['tags']:
                risk_score *= 1.2

        return min(100, int(risk_score))

    async def _get_entity_risk_factors(self, entity_id: uuid.UUID) -> List[Dict[str, Any]]:
        """Get all active risk factors for an entity"""
        cursor = self.db_connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute("""
            SELECT factor_type, factor_name, severity, risk_contribution, evidence
            FROM entity_risk_factors
            WHERE entity_id = %s AND is_active = TRUE
            ORDER BY risk_contribution DESC
        """, (entity_id,))

        return cursor.fetchall()

    # =========================================================================
    # SECURITY ZONE METHODS
    # =========================================================================

    async def _load_security_zones(self):
        """Load security zones into cache"""
        cursor = self.db_connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute("""
            SELECT zone_id, zone_name, zone_type, subnet_patterns,
                   hostname_patterns, trust_level, risk_multiplier
            FROM security_zones
            ORDER BY trust_level DESC
        """)

        zones = cursor.fetchall()
        for zone in zones:
            self.zone_cache[zone['zone_id']] = zone

        self.logger.info(f"Loaded {len(zones)} security zones")

    async def _handle_assign_zone(self, data: Dict[str, Any]):
        """Assign entity to appropriate security zone"""
        entity_id = uuid.UUID(data['entity_id'])

        zone_id = await self._auto_assign_security_zone(
            entity_id,
            data.get('entity_type'),
            data.get('entity_value')
        )

        self.enrichment_stats['zones_assigned'] += 1

        return zone_id

    async def _auto_assign_security_zone(self, entity_id: uuid.UUID,
                                        entity_type: str, entity_value: str) -> Optional[uuid.UUID]:
        """Automatically assign entity to appropriate security zone"""
        assigned_zone_id = None

        # Check IP-based assignment
        if entity_type in ['ip_address', 'source_ip', 'destination_ip']:
            for zone_id, zone in self.zone_cache.items():
                if zone['subnet_patterns']:
                    for pattern in zone['subnet_patterns']:
                        if self._ip_in_range(entity_value, pattern):
                            assigned_zone_id = zone_id
                            break
                if assigned_zone_id:
                    break

        # Check hostname-based assignment
        elif entity_type == 'hostname':
            for zone_id, zone in self.zone_cache.items():
                if zone['hostname_patterns']:
                    for pattern in zone['hostname_patterns']:
                        if self._hostname_matches_pattern(entity_value, pattern):
                            assigned_zone_id = zone_id
                            break
                if assigned_zone_id:
                    break

        # Default to external zone if no match
        if not assigned_zone_id:
            for zone_id, zone in self.zone_cache.items():
                if zone['zone_type'] == 'external':
                    assigned_zone_id = zone_id
                    break

        # Update entity with zone assignment
        if assigned_zone_id:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                UPDATE entities
                SET zone_id = %s
                WHERE entity_id = %s
            """, (assigned_zone_id, entity_id))
            self.db_connection.commit()

            # Audit the zone assignment
            await self._audit_enrichment(
                entity_id, 'automatic', 'zone_detector',
                {'zone_id': str(assigned_zone_id)}
            )

        return assigned_zone_id

    def _ip_in_range(self, ip_str: str, range_str: str) -> bool:
        """Check if IP is in given range"""
        try:
            ip = ipaddress.ip_address(ip_str)
            network = ipaddress.ip_network(range_str)
            return ip in network
        except:
            return False

    def _hostname_matches_pattern(self, hostname: str, pattern: str) -> bool:
        """Check if hostname matches pattern (supports wildcards)"""
        import fnmatch
        return fnmatch.fnmatch(hostname.lower(), pattern.lower())

    async def _get_zone_risk_multiplier(self, entity_id: uuid.UUID) -> float:
        """Get risk multiplier based on entity's security zone"""
        cursor = self.db_connection.cursor()

        cursor.execute("""
            SELECT sz.risk_multiplier
            FROM entities e
            JOIN security_zones sz ON e.zone_id = sz.zone_id
            WHERE e.entity_id = %s
        """, (entity_id,))

        result = cursor.fetchone()
        return result[0] if result else 1.0

    # =========================================================================
    # BUSINESS CONTEXT METHODS
    # =========================================================================

    async def _handle_add_business_context(self, data: Dict[str, Any]):
        """Add business context to an entity"""
        entity_id = uuid.UUID(data['entity_id'])
        business_context = data.get('business_context', {})

        cursor = self.db_connection.cursor()
        cursor.execute("""
            UPDATE entities
            SET business_context = COALESCE(business_context, '{}'::jsonb) || %s::jsonb,
                enrichment_updated_at = NOW(),
                enrichment_updated_by = %s
            WHERE entity_id = %s
        """, (json.dumps(business_context), data.get('updated_by', 'system'), entity_id))

        self.db_connection.commit()

        # Audit the update
        await self._audit_enrichment(
            entity_id, 'manual', data.get('updated_by', 'system'),
            {'business_context': business_context}
        )

    async def _handle_update_criticality(self, data: Dict[str, Any]):
        """Update entity criticality score"""
        entity_id = uuid.UUID(data['entity_id'])
        criticality_score = data.get('criticality_score')

        cursor = self.db_connection.cursor()
        cursor.execute("""
            UPDATE entities
            SET criticality_score = %s,
                enrichment_updated_at = NOW(),
                enrichment_updated_by = %s
            WHERE entity_id = %s
        """, (criticality_score, data.get('updated_by', 'system'), entity_id))

        self.db_connection.commit()

        # Recalculate risk score with new criticality
        await self._calculate_entity_risk_score(entity_id)

    async def _handle_apply_tags(self, data: Dict[str, Any]):
        """Apply tags to an entity"""
        entity_id = uuid.UUID(data['entity_id'])
        tags = data.get('tags', [])
        operation = data.get('operation', 'add')  # add, remove, replace

        cursor = self.db_connection.cursor()

        if operation == 'add':
            cursor.execute("""
                UPDATE entities
                SET tags = array_cat(COALESCE(tags, ARRAY[]::text[]), %s::text[]),
                    enrichment_updated_at = NOW()
                WHERE entity_id = %s
            """, (tags, entity_id))
        elif operation == 'remove':
            cursor.execute("""
                UPDATE entities
                SET tags = array_remove(tags, ANY(%s::text[])),
                    enrichment_updated_at = NOW()
                WHERE entity_id = %s
            """, (tags, entity_id))
        elif operation == 'replace':
            cursor.execute("""
                UPDATE entities
                SET tags = %s::text[],
                    enrichment_updated_at = NOW()
                WHERE entity_id = %s
            """, (tags, entity_id))

        self.db_connection.commit()

    # =========================================================================
    # BEHAVIORAL ANALYSIS METHODS
    # =========================================================================

    async def _handle_analyze_behavior(self, data: Dict[str, Any]):
        """Analyze entity behavior and create/update baseline"""
        entity_id = uuid.UUID(data['entity_id'])

        # Get entity activity history
        activity = await self._get_entity_activity(entity_id)

        # Create behavioral profile
        profile = await self._create_behavioral_profile(activity)

        # Store behavioral profile
        cursor = self.db_connection.cursor()
        cursor.execute("""
            UPDATE entities
            SET behavioral_profile = %s::jsonb,
                enrichment_updated_at = NOW()
            WHERE entity_id = %s
        """, (json.dumps(profile), entity_id))

        # Create or update baseline
        await self._create_entity_baseline(entity_id, profile.get('entity_type'))

        self.db_connection.commit()
        self.enrichment_stats['baselines_created'] += 1

    async def _create_behavioral_profile(self, activity: List[Dict]) -> Dict[str, Any]:
        """Create behavioral profile from activity history"""
        profile = {
            'activity_hours': [],
            'common_peers': [],
            'typical_actions': [],
            'data_volume': {'avg': 0, 'max': 0, 'min': 0},
            'frequency': {'daily': 0, 'weekly': 0, 'monthly': 0},
            'anomalies': [],
            'profile_confidence': 0.0,
            'last_updated': datetime.utcnow().isoformat()
        }

        # Analyze activity patterns (simplified)
        if activity:
            # Extract patterns from activity
            hours = set()
            peers = set()
            actions = set()

            for event in activity:
                if event.get('timestamp'):
                    hour = datetime.fromisoformat(event['timestamp']).hour
                    hours.add(hour)
                if event.get('peer_entity'):
                    peers.add(event['peer_entity'])
                if event.get('action'):
                    actions.add(event['action'])

            profile['activity_hours'] = sorted(list(hours))
            profile['common_peers'] = list(peers)[:10]  # Top 10 peers
            profile['typical_actions'] = list(actions)[:10]  # Top 10 actions
            profile['profile_confidence'] = min(len(activity) / 100, 1.0)  # Confidence based on data volume

        return profile

    async def _get_entity_activity(self, entity_id: uuid.UUID, days: int = 30) -> List[Dict]:
        """Get entity activity history"""
        cursor = self.db_connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute("""
            SELECT r.relationship_type as action,
                   r.timestamp,
                   dst.entity_value as peer_entity
            FROM relationships r
            JOIN entities dst ON r.destination_entity_id = dst.entity_id
            WHERE r.source_entity_id = %s
                AND r.timestamp > NOW() - INTERVAL '%s days'
            ORDER BY r.timestamp DESC
            LIMIT 1000
        """, (entity_id, days))

        return cursor.fetchall()

    async def _create_entity_baseline(self, entity_id: uuid.UUID, entity_type: str):
        """Create or update entity baseline"""
        cursor = self.db_connection.cursor()

        # Get current entity data
        cursor.execute("""
            SELECT properties, behavioral_profile, event_count
            FROM entities
            WHERE entity_id = %s
        """, (entity_id,))

        entity_data = cursor.fetchone()

        if entity_data:
            baseline_data = {
                'properties': entity_data[0],
                'behavioral_profile': entity_data[1],
                'event_count': entity_data[2],
                'timestamp': datetime.utcnow().isoformat()
            }

            # Store baseline
            cursor.execute("""
                INSERT INTO entity_baselines (
                    entity_id, baseline_type, baseline_data,
                    confidence_score, learning_window_days
                ) VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (entity_id, baseline_type)
                DO UPDATE SET
                    baseline_data = EXCLUDED.baseline_data,
                    last_updated = NOW()
            """, (entity_id, 'behavior', json.dumps(baseline_data), 0.8, 30))

            self.db_connection.commit()

    async def _handle_update_baseline(self, data: Dict[str, Any]):
        """Update entity baseline"""
        entity_id = uuid.UUID(data['entity_id'])
        baseline_type = data.get('baseline_type', 'behavior')

        await self._create_entity_baseline(entity_id, baseline_type)

    # =========================================================================
    # SESSION TRACKING METHODS
    # =========================================================================

    async def _handle_track_session(self, data: Dict[str, Any]):
        """Track temporal session for relationships"""
        relationship_id = uuid.UUID(data['relationship_id'])

        # Get or create session instance
        session_id = await self._get_or_create_session_instance(
            relationship_id,
            data.get('timeout_minutes', 60)
        )

        # Update session activity
        cursor = self.db_connection.cursor()
        cursor.execute("""
            UPDATE session_instances
            SET event_count = event_count + 1,
                last_activity = NOW()
            WHERE instance_id = %s
        """, (session_id,))

        self.db_connection.commit()

        return session_id

    async def _get_or_create_session_instance(self, relationship_id: uuid.UUID,
                                             timeout_minutes: int) -> uuid.UUID:
        """Get active session or create new one"""
        cursor = self.db_connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # Check for active session
        cursor.execute("""
            SELECT instance_id
            FROM session_instances
            WHERE relationship_id = %s
                AND is_active = TRUE
                AND last_activity > NOW() - INTERVAL '%s minutes'
            ORDER BY last_activity DESC
            LIMIT 1
        """, (relationship_id, timeout_minutes))

        result = cursor.fetchone()

        if result:
            return result['instance_id']
        else:
            # Create new session
            session_id = uuid.uuid4()
            cursor.execute("""
                INSERT INTO session_instances (
                    instance_id, relationship_id, session_start,
                    timeout_minutes, is_active, event_count
                ) VALUES (%s, %s, NOW(), %s, TRUE, 1)
            """, (session_id, relationship_id, timeout_minutes))

            self.db_connection.commit()
            return session_id

    # =========================================================================
    # AUDIT AND HELPER METHODS
    # =========================================================================

    async def _audit_enrichment(self, entity_id: uuid.UUID, enrichment_type: str,
                               source: str, changes: Dict[str, Any]):
        """Audit all enrichment operations"""
        cursor = self.db_connection.cursor()

        cursor.execute("""
            INSERT INTO enrichment_audit (
                entity_id, enrichment_type, enrichment_source,
                new_value, change_reason
            ) VALUES (%s, %s, %s, %s, %s)
        """, (entity_id, enrichment_type, source, json.dumps(changes),
              'Automated enrichment'))

        self.db_connection.commit()

    async def _update_entity_enrichment(self, entity_id: uuid.UUID,
                                       enrichments: Dict[str, Any]):
        """Update entity with enrichment metadata"""
        cursor = self.db_connection.cursor()

        cursor.execute("""
            UPDATE entities
            SET enrichment_metadata = COALESCE(enrichment_metadata, '{}'::jsonb) || %s::jsonb,
                enrichment_updated_at = NOW()
            WHERE entity_id = %s
        """, (json.dumps(enrichments), entity_id))

        self.db_connection.commit()

    async def _load_enrichment_rules(self):
        """Load active enrichment rules into cache"""
        cursor = self.db_connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cursor.execute("""
            SELECT rule_id, rule_name, rule_type, conditions, actions,
                   priority, ai_model
            FROM enrichment_rules
            WHERE is_active = TRUE
            ORDER BY priority DESC
        """)

        rules = cursor.fetchall()
        for rule in rules:
            self.enrichment_rules_cache[rule['rule_id']] = rule

        self.logger.info(f"Loaded {len(rules)} enrichment rules")

    # =========================================================================
    # BACKGROUND LOOP METHODS
    # =========================================================================

    async def _enrichment_loop(self):
        """Main enrichment processing loop"""
        while self.is_running:
            try:
                # Process any pending enrichments
                await asyncio.sleep(30)
            except Exception as e:
                self.logger.error(f"Enrichment loop error: {e}")
                await asyncio.sleep(5)

    async def _risk_assessment_loop(self):
        """Continuous risk assessment loop"""
        while self.is_running:
            try:
                # Periodically recalculate risk scores
                await asyncio.sleep(300)  # Every 5 minutes
            except Exception as e:
                self.logger.error(f"Risk assessment loop error: {e}")
                await asyncio.sleep(30)

    async def _zone_management_loop(self):
        """Security zone management loop"""
        while self.is_running:
            try:
                # Reload zones periodically
                await self._load_security_zones()
                await asyncio.sleep(3600)  # Every hour
            except Exception as e:
                self.logger.error(f"Zone management loop error: {e}")
                await asyncio.sleep(300)

    async def _behavioral_analysis_loop(self):
        """Behavioral analysis loop"""
        while self.is_running:
            try:
                # Analyze entity behaviors
                await asyncio.sleep(600)  # Every 10 minutes
            except Exception as e:
                self.logger.error(f"Behavioral analysis loop error: {e}")
                await asyncio.sleep(60)

    async def _relationship_analysis_loop(self):
        """Relationship pattern analysis loop"""
        while self.is_running:
            try:
                # Analyze relationship patterns
                await asyncio.sleep(300)  # Every 5 minutes
            except Exception as e:
                self.logger.error(f"Relationship analysis loop error: {e}")
                await asyncio.sleep(30)

    async def _session_tracking_loop(self):
        """Session lifecycle management loop"""
        while self.is_running:
            try:
                # Close expired sessions
                cursor = self.db_connection.cursor()
                cursor.execute("""
                    UPDATE session_instances
                    SET is_active = FALSE,
                        session_end = last_activity
                    WHERE is_active = TRUE
                        AND last_activity < NOW() - (timeout_minutes || ' minutes')::interval
                """)

                closed = cursor.rowcount
                if closed > 0:
                    self.db_connection.commit()
                    self.logger.info(f"Closed {closed} expired sessions")

                await asyncio.sleep(60)  # Every minute
            except Exception as e:
                self.logger.error(f"Session tracking loop error: {e}")
                await asyncio.sleep(30)

    async def _baseline_update_loop(self):
        """Baseline update and maintenance loop"""
        while self.is_running:
            try:
                # Update baselines for active entities
                await asyncio.sleep(3600)  # Every hour
            except Exception as e:
                self.logger.error(f"Baseline update loop error: {e}")
                await asyncio.sleep(300)

    async def _cache_maintenance_loop(self):
        """Cache cleanup and maintenance loop"""
        while self.is_running:
            try:
                # Clean expired cache entries
                current_time = datetime.utcnow()

                # Clean entity cache (keep last 1000 entries)
                if len(self.entity_cache) > 1000:
                    sorted_cache = sorted(
                        self.entity_cache.items(),
                        key=lambda x: x[1].get('last_accessed', current_time)
                    )
                    self.entity_cache = dict(sorted_cache[-1000:])

                # Log statistics
                self.logger.info(f"Contextualization stats: {self.enrichment_stats}")

                await asyncio.sleep(300)  # Every 5 minutes
            except Exception as e:
                self.logger.error(f"Cache maintenance error: {e}")
                await asyncio.sleep(60)

    async def _handle_enrich_log(self, data: Dict[str, Any]):
        """Handle log enrichment request with entity extraction"""
        log = data.get('log', {})
        enrich_level = data.get('enrich_level', 'standard')

        # Extract entities from log
        entities = await self._extract_entities(log)

        # Enrich each entity
        enriched_entities = []
        for entity_type, entity_value in entities.items():
            if isinstance(entity_value, (dict, list)):
                entity_value = str(entity_value)

            entity_id = await self._get_or_create_enhanced_entity(
                entity_type, entity_value, {'source_log': log}
            )

            enrichments = await self._apply_comprehensive_enrichment(
                entity_id, entity_type, entity_value, enrich_level
            )

            enriched_entities.append({
                'entity_id': str(entity_id),
                'entity_type': entity_type,
                'entity_value': entity_value,
                'enrichments': enrichments
            })

        # Find relationships between entities
        await self._extract_and_store_relationships(enriched_entities, log)

        self.logger.info(f"Enriched log with {len(enriched_entities)} entities")

        # Publish results
        self.publish_message('contextualization.log_enriched', {
            'entities': enriched_entities,
            'entity_count': len(enriched_entities),
            'timestamp': datetime.utcnow().isoformat()
        })

    async def _extract_entities(self, log: Dict[str, Any]) -> Dict[str, str]:
        """Extract entities from log data"""
        entities = {}

        # Handle nested log structure
        log_data = log
        if 'data' in log and 'log' in log['data']:
            log_data = log['data']['log']
        elif 'log' in log:
            log_data = log['log']

        # Extract IPs
        ip_fields = ['source.ip', 'destination.ip', 'source_ip', 'destination_ip',
                    'src_ip', 'dst_ip', 'client_ip', 'server_ip']
        for field in ip_fields:
            if '.' in field:
                value = self._get_nested_value(log_data, field)
            else:
                value = log_data.get(field)

            if value:
                entity_value = str(value) if isinstance(value, dict) else value
                entities[f'ip_{field.replace(".", "_")}'] = entity_value

        # Extract hostnames
        hostname_fields = ['host', 'hostname', 'observer.name', 'host.name',
                          'computer_name', 'device_name']
        for field in hostname_fields:
            if '.' in field:
                value = self._get_nested_value(log_data, field)
            else:
                value = log_data.get(field)

            if value:
                entity_value = str(value) if isinstance(value, dict) else value
                entities[f'hostname_{field.replace(".", "_")}'] = entity_value

        # Extract users
        user_fields = ['user', 'username', 'user_name', 'user.name', 'account']
        for field in user_fields:
            if '.' in field:
                value = self._get_nested_value(log_data, field)
            else:
                value = log_data.get(field)

            if value:
                entity_value = str(value) if isinstance(value, dict) else value
                entities[f'user_{field.replace(".", "_")}'] = entity_value

        # Extract processes
        process_fields = ['process', 'process_name', 'process.name', 'image_name']
        for field in process_fields:
            if '.' in field:
                value = self._get_nested_value(log_data, field)
            else:
                value = log_data.get(field)

            if value:
                entity_value = str(value) if isinstance(value, dict) else value
                entities[f'process_{field.replace(".", "_")}'] = entity_value

        return entities

    def _get_nested_value(self, data: Dict[str, Any], field_path: str) -> Any:
        """Get value from nested dictionary using dot notation"""
        try:
            value = data
            for key in field_path.split('.'):
                value = value[key]
            return value
        except (KeyError, TypeError):
            return None

    async def _extract_and_store_relationships(self, entities: List[Dict], log: Dict):
        """Extract and store relationships between entities"""
        # Simple relationship extraction based on common patterns
        for i, src_entity in enumerate(entities):
            for dst_entity in entities[i+1:]:
                # Create relationships based on entity types
                if (src_entity['entity_type'].startswith('user') and
                    dst_entity['entity_type'].startswith('hostname')):
                    await self._create_relationship(
                        src_entity['entity_id'],
                        dst_entity['entity_id'],
                        'logged_into',
                        {'source': 'log_analysis', 'confidence': 0.8}
                    )
                elif (src_entity['entity_type'].startswith('hostname') and
                      dst_entity['entity_type'].startswith('ip')):
                    await self._create_relationship(
                        src_entity['entity_id'],
                        dst_entity['entity_id'],
                        'has_ip',
                        {'source': 'log_analysis', 'confidence': 0.9}
                    )

    async def _create_relationship(self, source_id: str, dest_id: str,
                                  rel_type: str, properties: Dict):
        """Create a relationship between entities"""
        cursor = self.db_connection.cursor()

        try:
            cursor.execute("""
                INSERT INTO relationships (
                    source_entity_id, destination_entity_id,
                    relationship_type, properties, confidence_score
                ) VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT DO NOTHING
            """, (uuid.UUID(source_id), uuid.UUID(dest_id), rel_type,
                  json.dumps(properties), properties.get('confidence', 0.5)))

            self.db_connection.commit()
            self.enrichment_stats['relationships_created'] += 1
        except Exception as e:
            self.logger.error(f"Error creating relationship: {e}")
            self.db_connection.rollback()

    async def _handle_find_relationships(self, data: Dict[str, Any]):
        """Find relationships for an entity"""
        entity = data.get('entity', {})
        relationship_types = data.get('types', ['all'])

        relationships = await self._find_entity_relationships(entity, relationship_types)

        # Publish relationship results
        self.publish_message('contextualization.relationships_found', {
            'entity': entity,
            'relationships': relationships,
            'timestamp': datetime.utcnow().isoformat()
        })

        return relationships

    async def _find_entity_relationships(self, entity: Dict[str, Any],
                                        types: List[str]) -> List[Dict[str, Any]]:
        """Find relationships for an entity"""
        # This is a simplified version - in production would query the database
        relationships = []

        entity_type = entity.get('type')
        entity_value = entity.get('value')

        # Simulate relationship discovery
        if entity_type == 'ip':
            relationships.append({
                'type': 'communicates_with',
                'target_entity': {'type': 'hostname', 'value': 'server-01'},
                'confidence': 0.85,
                'last_seen': datetime.utcnow().isoformat()
            })

        return relationships

    async def _handle_get_context(self, data: Dict[str, Any]):
        """Get comprehensive context for an entity"""
        entity = data.get('entity', {})
        context_depth = data.get('depth', 2)

        context = await self._build_entity_context(entity, context_depth)

        # Publish context result
        self.publish_message('contextualization.context_response', {
            'entity': entity,
            'context': context,
            'timestamp': datetime.utcnow().isoformat()
        })

        return context

    async def _build_entity_context(self, entity: Dict[str, Any], depth: int) -> Dict[str, Any]:
        """Build comprehensive context for an entity"""
        context = {
            'entity': entity,
            'direct_relationships': [],
            'indirect_relationships': [],
            'timeline': [],
            'risk_factors': [],
            'business_context': {},
            'zone_info': {}
        }

        # Build context based on depth
        if depth >= 1:
            context['direct_relationships'] = await self._find_entity_relationships(entity, ['all'])

        if depth >= 2:
            for rel in context['direct_relationships']:
                target_entity = rel['target_entity']
                indirect_rels = await self._find_entity_relationships(target_entity, ['all'])
                context['indirect_relationships'].extend(indirect_rels)

        return context

    async def _handle_generate_report(self, data: Dict[str, Any]):
        """Generate contextualization report"""
        report_type = data.get('report_type', 'summary')

        report = {
            'type': report_type,
            'timestamp': datetime.utcnow().isoformat(),
            'statistics': self.enrichment_stats,
            'active_zones': len(self.zone_cache),
            'active_rules': len(self.enrichment_rules_cache),
            'cache_size': {
                'entities': len(self.entity_cache),
                'relationships': len(self.relationship_cache)
            }
        }

        # Publish report
        self.publish_message('contextualization.report_generated', report)

        return report

async def main():
    """Main entry point for Enhanced Contextualization Engine"""
    engine = EnhancedContextualizationEngine()
    await engine.run()

if __name__ == "__main__":
    asyncio.run(main())