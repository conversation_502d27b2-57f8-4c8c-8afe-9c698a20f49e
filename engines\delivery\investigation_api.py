"""
Auto-Investigation Dashboard API
REST API endpoints for investigation management and auto-enrichment
"""

import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from aiohttp import web
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.historical_context_manager import HistoricalContextManager

logger = logging.getLogger(__name__)


class InvestigationAPI:
    """
    REST API for investigation dashboard

    Endpoints:
    - POST /api/v1/investigations - Create investigation
    - GET /api/v1/investigations/{id} - Get investigation details
    - GET /api/v1/investigations - List investigations
    - POST /api/v1/investigations/{id}/enrich - Auto-enrich investigation
    - GET /api/v1/investigations/{id}/timeline - Get investigation timeline
    - GET /api/v1/investigations/{id}/evidence - Get evidence
    - POST /api/v1/investigations/{id}/entities - Add entity to investigation
    - PUT /api/v1/investigations/{id}/status - Update investigation status
    """

    def __init__(self, db_connection, redis_client):
        self.db = db_connection
        self.redis = redis_client
        self.context_manager = HistoricalContextManager(db_connection, redis_client)

    def setup_routes(self, app: web.Application):
        """Setup API routes"""
        app.router.add_post('/api/v1/investigations', self.create_investigation)
        app.router.add_get('/api/v1/investigations/{investigation_id}', self.get_investigation)
        app.router.add_get('/api/v1/investigations', self.list_investigations)
        app.router.add_post('/api/v1/investigations/{investigation_id}/enrich', self.enrich_investigation)
        app.router.add_get('/api/v1/investigations/{investigation_id}/timeline', self.get_timeline)
        app.router.add_get('/api/v1/investigations/{investigation_id}/evidence', self.get_evidence)
        app.router.add_post('/api/v1/investigations/{investigation_id}/entities', self.add_entity)
        app.router.add_put('/api/v1/investigations/{investigation_id}/status', self.update_status)

    async def create_investigation(self, request: web.Request) -> web.Response:
        """
        Create new investigation

        POST /api/v1/investigations
        {
            "title": "Investigation title",
            "alert_id": "alert_123",
            "source_siem": "elastic",
            "severity": "high",
            "entities": {
                "ips": ["*******"],
                "users": ["jdoe"],
                "hosts": ["WORKSTATION01"]
            },
            "mitre_techniques": ["T1059.001"],
            "description": "Optional description"
        }
        """
        try:
            data = await request.json()

            # Validate required fields
            if not data.get('title'):
                return web.json_response({'error': 'Title is required'}, status=400)

            # Create investigation ID
            investigation_id = str(uuid4())

            # Extract entities for enrichment
            entities = data.get('entities', {})
            entity_list = []
            for entity_type, values in entities.items():
                for value in values:
                    entity_list.append({'type': entity_type.rstrip('s'), 'value': value})

            # Auto-enrich investigation with historical context
            enrichment = await self._auto_enrich(entity_list)

            # Create investigation record
            investigation = {
                'investigation_id': investigation_id,
                'title': data['title'],
                'alert_id': data.get('alert_id'),
                'source_siem': data.get('source_siem'),
                'severity': data.get('severity', 'medium'),
                'status': 'open',
                'entities': entities,
                'mitre_techniques': data.get('mitre_techniques', []),
                'description': data.get('description', ''),
                'enrichment': enrichment,
                'created_at': datetime.utcnow(),
                'created_by': data.get('created_by', 'system'),
                'updated_at': datetime.utcnow()
            }

            # Store in database
            await self._store_investigation(investigation)

            # Publish to Redis for real-time updates
            await self.redis.publish('investigation.created', json.dumps({
                'investigation_id': investigation_id,
                'title': investigation['title'],
                'severity': investigation['severity']
            }))

            return web.json_response({
                'investigation_id': investigation_id,
                'status': 'created',
                'enrichment_summary': {
                    'entities_enriched': len(enrichment.get('entity_contexts', {})),
                    'total_events': sum([len(ctx.get('recent_events', [])) for ctx in enrichment.get('entity_contexts', {}).values()]),
                    'risk_trend': enrichment.get('summary', {}).get('overall_risk_trend')
                }
            }, status=201)

        except Exception as e:
            logger.error(f"Error creating investigation: {e}", exc_info=True)
            return web.json_response({'error': str(e)}, status=500)

    async def get_investigation(self, request: web.Request) -> web.Response:
        """
        Get investigation details

        GET /api/v1/investigations/{investigation_id}
        """
        try:
            investigation_id = request.match_info['investigation_id']

            investigation = await self._fetch_investigation(investigation_id)
            if not investigation:
                return web.json_response({'error': 'Investigation not found'}, status=404)

            return web.json_response(investigation)

        except Exception as e:
            logger.error(f"Error fetching investigation: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def list_investigations(self, request: web.Request) -> web.Response:
        """
        List investigations with filtering

        GET /api/v1/investigations?status=open&severity=high&limit=50
        """
        try:
            # Get query parameters
            status = request.query.get('status', 'open')
            severity = request.query.get('severity')
            limit = int(request.query.get('limit', 50))
            offset = int(request.query.get('offset', 0))

            # Build filter
            filters = {'status': status}
            if severity:
                filters['severity'] = severity

            investigations = await self._list_investigations(filters, limit, offset)

            return web.json_response({
                'investigations': investigations,
                'total': len(investigations),
                'limit': limit,
                'offset': offset
            })

        except Exception as e:
            logger.error(f"Error listing investigations: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def enrich_investigation(self, request: web.Request) -> web.Response:
        """
        Auto-enrich investigation with latest context

        POST /api/v1/investigations/{investigation_id}/enrich
        {
            "hours_back": 24,
            "include_new_entities": true
        }
        """
        try:
            investigation_id = request.match_info['investigation_id']
            data = await request.json()

            investigation = await self._fetch_investigation(investigation_id)
            if not investigation:
                return web.json_response({'error': 'Investigation not found'}, status=404)

            # Get entities
            entities = []
            for entity_type, values in investigation.get('entities', {}).items():
                for value in values:
                    entities.append({'type': entity_type.rstrip('s'), 'value': value})

            # Re-enrich with specified parameters
            hours_back = data.get('hours_back', 24)
            enrichment = await self._auto_enrich(entities, hours_back)

            # Update investigation
            investigation['enrichment'] = enrichment
            investigation['enrichment_updated_at'] = datetime.utcnow()
            await self._update_investigation(investigation_id, {'enrichment': enrichment})

            return web.json_response({
                'status': 'enriched',
                'entities_enriched': len(enrichment.get('entity_contexts', {})),
                'total_events': sum([len(ctx.get('recent_events', [])) for ctx in enrichment.get('entity_contexts', {}).values()]),
                'new_entities_found': len(enrichment.get('discovered_entities', []))
            })

        except Exception as e:
            logger.error(f"Error enriching investigation: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def get_timeline(self, request: web.Request) -> web.Response:
        """
        Get investigation timeline (most recent first)

        GET /api/v1/investigations/{investigation_id}/timeline?hours_back=24
        """
        try:
            investigation_id = request.match_info['investigation_id']
            hours_back = int(request.query.get('hours_back', 24))

            investigation = await self._fetch_investigation(investigation_id)
            if not investigation:
                return web.json_response({'error': 'Investigation not found'}, status=404)

            # Build timeline from all entities
            timeline_items = []

            for entity_type, values in investigation.get('entities', {}).items():
                for value in values:
                    context = await self.context_manager.get_entity_history(
                        entity_type.rstrip('s'),
                        value,
                        hours_back
                    )

                    if context:
                        timeline_items.extend(context.timeline)

            # Sort by timestamp DESC (most recent first)
            timeline_items.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

            return web.json_response({
                'investigation_id': investigation_id,
                'timeline': timeline_items[:1000],  # Limit to 1000 most recent
                'total_items': len(timeline_items)
            })

        except Exception as e:
            logger.error(f"Error getting timeline: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def get_evidence(self, request: web.Request) -> web.Response:
        """
        Get investigation evidence

        GET /api/v1/investigations/{investigation_id}/evidence
        """
        try:
            investigation_id = request.match_info['investigation_id']

            investigation = await self._fetch_investigation(investigation_id)
            if not investigation:
                return web.json_response({'error': 'Investigation not found'}, status=404)

            # Gather evidence from enrichment
            evidence = {
                'investigation_id': investigation_id,
                'entity_evidence': [],
                'relationship_evidence': [],
                'session_evidence': [],
                'cti_matches': []
            }

            enrichment = investigation.get('enrichment', {})

            for entity_key, context in enrichment.get('entity_contexts', {}).items():
                evidence['entity_evidence'].append({
                    'entity': entity_key,
                    'recent_events': len(context.get('recent_events', [])),
                    'relationships': len(context.get('relationships', [])),
                    'risk_score': context.get('entity', {}).get('risk_score'),
                    'first_seen': context.get('entity', {}).get('first_seen'),
                    'last_seen': context.get('entity', {}).get('last_seen')
                })

                # Add CTI matches
                for event in context.get('recent_events', [])[:50]:  # Top 50
                    if event.get('cti_matches'):
                        evidence['cti_matches'].extend(event['cti_matches'])

            return web.json_response(evidence)

        except Exception as e:
            logger.error(f"Error getting evidence: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def add_entity(self, request: web.Request) -> web.Response:
        """
        Add entity to investigation

        POST /api/v1/investigations/{investigation_id}/entities
        {
            "entity_type": "ip",
            "entity_value": "********"
        }
        """
        try:
            investigation_id = request.match_info['investigation_id']
            data = await request.json()

            entity_type = data.get('entity_type')
            entity_value = data.get('entity_value')

            if not entity_type or not entity_value:
                return web.json_response({'error': 'entity_type and entity_value required'}, status=400)

            investigation = await self._fetch_investigation(investigation_id)
            if not investigation:
                return web.json_response({'error': 'Investigation not found'}, status=404)

            # Add entity
            entities = investigation.get('entities', {})
            entity_list_key = f"{entity_type}s"
            if entity_list_key not in entities:
                entities[entity_list_key] = []

            if entity_value not in entities[entity_list_key]:
                entities[entity_list_key].append(entity_value)

            # Get context for new entity
            context = await self.context_manager.get_entity_history(entity_type, entity_value, 24)

            # Update investigation
            await self._update_investigation(investigation_id, {
                'entities': entities,
                'enrichment.entity_contexts': {f"{entity_type}:{entity_value}": context}
            })

            return web.json_response({
                'status': 'added',
                'entity': f"{entity_type}:{entity_value}",
                'context_summary': {
                    'recent_events': len(context.recent_events) if context else 0,
                    'relationships': len(context.relationships) if context else 0
                }
            })

        except Exception as e:
            logger.error(f"Error adding entity: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def update_status(self, request: web.Request) -> web.Response:
        """
        Update investigation status

        PUT /api/v1/investigations/{investigation_id}/status
        {
            "status": "closed",
            "resolution": "false_positive",
            "notes": "Investigation notes"
        }
        """
        try:
            investigation_id = request.match_info['investigation_id']
            data = await request.json()

            status = data.get('status')
            if status not in ['open', 'in_progress', 'closed', 'escalated']:
                return web.json_response({'error': 'Invalid status'}, status=400)

            investigation = await self._fetch_investigation(investigation_id)
            if not investigation:
                return web.json_response({'error': 'Investigation not found'}, status=404)

            # Update investigation
            updates = {
                'status': status,
                'updated_at': datetime.utcnow()
            }

            if data.get('resolution'):
                updates['resolution'] = data['resolution']
            if data.get('notes'):
                updates['notes'] = data['notes']
            if status == 'closed':
                updates['closed_at'] = datetime.utcnow()
                updates['closed_by'] = data.get('closed_by', 'system')

            await self._update_investigation(investigation_id, updates)

            return web.json_response({'status': 'updated', 'investigation_status': status})

        except Exception as e:
            logger.error(f"Error updating status: {e}")
            return web.json_response({'error': str(e)}, status=500)

    # Helper methods

    async def _auto_enrich(self, entities: List[Dict], hours_back: int = 24) -> Dict:
        """Auto-enrich entities with historical context"""
        try:
            # Get multi-entity context
            context = await self.context_manager.get_multi_entity_context(entities, hours_back)

            # Extract enrichment data
            enrichment = {
                'entity_contexts': context.get('individual_contexts', {}),
                'intersections': context.get('intersections', {}),
                'summary': context.get('summary', {}),
                'discovered_entities': [],
                'enriched_at': datetime.utcnow().isoformat()
            }

            # Discover new related entities from relationships
            for entity_key, entity_context in context.get('individual_contexts', {}).items():
                for rel in entity_context.get('relationships', [])[:10]:  # Top 10
                    # Add related entity to discovered entities
                    if rel['relationship_type'] in ['connected_to', 'accessed', 'executed_on']:
                        enrichment['discovered_entities'].append({
                            'type': rel['to_entity_type'],
                            'value': rel['to_entity_value'],
                            'relationship': rel['relationship_type'],
                            'from': entity_key
                        })

            return enrichment

        except Exception as e:
            logger.error(f"Auto-enrichment error: {e}")
            return {}

    async def _store_investigation(self, investigation: Dict):
        """Store investigation in database"""
        try:
            query = """
                INSERT INTO investigations (
                    investigation_id, title, alert_id, source_siem, severity,
                    status, entities, mitre_techniques, description, enrichment,
                    created_at, created_by, updated_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            cursor = self.db.cursor()
            cursor.execute(query, (
                investigation['investigation_id'],
                investigation['title'],
                investigation.get('alert_id'),
                investigation.get('source_siem'),
                investigation['severity'],
                investigation['status'],
                json.dumps(investigation['entities']),
                investigation['mitre_techniques'],
                investigation.get('description'),
                json.dumps(investigation.get('enrichment', {})),
                investigation['created_at'],
                investigation['created_by'],
                investigation['updated_at']
            ))
            self.db.commit()
            cursor.close()

        except Exception as e:
            logger.error(f"Error storing investigation: {e}")
            self.db.rollback()

    async def _fetch_investigation(self, investigation_id: str) -> Optional[Dict]:
        """Fetch investigation from database"""
        try:
            query = """
                SELECT * FROM investigations
                WHERE investigation_id = %s
            """

            cursor = self.db.cursor()
            cursor.execute(query, (investigation_id,))
            result = cursor.fetchone()
            cursor.close()

            return dict(result) if result else None

        except Exception as e:
            logger.error(f"Error fetching investigation: {e}")
            return None

    async def _list_investigations(self, filters: Dict, limit: int, offset: int) -> List[Dict]:
        """List investigations with filters"""
        try:
            where_clauses = []
            params = []

            for key, value in filters.items():
                where_clauses.append(f"{key} = %s")
                params.append(value)

            where_sql = " AND ".join(where_clauses) if where_clauses else "1=1"

            query = f"""
                SELECT investigation_id, title, severity, status, created_at, updated_at
                FROM investigations
                WHERE {where_sql}
                ORDER BY created_at DESC
                LIMIT %s OFFSET %s
            """

            params.extend([limit, offset])

            cursor = self.db.cursor()
            cursor.execute(query, tuple(params))
            results = cursor.fetchall()
            cursor.close()

            return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error listing investigations: {e}")
            return []

    async def _update_investigation(self, investigation_id: str, updates: Dict):
        """Update investigation"""
        try:
            set_clauses = []
            params = []

            for key, value in updates.items():
                set_clauses.append(f"{key} = %s")
                if isinstance(value, (dict, list)):
                    params.append(json.dumps(value))
                else:
                    params.append(value)

            set_sql = ", ".join(set_clauses)
            params.append(investigation_id)

            query = f"""
                UPDATE investigations
                SET {set_sql}, updated_at = NOW()
                WHERE investigation_id = %s
            """

            cursor = self.db.cursor()
            cursor.execute(query, tuple(params))
            self.db.commit()
            cursor.close()

        except Exception as e:
            logger.error(f"Error updating investigation: {e}")
            self.db.rollback()
