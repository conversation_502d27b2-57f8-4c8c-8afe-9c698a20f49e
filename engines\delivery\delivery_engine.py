"""
SIEMLess v2.0 - Delivery Engine
Case management, workflow orchestration, frontend services, and visualization
ENSURES delivery of all platform capabilities through orchestrated workflows
"""

import asyncio
import json
import os
import time
import base64
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from aiohttp import web, ClientSession
from base_engine import <PERSON><PERSON>ngine
from workflow_orchestrator import WorkflowOrchestrator
from workflow_templates import W<PERSON><PERSON><PERSON><PERSON>_TEMPLATES
from investigation_engine import InvestigationEngine
from investigation_http_handlers import InvestigationHTTPHandlers
from investigation_context_generator import InvestigationContextGenerator
from business_context_manager import BusinessContextManager
# from auth_middleware import KeycloakAuthMiddleware  # COMMENTED OUT - Auth disabled for now

class DeliveryEngine(BaseEngine):
    """Delivery Engine for SIEMLess v2.0"""

    def __init__(self):
        super().__init__("delivery")
        self.active_cases = {}
        self.dashboard_data = {}
        self.user_sessions = {}
        self.auth_middleware = None  # Will be initialized in _setup_http_routes (COMMENTED OUT)
        self.delivery_stats = {
            'cases_created': 0,
            'cases_closed': 0,
            'active_sessions': 0,
            'alerts_delivered': 0,
            'workflows_executed': 0,
            'workflows_completed': 0,
            'workflows_failed': 0
        }
        self.case_templates = {
            'security_incident': {
                'priority': 'high',
                'workflow': ['triage', 'investigation', 'containment', 'resolution'],
                'auto_assign': True
            },
            'anomaly_detection': {
                'priority': 'medium',
                'workflow': ['analysis', 'validation', 'action'],
                'auto_assign': False
            }
        }

        # Initialize Workflow Orchestrator - The heart of ENSURING delivery
        # Pass self so orchestrator can use real engine methods
        self.orchestrator = WorkflowOrchestrator(
            self.redis_client,
            self.db_pool,
            self.logger,
            engine=self  # Pass engine instance for real execution
        )

        # Load workflow templates
        self.workflow_templates = WORKFLOW_TEMPLATES

        # Initialize Investigation Engine for auto-investigation
        self.investigation_engine = InvestigationEngine(
            self.redis_client,
            self.db_pool,
            self.logger
        )

        # Initialize Investigation HTTP handlers
        self.investigation_http_handlers = InvestigationHTTPHandlers(
            self.investigation_engine,
            self.logger
        )

        # Initialize Investigation Context Generator
        self.context_generator = InvestigationContextGenerator(
            self.db_pool,
            self.redis_client,
            self.logger
        )

        # Initialize Business Context Manager
        self.business_context_manager = BusinessContextManager(self.db_pool)
        self.logger.info("Business Context Manager initialized")

        self.logger.info("Delivery Engine with Workflow Orchestration and Investigation Engine initialized")

    def start_engine_tasks(self) -> List[asyncio.Task]:
        """Start delivery-specific background tasks"""
        tasks = []

        # Workflow orchestration task (PRIMARY)
        tasks.append(asyncio.create_task(self._workflow_orchestration_loop()))

        # Workflow monitoring task
        tasks.append(asyncio.create_task(self._workflow_monitoring_loop()))

        # Case management task
        tasks.append(asyncio.create_task(self._case_management_loop()))

        # Dashboard update task
        tasks.append(asyncio.create_task(self._dashboard_update_loop()))

        # Alert delivery task
        tasks.append(asyncio.create_task(self._alert_delivery_loop()))

        # Session management task
        tasks.append(asyncio.create_task(self._session_management_loop()))

        # Investigation processing task (NEW)
        tasks.append(asyncio.create_task(self._investigation_processing_loop()))

        # Recover incomplete workflows on startup
        asyncio.create_task(self._recover_workflows())

        self.logger.info("Delivery Engine tasks started with Workflow Orchestration and Investigation Engine")
        return tasks

    def get_subscribed_channels(self) -> List[str]:
        """Return list of Redis channels this engine subscribes to"""
        return [
            'delivery.create_case',
            'delivery.update_case',
            'delivery.close_case',
            'delivery.store_enriched_log',
            'delivery.send_alert',
            'delivery.get_dashboard_data',
            'delivery.user_login',
            'delivery.user_logout',
            # Workflow orchestration channels
            'delivery.start_workflow',
            'delivery.cancel_workflow',
            'delivery.get_workflow_status',
            'delivery.workflow_completed',
            # Workflow step execution (local)
            'delivery.workflow.create_incident_case',
            'delivery.workflow.send_incident_notifications',
            'delivery.workflow.create_investigation_report',
            'delivery.workflow.health_check',
            # Investigation channels (NEW)
            'investigation.create',
            'ingestion.alerts.received',  # For auto-investigation
            # Context plugin results (pattern match for all request IDs)
            'delivery.context.*.complete',
            'delivery.context.*.error',
            # Alert enrichment and correlation results (NEW)
            'contextualization.alert.enriched.*',  # Enrichment results
            'backend.correlation.complete.*'  # Correlation results
        ]

    async def process_message(self, message: Dict[str, Any]):
        """Process incoming message from message queue"""
        try:
            data = json.loads(message['data'])
            channel = message['channel']

            self.logger.info(f"Processing message from {channel}")

            if channel == 'delivery.create_case':
                await self._handle_create_case(data['data'])
            elif channel == 'delivery.update_case':
                await self._handle_update_case(data['data'])
            elif channel == 'delivery.close_case':
                await self._handle_close_case(data['data'])
            elif channel == 'delivery.store_enriched_log':
                await self._handle_store_enriched_log(data['data'])
            elif channel == 'delivery.send_alert':
                await self._handle_send_alert(data['data'])
            elif channel == 'delivery.get_dashboard_data':
                await self._handle_get_dashboard_data(data['data'])
            elif channel == 'delivery.user_login':
                await self._handle_user_login(data['data'])
            elif channel == 'delivery.user_logout':
                await self._handle_user_logout(data['data'])
            # Workflow orchestration handlers
            elif channel == 'delivery.start_workflow':
                await self._handle_start_workflow(data['data'])
            elif channel == 'delivery.cancel_workflow':
                await self._handle_cancel_workflow(data['data'])
            elif channel == 'delivery.get_workflow_status':
                await self._handle_get_workflow_status(data['data'])
            # Workflow step handlers
            elif channel.startswith('delivery.workflow.'):
                await self._handle_workflow_step(channel, data['data'])
            # Investigation handlers (NEW)
            elif channel == 'investigation.create':
                await self._handle_investigation_create(data['data'])
            elif channel == 'ingestion.alerts.received':
                await self._handle_alert_for_investigation(data['data'])
            # Alert enrichment and correlation results (NEW)
            elif channel.startswith('contextualization.alert.enriched.'):
                await self._handle_enrichment_result(data)
            elif channel.startswith('backend.correlation.complete.'):
                await self._handle_correlation_result(data)

        except Exception as e:
            self.logger.error(f"Error processing message: {e}")

    async def _handle_create_case(self, data: Dict[str, Any]):
        """Handle case creation request"""
        case_type = data.get('case_type', 'security_incident')
        case_title = data.get('title', 'Untitled Case')
        case_description = data.get('description', '')
        priority = data.get('priority', 'medium')
        assigned_to = data.get('assigned_to')

        case_id = f"case_{int(time.time())}"

        # Create case from template
        template = self.case_templates.get(case_type, self.case_templates['security_incident'])

        case = {
            'case_id': case_id,
            'title': case_title,
            'description': case_description,
            'type': case_type,
            'priority': priority,
            'status': 'open',
            'workflow_stage': template['workflow'][0],
            'workflow_stages': template['workflow'],
            'assigned_to': assigned_to,
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat(),
            'evidence': [],
            'timeline': [
                {
                    'timestamp': datetime.utcnow().isoformat(),
                    'action': 'case_created',
                    'details': f"Case created: {case_title}"
                }
            ]
        }

        self.active_cases[case_id] = case
        self.delivery_stats['cases_created'] += 1

        # Publish case creation notification
        self.publish_message('delivery.case_created', {
            'case': case,
            'timestamp': datetime.utcnow().isoformat()
        })

        self.logger.info(f"Created case: {case_id}")

    async def _handle_update_case(self, data: Dict[str, Any]):
        """Handle case update request"""
        case_id = data.get('case_id')
        updates = data.get('updates', {})

        if case_id in self.active_cases:
            case = self.active_cases[case_id]

            # Apply updates
            for field, value in updates.items():
                if field in case:
                    old_value = case[field]
                    case[field] = value

                    # Add to timeline
                    case['timeline'].append({
                        'timestamp': datetime.utcnow().isoformat(),
                        'action': 'field_updated',
                        'details': f"Updated {field}: {old_value} -> {value}"
                    })

            case['updated_at'] = datetime.utcnow().isoformat()

            # Publish case update notification
            self.publish_message('delivery.case_updated', {
                'case': case,
                'updates': updates,
                'timestamp': datetime.utcnow().isoformat()
            })

            self.logger.info(f"Updated case: {case_id}")

    async def _handle_close_case(self, data: Dict[str, Any]):
        """Handle case closure request"""
        case_id = data.get('case_id')
        resolution = data.get('resolution', 'Resolved')
        closed_by = data.get('closed_by')

        if case_id in self.active_cases:
            case = self.active_cases[case_id]
            case['status'] = 'closed'
            case['resolution'] = resolution
            case['closed_by'] = closed_by
            case['closed_at'] = datetime.utcnow().isoformat()
            case['updated_at'] = datetime.utcnow().isoformat()

            # Add to timeline
            case['timeline'].append({
                'timestamp': datetime.utcnow().isoformat(),
                'action': 'case_closed',
                'details': f"Case closed by {closed_by}: {resolution}"
            })

            self.delivery_stats['cases_closed'] += 1

            # Move to closed cases (remove from active)
            closed_case = self.active_cases.pop(case_id)

            # Publish case closure notification
            self.publish_message('delivery.case_closed', {
                'case': closed_case,
                'timestamp': datetime.utcnow().isoformat()
            })

            self.logger.info(f"Closed case: {case_id}")

    async def _handle_store_enriched_log(self, data: Dict[str, Any]):
        """Handle enriched log storage request"""
        enriched_log = data.get('enriched_log', {})
        priority = data.get('priority', 'normal')

        # Store enriched log in database (simulated)
        log_id = f"log_{int(time.time())}"

        # Check if this log should trigger a case
        if await self._should_create_case(enriched_log):
            case_title = await self._generate_case_title(enriched_log)
            case_description = await self._generate_case_description(enriched_log)

            # Create case
            await self._handle_create_case({
                'case_type': 'security_incident',
                'title': case_title,
                'description': case_description,
                'priority': 'high'
            })

        self.logger.info(f"Stored enriched log: {log_id}")

    async def _handle_send_alert(self, data: Dict[str, Any]):
        """Handle alert delivery request"""
        alert_type = data.get('alert_type', 'info')
        message = data.get('message', '')
        recipients = data.get('recipients', ['all'])
        priority = data.get('priority', 'normal')

        alert = {
            'alert_id': f"alert_{int(time.time())}",
            'type': alert_type,
            'message': message,
            'priority': priority,
            'recipients': recipients,
            'created_at': datetime.utcnow().isoformat(),
            'delivered_at': datetime.utcnow().isoformat()
        }

        # Simulate alert delivery to different channels
        delivery_channels = ['email', 'slack', 'dashboard']
        if priority == 'critical':
            delivery_channels.append('sms')

        for channel in delivery_channels:
            await self._deliver_alert_to_channel(alert, channel)

        self.delivery_stats['alerts_delivered'] += 1

        # Publish alert delivery confirmation
        self.publish_message('delivery.alert_delivered', {
            'alert': alert,
            'channels': delivery_channels,
            'timestamp': datetime.utcnow().isoformat()
        })

        self.logger.info(f"Delivered alert: {alert['alert_id']}")

    async def _handle_get_dashboard_data(self, data: Dict[str, Any]):
        """Handle dashboard data request"""
        user_id = data.get('user_id')
        dashboard_type = data.get('dashboard_type', 'overview')

        dashboard_data = await self._generate_dashboard_data(user_id, dashboard_type)

        # Publish dashboard data
        self.publish_message('delivery.dashboard_data_response', {
            'user_id': user_id,
            'dashboard_type': dashboard_type,
            'data': dashboard_data,
            'timestamp': datetime.utcnow().isoformat()
        })

    async def _handle_user_login(self, data: Dict[str, Any]):
        """Handle user login event"""
        user_id = data.get('user_id')
        session_id = data.get('session_id')

        if user_id:
            self.user_sessions[session_id] = {
                'user_id': user_id,
                'login_time': datetime.utcnow().isoformat(),
                'last_activity': datetime.utcnow().isoformat(),
                'active': True
            }

            self.delivery_stats['active_sessions'] += 1
            self.logger.info(f"User logged in: {user_id}")

    async def _handle_user_logout(self, data: Dict[str, Any]):
        """Handle user logout event"""
        session_id = data.get('session_id')

        if session_id in self.user_sessions:
            session = self.user_sessions[session_id]
            session['active'] = False
            session['logout_time'] = datetime.utcnow().isoformat()

            self.delivery_stats['active_sessions'] -= 1
            self.logger.info(f"User logged out: {session['user_id']}")

    async def _should_create_case(self, enriched_log: Dict[str, Any]) -> bool:
        """Determine if an enriched log should trigger case creation"""
        entities = enriched_log.get('entities', {})

        # Check for high-risk indicators
        for entity_type, entity_data in entities.items():
            enrichments = entity_data.get('enrichments', {})

            # Check threat intelligence
            threat_intel = enrichments.get('threat_intel', {})
            if threat_intel.get('is_malicious', False):
                return True

            # Check reputation scores
            reputation_score = threat_intel.get('reputation_score', 100)
            if reputation_score < 50:
                return True

            # Check for suspicious processes
            if entity_type == 'process':
                process_info = enrichments.get('process_info', {})
                risk_score = process_info.get('risk_score', 0)
                if risk_score > 7:
                    return True

        return False

    async def _generate_case_title(self, enriched_log: Dict[str, Any]) -> str:
        """Generate a descriptive case title from enriched log"""
        original_log = enriched_log.get('original_log', {})
        log_data = original_log.get('data', {})

        if log_data.get('event_type') == 'detection':
            severity = log_data.get('severity', 'unknown')
            host = log_data.get('host', 'unknown')
            return f"Security Detection - {severity.title()} severity on {host}"

        return "Security Incident - Automated Detection"

    async def _generate_case_description(self, enriched_log: Dict[str, Any]) -> str:
        """Generate a case description from enriched log"""
        original_log = enriched_log.get('original_log', {})
        entities = enriched_log.get('entities', {})

        description_parts = [
            "Automated case created from security detection.",
            f"Original log timestamp: {original_log.get('timestamp', 'unknown')}",
            f"Entities involved: {', '.join(entities.keys())}"
        ]

        return " ".join(description_parts)

    async def _deliver_alert_to_channel(self, alert: Dict[str, Any], channel: str):
        """REAL alert delivery to specific channel (writes to files/logs)"""
        import os
        from pathlib import Path

        # Create alerts directory if it doesn't exist
        alerts_dir = Path('alerts')
        alerts_dir.mkdir(exist_ok=True)

        # Format alert for delivery
        alert_content = {
            'alert_id': alert['alert_id'],
            'type': alert['type'],
            'message': alert['message'],
            'priority': alert['priority'],
            'recipients': alert['recipients'],
            'delivered_at': alert['delivered_at'],
            'channel': channel
        }

        # Write to channel-specific file
        if channel == 'email':
            # Write to email queue file
            email_file = alerts_dir / 'email_alerts.jsonl'
            with open(email_file, 'a') as f:
                f.write(json.dumps(alert_content) + '\n')

            # Also log it
            self.logger.info(f"EMAIL ALERT: {alert['message'][:100]}")
            await asyncio.sleep(0.01)  # Small delay for realism

        elif channel == 'slack':
            # Write to Slack queue file
            slack_file = alerts_dir / 'slack_alerts.jsonl'
            with open(slack_file, 'a') as f:
                f.write(json.dumps(alert_content) + '\n')

            # Format for Slack-style output
            self.logger.info(f"SLACK: #{alert['priority']}-alerts | {alert['message'][:100]}")
            await asyncio.sleep(0.01)

        elif channel == 'sms':
            # Write to SMS queue file
            sms_file = alerts_dir / 'sms_alerts.jsonl'
            with open(sms_file, 'a') as f:
                # SMS would be shorter
                sms_content = alert_content.copy()
                sms_content['message'] = alert['message'][:160]  # SMS limit
                f.write(json.dumps(sms_content) + '\n')

            self.logger.info(f"SMS ALERT to on-call: {alert['message'][:60]}")
            await asyncio.sleep(0.02)

        elif channel == 'dashboard':
            # Write to dashboard events file
            dashboard_file = alerts_dir / 'dashboard_events.jsonl'
            with open(dashboard_file, 'a') as f:
                f.write(json.dumps(alert_content) + '\n')

            # Update in-memory dashboard data
            if not hasattr(self, 'recent_alerts'):
                self.recent_alerts = []

            self.recent_alerts.append(alert_content)
            # Keep only last 100 alerts in memory
            self.recent_alerts = self.recent_alerts[-100:]

        self.logger.info(f"Alert {alert['alert_id']} delivered via {channel} [REAL]")

    async def _generate_dashboard_data(self, user_id: str, dashboard_type: str) -> Dict[str, Any]:
        """Generate dashboard data for user"""
        base_data = {
            'user_id': user_id,
            'dashboard_type': dashboard_type,
            'generated_at': datetime.utcnow().isoformat(),
            'stats': self.delivery_stats.copy()
        }

        if dashboard_type == 'overview':
            base_data.update({
                'active_cases': len(self.active_cases),
                'recent_cases': list(self.active_cases.values())[:5],
                'system_health': 'healthy',
                'recent_alerts': []
            })
        elif dashboard_type == 'cases':
            base_data.update({
                'all_cases': list(self.active_cases.values()),
                'case_summary': {
                    'open': len([c for c in self.active_cases.values() if c['status'] == 'open']),
                    'high_priority': len([c for c in self.active_cases.values() if c['priority'] == 'high'])
                }
            })

        return base_data

    async def _case_management_loop(self):
        """Main case management processing loop"""
        while self.is_running:
            try:
                # Check for stale cases that need attention
                current_time = datetime.utcnow()

                for case_id, case in self.active_cases.items():
                    updated_at = datetime.fromisoformat(case['updated_at'])
                    time_since_update = current_time - updated_at

                    # Flag cases that haven't been updated in 4 hours
                    if time_since_update > timedelta(hours=4):
                        case['flags'] = case.get('flags', [])
                        if 'stale' not in case['flags']:
                            case['flags'].append('stale')

                            # Publish stale case alert
                            self.publish_message('delivery.case_stale', {
                                'case_id': case_id,
                                'time_since_update': str(time_since_update),
                                'timestamp': datetime.utcnow().isoformat()
                            })

                await asyncio.sleep(300)  # Check every 5 minutes

            except Exception as e:
                self.logger.error(f"Case management loop error: {e}")
                await asyncio.sleep(60)

    async def _dashboard_update_loop(self):
        """Update dashboard data periodically"""
        while self.is_running:
            try:
                # Update global dashboard data
                self.dashboard_data = {
                    'last_updated': datetime.utcnow().isoformat(),
                    'system_stats': self.delivery_stats.copy(),
                    'active_cases_count': len(self.active_cases),
                    'active_sessions_count': len([s for s in self.user_sessions.values() if s['active']])
                }

                # Publish dashboard update
                self.publish_message('delivery.dashboard_updated', {
                    'data': self.dashboard_data,
                    'timestamp': datetime.utcnow().isoformat()
                })

                await asyncio.sleep(30)  # Update every 30 seconds

            except Exception as e:
                self.logger.error(f"Dashboard update loop error: {e}")
                await asyncio.sleep(60)

    async def _alert_delivery_loop(self):
        """Process alert delivery queue"""
        while self.is_running:
            try:
                # Check for scheduled alerts or escalations
                # For now, just maintain delivery stats
                await asyncio.sleep(60)

            except Exception as e:
                self.logger.error(f"Alert delivery loop error: {e}")
                await asyncio.sleep(30)

    async def _session_management_loop(self):
        """Manage user sessions and cleanup"""
        while self.is_running:
            try:
                # Clean up inactive sessions
                current_time = datetime.utcnow()
                expired_sessions = []

                for session_id, session in self.user_sessions.items():
                    last_activity = datetime.fromisoformat(session['last_activity'])
                    if current_time - last_activity > timedelta(hours=8):
                        expired_sessions.append(session_id)

                for session_id in expired_sessions:
                    session = self.user_sessions.pop(session_id)
                    if session['active']:
                        self.delivery_stats['active_sessions'] -= 1

                if expired_sessions:
                    self.logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")

                await asyncio.sleep(600)  # Clean every 10 minutes

            except Exception as e:
                self.logger.error(f"Session management loop error: {e}")
                await asyncio.sleep(300)

    # ============================================
    # WORKFLOW ORCHESTRATION METHODS
    # ============================================

    async def _workflow_orchestration_loop(self):
        """Main workflow orchestration processing loop"""
        while self.is_running:
            try:
                # Monitor active workflows
                active_count = len(self.orchestrator.active_workflows)

                if active_count > 0:
                    self.logger.info(f"Active workflows: {active_count}")

                # Update workflow stats
                self.delivery_stats['workflows_executed'] = self.orchestrator.stats['workflows_started']
                self.delivery_stats['workflows_completed'] = self.orchestrator.stats['workflows_completed']
                self.delivery_stats['workflows_failed'] = self.orchestrator.stats['workflows_failed']

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                self.logger.error(f"Workflow orchestration loop error: {e}")
                await asyncio.sleep(60)

    async def _workflow_monitoring_loop(self):
        """Monitor workflow health and performance"""
        while self.is_running:
            try:
                # Check for stuck workflows
                for workflow_id, workflow in self.orchestrator.active_workflows.items():
                    if workflow['status'] == 'running':
                        started_at = datetime.fromisoformat(workflow['started_at'])
                        runtime = (datetime.utcnow() - started_at).seconds

                        # Alert if workflow is taking too long
                        template = self.workflow_templates.get(workflow['type'], {})
                        expected_timeout = template.get('timeout', 3600)

                        if runtime > expected_timeout * 1.5:  # 50% over expected
                            self.logger.warning(f"Workflow {workflow_id} exceeding expected runtime: {runtime}s")

                            # Send alert
                            await self._handle_send_alert({
                                'alert_type': 'warning',
                                'message': f"Workflow {workflow_id} running longer than expected",
                                'priority': 'medium'
                            })

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                self.logger.error(f"Workflow monitoring loop error: {e}")
                await asyncio.sleep(120)

    async def _recover_workflows(self):
        """Recover incomplete workflows on startup"""
        try:
            recovered = await self.orchestrator.recover_workflows()
            if recovered > 0:
                self.logger.info(f"Recovered {recovered} incomplete workflows")
        except Exception as e:
            self.logger.error(f"Failed to recover workflows: {e}")

    async def _handle_start_workflow(self, data: Dict[str, Any]):
        """Handle workflow start request"""
        try:
            workflow_type = data.get('workflow_type')
            context = data.get('context', {})
            initiator = data.get('initiator', 'system')

            if not workflow_type:
                self.logger.error("No workflow type specified")
                return

            # Start the workflow
            workflow_id = await self.orchestrator.start_workflow(
                workflow_type, context, initiator
            )

            self.logger.info(f"Started workflow {workflow_id} of type {workflow_type}")

            # Publish workflow started event
            self.publish_message('delivery.workflow_started', {
                'workflow_id': workflow_id,
                'workflow_type': workflow_type,
                'timestamp': datetime.utcnow().isoformat()
            })

            # If this workflow should create a case, link them
            if workflow_type in ['incident_response', 'full_incident_response']:
                case_data = {
                    'title': f"Workflow: {workflow_type}",
                    'description': f"Automated workflow execution: {workflow_id}",
                    'case_type': 'workflow_execution',
                    'workflow_id': workflow_id
                }
                await self._handle_create_case(case_data)

        except Exception as e:
            self.logger.error(f"Failed to start workflow: {e}")

    async def _handle_cancel_workflow(self, data: Dict[str, Any]):
        """Handle workflow cancellation request"""
        try:
            workflow_id = data.get('workflow_id')

            if not workflow_id:
                self.logger.error("No workflow ID specified for cancellation")
                return

            success = await self.orchestrator.cancel_workflow(workflow_id)

            if success:
                self.logger.info(f"Cancelled workflow {workflow_id}")

                # Publish cancellation event
                self.publish_message('delivery.workflow_cancelled', {
                    'workflow_id': workflow_id,
                    'timestamp': datetime.utcnow().isoformat()
                })
            else:
                self.logger.warning(f"Failed to cancel workflow {workflow_id}")

        except Exception as e:
            self.logger.error(f"Failed to cancel workflow: {e}")

    async def _handle_get_workflow_status(self, data: Dict[str, Any]):
        """Handle workflow status request"""
        try:
            workflow_id = data.get('workflow_id')
            request_id = data.get('request_id')

            if not workflow_id:
                self.logger.error("No workflow ID specified for status check")
                return

            status = await self.orchestrator.get_workflow_status(workflow_id)

            # Publish status response
            self.publish_message('delivery.workflow_status_response', {
                'request_id': request_id,
                'workflow_id': workflow_id,
                'status': status,
                'timestamp': datetime.utcnow().isoformat()
            })

        except Exception as e:
            self.logger.error(f"Failed to get workflow status: {e}")

    async def _handle_workflow_step(self, channel: str, data: Dict[str, Any]):
        """Handle workflow step execution requests"""
        try:
            action = channel.split('.')[-1]
            step_id = data.get('step_id')
            context = data.get('context', {})

            self.logger.info(f"Executing workflow step: {action}")

            # Execute the appropriate action
            result = {}

            if action == 'create_incident_case':
                case_data = {
                    'title': context.get('incident_title', 'Workflow Incident'),
                    'description': context.get('incident_description', ''),
                    'priority': context.get('priority', 'high'),
                    'case_type': 'security_incident'
                }
                await self._handle_create_case(case_data)
                result = {'case_created': True}

            elif action == 'send_incident_notifications':
                alert_data = {
                    'alert_type': 'incident',
                    'message': context.get('notification_message', 'Security incident detected'),
                    'recipients': context.get('recipients', ['soc']),
                    'priority': context.get('priority', 'high')
                }
                await self._handle_send_alert(alert_data)
                result = {'notifications_sent': True}

            elif action == 'create_investigation_report':
                # Generate investigation report
                report_id = f"report_{int(time.time())}"
                result = {
                    'report_id': report_id,
                    'report_generated': True
                }

            elif action == 'health_check':
                # Perform health check
                result = {
                    'engine': 'delivery',
                    'status': 'healthy' if self.is_running else 'unhealthy',
                    'timestamp': datetime.utcnow().isoformat()
                }

            # Send response back to orchestrator
            response_channel = f"workflow.response.{step_id}"
            self.publish_message(response_channel, result)

        except Exception as e:
            self.logger.error(f"Failed to execute workflow step {channel}: {e}")

            # Send error response
            if 'step_id' in data:
                response_channel = f"workflow.response.{data['step_id']}"
                self.publish_message(response_channel, {'error': str(e)})

    # ============================================
    # INVESTIGATION ENGINE INTEGRATION
    # ============================================

    async def _handle_investigation_create(self, data: Dict[str, Any]):
        """Handle manual investigation creation"""
        try:
            investigation = await self.investigation_engine.create_investigation_from_alert(data)
            self.logger.info(f"Created investigation: {investigation.id}")

            # Publish investigation created event
            self.publish_message('delivery.investigation_created', {
                'investigation_id': investigation.id,
                'title': investigation.title,
                'severity': investigation.severity,
                'timestamp': datetime.utcnow().isoformat()
            })

        except Exception as e:
            self.logger.error(f"Failed to create investigation: {e}")

    async def _handle_alert_for_investigation(self, data: Dict[str, Any]):
        """
        Handle alerts from ingestion for auto-investigation

        NEW: Now triggers enrichment and correlation for all high/critical alerts
        """
        try:
            # Auto-create investigation for high/critical severity
            severity = data.get('severity', '').lower()
            if severity in ['high', 'critical']:
                self.logger.info(f"Auto-creating investigation for {severity} severity alert")

                # Extract alert metadata
                alert_id = data.get('alert_id', data.get('id', str(uuid.uuid4())))
                entities = data.get('entities', {})
                timestamp = data.get('timestamp', datetime.utcnow().isoformat())
                mitre_techniques = data.get('mitre_techniques', data.get('attack', {}).get('technique', []))

                # Generate request IDs for tracking
                enrich_request_id = f"enrich_{alert_id}"
                corr_request_id = f"corr_{alert_id}"

                # Trigger enrichment in Contextualization Engine
                self.logger.info(f"Triggering enrichment for alert {alert_id}")
                self.publish_message('contextualization.enrich_alert', {
                    'alert_id': alert_id,
                    'entities': entities,
                    'request_id': enrich_request_id,
                    'timestamp': timestamp
                })

                # Trigger correlation in Backend Engine
                self.logger.info(f"Triggering correlation for alert {alert_id}")
                self.publish_message('backend.correlate_alert', {
                    'alert_id': alert_id,
                    'entities': entities,
                    'timestamp': timestamp,
                    'mitre_techniques': mitre_techniques,
                    'request_id': corr_request_id
                })

                # Create investigation (will be enriched asynchronously)
                investigation = await self.investigation_engine.create_investigation_from_alert(data)

                # Subscribe to enrichment and correlation results
                # Store tracking info for async enrichment updates
                enrichment_tracking = {
                    'investigation_id': investigation.id,
                    'enrich_request_id': enrich_request_id,
                    'corr_request_id': corr_request_id,
                    'enrichment_pending': True,
                    'correlation_pending': True,
                    'created_at': datetime.utcnow().isoformat()
                }

                # Store in Redis for async updates
                self.redis_client.setex(
                    f"investigation_enrichment:{investigation.id}",
                    3600,  # 1 hour TTL
                    json.dumps(enrichment_tracking)
                )

                # Publish investigation created event
                self.publish_message('delivery.investigation_created', {
                    'investigation_id': investigation.id,
                    'title': investigation.title,
                    'severity': investigation.severity,
                    'auto_created': True,
                    'enrichment_requested': True,
                    'correlation_requested': True,
                    'timestamp': datetime.utcnow().isoformat()
                })

                self.logger.info(
                    f"Investigation {investigation.id} created with enrichment and correlation in progress"
                )

        except Exception as e:
            self.logger.error(f"Failed to auto-create investigation: {e}")

    async def _handle_enrichment_result(self, data: Dict[str, Any]):
        """
        NEW: Handle enrichment results from Contextualization Engine

        Updates investigation with enriched entity data
        """
        try:
            alert_id = data.get('alert_id')
            enriched_entities = data.get('enriched_entities', {})
            enrichment_summary = data.get('enrichment_summary', {})

            self.logger.info(
                f"Received enrichment for alert {alert_id}: "
                f"{enrichment_summary.get('enriched_count', 0)} entities enriched, "
                f"{enrichment_summary.get('threat_indicators_found', 0)} threats found"
            )

            # Find investigation by alert_id
            tracking_key = f"investigation_enrichment:*"
            keys = []
            for key in self.redis_client.scan_iter(match=tracking_key):
                tracking_data = self.redis_client.get(key)
                if tracking_data:
                    tracking = json.loads(tracking_data)
                    if tracking.get('enrich_request_id', '').endswith(alert_id):
                        keys.append(key)
                        investigation_id = tracking.get('investigation_id')

                        # Update investigation with enrichment
                        if investigation_id in self.investigation_engine.active_investigations:
                            investigation = self.investigation_engine.active_investigations[investigation_id]

                            # Add enrichment data to investigation
                            if not hasattr(investigation, 'enrichment'):
                                investigation.enrichment = {}

                            investigation.enrichment = {
                                'entities': enriched_entities,
                                'summary': enrichment_summary,
                                'timestamp': datetime.utcnow().isoformat()
                            }

                            # Mark enrichment complete
                            tracking['enrichment_pending'] = False
                            self.redis_client.setex(key, 3600, json.dumps(tracking))

                            self.logger.info(f"Investigation {investigation_id} updated with enrichment data")

        except Exception as e:
            self.logger.error(f"Error handling enrichment result: {e}", exc_info=True)

    async def _handle_correlation_result(self, data: Dict[str, Any]):
        """
        NEW: Handle correlation results from Backend Engine

        Updates investigation with correlated events and attack chain
        """
        try:
            alert_id = data.get('alert_id')
            related_events = data.get('related_events', [])
            correlation_summary = data.get('correlation_summary', {})
            time_window = data.get('time_window', {})

            self.logger.info(
                f"Received correlation for alert {alert_id}: "
                f"{correlation_summary.get('total_related_events', 0)} related events, "
                f"{len(correlation_summary.get('attack_stages_detected', []))} attack stages, "
                f"score: {correlation_summary.get('correlation_score', 0):.2f}"
            )

            # Find investigation by alert_id
            tracking_key = f"investigation_enrichment:*"
            for key in self.redis_client.scan_iter(match=tracking_key):
                tracking_data = self.redis_client.get(key)
                if tracking_data:
                    tracking = json.loads(tracking_data)
                    if tracking.get('corr_request_id', '').endswith(alert_id):
                        investigation_id = tracking.get('investigation_id')

                        # Update investigation with correlation
                        if investigation_id in self.investigation_engine.active_investigations:
                            investigation = self.investigation_engine.active_investigations[investigation_id]

                            # Add correlation data to investigation
                            if not hasattr(investigation, 'correlation'):
                                investigation.correlation = {}

                            investigation.correlation = {
                                'related_events': related_events,
                                'summary': correlation_summary,
                                'time_window': time_window,
                                'mitre_chain': correlation_summary.get('mitre_chain', []),
                                'attack_stages': correlation_summary.get('attack_stages_detected', []),
                                'score': correlation_summary.get('correlation_score', 0),
                                'timestamp': datetime.utcnow().isoformat()
                            }

                            # Mark correlation complete
                            tracking['correlation_pending'] = False
                            self.redis_client.setex(key, 3600, json.dumps(tracking))

                            self.logger.info(f"Investigation {investigation_id} updated with correlation data")

                            # If both enrichment and correlation complete, trigger final analysis
                            if not tracking.get('enrichment_pending', True) and not tracking.get('correlation_pending', True):
                                self.logger.info(
                                    f"Investigation {investigation_id} fully enriched and correlated - ready for analysis"
                                )

                                # Could trigger additional analysis here
                                # e.g., AI-powered investigation summary, risk scoring, etc.

        except Exception as e:
            self.logger.error(f"Error handling correlation result: {e}", exc_info=True)

    async def _investigation_processing_loop(self):
        """Process investigation updates and enrichment"""
        while self.is_running:
            try:
                # Monitor active investigations
                active_count = len(self.investigation_engine.active_investigations)

                if active_count > 0:
                    self.logger.debug(f"Active investigations: {active_count}")

                # Check for stale investigations
                for inv_id, inv in list(self.investigation_engine.active_investigations.items()):
                    if inv.status == 'open':
                        created_at = inv.created_at
                        age = (datetime.utcnow() - created_at).seconds

                        # Alert on investigations open > 1 hour
                        if age > 3600 and not hasattr(inv, 'stale_alerted'):
                            self.logger.warning(f"Investigation {inv_id} open for {age}s without action")
                            inv.stale_alerted = True

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                self.logger.error(f"Investigation processing loop error: {e}")
                await asyncio.sleep(60)

    async def _setup_http_routes(self, app: web.Application):
        """Setup HTTP routes for REST API"""

        # ============================================
        # AUTHENTICATION MIDDLEWARE (COMMENTED OUT)
        # ============================================
        # Uncomment the following lines to enable authentication
        # if self.auth_middleware is None:
        #     import redis.asyncio as redis_async
        #     async_redis = await redis_async.Redis(
        #         host=os.getenv('REDIS_HOST', 'localhost'),
        #         port=int(os.getenv('REDIS_PORT', 6379)),
        #         decode_responses=True
        #     )
        #
        #     from auth_middleware import KeycloakAuthMiddleware
        #     self.auth_middleware = KeycloakAuthMiddleware(
        #         keycloak_url=os.getenv('KEYCLOAK_URL', 'http://keycloak:8080'),
        #         realm='siemless',
        #         client_id='siemless-api',
        #         redis_client=async_redis
        #     )
        #     self.logger.info("Authentication middleware initialized")
        #
        # # Add middleware to app
        # app.middlewares.append(self.auth_middleware.auth_middleware)
        # self.logger.info("Authentication middleware added to HTTP app")
        # ============================================

        # Case Management Endpoints
        app.router.add_post('/api/cases', self._api_create_case)
        app.router.add_get('/api/cases', self._api_list_cases)
        app.router.add_get('/api/cases/{case_id}', self._api_get_case)
        app.router.add_put('/api/cases/{case_id}', self._api_update_case)
        app.router.add_delete('/api/cases/{case_id}', self._api_close_case)
        app.router.add_post('/api/cases/{case_id}/evidence', self._api_add_evidence)

        # Dashboard Endpoints
        app.router.add_get('/api/dashboard/overview', self._api_dashboard_overview)
        app.router.add_get('/api/dashboard/cases', self._api_dashboard_cases)
        app.router.add_get('/api/dashboard/stats', self._api_dashboard_stats)

        # Alert Management Endpoints
        app.router.add_post('/api/alerts', self._api_send_alert)
        app.router.add_get('/api/alerts', self._api_list_alerts)  # NEW - for frontend
        app.router.add_get('/api/alerts/{alert_id}/context', self._api_get_alert_context)  # NEW - Investigation context
        app.router.add_get('/api/alerts/history', self._api_alert_history)
        # NEW: Alert enrichment and correlation endpoints
        app.router.add_post('/api/alerts/{alert_id}/enrich', self._api_trigger_enrichment)  # Manual enrichment
        app.router.add_post('/api/alerts/{alert_id}/correlate', self._api_trigger_correlation)  # Manual correlation
        app.router.add_get('/api/alerts/{alert_id}/enrichment', self._api_get_enrichment_status)  # Enrichment status
        app.router.add_get('/api/alerts/{alert_id}/correlation', self._api_get_correlation_results)  # Correlation results

        # User Session Endpoints
        app.router.add_post('/api/auth/login', self._api_user_login)
        app.router.add_post('/api/auth/logout', self._api_user_logout)
        app.router.add_get('/api/auth/sessions', self._api_list_sessions)

        # System Status Endpoints
        app.router.add_get('/api/system/status', self._api_system_status)
        app.router.add_get('/api/system/engines', self._api_engine_status)

        # Workflow Orchestration Endpoints - ENSURING DELIVERY
        app.router.add_post('/api/workflows/start', self._api_start_workflow)
        app.router.add_get('/api/workflows', self._api_list_workflows)
        app.router.add_get('/api/workflows/{workflow_id}', self._api_get_workflow)
        app.router.add_post('/api/workflows/{workflow_id}/cancel', self._api_cancel_workflow)
        app.router.add_get('/api/workflows/templates', self._api_list_workflow_templates)
        app.router.add_get('/api/workflows/statistics', self._api_workflow_statistics)

        # Pattern Management Endpoints
        app.router.add_get('/api/patterns', self._api_list_patterns)
        app.router.add_get('/api/patterns/{pattern_id}', self._api_get_pattern)
        app.router.add_post('/api/patterns/test', self._api_test_pattern)
        app.router.add_get('/api/patterns/{pattern_id}/performance', self._api_pattern_performance)

        # Rule Management Endpoints
        app.router.add_get('/api/rules', self._api_list_rules)
        app.router.add_get('/api/rules/{rule_id}', self._api_get_rule)
        app.router.add_post('/api/rules/test', self._api_test_rule)
        app.router.add_delete('/api/rules/{rule_id}', self._api_delete_rule)
        app.router.add_patch('/api/rules/{rule_id}', self._api_update_rule)
        app.router.add_post('/api/rules/bulk-delete', self._api_bulk_delete_rules)

        # Entity Endpoints
        app.router.add_get('/api/entities', self._api_list_entities)
        app.router.add_get('/api/entities/{entity_id}', self._api_get_entity)
        app.router.add_get('/api/entities/{entity_id}/enrichment', self._api_get_entity_enrichment)
        app.router.add_get('/api/entities/{entity_id}/relationships', self._api_get_entity_relationships)
        app.router.add_get('/api/entities/{entity_id}/timeline', self._api_get_entity_timeline)
        app.router.add_get('/api/entities/{entity_id}/risk', self._api_get_entity_risk)

        # Investigation Endpoints (NEW)
        for route in self.investigation_http_handlers.get_routes():
            app.router.add_route(route.method, route.path, route.handler)

        # Business Context Management Endpoints (NEW)
        self.business_context_manager.register_routes(app)
        self.logger.info("Business Context Management routes registered")

        # Static file serving for frontend
        app.router.add_static('/', path='frontend/', name='static')

        self.logger.info("HTTP routes configured for Delivery Engine with Workflow Orchestration and Investigation Engine")

    async def _api_create_case(self, request: web.Request) -> web.Response:
        """REST API endpoint for creating cases"""
        try:
            data = await request.json()

            # Validate required fields
            if 'title' not in data:
                return web.json_response({'error': 'Title is required'}, status=400)

            await self._handle_create_case(data)

            return web.json_response({
                'success': True,
                'message': 'Case created successfully'
            })

        except Exception as e:
            self.logger.error(f"API create case error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_list_cases(self, request: web.Request) -> web.Response:
        """REST API endpoint for listing cases"""
        try:
            status_filter = request.query.get('status', 'all')
            priority_filter = request.query.get('priority', 'all')

            cases = list(self.active_cases.values())

            # Apply filters
            if status_filter != 'all':
                cases = [c for c in cases if c['status'] == status_filter]
            if priority_filter != 'all':
                cases = [c for c in cases if c['priority'] == priority_filter]

            return web.json_response({
                'cases': cases,
                'total': len(cases),
                'filters': {
                    'status': status_filter,
                    'priority': priority_filter
                }
            })

        except Exception as e:
            self.logger.error(f"API list cases error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_get_case(self, request: web.Request) -> web.Response:
        """REST API endpoint for getting specific case"""
        try:
            case_id = request.match_info['case_id']

            if case_id not in self.active_cases:
                return web.json_response({'error': 'Case not found'}, status=404)

            case = self.active_cases[case_id]
            return web.json_response({'case': case})

        except Exception as e:
            self.logger.error(f"API get case error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_update_case(self, request: web.Request) -> web.Response:
        """REST API endpoint for updating cases"""
        try:
            case_id = request.match_info['case_id']
            updates = await request.json()

            if case_id not in self.active_cases:
                return web.json_response({'error': 'Case not found'}, status=404)

            await self._handle_update_case({
                'case_id': case_id,
                'updates': updates
            })

            return web.json_response({
                'success': True,
                'message': 'Case updated successfully',
                'case': self.active_cases[case_id]
            })

        except Exception as e:
            self.logger.error(f"API update case error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_close_case(self, request: web.Request) -> web.Response:
        """REST API endpoint for closing cases"""
        try:
            case_id = request.match_info['case_id']
            data = await request.json()

            if case_id not in self.active_cases:
                return web.json_response({'error': 'Case not found'}, status=404)

            await self._handle_close_case({
                'case_id': case_id,
                'resolution': data.get('resolution', 'Closed via API'),
                'closed_by': data.get('closed_by', 'API User')
            })

            return web.json_response({
                'success': True,
                'message': 'Case closed successfully'
            })

        except Exception as e:
            self.logger.error(f"API close case error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_add_evidence(self, request: web.Request) -> web.Response:
        """REST API endpoint for adding evidence to cases"""
        try:
            case_id = request.match_info['case_id']
            evidence = await request.json()

            if case_id not in self.active_cases:
                return web.json_response({'error': 'Case not found'}, status=404)

            case = self.active_cases[case_id]
            evidence['added_at'] = datetime.utcnow().isoformat()
            evidence['evidence_id'] = f"evidence_{int(time.time())}"

            case['evidence'].append(evidence)
            case['timeline'].append({
                'timestamp': datetime.utcnow().isoformat(),
                'action': 'evidence_added',
                'details': f"Evidence added: {evidence.get('type', 'unknown')}"
            })

            return web.json_response({
                'success': True,
                'message': 'Evidence added successfully',
                'evidence_id': evidence['evidence_id']
            })

        except Exception as e:
            self.logger.error(f"API add evidence error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_dashboard_overview(self, request: web.Request) -> web.Response:
        """REST API endpoint for dashboard overview"""
        try:
            user_id = request.query.get('user_id', 'anonymous')
            dashboard_data = await self._generate_dashboard_data(user_id, 'overview')

            return web.json_response(dashboard_data)

        except Exception as e:
            self.logger.error(f"API dashboard overview error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_dashboard_cases(self, request: web.Request) -> web.Response:
        """REST API endpoint for dashboard cases view"""
        try:
            user_id = request.query.get('user_id', 'anonymous')
            dashboard_data = await self._generate_dashboard_data(user_id, 'cases')

            return web.json_response(dashboard_data)

        except Exception as e:
            self.logger.error(f"API dashboard cases error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_dashboard_stats(self, request: web.Request) -> web.Response:
        """REST API endpoint for dashboard statistics"""
        try:
            return web.json_response({
                'stats': self.delivery_stats,
                'active_cases': len(self.active_cases),
                'active_sessions': len([s for s in self.user_sessions.values() if s['active']]),
                'timestamp': datetime.utcnow().isoformat()
            })

        except Exception as e:
            self.logger.error(f"API dashboard stats error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _fetch_elastic_alerts(self, status_filter: str = 'open', limit: int = 50) -> List[Dict]:
        """Fetch alerts from Elastic Security"""
        try:
            # Get Elastic credentials from environment
            cloud_id = os.getenv('ELASTIC_CLOUD_ID', '')
            api_key = os.getenv('ELASTIC_API_KEY', '')

            if not cloud_id or not api_key:
                self.logger.warning("Elastic credentials not configured")
                return []

            # Decode cloud ID to get Elastic URL
            cloud_data = base64.b64decode(cloud_id.split(':')[1]).decode('utf-8')
            parts = cloud_data.split('$')
            elastic_url = f"https://{parts[1]}.{parts[0]}"

            headers = {
                'Authorization': f"ApiKey {api_key}",
                'Content-Type': 'application/json'
            }

            # Build query based on status filter
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {"range": {"@timestamp": {"gte": "now-7d"}}}
                        ]
                    }
                },
                "size": limit,
                "sort": [{"@timestamp": "desc"}]
            }

            # Add status filter if not 'all'
            if status_filter != 'all':
                query["query"]["bool"]["must"].append({
                    "term": {"kibana.alert.workflow_status": status_filter}
                })

            async with ClientSession() as session:
                url = f"{elastic_url}/.alerts-*/_search"
                async with session.post(url, headers=headers, json=query, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        hits = data.get('hits', {}).get('hits', [])

                        # Normalize alerts to our format
                        alerts = []
                        for hit in hits:
                            source = hit.get('_source', {})

                            # Extract investigation guide from rule note
                            investigation_guide = source.get('kibana.alert.rule.note', '')

                            alert = {
                                'alert_id': hit.get('_id', ''),
                                'title': source.get('kibana.alert.rule.name', 'Unknown Alert'),
                                'severity': source.get('kibana.alert.severity', 'medium'),
                                'timestamp': source.get('@timestamp', datetime.utcnow().isoformat()),
                                'status': source.get('kibana.alert.workflow_status', 'open'),
                                'type': 'elastic_security',
                                'message': source.get('kibana.alert.reason', ''),
                                'investigation_guide': investigation_guide,  # NEW - Investigation guide from rule
                                'rule_description': source.get('kibana.alert.rule.description', ''),  # NEW - Rule description
                                'entities': {
                                    'ips': source.get('source.ip', []) if isinstance(source.get('source.ip'), list) else [source.get('source.ip')] if source.get('source.ip') else [],
                                    'users': source.get('user.name', []) if isinstance(source.get('user.name'), list) else [source.get('user.name')] if source.get('user.name') else [],
                                    'hosts': source.get('host.name', []) if isinstance(source.get('host.name'), list) else [source.get('host.name')] if source.get('host.name') else [],
                                },
                                'mitre_techniques': self._extract_mitre_from_elastic(source),
                                'raw_data': source
                            }
                            alerts.append(alert)

                        self.logger.info(f"Fetched {len(alerts)} alerts from Elastic")
                        return alerts
                    else:
                        self.logger.error(f"Elastic query failed: {response.status}")
                        return []

        except Exception as e:
            self.logger.error(f"Error fetching Elastic alerts: {e}")
            return []

    def _extract_mitre_from_elastic(self, source: Dict) -> List[str]:
        """Extract MITRE ATT&CK techniques from Elastic alert"""
        techniques = []
        try:
            threat_data = source.get('kibana.alert.rule.threat', [])
            for threat in threat_data:
                for technique in threat.get('technique', []):
                    tech_id = technique.get('id')
                    if tech_id:
                        techniques.append(tech_id)
        except Exception:
            pass
        return techniques

    async def _api_send_alert(self, request: web.Request) -> web.Response:
        """REST API endpoint for sending alerts"""
        try:
            alert_data = await request.json()

            # Validate required fields
            if 'message' not in alert_data:
                return web.json_response({'error': 'Message is required'}, status=400)

            await self._handle_send_alert(alert_data)

            return web.json_response({
                'success': True,
                'message': 'Alert sent successfully'
            })

        except Exception as e:
            self.logger.error(f"API send alert error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_list_alerts(self, request: web.Request) -> web.Response:
        """
        REST API endpoint for listing alerts
        GET /api/alerts?status=open&limit=50

        Returns:
        {
            "alerts": [...],
            "count": 10
        }
        """
        try:
            status_filter = request.query.get('status', 'open')
            limit = int(request.query.get('limit', 50))

            # Fetch alerts from Elastic Security
            alerts = await self._fetch_elastic_alerts(status_filter, limit)

            return web.json_response({
                'alerts': alerts,
                'count': len(alerts)
            })

        except Exception as e:
            self.logger.error(f"API list alerts error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_get_alert_context(self, request: web.Request) -> web.Response:
        """
        REST API endpoint for getting full investigation context for an alert
        GET /api/alerts/{alert_id}/context

        This answers the generic investigation guide with REAL context:
        - "Correlate with threat intelligence" → Shows actual TI results
        - "Check if internal/external" → Shows IP ownership & location
        - "Review historical behavior" → Shows past activity patterns
        - "Check related entities" → Shows entity graph

        Returns:
        {
            "alert_id": "...",
            "entities": {
                "ip:***********": {
                    "type": "ip",
                    "ip_type": "internal",
                    "risk_score": 0,
                    ...
                }
            },
            "threat_intelligence": {
                "verdict": "BENIGN",
                "risk_score": 0,
                "sources": [...]
            },
            "historical_behavior": {...},
            "timeline": [...],
            "ai_verdict": {
                "verdict": "LIKELY_BENIGN",
                "confidence": 85,
                "reasoning": [...],
                "recommended_action": "REVIEW_AND_CLOSE"
            },
            "siem_linkback": {
                "query": "...",
                "url_template": "..."
            }
        }
        """
        try:
            alert_id = request.match_info['alert_id']

            # First fetch the alert from Elastic
            alerts = await self._fetch_elastic_alerts('all', 100)

            # Find the specific alert
            alert = None
            for a in alerts:
                if a.get('alert_id') == alert_id:
                    alert = a
                    break

            if not alert:
                return web.json_response({
                    'error': f'Alert {alert_id} not found'
                }, status=404)

            # Generate full investigation context
            context = await self.context_generator.generate_investigation_context(alert)

            return web.json_response(context)

        except Exception as e:
            self.logger.error(f"API get alert context error: {e}")
            import traceback
            traceback.print_exc()
            return web.json_response({'error': str(e)}, status=500)

    async def _api_alert_history(self, request: web.Request) -> web.Response:
        """REST API endpoint for alert history"""
        try:
            # This would typically fetch from database
            # For now, return delivery stats
            return web.json_response({
                'total_alerts': self.delivery_stats['alerts_delivered'],
                'message': 'Alert history feature coming soon'
            })

        except Exception as e:
            self.logger.error(f"API alert history error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_trigger_enrichment(self, request: web.Request) -> web.Response:
        """
        NEW: Manually trigger alert enrichment
        POST /api/alerts/{alert_id}/enrich

        Request body:
        {
            "entities": {
                "ip": ["***********"],
                "user": ["john.doe"],
                "host": ["workstation-1"]
            }
        }

        Returns:
        {
            "success": true,
            "request_id": "enrich_abc123",
            "message": "Enrichment triggered"
        }
        """
        try:
            alert_id = request.match_info['alert_id']
            data = await request.json()

            entities = data.get('entities', {})
            request_id = f"manual_enrich_{alert_id}"

            # Trigger enrichment
            self.publish_message('contextualization.enrich_alert', {
                'alert_id': alert_id,
                'entities': entities,
                'request_id': request_id,
                'timestamp': datetime.utcnow().isoformat(),
                'manual_trigger': True
            })

            self.logger.info(f"Manual enrichment triggered for alert {alert_id}")

            return web.json_response({
                'success': True,
                'request_id': request_id,
                'message': f'Enrichment triggered for alert {alert_id}',
                'result_channel': f'contextualization.alert.enriched.{request_id}'
            })

        except Exception as e:
            self.logger.error(f"API trigger enrichment error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_trigger_correlation(self, request: web.Request) -> web.Response:
        """
        NEW: Manually trigger alert correlation
        POST /api/alerts/{alert_id}/correlate

        Request body:
        {
            "entities": {
                "ip": ["***********"],
                "user": ["john.doe"]
            },
            "timestamp": "2025-10-03T12:00:00Z",
            "mitre_techniques": ["T1021", "T1078"]
        }

        Returns:
        {
            "success": true,
            "request_id": "corr_abc123",
            "message": "Correlation triggered"
        }
        """
        try:
            alert_id = request.match_info['alert_id']
            data = await request.json()

            entities = data.get('entities', {})
            timestamp = data.get('timestamp', datetime.utcnow().isoformat())
            mitre_techniques = data.get('mitre_techniques', [])
            request_id = f"manual_corr_{alert_id}"

            # Trigger correlation
            self.publish_message('backend.correlate_alert', {
                'alert_id': alert_id,
                'entities': entities,
                'timestamp': timestamp,
                'mitre_techniques': mitre_techniques,
                'request_id': request_id,
                'manual_trigger': True
            })

            self.logger.info(f"Manual correlation triggered for alert {alert_id}")

            return web.json_response({
                'success': True,
                'request_id': request_id,
                'message': f'Correlation triggered for alert {alert_id}',
                'result_channel': f'backend.correlation.complete.{request_id}'
            })

        except Exception as e:
            self.logger.error(f"API trigger correlation error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_get_enrichment_status(self, request: web.Request) -> web.Response:
        """
        NEW: Get enrichment status for an alert
        GET /api/alerts/{alert_id}/enrichment

        Returns:
        {
            "alert_id": "abc123",
            "enrichment": {
                "entities": {...},
                "summary": {...},
                "timestamp": "2025-10-03T12:00:00Z"
            },
            "status": "completed" | "pending" | "not_found"
        }
        """
        try:
            alert_id = request.match_info['alert_id']

            # Look for enrichment tracking data
            tracking_key = f"investigation_enrichment:*"
            enrichment_data = None

            for key in self.redis_client.scan_iter(match=tracking_key):
                tracking = json.loads(self.redis_client.get(key))
                if tracking.get('enrich_request_id', '').endswith(alert_id):
                    investigation_id = tracking.get('investigation_id')

                    # Get enrichment from investigation
                    if investigation_id in self.investigation_engine.active_investigations:
                        inv = self.investigation_engine.active_investigations[investigation_id]
                        if hasattr(inv, 'enrichment'):
                            enrichment_data = inv.enrichment
                            status = 'completed'
                        else:
                            status = 'pending' if tracking.get('enrichment_pending', True) else 'failed'
                    break

            if enrichment_data:
                return web.json_response({
                    'alert_id': alert_id,
                    'enrichment': enrichment_data,
                    'status': status
                })
            else:
                return web.json_response({
                    'alert_id': alert_id,
                    'enrichment': None,
                    'status': 'not_found',
                    'message': 'No enrichment data found for this alert'
                })

        except Exception as e:
            self.logger.error(f"API get enrichment status error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_get_correlation_results(self, request: web.Request) -> web.Response:
        """
        NEW: Get correlation results for an alert
        GET /api/alerts/{alert_id}/correlation

        Returns:
        {
            "alert_id": "abc123",
            "correlation": {
                "related_events": [...],
                "summary": {...},
                "mitre_chain": [...],
                "attack_stages": [...],
                "score": 0.85
            },
            "status": "completed" | "pending" | "not_found"
        }
        """
        try:
            alert_id = request.match_info['alert_id']

            # Look for correlation tracking data
            tracking_key = f"investigation_enrichment:*"
            correlation_data = None

            for key in self.redis_client.scan_iter(match=tracking_key):
                tracking = json.loads(self.redis_client.get(key))
                if tracking.get('corr_request_id', '').endswith(alert_id):
                    investigation_id = tracking.get('investigation_id')

                    # Get correlation from investigation
                    if investigation_id in self.investigation_engine.active_investigations:
                        inv = self.investigation_engine.active_investigations[investigation_id]
                        if hasattr(inv, 'correlation'):
                            correlation_data = inv.correlation
                            status = 'completed'
                        else:
                            status = 'pending' if tracking.get('correlation_pending', True) else 'failed'
                    break

            if correlation_data:
                return web.json_response({
                    'alert_id': alert_id,
                    'correlation': correlation_data,
                    'status': status
                })
            else:
                return web.json_response({
                    'alert_id': alert_id,
                    'correlation': None,
                    'status': 'not_found',
                    'message': 'No correlation data found for this alert'
                })

        except Exception as e:
            self.logger.error(f"API get correlation results error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_user_login(self, request: web.Request) -> web.Response:
        """REST API endpoint for user login"""
        try:
            login_data = await request.json()
            user_id = login_data.get('user_id')

            if not user_id:
                return web.json_response({'error': 'User ID is required'}, status=400)

            session_id = f"session_{int(time.time())}"

            await self._handle_user_login({
                'user_id': user_id,
                'session_id': session_id
            })

            return web.json_response({
                'success': True,
                'session_id': session_id,
                'message': 'Login successful'
            })

        except Exception as e:
            self.logger.error(f"API user login error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_user_logout(self, request: web.Request) -> web.Response:
        """REST API endpoint for user logout"""
        try:
            logout_data = await request.json()
            session_id = logout_data.get('session_id')

            if not session_id:
                return web.json_response({'error': 'Session ID is required'}, status=400)

            await self._handle_user_logout({'session_id': session_id})

            return web.json_response({
                'success': True,
                'message': 'Logout successful'
            })

        except Exception as e:
            self.logger.error(f"API user logout error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_list_sessions(self, request: web.Request) -> web.Response:
        """REST API endpoint for listing active sessions"""
        try:
            active_sessions = [
                {
                    'session_id': sid,
                    'user_id': session['user_id'],
                    'login_time': session['login_time'],
                    'last_activity': session['last_activity'],
                    'active': session['active']
                }
                for sid, session in self.user_sessions.items()
                if session['active']
            ]

            return web.json_response({
                'sessions': active_sessions,
                'total': len(active_sessions)
            })

        except Exception as e:
            self.logger.error(f"API list sessions error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_system_status(self, request: web.Request) -> web.Response:
        """REST API endpoint for system status"""
        try:
            # Check connectivity to Redis and database
            redis_status = await self._check_redis_connection()
            db_status = await self._check_database_connection()

            system_status = {
                'engine': 'delivery',
                'status': 'healthy' if self.is_running else 'unhealthy',
                'uptime': str(datetime.utcnow() - self.start_time) if self.start_time else '0',
                'redis_connected': redis_status,
                'database_connected': db_status,
                'active_cases': len(self.active_cases),
                'active_sessions': len([s for s in self.user_sessions.values() if s['active']]),
                'stats': self.delivery_stats,
                'timestamp': datetime.utcnow().isoformat()
            }

            return web.json_response(system_status)

        except Exception as e:
            self.logger.error(f"API system status error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_engine_status(self, request: web.Request) -> web.Response:
        """REST API endpoint for all engine status"""
        try:
            # This would query other engines via Redis
            # For now, return basic info
            return web.json_response({
                'engines': {
                    'delivery': {
                        'status': 'healthy' if self.is_running else 'unhealthy',
                        'port': 8005
                    }
                },
                'message': 'Full engine status feature coming soon'
            })

        except Exception as e:
            self.logger.error(f"API engine status error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    # ============================================
    # WORKFLOW ORCHESTRATION API ENDPOINTS
    # ============================================

    async def _api_start_workflow(self, request: web.Request) -> web.Response:
        """REST API endpoint to start a workflow"""
        try:
            data = await request.json()

            workflow_type = data.get('workflow_type')
            if not workflow_type:
                return web.json_response({'error': 'workflow_type is required'}, status=400)

            if workflow_type not in self.workflow_templates:
                return web.json_response({'error': f'Unknown workflow type: {workflow_type}'}, status=400)

            context = data.get('context', {})
            initiator = data.get('initiator', 'API')

            # Start the workflow
            workflow_id = await self.orchestrator.start_workflow(
                workflow_type, context, initiator
            )

            return web.json_response({
                'success': True,
                'workflow_id': workflow_id,
                'workflow_type': workflow_type,
                'status': 'started',
                'message': f'Workflow {workflow_id} started successfully'
            })

        except Exception as e:
            self.logger.error(f"API start workflow error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_list_workflows(self, request: web.Request) -> web.Response:
        """REST API endpoint to list active workflows"""
        try:
            status_filter = request.query.get('status', 'all')

            workflows = []
            for workflow_id, workflow in self.orchestrator.active_workflows.items():
                if status_filter == 'all' or workflow['status'] == status_filter:
                    workflows.append({
                        'workflow_id': workflow_id,
                        'type': workflow['type'],
                        'status': workflow['status'],
                        'started_at': workflow['started_at'],
                        'completed_at': workflow.get('completed_at'),
                        'initiator': workflow.get('initiator', 'system')
                    })

            return web.json_response({
                'workflows': workflows,
                'total': len(workflows),
                'filter': status_filter
            })

        except Exception as e:
            self.logger.error(f"API list workflows error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_get_workflow(self, request: web.Request) -> web.Response:
        """REST API endpoint to get workflow details"""
        try:
            workflow_id = request.match_info['workflow_id']

            workflow = await self.orchestrator.get_workflow_status(workflow_id)

            if not workflow:
                return web.json_response({'error': 'Workflow not found'}, status=404)

            # Summarize steps for response
            steps_summary = {}
            for step_id, step in workflow.get('steps', {}).items():
                steps_summary[step_id] = {
                    'status': step['status'],
                    'engine': step['engine'],
                    'action': step['action'],
                    'started_at': step.get('started_at'),
                    'completed_at': step.get('completed_at'),
                    'error': step.get('error')
                }

            return web.json_response({
                'workflow_id': workflow_id,
                'type': workflow['type'],
                'status': workflow['status'],
                'started_at': workflow['started_at'],
                'completed_at': workflow.get('completed_at'),
                'current_step': workflow.get('current_step'),
                'steps': steps_summary,
                'errors': workflow.get('errors', [])
            })

        except Exception as e:
            self.logger.error(f"API get workflow error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_cancel_workflow(self, request: web.Request) -> web.Response:
        """REST API endpoint to cancel a workflow"""
        try:
            workflow_id = request.match_info['workflow_id']

            success = await self.orchestrator.cancel_workflow(workflow_id)

            if success:
                return web.json_response({
                    'success': True,
                    'workflow_id': workflow_id,
                    'message': f'Workflow {workflow_id} cancelled successfully'
                })
            else:
                return web.json_response({
                    'success': False,
                    'error': 'Failed to cancel workflow (not found or not running)'
                }, status=400)

        except Exception as e:
            self.logger.error(f"API cancel workflow error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_list_workflow_templates(self, request: web.Request) -> web.Response:
        """REST API endpoint to list available workflow templates"""
        try:
            category = request.query.get('category')
            severity = request.query.get('severity')

            templates = []
            for key, template in self.workflow_templates.items():
                # Apply filters
                if category and template.get('category') != category:
                    continue
                if severity and template.get('severity') != severity:
                    continue

                templates.append({
                    'type': key,
                    'name': template['name'],
                    'description': template['description'],
                    'category': template.get('category', 'uncategorized'),
                    'severity': template.get('severity', 'medium'),
                    'timeout': template.get('timeout', 3600),
                    'steps_count': len(template.get('steps', []))
                })

            # Get unique categories and severities for filters
            all_categories = set()
            all_severities = set()
            for template in self.workflow_templates.values():
                all_categories.add(template.get('category', 'uncategorized'))
                all_severities.add(template.get('severity', 'medium'))

            return web.json_response({
                'templates': templates,
                'total': len(templates),
                'filters': {
                    'categories': list(all_categories),
                    'severities': list(all_severities)
                }
            })

        except Exception as e:
            self.logger.error(f"API list workflow templates error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_workflow_statistics(self, request: web.Request) -> web.Response:
        """REST API endpoint for workflow statistics"""
        try:
            stats = self.orchestrator.get_statistics()

            # Add delivery engine workflow stats
            stats['delivery_stats'] = {
                'workflows_executed': self.delivery_stats['workflows_executed'],
                'workflows_completed': self.delivery_stats['workflows_completed'],
                'workflows_failed': self.delivery_stats['workflows_failed']
            }

            # Calculate success rate
            total_executed = stats['stats'].get('workflows_started', 0)
            total_completed = stats['stats'].get('workflows_completed', 0)
            success_rate = (total_completed / total_executed * 100) if total_executed > 0 else 0

            stats['success_rate'] = f"{success_rate:.1f}%"

            return web.json_response(stats)

        except Exception as e:
            self.logger.error(f"API workflow statistics error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    # Pattern Management API Endpoints
    async def _api_list_patterns(self, request: web.Request) -> web.Response:
        """List all patterns from the pattern library"""
        try:
            # Query patterns from database
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT pattern_id, pattern_data, pattern_type, confidence_score,
                           created_at, last_used, match_count
                    FROM pattern_library
                    ORDER BY created_at DESC LIMIT 100
                """)

                patterns = []
                for row in rows:
                    patterns.append({
                        'id': row['pattern_id'],
                        'pattern_data': row['pattern_data'],
                        'pattern_type': row['pattern_type'],
                        'confidence_score': row['confidence_score'],
                        'created_at': row['created_at'].isoformat() if row['created_at'] else None,
                        'last_used': row['last_used'].isoformat() if row['last_used'] else None,
                        'match_count': row['match_count']
                    })

                return web.json_response({'patterns': patterns})

        except Exception as e:
            self.logger.error(f"API list patterns error: {e}")
            # Return empty list if database not accessible
            return web.json_response({'patterns': []})

    async def _api_get_pattern(self, request: web.Request) -> web.Response:
        """Get specific pattern details"""
        try:
            pattern_id = request.match_info['pattern_id']

            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT * FROM pattern_library WHERE pattern_id = $1
                """, pattern_id)

                if row:
                    pattern = {
                        'id': row['pattern_id'],
                        'pattern_data': row['pattern_data'],
                        'pattern_type': row['pattern_type'],
                        'confidence_score': row['confidence_score'],
                        'created_at': row['created_at'].isoformat() if row['created_at'] else None
                    }
                    return web.json_response(pattern)
                else:
                    return web.json_response({'error': 'Pattern not found'}, status=404)

        except Exception as e:
            self.logger.error(f"API get pattern error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_test_pattern(self, request: web.Request) -> web.Response:
        """Test a pattern against sample data"""
        try:
            data = await request.json()
            pattern = data.get('pattern')
            test_data = data.get('test_data')

            # Simple pattern matching simulation
            import re
            try:
                regex = re.compile(pattern)
                matches = regex.findall(test_data)
                return web.json_response({
                    'success': True,
                    'matches': matches,
                    'match_count': len(matches)
                })
            except re.error as e:
                return web.json_response({
                    'success': False,
                    'error': f'Invalid pattern: {str(e)}'
                }, status=400)

        except Exception as e:
            self.logger.error(f"API test pattern error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_pattern_performance(self, request: web.Request) -> web.Response:
        """Get pattern performance metrics"""
        try:
            pattern_id = request.match_info['pattern_id']

            # Return mock performance data for now
            return web.json_response({
                'pattern_id': pattern_id,
                'match_count': 1234,
                'avg_execution_time': 0.45,
                'false_positive_rate': 0.02,
                'last_matched': datetime.utcnow().isoformat()
            })

        except Exception as e:
            self.logger.error(f"API pattern performance error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    # Rule Management API Endpoints
    async def _api_list_rules(self, request: web.Request) -> web.Response:
        """List detection rules with optional quality_label filter"""
        try:
            # Get query parameters
            quality_label = request.rel_url.query.get('quality_label')  # Optional: high/medium/low
            source = request.rel_url.query.get('source')  # Optional: otx, threatfox, etc.
            limit = int(request.rel_url.query.get('limit', 100))  # Default 100 rules

            self.logger.info(f"API list_rules called with quality_label={quality_label}, source={source}, limit={limit}")

            # Build query dynamically
            where_conditions = []
            where_values = []

            if quality_label:
                where_conditions.append("quality_label = %s")
                where_values.append(quality_label)

            if source:
                where_conditions.append("rule_data->>'source' = %s")
                where_values.append(source)

            where_clause = f"WHERE {' AND '.join(where_conditions)}" if where_conditions else ""

            # Query rules from database using asyncpg
            param_count = 1
            query_params = []
            query_placeholders = []

            # Build parameterized query with $1, $2, etc.
            for val in where_values:
                query_params.append(val)
                query_placeholders.append(f"${param_count}")
                param_count += 1

            # Replace %s with proper placeholders
            if where_conditions:
                where_clause_parts = []
                placeholder_idx = 0
                for condition in where_conditions:
                    where_clause_parts.append(condition.replace('%s', query_placeholders[placeholder_idx]))
                    placeholder_idx += 1
                where_clause = f"WHERE {' AND '.join(where_clause_parts)}"

            query_params.append(limit)
            limit_placeholder = f"${param_count}"

            query = f"""
                SELECT rule_id, rule_data->>'name' as rule_name,
                       rule_data->>'source' as source,
                       rule_data->>'ioc_type' as ioc_type,
                       rule_data->>'ioc_value' as ioc_value,
                       (rule_data->>'quality_score')::float as quality_score,
                       quality_label,
                       custom_tags,
                       deployed_to_elastic, deployed_to_splunk,
                       deployed_to_sentinel, deployed_to_qradar,
                       created_at, updated_at
                FROM detection_rules
                {where_clause}
                ORDER BY created_at DESC
                LIMIT {limit_placeholder}
            """
            self.logger.info(f"Executing query: {query}")
            self.logger.info(f"With values: {query_params}")

            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch(query, *query_params)
                self.logger.info(f"Query returned {len(rows)} rows")

                rules = []
                for row in rows:
                    rules.append({
                        'rule_id': str(row['rule_id']),
                        'rule_name': row['rule_name'],
                        'source': row['source'],
                        'ioc_type': row['ioc_type'],
                        'ioc_value': row['ioc_value'],
                        'quality_score': row['quality_score'],
                        'quality_label': row['quality_label'],
                        'custom_tags': row['custom_tags'],
                        'deployed_to': {
                            'elastic': row['deployed_to_elastic'],
                            'splunk': row['deployed_to_splunk'],
                            'sentinel': row['deployed_to_sentinel'],
                            'qradar': row['deployed_to_qradar']
                        },
                        'created_at': row['created_at'].isoformat() if row['created_at'] else None,
                        'updated_at': row['updated_at'].isoformat() if row['updated_at'] else None
                    })

                return web.json_response({
                    'rules': rules,
                    'total': len(rules),
                    'filters': {
                        'quality_label': quality_label,
                        'source': source
                    }
                })

        except Exception as e:
            import traceback
            self.logger.error(f"API list rules error: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            # Return error details for debugging
            return web.json_response({
                'error': str(e),
                'traceback': traceback.format_exc(),
                'rules': [],
                'total': 0
            }, status=500)

    async def _api_get_rule(self, request: web.Request) -> web.Response:
        """Get specific rule details"""
        try:
            rule_id = request.match_info['rule_id']

            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT * FROM detection_rules WHERE rule_id = $1
                """, rule_id)

                if row:
                    rule = {
                        'id': row['rule_id'],
                        'name': row.get('name') or row.get('rule_data', {}).get('name'),
                        'platform': row.get('platform'),
                        'query': row.get('query'),
                        'severity': row.get('severity'),
                        'created_at': row['created_at'].isoformat() if row.get('created_at') else None
                    }
                    return web.json_response(rule)
                else:
                    return web.json_response({'error': 'Rule not found'}, status=404)

        except Exception as e:
            self.logger.error(f"API get rule error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_test_rule(self, request: web.Request) -> web.Response:
        """Test a detection rule"""
        try:
            data = await request.json()
            rule_query = data.get('query')
            test_log = data.get('test_log')

            # Simulate rule testing
            return web.json_response({
                'success': True,
                'matched': True,
                'execution_time': 0.123,
                'details': 'Rule matched successfully'
            })

        except Exception as e:
            self.logger.error(f"API test rule error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_delete_rule(self, request: web.Request) -> web.Response:
        """Delete a single detection rule"""
        try:
            rule_id = request.match_info['rule_id']

            # Check if rule exists and get deployment status
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT deployed_to_elastic, deployed_to_splunk,
                           deployed_to_sentinel, deployed_to_qradar,
                           elastic_rule_id, splunk_rule_id,
                           sentinel_rule_id, qradar_rule_id
                    FROM detection_rules
                    WHERE rule_id = $1
                """, rule_id)

                if not row:
                    return web.json_response({'error': 'Rule not found'}, status=404)

                deployed_elastic = row['deployed_to_elastic']
                deployed_splunk = row['deployed_to_splunk']
                deployed_sentinel = row['deployed_to_sentinel']
                deployed_qradar = row['deployed_to_qradar']
                elastic_rule_id = row['elastic_rule_id']
                splunk_rule_id = row['splunk_rule_id']
                sentinel_rule_id = row['sentinel_rule_id']
                qradar_rule_id = row['qradar_rule_id']

            deleted_from_siems = []

            # Delete from Elastic if deployed
            if deployed_elastic and elastic_rule_id:
                try:
                    # Publish delete request to Ingestion Engine
                    self.publish_message('ingestion.rule.delete_from_siem', {
                        'siem': 'elastic',
                        'rule_id': elastic_rule_id
                    })
                    deleted_from_siems.append('elastic')
                except Exception as e:
                    self.logger.error(f"Failed to delete from Elastic: {e}")

            # Delete from Splunk if deployed
            if deployed_splunk and splunk_rule_id:
                try:
                    self.publish_message('ingestion.rule.delete_from_siem', {
                        'siem': 'splunk',
                        'rule_id': splunk_rule_id
                    })
                    deleted_from_siems.append('splunk')
                except Exception as e:
                    self.logger.error(f"Failed to delete from Splunk: {e}")

            # Delete from Sentinel if deployed
            if deployed_sentinel and sentinel_rule_id:
                try:
                    self.publish_message('ingestion.rule.delete_from_siem', {
                        'siem': 'sentinel',
                        'rule_id': sentinel_rule_id
                    })
                    deleted_from_siems.append('sentinel')
                except Exception as e:
                    self.logger.error(f"Failed to delete from Sentinel: {e}")

            # Delete from QRadar if deployed
            if deployed_qradar and qradar_rule_id:
                try:
                    self.publish_message('ingestion.rule.delete_from_siem', {
                        'siem': 'qradar',
                        'rule_id': qradar_rule_id
                    })
                    deleted_from_siems.append('qradar')
                except Exception as e:
                    self.logger.error(f"Failed to delete from QRadar: {e}")

            # Delete from database (cascade to related tables)
            async with self.db_pool.acquire() as conn:
                # Delete test cases
                await conn.execute("DELETE FROM rule_test_cases WHERE rule_id = $1", rule_id)

                # Delete performance data
                await conn.execute("DELETE FROM rule_performance WHERE rule_id = $1", rule_id)

                # Delete the rule itself
                await conn.execute("DELETE FROM detection_rules WHERE rule_id = $1", rule_id)

            return web.json_response({
                'success': True,
                'rule_id': rule_id,
                'deleted_from_siems': deleted_from_siems,
                'message': f'Rule deleted from database and {len(deleted_from_siems)} SIEM(s)'
            })

        except Exception as e:
            self.logger.error(f"API delete rule error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_update_rule(self, request: web.Request) -> web.Response:
        """Update rule metadata (quality_label, tags, notes)"""
        try:
            rule_id = request.match_info['rule_id']
            data = await request.json()

            # Check if rule exists
            async with self.db_pool.acquire() as conn:
                exists = await conn.fetchval("SELECT rule_id FROM detection_rules WHERE rule_id = $1", rule_id)
                if not exists:
                    return web.json_response({'error': 'Rule not found'}, status=404)

                # Build UPDATE query dynamically based on provided fields
                update_fields = []
                update_values = []
                param_count = 1

                # Quality label override (high/medium/low)
                if 'quality_label' in data:
                    quality_label = data['quality_label']
                    if quality_label not in ['high', 'medium', 'low', None]:
                        return web.json_response({
                            'error': 'Invalid quality_label. Must be: high, medium, low, or null'
                        }, status=400)
                    update_fields.append(f"quality_label = ${param_count}")
                    update_values.append(quality_label)
                    param_count += 1

                # Custom tags (array of strings)
                if 'custom_tags' in data:
                    update_fields.append(f"custom_tags = ${param_count}")
                    update_values.append(data['custom_tags'])
                    param_count += 1

                # Notes (text field)
                if 'notes' in data:
                    update_fields.append(f"notes = ${param_count}")
                    update_values.append(data['notes'])
                    param_count += 1

                # Reviewed by + timestamp
                if 'reviewed_by' in data:
                    update_fields.append(f"reviewed_by = ${param_count}")
                    update_values.append(data['reviewed_by'])
                    param_count += 1
                    update_fields.append("reviewed_at = NOW()")

                if not update_fields:
                    return web.json_response({
                        'error': 'No valid fields to update'
                    }, status=400)

                # Execute update
                update_values.append(rule_id)  # For WHERE clause
                query = f"""
                    UPDATE detection_rules
                    SET {', '.join(update_fields)}, updated_at = NOW()
                    WHERE rule_id = ${param_count}
                    RETURNING rule_id, quality_label, custom_tags, notes, reviewed_by, reviewed_at
                """

                row = await conn.fetchrow(query, *update_values)

                return web.json_response({
                    'success': True,
                    'rule_id': str(row['rule_id']),
                    'quality_label': row['quality_label'],
                    'custom_tags': row['custom_tags'],
                    'notes': row['notes'],
                    'reviewed_by': row['reviewed_by'],
                    'reviewed_at': row['reviewed_at'].isoformat() if row['reviewed_at'] else None
                })

        except Exception as e:
            self.logger.error(f"API update rule error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_bulk_delete_rules(self, request: web.Request) -> web.Response:
        """Bulk delete rules based on filters with dry-run mode"""
        try:
            data = await request.json()

            # Extract filters
            quality_label = data.get('quality_label')  # 'low', 'medium', 'high'
            source = data.get('source')  # 'otx', 'threatfox', 'crowdstrike', etc.
            deployed = data.get('deployed')  # true/false - any SIEM deployment
            dry_run = data.get('dry_run', True)  # Default to dry-run for safety

            # Build WHERE clause dynamically
            where_conditions = []
            where_values = []

            if quality_label:
                where_conditions.append("quality_label = %s")
                where_values.append(quality_label)

            if source:
                where_conditions.append("rule_data->>'source' = %s")
                where_values.append(source)

            if deployed is not None:
                if deployed:
                    where_conditions.append("""(
                        deployed_to_elastic = true OR
                        deployed_to_splunk = true OR
                        deployed_to_sentinel = true OR
                        deployed_to_qradar = true
                    )""")
                else:
                    where_conditions.append("""(
                        deployed_to_elastic = false AND
                        deployed_to_splunk = false AND
                        deployed_to_sentinel = false AND
                        deployed_to_qradar = false
                    )""")

            if not where_conditions:
                return web.json_response({
                    'error': 'At least one filter required (quality_label, source, or deployed)'
                }, status=400)

            # Build parameterized query with $1, $2, etc.
            param_count = 1
            query_params = []
            query_placeholders = []

            for val in where_values:
                query_params.append(val)
                query_placeholders.append(f"${param_count}")
                param_count += 1

            # Replace %s with proper placeholders in where_conditions
            where_clause_parts = []
            placeholder_idx = 0
            for condition in where_conditions:
                # Replace %s with placeholders, handling conditions without placeholders
                if '%s' in condition:
                    where_clause_parts.append(condition.replace('%s', query_placeholders[placeholder_idx]))
                    placeholder_idx += 1
                else:
                    where_clause_parts.append(condition)

            where_clause = " AND ".join(where_clause_parts)

            # Query matching rules
            async with self.db_pool.acquire() as conn:
                query = f"""
                    SELECT rule_id, rule_data->>'name' as rule_name,
                           quality_label, rule_data->>'source' as source,
                           deployed_to_elastic, deployed_to_splunk,
                           deployed_to_sentinel, deployed_to_qradar
                    FROM detection_rules
                    WHERE {where_clause}
                    LIMIT 1000
                """
                rows = await conn.fetch(query, *query_params)

                matching_rules = []
                for row in rows:
                    matching_rules.append({
                        'rule_id': str(row['rule_id']),
                        'rule_name': row['rule_name'],
                        'quality_label': row['quality_label'],
                        'source': row['source'],
                        'deployed_to': {
                            'elastic': row['deployed_to_elastic'],
                            'splunk': row['deployed_to_splunk'],
                            'sentinel': row['deployed_to_sentinel'],
                            'qradar': row['deployed_to_qradar']
                        }
                    })

                # Dry-run mode: Return preview only
                if dry_run:
                    return web.json_response({
                        'dry_run': True,
                        'total_matches': len(matching_rules),
                        'sample_rules': matching_rules[:10],  # Show first 10
                        'message': f'Would delete {len(matching_rules)} rules. Set dry_run=false to execute.'
                    })

                # Execute deletion
                deleted_count = 0
                for rule in matching_rules:
                    rule_id = rule['rule_id']

                    # Delete test cases and performance data
                    await conn.execute("DELETE FROM rule_test_cases WHERE rule_id = $1", rule_id)
                    await conn.execute("DELETE FROM rule_performance WHERE rule_id = $1", rule_id)

                    # Delete the rule
                    await conn.execute("DELETE FROM detection_rules WHERE rule_id = $1", rule_id)
                    deleted_count += 1

            return web.json_response({
                'success': True,
                'deleted_count': deleted_count,
                'filters_used': {
                    'quality_label': quality_label,
                    'source': source,
                    'deployed': deployed
                },
                'message': f'Successfully deleted {deleted_count} rules'
            })

        except Exception as e:
            self.logger.error(f"API bulk delete rules error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    # Entity API Endpoints
    async def _api_list_entities(self, request: web.Request) -> web.Response:
        """List all entities"""
        try:
            # Query entities from database
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT entity_id, entity_type, entity_value, risk_score,
                           first_seen, last_seen
                    FROM entities
                    ORDER BY last_seen DESC LIMIT 100
                """)

                entities = []
                for row in rows:
                    entities.append({
                        'id': row['entity_id'],
                        'type': row['entity_type'],
                        'value': row['entity_value'],
                        'risk_score': row['risk_score'],
                        'first_seen': row['first_seen'].isoformat() if row['first_seen'] else None,
                        'last_seen': row['last_seen'].isoformat() if row['last_seen'] else None
                    })

                return web.json_response({'entities': entities})

        except Exception as e:
            self.logger.error(f"API list entities error: {e}")
            # Return empty list if database not accessible
            return web.json_response({'entities': []})

    async def _api_get_entity(self, request: web.Request) -> web.Response:
        """Get specific entity details"""
        try:
            entity_id = request.match_info['entity_id']

            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT * FROM entities WHERE entity_id = $1
                """, entity_id)

                if row:
                    entity = {
                        'id': row['entity_id'],
                        'type': row['entity_type'],
                        'value': row['entity_value'],
                        'risk_score': row['risk_score'],
                        'first_seen': row['first_seen'].isoformat() if row.get('first_seen') else None,
                        'last_seen': row['last_seen'].isoformat() if row.get('last_seen') else None
                    }
                    return web.json_response(entity)
                else:
                    return web.json_response({'error': 'Entity not found'}, status=404)

        except Exception as e:
            self.logger.error(f"API get entity error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_get_entity_enrichment(self, request: web.Request) -> web.Response:
        """Get entity enrichment data"""
        try:
            entity_id = request.match_info['entity_id']

            # Return mock enrichment data for now
            return web.json_response([
                {
                    'source': 'GeoIP',
                    'data': {'country': 'US', 'city': 'New York'},
                    'timestamp': datetime.utcnow().isoformat()
                },
                {
                    'source': 'Threat Intel',
                    'data': {'reputation': 'suspicious', 'score': 75},
                    'timestamp': datetime.utcnow().isoformat()
                }
            ])

        except Exception as e:
            self.logger.error(f"API entity enrichment error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_get_entity_relationships(self, request: web.Request) -> web.Response:
        """Get entity relationships"""
        try:
            entity_id = request.match_info['entity_id']

            # Query relationships from database
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT source_entity_id, target_entity_id, relationship_type,
                           confidence, created_at
                    FROM relationships
                    WHERE source_entity_id = $1 OR target_entity_id = $1
                    LIMIT 50
                """, entity_id)

                relationships = []
                entities = {}

                for row in rows:
                    relationships.append({
                        'source': row['source_entity_id'],
                        'target': row['target_entity_id'],
                        'type': row['relationship_type'],
                        'weight': row['confidence'],
                        'first_seen': row['created_at'].isoformat() if row['created_at'] else None
                    })

                    # Fetch related entity details
                    related_id = row['target_entity_id'] if row['source_entity_id'] == entity_id else row['source_entity_id']
                    if related_id not in entities:
                        entity_row = await conn.fetchrow("""
                            SELECT entity_id, entity_type, entity_value, risk_score
                            FROM entities WHERE entity_id = $1
                        """, related_id)
                        if entity_row:
                            entities[related_id] = {
                                'id': entity_row['entity_id'],
                                'type': entity_row['entity_type'],
                                'value': entity_row['entity_value'],
                                'risk_score': entity_row['risk_score']
                            }

                return web.json_response({
                    'relationships': relationships,
                    'entities': list(entities.values())
                })

        except Exception as e:
            self.logger.error(f"API entity relationships error: {e}")
            return web.json_response({'relationships': [], 'entities': []})

    async def _api_get_entity_timeline(self, request: web.Request) -> web.Response:
        """Get entity timeline"""
        try:
            entity_id = request.match_info['entity_id']

            # Return mock timeline data for now
            return web.json_response({
                'entity_id': entity_id,
                'events': [
                    {
                        'timestamp': datetime.utcnow().isoformat(),
                        'event_type': 'first_seen',
                        'description': 'Entity first observed'
                    },
                    {
                        'timestamp': (datetime.utcnow() - timedelta(hours=2)).isoformat(),
                        'event_type': 'suspicious_activity',
                        'description': 'Suspicious connection detected'
                    }
                ]
            })

        except Exception as e:
            self.logger.error(f"API entity timeline error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _api_get_entity_risk(self, request: web.Request) -> web.Response:
        """Get entity risk score"""
        try:
            entity_id = request.match_info['entity_id']

            # Calculate or retrieve risk score
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT risk_score FROM entities WHERE entity_id = $1
                """, entity_id)

                if row:
                    return web.json_response({
                        'entity_id': entity_id,
                        'risk_score': row['risk_score'] or 0,
                        'factors': [
                            {'factor': 'Recent alerts', 'weight': 0.3},
                            {'factor': 'Known malicious', 'weight': 0.5},
                            {'factor': 'Behavioral anomaly', 'weight': 0.2}
                        ]
                    })
                else:
                    return web.json_response({'error': 'Entity not found'}, status=404)

        except Exception as e:
            self.logger.error(f"API entity risk error: {e}")
            return web.json_response({'error': str(e)}, status=500)

if __name__ == "__main__":
    async def main():
        engine = DeliveryEngine()
        await engine.start()

    asyncio.run(main())