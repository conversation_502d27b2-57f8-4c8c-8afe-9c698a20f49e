# Pattern Validation Prompt Template
# Version 1.0 - Validate security patterns before crystallization

name: pattern_validation
version: "1.0"
task_type: pattern_validation
active: true

variables:
  - pattern_name
  - pattern_description
  - pattern_logic
  - occurrences
  - false_positive_rate

metadata:
  author: SIEMLess Intelligence Engine
  created: 2025-09-30
  description: Validates security patterns for crystallization into pattern library
  response_format: json

template: |
  # Security Pattern Validation Task

  ## Pattern to Validate
  **Name**: {pattern_name}
  **Description**: {pattern_description}
  **Occurrences**: {occurrences}
  **Current FP Rate**: {false_positive_rate}%

  **Pattern Logic**:
  ```
  {pattern_logic}
  ```

  ## Validation Requirements

  ### 1. Pattern Accuracy
  Evaluate the pattern's accuracy:
  - Are the detection conditions sufficient?
  - Are there any logical errors?
  - Does it match the intended behavior?
  - Is the scope too broad or too narrow?

  ### 2. False Positive Analysis
  Identify potential false positive sources:
  - Legitimate activities that match this pattern
  - Environmental factors (staging, dev, prod)
  - Scheduled tasks or automation
  - Common administrative operations

  ### 3. Evasion Resistance
  Evaluate how easily this pattern can be evaded:
  - Simple parameter changes
  - Alternative execution methods
  - Obfuscation techniques
  - Living-off-the-land alternatives

  ### 4. Performance Impact
  Assess computational cost:
  - Query complexity
  - Index requirements
  - CPU/memory usage
  - Scalability concerns

  ### 5. Improvement Recommendations
  Suggest specific improvements:
  - Additional conditions to reduce FPs
  - Alternative detection methods
  - Performance optimizations
  - Missing edge cases

  ## Response Format

  ```json
  {
    "validation_result": "approved|needs_improvement|rejected",
    "confidence": 0.85,
    "accuracy_assessment": {
      "score": 0.80,
      "strengths": ["Clear detection logic", "Low false positives"],
      "weaknesses": ["Missing edge cases", "Can be evaded easily"]
    },
    "false_positive_analysis": {
      "estimated_fp_rate": 0.05,
      "fp_sources": [
        {
          "source": "Windows Update process",
          "likelihood": "high",
          "mitigation": "Add process.parent.name filter"
        }
      ]
    },
    "evasion_resistance": {
      "score": 0.70,
      "evasion_techniques": [
        {
          "technique": "Rename executable",
          "likelihood": "high",
          "mitigation": "Use file hash or behavior"
        }
      ]
    },
    "performance_impact": {
      "estimated_load": "low|medium|high",
      "concerns": ["Regex matching may be slow on large datasets"],
      "optimizations": ["Index the process.name field"]
    },
    "improvements": [
      {
        "category": "accuracy",
        "suggestion": "Add parent process check",
        "expected_impact": "Reduce FPs by 30%"
      }
    ],
    "crystallization_ready": true,
    "recommended_actions": [
      "Apply suggested improvements",
      "Test in staging environment",
      "Monitor FP rate for 7 days"
    ]
  }
  ```

  Be **critical and thorough**. Crystallized patterns are permanent and affect all future detections.
