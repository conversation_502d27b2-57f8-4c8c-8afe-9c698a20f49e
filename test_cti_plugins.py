#!/usr/bin/env python3
"""
Test CTI Plugin System
Quick validation that plugins are fetching real data
"""
import requests
import json
import time

INGESTION_URL = "http://localhost:8003"

def test_cti_connectors():
    """Test CTI connectors endpoint"""
    print("\n=== Testing CTI Connectors ===")
    response = requests.get(f"{INGESTION_URL}/cti/connectors")
    print(f"Status: {response.status_code}")
    data = response.json()
    print(f"Plugins registered: {data['plugins']}")
    print(f"Source types: {json.dumps(data['source_types'], indent=2)}")
    return data

def test_cti_status():
    """Test CTI status endpoint"""
    print("\n=== Testing CTI Status ===")
    response = requests.get(f"{INGESTION_URL}/cti/status")
    print(f"Status: {response.status_code}")
    data = response.json()
    print(f"Plugin count: {data['plugin_count']}")
    for plugin, health in data['health'].items():
        status = "healthy" if health['healthy'] else "UNHEALTHY"
        print(f"  {plugin}: {status} (type: {health['type']})")
    return data

def test_manual_update_otx():
    """Test manual CTI update for OTX"""
    print("\n=== Testing Manual CTI Update (OTX) ===")
    payload = {
        "source": "otx",
        "since_days": 1,
        "limit": 50
    }

    print(f"Requesting indicators from last {payload['since_days']} day(s)...")
    print(f"Limit: {payload['limit']} indicators")

    response = requests.post(
        f"{INGESTION_URL}/cti/manual_update",
        json=payload
    )

    print(f"Status: {response.status_code}")

    if response.status_code == 200:
        data = response.json()
        print(f"✓ Success!")
        print(f"  Source: {data.get('source', 'N/A')}")
        print(f"  Indicators fetched: {data.get('indicators_fetched', 0)}")
        return data
    else:
        print(f"✗ Failed: {response.text}")
        return None

def test_manual_update_all():
    """Test manual CTI update for all sources"""
    print("\n=== Testing Manual CTI Update (ALL SOURCES) ===")
    payload = {
        "source": "all",
        "since_days": 1,
        "limit": 100
    }

    print(f"Requesting indicators from ALL sources...")
    print(f"Time range: last {payload['since_days']} day(s)")
    print(f"Limit per source: {payload['limit']} indicators")

    response = requests.post(
        f"{INGESTION_URL}/cti/manual_update",
        json=payload
    )

    print(f"Status: {response.status_code}")

    if response.status_code == 200:
        data = response.json()
        print(f"✓ Success!")
        print(f"  Total indicators fetched: {data.get('indicators_fetched', 0)}")
        print(f"  Sources used: {data.get('sources', [])}")
        return data
    else:
        print(f"✗ Failed: {response.text}")
        return None

if __name__ == "__main__":
    print("=" * 60)
    print("CTI Plugin System Test")
    print("=" * 60)

    # Test 1: Check connectors
    connectors = test_cti_connectors()

    # Test 2: Check status
    status = test_cti_status()

    # Test 3: Manual update from OTX only
    otx_result = test_manual_update_otx()

    # Test 4: Manual update from all sources
    # all_result = test_manual_update_all()

    print("\n" + "=" * 60)
    print("Test Complete!")
    print("=" * 60)
