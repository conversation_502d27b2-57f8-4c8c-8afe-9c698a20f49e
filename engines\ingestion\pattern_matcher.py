"""
Pattern Matcher for SIEMLess v2.0
Matches logs against crystallized patterns (FREE, deterministic, instant)
"""

import json
import re
from typing import Dict, Any, Optional, List
from datetime import datetime

class PatternMatcher:
    """Match logs against crystallized patterns from the pattern library"""

    def __init__(self, db_connection, redis_client, logger):
        self.db = db_connection
        self.redis = redis_client
        self.logger = logger
        self.patterns = {}
        self.load_patterns()

    def load_patterns(self):
        """Load crystallized patterns from database"""
        try:
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT pattern_id, pattern_type, pattern_data
                FROM pattern_library
                WHERE is_active = true
                ORDER BY usage_count DESC
            """)

            rows = cursor.fetchall()
            self.logger.debug(f"SQL returned {len(rows)} rows")
            for i, row in enumerate(rows):
                self.logger.debug(f"Row {i}: {row}")
                # Handle both tuple and dict formats
                if isinstance(row, dict):
                    pattern_id = row['pattern_id']
                    pattern_type = row['pattern_type']
                    pattern_data = row['pattern_data']
                else:
                    pattern_id = row[0]
                    pattern_type = row[1]
                    pattern_data = row[2]
                self.logger.debug(f"Extracted - ID: {pattern_id}, Type: {pattern_type}, Data: {pattern_data}")
                try:
                    compiled_regex = self._compile_pattern(pattern_data)
                    if compiled_regex:
                        self.patterns[pattern_id] = {
                            'type': pattern_type,
                            'data': pattern_data,
                            'regex': compiled_regex
                        }
                        self.logger.debug(f"Successfully loaded pattern: {pattern_id}")
                    else:
                        self.logger.warning(f"Failed to compile pattern: {pattern_id}")
                except Exception as e:
                    self.logger.error(f"Error loading pattern {pattern_id}: {e}")

            self.logger.info(f"Loaded {len(self.patterns)} crystallized patterns")

        except Exception as e:
            self.logger.error(f"Failed to load patterns: {e}")

    def _compile_pattern(self, pattern_data: Dict) -> Optional[re.Pattern]:
        """Compile pattern data into regex for fast matching"""
        try:
            self.logger.debug(f"Compiling pattern_data: {pattern_data} (type: {type(pattern_data)})")
            if 'regex' in pattern_data:
                regex_pattern = pattern_data['regex']
                self.logger.debug(f"Found regex: {regex_pattern}")
                return re.compile(regex_pattern, re.IGNORECASE)
            elif 'keywords' in pattern_data:
                # Convert keywords to regex
                keywords = '|'.join(re.escape(k) for k in pattern_data['keywords'])
                return re.compile(f'({keywords})', re.IGNORECASE)
            else:
                self.logger.warning(f"No regex or keywords found in pattern_data: {pattern_data}")
                return None
        except Exception as e:
            self.logger.error(f"Pattern compilation failed: {e}")
            return None

    def match(self, log: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Match log against patterns - this is the HOT PATH
        Must be FAST - this runs on 99.9% of logs
        """
        log_text = json.dumps(log).lower()

        # Try each pattern (ordered by usage frequency for speed)
        for pattern_id, pattern in self.patterns.items():
            if pattern['regex'] and pattern['regex'].search(log_text):
                # Pattern matched! Update usage stats
                self._update_pattern_usage(pattern_id)

                # Extract entities based on pattern
                entities = self._extract_entities(log, pattern['data'])

                return {
                    'pattern_id': pattern_id,
                    'pattern_type': pattern['type'],
                    'entities': entities,
                    'confidence': 1.0,  # Crystallized patterns have 100% confidence
                    'processing_time': 0.001  # Near instant
                }

        # No pattern matched - this is rare (0.1% of logs)
        return None

    def _extract_entities(self, log: Dict, pattern_data: Dict) -> List[Dict]:
        """Extract entities using pattern's extraction rules"""
        entities = []

        # Get extraction rules from pattern
        extractors = pattern_data.get('entity_extractors', {})

        # Common entity extractions
        if 'ip_fields' in extractors:
            for field in extractors['ip_fields']:
                if value := self._get_nested_value(log, field):
                    entities.append({
                        'type': 'ip_address',
                        'value': value,
                        'field': field
                    })

        if 'user_fields' in extractors:
            for field in extractors['user_fields']:
                if value := self._get_nested_value(log, field):
                    entities.append({
                        'type': 'username',
                        'value': value,
                        'field': field
                    })

        if 'host_fields' in extractors:
            for field in extractors['host_fields']:
                if value := self._get_nested_value(log, field):
                    entities.append({
                        'type': 'hostname',
                        'value': value,
                        'field': field
                    })

        return entities

    def _get_nested_value(self, data: Dict, path: str) -> Optional[Any]:
        """Get value from nested dict using dot notation"""
        keys = path.split('.')
        value = data

        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None

        return value

    def _update_pattern_usage(self, pattern_id: str):
        """Update pattern usage statistics for optimization"""
        try:
            # Update in database (async, don't block)
            update_query = """
                UPDATE pattern_library
                SET usage_count = usage_count + 1,
                    last_matched = NOW()
                WHERE pattern_id = %s
            """
            cursor = self.db.cursor()
            cursor.execute(update_query, (pattern_id,))
            self.db.commit()

        except Exception as e:
            # Don't let stats updates break processing
            pass

    def add_pattern(self, pattern_data: Dict) -> str:
        """Add new crystallized pattern from Intelligence Engine"""
        pattern_id = f"pattern_{int(datetime.now().timestamp())}"

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO pattern_library
                (pattern_id, pattern_type, pattern_data, source, created_at)
                VALUES (%s, %s, %s, %s, NOW())
            """, (
                pattern_id,
                pattern_data.get('type', 'unknown'),
                json.dumps(pattern_data),
                'intelligence_engine'
            ))
            self.db.commit()

            # Add to in-memory cache
            self.patterns[pattern_id] = {
                'type': pattern_data.get('type'),
                'data': pattern_data,
                'regex': self._compile_pattern(pattern_data)
            }

            self.logger.info(f"Added new crystallized pattern: {pattern_id}")
            return pattern_id

        except Exception as e:
            self.logger.error(f"Failed to add pattern: {e}")
            return None

    def get_stats(self) -> Dict[str, Any]:
        """Get pattern matching statistics"""
        return {
            'total_patterns': len(self.patterns),
            'pattern_types': self._count_pattern_types(),
            'cache_size': len(self.patterns),
            'last_reload': datetime.now().isoformat()
        }

    def _count_pattern_types(self) -> Dict[str, int]:
        """Count patterns by type"""
        types = {}
        for pattern in self.patterns.values():
            pattern_type = pattern['type']
            types[pattern_type] = types.get(pattern_type, 0) + 1
        return types