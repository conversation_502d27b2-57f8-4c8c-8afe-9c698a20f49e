# Wazuh SIEM Configuration
# Query Language: OpenSearch Query DSL / Lucene (via OpenSearch backend)

platform:
  name: wazuh
  display_name: Wazuh
  query_language: opensearch-dsl
  description: Open source SIEM with OpenSearch backend for log analysis
  vendor: Wazuh, Inc.
  version: "1.0"
  active: true

# Field mappings: generic_field -> Wazuh field
field_mappings:
  source_ip: data.srcip
  destination_ip: data.dstip
  username: data.win.eventdata.targetUserName
  process_name: data.win.eventdata.image
  file_hash: data.win.eventdata.hashes
  event_id: data.win.system.eventID
  hostname: agent.name
  port: data.dstport
  source_port: data.srcport
  destination_port: data.dstport
  domain: data.win.eventdata.targetDomainName
  url: data.url
  file_name: data.win.eventdata.targetFilename
  file_path: syscheck.path
  registry_path: data.win.eventdata.targetObject
  command_line: data.win.eventdata.commandLine
  parent_process: data.win.eventdata.parentImage
  network_protocol: data.protocol
  http_method: data.http.request.method
  user_agent: data.http.userAgent
  email_sender: data.email.from
  email_recipient: data.email.to
  dns_query: data.aws.requestParameters.domainName
  service_name: data.win.eventdata.serviceName
  account_name: data.win.eventdata.subjectUserName
  process_id: data.win.eventdata.processId
  parent_process_id: data.win.eventdata.parentProcessId
  rule_id: rule.id
  rule_description: rule.description
  rule_level: rule.level
  rule_groups: rule.groups
  decoder_name: decoder.name
  location: location

# Operator mappings: generic_operator -> OpenSearch DSL operator
operator_mappings:
  equals: match
  not_equals: must_not
  contains: wildcard
  not_contains: must_not_wildcard
  regex: regexp
  greater_than: gt
  less_than: lt
  greater_equal: gte
  less_equal: lte
  in_list: terms
  not_in_list: must_not_terms
  exists: exists
  not_exists: must_not_exists
  range: range

# Time field for temporal queries
time_field: timestamp

# Query syntax specifics
syntax:
  comment: "#"
  string_quote: "\""
  escape_char: "\\"
  wildcard: "*"
  field_separator: "."
  logical_and: AND
  logical_or: OR
  logical_not: NOT
  query_dsl_format: json
  lucene_syntax_supported: true
  case_sensitive: false

# Platform-specific features
metadata:
  supports_regex: true
  supports_wildcards: true
  supports_aggregations: true
  supports_scripting: true
  backend_engine: opensearch
  max_result_window: 10000
  default_time_range: "24h"

  # Wazuh rule levels (severity)
  rule_levels:
    0: Ignored
    1: Ignored
    2: Ignored
    3: Low
    4: Low
    5: Low
    6: Low
    7: Medium
    8: Medium
    9: Medium
    10: Medium
    11: Medium
    12: High
    13: High
    14: High
    15: Critical

  # Wazuh modules
  modules:
    - Log collector
    - File integrity monitoring (FIM)
    - Security Configuration Assessment (SCA)
    - System inventory
    - Vulnerability detection
    - Malware detection
    - Log data analysis
    - Intrusion detection
    - Policy monitoring
    - Active response
    - Container security
    - Cloud security (AWS, Azure, GCP)
    - Office 365 monitoring
    - Google Workspace monitoring

  # Common index patterns
  index_patterns:
    - wazuh-alerts-*
    - wazuh-archives-*
    - wazuh-monitoring-*
    - wazuh-statistics-*

  # OpenSearch DSL query template
  rule_template: |
    {
      "query": {
        "bool": {
          "must": [
            {"match": {"{field}": "{value}"}},
            {"range": {"timestamp": {"gte": "now-{time_range}"}}}
          ]
        }
      }
    }

  # Lucene query template (alternative)
  lucene_template: |
    {field}:"{value}" AND timestamp:[now-{time_range} TO now]

  # Wazuh decoders (log parsing)
  decoder_types:
    - syslog
    - json
    - windows-eventchannel
    - eventlog
    - audit
    - apache
    - nginx
    - mysql
    - postgresql
    - firewall
    - cisco
    - juniper
    - paloalto
    - fortinet
    - checkpoint
    - sophos

  # Wazuh rule groups
  rule_groups:
    - authentication_failed
    - authentication_success
    - syslog
    - windows
    - linux
    - web
    - attack
    - exploit
    - malware
    - vulnerability-detector
    - sca
    - syscheck
    - rootcheck
    - docker
    - osquery
    - virustotal
    - aws
    - azure
    - gcp
    - office365
    - github
    - gdpr
    - gpg13
    - hipaa
    - nist_800_53
    - pci_dss
    - tsc

  # Active response
  supports_active_response: true
  active_response_types:
    - firewall-drop  # Block IP
    - restart-wazuh  # Restart agent
    - disable-account  # Disable user
    - host-deny  # Add to hosts.deny
    - firewalld-drop  # Drop via firewalld
    - win_route-null  # Windows null route
    - pf  # BSD packet filter
    - npf  # Windows filtering platform
    - custom  # Custom scripts

  # File integrity monitoring
  fim_capabilities:
    - Real-time monitoring
    - Who-data (who made changes)
    - Report changes
    - Recursion
    - Wildcards
    - Windows registry monitoring
    - Check permissions
    - Check ownership
    - Check MD5/SHA1/SHA256

  # Security Configuration Assessment
  sca_policies:
    - CIS benchmarks
    - PCI DSS
    - NIST 800-53
    - GDPR
    - HIPAA
    - TSC
    - Custom policies

  # Vulnerability detection
  vulnerability_feeds:
    - NVD (National Vulnerability Database)
    - Red Hat OVAL
    - Debian OVAL
    - Ubuntu OVAL
    - Arch Linux
    - Amazon Linux
    - Windows updates

  # Integration capabilities
  integrations:
    - VirusTotal
    - Slack
    - PagerDuty
    - Shuffle
    - TheHive
    - MISP
    - Jira
    - Email
    - Custom integrations

  # MITRE ATT&CK mapping
  supports_mitre: true
  mitre_enabled: true

  # Compliance frameworks
  compliance_frameworks:
    - PCI DSS
    - GDPR
    - NIST 800-53
    - HIPAA
    - GPG13
    - TSC

  # Multi-tenancy
  supports_multi_tenancy: true
  agent_groups_supported: true
