"""
Data Source Integrations for Ingestion Engine
Real API implementations for Elasticsearch, CrowdStrike, and other sources
"""

import aiohttp
import asyncio
import json
import base64
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging


class ElasticsearchSource:
    """Elasticsearch/Elastic Cloud data source integration"""

    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.cloud_id = None
        self.api_key = None
        self.client = None
        self.last_query_time = datetime.utcnow() - timedelta(minutes=5)

    async def configure(self, config: Dict[str, Any]):
        """Configure Elasticsearch connection"""
        self.cloud_id = config.get('cloud_id')
        self.api_key = config.get('api_key')
        self.index_pattern = config.get('index_pattern', 'logs-*')
        self.batch_size = config.get('batch_size', 1000)

        if self.cloud_id and self.api_key:
            # Decode cloud ID to get the Elasticsearch URL
            self.es_url = self._decode_cloud_id(self.cloud_id)
            self.logger.info(f"Configured Elasticsearch source with Cloud ID")
            return True

        # Alternative: Direct URL connection
        self.es_url = config.get('url', 'http://localhost:9200')
        self.username = config.get('username')
        self.password = config.get('password')

        if self.es_url:
            self.logger.info(f"Configured Elasticsearch source with URL: {self.es_url}")
            return True

        return False

    def _decode_cloud_id(self, cloud_id: str) -> str:
        """Decode Elastic Cloud ID to get the Elasticsearch URL"""
        try:
            # Cloud ID format: deployment-name:base64(es_host$kibana_host)
            parts = cloud_id.split(':')
            if len(parts) == 2:
                decoded = base64.b64decode(parts[1]).decode('utf-8')
                es_host = decoded.split('$')[0]
                return f"https://{es_host}:443"
        except Exception as e:
            self.logger.error(f"Failed to decode cloud ID: {e}")
        return None

    async def fetch_logs(self) -> List[Dict[str, Any]]:
        """Fetch logs from Elasticsearch"""
        if not self.es_url:
            self.logger.warning("Elasticsearch not configured")
            return []

        try:
            # Build the search query
            query = {
                "size": self.batch_size,
                "sort": [{"@timestamp": {"order": "desc"}}],
                "query": {
                    "range": {
                        "@timestamp": {
                            "gte": self.last_query_time.isoformat(),
                            "lte": "now"
                        }
                    }
                }
            }

            headers = {}

            # Set authentication
            if self.api_key:
                headers['Authorization'] = f'ApiKey {self.api_key}'
            elif self.username and self.password:
                auth = base64.b64encode(f"{self.username}:{self.password}".encode()).decode()
                headers['Authorization'] = f'Basic {auth}'

            async with aiohttp.ClientSession() as session:
                url = f"{self.es_url}/{self.index_pattern}/_search"

                async with session.post(url, json=query, headers=headers, ssl=False) as response:
                    if response.status == 200:
                        data = await response.json()
                        hits = data.get('hits', {}).get('hits', [])

                        logs = []
                        for hit in hits:
                            log_entry = {
                                'source': 'elasticsearch',
                                'id': hit.get('_id'),
                                'index': hit.get('_index'),
                                'timestamp': hit.get('_source', {}).get('@timestamp', datetime.utcnow().isoformat()),
                                'data': hit.get('_source', {})
                            }
                            logs.append(log_entry)

                        # Update last query time
                        if logs:
                            self.last_query_time = datetime.utcnow()
                            self.logger.info(f"Fetched {len(logs)} logs from Elasticsearch")

                        return logs
                    else:
                        error_text = await response.text()
                        self.logger.error(f"Elasticsearch query failed: {response.status} - {error_text}")
                        return []

        except Exception as e:
            self.logger.error(f"Error fetching Elasticsearch logs: {e}")
            return []


class CrowdStrikeSource:
    """CrowdStrike Falcon data source integration with OAuth2"""

    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.client_id = None
        self.client_secret = None
        self.base_url = "https://api.crowdstrike.com"
        self.access_token = None
        self.token_expiry = None
        self.scopes = []

    async def configure(self, config: Dict[str, Any]):
        """Configure CrowdStrike connection"""
        self.client_id = config.get('client_id')
        self.client_secret = config.get('client_secret')
        self.base_url = config.get('base_url', self.base_url)
        self.scopes = config.get('scopes', [
            'detections:read',
            'incidents:read',
            'hosts:read',
            'alerts:read',
            'event-streams:read'
        ])

        if self.client_id and self.client_secret:
            # Get OAuth2 token
            await self._authenticate()
            self.logger.info(f"Configured CrowdStrike source with scopes: {self.scopes}")
            return True

        return False

    async def _authenticate(self):
        """Authenticate with CrowdStrike OAuth2"""
        try:
            url = f"{self.base_url}/oauth2/token"

            # Prepare OAuth2 request
            data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret
            }

            # Add scopes if specified
            if self.scopes:
                data['scope'] = ' '.join(self.scopes)

            async with aiohttp.ClientSession() as session:
                async with session.post(url, data=data) as response:
                    if response.status == 201 or response.status == 200:
                        token_data = await response.json()
                        self.access_token = token_data.get('access_token')
                        expires_in = token_data.get('expires_in', 3600)
                        self.token_expiry = datetime.utcnow() + timedelta(seconds=expires_in)
                        self.logger.info("CrowdStrike authentication successful")
                        return True
                    else:
                        error_text = await response.text()
                        self.logger.error(f"CrowdStrike authentication failed: {error_text}")
                        return False

        except Exception as e:
            self.logger.error(f"Error authenticating with CrowdStrike: {e}")
            return False

    async def _ensure_authenticated(self):
        """Ensure we have a valid access token"""
        if not self.access_token or datetime.utcnow() >= self.token_expiry:
            await self._authenticate()

    async def fetch_detections(self) -> List[Dict[str, Any]]:
        """Fetch detections from CrowdStrike"""
        await self._ensure_authenticated()

        if not self.access_token:
            self.logger.warning("CrowdStrike not authenticated")
            return []

        try:
            # First, get detection IDs
            url = f"{self.base_url}/detects/queries/detects/v1"
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            params = {
                'limit': 100,
                'sort': 'created_timestamp.desc'
            }

            async with aiohttp.ClientSession() as session:
                # Get detection IDs
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        detection_ids = data.get('resources', [])

                        if not detection_ids:
                            return []

                        # Get detection details
                        details_url = f"{self.base_url}/detects/entities/summaries/GET/v1"
                        body = {'ids': detection_ids[:100]}  # Process up to 100 detections

                        async with session.post(details_url, headers=headers, json=body) as details_response:
                            if details_response.status == 200:
                                details_data = await details_response.json()
                                detections = details_data.get('resources', [])

                                logs = []
                                for detection in detections:
                                    log_entry = {
                                        'source': 'crowdstrike',
                                        'type': 'detection',
                                        'id': detection.get('detection_id'),
                                        'timestamp': detection.get('created_timestamp', datetime.utcnow().isoformat()),
                                        'severity': detection.get('max_severity_displayname', 'unknown'),
                                        'status': detection.get('status'),
                                        'behaviors': detection.get('behaviors', []),
                                        'device': detection.get('device', {}),
                                        'data': detection
                                    }
                                    logs.append(log_entry)

                                self.logger.info(f"Fetched {len(logs)} detections from CrowdStrike")
                                return logs
                    else:
                        error_text = await response.text()
                        self.logger.error(f"CrowdStrike query failed: {response.status} - {error_text}")
                        return []

        except Exception as e:
            self.logger.error(f"Error fetching CrowdStrike detections: {e}")
            return []

    async def fetch_incidents(self) -> List[Dict[str, Any]]:
        """Fetch incidents from CrowdStrike"""
        await self._ensure_authenticated()

        if not self.access_token:
            return []

        try:
            url = f"{self.base_url}/incidents/queries/incidents/v1"
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            params = {
                'limit': 50,
                'sort': 'start.desc'
            }

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        incident_ids = data.get('resources', [])

                        if not incident_ids:
                            return []

                        # Get incident details
                        details_url = f"{self.base_url}/incidents/entities/incidents/GET/v1"
                        body = {'ids': incident_ids[:10]}  # Limit to 10 incidents

                        async with session.post(details_url, headers=headers, json=body) as details_response:
                            if details_response.status == 200:
                                details_data = await details_response.json()
                                incidents = details_data.get('resources', [])

                                logs = []
                                for incident in incidents:
                                    log_entry = {
                                        'source': 'crowdstrike',
                                        'type': 'incident',
                                        'id': incident.get('incident_id'),
                                        'timestamp': incident.get('start', datetime.utcnow().isoformat()),
                                        'severity': incident.get('fine_score', 0),
                                        'state': incident.get('state'),
                                        'data': incident
                                    }
                                    logs.append(log_entry)

                                self.logger.info(f"Fetched {len(logs)} incidents from CrowdStrike")
                                return logs
                    else:
                        error_text = await response.text()
                        self.logger.error(f"CrowdStrike incidents query failed: {error_text}")
                        return []

        except Exception as e:
            self.logger.error(f"Error fetching CrowdStrike incidents: {e}")
            return []

    async def fetch_logs(self) -> List[Dict[str, Any]]:
        """Main fetch method - gets both detections and incidents"""
        logs = []

        # Fetch detections
        detections = await self.fetch_detections()
        logs.extend(detections)

        # Fetch incidents
        incidents = await self.fetch_incidents()
        logs.extend(incidents)

        return logs


class GitHubSource:
    """GitHub repository pattern source integration"""

    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.github_sync = None
        self.repositories = []
        self.sync_interval = 3600  # Default 1 hour
        self.github_token = None

    async def configure(self, config: Dict[str, Any]):
        """Configure GitHub source"""
        from .github_pattern_sync import GitHubPatternSync

        # Get configuration
        self.github_token = config.get('github_token')
        self.repositories = config.get('repositories', [])
        self.sync_interval = config.get('sync_interval', 3600)

        # Initialize GitHub sync module
        redis_client = config.get('redis_client')
        postgres_conn = config.get('postgres_conn')

        if redis_client and postgres_conn:
            self.github_sync = GitHubPatternSync(redis_client, postgres_conn, self.logger)
            await self.github_sync.initialize(self.github_token)

            # Add configured repositories
            for repo in self.repositories:
                if isinstance(repo, str):
                    await self.github_sync.add_repository(repo, sync_interval=self.sync_interval)
                elif isinstance(repo, dict):
                    await self.github_sync.add_repository(
                        repo.get('url'),
                        branch=repo.get('branch', 'main'),
                        sync_interval=repo.get('sync_interval', self.sync_interval)
                    )

            self.logger.info(f"Configured GitHub source with {len(self.repositories)} repositories")
            return True

        self.logger.error("GitHub source requires redis_client and postgres_conn")
        return False

    async def fetch_patterns(self) -> List[Dict[str, Any]]:
        """Fetch and sync patterns from GitHub repositories"""
        if not self.github_sync:
            self.logger.warning("GitHub sync not initialized")
            return []

        try:
            # Sync all repositories
            results = await self.github_sync.sync_all_repositories()

            patterns = []
            for result in results:
                if result['status'] == 'completed':
                    patterns.append({
                        'source': 'github',
                        'type': 'pattern_update',
                        'repository': result['repo'],
                        'patterns_deployed': result['patterns_deployed'],
                        'timestamp': datetime.utcnow().isoformat()
                    })

            self.logger.info(f"Synced {len(patterns)} pattern updates from GitHub")
            return patterns

        except Exception as e:
            self.logger.error(f"Error fetching GitHub patterns: {e}")
            return []

    async def fetch_logs(self) -> List[Dict[str, Any]]:
        """Compatibility method - returns pattern updates as logs"""
        return await self.fetch_patterns()


class DataSourceFactory:
    """Factory for creating data source instances"""

    @staticmethod
    def create_source(source_type: str, logger: logging.Logger):
        """Create a data source instance based on type"""
        if source_type == 'elasticsearch':
            return ElasticsearchSource(logger)
        elif source_type == 'crowdstrike':
            return CrowdStrikeSource(logger)
        elif source_type == 'github':
            return GitHubSource(logger)
        else:
            return None


# Test the implementations
async def test_sources():
    """Test data source connections"""
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    print("Testing Data Source Integrations")
    print("=" * 50)

    # Test Elasticsearch
    print("\n1. Testing Elasticsearch Source")
    es_source = ElasticsearchSource(logger)
    es_config = {
        'url': 'http://localhost:9200',
        'index_pattern': 'logs-*',
        'batch_size': 10
    }

    if await es_source.configure(es_config):
        logs = await es_source.fetch_logs()
        print(f"   Fetched {len(logs)} logs from Elasticsearch")
    else:
        print("   Failed to configure Elasticsearch")

    # Test CrowdStrike
    print("\n2. Testing CrowdStrike Source")
    cs_source = CrowdStrikeSource(logger)
    # Note: Requires real credentials to test
    cs_config = {
        'client_id': 'your_client_id',
        'client_secret': 'your_client_secret',
        'scopes': ['detections:read', 'incidents:read']
    }

    if cs_config['client_id'] != 'your_client_id':
        if await cs_source.configure(cs_config):
            logs = await cs_source.fetch_logs()
            print(f"   Fetched {len(logs)} logs from CrowdStrike")
        else:
            print("   Failed to configure CrowdStrike")
    else:
        print("   CrowdStrike credentials not configured")


if __name__ == "__main__":
    asyncio.run(test_sources())