"""
Simple Manual Analysis Trigger

Trigger re-analysis and enrichment of all stored data
Works with current warm_storage schema (storage_id, data, created_at, expires_at)
"""

import json
import redis
import psycopg2
import time
import argparse
from datetime import datetime


def connect_db():
    """Connect to PostgreSQL"""
    return psycopg2.connect(
        host='localhost',
        port=5433,
        user='siemless',
        password='siemless123',
        database='siemless_v2'
    )


def connect_redis():
    """Connect to Redis"""
    return redis.Redis(
        host='localhost',
        port=6380,
        decode_responses=True
    )


def get_storage_stats(db):
    """Get statistics about stored data"""
    cursor = db.cursor()

    # Warm storage count
    cursor.execute("SELECT COUNT(*) FROM warm_storage")
    warm_storage_count = cursor.fetchone()[0]

    # Entities count
    cursor.execute("SELECT COUNT(*) FROM entities")
    entities_count = cursor.fetchone()[0]

    # Detection rules count
    cursor.execute("SELECT COUNT(*) FROM detection_rules")
    rules_count = cursor.fetchone()[0]

    # Entity types
    cursor.execute("""
        SELECT entity_type, COUNT(*)
        FROM entities
        GROUP BY entity_type
        ORDER BY COUNT(*) DESC
    """)
    entity_types = cursor.fetchall()

    cursor.close()

    return {
        'warm_storage': warm_storage_count,
        'entities': entities_count,
        'detection_rules': rules_count,
        'entity_types': entity_types
    }


def trigger_entity_enrichment(redis_conn, entity_limit=None, dry_run=False):
    """Trigger enrichment for all entities"""
    db = connect_db()
    cursor = db.cursor()

    query = "SELECT entity_type, entity_value FROM entities"
    if entity_limit:
        query += f" LIMIT {entity_limit}"

    cursor.execute(query)
    entities = cursor.fetchall()
    cursor.close()
    db.close()

    print(f"\n{'[DRY RUN] ' if dry_run else ''}Found {len(entities)} entities to enrich")

    if dry_run:
        print(f"Would publish {len(entities)} messages to contextualization.enrich_entity")
        return len(entities)

    enriched = 0
    for entity_type, entity_value in entities:
        try:
            message = {
                'entity_type': entity_type,
                'entity_value': entity_value,
                'enrich_level': 'deep'
            }
            redis_conn.publish('contextualization.enrich_entity', json.dumps(message))
            enriched += 1

            if enriched % 50 == 0:
                print(f"  Progress: {enriched}/{len(entities)} entities...")
                time.sleep(0.5)  # Small delay every 50

        except Exception as e:
            print(f"  ERROR: {entity_type}:{entity_value} - {e}")

    print(f"Completed: Triggered enrichment for {enriched} entities")
    return enriched


def reprocess_warm_storage(redis_conn, limit=None, dry_run=False):
    """
    Re-process warm storage entries

    Note: warm_storage schema is:
    - storage_id (uuid)
    - data (jsonb)
    - created_at (timestamp)
    - expires_at (timestamp)

    The 'data' field contains the stored log/event data
    """
    db = connect_db()
    cursor = db.cursor()

    query = "SELECT storage_id, data FROM warm_storage"
    if limit:
        query += f" LIMIT {limit}"

    cursor.execute(query)
    records = cursor.fetchall()
    cursor.close()
    db.close()

    print(f"\n{'[DRY RUN] ' if dry_run else ''}Found {len(records)} warm_storage records")

    if dry_run:
        print(f"Would publish {len(records)} messages to contextualization.process_log")
        return len(records)

    processed = 0
    for storage_id, data in records:
        try:
            # Parse data if it's a string
            if isinstance(data, str):
                data = json.loads(data)

            # Send to contextualization for processing
            message = {
                'log': data,
                'pattern_type': 'reprocessing',
                'log_id': str(storage_id),
                'entity_hints': []
            }

            redis_conn.publish('contextualization.process_log', json.dumps(message))
            processed += 1

            if processed % 100 == 0:
                print(f"  Progress: {processed}/{len(records)} records...")
                time.sleep(1)  # Delay every 100

        except Exception as e:
            print(f"  ERROR: {storage_id} - {e}")

    print(f"Completed: Triggered processing for {processed} records")
    return processed


def main():
    parser = argparse.ArgumentParser(description='Trigger manual analysis of stored data')

    parser.add_argument(
        '--action',
        choices=['report', 'enrich-entities', 'reprocess-storage', 'full'],
        default='report',
        help='Action to perform (default: report)'
    )

    parser.add_argument('--limit', type=int, help='Limit number of items to process')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done')

    args = parser.parse_args()

    print("="*80)
    print("SIEMLESS V2.0 - MANUAL ANALYSIS TRIGGER")
    print("="*80)

    # Get statistics
    db = connect_db()
    stats = get_storage_stats(db)
    db.close()

    print(f"\nCurrent Storage Statistics:")
    print(f"  Warm Storage Records: {stats['warm_storage']:,}")
    print(f"  Entities:            {stats['entities']:,}")
    print(f"  Detection Rules:     {stats['detection_rules']:,}")
    print(f"\nEntity Types:")
    for entity_type, count in stats['entity_types']:
        print(f"  {entity_type:20} {count:,}")

    if args.action == 'report':
        print("\n" + "="*80)
        print("REPORT ONLY - No processing triggered")
        print("="*80)
        print("\nAvailable actions:")
        print("  --action enrich-entities    : Re-enrich all entities with latest context")
        print("  --action reprocess-storage  : Re-process warm_storage records")
        print("  --action full              : Run all processing")
        print("\nAdd --dry-run to see what would be done without executing")
        print("Add --limit N to process only N items (for testing)")
        return

    redis_conn = connect_redis()

    try:
        if args.action in ['enrich-entities', 'full']:
            print("\n" + "="*80)
            print("PHASE 1: ENRICH ENTITIES")
            print("="*80)
            trigger_entity_enrichment(redis_conn, args.limit, args.dry_run)

        if args.action in ['reprocess-storage', 'full']:
            print("\n" + "="*80)
            print("PHASE 2: REPROCESS WARM STORAGE")
            print("="*80)
            reprocess_warm_storage(redis_conn, args.limit, args.dry_run)

        print("\n" + "="*80)
        if args.dry_run:
            print("DRY RUN COMPLETE")
        else:
            print("ANALYSIS TRIGGERED - Check engine logs for progress:")
            print("  docker-compose logs -f contextualization_engine")
        print("="*80)

    finally:
        redis_conn.close()


if __name__ == "__main__":
    main()
