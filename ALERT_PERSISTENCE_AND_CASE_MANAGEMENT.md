# Alert Persistence & Case Management Strategy

## Current Architecture Analysis (October 2025)

### What We Have Now

#### 1. Alert Data Flow
```
Elastic Security (Source of Truth)
    ↓ API Query (live)
Delivery Engine (_fetch_elastic_alerts)
    ↓ Enrichment Request
Contextualization Engine (adds intelligence)
    ↓ Return enriched data
Frontend (displays with context)
```

**Problem**: Zero persistence of:
- Enrichment results
- Correlation findings
- Investigation actions
- Success/failure metrics
- Analyst decisions

#### 2. Database Schema (EXISTS but UNUSED)

**✅ Tables Already Created:**

```sql
-- Investigation Tracking
investigations (
    id UUID PRIMARY KEY,
    title TEXT,
    severity VARCHAR(20),
    status VARCHAR(20) DEFAULT 'open',
    alert_ids JSONB,              -- Links to Elastic alerts
    entities JSONB,               -- Extracted entities
    mitre_techniques JSONB,       -- ATT&CK mapping
    threat_intel JSONB,           -- CTI findings
    timeline JSONB,               -- Investigation timeline
    risk_score INTEGER DEFAULT 0,
    assignee <PERSON><PERSON><PERSON><PERSON>(255),
    created_at TIMESTAMP,
    closed_at TIMESTAMP
)

-- Evidence Linking (SIEM → Investigation)
investigation_evidence (
    id SERIAL PRIMARY KEY,
    investigation_id UUID REFERENCES investigations(id),
    source_siem VARCHAR(50),      -- 'elastic', 'splunk', etc.
    evidence_type VARCHAR(50),    -- 'alert', 'event', 'metric'
    siem_query TEXT,              -- Exact query used
    siem_url TEXT,                -- Deep link to SIEM
    event_count INTEGER,
    timestamp_start TIMESTAMP,
    timestamp_end TIMESTAMP
)

-- Enrichment Audit Trail
enrichment_audit (
    audit_id UUID PRIMARY KEY,
    entity_id UUID,
    enrichment_type TEXT,         -- 'manual', 'automatic', 'ai_generated'
    enrichment_source TEXT,       -- 'crowdstrike', 'virustotal', 'gemini'
    old_value JSONB,
    new_value JSONB,
    confidence_score NUMERIC(5,2),
    ai_model_used TEXT,
    processing_time_ms INTEGER,
    timestamp TIMESTAMP
)

-- Case Management (separate from investigations)
cases (
    case_id UUID PRIMARY KEY,
    case_title VARCHAR(500),
    case_data JSONB,              -- Flexible structure
    status VARCHAR(50) DEFAULT 'open',
    assigned_to VARCHAR(100),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- Workflow Tracking
workflow_instances (
    -- Full workflow execution tracking
)
```

**Current Status**: All tables exist with 0 rows

---

## The Persistence Problem You Identified

### Scenario Without Persistence:
1. Alert comes in from Elastic
2. We enrich it (CrowdStrike context, VirusTotal, MITRE ATT&CK)
3. We correlate it (find related alerts, build attack chain)
4. Analyst investigates and resolves
5. **Alert ages out of Elastic (30-90 days)**
6. ❌ **ALL our intelligence is lost**
7. ❌ **No historical record of what worked**
8. ❌ **Can't measure MTTD/MTTR/success rates**
9. ❌ **Can't learn from past investigations**

### With Proper Persistence:
1. Alert detected → Create `investigation` record
2. Enrichment completed → Store in `enrichment_audit`
3. Correlation found → Store in `investigation_evidence`
4. Analyst actions → Update `investigation.timeline`
5. Case created → Link to `cases` table
6. Alert expires in Elastic → ✅ **We keep our intelligence**
7. ✅ **Historical record preserved**
8. ✅ **Metrics calculable (MTTD/MTTR)**
9. ✅ **Pattern learning from outcomes**

---

## ITSM Integration Strategy

### Option 1: Native Case Management (What We Have)
**Use SIEMLess's built-in case management**

**Pros:**
- Already have the schema (`cases`, `investigations` tables)
- Tight integration with enrichment/correlation
- No external dependencies
- Full control over workflow

**Cons:**
- Not integrated with existing ticketing systems
- Analysts need to check another tool
- No approval workflows/SLAs from ITSM

**Best For:**
- Small teams (< 10 analysts)
- Pure security-focused workflow
- Teams without existing ITSM

### Option 2: Jira Integration (Bi-Directional Sync)
**Sync SIEMLess investigations ↔ Jira tickets**

```python
# Pattern for Jira Integration
class JiraIntegration:
    async def create_ticket_from_investigation(self, investigation_id):
        """
        When: Investigation created in SIEMLess
        Action: Create Jira ticket with enriched context

        Jira Ticket Contains:
        - Title: Alert title + severity
        - Description: Enriched context (CTI, MITRE, entities)
        - Custom Fields: risk_score, mitre_techniques, threat_actors
        - Attachments: Evidence links, SIEM queries
        - Labels: alert_ids, entity_types
        """
        inv = await db.fetch_investigation(investigation_id)

        jira_ticket = {
            'project': 'SEC',
            'issuetype': 'Security Incident',
            'summary': f"[{inv.severity}] {inv.title}",
            'description': self._format_investigation_context(inv),
            'customfield_10050': inv.risk_score,  # Risk Score
            'customfield_10051': inv.mitre_techniques,  # ATT&CK
            'customfield_10052': inv.threat_intel,  # CTI findings
        }

        ticket = jira_client.create_issue(fields=jira_ticket)

        # Store bidirectional link
        await db.execute("""
            UPDATE investigations
            SET case_data = jsonb_set(case_data, '{jira_ticket}', %s)
            WHERE id = %s
        """, ticket.key, investigation_id)

    async def sync_status_from_jira(self, ticket_key):
        """
        When: Jira ticket status changes
        Action: Update investigation status in SIEMLess
        """
        ticket = jira_client.issue(ticket_key)
        investigation_id = ticket.customfield_10053  # SIEMLess Investigation ID

        status_map = {
            'Open': 'open',
            'In Progress': 'investigating',
            'Resolved': 'resolved',
            'Closed': 'closed',
            'False Positive': 'false_positive'
        }

        await db.execute("""
            UPDATE investigations
            SET status = %s, updated_at = NOW()
            WHERE id = %s
        """, status_map[ticket.status.name], investigation_id)
```

**Pros:**
- Integrates with existing workflows
- Analysts use familiar tool (Jira)
- Approval workflows, SLAs, escalations
- Audit trail in Jira

**Cons:**
- Requires Jira licenses
- Sync complexity (webhooks, polling)
- Data duplication

**Best For:**
- Teams already using Jira for incidents
- Enterprise environments (compliance/audit)
- Cross-team coordination (SecOps + IT)

### Option 3: ServiceNow Integration
**Similar to Jira but for ServiceNow ITSM**

```python
class ServiceNowIntegration:
    async def create_incident(self, investigation_id):
        """
        Create ServiceNow incident from investigation

        Maps to:
        - Incident table (incident)
        - Security Incident table (sn_si_incident)
        - Enrichment as Work Notes
        - Evidence as Attachments
        """
        inv = await db.fetch_investigation(investigation_id)

        incident = {
            'short_description': inv.title,
            'description': self._format_context(inv),
            'urgency': self._map_severity(inv.severity),  # 1-5
            'category': 'Security',
            'subcategory': 'Intrusion',
            'u_mitre_techniques': json.dumps(inv.mitre_techniques),
            'u_risk_score': inv.risk_score,
            'u_threat_actors': json.dumps(inv.threat_intel.get('actors', []))
        }

        response = snow_client.post('/api/now/table/incident', json=incident)
        return response.json()['result']['sys_id']
```

**Pros:**
- Enterprise-grade ITSM features
- Built-in SLA management
- Change management integration
- Compliance/audit trails

**Cons:**
- Expensive (ServiceNow licensing)
- Complex setup
- Overkill for pure security teams

**Best For:**
- Large enterprises with ServiceNow
- Regulated industries (finance, healthcare)
- Integrated IT + Security operations

---

## Recommended Hybrid Approach

### Phase 1: Native Persistence (Immediate - 1 week)
**Implement SIEMLess native persistence for all intelligence**

```python
# When alert enrichment completes
async def store_enrichment_results(alert_id, enrichment_data):
    # 1. Create/update investigation record
    investigation_id = await db.execute("""
        INSERT INTO investigations (id, title, severity, status, alert_ids, entities, threat_intel, created_at)
        VALUES (gen_random_uuid(), %s, %s, 'open', %s, %s, %s, NOW())
        ON CONFLICT (id) DO UPDATE
        SET entities = EXCLUDED.entities,
            threat_intel = EXCLUDED.threat_intel,
            updated_at = NOW()
        RETURNING id
    """, alert_title, severity, [alert_id], enrichment_data.entities, enrichment_data.threat_intel)

    # 2. Store enrichment audit trail
    for entity_id, enrichment in enrichment_data.items():
        await db.execute("""
            INSERT INTO enrichment_audit (entity_id, enrichment_type, enrichment_source, new_value, confidence_score, ai_model_used, timestamp)
            VALUES (%s, 'automatic', %s, %s, %s, %s, NOW())
        """, entity_id, enrichment.source, enrichment.data, enrichment.confidence, enrichment.ai_model)

    # 3. Link SIEM evidence
    await db.execute("""
        INSERT INTO investigation_evidence (investigation_id, source_siem, evidence_type, siem_query, siem_url, event_count, timestamp_start)
        VALUES (%s, 'elastic', 'alert', %s, %s, 1, %s)
    """, investigation_id, alert_query, elastic_url, alert_timestamp)
```

**Benefits:**
- ✅ Preserve all enrichment intelligence
- ✅ Historical record even after alert expires
- ✅ Enable metrics (MTTD, MTTR, enrichment quality)
- ✅ Pattern learning from outcomes
- ✅ No external dependencies

### Phase 2: ITSM Integration (Optional - 2-4 weeks)
**Add Jira/ServiceNow sync for teams that need it**

```python
# Configuration-driven ITSM integration
ITSM_CONFIG = {
    'enabled': True,
    'provider': 'jira',  # or 'servicenow'
    'sync_mode': 'bidirectional',  # 'push_only' or 'bidirectional'
    'create_ticket_on': ['investigation_created', 'risk_score_high'],
    'sync_status': True,
    'sync_comments': True,
}

# Auto-create ticket when investigation meets criteria
if ITSM_CONFIG['enabled'] and inv.risk_score >= 80:
    await itsm_integrations[ITSM_CONFIG['provider']].create_ticket(inv.id)
```

---

## Metrics & Success Tracking

### With Proper Persistence, We Can Measure:

**1. Investigation Metrics**
```sql
-- Mean Time to Detect (MTTD)
SELECT AVG(EXTRACT(EPOCH FROM (created_at - alert_timestamp)) / 60) as mttd_minutes
FROM investigations;

-- Mean Time to Resolve (MTTR)
SELECT AVG(EXTRACT(EPOCH FROM (closed_at - created_at)) / 3600) as mttr_hours
FROM investigations
WHERE status = 'resolved';

-- Resolution Rate by Severity
SELECT severity,
       COUNT(*) FILTER (WHERE status = 'resolved') * 100.0 / COUNT(*) as resolution_rate
FROM investigations
GROUP BY severity;
```

**2. Enrichment Quality**
```sql
-- Enrichment sources contributing to true positives
SELECT enrichment_source, COUNT(*) as true_positive_count
FROM enrichment_audit ea
JOIN investigations i ON i.id = ea.investigation_id
WHERE i.status = 'true_positive'
GROUP BY enrichment_source
ORDER BY true_positive_count DESC;

-- AI model accuracy
SELECT ai_model_used,
       AVG(confidence_score) as avg_confidence,
       COUNT(*) FILTER (WHERE i.status = 'true_positive') * 100.0 / COUNT(*) as accuracy
FROM enrichment_audit ea
JOIN investigations i ON i.id = ea.investigation_id
WHERE ai_model_used IS NOT NULL
GROUP BY ai_model_used;
```

**3. Analyst Performance**
```sql
-- Investigations per analyst
SELECT assignee,
       COUNT(*) as total_investigations,
       COUNT(*) FILTER (WHERE status = 'resolved') as resolved,
       AVG(EXTRACT(EPOCH FROM (closed_at - created_at)) / 3600) as avg_resolution_hours
FROM investigations
WHERE assignee IS NOT NULL
GROUP BY assignee;
```

**4. Pattern Learning**
```sql
-- Most common MITRE techniques in true positives
SELECT jsonb_array_elements_text(mitre_techniques) as technique,
       COUNT(*) as occurrences
FROM investigations
WHERE status = 'true_positive'
GROUP BY technique
ORDER BY occurrences DESC
LIMIT 10;
```

---

## Implementation Plan

### Week 1: Core Persistence
- [ ] Implement investigation creation on alert enrichment
- [ ] Store enrichment results in `enrichment_audit`
- [ ] Link SIEM evidence in `investigation_evidence`
- [ ] Add investigation status updates

### Week 2: Metrics Dashboard
- [ ] Build investigation metrics queries
- [ ] Create Grafana dashboards for MTTD/MTTR
- [ ] Enrichment quality dashboard
- [ ] Analyst performance metrics

### Week 3: Historical Analysis
- [ ] Pattern learning from closed investigations
- [ ] Auto-suggest similar past cases
- [ ] Success rate by enrichment type
- [ ] Threat actor tracking over time

### Week 4 (Optional): ITSM Integration
- [ ] Jira API integration
- [ ] Bidirectional sync (SIEMLess ↔ Jira)
- [ ] Configuration UI for ITSM settings
- [ ] Webhook handlers for status updates

---

## Conclusion

**Short Answer to Your Questions:**

1. **Alert Persistence**: We need it. Schema exists, just needs implementation. Without it, we lose all our intelligence when alerts age out of Elastic.

2. **ITSM Integration**:
   - **Start with native case management** (we have the schema)
   - **Add Jira integration if needed** (bidirectional sync for enterprise)
   - **ServiceNow for large enterprises** (full ITSM features)

3. **Recommended Path**:
   - Phase 1: Implement native persistence (1 week)
   - Phase 2: Add Jira sync if team uses it (2 weeks)
   - Phase 3: ServiceNow only if required by enterprise policy

The schema is already there. We just need to actually USE it.
