/**
 * CTI Plugin Status Widget
 * Monitor all threat intelligence sources in real-time
 *
 * Features:
 * - 4 CTI source monitoring (OTX, ThreatFox, CrowdStrike, OpenCTI)
 * - Real-time status updates
 * - Manual update triggers
 * - Credential validation
 * - Aggregate statistics
 * - Auto-refresh every 30 seconds
 */

import React, { useEffect } from 'react'
import { Database, RefreshCw, TrendingUp, AlertTriangle } from 'lucide-react'
import { useCTIStore } from '../stores/ctiStore'
import { CTIPluginCard, CTIPluginCardSkeleton } from '../components/cti/CTIPluginCard'
import { StatCard } from '../components/common/StatCard'
import { wsClient } from '../api/client'
import type { WebSocketMessage } from '../types/api'

const CTIPluginStatus: React.FC = () => {
  const {
    plugins,
    stats,
    loading,
    updating,
    polling,
    error,
    fetchStatus,
    fetchStats,
    startPolling,
    stopPolling,
    triggerUpdate,
    testCredentials,
    refreshAll
  } = useCTIStore()

  // Initial load and start polling
  useEffect(() => {
    startPolling()

    return () => {
      stopPolling()
    }
  }, [startPolling, stopPolling])

  // WebSocket real-time updates
  useEffect(() => {
    const handleCTIUpdate = (message: WebSocketMessage) => {
      console.log('CTI update received:', message)
      refreshAll()
    }

    // Subscribe to CTI update completion
    wsClient.on('cti.update.complete', handleCTIUpdate)

    // Connect if not already connected
    // TODO: Enable WebSocket when backend /ws endpoint is implemented
    // try {
    //   wsClient.connect()
    // } catch (error) {
    //   console.error('WebSocket connection failed:', error)
    // }

    return () => {
      wsClient.off('cti.update.complete', handleCTIUpdate)
    }
  }, [refreshAll])

  // Handle manual update
  const handleUpdate = async (source: string) => {
    await triggerUpdate(source, 7)  // Last 7 days
  }

  // Handle credential test
  const handleTestCredentials = async (source: string) => {
    const valid = await testCredentials(source)
    if (valid) {
      alert(`${source.toUpperCase()} credentials are valid!`)
    } else {
      alert(`${source.toUpperCase()} credentials are invalid. Please check configuration.`)
    }
  }

  // Get aggregate metrics
  const getActiveSourcesCount = () => {
    return plugins.filter(p => p.status === 'active').length
  }

  const getTotalIndicators = () => {
    return plugins.reduce((sum, p) => sum + p.total_indicators, 0)
  }

  const getNewIndicatorsToday = () => {
    return plugins.reduce((sum, p) => sum + p.new_indicators_today, 0)
  }

  const getErrorCount = () => {
    return plugins.filter(p => p.status === 'error').length
  }

  return (
    <div className="h-full flex flex-col bg-gray-50 p-6 overflow-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">CTI Plugin Status</h1>
          <p className="text-sm text-gray-500 mt-1">
            Monitor threat intelligence sources
            {polling && <span className="ml-2 text-green-600">● Auto-refreshing</span>}
          </p>
        </div>

        <button
          onClick={() => refreshAll()}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center gap-2"
        >
          <RefreshCw size={16} className={loading ? 'animate-spin' : ''} />
          Refresh All
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start gap-3">
          <AlertTriangle size={20} className="text-red-600 mt-0.5" />
          <div>
            <p className="text-red-800 font-medium">Error Loading CTI Status</p>
            <p className="text-sm text-red-600 mt-1">{error}</p>
          </div>
        </div>
      )}

      {/* Aggregate Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Active Sources */}
        <StatCard
          title="Active Sources"
          value={`${getActiveSourcesCount()}/${plugins.length}`}
          subtitle="Operational"
          icon={<Database size={24} />}
          color={getActiveSourcesCount() === plugins.length ? 'green' : 'yellow'}
          loading={loading}
        />

        {/* Total Indicators */}
        <StatCard
          title="Total Indicators"
          value={getTotalIndicators().toLocaleString()}
          subtitle="Across all sources"
          icon={<Database size={24} />}
          color="blue"
          loading={loading}
        />

        {/* New Indicators Today */}
        <StatCard
          title="New Today"
          value={`+${getNewIndicatorsToday()}`}
          subtitle="Fresh threat data"
          icon={<TrendingUp size={24} />}
          color="purple"
          loading={loading}
        />

        {/* Average Threat Score */}
        {stats && (
          <StatCard
            title="Avg Threat Score"
            value={stats.avg_threat_score.toFixed(1)}
            subtitle={`${stats.high_confidence_indicators} high confidence`}
            icon={<AlertTriangle size={24} />}
            color={stats.avg_threat_score > 70 ? 'red' : stats.avg_threat_score > 50 ? 'yellow' : 'green'}
            loading={loading}
          />
        )}
      </div>

      {/* Error Summary */}
      {getErrorCount() > 0 && (
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 font-medium">
            ⚠️ {getErrorCount()} source{getErrorCount() > 1 ? 's' : ''} reporting errors
          </p>
          <p className="text-sm text-yellow-700 mt-1">
            Check individual plugin cards below for details
          </p>
        </div>
      )}

      {/* Plugin Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
        {loading && plugins.length === 0 ? (
          // Loading skeletons
          <>
            <CTIPluginCardSkeleton />
            <CTIPluginCardSkeleton />
            <CTIPluginCardSkeleton />
            <CTIPluginCardSkeleton />
          </>
        ) : plugins.length === 0 ? (
          // No plugins
          <div className="col-span-full text-center py-12 text-gray-500">
            <Database size={48} className="mx-auto mb-4 opacity-20" />
            <p className="text-lg font-medium">No CTI plugins configured</p>
            <p className="text-sm mt-2">Configure CTI sources to start monitoring threat intelligence</p>
          </div>
        ) : (
          // Plugin cards
          plugins.map((plugin) => (
            <CTIPluginCard
              key={plugin.source}
              plugin={plugin}
              onUpdate={handleUpdate}
              onTestCredentials={handleTestCredentials}
              updating={updating[plugin.source] || false}
            />
          ))
        )}
      </div>

      {/* Indicator Type Breakdown */}
      {stats && stats.indicators_by_type && Object.keys(stats.indicators_by_type).length > 0 && (
        <div className="mt-6 bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Indicator Breakdown by Type
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(stats.indicators_by_type).map(([type, count]) => (
              <div key={type} className="text-center p-4 bg-gray-50 rounded">
                <p className="text-2xl font-bold text-gray-900">{count}</p>
                <p className="text-sm text-gray-600 mt-1">{type.toUpperCase()}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-sm font-medium text-blue-900 mb-2">
          💡 About CTI Plugin Status
        </h3>
        <div className="text-sm text-blue-700 space-y-1">
          <p>• <strong>Active</strong>: Plugin is operational and updating normally</p>
          <p>• <strong>Updating</strong>: Plugin is currently fetching new indicators</p>
          <p>• <strong>Error</strong>: Plugin encountered an issue (check error message)</p>
          <p>• <strong>Update Now</strong>: Manually trigger an update for the last 7 days</p>
          <p>• <strong>Test</strong>: Validate API credentials and connectivity</p>
        </div>
      </div>
    </div>
  )
}

export default CTIPluginStatus
