import React, { useState, useRef, useEffect } from 'react'
import {
  Search, Bell, Plus, Zap, Menu, X,
  ChevronDown, Settings, LogOut, User,
  Shield, Command, HelpCircle, Moon, Sun
} from 'lucide-react'
import { useAuthStore } from '../../stores/authStore'
import { useNavigationStore } from '../../stores/navigationStore'
import { useNotificationStore } from '../../stores/notificationStore'
import GlobalSearch from './GlobalSearch'
import NotificationPanel from './NotificationPanel'
import QuickActions from './QuickActions'

const TopBar: React.FC = () => {
  const { user, logout } = useAuthStore()
  const { toggleSidebar } = useNavigationStore()
  const { unreadCount } = useNotificationStore()

  const [showSearch, setShowSearch] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [theme, setTheme] = useState<'light' | 'dark'>('light')

  const notificationRef = useRef<HTMLDivElement>(null)
  const userMenuRef = useRef<HTMLDivElement>(null)

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setShowNotifications(false)
      }
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
    document.documentElement.classList.toggle('dark', newTheme === 'dark')
  }

  const getEnvironmentBadge = () => {
    const env = import.meta.env.VITE_ENVIRONMENT || 'development'
    const colors = {
      development: 'bg-green-500',
      staging: 'bg-yellow-500',
      production: 'bg-red-500'
    }
    return { env, color: colors[env as keyof typeof colors] || 'bg-gray-500' }
  }

  const { env, color } = getEnvironmentBadge()

  return (
    <header className="h-14 bg-white border-b border-gray-200 flex items-center justify-between px-4">
      {/* Left Section: Brand & Menu */}
      <div className="flex items-center gap-4">
        {/* Sidebar Toggle */}
        <button
          onClick={toggleSidebar}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          title="Toggle Sidebar"
        >
          <Menu size={20} />
        </button>

        {/* Brand */}
        <div className="flex items-center gap-3">
          <Shield className="text-blue-600" size={28} />
          <div className="flex items-center gap-2">
            <span className="font-bold text-lg">SIEMLess</span>
            <span className="text-xs text-gray-500">v2.0</span>
            {env !== 'production' && (
              <span className={`text-xs px-2 py-0.5 rounded text-white ${color}`}>
                {env.toUpperCase()}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Center Section: Search */}
      <div className="flex-1 max-w-2xl mx-8">
        <button
          onClick={() => setShowSearch(true)}
          className="w-full max-w-md mx-auto flex items-center gap-2 px-4 py-2 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <Search size={16} className="text-gray-400" />
          <span className="text-sm text-gray-500">Search cases, entities, or actions...</span>
          <kbd className="ml-auto px-2 py-0.5 text-xs bg-white rounded border border-gray-300">
            Ctrl+K
          </kbd>
        </button>
      </div>

      {/* Right Section: Actions & User */}
      <div className="flex items-center gap-2">
        {/* Quick Actions */}
        <QuickActions />

        {/* Quick Response */}
        <button
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors group"
          title="Quick Response (Ctrl+Shift+R)"
        >
          <Zap size={20} className="text-gray-600 group-hover:text-yellow-500" />
        </button>

        {/* Notifications */}
        <div ref={notificationRef} className="relative">
          <button
            onClick={() => setShowNotifications(!showNotifications)}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors relative"
            title="Notifications"
          >
            <Bell size={20} className="text-gray-600" />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 px-1.5 py-0.5 text-xs bg-red-500 text-white rounded-full min-w-[20px] text-center">
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}
          </button>

          {/* Notification Dropdown */}
          {showNotifications && (
            <NotificationPanel
              onClose={() => setShowNotifications(false)}
            />
          )}
        </div>

        {/* Theme Toggle */}
        <button
          onClick={toggleTheme}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          title="Toggle Theme"
        >
          {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}
        </button>

        {/* User Menu */}
        <div ref={userMenuRef} className="relative">
          <button
            onClick={() => setShowUserMenu(!showUserMenu)}
            className="flex items-center gap-2 p-1.5 hover:bg-gray-100 rounded-lg transition-colors"
          >
            {/* User Avatar */}
            <div className="relative">
              {user?.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-8 h-8 rounded-full"
                />
              ) : (
                <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  {user?.name?.charAt(0).toUpperCase() || 'U'}
                </div>
              )}
              {/* Online Status Indicator */}
              <div className="absolute bottom-0 right-0 w-2.5 h-2.5 bg-green-400 border-2 border-white rounded-full" />
            </div>
            <ChevronDown size={16} className="text-gray-500" />
          </button>

          {/* User Dropdown Menu */}
          {showUserMenu && (
            <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
              {/* User Info */}
              <div className="p-4 border-b">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center font-medium">
                    {user?.name?.charAt(0).toUpperCase() || 'U'}
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{user?.name || 'User'}</div>
                    <div className="text-xs text-gray-500">{user?.email}</div>
                  </div>
                </div>
              </div>

              {/* Menu Items */}
              <div className="py-2">
                <button className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2">
                  <User size={16} />
                  View Profile
                </button>
                <button className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2">
                  <Settings size={16} />
                  Account Settings
                </button>
                <button className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2">
                  <Command size={16} />
                  Keyboard Shortcuts
                </button>
                <button className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2">
                  <HelpCircle size={16} />
                  Help & Documentation
                </button>
              </div>

              {/* Logout */}
              <div className="border-t py-2">
                <button
                  onClick={logout}
                  className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                >
                  <LogOut size={16} />
                  Sign Out
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Global Search Modal */}
      {showSearch && (
        <GlobalSearch onClose={() => setShowSearch(false)} />
      )}
    </header>
  )
}

export default TopBar