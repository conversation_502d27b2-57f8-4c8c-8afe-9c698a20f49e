# Google Chronicle SIEM Configuration
# Query Language: YARA-L 2.0

platform:
  name: chronicle
  display_name: Google Chronicle
  query_language: yara-l
  description: Google Chronicle's YARA-L 2.0 detection rule language
  vendor: Google LLC
  version: "1.0"
  active: true

# Field mappings: generic_field -> Chronicle UDM field
field_mappings:
  source_ip: principal.ip
  destination_ip: target.ip
  username: principal.user.userid
  process_name: target.process.file.full_path
  file_hash: target.file.sha256
  event_id: metadata.event_type
  hostname: principal.hostname
  port: target.port
  source_port: principal.port
  destination_port: target.port
  domain: network.dns.questions.name
  url: target.url
  file_name: target.file.full_path
  file_path: target.file.full_path
  registry_path: target.registry.registry_key
  command_line: target.process.command_line
  parent_process: principal.process.file.full_path
  network_protocol: network.application_protocol
  http_method: network.http.method
  user_agent: network.http.user_agent
  email_sender: network.email.from
  email_recipient: network.email.to
  dns_query: network.dns.questions.name
  service_name: target.resource.name
  account_name: principal.user.userid
  process_id: target.process.pid
  asset_id: principal.asset_id
  product_name: principal.asset.product_object_id

# Operator mappings: generic_operator -> Chronicle operator
operator_mappings:
  equals: "="
  not_equals: "!="
  contains: "=~"
  not_contains: "!~"
  regex: "=~"
  greater_than: ">"
  less_than: "<"
  greater_equal: ">="
  less_equal: "<="
  in_list: "in"
  not_in_list: "not in"

# Time field for temporal queries
time_field: metadata.event_timestamp

# Query syntax specifics
syntax:
  comment: "//"
  string_quote: "\""
  escape_char: "\\"
  wildcard: ".*"
  field_separator: "."
  logical_and: and
  logical_or: or
  logical_not: not
  rule_keyword: rule
  meta_section: meta
  events_section: events
  match_section: match
  outcome_section: outcome
  condition_section: condition
  regex_delimiter: "/"
  case_sensitive: true

# Platform-specific features
metadata:
  supports_regex: true
  supports_wildcards: true
  supports_multiline_detection: true
  supports_threat_intel: true
  udm_version: "2.0"
  max_detection_window: "48h"

  # UDM (Unified Data Model) event types
  udm_event_types:
    - NETWORK_CONNECTION
    - NETWORK_HTTP
    - NETWORK_DNS
    - PROCESS_LAUNCH
    - PROCESS_TERMINATION
    - FILE_CREATION
    - FILE_DELETION
    - FILE_MODIFICATION
    - FILE_READ
    - REGISTRY_CREATION
    - REGISTRY_MODIFICATION
    - REGISTRY_DELETION
    - USER_LOGIN
    - USER_LOGOUT
    - USER_CREATION
    - USER_DELETION
    - RESOURCE_CREATION
    - RESOURCE_DELETION
    - STATUS_UPDATE
    - SCAN_PROCESS_TERMINATE

  # Detection rule template
  rule_template: |
    rule {rule_name} {{
      meta:
        author = "{author}"
        description = "{description}"
        severity = "{severity}"

      events:
        $e.metadata.event_type = "{event_type}"
        {conditions}

      condition:
        $e
    }}

  # Multi-event detection template
  multi_event_template: |
    rule {rule_name} {{
      meta:
        author = "{author}"
        description = "{description}"

      events:
        $event1.metadata.event_type = "NETWORK_CONNECTION"
        {event1_conditions}

        $event2.metadata.event_type = "PROCESS_LAUNCH"
        {event2_conditions}

      match:
        $event1.principal.hostname = $event2.principal.hostname over {time_window}

      condition:
        $event1 and $event2
    }}

  # Outcome sections for enrichment
  outcome_types:
    - risk_score  # Numeric risk score
    - severity  # LOW, MEDIUM, HIGH, CRITICAL
    - summary  # Detection summary
    - mitre_attack  # MITRE ATT&CK mapping
    - graph  # Entity graph references

  # Reference lists
  supports_reference_lists: true
  reference_list_types:
    - IP addresses
    - Domain names
    - URLs
    - File hashes
    - Usernames
    - Hostnames

  # Detection tuning
  supports_false_positive_reduction: true
  supports_entity_context: true
  supports_threat_score_adjustment: true

  # Retrohunt capability
  supports_retrohunt: true
  max_retrohunt_window: "1 year"

  # YARA-L 2.0 specific features
  yara_l_features:
    - placeholder_variables  # $e, $event1, etc.
    - match_sections  # Correlation across events
    - outcome_sections  # Detection enrichment
    - reference_lists  # IOC matching
    - regex_support  # Pattern matching
    - time_windows  # Temporal correlation
    - entity_graphs  # Relationship analysis
