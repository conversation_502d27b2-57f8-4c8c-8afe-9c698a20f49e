# Rule Management API - Complete Reference

## Overview

SIEMLess provides comprehensive rule management capabilities across three engines:
- **Backend Engine**: Rule creation, storage, deployment orchestration
- **Delivery Engine**: Rule listing, viewing, testing (user-facing API)
- **Ingestion Engine**: Rule deployment to SIEMs (external integration)

---

## Current API Endpoints

### Delivery Engine (Port 8005) - User-Facing

#### 1. List All Rules
```bash
GET /api/rules
```

**Response**:
```json
{
  "rules": [
    {
      "id": "uuid",
      "name": "Rule Name",
      "source": "opencti",
      "ioc_type": "domain",
      "quality_score": 0.5,
      "deployed": false
    }
  ]
}
```

**Current Source**: Redis cache (`frontend:rules:all`)

#### 2. Get Rule Details
```bash
GET /api/rules/{rule_id}
```

**Response**:
```json
{
  "id": "uuid",
  "name": "OPENCTI domain: malicious.com",
  "platform": "elastic",
  "query": "dns.question.name: \"malicious.com\"",
  "severity": "medium",
  "created_at": "2025-10-03T08:00:00Z"
}
```

#### 3. Test Rule
```bash
POST /api/rules/test
Content-Type: application/json

{
  "query": "dns.question.name: \"malicious.com\"",
  "test_log": { ... }
}
```

**Response**:
```json
{
  "success": true,
  "matched": true,
  "execution_time": 0.123,
  "details": "Rule matched successfully"
}
```

### Ingestion Engine (Port 8003) - Deployment

#### 4. Deploy Rule to Elastic
```bash
POST /api/rules/{rule_id}/deploy/elastic
```

**Response**:
```json
{
  "success": true,
  "rule_id": "uuid",
  "elastic_rule_id": "elastic-uuid",
  "enabled": false,
  "deployed_at": "2025-10-03T08:00:00Z"
}
```

#### 5. Deploy to Any SIEM
```bash
POST /api/rules/{rule_id}/deploy/{target}
# target: elastic | splunk | sentinel | qradar
```

#### 6. Bulk Deploy
```bash
POST /api/rules/deploy/bulk
Content-Type: application/json

{
  "rule_ids": ["uuid1", "uuid2"],
  "target": "elastic"
}
```

#### 7. Update Deployed Rule
```bash
PUT /api/rules/{rule_id}/deployment/elastic/{elastic_rule_id}
Content-Type: application/json

{
  "enabled": true  # Enable the rule
}
```

#### 8. Delete Deployed Rule
```bash
DELETE /api/rules/deployment/elastic/{elastic_rule_id}
```

#### 9. Get Deployment Status
```bash
GET /api/rules/{rule_id}/deployment/status
```

**Response**:
```json
{
  "rule_id": "uuid",
  "deployed_to_elastic": true,
  "elastic_rule_id": "elastic-uuid",
  "deployed_to_splunk": false,
  "last_deployed_at": "2025-10-03T08:00:00Z"
}
```

---

## Missing CRUD Operations ⚠️

### What's Missing:

1. **❌ Update Rule (Local Database)**
   - No endpoint to edit rule name, description, severity
   - Can only update deployed rules in SIEM

2. **❌ Delete Rule (Local Database)**
   - No endpoint to delete rules from SIEMLess database
   - Can only delete from SIEM deployments

3. **❌ Create Rule (Manual)**
   - No endpoint to manually create a rule
   - Rules only created via CTI ingestion

4. **❌ Tag/Label Management**
   - No way to add quality labels (low/medium/high)
   - No custom tags for organization

---

## Recommendation: Label-Based Approach for Low-Quality Rules ✅

### Why Labels Are Better Than Auto-Delete:

1. **User Control**: Analysts decide what to keep/delete
2. **Learning**: Low-quality rules might have value in specific contexts
3. **Audit Trail**: Can review why rules were flagged
4. **Flexibility**: Quality thresholds can be adjusted

### Proposed Implementation:

#### Add Quality Labels to Rules

**Database Update**:
```sql
-- Add quality_label field (auto-calculated)
ALTER TABLE detection_rules ADD COLUMN quality_label VARCHAR(20);

-- Create index for filtering
CREATE INDEX idx_quality_label ON detection_rules(quality_label);

-- Auto-populate based on quality_score
UPDATE detection_rules
SET quality_label = CASE
  WHEN (rule_data->>'quality_score')::float >= 0.7 THEN 'high'
  WHEN (rule_data->>'quality_score')::float >= 0.4 THEN 'medium'
  ELSE 'low'
END;
```

#### New API Endpoints Needed:

**1. List Rules with Filters**
```bash
GET /api/rules?quality=low&source=opencti&deployed=false
```

**2. Update Rule Metadata**
```bash
PATCH /api/rules/{rule_id}
Content-Type: application/json

{
  "quality_label": "reviewed",  # Override auto-label
  "custom_tags": ["false_positive", "review_later"],
  "notes": "This domain is actually legitimate"
}
```

**3. Delete Rule**
```bash
DELETE /api/rules/{rule_id}
```

**Response**:
```json
{
  "success": true,
  "deleted_rule_id": "uuid",
  "also_deleted_from": ["elastic"]  # If deployed
}
```

**4. Bulk Delete by Criteria**
```bash
POST /api/rules/bulk-delete
Content-Type: application/json

{
  "filters": {
    "quality_label": "low",
    "source": "unknown",
    "deployed": false,
    "created_before": "2025-10-01"
  },
  "dry_run": true  # Preview before actual delete
}
```

**Response**:
```json
{
  "dry_run": true,
  "would_delete": 500,
  "sample_rules": [
    {"id": "uuid1", "name": "Rule 1"},
    {"id": "uuid2", "name": "Rule 2"}
  ]
}
```

---

## Implementation Plan

### Phase 1: Add Labels (Non-Breaking)

1. Add `quality_label` column to database ✅ Simple migration
2. Update rule creation to auto-label based on quality_score
3. Add filter parameter to existing `/api/rules` endpoint

**Code Location**: `engines/backend/backend_engine.py::_store_rule()`

```python
# Add to _store_rule function
quality_label = 'high' if rule['quality_score'] >= 0.7 else 'medium' if rule['quality_score'] >= 0.4 else 'low'

cursor.execute("""
    INSERT INTO detection_rules (rule_id, rule_data, quality_label, created_at)
    VALUES (%s, %s, %s, %s)
""", (rule['id'], json.dumps(rule), quality_label, datetime.utcnow()))
```

### Phase 2: Add Delete Endpoints

**File**: `engines/delivery/delivery_engine.py`

```python
async def _api_delete_rule(self, request: web.Request) -> web.Response:
    """Delete rule from database and all SIEMs"""
    try:
        rule_id = request.match_info['rule_id']

        # Check if deployed
        cursor = self.db_connection.cursor()
        cursor.execute("""
            SELECT deployed_to_elastic, elastic_rule_id,
                   deployed_to_splunk, splunk_rule_id
            FROM detection_rules
            WHERE rule_id = %s
        """, (rule_id,))

        row = cursor.fetchone()
        if not row:
            return web.json_response({'error': 'Rule not found'}, status=404)

        deleted_from = []

        # Delete from Elastic if deployed
        if row['deployed_to_elastic']:
            await self._delete_from_elastic(row['elastic_rule_id'])
            deleted_from.append('elastic')

        # Delete from database
        cursor.execute("DELETE FROM detection_rules WHERE rule_id = %s", (rule_id,))
        cursor.execute("DELETE FROM rule_test_cases WHERE rule_id = %s", (rule_id,))
        cursor.execute("DELETE FROM rule_performance WHERE rule_id = %s", (rule_id,))
        self.db_connection.commit()

        return web.json_response({
            'success': True,
            'deleted_rule_id': rule_id,
            'also_deleted_from': deleted_from
        })

    except Exception as e:
        self.logger.error(f"Delete rule error: {e}")
        return web.json_response({'error': str(e)}, status=500)
```

### Phase 3: Add Bulk Operations

```python
async def _api_bulk_delete_rules(self, request: web.Request) -> web.Response:
    """Bulk delete rules with filters"""
    try:
        data = await request.json()
        filters = data.get('filters', {})
        dry_run = data.get('dry_run', True)

        # Build WHERE clause from filters
        where_clauses = []
        params = []

        if 'quality_label' in filters:
            where_clauses.append("quality_label = %s")
            params.append(filters['quality_label'])

        if 'source' in filters:
            where_clauses.append("rule_data->>'source' = %s")
            params.append(filters['source'])

        if 'deployed' in filters:
            where_clauses.append(f"deployed_to_elastic = {filters['deployed']}")

        where_sql = " AND ".join(where_clauses) if where_clauses else "1=1"

        # Get rules to delete
        cursor = self.db_connection.cursor(cursor_factory=RealDictCursor)
        cursor.execute(f"""
            SELECT rule_id, rule_data->>'name' as name
            FROM detection_rules
            WHERE {where_sql}
            LIMIT 1000
        """, tuple(params))

        rules_to_delete = cursor.fetchall()

        if dry_run:
            return web.json_response({
                'dry_run': True,
                'would_delete': len(rules_to_delete),
                'sample_rules': rules_to_delete[:10]
            })
        else:
            # Actually delete
            rule_ids = [r['rule_id'] for r in rules_to_delete]
            cursor.execute(f"""
                DELETE FROM detection_rules
                WHERE {where_sql}
            """, tuple(params))
            self.db_connection.commit()

            return web.json_response({
                'success': True,
                'deleted_count': len(rules_to_delete)
            })

    except Exception as e:
        self.logger.error(f"Bulk delete error: {e}")
        return web.json_response({'error': str(e)}, status=500)
```

---

## Usage Examples

### Scenario 1: Review Low-Quality Rules

```bash
# 1. List low-quality rules
curl http://localhost:8005/api/rules?quality=low&deployed=false

# 2. Review each rule manually
curl http://localhost:8005/api/rules/{rule_id}

# 3. Delete if confirmed bad
curl -X DELETE http://localhost:8005/api/rules/{rule_id}
```

### Scenario 2: Bulk Cleanup

```bash
# 1. Preview what would be deleted
curl -X POST http://localhost:8005/api/rules/bulk-delete \
  -H "Content-Type: application/json" \
  -d '{
    "filters": {
      "quality_label": "low",
      "source": "unknown",
      "deployed": false
    },
    "dry_run": true
  }'

# 2. Review the preview, then execute
curl -X POST http://localhost:8005/api/rules/bulk-delete \
  -H "Content-Type: application/json" \
  -d '{
    "filters": {
      "quality_label": "low",
      "source": "unknown",
      "deployed": false
    },
    "dry_run": false
  }'
```

### Scenario 3: Override Quality Label

```bash
# Mark a "low" quality rule as "reviewed" after manual check
curl -X PATCH http://localhost:8005/api/rules/{rule_id} \
  -H "Content-Type: application/json" \
  -d '{
    "quality_label": "reviewed",
    "notes": "Manually validated - legitimate traffic"
  }'
```

---

## Database Schema Updates

### Add Quality Label Column

```sql
-- Add quality_label column
ALTER TABLE detection_rules
ADD COLUMN quality_label VARCHAR(20),
ADD COLUMN custom_tags TEXT[],
ADD COLUMN notes TEXT,
ADD COLUMN reviewed_by VARCHAR(255),
ADD COLUMN reviewed_at TIMESTAMP;

-- Create indexes
CREATE INDEX idx_quality_label ON detection_rules(quality_label);
CREATE INDEX idx_custom_tags ON detection_rules USING GIN(custom_tags);

-- Auto-populate existing rules
UPDATE detection_rules
SET quality_label = CASE
  WHEN (rule_data->>'quality_score')::float >= 0.7 THEN 'high'
  WHEN (rule_data->>'quality_score')::float >= 0.4 THEN 'medium'
  ELSE 'low'
END;
```

---

## Summary: Label-Based vs Auto-Delete

| Approach | Pros | Cons | Recommendation |
|----------|------|------|----------------|
| **Auto-Delete** | • Automatic cleanup<br>• No user action needed | • Might delete useful rules<br>• No flexibility<br>• Can't review | ❌ Not recommended |
| **Label-Based** | • User control<br>• Audit trail<br>• Flexible filtering<br>• Can review before delete | • Requires user action<br>• Manual cleanup | ✅ **Recommended** |

---

## Next Steps

1. ✅ Document current API (this file)
2. ⏳ Add `quality_label` column to database
3. ⏳ Implement DELETE `/api/rules/{rule_id}` endpoint
4. ⏳ Implement PATCH `/api/rules/{rule_id}` for metadata updates
5. ⏳ Implement POST `/api/rules/bulk-delete` with filters
6. ⏳ Add filtering to GET `/api/rules` endpoint
7. ⏳ Update frontend to show quality labels
8. ⏳ Add UI for bulk delete operations

---

**Document Version**: 1.0
**Last Updated**: 2025-10-03
**Related Docs**: RULE_CREATION_WORKFLOW.md, API_DOCUMENTATION.md
