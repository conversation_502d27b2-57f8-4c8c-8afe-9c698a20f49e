/**
 * Dashboard API Service
 * Handles all dashboard-related API calls
 */

import apiClient from '../client'
import type {
  DashboardStats,
  APIResponse
} from '../../types/api'

export const dashboardService = {
  /**
   * Get dashboard overview statistics
   */
  async getStats(): Promise<DashboardStats> {
    const response = await apiClient.get<APIResponse<DashboardStats>>(
      '/dashboard/stats'
    )
    return response.data.data
  },

  /**
   * Get dashboard overview (legacy endpoint)
   */
  async getOverview(): Promise<DashboardStats> {
    const response = await apiClient.get<APIResponse<DashboardStats>>(
      '/dashboard/overview'
    )
    return response.data.data
  }
}
