"""
Source Update Manager
Keeps log source identification, quality scores, and correlation rules current
"""

import asyncio
import json
import aiohttp
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import hashlib
import logging
from dataclasses import dataclass, asdict
import yaml

logger = logging.getLogger(__name__)


@dataclass
class SourceUpdate:
    """Represents an update to a log source definition"""
    vendor: str
    product: str
    version: str
    quality_score: int
    new_patterns: List[Dict]
    capabilities: List[str]
    mitre_coverage: List[str]
    update_type: str  # 'new', 'update', 'deprecation'
    source: str  # 'cti', 'community', 'manual', 'learned'
    confidence: float


class SourceUpdateManager:
    """
    Manages updates to log source identification patterns and quality scores
    Integrates with multiple update sources:
    1. CTI feeds for new threat detection capabilities
    2. Community updates from GitHub/GitLab
    3. Learning from actual log patterns
    4. Vendor API integrations
    """

    def __init__(self, db_pool = None, redis_client=None):
        self.db_pool = db_pool
        self.redis_client = redis_client

        # Update sources configuration
        self.update_sources = {
            'github': {
                'url': 'https://api.github.com/repos/SIEMLess/source-definitions/contents/sources',
                'type': 'community',
                'frequency': timedelta(days=1),
                'last_check': None
            },
            'vendor_apis': {
                'crowdstrike': {
                    'url': 'https://api.crowdstrike.com/detection-capabilities/v1',
                    'requires_auth': True,
                    'frequency': timedelta(hours=6)
                },
                'elastic': {
                    'url': 'https://artifacts.elastic.co/detection-rules/latest',
                    'requires_auth': False,
                    'frequency': timedelta(days=1)
                }
            },
            'mitre': {
                'url': 'https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json',
                'type': 'cti',
                'frequency': timedelta(days=7),
                'last_check': None
            }
        }

        # Learning configuration
        self.learning_config = {
            'min_samples': 100,  # Minimum samples before creating pattern
            'confidence_threshold': 0.8,
            'pattern_validation': True,
            'auto_approve': False  # Require manual approval for learned patterns
        }

        # Version tracking
        self.pattern_versions = {}
        self.update_history = []

    async def check_for_updates(self) -> List[SourceUpdate]:
        """
        Check all configured sources for updates
        Returns list of available updates
        """
        updates = []

        # Check GitHub community updates
        github_updates = await self._check_github_updates()
        updates.extend(github_updates)

        # Check vendor API updates
        vendor_updates = await self._check_vendor_updates()
        updates.extend(vendor_updates)

        # Check MITRE ATT&CK updates
        mitre_updates = await self._check_mitre_updates()
        updates.extend(mitre_updates)

        # Check for learned patterns
        learned_updates = await self._check_learned_patterns()
        updates.extend(learned_updates)

        # Check product CVE updates (affects quality scores)
        cve_updates = await self._check_cve_impacts()
        updates.extend(cve_updates)

        logger.info(f"Found {len(updates)} available updates")
        return updates

    async def _check_github_updates(self) -> List[SourceUpdate]:
        """Check GitHub for community-contributed source definitions"""
        updates = []

        try:
            async with aiohttp.ClientSession() as session:
                # Fetch source definition files
                async with session.get(self.update_sources['github']['url']) as response:
                    if response.status == 200:
                        files = await response.json()

                        for file in files:
                            if file['name'].endswith('.yaml'):
                                # Download and parse definition file
                                async with session.get(file['download_url']) as file_response:
                                    content = await file_response.text()
                                    definition = yaml.safe_load(content)

                                    # Check if this is new or updated
                                    if await self._is_new_or_updated(definition):
                                        update = SourceUpdate(
                                            vendor=definition['vendor'],
                                            product=definition['product'],
                                            version=definition.get('version', '1.0'),
                                            quality_score=definition.get('quality_score', 5),
                                            new_patterns=definition.get('patterns', []),
                                            capabilities=definition.get('capabilities', []),
                                            mitre_coverage=definition.get('mitre_coverage', []),
                                            update_type='update' if await self._source_exists(definition['vendor'], definition['product']) else 'new',
                                            source='community',
                                            confidence=0.7  # Community contributions have medium confidence
                                        )
                                        updates.append(update)

        except Exception as e:
            logger.error(f"Failed to check GitHub updates: {e}")

        return updates

    async def _check_vendor_updates(self) -> List[SourceUpdate]:
        """Check vendor APIs for capability updates"""
        updates = []

        for vendor, config in self.update_sources['vendor_apis'].items():
            try:
                # Check if we have API credentials
                api_key = await self._get_vendor_api_key(vendor)
                if config['requires_auth'] and not api_key:
                    continue

                async with aiohttp.ClientSession() as session:
                    headers = {'Authorization': f'Bearer {api_key}'} if api_key else {}

                    async with session.get(config['url'], headers=headers) as response:
                        if response.status == 200:
                            data = await response.json()

                            # Parse vendor-specific format
                            if vendor == 'crowdstrike':
                                updates.extend(await self._parse_crowdstrike_updates(data))
                            elif vendor == 'elastic':
                                updates.extend(await self._parse_elastic_updates(data))

            except Exception as e:
                logger.error(f"Failed to check {vendor} updates: {e}")

        return updates

    async def _check_mitre_updates(self) -> List[SourceUpdate]:
        """Check MITRE ATT&CK for technique coverage updates"""
        updates = []

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.update_sources['mitre']['url']) as response:
                    if response.status == 200:
                        data = await response.json()

                        # Extract technique mappings for known products
                        techniques_by_product = await self._extract_mitre_mappings(data)

                        for product, techniques in techniques_by_product.items():
                            current_coverage = await self._get_current_mitre_coverage(product)
                            new_techniques = set(techniques) - set(current_coverage)

                            if new_techniques:
                                vendor, product_name = product.split(':', 1)
                                update = SourceUpdate(
                                    vendor=vendor,
                                    product=product_name,
                                    version='latest',
                                    quality_score=0,  # Don't change quality
                                    new_patterns=[],
                                    capabilities=[],
                                    mitre_coverage=list(new_techniques),
                                    update_type='update',
                                    source='cti',
                                    confidence=0.9
                                )
                                updates.append(update)

        except Exception as e:
            logger.error(f"Failed to check MITRE updates: {e}")

        return updates

    async def _check_learned_patterns(self) -> List[SourceUpdate]:
        """
        Check for patterns learned from actual log processing
        This is where the system self-improves by analyzing unidentified logs
        """
        updates = []

        if not self.db_pool:
            return updates

        try:
            async with self.db_pool.acquire() as conn:
                # Find frequently seen but unidentified log patterns
                unidentified_patterns = await conn.fetch("""
                    SELECT
                        COUNT(*) as occurrence_count,
                        source_type,
                        jsonb_object_keys(log_data) as common_fields,
                        substring(log_data::text from 1 for 100) as sample
                    FROM ingestion_logs
                    WHERE source_vendor = 'unknown'
                    AND created_at > NOW() - INTERVAL '7 days'
                    GROUP BY source_type, common_fields, sample
                    HAVING COUNT(*) > $1
                    ORDER BY occurrence_count DESC
                    LIMIT 20
                """, self.learning_config['min_samples'])

                for pattern in unidentified_patterns:
                    # Analyze pattern to identify potential vendor/product
                    identification = await self._analyze_unknown_pattern(
                        pattern['sample'],
                        pattern['common_fields'],
                        pattern['occurrence_count']
                    )

                    if identification and identification['confidence'] >= self.learning_config['confidence_threshold']:
                        update = SourceUpdate(
                            vendor=identification['vendor'],
                            product=identification['product'],
                            version='learned',
                            quality_score=identification.get('quality_score', 5),
                            new_patterns=[{
                                'type': 'field_match',
                                'pattern': pattern['common_fields'],
                                'sample': pattern['sample']
                            }],
                            capabilities=identification.get('capabilities', []),
                            mitre_coverage=[],
                            update_type='new',
                            source='learned',
                            confidence=identification['confidence']
                        )
                        updates.append(update)

        except Exception as e:
            logger.error(f"Failed to check learned patterns: {e}")

        return updates

    async def _check_cve_impacts(self) -> List[SourceUpdate]:
        """
        Check for CVEs that might affect product quality scores
        Security products with critical vulnerabilities should have reduced scores
        """
        updates = []

        try:
            # Check NVD or other CVE feeds
            cve_data = await self._fetch_recent_cves()

            for cve in cve_data:
                affected_products = await self._identify_affected_products(cve)

                for product in affected_products:
                    if cve['severity'] in ['CRITICAL', 'HIGH']:
                        # Reduce quality score for products with critical vulnerabilities
                        update = SourceUpdate(
                            vendor=product['vendor'],
                            product=product['product'],
                            version=product['version'],
                            quality_score=max(1, product['current_score'] - 2),  # Reduce by 2 points
                            new_patterns=[],
                            capabilities=[],
                            mitre_coverage=[],
                            update_type='update',
                            source='cve',
                            confidence=1.0
                        )
                        updates.append(update)

        except Exception as e:
            logger.error(f"Failed to check CVE impacts: {e}")

        return updates

    async def apply_updates(self, updates: List[SourceUpdate], auto_apply: bool = False) -> Dict[str, Any]:
        """
        Apply approved updates to the system

        Args:
            updates: List of updates to apply
            auto_apply: If True, apply without manual approval

        Returns:
            Summary of applied updates
        """
        results = {
            'applied': [],
            'pending': [],
            'failed': []
        }

        for update in updates:
            try:
                # Check if auto-apply is allowed for this update type
                if auto_apply or update.source in ['cti', 'vendor']:
                    success = await self._apply_single_update(update)
                    if success:
                        results['applied'].append(update)
                        await self._record_update_history(update, 'applied')
                    else:
                        results['failed'].append(update)
                else:
                    # Requires manual approval
                    await self._queue_for_approval(update)
                    results['pending'].append(update)

            except Exception as e:
                logger.error(f"Failed to apply update for {update.vendor} {update.product}: {e}")
                results['failed'].append(update)

        # Notify about results
        await self._send_update_notification(results)

        return results

    async def _apply_single_update(self, update: SourceUpdate) -> bool:
        """Apply a single update to the database"""
        if not self.db_pool:
            return False

        try:
            async with self.db_pool.acquire() as conn:
                # Update or insert log source registry
                if update.update_type == 'new':
                    await conn.execute("""
                        INSERT INTO log_source_registry (
                            source_identifier, source_type, vendor, product,
                            quality_score, quality_tier, detection_capabilities,
                            mitre_coverage, version
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                    """,
                        f"{update.vendor.lower()}-{update.product.lower()}-auto",
                        self._determine_source_type(update.vendor, update.product),
                        update.vendor,
                        update.product,
                        update.quality_score,
                        self._get_quality_tier(update.quality_score),
                        update.capabilities,
                        update.mitre_coverage,
                        update.version
                    )
                else:
                    # Update existing source
                    await conn.execute("""
                        UPDATE log_source_registry
                        SET quality_score = COALESCE($1, quality_score),
                            detection_capabilities = COALESCE($2, detection_capabilities),
                            mitre_coverage = COALESCE($3, mitre_coverage),
                            version = $4,
                            updated_at = NOW()
                        WHERE vendor = $5 AND product = $6
                    """,
                        update.quality_score if update.quality_score > 0 else None,
                        update.capabilities if update.capabilities else None,
                        update.mitre_coverage if update.mitre_coverage else None,
                        update.version,
                        update.vendor,
                        update.product
                    )

                # Add new patterns
                for pattern in update.new_patterns:
                    pattern_name = f"{update.vendor}_{update.product}_{hashlib.md5(json.dumps(pattern).encode()).hexdigest()[:8]}"

                    await conn.execute("""
                        INSERT INTO source_identification_patterns (
                            pattern_name, pattern_type, pattern_value,
                            identifies_vendor, identifies_product, identifies_type,
                            confidence_score
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                        ON CONFLICT (pattern_name) DO UPDATE
                        SET pattern_value = EXCLUDED.pattern_value,
                            confidence_score = EXCLUDED.confidence_score
                    """,
                        pattern_name,
                        pattern.get('type', 'regex'),
                        pattern.get('pattern'),
                        update.vendor,
                        update.product,
                        self._determine_source_type(update.vendor, update.product),
                        update.confidence
                    )

            # Update Redis cache
            if self.redis_client:
                cache_key = f"source_quality:{update.vendor}:{update.product}"
                cache_data = {
                    'quality_score': update.quality_score,
                    'capabilities': update.capabilities,
                    'mitre_coverage': update.mitre_coverage,
                    'version': update.version,
                    'updated_at': datetime.utcnow().isoformat()
                }
                self.redis_client.setex(
                    cache_key,
                    3600,  # 1 hour TTL
                    json.dumps(cache_data)
                )

            logger.info(f"Successfully applied update for {update.vendor} {update.product}")
            return True

        except Exception as e:
            logger.error(f"Failed to apply update: {e}")
            return False

    async def _analyze_unknown_pattern(self, sample: str, fields: str, count: int) -> Optional[Dict]:
        """
        Analyze unknown log pattern to identify potential vendor/product
        Uses heuristics and field analysis
        """
        identification = {
            'vendor': 'unknown',
            'product': 'unknown',
            'confidence': 0.0,
            'quality_score': 5
        }

        # Common field patterns for different vendors
        field_signatures = {
            'microsoft': ['EventID', 'Provider', 'Computer', 'SecurityID'],
            'cisco': ['mnemonic', 'facility', 'severity', 'message_id'],
            'fortinet': ['devid', 'devname', 'logid', 'vd'],
            'paloalto': ['serial', 'type', 'subtype', 'config_ver'],
            'checkpoint': ['blade', 'origin', 'policy_name', 'rule_uid'],
            'sophos': ['device_id', 'device_name', 'threat_type', 'detection_identity'],
            'symantec': ['event_id', 'severity_level', 'device_hostname', 'signature_id']
        }

        # Check field signatures
        for vendor, signature_fields in field_signatures.items():
            match_count = sum(1 for field in signature_fields if field.lower() in fields.lower())
            if match_count >= 2:  # At least 2 matching fields
                identification['vendor'] = vendor
                identification['confidence'] = match_count / len(signature_fields)

                # Try to determine product
                if vendor == 'microsoft':
                    if 'Sysmon' in sample:
                        identification['product'] = 'Sysmon'
                        identification['quality_score'] = 7
                    elif 'Defender' in sample:
                        identification['product'] = 'Defender'
                        identification['quality_score'] = 8
                elif vendor == 'fortinet':
                    identification['product'] = 'FortiGate'
                    identification['quality_score'] = 7

                break

        # If we have high occurrence, increase confidence
        if count > 1000:
            identification['confidence'] = min(1.0, identification['confidence'] * 1.2)

        return identification if identification['confidence'] > 0 else None

    async def _queue_for_approval(self, update: SourceUpdate):
        """Queue update for manual approval"""
        if not self.db_pool:
            return

        async with self.db_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO update_approval_queue (
                    vendor, product, update_data, source, confidence,
                    status, created_at
                ) VALUES ($1, $2, $3, $4, $5, 'pending', NOW())
            """,
                update.vendor,
                update.product,
                json.dumps(asdict(update)),
                update.source,
                update.confidence
            )

    async def _record_update_history(self, update: SourceUpdate, status: str):
        """Record update in history for audit trail"""
        if not self.db_pool:
            return

        async with self.db_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO source_update_history (
                    vendor, product, version, update_type, source,
                    status, applied_at, update_data
                ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), $7)
            """,
                update.vendor,
                update.product,
                update.version,
                update.update_type,
                update.source,
                status,
                json.dumps(asdict(update))
            )

    async def _send_update_notification(self, results: Dict[str, Any]):
        """Send notification about update results"""
        if self.redis_client:
            notification = {
                'type': 'source_updates',
                'timestamp': datetime.utcnow().isoformat(),
                'summary': {
                    'applied': len(results['applied']),
                    'pending': len(results['pending']),
                    'failed': len(results['failed'])
                },
                'details': results
            }

            await self.redis_client.publish(
                'backend.source_updates',
                json.dumps(notification)
            )

    def _determine_source_type(self, vendor: str, product: str) -> str:
        """Determine source type based on vendor and product"""
        type_mapping = {
            ('crowdstrike', 'falcon'): 'edr',
            ('sentinelone', 'singularity'): 'edr',
            ('microsoft', 'defender'): 'edr',
            ('microsoft', 'sysmon'): 'host_monitoring',
            ('paloalto', 'pan-os'): 'firewall',
            ('fortinet', 'fortigate'): 'firewall',
            ('cisco', 'asa'): 'firewall',
            ('wazuh', 'wazuh'): 'hids',
            ('elastic', 'security'): 'siem',
            ('splunk', 'enterprise'): 'siem'
        }

        key = (vendor.lower(), product.lower())
        return type_mapping.get(key, 'unknown')

    def _get_quality_tier(self, score: int) -> str:
        """Convert quality score to tier"""
        if score >= 9:
            return 'premium'
        elif score >= 7:
            return 'good'
        elif score >= 5:
            return 'basic'
        else:
            return 'minimal'

    async def _get_vendor_api_key(self, vendor: str) -> Optional[str]:
        """Retrieve API key for vendor from secure storage"""
        # In production, this would retrieve from secure vault
        # For now, check environment variables
        import os
        return os.getenv(f"{vendor.upper()}_API_KEY")

    async def _source_exists(self, vendor: str, product: str) -> bool:
        """Check if source already exists in registry"""
        if not self.db_pool:
            return False

        async with self.db_pool.acquire() as conn:
            result = await conn.fetchval("""
                SELECT EXISTS(
                    SELECT 1 FROM log_source_registry
                    WHERE vendor = $1 AND product = $2
                )
            """, vendor, product)

        return result

    async def _get_current_mitre_coverage(self, product: str) -> List[str]:
        """Get current MITRE technique coverage for a product"""
        if not self.db_pool:
            return []

        vendor, product_name = product.split(':', 1)

        async with self.db_pool.acquire() as conn:
            result = await conn.fetchval("""
                SELECT mitre_coverage
                FROM log_source_registry
                WHERE vendor = $1 AND product = $2
            """, vendor, product_name)

        return result or []

    async def _is_new_or_updated(self, definition: Dict) -> bool:
        """Check if definition is new or has updates"""
        if not self.db_pool:
            return True

        # Calculate hash of definition
        definition_hash = hashlib.sha256(
            json.dumps(definition, sort_keys=True).encode()
        ).hexdigest()

        async with self.db_pool.acquire() as conn:
            # Check if we've seen this exact definition before
            exists = await conn.fetchval("""
                SELECT EXISTS(
                    SELECT 1 FROM source_definition_hashes
                    WHERE definition_hash = $1
                )
            """, definition_hash)

            if not exists:
                # Store the hash
                await conn.execute("""
                    INSERT INTO source_definition_hashes (
                        vendor, product, definition_hash, created_at
                    ) VALUES ($1, $2, $3, NOW())
                    ON CONFLICT (vendor, product) DO UPDATE
                    SET definition_hash = EXCLUDED.definition_hash
                """, definition['vendor'], definition['product'], definition_hash)

                return True

        return False

    async def _fetch_recent_cves(self) -> List[Dict]:
        """Fetch recent CVEs from NVD or other sources"""
        # Simplified implementation - in production would use NVD API
        return []

    async def _identify_affected_products(self, cve: Dict) -> List[Dict]:
        """Identify which products in our registry are affected by a CVE"""
        # Simplified implementation
        return []

    async def _parse_crowdstrike_updates(self, data: Dict) -> List[SourceUpdate]:
        """Parse CrowdStrike API response for updates"""
        # Implementation specific to CrowdStrike API format
        return []

    async def _parse_elastic_updates(self, data: Dict) -> List[SourceUpdate]:
        """Parse Elastic detection rules for updates"""
        # Implementation specific to Elastic format
        return []

    async def _extract_mitre_mappings(self, data: Dict) -> Dict[str, List[str]]:
        """Extract MITRE technique mappings from ATT&CK data"""
        # Parse MITRE ATT&CK JSON for technique/product mappings
        return {}