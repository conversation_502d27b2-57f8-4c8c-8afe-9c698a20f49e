/**
 * <PERSON>ert Queue Widget - Shows incoming alerts with enrichment/correlation status
 */

import React, { useState, useEffect } from 'react';
import { Alert, EnrichmentStatus, CorrelationStatus } from '../types/investigation';
import { AlertService } from '../services/alertService';
import '../styles/AlertQueue.css';

interface AlertQueueWidgetProps {
  onAlertClick?: (alertId: string) => void;
  autoRefresh?: boolean;
  refreshInterval?: number; // milliseconds
}

export const AlertQueueWidget: React.FC<AlertQueueWidgetProps> = ({
  onAlertClick,
  autoRefresh = true,
  refreshInterval = 30000 // 30 seconds
}) => {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'critical' | 'high' | 'medium' | 'low'>('all');

  useEffect(() => {
    loadAlerts();

    if (autoRefresh) {
      const interval = setInterval(loadAlerts, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const loadAlerts = async () => {
    try {
      setLoading(true);
      const fetchedAlerts = await AlertService.listAlerts({ status: 'open', limit: 50 });
      setAlerts(fetchedAlerts);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load alerts');
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string): string => {
    switch (severity.toLowerCase()) {
      case 'critical': return '#dc2626';
      case 'high': return '#ea580c';
      case 'medium': return '#ca8a04';
      case 'low': return '#65a30d';
      default: return '#6b7280';
    }
  };

  const getSeverityIcon = (severity: string): string => {
    switch (severity.toLowerCase()) {
      case 'critical': return '🔴';
      case 'high': return '🟠';
      case 'medium': return '🟡';
      case 'low': return '🟢';
      default: return '⚪';
    }
  };

  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const then = new Date(timestamp);
    const diffMs = now.getTime() - then.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
  };

  const renderEnrichmentStatus = (status?: EnrichmentStatus) => {
    if (!status) {
      return (
        <div className="status-line">
          <span className="status-icon">🔍</span>
          <span className="status-text">Not enriched</span>
        </div>
      );
    }

    switch (status.status) {
      case 'completed':
        return (
          <div className="status-line status-completed">
            <span className="status-icon">🔍</span>
            <span className="status-text">
              Enriched ({status.enriched_count}/{status.total_entities} entities)
            </span>
            {status.threat_indicators_found > 0 && (
              <span className="threat-badge">{status.threat_indicators_found} threats</span>
            )}
          </div>
        );
      case 'in_progress':
        return (
          <div className="status-line status-in-progress">
            <span className="status-icon spinner">⏳</span>
            <span className="status-text">
              Enriching... ({status.enriched_count}/{status.total_entities} entities)
            </span>
          </div>
        );
      default:
        return (
          <div className="status-line">
            <span className="status-icon">🔍</span>
            <span className="status-text">Not enriched</span>
          </div>
        );
    }
  };

  const renderCorrelationStatus = (status?: CorrelationStatus) => {
    if (!status) {
      return (
        <div className="status-line">
          <span className="status-icon">🔗</span>
          <span className="status-text">Not correlated</span>
        </div>
      );
    }

    switch (status.status) {
      case 'completed':
        return (
          <div className="status-line status-completed">
            <span className="status-icon">🔗</span>
            <span className="status-text">
              Correlated ({status.total_related_events} related events)
            </span>
            {status.attack_stages_detected.length > 0 && (
              <span className="stage-count">
                {status.attack_stages_detected.length} stages
              </span>
            )}
          </div>
        );
      case 'in_progress':
        return (
          <div className="status-line status-in-progress">
            <span className="status-icon spinner">⏳</span>
            <span className="status-text">Correlating... (searching 30min window)</span>
          </div>
        );
      default:
        return (
          <div className="status-line">
            <span className="status-icon">🔗</span>
            <span className="status-text">Not correlated</span>
          </div>
        );
    }
  };

  const renderMitreTechniques = (techniques?: string[]) => {
    if (!techniques || techniques.length === 0) return null;

    return (
      <div className="status-line">
        <span className="status-icon">⚔️</span>
        <span className="status-text">MITRE: {techniques.join(', ')}</span>
      </div>
    );
  };

  const renderCorrelationScore = (score?: number) => {
    if (score === undefined) return null;

    const level = score >= 0.7 ? 'HIGH' : score >= 0.4 ? 'MEDIUM' : 'LOW';
    const color = score >= 0.7 ? '#dc2626' : score >= 0.4 ? '#ea580c' : '#65a30d';

    return (
      <div className="status-line">
        <span className="status-icon">📊</span>
        <span className="status-text">
          Correlation Score: {score.toFixed(2)} ({level})
        </span>
      </div>
    );
  };

  const filteredAlerts = filter === 'all'
    ? alerts
    : alerts.filter(a => a.severity === filter);

  if (loading && alerts.length === 0) {
    return (
      <div className="alert-queue-widget">
        <div className="widget-header">
          <h2>🚨 Alert Queue</h2>
        </div>
        <div className="widget-body">
          <div className="loading">Loading alerts...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert-queue-widget">
        <div className="widget-header">
          <h2>🚨 Alert Queue</h2>
        </div>
        <div className="widget-body">
          <div className="error">Error: {error}</div>
          <button onClick={loadAlerts} className="retry-button">Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div className="alert-queue-widget">
      <div className="widget-header">
        <h2>🚨 Alert Queue</h2>
        <div className="header-controls">
          <label className="auto-refresh">
            <input type="checkbox" checked={autoRefresh} readOnly />
            Auto-refresh
          </label>
          <button onClick={loadAlerts} className="refresh-button" title="Refresh now">
            🔄
          </button>
        </div>
      </div>

      <div className="widget-filters">
        <button
          className={filter === 'all' ? 'active' : ''}
          onClick={() => setFilter('all')}
        >
          All ({alerts.length})
        </button>
        <button
          className={filter === 'critical' ? 'active' : ''}
          onClick={() => setFilter('critical')}
        >
          Critical ({alerts.filter(a => a.severity === 'critical').length})
        </button>
        <button
          className={filter === 'high' ? 'active' : ''}
          onClick={() => setFilter('high')}
        >
          High ({alerts.filter(a => a.severity === 'high').length})
        </button>
        <button
          className={filter === 'medium' ? 'active' : ''}
          onClick={() => setFilter('medium')}
        >
          Medium ({alerts.filter(a => a.severity === 'medium').length})
        </button>
      </div>

      <div className="widget-body">
        {filteredAlerts.length === 0 ? (
          <div className="no-alerts">No alerts to display</div>
        ) : (
          <div className="alert-list">
            {filteredAlerts.map(alert => (
              <div
                key={alert.alert_id}
                className="alert-card"
                style={{ borderLeft: `4px solid ${getSeverityColor(alert.severity)}` }}
              >
                <div className="alert-header">
                  <span className="severity-badge" style={{ color: getSeverityColor(alert.severity) }}>
                    {getSeverityIcon(alert.severity)} {alert.severity.toUpperCase()}
                  </span>
                  <span className="alert-time">{formatTimeAgo(alert.timestamp)}</span>
                </div>

                <div className="alert-title">{alert.title}</div>

                <div className="alert-summary">
                  {alert.entities.ip && alert.entities.ip[0]}
                  {alert.entities.user && ` → ${alert.entities.user[0]}`}
                  {alert.entities.host && alert.entities.host.length > 1 && ` → ${alert.entities.host.length} hosts`}
                </div>

                <div className="alert-status">
                  {renderEnrichmentStatus(alert.enrichment_status)}
                  {renderCorrelationStatus(alert.correlation_status)}
                  {renderMitreTechniques(alert.mitre_techniques)}
                  {renderCorrelationScore(alert.correlation_score)}
                </div>

                <div className="alert-actions">
                  <button
                    className="view-button"
                    onClick={() => onAlertClick?.(alert.alert_id)}
                  >
                    View Investigation →
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AlertQueueWidget;
