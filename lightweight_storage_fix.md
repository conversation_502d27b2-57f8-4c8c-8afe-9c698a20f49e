# Lightweight Storage Architecture Fix

## Problem
Currently storing 45,832 full logs (447 MB) instead of extracted intelligence.
Only 30 entities extracted from 45,832 logs = 99.93% waste!

## Current Flow (WRONG)
```
Log → Ingestion → Pattern Match → Backend (stores FULL LOG) + Contextualization (extracts entities)
                                    ↓                           ↓
                              447 MB stored              30 entities (should be 10,000+)
```

## Correct Lightweight Flow
```
Log → Ingestion → Pattern Match → Contextualization ONLY
                                    ↓
                              Extract & Store:
                              - Entities (IPs, users, hosts, domains)
                              - Relationships (who→where→when)
                              - Sessions (grouped activity)
                              - Events (security-relevant only)
                              - Metadata (pattern_id, timestamp, source)
                                    ↓
                              ~5 MB instead of 447 MB
```

## Implementation Changes Needed

### 1. Stop Backend from Storing Full Logs

In `engines/backend/backend_engine.py`, change `_handle_store_processed_log`:

```python
async def _handle_store_processed_log(self, data: Dict[str, Any]):
    """Store only metadata, not full logs"""

    # Extract only essential metadata
    metadata = {
        'log_id': str(uuid.uuid4()),
        'source_type': data.get('source_type'),
        'pattern_id': data.get('pattern_id'),
        'timestamp': datetime.utcnow(),
        'entity_count': len(data.get('entities', [])),
        'severity': data.get('severity', 'info')
    }

    # Store in lightweight metadata table (create new table)
    cursor.execute("""
        INSERT INTO log_metadata
        (log_id, source_type, pattern_id, timestamp, entity_count, severity)
        VALUES (%s, %s, %s, %s, %s, %s)
    """, (...))

    # DO NOT store full log_data!
```

### 2. Fix Contextualization to Extract More Entities

The contextualization engine is only extracting 30 entities because:
- It's not being called for all logs
- Entity extraction logic is too restrictive

In `engines/contextualization/contextualization_engine.py`:

```python
async def _handle_process_log(self, data: Dict[str, Any]):
    """Extract ALL entities from log"""

    # More aggressive extraction
    entities_to_extract = [
        # IPs
        (r'\b(?:\d{1,3}\.){3}\d{1,3}\b', 'ip_address'),
        # Hostnames
        (r'[a-zA-Z0-9\-]{2,63}(?:\.[a-zA-Z0-9\-]{2,63})+', 'domain'),
        # Users
        (r'(?:user|username|account)["\s:]+([a-zA-Z0-9\.\-_]+)', 'username'),
        # Files
        (r'(?:[A-Z]:\\|/)[^\s"]+', 'file_path'),
        # Ports
        (r'(?:port|:)(\d{1,5})', 'port'),
    ]

    # Extract and store entities
    for pattern, entity_type in entities_to_extract:
        matches = re.findall(pattern, log_str)
        for match in matches:
            self._store_entity(entity_type, match, log_id)

    # Create relationships between entities in same log
    self._create_relationships(extracted_entities, log_id)
```

### 3. Change Routing Logic

In `engines/ingestion/log_router.py`:

```python
# REMOVE backend from most routes
self.use_case_routes = {
    'authentication': ['contextualization', 'intelligence'],  # No backend!
    'system': ['contextualization'],  # No backend!
    'traffic': ['contextualization'],  # No backend!

    # Only store full logs for critical security events
    'malware': ['contextualization', 'intelligence', 'backend'],
    'threat_detection': ['contextualization', 'intelligence', 'backend'],
}
```

### 4. Create Lightweight Storage Schema

```sql
-- Lightweight metadata only
CREATE TABLE log_metadata (
    log_id UUID PRIMARY KEY,
    source_type VARCHAR(50),
    pattern_id VARCHAR(100),
    timestamp TIMESTAMP,
    entity_count INTEGER,
    relationship_count INTEGER,
    severity VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Entity occurrences (when/where entities were seen)
CREATE TABLE entity_occurrences (
    occurrence_id UUID PRIMARY KEY,
    entity_id UUID REFERENCES entities(entity_id),
    log_metadata_id UUID REFERENCES log_metadata(log_id),
    context JSONB,  -- Small context, not full log
    occurred_at TIMESTAMP
);

-- Security events (derived from logs)
CREATE TABLE security_events (
    event_id UUID PRIMARY KEY,
    event_type VARCHAR(100),  -- lateral_movement, data_theft, etc
    severity VARCHAR(20),
    entities JSONB,  -- List of involved entities
    metadata JSONB,  -- Small metadata, not full log
    detected_at TIMESTAMP
);
```

## Expected Results After Fix

### Before (Current)
- **Storage**: 447 MB for 45,832 logs
- **Entities**: 30 (0.06% extraction rate)
- **Relationships**: 0
- **Value**: Almost none - just storing raw data

### After (Fixed)
- **Storage**: ~5-10 MB metadata + entities
- **Entities**: 5,000-10,000 (10-20% extraction rate)
- **Relationships**: 1,000+ connections
- **Sessions**: 100+ user/network sessions
- **Events**: 500+ security-relevant events
- **Value**: High - actual intelligence, not raw data

## Benefits

1. **95% Storage Reduction**: 447 MB → 10 MB
2. **100x More Intelligence**: 30 entities → 5,000+ entities
3. **Actual Relationships**: 0 → 1,000+ connections
4. **Faster Queries**: Query entities, not massive JSONB logs
5. **True Lightweight**: As originally designed

## Testing the Fix

```python
# After implementing changes, test with:
python test_lightweight_extraction.py

# Should see:
# - Entities extracted: 5000+
# - Relationships created: 1000+
# - Storage used: <10 MB
# - No full logs in ingestion_logs
```