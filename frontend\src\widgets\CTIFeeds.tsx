import React from 'react'
import { Rss, AlertTriangle, Activity, TrendingUp } from 'lucide-react'

export const CTIFeeds: React.FC = () => {
  const feeds = [
    { name: 'AlienVault OTX', status: 'active', indicators: 2456, lastUpdate: '2 min ago' },
    { name: 'MISP Feed', status: 'active', indicators: 1823, lastUpdate: '5 min ago' },
    { name: 'ThreatFox', status: 'updating', indicators: 945, lastUpdate: '10 min ago' },
    { name: 'Custom Intel', status: 'active', indicators: 523, lastUpdate: '1 hour ago' }
  ]

  return (
    <div className="flex flex-col h-full bg-white p-4">
      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
        <Rss size={20} />
        CTI Feeds
      </h3>

      <div className="space-y-3">
        {feeds.map((feed, idx) => (
          <div key={idx} className="p-3 border rounded-lg hover:bg-gray-50">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="font-medium">{feed.name}</h4>
                <p className="text-sm text-gray-600">{feed.indicators} indicators</p>
              </div>
              <div className="text-right">
                <span className={`inline-flex items-center gap-1 px-2 py-1 rounded text-xs ${
                  feed.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                }`}>
                  <Activity size={12} />
                  {feed.status}
                </span>
                <p className="text-xs text-gray-500 mt-1">{feed.lastUpdate}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-auto pt-4 border-t">
        <div className="flex justify-between text-sm">
          <span>Total Indicators: 5,747</span>
          <span className="flex items-center gap-1 text-green-600">
            <TrendingUp size={14} />
            +234 today
          </span>
        </div>
      </div>
    </div>
  )
}

export default CTIFeeds