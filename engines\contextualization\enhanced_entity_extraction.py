"""
Enhanced entity extraction for contextualization engine
Extracts ALL entities, relationships, and creates sessions/events
"""

import re
import json
import ipaddress
import hashlib
from typing import Dict, List, Set, Tuple, Any
from datetime import datetime, timedelta
import logging

class EnhancedEntityExtractor:
    """Extract maximum intelligence from logs"""

    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or logging.getLogger(__name__)

        # Comprehensive extraction patterns
        self.extraction_patterns = {
            'ip_address': [
                (r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b', 'ip'),
            ],
            'hostname': [
                (r'(?:host|hostname|computer|machine|server|workstation)[\s":]+([a-zA-Z0-9\-\.]+)', 'hostname'),
                (r'(?:source|src|destination|dest)[\s":]+([a-zA-Z0-9\-\.]+)', 'hostname'),
                (r'([a-zA-Z0-9\-]+(?:\.[a-zA-Z0-9\-]+)*\.(?:com|org|net|io|local|internal))', 'domain'),
            ],
            'username': [
                (r'(?:user|username|account|login|logon|subject)[\s":]+([a-zA-Z0-9\.\-_@]+)', 'username'),
                (r'(?:by|from)\s+user\s+([a-zA-Z0-9\.\-_]+)', 'username'),
                (r'\\([a-zA-Z0-9\.\-_]+)@', 'username'),  # domain\user format
            ],
            'email': [
                (r'\b([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\b', 'email'),
            ],
            'file_path': [
                (r'(?:[A-Z]:\\|/)[^\s"<>|]+', 'file_path'),
                (r'(?:file|path|location)[\s":]+([^\s"<>|]+)', 'file_path'),
            ],
            'process': [
                (r'(?:process|proc|exe|binary|command)[\s":]+([^\s"]+\.exe)', 'process'),
                (r'\\([^\\]+\.(?:exe|dll|sys|bat|ps1|sh))', 'process'),
            ],
            'port': [
                (r'(?:port|sport|dport)[\s":]+(\d{1,5})', 'port'),
                (r':(\d{1,5})\s', 'port'),  # IP:port format
            ],
            'hash': [
                (r'\b([a-fA-F0-9]{32})\b', 'md5'),
                (r'\b([a-fA-F0-9]{40})\b', 'sha1'),
                (r'\b([a-fA-F0-9]{64})\b', 'sha256'),
            ],
            'url': [
                (r'(https?://[^\s<>"{}|\\^`\[\]]+)', 'url'),
                (r'(?:url|uri|link)[\s":]+([^\s"<>]+)', 'url'),
            ],
            'registry': [
                (r'(HKEY_[A-Z_]+\\[^\s"]+)', 'registry_key'),
                (r'(?:registry|reg)[\s":]+([^\s"]+)', 'registry_key'),
            ],
            'command': [
                (r'(?:cmd|command|commandline)[\s":]+([^\n]+)', 'command'),
                (r'(?:powershell|cmd\.exe|bash|sh)\s+(.+)', 'command'),
            ]
        }

    def extract_all_entities(self, log_data: Dict[str, Any]) -> Dict[str, List[Dict]]:
        """Extract all possible entities from a log"""
        entities = {
            'extracted': [],
            'relationships': [],
            'context': {}
        }

        # Convert log to string for pattern matching
        log_str = json.dumps(log_data).lower()

        # Extract using all patterns
        seen_entities = set()  # Dedup

        for entity_type, patterns in self.extraction_patterns.items():
            for pattern, label in patterns:
                try:
                    matches = re.findall(pattern, log_str, re.IGNORECASE)
                    for match in matches:
                        if isinstance(match, tuple):
                            match = match[0]

                        # Normalize and validate
                        match = str(match).strip()
                        if not match or len(match) < 2:
                            continue

                        # Create unique key for dedup
                        entity_key = f"{label}:{match.lower()}"
                        if entity_key not in seen_entities:
                            seen_entities.add(entity_key)

                            entity = {
                                'type': label,
                                'value': match,
                                'confidence': self._calculate_confidence(label, match),
                                'extracted_at': datetime.utcnow().isoformat()
                            }

                            # Add context for certain types
                            if label == 'ip':
                                entity['context'] = self._get_ip_context(match)
                            elif label == 'username':
                                entity['context'] = self._get_user_context(match)
                            elif label == 'process':
                                entity['context'] = self._get_process_context(match)

                            entities['extracted'].append(entity)

                except Exception as e:
                    self.logger.debug(f"Pattern extraction error: {e}")

        # Extract relationships between entities
        entities['relationships'] = self._extract_relationships(
            entities['extracted'], log_data
        )

        # Add log context
        entities['context'] = self._extract_log_context(log_data)

        return entities

    def _calculate_confidence(self, entity_type: str, value: str) -> float:
        """Calculate confidence score for extracted entity"""
        confidence = 0.5  # Base confidence

        # Adjust based on type and pattern
        if entity_type == 'ip':
            try:
                ip = ipaddress.ip_address(value)
                confidence = 0.9 if not ip.is_private else 0.8
            except:
                confidence = 0.3

        elif entity_type == 'username':
            # Service accounts are high confidence
            if value.startswith('svc_') or value.startswith('service'):
                confidence = 0.9
            elif '@' in value or '\\' in value:
                confidence = 0.8
            else:
                confidence = 0.6

        elif entity_type in ['md5', 'sha1', 'sha256']:
            confidence = 0.95  # Hashes are definitive

        elif entity_type == 'process':
            # Known processes get higher confidence
            known_processes = ['svchost.exe', 'chrome.exe', 'firefox.exe', 'explorer.exe']
            if any(proc in value.lower() for proc in known_processes):
                confidence = 0.8

        return confidence

    def _get_ip_context(self, ip_str: str) -> Dict:
        """Get context for IP address"""
        context = {'type': 'unknown'}

        try:
            ip = ipaddress.ip_address(ip_str)

            if ip.is_private:
                context['type'] = 'internal'
                if ip_str.startswith('10.'):
                    context['network'] = 'corporate'
                elif ip_str.startswith('192.168.'):
                    context['network'] = 'local'
                elif ip_str.startswith('172.'):
                    context['network'] = 'docker/cloud'
            else:
                context['type'] = 'external'
                context['network'] = 'internet'

            context['is_multicast'] = ip.is_multicast
            context['is_loopback'] = ip.is_loopback

        except:
            pass

        return context

    def _get_user_context(self, username: str) -> Dict:
        """Get context for username"""
        context = {}

        # Identify user type
        if username.startswith('svc_') or username.startswith('service'):
            context['type'] = 'service_account'
            context['risk'] = 'high'  # Service accounts are high value targets
        elif username.lower() in ['admin', 'administrator', 'root']:
            context['type'] = 'admin'
            context['risk'] = 'critical'
        elif username.endswith('$'):
            context['type'] = 'machine_account'
            context['risk'] = 'medium'
        else:
            context['type'] = 'user'
            context['risk'] = 'normal'

        return context

    def _get_process_context(self, process: str) -> Dict:
        """Get context for process"""
        context = {}

        process_lower = process.lower()

        # Categorize process
        if any(browser in process_lower for browser in ['chrome', 'firefox', 'edge', 'safari']):
            context['category'] = 'browser'
            context['risk'] = 'medium'
        elif any(shell in process_lower for shell in ['cmd', 'powershell', 'bash', 'sh']):
            context['category'] = 'shell'
            context['risk'] = 'high'
        elif any(sys in process_lower for sys in ['svchost', 'lsass', 'winlogon', 'csrss']):
            context['category'] = 'system'
            context['risk'] = 'critical'
        elif any(tool in process_lower for tool in ['psexec', 'wmic', 'mimikatz', 'certutil']):
            context['category'] = 'admin_tool'
            context['risk'] = 'very_high'
        else:
            context['category'] = 'application'
            context['risk'] = 'normal'

        return context

    def _extract_relationships(self, entities: List[Dict], log_data: Dict) -> List[Dict]:
        """Extract relationships between entities"""
        relationships = []

        # Group entities by type
        by_type = {}
        for entity in entities:
            entity_type = entity['type']
            if entity_type not in by_type:
                by_type[entity_type] = []
            by_type[entity_type].append(entity)

        # User → Host relationships
        if 'username' in by_type and ('hostname' in by_type or 'ip' in by_type):
            for user in by_type['username']:
                for host in by_type.get('hostname', []) + by_type.get('ip', []):
                    relationships.append({
                        'type': 'user_on_host',
                        'source': {'type': 'username', 'value': user['value']},
                        'target': {'type': host['type'], 'value': host['value']},
                        'confidence': min(user['confidence'], host['confidence'])
                    })

        # Process → Host relationships
        if 'process' in by_type and ('hostname' in by_type or 'ip' in by_type):
            for proc in by_type['process']:
                for host in by_type.get('hostname', []) + by_type.get('ip', []):
                    relationships.append({
                        'type': 'process_on_host',
                        'source': {'type': 'process', 'value': proc['value']},
                        'target': {'type': host['type'], 'value': host['value']},
                        'confidence': min(proc['confidence'], host['confidence'])
                    })

        # IP → Port relationships (connections)
        if 'ip' in by_type and 'port' in by_type:
            for ip in by_type['ip']:
                for port in by_type['port']:
                    relationships.append({
                        'type': 'connection',
                        'source': {'type': 'ip', 'value': ip['value']},
                        'target': {'type': 'port', 'value': port['value']},
                        'confidence': min(ip['confidence'], port['confidence'])
                    })

        # File → Process relationships
        if 'file_path' in by_type and 'process' in by_type:
            for file in by_type['file_path']:
                for proc in by_type['process']:
                    relationships.append({
                        'type': 'file_accessed_by',
                        'source': {'type': 'file', 'value': file['value']},
                        'target': {'type': 'process', 'value': proc['value']},
                        'confidence': min(file['confidence'], proc['confidence'])
                    })

        return relationships

    def _extract_log_context(self, log_data: Dict) -> Dict:
        """Extract contextual information from the log"""
        context = {}

        # Extract timestamp if present
        for time_field in ['timestamp', 'time', '@timestamp', 'event_time', 'created_at']:
            if time_field in log_data:
                context['timestamp'] = log_data[time_field]
                break

        # Extract severity/level
        for level_field in ['severity', 'level', 'priority', 'event_severity']:
            if level_field in log_data:
                context['severity'] = log_data[level_field]
                break

        # Extract event type
        for event_field in ['event_type', 'event_name', 'action', 'operation']:
            if event_field in log_data:
                context['event_type'] = log_data[event_field]
                break

        # Extract source
        for source_field in ['source', 'source_name', 'log_source', 'origin']:
            if source_field in log_data:
                context['source'] = log_data[source_field]
                break

        return context

    def create_session(self, entities: List[Dict], window_minutes: int = 5) -> Dict:
        """Group related entities into a session"""
        if not entities:
            return None

        # Create session ID from entities
        session_key = hashlib.md5(
            json.dumps(sorted([e['value'] for e in entities])).encode()
        ).hexdigest()[:8]

        session = {
            'session_id': f"sess_{session_key}",
            'entities': entities,
            'start_time': datetime.utcnow() - timedelta(minutes=window_minutes),
            'end_time': datetime.utcnow(),
            'entity_count': len(entities),
            'unique_types': list(set(e['type'] for e in entities))
        }

        return session

    def create_event(self, pattern_type: str, entities: List[Dict],
                    severity: str = 'medium') -> Dict:
        """Create a security event from extracted entities"""
        event = {
            'event_id': hashlib.md5(
                f"{pattern_type}:{datetime.utcnow().isoformat()}".encode()
            ).hexdigest()[:12],
            'event_type': pattern_type,
            'severity': severity,
            'entities_involved': entities,
            'entity_count': len(entities),
            'detected_at': datetime.utcnow().isoformat(),
            'requires_investigation': severity in ['high', 'critical']
        }

        return event