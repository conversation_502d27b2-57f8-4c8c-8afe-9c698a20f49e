"""
Preview-Before-Download for Cloud Updates
Allows previewing rule and definition updates before applying them
"""

import json
import logging
from typing import Dict, List, Optional
from datetime import datetime
import difflib
import asyncpg

logger = logging.getLogger(__name__)


class PreviewBeforeDownload:
    """
    Preview cloud updates before applying

    Features:
    - Preview rule/definition changes
    - Diff view of changes
    - Approval workflow
    - Rollback mechanism
    """

    def __init__(self, db_pool: asyncpg.Pool, redis_client):
        self.db_pool = db_pool
        self.redis = redis_client

    async def get_pending_updates(self) -> List[Dict]:
        """Get list of pending updates"""
        try:
            async with self.db_pool.acquire() as conn:
                updates = await conn.fetch("""
                    SELECT
                        update_id,
                        source_name,
                        update_type,
                        total_changes,
                        detected_at,
                        status
                    FROM cloud_updates
                    WHERE status = 'pending_review'
                    ORDER BY detected_at DESC
                    LIMIT 50
                """)

                return [dict(u) for u in updates]

        except Exception as e:
            logger.error(f"Error getting pending updates: {e}")
            return []

    async def preview_update(self, update_id: str) -> Dict:
        """
        Preview a specific update with diff

        Returns:
        {
            "update_id": "uuid",
            "source": "sigma_hq",
            "changes": [
                {
                    "type": "added",
                    "rule_id": "rule-123",
                    "rule_name": "New Detection Rule",
                    "content_preview": "..."
                },
                {
                    "type": "modified",
                    "rule_id": "rule-456",
                    "rule_name": "Updated Rule",
                    "diff": "...unified diff...",
                    "changes_summary": "Updated detection logic"
                },
                {
                    "type": "deleted",
                    "rule_id": "rule-789",
                    "rule_name": "Deprecated Rule"
                }
            ],
            "summary": {
                "added": 5,
                "modified": 12,
                "deleted": 2
            }
        }
        """
        try:
            async with self.db_pool.acquire() as conn:
                # Get update details
                update = await conn.fetchrow("""
                    SELECT *
                    FROM cloud_updates
                    WHERE update_id = $1
                """, update_id)

                if not update:
                    return {'error': 'Update not found'}

                # Get change details
                changes = await conn.fetch("""
                    SELECT
                        change_type,
                        rule_id,
                        rule_name,
                        old_content,
                        new_content
                    FROM cloud_update_changes
                    WHERE update_id = $1
                    ORDER BY change_type, rule_name
                """, update_id)

            # Build preview
            preview_changes = []
            summary = {'added': 0, 'modified': 0, 'deleted': 0}

            for change in changes:
                change_type = change['change_type']
                summary[change_type] = summary.get(change_type, 0) + 1

                change_data = {
                    'type': change_type,
                    'rule_id': change['rule_id'],
                    'rule_name': change['rule_name']
                }

                if change_type == 'added':
                    change_data['content_preview'] = (change['new_content'] or '')[:500]

                elif change_type == 'modified':
                    # Generate diff
                    old_lines = (change['old_content'] or '').splitlines(keepends=True)
                    new_lines = (change['new_content'] or '').splitlines(keepends=True)

                    diff = ''.join(difflib.unified_diff(
                        old_lines,
                        new_lines,
                        fromfile='old',
                        tofile='new',
                        lineterm=''
                    ))

                    change_data['diff'] = diff
                    change_data['changes_summary'] = self._summarize_changes(old_lines, new_lines)

                elif change_type == 'deleted':
                    change_data['content_preview'] = (change['old_content'] or '')[:500]

                preview_changes.append(change_data)

            return {
                'update_id': update_id,
                'source': update['source_name'],
                'detected_at': update['detected_at'].isoformat(),
                'changes': preview_changes,
                'summary': summary
            }

        except Exception as e:
            logger.error(f"Error previewing update {update_id}: {e}")
            return {'error': str(e)}

    def _summarize_changes(self, old_lines: List[str], new_lines: List[str]) -> str:
        """Generate human-readable summary of changes"""
        old_set = set(old_lines)
        new_set = set(new_lines)

        added_lines = len(new_set - old_set)
        removed_lines = len(old_set - new_set)

        summary_parts = []

        if added_lines:
            summary_parts.append(f"+{added_lines} lines")
        if removed_lines:
            summary_parts.append(f"-{removed_lines} lines")

        return ', '.join(summary_parts) if summary_parts else "No significant changes"

    async def approve_update(self, update_id: str, approved_by: str, notes: str = "") -> bool:
        """
        Approve update for application

        Returns:
            True if approved successfully
        """
        try:
            async with self.db_pool.acquire() as conn:
                # Update status to approved
                result = await conn.fetchrow("""
                    UPDATE cloud_updates
                    SET
                        status = 'approved',
                        approved_by = $1,
                        approved_at = NOW(),
                        approval_notes = $2
                    WHERE update_id = $3
                    RETURNING update_id
                """, approved_by, notes, update_id)

                if result:
                    logger.info(f"Update {update_id} approved by {approved_by}")
                    # Trigger application via Redis
                    self.redis.publish('backend.apply_update', json.dumps({
                        'update_id': update_id,
                        'approved_by': approved_by
                    }))
                    return True

                return False

        except Exception as e:
            logger.error(f"Error approving update {update_id}: {e}")
            return False

    async def reject_update(self, update_id: str, rejected_by: str, reason: str) -> bool:
        """
        Reject update

        Returns:
            True if rejected successfully
        """
        try:
            async with self.db_pool.acquire() as conn:
                result = await conn.fetchrow("""
                    UPDATE cloud_updates
                    SET
                        status = 'rejected',
                        rejected_by = $1,
                        rejected_at = NOW(),
                        rejection_reason = $2
                    WHERE update_id = $3
                    RETURNING update_id
                """, rejected_by, reason, update_id)

                if result:
                    logger.info(f"Update {update_id} rejected by {rejected_by}: {reason}")
                    return True

                return False

        except Exception as e:
            logger.error(f"Error rejecting update {update_id}: {e}")
            return False

    async def rollback_update(self, update_id: str, rolled_back_by: str) -> bool:
        """
        Rollback previously applied update

        Returns:
            True if rollback successful
        """
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.transaction():
                    # Get update details
                    update = await conn.fetchrow("""
                        SELECT *
                        FROM cloud_updates
                        WHERE update_id = $1 AND status = 'applied'
                    """, update_id)

                    if not update:
                        logger.warning(f"Cannot rollback {update_id}: not found or not applied")
                        return False

                    # Get changes to reverse
                    changes = await conn.fetch("""
                        SELECT
                            change_type,
                            rule_id,
                            old_content,
                            new_content
                        FROM cloud_update_changes
                        WHERE update_id = $1
                    """, update_id)

                    # Reverse changes
                    for change in changes:
                        if change['change_type'] == 'added':
                            # Delete the added rule
                            await conn.execute("DELETE FROM detection_rules WHERE rule_id = $1", change['rule_id'])

                        elif change['change_type'] == 'modified':
                            # Restore old content
                            await conn.execute("""
                                UPDATE detection_rules
                                SET rule_content = $1,
                                    updated_at = NOW()
                                WHERE rule_id = $2
                            """, change['old_content'], change['rule_id'])

                        elif change['change_type'] == 'deleted':
                            # Restore deleted rule
                            await conn.execute("""
                                INSERT INTO detection_rules (rule_id, rule_content, created_at)
                                VALUES ($1, $2, NOW())
                                ON CONFLICT (rule_id) DO UPDATE
                                SET rule_content = EXCLUDED.rule_content, updated_at = NOW()
                            """, change['rule_id'], change['old_content'])

                    # Mark update as rolled back
                    await conn.execute("""
                        UPDATE cloud_updates
                        SET
                            status = 'rolled_back',
                            rolled_back_by = $1,
                            rolled_back_at = NOW()
                        WHERE update_id = $2
                    """, rolled_back_by, update_id)

                logger.info(f"Update {update_id} rolled back by {rolled_back_by}")
                return True

        except Exception as e:
            logger.error(f"Error rolling back update {update_id}: {e}")
            return False
