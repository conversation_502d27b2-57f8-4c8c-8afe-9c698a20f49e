# IBM QRadar SIEM Configuration
# Query Language: AQL (Ariel Query Language)

platform:
  name: qradar
  display_name: IBM QRadar
  query_language: aql
  description: IBM QRadar's Ariel Query Language for security analytics
  vendor: IBM Corporation
  version: "1.0"
  active: true

# Field mappings: generic_field -> QRadar field
field_mappings:
  source_ip: sourceip
  destination_ip: destinationip
  username: username
  process_name: processname
  file_hash: filehash
  event_id: EventID
  hostname: hostname
  port: destinationport
  source_port: sourceport
  destination_port: destinationport
  domain: domainname
  url: url
  file_name: filename
  file_path: filepath
  registry_path: registrykey
  command_line: commandline
  parent_process: parentprocessname
  network_protocol: protocolname
  http_method: httpmethod
  user_agent: useragent
  email_sender: emailsender
  email_recipient: emailrecipient
  dns_query: dnsquery
  service_name: servicename
  account_name: accountname
  event_name: eventname
  category: category
  log_source_name: logsourcename
  magnitude: magnitude
  credibility: credibility
  relevance: relevance

# Operator mappings: generic_operator -> QRadar operator
operator_mappings:
  equals: "="
  not_equals: "!="
  contains: ILIKE
  not_contains: NOT ILIKE
  regex: MATCHES
  greater_than: ">"
  less_than: "<"
  greater_equal: ">="
  less_equal: "<="
  in_list: IN
  not_in_list: NOT IN
  is_null: IS NULL
  is_not_null: IS NOT NULL
  between: BETWEEN

# Time field for temporal queries
time_field: starttime

# Query syntax specifics
syntax:
  comment: "--"
  string_quote: "'"
  escape_char: "\\"
  wildcard: "%"
  field_separator: " "
  logical_and: AND
  logical_or: OR
  logical_not: NOT
  select_statement: SELECT
  from_statement: FROM
  where_statement: WHERE
  order_by_statement: ORDER BY
  group_by_statement: GROUP BY
  limit_statement: LIMIT
  case_sensitive: false

# Platform-specific features
metadata:
  supports_regex: true
  supports_wildcards: true
  supports_joins: true
  supports_subqueries: true
  supports_aggregations: true
  max_query_results: 50000
  default_time_range: "LAST 1 HOURS"

  # Common data sources
  common_log_sources:
    - flows
    - events
    - offenses
    - assets
    - vulnerabilities

  # QRadar-specific tables
  available_tables:
    - events
    - flows
    - offenses
    - assetprofile
    - categories
    - qidmap
    - logsourcetypemap

  # Detection rule template (Custom Rule Engine)
  rule_template: |
    SELECT * FROM events
    WHERE {conditions}
    AND starttime > {time_condition}
    LAST {time_range}

  # AQL time formats
  time_ranges:
    - LAST 1 MINUTES
    - LAST 5 MINUTES
    - LAST 1 HOURS
    - LAST 24 HOURS
    - LAST 7 DAYS
    - START '{start_time}' STOP '{stop_time}'

  # Rule types
  rule_types:
    - Event  # Single event detection
    - Flow  # Network flow detection
    - Offense  # Correlation rule
    - Common  # Building block

  # Building blocks
  building_blocks:
    - BB:CategoryDefinition
    - BB:DestinationIP
    - BB:SourceIP
    - BB:UserDefinition

  # Response actions
  response_actions:
    - Add to Reference Set
    - Send Email
    - Execute Script
    - Create Offense
    - Log to System
    - SIEM Action

  # Reference set types
  reference_set_types:
    - ALN  # Alphanumeric
    - IP   # IP Address
    - NUM  # Numeric
    - PORT # Port
    - DATE # Date

  # Offense severity levels
  severity_levels:
    - 0  # Info
    - 1  # Low
    - 2  # Low
    - 3  # Low
    - 4  # Medium
    - 5  # Medium
    - 6  # Medium
    - 7  # High
    - 8  # High
    - 9  # Critical
    - 10 # Critical
