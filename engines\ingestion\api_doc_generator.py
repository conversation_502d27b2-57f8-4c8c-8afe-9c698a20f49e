"""
API Documentation Generator
Generates OpenAPI 3.0 specifications and documentation for ingested sources
"""

import json
import yaml
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import logging

class APIDocumentationGenerator:
    """Generate OpenAPI documentation for ingested sources and patterns"""

    def __init__(self, redis_client, postgres_conn, logger):
        self.redis = redis_client
        self.db = postgres_conn
        self.logger = logger

        # OpenAPI base structure
        self.openapi_spec = {
            "openapi": "3.0.3",
            "info": {
                "title": "SIEMLess v2.0 API",
                "description": "Intelligence Foundation Platform for Security Operations",
                "version": "2.0.0",
                "contact": {
                    "name": "SIEMLess Support",
                    "url": "https://github.com/siemless/siemless-v2"
                }
            },
            "servers": [
                {
                    "url": "http://localhost:8003",
                    "description": "Ingestion Engine"
                },
                {
                    "url": "http://localhost:8001",
                    "description": "Intelligence Engine"
                },
                {
                    "url": "http://localhost:8002",
                    "description": "Backend Engine"
                },
                {
                    "url": "http://localhost:8004",
                    "description": "Contextualization Engine"
                },
                {
                    "url": "http://localhost:8005",
                    "description": "Delivery Engine"
                }
            ],
            "paths": {},
            "components": {
                "schemas": {},
                "securitySchemes": {
                    "ApiKey": {
                        "type": "apiKey",
                        "in": "header",
                        "name": "X-API-Key"
                    },
                    "BearerAuth": {
                        "type": "http",
                        "scheme": "bearer"
                    }
                }
            },
            "tags": []
        }

    async def generate_documentation(self) -> Dict[str, Any]:
        """Generate complete API documentation"""
        try:
            # Document ingestion endpoints
            await self._document_ingestion_endpoints()

            # Document pattern endpoints
            await self._document_pattern_endpoints()

            # Document query endpoints
            await self._document_query_endpoints()

            # Document data source endpoints
            await self._document_datasource_endpoints()

            # Generate schemas from database
            await self._generate_schemas()

            # Add webhook documentation
            await self._document_webhooks()

            self.logger.info("Generated complete API documentation")
            return self.openapi_spec

        except Exception as e:
            self.logger.error(f"Failed to generate documentation: {e}")
            return self.openapi_spec

    async def _document_ingestion_endpoints(self):
        """Document ingestion API endpoints"""

        # Log ingestion endpoint
        self.openapi_spec["paths"]["/api/v1/ingest"] = {
            "post": {
                "tags": ["Ingestion"],
                "summary": "Ingest security logs",
                "description": "Submit security logs for processing and pattern matching",
                "operationId": "ingestLogs",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/LogIngestionRequest"
                            },
                            "examples": {
                                "crowdstrike": {
                                    "summary": "CrowdStrike detection",
                                    "value": {
                                        "source": "crowdstrike",
                                        "logs": [
                                            {
                                                "detection_id": "ldt:12345",
                                                "timestamp": "2025-01-01T00:00:00Z",
                                                "severity": "high",
                                                "device": {"hostname": "DESKTOP-001"}
                                            }
                                        ]
                                    }
                                },
                                "fortinet": {
                                    "summary": "Fortinet firewall log",
                                    "value": {
                                        "source": "fortinet",
                                        "logs": [
                                            {
                                                "type": "traffic",
                                                "srcip": "*************",
                                                "dstip": "*******",
                                                "action": "accept"
                                            }
                                        ]
                                    }
                                }
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Logs successfully ingested",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/IngestionResponse"
                                }
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid request format"
                    },
                    "500": {
                        "description": "Internal server error"
                    }
                },
                "security": [{"ApiKey": []}]
            }
        }

        # Batch ingestion endpoint
        self.openapi_spec["paths"]["/api/v1/ingest/batch"] = {
            "post": {
                "tags": ["Ingestion"],
                "summary": "Batch ingest logs",
                "description": "Submit multiple log batches for parallel processing",
                "operationId": "batchIngestLogs",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "array",
                                "items": {
                                    "$ref": "#/components/schemas/LogIngestionRequest"
                                }
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Batch successfully processed",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "array",
                                    "items": {
                                        "$ref": "#/components/schemas/IngestionResponse"
                                    }
                                }
                            }
                        }
                    }
                },
                "security": [{"ApiKey": []}]
            }
        }

        # Add ingestion tag
        self.openapi_spec["tags"].append({
            "name": "Ingestion",
            "description": "Log ingestion and processing endpoints"
        })

    async def _document_pattern_endpoints(self):
        """Document pattern management endpoints"""

        # List patterns
        self.openapi_spec["paths"]["/api/v1/patterns"] = {
            "get": {
                "tags": ["Patterns"],
                "summary": "List all patterns",
                "description": "Retrieve all active patterns from the pattern library",
                "operationId": "listPatterns",
                "parameters": [
                    {
                        "name": "type",
                        "in": "query",
                        "description": "Filter by pattern type",
                        "schema": {
                            "type": "string",
                            "enum": ["log_parser", "entity_extractor", "query_template", "detection_rule"]
                        }
                    },
                    {
                        "name": "source",
                        "in": "query",
                        "description": "Filter by pattern source",
                        "schema": {"type": "string"}
                    },
                    {
                        "name": "active",
                        "in": "query",
                        "description": "Filter by active status",
                        "schema": {"type": "boolean"}
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of patterns",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "array",
                                    "items": {
                                        "$ref": "#/components/schemas/Pattern"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "post": {
                "tags": ["Patterns"],
                "summary": "Create new pattern",
                "description": "Add a new pattern to the library",
                "operationId": "createPattern",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/PatternCreate"
                            }
                        }
                    }
                },
                "responses": {
                    "201": {
                        "description": "Pattern created",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/Pattern"
                                }
                            }
                        }
                    }
                },
                "security": [{"ApiKey": []}]
            }
        }

        # Pattern operations
        self.openapi_spec["paths"]["/api/v1/patterns/{pattern_id}"] = {
            "get": {
                "tags": ["Patterns"],
                "summary": "Get pattern details",
                "operationId": "getPattern",
                "parameters": [
                    {
                        "name": "pattern_id",
                        "in": "path",
                        "required": True,
                        "schema": {"type": "string"}
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Pattern details",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/Pattern"
                                }
                            }
                        }
                    },
                    "404": {
                        "description": "Pattern not found"
                    }
                }
            },
            "put": {
                "tags": ["Patterns"],
                "summary": "Update pattern",
                "operationId": "updatePattern",
                "parameters": [
                    {
                        "name": "pattern_id",
                        "in": "path",
                        "required": True,
                        "schema": {"type": "string"}
                    }
                ],
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/PatternUpdate"
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Pattern updated"
                    }
                },
                "security": [{"ApiKey": []}]
            },
            "delete": {
                "tags": ["Patterns"],
                "summary": "Disable pattern",
                "operationId": "disablePattern",
                "parameters": [
                    {
                        "name": "pattern_id",
                        "in": "path",
                        "required": True,
                        "schema": {"type": "string"}
                    }
                ],
                "responses": {
                    "204": {
                        "description": "Pattern disabled"
                    }
                },
                "security": [{"ApiKey": []}]
            }
        }

        # Pattern sync from GitHub
        self.openapi_spec["paths"]["/api/v1/patterns/sync/github"] = {
            "post": {
                "tags": ["Patterns"],
                "summary": "Sync patterns from GitHub",
                "description": "Trigger synchronization of patterns from configured GitHub repositories",
                "operationId": "syncGitHubPatterns",
                "requestBody": {
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "repository": {
                                        "type": "string",
                                        "description": "Specific repository to sync (optional)"
                                    },
                                    "force": {
                                        "type": "boolean",
                                        "description": "Force sync even if up to date"
                                    }
                                }
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Sync results",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/SyncResult"
                                }
                            }
                        }
                    }
                },
                "security": [{"ApiKey": []}]
            }
        }

        # Add patterns tag
        self.openapi_spec["tags"].append({
            "name": "Patterns",
            "description": "Pattern library management"
        })

    async def _document_query_endpoints(self):
        """Document query generation endpoints"""

        self.openapi_spec["paths"]["/api/v1/query/generate"] = {
            "post": {
                "tags": ["Query"],
                "summary": "Generate SIEM queries",
                "description": "Generate queries for multiple SIEM platforms from universal query intent",
                "operationId": "generateQueries",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/QueryIntent"
                            },
                            "examples": {
                                "search": {
                                    "summary": "Basic search query",
                                    "value": {
                                        "type": "search",
                                        "filters": {
                                            "user": "admin",
                                            "action": "login"
                                        },
                                        "time_range": "-24h"
                                    }
                                },
                                "threat_hunt": {
                                    "summary": "Threat hunting query",
                                    "value": {
                                        "type": "threat_hunt",
                                        "filters": {
                                            "iocs": [
                                                {"type": "ip", "value": "*************"},
                                                {"type": "hash", "value": "abc123def456"}
                                            ],
                                            "mitre_techniques": ["T1055", "T1003"]
                                        },
                                        "time_range": "-7d"
                                    }
                                }
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Generated queries for each SIEM",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/GeneratedQueries"
                                }
                            }
                        }
                    }
                }
            }
        }

        # Natural language query
        self.openapi_spec["paths"]["/api/v1/query/natural"] = {
            "post": {
                "tags": ["Query"],
                "summary": "Natural language query",
                "description": "Convert natural language to SIEM queries",
                "operationId": "naturalLanguageQuery",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "query": {
                                        "type": "string",
                                        "description": "Natural language query",
                                        "example": "Show me all failed login attempts from external IPs in the last 24 hours"
                                    }
                                },
                                "required": ["query"]
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Parsed intent and generated queries",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "intent": {
                                            "$ref": "#/components/schemas/QueryIntent"
                                        },
                                        "queries": {
                                            "$ref": "#/components/schemas/GeneratedQueries"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        # Add query tag
        self.openapi_spec["tags"].append({
            "name": "Query",
            "description": "Query generation and translation"
        })

    async def _document_datasource_endpoints(self):
        """Document data source management endpoints"""

        self.openapi_spec["paths"]["/api/v1/sources"] = {
            "get": {
                "tags": ["Data Sources"],
                "summary": "List configured data sources",
                "operationId": "listDataSources",
                "responses": {
                    "200": {
                        "description": "List of data sources",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "array",
                                    "items": {
                                        "$ref": "#/components/schemas/DataSource"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "post": {
                "tags": ["Data Sources"],
                "summary": "Configure new data source",
                "operationId": "configureDataSource",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/DataSourceConfig"
                            }
                        }
                    }
                },
                "responses": {
                    "201": {
                        "description": "Data source configured"
                    }
                },
                "security": [{"ApiKey": []}]
            }
        }

        # GitHub repository management
        self.openapi_spec["paths"]["/api/v1/sources/github/repositories"] = {
            "get": {
                "tags": ["Data Sources"],
                "summary": "List GitHub repositories",
                "description": "Get list of configured GitHub pattern repositories",
                "operationId": "listGitHubRepos",
                "responses": {
                    "200": {
                        "description": "List of repositories",
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "array",
                                    "items": {
                                        "$ref": "#/components/schemas/GitHubRepository"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "post": {
                "tags": ["Data Sources"],
                "summary": "Add GitHub repository",
                "description": "Add a new GitHub repository for pattern synchronization",
                "operationId": "addGitHubRepo",
                "requestBody": {
                    "required": True,
                    "content": {
                        "application/json": {
                            "schema": {
                                "$ref": "#/components/schemas/GitHubRepoConfig"
                            }
                        }
                    }
                },
                "responses": {
                    "201": {
                        "description": "Repository added"
                    }
                },
                "security": [{"ApiKey": []}]
            }
        }

        # Add data sources tag
        self.openapi_spec["tags"].append({
            "name": "Data Sources",
            "description": "Data source configuration and management"
        })

    async def _generate_schemas(self):
        """Generate component schemas from database and code"""

        # Log ingestion schemas
        self.openapi_spec["components"]["schemas"]["LogIngestionRequest"] = {
            "type": "object",
            "required": ["source", "logs"],
            "properties": {
                "source": {
                    "type": "string",
                    "description": "Source system identifier",
                    "enum": ["crowdstrike", "fortinet", "paloalto", "elasticsearch", "custom"]
                },
                "logs": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "additionalProperties": True
                    },
                    "minItems": 1,
                    "maxItems": 10000
                },
                "metadata": {
                    "type": "object",
                    "description": "Additional metadata",
                    "additionalProperties": True
                }
            }
        }

        self.openapi_spec["components"]["schemas"]["IngestionResponse"] = {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "enum": ["success", "partial", "failed"]
                },
                "processed": {
                    "type": "integer",
                    "description": "Number of logs processed"
                },
                "matched": {
                    "type": "integer",
                    "description": "Number of logs matched to patterns"
                },
                "entities_extracted": {
                    "type": "integer",
                    "description": "Total entities extracted"
                },
                "processing_time": {
                    "type": "number",
                    "format": "float",
                    "description": "Processing time in seconds"
                }
            }
        }

        # Pattern schemas
        self.openapi_spec["components"]["schemas"]["Pattern"] = {
            "type": "object",
            "properties": {
                "pattern_id": {"type": "string"},
                "pattern_type": {
                    "type": "string",
                    "enum": ["log_parser", "entity_extractor", "query_template", "detection_rule"]
                },
                "pattern_data": {
                    "type": "object",
                    "additionalProperties": True
                },
                "version": {"type": "string"},
                "source": {"type": "string"},
                "is_active": {"type": "boolean"},
                "usage_count": {"type": "integer"},
                "success_rate": {"type": "number"},
                "created_at": {"type": "string", "format": "date-time"},
                "updated_at": {"type": "string", "format": "date-time"}
            }
        }

        self.openapi_spec["components"]["schemas"]["PatternCreate"] = {
            "type": "object",
            "required": ["pattern_id", "pattern_type", "pattern_data"],
            "properties": {
                "pattern_id": {"type": "string"},
                "pattern_type": {
                    "type": "string",
                    "enum": ["log_parser", "entity_extractor", "query_template", "detection_rule"]
                },
                "pattern_data": {
                    "type": "object",
                    "additionalProperties": True
                }
            }
        }

        # Query schemas
        self.openapi_spec["components"]["schemas"]["QueryIntent"] = {
            "type": "object",
            "properties": {
                "type": {
                    "type": "string",
                    "enum": ["search", "aggregate", "timeline", "correlation", "threat_hunt", "statistical"]
                },
                "filters": {
                    "type": "object",
                    "additionalProperties": True
                },
                "aggregations": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "string"},
                            "field": {"type": "string"},
                            "group_by": {"type": "string"}
                        }
                    }
                },
                "time_range": {
                    "type": "string",
                    "pattern": "^-\\d+[hdm]$"
                }
            }
        }

        self.openapi_spec["components"]["schemas"]["GeneratedQueries"] = {
            "type": "object",
            "properties": {
                "splunk": {"type": "string"},
                "elastic": {"type": "string"},
                "sentinel": {"type": "string"},
                "qradar": {"type": "string"},
                "chronicle": {"type": "string"}
            }
        }

        # GitHub schemas
        self.openapi_spec["components"]["schemas"]["GitHubRepository"] = {
            "type": "object",
            "properties": {
                "id": {"type": "integer"},
                "repo_url": {"type": "string"},
                "branch": {"type": "string"},
                "last_sync": {"type": "string", "format": "date-time"},
                "patterns_count": {"type": "integer"},
                "active": {"type": "boolean"}
            }
        }

        self.openapi_spec["components"]["schemas"]["GitHubRepoConfig"] = {
            "type": "object",
            "required": ["repo_url"],
            "properties": {
                "repo_url": {
                    "type": "string",
                    "example": "https://github.com/siemless/patterns"
                },
                "branch": {
                    "type": "string",
                    "default": "main"
                },
                "sync_interval": {
                    "type": "integer",
                    "default": 3600,
                    "description": "Sync interval in seconds"
                }
            }
        }

        self.openapi_spec["components"]["schemas"]["SyncResult"] = {
            "type": "object",
            "properties": {
                "repo": {"type": "string"},
                "status": {"type": "string"},
                "patterns_found": {"type": "integer"},
                "patterns_validated": {"type": "integer"},
                "patterns_deployed": {"type": "integer"},
                "errors": {
                    "type": "array",
                    "items": {"type": "string"}
                }
            }
        }

    async def _document_webhooks(self):
        """Document webhook endpoints for GitHub integration"""

        if "webhooks" not in self.openapi_spec:
            self.openapi_spec["webhooks"] = {}

        self.openapi_spec["webhooks"]["patternUpdate"] = {
            "post": {
                "requestBody": {
                    "description": "Pattern update notification",
                    "content": {
                        "application/json": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "event": {"type": "string", "enum": ["pattern.created", "pattern.updated", "pattern.deleted"]},
                                    "pattern_id": {"type": "string"},
                                    "repository": {"type": "string"},
                                    "timestamp": {"type": "string", "format": "date-time"}
                                }
                            }
                        }
                    }
                },
                "responses": {
                    "200": {
                        "description": "Webhook processed"
                    }
                }
            }
        }

    async def export_documentation(self, format: str = "json",
                                  output_path: Optional[str] = None) -> str:
        """Export documentation in specified format"""
        try:
            # Generate full documentation
            spec = await self.generate_documentation()

            # Convert to requested format
            if format.lower() == "json":
                output = json.dumps(spec, indent=2)
            elif format.lower() == "yaml":
                output = yaml.dump(spec, default_flow_style=False, sort_keys=False)
            else:
                raise ValueError(f"Unsupported format: {format}")

            # Save to file if path provided
            if output_path:
                Path(output_path).write_text(output)
                self.logger.info(f"Documentation exported to {output_path}")

            return output

        except Exception as e:
            self.logger.error(f"Failed to export documentation: {e}")
            raise

    async def generate_client_sdk(self, language: str = "python") -> str:
        """Generate client SDK code for specified language"""
        try:
            if language.lower() == "python":
                return self._generate_python_sdk()
            elif language.lower() == "javascript":
                return self._generate_javascript_sdk()
            else:
                raise ValueError(f"Unsupported language: {language}")

        except Exception as e:
            self.logger.error(f"Failed to generate SDK: {e}")
            raise

    def _generate_python_sdk(self) -> str:
        """Generate Python client SDK"""
        sdk_code = '''"""
SIEMLess v2.0 Python Client SDK
Auto-generated from OpenAPI specification
"""

import requests
from typing import Dict, List, Any, Optional


class SIEMLessClient:
    """Python client for SIEMLess v2.0 API"""

    def __init__(self, base_url: str = "http://localhost:8003", api_key: Optional[str] = None):
        self.base_url = base_url.rstrip("/")
        self.session = requests.Session()
        if api_key:
            self.session.headers["X-API-Key"] = api_key

    def ingest_logs(self, source: str, logs: List[Dict]) -> Dict:
        """Ingest security logs"""
        response = self.session.post(
            f"{self.base_url}/api/v1/ingest",
            json={"source": source, "logs": logs}
        )
        response.raise_for_status()
        return response.json()

    def list_patterns(self, pattern_type: Optional[str] = None) -> List[Dict]:
        """List patterns from the pattern library"""
        params = {}
        if pattern_type:
            params["type"] = pattern_type

        response = self.session.get(
            f"{self.base_url}/api/v1/patterns",
            params=params
        )
        response.raise_for_status()
        return response.json()

    def sync_github_patterns(self, repository: Optional[str] = None, force: bool = False) -> Dict:
        """Trigger GitHub pattern synchronization"""
        response = self.session.post(
            f"{self.base_url}/api/v1/patterns/sync/github",
            json={"repository": repository, "force": force}
        )
        response.raise_for_status()
        return response.json()

    def generate_queries(self, query_intent: Dict) -> Dict[str, str]:
        """Generate SIEM queries from intent"""
        response = self.session.post(
            f"{self.base_url}/api/v1/query/generate",
            json=query_intent
        )
        response.raise_for_status()
        return response.json()

    def natural_language_query(self, query: str) -> Dict:
        """Convert natural language to SIEM queries"""
        response = self.session.post(
            f"{self.base_url}/api/v1/query/natural",
            json={"query": query}
        )
        response.raise_for_status()
        return response.json()


# Example usage
if __name__ == "__main__":
    client = SIEMLessClient(api_key="your-api-key")

    # Ingest logs
    result = client.ingest_logs(
        source="crowdstrike",
        logs=[{"detection_id": "123", "severity": "high"}]
    )
    print(f"Ingested {result['processed']} logs")

    # Generate queries
    queries = client.generate_queries({
        "type": "search",
        "filters": {"user": "admin"},
        "time_range": "-24h"
    })
    print(f"Splunk query: {queries['splunk']}")
'''
        return sdk_code

    def _generate_javascript_sdk(self) -> str:
        """Generate JavaScript/TypeScript client SDK"""
        sdk_code = '''/**
 * SIEMLess v2.0 JavaScript Client SDK
 * Auto-generated from OpenAPI specification
 */

class SIEMLessClient {
    constructor(baseUrl = "http://localhost:8003", apiKey = null) {
        this.baseUrl = baseUrl.replace(/\/$/, "");
        this.apiKey = apiKey;
    }

    async _request(method, path, data = null) {
        const headers = {
            "Content-Type": "application/json"
        };

        if (this.apiKey) {
            headers["X-API-Key"] = this.apiKey;
        }

        const options = {
            method,
            headers
        };

        if (data && (method === "POST" || method === "PUT")) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(`${this.baseUrl}${path}`, options);

        if (!response.ok) {
            throw new Error(`Request failed: ${response.statusText}`);
        }

        return response.json();
    }

    async ingestLogs(source, logs) {
        return this._request("POST", "/api/v1/ingest", { source, logs });
    }

    async listPatterns(type = null) {
        const params = type ? `?type=${type}` : "";
        return this._request("GET", `/api/v1/patterns${params}`);
    }

    async syncGitHubPatterns(repository = null, force = false) {
        return this._request("POST", "/api/v1/patterns/sync/github", { repository, force });
    }

    async generateQueries(queryIntent) {
        return this._request("POST", "/api/v1/query/generate", queryIntent);
    }

    async naturalLanguageQuery(query) {
        return this._request("POST", "/api/v1/query/natural", { query });
    }
}

// Example usage
// const client = new SIEMLessClient("http://localhost:8003", "your-api-key");
// const result = await client.ingestLogs("crowdstrike", [{detection_id: "123"}]);

export default SIEMLessClient;
'''
        return sdk_code