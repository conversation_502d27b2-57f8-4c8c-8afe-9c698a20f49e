#!/usr/bin/env python3
"""
Test Log Source Quality API via Redis pub/sub
Shows how to call all 11 endpoints through Redis messaging
"""

import redis
import json
import time
import uuid
import sys

def test_redis_log_quality():
    """Test all log quality endpoints via Redis"""

    # Connect to Redis
    r = redis.Redis(host='localhost', port=6380, decode_responses=True)
    pubsub = r.pubsub()

    print("\n" + "="*60)
    print("LOG SOURCE QUALITY - REDIS PUB/SUB TEST")
    print("="*60)

    # Test cases for all 11 endpoints
    test_cases = [
        {
            'name': 'Get Log Source Status',
            'channel': 'backend.log_quality.status',
            'data': {}
        },
        {
            'name': 'Register New Source',
            'channel': 'backend.log_quality.register',
            'data': {
                'name': 'TestRedisEDR',
                'type': 'endpoint',
                'product': 'Redis Test',
                'capabilities': ['process_monitoring', 'network_monitoring']
            }
        },
        {
            'name': 'Calculate Detection Fidelity',
            'channel': 'backend.log_quality.detection_fidelity',
            'data': {
                'attack_types': ['ransomware', 'lateral_movement']
            }
        },
        {
            'name': 'Get Overall Coverage',
            'channel': 'backend.log_quality.coverage',
            'data': {}
        },
        {
            'name': 'Check MITRE Technique Coverage',
            'channel': 'backend.log_quality.technique_coverage',
            'data': {
                'techniques': ['T1055', 'T1021', 'T1003']
            }
        },
        {
            'name': 'Assess Correlation Capability',
            'channel': 'backend.log_quality.correlation_capability',
            'data': {}
        },
        {
            'name': 'Check Correlation Requirements',
            'channel': 'backend.log_quality.correlation_requirements',
            'data': {
                'attack_type': 'ransomware'
            }
        },
        {
            'name': 'Get Source Recommendations',
            'channel': 'backend.log_quality.recommendations',
            'data': {
                'target_attacks': ['ransomware', 'data_exfiltration']
            }
        },
        {
            'name': 'Analyze Coverage Gaps',
            'channel': 'backend.log_quality.coverage_gaps',
            'data': {}
        },
        {
            'name': 'Simulate Coverage Changes',
            'channel': 'backend.log_quality.simulate',
            'data': {
                'add_sources': [
                    {'name': 'NewFirewall', 'category': 'network', 'tier': 'GOLD'},
                    {'name': 'NewSIEM', 'category': 'siem', 'tier': 'SILVER'}
                ],
                'remove_sources': []
            }
        }
    ]

    print("\nRunning tests...")
    print("-" * 60)

    for test in test_cases:
        try:
            # Generate request ID
            request_id = str(uuid.uuid4())
            test['data']['request_id'] = request_id

            # Subscribe to response channel
            response_channel = f'backend.log_quality.response.{request_id}'
            pubsub.subscribe(response_channel)

            # Send request
            print(f"\n[TEST] {test['name']}")
            print(f"  Channel: {test['channel']}")

            r.publish(test['channel'], json.dumps(test['data']))

            # Wait for response (with timeout)
            timeout = time.time() + 5  # 5 second timeout
            response_received = False

            while time.time() < timeout:
                message = pubsub.get_message(timeout=0.1)
                if message and message['type'] == 'message':
                    response = json.loads(message['data'])

                    if response.get('status') == 'success':
                        print(f"  [OK] SUCCESS")
                        # Show key results
                        if 'sources' in response:
                            print(f"     Sources found: {response.get('count', len(response['sources']))}")
                        if 'fidelity' in response:
                            print(f"     Fidelity calculated for {len(response.get('attack_types', []))} attacks")
                        if 'coverage' in response:
                            print(f"     Coverage analyzed: {response.get('overall_coverage', 'N/A')}")
                        if 'recommendations' in response:
                            recs = response['recommendations']
                            print(f"     Recommendations: {len(recs.get('priority_sources', []))} priority sources")
                        if 'gaps' in response:
                            print(f"     Gaps found: {response.get('total_gaps', 0)} total, {response.get('critical_gaps', 0)} critical")
                        if 'simulation' in response:
                            sim = response['simulation']
                            print(f"     Simulation: {sim['after']['average_confidence']:.1f}% vs {sim['before']['average_confidence']:.1f}%")
                    else:
                        print(f"  [X] ERROR: {response.get('error', 'Unknown error')}")

                    response_received = True
                    break

            if not response_received:
                print(f"  [!] TIMEOUT - No response received")

            # Unsubscribe from response channel
            pubsub.unsubscribe(response_channel)

        except Exception as e:
            print(f"  [X] EXCEPTION: {str(e)}")

    print("\n" + "="*60)
    print("TEST COMPLETE")
    print("="*60)

def test_delivery_to_backend():
    """Example: How Delivery Engine would call Backend via Redis"""

    print("\n" + "="*60)
    print("EXAMPLE: Delivery Engine → Backend Engine (via Redis)")
    print("="*60)

    r = redis.Redis(host='localhost', port=6380, decode_responses=True)
    pubsub = r.pubsub()

    # Delivery engine wants to check detection capability
    request_id = str(uuid.uuid4())
    response_channel = f'backend.log_quality.response.{request_id}'

    # Subscribe to response
    pubsub.subscribe(response_channel)

    # Send request from delivery engine
    print("\n[Delivery Engine] Checking if we can detect ransomware...")
    r.publish('backend.log_quality.detection_fidelity', json.dumps({
        'request_id': request_id,
        'attack_types': ['ransomware'],
        'source': 'delivery_engine'  # Optional: identify caller
    }))

    # Wait for backend response
    timeout = time.time() + 5
    while time.time() < timeout:
        message = pubsub.get_message(timeout=0.1)
        if message and message['type'] == 'message':
            response = json.loads(message['data'])

            print("\n[Backend Engine Response]")
            if response.get('status') == 'success':
                fidelity = response.get('fidelity', {})
                ransomware_fidelity = fidelity.get('attack_fidelity', {}).get('ransomware', {})
                confidence = ransomware_fidelity.get('confidence', 0)

                print(f"  Detection Confidence: {confidence:.1f}%")
                print(f"  Available Sources: {len(response.get('available_sources', []))}")

                if confidence < 50:
                    print("\n[Delivery Engine] [!] Low confidence! Requesting recommendations...")

                    # Get recommendations
                    rec_request_id = str(uuid.uuid4())
                    rec_channel = f'backend.log_quality.response.{rec_request_id}'
                    pubsub.subscribe(rec_channel)

                    r.publish('backend.log_quality.recommendations', json.dumps({
                        'request_id': rec_request_id,
                        'target_attacks': ['ransomware']
                    }))

                    # Wait for recommendations
                    rec_timeout = time.time() + 5
                    while time.time() < rec_timeout:
                        rec_msg = pubsub.get_message(timeout=0.1)
                        if rec_msg and rec_msg['type'] == 'message':
                            rec_response = json.loads(rec_msg['data'])
                            if rec_response.get('status') == 'success':
                                recs = rec_response.get('recommendations', {})
                                priority = recs.get('priority_sources', [])
                                print(f"\n[Backend Recommendations]")
                                for i, source in enumerate(priority[:3], 1):
                                    print(f"  {i}. Add {source['name']} ({source['tier']}) - Impact: +{source['impact']:.1f}%")
                            break
                else:
                    print(f"\n[Delivery Engine] [OK] Good detection capability!")
            break

    print("\n" + "="*60)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "delivery":
        test_delivery_to_backend()
    else:
        test_redis_log_quality()
        print("\nTo see Delivery→Backend example, run: python test_redis_log_quality.py delivery")