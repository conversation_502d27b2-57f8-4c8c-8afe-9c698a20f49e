# Smart AI Verdict Logic - Port Scan Example

## Why "Internal + Clean Threat Intel" Isn't Enough

You're absolutely correct! Just because an IP is internal and has no threat intel hits doesn't mean a port scan is benign.

## Real-World Scenarios

### Scenario 1: BENIGN - Authorized Vulnerability Scanner
```json
{
  "ip": "*************",
  "ip_type": "internal",
  "asset_info": {
    "hostname": "prod-scanner-01.internal",
    "owner": "IT-Operations",
    "role": "Vulnerability Scanner",
    "authorized_to_scan": true
  },
  "user": "service-account-scanner",
  "time": "Tuesday 02:00 AM",
  "maintenance_window": true,
  "ports_scanned": [22, 80, 443, 3389],
  "scan_pattern": "systematic across subnet"
}

Verdict: LIKELY_BENIGN (95% confidence)
Reason: Authorized scanner during maintenance window
```

### Scenario 2: SUSPICIOUS - Compromised Workstation
```json
{
  "ip": "**************",
  "ip_type": "internal",
  "asset_info": {
    "hostname": "HR-LAPTOP-42",
    "owner": "<PERSON> (HR)",
    "role": "Employee Workstation",
    "authorized_to_scan": false
  },
  "user": "jane.doe",
  "time": "Wednesday 3:47 PM",
  "maintenance_window": false,
  "ports_scanned": [445, 3389, 135, 139],  // SMB, RDP
  "scan_pattern": "lateral movement pattern"
}

Verdict: LIKELY_MALICIOUS (80% confidence)
Reason: HR laptop scanning for SMB/RDP (potential lateral movement)
```

### Scenario 3: REQUIRES_INVESTIGATION - Workstation Off-Hours
```json
{
  "ip": "**************",
  "ip_type": "internal",
  "asset_info": {
    "hostname": "DEV-WORKSTATION-09",
    "owner": "Bob Smith (Developer)",
    "role": "Developer Workstation",
    "authorized_to_scan": "limited"
  },
  "user": "bob.smith",
  "time": "Saturday 11:30 PM",  // Off-hours
  "maintenance_window": false,
  "ports_scanned": [22, 80, 8080, 9000],
  "scan_pattern": "development ports"
}

Verdict: REQUIRES_INVESTIGATION (60% confidence)
Reason: Developer scanning at odd hours - could be legitimate testing or reconnaissance
```

## Improved Scoring Logic

### Factors to Consider

```python
# 1. Asset Context (+/-30 points)
if asset.role == "Vulnerability Scanner":
    confidence += 30
    reasoning.append("Source is authorized vulnerability scanner")
elif asset.role == "Server" or asset.role == "Network Device":
    confidence -= 10
    reasoning.append("Servers shouldn't normally scan")
elif asset.role == "Workstation":
    confidence -= 20
    reasoning.append("Workstations shouldn't scan network")

# 2. User Context (+/-20 points)
if user.startswith("service-account-"):
    confidence += 15
    reasoning.append("Service account - likely automated scan")
elif user.department == "Security" or user.department == "IT-Operations":
    confidence += 10
    reasoning.append("IT/Security staff - authorized for scanning")
else:
    confidence -= 15
    reasoning.append(f"{user.department} staff shouldn't perform network scans")

# 3. Time Context (+/-15 points)
if in_maintenance_window(scan_time):
    confidence += 15
    reasoning.append("Scan occurred during scheduled maintenance window")
elif is_business_hours(scan_time):
    confidence += 5
    reasoning.append("Scan during business hours")
else:
    confidence -= 10
    reasoning.append("Scan outside business hours - unusual")

# 4. Port Analysis (+/-25 points)
critical_ports = [445, 3389, 135, 139]  # SMB, RDP, Windows
dev_ports = [22, 80, 443, 8080, 9000]
db_ports = [1433, 3306, 5432, 27017]

if any(port in critical_ports for port in ports_scanned):
    confidence -= 25
    reasoning.append("Scanning critical ports (SMB/RDP) - potential lateral movement")
elif any(port in db_ports for port in ports_scanned):
    confidence -= 15
    reasoning.append("Scanning database ports - requires investigation")
elif all(port in dev_ports for port in ports_scanned):
    confidence += 10
    reasoning.append("Scanning standard web/dev ports")

# 5. Scan Pattern (+/-20 points)
if scan_count > 100 and scan_duration < 60:  # Fast scan
    confidence -= 20
    reasoning.append("Rapid scan pattern - potential reconnaissance")
elif scan_count < 20:
    confidence += 10
    reasoning.append("Limited scope scan - less concerning")

# 6. Historical Behavior (+30 points)
if similar_alerts_count > 5:
    if all_previous_false_positive:
        confidence += 30
        reasoning.append("This exact pattern has occurred 5+ times - always benign")
    else:
        confidence -= 15
        reasoning.append("This pattern has triggered multiple alerts before")
```

## Final Scoring Examples

### Example 1: Authorized Scanner
```
Base: 0
+ Threat intel clean: +40
+ Internal IP: +25
+ Low severity: +10
+ Authorized scanner: +30
+ Service account: +15
+ Maintenance window: +15
+ Standard ports: +10
= 145 confidence (capped at 95)
Verdict: LIKELY_BENIGN (95%)
Action: AUTO_CLOSE or REVIEW_AND_CLOSE
```

### Example 2: HR Laptop Scanning SMB
```
Base: 0
+ Threat intel clean: +40
+ Internal IP: +25
+ Low severity: +10
- Workstation shouldn't scan: -20
- Non-IT user: -15
- Critical ports (SMB/RDP): -25
- Fast scan pattern: -20
= -5 confidence
Verdict: LIKELY_MALICIOUS (85%)
Action: ESCALATE_IMMEDIATELY
```

### Example 3: Developer Off-Hours
```
Base: 0
+ Threat intel clean: +40
+ Internal IP: +25
+ Low severity: +10
- Developer workstation: -10
+ IT staff: +10
- Off-hours: -10
+ Dev ports: +10
= 75 confidence
Verdict: POSSIBLY_BENIGN (75%)
Action: QUICK_REVIEW
```

## Implementation Approach

### Step 1: Add Asset Database Lookup
```python
async def _get_asset_context(self, ip: str) -> Dict:
    """Get asset context from CMDB/asset database"""
    # Query asset database
    asset = await self.db.fetch_one("""
        SELECT hostname, owner, department, role,
               authorized_to_scan, criticality
        FROM asset_inventory
        WHERE ip_address = %s
    """, ip)

    return asset or {"role": "unknown"}
```

### Step 2: Add User Context Lookup
```python
async def _get_user_context(self, user: str) -> Dict:
    """Get user context from Active Directory/LDAP"""
    # Query AD or user database
    user_info = await self.ldap.get_user(user)

    return {
        "department": user_info.get("department"),
        "title": user_info.get("title"),
        "manager": user_info.get("manager"),
        "is_privileged": user_info.get("is_admin", False)
    }
```

### Step 3: Add Port Analysis
```python
def _analyze_ports(self, ports: List[int]) -> Dict:
    """Analyze which ports were scanned"""
    critical = [445, 3389, 135, 139, 1433, 3306]
    lateral_movement = [445, 3389, 5985, 5986]

    return {
        "has_critical": any(p in critical for p in ports),
        "has_lateral_movement": any(p in lateral_movement for p in ports),
        "is_database_focused": any(p in [1433, 3306, 5432] for p in ports),
        "risk_level": "high" if any(p in critical for p in ports) else "low"
    }
```

### Step 4: Add Time Context
```python
def _check_time_context(self, timestamp: datetime) -> Dict:
    """Check if scan occurred during expected time"""
    hour = timestamp.hour
    day = timestamp.weekday()  # 0=Monday

    # Check maintenance windows (e.g., Tuesdays 1-3 AM)
    is_maintenance = (day == 1 and 1 <= hour < 3)

    # Check business hours (8 AM - 6 PM)
    is_business_hours = (9 <= hour < 18)

    return {
        "is_maintenance_window": is_maintenance,
        "is_business_hours": is_business_hours,
        "is_off_hours": not is_business_hours and not is_maintenance,
        "day_of_week": ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"][day],
        "hour": hour
    }
```

## The Result

Instead of a simple "internal + clean = benign" logic, we get:

**Smart Context-Aware Verdicts:**
- ✅ Authorized scanner in maintenance window → AUTO_CLOSE
- ⚠️ Developer scanning off-hours → QUICK_REVIEW (with context)
- 🚨 HR laptop scanning SMB/RDP → ESCALATE_IMMEDIATELY

This is what makes SIEMLess intelligent - we don't just check threat intel, we understand the **business context** of the activity.
