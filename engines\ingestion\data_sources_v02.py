"""
Data Source Integrations from proven v0.2 implementation
Includes Elasticsearch and CrowdStrike with full scope support
"""

import os
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Generator

# Elasticsearch imports
from elasticsearch import Elasticsearch

# CrowdStrike imports
try:
    from falconpy import APIHarnessV2, Detects, EventStreams, Hosts, Alerts, Incidents, Intel, IOC
    FALCONPY_AVAILABLE = True
except ImportError:
    FALCONPY_AVAILABLE = False
    print("Warning: falconpy not installed. CrowdStrike integration will not be available.")

# Import scope configuration
from crowdstrike_scope_config import CrowdStrikeScopeManager, CrowdStrikeScope


class UniversalElasticIngestor:
    """
    A universal ingestor that can pull data from any Elasticsearch index
    based on a JSON definition file. (From proven v0.2 implementation)
    """

    def __init__(self, config: dict, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.client = self._create_client()
        self.logger.info(f"Elasticsearch ingestor initialized for: {self.config.get('source_name', 'elasticsearch')}")

        # Rate limiting state
        self.rate_limit_remaining = None
        self.rate_limit_reset_time = None

    def _create_client(self) -> Elasticsearch:
        """Creates and configures the Elasticsearch client."""
        # Try multiple credential options
        cloud_id = os.environ.get('ELASTIC_CLOUD_ID')
        api_key = os.environ.get('ELASTIC_API_KEY')

        # Alternative: direct URL connection
        es_url = os.environ.get('ELASTIC_URL', 'http://localhost:9200')
        username = os.environ.get('ELASTIC_USERNAME')
        password = os.environ.get('ELASTIC_PASSWORD')

        if cloud_id and api_key:
            self.logger.info("Connecting to Elastic Cloud")
            return Elasticsearch(cloud_id=cloud_id, api_key=api_key)
        elif username and password:
            self.logger.info(f"Connecting to Elasticsearch at {es_url} with basic auth")
            return Elasticsearch(
                hosts=[es_url],
                basic_auth=(username, password),
                verify_certs=False
            )
        else:
            self.logger.info(f"Connecting to Elasticsearch at {es_url} without auth")
            return Elasticsearch(hosts=[es_url], verify_certs=False)

    def _update_rate_limit_state(self, headers):
        """Updates the rate limit state from response headers."""
        remaining = headers.get('x-ratelimit-remaining')
        interval_str = headers.get('x-ratelimit-interval')

        if remaining is not None:
            self.rate_limit_remaining = int(remaining)

        if interval_str is not None:
            interval_seconds = int(interval_str.replace('s', ''))
            self.rate_limit_reset_time = time.time() + interval_seconds

    def _wait_for_rate_limit(self):
        """Waits for the rate limit to reset if necessary."""
        if self.rate_limit_remaining is not None and self.rate_limit_remaining < 10:
            if self.rate_limit_reset_time is not None:
                sleep_time = self.rate_limit_reset_time - time.time()
                if sleep_time > 0:
                    self.logger.warning(f"Rate limit quota low. Waiting for {sleep_time:.2f} seconds until reset.")
                    time.sleep(sleep_time)

    def test_connection(self) -> bool:
        """Test connection to Elasticsearch."""
        try:
            info = self.client.info()
            self.logger.info(f"✅ Connected to Elasticsearch {info['version']['number']}")

            # Try to get index information
            indices = self.client.indices.get_alias(index="*")
            self.logger.info(f"   Found {len(indices)} indices")
            return True
        except Exception as e:
            self.logger.error(f"❌ Elasticsearch connection failed: {e}")
            return False

    def fetch_logs(self, start_time: datetime = None, end_time: datetime = None,
                   max_records: int = 1000, index_pattern: str = "logs-*") -> Generator[Dict[str, Any], None, None]:
        """
        Fetches logs from Elasticsearch within a given time window.
        This method is a GENERATOR, yielding batches of logs to keep memory usage low.
        """
        if not start_time:
            start_time = datetime.utcnow() - timedelta(minutes=5)
        if not end_time:
            end_time = datetime.utcnow()

        timestamp_field = self.config.get('timestamp_field', '@timestamp')

        time_range_query = {
            "range": {
                timestamp_field: {
                    "gte": start_time.isoformat(),
                    "lte": end_time.isoformat(),
                    "format": "strict_date_optional_time"
                }
            }
        }

        final_query = {
            "bool": {
                "must": [time_range_query]
            }
        }

        self.logger.info(f"Querying Elasticsearch index '{index_pattern}' (max {max_records} records)...")
        total_fetched = 0

        try:
            self._wait_for_rate_limit()
            resp = self.client.search(
                index=index_pattern,
                query=final_query,
                size=min(100, max_records),
                scroll='2m'
            )

            scroll_id = resp.get('_scroll_id')
            hits = resp['hits']['hits']

            while hits and total_fetched < max_records:
                batch = []
                for hit in hits:
                    log_entry = {
                        'source': 'elasticsearch',
                        'id': hit['_id'],
                        'index': hit['_index'],
                        'timestamp': hit['_source'].get(timestamp_field, datetime.utcnow().isoformat()),
                        'data': hit['_source']
                    }
                    batch.append(log_entry)
                    total_fetched += 1

                    if total_fetched >= max_records:
                        break

                yield batch

                if total_fetched >= max_records:
                    break

                # Get next batch
                self._wait_for_rate_limit()
                resp = self.client.scroll(scroll_id=scroll_id, scroll='2m')
                hits = resp['hits']['hits']

            # Clear scroll
            if scroll_id:
                self.client.clear_scroll(scroll_id=scroll_id)

            self.logger.info(f"✅ Fetched {total_fetched} logs from Elasticsearch")

        except Exception as e:
            self.logger.error(f"❌ Error fetching logs: {e}")


class CrowdStrikeApiIngestor:
    """
    CrowdStrike API ingestor using FalconPy for multiple data types.
    Supports: detections, hosts, alerts, incidents, intel, and IOCs.
    (From proven v0.2 implementation)
    """

    # Endpoint configurations for different CrowdStrike API scopes
    ENDPOINT_CONFIGS = {
        'detections': {
            'query_command': 'QueryDetects',
            'details_command': 'GetDetectSummaries',
            'id_field': 'detection_id',
            'time_filter_field': 'first_behavior',
            'description': 'CrowdStrike Detection Events'
        },
        'hosts': {
            'query_command': 'QueryDevicesByFilterScroll',
            'details_command': 'GetDeviceDetails',
            'id_field': 'device_id',
            'time_filter_field': 'first_seen',
            'description': 'CrowdStrike Host/Device Information'
        },
        'alerts': {
            'query_command': 'QueryAlertsV2',
            'details_command': 'GetAlertsV2',
            'id_field': 'alert_id',
            'time_filter_field': 'timestamp',
            'description': 'CrowdStrike Alert Events'
        },
        'incidents': {
            'query_command': 'QueryIncidents',
            'details_command': 'GetIncidents',
            'id_field': 'incident_id',
            'time_filter_field': 'start',
            'description': 'CrowdStrike Incident Response Data'
        },
        'intel': {
            'query_command': 'QueryIntelIndicatorIds',
            'details_command': 'GetIntelIndicatorEntities',
            'id_field': 'id',
            'time_filter_field': 'published_date',
            'description': 'CrowdStrike Threat Intelligence'
        },
        'iocs': {
            'query_command': 'QueryIOCs',
            'details_command': 'GetIOCs',
            'id_field': 'id',
            'time_filter_field': 'created_timestamp',
            'description': 'CrowdStrike Indicators of Compromise'
        }
    }

    def __init__(self, config: dict, logger: logging.Logger):
        if not FALCONPY_AVAILABLE:
            raise ImportError("falconpy library not installed. Run: pip install crowdstrike-falconpy")

        self.config = config
        self.logger = logger

        # Get credentials from environment
        self.client_id = os.environ.get('CROWDSTRIKE_CLIENT_ID')
        self.client_secret = os.environ.get('CROWDSTRIKE_CLIENT_SECRET')
        self.base_url = os.environ.get('CROWDSTRIKE_BASE_URL', 'https://api.crowdstrike.com')

        if not self.client_id or not self.client_secret:
            raise ValueError("CrowdStrike credentials not found in environment variables")

        # Determine scope type
        self.scope_type = self._detect_scope_type(config)
        self.endpoint_config = self.ENDPOINT_CONFIGS.get(self.scope_type, self.ENDPOINT_CONFIGS['detections'])

        # Initialize scope manager
        self.scope_manager = CrowdStrikeScopeManager(self.client_id, "production")

        # Configure scopes from environment or config
        configured_scopes = os.environ.get('CROWDSTRIKE_SCOPES', '').split(',')
        if not configured_scopes[0]:
            # Default scopes
            configured_scopes = ['detections:read', 'hosts:read', 'incidents:read']

        scope_config = self.scope_manager.configure_scopes(configured_scopes)
        self.logger.info(f"CrowdStrike scopes configured: {scope_config['configured_scopes']}")

        # Initialize specialized FalconPy clients
        self._init_specialized_clients()

    def _detect_scope_type(self, config: dict) -> str:
        """Detect the CrowdStrike API scope type from configuration."""
        source_type = config.get('source_type', 'detections')
        if source_type in self.ENDPOINT_CONFIGS:
            return source_type
        return 'detections'  # Default

    def _init_specialized_clients(self):
        """Initialize specialized FalconPy clients for different scopes."""
        common_params = {
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'base_url': self.base_url
        }

        # Initialize appropriate client based on scope type
        if self.scope_type == 'detections':
            self.client = Detects(**common_params)
        elif self.scope_type == 'hosts':
            self.client = Hosts(**common_params)
        elif self.scope_type == 'alerts':
            self.client = Alerts(**common_params)
        elif self.scope_type == 'incidents':
            self.client = Incidents(**common_params)
        elif self.scope_type == 'intel':
            self.client = Intel(**common_params)
        elif self.scope_type == 'iocs':
            self.client = IOC(**common_params)
        else:
            self.client = APIHarnessV2(**common_params)

        self.logger.info(f"✅ CrowdStrike {self.endpoint_config['description']} ingestor initialized")

    def test_connection(self) -> bool:
        """Tests the connection to CrowdStrike API."""
        try:
            self.logger.info(f"🔍 Testing CrowdStrike {self.endpoint_config['description']} API access...")

            # Test with a simple query based on scope type
            if self.scope_type == 'detections':
                # Use QueryDetects method for Detects service
                response = self.client.query_detects(limit=1)
            elif self.scope_type == 'incidents':
                # Use QueryIncidents for Incidents service
                response = self.client.query_incidents(limit=1)
            elif self.scope_type == 'hosts':
                # Use QueryDevicesByFilter for Hosts service
                response = self.client.query_devices_by_filter(limit=1)
            elif self.scope_type == 'alerts':
                # Use QueryAlertsV1 for Alerts service
                response = self.client.query_alerts_v1(limit=1)
            else:
                # For other scopes, try to get a minimal query
                self.logger.warning(f"Test connection for scope {self.scope_type} not fully implemented")
                return True  # Assume connection is OK if we got this far

            if response['status_code'] == 200:
                self.logger.info(f"✅ CrowdStrike {self.scope_type} connection test successful")
                resource_count = len(response['body'].get('resources', []))
                self.logger.info(f"   Found {resource_count} recent {self.scope_type} record(s)")
                return True
            else:
                self.logger.error(f"❌ Connection test failed: {response['status_code']}")
                return False

        except Exception as e:
            self.logger.error(f"❌ CrowdStrike connection test failed: {e}")
            return False

    def fetch_detections(self, start_time: datetime = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Fetch detections from CrowdStrike."""
        if not start_time:
            start_time = datetime.utcnow() - timedelta(hours=1)

        try:
            # Query for detection IDs
            filter_str = f"first_behavior:>'{start_time.isoformat()}'"
            response = self.client.query_detects(filter=filter_str, limit=limit)

            if response['status_code'] != 200:
                self.logger.error(f"Failed to query detections: {response}")
                return []

            detection_ids = response['body'].get('resources', [])
            if not detection_ids:
                return []

            # Get detection details
            response = self.client.get_detect_summaries(body={'ids': detection_ids})

            if response['status_code'] != 200:
                return []

            detections = response['body'].get('resources', [])

            # Format for ingestion
            logs = []
            for detection in detections:
                log_entry = {
                    'source': 'crowdstrike',
                    'type': 'detection',
                    'id': detection.get('detection_id'),
                    'timestamp': detection.get('created_timestamp', datetime.utcnow().isoformat()),
                    'severity': detection.get('max_severity_displayname', 'unknown'),
                    'status': detection.get('status'),
                    'behaviors': detection.get('behaviors', []),
                    'device': detection.get('device', {}),
                    'data': detection
                }
                logs.append(log_entry)

            self.logger.info(f"✅ Fetched {len(logs)} detections from CrowdStrike")
            return logs

        except Exception as e:
            self.logger.error(f"Error fetching CrowdStrike detections: {e}")
            return []

    def fetch_incidents(self, start_time: datetime = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Fetch incidents from CrowdStrike."""
        if not start_time:
            start_time = datetime.utcnow() - timedelta(hours=24)

        try:
            # Query for incident IDs
            filter_str = f"start:>'{start_time.isoformat()}'"
            response = self.client.query_incidents(filter=filter_str, limit=limit)

            if response['status_code'] != 200:
                return []

            incident_ids = response['body'].get('resources', [])
            if not incident_ids:
                return []

            # Get incident details
            response = self.client.get_incidents(body={'ids': incident_ids})

            if response['status_code'] != 200:
                return []

            incidents = response['body'].get('resources', [])

            # Format for ingestion
            logs = []
            for incident in incidents:
                log_entry = {
                    'source': 'crowdstrike',
                    'type': 'incident',
                    'id': incident.get('incident_id'),
                    'timestamp': incident.get('start', datetime.utcnow().isoformat()),
                    'severity': incident.get('fine_score', 0),
                    'state': incident.get('state'),
                    'tactics': incident.get('tactics', []),
                    'techniques': incident.get('techniques', []),
                    'data': incident
                }
                logs.append(log_entry)

            self.logger.info(f"✅ Fetched {len(logs)} incidents from CrowdStrike")
            return logs

        except Exception as e:
            self.logger.error(f"Error fetching CrowdStrike incidents: {e}")
            return []

    def fetch_logs(self, start_time: datetime = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Main fetch method - gets data based on configured scope type."""
        if self.scope_type == 'detections':
            return self.fetch_detections(start_time, limit)
        elif self.scope_type == 'incidents':
            return self.fetch_incidents(start_time, limit)
        else:
            # Implement other scope types as needed
            self.logger.warning(f"Fetch not implemented for scope type: {self.scope_type}")
            return []


class DataSourceFactory:
    """Factory for creating data source instances based on v0.2 implementations."""

    @staticmethod
    def create_source(source_type: str, config: dict, logger: logging.Logger):
        """Create a data source instance based on type."""
        if source_type == 'elasticsearch':
            return UniversalElasticIngestor(config, logger)
        elif source_type == 'crowdstrike':
            return CrowdStrikeApiIngestor(config, logger)
        else:
            logger.warning(f"Unknown source type: {source_type}")
            return None


# Test the implementations
if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    print("\nTesting v0.2 Data Source Implementations")
    print("=" * 50)

    # Test Elasticsearch
    print("\n1. Testing Elasticsearch Source")
    try:
        es_config = {'source_name': 'test_elasticsearch', 'timestamp_field': '@timestamp'}
        es_source = UniversalElasticIngestor(es_config, logger)

        if es_source.test_connection():
            # Try to fetch some logs
            for batch in es_source.fetch_logs(max_records=10):
                print(f"   Got batch of {len(batch)} logs")
                break
    except Exception as e:
        print(f"   Elasticsearch test error: {e}")

    # Test CrowdStrike
    print("\n2. Testing CrowdStrike Source")
    try:
        cs_config = {'source_type': 'detections', 'source_name': 'test_crowdstrike'}
        cs_source = CrowdStrikeApiIngestor(cs_config, logger)

        if cs_source.test_connection():
            # Try to fetch some detections
            logs = cs_source.fetch_logs(limit=5)
            print(f"   Fetched {len(logs)} logs from CrowdStrike")
    except Exception as e:
        print(f"   CrowdStrike test error: {e}")