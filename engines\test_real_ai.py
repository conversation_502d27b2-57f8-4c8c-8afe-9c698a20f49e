#!/usr/bin/env python3
"""
Test REAL AI API calls through Intelligence Engine
"""

import json
import redis
import time

def test_real_ai_calls():
    print("=== Testing REAL AI API Calls ===")

    redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)

    # Test with complex request that should trigger high-quality AI models
    security_event = {
        'request_id': f'real_ai_test_{int(time.time())}',
        'pattern_data': {
            'event_type': 'suspicious_powershell_execution',
            'source_ip': '*************',
            'target_host': 'DC01.company.local',
            'user': 'administrator',
            'command': 'powershell.exe -ExecutionPolicy Bypass -EncodedCommand SQBuAHYAbwBrAGUALQBXAGUAYgBSAGUAcQB1AGUAcwB0AC4ALgAuAA==',
            'process_tree': ['cmd.exe', 'powershell.exe', 'rundll32.exe'],
            'network_connections': ['suspicious-domain.com:443', '*************:445'],
            'file_modifications': ['C:\\Windows\\Temp\\update.exe', 'C:\\Users\\<USER>\\AppData\\malware.dll'],
            'indicators': {
                'encoded_commands': True,
                'lateral_movement': True,
                'privilege_escalation': True,
                'persistence_mechanism': True
            }
        },
        'complexity': 'critical',  # This should trigger Claude Opus + GPT-4
        'task': 'advanced_threat_analysis_and_incident_response'
    }

    print(f"[SEND] Real AI analysis request: {security_event['request_id']}")
    redis_client.publish('intelligence.consensus', json.dumps(security_event))

    # Monitor for responses
    pubsub = redis_client.pubsub()
    pubsub.subscribe('intelligence.consensus_result')

    print("[MONITOR] Waiting for REAL AI response...")
    start_time = time.time()

    for message in pubsub.listen():
        if time.time() - start_time > 60:  # 60 second timeout for real API calls
            print("[TIMEOUT] No response received in 60 seconds")
            break

        if message['type'] == 'message':
            response = json.loads(message['data'])
            print(f"\n[RESPONSE] Real AI Analysis Result:")
            print(json.dumps(response, indent=2))

            # Check if this is a real API response or mock
            consensus_data = response.get('data', {}).get('consensus', {})
            responses = consensus_data.get('responses', [])

            print(f"\n[ANALYSIS] Checking response authenticity:")
            for i, resp in enumerate(responses):
                model = resp.get('model', 'unknown')
                result = resp.get('result', '')
                reasoning = resp.get('reasoning', '')

                print(f"  Model {i+1}: {model}")
                print(f"  Result length: {len(result)} characters")
                print(f"  Reasoning: {reasoning}")

                # Check if it's a mock response
                if 'Mock analysis' in result or 'API unavailable' in result:
                    print(f"  [MOCK] This is a mock response")
                elif len(result) > 100 and ('threat' in result.lower() or 'security' in result.lower()):
                    print(f"  [REAL] This appears to be a real AI analysis")
                else:
                    print(f"  [UNKNOWN] Response type unclear")

            break

if __name__ == "__main__":
    test_real_ai_calls()