import type { Meta, StoryObj } from '@storybook/react'
import { AlertQueue } from './AlertQueue'

const meta: Meta<typeof AlertQueue> = {
  title: 'Widgets/AlertQueue',
  component: AlertQueue,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Real-time alert queue with AG-Grid displaying security alerts with severity, confidence scores, and entity relationships.'
      }
    }
  },
  decorators: [
    (Story) => (
      <div style={{ height: '600px', width: '100%' }}>
        <Story />
      </div>
    )
  ],
  tags: ['autodocs'],
  argTypes: {
    refreshInterval: {
      control: { type: 'number', min: 1000, max: 60000, step: 1000 },
      description: 'Auto-refresh interval in milliseconds'
    },
    onAlertSelect: {
      action: 'alert-selected',
      description: 'Callback when an alert is selected'
    }
  }
}

export default meta
type Story = StoryObj<typeof meta>

// Default story
export const Default: Story = {
  args: {
    refreshInterval: 5000
  }
}

// Story with no refresh
export const NoAutoRefresh: Story = {
  args: {
    refreshInterval: 0
  }
}

// Story with fast refresh
export const FastRefresh: Story = {
  args: {
    refreshInterval: 2000
  },
  parameters: {
    docs: {
      description: {
        story: 'Alert queue with 2-second refresh interval for high-frequency monitoring'
      }
    }
  }
}

// Story with selection callback
export const WithSelectionCallback: Story = {
  args: {
    refreshInterval: 5000,
    onAlertSelect: (alert) => {
      console.log('Selected alert:', alert)
      // In real app, this would open alert details panel
    }
  }
}

// Story demonstrating different severity levels
export const SeverityShowcase: Story = {
  args: {
    refreshInterval: 0
  },
  parameters: {
    docs: {
      description: {
        story: 'Showcases all severity levels: Critical, High, Medium, and Low'
      }
    }
  }
}