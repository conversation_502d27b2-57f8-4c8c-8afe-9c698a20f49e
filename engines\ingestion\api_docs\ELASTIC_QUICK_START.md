# Elastic Plugin - Quick Start Guide

## 5-Minute Setup

### 1. Configure Credentials

Add to `.env` file:

```bash
# Option A: Elastic Cloud (Recommended)
ELASTIC_CLOUD_ID=YourDeployment:base64encodedstring
ELASTIC_API_KEY=your_api_key_here

# Option B: Self-Hosted
ELASTIC_URL=https://your-elastic:9200
ELASTIC_USERNAME=elastic
ELASTIC_PASSWORD=changeme
```

### 2. Restart Ingestion Engine

```bash
docker-compose restart ingestion_engine
```

### 3. Test Connection

```bash
curl http://localhost:8003/health
```

Should show:
```json
{
  "status": "healthy",
  "plugins": {
    "elastic": "connected"
  }
}
```

---

## Your First Query

### Python Example

```python
import redis
import json
from uuid import uuid4

# Connect to Redis
r = redis.Redis(host='localhost', port=6380, decode_responses=True)

# Create query
request_id = str(uuid4())
query = {
    'request_id': request_id,
    'query': {
        'query_type': 'hostname',
        'query_value': 'YOUR-COMPUTER-NAME',
        'categories': ['ASSET', 'DETECTION'],
        'time_range_hours': 24
    }
}

# Send request
r.publish('ingestion.pull_context', json.dumps(query))

# Get response
pubsub = r.pubsub()
pubsub.subscribe(f'ingestion.context_response.{request_id}')

for msg in pubsub.listen():
    if msg['type'] == 'message':
        result = json.loads(msg['data'])
        print(json.dumps(result, indent=2))
        break
```

### cURL Example

```bash
# Using Redis CLI
redis-cli -h localhost -p 6380 PUBLISH ingestion.pull_context '{
  "request_id": "test-123",
  "query": {
    "query_type": "ip",
    "query_value": "***********",
    "categories": ["NETWORK"],
    "time_range_hours": 1
  }
}'

# Subscribe to response
redis-cli -h localhost -p 6380 SUBSCRIBE ingestion.context_response.test-123
```

---

## Common Query Patterns

### Find All Activity for a Host

```json
{
  "query_type": "hostname",
  "query_value": "WORKSTATION-01",
  "categories": ["ASSET", "DETECTION", "LOG"],
  "time_range_hours": 168
}
```

### Investigate Suspicious IP

```json
{
  "query_type": "ip",
  "query_value": "***********",
  "categories": ["DETECTION", "NETWORK"],
  "time_range_hours": 24
}
```

### Track User Activity

```json
{
  "query_type": "user",
  "query_value": "admin",
  "categories": ["ASSET", "LOG"],
  "time_range_hours": 24
}
```

### Search for IOC

```json
{
  "query_type": "hash",
  "query_value": "d41d8cd98f00b204e9800998ecf8427e",
  "categories": ["DETECTION", "LOG"],
  "time_range_hours": 720
}
```

---

## Response Structure

Every query returns:

```json
{
  "request_id": "uuid",
  "status": "success",
  "context_results": {
    "elastic": [
      {
        "source_name": "elastic",
        "category": "ASSET|DETECTION|NETWORK|LOG",
        "confidence": 0.90,
        "data": {
          // Category-specific data
        },
        "timestamp": "2025-10-02T14:30:00Z",
        "metadata": {}
      }
    ]
  }
}
```

---

## Category Data Examples

### ASSET Response

```json
{
  "hostname": "DESKTOP-ABC",
  "ip_addresses": ["*************"],
  "os": "Windows 10 Pro",
  "first_seen": "2025-10-01T00:00:00Z",
  "last_seen": "2025-10-02T14:30:00Z",
  "log_count": 1234
}
```

### DETECTION Response

```json
{
  "alert_id": "abc-123",
  "rule_name": "Suspicious PowerShell",
  "severity": "high",
  "risk_score": 75,
  "process": "powershell.exe",
  "command_line": "powershell -enc ..."
}
```

### NETWORK Response

```json
{
  "source_ip": "*************",
  "destination_ip": "***********",
  "destination_port": 445,
  "protocol": "tcp",
  "bytes_sent": 1024
}
```

---

## Troubleshooting

### "No results found"

1. Check time range (try increasing)
2. Verify data exists in Elastic
3. Use wildcards: `"192.168"` instead of exact IP

### "Connection failed"

1. Test Elastic credentials manually
2. Verify ELASTIC_CLOUD_ID format
3. Check firewall/network access

### "Permission denied"

Update API key permissions in Kibana:
- Read access to logs-*, filebeat-*, winlogbeat-*
- Monitor cluster
- View index metadata

---

## Next Steps

1. **Read Full API Docs**: [elastic_plugin_api.md](elastic_plugin_api.md)
2. **Integrate with Use Case Analysis**: See AI investigation workflows
3. **Community Contribution**: Share successful query patterns
4. **Advanced Features**: AI-powered query generation

---

**Questions?** See [elastic_plugin_api.md](elastic_plugin_api.md) for complete documentation
