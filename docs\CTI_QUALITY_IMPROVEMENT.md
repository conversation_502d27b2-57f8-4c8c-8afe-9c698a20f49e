# CTI Rule Quality Improvement Action Plan

## Executive Summary

**Current State**: 711 detection rules with only **7-20% immediately actionable**
**Goal**: Achieve **60%+ actionable rules** within 30 days

## The Problem: Signal vs Noise

### Current Rule Breakdown
```
Total Rules: 711
├── High Value (7%): 51 rules - Named malware, specific campaigns
├── Medium Value (13%): 94 rules - Has context but generic
└── Low Value (80%): 566 rules - Scanners, honeypots, noise
```

### Why Rules Lack Quality

| Issue | Impact | Example |
|-------|--------|---------|
| **Honeypot Bias** | 50% of rules | "Honeypot Data - September 2025" |
| **Scanner Noise** | 30% of rules | IPs like 162.142.125.x (Censys scanner) |
| **No Context** | 80% lack labels | Just an IP with no attribution |
| **No Validation** | 0% tested | No verification if IOCs are still active |
| **Stale Data** | Unknown age | IOCs from months/years ago |

## Immediate Actions (Week 1)

### 1. Implement Quality Filtering

```python
# engines/backend/backend_engine.py
async def _process_cti_for_rules(self, cti_data, source):
    rules = []
    for ioc in iocs:
        rule = await self._create_ioc_rule(ioc, source)

        # ADD: Quality gate
        if rule and rule['quality_score'] >= 0.4:
            rules.append(rule)
        else:
            self.logger.debug(f"Skipped low-quality rule: {rule.get('name')}")

    return rules
```

**Expected Result**: Reduce rules by ~60%, keeping only quality ones

### 2. Filter Known Noise

```python
# engines/ingestion/cti_manager.py

NOISE_PATTERNS = {
    'ips': [
        # Known scanners
        '162.142.125.',  # Censys
        '167.94.138.',   # Shodan
        '167.94.145.',   # Shodan
        '167.94.146.',   # Shodan
        '71.6.135.',     # Shodan
        '71.6.146.',     # Shodan
        '71.6.158.',     # Shodan
        '71.6.165.',     # Shodan
        '71.6.167.',     # Shodan
        '80.82.77.',     # Shodan
        '89.248.167.',   # Mass scanner
        '89.248.172.',   # Mass scanner
    ],
    'domains': [
        'google.com',
        'microsoft.com',
        'amazon.com',
        'cloudflare.com'
    ]
}

def is_noise(self, ioc):
    value = ioc.get('value', '').lower()

    # Check IP patterns
    for pattern in self.NOISE_PATTERNS['ips']:
        if value.startswith(pattern):
            return True

    # Check domains
    for domain in self.NOISE_PATTERNS['domains']:
        if domain in value:
            return True

    return False
```

**Expected Result**: Remove ~200 scanner rules

### 3. Prioritize CTI Sources

```python
# Modify update intervals based on quality
self.update_intervals = {
    'opencti': 1800,    # 30 min - Your instance, highest value
    'threatfox': 3600,  # 1 hour - Malware focused
    'otx': 21600,       # 6 hours - Lower priority, mostly honeypots
}

# Or disable OTX honeypot data entirely
if source == 'otx' and 'honeypot' in name.lower():
    return None  # Skip honeypot data
```

## Week 2: Enrichment

### 4. Add Context Enrichment

```python
class IOCEnricher:
    """Add context to improve quality scores"""

    async def enrich_ip(self, ip_address):
        enrichments = {}

        # GeoIP lookup
        enrichments['country'] = await self.get_geoip(ip_address)

        # Check if cloud provider
        enrichments['is_cloud'] = self.is_cloud_ip(ip_address)

        # VirusTotal reputation (if API available)
        enrichments['vt_score'] = await self.check_virustotal(ip_address)

        # AbuseIPDB check
        enrichments['abuse_score'] = await self.check_abuseipdb(ip_address)

        return enrichments

    def calculate_enriched_score(self, base_score, enrichments):
        score = base_score

        # Boost for malicious reputation
        if enrichments.get('vt_score', 0) > 5:
            score += 0.2

        # Reduce for cloud IPs (likely legitimate)
        if enrichments.get('is_cloud'):
            score *= 0.5

        # Boost for high-risk countries
        if enrichments.get('country') in ['RU', 'CN', 'KP', 'IR']:
            score += 0.1

        return min(score, 1.0)
```

### 5. Implement MITRE Mapping

```python
# Map common malware to MITRE techniques
MALWARE_TTP_MAPPING = {
    'cobalt strike': ['T1055', 'T1086', 'T1053', 'T1543.003'],
    'emotet': ['T1566.001', 'T1059.001', 'T1547.001', 'T1055'],
    'lockbit': ['T1486', 'T1490', 'T1489', 'T1078'],
    'clearfake': ['T1566.002', 'T1204.002', 'T1189'],
}

def add_mitre_mapping(rule):
    labels = rule.get('labels', [])

    for malware, techniques in MALWARE_TTP_MAPPING.items():
        if malware in rule.get('name', '').lower():
            for technique in techniques:
                labels.append(f'attack.{technique.lower()}')

    rule['labels'] = labels
    return rule
```

## Week 3: Validation & Feedback

### 6. Add Active Validation

```python
class IOCValidator:
    """Validate IOCs are still active threats"""

    async def validate_domain(self, domain):
        """Check if domain is still resolving and malicious"""
        try:
            # DNS resolution check
            import socket
            socket.gethostbyname(domain)

            # Still resolving, check reputation
            return await self.check_domain_reputation(domain)
        except:
            # Not resolving, probably defunct
            return False

    async def validate_ip(self, ip):
        """Check if IP is still malicious"""
        # Check if responsive
        import subprocess
        result = subprocess.run(['ping', '-n', '1', ip], capture_output=True)

        if result.returncode != 0:
            return False  # Not responsive

        # Check current reputation
        return await self.check_ip_reputation(ip)
```

### 7. Track Rule Performance

```sql
-- Add table to track rule performance
CREATE TABLE rule_performance_metrics (
    rule_id UUID REFERENCES detection_rules(rule_id),
    matched_count INTEGER DEFAULT 0,
    false_positive_count INTEGER DEFAULT 0,
    true_positive_count INTEGER DEFAULT 0,
    last_matched TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Query to find effective rules
SELECT
    r.rule_data->>'name' as rule_name,
    rpm.matched_count,
    rpm.true_positive_count::float / NULLIF(rpm.matched_count, 0) as accuracy
FROM detection_rules r
JOIN rule_performance_metrics rpm ON r.rule_id = rpm.rule_id
WHERE rpm.matched_count > 0
ORDER BY accuracy DESC, matched_count DESC;
```

## Week 4: Automation

### 8. Automated Quality Reports

```bash
#!/bin/bash
# Daily quality report cron job

cd /opt/siemless/engines/backend

# Generate report
python rule_monitor.py > /tmp/cti_report_$(date +%Y%m%d).txt

# Check quality threshold
QUALITY_PCT=$(python -c "
from rule_monitor import RuleMonitor
monitor = RuleMonitor(db_config)
stats = monitor.assess_rule_quality()
print(stats['high_quality'] / stats['total'] * 100)
")

# Alert if quality drops
if (( $(echo "$QUALITY_PCT < 40" | bc -l) )); then
    echo "CTI Quality Alert: Only ${QUALITY_PCT}% high-quality rules" | \
        mail -s "CTI Quality Warning" <EMAIL>
fi

# Clean old/stale rules (>90 days, never matched)
psql siemless_v2 -c "
DELETE FROM detection_rules
WHERE created_at < NOW() - INTERVAL '90 days'
AND rule_id NOT IN (
    SELECT rule_id FROM rule_performance_metrics
    WHERE matched_count > 0
)"
```

### 9. Feedback Loop Implementation

```python
class RuleFeedback:
    """Learn from rule performance"""

    def adjust_quality_based_on_performance(self, rule_id):
        # Get performance metrics
        metrics = self.get_performance_metrics(rule_id)

        # Adjust quality score
        if metrics['false_positive_rate'] > 0.5:
            self.reduce_quality_score(rule_id, 0.3)
        elif metrics['true_positive_count'] > 10:
            self.increase_quality_score(rule_id, 0.2)

        # Auto-disable high FP rules
        if metrics['false_positive_rate'] > 0.8:
            self.disable_rule(rule_id)
```

## Success Metrics

### 30-Day Targets

| Metric | Current | Target | How to Measure |
|--------|---------|--------|----------------|
| **Total Rules** | 711 | 300-400 | `SELECT COUNT(*) FROM detection_rules` |
| **High Quality (>0.7)** | 0% | 40% | Quality score distribution |
| **MITRE Coverage** | 0.8% | 30% | Rules with attack.* labels |
| **Has Context** | 20% | 80% | Rules with labels/description |
| **Active IOCs** | Unknown | 90% | Validation checks |
| **Duplicate Rate** | 0% | <1% | Monitor for regressions |

### Quality Formula

```
Quality Score = (
    0.3 × Base Confidence +
    0.2 × Context Richness +
    0.2 × Source Reputation +
    0.15 × MITRE Mapping +
    0.15 × Validation Status
) - Penalties

Penalties:
- Scanner IP: -0.5
- Cloud IP: -0.3
- Old IOC (>30 days): -0.2
- High FP rate: -0.4
```

## Quick Wins Checklist

- [ ] Filter quality_score < 0.4 (1 hour)
- [ ] Block known scanner IPs (30 min)
- [ ] Disable OTX honeypot data (5 min)
- [ ] Add MITRE mappings for known malware (2 hours)
- [ ] Implement basic IP validation (1 day)
- [ ] Set up daily quality reports (1 hour)
- [ ] Create allowlist for legitimate services (1 hour)
- [ ] Add GeoIP enrichment (1 day)
- [ ] Track rule performance metrics (2 days)
- [ ] Implement feedback loop (1 week)

## Expected Outcomes

After implementing these improvements:

### Before
- 711 rules, 80% noise
- 0% with quality scores
- 51 actually useful rules
- No performance tracking
- No validation

### After (30 days)
- 350 rules, 80% actionable
- 100% quality scored
- 280+ useful rules
- Performance tracked
- Active validation

### ROI
- **Reduced false positives**: 70% reduction
- **Improved detection**: 5x more relevant rules
- **Analyst time saved**: 10 hours/week less noise
- **Better coverage**: 30% MITRE techniques mapped
- **Actionable intelligence**: 80% rules immediately useful

## Next Steps

1. **Immediate**: Implement quality filtering (Week 1 items)
2. **This Month**: Complete enrichment and validation
3. **Next Quarter**: Machine learning for automatic quality scoring
4. **Long Term**: Build proprietary threat intelligence from detections