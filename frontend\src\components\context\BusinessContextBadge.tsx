/**
 * Business Context Badge
 * Shows a badge when an entity has organizational context
 */

import React, { useState, useEffect } from 'react'
import { Building2, Info } from 'lucide-react'
import { apiRequest } from '../../api/services'

interface BusinessContext {
  context_id: string
  entity_type: string
  entity_value: string
  context_label: string
  context_description: string
  business_unit?: string
  owner?: string
  criticality: string
  security_zone: string
  behavior_pattern?: {
    scheduled_jobs?: string[]
    normal_times?: string[]
    expected_traffic?: string[]
  }
}

interface BusinessContextBadgeProps {
  entityType: string
  entityValue: string
  showTooltip?: boolean
  inline?: boolean
}

export const BusinessContextBadge: React.FC<BusinessContextBadgeProps> = ({
  entityType,
  entityValue,
  showTooltip = true,
  inline = false
}) => {
  const [context, setContext] = useState<BusinessContext | null>(null)
  const [loading, setLoading] = useState(true)
  const [showDetails, setShowDetails] = useState(false)

  useEffect(() => {
    fetchContext()
  }, [entityType, entityValue])

  const fetchContext = async () => {
    try {
      const response = await apiRequest(`/context/entity/${entityType}/${encodeURIComponent(entityValue)}`)
      if (response.has_context) {
        setContext(response.context)
      }
    } catch (error) {
      console.error('Error fetching context:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading || !context) {
    return null
  }

  const CriticalityColor: Record<string, string> = {
    critical: 'bg-red-100 text-red-800 border-red-300',
    high: 'bg-orange-100 text-orange-800 border-orange-300',
    medium: 'bg-blue-100 text-blue-800 border-blue-300',
    low: 'bg-gray-100 text-gray-800 border-gray-300'
  }

  return (
    <div className={inline ? 'inline-flex items-center gap-2' : 'relative'}>
      {/* Badge */}
      <div
        className={`flex items-center gap-1.5 px-2 py-1 rounded-md border ${CriticalityColor[context.criticality]} text-xs font-medium cursor-pointer`}
        onMouseEnter={() => setShowDetails(true)}
        onMouseLeave={() => setShowDetails(false)}
      >
        <Building2 size={12} />
        <span>Known {entityType === 'host' ? 'Device' : entityType === 'user' ? 'User' : 'Entity'}</span>
        {showTooltip && <Info size={12} className="opacity-60" />}
      </div>

      {/* Tooltip */}
      {showTooltip && showDetails && (
        <div className="absolute z-50 bg-white border-2 border-gray-200 rounded-lg shadow-xl p-4 mt-2 min-w-[320px] max-w-md">
          <div className="space-y-3">
            {/* Header */}
            <div className="border-b pb-2">
              <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                <Building2 size={16} className="text-blue-600" />
                {context.context_label}
              </h4>
              <p className="text-xs text-gray-500 mt-1">
                {context.entity_type}: {context.entity_value}
              </p>
            </div>

            {/* Description */}
            {context.context_description && (
              <div>
                <p className="text-sm text-gray-700">{context.context_description}</p>
              </div>
            )}

            {/* Metadata */}
            <div className="grid grid-cols-2 gap-2 text-xs">
              {context.business_unit && (
                <div>
                  <span className="text-gray-500">Business Unit:</span>
                  <br />
                  <span className="font-medium text-gray-900">{context.business_unit}</span>
                </div>
              )}
              {context.owner && (
                <div>
                  <span className="text-gray-500">Owner:</span>
                  <br />
                  <span className="font-medium text-gray-900">{context.owner}</span>
                </div>
              )}
              <div>
                <span className="text-gray-500">Criticality:</span>
                <br />
                <span className={`font-medium ${
                  context.criticality === 'critical' ? 'text-red-600' :
                  context.criticality === 'high' ? 'text-orange-600' :
                  context.criticality === 'medium' ? 'text-blue-600' : 'text-gray-600'
                }`}>
                  {context.criticality.toUpperCase()}
                </span>
              </div>
              <div>
                <span className="text-gray-500">Security Zone:</span>
                <br />
                <span className="font-medium text-gray-900">{context.security_zone.toUpperCase()}</span>
              </div>
            </div>

            {/* Behavior Pattern */}
            {context.behavior_pattern && (
              <>
                {(context.behavior_pattern.scheduled_jobs?.length ||
                  context.behavior_pattern.normal_times?.length ||
                  context.behavior_pattern.expected_traffic?.length) && (
                  <div className="border-t pt-2">
                    <p className="text-xs font-semibold text-gray-700 mb-1">Normal Behavior:</p>
                    <div className="space-y-1 text-xs text-gray-600">
                      {context.behavior_pattern.scheduled_jobs?.length > 0 && (
                        <div>
                          <span className="text-gray-500">Jobs:</span>{' '}
                          {context.behavior_pattern.scheduled_jobs.join(', ')}
                        </div>
                      )}
                      {context.behavior_pattern.normal_times?.length > 0 && (
                        <div>
                          <span className="text-gray-500">Times:</span>{' '}
                          {context.behavior_pattern.normal_times.join(', ')}
                        </div>
                      )}
                      {context.behavior_pattern.expected_traffic?.length > 0 && (
                        <div>
                          <span className="text-gray-500">Traffic:</span>{' '}
                          {context.behavior_pattern.expected_traffic.join(', ')}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </>
            )}

            {/* Recommendation */}
            <div className="bg-blue-50 border-l-4 border-blue-500 p-2 text-xs">
              <p className="text-blue-900">
                <span className="font-semibold">✓ Recommendation:</span> This is a known entity with documented behavior.
                Verify activity matches expected patterns before escalating.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
