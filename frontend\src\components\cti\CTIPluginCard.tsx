/**
 * CTI Plugin Card Component
 * Displays individual CTI plugin status with actions
 */

import React from 'react'
import { RefreshCw, CheckCircle, XCircle, AlertCircle, Clock } from 'lucide-react'
import type { CTIPluginInfo } from '../../types/api'

interface CTIPluginCardProps {
  plugin: CTIPluginInfo
  onUpdate: (source: string) => void
  onTestCredentials: (source: string) => void
  updating: boolean
}

export const CTIPluginCard: React.FC<CTIPluginCardProps> = ({
  plugin,
  onUpdate,
  onTestCredentials,
  updating
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500'
      case 'updating':
        return 'bg-yellow-500 animate-pulse'
      case 'error':
        return 'bg-red-500'
      case 'disabled':
        return 'bg-gray-400'
      default:
        return 'bg-gray-300'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle size={20} className="text-green-600" />
      case 'updating':
        return <RefreshCw size={20} className="text-yellow-600 animate-spin" />
      case 'error':
        return <XCircle size={20} className="text-red-600" />
      case 'disabled':
        return <AlertCircle size={20} className="text-gray-600" />
      default:
        return null
    }
  }

  const getRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return `${days}d ago`
  }

  return (
    <div className="bg-white rounded-lg shadow p-6 border-l-4" style={{
      borderLeftColor: plugin.status === 'active' ? '#10b981' :
        plugin.status === 'error' ? '#ef4444' : '#9ca3af'
    }}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className={`w-3 h-3 rounded-full ${getStatusColor(plugin.status)}`}></div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {plugin.source.toUpperCase()}
            </h3>
            <p className="text-xs text-gray-500">
              {plugin.status === 'active' ? 'Operational' :
               plugin.status === 'updating' ? 'Updating...' :
               plugin.status === 'error' ? 'Error' : 'Disabled'}
            </p>
          </div>
        </div>
        {getStatusIcon(plugin.status)}
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <p className="text-xs text-gray-500">Total Indicators</p>
          <p className="text-2xl font-bold text-gray-900">
            {plugin.total_indicators.toLocaleString()}
          </p>
        </div>
        <div>
          <p className="text-xs text-gray-500">New Today</p>
          <p className="text-2xl font-bold text-green-600">
            +{plugin.new_indicators_today}
          </p>
        </div>
      </div>

      {/* Last Update */}
      <div className="flex items-center gap-2 text-sm text-gray-600 mb-4">
        <Clock size={14} />
        <span>Last update: {getRelativeTime(plugin.last_update)}</span>
      </div>

      {/* Error Message */}
      {plugin.error_message && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded text-sm text-red-700">
          <p className="font-medium">Error:</p>
          <p>{plugin.error_message}</p>
        </div>
      )}

      {/* Credentials Status */}
      <div className="flex items-center gap-2 mb-4">
        {plugin.credentials_valid ? (
          <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs font-medium">
            ✓ Credentials Valid
          </span>
        ) : (
          <span className="px-2 py-1 bg-red-100 text-red-700 rounded text-xs font-medium">
            ✗ Invalid Credentials
          </span>
        )}
      </div>

      {/* Actions */}
      <div className="flex gap-2">
        <button
          onClick={() => onUpdate(plugin.source)}
          disabled={updating || plugin.status === 'updating'}
          className="flex-1 px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 text-sm"
        >
          <RefreshCw size={14} className={updating ? 'animate-spin' : ''} />
          {updating ? 'Updating...' : 'Update Now'}
        </button>

        <button
          onClick={() => onTestCredentials(plugin.source)}
          className="px-3 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 text-sm"
        >
          Test
        </button>
      </div>
    </div>
  )
}

// Skeleton loader for CTI Plugin Card
export const CTIPluginCardSkeleton: React.FC = () => (
  <div className="bg-white rounded-lg shadow p-6 animate-pulse">
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center gap-3">
        <div className="w-3 h-3 rounded-full bg-gray-200"></div>
        <div>
          <div className="h-5 bg-gray-200 rounded w-24 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-16"></div>
        </div>
      </div>
      <div className="h-5 w-5 bg-gray-200 rounded"></div>
    </div>

    <div className="grid grid-cols-2 gap-4 mb-4">
      <div>
        <div className="h-3 bg-gray-200 rounded w-20 mb-2"></div>
        <div className="h-6 bg-gray-200 rounded w-16"></div>
      </div>
      <div>
        <div className="h-3 bg-gray-200 rounded w-16 mb-2"></div>
        <div className="h-6 bg-gray-200 rounded w-12"></div>
      </div>
    </div>

    <div className="h-4 bg-gray-200 rounded w-32 mb-4"></div>
    <div className="h-8 bg-gray-200 rounded w-24 mb-4"></div>
    <div className="flex gap-2">
      <div className="flex-1 h-9 bg-gray-200 rounded"></div>
      <div className="h-9 w-16 bg-gray-200 rounded"></div>
    </div>
  </div>
)
