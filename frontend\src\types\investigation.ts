/**
 * TypeScript types for Alert Investigation with Enrichment and Correlation
 */

export interface Alert {
  alert_id: string;
  title: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  status: 'open' | 'investigating' | 'resolved' | 'closed';
  timestamp: string;
  rule_id?: string;
  rule_name?: string;
  source: string;
  entities: AlertEntities;
  mitre_techniques?: string[];
  enrichment_status?: EnrichmentStatus;
  correlation_status?: CorrelationStatus;
  threat_score?: number;
  correlation_score?: number;
  created_at: string;
}

export interface AlertEntities {
  ip?: string[];
  user?: string[];
  host?: string[];
  domain?: string[];
  file?: string[];
  process?: string[];
  [key: string]: string[] | undefined;
}

export interface EnrichmentStatus {
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'not_found';
  total_entities: number;
  enriched_count: number;
  threat_indicators_found: number;
  timestamp?: string;
}

export interface CorrelationStatus {
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'not_found';
  total_related_events: number;
  correlation_score: number;
  attack_stages_detected: string[];
  timestamp?: string;
}

// Enrichment Data Types

export interface EnrichmentData {
  alert_id: string;
  status: 'completed' | 'pending' | 'not_found';
  enrichment?: {
    entities: EnrichedEntities;
    summary: EnrichmentSummary;
    timestamp: string;
  };
}

export interface EnrichedEntities {
  [entityType: string]: EnrichedEntity[];
}

export interface EnrichedEntity {
  value: string;
  type: string;
  basic?: BasicEnrichment;
  cti?: CTIEnrichment;
  environmental?: EnvironmentalEnrichment;
  threat_score: number;
  is_malicious: boolean;
  error?: string;
}

export interface BasicEnrichment {
  geolocation?: {
    country?: string;
    city?: string;
    latitude?: number;
    longitude?: number;
    asn?: string;
    org?: string;
  };
  whois?: {
    registrar?: string;
    created?: string;
    updated?: string;
    country?: string;
  };
  dns?: {
    domain?: string;
    ptr?: string;
  };
  ip_type?: 'internal' | 'external' | 'private' | 'public';
}

export interface CTIEnrichment {
  threat_score: number;
  is_malicious: boolean;
  sources: CTISource[];
  tags?: string[];
  first_seen?: string;
  last_seen?: string;
}

export interface CTISource {
  name: string;
  verdict: string;
  confidence: string;
  tags?: string[];
  description?: string;
  related_indicators?: number;
}

export interface EnvironmentalEnrichment {
  asset_name?: string;
  asset_type?: string;
  criticality?: 'critical' | 'high' | 'medium' | 'low';
  owner?: string;
  department?: string;
  user_info?: {
    full_name?: string;
    email?: string;
    department?: string;
    title?: string;
    privilege_level?: string;
  };
  baseline?: {
    typical_login_times?: string;
    typical_sources?: string[];
    unusual_activity?: string[];
  };
}

export interface EnrichmentSummary {
  total_entities: number;
  enriched_count: number;
  threat_indicators_found: number;
}

// Correlation Data Types

export interface CorrelationData {
  alert_id: string;
  status: 'completed' | 'pending' | 'not_found';
  correlation?: {
    related_events: RelatedEvent[];
    summary: CorrelationSummary;
    time_window: TimeWindow;
    mitre_chain: MITRETechniqueEntry[];
    attack_stages: string[];
    score: number;
    timestamp: string;
  };
}

export interface RelatedEvent {
  log_id: string;
  source_type: string;
  timestamp: string;
  entity_type: string;
  entity_value: string;
  log_data?: any;
  summary?: string;
}

export interface CorrelationSummary {
  total_related_events: number;
  events_by_entity_type: { [key: string]: number };
  mitre_chain: MITRETechniqueEntry[];
  correlation_score: number;
  attack_stages_detected: string[];
}

export interface TimeWindow {
  start: string;
  end: string;
  duration_minutes: number;
}

export interface MITRETechniqueEntry {
  technique: string;
  timestamp: string;
  event_id: string;
  tactic?: string;
  description?: string;
}

// MITRE ATT&CK Types

export interface MITRETechnique {
  id: string;
  name: string;
  tactic: string;
  description: string;
  detection_time: string;
  confidence: number;
  evidence: string[];
  sub_techniques?: string[];
}

// AI Analysis Types

export interface AIAnalysis {
  verdict: 'LIKELY_MALICIOUS' | 'LIKELY_BENIGN' | 'SUSPICIOUS' | 'UNKNOWN';
  confidence: number;
  risk_level: 'critical' | 'high' | 'medium' | 'low';
  key_findings: AIFinding[];
  reasoning: AIReasoning;
  recommended_actions: AIAction[];
  playbook_available?: PlaybookInfo;
  timestamp: string;
}

export interface AIFinding {
  title: string;
  description: string;
  risk: 'critical' | 'high' | 'medium' | 'low';
  evidence: string[];
}

export interface AIReasoning {
  why_malicious: string[];
  alternate_hypotheses: AlternateHypothesis[];
  supporting_evidence: string[];
}

export interface AlternateHypothesis {
  hypothesis: string;
  probability: number;
  reason_rejected: string;
}

export interface AIAction {
  priority: 'immediate' | 'short_term' | 'long_term';
  step_number: number;
  action: string;
  impact: string;
  automated: boolean;
  api_endpoint?: string;
}

export interface PlaybookInfo {
  name: string;
  steps_total: number;
  steps_automated: number;
  steps_manual: number;
  estimated_time: string;
}

// Investigation Timeline Types

export interface TimelineEvent {
  timestamp: string;
  type: 'alert' | 'enrichment' | 'correlation' | 'system' | 'analyst';
  title: string;
  description: string;
  details?: any;
  icon?: string;
  color?: string;
}

// Complete Investigation Type

export interface Investigation {
  investigation_id: string;
  alert: Alert;
  enrichment?: EnrichmentData;
  correlation?: CorrelationData;
  mitre_techniques?: MITRETechnique[];
  ai_analysis?: AIAnalysis;
  timeline?: TimelineEvent[];
  status: 'open' | 'investigating' | 'resolved' | 'closed';
  assigned_to?: string;
  created_at: string;
  updated_at: string;
}
