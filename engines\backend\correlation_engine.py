"""
Multi-Source Correlation Engine for Advanced Threat Detection
Handles correlation across different log sources to detect complex attack patterns
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import hashlib
import re
from collections import defaultdict

logger = logging.getLogger(__name__)


class CorrelationType(Enum):
    """Types of correlation rules"""
    SIMPLE = "simple"  # Single source, single condition
    CORRELATION = "correlation"  # Multiple sources, related events
    SEQUENCE = "sequence"  # Ordered sequence of events
    AGGREGATION = "aggregation"  # Statistical/threshold based
    BEHAVIORAL = "behavioral"  # Baseline deviation


class LogSourceType(Enum):
    """Types of log sources"""
    FIREWALL = "firewall"
    EDR = "edr"  # Endpoint Detection & Response
    PROXY = "proxy"
    DNS = "dns"
    AUTH = "auth"  # Authentication logs
    CLOUD = "cloud"
    EMAIL = "email"
    DLP = "dlp"  # Data Loss Prevention


@dataclass
class CorrelationRule:
    """Represents a multi-source correlation rule"""
    rule_id: str
    rule_name: str
    rule_type: CorrelationType
    use_case: str
    description: str

    # Sources and conditions
    required_sources: List[LogSourceType]
    conditions: List[Dict]  # Conditions per source
    correlation_logic: Dict  # How to correlate between sources

    # Timing
    time_window: int  # seconds
    sequence_ordered: bool = False

    # Thresholds
    threshold: int = 1
    severity: str = "medium"
    confidence: float = 0.7

    # Metadata
    mitre_techniques: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)


@dataclass
class CorrelationContext:
    """Maintains state for ongoing correlations"""
    context_id: str
    rule: CorrelationRule
    entity_id: str  # What we're correlating on (IP, user, host, etc)
    entity_type: str

    # State tracking
    events_matched: List[Dict] = field(default_factory=list)
    sources_seen: Set[str] = field(default_factory=set)
    conditions_met: Set[int] = field(default_factory=set)

    # Timing
    first_seen: datetime = field(default_factory=datetime.now)
    last_seen: datetime = field(default_factory=datetime.now)
    expires_at: datetime = field(default_factory=lambda: datetime.now() + timedelta(hours=1))

    # Scoring
    correlation_score: float = 0.0
    triggered: bool = False


class CorrelationEngine:
    """
    Advanced correlation engine for multi-source threat detection
    """

    def __init__(self, db_pool = None):
        self.db_pool = db_pool
        self.active_contexts: Dict[str, CorrelationContext] = {}
        self.rules: Dict[str, CorrelationRule] = {}
        self.source_capabilities: Dict[str, List[str]] = {}
        self._load_initial_rules()

    def _load_initial_rules(self):
        """Load pre-defined correlation rules"""

        # Lateral Movement Detection
        self.rules['lateral_movement'] = CorrelationRule(
            rule_id='CORR_001',
            rule_name='Lateral Movement Detection',
            rule_type=CorrelationType.SEQUENCE,
            use_case='lateral_movement',
            description='Detect lateral movement by correlating auth and network events',
            required_sources=[LogSourceType.AUTH, LogSourceType.FIREWALL, LogSourceType.EDR],
            conditions=[
                # Auth condition
                {
                    'source': 'auth',
                    'field': 'event_id',
                    'operator': 'equals',
                    'value': '4624',  # Windows logon
                    'description': 'Successful logon'
                },
                # Network condition
                {
                    'source': 'firewall',
                    'field': 'action',
                    'operator': 'equals',
                    'value': 'allow',
                    'additional': {
                        'port': [445, 139, 3389],  # SMB, RDP
                        'direction': 'internal'
                    }
                },
                # Process condition
                {
                    'source': 'edr',
                    'field': 'process_name',
                    'operator': 'in',
                    'value': ['psexec.exe', 'wmic.exe', 'powershell.exe'],
                    'description': 'Remote execution tool'
                }
            ],
            correlation_logic={
                'correlation_field': 'source_ip',  # Correlate on source IP
                'require_all': False,  # 2 out of 3 is enough
                'minimum_sources': 2
            },
            time_window=300,  # 5 minutes
            sequence_ordered=True,
            severity='high',
            confidence=0.85,
            mitre_techniques=['T1021', 'T1077']
        )

        # Data Exfiltration Detection
        self.rules['data_exfiltration'] = CorrelationRule(
            rule_id='CORR_002',
            rule_name='Data Exfiltration Detection',
            rule_type=CorrelationType.AGGREGATION,
            use_case='data_exfiltration',
            description='Detect potential data exfiltration through unusual upload patterns',
            required_sources=[LogSourceType.PROXY, LogSourceType.FIREWALL, LogSourceType.EDR],
            conditions=[
                # Large data transfer
                {
                    'source': 'proxy',
                    'field': 'bytes_out',
                    'operator': 'greater_than',
                    'value': 100000000,  # 100MB
                    'description': 'Large upload'
                },
                # Uncommon destination
                {
                    'source': 'firewall',
                    'field': 'destination_ip',
                    'operator': 'not_in',
                    'value': 'known_services',  # Would be a list of known IPs
                    'description': 'Unknown destination'
                },
                # File access spike
                {
                    'source': 'edr',
                    'field': 'file_events',
                    'operator': 'spike',
                    'value': 'baseline',  # Compare to baseline
                    'description': 'Unusual file access'
                }
            ],
            correlation_logic={
                'correlation_field': 'user',
                'aggregation': 'sum',
                'threshold_field': 'bytes_out',
                'threshold_value': 500000000  # 500MB total
            },
            time_window=3600,  # 1 hour
            threshold=3,  # At least 3 uploads
            severity='critical',
            confidence=0.9,
            mitre_techniques=['T1048', 'T1567']
        )

        # Ransomware Kill Chain
        self.rules['ransomware_chain'] = CorrelationRule(
            rule_id='CORR_003',
            rule_name='Ransomware Kill Chain Detection',
            rule_type=CorrelationType.BEHAVIORAL,
            use_case='ransomware',
            description='Detect ransomware behavior patterns across endpoints',
            required_sources=[LogSourceType.EDR],
            conditions=[
                # Shadow copy deletion
                {
                    'source': 'edr',
                    'field': 'command_line',
                    'operator': 'contains',
                    'value': 'vssadmin delete shadows',
                    'description': 'Shadow copy deletion'
                },
                # Encryption activity
                {
                    'source': 'edr',
                    'field': 'file_operation',
                    'operator': 'pattern',
                    'value': 'rapid_rename',  # Many file renames
                    'description': 'Mass file encryption'
                },
                # Process tree anomaly
                {
                    'source': 'edr',
                    'field': 'process_tree',
                    'operator': 'anomaly',
                    'value': 'baseline',
                    'description': 'Unusual process behavior'
                }
            ],
            correlation_logic={
                'correlation_field': 'hostname',
                'behavioral_score': True,
                'score_threshold': 0.8
            },
            time_window=600,  # 10 minutes
            severity='critical',
            confidence=0.95,
            mitre_techniques=['T1486', 'T1490']
        )

    async def process_event(self, event: Dict) -> Optional[List[Dict]]:
        """
        Process incoming event and check for correlations
        Returns list of triggered rules if any
        """
        triggered_rules = []

        # Extract event metadata
        source_type = event.get('source_type')
        entity_id = self._extract_entity(event)
        timestamp = self._parse_timestamp(event.get('timestamp'))

        # Check against all active rules
        for rule in self.rules.values():
            if self._is_source_relevant(source_type, rule):
                context = await self._get_or_create_context(rule, entity_id, event)

                if context:
                    # Update context with new event
                    await self._update_context(context, event, source_type)

                    # Check if correlation conditions are met
                    if await self._check_correlation(context):
                        triggered = await self._trigger_rule(context)
                        if triggered:
                            triggered_rules.append(triggered)

        # Clean up expired contexts
        await self._cleanup_contexts()

        return triggered_rules if triggered_rules else None

    def _extract_entity(self, event: Dict) -> str:
        """Extract correlating entity from event"""
        # Priority order for correlation entities
        for field in ['source_ip', 'user', 'hostname', 'destination_ip', 'email']:
            if field in event and event[field]:
                return event[field]

        # Fallback to event ID
        return event.get('event_id', 'unknown')

    def _is_source_relevant(self, source_type: str, rule: CorrelationRule) -> bool:
        """Check if the event source is relevant for this rule"""
        try:
            source_enum = LogSourceType(source_type.lower())
            return source_enum in rule.required_sources
        except:
            return False

    async def _get_or_create_context(
        self,
        rule: CorrelationRule,
        entity_id: str,
        event: Dict
    ) -> Optional[CorrelationContext]:
        """Get existing context or create new one"""

        context_key = f"{rule.rule_id}:{entity_id}"

        if context_key in self.active_contexts:
            context = self.active_contexts[context_key]

            # Check if context expired
            if datetime.now() > context.expires_at:
                del self.active_contexts[context_key]
                return await self._create_context(rule, entity_id, event)

            return context
        else:
            # Check if this event matches any condition for this rule
            if self._event_matches_conditions(event, rule):
                return await self._create_context(rule, entity_id, event)

        return None

    async def _create_context(
        self,
        rule: CorrelationRule,
        entity_id: str,
        event: Dict
    ) -> CorrelationContext:
        """Create new correlation context"""

        context = CorrelationContext(
            context_id=hashlib.sha256(f"{rule.rule_id}:{entity_id}:{datetime.now()}".encode()).hexdigest()[:16],
            rule=rule,
            entity_id=entity_id,
            entity_type=self._determine_entity_type(entity_id),
            expires_at=datetime.now() + timedelta(seconds=rule.time_window)
        )

        context_key = f"{rule.rule_id}:{entity_id}"
        self.active_contexts[context_key] = context

        # Store in database if available
        if self.db_pool:
            await self._store_context_db(context)

        return context

    def _event_matches_conditions(self, event: Dict, rule: CorrelationRule) -> bool:
        """Check if event matches any rule condition"""
        source_type = event.get('source_type', '').lower()

        for condition in rule.conditions:
            if condition.get('source') == source_type:
                if self._evaluate_condition(event, condition):
                    return True

        return False

    def _evaluate_condition(self, event: Dict, condition: Dict) -> bool:
        """Evaluate a single condition against an event"""
        field = condition.get('field')
        operator = condition.get('operator')
        expected_value = condition.get('value')
        actual_value = event.get(field)

        if actual_value is None:
            return False

        # Operator logic
        if operator == 'equals':
            return actual_value == expected_value
        elif operator == 'contains':
            return expected_value in str(actual_value)
        elif operator == 'greater_than':
            try:
                return float(actual_value) > float(expected_value)
            except:
                return False
        elif operator == 'in':
            return actual_value in expected_value
        elif operator == 'not_in':
            return actual_value not in expected_value
        elif operator == 'regex':
            return bool(re.match(expected_value, str(actual_value)))
        elif operator == 'pattern':
            # Special pattern matching (would need implementation)
            return self._match_pattern(actual_value, expected_value)

        return False

    def _match_pattern(self, value: Any, pattern: str) -> bool:
        """Match special patterns like 'rapid_rename' or 'spike'"""
        if pattern == 'rapid_rename':
            # Check for many file rename operations
            # This would need access to historical data
            return False  # Placeholder
        elif pattern == 'spike':
            # Check for statistical anomaly
            return False  # Placeholder

        return False

    async def _update_context(
        self,
        context: CorrelationContext,
        event: Dict,
        source_type: str
    ):
        """Update correlation context with new event"""

        # Add event to matched events
        context.events_matched.append({
            'timestamp': event.get('timestamp'),
            'source': source_type,
            'event_id': event.get('event_id'),
            'summary': self._summarize_event(event)
        })

        # Update sources seen
        context.sources_seen.add(source_type)

        # Check which conditions this event meets
        for i, condition in enumerate(context.rule.conditions):
            if condition.get('source') == source_type:
                if self._evaluate_condition(event, condition):
                    context.conditions_met.add(i)

        # Update timestamps
        context.last_seen = datetime.now()

        # Calculate correlation score
        context.correlation_score = self._calculate_score(context)

    def _calculate_score(self, context: CorrelationContext) -> float:
        """Calculate correlation score based on matched conditions"""

        rule = context.rule
        base_score = 0.0

        # Score based on conditions met
        if rule.rule_type == CorrelationType.CORRELATION:
            # For correlation rules, score based on sources seen
            min_sources = rule.correlation_logic.get('minimum_sources', len(rule.required_sources))
            source_score = len(context.sources_seen) / min_sources
            base_score = min(source_score, 1.0)

        elif rule.rule_type == CorrelationType.SEQUENCE:
            # For sequences, check order and completeness
            conditions_ratio = len(context.conditions_met) / len(rule.conditions)
            base_score = conditions_ratio

        elif rule.rule_type == CorrelationType.AGGREGATION:
            # For aggregation, check threshold
            event_count = len(context.events_matched)
            threshold_ratio = event_count / rule.threshold
            base_score = min(threshold_ratio, 1.0)

        elif rule.rule_type == CorrelationType.BEHAVIORAL:
            # For behavioral, use anomaly scoring
            base_score = self._calculate_anomaly_score(context)

        # Apply confidence modifier
        final_score = base_score * rule.confidence

        return min(final_score, 1.0)

    def _calculate_anomaly_score(self, context: CorrelationContext) -> float:
        """Calculate anomaly score for behavioral rules"""
        # This would implement behavioral analysis
        # For now, return a placeholder
        return 0.7

    async def _check_correlation(self, context: CorrelationContext) -> bool:
        """Check if correlation conditions are met"""

        rule = context.rule

        # Check score threshold
        score_threshold = rule.correlation_logic.get('score_threshold', 0.7)
        if context.correlation_score < score_threshold:
            return False

        # Check minimum sources
        if 'minimum_sources' in rule.correlation_logic:
            if len(context.sources_seen) < rule.correlation_logic['minimum_sources']:
                return False

        # Check if all required conditions are met
        if rule.correlation_logic.get('require_all', False):
            if len(context.conditions_met) < len(rule.conditions):
                return False

        # Check threshold for aggregation rules
        if rule.rule_type == CorrelationType.AGGREGATION:
            if len(context.events_matched) < rule.threshold:
                return False

        # Don't trigger multiple times
        if context.triggered:
            return False

        return True

    async def _trigger_rule(self, context: CorrelationContext) -> Dict:
        """Trigger a correlation rule and generate alert"""

        context.triggered = True
        rule = context.rule

        alert = {
            'alert_id': hashlib.sha256(f"{context.context_id}:{datetime.now()}".encode()).hexdigest()[:16],
            'rule_id': rule.rule_id,
            'rule_name': rule.rule_name,
            'use_case': rule.use_case,
            'severity': rule.severity,
            'confidence': context.correlation_score,
            'entity': {
                'id': context.entity_id,
                'type': context.entity_type
            },
            'evidence': {
                'events_count': len(context.events_matched),
                'sources': list(context.sources_seen),
                'first_seen': context.first_seen.isoformat(),
                'last_seen': context.last_seen.isoformat(),
                'events': context.events_matched[-10:]  # Last 10 events
            },
            'mitre_techniques': rule.mitre_techniques,
            'description': self._generate_alert_description(context),
            'recommended_actions': self._get_recommended_actions(rule.use_case),
            'timestamp': datetime.now().isoformat()
        }

        # Store alert in database
        if self.db_pool:
            await self._store_alert_db(alert)

        logger.warning(f"CORRELATION ALERT: {rule.rule_name} triggered for {context.entity_id}")

        return alert

    def _generate_alert_description(self, context: CorrelationContext) -> str:
        """Generate human-readable alert description"""

        rule = context.rule

        if rule.use_case == 'lateral_movement':
            return (f"Potential lateral movement detected for {context.entity_id}. "
                   f"Observed {len(context.events_matched)} suspicious events across "
                   f"{len(context.sources_seen)} log sources including authentication, "
                   f"network connections, and process execution.")

        elif rule.use_case == 'data_exfiltration':
            return (f"Possible data exfiltration by {context.entity_id}. "
                   f"Detected unusual data transfer patterns with "
                   f"{len(context.events_matched)} large uploads to uncommon destinations.")

        elif rule.use_case == 'ransomware':
            return (f"Ransomware behavior detected on {context.entity_id}. "
                   f"Multiple indicators including shadow copy deletion and "
                   f"mass file encryption patterns observed.")

        return f"{rule.description} detected for {context.entity_id}"

    def _get_recommended_actions(self, use_case: str) -> List[str]:
        """Get recommended response actions for use case"""

        actions = {
            'lateral_movement': [
                'Isolate affected systems from network',
                'Reset credentials for affected accounts',
                'Review authentication logs for anomalies',
                'Check for persistence mechanisms',
                'Perform memory analysis on affected hosts'
            ],
            'data_exfiltration': [
                'Block communication to suspicious destinations',
                'Disable affected user accounts',
                'Review file access logs',
                'Check for data staging locations',
                'Notify data protection team'
            ],
            'ransomware': [
                'Immediately isolate affected systems',
                'Preserve evidence for forensics',
                'Check backups availability',
                'Identify patient zero',
                'Activate incident response plan'
            ]
        }

        return actions.get(use_case, ['Investigate alert', 'Gather additional evidence'])

    def _summarize_event(self, event: Dict) -> str:
        """Create a brief summary of an event"""
        event_type = event.get('event_type', 'Unknown')
        action = event.get('action', event.get('event_name', ''))
        target = event.get('destination', event.get('target', ''))

        return f"{event_type}: {action} -> {target}"[:100]

    def _determine_entity_type(self, entity_id: str) -> str:
        """Determine the type of entity from its ID"""

        # IP address pattern
        if re.match(r'^(?:\d{1,3}\.){3}\d{1,3}$', entity_id):
            return 'ip_address'

        # Email pattern
        if '@' in entity_id:
            return 'email'

        # Hostname pattern
        if '.' in entity_id and not entity_id.replace('.', '').isdigit():
            return 'hostname'

        # Username (default)
        return 'username'

    def _parse_timestamp(self, timestamp: Any) -> datetime:
        """Parse various timestamp formats"""
        if isinstance(timestamp, datetime):
            return timestamp

        if isinstance(timestamp, str):
            formats = [
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%d %H:%M:%S'
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(timestamp, fmt)
                except:
                    continue

        return datetime.now()

    async def _cleanup_contexts(self):
        """Remove expired correlation contexts"""
        expired = []

        for key, context in self.active_contexts.items():
            if datetime.now() > context.expires_at:
                expired.append(key)

        for key in expired:
            del self.active_contexts[key]

        if expired:
            logger.debug(f"Cleaned up {len(expired)} expired contexts")

    async def _store_context_db(self, context: CorrelationContext):
        """Store context in database"""
        if not self.db_pool:
            return

        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO correlation_contexts
                    (context_id, rule_id, entity_id, entity_type, status, expires_at)
                    VALUES ($1, $2, $3, $4, 'active', $5)
                """, context.context_id, context.rule.rule_id, context.entity_id,
                    context.entity_type, context.expires_at)
        except Exception as e:
            logger.error(f"Failed to store context: {e}")

    async def _store_alert_db(self, alert: Dict):
        """Store alert in database"""
        if not self.db_pool:
            return

        try:
            async with self.db_pool.acquire() as conn:
                # Store in cases table or alert table
                await conn.execute("""
                    INSERT INTO cases
                    (case_id, title, severity, status, description, created_at)
                    VALUES ($1, $2, $3, 'open', $4, $5)
                """, alert['alert_id'], alert['rule_name'], alert['severity'],
                    json.dumps(alert), datetime.now())
        except Exception as e:
            logger.error(f"Failed to store alert: {e}")

    async def get_active_correlations(self) -> List[Dict]:
        """Get all active correlation contexts"""
        active = []

        for context in self.active_contexts.values():
            if not context.triggered:
                active.append({
                    'context_id': context.context_id,
                    'rule_name': context.rule.rule_name,
                    'entity': context.entity_id,
                    'score': context.correlation_score,
                    'sources_seen': list(context.sources_seen),
                    'expires_in': (context.expires_at - datetime.now()).seconds
                })

        return active

    async def get_rule_statistics(self) -> Dict:
        """Get statistics about correlation rules"""
        stats = {
            'total_rules': len(self.rules),
            'active_contexts': len(self.active_contexts),
            'rules_by_type': defaultdict(int),
            'rules_by_use_case': defaultdict(int),
            'coverage': {}
        }

        for rule in self.rules.values():
            stats['rules_by_type'][rule.rule_type.value] += 1
            stats['rules_by_use_case'][rule.use_case] += 1

        # Calculate coverage
        all_sources = set()
        for rule in self.rules.values():
            all_sources.update(rule.required_sources)

        stats['coverage']['sources_required'] = [s.value for s in all_sources]
        stats['coverage']['use_cases'] = list(stats['rules_by_use_case'].keys())

        return dict(stats)