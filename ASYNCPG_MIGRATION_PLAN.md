# AsyncPG Migration Plan - SIEMLess v2.0

**Date**: October 4, 2025
**Status**: 🚀 READY TO EXECUTE
**Decision**: Full asyncpg migration to fix database connection pool exhaustion

---

## Executive Summary

**Why asyncpg:**
- ✅ Designed for async/await Python (aiohttp, FastAPI, etc.)
- ✅ 3-5x faster than psycopg2/aiopg
- ✅ Native connection pooling (no leaks)
- ✅ Never blocks event loop
- ✅ Industry standard for async Python + PostgreSQL
- ✅ Already used in 2 files (cti_rule_generator.py, pattern_storage.py)

**Why NOT psycopg2 + ThreadPoolExecutor:**
- ❌ Thread overhead and context switching
- ❌ Complex error handling across thread boundaries
- ❌ Doesn't scale well under load
- ❌ Band-aid solution that will need replacement later

**Estimated Timeline**: 6-8 hours total work

---

## Scope Assessment

### Database Operations Count (by Engine)

| Engine | Cursor Operations | Files to Modify | Priority |
|--------|------------------|----------------|----------|
| **Backend** | 27+ operations | ~8 files | P0 (Critical - serves frontend) |
| **Contextualization** | 6 operations | ~3 files | P1 (High - data pipeline) |
| **Ingestion** | 6 operations | ~3 files | P1 (High - data intake) |
| **Delivery** | 1 operation | ~2 files | P2 (Medium - frontend serving) |
| **Intelligence** | 0 operations | ~1 file | P3 (Low - uses Redis mostly) |

**Total**: ~40 cursor operations across ~17 files

### Core Files to Modify

**Phase 0: Foundation (1 hour)**
1. `engines/base_engine.py` - Base class for all engines ⭐ CRITICAL
2. All `engines/*/requirements.txt` - Add asyncpg, ensure consistency

**Phase 1: Backend Engine (2 hours)** - PRIORITY
3. `engines/backend/backend_engine.py` - Main backend engine (27 operations)
4. `engines/backend/update_scheduler.py` - Rule update scheduling
5. `engines/backend/source_update_manager.py` - Source management
6. `engines/backend/correlation_engine.py` - Correlation logic
7. `engines/backend/mitre_attack_mapper.py` - MITRE mappings

**Phase 2: Data Pipeline (1.5 hours)**
8. `engines/contextualization/contextualization_engine.py` - Entity extraction (6 ops)
9. `engines/ingestion/ingestion_engine.py` - Data ingestion (6 ops)

**Phase 3: Frontend & Intelligence (1 hour)**
10. `engines/delivery/delivery_engine.py` - Frontend APIs (1 op)
11. `engines/intelligence/intelligence_engine.py` - AI consensus (verify no ops)

**Phase 4: Testing & Validation (1.5 hours)**
- Individual engine tests
- Integration tests
- Load testing
- Monitoring setup

---

## Migration Pattern

### Current Pattern (psycopg2 - BROKEN)

```python
# base_engine.py
import psycopg2

class BaseEngine:
    def _setup_database(self):
        connection = psycopg2.connect(...)
        connection.autocommit = True
        return connection

# Any engine file
async def handler(self):
    cursor = self.db_connection.cursor()  # Sync cursor
    cursor.execute("SELECT * FROM table WHERE id = %s", (id,))
    result = cursor.fetchall()
    cursor.close()
    # Problem: Blocks event loop during query execution
```

### Target Pattern (asyncpg - CORRECT)

```python
# base_engine.py
import asyncpg

class BaseEngine:
    async def _setup_database(self):
        self.db_pool = await asyncpg.create_pool(
            host=os.getenv('POSTGRES_HOST', 'localhost'),
            port=int(os.getenv('POSTGRES_PORT', 5432)),
            database=os.getenv('POSTGRES_DB', 'siemless_v2'),
            user=os.getenv('POSTGRES_USER', 'siemless'),
            password=os.getenv('POSTGRES_PASSWORD', 'password'),
            min_size=5,
            max_size=20,
            command_timeout=60
        )
        return self.db_pool

# Any engine file
async def handler(self):
    async with self.db_pool.acquire() as conn:
        result = await conn.fetch(
            "SELECT * FROM table WHERE id = $1",  # Note: $1 not %s
            id
        )
        # Returns: List[asyncpg.Record]
        # Connection auto-returns to pool ✅
```

---

## Key API Changes

### 1. Connection Setup

```python
# OLD (psycopg2):
connection = psycopg2.connect(host=..., database=..., user=..., password=...)
connection.autocommit = True

# NEW (asyncpg):
pool = await asyncpg.create_pool(host=..., database=..., user=..., password=...)
# Auto-commit by default, transactions explicit
```

### 2. Query Execution

```python
# OLD (psycopg2):
cursor = connection.cursor()
cursor.execute("SELECT * FROM table WHERE id = %s", (id,))
rows = cursor.fetchall()
cursor.close()

# NEW (asyncpg):
async with pool.acquire() as conn:
    rows = await conn.fetch("SELECT * FROM table WHERE id = $1", id)
```

### 3. Placeholder Syntax

| Operation | psycopg2 | asyncpg |
|-----------|----------|---------|
| Single param | `WHERE id = %s` | `WHERE id = $1` |
| Multiple params | `WHERE id = %s AND name = %s` | `WHERE id = $1 AND name = $2` |
| IN clause | `WHERE id IN %s` (tuple) | `WHERE id = ANY($1::int[])` (list) |

### 4. Return Types

```python
# psycopg2 returns:
# - fetchall() -> List[Dict] (with RealDictCursor)
# - fetchone() -> Dict or None

# asyncpg returns:
# - fetch() -> List[asyncpg.Record]
# - fetchrow() -> asyncpg.Record or None
# - fetchval() -> single value

# Convert Record to Dict:
row = await conn.fetchrow("SELECT * FROM table WHERE id = $1", id)
row_dict = dict(row)  # Simple conversion
```

### 5. Single Value Queries

```python
# OLD:
cursor.execute("SELECT COUNT(*) FROM table")
count = cursor.fetchone()[0]

# NEW:
count = await conn.fetchval("SELECT COUNT(*) FROM table")
```

### 6. INSERT with RETURNING

```python
# OLD:
cursor.execute("INSERT INTO table (name) VALUES (%s) RETURNING id", (name,))
new_id = cursor.fetchone()['id']

# NEW:
new_id = await conn.fetchval(
    "INSERT INTO table (name) VALUES ($1) RETURNING id",
    name
)
```

---

## Phase-by-Phase Implementation

### Phase 0: Foundation (1 hour)

**File**: `engines/base_engine.py`

**Changes**:
1. Replace `import psycopg2` with `import asyncpg`
2. Change `_setup_database()` to async function returning pool
3. Update `__init__()` to handle async initialization
4. Add pool health check to health endpoint

**Before**:
```python
def _setup_database(self) -> psycopg2.extensions.connection:
    connection = psycopg2.connect(...)
    connection.autocommit = True
    return connection
```

**After**:
```python
async def _setup_database(self) -> asyncpg.Pool:
    pool = await asyncpg.create_pool(
        host=os.getenv('POSTGRES_HOST', 'localhost'),
        port=int(os.getenv('POSTGRES_PORT', 5432)),
        database=os.getenv('POSTGRES_DB', 'siemless_v2'),
        user=os.getenv('POSTGRES_USER', 'siemless'),
        password=os.getenv('POSTGRES_PASSWORD', 'password'),
        min_size=5,
        max_size=20,
        command_timeout=60
    )
    self.logger.info(f"Database pool created: {pool.get_size()} connections")
    return pool
```

**Update all requirements.txt**:
```txt
# Remove or keep psycopg2-binary (might be used by other tools)
psycopg2-binary==2.9.7  # Optional - keep if needed elsewhere

# Add asyncpg (ensure consistent version)
asyncpg==0.29.0
```

---

### Phase 1: Backend Engine (2 hours)

**Priority**: P0 - This engine serves the frontend

**Files**:
- `engines/backend/backend_engine.py` (27 operations)
- `engines/backend/update_scheduler.py`
- `engines/backend/source_update_manager.py`
- `engines/backend/correlation_engine.py`
- `engines/backend/mitre_attack_mapper.py`

**Pattern to Replace** (appears 27 times):

```python
# Find this pattern:
cursor = self.db_connection.cursor()
cursor.execute("SELECT ... WHERE id = %s", (id,))
result = cursor.fetchall()
cursor.close()

# Replace with:
async with self.db_pool.acquire() as conn:
    result = await conn.fetch("SELECT ... WHERE id = $1", id)
```

**Common Operations**:

1. **List queries** (`fetchall` → `fetch`):
```python
# OLD:
cursor.execute("SELECT * FROM detection_rules WHERE quality_score > %s", (0.7,))
rules = cursor.fetchall()

# NEW:
rules = await conn.fetch("SELECT * FROM detection_rules WHERE quality_score > $1", 0.7)
```

2. **Single row** (`fetchone` → `fetchrow`):
```python
# OLD:
cursor.execute("SELECT * FROM table WHERE id = %s", (id,))
row = cursor.fetchone()

# NEW:
row = await conn.fetchrow("SELECT * FROM table WHERE id = $1", id)
```

3. **Single value** (`fetchone()[0]` → `fetchval`):
```python
# OLD:
cursor.execute("SELECT COUNT(*) FROM table")
count = cursor.fetchone()[0]

# NEW:
count = await conn.fetchval("SELECT COUNT(*) FROM table")
```

4. **INSERT/UPDATE/DELETE** (`execute` → `execute`):
```python
# OLD:
cursor.execute("UPDATE table SET status = %s WHERE id = %s", (status, id))
self.db_connection.commit()

# NEW:
await conn.execute("UPDATE table SET status = $1 WHERE id = $2", status, id)
# Note: No commit needed, auto-committed
```

---

### Phase 2: Data Pipeline (1.5 hours)

**Files**:
- `engines/contextualization/contextualization_engine.py` (6 ops)
- `engines/ingestion/ingestion_engine.py` (6 ops)

**Contextualization Engine** - Entity storage:
```python
# OLD:
cursor.execute("""
    INSERT INTO entities (entity_id, entity_type, entity_value, source_log_id)
    VALUES (%s, %s, %s, %s)
    ON CONFLICT (entity_id) DO NOTHING
""", (entity_id, entity_type, entity_value, log_id))

# NEW:
await conn.execute("""
    INSERT INTO entities (entity_id, entity_type, entity_value, source_log_id)
    VALUES ($1, $2, $3, $4)
    ON CONFLICT (entity_id) DO NOTHING
""", entity_id, entity_type, entity_value, log_id)
```

**Ingestion Engine** - Source tracking:
```python
# OLD:
cursor.execute("SELECT * FROM ingestion_sources WHERE source_id = %s", (source_id,))
source = cursor.fetchone()

# NEW:
source = await conn.fetchrow("SELECT * FROM ingestion_sources WHERE source_id = $1", source_id)
```

---

### Phase 3: Frontend & Intelligence (1 hour)

**Delivery Engine** - Minimal changes (1 operation):
```python
# OLD:
cursor.execute("SELECT * FROM cases WHERE case_id = %s", (case_id,))
case = cursor.fetchone()

# NEW:
case = await conn.fetchrow("SELECT * FROM cases WHERE case_id = $1", case_id)
```

**Intelligence Engine** - Verify no database operations (uses Redis primarily)

---

### Phase 4: Testing & Validation (1.5 hours)

**Test Strategy**:

1. **Unit Tests** (30 min):
```bash
# Test each engine individually
python -m pytest engines/backend/tests/ -v
python -m pytest engines/delivery/tests/ -v
python -m pytest engines/contextualization/tests/ -v
python -m pytest engines/ingestion/tests/ -v
python -m pytest engines/intelligence/tests/ -v
```

2. **Integration Tests** (30 min):
```bash
# Start all engines with asyncpg
docker-compose up --build -d

# Check health endpoints
for port in 8001 8002 8003 8004 8005; do
  echo "=== Engine on port $port ==="
  curl -s http://localhost:$port/health | python -m json.tool
done

# Should show:
# "database": "connected"
# "pool": {"size": 20, "available": 18, "in_use": 2}
```

3. **Load Testing** (30 min):
```bash
# Test connection pool under load
for i in {1..100}; do
  curl -X POST http://localhost:8002/api/detection/fidelity -d '{}' &
done

# Monitor pool:
watch -n 5 'curl -s http://localhost:8002/health | jq .pool'
```

4. **Monitoring Setup** (30 min):
- Add pool metrics to Prometheus
- Create Grafana dashboard for connection pool
- Set alerts for pool exhaustion

---

## Rollback Plan

**If asyncpg migration fails:**

1. **Immediate Rollback** (5 minutes):
```bash
git revert HEAD
docker-compose down
docker-compose up --build -d
```

2. **Partial Rollback** (Keep working engines):
- Rollback individual engine to psycopg2
- Keep engines that successfully migrated on asyncpg
- Isolate problematic engine for debugging

3. **Emergency Workaround** (1 hour):
- Revert to psycopg2 for all engines
- Remove `await` from database operations
- Add proper cursor cleanup (`cursor.close()`)
- Accept event loop blocking temporarily
- Schedule proper asyncpg migration later

---

## Risk Assessment

### Low Risk Items ✅
- `base_engine.py` modification (well-defined change)
- `requirements.txt` updates (additive, not breaking)
- Simple SELECT queries (straightforward conversion)
- Connection pool setup (asyncpg handles automatically)

### Medium Risk Items ⚠️
- Complex queries with JOINs (test thoroughly)
- Transactions (asyncpg uses explicit `async with conn.transaction()`)
- Error handling (different exception types)
- Type conversions (asyncpg.Record vs dict)

### High Risk Items 🔴
- Third-party libraries expecting psycopg2 (audit dependencies)
- Shared database connections across threads (won't work with asyncpg)
- Custom PostgreSQL types (may need type codecs)

**Mitigation**:
- Test each engine individually before integration
- Keep psycopg2 installed for backward compatibility
- Use feature flags to toggle between drivers if needed

---

## Success Criteria

### ✅ Definition of Done

**Functional Requirements**:
1. All 5 engines start successfully with asyncpg
2. All health endpoints return `"database": "connected"`
3. Zero "connection already closed" errors in logs
4. Frontend APIs return data (not 500 errors)
5. Connection pool stays healthy under load (< 80% utilization)

**Performance Requirements**:
1. Database queries complete in < 100ms (95th percentile)
2. Connection pool never exhausts (< 20 connections used)
3. No event loop blocking (all operations yield properly)
4. Throughput: Handle 100+ concurrent requests

**Code Quality Requirements**:
1. All tests passing
2. No asyncpg-related errors in logs
3. Proper error handling for database failures
4. Documentation updated in CLAUDE.md

---

## Post-Migration Cleanup

**After successful migration**:

1. **Remove psycopg2** (if not needed elsewhere):
```bash
# Check if anything else uses psycopg2
grep -r "import psycopg2" . --include="*.py" | grep -v ".pyc"

# If nothing uses it:
# Remove from all requirements.txt files
```

2. **Update CLAUDE.md**:
- Remove "Database Connection Pattern (CRITICAL)" section about psycopg2
- Add new section: "AsyncPG Best Practices"
- Document connection pool configuration
- Add asyncpg code examples

3. **Create Monitoring Dashboard**:
- Connection pool size over time
- Query execution times
- Connection checkout duration
- Pool exhaustion alerts

4. **Document Lessons Learned**:
- Why asyncpg was the right choice
- Common migration pitfalls
- Performance improvements observed
- Best practices for future development

---

## Timeline Summary

| Phase | Duration | Can Work in Parallel? |
|-------|----------|----------------------|
| Phase 0: Foundation | 1 hour | No - blocks everything |
| Phase 1: Backend Engine | 2 hours | No - after Phase 0 |
| Phase 2: Data Pipeline | 1.5 hours | **Yes** - after Phase 0 |
| Phase 3: Frontend & Intelligence | 1 hour | **Yes** - after Phase 0 |
| Phase 4: Testing | 1.5 hours | No - after all phases |
| **Total Serial** | **7 hours** | If done sequentially |
| **Total Parallel** | **6 hours** | If Phases 1-3 done together |

**Recommended Approach**: Sequential (safer, easier to debug)

---

## Next Steps

**Ready to proceed?**

1. ✅ **Review this plan** - Any concerns or questions?
2. ✅ **Backup current state** - `git commit` everything
3. 🚀 **Start Phase 0** - Update base_engine.py
4. 🚀 **Proceed phase by phase** - Test after each phase
5. ✅ **Validate success** - Run all tests, check health endpoints

**Command to start**:
```bash
# Commit current state
git add .
git commit -m "Pre-asyncpg migration checkpoint"

# Create migration branch
git checkout -b feature/asyncpg-migration

# Ready to modify base_engine.py!
```

---

**Status**: 📋 PLAN COMPLETE - READY FOR EXECUTION
**Estimated Completion**: 6-8 hours
**Risk Level**: Medium (well-understood migration)
**Recommendation**: PROCEED with asyncpg migration

Let's get your database connections working properly! 🚀
