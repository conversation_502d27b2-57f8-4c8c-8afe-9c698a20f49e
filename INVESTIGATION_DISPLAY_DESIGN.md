# Investigation Display Design - Real Context Example

## The Real Alert Data We Have

```json
{
  "source.ip": "*************",
  "destination.port": 7680,  // ← THE KEY DETAIL!
  "unique_dests": 126,      // ← Scanned 126 different IPs
  "event_count": 315,       // ← 315 connection attempts
  "company.name": "Naga World",
  "observer.vendor": "Fortinet",  // ← Data from FortiGate firewall
  "observer.name": "Remus",
  "protocol": "UNKNOWN"     // ← MISSING - this annoys you!
}
```

## Your Questions (The Good Analyst Questions!)

1. ✅ **Is ************* in our entity database?**
   - Answer: NO - Not in database
   - Impact: We don't know what this device is!

2. ❓ **Is it internet-facing?**
   - Answer: Internal IP (192.168.x.x)
   - But: Could still have internet access through NAT
   - Missing: Firewall rules, routing table

3. 😤 **What PROTOCOL was used for scanning?**
   - Answer: NOT IN ALERT DATA
   - Port 7680: Could be TCP, UDP, or other
   - This is CRITICAL context missing!

4. ❓ **Was it ICMP ping scan or actual connection attempts?**
   - Answer: Firewall logs show "connection attempts" (not ICMP)
   - But: No protocol field in alert

---

## Display Design: 4 Panels

### Panel 1: Alert Summary (Top Banner)
```
┌─────────────────────────────────────────────────────────────────────┐
│ 🚨 DACTA Horizontal Port Scan Detected                             │
│ Severity: LOW  |  Time: 2025-10-02 11:37:21  |  Status: OPEN       │
├─────────────────────────────────────────────────────────────────────┤
│ Quick Facts:                                                         │
│ • Source: ************* → 126 different targets                    │
│ • Port: 7680 (Unknown service - not standard)                      │
│ • Events: 315 connection attempts in 10 minutes                     │
│ • Observer: FortiGate (Remus) - Naga World network                 │
│                                                                      │
│ [View Full Investigation Guide] [Open in Elastic] [Create Case]    │
└─────────────────────────────────────────────────────────────────────┘
```

---

### Panel 2: Scoring Breakdown (Interactive, Collapsible)
```
┌─────────────────────────────────────────────────────────────────────┐
│ 📊 Risk Scoring Breakdown                          Total: 75 / 100 │
├─────────────────────────────────────────────────────────────────────┤
│                                                                      │
│ ✅ REDUCES RISK (More Benign)                                      │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│                                                                      │
│ [+40] Threat Intelligence Clean                                     │
│       └─ No malicious indicators in IOC database                    │
│       └─ Checked: SIEMLess CTI, would check VirusTotal, AbuseIPDB  │
│                                                                      │
│ [+25] Internal IP Address (RFC1918)                                 │
│       └─ ************* is private network space                     │
│       └─ Less likely to be external attacker                        │
│       └─ BUT: Could be compromised internal host                    │
│                                                                      │
│ [+10] Low Severity Rating                                           │
│       └─ Rule author assessed as low-priority                       │
│       └─ Risk score: 21/100 from Elastic rule                       │
│                                                                      │
│ ⚠️  INCREASES RISK (More Suspicious)                                │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│                                                                      │
│ [-10] Unknown Port (7680)                                            │
│       └─ Not a standard service port                                │
│       └─ Port 7680: Windows Delivery Optimization (WUDO)            │
│       └─ Uncommon to scan this across 126 targets                   │
│                                                                      │
│ [-5]  High Scan Volume                                              │
│       └─ 126 unique targets in 10 minutes                           │
│       └─ 315 total connection attempts                              │
│       └─ Rapid systematic scanning behavior                         │
│                                                                      │
│ ⚠️  MISSING CONTEXT (Unknown Impact)                                │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│                                                                      │
│ [  ?] Device Identity                                                │
│       └─ NOT IN ASSET DATABASE                                      │
│       └─ Could add: +20 if authorized scanner                       │
│       └─ Could add: -30 if end-user workstation                     │
│       └─ Action: Add to asset inventory!                            │
│                                                                      │
│ [  ?] Protocol Used                                                  │
│       └─ NOT IN ALERT DATA (Firewall limitation)                    │
│       └─ TCP? UDP? Other?                                            │
│       └─ Impact: Changes interpretation of "scan"                   │
│                                                                      │
│ [  ?] Internet-Facing Status                                         │
│       └─ Internal IP but routing unknown                            │
│       └─ Could have internet access via NAT                         │
│       └─ Check: Firewall rules, routing table                       │
│                                                                      │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│ 📊 Confidence Scale                                                 │
│                                                                      │
│  0──────20──────40──────60──────80──────100                        │
│  │       │       │       │  [75] │       │                         │
│  MALICIOUS    SUSPICIOUS    BENIGN                                  │
│                              ↑                                       │
│                         LIKELY BENIGN                                │
│                     (Review & Close if context confirms)            │
└─────────────────────────────────────────────────────────────────────┘
```

---

### Panel 3: Context Intelligence (Entity Relationships)
```
┌─────────────────────────────────────────────────────────────────────┐
│ 🔍 Entity Context & Relationships                                   │
├─────────────────────────────────────────────────────────────────────┤
│                                                                      │
│ Source Device: *************                                        │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│                                                                      │
│ ⚠️  DEVICE NOT IN ASSET DATABASE                                    │
│                                                                      │
│ What we know:                                                        │
│ • IP: ************* (Private, Internal)                            │
│ • Network: Naga World corporate network                             │
│ • First Seen: TODAY (this is the first alert)                      │
│                                                                      │
│ What we DON'T know (critical gaps!):                                │
│ • Hostname: Unknown                                                  │
│ • Owner: Unknown                                                     │
│ • Device Type: Unknown (Server? Workstation? Scanner?)             │
│ • Role: Unknown (Authorized to scan?)                               │
│ • Internet Access: Unknown (NAT routing?)                           │
│                                                                      │
│ 📋 Recommended Actions:                                             │
│ 1. Check Active Directory for *************                         │
│ 2. Query DHCP server for MAC address & hostname                     │
│ 3. Check CMDB/Asset inventory                                       │
│ 4. Ask network team: "Who owns this IP?"                            │
│                                                                      │
│ Scanned Targets: 126 Internal IPs                                   │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│                                                                      │
│ Target Pattern:                                                      │
│ • All targets: Internal IPs (192.168.x.x range)                    │
│ • Scan type: Horizontal (same port across many IPs)                │
│ • Port targeted: 7680 (Windows Delivery Optimization)              │
│                                                                      │
│ 💡 Context: Port 7680 Analysis                                     │
│ • Service: Windows Update Delivery Optimization (WUDO)              │
│ • Purpose: Peer-to-peer Windows update sharing                     │
│ • Benign Use: Windows 10+ looking for update peers                 │
│ • Suspicious: Not typically scanned across 126+ hosts              │
│                                                                      │
│ Historical Behavior                                                  │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│                                                                      │
│ Similar Alerts: 0 in past 30 days                                   │
│ Pattern: FIRST OCCURRENCE                                            │
│                                                                      │
│ ⚠️  This is new behavior - no historical baseline                   │
│                                                                      │
│ Related Events (would query from Elastic):                          │
│ • Authentication attempts from this IP                              │
│ • Other scanning activity                                           │
│ • Follow-up lateral movement                                        │
│                                                                      │
└─────────────────────────────────────────────────────────────────────┘
```

---

### Panel 4: Detection Coverage (Venn Diagram - Log Source Quality)
```
┌─────────────────────────────────────────────────────────────────────┐
│ 📊 Detection Coverage & Log Source Quality                          │
├─────────────────────────────────────────────────────────────────────┤
│                                                                      │
│ Data Sources for This Alert:                                        │
│                                                                      │
│     ┌──────────────────┐                                            │
│     │   Fortinet       │                                            │
│     │   FortiGate      │                                            │
│     │   "Remus"        │                                            │
│     │                  │                                            │
│     │  ✅ Network Flow │                                            │
│     │  ✅ Source IP    │                                            │
│     │  ✅ Dest IP      │                                            │
│     │  ✅ Dest Port    │                                            │
│     │  ✅ Event Count  │                                            │
│     │  ❌ Protocol     │  ← MISSING!                                │
│     │  ❌ Payload      │  ← MISSING!                                │
│     │  ❌ User Context │  ← MISSING!                                │
│     └──────────────────┘                                            │
│                                                                      │
│ What This Means:                                                     │
│                                                                      │
│ ✅ We CAN detect:                                                   │
│ • That scanning occurred                                            │
│ • Which IP initiated it                                             │
│ • Which port was targeted                                           │
│ • How many targets were hit                                         │
│                                                                      │
│ ❌ We CANNOT detect:                                                │
│ • What protocol was used (TCP/UDP/ICMP?)                            │
│ • Packet payload (actual scan type)                                │
│ • User/service account responsible                                  │
│ • Process that initiated scan                                       │
│                                                                      │
│ 📈 Detection Fidelity for Port Scanning (T1046)                     │
│                                                                      │
│ Current Coverage: 60%                                                │
│                                                                      │
│ ┌────────────────────────────────────┐                              │
│ │ Network Layer (Firewall)           │ 60% ✅ (We have this)        │
│ ├────────────────────────────────────┤                              │
│ │ Endpoint Telemetry (EDR)           │  0% ❌ (Not integrated)      │
│ ├────────────────────────────────────┤                              │
│ │ Asset Context (CMDB)               │  0% ❌ (IP not in DB)        │
│ ├────────────────────────────────────┤                              │
│ │ User Context (AD/Auth logs)        │  0% ❌ (No user mapping)     │
│ └────────────────────────────────────┘                              │
│                                                                      │
│ 💡 To Improve Detection:                                            │
│                                                                      │
│ 1. Add EDR (CrowdStrike, SentinelOne)                              │
│    → Would provide: Process, user, command line                     │
│    → Detection fidelity: 60% → 85%                                  │
│                                                                      │
│ 2. Integrate Asset Database                                         │
│    → Would provide: Device role, owner, authorization               │
│    → False positive reduction: 40% → 10%                            │
│                                                                      │
│ 3. Add Authentication Logs                                          │
│    → Would provide: User context, privilege level                   │
│    → Detection fidelity: 85% → 95%                                  │
│                                                                      │
│ Recommended Log Sources for T1046 Detection:                         │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│                                                                      │
│ Essential (Must Have):                                               │
│ ✅ Network Firewall Logs (Fortinet) - We have this                 │
│ ❌ Endpoint Detection & Response (EDR) - Missing                    │
│                                                                      │
│ Important (Should Have):                                             │
│ ❌ DNS Logs - Missing                                               │
│ ❌ Proxy Logs - Missing                                             │
│ ❌ Asset Database - Missing                                         │
│                                                                      │
│ Nice to Have:                                                        │
│ ❌ NetFlow/IPFIX - Missing                                          │
│ ❌ IDS/IPS Alerts - Missing                                         │
│                                                                      │
│ Current Risk: MEDIUM-HIGH                                            │
│ Reason: Limited visibility - only firewall logs                     │
│                                                                      │
└─────────────────────────────────────────────────────────────────────┘
```

---

## Interactive Elements

### 1. Expandable Sections
- Click "What we DON'T know" to see detailed gap analysis
- Click each scoring factor to see full reasoning
- Click "Protocol Used - MISSING" to see why it matters

### 2. Actionable Buttons
```
[Query Elastic for Full Details]
  → Opens pre-built query:
     source.ip:"*************" AND @timestamp:[11:20 TO 11:40]

[Check Active Directory]
  → Opens AD lookup tool

[Add to Asset Database]
  → Form to register this device

[Create Investigation Case]
  → Auto-populates case with all context
```

### 3. Tooltip Explanations
Hover over "Port 7680":
```
Port 7680 - Windows Delivery Optimization

Used by Windows 10+ for peer-to-peer update distribution.
Devices scan the network to find other Windows machines
that have already downloaded updates.

Benign: Normal Windows behavior in corporate network
Suspicious: Unusual to scan 126+ hosts in 10 minutes
```

---

## The REAL Questions This Answers

### Your Question: "Is this internet-facing?"
**Answer Display:**
```
Internet Exposure: UNKNOWN
├─ IP Type: Internal (*************)
├─ NAT Status: Unknown (check firewall rules)
├─ Egress Allowed: Unknown (check routing table)
└─ Recommendation: Query firewall for NAT rules
```

### Your Question: "What protocol?"
**Answer Display:**
```
Protocol: NOT IN ALERT DATA ⚠️

Why This Matters:
• TCP scan: Actual connection attempts (more aggressive)
• UDP scan: Probe packets (less aggressive)
• ICMP scan: Ping sweep (least aggressive)

Limitation: FortiGate logs don't include protocol field
in aggregated alerts

How to Find Out:
[Query Raw Firewall Logs] → Show individual events with protocol
```

### Your Question: "In entity database?"
**Answer Display:**
```
Asset Database Status: NOT FOUND ⚠️

Impact:
• Cannot determine if authorized scanner
• Cannot identify device owner
• Cannot assess normal behavior baseline

Actions:
1. [Query Active Directory] for hostname
2. [Check DHCP] for MAC address
3. [Check CMDB] for asset record
4. [Ask Network Team] via Slack/ticket
```

---

## This Is What Analysts Actually Need!

**Not:** "Here's the verdict: BENIGN"

**Instead:**
- Here's what we know
- Here's what we DON'T know
- Here's why the missing data matters
- Here's where to find it
- Here's how it would change the verdict

**Result:** Analyst learns to think critically about context gaps! 🎯
