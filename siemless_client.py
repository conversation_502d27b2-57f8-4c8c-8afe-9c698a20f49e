"""
SIEMLess v2.0 Python Client SDK
Auto-generated from OpenAPI specification
"""

import requests
from typing import Dict, List, Any, Optional


class SIEMLessClient:
    """Python client for SIEMLess v2.0 API"""

    def __init__(self, base_url: str = "http://localhost:8003", api_key: Optional[str] = None):
        self.base_url = base_url.rstrip("/")
        self.session = requests.Session()
        if api_key:
            self.session.headers["X-API-Key"] = api_key

    def ingest_logs(self, source: str, logs: List[Dict]) -> Dict:
        """Ingest security logs"""
        response = self.session.post(
            f"{self.base_url}/api/v1/ingest",
            json={"source": source, "logs": logs}
        )
        response.raise_for_status()
        return response.json()

    def list_patterns(self, pattern_type: Optional[str] = None) -> List[Dict]:
        """List patterns from the pattern library"""
        params = {}
        if pattern_type:
            params["type"] = pattern_type

        response = self.session.get(
            f"{self.base_url}/api/v1/patterns",
            params=params
        )
        response.raise_for_status()
        return response.json()

    def sync_github_patterns(self, repository: Optional[str] = None, force: bool = False) -> Dict:
        """Trigger GitHub pattern synchronization"""
        response = self.session.post(
            f"{self.base_url}/api/v1/patterns/sync/github",
            json={"repository": repository, "force": force}
        )
        response.raise_for_status()
        return response.json()

    def generate_queries(self, query_intent: Dict) -> Dict[str, str]:
        """Generate SIEM queries from intent"""
        response = self.session.post(
            f"{self.base_url}/api/v1/query/generate",
            json=query_intent
        )
        response.raise_for_status()
        return response.json()

    def natural_language_query(self, query: str) -> Dict:
        """Convert natural language to SIEM queries"""
        response = self.session.post(
            f"{self.base_url}/api/v1/query/natural",
            json={"query": query}
        )
        response.raise_for_status()
        return response.json()


# Example usage
if __name__ == "__main__":
    client = SIEMLessClient(api_key="your-api-key")

    # Ingest logs
    result = client.ingest_logs(
        source="crowdstrike",
        logs=[{"detection_id": "123", "severity": "high"}]
    )
    print(f"Ingested {result['processed']} logs")

    # Generate queries
    queries = client.generate_queries({
        "type": "search",
        "filters": {"user": "admin"},
        "time_range": "-24h"
    })
    print(f"Splunk query: {queries['splunk']}")
