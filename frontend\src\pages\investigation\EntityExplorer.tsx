import React from 'react'
import { useParams } from 'react-router-dom'
import EntityExplorerWidget from '../../widgets/EntityExplorer'

const EntityExplorer: React.FC = () => {
  const { entityId } = useParams()

  return (
    <div className="h-full p-4">
      <div className="h-full bg-white rounded-lg shadow">
        <EntityExplorerWidget entityId={entityId || 'default-entity'} />
      </div>
    </div>
  )
}

export default EntityExplorer