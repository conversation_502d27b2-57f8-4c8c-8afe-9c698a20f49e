/**
 * StatCard Component
 * Reusable KPI card for dashboard displays
 */

import React from 'react'
import { TrendingUp, TrendingDown, Minus } from 'lucide-react'

interface StatCardProps {
  title: string
  value: string | number
  subtitle?: string
  trend?: {
    value: number  // Percentage change
    isPositive?: boolean  // True if increase is good
  }
  icon?: React.ReactNode
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'gray'
  onClick?: () => void
  loading?: boolean
}

const colorClasses = {
  blue: {
    bg: 'bg-blue-50',
    icon: 'text-blue-600',
    trend: 'text-blue-600'
  },
  green: {
    bg: 'bg-green-50',
    icon: 'text-green-600',
    trend: 'text-green-600'
  },
  red: {
    bg: 'bg-red-50',
    icon: 'text-red-600',
    trend: 'text-red-600'
  },
  yellow: {
    bg: 'bg-yellow-50',
    icon: 'text-yellow-600',
    trend: 'text-yellow-600'
  },
  purple: {
    bg: 'bg-purple-50',
    icon: 'text-purple-600',
    trend: 'text-purple-600'
  },
  gray: {
    bg: 'bg-gray-50',
    icon: 'text-gray-600',
    trend: 'text-gray-600'
  }
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  trend,
  icon,
  color = 'blue',
  onClick,
  loading = false
}) => {
  const colors = colorClasses[color]

  const getTrendIcon = () => {
    if (!trend) return null

    const isActuallyPositive = trend.isPositive !== false

    if (trend.value > 0) {
      return (
        <div className={`flex items-center gap-1 ${isActuallyPositive ? 'text-green-600' : 'text-red-600'}`}>
          <TrendingUp size={16} />
          <span className="text-sm font-medium">+{trend.value}%</span>
        </div>
      )
    } else if (trend.value < 0) {
      return (
        <div className={`flex items-center gap-1 ${isActuallyPositive ? 'text-red-600' : 'text-green-600'}`}>
          <TrendingDown size={16} />
          <span className="text-sm font-medium">{trend.value}%</span>
        </div>
      )
    } else {
      return (
        <div className="flex items-center gap-1 text-gray-600">
          <Minus size={16} />
          <span className="text-sm font-medium">0%</span>
        </div>
      )
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6 animate-pulse">
        <div className="flex items-center justify-between mb-4">
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-8 w-8 bg-gray-200 rounded"></div>
        </div>
        <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    )
  }

  return (
    <div
      className={`bg-white rounded-lg shadow p-6 transition-all ${
        onClick ? 'cursor-pointer hover:shadow-lg' : ''
      }`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-600">{title}</h3>
        {icon && (
          <div className={`p-2 rounded-lg ${colors.bg}`}>
            <div className={colors.icon}>{icon}</div>
          </div>
        )}
      </div>

      <div className="mb-2">
        <p className="text-3xl font-bold text-gray-900">{value}</p>
      </div>

      <div className="flex items-center justify-between">
        {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
        {getTrendIcon()}
      </div>
    </div>
  )
}

// Skeleton loader for StatCard
export const StatCardSkeleton: React.FC = () => (
  <div className="bg-white rounded-lg shadow p-6 animate-pulse">
    <div className="flex items-center justify-between mb-4">
      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      <div className="h-8 w-8 bg-gray-200 rounded"></div>
    </div>
    <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
    <div className="h-4 bg-gray-200 rounded w-1/2"></div>
  </div>
)
