"""
SIEMLess v2.0 - Statistics and Monitoring
Centralized statistics collection and monitoring for the ingestion engine
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import logging


class StatsMonitor:
    """Manages statistics collection and monitoring for ingestion engine"""

    def __init__(self, publisher, logger: logging.Logger):
        self.publisher = publisher
        self.logger = logger
        self.stats = self._initialize_stats()
        self.performance_history = []
        self.alert_thresholds = self._initialize_thresholds()

    def _initialize_stats(self) -> Dict[str, Any]:
        """Initialize statistics structure"""
        return {
            'total_logs': 0,
            'processed_logs': 0,
            'failed_logs': 0,
            'sources_active': 0,
            'patterns_loaded': 0,
            'github_repos': 0,
            'pattern_matched': 0,
            'unknown_patterns': 0,
            'routing_errors': 0,
            'last_reset': datetime.utcnow().isoformat(),
            'uptime_start': datetime.utcnow().isoformat()
        }

    def _initialize_thresholds(self) -> Dict[str, Any]:
        """Initialize alert thresholds"""
        return {
            'error_rate_threshold': 0.05,  # 5% error rate
            'unknown_pattern_threshold': 0.1,  # 10% unknown patterns
            'processing_time_threshold': 5.0,  # 5 seconds max processing time
            'source_health_threshold': 0.8,  # 80% sources must be healthy
            'memory_threshold': 1024 * 1024 * 512,  # 512MB memory limit
        }

    def update_stat(self, stat_name: str, value: Any, increment: bool = False):
        """Update a specific statistic"""
        if increment and stat_name in self.stats:
            if isinstance(self.stats[stat_name], (int, float)):
                self.stats[stat_name] += value
            else:
                self.logger.warning(f"Cannot increment non-numeric stat: {stat_name}")
        else:
            self.stats[stat_name] = value

        self.logger.debug(f"Updated stat {stat_name}: {self.stats[stat_name]}")

    def increment_stat(self, stat_name: str, amount: int = 1):
        """Increment a statistic by a specific amount"""
        self.update_stat(stat_name, amount, increment=True)

    def get_stat(self, stat_name: str, default=None) -> Any:
        """Get a specific statistic"""
        return self.stats.get(stat_name, default)

    def get_all_stats(self) -> Dict[str, Any]:
        """Get all statistics"""
        # Add calculated stats
        calculated_stats = self.stats.copy()
        calculated_stats.update(self._calculate_derived_stats())
        return calculated_stats

    def _calculate_derived_stats(self) -> Dict[str, Any]:
        """Calculate derived statistics"""
        derived = {}

        # Calculate processing rates
        total_processed = self.stats['processed_logs'] + self.stats['failed_logs']
        if total_processed > 0:
            derived['success_rate'] = (self.stats['processed_logs'] / total_processed) * 100
            derived['error_rate'] = (self.stats['failed_logs'] / total_processed) * 100
        else:
            derived['success_rate'] = 100.0
            derived['error_rate'] = 0.0

        # Calculate pattern matching rates
        pattern_total = self.stats['pattern_matched'] + self.stats['unknown_patterns']
        if pattern_total > 0:
            derived['pattern_match_rate'] = (self.stats['pattern_matched'] / pattern_total) * 100
            derived['unknown_pattern_rate'] = (self.stats['unknown_patterns'] / pattern_total) * 100
        else:
            derived['pattern_match_rate'] = 0.0
            derived['unknown_pattern_rate'] = 0.0

        # Calculate uptime
        try:
            uptime_start = datetime.fromisoformat(self.stats['uptime_start'])
            uptime = datetime.utcnow() - uptime_start
            derived['uptime_seconds'] = int(uptime.total_seconds())
            derived['uptime_hours'] = round(uptime.total_seconds() / 3600, 2)
        except Exception:
            derived['uptime_seconds'] = 0
            derived['uptime_hours'] = 0.0

        # Calculate logs per hour
        if derived['uptime_hours'] > 0:
            derived['logs_per_hour'] = round(self.stats['total_logs'] / derived['uptime_hours'], 2)
        else:
            derived['logs_per_hour'] = 0.0

        return derived

    def record_performance_metric(self, metric_name: str, value: float, metadata: Optional[Dict] = None):
        """Record a performance metric with timestamp"""
        metric = {
            'metric': metric_name,
            'value': value,
            'timestamp': datetime.utcnow().isoformat(),
            'metadata': metadata or {}
        }

        self.performance_history.append(metric)

        # Keep only last 1000 metrics to prevent memory bloat
        if len(self.performance_history) > 1000:
            self.performance_history = self.performance_history[-1000:]

        # Check for alerts
        self._check_performance_alerts(metric_name, value)

    def get_performance_metrics(self, metric_name: Optional[str] = None,
                               since: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Get performance metrics, optionally filtered"""
        metrics = self.performance_history

        if metric_name:
            metrics = [m for m in metrics if m['metric'] == metric_name]

        if since:
            since_iso = since.isoformat()
            metrics = [m for m in metrics if m['timestamp'] >= since_iso]

        return metrics

    def get_performance_summary(self, hours: int = 1) -> Dict[str, Any]:
        """Get performance summary for the last N hours"""
        since = datetime.utcnow() - timedelta(hours=hours)
        recent_metrics = self.get_performance_metrics(since=since)

        if not recent_metrics:
            return {'period_hours': hours, 'metrics_count': 0}

        # Group by metric type
        by_metric = {}
        for metric in recent_metrics:
            metric_name = metric['metric']
            if metric_name not in by_metric:
                by_metric[metric_name] = []
            by_metric[metric_name].append(metric['value'])

        # Calculate summaries
        summary = {
            'period_hours': hours,
            'metrics_count': len(recent_metrics),
            'by_metric': {}
        }

        for metric_name, values in by_metric.items():
            summary['by_metric'][metric_name] = {
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'avg': sum(values) / len(values),
                'latest': values[-1] if values else 0
            }

        return summary

    def _check_performance_alerts(self, metric_name: str, value: float):
        """Check if a performance metric triggers an alert"""
        alerts = []

        if metric_name == 'processing_time' and value > self.alert_thresholds['processing_time_threshold']:
            alerts.append({
                'type': 'performance',
                'severity': 'warning',
                'message': f"High processing time: {value:.2f}s (threshold: {self.alert_thresholds['processing_time_threshold']}s)"
            })

        elif metric_name == 'error_rate' and value > self.alert_thresholds['error_rate_threshold']:
            alerts.append({
                'type': 'error_rate',
                'severity': 'critical',
                'message': f"High error rate: {value:.1%} (threshold: {self.alert_thresholds['error_rate_threshold']:.1%})"
            })

        elif metric_name == 'unknown_pattern_rate' and value > self.alert_thresholds['unknown_pattern_threshold']:
            alerts.append({
                'type': 'pattern_matching',
                'severity': 'warning',
                'message': f"High unknown pattern rate: {value:.1%} (threshold: {self.alert_thresholds['unknown_pattern_threshold']:.1%})"
            })

        # Send alerts
        for alert in alerts:
            self._send_alert(alert)

    def _send_alert(self, alert: Dict[str, Any]):
        """Send an alert notification"""
        try:
            alert_message = {
                'source': 'ingestion_engine',
                'timestamp': datetime.utcnow().isoformat(),
                'alert': alert
            }

            self.publisher('monitoring.alert', alert_message)
            self.logger.warning(f"Alert: {alert['message']}")

        except Exception as e:
            self.logger.error(f"Failed to send alert: {e}")

    def update_processing_stats(self, routing_stats: Dict[str, int]):
        """Update statistics from log routing results"""
        if 'pattern_matched' in routing_stats:
            self.increment_stat('pattern_matched', routing_stats['pattern_matched'])

        if 'unknown_patterns' in routing_stats:
            self.increment_stat('unknown_patterns', routing_stats['unknown_patterns'])

        if 'total_processed' in routing_stats:
            self.increment_stat('processed_logs', routing_stats['total_processed'])

        if 'routing_errors' in routing_stats:
            self.increment_stat('routing_errors', routing_stats['routing_errors'])
            self.increment_stat('failed_logs', routing_stats['routing_errors'])

        # Calculate and record performance metrics
        total_logs = routing_stats.get('total_processed', 0)
        if total_logs > 0:
            error_rate = routing_stats.get('routing_errors', 0) / total_logs
            unknown_rate = routing_stats.get('unknown_patterns', 0) / total_logs

            self.record_performance_metric('error_rate', error_rate)
            self.record_performance_metric('unknown_pattern_rate', unknown_rate)

    def reset_stats(self, preserve_uptime: bool = True):
        """Reset statistics (useful for testing or maintenance)"""
        uptime_start = self.stats['uptime_start'] if preserve_uptime else datetime.utcnow().isoformat()

        self.stats = self._initialize_stats()
        if preserve_uptime:
            self.stats['uptime_start'] = uptime_start

        self.stats['last_reset'] = datetime.utcnow().isoformat()
        self.logger.info("Statistics reset")

    def get_health_status(self, source_manager=None) -> Dict[str, Any]:
        """Get overall health status based on current statistics"""
        health = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'checks': {}
        }

        # Check error rate
        derived_stats = self._calculate_derived_stats()
        error_rate = derived_stats.get('error_rate', 0) / 100

        if error_rate > self.alert_thresholds['error_rate_threshold']:
            health['status'] = 'unhealthy'
            health['checks']['error_rate'] = 'failed'
        else:
            health['checks']['error_rate'] = 'passed'

        # Check unknown pattern rate
        unknown_rate = derived_stats.get('unknown_pattern_rate', 0) / 100
        if unknown_rate > self.alert_thresholds['unknown_pattern_threshold']:
            health['status'] = 'degraded' if health['status'] == 'healthy' else health['status']
            health['checks']['pattern_matching'] = 'warning'
        else:
            health['checks']['pattern_matching'] = 'passed'

        # Check source health if source manager available
        if source_manager:
            source_health = source_manager.get_source_health_summary()
            total_sources = source_health['total_sources']
            healthy_sources = source_health['healthy_sources']

            if total_sources > 0:
                source_health_rate = healthy_sources / total_sources
                if source_health_rate < self.alert_thresholds['source_health_threshold']:
                    health['status'] = 'degraded' if health['status'] == 'healthy' else health['status']
                    health['checks']['source_health'] = 'warning'
                else:
                    health['checks']['source_health'] = 'passed'
            else:
                health['checks']['source_health'] = 'no_sources'

        return health

    def export_metrics_for_monitoring(self) -> Dict[str, Any]:
        """Export metrics in a format suitable for external monitoring systems"""
        all_stats = self.get_all_stats()
        performance_summary = self.get_performance_summary(hours=1)

        return {
            'metrics': {
                'ingestion_total_logs': all_stats['total_logs'],
                'ingestion_processed_logs': all_stats['processed_logs'],
                'ingestion_failed_logs': all_stats['failed_logs'],
                'ingestion_success_rate': all_stats.get('success_rate', 0),
                'ingestion_error_rate': all_stats.get('error_rate', 0),
                'ingestion_pattern_match_rate': all_stats.get('pattern_match_rate', 0),
                'ingestion_unknown_pattern_rate': all_stats.get('unknown_pattern_rate', 0),
                'ingestion_logs_per_hour': all_stats.get('logs_per_hour', 0),
                'ingestion_sources_active': all_stats['sources_active'],
                'ingestion_patterns_loaded': all_stats['patterns_loaded'],
                'ingestion_uptime_hours': all_stats.get('uptime_hours', 0)
            },
            'performance_summary': performance_summary,
            'timestamp': datetime.utcnow().isoformat()
        }