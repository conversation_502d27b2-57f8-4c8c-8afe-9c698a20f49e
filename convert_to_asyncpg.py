#!/usr/bin/env python3
"""
Comprehensive migration script for backend_engine.py
Converts psycopg2 patterns to asyncpg patterns
"""

import re

def process_file(input_path, output_path):
    """Process the file and convert psycopg2 to asyncpg"""

    with open(input_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Track changes
    changes = []

    # Step 1: Replace simple db_connection references (not in cursor() calls)
    pattern = r'([^.])self\.db_connection(?!\.cursor\(\)|\.commit\(\))'
    def replace_simple_conn(match):
        changes.append(f"Line {content[:match.start()].count(chr(10)) + 1}: self.db_connection -> self.db_pool")
        return match.group(1) + 'self.db_pool'

    content = re.sub(pattern, replace_simple_conn, content)

    # Step 2: Handle specific initialization patterns
    init_patterns = [
        (r'LogSourceIdentifier\(self\.db_connection\)',
         'LogSourceIdentifier(self.db_pool)'),
        (r'UpdateScheduler\(self\.db_connection,',
         'UpdateScheduler(self.db_pool,'),
        (r'db_connection=self\.db_connection',
         'db_pool=self.db_pool'),
    ]

    for pattern, replacement in init_patterns:
        if re.search(pattern, content):
            content = re.sub(pattern, replacement, content)
            changes.append(f"Replaced pattern: {pattern[:40]}...")

    # Step 3: Remove commit() calls (asyncpg auto-commits)
    content = re.sub(
        r'\s*self\.db_connection\.commit\(\)\s*\n',
        '\n            # asyncpg auto-commits\n',
        content
    )
    changes.append("Removed self.db_connection.commit() calls")

    # Step 4: Document cursor patterns that need manual conversion
    cursor_pattern = r'cursor = self\.db_connection\.cursor\(\)'
    cursor_matches = list(re.finditer(cursor_pattern, content))

    print(f"\nFound {len(cursor_matches)} cursor patterns that need manual conversion:")
    for i, match in enumerate(cursor_matches, 1):
        line_no = content[:match.start()].count('\n') + 1
        print(f"  {i}. Line {line_no}")
        changes.append(f"MANUAL: Line {line_no} needs cursor -> asyncpg conversion")

    # Write output
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(content)

    # Write change log
    with open(output_path + '.changes.txt', 'w', encoding='utf-8') as f:
        f.write("Migration Changes:\n")
        f.write("=" * 80 + "\n\n")
        for change in changes:
            f.write(change + "\n")

    print(f"\nWrote output to: {output_path}")
    print(f"Change log: {output_path}.changes.txt")
    print(f"\nTotal changes: {len(changes)}")

if __name__ == '__main__':
    input_file = r'c:\Users\<USER>\Documents\siemless_v2\engines\backend\backend_engine.py'
    output_file = r'c:\Users\<USER>\Documents\siemless_v2\engines\backend\backend_engine.py.converted'

    process_file(input_file, output_file)
