#!/usr/bin/env python3
"""
Complete Ingestion Engine Stack Testing
Tests all functions: source management, configuration, monitoring, routing
"""

import json
import redis
import time
from datetime import datetime

class IngestionEngineTester:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)
        self.test_results = {}
        self.source_id = f'test_source_{int(time.time())}'

    def wait_for_response(self, channel, timeout=10):
        """Wait for response on a channel"""
        pubsub = self.redis_client.pubsub()
        pubsub.subscribe(channel)

        start_time = time.time()
        for message in pubsub.listen():
            if time.time() - start_time > timeout:
                return None

            if message['type'] == 'message':
                return json.loads(message['data'])

        return None

    def test_start_source(self):
        """Test 1: Start a data source"""
        print("\n" + "="*60)
        print("TEST 1: START DATA SOURCE")
        print("="*60)

        # Start Elasticsearch source
        request = {
            'data': {
                'source_id': self.source_id,
                'source_type': 'elasticsearch'
            }
        }

        print(f"[SEND] Starting source: {self.source_id}")
        self.redis_client.publish('ingestion.start_source', json.dumps(request))

        # Wait for confirmation
        print("[WAIT] Waiting for source start confirmation...")
        response = self.wait_for_response('ingestion.source_started', timeout=5)

        if response:
            print("[SUCCESS] Source started!")
            print(f"  - Source ID: {response.get('data', {}).get('source_id', 'N/A')}")
            print(f"  - Status: {response.get('data', {}).get('status', 'N/A')}")
            print(f"  - Timestamp: {response.get('data', {}).get('timestamp', 'N/A')}")
            self.test_results['start_source'] = 'PASSED'
            return True
        else:
            print("[FAILED] No source start confirmation received")
            self.test_results['start_source'] = 'FAILED'
            return False

    def test_configure_source(self):
        """Test 2: Configure source parameters"""
        print("\n" + "="*60)
        print("TEST 2: CONFIGURE SOURCE")
        print("="*60)

        # Configure Elasticsearch with custom settings
        request = {
            'data': {
                'source_type': 'elasticsearch',
                'config': {
                    'batch_size': 2000,
                    'poll_interval': 15,
                    'enabled': True
                }
            }
        }

        print(f"[SEND] Configuring elasticsearch source")
        print(f"  - Batch size: 2000")
        print(f"  - Poll interval: 15s")
        self.redis_client.publish('ingestion.configure_source', json.dumps(request))

        # Wait for confirmation
        print("[WAIT] Waiting for configuration confirmation...")
        response = self.wait_for_response('ingestion.source_configured', timeout=5)

        if response:
            print("[SUCCESS] Source configured!")
            config = response.get('data', {}).get('config', {})
            print(f"  - Batch size: {config.get('batch_size', 'N/A')}")
            print(f"  - Poll interval: {config.get('poll_interval', 'N/A')}")
            self.test_results['configure_source'] = 'PASSED'
        else:
            print("[FAILED] No configuration confirmation received")
            self.test_results['configure_source'] = 'FAILED'

        return response

    def test_get_stats(self):
        """Test 3: Get ingestion statistics"""
        print("\n" + "="*60)
        print("TEST 3: GET STATISTICS")
        print("="*60)

        request = {
            'data': {}
        }

        print(f"[SEND] Requesting statistics")
        self.redis_client.publish('ingestion.get_stats', json.dumps(request))

        # Wait for response
        print("[WAIT] Waiting for statistics...")
        response = self.wait_for_response('ingestion.stats_response', timeout=5)

        if response:
            print("[SUCCESS] Statistics received!")
            stats = response.get('data', {}).get('stats', {})
            active_sources = response.get('data', {}).get('active_sources', {})

            print(f"  - Total logs: {stats.get('total_logs', 0)}")
            print(f"  - Processed logs: {stats.get('processed_logs', 0)}")
            print(f"  - Failed logs: {stats.get('failed_logs', 0)}")
            print(f"  - Active sources: {stats.get('sources_active', 0)}")

            if active_sources:
                print("\n  Active Source Details:")
                for src_id, src_info in active_sources.items():
                    print(f"    - {src_id}: {src_info.get('type')} ({src_info.get('status')})")
                    print(f"      Logs processed: {src_info.get('logs_processed', 0)}")

            self.test_results['get_stats'] = 'PASSED'
        else:
            print("[FAILED] No statistics received")
            self.test_results['get_stats'] = 'FAILED'

        return response

    def test_log_routing(self):
        """Test 4: Monitor log routing"""
        print("\n" + "="*60)
        print("TEST 4: LOG ROUTING MONITORING")
        print("="*60)

        print("[MONITOR] Checking if logs are being routed...")

        # Subscribe to routing channels
        pubsub = self.redis_client.pubsub()
        pubsub.subscribe([
            'intelligence.analyze_log',
            'contextualization.enrich_log',
            'monitoring.ingestion_stats'
        ])

        print("[WAIT] Monitoring for 10 seconds...")
        start_time = time.time()
        messages_received = {
            'intelligence': 0,
            'contextualization': 0,
            'monitoring': 0
        }

        while time.time() - start_time < 10:
            message = pubsub.get_message(timeout=1)
            if message and message['type'] == 'message':
                channel = message['channel']
                if 'intelligence' in channel:
                    messages_received['intelligence'] += 1
                elif 'contextualization' in channel:
                    messages_received['contextualization'] += 1
                elif 'monitoring' in channel:
                    messages_received['monitoring'] += 1

        print(f"[RESULTS] Messages routed in 10 seconds:")
        print(f"  - To Intelligence Engine: {messages_received['intelligence']}")
        print(f"  - To Contextualization Engine: {messages_received['contextualization']}")
        print(f"  - To Monitoring: {messages_received['monitoring']}")

        if sum(messages_received.values()) > 0:
            print("[SUCCESS] Log routing is active!")
            self.test_results['log_routing'] = 'PASSED'
        else:
            print("[WARNING] No routing detected - sources may need to be active")
            self.test_results['log_routing'] = 'PENDING'

    def test_stop_source(self):
        """Test 5: Stop a data source"""
        print("\n" + "="*60)
        print("TEST 5: STOP DATA SOURCE")
        print("="*60)

        request = {
            'data': {
                'source_id': self.source_id
            }
        }

        print(f"[SEND] Stopping source: {self.source_id}")
        self.redis_client.publish('ingestion.stop_source', json.dumps(request))

        # Wait for confirmation
        print("[WAIT] Waiting for source stop confirmation...")
        response = self.wait_for_response('ingestion.source_stopped', timeout=5)

        if response:
            print("[SUCCESS] Source stopped!")
            print(f"  - Source ID: {response.get('data', {}).get('source_id', 'N/A')}")
            print(f"  - Status: {response.get('data', {}).get('status', 'N/A')}")
            self.test_results['stop_source'] = 'PASSED'
        else:
            print("[FAILED] No source stop confirmation received")
            self.test_results['stop_source'] = 'FAILED'

        return response

    def test_multi_source(self):
        """Test 6: Multiple source management"""
        print("\n" + "="*60)
        print("TEST 6: MULTI-SOURCE MANAGEMENT")
        print("="*60)

        sources = []

        # Start multiple sources
        print("[START] Starting multiple sources...")
        for source_type in ['elasticsearch', 'crowdstrike', 'palo_alto']:
            source_id = f'test_{source_type}_{int(time.time())}'
            sources.append(source_id)

            request = {
                'data': {
                    'source_id': source_id,
                    'source_type': source_type
                }
            }

            print(f"  - Starting {source_type} source: {source_id}")
            self.redis_client.publish('ingestion.start_source', json.dumps(request))
            time.sleep(0.5)  # Brief pause between starts

        # Check statistics
        time.sleep(2)
        print("\n[CHECK] Verifying all sources are active...")

        request = {'data': {}}
        self.redis_client.publish('ingestion.get_stats', json.dumps(request))

        response = self.wait_for_response('ingestion.stats_response', timeout=5)

        if response:
            active_count = response.get('data', {}).get('stats', {}).get('sources_active', 0)
            print(f"[SUCCESS] {active_count} sources active")

            if active_count >= 3:
                self.test_results['multi_source'] = 'PASSED'
            else:
                self.test_results['multi_source'] = 'PARTIAL'
        else:
            self.test_results['multi_source'] = 'FAILED'

        # Clean up - stop all test sources
        print("\n[CLEANUP] Stopping test sources...")
        for source_id in sources:
            request = {'data': {'source_id': source_id}}
            self.redis_client.publish('ingestion.stop_source', json.dumps(request))
            print(f"  - Stopped {source_id}")

    def run_all_tests(self):
        """Run complete test suite"""
        print("\n" + "#"*60)
        print("# INGESTION ENGINE COMPLETE STACK TEST")
        print("#"*60)
        print(f"# Started: {datetime.now().isoformat()}")
        print("#"*60)

        # Test 1: Start source
        source_started = self.test_start_source()
        time.sleep(2)

        # Only continue if source started successfully
        if source_started:
            # Test 2: Configure source
            self.test_configure_source()
            time.sleep(2)

            # Test 3: Get statistics
            self.test_get_stats()
            time.sleep(2)

            # Test 4: Monitor routing
            self.test_log_routing()
            time.sleep(2)

            # Test 5: Stop source
            self.test_stop_source()
            time.sleep(2)

        # Test 6: Multi-source management
        self.test_multi_source()

        # Summary
        print("\n" + "#"*60)
        print("# TEST SUMMARY")
        print("#"*60)

        for test_name, result in self.test_results.items():
            status_icon = "✅" if result == "PASSED" else "❌" if result == "FAILED" else "⚠️"
            print(f"{status_icon} {test_name:20} : {result}")

        print("#"*60)
        print(f"# Completed: {datetime.now().isoformat()}")
        print("#"*60)

        # Check engine health
        print("\n[BONUS] Checking engine health...")
        try:
            # Check if engine is in coordination table
            # This would require database access, so we check via stats
            request = {'data': {}}
            self.redis_client.publish('ingestion.get_stats', json.dumps(request))
            response = self.wait_for_response('ingestion.stats_response', timeout=2)

            if response:
                print("✅ Ingestion Engine is responding to requests")
            else:
                print("⚠️ Ingestion Engine may not be responding")
        except Exception as e:
            print(f"⚠️ Health check error: {e}")

if __name__ == "__main__":
    tester = IngestionEngineTester()
    tester.run_all_tests()