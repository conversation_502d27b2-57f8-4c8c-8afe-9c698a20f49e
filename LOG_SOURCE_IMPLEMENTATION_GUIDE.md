# Log Source Implementation Guide - From Zero to Full Coverage

## Table of Contents
1. [Quick Start Guide](#quick-start-guide)
2. [Implementation by Environment](#implementation-by-environment)
3. [Configuration Templates](#configuration-templates)
4. [Integration with SIEMLess](#integration-with-siemless)
5. [Validation & Testing](#validation--testing)
6. [Troubleshooting](#troubleshooting)

---

## Quick Start Guide

### 30-Day Implementation Plan

```python
IMPLEMENTATION_TIMELINE = {
    'week_1': {
        'name': 'Foundation',
        'tasks': [
            'Enable Windows Security auditing',
            'Deploy Sysmon with SwiftOnSecurity config',
            'Configure PowerShell logging',
            'Set up log collection'
        ],
        'expected_coverage': 35,
        'effort_hours': 40
    },
    'week_2': {
        'name': 'Network & Identity',
        'tasks': [
            'Enable firewall logging',
            'Configure AD advanced auditing',
            'Set up DNS logging',
            'Enable command line auditing'
        ],
        'expected_coverage': 50,
        'effort_hours': 40
    },
    'week_3': {
        'name': 'Enhancement',
        'tasks': [
            'Deploy EDR pilot',
            'Configure web proxy logging',
            'Enable cloud audit logs',
            'Set up centralized collection'
        ],
        'expected_coverage': 65,
        'effort_hours': 60
    },
    'week_4': {
        'name': 'Optimization',
        'tasks': [
            'Tune noise reduction',
            'Create correlation rules',
            'Test detection scenarios',
            'Document procedures'
        ],
        'expected_coverage': 70,
        'effort_hours': 40
    }
}
```

---

## Implementation by Environment

### Windows Environment

#### Step 1: Enable Windows Security Auditing

```powershell
# Enable Advanced Audit Policies
auditpol /set /category:"Logon/Logoff" /success:enable /failure:enable
auditpol /set /category:"Account Logon" /success:enable /failure:enable
auditpol /set /category:"Account Management" /success:enable /failure:enable
auditpol /set /category:"Object Access" /success:enable /failure:enable
auditpol /set /category:"Process Tracking" /success:enable /failure:enable
auditpol /set /category:"Privilege Use" /success:enable /failure:enable
auditpol /set /category:"System" /success:enable /failure:enable

# Enable command line auditing
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System\Audit" /v ProcessCreationIncludeCmdLine_Enabled /t REG_DWORD /d 1 /f

# Increase Security log size
wevtutil sl Security /ms:**********  # 4GB
```

#### Step 2: Deploy Sysmon

```powershell
# Download Sysmon
Invoke-WebRequest -Uri "https://download.sysinternals.com/files/Sysmon.zip" -OutFile "Sysmon.zip"
Expand-Archive -Path "Sysmon.zip" -DestinationPath "C:\Tools\Sysmon"

# Download SwiftOnSecurity config
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/SwiftOnSecurity/sysmon-config/master/sysmonconfig-export.xml" -OutFile "sysmonconfig.xml"

# Install Sysmon with config
C:\Tools\Sysmon\Sysmon64.exe -accepteula -i sysmonconfig.xml

# Verify installation
Get-Service Sysmon64
Get-WinEvent -LogName "Microsoft-Windows-Sysmon/Operational" -MaxEvents 10
```

#### Step 3: Enable PowerShell Logging

```powershell
# Enable PowerShell Script Block Logging
$basePath = "HKLM:\Software\Policies\Microsoft\Windows\PowerShell\ScriptBlockLogging"
if (-not (Test-Path $basePath)) {
    New-Item -Path $basePath -Force
}
Set-ItemProperty -Path $basePath -Name "EnableScriptBlockLogging" -Value 1

# Enable Module Logging
$modulePath = "HKLM:\Software\Policies\Microsoft\Windows\PowerShell\ModuleLogging"
if (-not (Test-Path $modulePath)) {
    New-Item -Path $modulePath -Force
}
Set-ItemProperty -Path $modulePath -Name "EnableModuleLogging" -Value 1

# Enable Transcription
$transcriptPath = "HKLM:\Software\Policies\Microsoft\Windows\PowerShell\Transcription"
if (-not (Test-Path $transcriptPath)) {
    New-Item -Path $transcriptPath -Force
}
Set-ItemProperty -Path $transcriptPath -Name "EnableTranscripting" -Value 1
Set-ItemProperty -Path $transcriptPath -Name "OutputDirectory" -Value "C:\PSTranscripts"
```

### Linux Environment

#### Step 1: Configure Auditd

```bash
# Install auditd
sudo apt-get install auditd audispd-plugins  # Debian/Ubuntu
sudo yum install audit audit-libs  # RHEL/CentOS

# Configure audit rules
cat > /etc/audit/rules.d/siemless.rules << 'EOF'
# Delete all rules
-D

# Buffer size
-b 8192

# Failure handling
-f 1

# File integrity monitoring
-w /etc/passwd -p wa -k passwd_changes
-w /etc/group -p wa -k group_changes
-w /etc/shadow -p wa -k shadow_changes
-w /etc/sudoers -p wa -k sudoers_changes

# Command execution
-a exit,always -F arch=b64 -S execve -k command_execution
-a exit,always -F arch=b32 -S execve -k command_execution

# Network connections
-a exit,always -F arch=b64 -S socket -S connect -k network_connections
-a exit,always -F arch=b32 -S socket -S connect -k network_connections

# Privilege escalation
-a always,exit -F arch=b64 -S setuid -S setgid -S setreuid -S setregid -k privilege_escalation
-a always,exit -F arch=b32 -S setuid -S setgid -S setreuid32 -S setregid32 -k privilege_escalation
EOF

# Apply rules
sudo augenrules --load
sudo systemctl restart auditd
```

#### Step 2: Configure Syslog

```bash
# Configure rsyslog for centralized logging
cat > /etc/rsyslog.d/01-siemless.conf << 'EOF'
# Log authentication events
auth,authpriv.*  @@siem-server:514

# Log system events
*.info;mail.none;authpriv.none;cron.none  @@siem-server:514

# Log kernel messages
kern.*  @@siem-server:514

# Enable high precision timestamps
$ActionFileDefaultTemplate RSYSLOG_TraditionalFileFormat
$SystemLogRateLimitInterval 0
$SystemLogRateLimitBurst 0
EOF

# Restart rsyslog
sudo systemctl restart rsyslog
```

### Active Directory

#### Advanced Auditing Configuration

```powershell
# Run on Domain Controller
# Enable detailed auditing via Group Policy

# Create new GPO for auditing
$gpoName = "Advanced Security Auditing"
New-GPO -Name $gpoName

# Configure audit settings
$auditSettings = @{
    "Audit Credential Validation" = "Success, Failure"
    "Audit Kerberos Authentication Service" = "Success, Failure"
    "Audit Kerberos Service Ticket Operations" = "Success, Failure"
    "Audit Logon" = "Success, Failure"
    "Audit Other Logon/Logoff Events" = "Success, Failure"
    "Audit Special Logon" = "Success, Failure"
    "Audit Computer Account Management" = "Success, Failure"
    "Audit Distribution Group Management" = "Success, Failure"
    "Audit Other Account Management Events" = "Success, Failure"
    "Audit Security Group Management" = "Success, Failure"
    "Audit User Account Management" = "Success, Failure"
    "Audit Directory Service Access" = "Success, Failure"
    "Audit Directory Service Changes" = "Success, Failure"
}

# Link GPO to Domain Controllers OU
Get-GPO -Name $gpoName | New-GPLink -Target "OU=Domain Controllers,DC=domain,DC=com"
```

### Network Devices

#### Firewall Configuration (Palo Alto)

```bash
# Configure syslog forwarding
configure
set deviceconfig system syslog-server siem-server server 192.168.1.100
set deviceconfig system syslog-server siem-server port 514
set deviceconfig system syslog-server siem-server facility LOG_USER
set deviceconfig system syslog-server siem-server format BSD

# Configure log forwarding profiles
set log-settings profiles syslog SIEM-Forward servers siem-server
set log-settings profiles syslog SIEM-Forward traffic all
set log-settings profiles syslog SIEM-Forward threat all
set log-settings profiles syslog SIEM-Forward wildfire all
set log-settings profiles syslog SIEM-Forward url all

# Apply to security policies
set rulebase security rules all log-end yes
set rulebase security rules all log-setting SIEM-Forward

commit
```

#### Network IDS (Zeek)

```bash
# Install Zeek
sudo apt-get install zeek  # Debian/Ubuntu

# Configure Zeek
cat > /opt/zeek/etc/node.cfg << 'EOF'
[manager]
type=manager
host=localhost

[proxy]
type=proxy
host=localhost

[worker-1]
type=worker
host=localhost
interface=eth0
EOF

# Configure logging
cat > /opt/zeek/etc/zeekctl.cfg << 'EOF'
LogDir = /var/log/zeek
LogRotationInterval = 3600
LogExpireInterval = 30
CompressLogs = 1
EOF

# Deploy configuration
zeekctl deploy
zeekctl start
```

### Cloud Platforms

#### AWS CloudTrail

```python
import boto3

def setup_cloudtrail():
    """Configure AWS CloudTrail for comprehensive logging"""

    cloudtrail = boto3.client('cloudtrail')
    s3 = boto3.client('s3')

    # Create S3 bucket for logs
    bucket_name = 'company-cloudtrail-logs'
    s3.create_bucket(Bucket=bucket_name)

    # Create CloudTrail
    trail_name = 'company-security-trail'

    response = cloudtrail.create_trail(
        Name=trail_name,
        S3BucketName=bucket_name,
        IncludeGlobalServiceEvents=True,
        IsMultiRegionTrail=True,
        EnableLogFileValidation=True,
        EventSelectors=[
            {
                'ReadWriteType': 'All',
                'IncludeManagementEvents': True,
                'DataResources': [
                    {
                        'Type': 'AWS::S3::Object',
                        'Values': ['arn:aws:s3:::*/*']
                    },
                    {
                        'Type': 'AWS::Lambda::Function',
                        'Values': ['arn:aws:lambda:*:*:function/*']
                    }
                ]
            }
        ]
    )

    # Enable CloudTrail
    cloudtrail.start_logging(Name=trail_name)

    # Enable CloudWatch integration
    cloudtrail.put_event_selectors(
        TrailName=trail_name,
        InsightSelectors=[
            {
                'InsightType': 'ApiCallRateInsight'
            }
        ]
    )

    return response
```

#### Azure Activity Logs

```powershell
# Using Azure PowerShell
# Enable Activity Log collection

# Create Log Analytics Workspace
$workspace = New-AzOperationalInsightsWorkspace `
    -ResourceGroupName "SecurityLogs" `
    -Name "SecurityWorkspace" `
    -Location "East US" `
    -Sku "PerGB2018"

# Configure Activity Log export
$subscriptionId = (Get-AzContext).Subscription.Id
$workspaceId = $workspace.ResourceId

Set-AzDiagnosticSetting `
    -ResourceId "/subscriptions/$subscriptionId" `
    -WorkspaceId $workspaceId `
    -Enabled $true `
    -Name "SecurityExport" `
    -Category Administrative, Security, Alert, Recommendation, Policy

# Enable Azure AD logs
Connect-AzureAD
$tenantId = (Get-AzureADTenantDetail).ObjectId

# Configure AD audit logs
$diagnosticSettings = @{
    workspaceId = $workspaceId
    logs = @(
        @{category="AuditLogs"; enabled=$true; retentionPolicy=@{days=90; enabled=$true}}
        @{category="SignInLogs"; enabled=$true; retentionPolicy=@{days=90; enabled=$true}}
        @{category="RiskyUsers"; enabled=$true; retentionPolicy=@{days=90; enabled=$true}}
        @{category="UserRiskEvents"; enabled=$true; retentionPolicy=@{days=90; enabled=$true}}
    )
}
```

---

## Configuration Templates

### Sysmon Configuration Template

```xml
<Sysmon schemaversion="4.50">
  <HashAlgorithms>md5,sha256,IMPHASH</HashAlgorithms>
  <EventFiltering>
    <!-- Process Creation with Command Line -->
    <ProcessCreate onmatch="exclude">
      <CommandLine condition="begin with">C:\Windows\system32\svchost.exe -k</CommandLine>
      <CommandLine condition="begin with">C:\Program Files\</CommandLine>
    </ProcessCreate>

    <!-- Network Connections -->
    <NetworkConnect onmatch="include">
      <DestinationPort>22,23,25,135,139,445,1433,3389,5985,5986</DestinationPort>
      <DestinationIp condition="is not">10.0.0.0/8</DestinationIp>
      <DestinationIp condition="is not">**********/12</DestinationIp>
      <DestinationIp condition="is not">***********/16</DestinationIp>
    </NetworkConnect>

    <!-- Process Access (Credential Theft) -->
    <ProcessAccess onmatch="include">
      <TargetImage condition="is">C:\Windows\system32\lsass.exe</TargetImage>
      <GrantedAccess>0x1410</GrantedAccess>
    </ProcessAccess>

    <!-- Registry Persistence -->
    <RegistryEvent onmatch="include">
      <TargetObject condition="contains">CurrentVersion\Run</TargetObject>
      <TargetObject condition="contains">CurrentVersion\RunOnce</TargetObject>
      <TargetObject condition="contains">CurrentVersion\Explorer\Shell Folders</TargetObject>
    </RegistryEvent>
  </EventFiltering>
</Sysmon>
```

### EDR Deployment Template

```python
# CrowdStrike Deployment Script
import subprocess
import os

def deploy_crowdstrike(customer_id, token):
    """Deploy CrowdStrike Falcon sensor"""

    # Windows deployment
    if os.name == 'nt':
        installer_path = "WindowsSensor.exe"
        install_cmd = f"{installer_path} /install /quiet /norestart CID={customer_id} TOKEN={token}"

        # Set prevention policies
        policy_settings = {
            'PreventionPolicy': 'Aggressive',
            'DetectionPolicy': 'ExtraAggressive',
            'NextGenAV': 'Enabled',
            'MalwareProtection': 'Enabled',
            'BehaviorProtection': 'Enabled'
        }

    # Linux deployment
    else:
        installer_path = "falcon-sensor.rpm"
        install_cmd = f"rpm -ivh {installer_path} --nodeps"
        config_cmd = f"/opt/CrowdStrike/falconctl -s --cid={customer_id}"

    # Execute installation
    subprocess.run(install_cmd, shell=True, check=True)

    # Verify installation
    verify_cmd = "sc query csagent" if os.name == 'nt' else "systemctl status falcon-sensor"
    result = subprocess.run(verify_cmd, shell=True, capture_output=True)

    return result.returncode == 0
```

---

## Integration with SIEMLess

### Log Forwarding Configuration

```python
# SIEMLess Ingestion Configuration
SIEMLESS_INGESTION = {
    'endpoint': 'https://siemless.company.com:8003/ingest',
    'api_key': 'YOUR_API_KEY',
    'batch_size': 1000,
    'compression': 'gzip',
    'encryption': 'TLS1.3'
}

# Log Source Mapping
LOG_SOURCE_MAPPING = {
    'windows_security': {
        'source_type': 'windows_events',
        'quality_tier': 'SILVER',
        'parser': 'windows_security_parser',
        'enrichment': ['user_context', 'asset_info']
    },
    'sysmon': {
        'source_type': 'sysmon',
        'quality_tier': 'GOLD',
        'parser': 'sysmon_parser',
        'enrichment': ['process_reputation', 'hash_lookup']
    },
    'crowdstrike': {
        'source_type': 'crowdstrike_edr',
        'quality_tier': 'PLATINUM',
        'parser': 'crowdstrike_parser',
        'enrichment': ['threat_intel', 'behavioral_analysis']
    },
    'network_ids': {
        'source_type': 'zeek',
        'quality_tier': 'GOLD',
        'parser': 'zeek_parser',
        'enrichment': ['geo_location', 'domain_reputation']
    }
}
```

### Quality Tier Registration

```python
def register_log_sources(siemless_api):
    """Register log sources with quality tiers"""

    sources = [
        {
            'name': 'Domain Controller',
            'type': 'active_directory',
            'tier': 'PLATINUM',
            'score': 92,
            'capabilities': ['authentication', 'kerberos', 'privilege_changes']
        },
        {
            'name': 'CrowdStrike Falcon',
            'type': 'endpoint_edr',
            'tier': 'PLATINUM',
            'score': 98,
            'capabilities': ['process_injection', 'behavioral', 'memory_analysis']
        },
        {
            'name': 'Palo Alto Firewall',
            'type': 'network_firewall',
            'tier': 'GOLD',
            'score': 85,
            'capabilities': ['network_flows', 'application_id', 'threat_prevention']
        }
    ]

    for source in sources:
        response = siemless_api.register_source(source)
        print(f"Registered {source['name']}: {response['status']}")
```

---

## Validation & Testing

### Coverage Validation Script

```python
def validate_log_coverage():
    """Validate that all critical log sources are working"""

    validations = {
        'windows_events': check_windows_events(),
        'sysmon': check_sysmon(),
        'powershell': check_powershell_logs(),
        'firewall': check_firewall_logs(),
        'edr': check_edr_agent(),
        'active_directory': check_ad_logs()
    }

    # Calculate coverage score
    working_sources = sum(1 for v in validations.values() if v['status'] == 'OK')
    total_sources = len(validations)
    coverage_score = (working_sources / total_sources) * 100

    # Check detection capabilities
    capabilities = calculate_detection_capabilities(validations)

    return {
        'coverage_score': coverage_score,
        'working_sources': working_sources,
        'total_sources': total_sources,
        'detection_capabilities': capabilities,
        'validations': validations
    }

def check_windows_events():
    """Check Windows Event logging"""
    import win32evtlog

    try:
        hand = win32evtlog.OpenEventLog(None, "Security")
        total = win32evtlog.GetNumberOfEventLogRecords(hand)

        # Check for recent events
        events = win32evtlog.ReadEventLog(hand,
            win32evtlog.EVENTLOG_BACKWARDS_READ | win32evtlog.EVENTLOG_SEQUENTIAL_READ, 0)

        # Check for command line auditing
        cmdline_enabled = any(e.EventID == 4688 for e in events[:100])

        return {
            'status': 'OK',
            'event_count': total,
            'cmdline_auditing': cmdline_enabled,
            'recent_events': len(events)
        }
    except Exception as e:
        return {'status': 'ERROR', 'error': str(e)}

def check_sysmon():
    """Check Sysmon installation and logging"""
    import subprocess

    try:
        # Check service status
        result = subprocess.run(['sc', 'query', 'Sysmon64'],
                              capture_output=True, text=True)

        if 'RUNNING' in result.stdout:
            # Check for recent Sysmon events
            events_cmd = 'wevtutil qe Microsoft-Windows-Sysmon/Operational /c:10 /f:text'
            events_result = subprocess.run(events_cmd, shell=True,
                                          capture_output=True, text=True)

            return {
                'status': 'OK',
                'service': 'RUNNING',
                'recent_events': events_result.stdout.count('Event[')
            }
        else:
            return {'status': 'ERROR', 'service': 'NOT_RUNNING'}

    except Exception as e:
        return {'status': 'ERROR', 'error': str(e)}
```

### Attack Simulation Tests

```python
# MITRE ATT&CK Technique Testing
ATTACK_TESTS = {
    'T1059_001': {  # PowerShell
        'name': 'PowerShell Execution',
        'command': 'powershell.exe -EncodedCommand SABlAGwAbABvACAAVwBvAHIAbABkAA==',
        'expected_logs': ['Sysmon Event 1', 'Windows 4688', 'PowerShell 4104'],
        'detection_confidence': {
            'with_edr': 95,
            'with_sysmon': 85,
            'basic': 60
        }
    },

    'T1055': {  # Process Injection
        'name': 'Process Injection',
        'command': 'mavinject.exe /INJECTRUNNING C:\\test.dll',
        'expected_logs': ['Sysmon Event 8', 'EDR Alert'],
        'detection_confidence': {
            'with_edr': 95,
            'with_sysmon': 70,
            'basic': 20
        }
    },

    'T1003_001': {  # LSASS Memory
        'name': 'Credential Dumping',
        'command': 'rundll32.exe comsvcs.dll MiniDump',
        'expected_logs': ['Sysmon Event 10', 'Windows 4656', 'EDR Alert'],
        'detection_confidence': {
            'with_edr': 98,
            'with_sysmon': 75,
            'basic': 40
        }
    }
}

def run_detection_test(technique):
    """Run detection test for specific technique"""
    test = ATTACK_TESTS[technique]

    print(f"Testing: {test['name']}")
    print(f"Command: {test['command']}")
    print("WARNING: This will trigger security alerts!")

    if input("Continue? (y/n): ").lower() != 'y':
        return

    # Execute test
    # Note: Only run in isolated test environment!

    # Wait for logs
    import time
    time.sleep(5)

    # Check for detection
    detected_logs = check_for_logs(test['expected_logs'])

    return {
        'technique': technique,
        'detected': len(detected_logs) > 0,
        'logs_found': detected_logs,
        'confidence': calculate_confidence(detected_logs)
    }
```

---

## Troubleshooting

### Common Issues and Solutions

```python
TROUBLESHOOTING_GUIDE = {
    'no_logs_received': {
        'symptoms': ['No events in SIEM', 'Empty log files'],
        'checks': [
            'Verify service is running',
            'Check network connectivity',
            'Validate credentials',
            'Review firewall rules',
            'Check log file permissions'
        ],
        'solutions': {
            'windows': 'sc query eventlog && netstat -an | findstr 514',
            'linux': 'systemctl status rsyslog && netstat -an | grep 514'
        }
    },

    'high_volume': {
        'symptoms': ['Log flooding', 'Storage issues', 'Performance impact'],
        'checks': [
            'Review audit policies',
            'Check for noisy rules',
            'Identify top talkers'
        ],
        'solutions': {
            'filter_noise': 'Add exclusions for known-good',
            'rate_limit': 'Implement log rate limiting',
            'sample': 'Use sampling for high-volume sources'
        }
    },

    'missing_events': {
        'symptoms': ['Expected events not appearing', 'Gaps in coverage'],
        'checks': [
            'Verify audit policy configuration',
            'Check log size limits',
            'Review collection filters'
        ],
        'solutions': {
            'increase_log_size': 'wevtutil sl Security /ms:**********',
            'fix_auditing': 'auditpol /set /category:* /success:enable /failure:enable'
        }
    }
}
```

### Performance Optimization

```python
def optimize_log_collection():
    """Optimize log collection for performance"""

    optimizations = {
        'batch_collection': {
            'description': 'Collect logs in batches',
            'impact': 'Reduces network overhead',
            'configuration': {
                'batch_size': 1000,
                'batch_interval': '5s'
            }
        },

        'compression': {
            'description': 'Enable log compression',
            'impact': 'Reduces bandwidth 60-80%',
            'configuration': {
                'algorithm': 'gzip',
                'level': 6
            }
        },

        'filtering': {
            'description': 'Filter at source',
            'impact': 'Reduces volume 30-50%',
            'configuration': {
                'exclude_success_logons': True,
                'exclude_process_noise': True,
                'exclude_known_good': True
            }
        },

        'caching': {
            'description': 'Cache enrichment data',
            'impact': 'Reduces API calls 90%',
            'configuration': {
                'cache_ttl': 3600,
                'cache_size': '1GB'
            }
        }
    }

    return optimizations
```

---

## Conclusion

Successful log source implementation requires:

1. **Systematic approach**: Follow the implementation timeline
2. **Proper configuration**: Use templates and validate settings
3. **Quality over quantity**: Better to have fewer well-configured sources
4. **Continuous validation**: Regular testing ensures coverage
5. **Performance optimization**: Balance visibility with performance

Key success factors:
- Start with free improvements (Sysmon, auditing)
- Validate each source before moving to next
- Document all configurations
- Test detection capabilities regularly
- Monitor log volume and adjust as needed