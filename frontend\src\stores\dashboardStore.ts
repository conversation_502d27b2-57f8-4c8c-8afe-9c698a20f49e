/**
 * Dashboard Zustand Store
 * Manages dashboard overview state
 */

import { create } from 'zustand'
import { dashboardService, alertService, caseService, ctiService, detectionService } from '../api/services'
import type {
  DashboardStats,
  Alert,
  Case,
  CTIPluginInfo,
  DetectionFidelity
} from '../types/api'

interface DashboardState {
  // Data
  stats: DashboardStats | null
  recentAlerts: Alert[]
  openCases: Case[]
  ctiStatus: CTIPluginInfo[]
  detectionFidelity: DetectionFidelity | null

  // Loading states
  loading: {
    stats: boolean
    alerts: boolean
    cases: boolean
    cti: boolean
    fidelity: boolean
  }

  // Error states
  error: {
    stats: string | null
    alerts: string | null
    cases: string | null
    cti: string | null
    fidelity: string | null
  }

  // Actions
  fetchDashboard: () => Promise<void>
  fetchStats: () => Promise<void>
  fetchRecentAlerts: () => Promise<void>
  fetchOpenCases: () => Promise<void>
  fetchCTIStatus: () => Promise<void>
  fetchDetectionFidelity: () => Promise<void>
  refreshAll: () => Promise<void>
  clearErrors: () => void
}

export const useDashboardStore = create<DashboardState>((set, get) => ({
  // Initial state
  stats: null,
  recentAlerts: [],
  openCases: [],
  ctiStatus: [],
  detectionFidelity: null,

  loading: {
    stats: false,
    alerts: false,
    cases: false,
    cti: false,
    fidelity: false
  },

  error: {
    stats: null,
    alerts: null,
    cases: null,
    cti: null,
    fidelity: null
  },

  // Fetch all dashboard data in parallel
  fetchDashboard: async () => {
    await Promise.all([
      get().fetchStats(),
      get().fetchRecentAlerts(),
      get().fetchOpenCases(),
      get().fetchCTIStatus(),
      get().fetchDetectionFidelity()
    ])
  },

  // Fetch dashboard statistics
  fetchStats: async () => {
    set((state) => ({
      loading: { ...state.loading, stats: true },
      error: { ...state.error, stats: null }
    }))

    try {
      const stats = await dashboardService.getStats()
      set((state) => ({
        stats,
        loading: { ...state.loading, stats: false }
      }))
    } catch (error) {
      set((state) => ({
        loading: { ...state.loading, stats: false },
        error: {
          ...state.error,
          stats: error instanceof Error ? error.message : 'Failed to load stats'
        }
      }))
    }
  },

  // Fetch recent critical alerts
  fetchRecentAlerts: async () => {
    set((state) => ({
      loading: { ...state.loading, alerts: true },
      error: { ...state.error, alerts: null }
    }))

    try {
      const response = await alertService.getAlerts(
        {
          severity: ['critical', 'high'],
          status: ['open']
        },
        1,
        10 // Limit to 10 most recent
      )

      set((state) => ({
        recentAlerts: response.data,
        loading: { ...state.loading, alerts: false }
      }))
    } catch (error) {
      set((state) => ({
        loading: { ...state.loading, alerts: false },
        error: {
          ...state.error,
          alerts: error instanceof Error ? error.message : 'Failed to load alerts'
        }
      }))
    }
  },

  // Fetch open cases
  fetchOpenCases: async () => {
    set((state) => ({
      loading: { ...state.loading, cases: true },
      error: { ...state.error, cases: null }
    }))

    try {
      const response = await caseService.getCases(
        { status: ['open', 'investigating'] },
        1,
        10
      )

      set((state) => ({
        openCases: response.data,
        loading: { ...state.loading, cases: false }
      }))
    } catch (error) {
      set((state) => ({
        loading: { ...state.loading, cases: false },
        error: {
          ...state.error,
          cases: error instanceof Error ? error.message : 'Failed to load cases'
        }
      }))
    }
  },

  // Fetch CTI plugin status
  fetchCTIStatus: async () => {
    set((state) => ({
      loading: { ...state.loading, cti: true },
      error: { ...state.error, cti: null }
    }))

    try {
      const ctiStatus = await ctiService.getStatus()
      set((state) => ({
        ctiStatus,
        loading: { ...state.loading, cti: false }
      }))
    } catch (error) {
      set((state) => ({
        loading: { ...state.loading, cti: false },
        error: {
          ...state.error,
          cti: error instanceof Error ? error.message : 'Failed to load CTI status'
        }
      }))
    }
  },

  // Fetch detection fidelity
  fetchDetectionFidelity: async () => {
    set((state) => ({
      loading: { ...state.loading, fidelity: true },
      error: { ...state.error, fidelity: null }
    }))

    try {
      const fidelity = await detectionService.getFidelity()
      set((state) => ({
        detectionFidelity: fidelity,
        loading: { ...state.loading, fidelity: false }
      }))
    } catch (error) {
      set((state) => ({
        loading: { ...state.loading, fidelity: false },
        error: {
          ...state.error,
          fidelity: error instanceof Error ? error.message : 'Failed to load detection fidelity'
        }
      }))
    }
  },

  // Refresh all data
  refreshAll: async () => {
    await get().fetchDashboard()
  },

  // Clear all errors
  clearErrors: () => {
    set({
      error: {
        stats: null,
        alerts: null,
        cases: null,
        cti: null,
        fidelity: null
      }
    })
  }
}))
