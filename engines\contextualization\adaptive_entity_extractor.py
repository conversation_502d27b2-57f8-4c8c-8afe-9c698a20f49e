"""
Adaptive Entity Extractor with AI Fallback
Auto-detects unknown patterns and uses AI to populate missing information

This solves the problem:
- Hardcoded patterns work for known vendors (CrowdStrike, Fortinet, Palo Alto)
- But TippingPoint, ThreatLocker, and future vendors have unique schemas
- System should automatically detect and learn new patterns using AI
"""

import json
import re
import logging
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
import redis.asyncio as redis_async


class AdaptiveEntityExtractor:
    """
    Adaptive entity extraction with automatic pattern learning

    Flow:
    1. Try hardcoded patterns first (fast, free)
    2. If low confidence or unknown vendor → Ask AI
    3. AI analyzes log structure and extracts entities
    4. Successful extractions become new patterns (crystallized)
    5. Future logs from same vendor use learned patterns (free)
    """

    def __init__(self, redis_client, logger=None):
        self.redis_client = redis_client
        self.logger = logger or logging.getLogger(__name__)

        # Known patterns (hardcoded for common vendors)
        self.known_patterns = self._load_known_patterns()

        # Learned patterns (from AI extractions)
        self.learned_patterns = {}

        # Vendor detection patterns
        self.vendor_signatures = {
            'tippingpoint': ['observer.type', 'tippingpoint'],
            'threatlocker': ['threatlocker', 'application'],
            'crowdstrike': ['agent.type', 'crowdstrike'],
            'fortinet': ['fortinet', 'fortigate'],
            'palo_alto': ['panw', 'panos'],
            'elastic': ['elastic_agent', 'endpoint'],
            'microsoft': ['defender', 'microsoft']
        }

    def _load_known_patterns(self) -> Dict:
        """Load hardcoded patterns for known vendors"""
        return {
            'crowdstrike': {
                'entity_fields': {
                    'ip_address': ['ComputerName', 'LocalAddressIP4', 'external_ip'],
                    'hostname': ['ComputerName', 'aid', 'device_id'],
                    'username': ['UserName', 'user_name'],
                    'process': ['ImageFileName', 'process_name'],
                    'hash': ['SHA256HashData', 'MD5HashData']
                },
                'confidence': 0.95
            },
            'fortinet': {
                'entity_fields': {
                    'ip_address': ['source.ip', 'destination.ip', 'srcip', 'dstip'],
                    'hostname': ['hostname', 'host'],
                    'username': ['user', 'srcuser', 'dstuser'],
                    'port': ['source.port', 'destination.port', 'srcport', 'dstport']
                },
                'confidence': 0.90
            },
            'palo_alto': {
                'entity_fields': {
                    'ip_address': ['source.ip', 'destination.ip'],
                    'hostname': ['host.name'],
                    'username': ['user.name', 'source.user.name'],
                    'application': ['panw.panos.application'],
                    'url': ['url.domain', 'url.original']
                },
                'confidence': 0.90
            }
        }

    async def extract_entities(
        self,
        log_data: Dict[str, Any],
        vendor: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Extract entities from log data with AI fallback

        Returns:
        {
            'entities': [...],
            'method': 'pattern|ai|hybrid',
            'confidence': 0.0-1.0,
            'vendor': 'detected_vendor',
            'learned': True/False  # Whether new pattern was learned
        }
        """

        # Step 1: Detect vendor if not provided
        if not vendor:
            vendor = self._detect_vendor(log_data)
            self.logger.info(f"Auto-detected vendor: {vendor}")

        # Step 2: Try known patterns first
        pattern_entities = self._extract_with_patterns(log_data, vendor)

        # Step 3: Evaluate if we need AI assistance
        needs_ai = self._should_use_ai(pattern_entities, vendor, log_data)

        if needs_ai:
            self.logger.info(f"Pattern extraction insufficient for {vendor}, using AI")

            # Step 4: Use AI to extract entities
            ai_entities = await self._extract_with_ai(log_data, vendor, pattern_entities)

            # Step 5: Learn from AI extraction
            if ai_entities['confidence'] > 0.7:
                await self._learn_pattern(vendor, log_data, ai_entities)

            return ai_entities
        else:
            return {
                'entities': pattern_entities,
                'method': 'pattern',
                'confidence': self.known_patterns.get(vendor, {}).get('confidence', 0.5),
                'vendor': vendor,
                'learned': False
            }

    def _detect_vendor(self, log_data: Dict) -> str:
        """
        Auto-detect vendor from log structure

        Uses vendor signatures to identify log source
        """
        log_str = json.dumps(log_data).lower()

        # Check each vendor signature
        for vendor, signatures in self.vendor_signatures.items():
            matches = sum(1 for sig in signatures if sig.lower() in log_str)
            if matches >= len(signatures) * 0.5:  # 50% signature match
                return vendor

        # Check data_stream or observer fields (ECS)
        if 'data_stream' in log_data:
            dataset = log_data['data_stream'].get('dataset', '')
            if 'tippingpoint' in dataset:
                return 'tippingpoint'
            elif 'threatlocker' in dataset:
                return 'threatlocker'
            elif 'fortinet' in dataset:
                return 'fortinet'
            elif 'panw' in dataset or 'panos' in dataset:
                return 'palo_alto'

        if 'observer' in log_data:
            obs_type = log_data['observer'].get('type', '').lower()
            if obs_type:
                return obs_type

        # Check index name if available
        if '_index' in log_data:
            index = log_data['_index'].lower()
            for vendor in self.vendor_signatures.keys():
                if vendor in index:
                    return vendor

        return 'unknown'

    def _extract_with_patterns(
        self,
        log_data: Dict,
        vendor: str
    ) -> List[Dict]:
        """Extract entities using known patterns"""
        entities = []

        # Try known patterns first
        if vendor in self.known_patterns:
            pattern = self.known_patterns[vendor]
            entities = self._apply_pattern(log_data, pattern)

        # Try learned patterns
        elif vendor in self.learned_patterns:
            pattern = self.learned_patterns[vendor]
            entities = self._apply_pattern(log_data, pattern)

        return entities

    def _apply_pattern(self, log_data: Dict, pattern: Dict) -> List[Dict]:
        """Apply extraction pattern to log data"""
        entities = []

        entity_fields = pattern.get('entity_fields', {})

        for entity_type, field_paths in entity_fields.items():
            for field_path in field_paths:
                value = self._get_nested_value(log_data, field_path)
                if value:
                    entities.append({
                        'type': entity_type,
                        'value': str(value),
                        'confidence': pattern.get('confidence', 0.5),
                        'source_field': field_path
                    })

        return entities

    def _get_nested_value(self, data: Dict, path: str) -> Optional[Any]:
        """Get value from nested dict using dot notation"""
        keys = path.split('.')
        current = data

        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None

        return current

    def _should_use_ai(
        self,
        pattern_entities: List[Dict],
        vendor: str,
        log_data: Dict
    ) -> bool:
        """
        Decide if AI extraction is needed

        Use AI when:
        - No entities extracted by patterns
        - Unknown vendor
        - Low confidence extractions
        - New field structures detected
        """

        # No entities found - definitely need AI
        if not pattern_entities:
            return True

        # Unknown vendor - learn with AI
        if vendor == 'unknown':
            return True

        # Known vendor but not in our patterns - learn it
        if vendor not in self.known_patterns and vendor not in self.learned_patterns:
            return True

        # Too few entities (less than 2) - might be missing fields
        if len(pattern_entities) < 2:
            return True

        # Low confidence average
        avg_confidence = sum(e['confidence'] for e in pattern_entities) / len(pattern_entities)
        if avg_confidence < 0.6:
            return True

        return False

    async def _extract_with_ai(
        self,
        log_data: Dict,
        vendor: str,
        existing_entities: List[Dict]
    ) -> Dict[str, Any]:
        """
        Use AI to extract entities from unknown log formats

        This is the key innovation - AI learns new patterns automatically
        """

        from uuid import uuid4
        request_id = str(uuid4())

        # Build AI prompt
        prompt = self._build_extraction_prompt(log_data, vendor, existing_entities)

        # Request AI analysis
        request = {
            'request_id': request_id,
            'task': 'extract_entities',
            'vendor': vendor,
            'prompt': prompt,
            'log_sample': log_data
        }

        await self.redis_client.publish(
            'intelligence.extract_entities_ai',
            json.dumps(request)
        )

        # Wait for response
        pubsub = self.redis_client.pubsub()
        await pubsub.subscribe(f'intelligence.extraction_response.{request_id}')

        result = None
        timeout = 30

        try:
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    response = json.loads(message['data'])
                    if response.get('status') == 'success':
                        result = response
                    break
        finally:
            await pubsub.unsubscribe(f'intelligence.extraction_response.{request_id}')

        if result:
            return {
                'entities': result.get('entities', []),
                'method': 'ai',
                'confidence': result.get('confidence', 0.8),
                'vendor': vendor,
                'learned': False,
                'extraction_rules': result.get('extraction_rules', {})
            }
        else:
            # AI failed, return pattern results
            return {
                'entities': existing_entities,
                'method': 'pattern_fallback',
                'confidence': 0.5,
                'vendor': vendor,
                'learned': False
            }

    def _build_extraction_prompt(
        self,
        log_data: Dict,
        vendor: str,
        existing_entities: List[Dict]
    ) -> str:
        """Build prompt for AI entity extraction"""

        # Remove large fields to reduce token usage
        compact_log = self._compact_log(log_data)

        prompt = f"""Extract security-relevant entities from this {vendor} log.

LOG DATA:
{json.dumps(compact_log, indent=2)[:1500]}

EXISTING ENTITIES (from pattern matching):
{json.dumps(existing_entities, indent=2) if existing_entities else "None found"}

TASK:
1. Identify ALL security-relevant entities:
   - IP addresses (source, destination, any IPs)
   - Hostnames/computer names
   - Usernames/accounts
   - Processes/applications
   - File paths/names
   - Domains/URLs
   - Ports
   - MAC addresses
   - Hashes (MD5, SHA256)
   - Any vendor-specific threat indicators

2. For each entity, provide:
   - type: (ip_address, hostname, username, process, etc.)
   - value: the actual value
   - confidence: 0.0-1.0
   - source_field: the JSON path where you found it

3. Also provide extraction_rules:
   - Field paths for each entity type
   - This will be used to create a pattern for future {vendor} logs

OUTPUT FORMAT (JSON):
{{
  "entities": [
    {{"type": "ip_address", "value": "***********", "confidence": 0.95, "source_field": "source.ip"}},
    ...
  ],
  "extraction_rules": {{
    "ip_address": ["source.ip", "destination.ip"],
    "username": ["user.name"],
    ...
  }},
  "confidence": 0.85
}}
"""

        return prompt

    def _compact_log(self, log_data: Dict, max_depth: int = 3) -> Dict:
        """Compact log by removing very large nested structures"""

        def compact_value(value, depth=0):
            if depth > max_depth:
                return "..."

            if isinstance(value, dict):
                return {k: compact_value(v, depth+1) for k, v in list(value.items())[:20]}
            elif isinstance(value, list):
                return [compact_value(v, depth+1) for v in value[:10]]
            elif isinstance(value, str) and len(value) > 200:
                return value[:200] + "..."
            else:
                return value

        return compact_value(log_data)

    async def _learn_pattern(
        self,
        vendor: str,
        log_sample: Dict,
        ai_result: Dict
    ):
        """
        Learn new pattern from successful AI extraction

        This is pattern crystallization - "learn expensive once, use free forever"
        """

        extraction_rules = ai_result.get('extraction_rules', {})
        confidence = ai_result.get('confidence', 0.8)

        if not extraction_rules:
            self.logger.warning(f"No extraction rules from AI for {vendor}")
            return

        # Create learned pattern
        learned_pattern = {
            'entity_fields': extraction_rules,
            'confidence': confidence,
            'learned_at': datetime.utcnow().isoformat(),
            'sample_log': self._compact_log(log_sample, max_depth=2)
        }

        # Store in memory
        self.learned_patterns[vendor] = learned_pattern

        # Persist to database (for future sessions)
        await self._persist_learned_pattern(vendor, learned_pattern)

        self.logger.info(
            f"Learned new pattern for {vendor} vendor "
            f"({len(extraction_rules)} entity types, confidence: {confidence:.2f})"
        )

    async def _persist_learned_pattern(self, vendor: str, pattern: Dict):
        """Save learned pattern to database for persistence"""

        # Publish to backend for storage
        await self.redis_client.publish(
            'backend.save_pattern',
            json.dumps({
                'vendor': vendor,
                'pattern_type': 'entity_extraction',
                'pattern': pattern,
                'created_at': datetime.utcnow().isoformat()
            })
        )

    async def get_statistics(self) -> Dict:
        """Get extraction statistics"""
        return {
            'known_vendors': len(self.known_patterns),
            'learned_vendors': len(self.learned_patterns),
            'total_patterns': len(self.known_patterns) + len(self.learned_patterns),
            'vendor_list': {
                'known': list(self.known_patterns.keys()),
                'learned': list(self.learned_patterns.keys())
            }
        }


class UnknownPatternDetector:
    """
    Detects when we encounter logs that don't match any known patterns
    Triggers AI learning automatically
    """

    def __init__(self, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.unknown_patterns = {}  # vendor -> sample logs
        self.detection_threshold = 5  # Learn after 5 unknown logs

    def record_unknown(self, vendor: str, log_data: Dict):
        """Record an unknown pattern for learning"""

        if vendor not in self.unknown_patterns:
            self.unknown_patterns[vendor] = []

        self.unknown_patterns[vendor].append(log_data)

        count = len(self.unknown_patterns[vendor])

        if count >= self.detection_threshold:
            self.logger.warning(
                f"Detected {count} unknown {vendor} logs - "
                f"triggering AI pattern learning"
            )
            return True  # Trigger learning

        return False

    def get_unknown_vendors(self) -> List[str]:
        """Get list of vendors with unknown patterns"""
        return list(self.unknown_patterns.keys())
