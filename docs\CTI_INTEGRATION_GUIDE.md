# CTI Integration & Rule Quality Guide

## Table of Contents
1. [Overview](#overview)
2. [Rule Quality Scoring](#rule-quality-scoring)
3. [Usefulness Analysis](#usefulness-analysis)
4. [Architecture](#architecture)
5. [Duplicate Prevention](#duplicate-prevention)
6. [Monitoring & Maintenance](#monitoring--maintenance)
7. [API Reference](#api-reference)

## Overview

SIEMLess v2.0's CTI (Cyber Threat Intelligence) integration automatically ingests threat indicators from multiple sources, generates detection rules, and manages their quality and deduplication. The system processes **700+ unique rules** from three active sources: OpenCTI, OTX, and ThreatFox.

### Current Statistics (as of September 2025)
- **711 unique detection rules** (after removing 835 duplicates)
- **3 active CTI sources**: OpenCTI, OTX, ThreatFox
- **62.6%** are IP-based indicators
- **20.4%** have contextual labels
- **~7%** are high-value specific threats

## Rule Quality Scoring

### What Determines Rule Quality?

The quality score (0.0 - 1.0) is calculated based on multiple factors:

```python
def calculate_rule_quality_score(rule):
    score = 0.0

    # 1. Base Confidence (30% weight)
    score += rule.get('confidence', 0.5) * 0.3

    # 2. Meaningful Name (10% weight)
    if rule.get('name') and rule.get('name') != rule.get('ioc_value'):
        score += 0.1

    # 3. Description Quality (10% weight)
    if len(rule.get('description', '')) > 20:
        score += 0.1

    # 4. Contextual Labels (up to 20% weight)
    labels = rule.get('labels', [])
    score += min(len(labels) * 0.05, 0.2)  # 5% per label, max 20%

    # 5. MITRE ATT&CK Mapping (15% weight)
    if any('attack.' in label or label.startswith('T') for label in labels):
        score += 0.15

    # 6. Source Reputation (up to 15% weight)
    source_scores = {
        'opencti': 0.15,   # Your own instance - highest trust
        'otx': 0.10,       # Community feed - medium trust
        'threatfox': 0.12  # Specialized malware - high trust
    }
    score += source_scores.get(rule.get('source'), 0.05)

    return min(score, 1.0)
```

### Quality Score Breakdown

| Component | Weight | Description |
|-----------|--------|-------------|
| **Base Confidence** | 30% | Confidence score from CTI source |
| **Meaningful Name** | 10% | Has descriptive name (not just IOC value) |
| **Description** | 10% | Contains useful context (>20 chars) |
| **Labels/Tags** | 20% | Contextual tags (5% each, max 4) |
| **MITRE Mapping** | 15% | Mapped to ATT&CK framework |
| **Source Trust** | 15% | Reputation of intelligence source |

### Quality Tiers

- **High Quality (≥0.7)**: Actionable, specific, well-documented
- **Medium Quality (0.4-0.7)**: Useful but needs validation
- **Low Quality (<0.4)**: Generic, lacks context, high FP risk

## Usefulness Analysis

### Current Rule Distribution

Based on analysis of 711 rules:

#### By Type
- **IPv4 Addresses**: 445 (62.6%) - Mix of scanners and actual threats
- **STIX Patterns**: 163 (22.9%) - Structured threat patterns
- **File Hashes**: 90 (12.7%) - Specific malware samples
- **Domains**: 13 (1.8%) - C2 servers and malicious domains

#### By Actual Usefulness

**High-Value Rules (~7% of total)**
- **Named Malware**: 51 rules
  - ClearFake, Cobalt Strike, Emotet variants
  - Specific malware families with known TTPs
  - Actionable for immediate blocking

**Medium-Value Rules (~20% of total)**
- **Labeled IOCs**: 145 rules
  - Have some context but generic
  - Require correlation with other data
  - Useful for threat hunting

**Low-Value Rules (~73% of total)**
- **Honeypot Scanners**: 353 rules
  - Internet-wide scanning activity
  - Not targeted attacks
  - High false positive rate

- **Cloud Infrastructure**: 70 rules
  - Azure/AWS IP ranges
  - Could be legitimate or malicious
  - Requires additional context

### Why So Many Low-Value Rules?

1. **Honeypot Bias**: OTX heavily features honeypot data (50% of our rules)
2. **Scanner Noise**: Automated internet scanning creates volume, not value
3. **Missing Context**: Most IOCs lack attribution or campaign details
4. **No Filtering**: Currently ingesting ALL indicators without quality threshold

### Actually Useful Rules

Only about **7-20%** of rules are immediately actionable:

```sql
-- High-value rules have:
-- 1. Specific malware attribution
-- 2. Multiple corroborating sources
-- 3. Recent activity (< 7 days)
-- 4. Not in common cloud/CDN ranges

SELECT COUNT(*) FROM detection_rules
WHERE
    jsonb_array_length(rule_data->'labels') > 0  -- Has context
    AND rule_data->>'source' != 'otx'            -- Not honeypot data
    AND created_at > NOW() - INTERVAL '7 days'   -- Recent
```

## Architecture

### Data Flow

```
CTI Sources                 Ingestion Engine              Backend Engine
-----------                 ----------------              --------------
                                    │
OpenCTI  ─────┐                    │                     ┌─> Deduplication
              │                    │                     │
OTX      ─────┼──> Fetch ──> Normalize ──> Redis ──────┼─> Quality Score
              │                    │                     │
ThreatFox ────┘                    │                     └─> Rule Generation
                                   │                              │
                            Update Intervals:                     ▼
                            - OpenCTI: 1 hour                PostgreSQL
                            - OTX: 1 hour                    (detection_rules)
                            - ThreatFox: 2 hours
```

### Key Components

1. **CTI Manager** (`engines/ingestion/cti_manager.py`)
   - Orchestrates all CTI feed connections
   - Manages update intervals
   - Publishes to Redis channels

2. **CTI Connectors**
   - `opencti_integration.py`: GraphQL API to your OpenCTI instance
   - `otx_integration.py`: REST API to AlienVault OTX
   - `threatfox_integration.py`: abuse.ch ThreatFox API

3. **Backend Engine** (`engines/backend/backend_engine.py`)
   - Receives CTI data from Redis
   - Generates Sigma rules
   - Calculates quality scores
   - Handles deduplication

4. **Rule Monitor** (`engines/backend/rule_monitor.py`)
   - Analyzes rule quality
   - Detects duplicates
   - Generates reports

## Duplicate Prevention

### How It Works

```python
# Before inserting a new rule:
1. Check if IOC value + source already exists
2. If exists:
   - Compare quality scores
   - Check if new rule has more labels
   - Update only if meaningful improvement
3. If not exists:
   - Insert new rule
   - Calculate quality score
   - Validate for false positives
```

### Deduplication Strategy

- **Primary Key**: `ioc_value` + `source`
- **Keep**: Highest quality score or oldest (if equal)
- **Update**: Only if >20% confidence increase or new labels

### Results

- **Before**: 1,546 rules with 54% duplicates
- **After**: 711 unique rules
- **Prevented**: 835 duplicate insertions

## Monitoring & Maintenance

### Daily Operations

```bash
# Check rule quality distribution
cd engines/backend
python rule_monitor.py

# Clean duplicates (dry run)
python -c "
from rule_monitor import RuleMonitor
monitor = RuleMonitor(db_config)
monitor.cleanup_duplicates(dry_run=True)
"

# View recent high-quality rules
psql -d siemless_v2 -c "
SELECT rule_data->>'name', rule_data->>'source'
FROM detection_rules
WHERE (rule_data->>'quality_score')::float > 0.7
ORDER BY created_at DESC LIMIT 10
"
```

### Key Metrics to Track

1. **Duplicate Rate**: Should stay < 1%
2. **Quality Distribution**: Aim for >30% medium/high quality
3. **MITRE Coverage**: Target >20% with ATT&CK mapping
4. **False Positive Rate**: Monitor cloud/CDN IPs

### Improvement Recommendations

1. **Filter at Ingestion**
   ```python
   # Add to CTI Manager
   if indicator.confidence < 0.3:
       continue  # Skip low-confidence IOCs
   ```

2. **Enrich with Context**
   - Cross-reference with VirusTotal
   - Add GeoIP data for IP addresses
   - Query passive DNS for domains

3. **Add Allowlisting**
   ```python
   ALLOWLIST_PATTERNS = [
       '10.0.0.0/8',      # Private ranges
       '**********/12',
       '***********/16',
       'google.com',       # Known good domains
       'microsoft.com'
   ]
   ```

4. **Implement Aging**
   - Reduce confidence over time
   - Archive rules older than 90 days
   - Higher weight to recent intelligence

## API Reference

### CTI Update Message (Redis)

```json
{
    "source": "opencti|otx|threatfox",
    "timestamp": "2025-09-29T15:00:00Z",
    "total_items": 100,
    "data": {
        "indicators": [...],
        "reports": [...],
        "attack_patterns": [...]
    }
}
```

### Detection Rule Schema

```json
{
    "id": "uuid",
    "name": "Descriptive name",
    "title": "Display title",
    "description": "Detailed description",
    "ioc_value": "The actual IOC",
    "ioc_type": "ip|domain|hash|url",
    "pattern": "STIX pattern if available",
    "confidence": 0.0-1.0,
    "quality_score": 0.0-1.0,
    "source": "opencti|otx|threatfox",
    "labels": ["malware:emotet", "attack.t1055"],
    "sigma_rule": "Full Sigma rule YAML",
    "validation": {
        "quality_score": 0.5,
        "potential_fps": ["Private IP"],
        "recommendations": ["Review context"]
    },
    "created_at": "ISO timestamp"
}
```

### Environment Variables

```bash
# OpenCTI Configuration
OPENCTI_URL=http://***********:8080
OPENCTI_API_KEY=your-opencti-token
OPENCTI_ENABLED=true

# OTX Configuration
OTX_API_KEY=your-otx-key
OTX_ENABLED=true

# ThreatFox Configuration
THREATFOX_AUTH_KEY=your-threatfox-key
THREATFOX_ENABLED=true
```

## Conclusion

The CTI integration successfully ingests and processes threat intelligence, but **only 7-20% of rules are immediately useful** for detection. The majority are:
- Generic internet scanners (not targeted)
- Honeypot artifacts (not real attacks)
- Missing context (can't determine relevance)

### To Improve Usefulness:

1. **Quality Threshold**: Only store rules with quality_score > 0.4
2. **Source Filtering**: Prioritize OpenCTI and ThreatFox over OTX honeypot data
3. **Context Enrichment**: Add VirusTotal, passive DNS, GeoIP
4. **Feedback Loop**: Track which rules actually fired in production
5. **Allowlisting**: Filter out known-good IPs and domains

The system provides the foundation for CTI-driven detection, but requires tuning to maximize signal-to-noise ratio.