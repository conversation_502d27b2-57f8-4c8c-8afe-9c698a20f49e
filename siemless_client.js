/**
 * SIEMLess v2.0 JavaScript Client SDK
 * Auto-generated from OpenAPI specification
 */

class SIEMLessClient {
    constructor(baseUrl = "http://localhost:8003", apiKey = null) {
        this.baseUrl = baseUrl.replace(/\/$/, "");
        this.apiKey = apiKey;
    }

    async _request(method, path, data = null) {
        const headers = {
            "Content-Type": "application/json"
        };

        if (this.apiKey) {
            headers["X-API-Key"] = this.apiKey;
        }

        const options = {
            method,
            headers
        };

        if (data && (method === "POST" || method === "PUT")) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(`${this.baseUrl}${path}`, options);

        if (!response.ok) {
            throw new Error(`Request failed: ${response.statusText}`);
        }

        return response.json();
    }

    async ingestLogs(source, logs) {
        return this._request("POST", "/api/v1/ingest", { source, logs });
    }

    async listPatterns(type = null) {
        const params = type ? `?type=${type}` : "";
        return this._request("GET", `/api/v1/patterns${params}`);
    }

    async syncGitHubPatterns(repository = null, force = false) {
        return this._request("POST", "/api/v1/patterns/sync/github", { repository, force });
    }

    async generateQueries(queryIntent) {
        return this._request("POST", "/api/v1/query/generate", queryIntent);
    }

    async naturalLanguageQuery(query) {
        return this._request("POST", "/api/v1/query/natural", { query });
    }
}

// Example usage
// const client = new SIEMLessClient("http://localhost:8003", "your-api-key");
// const result = await client.ingestLogs("crowdstrike", [{detection_id: "123"}]);

export default SIEMLessClient;
