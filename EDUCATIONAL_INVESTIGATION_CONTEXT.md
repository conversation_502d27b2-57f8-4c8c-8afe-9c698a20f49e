# Educational Investigation Context System

## The New Philosophy: "Teach, Don't Just Tell"

### ❌ Old Approach (Black Box)
```
Verdict: LIKELY_BENIGN
Action: REVIEW_AND_CLOSE
```
**Problem:** Analyst learns nothing. Next similar alert, they still don't know how to investigate.

### ✅ New Approach (Educational)
```
┌─────────────────────────────────────────────────────────────┐
│ 🎓 INVESTIGATION WALKTHROUGH                                │
├─────────────────────────────────────────────────────────────┤
│ Let me show you how I investigated this alert...            │
│                                                              │
│ STEP 1: Identify the Source IP                              │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│ Source IP: *************                                     │
│                                                              │
│ 🔍 What I checked:                                          │
│   • Is this IP internal or external?                        │
│     → Internal (RFC1918 private address range)              │
│                                                              │
│   • Where can I verify this?                                │
│     📚 Resource: RFC1918 defines private IP ranges:         │
│        • 10.0.0.0/8                                          │
│        • **********/12                                       │
│        • ***********/16 ← This IP falls here               │
│                                                              │
│   ✅ FINDING: Internal IP (+25 confidence points)           │
│      WHY: Internal IPs are generally less risky than        │
│           external attackers, but can still indicate        │
│           lateral movement if compromised.                  │
│                                                              │
│ STEP 2: Check Threat Intelligence                           │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│ 🔍 What I checked:                                          │
│   • Is this IP in our IOC database?                         │
│     → Query: SELECT * FROM iocs WHERE ioc_value='192.168...'│
│     → Result: Not found ✅                                  │
│                                                              │
│   • What would I check externally? (You should verify!)     │
│     📚 Recommended Tools:                                   │
│        1. VirusTotal (virustotal.com)                       │
│           → Check IP reputation across 80+ vendors          │
│        2. AbuseIPDB (abuseipdb.com)                         │
│           → Community-reported malicious IPs                │
│        3. GreyNoise (greynoise.io)                          │
│           → Internet-wide scanning activity                 │
│        4. AlienVault OTX (otx.alienvault.com)               │
│           → Open threat intelligence platform               │
│                                                              │
│   ✅ FINDING: No malicious indicators (+40 confidence)      │
│      WHY: Clean threat intel suggests this isn't a known    │
│           malicious actor, but doesn't rule out compromise. │
│                                                              │
│ STEP 3: Analyze the Activity (Port Scanning)                │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│ 🔍 What I checked:                                          │
│   • What type of activity was detected?                     │
│     → Horizontal Port Scan (T1046 - Network Discovery)      │
│                                                              │
│   • What does this mean?                                    │
│     📚 MITRE ATT&CK T1046:                                  │
│        "Adversaries may attempt to get a listing of         │
│         services running on remote hosts by probing ports." │
│                                                              │
│   • Is this ALWAYS malicious?                               │
│     ❌ NO! Port scanning can be:                            │
│        BENIGN:                                               │
│        • Authorized vulnerability scanners                   │
│        • Network monitoring tools                            │
│        • IT troubleshooting                                  │
│                                                              │
│        MALICIOUS:                                            │
│        • Post-compromise reconnaissance                      │
│        • Lateral movement preparation                        │
│        • Pre-ransomware mapping                              │
│                                                              │
│   🤔 So how do we tell the difference?                      │
│      We need MORE CONTEXT! (See next steps...)              │
│                                                              │
│ STEP 4: Check Asset Context                                 │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│ 🔍 What I checked:                                          │
│   • What is this device?                                    │
│     → Query: Asset Database for *************               │
│     → Result: No entry found ⚠️                             │
│                                                              │
│   📚 What you should check:                                 │
│      • CMDB (Configuration Management Database)             │
│      • Active Directory computer objects                    │
│      • Network documentation                                │
│      • DHCP logs for hostname                               │
│                                                              │
│   ⚠️  MISSING DATA: No asset information available          │
│      RECOMMENDATION: Add this IP to asset inventory!        │
│      ACTION: Ask IT: "Who owns *************?"              │
│                                                              │
│ STEP 5: Analyze Alert Severity                              │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│   • What severity did the rule assign?                      │
│     → LOW                                                    │
│                                                              │
│   📚 Understanding Severity:                                │
│      LOW: Unusual behavior but low immediate risk           │
│      MEDIUM: Suspicious activity requiring review           │
│      HIGH: Likely malicious, investigate immediately        │
│      CRITICAL: Active attack, respond now                   │
│                                                              │
│   ✅ FINDING: Low severity (+10 confidence)                 │
│      WHY: The rule author assessed this specific pattern    │
│           as low risk based on their experience.            │
│                                                              │
│ STEP 6: Check Historical Behavior                           │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│   • Has this IP done this before?                           │
│     → Query: Last 30 days of similar alerts                 │
│     → Result: 0 similar alerts                              │
│                                                              │
│   📚 What you should check in Elastic:                      │
│      Query: source.ip:"*************" AND                   │
│             kibana.alert.rule.name:*scan*                   │
│      Time Range: Last 30 days                               │
│                                                              │
│   🤔 What does this tell us?                                │
│      • First time seeing this behavior                      │
│      • Could be new authorized scanner                      │
│      • Could be newly compromised host                      │
│      • Need MORE context to decide!                         │
│                                                              │
│ 📊 CONFIDENCE SCORE BREAKDOWN                               │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│                                                              │
│ Starting Score:          0 points                           │
│                                                              │
│ + Threat Intel Clean:   +40 points                          │
│   Why: No known malicious indicators                        │
│                                                              │
│ + Internal IP:          +25 points                          │
│   Why: Internal IPs are generally less risky                │
│                                                              │
│ + Low Severity:         +10 points                          │
│   Why: Rule author assessed as low risk                     │
│                                                              │
│ Total Confidence:       75 points                           │
│                                                              │
│ 📏 Confidence Scale:                                        │
│    0-39:   REQUIRES_INVESTIGATION (high risk)               │
│    40-69:  POSSIBLY_BENIGN (review needed)                  │
│    70-100: LIKELY_BENIGN (low risk)                         │
│                                                              │
│ 🎯 MY VERDICT                                               │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│                                                              │
│ Verdict: LIKELY_BENIGN (75% confidence)                     │
│ Action:  REVIEW_AND_CLOSE                                   │
│                                                              │
│ 📝 MY REASONING:                                            │
│   1. IP is internal to your network (+)                     │
│   2. No malicious indicators in threat intelligence (+)     │
│   3. Alert severity is low (+)                              │
│   4. BUT: No asset information available (-)                │
│   5. AND: First time seeing this behavior (-)               │
│                                                              │
│ ⚠️  WHAT'S MISSING (and why I can't be 95% confident):      │
│   • Asset information: Is this an authorized scanner?       │
│   • User context: What account initiated this?              │
│   • Time context: During maintenance window?                │
│   • Port details: What specific ports were scanned?         │
│                                                              │
│ 🎓 WHAT YOU SHOULD DO:                                      │
│                                                              │
│   QUICK WIN (2 minutes):                                    │
│   1. Check if this IP is in your maintenance calendar       │
│   2. Look up hostname in Active Directory                   │
│   3. If it's a known scanner → Close as False Positive      │
│                                                              │
│   THOROUGH INVESTIGATION (10 minutes):                      │
│   1. Query Elastic for full scan details:                   │
│      source.ip:"*************" AND @timestamp:[...]         │
│   2. Check what ports were scanned                          │
│   3. See if any connections succeeded                       │
│   4. Check for follow-up lateral movement                   │
│   5. Verify with IT if this was authorized                  │
│                                                              │
│ 🔗 SIEM LINK-BACK                                           │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│                                                              │
│   Query to run in Elastic:                                  │
│   source.ip:"*************"                                 │
│                                                              │
│   Time Range:                                                │
│   From: 2025-10-02 09:57:22 (1 hour before alert)          │
│   To:   2025-10-02 11:07:22 (10 min after alert)           │
│                                                              │
│   [Open in Elastic] ←─ (Click to view raw logs)            │
│                                                              │
│ 💡 LEARNING RESOURCES                                       │
│ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ │
│                                                              │
│   Want to learn more about this technique?                  │
│                                                              │
│   📚 MITRE ATT&CK:                                          │
│      T1046 - Network Service Discovery                      │
│      https://attack.mitre.org/techniques/T1046/             │
│                                                              │
│   📚 RFC1918 (Private IP Ranges):                           │
│      https://datatracker.ietf.org/doc/html/rfc1918         │
│                                                              │
│   📚 Threat Intel Best Practices:                           │
│      • VirusTotal: virustotal.com                           │
│      • AbuseIPDB: abuseipdb.com                             │
│      • GreyNoise: greynoise.io                              │
│                                                              │
│   📚 Investigation Guide (from this rule):                  │
│      [View Full Guide] ←─ (Expand to see detailed steps)   │
│                                                              │
└─────────────────────────────────────────────────────────────┘
```

## Key Educational Elements

### 1. **Step-by-Step Reasoning**
Every decision is broken down:
- What question did I ask?
- How did I answer it?
- Where did I get the data?
- What does this tell us?

### 2. **Tool References**
For every check, show:
- ✅ What I checked automatically
- 📚 What YOU should verify manually
- 🔗 Links to tools/resources

### 3. **Context Explanations**
Don't just say "Internal IP = +25 points"
Say: "WHY does internal IP matter? Because..."

### 4. **Teach the Nuance**
"Port scanning is NOT always malicious!"
Show both benign and malicious scenarios

### 5. **Confidence Breakdown**
Show the math:
```
Starting:    0
+ TI Clean: +40
+ Internal: +25
+ Low Sev:  +10
= 75 total
```

### 6. **What's Missing**
Be honest about uncertainty:
"I can't be 95% confident because I don't have asset information"

### 7. **Action Recommendations**
Give them choices:
- Quick win (2 min)
- Thorough investigation (10 min)

### 8. **Learning Resources**
Link to:
- MITRE ATT&CK
- RFCs
- Threat intel tools
- Investigation guides

## JSON Response Format (Educational)

```json
{
  "alert_id": "...",
  "investigation_walkthrough": {
    "steps": [
      {
        "step_number": 1,
        "title": "Identify the Source IP",
        "questions_asked": [
          "Is this IP internal or external?"
        ],
        "what_i_checked": {
          "method": "IP range analysis",
          "query": "Check if IP falls in RFC1918 ranges",
          "result": "Internal (***********/16)"
        },
        "educational_notes": {
          "concept": "RFC1918 Private IP Ranges",
          "explanation": "Private IPs cannot be routed on the internet...",
          "resources": [
            {
              "title": "RFC1918 Specification",
              "url": "https://datatracker.ietf.org/doc/html/rfc1918"
            }
          ]
        },
        "finding": {
          "summary": "Internal IP",
          "confidence_impact": +25,
          "why": "Internal IPs are generally less risky than external attackers..."
        }
      },
      {
        "step_number": 2,
        "title": "Check Threat Intelligence",
        "questions_asked": [
          "Is this IP in our IOC database?",
          "What external sources should I check?"
        ],
        "what_i_checked": {
          "method": "Database query",
          "query": "SELECT * FROM iocs WHERE ioc_value='*************'",
          "result": "Not found"
        },
        "what_you_should_verify": {
          "manual_checks": [
            {
              "tool": "VirusTotal",
              "url": "https://virustotal.com",
              "how_to": "Search for the IP and check vendor detections"
            },
            {
              "tool": "AbuseIPDB",
              "url": "https://abuseipdb.com",
              "how_to": "Check if IP has been reported for malicious activity"
            }
          ]
        },
        "finding": {
          "summary": "No malicious indicators",
          "confidence_impact": +40,
          "why": "Clean threat intel suggests this isn't a known malicious actor..."
        }
      },
      {
        "step_number": 3,
        "title": "Understand the Activity Type",
        "questions_asked": [
          "What activity was detected?",
          "Is this always malicious?"
        ],
        "educational_notes": {
          "concept": "MITRE ATT&CK T1046",
          "explanation": "Network Service Discovery - scanning to find vulnerable services",
          "benign_scenarios": [
            "Authorized vulnerability scanners",
            "Network monitoring tools",
            "IT troubleshooting"
          ],
          "malicious_scenarios": [
            "Post-compromise reconnaissance",
            "Lateral movement preparation",
            "Pre-ransomware environment mapping"
          ],
          "resources": [
            {
              "title": "MITRE T1046",
              "url": "https://attack.mitre.org/techniques/T1046/"
            }
          ]
        },
        "finding": {
          "summary": "Context-dependent activity",
          "confidence_impact": 0,
          "why": "Need more context to determine if benign or malicious"
        }
      }
    ],
    "confidence_breakdown": {
      "starting_score": 0,
      "adjustments": [
        {
          "factor": "Threat Intelligence Clean",
          "points": +40,
          "explanation": "No known malicious indicators found"
        },
        {
          "factor": "Internal IP",
          "points": +25,
          "explanation": "Internal IPs are generally less risky"
        },
        {
          "factor": "Low Severity",
          "points": +10,
          "explanation": "Rule author assessed as low risk"
        }
      ],
      "total": 75,
      "scale_explanation": {
        "0-39": "REQUIRES_INVESTIGATION (high risk)",
        "40-69": "POSSIBLY_BENIGN (review needed)",
        "70-100": "LIKELY_BENIGN (low risk)"
      }
    },
    "verdict": {
      "classification": "LIKELY_BENIGN",
      "confidence": 75,
      "recommended_action": "REVIEW_AND_CLOSE",
      "reasoning": [
        "IP is internal to your network",
        "No malicious indicators in threat intelligence",
        "Alert severity is low"
      ],
      "caveats": [
        "No asset information available - unknown if authorized scanner",
        "First time seeing this behavior - no historical pattern"
      ]
    },
    "what_is_missing": [
      {
        "data": "Asset information",
        "why_important": "Would tell us if this is an authorized scanner",
        "where_to_find": "CMDB, Active Directory, Asset Database",
        "confidence_impact": "Would increase to 90%+ if authorized"
      },
      {
        "data": "User/account context",
        "why_important": "Service account vs. user account matters",
        "where_to_find": "Windows Event Logs, EDR telemetry",
        "confidence_impact": "+15 points if service account"
      }
    ],
    "recommended_actions": {
      "quick_review": {
        "time_estimate": "2 minutes",
        "steps": [
          "Check if IP is in maintenance calendar",
          "Look up hostname in Active Directory",
          "If known scanner → Close as False Positive"
        ]
      },
      "thorough_investigation": {
        "time_estimate": "10 minutes",
        "steps": [
          "Query Elastic for full scan details",
          "Check what ports were scanned",
          "See if any connections succeeded",
          "Check for follow-up lateral movement",
          "Verify with IT if authorized"
        ]
      }
    },
    "siem_linkback": {
      "query": "source.ip:\"*************\"",
      "time_range": {
        "start": "2025-10-02T09:57:22",
        "end": "2025-10-02T11:07:22"
      },
      "elastic_url": "https://your-elastic.com/app/discover#/?query=...",
      "instructions": "Click to view raw logs in Elastic for detailed analysis"
    },
    "learning_resources": [
      {
        "category": "MITRE ATT&CK",
        "title": "T1046 - Network Service Discovery",
        "url": "https://attack.mitre.org/techniques/T1046/",
        "why_relevant": "This alert maps to this technique"
      },
      {
        "category": "Threat Intelligence",
        "title": "VirusTotal",
        "url": "https://virustotal.com",
        "why_relevant": "Verify IP reputation across 80+ vendors"
      },
      {
        "category": "Investigation Guide",
        "title": "Full Investigation Guide for This Rule",
        "content": "...(the full guide from Elastic)...",
        "why_relevant": "Detailed steps from rule author"
      }
    ]
  }
}
```

## This Transforms SIEMLess Into a Training Platform

**New analysts learn:**
- How to investigate step-by-step
- What questions to ask
- Where to find information
- What tools to use
- Why each factor matters

**Experienced analysts benefit:**
- Quick confidence check on their own reasoning
- Documentation for audit trail
- Resources to share with junior staff
- Consistent methodology across team

**The organization gains:**
- Reduced training time for new hires
- Standardized investigation process
- Knowledge retention (embedded in the tool)
- Continuous improvement (analysts see the logic and can suggest improvements)

This is **"Show your work"** applied to cybersecurity investigations!
