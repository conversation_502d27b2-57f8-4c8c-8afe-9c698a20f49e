import React, { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Search, FileText, Database, Users, Sparkles,
  Clock, ArrowRight, X, Command, Hash, Zap
} from 'lucide-react'
import { useNavigationStore } from '../../stores/navigationStore'

interface SearchResult {
  id: string
  type: 'case' | 'entity' | 'pattern' | 'document' | 'action'
  title: string
  description: string
  path: string
  icon: React.ComponentType<{ size?: number }>
  metadata?: Record<string, any>
}

interface GlobalSearchProps {
  onClose: () => void
}

const GlobalSearch: React.FC<GlobalSearchProps> = ({ onClose }) => {
  const navigate = useNavigate()
  const { addToRecent } = useNavigationStore()
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [loading, setLoading] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    inputRef.current?.focus()
  }, [])

  // Mock search function
  useEffect(() => {
    if (!query.trim()) {
      setResults([])
      return
    }

    setLoading(true)
    const timer = setTimeout(() => {
      // Mock results
      const mockResults: SearchResult[] = [
        {
          id: '1',
          type: 'case',
          title: `CASE-${Math.floor(Math.random() * 1000)}`,
          description: 'Suspicious PowerShell activity detected',
          path: `/cases/${Math.floor(Math.random() * 1000)}`,
          icon: FileText
        },
        {
          id: '2',
          type: 'entity',
          title: '*************',
          description: 'Internal IP address with high activity',
          path: '/investigation/entities/ip-192-168-1-100',
          icon: Database
        },
        {
          id: '3',
          type: 'pattern',
          title: 'Lateral Movement Detection',
          description: 'Pattern for detecting lateral movement attempts',
          path: '/engineering/patterns/lateral-movement',
          icon: Sparkles
        },
        {
          id: '4',
          type: 'action',
          title: 'Isolate Host',
          description: 'Quick action to isolate a compromised host',
          path: '#isolate-host',
          icon: Zap
        }
      ].filter(r =>
        r.title.toLowerCase().includes(query.toLowerCase()) ||
        r.description.toLowerCase().includes(query.toLowerCase())
      )

      setResults(mockResults)
      setLoading(false)
    }, 300)

    return () => clearTimeout(timer)
  }, [query])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      } else if (e.key === 'ArrowDown') {
        e.preventDefault()
        setSelectedIndex(prev => Math.min(prev + 1, results.length - 1))
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setSelectedIndex(prev => Math.max(prev - 1, 0))
      } else if (e.key === 'Enter' && results[selectedIndex]) {
        e.preventDefault()
        handleResultClick(results[selectedIndex])
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [results, selectedIndex, onClose])

  const handleResultClick = (result: SearchResult) => {
    if (result.type === 'action') {
      // Handle quick actions
      console.log('Execute action:', result.path)
    } else {
      // Navigate to the result
      navigate(result.path)
      addToRecent({
        id: result.id,
        label: result.title,
        path: result.path,
        type: result.type as any
      })
    }
    onClose()
  }

  const recentSearches = [
    'CASE-123',
    'PowerShell',
    '*************',
    'lateral movement'
  ]

  const quickActions = [
    { label: 'New Investigation', shortcut: 'Ctrl+N', path: '/investigation/new' },
    { label: 'View Dashboard', shortcut: 'Ctrl+D', path: '/' },
    { label: 'Pattern Library', shortcut: 'Ctrl+P', path: '/engineering/patterns' }
  ]

  return (
    <div className="fixed inset-0 z-50 flex items-start justify-center pt-20">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />

      {/* Search Modal */}
      <div className="relative w-full max-w-2xl bg-white rounded-lg shadow-2xl">
        {/* Search Input */}
        <div className="flex items-center gap-3 px-4 py-4 border-b">
          <Search size={20} className="text-gray-400" />
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Search cases, entities, patterns, or actions..."
            className="flex-1 text-lg outline-none"
          />
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="max-h-96 overflow-y-auto">
          {!query.trim() ? (
            <>
              {/* Recent Searches */}
              <div className="p-4">
                <div className="flex items-center gap-2 mb-3 text-sm text-gray-500">
                  <Clock size={14} />
                  <span>Recent Searches</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {recentSearches.map(search => (
                    <button
                      key={search}
                      onClick={() => setQuery(search)}
                      className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-full"
                    >
                      {search}
                    </button>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="p-4 border-t">
                <div className="flex items-center gap-2 mb-3 text-sm text-gray-500">
                  <Command size={14} />
                  <span>Quick Actions</span>
                </div>
                <div className="space-y-1">
                  {quickActions.map((action, idx) => (
                    <button
                      key={idx}
                      onClick={() => {
                        navigate(action.path)
                        onClose()
                      }}
                      className="w-full flex items-center justify-between p-2 text-left hover:bg-gray-100 rounded"
                    >
                      <span>{action.label}</span>
                      <kbd className="px-2 py-0.5 text-xs bg-gray-100 rounded border border-gray-300">
                        {action.shortcut}
                      </kbd>
                    </button>
                  ))}
                </div>
              </div>
            </>
          ) : loading ? (
            <div className="p-8 text-center text-gray-500">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4" />
              Searching...
            </div>
          ) : results.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              No results found for "{query}"
            </div>
          ) : (
            <div className="py-2">
              {results.map((result, idx) => (
                <button
                  key={result.id}
                  onClick={() => handleResultClick(result)}
                  onMouseEnter={() => setSelectedIndex(idx)}
                  className={`w-full flex items-center gap-3 px-4 py-3 text-left transition-colors ${
                    idx === selectedIndex ? 'bg-blue-50' : 'hover:bg-gray-50'
                  }`}
                >
                  <result.icon size={20} className="text-gray-400 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-gray-900">{result.title}</div>
                    <div className="text-sm text-gray-500 truncate">{result.description}</div>
                  </div>
                  {idx === selectedIndex && (
                    <ArrowRight size={16} className="text-blue-500" />
                  )}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-4 py-3 border-t bg-gray-50 flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center gap-4">
            <span className="flex items-center gap-1">
              <kbd className="px-1.5 py-0.5 bg-white rounded border border-gray-300">↑↓</kbd>
              Navigate
            </span>
            <span className="flex items-center gap-1">
              <kbd className="px-1.5 py-0.5 bg-white rounded border border-gray-300">Enter</kbd>
              Select
            </span>
            <span className="flex items-center gap-1">
              <kbd className="px-1.5 py-0.5 bg-white rounded border border-gray-300">Esc</kbd>
              Close
            </span>
          </div>
          <div>
            {results.length > 0 && (
              <span>{results.length} result{results.length !== 1 ? 's' : ''}</span>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default GlobalSearch