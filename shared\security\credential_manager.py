"""
SIEMLess v2.0 Credential Manager

Provides secure access to encrypted API credentials stored in the database.
Integrates with the v2 microservices architecture for credential retrieval.
"""

import os
import json
import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

import asyncpg
from asyncpg import Connection

# Crypto imports
import secrets
import base64
from cryptography.hazmat.primitives.ciphers.aead import AESGCM

logger = logging.getLogger(__name__)

class CredentialManager:
    """
    Manages encrypted credentials for SIEMLess v2.0 engines.

    Provides simple access to API keys while maintaining security through
    database encryption and proper access controls.
    """

    def __init__(self, db_connection_params: Optional[Dict[str, Any]] = None):
        """
        Initialize the credential manager.

        Args:
            db_connection_params: Database connection parameters.
                                If None, will use environment variables.
        """
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        # Database configuration
        self.db_config = db_connection_params or {
            'host': os.environ.get('POSTGRES_HOST', 'localhost'),
            'port': int(os.environ.get('POSTGRES_PORT', 5432)),
            'database': os.environ.get('POSTGRES_DB', 'siemless_v2'),
            'user': os.environ.get('POSTGRES_USER', 'siemless'),
            'password': os.environ.get('POSTGRES_PASSWORD', 'siemless123')
        }

        # Crypto setup
        self.master_key = self._get_master_key()

        # Default client/environment IDs (can be overridden)
        self.default_client_id = None
        self.default_environment_id = None

        self.logger.info("Credential manager initialized")

    def _get_master_key(self) -> bytes:
        """Get master key from environment"""
        master_key_str = os.environ.get("SIEMLESS_MASTER_KEY")
        if master_key_str:
            try:
                return base64.b64decode(master_key_str)
            except Exception:
                try:
                    return bytes.fromhex(master_key_str)
                except ValueError:
                    raise ValueError("Invalid master key format in environment")

        # Generate temporary key (not recommended for production)
        self.logger.warning("No master key found - generating temporary key")
        return secrets.token_bytes(32)

    async def _get_connection(self) -> Connection:
        """Get database connection"""
        try:
            return await asyncpg.connect(**self.db_config)
        except Exception as e:
            self.logger.error(f"Failed to connect to database: {e}")
            raise ConnectionError(f"Database connection failed: {e}")

    async def _get_default_client_environment(self, conn: Connection) -> tuple:
        """Get default client and environment IDs"""

        if self.default_client_id and self.default_environment_id:
            return self.default_client_id, self.default_environment_id

        # Get default client
        client_result = await conn.fetchrow("""
            SELECT client_id FROM siemless_v2.clients
            WHERE client_name = 'SIEMLess Default Client' AND is_active = TRUE
            LIMIT 1
        """)

        if not client_result:
            raise ValueError("No default client found - run setup_database.py first")

        client_id = str(client_result['client_id'])

        # Get default environment
        env_result = await conn.fetchrow("""
            SELECT environment_id FROM siemless_v2.environments
            WHERE client_id = $1 AND environment_name = 'production' AND is_active = TRUE
            LIMIT 1
        """, client_id)

        if not env_result:
            raise ValueError("No default environment found - run setup_database.py first")

        environment_id = str(env_result['environment_id'])

        # Cache for future use
        self.default_client_id = client_id
        self.default_environment_id = environment_id

        return client_id, environment_id

    def _decrypt_credentials(self, encrypted_data: bytes) -> Dict[str, Any]:
        """Decrypt credentials using AES-GCM"""
        try:
            # Convert memoryview to bytes if needed
            if isinstance(encrypted_data, memoryview):
                encrypted_data = bytes(encrypted_data)

            # Extract nonce and ciphertext (first 12 bytes are nonce)
            nonce = encrypted_data[:12]
            ciphertext = encrypted_data[12:]

            # Decrypt
            aesgcm = AESGCM(self.master_key)
            plaintext = aesgcm.decrypt(nonce, ciphertext, None)

            # Parse JSON
            credentials = json.loads(plaintext.decode('utf-8'))

            self.logger.debug(f"Decrypted credentials with {len(credentials)} keys")
            return credentials

        except Exception as e:
            self.logger.error(f"Failed to decrypt credentials: {e}")
            raise ValueError(f"Credential decryption failed: {e}")

    async def get_credentials(self,
                            provider_type: str,
                            provider_name: str,
                            credential_name: str = 'default',
                            client_id: Optional[str] = None,
                            environment_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get decrypted credentials for a provider.

        Args:
            provider_type: Type of provider ('ai_provider', 'cti_provider', 'siem_provider')
            provider_name: Name of provider ('anthropic', 'openai', 'opencti', etc.)
            credential_name: Name of credential set ('default', 'production', etc.)
            client_id: Optional client ID (uses default if None)
            environment_id: Optional environment ID (uses default if None)

        Returns:
            Dictionary of decrypted credentials

        Raises:
            ValueError: If credentials not found or decryption fails
            ConnectionError: If database connection fails
        """

        conn = None
        try:
            conn = await self._get_connection()

            # Get client/environment IDs
            if not client_id or not environment_id:
                client_id, environment_id = await self._get_default_client_environment(conn)

            # Retrieve encrypted credentials
            credential_result = await conn.fetchrow("""
                SELECT encrypted_data, created_at, metadata
                FROM siemless_v2.credentials
                WHERE client_id = $1 AND environment_id = $2
                      AND provider_type = $3 AND provider_name = $4
                      AND credential_name = $5 AND is_active = TRUE
                LIMIT 1
            """, client_id, environment_id, provider_type, provider_name, credential_name)

            if not credential_result:
                raise ValueError(f"No credentials found for {provider_type}/{provider_name}/{credential_name}")

            # Decrypt credentials
            decrypted_creds = self._decrypt_credentials(credential_result['encrypted_data'])

            # Log access (audit trail)
            await conn.execute("""
                INSERT INTO siemless_v2.credential_audit_log
                (client_id, environment_id, action, performed_by, details)
                VALUES ($1, $2, 'read', 'system_access', $3)
            """, client_id, environment_id, json.dumps({
                'provider_type': provider_type,
                'provider_name': provider_name,
                'credential_name': credential_name,
                'access_time': datetime.utcnow().isoformat(),
                'key_count': len(decrypted_creds)
            }))

            self.logger.info(f"Retrieved credentials for {provider_type}/{provider_name}")
            return decrypted_creds

        except Exception as e:
            self.logger.error(f"Failed to get credentials: {e}")
            raise

        finally:
            if conn:
                await conn.close()

    async def get_ai_provider_key(self, provider_name: str, key_name: Optional[str] = None) -> str:
        """
        Convenience method to get AI provider API key.

        Args:
            provider_name: AI provider ('anthropic', 'openai', 'google')
            key_name: Specific key name (auto-detected if None)

        Returns:
            API key string
        """

        credentials = await self.get_credentials('ai_provider', provider_name)

        # Auto-detect key name if not provided
        if not key_name:
            key_mapping = {
                'anthropic': 'ANTHROPIC_API_KEY',
                'openai': 'OPENAI_API_KEY',
                'google': 'GEMINI_API_KEY'
            }
            key_name = key_mapping.get(provider_name)

        if not key_name or key_name not in credentials:
            available_keys = list(credentials.keys())
            raise ValueError(f"Key '{key_name}' not found. Available: {available_keys}")

        return credentials[key_name]

    async def get_cti_provider_credentials(self, provider_name: str) -> Dict[str, Any]:
        """
        Convenience method to get CTI provider credentials.

        Args:
            provider_name: CTI provider ('opencti', 'otx', 'misp')

        Returns:
            Dictionary of CTI credentials
        """

        return await self.get_credentials('cti_provider', provider_name)

    async def get_siem_provider_credentials(self, provider_name: str) -> Dict[str, Any]:
        """
        Convenience method to get SIEM provider credentials.

        Args:
            provider_name: SIEM provider ('crowdstrike', 'elasticsearch', 'splunk')

        Returns:
            Dictionary of SIEM credentials
        """

        return await self.get_credentials('siem_provider', provider_name)

    async def list_available_credentials(self) -> Dict[str, Any]:
        """
        List all available credential providers.

        Returns:
            Dictionary grouped by provider type
        """

        conn = None
        try:
            conn = await self._get_connection()

            # Get default client/environment
            client_id, environment_id = await self._get_default_client_environment(conn)

            # Get all credentials
            credentials = await conn.fetch("""
                SELECT provider_type, provider_name, credential_name,
                       created_at, metadata
                FROM siemless_v2.credentials
                WHERE client_id = $1 AND environment_id = $2 AND is_active = TRUE
                ORDER BY provider_type, provider_name, credential_name
            """, client_id, environment_id)

            # Group by provider type
            result = {}
            for cred in credentials:
                provider_type = cred['provider_type']
                if provider_type not in result:
                    result[provider_type] = {}

                provider_name = cred['provider_name']
                if provider_name not in result[provider_type]:
                    result[provider_type][provider_name] = []

                result[provider_type][provider_name].append({
                    'credential_name': cred['credential_name'],
                    'created_at': cred['created_at'].isoformat(),
                    'metadata': cred['metadata']
                })

            return result

        finally:
            if conn:
                await conn.close()

# Global instance for use throughout v2 engines
_credential_manager: Optional[CredentialManager] = None

def get_credential_manager() -> CredentialManager:
    """
    Get the global credential manager instance.

    Returns:
        Initialized credential manager
    """
    global _credential_manager
    if _credential_manager is None:
        _credential_manager = CredentialManager()
    return _credential_manager

# Async convenience functions for engines
async def get_ai_key(provider: str, key_name: Optional[str] = None) -> str:
    """Get AI provider API key"""
    manager = get_credential_manager()
    return await manager.get_ai_provider_key(provider, key_name)

async def get_cti_credentials(provider: str) -> Dict[str, Any]:
    """Get CTI provider credentials"""
    manager = get_credential_manager()
    return await manager.get_cti_provider_credentials(provider)

async def get_siem_credentials(provider: str) -> Dict[str, Any]:
    """Get SIEM provider credentials"""
    manager = get_credential_manager()
    return await manager.get_siem_provider_credentials(provider)