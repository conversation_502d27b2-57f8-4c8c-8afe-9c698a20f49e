#!/bin/bash
# Elastic Detection Rules Import Script
# Generated by SIEMLess v2.0

ELASTIC_URL="${ELASTIC_URL:-http://localhost:9200}"
ELASTIC_USER="${ELASTIC_USER:-elastic}"
ELASTIC_PASS="${ELASTIC_PASS:-changeme}"

echo "Importing detection rules to Elastic..."
echo "Target: $ELASTIC_URL"


# Import rule: DNS Query to Malicious Domain apt28.adversary.org
curl -X POST "$ELASTIC_URL/_security/rule" \
  -u "$ELASTIC_USER:$ELASTIC_PASS" \
  -H "Content-Type: application/json" \
  -d "{\"rule_id\": \"CTI_apt_bfe0b06b\", \"name\": \"DNS Query to Malicious Domain apt28.adversary.org\", \"description\": \"CTI-based detection for DNS Query to Malicious Domain apt28.adversary.org\", \"risk_score\": 100, \"severity\": \"critical\", \"type\": \"query\", \"query\": \"event.type:dns and dns.question.name:apt28.adversary.org\", \"language\": \"kuery\", \"index\": [\"logs-*\", \"filebeat-*\", \"winlogbeat-*\"], \"tags\": [\"cti\", \"otx\", \"T1071\"], \"threat\": [{\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1071\", \"name\": \"command-and-control\", \"reference\": \"https://attack.mitre.org/tactics/T1071\"}, \"technique\": [{\"id\": \"T1071\", \"name\": \"Application Layer Protocol\", \"reference\": \"https://attack.mitre.org/techniques/T1071\"}]}], \"author\": [\"SIEMLess v2.0 CTI Engine\"], \"false_positives\": [\"Legitimate services may occasionally match these patterns\"], \"from\": \"now-15m\", \"interval\": \"5m\", \"meta\": {\"confidence\": 1.0, \"indicators\": [\"apt28.adversary.org\"], \"source\": \"OTX CTI Feed\"}, \"enabled\": true}"


# Import rule: Suspicious C2 Communication to **************
curl -X POST "$ELASTIC_URL/_security/rule" \
  -u "$ELASTIC_USER:$ELASTIC_PASS" \
  -H "Content-Type: application/json" \
  -d "{\"rule_id\": \"CTI_botnet_2bf58664\", \"name\": \"Suspicious C2 Communication to **************\", \"description\": \"CTI-based detection for Suspicious C2 Communication to **************\", \"risk_score\": 45, \"severity\": \"medium\", \"type\": \"query\", \"query\": \"(source.ip:10.0.0.0/8 or source.ip:**********/12 or source.ip:***********/16) and destination.ip:************** and (network.bytes >= 1000 or event.count >= 10)\", \"language\": \"kuery\", \"index\": [\"logs-*\", \"filebeat-*\", \"winlogbeat-*\"], \"tags\": [\"cti\", \"otx\", \"T1071\", \"T1105\"], \"threat\": [{\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1071\", \"name\": \"command-and-control\", \"reference\": \"https://attack.mitre.org/tactics/T1071\"}, \"technique\": [{\"id\": \"T1071\", \"name\": \"Application Layer Protocol\", \"reference\": \"https://attack.mitre.org/techniques/T1071\"}]}, {\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1105\", \"name\": \"command-and-control\", \"reference\": \"https://attack.mitre.org/tactics/T1105\"}, \"technique\": [{\"id\": \"T1105\", \"name\": \"Ingress Tool Transfer\", \"reference\": \"https://attack.mitre.org/techniques/T1105\"}]}], \"author\": [\"SIEMLess v2.0 CTI Engine\"], \"false_positives\": [\"Legitimate services may occasionally match these patterns\"], \"from\": \"now-15m\", \"interval\": \"5m\", \"meta\": {\"confidence\": 0.9, \"indicators\": [\"**************\"], \"source\": \"OTX CTI Feed\"}, \"enabled\": true}"


# Import rule: DNS Query to Malicious Domain evil-malware-c2.com
curl -X POST "$ELASTIC_URL/_security/rule" \
  -u "$ELASTIC_USER:$ELASTIC_PASS" \
  -H "Content-Type: application/json" \
  -d "{\"rule_id\": \"CTI_command_and_control_3354bf44\", \"name\": \"DNS Query to Malicious Domain evil-malware-c2.com\", \"description\": \"CTI-based detection for DNS Query to Malicious Domain evil-malware-c2.com\", \"risk_score\": 75, \"severity\": \"high\", \"type\": \"query\", \"query\": \"event.type:dns and dns.question.name:evil-malware-c2.com\", \"language\": \"kuery\", \"index\": [\"logs-*\", \"filebeat-*\", \"winlogbeat-*\"], \"tags\": [\"cti\", \"otx\", \"T1071\"], \"threat\": [{\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1071\", \"name\": \"command-and-control\", \"reference\": \"https://attack.mitre.org/tactics/T1071\"}, \"technique\": [{\"id\": \"T1071\", \"name\": \"Application Layer Protocol\", \"reference\": \"https://attack.mitre.org/techniques/T1071\"}]}], \"author\": [\"SIEMLess v2.0 CTI Engine\"], \"false_positives\": [\"Legitimate services may occasionally match these patterns\"], \"from\": \"now-15m\", \"interval\": \"5m\", \"meta\": {\"confidence\": 1.0, \"indicators\": [\"evil-malware-c2.com\"], \"source\": \"OTX CTI Feed\"}, \"enabled\": true}"


# Import rule: Suspicious C2 Communication to **************
curl -X POST "$ELASTIC_URL/_security/rule" \
  -u "$ELASTIC_USER:$ELASTIC_PASS" \
  -H "Content-Type: application/json" \
  -d "{\"rule_id\": \"CTI_command_and_control_7e3f4376\", \"name\": \"Suspicious C2 Communication to **************\", \"description\": \"CTI-based detection for Suspicious C2 Communication to **************\", \"risk_score\": 100, \"severity\": \"critical\", \"type\": \"query\", \"query\": \"(source.ip:10.0.0.0/8 or source.ip:**********/12 or source.ip:***********/16) and destination.ip:************** and (network.bytes >= 1000 or event.count >= 10)\", \"language\": \"kuery\", \"index\": [\"logs-*\", \"filebeat-*\", \"winlogbeat-*\"], \"tags\": [\"cti\", \"otx\", \"T1071\", \"T1105\"], \"threat\": [{\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1071\", \"name\": \"command-and-control\", \"reference\": \"https://attack.mitre.org/tactics/T1071\"}, \"technique\": [{\"id\": \"T1071\", \"name\": \"Application Layer Protocol\", \"reference\": \"https://attack.mitre.org/techniques/T1071\"}]}, {\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1105\", \"name\": \"command-and-control\", \"reference\": \"https://attack.mitre.org/tactics/T1105\"}, \"technique\": [{\"id\": \"T1105\", \"name\": \"Ingress Tool Transfer\", \"reference\": \"https://attack.mitre.org/techniques/T1105\"}]}], \"author\": [\"SIEMLess v2.0 CTI Engine\"], \"false_positives\": [\"Legitimate services may occasionally match these patterns\"], \"from\": \"now-15m\", \"interval\": \"5m\", \"meta\": {\"confidence\": 1.0, \"indicators\": [\"**************\"], \"source\": \"OTX CTI Feed\"}, \"enabled\": true}"


# Import rule: DNS Query to Malicious Domain cryptominer.poolserver.io
curl -X POST "$ELASTIC_URL/_security/rule" \
  -u "$ELASTIC_USER:$ELASTIC_PASS" \
  -H "Content-Type: application/json" \
  -d "{\"rule_id\": \"CTI_cryptominer_27371430\", \"name\": \"DNS Query to Malicious Domain cryptominer.poolserver.io\", \"description\": \"CTI-based detection for DNS Query to Malicious Domain cryptominer.poolserver.io\", \"risk_score\": 45, \"severity\": \"medium\", \"type\": \"query\", \"query\": \"event.type:dns and dns.question.name:cryptominer.poolserver.io\", \"language\": \"kuery\", \"index\": [\"logs-*\", \"filebeat-*\", \"winlogbeat-*\"], \"tags\": [\"cti\", \"otx\", \"T1071\"], \"threat\": [{\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1071\", \"name\": \"command-and-control\", \"reference\": \"https://attack.mitre.org/tactics/T1071\"}, \"technique\": [{\"id\": \"T1071\", \"name\": \"Application Layer Protocol\", \"reference\": \"https://attack.mitre.org/techniques/T1071\"}]}], \"author\": [\"SIEMLess v2.0 CTI Engine\"], \"false_positives\": [\"Legitimate services may occasionally match these patterns\"], \"from\": \"now-15m\", \"interval\": \"5m\", \"meta\": {\"confidence\": 0.9, \"indicators\": [\"cryptominer.poolserver.io\"], \"source\": \"OTX CTI Feed\"}, \"enabled\": true}"


# Import rule: Suspicious Registry Modification HKLM\Software\Microsoft\Window
curl -X POST "$ELASTIC_URL/_security/rule" \
  -u "$ELASTIC_USER:$ELASTIC_PASS" \
  -H "Content-Type: application/json" \
  -d "{\"rule_id\": \"CTI_malware_313855ea\", \"name\": \"Suspicious Registry Modification HKLM\\Software\\Microsoft\\Window\", \"description\": \"CTI-based detection for Suspicious Registry Modification HKLM\\Software\\Microsoft\\Window\", \"risk_score\": 67, \"severity\": \"high\", \"type\": \"query\", \"query\": \"event.type:registry_modification and registry.path:HKLM\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\\EvilMalware\", \"language\": \"kuery\", \"index\": [\"logs-*\", \"filebeat-*\", \"winlogbeat-*\"], \"tags\": [\"cti\", \"otx\", \"T1547\"], \"threat\": [{\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1547\", \"name\": \"persistence\", \"reference\": \"https://attack.mitre.org/tactics/T1547\"}, \"technique\": [{\"id\": \"T1547\", \"name\": \"Boot or Logon Autostart Execution\", \"reference\": \"https://attack.mitre.org/techniques/T1547\"}]}], \"author\": [\"SIEMLess v2.0 CTI Engine\"], \"false_positives\": [\"Legitimate services may occasionally match these patterns\"], \"from\": \"now-15m\", \"interval\": \"5m\", \"meta\": {\"confidence\": 0.9, \"indicators\": [\"HKLM\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\\EvilMalware\"], \"source\": \"OTX CTI Feed\"}, \"enabled\": true}"


# Import rule: Execution of Malicious File 5d41402abc4b2a76b9719d911017c5
curl -X POST "$ELASTIC_URL/_security/rule" \
  -u "$ELASTIC_USER:$ELASTIC_PASS" \
  -H "Content-Type: application/json" \
  -d "{\"rule_id\": \"CTI_malware_61fb2b45\", \"name\": \"Execution of Malicious File 5d41402abc4b2a76b9719d911017c5\", \"description\": \"CTI-based detection for Execution of Malicious File 5d41402abc4b2a76b9719d911017c5\", \"risk_score\": 75, \"severity\": \"high\", \"type\": \"query\", \"query\": \"event.type:process_creation and file.hash.*:5d41402abc4b2a76b9719d911017c592\", \"language\": \"kuery\", \"index\": [\"logs-*\", \"filebeat-*\", \"winlogbeat-*\"], \"tags\": [\"cti\", \"otx\", \"T1059\", \"T1105\"], \"threat\": [{\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1059\", \"name\": \"execution\", \"reference\": \"https://attack.mitre.org/tactics/T1059\"}, \"technique\": [{\"id\": \"T1059\", \"name\": \"Command and Scripting Interpreter\", \"reference\": \"https://attack.mitre.org/techniques/T1059\"}]}, {\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1105\", \"name\": \"command-and-control\", \"reference\": \"https://attack.mitre.org/tactics/T1105\"}, \"technique\": [{\"id\": \"T1105\", \"name\": \"Ingress Tool Transfer\", \"reference\": \"https://attack.mitre.org/techniques/T1105\"}]}], \"author\": [\"SIEMLess v2.0 CTI Engine\"], \"false_positives\": [\"Legitimate services may occasionally match these patterns\"], \"from\": \"now-15m\", \"interval\": \"5m\", \"meta\": {\"confidence\": 1.0, \"indicators\": [\"5d41402abc4b2a76b9719d911017c592\"], \"source\": \"OTX CTI Feed\"}, \"enabled\": true}"


# Import rule: Access to Phishing URL http://malicious-payload.ru/do
curl -X POST "$ELASTIC_URL/_security/rule" \
  -u "$ELASTIC_USER:$ELASTIC_PASS" \
  -H "Content-Type: application/json" \
  -d "{\"rule_id\": \"CTI_malware_da523909\", \"name\": \"Access to Phishing URL http://malicious-payload.ru/do\", \"description\": \"CTI-based detection for Access to Phishing URL http://malicious-payload.ru/do\", \"risk_score\": 67, \"severity\": \"high\", \"type\": \"query\", \"query\": \"event.type:web_request and url.full:http://malicious-payload.ru/download/evil.exe and method:GET\", \"language\": \"kuery\", \"index\": [\"logs-*\", \"filebeat-*\", \"winlogbeat-*\"], \"tags\": [\"cti\", \"otx\", \"T1566\", \"T1190\"], \"threat\": [{\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1566\", \"name\": \"initial-access\", \"reference\": \"https://attack.mitre.org/tactics/T1566\"}, \"technique\": [{\"id\": \"T1566\", \"name\": \"Phishing\", \"reference\": \"https://attack.mitre.org/techniques/T1566\"}]}, {\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1190\", \"name\": \"initial-access\", \"reference\": \"https://attack.mitre.org/tactics/T1190\"}, \"technique\": [{\"id\": \"T1190\", \"name\": \"Exploit Public-Facing Application\", \"reference\": \"https://attack.mitre.org/techniques/T1190\"}]}], \"author\": [\"SIEMLess v2.0 CTI Engine\"], \"false_positives\": [\"Legitimate services may occasionally match these patterns\"], \"from\": \"now-15m\", \"interval\": \"5m\", \"meta\": {\"confidence\": 0.9, \"indicators\": [\"http://malicious-payload.ru/download/evil.exe\"], \"source\": \"OTX CTI Feed\"}, \"enabled\": true}"


# Import rule: DNS Query to Malicious Domain phishing-scam.net
curl -X POST "$ELASTIC_URL/_security/rule" \
  -u "$ELASTIC_USER:$ELASTIC_PASS" \
  -H "Content-Type: application/json" \
  -d "{\"rule_id\": \"CTI_phishing_9d2c9aae\", \"name\": \"DNS Query to Malicious Domain phishing-scam.net\", \"description\": \"CTI-based detection for DNS Query to Malicious Domain phishing-scam.net\", \"risk_score\": 100, \"severity\": \"critical\", \"type\": \"query\", \"query\": \"event.type:dns and dns.question.name:phishing-scam.net\", \"language\": \"kuery\", \"index\": [\"logs-*\", \"filebeat-*\", \"winlogbeat-*\"], \"tags\": [\"cti\", \"otx\", \"T1071\"], \"threat\": [{\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1071\", \"name\": \"command-and-control\", \"reference\": \"https://attack.mitre.org/tactics/T1071\"}, \"technique\": [{\"id\": \"T1071\", \"name\": \"Application Layer Protocol\", \"reference\": \"https://attack.mitre.org/techniques/T1071\"}]}], \"author\": [\"SIEMLess v2.0 CTI Engine\"], \"false_positives\": [\"Legitimate services may occasionally match these patterns\"], \"from\": \"now-15m\", \"interval\": \"5m\", \"meta\": {\"confidence\": 1.0, \"indicators\": [\"phishing-scam.net\"], \"source\": \"OTX CTI Feed\"}, \"enabled\": true}"


# Import rule: Execution of Malicious File a87ff679a2f3e71d9181a67b754212
curl -X POST "$ELASTIC_URL/_security/rule" \
  -u "$ELASTIC_USER:$ELASTIC_PASS" \
  -H "Content-Type: application/json" \
  -d "{\"rule_id\": \"CTI_ransomware_25d86fcf\", \"name\": \"Execution of Malicious File a87ff679a2f3e71d9181a67b754212\", \"description\": \"CTI-based detection for Execution of Malicious File a87ff679a2f3e71d9181a67b754212\", \"risk_score\": 100, \"severity\": \"critical\", \"type\": \"query\", \"query\": \"event.type:process_creation and file.hash.*:a87ff679a2f3e71d9181a67b7542122c4e5d4c3a\", \"language\": \"kuery\", \"index\": [\"logs-*\", \"filebeat-*\", \"winlogbeat-*\"], \"tags\": [\"cti\", \"otx\", \"T1059\", \"T1105\"], \"threat\": [{\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1059\", \"name\": \"execution\", \"reference\": \"https://attack.mitre.org/tactics/T1059\"}, \"technique\": [{\"id\": \"T1059\", \"name\": \"Command and Scripting Interpreter\", \"reference\": \"https://attack.mitre.org/techniques/T1059\"}]}, {\"framework\": \"MITRE ATT&CK\", \"tactic\": {\"id\": \"T1105\", \"name\": \"command-and-control\", \"reference\": \"https://attack.mitre.org/tactics/T1105\"}, \"technique\": [{\"id\": \"T1105\", \"name\": \"Ingress Tool Transfer\", \"reference\": \"https://attack.mitre.org/techniques/T1105\"}]}], \"author\": [\"SIEMLess v2.0 CTI Engine\"], \"false_positives\": [\"Legitimate services may occasionally match these patterns\"], \"from\": \"now-15m\", \"interval\": \"5m\", \"meta\": {\"confidence\": 1.0, \"indicators\": [\"a87ff679a2f3e71d9181a67b7542122c4e5d4c3a\"], \"source\": \"OTX CTI Feed\"}, \"enabled\": true}"


echo "Import complete!"
