"""
Authentication middleware for SIEMLess Delivery Engine
Integrates with Keycloak for JWT validation and RBAC
"""

import os
import json
import jwt
import aiohttp
import hashlib
from functools import wraps
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from aiohttp import web
import redis.asyncio as redis_async
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend
import logging

logger = logging.getLogger(__name__)


class KeycloakAuthMiddleware:
    """
    Middleware for validating Keycloak JWT tokens and managing sessions
    """

    def __init__(
        self,
        keycloak_url: str = None,
        realm: str = "siemless",
        client_id: str = "siemless-api",
        redis_client: redis_async.Redis = None
    ):
        self.keycloak_url = keycloak_url or os.getenv('KEYCLOAK_URL', 'http://keycloak:8080')
        self.realm = realm
        self.client_id = client_id
        self.redis_client = redis_client

        # Cache for Keycloak public key
        self.public_key = None
        self.public_key_expiry = None

        # URLs for Keycloak endpoints
        self.issuer = f"{self.keycloak_url}/realms/{realm}"
        self.jwks_uri = f"{self.issuer}/protocol/openid-connect/certs"
        self.userinfo_uri = f"{self.issuer}/protocol/openid-connect/userinfo"
        self.token_introspection_uri = f"{self.issuer}/protocol/openid-connect/token/introspect"

        # Simple API key fallback for development
        self.api_keys = self._load_api_keys()

    def _load_api_keys(self) -> Dict[str, Dict]:
        """Load development API keys from environment"""
        api_keys = {}

        # Development API keys (remove in production)
        if os.getenv('ENABLE_DEV_API_KEYS', 'false').lower() == 'true':
            api_keys = {
                hashlib.sha256(b"dev-admin-key").hexdigest(): {
                    "user": "dev-admin",
                    "roles": ["siemless-admin"],
                    "email": "<EMAIL>"
                },
                hashlib.sha256(b"dev-analyst-key").hexdigest(): {
                    "user": "dev-analyst",
                    "roles": ["siemless-analyst"],
                    "email": "<EMAIL>"
                },
                hashlib.sha256(b"dev-engineer-key").hexdigest(): {
                    "user": "dev-engineer",
                    "roles": ["siemless-engineer"],
                    "email": "<EMAIL>"
                },
                hashlib.sha256(b"dev-viewer-key").hexdigest(): {
                    "user": "dev-viewer",
                    "roles": ["siemless-viewer"],
                    "email": "<EMAIL>"
                }
            }

        return api_keys

    async def get_public_key(self) -> str:
        """Fetch and cache Keycloak public key"""
        # Check if cached key is still valid
        if self.public_key and self.public_key_expiry and datetime.utcnow() < self.public_key_expiry:
            return self.public_key

        try:
            async with aiohttp.ClientSession() as session:
                # Get realm info
                async with session.get(f"{self.issuer}") as resp:
                    if resp.status == 200:
                        realm_info = await resp.json()
                        self.public_key = f"-----BEGIN PUBLIC KEY-----\n{realm_info['public_key']}\n-----END PUBLIC KEY-----"
                        self.public_key_expiry = datetime.utcnow() + timedelta(hours=1)
                        return self.public_key
        except Exception as e:
            logger.error(f"Failed to fetch Keycloak public key: {e}")

        return None

    async def validate_token(self, token: str) -> Optional[Dict]:
        """Validate JWT token from Keycloak"""
        try:
            # Get public key
            public_key = await self.get_public_key()
            if not public_key:
                logger.warning("Could not fetch Keycloak public key")
                return None

            # Decode and validate JWT
            decoded = jwt.decode(
                token,
                public_key,
                algorithms=["RS256"],
                audience=self.client_id,
                issuer=self.issuer
            )

            # Check token expiry
            if decoded.get('exp', 0) < datetime.utcnow().timestamp():
                logger.warning("Token expired")
                return None

            # Extract user info
            user_info = {
                'user': decoded.get('preferred_username', decoded.get('sub')),
                'email': decoded.get('email'),
                'roles': decoded.get('realm_access', {}).get('roles', []),
                'groups': decoded.get('groups', []),
                'name': decoded.get('name', ''),
                'sub': decoded.get('sub')
            }

            # Cache session in Redis
            if self.redis_client:
                session_key = f"session:{token[:50]}"  # Use first 50 chars as key
                await self.redis_client.setex(
                    session_key,
                    3600,  # 1 hour TTL
                    json.dumps(user_info)
                )

            return user_info

        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid token: {e}")
            return None
        except Exception as e:
            logger.error(f"Token validation error: {e}")
            return None

    async def validate_api_key(self, api_key: str) -> Optional[Dict]:
        """Validate API key (development fallback)"""
        if not api_key:
            return None

        # Hash the provided key
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()

        # Check if it matches any known keys
        if key_hash in self.api_keys:
            user_info = self.api_keys[key_hash].copy()

            # Cache session in Redis
            if self.redis_client:
                session_id = hashlib.sha256(f"{api_key}{datetime.utcnow()}".encode()).hexdigest()
                session_key = f"session:{session_id}"
                await self.redis_client.setex(
                    session_key,
                    3600,  # 1 hour TTL
                    json.dumps(user_info)
                )
                user_info['session_id'] = session_id

            return user_info

        return None

    @web.middleware
    async def auth_middleware(self, request: web.Request, handler: Callable) -> web.Response:
        """Middleware for protecting routes"""
        # Skip auth for certain paths
        skip_paths = ['/health', '/api/auth/login', '/api/auth/token', '/metrics']
        if any(request.path.startswith(path) for path in skip_paths):
            return await handler(request)

        # Extract token from Authorization header
        auth_header = request.headers.get('Authorization', '')

        user_info = None

        # Try Bearer token (Keycloak JWT)
        if auth_header.startswith('Bearer '):
            token = auth_header[7:]
            user_info = await self.validate_token(token)

        # Try API key (development)
        elif 'X-API-Key' in request.headers:
            api_key = request.headers['X-API-Key']
            user_info = await self.validate_api_key(api_key)

        # Try session token from Redis
        elif 'X-Session-Token' in request.headers:
            session_token = request.headers['X-Session-Token']
            if self.redis_client:
                session_key = f"session:{session_token}"
                session_data = await self.redis_client.get(session_key)
                if session_data:
                    user_info = json.loads(session_data)

        # Check if authentication succeeded
        if not user_info:
            return web.json_response(
                {'error': 'Unauthorized', 'message': 'Invalid or missing authentication'},
                status=401
            )

        # Add user info to request
        request['user'] = user_info

        # Process request
        return await handler(request)


class RBACDecorator:
    """
    Role-based access control decorator for route handlers
    """

    @staticmethod
    def require_role(*required_roles: str):
        """
        Decorator to require specific roles for a route

        Usage:
            @RBACDecorator.require_role('siemless-admin', 'siemless-engineer')
            async def handle_admin_route(request):
                # Only admins and engineers can access this
                pass
        """
        def decorator(handler: Callable) -> Callable:
            @wraps(handler)
            async def wrapped(request: web.Request) -> web.Response:
                user_info = request.get('user')
                if not user_info:
                    return web.json_response(
                        {'error': 'Unauthorized', 'message': 'Authentication required'},
                        status=401
                    )

                user_roles = set(user_info.get('roles', []))
                required_roles_set = set(required_roles)

                # Check if user has any of the required roles
                if not user_roles.intersection(required_roles_set):
                    logger.warning(
                        f"Access denied for user {user_info.get('user')} "
                        f"with roles {user_roles}. Required: {required_roles_set}"
                    )
                    return web.json_response(
                        {'error': 'Forbidden', 'message': 'Insufficient permissions'},
                        status=403
                    )

                # User has required role, proceed
                return await handler(request)

            return wrapped
        return decorator

    @staticmethod
    def require_any_role():
        """Decorator to require any authenticated user (any role)"""
        def decorator(handler: Callable) -> Callable:
            @wraps(handler)
            async def wrapped(request: web.Request) -> web.Response:
                if 'user' not in request:
                    return web.json_response(
                        {'error': 'Unauthorized', 'message': 'Authentication required'},
                        status=401
                    )
                return await handler(request)
            return wrapped
        return decorator


class AuthEndpoints:
    """
    Authentication-related endpoints
    """

    def __init__(self, auth_middleware: KeycloakAuthMiddleware):
        self.auth = auth_middleware

    async def login(self, request: web.Request) -> web.Response:
        """
        Login endpoint for API key authentication (development)
        """
        try:
            data = await request.json()
            api_key = data.get('api_key')

            if not api_key:
                return web.json_response(
                    {'error': 'Bad Request', 'message': 'API key required'},
                    status=400
                )

            user_info = await self.auth.validate_api_key(api_key)

            if not user_info:
                return web.json_response(
                    {'error': 'Unauthorized', 'message': 'Invalid API key'},
                    status=401
                )

            # Return session info
            return web.json_response({
                'status': 'success',
                'user': user_info['user'],
                'email': user_info.get('email'),
                'roles': user_info.get('roles', []),
                'session_id': user_info.get('session_id'),
                'token_type': 'api_key'
            })

        except Exception as e:
            logger.error(f"Login error: {e}")
            return web.json_response(
                {'error': 'Internal Server Error'},
                status=500
            )

    async def logout(self, request: web.Request) -> web.Response:
        """
        Logout endpoint to invalidate session
        """
        try:
            # Get session token
            session_token = request.headers.get('X-Session-Token')
            auth_header = request.headers.get('Authorization', '')

            if session_token and self.auth.redis_client:
                # Delete session from Redis
                session_key = f"session:{session_token}"
                await self.auth.redis_client.delete(session_key)

            elif auth_header.startswith('Bearer ') and self.auth.redis_client:
                # Delete JWT session
                token = auth_header[7:][:50]
                session_key = f"session:{token}"
                await self.auth.redis_client.delete(session_key)

            return web.json_response({
                'status': 'success',
                'message': 'Logged out successfully'
            })

        except Exception as e:
            logger.error(f"Logout error: {e}")
            return web.json_response(
                {'error': 'Internal Server Error'},
                status=500
            )

    async def user_info(self, request: web.Request) -> web.Response:
        """
        Get current user information
        """
        user_info = request.get('user')
        if not user_info:
            return web.json_response(
                {'error': 'Unauthorized'},
                status=401
            )

        return web.json_response({
            'user': user_info.get('user'),
            'email': user_info.get('email'),
            'roles': user_info.get('roles', []),
            'groups': user_info.get('groups', []),
            'name': user_info.get('name', '')
        })


# Example usage in Delivery Engine
def setup_authentication(app: web.Application, redis_client: redis_async.Redis = None):
    """
    Setup authentication middleware and routes

    Usage in delivery_engine.py:
        from auth_middleware import setup_authentication
        setup_authentication(self.app, self.redis_client)
    """
    # Create auth middleware
    auth_middleware = KeycloakAuthMiddleware(redis_client=redis_client)

    # Add middleware to app
    app.middlewares.append(auth_middleware.auth_middleware)

    # Create auth endpoints
    auth_endpoints = AuthEndpoints(auth_middleware)

    # Add auth routes
    app.router.add_post('/api/auth/login', auth_endpoints.login)
    app.router.add_post('/api/auth/logout', auth_endpoints.logout)
    app.router.add_get('/api/auth/user', auth_endpoints.user_info)

    # Store auth middleware in app for use by routes
    app['auth'] = auth_middleware
    app['rbac'] = RBACDecorator

    return auth_middleware