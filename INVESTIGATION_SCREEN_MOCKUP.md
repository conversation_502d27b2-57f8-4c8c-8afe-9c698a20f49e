# Investigation Screen - Contextual Intelligence Example

## Alert: "DACTA Horizontal Port Scan Detected"

### Current Problem: Generic Investigation Guide Says:
> "Correlate with Threat Intelligence - If source.ip is external, perform WHOIS lookups, threat reputation checks..."

**Analyst Question:** "Okay, but WHAT is the result? Do I need to do this manually?"

---

## Solution: Pre-Executed Context Cards

### 📍 IP Context Card: `*************`

```
┌─────────────────────────────────────────────────────────────┐
│ 🌐 IP Address: *************                                │
├─────────────────────────────────────────────────────────────┤
│ Type:           Internal (RFC1918 - Private Network)        │
│ Location:       Your Network (Datacenter-East)              │
│ Owner:          IT-Operations (Asset DB)                    │
│ Device Type:    Linux Server                                │
│ Hostname:       prod-scanner-01.internal                    │
│ First Seen:     2024-01-15 (280 days ago)                   │
│ Last Activity:  2025-10-02 10:57:22 (2 minutes ago)         │
│                                                              │
│ ✅ CONTEXT: This is an AUTHORIZED vulnerability scanner     │
│    used by IT Operations for weekly security scans          │
└─────────────────────────────────────────────────────────────┘
```

---

### 🔍 Threat Intelligence Card

```
┌─────────────────────────────────────────────────────────────┐
│ 🛡️ Threat Intelligence Check: *************                 │
├─────────────────────────────────────────────────────────────┤
│ VirusTotal:     ✅ Not found (Clean)                        │
│ AbuseIPDB:      ✅ 0 reports (Confidence: 0%)               │
│ GreyNoise:      ✅ Not observed in internet scans           │
│ AlienVault OTX: ✅ No pulses                                │
│ SIEMLess CTI:   ✅ No matches in IOC database               │
│                                                              │
│ Risk Score:     0/100 (Clean)                               │
│ Verdict:        ✅ BENIGN - No malicious indicators         │
└─────────────────────────────────────────────────────────────┘
```

---

### 📊 Historical Behavior Card

```
┌─────────────────────────────────────────────────────────────┐
│ 📈 Historical Activity: *************                        │
├─────────────────────────────────────────────────────────────┤
│ Previous Port Scans:  47 in last 30 days                    │
│ Pattern:              Every Tuesday 02:00 AM                 │
│ Schedule:             Matches change control window          │
│                                                              │
│ Previous Alerts:      12 similar alerts                      │
│ All Closed As:        False Positive (Authorized Scan)       │
│ Last Investigation:   2025-09-24 by <EMAIL>    │
│ Resolution Note:      "Weekly vuln scan - added to whitelist"│
│                                                              │
│ 📌 PATTERN MATCH: This is recurring authorized behavior     │
└─────────────────────────────────────────────────────────────┘
```

---

### 🔗 Related Entities Card

```
┌─────────────────────────────────────────────────────────────┐
│ 🔗 Connected Entities                                        │
├─────────────────────────────────────────────────────────────┤
│ Authenticated User:    service-account-scanner               │
│ Service Account Type:  Automated Tool                        │
│ Managed By:           IT-Operations Team                    │
│                                                              │
│ Scanned Hosts (last run):                                   │
│ • ************/24    (47 hosts)  ✅ Same subnet             │
│ • ************/24    (52 hosts)  ✅ Adjacent subnet         │
│ • ********/24        (31 hosts)  ✅ DMZ network             │
│                                                              │
│ Ports Scanned:                                               │
│ • 22 (SSH), 80 (HTTP), 443 (HTTPS), 3389 (RDP)              │
│   ✅ Standard vulnerability assessment ports                │
└─────────────────────────────────────────────────────────────┘
```

---

### ⏱️ Timeline Card

```
┌─────────────────────────────────────────────────────────────┐
│ ⏱️ Alert Timeline                                            │
├─────────────────────────────────────────────────────────────┤
│ 10:55:00  Scan initialized from prod-scanner-01             │
│ 10:56:30  Port 22 probed on ************/24                │
│ 10:57:15  Port 443 probed on ************/24               │
│ 10:57:22  🚨 ALERT TRIGGERED (threshold: 50 hosts)          │
│ 10:58:45  Scan completed (147 hosts, 588 connections)       │
│ 11:00:00  Scan results uploaded to vuln management system   │
│                                                              │
│ 📌 Normal scan duration: ~5 minutes (within expected range) │
└─────────────────────────────────────────────────────────────┘
```

---

## 🤖 AI-Generated Investigation Summary

```
┌─────────────────────────────────────────────────────────────┐
│ 🎯 Automated Analysis                                        │
├─────────────────────────────────────────────────────────────┤
│                                                              │
│ VERDICT: ✅ FALSE POSITIVE - Authorized Activity            │
│                                                              │
│ CONFIDENCE: 98% (High)                                       │
│                                                              │
│ REASONING:                                                   │
│ 1. Source IP is known authorized vulnerability scanner      │
│ 2. Activity matches scheduled maintenance window (Tues 2AM) │
│ 3. No malicious indicators in threat intelligence           │
│ 4. Historical pattern shows 47 identical scans (all benign) │
│ 5. Service account used is properly authorized              │
│ 6. Scanned ports match standard vuln assessment profile     │
│                                                              │
│ RECOMMENDED ACTION:                                          │
│ ✅ Close as False Positive                                  │
│ 🔧 Add to tuning: Suppress alerts from prod-scanner-01      │
│    during maintenance windows (Tuesdays 01:00-03:00)        │
│                                                              │
│ SIEM LINK-BACK:                                              │
│ 🔗 View raw logs in Elastic (last 1 hour)                   │
│    Query: source.ip:"*************" AND @timestamp:[...]    │
│                                                              │
└─────────────────────────────────────────────────────────────┘
```

---

## Key Difference: Generic vs. Contextualized

### ❌ Generic Investigation Guide Says:
> "Correlate with Threat Intelligence"

**Problem:** Analyst has to manually:
1. Copy IP address
2. Open VirusTotal
3. Open AbuseIPDB
4. Open GreyNoise
5. Check each one
6. Interpret results
7. Come to conclusion

**Time:** 5-10 minutes per alert

---

### ✅ Contextualized Investigation Shows:
> **Threat Intel Card shows:**
> - ✅ VirusTotal: Clean
> - ✅ AbuseIPDB: 0 reports
> - ✅ GreyNoise: Not seen
> - **Verdict: BENIGN**

**Benefit:** Analyst sees result immediately, with confidence score

**Time:** 5 seconds to read

---

## Implementation Architecture

```
Alert Triggered
      ↓
Delivery Engine receives alert
      ↓
PARALLEL ENRICHMENT (all at once):
      ├─→ IP Context Lookup (Asset DB)
      ├─→ Threat Intel Check (VirusTotal, AbuseIPDB, etc.)
      ├─→ Historical Behavior Query (Elastic/PostgreSQL)
      ├─→ Related Entities Graph (PostgreSQL relationships table)
      └─→ Timeline Construction (Elastic logs)
      ↓
AI Analysis (Intelligence Engine)
      ├─→ Pattern matching against known benign/malicious patterns
      ├─→ Risk scoring based on all context
      └─→ Recommendation generation
      ↓
Investigation Package Created
      ↓
Frontend displays all context cards + AI verdict
```

---

## The "Correlate with Threat Intelligence" Answer

**Investigation Guide says:** "Correlate with threat intelligence"

**SIEMLess automatically provides:**

```json
{
  "threat_intelligence": {
    "checked_at": "2025-10-02T11:00:00Z",
    "sources": [
      {
        "name": "VirusTotal",
        "result": "clean",
        "details": "0/89 vendors flagged this IP"
      },
      {
        "name": "AbuseIPDB",
        "result": "clean",
        "confidence": "0%",
        "reports": 0
      },
      {
        "name": "AlienVault OTX",
        "result": "clean",
        "pulses": 0
      }
    ],
    "verdict": "BENIGN",
    "risk_score": 0
  }
}
```

**Analyst sees:** ✅ "Threat Intel: CLEAN (0/100 risk)"

**They don't have to:** Manually check 3+ websites

---

This is the difference between "telling someone what to do" vs "doing it for them and showing the result."
