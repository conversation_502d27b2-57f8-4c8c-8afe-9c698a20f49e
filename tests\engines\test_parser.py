"""
Tests for Parser Engine - Deterministic Parsing
"""
import pytest
import sys
import os
import json
from pathlib import Path

# Add v2 to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from engines.parser.parser import ParserEngine


class TestParserEngine:
    """Test Parser Engine functionality"""

    @pytest.fixture
    def parser(self):
        """Create parser engine instance"""
        return ParserEngine()

    def test_cisco_asa_connection_built(self, parser):
        """Test parsing Cisco ASA connection built message"""
        # Sample Cisco ASA log
        log = "%ASA-6-302013: Built inbound TCP connection 123456 for outside:*************/54321 (*************/54321) to inside:*********/443 (*********/443)"

        # Load Cisco ASA patterns manually for testing
        with open('patterns/cisco_asa.json', 'r') as f:
            patterns = json.load(f)

        parser.pattern_cache['cisco_asa'] = []
        for pattern_id, pattern_data in patterns.items():
            pattern_data['pattern_id'] = pattern_id
            parser.pattern_cache['cisco_asa'].append(pattern_data)

        # Parse the log
        result = parser._parse_deterministic(log, 'cisco_asa')

        assert result is not None, "Should successfully parse Cisco ASA log"
        assert result['pattern_name'] == "Cisco ASA Connection Built"
        assert result['fields']['direction'] == 'inbound'
        assert result['fields']['protocol'] == 'TCP'
        assert result['fields']['session_id'] == 123456
        assert result['fields']['src_ip'] == '*************'
        assert result['fields']['dst_ip'] == '*********'
        assert result['fields']['dst_port'] == 443

    def test_cisco_asa_denied(self, parser):
        """Test parsing Cisco ASA denied traffic"""
        log = "%ASA-4-106023: Deny tcp src outside:*************/12345 dst inside:*********/22 by access-group \"OUTSIDE_IN\""

        # Load patterns
        with open('patterns/cisco_asa.json', 'r') as f:
            patterns = json.load(f)

        parser.pattern_cache['cisco_asa'] = []
        for pattern_id, pattern_data in patterns.items():
            pattern_data['pattern_id'] = pattern_id
            parser.pattern_cache['cisco_asa'].append(pattern_data)

        # Parse
        result = parser._parse_deterministic(log, 'cisco_asa')

        assert result is not None
        assert result['pattern_name'] == "Cisco ASA Denied Traffic"
        assert result['fields']['severity'] == 4
        assert result['fields']['protocol'] == 'tcp'
        assert result['fields']['src_ip'] == '*************'
        assert result['fields']['dst_port'] == 22
        assert result['fields']['acl_name'] == 'OUTSIDE_IN'
        assert 'threat_indicators' in result

    def test_cisco_asa_vpn(self, parser):
        """Test parsing Cisco ASA VPN events"""
        log = "%ASA-4-113019: Group = VPN_Users, Username = john.doe, IP = **************, Session disconnected"

        # Load patterns
        with open('patterns/cisco_asa.json', 'r') as f:
            patterns = json.load(f)

        parser.pattern_cache['cisco_asa'] = []
        for pattern_id, pattern_data in patterns.items():
            pattern_data['pattern_id'] = pattern_id
            parser.pattern_cache['cisco_asa'].append(pattern_data)

        # Parse
        result = parser._parse_deterministic(log, 'cisco_asa')

        assert result is not None
        assert result['pattern_name'] == "Cisco ASA VPN Events"
        assert result['fields']['username'] == 'john.doe'
        assert result['fields']['group'] == 'VPN_Users'
        assert result['fields']['ip'] == '**************'
        assert result['fields']['action'] == 'Session disconnected'
        assert 'entity_mappings' in result
        assert result['entity_mappings']['user'] == 'username'

    def test_log_type_identification(self, parser):
        """Test log type identification"""
        # Test Cisco ASA identification
        asa_log = "%ASA-6-302013: Built inbound TCP connection"
        assert parser._identify_log_type(asa_log, 'unknown') == 'cisco_asa'

        # Test source-based identification
        assert parser._identify_log_type("any log", 'cisco_asa') == 'cisco_asa'
        assert parser._identify_log_type("any log", 'palo_alto') == 'palo_alto'

        # Test pattern-based identification
        panos_log = "PANOS threat detected"
        assert parser._identify_log_type(panos_log, 'unknown') == 'palo_alto'

    def test_field_type_conversion(self, parser):
        """Test field type conversion"""
        # Test integer conversion
        assert parser._convert_field_type("123", "integer") == 123

        # Test IP validation
        assert parser._convert_field_type("***********", "ip") == "***********"
        assert parser._convert_field_type("999.999.999.999", "ip") is None

        # Test boolean conversion
        assert parser._convert_field_type("true", "boolean") is True
        assert parser._convert_field_type("false", "boolean") is False

        # Test default string
        assert parser._convert_field_type("text", "string") == "text"

    def test_pattern_statistics(self, parser):
        """Test pattern usage statistics"""
        # Initial stats
        assert parser.pattern_stats['hits'] == 0
        assert parser.pattern_stats['misses'] == 0

        # Load patterns
        with open('patterns/cisco_asa.json', 'r') as f:
            patterns = json.load(f)

        parser.pattern_cache['cisco_asa'] = []
        for pattern_id, pattern_data in patterns.items():
            pattern_data['pattern_id'] = pattern_id
            parser.pattern_cache['cisco_asa'].append(pattern_data)

        # Parse known pattern (should be hit)
        log = "%ASA-6-302014: Teardown TCP connection 123456 for outside:*************/12345 to inside:*********/80 duration 0:00:30 bytes 1234"
        result = parser._parse_deterministic(log, 'cisco_asa')

        assert result is not None
        assert result['pattern_name'] == "Cisco ASA Connection Teardown"

        # Parse unknown pattern (should be miss)
        unknown_log = "This is not a recognized log format"
        result = parser._parse_deterministic(unknown_log, 'unknown')

        assert result is None

    def test_threat_indicators(self, parser):
        """Test threat indicator extraction"""
        # Load patterns
        with open('patterns/cisco_asa.json', 'r') as f:
            patterns = json.load(f)

        parser.pattern_cache['cisco_asa'] = []
        for pattern_id, pattern_data in patterns.items():
            pattern_data['pattern_id'] = pattern_id
            parser.pattern_cache['cisco_asa'].append(pattern_data)

        # Parse threat detection log
        log = "%ASA-4-733100: [Scanning] Host ************* exceeded rate-limit threshold"
        result = parser._parse_deterministic(log, 'cisco_asa')

        assert result is not None
        assert result['pattern_name'] == "Cisco ASA Threat Detection"
        assert 'threat_indicators' in result
        assert result['threat_indicators']['threat_detected'] is True
        assert result['threat_indicators']['severity'] == 'high'


if __name__ == "__main__":
    pytest.main([__file__, "-v"])