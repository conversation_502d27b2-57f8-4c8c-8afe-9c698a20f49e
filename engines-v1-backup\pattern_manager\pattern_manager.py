"""
Pattern Manager Service - CRUD operations and versioning for patterns

This service manages:
- Pattern creation, update, deletion
- Version control and rollback
- A/B testing of patterns
- Performance tracking
- Sync between files and database
"""
import asyncio
import json
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import difflib
from dataclasses import dataclass, asdict
from enum import Enum

# Add v2 to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.base import BaseEngine


class PatternSource(Enum):
    """Pattern source types"""
    FILE = "file"
    DATABASE = "database"
    API = "api"
    AI_GENERATED = "ai_generated"
    MANUAL = "manual"


class PatternStatus(Enum):
    """Pattern lifecycle status"""
    DRAFT = "draft"
    TESTING = "testing"
    ACTIVE = "active"
    DEPRECATED = "deprecated"
    ARCHIVED = "archived"


@dataclass
class PatternVersion:
    """Represents a pattern version"""
    version: str
    timestamp: datetime
    source: PatternSource
    change_description: str
    author: str
    parent_version: Optional[str] = None
    performance_metrics: Optional[Dict] = None


@dataclass
class Pattern:
    """Complete pattern definition"""
    pattern_id: str
    name: str
    pattern_type: str  # parser, entity, relationship, detection, enrichment
    vendor: str
    product: str
    version: str
    status: PatternStatus
    content: Dict[str, Any]  # The actual pattern JSON
    metadata: Dict[str, Any]
    versions: List[PatternVersion]
    created_at: datetime
    updated_at: datetime
    performance_stats: Dict[str, Any]


class PatternManager(BaseEngine):
    """
    Pattern Manager Service: Central pattern authority

    Manages the lifecycle of patterns from creation to retirement,
    including version control, performance tracking, and synchronization.
    """

    def __init__(self):
        super().__init__('pattern_manager', '2.0.0')

        # Pattern storage (in production, this would be database-backed)
        self.patterns: Dict[str, Pattern] = {}

        # Pattern performance cache
        self.performance_cache = {}

        # A/B test configurations
        self.ab_tests = {}

        # File paths
        self.pattern_dirs = {
            'normalized': Path('v2/patterns/normalized'),
            'raw': Path('v2/patterns/raw'),
            'detection': Path('v2/patterns/detection'),
            'enrichment': Path('v2/patterns/enrichment')
        }

        # Pattern validation rules
        self.validation_rules = {
            'parser': ['entity_mappings', 'vendor', 'product'],
            'entity': ['entity_mappings', 'normalization_rules'],
            'relationship': ['relationship_mappings'],
            'detection': ['conditions', 'severity', 'mitre_techniques'],
            'enrichment': ['entity_enrichment']
        }

        # Statistics
        self.stats = {
            'total_patterns': 0,
            'active_patterns': 0,
            'patterns_by_type': {},
            'patterns_by_vendor': {},
            'total_versions': 0,
            'ab_tests_active': 0
        }

        # Initialize with existing patterns
        asyncio.create_task(self._load_patterns_from_files())

    async def _load_patterns_from_files(self):
        """Load existing patterns from file system"""
        for dir_type, dir_path in self.pattern_dirs.items():
            if not dir_path.exists():
                continue

            for pattern_file in dir_path.glob('*.json'):
                try:
                    with open(pattern_file, 'r') as f:
                        content = json.load(f)

                    # Create Pattern object
                    pattern = Pattern(
                        pattern_id=self._generate_pattern_id(pattern_file.stem),
                        name=pattern_file.stem,
                        pattern_type=dir_type if dir_type != 'normalized' else 'parser',
                        vendor=content.get('vendor', 'unknown'),
                        product=content.get('product', 'unknown'),
                        version=content.get('pattern_version', '1.0.0'),
                        status=PatternStatus.ACTIVE,
                        content=content,
                        metadata={
                            'file_path': str(pattern_file),
                            'source': PatternSource.FILE.value
                        },
                        versions=[
                            PatternVersion(
                                version='1.0.0',
                                timestamp=datetime.utcnow(),
                                source=PatternSource.FILE,
                                change_description='Initial load from file',
                                author='system'
                            )
                        ],
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                        performance_stats={}
                    )

                    self.patterns[pattern.pattern_id] = pattern
                    self.logger.log('pattern_loaded', {
                        'pattern_id': pattern.pattern_id,
                        'source': 'file'
                    })

                except Exception as e:
                    self.logger.log_error(e, {'file': str(pattern_file)})

        self._update_statistics()

    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Process pattern management requests"""
        command = message.get('command')
        data = message.get('data', {})

        try:
            if command == 'create':
                return await self._create_pattern(data)
            elif command == 'update':
                return await self._update_pattern(data)
            elif command == 'delete':
                return await self._delete_pattern(data)
            elif command == 'get':
                return await self._get_pattern(data)
            elif command == 'list':
                return await self._list_patterns(data)
            elif command == 'validate':
                return await self._validate_pattern(data)
            elif command == 'test':
                return await self._test_pattern(data)
            elif command == 'rollback':
                return await self._rollback_pattern(data)
            elif command == 'sync':
                return await self._sync_patterns(data)
            elif command == 'performance':
                return await self._get_performance_stats(data)
            elif command == 'ab_test':
                return await self._manage_ab_test(data)
            else:
                return {'success': False, 'error': f'Unknown command: {command}'}

        except Exception as e:
            self.logger.log_error(e, {'command': command, 'data': data})
            return {'success': False, 'error': str(e)}

    async def _create_pattern(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new pattern"""
        pattern_type = data.get('pattern_type')
        content = data.get('content')
        metadata = data.get('metadata', {})

        # Validate required fields
        if not self._validate_pattern_content(pattern_type, content):
            return {'success': False, 'error': 'Invalid pattern content'}

        # Generate pattern ID
        pattern_id = self._generate_pattern_id(
            f"{content.get('vendor', 'unknown')}_{content.get('product', 'unknown')}_{pattern_type}"
        )

        # Check if pattern already exists
        if pattern_id in self.patterns:
            return {'success': False, 'error': 'Pattern already exists'}

        # Create pattern
        pattern = Pattern(
            pattern_id=pattern_id,
            name=data.get('name', pattern_id),
            pattern_type=pattern_type,
            vendor=content.get('vendor', 'unknown'),
            product=content.get('product', 'unknown'),
            version='1.0.0',
            status=PatternStatus.DRAFT,
            content=content,
            metadata=metadata,
            versions=[
                PatternVersion(
                    version='1.0.0',
                    timestamp=datetime.utcnow(),
                    source=PatternSource(metadata.get('source', 'api')),
                    change_description='Initial creation',
                    author=metadata.get('author', 'unknown')
                )
            ],
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            performance_stats={}
        )

        self.patterns[pattern_id] = pattern

        # Save to file if specified
        if metadata.get('save_to_file'):
            await self._save_pattern_to_file(pattern)

        self._update_statistics()

        self.logger.log_decision(
            'pattern_created',
            data,
            {'pattern_id': pattern_id},
            reasoning='New pattern created successfully',
            confidence=1.0
        )

        return {
            'success': True,
            'pattern_id': pattern_id,
            'version': '1.0.0'
        }

    async def _update_pattern(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing pattern"""
        pattern_id = data.get('pattern_id')
        updates = data.get('updates', {})
        metadata = data.get('metadata', {})

        if pattern_id not in self.patterns:
            return {'success': False, 'error': 'Pattern not found'}

        pattern = self.patterns[pattern_id]

        # Create new version
        new_version = self._increment_version(pattern.version)

        # Apply updates
        updated_content = {**pattern.content, **updates}

        # Validate updated content
        if not self._validate_pattern_content(pattern.pattern_type, updated_content):
            return {'success': False, 'error': 'Invalid pattern update'}

        # Create version entry
        version_entry = PatternVersion(
            version=new_version,
            timestamp=datetime.utcnow(),
            source=PatternSource(metadata.get('source', 'api')),
            change_description=metadata.get('change_description', 'Pattern updated'),
            author=metadata.get('author', 'unknown'),
            parent_version=pattern.version
        )

        # Update pattern
        pattern.content = updated_content
        pattern.version = new_version
        pattern.versions.append(version_entry)
        pattern.updated_at = datetime.utcnow()

        # Update status if specified
        if 'status' in metadata:
            pattern.status = PatternStatus(metadata['status'])

        # Save to file if specified
        if metadata.get('save_to_file'):
            await self._save_pattern_to_file(pattern)

        # Invalidate performance cache
        self.performance_cache.pop(pattern_id, None)

        self.logger.log_decision(
            'pattern_updated',
            {'pattern_id': pattern_id, 'updates': updates},
            {'new_version': new_version},
            reasoning=f"Pattern updated from {pattern.versions[-2].version} to {new_version}",
            confidence=1.0
        )

        return {
            'success': True,
            'pattern_id': pattern_id,
            'new_version': new_version,
            'previous_version': pattern.versions[-2].version if len(pattern.versions) > 1 else None
        }

    async def _delete_pattern(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Delete or archive a pattern"""
        pattern_id = data.get('pattern_id')
        archive = data.get('archive', True)  # Archive by default instead of hard delete

        if pattern_id not in self.patterns:
            return {'success': False, 'error': 'Pattern not found'}

        pattern = self.patterns[pattern_id]

        if archive:
            # Archive pattern
            pattern.status = PatternStatus.ARCHIVED
            pattern.updated_at = datetime.utcnow()

            self.logger.log('pattern_archived', {'pattern_id': pattern_id})

            return {
                'success': True,
                'message': f'Pattern {pattern_id} archived'
            }
        else:
            # Hard delete
            del self.patterns[pattern_id]
            self.performance_cache.pop(pattern_id, None)

            self.logger.log('pattern_deleted', {'pattern_id': pattern_id})

            return {
                'success': True,
                'message': f'Pattern {pattern_id} deleted'
            }

    async def _get_pattern(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get a specific pattern"""
        pattern_id = data.get('pattern_id')
        version = data.get('version')  # Optional specific version

        if pattern_id not in self.patterns:
            return {'success': False, 'error': 'Pattern not found'}

        pattern = self.patterns[pattern_id]

        # If specific version requested, return that version's content
        if version:
            version_content = self._get_pattern_version(pattern, version)
            if not version_content:
                return {'success': False, 'error': f'Version {version} not found'}

            return {
                'success': True,
                'pattern': {
                    'pattern_id': pattern.pattern_id,
                    'name': pattern.name,
                    'version': version,
                    'content': version_content
                }
            }

        # Return current version
        return {
            'success': True,
            'pattern': {
                'pattern_id': pattern.pattern_id,
                'name': pattern.name,
                'pattern_type': pattern.pattern_type,
                'vendor': pattern.vendor,
                'product': pattern.product,
                'version': pattern.version,
                'status': pattern.status.value,
                'content': pattern.content,
                'metadata': pattern.metadata,
                'versions': [
                    {
                        'version': v.version,
                        'timestamp': v.timestamp.isoformat(),
                        'author': v.author,
                        'change_description': v.change_description
                    }
                    for v in pattern.versions[-5:]  # Last 5 versions
                ],
                'performance_stats': pattern.performance_stats
            }
        }

    async def _list_patterns(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """List patterns with filtering"""
        filters = {
            'pattern_type': data.get('pattern_type'),
            'vendor': data.get('vendor'),
            'status': data.get('status'),
            'search': data.get('search')
        }

        patterns_list = []

        for pattern in self.patterns.values():
            # Apply filters
            if filters['pattern_type'] and pattern.pattern_type != filters['pattern_type']:
                continue
            if filters['vendor'] and pattern.vendor != filters['vendor']:
                continue
            if filters['status'] and pattern.status.value != filters['status']:
                continue
            if filters['search'] and filters['search'].lower() not in pattern.name.lower():
                continue

            patterns_list.append({
                'pattern_id': pattern.pattern_id,
                'name': pattern.name,
                'pattern_type': pattern.pattern_type,
                'vendor': pattern.vendor,
                'product': pattern.product,
                'version': pattern.version,
                'status': pattern.status.value,
                'updated_at': pattern.updated_at.isoformat()
            })

        return {
            'success': True,
            'patterns': patterns_list,
            'total': len(patterns_list)
        }

    async def _validate_pattern(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a pattern against rules"""
        pattern_type = data.get('pattern_type')
        content = data.get('content')

        is_valid = self._validate_pattern_content(pattern_type, content)

        validation_errors = []
        if not is_valid:
            required_fields = self.validation_rules.get(pattern_type, [])
            for field in required_fields:
                if field not in content:
                    validation_errors.append(f"Missing required field: {field}")

        return {
            'success': True,
            'valid': is_valid,
            'errors': validation_errors
        }

    async def _test_pattern(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Test a pattern against sample logs"""
        pattern_id = data.get('pattern_id')
        test_logs = data.get('test_logs', [])

        if pattern_id not in self.patterns:
            return {'success': False, 'error': 'Pattern not found'}

        pattern = self.patterns[pattern_id]
        test_results = []

        for log in test_logs:
            # Simplified pattern testing
            result = {
                'log_id': log.get('id', 'unknown'),
                'matched': False,
                'extracted_entities': [],
                'performance_ms': 0
            }

            # Test entity mappings
            if 'entity_mappings' in pattern.content:
                for entity_type, mapping in pattern.content['entity_mappings'].items():
                    # Simplified extraction logic
                    if isinstance(mapping, dict) and 'primary' in mapping:
                        field_path = mapping['primary']
                        value = self._extract_field(log, field_path)
                        if value:
                            result['extracted_entities'].append({
                                'type': entity_type,
                                'value': value
                            })
                            result['matched'] = True

            test_results.append(result)

        # Update performance stats
        success_rate = sum(1 for r in test_results if r['matched']) / len(test_results) if test_results else 0

        pattern.performance_stats.update({
            'last_tested': datetime.utcnow().isoformat(),
            'test_success_rate': success_rate,
            'test_sample_size': len(test_logs)
        })

        return {
            'success': True,
            'pattern_id': pattern_id,
            'test_results': test_results,
            'summary': {
                'total_logs': len(test_logs),
                'matched': sum(1 for r in test_results if r['matched']),
                'success_rate': success_rate
            }
        }

    async def _rollback_pattern(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Rollback pattern to previous version"""
        pattern_id = data.get('pattern_id')
        target_version = data.get('target_version')

        if pattern_id not in self.patterns:
            return {'success': False, 'error': 'Pattern not found'}

        pattern = self.patterns[pattern_id]

        # Find target version
        version_content = self._get_pattern_version(pattern, target_version)
        if not version_content:
            return {'success': False, 'error': f'Version {target_version} not found'}

        # Create rollback version
        rollback_version = self._increment_version(pattern.version)

        version_entry = PatternVersion(
            version=rollback_version,
            timestamp=datetime.utcnow(),
            source=PatternSource.API,
            change_description=f"Rollback to version {target_version}",
            author=data.get('author', 'system'),
            parent_version=pattern.version
        )

        # Apply rollback
        pattern.content = version_content
        pattern.version = rollback_version
        pattern.versions.append(version_entry)
        pattern.updated_at = datetime.utcnow()

        self.logger.log('pattern_rollback', {
            'pattern_id': pattern_id,
            'from_version': pattern.versions[-2].version,
            'to_version': target_version
        })

        return {
            'success': True,
            'pattern_id': pattern_id,
            'new_version': rollback_version,
            'rolled_back_to': target_version
        }

    async def _sync_patterns(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Sync patterns between file and database"""
        direction = data.get('direction', 'file-to-db')  # or 'db-to-file'

        sync_results = {
            'synced': [],
            'failed': [],
            'skipped': []
        }

        if direction == 'file-to-db':
            # Reload patterns from files
            await self._load_patterns_from_files()
            sync_results['synced'] = list(self.patterns.keys())
        elif direction == 'db-to-file':
            # Save all active patterns to files
            for pattern in self.patterns.values():
                if pattern.status == PatternStatus.ACTIVE:
                    try:
                        await self._save_pattern_to_file(pattern)
                        sync_results['synced'].append(pattern.pattern_id)
                    except Exception as e:
                        sync_results['failed'].append({
                            'pattern_id': pattern.pattern_id,
                            'error': str(e)
                        })
                else:
                    sync_results['skipped'].append(pattern.pattern_id)

        return {
            'success': True,
            'direction': direction,
            'results': sync_results
        }

    async def _get_performance_stats(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Get pattern performance statistics"""
        pattern_id = data.get('pattern_id')

        if pattern_id:
            # Get stats for specific pattern
            if pattern_id not in self.patterns:
                return {'success': False, 'error': 'Pattern not found'}

            pattern = self.patterns[pattern_id]
            return {
                'success': True,
                'pattern_id': pattern_id,
                'performance': pattern.performance_stats
            }
        else:
            # Get overall stats
            return {
                'success': True,
                'overall_stats': self.stats,
                'top_performers': self._get_top_performing_patterns(5),
                'recently_updated': self._get_recently_updated_patterns(5)
            }

    async def _manage_ab_test(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Manage A/B testing of patterns"""
        action = data.get('action')  # create, status, conclude

        if action == 'create':
            pattern_a = data.get('pattern_a')
            pattern_b = data.get('pattern_b')
            test_id = f"ab_test_{datetime.utcnow().timestamp()}"

            self.ab_tests[test_id] = {
                'pattern_a': pattern_a,
                'pattern_b': pattern_b,
                'start_time': datetime.utcnow(),
                'status': 'active',
                'results': {
                    'pattern_a': {'processed': 0, 'matched': 0},
                    'pattern_b': {'processed': 0, 'matched': 0}
                }
            }

            self.stats['ab_tests_active'] += 1

            return {
                'success': True,
                'test_id': test_id,
                'message': 'A/B test created'
            }

        elif action == 'status':
            test_id = data.get('test_id')
            if test_id not in self.ab_tests:
                return {'success': False, 'error': 'Test not found'}

            test = self.ab_tests[test_id]
            return {
                'success': True,
                'test': test
            }

        elif action == 'conclude':
            test_id = data.get('test_id')
            if test_id not in self.ab_tests:
                return {'success': False, 'error': 'Test not found'}

            test = self.ab_tests[test_id]
            test['status'] = 'concluded'
            test['end_time'] = datetime.utcnow()

            # Determine winner
            results_a = test['results']['pattern_a']
            results_b = test['results']['pattern_b']

            rate_a = results_a['matched'] / results_a['processed'] if results_a['processed'] > 0 else 0
            rate_b = results_b['matched'] / results_b['processed'] if results_b['processed'] > 0 else 0

            winner = 'pattern_a' if rate_a >= rate_b else 'pattern_b'

            self.stats['ab_tests_active'] -= 1

            return {
                'success': True,
                'test_id': test_id,
                'winner': winner,
                'results': {
                    'pattern_a_rate': rate_a,
                    'pattern_b_rate': rate_b
                }
            }

        return {'success': False, 'error': 'Invalid action'}

    def _validate_pattern_content(self, pattern_type: str, content: Dict) -> bool:
        """Validate pattern content against rules"""
        required_fields = self.validation_rules.get(pattern_type, [])
        return all(field in content for field in required_fields)

    def _generate_pattern_id(self, base_name: str) -> str:
        """Generate unique pattern ID"""
        return hashlib.md5(f"{base_name}_{datetime.utcnow().timestamp()}".encode()).hexdigest()[:12]

    def _increment_version(self, version: str) -> str:
        """Increment semantic version"""
        parts = version.split('.')
        parts[-1] = str(int(parts[-1]) + 1)
        return '.'.join(parts)

    def _get_pattern_version(self, pattern: Pattern, version: str) -> Optional[Dict]:
        """Get specific version content (simplified)"""
        # In production, would reconstruct from version history
        for v in pattern.versions:
            if v.version == version:
                # Return current content (simplified - should apply diffs)
                return pattern.content
        return None

    def _extract_field(self, data: Dict, field_path: str) -> Any:
        """Extract field from nested dict using dot notation"""
        parts = field_path.split('.')
        current = data

        for part in parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return None

        return current

    async def _save_pattern_to_file(self, pattern: Pattern):
        """Save pattern to file system"""
        # Determine directory
        if pattern.pattern_type == 'parser':
            dir_type = 'normalized' if 'elastic' in pattern.name else 'raw'
        else:
            dir_type = pattern.pattern_type

        dir_path = self.pattern_dirs.get(dir_type, self.pattern_dirs['normalized'])
        dir_path.mkdir(parents=True, exist_ok=True)

        file_path = dir_path / f"{pattern.name}.json"

        with open(file_path, 'w') as f:
            json.dump(pattern.content, f, indent=2)

        pattern.metadata['file_path'] = str(file_path)

    def _update_statistics(self):
        """Update internal statistics"""
        self.stats['total_patterns'] = len(self.patterns)
        self.stats['active_patterns'] = sum(
            1 for p in self.patterns.values()
            if p.status == PatternStatus.ACTIVE
        )

        # Count by type
        self.stats['patterns_by_type'] = {}
        for pattern in self.patterns.values():
            self.stats['patterns_by_type'][pattern.pattern_type] = \
                self.stats['patterns_by_type'].get(pattern.pattern_type, 0) + 1

        # Count by vendor
        self.stats['patterns_by_vendor'] = {}
        for pattern in self.patterns.values():
            self.stats['patterns_by_vendor'][pattern.vendor] = \
                self.stats['patterns_by_vendor'].get(pattern.vendor, 0) + 1

        # Total versions
        self.stats['total_versions'] = sum(
            len(p.versions) for p in self.patterns.values()
        )

    def _get_top_performing_patterns(self, limit: int) -> List[Dict]:
        """Get top performing patterns"""
        patterns_with_stats = [
            (p, p.performance_stats.get('test_success_rate', 0))
            for p in self.patterns.values()
            if p.performance_stats
        ]

        patterns_with_stats.sort(key=lambda x: x[1], reverse=True)

        return [
            {
                'pattern_id': p.pattern_id,
                'name': p.name,
                'success_rate': rate
            }
            for p, rate in patterns_with_stats[:limit]
        ]

    def _get_recently_updated_patterns(self, limit: int) -> List[Dict]:
        """Get recently updated patterns"""
        sorted_patterns = sorted(
            self.patterns.values(),
            key=lambda p: p.updated_at,
            reverse=True
        )

        return [
            {
                'pattern_id': p.pattern_id,
                'name': p.name,
                'updated_at': p.updated_at.isoformat()
            }
            for p in sorted_patterns[:limit]
        ]

    def get_capabilities(self) -> Dict[str, Any]:
        """Return pattern manager capabilities"""
        return {
            'engine': 'pattern_manager',
            'version': self.version,
            'capabilities': [
                'pattern_crud',
                'version_control',
                'pattern_validation',
                'pattern_testing',
                'performance_tracking',
                'ab_testing',
                'file_database_sync',
                'pattern_rollback'
            ],
            'supported_pattern_types': list(self.validation_rules.keys()),
            'statistics': self.stats
        }

    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate pattern manager configuration"""
        return True  # No specific configuration required


async def main():
    """Main entry point for Pattern Manager"""
    manager = PatternManager()

    # Log capabilities
    capabilities = manager.get_capabilities()
    manager.logger.log('engine_capabilities', capabilities)

    # Test pattern creation
    test_pattern = {
        'command': 'create',
        'data': {
            'pattern_type': 'parser',
            'name': 'test_pattern',
            'content': {
                'vendor': 'test',
                'product': 'demo',
                'entity_mappings': {
                    'hostname': {'primary': 'host.name'},
                    'user': {'primary': 'user.name'}
                }
            },
            'metadata': {
                'author': 'test_system',
                'source': 'api'
            }
        }
    }

    result = await manager.process_message(test_pattern)
    print(f"Pattern creation test: {result}")

    # Start processing from queue
    await manager.start()


if __name__ == '__main__':
    asyncio.run(main())