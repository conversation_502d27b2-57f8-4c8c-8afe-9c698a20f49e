/**
 * Correlation Tab - Timeline and Related Events Styles
 */

.correlation-tab {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
}

/* No Correlation State */
.no-correlation {
  text-align: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.no-correlation-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.no-correlation h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.no-correlation p {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0;
}

.no-correlation .hint {
  font-size: 13px;
  font-style: italic;
  color: #9ca3af;
  margin-top: 12px;
}

/* Correlation Header */
.correlation-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.time-window-card,
.correlation-score-card {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.time-window-card h3,
.correlation-score-card h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.correlation-score-card {
  border-left: 4px solid;
}

.time-window-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.time-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.time-detail:last-child {
  border-bottom: none;
}

.time-detail .label {
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
}

.time-detail .value {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

/* Correlation Score */
.score-display {
  text-align: center;
  padding: 16px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.score-value {
  font-size: 36px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 8px;
}

.score-label {
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.score-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.score-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.score-row .label {
  color: #6b7280;
  font-weight: 500;
}

.score-row .value {
  color: #111827;
  font-weight: 600;
}

/* Distribution Section */
.distribution-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.distribution-card {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.distribution-card h4 {
  margin: 0 0 16px 0;
  font-size: 15px;
  font-weight: 600;
  color: #111827;
}

.distribution-bars {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.distribution-bar {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bar-label {
  min-width: 120px;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bar-container {
  flex: 1;
  height: 24px;
  background: #f3f4f6;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.bar-count {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  font-weight: 600;
  color: #111827;
}

/* MITRE Chain */
.mitre-chain-section {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.mitre-chain-section h4 {
  margin: 0 0 16px 0;
  font-size: 15px;
  font-weight: 600;
  color: #111827;
}

.mitre-chain {
  display: flex;
  align-items: center;
  gap: 12px;
  overflow-x: auto;
  padding: 12px 0;
}

.mitre-technique {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  background: #fef3c7;
  border-radius: 6px;
  border: 2px solid #fbbf24;
  min-width: 140px;
  flex-shrink: 0;
}

.technique-id {
  font-size: 12px;
  font-weight: 700;
  color: #92400e;
  margin-bottom: 4px;
}

.technique-name {
  font-size: 11px;
  font-weight: 600;
  color: #78350f;
  text-align: center;
  margin-bottom: 4px;
}

.technique-tactic {
  font-size: 10px;
  font-weight: 500;
  color: #a16207;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chain-arrow {
  font-size: 24px;
  color: #d97706;
  font-weight: 700;
  flex-shrink: 0;
}

/* Events Section */
.events-section {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.events-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.events-header h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #111827;
}

.export-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.export-btn:hover {
  background: #2563eb;
}

/* Events Table */
.events-table {
  display: flex;
  flex-direction: column;
}

.table-header {
  display: grid;
  grid-template-columns: 180px 100px 120px 120px 1fr 60px;
  gap: 12px;
  padding: 12px 20px;
  background: #f9fafb;
  border-bottom: 2px solid #e5e7eb;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.header-cell {
  cursor: pointer;
  user-select: none;
  transition: color 0.2s;
}

.header-cell:hover {
  color: #111827;
}

.table-body {
  display: flex;
  flex-direction: column;
}

.event-row {
  border-bottom: 1px solid #e5e7eb;
}

.event-row:last-child {
  border-bottom: none;
}

.event-main {
  display: grid;
  grid-template-columns: 180px 100px 120px 120px 1fr 60px;
  gap: 12px;
  padding: 16px 20px;
  transition: background 0.2s;
}

.event-main:hover {
  background: #f9fafb;
}

.event-cell {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #374151;
}

.timestamp-cell {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.severity-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 700;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.type-cell {
  font-weight: 500;
}

.source-cell {
  color: #6b7280;
}

.description-cell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.expand-btn {
  width: 32px;
  height: 32px;
  background: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expand-btn:hover {
  background: #e5e7eb;
  color: #111827;
}

/* Event Details */
.event-details {
  padding: 20px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.detail-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  gap: 12px;
  font-size: 13px;
}

.detail-label {
  min-width: 140px;
  font-weight: 500;
  color: #6b7280;
}

.detail-value {
  font-weight: 500;
  color: #111827;
  flex: 1;
}

.raw-event-data {
  background: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #374151;
  overflow-x: auto;
  max-height: 300px;
}

/* Summary Footer */
.summary-footer {
  background: #eff6ff;
  border-radius: 8px;
  border: 1px solid #bfdbfe;
  padding: 20px;
}

.summary-footer h4 {
  margin: 0 0 12px 0;
  font-size: 15px;
  font-weight: 600;
  color: #1e40af;
}

.findings-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.finding-item {
  padding: 10px 12px;
  background: #ffffff;
  border-left: 3px solid #3b82f6;
  border-radius: 4px;
  font-size: 13px;
  color: #374151;
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 1200px) {
  .correlation-header,
  .distribution-section {
    grid-template-columns: 1fr;
  }

  .table-header,
  .event-main {
    grid-template-columns: 140px 80px 100px 100px 1fr 50px;
  }
}

@media (max-width: 768px) {
  .correlation-tab {
    padding: 16px;
  }

  .table-header {
    display: none;
  }

  .event-main {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .event-cell {
    padding: 4px 0;
  }

  .event-cell::before {
    content: attr(data-label);
    font-weight: 600;
    margin-right: 8px;
    min-width: 100px;
    display: inline-block;
  }

  .mitre-chain {
    flex-direction: column;
    align-items: stretch;
  }

  .chain-arrow {
    transform: rotate(90deg);
    align-self: center;
  }
}

/* Print Styles */
@media print {
  .export-btn,
  .expand-btn {
    display: none;
  }

  .event-details {
    display: block !important;
  }

  .event-row {
    page-break-inside: avoid;
  }

  .raw-event-data {
    max-height: none;
  }
}
