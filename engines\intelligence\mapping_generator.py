"""
AI Mapping Generator
Generates entity extraction mappings for new log schemas using AI consensus
ONE-TIME operation per schema - then deterministic extraction is free forever
"""

import json
import logging
from typing import Dict, Any, List
from datetime import datetime

logger = logging.getLogger(__name__)


MAPPING_GENERATION_PROMPT_TEMPLATE = """
You are a log analysis expert specializing in security event data structures. Your task is to analyze this log and create entity extraction mappings.

**Sample Log:**
```json
{sample_log}
```

**Your Task:**
1. Identify the vendor (CrowdStrike, Fortinet, Palo Alto, Microsoft, etc.)
2. Identify the product (Falcon, FortiGate, PAN-OS, Defender, etc.)
3. Identify the log type (firewall, edr, authentication, network, application, etc.)
4. Map ALL security-relevant entities to their EXACT JSON paths

**Security Entities to Extract:**
- source_ip, destination_ip: IP addresses involved
- source_port, destination_port: Network ports
- hostname, device_name: Host/device identifiers
- username, user_id: User identifiers
- process_name, process_path: Process information
- file_name, file_path, file_hash: File information
- action, event_action: What happened (allow, deny, block, alert)
- protocol, network_protocol: Network protocol used
- application, app_name: Application involved
- severity, log_level: Event severity
- rule_id, rule_name, signature: Detection rule info
- threat_name, malware_family: Threat identifiers
- url, domain: Web/DNS information

**JSON Path Format:**
- Use exact paths: "content.log.data[0].data.source.ip"
- For arrays, use [0] to reference first element
- Be precise with nested structures
- Test your paths mentally - they must work

**Response Format (MUST BE VALID JSON):**
```json
{{
  "schema_name": "firewall_fortinet",
  "vendor": "Fortinet",
  "product": "FortiGate",
  "log_type": "firewall",
  "confidence": 0.95,
  "entity_mapping": {{
    "source_ip": "content.log.data[0].data.source.ip",
    "destination_ip": "content.log.data[0].data.destination.ip",
    "source_port": "content.log.data[0].data.source.port",
    "destination_port": "content.log.data[0].data.destination.port",
    "action": "content.log.data[0].data.event.action",
    "firewall_name": "content.log.data[0].data.observer.name",
    "protocol": "content.log.data[0].data.network.protocol"
  }}
}}
```

**CRITICAL REQUIREMENTS:**
- Entity mapping MUST be a flat dictionary (entity_type -> json_path)
- JSON paths MUST be strings, not nested objects
- Include ONLY entities that exist in the log
- Be 100% accurate with paths - extraction depends on this
- Confidence should reflect certainty about vendor/type identification (0.0-1.0)

Respond ONLY with the JSON object, no explanations.
"""


class MappingGenerator:
    """
    Generates entity extraction mappings using AI consensus
    Cost: $0.008 per schema (one-time)
    """

    def __init__(self, ai_model_manager, consensus_engine, logger=None):
        self.ai_model_manager = ai_model_manager
        self.consensus_engine = consensus_engine
        self.logger = logger or logging.getLogger(__name__)

    async def generate_mapping(self, sample_log: Dict[str, Any], models: List[str] = None) -> Dict[str, Any]:
        """
        Generate entity mapping using AI consensus

        Args:
            sample_log: Representative log sample
            models: AI models to use (default: ['free', 'mid_quality'])

        Returns:
            Mapping result with consensus validation
        """
        if models is None:
            models = ['free', 'mid_quality']  # Gemma + Sonnet

        self.logger.info(f"Generating mapping using {len(models)} AI models")

        try:
            # Build prompt
            prompt = MAPPING_GENERATION_PROMPT_TEMPLATE.format(
                sample_log=json.dumps(sample_log, indent=2)
            )

            # Get AI responses
            responses = []

            for model_tier in models:
                try:
                    self.logger.info(f"Calling AI model: {model_tier}")

                    response = await self.ai_model_manager.call_ai_model(
                        model_tier,
                        {'prompt': prompt}
                    )

                    # Parse AI response
                    parsed = self._parse_ai_response(response)

                    if parsed:
                        responses.append({
                            'model': model_tier,
                            'result': parsed,
                            'confidence': parsed.get('confidence', 0.0)
                        })

                        self.logger.info(f"Model {model_tier} generated mapping for {parsed.get('schema_name', 'unknown')}")
                    else:
                        self.logger.warning(f"Model {model_tier} failed to generate valid mapping")

                except Exception as e:
                    self.logger.error(f"Model {model_tier} error: {e}")
                    continue

            if not responses:
                raise Exception("No AI models successfully generated mappings")

            # Calculate consensus
            consensus = self._calculate_consensus(responses)

            self.logger.info(f"Mapping consensus reached with {len(responses)} models: {consensus['schema_name']}")

            return consensus

        except Exception as e:
            self.logger.error(f"Mapping generation failed: {e}")
            raise

    def _parse_ai_response(self, response: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Parse and validate AI response

        Extracts JSON from AI response and validates structure
        """
        try:
            # Get response text
            if isinstance(response, dict):
                if 'content' in response:
                    text = response['content']
                elif 'text' in response:
                    text = response['text']
                elif 'response' in response:
                    text = response['response']
                else:
                    text = json.dumps(response)
            else:
                text = str(response)

            # Try to extract JSON
            # Look for JSON object in response
            start_idx = text.find('{')
            end_idx = text.rfind('}') + 1

            if start_idx >= 0 and end_idx > start_idx:
                json_str = text[start_idx:end_idx]
                parsed = json.loads(json_str)

                # Validate structure
                required_fields = ['schema_name', 'vendor', 'log_type', 'entity_mapping']
                if all(field in parsed for field in required_fields):
                    # Validate entity_mapping is flat dict
                    entity_mapping = parsed['entity_mapping']
                    if isinstance(entity_mapping, dict) and all(isinstance(v, str) for v in entity_mapping.values()):
                        return parsed
                    else:
                        self.logger.warning("Entity mapping is not a flat dictionary")

            self.logger.warning(f"Failed to parse AI response: {text[:200]}")
            return None

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON decode error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Parse error: {e}")
            return None

    def _calculate_consensus(self, responses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate consensus from multiple AI responses

        Strategy:
        - Vendor/product/type: Use most common response
        - Entity mapping: Merge mappings (prefer higher confidence)
        - Confidence: Average of all responses
        """
        if not responses:
            raise Exception("No responses to calculate consensus")

        # If only one response, use it directly
        if len(responses) == 1:
            result = responses[0]['result'].copy()
            result['consensus_models'] = 1
            result['mapping_model'] = responses[0]['model']
            return result

        # Extract all results
        results = [r['result'] for r in responses]

        # Vote on vendor/product/type
        vendors = [r.get('vendor', 'Unknown') for r in results]
        products = [r.get('product', 'Unknown') for r in results]
        log_types = [r.get('log_type', 'generic') for r in results]

        consensus_result = {
            'schema_name': self._most_common(results, 'schema_name'),
            'vendor': self._most_common_value(vendors),
            'product': self._most_common_value(products),
            'log_type': self._most_common_value(log_types),
            'confidence': sum(r.get('confidence', 0.0) for r in results) / len(results),
            'entity_mapping': self._merge_entity_mappings(results),
            'consensus_models': len(responses),
            'mapping_model': 'consensus:' + '+'.join([r['model'] for r in responses])
        }

        return consensus_result

    def _most_common(self, results: List[Dict], field: str) -> str:
        """Get most common value for a field"""
        values = [r.get(field, '') for r in results if field in r]
        if not values:
            return 'unknown'
        return max(set(values), key=values.count)

    def _most_common_value(self, values: List[str]) -> str:
        """Get most common value from list"""
        if not values:
            return 'Unknown'
        return max(set(values), key=values.count)

    def _merge_entity_mappings(self, results: List[Dict]) -> Dict[str, str]:
        """
        Merge entity mappings from multiple results

        Strategy: For each entity type, use the most common path
        """
        all_entity_types = set()
        for result in results:
            all_entity_types.update(result.get('entity_mapping', {}).keys())

        merged_mapping = {}

        for entity_type in all_entity_types:
            # Get all paths for this entity type
            paths = []
            for result in results:
                mapping = result.get('entity_mapping', {})
                if entity_type in mapping:
                    paths.append(mapping[entity_type])

            if paths:
                # Use most common path
                most_common_path = max(set(paths), key=paths.count)
                merged_mapping[entity_type] = most_common_path

        return merged_mapping
