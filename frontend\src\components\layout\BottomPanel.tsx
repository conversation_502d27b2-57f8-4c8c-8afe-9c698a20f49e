import React from 'react'
import { X, Terminal, AlertCircle, CheckCircle, Info } from 'lucide-react'
import { useLayoutStore } from '../../stores/navigationStore'

const BottomPanel: React.FC = () => {
  const { toggleBottomPanel } = useLayoutStore()

  const logs = [
    { type: 'info', message: 'Investigation started for CASE-123', time: '10:32:15' },
    { type: 'success', message: 'Entity enrichment completed', time: '10:32:18' },
    { type: 'warning', message: 'Rate limit approaching for API calls', time: '10:32:20' },
    { type: 'error', message: 'Failed to connect to CTI feed', time: '10:32:25' },
    { type: 'info', message: 'Pattern crystallization in progress', time: '10:32:30' }
  ]

  const getIcon = (type: string) => {
    switch (type) {
      case 'success': return CheckCircle
      case 'error': return AlertCircle
      case 'warning': return AlertCircle
      default: return Info
    }
  }

  const getColor = (type: string) => {
    switch (type) {
      case 'success': return 'text-green-600'
      case 'error': return 'text-red-600'
      case 'warning': return 'text-yellow-600'
      default: return 'text-blue-600'
    }
  }

  return (
    <div className="h-64 bg-gray-900 border-t flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-2 border-b border-gray-800">
        <div className="flex items-center gap-2 text-gray-300">
          <Terminal size={16} />
          <span className="text-sm font-medium">System Console</span>
        </div>
        <button
          onClick={toggleBottomPanel}
          className="p-1 hover:bg-gray-800 rounded text-gray-400"
        >
          <X size={16} />
        </button>
      </div>

      {/* Logs */}
      <div className="flex-1 overflow-y-auto p-2 font-mono text-xs">
        {logs.map((log, idx) => {
          const Icon = getIcon(log.type)
          return (
            <div key={idx} className="flex items-start gap-2 py-1">
              <span className="text-gray-600">{log.time}</span>
              <Icon size={14} className={getColor(log.type)} />
              <span className="text-gray-300">{log.message}</span>
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default BottomPanel