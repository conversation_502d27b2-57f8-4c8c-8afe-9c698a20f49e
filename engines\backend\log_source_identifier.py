"""
Log Source Identifier
Automatically identifies and tracks log sources in the Backend Engine
"""

import re
import json
from typing import Dict, Optional, Tuple, List
from datetime import datetime
import logging


def db_fetch(connection, query: str, *params):
    """Helper to execute fetch queries with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    results = cursor.fetchall()
    cursor.close()
    return results


def db_execute(connection, query: str, *params):
    """Helper to execute non-query commands with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    cursor.close()


def db_fetchval(connection, query: str, *params):
    """Helper to fetch a single value with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    result = cursor.fetchone()
    cursor.close()
    return result[0] if result else None


def db_fetchrow(connection, query: str, *params):
    """Helper to fetch a single row with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    result = cursor.fetchone()
    cursor.close()
    return result


logger = logging.getLogger(__name__)


class LogSourceIdentifier:
    """Identifies and tracks log sources from incoming events"""

    def __init__(self, db_connection = None):
        self.db_connection = db_connection

        # Compiled patterns for faster matching
        self.identification_patterns = self._compile_patterns()

        # Cache for source quality lookups
        self.quality_cache = {}
        self.cache_ttl = 3600  # 1 hour

    def _compile_patterns(self) -> Dict[str, Dict]:
        """Compile identification patterns for efficient matching"""
        patterns = {
            'crowdstrike': {
                'regex': re.compile(r'event_simpleName|"aid":\s*"[a-f0-9]{32}"', re.IGNORECASE),
                'fields': ['event_simpleName', 'aid', 'ComputerName'],
                'vendor': 'CrowdStrike',
                'product': 'Falcon',
                'type': 'edr',
                'quality': 10
            },
            'sentinelone': {
                'regex': re.compile(r'agentId.*sentinelone|"dataSource":\s*"SentinelOne"', re.IGNORECASE),
                'fields': ['agentId', 'agentUuid', 'siteName'],
                'vendor': 'SentinelOne',
                'product': 'Singularity',
                'type': 'edr',
                'quality': 9
            },
            'defender': {
                'regex': re.compile(r'Provider.*Microsoft-Windows-Security|"DeviceId".*"MDE"', re.IGNORECASE),
                'fields': ['Provider', 'EventID', 'DeviceId'],
                'vendor': 'Microsoft',
                'product': 'Defender for Endpoint',
                'type': 'edr',
                'quality': 8
            },
            'paloalto': {
                'regex': re.compile(r'CEF:.*Palo Alto Networks|PAN-OS', re.IGNORECASE),
                'fields': ['device_name', 'serial', 'type'],
                'vendor': 'Palo Alto',
                'product': 'PAN-OS',
                'type': 'firewall',
                'quality': 8
            },
            'fortinet': {
                'regex': re.compile(r'devid=.*FG[0-9]+|fortigate', re.IGNORECASE),
                'fields': ['devid', 'devname', 'logid'],
                'vendor': 'Fortinet',
                'product': 'FortiGate',
                'type': 'firewall',
                'quality': 7
            },
            'wazuh': {
                'regex': re.compile(r'"groups".*wazuh|"decoder":\s*"wazuh"|"manager".*wazuh', re.IGNORECASE),
                'fields': ['rule', 'agent', 'manager'],
                'vendor': 'Wazuh',
                'product': 'Wazuh',
                'type': 'hids',
                'quality': 6
            },
            'sysmon': {
                'regex': re.compile(r'Microsoft-Windows-Sysmon|EventID":\s*[1-9]$', re.IGNORECASE),
                'fields': ['SourceName', 'EventID', 'ProcessGuid'],
                'vendor': 'Microsoft',
                'product': 'Sysmon',
                'type': 'host_monitoring',
                'quality': 7
            },
            'elastic': {
                'regex': re.compile(r'"agent\.type":\s*"filebeat"|"agent\.type":\s*"winlogbeat"', re.IGNORECASE),
                'fields': ['agent.type', 'agent.version', 'host.name'],
                'vendor': 'Elastic',
                'product': 'Security',
                'type': 'siem',
                'quality': 7
            },
            'splunk': {
                'regex': re.compile(r'"splunk_server"|"_indextime"', re.IGNORECASE),
                'fields': ['splunk_server', 'index', 'sourcetype'],
                'vendor': 'Splunk',
                'product': 'Enterprise Security',
                'type': 'siem',
                'quality': 8
            },
            'qradar': {
                'regex': re.compile(r'"QRadar"|"EventName".*"QRadar"', re.IGNORECASE),
                'fields': ['EventName', 'CategoryName', 'QID'],
                'vendor': 'IBM',
                'product': 'QRadar',
                'type': 'siem',
                'quality': 7
            }
        }
        return patterns

    async def identify_source(self, log_data: Dict) -> Dict[str, any]:
        """
        Identify the source of a log event

        Returns:
            Dict containing vendor, product, type, quality_score, and confidence
        """
        # Convert to string for regex matching
        log_str = json.dumps(log_data) if isinstance(log_data, dict) else str(log_data)

        # Check each pattern
        for pattern_name, pattern_info in self.identification_patterns.items():
            # Try regex match first (faster)
            if pattern_info['regex'].search(log_str):
                # Validate with field check for higher confidence
                confidence = 0.7  # Base confidence from regex match

                # Check for specific fields
                fields_found = 0
                for field in pattern_info['fields']:
                    if field in log_str:
                        fields_found += 1

                if fields_found > 0:
                    confidence = min(0.7 + (fields_found * 0.1), 1.0)

                return {
                    'vendor': pattern_info['vendor'],
                    'product': pattern_info['product'],
                    'source_type': pattern_info['type'],
                    'quality_score': pattern_info['quality'],
                    'confidence': confidence,
                    'identified_by': pattern_name
                }

        # Try to identify from explicit fields in the log
        source_info = await self._identify_from_fields(log_data)
        if source_info:
            return source_info

        # If database is available, try database patterns
        if self.db_connection:
            db_result = await self._identify_from_database(log_data)
            if db_result:
                return db_result

        # Unable to identify, return unknown
        return {
            'vendor': 'unknown',
            'product': 'unknown',
            'source_type': log_data.get('source_type', 'unknown'),
            'quality_score': 5,
            'confidence': 0.0,
            'identified_by': 'none'
        }

    async def _identify_from_fields(self, log_data: Dict) -> Optional[Dict]:
        """Try to identify source from explicit fields in the log"""
        # Common field mappings
        field_mappings = {
            'vendor': ['vendor', 'product_vendor', 'log_vendor', 'dvc_vendor'],
            'product': ['product', 'product_name', 'log_product', 'dvc_product'],
            'source_type': ['source_type', 'log_type', 'event_type', 'category'],
            'hostname': ['hostname', 'host', 'computer_name', 'dvc_host'],
            'source_id': ['source_id', 'device_id', 'agent_id', 'sensor_id']
        }

        extracted = {}
        for target_field, possible_fields in field_mappings.items():
            for field in possible_fields:
                if field in log_data and log_data[field]:
                    extracted[target_field] = log_data[field]
                    break

        if extracted.get('vendor') or extracted.get('product'):
            # Look up quality score
            quality = await self._get_quality_score(
                extracted.get('vendor', 'unknown'),
                extracted.get('product', 'unknown')
            )

            return {
                'vendor': extracted.get('vendor', 'unknown'),
                'product': extracted.get('product', 'unknown'),
                'source_type': extracted.get('source_type', 'unknown'),
                'quality_score': quality,
                'confidence': 0.5,  # Medium confidence from field extraction
                'identified_by': 'field_extraction',
                'hostname': extracted.get('hostname'),
                'source_id': extracted.get('source_id')
            }

        return None

    async def _identify_from_database(self, log_data: Dict) -> Optional[Dict]:
        """Use database patterns to identify source"""
        try:
            result = db_fetchrow(self.db_connection, """
                SELECT * FROM identify_log_source(%s::JSONB)
            """, json.dumps(log_data))

            if result and result['vendor'] != 'unknown':
                return {
                    'vendor': result['vendor'],
                    'product': result['product'],
                    'source_type': result['source_type'],
                    'quality_score': result['quality_score'],
                    'confidence': result['confidence'],
                    'identified_by': 'database_pattern'
                }
        except Exception as e:
            logger.error(f"Database identification failed: {e}")

        return None

    async def _get_quality_score(self, vendor: str, product: str) -> int:
        """Get quality score for a vendor/product combination"""
        # Check cache first
        cache_key = f"{vendor}:{product}"
        if cache_key in self.quality_cache:
            cached_data = self.quality_cache[cache_key]
            if (datetime.now().timestamp() - cached_data['timestamp']) < self.cache_ttl:
                return cached_data['score']

        # Default quality scores
        quality_map = {
            'crowdstrike:falcon': 10,
            'sentinelone:singularity': 9,
            'microsoft:defender for endpoint': 8,
            'microsoft:defender': 8,
            'palo alto:pan-os': 8,
            'splunk:enterprise security': 8,
            'elastic:security': 7,
            'fortinet:fortigate': 7,
            'microsoft:sysmon': 7,
            'ibm:qradar': 7,
            'wazuh:wazuh': 6,
            'ossec:ossec': 5
        }

        key = f"{vendor.lower()}:{product.lower()}"
        score = quality_map.get(key, 5)

        # Update cache
        self.quality_cache[cache_key] = {
            'score': score,
            'timestamp': datetime.now().timestamp()
        }

        return score

    async def enrich_with_source_info(self, event: Dict) -> Dict:
        """
        Enrich an event with source identification information

        Args:
            event: The log event to enrich

        Returns:
            The enriched event with source metadata
        """
        # Identify the source
        source_info = await self.identify_source(event)

        # Add source metadata to event
        event['source_metadata'] = {
            'vendor': source_info['vendor'],
            'product': source_info['product'],
            'source_type': source_info['source_type'],
            'quality_score': source_info['quality_score'],
            'quality_tier': self._get_quality_tier(source_info['quality_score']),
            'identification_confidence': source_info['confidence'],
            'identified_by': source_info['identified_by'],
            'identified_at': datetime.utcnow().isoformat()
        }

        # Add hostname and source_id if available
        if source_info.get('hostname'):
            event['source_metadata']['hostname'] = source_info['hostname']
        if source_info.get('source_id'):
            event['source_metadata']['source_id'] = source_info['source_id']

        # Override source_type if we have high confidence
        if source_info['confidence'] >= 0.7 and source_info['source_type'] != 'unknown':
            event['source_type'] = source_info['source_type']

        return event

    def _get_quality_tier(self, quality_score: int) -> str:
        """Convert quality score to tier"""
        if quality_score >= 9:
            return 'premium'
        elif quality_score >= 7:
            return 'good'
        elif quality_score >= 5:
            return 'basic'
        else:
            return 'minimal'

    async def register_source(self, source_info: Dict) -> bool:
        """
        Register a new log source in the database

        Args:
            source_info: Dictionary containing source information

        Returns:
            True if registration successful
        """
        if not self.db_connection:
            logger.warning("No database connection for source registration")
            return False

        try:
            db_execute(self.db_connection, """
                INSERT INTO log_source_registry (
                    source_identifier, source_type, vendor, product,
                    quality_score, quality_tier, hostname, ip_address,
                    integration_status, last_event_received
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, $10)
                ON CONFLICT (source_identifier) DO UPDATE SET
                    last_event_received = EXCLUDED.last_event_received,
                    integration_status = EXCLUDED.integration_status
            """,
                source_info.get('source_identifier', f"{source_info['vendor']}-{source_info['product']}-01"),
                source_info['source_type'],
                source_info['vendor'],
                source_info['product'],
                source_info.get('quality_score', 5),
                self._get_quality_tier(source_info.get('quality_score', 5)),
                source_info.get('hostname'),
                source_info.get('ip_address'),
                'active',
                datetime.utcnow()
            )

            logger.info(f"Registered source: {source_info['vendor']} {source_info['product']}")
            return True

        except Exception as e:
            logger.error(f"Failed to register source: {e}")
            return False

    async def update_source_metrics(self, source_identifier: str, metrics: Dict) -> bool:
        """
        Update metrics for a log source

        Args:
            source_identifier: Unique identifier for the source
            metrics: Dictionary containing metric updates

        Returns:
            True if update successful
        """
        if not self.db_connection:
            return False

        try:
            db_execute(self.db_connection, """
                INSERT INTO log_source_metrics (
                    source_identifier, events_received, parse_success_rate,
                    alerts_generated, metric_date
                ) VALUES (%s, %s, %s, %s, CURRENT_DATE)
                ON CONFLICT (source_identifier, metric_date) DO UPDATE SET
                    events_received = log_source_metrics.events_received + EXCLUDED.events_received,
                    parse_success_rate =
                        (log_source_metrics.parse_success_rate * log_source_metrics.events_received +
                         EXCLUDED.parse_success_rate * EXCLUDED.events_received) /
                        (log_source_metrics.events_received + EXCLUDED.events_received),
                    alerts_generated = log_source_metrics.alerts_generated + EXCLUDED.alerts_generated
            """,
                source_identifier,
                metrics.get('events_received', 1),
                metrics.get('parse_success_rate', 1.0),
                metrics.get('alerts_generated', 0)
            )

            return True

        except Exception as e:
            logger.error(f"Failed to update source metrics: {e}")
            return False

    async def get_source_quality_assessment(self) -> List[Dict]:
        """Get quality assessment for all registered sources"""
        if not self.db_connection:
            return []

        try:
            rows = db_fetch(self.db_connection, """
                SELECT * FROM v_source_quality_assessment
                ORDER BY quality_score DESC
            """)

            return [dict(row) for row in rows]

        except Exception as e:
            logger.error(f"Failed to get quality assessment: {e}")
            return []