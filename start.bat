@echo off
REM SIEMLess v2.0 - Unified Platform Startup (Windows)

echo ============================================
echo    SIEMLess v2.0 - Starting Platform
echo ============================================

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo Error: Docker is not running!
    echo Please start Docker Desktop and try again.
    pause
    exit /b 1
)

REM Check if .env exists
if not exist .env (
    echo Creating .env from .env.example...
    copy .env.example .env >nul
    echo Created .env file - edit to add your API keys if needed
)

REM Clean up existing containers
echo Cleaning up existing containers...
docker-compose down >nul 2>&1

REM Build and start all services
echo Starting all services...
docker-compose up -d --build

REM Wait for services
echo Waiting for services to initialize (30 seconds)...
timeout /t 30 /nobreak >nul

echo.
echo ============================================
echo    SIEMLess v2.0 Ready!
echo ============================================
echo.
echo Access Points:
echo    Frontend:           http://localhost:3000
echo    Intelligence API:   http://localhost:8001/docs
echo    Backend API:        http://localhost:8002/docs
echo    Ingestion API:      http://localhost:8003/docs
echo    Context API:        http://localhost:8004/docs
echo    Delivery API:       http://localhost:8005/docs
echo.
echo Infrastructure:
echo    PostgreSQL:         localhost:5432
echo    Redis:              localhost:6379
echo.
echo Useful Commands:
echo    View logs:          docker-compose logs -f [service]
echo    Stop all:           docker-compose down
echo    Clean restart:      docker-compose down -v
echo.
echo Platform is ready for use!
echo.
pause