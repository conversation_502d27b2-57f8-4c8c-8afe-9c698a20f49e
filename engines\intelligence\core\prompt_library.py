"""
Prompt Library - Versioned prompt management for AI models
Manages prompt templates, versioning, A/B testing, and effectiveness tracking
"""

import os
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path
import yaml


class PromptTemplate:
    """Individual prompt template with metadata"""

    def __init__(
        self,
        name: str,
        template: str,
        version: str,
        task_type: str,
        variables: List[str] = None,
        metadata: Dict[str, Any] = None
    ):
        self.name = name
        self.template = template
        self.version = version
        self.task_type = task_type
        self.variables = variables or []
        self.metadata = metadata or {}
        self.created_at = datetime.now()

    def render(self, **kwargs) -> str:
        """
        Render template with provided variables

        Args:
            **kwargs: Variable values to substitute

        Returns:
            Rendered prompt string
        """
        # Simple variable substitution (can be upgraded to Jinja2 later)
        rendered = self.template
        for key, value in kwargs.items():
            placeholder = f"{{{key}}}"
            rendered = rendered.replace(placeholder, str(value))
        return rendered

    def validate_variables(self, **kwargs) -> bool:
        """Check if all required variables are provided"""
        missing = set(self.variables) - set(kwargs.keys())
        return len(missing) == 0

    def __repr__(self) -> str:
        return f"PromptTemplate(name={self.name}, version={self.version}, task={self.task_type})"


class PromptLibrary:
    """
    Centralized prompt management system

    Features:
    - Load prompts from YAML files
    - Version management (A/B testing support)
    - Template rendering with variable substitution
    - Prompt effectiveness tracking
    - Hot-reload support
    """

    def __init__(
        self,
        prompts_dir: str = None,
        db_connection = None,
        logger: logging.Logger = None
    ):
        """
        Initialize Prompt Library

        Args:
            prompts_dir: Directory containing prompt YAML files
            db_connection: PostgreSQL connection (optional)
            logger: Logger instance
        """
        self.prompts_dir = prompts_dir or os.path.join(
            os.path.dirname(__file__), '..', 'prompts'
        )
        self.db = db_connection
        self.logger = logger or logging.getLogger(__name__)

        # In-memory prompt storage
        self.prompts: Dict[str, Dict[str, PromptTemplate]] = {}  # {task_type: {version: template}}
        self.active_versions: Dict[str, str] = {}  # {task_type: active_version}

        # Effectiveness tracking
        self.prompt_stats: Dict[str, Dict[str, Any]] = {}  # {prompt_name: stats}

        # Load prompts
        self._ensure_prompts_dir()
        self.reload_prompts()

        self.logger.info(f"Prompt Library initialized with {len(self.prompts)} task types")

    def _ensure_prompts_dir(self):
        """Create prompts directory if it doesn't exist"""
        Path(self.prompts_dir).mkdir(parents=True, exist_ok=True)
        self.logger.debug(f"Prompts directory: {self.prompts_dir}")

    def reload_prompts(self) -> bool:
        """
        Hot-reload all prompts from YAML files

        Returns:
            True if reload successful
        """
        try:
            self.prompts.clear()
            self.active_versions.clear()

            prompts_path = Path(self.prompts_dir)
            if not prompts_path.exists():
                self.logger.warning(f"Prompts directory not found: {self.prompts_dir}")
                return False

            # Load all YAML files
            yaml_files = list(prompts_path.glob("*.yaml")) + list(prompts_path.glob("*.yml"))

            for yaml_file in yaml_files:
                try:
                    with open(yaml_file, 'r', encoding='utf-8') as f:
                        prompt_config = yaml.safe_load(f)

                    if not prompt_config:
                        continue

                    # Parse prompt configuration
                    task_type = prompt_config.get('task_type')
                    if not task_type:
                        self.logger.warning(f"No task_type in {yaml_file.name}, skipping")
                        continue

                    # Create template
                    template = PromptTemplate(
                        name=prompt_config.get('name', yaml_file.stem),
                        template=prompt_config.get('template', ''),
                        version=prompt_config.get('version', '1.0'),
                        task_type=task_type,
                        variables=prompt_config.get('variables', []),
                        metadata=prompt_config.get('metadata', {})
                    )

                    # Store by task_type and version
                    if task_type not in self.prompts:
                        self.prompts[task_type] = {}

                    self.prompts[task_type][template.version] = template

                    # Set as active version if specified
                    if prompt_config.get('active', False):
                        self.active_versions[task_type] = template.version

                    self.logger.debug(f"Loaded prompt: {template.name} v{template.version} for {task_type}")

                except Exception as e:
                    self.logger.error(f"Error loading {yaml_file.name}: {e}")

            # Set default active versions for tasks without one
            for task_type, versions in self.prompts.items():
                if task_type not in self.active_versions and versions:
                    # Use latest version as default
                    latest = max(versions.keys())
                    self.active_versions[task_type] = latest

            self.logger.info(f"Reloaded {sum(len(v) for v in self.prompts.values())} prompts across {len(self.prompts)} task types")
            return True

        except Exception as e:
            self.logger.error(f"Failed to reload prompts: {e}")
            return False

    def get_prompt(
        self,
        task_type: str,
        version: str = None,
        **variables
    ) -> Optional[str]:
        """
        Get rendered prompt for task type

        Args:
            task_type: Type of task (e.g., 'sigma_enhancement', 'log_parsing')
            version: Specific version to use (uses active version if None)
            **variables: Variables to substitute in template

        Returns:
            Rendered prompt string or None if not found
        """
        # Get task prompts
        if task_type not in self.prompts:
            self.logger.warning(f"No prompts found for task type: {task_type}")
            return None

        # Determine version to use
        if version is None:
            version = self.active_versions.get(task_type)
            if not version:
                self.logger.warning(f"No active version for task type: {task_type}")
                return None

        # Get template
        template = self.prompts[task_type].get(version)
        if not template:
            self.logger.warning(f"Prompt version not found: {task_type} v{version}")
            return None

        # Validate variables
        if not template.validate_variables(**variables):
            missing = set(template.variables) - set(variables.keys())
            self.logger.warning(f"Missing required variables for {task_type}: {missing}")
            return None

        # Render and return
        rendered = template.render(**variables)
        self._record_prompt_use(task_type, version)
        return rendered

    def get_template(self, task_type: str, version: str = None) -> Optional[PromptTemplate]:
        """
        Get prompt template object

        Args:
            task_type: Type of task
            version: Specific version (uses active if None)

        Returns:
            PromptTemplate object or None
        """
        if task_type not in self.prompts:
            return None

        if version is None:
            version = self.active_versions.get(task_type)

        return self.prompts[task_type].get(version)

    def list_prompts(self, task_type: str = None) -> List[Dict[str, Any]]:
        """
        List all available prompts

        Args:
            task_type: Filter by task type (optional)

        Returns:
            List of prompt metadata
        """
        results = []

        tasks_to_list = [task_type] if task_type else self.prompts.keys()

        for task in tasks_to_list:
            if task not in self.prompts:
                continue

            for version, template in self.prompts[task].items():
                results.append({
                    'name': template.name,
                    'task_type': template.task_type,
                    'version': template.version,
                    'is_active': version == self.active_versions.get(task),
                    'variables': template.variables,
                    'metadata': template.metadata
                })

        return results

    def set_active_version(self, task_type: str, version: str) -> bool:
        """
        Set active version for task type

        Args:
            task_type: Type of task
            version: Version to activate

        Returns:
            True if successful
        """
        if task_type not in self.prompts:
            self.logger.warning(f"Task type not found: {task_type}")
            return False

        if version not in self.prompts[task_type]:
            self.logger.warning(f"Version not found: {task_type} v{version}")
            return False

        self.active_versions[task_type] = version
        self.logger.info(f"Set active version for {task_type}: v{version}")
        return True

    def _record_prompt_use(self, task_type: str, version: str):
        """Record prompt usage for tracking"""
        key = f"{task_type}:{version}"
        if key not in self.prompt_stats:
            self.prompt_stats[key] = {
                'use_count': 0,
                'first_used': datetime.now(),
                'last_used': None
            }

        self.prompt_stats[key]['use_count'] += 1
        self.prompt_stats[key]['last_used'] = datetime.now()

    def record_prompt_effectiveness(
        self,
        task_type: str,
        version: str,
        success: bool,
        metrics: Dict[str, Any] = None
    ):
        """
        Record prompt effectiveness metrics

        Args:
            task_type: Type of task
            version: Prompt version used
            success: Whether prompt was effective
            metrics: Additional metrics (quality score, time, etc.)
        """
        key = f"{task_type}:{version}"
        if key not in self.prompt_stats:
            self.prompt_stats[key] = {
                'use_count': 0,
                'success_count': 0,
                'failure_count': 0,
                'metrics': []
            }

        if success:
            self.prompt_stats[key]['success_count'] = self.prompt_stats[key].get('success_count', 0) + 1
        else:
            self.prompt_stats[key]['failure_count'] = self.prompt_stats[key].get('failure_count', 0) + 1

        if metrics:
            self.prompt_stats[key].setdefault('metrics', []).append({
                'timestamp': datetime.now().isoformat(),
                'success': success,
                'data': metrics
            })

        # Store in database if available
        if self.db:
            self._store_effectiveness_in_db(task_type, version, success, metrics)

    def get_prompt_statistics(self, task_type: str = None) -> Dict[str, Any]:
        """
        Get usage statistics for prompts

        Args:
            task_type: Filter by task type (optional)

        Returns:
            Dictionary with prompt statistics
        """
        if task_type:
            # Filter stats for specific task
            return {
                k: v for k, v in self.prompt_stats.items()
                if k.startswith(f"{task_type}:")
            }
        else:
            return self.prompt_stats.copy()

    def _store_effectiveness_in_db(self, task_type: str, version: str, success: bool, metrics: Dict[str, Any]):
        """Store effectiveness data in PostgreSQL"""
        if not self.db:
            return

        try:
            self.db.execute(
                """
                INSERT INTO prompt_effectiveness
                (task_type, version, success, metrics, recorded_at)
                VALUES ($1, $2, $3, $4, NOW())
                """,
                task_type, version, success, metrics or {}
            )
        except Exception as e:
            self.logger.error(f"Failed to store prompt effectiveness: {e}")

    def __repr__(self) -> str:
        return f"PromptLibrary(tasks={len(self.prompts)}, prompts={sum(len(v) for v in self.prompts.values())})"
