#!/bin/bash

# SIEMLess Keycloak Setup Script
# Sets up local authentication with Keycloak

set -e

echo "======================================"
echo "SIEMLess Keycloak Setup"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo -e "${YELLOW}Creating .env file...${NC}"
    cat > .env << EOF
# Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=siemless_v2
POSTGRES_USER=siemless
POSTGRES_PASSWORD=siemless123

# Redis
REDIS_HOST=localhost
REDIS_PORT=6380
REDIS_PASSWORD=redis123
REDIS_SESSION_PASSWORD=session123

# Keycloak
KEYCLOAK_ADMIN=admin
KEYCLOAK_ADMIN_PASSWORD=admin123
KEYCLOAK_HOSTNAME=localhost
KEYCLOAK_PORT=8080

# Development
ENABLE_DEV_API_KEYS=true
EOF
    echo -e "${GREEN}Created .env file${NC}"
fi

# Step 1: Start PostgreSQL if not running
echo -e "\n${YELLOW}Step 1: Checking PostgreSQL...${NC}"
if ! docker ps | grep -q "siemless-postgres"; then
    echo "Starting PostgreSQL..."
    docker-compose up -d postgres
    sleep 10
fi

# Step 2: Create Keycloak database
echo -e "\n${YELLOW}Step 2: Creating Keycloak database...${NC}"
cd engines
docker-compose exec -T postgres psql -U siemless -d postgres << EOF
CREATE DATABASE keycloak;
EOF
echo -e "${GREEN}Keycloak database created (or already exists)${NC}"

# Step 3: Start Keycloak
echo -e "\n${YELLOW}Step 3: Starting Keycloak...${NC}"
docker-compose -f docker-compose.keycloak.yml up -d keycloak
cd ..
echo "Waiting for Keycloak to start (60 seconds)..."
sleep 60

# Step 4: Check Keycloak health
echo -e "\n${YELLOW}Step 4: Checking Keycloak health...${NC}"
for i in {1..10}; do
    if curl -s http://localhost:8080/health/ready | grep -q "UP"; then
        echo -e "${GREEN}Keycloak is ready!${NC}"
        break
    else
        echo "Waiting for Keycloak... (attempt $i/10)"
        sleep 5
    fi
done

# Step 5: Start Redis session store
echo -e "\n${YELLOW}Step 5: Starting Redis session store...${NC}"
cd engines
docker-compose -f docker-compose.keycloak.yml up -d redis-sessions
cd ..
echo -e "${GREEN}Redis session store started${NC}"

# Step 6: Display access information
echo -e "\n${GREEN}======================================"
echo "Keycloak Setup Complete!"
echo "======================================${NC}"
echo ""
echo "Access URLs:"
echo -e "${GREEN}Keycloak Admin Console:${NC} http://localhost:8080/admin"
echo -e "${GREEN}Username:${NC} admin"
echo -e "${GREEN}Password:${NC} admin123"
echo ""
echo "Test Users (Local Authentication):"
echo "-----------------------------------"
echo -e "${YELLOW}Admin User:${NC}"
echo "  Username: admin"
echo "  Password: admin123"
echo "  Role: siemless-admin (full access)"
echo ""
echo -e "${YELLOW}Analyst User:${NC}"
echo "  Username: analyst1"
echo "  Password: analyst123"
echo "  Role: siemless-analyst (investigation access)"
echo ""
echo -e "${YELLOW}Engineer User:${NC}"
echo "  Username: engineer1"
echo "  Password: engineer123"
echo "  Role: siemless-engineer (pattern management)"
echo ""
echo -e "${YELLOW}Viewer User:${NC}"
echo "  Username: viewer1"
echo "  Password: viewer123"
echo "  Role: siemless-viewer (read-only)"
echo ""
echo "Development API Keys:"
echo "-------------------"
echo "Admin:    dev-admin-key"
echo "Analyst:  dev-analyst-key"
echo "Engineer: dev-engineer-key"
echo "Viewer:   dev-viewer-key"
echo ""
echo -e "${GREEN}Next Steps:${NC}"
echo "1. Access Keycloak admin console"
echo "2. Review the 'siemless' realm configuration"
echo "3. Test authentication with: ./test_auth.sh"
echo "4. When ready, add Azure AD and Google SSO providers"