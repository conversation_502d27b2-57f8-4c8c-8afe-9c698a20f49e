"""
Ingestion Engine - Entry point for all logs into SIEMLess v2.0

This engine handles:
- Multiple input sources (syslog, HTTP, file, Kafka)
- Initial validation and deduplication
- Rate limiting and backpressure
- Intelligent routing to appropriate parser
"""
import asyncio
import json
import socket
import hashlib
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
import time
from collections import deque
from dataclasses import dataclass
import re

# Add v2 to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.base import BaseEngine
from .cti_integration import CTIProcessor, CTISource, CTISourceType


@dataclass
class IngestionStats:
    """Track ingestion metrics"""
    total_received: int = 0
    total_processed: int = 0
    total_duplicates: int = 0
    total_errors: int = 0
    total_routed: int = 0
    bytes_processed: int = 0
    start_time: datetime = None

    def __post_init__(self):
        if self.start_time is None:
            self.start_time = datetime.utcnow()

    def get_rate(self) -> float:
        """Calculate logs per second"""
        elapsed = (datetime.utcnow() - self.start_time).total_seconds()
        return self.total_processed / elapsed if elapsed > 0 else 0


class IngestionEngine(BaseEngine):
    """
    Ingestion Engine: High-performance log collection and routing

    Accepts logs from multiple sources and routes to appropriate parser.
    Implements deduplication, rate limiting, and intelligent routing.
    """

    def __init__(self):
        super().__init__('ingestion', '2.0.0')

        # Ingestion configuration
        self.config = {
            'syslog_port': 514,
            'http_port': 8514,
            'max_batch_size': 100,
            'batch_timeout_ms': 1000,
            'dedup_window_size': 10000,
            'rate_limit_per_second': 10000,
            'max_queue_size': 100000
        }

        # Deduplication cache (circular buffer)
        self.dedup_cache = deque(maxlen=self.config['dedup_window_size'])

        # Rate limiting
        self.rate_limiter = {
            'tokens': self.config['rate_limit_per_second'],
            'last_refill': time.time()
        }

        # Processing queue
        self.processing_queue = asyncio.Queue(maxsize=self.config['max_queue_size'])

        # Batch buffer
        self.batch_buffer = []
        self.last_batch_time = time.time()

        # Statistics
        self.stats = IngestionStats()

        # CTI Integration
        self.cti_processor = CTIProcessor()
        self.cti_enabled = False
        self.cti_sync_interval = 3600  # 1 hour

        # Log type detection patterns (from v1 experience)
        self.log_patterns = {
            'crowdstrike': [
                r'"detection_id":\s*"[^"]+',
                r'"behaviors":\s*\[',
                r'"device":\s*{.*"hostname"',
                r'"max_severity":\s*\d+'
            ],
            'paloalto': [
                r'"rule":\s*{.*"name"',
                r'"source":\s*{.*"ip"',
                r'"destination":\s*{.*"ip"',
                r'"panw\.panos\.'
            ],
            'fortinet': [
                r'"fortinet\.firewall\.',
                r'"policy_id":\s*\d+',
                r'"observer":\s*{.*"hostname"',
                r'"attack_name":\s*"[^"]+'
            ],
            'tippingpoint': [
                r'"tippingpoint\.ips\.',
                r'"attack_id":\s*"[^"]+',
                r'"signature_name":\s*"[^"]+',
                r'"zone_name":\s*"[^"]+'
            ],
            'cisco_asa': [
                r'%ASA-\d+-\d+:',
                r'(Built|Teardown)\s+(inbound|outbound)',
                r'access-group\s+\w+',
                r'(src|dst)\s+\d+\.\d+\.\d+\.\d+'
            ],
            'generic_syslog': [
                r'^\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2}',
                r'<\d+>',
                r'\w+\[\d+\]:',
                r'(info|warning|error|critical)'
            ]
        }

        # Input handlers
        self.input_handlers = {
            'syslog': self._handle_syslog,
            'http': self._handle_http,
            'file': self._handle_file,
            'kafka': self._handle_kafka
        }

    async def start(self):
        """Start all ingestion services"""
        self.logger.log('engine_start', {'engine': self.name, 'version': self.version})

        # Initialize CTI sources
        await self._initialize_cti()

        # Start input listeners
        tasks = [
            asyncio.create_task(self._start_syslog_listener()),
            asyncio.create_task(self._start_http_listener()),
            asyncio.create_task(self._start_batch_processor()),
            asyncio.create_task(self._start_stats_reporter())
        ]

        # Add CTI sync task if enabled
        if self.cti_enabled:
            tasks.append(asyncio.create_task(self._start_cti_sync_scheduler()))

        # Also start base message queue processor
        tasks.append(asyncio.create_task(super().start()))

        await asyncio.gather(*tasks)

    async def _start_syslog_listener(self):
        """Start UDP syslog listener"""
        try:
            # Create UDP socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.bind(('0.0.0.0', self.config['syslog_port']))
            sock.setblocking(False)

            self.logger.log('syslog_listener_started', {
                'port': self.config['syslog_port']
            })

            while True:
                try:
                    data, addr = await asyncio.get_event_loop().sock_recvfrom(sock, 65536)
                    log_data = data.decode('utf-8', errors='ignore')

                    await self._ingest_log({
                        'source': 'syslog',
                        'source_ip': addr[0],
                        'raw': log_data,
                        'timestamp': datetime.utcnow().isoformat()
                    })

                except Exception as e:
                    self.logger.log_error(e, {'source': 'syslog_listener'})
                    await asyncio.sleep(0.1)

        except Exception as e:
            self.logger.log_error(e, {'component': 'syslog_listener'})

    async def _start_http_listener(self):
        """Start HTTP endpoint for log ingestion"""
        # Simplified HTTP listener for MVP
        # In production, would use aiohttp or FastAPI
        self.logger.log('http_listener_started', {
            'port': self.config['http_port']
        })

    async def _start_batch_processor(self):
        """Process logs in batches for efficiency"""
        while True:
            try:
                # Check if batch is ready
                current_time = time.time()
                batch_age_ms = (current_time - self.last_batch_time) * 1000

                if (len(self.batch_buffer) >= self.config['max_batch_size'] or
                    (len(self.batch_buffer) > 0 and batch_age_ms >= self.config['batch_timeout_ms'])):

                    # Process batch
                    await self._process_batch(self.batch_buffer[:])
                    self.batch_buffer.clear()
                    self.last_batch_time = current_time

                # Small sleep to prevent CPU spinning
                await asyncio.sleep(0.01)

            except Exception as e:
                self.logger.log_error(e, {'component': 'batch_processor'})
                await asyncio.sleep(1)

    async def _start_stats_reporter(self):
        """Report ingestion statistics periodically"""
        while True:
            await asyncio.sleep(60)  # Report every minute

            self.logger.log('ingestion_stats', {
                'total_received': self.stats.total_received,
                'total_processed': self.stats.total_processed,
                'total_duplicates': self.stats.total_duplicates,
                'total_errors': self.stats.total_errors,
                'total_routed': self.stats.total_routed,
                'bytes_processed': self.stats.bytes_processed,
                'rate_per_second': self.stats.get_rate(),
                'queue_size': self.processing_queue.qsize()
            })

    async def _ingest_log(self, log_entry: Dict[str, Any]):
        """Ingest a single log entry"""
        try:
            self.stats.total_received += 1

            # Rate limiting
            if not self._check_rate_limit():
                self.logger.log('rate_limit_exceeded', {'dropped': True})
                return

            # Deduplication
            log_hash = self._calculate_hash(log_entry.get('raw', ''))
            if log_hash in self.dedup_cache:
                self.stats.total_duplicates += 1
                return

            self.dedup_cache.append(log_hash)

            # Add to batch
            self.batch_buffer.append(log_entry)
            self.stats.bytes_processed += len(log_entry.get('raw', ''))

        except Exception as e:
            self.stats.total_errors += 1
            self.logger.log_error(e, {'log_entry': log_entry})

    async def _process_batch(self, batch: List[Dict[str, Any]]):
        """Process a batch of logs"""
        if not batch:
            return

        for log_entry in batch:
            try:
                # Detect log type
                log_type = self._detect_log_type(log_entry.get('raw', ''))
                log_entry['detected_type'] = log_type

                # Parse if it's JSON
                if log_entry['raw'].strip().startswith('{'):
                    try:
                        log_entry['parsed'] = json.loads(log_entry['raw'])
                    except:
                        pass

                # Route to appropriate parser
                await self._route_to_parser(log_entry)

                self.stats.total_processed += 1
                self.stats.total_routed += 1

            except Exception as e:
                self.stats.total_errors += 1
                self.logger.log_error(e, {'log_entry': log_entry})

    def _detect_log_type(self, raw_log: str) -> str:
        """Detect the type of log based on patterns"""
        for log_type, patterns in self.log_patterns.items():
            matches = sum(1 for pattern in patterns if re.search(pattern, raw_log, re.IGNORECASE))
            if matches >= 2:  # Need at least 2 pattern matches
                return log_type

        # Check if it's JSON
        if raw_log.strip().startswith('{'):
            try:
                data = json.loads(raw_log)
                # Additional JSON-based detection
                if 'detection_id' in data and 'behaviors' in data:
                    return 'crowdstrike'
                elif 'panw.panos' in str(data):
                    return 'paloalto'
                elif 'fortinet.firewall' in str(data):
                    return 'fortinet'
            except:
                pass

        return 'unknown'

    async def _route_to_parser(self, log_entry: Dict[str, Any]):
        """Route log to appropriate parser engine"""
        message = {
            'id': self._calculate_hash(log_entry.get('raw', '')),
            'source_engine': self.name,
            'target_engine': 'parser',
            'timestamp': datetime.utcnow().isoformat(),
            'data': log_entry
        }

        # Send to parser via message queue
        await self.message_queue.publish('parser', message)

        # Log routing decision
        self.logger.log_decision(
            'route_to_parser',
            {'log_type': log_entry.get('detected_type')},
            {'target': 'parser'},
            reasoning=f"Detected log type: {log_entry.get('detected_type')}",
            confidence=0.9
        )

    def _check_rate_limit(self) -> bool:
        """Token bucket rate limiting"""
        current_time = time.time()
        time_passed = current_time - self.rate_limiter['last_refill']

        # Refill tokens
        tokens_to_add = int(time_passed * self.config['rate_limit_per_second'])
        self.rate_limiter['tokens'] = min(
            self.config['rate_limit_per_second'],
            self.rate_limiter['tokens'] + tokens_to_add
        )
        self.rate_limiter['last_refill'] = current_time

        # Check if we have tokens
        if self.rate_limiter['tokens'] >= 1:
            self.rate_limiter['tokens'] -= 1
            return True

        return False

    def _calculate_hash(self, data: str) -> str:
        """Calculate hash for deduplication"""
        return hashlib.md5(data.encode()).hexdigest()

    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Process control messages from other engines"""
        command = message.get('command')

        if command == 'ingest_file':
            # Handle file ingestion request
            file_path = message.get('data', {}).get('file_path')
            return await self._handle_file({'path': file_path})

        elif command == 'get_stats':
            # Return current statistics
            return {
                'success': True,
                'stats': {
                    'total_received': self.stats.total_received,
                    'total_processed': self.stats.total_processed,
                    'total_duplicates': self.stats.total_duplicates,
                    'total_errors': self.stats.total_errors,
                    'rate_per_second': self.stats.get_rate(),
                    'queue_size': self.processing_queue.qsize()
                }
            }

        return {'success': False, 'error': f'Unknown command: {command}'}

    async def _handle_syslog(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Handle syslog input"""
        # Implemented in _start_syslog_listener
        return {'success': True, 'message': 'Syslog listener active'}

    async def _handle_http(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Handle HTTP input"""
        # Would implement HTTP endpoint
        return {'success': True, 'message': 'HTTP endpoint active'}

    async def _handle_file(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Handle file input"""
        file_path = Path(config.get('path', ''))

        if not file_path.exists():
            return {'success': False, 'error': f'File not found: {file_path}'}

        try:
            with open(file_path, 'r') as f:
                # Handle both single JSON and JSONL formats
                content = f.read()

                # Try as single JSON first
                try:
                    data = json.loads(content)
                    if isinstance(data, list):
                        # Array of logs
                        for log in data:
                            await self._ingest_log({
                                'source': 'file',
                                'file_path': str(file_path),
                                'raw': json.dumps(log),
                                'timestamp': datetime.utcnow().isoformat()
                            })
                    else:
                        # Single log
                        await self._ingest_log({
                            'source': 'file',
                            'file_path': str(file_path),
                            'raw': json.dumps(data),
                            'timestamp': datetime.utcnow().isoformat()
                        })
                except json.JSONDecodeError:
                    # Try as JSONL
                    for line in content.strip().split('\n'):
                        if line.strip():
                            await self._ingest_log({
                                'source': 'file',
                                'file_path': str(file_path),
                                'raw': line,
                                'timestamp': datetime.utcnow().isoformat()
                            })

            return {
                'success': True,
                'message': f'File ingested: {file_path}',
                'stats': {
                    'total_processed': self.stats.total_processed
                }
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    async def _handle_kafka(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Kafka input"""
        # Would implement Kafka consumer
        return {'success': True, 'message': 'Kafka consumer would be started'}

    def get_capabilities(self) -> Dict[str, Any]:
        """Return ingestion engine capabilities"""
        capabilities = [
            'syslog_ingestion',
            'http_ingestion',
            'file_ingestion',
            'kafka_ingestion',
            'deduplication',
            'rate_limiting',
            'batch_processing',
            'intelligent_routing'
        ]

        # Add CTI capabilities if enabled
        if self.cti_enabled:
            capabilities.extend([
                'cti_integration',
                'opencti_ingestion',
                'otx_ingestion',
                'threat_intel_enrichment',
                'ioc_extraction'
            ])

        return {
            'engine': 'ingestion',
            'version': self.version,
            'capabilities': capabilities,
            'supported_log_types': list(self.log_patterns.keys()),
            'cti_status': self.get_cti_status() if hasattr(self, 'cti_processor') else None,
            'configuration': self.config,
            'statistics': {
                'total_received': self.stats.total_received,
                'total_processed': self.stats.total_processed,
                'total_duplicates': self.stats.total_duplicates,
                'rate_per_second': self.stats.get_rate()
            }
        }

    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate ingestion configuration"""
        required_fields = ['syslog_port', 'http_port', 'max_batch_size']
        return all(field in config for field in required_fields)

    # CTI Integration Methods
    async def _initialize_cti(self):
        """Initialize CTI sources from environment variables"""
        try:
            await self.cti_processor.setup_default_sources()

            if len(self.cti_processor.sources) > 0:
                self.cti_enabled = True
                self.logger.log('cti_initialized', {
                    'sources_count': len(self.cti_processor.sources),
                    'source_types': [s.source_type.value for s in self.cti_processor.sources]
                })
            else:
                self.logger.log('cti_disabled', {
                    'reason': 'No CTI sources configured',
                    'help': 'Set OPENCTI_URL/OPENCTI_TOKEN or OTX_API_KEY environment variables'
                })

        except Exception as e:
            self.logger.log_error(e, {'component': 'cti_initialization'})

    async def _start_cti_sync_scheduler(self):
        """Start CTI sync scheduler"""
        while True:
            try:
                self.logger.log('cti_sync_start', {'interval_hours': self.cti_sync_interval / 3600})

                # Sync all CTI sources
                sync_results = await self.cti_processor.sync_all_sources(hours_back=24)

                self.logger.log('cti_sync_complete', sync_results)

                # Convert CTI entries to enrichment data and send to Context Engine
                if sync_results['total_entries'] > 0:
                    await self._send_cti_enrichment()

                # Wait for next sync
                await asyncio.sleep(self.cti_sync_interval)

            except Exception as e:
                self.logger.log_error(e, {'component': 'cti_sync_scheduler'})
                await asyncio.sleep(300)  # Wait 5 minutes on error

    async def _send_cti_enrichment(self):
        """Send CTI data as enrichment to Context Engine"""
        try:
            # Get all cached CTI data
            cti_cache = self.cti_processor.get_cached_cti_data()

            for source_id, source_data in cti_cache.items():
                entries = source_data.get('entries', [])
                if entries:
                    # Convert to enrichment format
                    enrichment_data = self.cti_processor.convert_to_enrichment_data(entries)

                    # Send to Context Engine via message queue
                    enrichment_message = {
                        'type': 'cti_enrichment',
                        'source_id': source_id,
                        'source_name': source_data.get('source', {}).name,
                        'enrichment_data': enrichment_data,
                        'timestamp': datetime.utcnow().isoformat(),
                        'entry_count': len(entries)
                    }

                    await self.send_to_queue('enrichment_updates', enrichment_message)

                    self.logger.log('cti_enrichment_sent', {
                        'source_id': source_id,
                        'entry_count': len(entries),
                        'ioc_count': enrichment_data['threat_intel']['ioc_matches']
                    })

        except Exception as e:
            self.logger.log_error(e, {'component': 'cti_enrichment_sender'})

    async def sync_cti_on_demand(self, hours_back: int = 24) -> Dict[str, Any]:
        """Manually trigger CTI sync (for API calls)"""
        if not self.cti_enabled:
            return {
                'success': False,
                'error': 'CTI integration not enabled',
                'help': 'Configure CTI sources via environment variables'
            }

        try:
            results = await self.cti_processor.sync_all_sources(hours_back)

            # Send enrichment data if we got results
            if results['total_entries'] > 0:
                await self._send_cti_enrichment()

            return {
                'success': True,
                'sync_results': results,
                'enrichment_sent': results['total_entries'] > 0
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def get_cti_status(self) -> Dict[str, Any]:
        """Get CTI integration status"""
        return {
            'cti_enabled': self.cti_enabled,
            'sources_configured': len(self.cti_processor.sources),
            'sources': [
                {
                    'source_id': s.source_id,
                    'name': s.name,
                    'type': s.source_type.value,
                    'active': s.is_active,
                    'last_sync': s.last_sync.isoformat() if s.last_sync else None
                }
                for s in self.cti_processor.sources
            ],
            'cached_data': {
                source_id: {
                    'entry_count': len(data.get('entries', [])),
                    'last_sync': data.get('last_sync', {}).isoformat() if data.get('last_sync') else None
                }
                for source_id, data in self.cti_processor.cache.items()
            }
        }


async def main():
    """Main entry point for Ingestion Engine"""
    engine = IngestionEngine()

    # Log capabilities
    capabilities = engine.get_capabilities()
    engine.logger.log('engine_capabilities', capabilities)

    # Example: Test file ingestion
    test_result = await engine._handle_file({
        'path': 'v2/tests/data/sample_logs.json'
    })
    print(f"File ingestion test: {test_result}")

    # Start the engine
    await engine.start()


if __name__ == '__main__':
    asyncio.run(main())