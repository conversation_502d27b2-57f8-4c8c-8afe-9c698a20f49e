"""
Credential Manager - Hot-reloadable API key management
Handles credential loading, rotation, validation, and security
"""

import yaml
import os
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import threading
from collections import defaultdict


class CredentialManager:
    """
    Manages API credentials with hot-reload and rotation support

    Features:
    - Load credentials from YAML or environment variables
    - Hot-reload on config changes
    - Key rotation for rate limit handling
    - Key validation
    - Secure key masking in logs
    - Usage tracking per key
    """

    def __init__(self, config_path: Optional[str] = None, logger: logging.Logger = None):
        """
        Initialize Credential Manager

        Args:
            config_path: Path to api_credentials.yaml (optional, can use env vars only)
            logger: Logger instance
        """
        self.config_path = config_path
        self.logger = logger or logging.getLogger(__name__)

        # Credentials storage
        self.credentials: Dict[str, Dict[str, Any]] = {}
        self.rotation_config: Dict[str, Any] = {}
        self.validation_config: Dict[str, Any] = {}
        self.security_config: Dict[str, Any] = {}

        # Rotation state
        self._current_key_index: Dict[str, int] = defaultdict(int)
        self._key_usage_count: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        self._failed_keys: Dict[str, set] = defaultdict(set)

        self._last_reload = None
        self._reload_lock = threading.Lock()

        # Initial load
        self.reload_credentials()

    def reload_credentials(self) -> bool:
        """
        Hot-reload credentials from config and environment

        Returns:
            True if successful, False otherwise
        """
        with self._reload_lock:
            try:
                self.logger.info("Reloading API credentials")

                new_credentials = {}

                # Load from config file if provided
                if self.config_path and os.path.exists(self.config_path):
                    with open(self.config_path, 'r') as f:
                        config = yaml.safe_load(f)

                    # Parse credentials
                    for provider, cred_data in config.get('credentials', {}).items():
                        primary_key = self._resolve_env_var(cred_data.get('primary', ''))
                        secondary_keys = [
                            self._resolve_env_var(k)
                            for k in cred_data.get('secondary', [])
                        ]

                        # Filter out empty keys
                        all_keys = [k for k in [primary_key] + secondary_keys if k]

                        if not all_keys:
                            self.logger.warning(f"No valid keys for provider: {provider}")
                            continue

                        new_credentials[provider] = {
                            'keys': all_keys,
                            'endpoint': cred_data.get('endpoint'),
                            'metadata': {
                                'organization': cred_data.get('organization')
                            }
                        }

                    # Load configs
                    self.rotation_config = config.get('rotation', {})
                    self.validation_config = config.get('validation', {})
                    self.security_config = config.get('security', {})

                else:
                    # Load from environment only
                    self.logger.info("No config file, loading from environment variables")
                    env_mapping = {
                        'google': 'GOOGLE_API_KEY',
                        'anthropic': 'ANTHROPIC_API_KEY',
                        'openai': 'OPENAI_API_KEY'
                    }

                    for provider, env_var in env_mapping.items():
                        key = os.getenv(env_var)
                        if key:
                            new_credentials[provider] = {
                                'keys': [key],
                                'endpoint': None,
                                'metadata': {}
                            }

                    # Default configs
                    self.rotation_config = {'enabled': True, 'strategy': 'round_robin'}
                    self.validation_config = {'validate_on_load': False}
                    self.security_config = {'mask_in_logs': True}

                # Validate credentials if enabled
                if self.validation_config.get('validate_on_load', False):
                    new_credentials = self._validate_credentials(new_credentials)

                # Update credentials
                self.credentials = new_credentials
                self._last_reload = datetime.now()

                self.logger.info(f"Successfully loaded credentials for {len(self.credentials)} providers")
                return True

            except Exception as e:
                self.logger.error(f"Failed to reload credentials: {e}")
                return False

    def _resolve_env_var(self, value: str) -> str:
        """
        Resolve environment variable placeholders

        ${VAR_NAME} -> value of VAR_NAME
        """
        if not value:
            return ""

        if value.startswith('${') and value.endswith('}'):
            env_var = value[2:-1]
            return os.getenv(env_var, "")

        return value

    def get_credential(self, provider: str) -> Optional[str]:
        """
        Get API key for provider with rotation support

        Args:
            provider: Provider name (google, anthropic, openai, etc.)

        Returns:
            API key or None if not found
        """
        if provider not in self.credentials:
            self.logger.error(f"No credentials for provider: {provider}")
            return None

        cred_data = self.credentials[provider]
        keys = cred_data['keys']

        if not keys:
            return None

        # Rotation strategy
        strategy = self.rotation_config.get('strategy', 'round_robin')

        if strategy == 'round_robin':
            # Round-robin through available keys
            key = self._get_next_key_round_robin(provider, keys)
        elif strategy == 'least_used':
            # Use least-used key
            key = self._get_least_used_key(provider, keys)
        elif strategy == 'failover':
            # Use first working key
            key = self._get_failover_key(provider, keys)
        else:
            # Default: first key
            key = keys[0]

        # Track usage
        if self.rotation_config.get('track_usage', True):
            self._key_usage_count[provider][key] += 1

        return key

    def _get_next_key_round_robin(self, provider: str, keys: List[str]) -> str:
        """Get next key using round-robin"""
        # Filter out failed keys
        available_keys = [k for k in keys if k not in self._failed_keys[provider]]

        if not available_keys:
            # All keys failed, reset and try again
            self.logger.warning(f"All keys failed for {provider}, resetting failures")
            self._failed_keys[provider].clear()
            available_keys = keys

        # Get current index
        current_idx = self._current_key_index[provider]
        key = available_keys[current_idx % len(available_keys)]

        # Increment for next call
        self._current_key_index[provider] = (current_idx + 1) % len(available_keys)

        return key

    def _get_least_used_key(self, provider: str, keys: List[str]) -> str:
        """Get least-used key"""
        usage = self._key_usage_count[provider]
        available_keys = [k for k in keys if k not in self._failed_keys[provider]]

        if not available_keys:
            available_keys = keys

        # Sort by usage count
        sorted_keys = sorted(available_keys, key=lambda k: usage.get(k, 0))
        return sorted_keys[0]

    def _get_failover_key(self, provider: str, keys: List[str]) -> str:
        """Get first non-failed key"""
        for key in keys:
            if key not in self._failed_keys[provider]:
                return key

        # All failed, reset and return first
        self._failed_keys[provider].clear()
        return keys[0]

    def rotate_key(self, provider: str):
        """
        Manually rotate to next key for provider

        Args:
            provider: Provider name
        """
        if provider in self.credentials:
            keys = self.credentials[provider]['keys']
            if len(keys) > 1:
                self._current_key_index[provider] += 1
                self.logger.info(f"Manually rotated key for {provider}")

    def mark_key_failed(self, provider: str, key: str):
        """
        Mark a key as failed (will be skipped in rotation)

        Args:
            provider: Provider name
            key: The failed API key
        """
        self._failed_keys[provider].add(key)
        self.logger.warning(f"Marked key as failed for {provider}")

    def mark_key_working(self, provider: str, key: str):
        """
        Mark a previously failed key as working again

        Args:
            provider: Provider name
            key: The working API key
        """
        if key in self._failed_keys[provider]:
            self._failed_keys[provider].remove(key)
            self.logger.info(f"Marked key as working again for {provider}")

    def _validate_credentials(self, credentials: Dict) -> Dict:
        """
        Validate credentials by attempting simple API calls

        Args:
            credentials: Credentials dict to validate

        Returns:
            Validated credentials (with failed keys removed)
        """
        # TODO: Implement actual validation calls
        # For now, just return as-is
        self.logger.info("Credential validation not yet implemented")
        return credentials

    def mask_key(self, key: str) -> str:
        """
        Mask API key for safe logging

        Args:
            key: Full API key

        Returns:
            Masked key (e.g., "sk-...xyz123")
        """
        if not key or not self.security_config.get('mask_in_logs', True):
            return key

        if len(key) <= 12:
            return "*" * len(key)

        return f"{key[:8]}...{key[-4:]}"

    def get_endpoint(self, provider: str) -> Optional[str]:
        """Get API endpoint for provider"""
        if provider in self.credentials:
            return self.credentials[provider].get('endpoint')
        return None

    def get_metadata(self, provider: str) -> Dict[str, Any]:
        """Get metadata for provider (e.g., organization ID)"""
        if provider in self.credentials:
            return self.credentials[provider].get('metadata', {})
        return {}

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get credential usage statistics"""
        stats = {}
        for provider, usage in self._key_usage_count.items():
            stats[provider] = {
                'total_calls': sum(usage.values()),
                'keys_used': len(usage),
                'failed_keys': len(self._failed_keys[provider])
            }
        return stats

    def has_credentials(self, provider: str) -> bool:
        """Check if credentials exist for provider"""
        return provider in self.credentials and len(self.credentials[provider]['keys']) > 0

    def list_providers(self) -> List[str]:
        """List all providers with credentials"""
        return list(self.credentials.keys())

    def __repr__(self) -> str:
        return f"CredentialManager({len(self.credentials)} providers)"
