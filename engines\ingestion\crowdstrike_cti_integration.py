"""
CrowdStrike CTI Integration
Fetches threat intelligence from CrowdStrike INTEL and IOCS scopes
Separate from EDR log ingestion - these are CTI feeds
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class CrowdStrikeCTIConnector:
    """
    Connects to CrowdStrike CTI feeds (NOT EDR logs)

    Two CTI scopes:
    1. INTEL_READ - Threat intelligence indicators and reports
    2. IOCS_READ - Custom IOCs (Indicators of Compromise)
    """

    def __init__(self, client_id: str, client_secret: str, base_url: str = "https://api.crowdstrike.com"):
        self.client_id = client_id
        self.client_secret = client_secret
        self.base_url = base_url
        self.access_token = None
        self.token_expiry = None

    async def _get_access_token(self) -> bool:
        """Authenticate with CrowdStrike API"""
        try:
            # Check if token is still valid
            if self.access_token and self.token_expiry:
                if datetime.utcnow() < self.token_expiry:
                    return True

            # Request new token
            auth_url = f"{self.base_url}/oauth2/token"

            async with aiohttp.ClientSession() as session:
                data = {
                    'client_id': self.client_id,
                    'client_secret': self.client_secret
                }

                async with session.post(auth_url, data=data) as response:
                    if response.status == 201:
                        token_data = await response.json()
                        self.access_token = token_data['access_token']
                        expires_in = token_data.get('expires_in', 1800)
                        self.token_expiry = datetime.utcnow() + timedelta(seconds=expires_in - 60)
                        logger.info("CrowdStrike CTI authentication successful")
                        return True
                    else:
                        logger.error(f"CrowdStrike CTI auth failed: {response.status}")
                        return False

        except Exception as e:
            logger.error(f"CrowdStrike CTI authentication error: {e}")
            return False

    async def test_connection(self) -> bool:
        """Test connection to CrowdStrike CTI API"""
        try:
            authenticated = await self._get_access_token()
            if not authenticated:
                return False

            # Test with a simple query
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                # Test INTEL scope with simple query
                url = f"{self.base_url}/intel/combined/indicators/v1"
                params = {'limit': 1}

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        logger.info("CrowdStrike CTI connection successful")
                        return True
                    else:
                        logger.warning(f"CrowdStrike CTI test returned status {response.status}")
                        return False

        except Exception as e:
            logger.error(f"CrowdStrike CTI connection test failed: {e}")
            return False

    async def get_intel_indicators(self, limit: int = 100, filter_query: str = None) -> List[Dict]:
        """
        Fetch threat intelligence indicators from INTEL_READ scope

        Args:
            limit: Maximum indicators to fetch
            filter_query: FQL filter query (e.g., "malicious_confidence:'high'")

        Returns:
            List of indicators
        """
        indicators = []

        try:
            authenticated = await self._get_access_token()
            if not authenticated:
                logger.warning("CrowdStrike CTI authentication failed")
                return indicators

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/intel/combined/indicators/v1"

                params = {
                    'limit': min(limit, 500),
                    'sort': 'published_date|desc'
                }

                if filter_query:
                    params['filter'] = filter_query
                else:
                    # Default: High confidence indicators from last 7 days
                    params['filter'] = "malicious_confidence:'high'+published_date:>='last 7 days'"

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        resources = data.get('resources', [])

                        for resource in resources:
                            indicator = {
                                'id': resource.get('id'),
                                'type': self._map_indicator_type(resource.get('type')),
                                'value': resource.get('indicator'),
                                'threat_score': self._calculate_threat_score(resource),
                                'confidence': resource.get('malicious_confidence', 'medium'),
                                'first_seen': resource.get('published_date'),
                                'last_updated': resource.get('last_updated'),
                                'tags': self._extract_tags(resource),
                                'threat_actor': ', '.join(resource.get('actors', [])),
                                'malware_family': ', '.join(resource.get('malware_families', [])),
                                'campaign': resource.get('threat_types', [''])[0] if resource.get('threat_types') else None,
                                'kill_chain_phases': resource.get('kill_chains', []),
                                'labels': resource.get('labels', []),
                                'source_description': resource.get('reports', [''])[0] if resource.get('reports') else None
                            }
                            indicators.append(indicator)

                        logger.info(f"Retrieved {len(indicators)} INTEL indicators from CrowdStrike")
                    else:
                        logger.warning(f"CrowdStrike INTEL query returned status {response.status}")

        except Exception as e:
            logger.error(f"Error fetching CrowdStrike INTEL indicators: {e}")

        return indicators

    async def get_custom_iocs(self, limit: int = 100, severity: str = None) -> List[Dict]:
        """
        Fetch custom IOCs from IOCS_READ scope

        Args:
            limit: Maximum IOCs to fetch
            severity: Filter by severity (critical, high, medium, low)

        Returns:
            List of custom IOCs
        """
        iocs = []

        try:
            authenticated = await self._get_access_token()
            if not authenticated:
                logger.warning("CrowdStrike CTI authentication failed")
                return iocs

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/indicators/combined/iocs/v1"

                params = {
                    'limit': min(limit, 500),
                    'sort': 'created_on|desc'
                }

                if severity:
                    params['filter'] = f"severity:'{severity}'"

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        resources = data.get('resources', [])

                        for resource in resources:
                            ioc = {
                                'id': resource.get('id'),
                                'type': self._map_indicator_type(resource.get('type')),
                                'value': resource.get('value'),
                                'threat_score': self._severity_to_score(resource.get('severity', 'medium')),
                                'confidence': 'high',  # Custom IOCs are high confidence
                                'first_seen': resource.get('created_on'),
                                'last_updated': resource.get('modified_on'),
                                'tags': resource.get('tags', []) + ['custom_ioc'],
                                'action': resource.get('action'),
                                'platforms': resource.get('platforms', []),
                                'description': resource.get('description'),
                                'source': resource.get('source'),
                                'applied_globally': resource.get('applied_globally', False)
                            }
                            iocs.append(ioc)

                        logger.info(f"Retrieved {len(iocs)} custom IOCs from CrowdStrike")
                    else:
                        logger.warning(f"CrowdStrike IOCS query returned status {response.status}")

        except Exception as e:
            logger.error(f"Error fetching CrowdStrike custom IOCs: {e}")

        return iocs

    async def get_threat_actors(self, limit: int = 50) -> List[Dict]:
        """Fetch threat actor information from INTEL scope"""
        actors = []

        try:
            authenticated = await self._get_access_token()
            if not authenticated:
                return actors

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/intel/combined/actors/v1"
                params = {'limit': min(limit, 100)}

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        resources = data.get('resources', [])

                        for resource in resources:
                            actor = {
                                'id': resource.get('id'),
                                'name': resource.get('name'),
                                'description': resource.get('short_description'),
                                'aliases': resource.get('known_as', []),
                                'first_seen': resource.get('first_activity_date'),
                                'last_updated': resource.get('last_modified_date'),
                                'target_countries': resource.get('target_countries', []),
                                'target_industries': resource.get('target_industries', []),
                                'motivations': resource.get('motivations', []),
                                'capabilities': resource.get('capability', [])
                            }
                            actors.append(actor)

                        logger.info(f"Retrieved {len(actors)} threat actors from CrowdStrike")

        except Exception as e:
            logger.error(f"Error fetching CrowdStrike threat actors: {e}")

        return actors

    def _map_indicator_type(self, cs_type: str) -> str:
        """Map CrowdStrike indicator types to standard types"""
        type_mapping = {
            'domain': 'domain',
            'ipv4': 'ip',
            'ipv6': 'ip',
            'md5': 'md5',
            'sha1': 'sha1',
            'sha256': 'sha256',
            'url': 'url',
            'email_address': 'email',
            'file_name': 'filename',
            'mutex_name': 'mutex',
            'registry': 'registry_key'
        }
        return type_mapping.get(cs_type, cs_type)

    def _calculate_threat_score(self, resource: Dict) -> int:
        """Calculate threat score from CrowdStrike data"""
        confidence = resource.get('malicious_confidence', 'medium')

        confidence_scores = {
            'high': 90,
            'medium': 70,
            'low': 50,
            'unverified': 30
        }

        base_score = confidence_scores.get(confidence, 50)

        # Boost score if associated with known threat actors
        if resource.get('actors'):
            base_score = min(base_score + 10, 100)

        # Boost if multiple malware families
        if len(resource.get('malware_families', [])) > 1:
            base_score = min(base_score + 5, 100)

        return base_score

    def _severity_to_score(self, severity: str) -> int:
        """Convert severity to numeric threat score"""
        severity_scores = {
            'critical': 95,
            'high': 85,
            'medium': 65,
            'low': 40,
            'informational': 20
        }
        return severity_scores.get(severity, 50)

    def _extract_tags(self, resource: Dict) -> List[str]:
        """Extract and normalize tags from CrowdStrike resource"""
        tags = []

        # Add threat types as tags
        tags.extend(resource.get('threat_types', []))

        # Add labels
        tags.extend(resource.get('labels', []))

        # Add malware families
        tags.extend([f"malware:{fam}" for fam in resource.get('malware_families', [])])

        # Add actors
        tags.extend([f"actor:{actor}" for actor in resource.get('actors', [])])

        # Add confidence level
        confidence = resource.get('malicious_confidence')
        if confidence:
            tags.append(f"confidence:{confidence}")

        return list(set(tags))  # Deduplicate


async def test_crowdstrike_cti_integration():
    """Test CrowdStrike CTI integration"""

    print("Testing CrowdStrike CTI Integration...")
    print("-" * 50)

    # NOTE: Requires valid CrowdStrike API credentials
    # Set these via environment variables or config
    import os
    client_id = os.getenv('CROWDSTRIKE_CLIENT_ID')
    client_secret = os.getenv('CROWDSTRIKE_CLIENT_SECRET')

    if not client_id or not client_secret:
        print("ERROR: CrowdStrike credentials not set")
        print("Set CROWDSTRIKE_CLIENT_ID and CROWDSTRIKE_CLIENT_SECRET")
        return

    connector = CrowdStrikeCTIConnector(client_id, client_secret)

    # Test connection
    connected = await connector.test_connection()
    print(f"Connection Test: {'Success' if connected else 'Failed'}")

    if not connected:
        return

    # Get INTEL indicators
    print("\n[Fetching INTEL Indicators]")
    intel_indicators = await connector.get_intel_indicators(limit=5)
    for ind in intel_indicators:
        print(f"  - {ind['type']}: {ind['value'][:50]} (Score: {ind['threat_score']})")

    # Get custom IOCs
    print("\n[Fetching Custom IOCs]")
    custom_iocs = await connector.get_custom_iocs(limit=5)
    for ioc in custom_iocs:
        print(f"  - {ioc['type']}: {ioc['value'][:50]} (Severity: {ioc.get('threat_score')})")

    # Get threat actors
    print("\n[Fetching Threat Actors]")
    actors = await connector.get_threat_actors(limit=3)
    for actor in actors:
        print(f"  - {actor['name']}: {actor.get('description', '')[:100]}")

    print(f"\nTotal CTI items: {len(intel_indicators) + len(custom_iocs) + len(actors)}")


if __name__ == "__main__":
    asyncio.run(test_crowdstrike_cti_integration())
