/**
 * Complete TypeScript API Type Definitions
 *
 * This file contains all type definitions for the SIEMLess v2 API
 * Generated from backend API documentation
 */

// ============================================================================
// Generic API Response Types
// ============================================================================

export interface PaginatedResponse<T> {
  items: T[]
  page: number
  page_size: number
  total_items: number
  total_pages: number
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// ============================================================================
// Detection Rules Types
// ============================================================================

export interface DetectionRule {
  rule_id: string
  rule_name: string
  rule_type: 'sigma' | 'splunk' | 'elastic' | 'sentinel' | 'qradar'
  rule_content: string
  description?: string
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info'

  // MITRE mapping
  mitre_tactics?: string[]
  mitre_techniques?: string[]

  // Metadata
  created_at: string
  updated_at: string
  created_by?: string
  source: 'cti_generated' | 'imported' | 'manual'

  // CTI linkage
  cti_indicator_id?: string
  cti_source?: 'otx' | 'threatfox' | 'crowdstrike' | 'opencti'

  // Status
  status: 'pending' | 'approved' | 'active' | 'disabled' | 'archived'
  enabled: boolean

  // Quality
  quality_score?: number
  test_cases?: RuleTestCase[]

  // Organization
  folder?: string
  tags?: string[]
}

export interface PendingRule {
  pending_id: string

  // CTI source
  cti_indicator: CTIIndicator
  cti_source: 'otx' | 'threatfox' | 'crowdstrike' | 'opencti'

  // Generated rules
  sigma_rule: string
  translated_rules: {
    splunk?: string
    elastic?: string
    sentinel?: string
    qradar?: string
  }

  // Quality
  quality_score: number
  generation_method: 'ai_generated' | 'template_based'
  ai_model?: string

  // Test cases
  test_cases?: RuleTestCase[]

  // Review
  status: 'pending' | 'approved' | 'rejected' | 'needs_revision'
  reviewed_by?: string
  reviewed_at?: string
  review_notes?: string

  generated_at: string
}

export interface RuleTestCase {
  test_id: string
  description: string
  test_data: string
  expected_result: 'match' | 'no_match'
  actual_result?: 'match' | 'no_match'
  passed?: boolean
}

export interface RulePerformance {
  performance_id: string
  rule_id: string

  // Metrics
  total_alerts: number
  true_positives: number
  false_positives: number
  false_negatives: number

  // Rates
  true_positive_rate: number
  false_positive_rate: number
  precision: number
  recall: number
  f1_score: number

  // Timing
  avg_triage_time_seconds?: number
  last_triggered?: string

  // Score
  performance_score: number

  // Period
  measurement_period: '24h' | '7d' | '30d' | '90d'
  measured_at: string
}

export interface RulePerformanceSummary {
  total_rules: number
  active_rules: number
  total_alerts_24h: number
  avg_true_positive_rate: number
  avg_false_positive_rate: number
  top_performers: Array<{
    rule_id: string
    rule_name: string
    performance_score: number
  }>
  needs_tuning: Array<{
    rule_id: string
    rule_name: string
    false_positive_rate: number
  }>
}

export interface RuleTestResult {
  test_id: string
  rule_id: string
  test_data: string
  expected_result: 'match' | 'no_match'
  actual_result: 'match' | 'no_match'
  passed: boolean
  execution_time_ms: number
  executed_at: string
}

export interface RuleFolder {
  folder_id: string
  name: string
  description?: string
  parent_folder?: string
  rule_count: number
  created_at: string
  updated_at: string
}

// ============================================================================
// Request Types for Rules
// ============================================================================

export interface CreateRuleRequest {
  rule_name: string
  rule_type: 'sigma' | 'splunk' | 'elastic' | 'sentinel' | 'qradar'
  rule_content: string
  description?: string
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info'
  mitre_tactics?: string[]
  mitre_techniques?: string[]
  folder?: string
  tags?: string[]
  enabled?: boolean
}

export interface UpdateRuleRequest {
  rule_name?: string
  rule_content?: string
  description?: string
  severity?: 'critical' | 'high' | 'medium' | 'low' | 'info'
  mitre_tactics?: string[]
  mitre_techniques?: string[]
  folder?: string
  tags?: string[]
  enabled?: boolean
  status?: 'active' | 'disabled' | 'archived'
}

export interface RuleTestRequest {
  test_data: string
  expected_result: 'match' | 'no_match'
}

export interface RuleImportRequest {
  format: 'sigma' | 'json' | 'yaml'
  rules: string | DetectionRule[]
  folder?: string
  auto_enable?: boolean
}

// ============================================================================
// CTI (Cyber Threat Intelligence) Types
// ============================================================================

export interface CTIIndicator {
  indicator_id: string
  indicator_type: 'ip' | 'domain' | 'hash' | 'url' | 'email' | 'file'
  value: string
  threat_score: number
  confidence: number
  source: 'otx' | 'threatfox' | 'crowdstrike' | 'opencti'
  first_seen: string
  last_seen: string
  metadata: {
    threat_actor?: string
    malware_family?: string
    campaign?: string
    tags?: string[]
    description?: string
  }
}

export interface CTIPluginInfo {
  source: 'otx' | 'threatfox' | 'crowdstrike' | 'opencti'
  enabled: boolean
  status: 'healthy' | 'degraded' | 'error'
  last_update: string
  total_indicators: number
  new_indicators_today: number
  avg_threat_score: number
  error_message?: string
  credentials_valid: boolean
}

export interface CTIStats {
  total_indicators: number
  indicators_by_type: Record<string, number>
  indicators_by_source: Record<string, number>
  new_indicators_24h: number
  avg_threat_score: number
  active_sources: number
  total_sources: number
}

// ============================================================================
// Alert Types
// ============================================================================

export interface Alert {
  alert_id: string
  title: string
  description: string
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info'
  status: 'new' | 'investigating' | 'escalated' | 'resolved' | 'false_positive'
  priority: number
  source: string
  created_at: string
  updated_at: string
  assigned_to?: string
  case_id?: string
  mitre_techniques?: string[]
  entities?: string[]
  enriched_entities?: Entity[]
}

export interface AlertDetail extends Alert {
  raw_log?: any
  timeline?: TimelineEvent[]
  related_alerts?: Alert[]
  investigation_steps?: InvestigationStep[]
  evidence?: Evidence[]
}

export interface AlertFilters {
  severity?: string[]
  status?: string[]
  source?: string[]
  assigned_to?: string
  start_date?: string
  end_date?: string
  search?: string
}

export interface UpdateAlertRequest {
  status?: 'new' | 'investigating' | 'escalated' | 'resolved' | 'false_positive'
  assigned_to?: string
  priority?: number
  notes?: string
}

export interface CreateAlertRequest {
  title: string
  description: string
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info'
  source: string
  raw_log?: any
  entities?: string[]
}

// ============================================================================
// Entity Types
// ============================================================================

export interface Entity {
  entity_id: string
  entity_type: 'ip' | 'user' | 'host' | 'hash' | 'domain' | 'file' | 'process' | 'email'
  entity_value: string
  first_seen: string
  last_seen: string
  occurrence_count: number
  risk_score: number
  tags?: string[]
  enrichments?: EntityEnrichments
}

export interface EntityEnrichments {
  layer1?: {
    geolocation?: GeolocationEnrichment
    asset_info?: AssetInfoEnrichment
    network_info?: NetworkInfoEnrichment
    user_info?: UserInfoEnrichment
  }
  layer2?: {
    cti_matches?: CTIMatchEnrichment[]
    threat_score?: number
  }
  layer3?: {
    vendor_context?: VendorContextEnrichment[]
  }
}

export interface GeolocationEnrichment {
  country?: string
  country_code?: string
  city?: string
  region?: string
  latitude?: number
  longitude?: number
  isp?: string
  is_vpn?: boolean
  is_proxy?: boolean
  is_tor?: boolean
}

export interface AssetInfoEnrichment {
  asset_id?: string
  asset_name?: string
  asset_tier?: string
  criticality?: 'critical' | 'high' | 'medium' | 'low'
  security_zone?: string
  owner?: string
  compliance_tags?: string[]
}

export interface NetworkInfoEnrichment {
  whois?: any
  rdns?: string
  asn?: number
  asn_org?: string
}

export interface UserInfoEnrichment {
  display_name?: string
  email?: string
  department?: string
  title?: string
  manager?: string
  privileged?: boolean
  risk_level?: string
}

export interface CTIMatchEnrichment {
  source: 'otx' | 'threatfox' | 'crowdstrike' | 'opencti'
  indicator_id: string
  threat_score: number
  confidence: number
  threat_actor?: string
  malware_family?: string
  campaign?: string
  tags?: string[]
  first_seen: string
  last_seen: string
}

export interface VendorContextEnrichment {
  vendor: 'crowdstrike' | 'elastic' | 'splunk' | 'sentinel'
  context_type: string
  data: any
  retrieved_at: string
}

export interface Relationship {
  source_entity: string
  target_entity: string
  relationship_type: string
  weight: number
  first_seen: string
  last_seen: string
  occurrence_count: number
}

export interface EntitySearchRequest {
  entity_type?: string
  entity_value?: string
  risk_score_min?: number
  risk_score_max?: number
  tags?: string[]
  start_date?: string
  end_date?: string
  page?: number
  page_size?: number
}

export interface TimelineEvent {
  event_id: string
  timestamp: string
  event_type: string
  description: string
  entities: string[]
  severity?: string
  source?: string
}

// ============================================================================
// Case Types
// ============================================================================

export interface Case {
  case_id: string
  title: string
  description: string
  status: 'open' | 'investigating' | 'escalated' | 'resolved' | 'closed'
  priority: 'critical' | 'high' | 'medium' | 'low'
  created_at: string
  updated_at: string
  created_by: string
  assigned_to?: string
  alert_count: number
  entity_count: number
  evidence_count: number
  mitre_techniques?: string[]
}

export interface CaseDetail extends Case {
  alerts: Alert[]
  entities: Entity[]
  evidence: Evidence[]
  timeline: TimelineEvent[]
  investigation_guide?: InvestigationGuide
  notes?: CaseNote[]
}

export interface InvestigationStep {
  step_id: string
  title: string
  description: string
  queries: Record<string, string>
  evidence_required: string[]
  completed: boolean
  completed_at?: string
  completed_by?: string
}

export interface InvestigationGuide {
  case_id: string
  steps: InvestigationStep[]
  generated_at: string
  ai_model: string
}

export interface Evidence {
  evidence_id: string
  case_id: string
  type: 'log' | 'file' | 'screenshot' | 'note' | 'network_capture'
  description: string
  content: any
  collected_at: string
  collected_by: string
  tags?: string[]
}

export interface CaseNote {
  note_id: string
  case_id: string
  content: string
  created_at: string
  created_by: string
}

// ============================================================================
// Dashboard Types
// ============================================================================

export interface DashboardStats {
  critical_alerts: number
  open_cases: number
  detection_confidence: number
  total_cti_indicators: number
  entities_tracked: number
  mitre_coverage_score: number
  alerts_24h: number
  cases_closed_7d: number
  avg_triage_time_seconds: number
}

// ============================================================================
// Detection Fidelity Types
// ============================================================================

export interface DetectionFidelity {
  overall_confidence: number
  technique_coverage: TechniqueCoverage[]
  log_source_quality: LogSourceQuality[]
  detection_gaps: DetectionGap[]
  calculated_at: string
}

export interface TechniqueCoverage {
  technique_id: string
  technique_name: string
  tactic: string
  detection_confidence: number
  log_sources: string[]
  rule_count: number
  has_premium_source: boolean
}

export interface LogSourceQuality {
  source_name: string
  source_type: string
  quality_tier: 'premium' | 'good' | 'basic'
  detection_quality_score: number
  enabled: boolean
  event_count_24h: number
}

export interface DetectionGap {
  gap_type: string
  description: string
  affected_techniques: string[]
  severity: 'critical' | 'high' | 'medium' | 'low'
  recommendation: string
}

// ============================================================================
// Export all types
// ============================================================================

export type {
  // Generic
  PaginatedResponse,
  ApiResponse,

  // Rules
  DetectionRule,
  PendingRule,
  RuleTestCase,
  RulePerformance,
  RulePerformanceSummary,
  RuleTestResult,
  RuleFolder,
  CreateRuleRequest,
  UpdateRuleRequest,
  RuleTestRequest,
  RuleImportRequest,

  // CTI
  CTIIndicator,
  CTIPluginInfo,
  CTIStats,

  // Alerts
  Alert,
  AlertDetail,
  AlertFilters,
  UpdateAlertRequest,
  CreateAlertRequest,

  // Entities
  Entity,
  EntityEnrichments,
  GeolocationEnrichment,
  AssetInfoEnrichment,
  NetworkInfoEnrichment,
  UserInfoEnrichment,
  CTIMatchEnrichment,
  VendorContextEnrichment,
  Relationship,
  EntitySearchRequest,
  TimelineEvent,

  // Cases
  Case,
  CaseDetail,
  InvestigationStep,
  InvestigationGuide,
  Evidence,
  CaseNote,

  // Dashboard
  DashboardStats,

  // Detection
  DetectionFidelity,
  TechniqueCoverage,
  LogSourceQuality,
  DetectionGap
}
