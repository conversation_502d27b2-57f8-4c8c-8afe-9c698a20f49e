import React, { useState } from 'react'
import FlexLayout from 'flexlayout-react'
import { Model, TabNode, Actions, IJsonModel } from 'flexlayout-react'
import 'flexlayout-react/style/light.css'
import {
  LayoutGrid, Plus, Save, FolderOpen, Settings,
  Maximize2, Minimize2, X
} from 'lucide-react'

// Import our existing widgets
import AlertQueue from '../widgets/AlertQueue'
import CostSavingsTracker from '../widgets/CostSavingsTracker'
import EntityExplorer from '../widgets/EntityExplorer'
import RelationshipGraph from '../widgets/RelationshipGraph'
import CTIFeeds from '../widgets/CTIFeeds'
import MITREHeatmap from '../widgets/MITREHeatmap'
import AIInvestigationGuide from '../widgets/AIInvestigationGuide'
import ActionToolbar from '../widgets/ActionToolbar'
import CrystallizationQueue from '../widgets/CrystallizationQueue'
import DashboardOverview from '../widgets/DashboardOverview'
import CTIPluginStatus from '../widgets/CTIPluginStatus'
import AlertQueueWidget from '../widgets/AlertQueueWidget'
import PendingRulesWidget from '../widgets/PendingRulesWidget'
import RuleLibraryWidget from '../widgets/RuleLibraryWidget'
import RulePerformanceWidget from '../widgets/RulePerformanceWidget'

// Widget registry
const widgetComponents: Record<string, React.ComponentType<any>> = {
  AlertQueue,
  AlertQueueWidget,
  CostSavingsTracker,
  EntityExplorer,
  RelationshipGraph,
  CTIFeeds,
  MITREHeatmap,
  AIInvestigationGuide,
  ActionToolbar,
  CrystallizationQueue,
  DashboardOverview,
  CTIPluginStatus,
  PendingRulesWidget,
  RuleLibraryWidget,
  RulePerformanceWidget
}

// Default layout configuration
const defaultLayout: IJsonModel = {
  global: {
    tabEnableClose: true,
    tabEnableFloat: true,
    tabEnableRename: true,
    tabSetEnableMaximize: true,
    tabSetTabStripHeight: 32,
    tabSetHeaderHeight: 28,
    borderBarSize: 8
  },
  borders: [],
  layout: {
    type: 'row',
    weight: 100,
    children: [
      {
        type: 'tabset',
        weight: 100,
        children: [
          {
            type: 'tab',
            name: 'Dashboard Overview',
            component: 'DashboardOverview',
            enableClose: false
          },
          {
            type: 'tab',
            name: 'Alert Queue',
            component: 'AlertQueue'
          },
          {
            type: 'tab',
            name: 'MITRE Heatmap',
            component: 'MITREHeatmap'
          },
          {
            type: 'tab',
            name: 'CTI Feed',
            component: 'CTIFeeds'
          }
        ]
      }
    ]
  }
}

const Dashboard: React.FC = () => {
  const [model] = useState(() => Model.fromJson(defaultLayout))
  const [savedLayouts, setSavedLayouts] = useState<Array<{ name: string; config: IJsonModel }>>([])
  const [showLayoutMenu, setShowLayoutMenu] = useState(false)

  // Widget factory
  const factory = (node: TabNode) => {
    const component = node.getComponent()
    if (!component) return null

    const WidgetComponent = widgetComponents[component]
    if (!WidgetComponent) {
      return (
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center">
            <p>Widget not found: {component}</p>
          </div>
        </div>
      )
    }

    // Pass any required props to widgets
    const widgetProps: any = {}

    // Special props for specific widgets
    if (component === 'EntityExplorer') {
      widgetProps.entityId = 'default-entity'
    } else if (component === 'AIInvestigationGuide') {
      widgetProps.caseId = 'CASE-123'
    }

    return <WidgetComponent {...widgetProps} />
  }

  // Save current layout
  const saveLayout = () => {
    const layoutName = prompt('Enter layout name:')
    if (layoutName) {
      const config = model.toJson()
      setSavedLayouts(prev => [...prev, { name: layoutName, config }])
      // Could also save to backend or localStorage
      localStorage.setItem(`layout-${layoutName}`, JSON.stringify(config))
    }
  }

  // Load saved layout
  const loadLayout = (config: IJsonModel) => {
    model.doAction(Actions.updateModel(config))
  }

  // Add new widget
  const addWidget = (widgetName: string) => {
    const newTab = {
      type: 'tab',
      name: widgetName,
      component: widgetName
    }
    model.doAction(Actions.addNode(newTab, 'root', 'middle', 0))
  }

  // Available widgets menu
  const availableWidgets = [
    { name: 'Dashboard Overview', icon: '📊' },
    { name: 'Alert Queue Widget', icon: '🚨' },
    { name: 'CTI Plugin Status', icon: '🛡️' },
    { name: 'Pending Rules Widget', icon: '📋' },
    { name: 'Rule Library Widget', icon: '📚' },
    { name: 'Rule Performance Widget', icon: '📈' },
    { name: 'Entity Explorer', icon: '🔍' },
    { name: 'Relationship Graph', icon: '🕸️' },
    { name: 'CTI Feed', icon: '📡' },
    { name: 'MITRE Heatmap', icon: '🎯' },
    { name: 'AI Investigation Guide', icon: '🤖' },
    { name: 'Crystallization Queue', icon: '✨' },
    { name: 'Cost Savings Tracker', icon: '💰' },
    { name: 'Action Toolbar', icon: '⚡' }
  ]

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Dashboard Header */}
      <div className="bg-white border-b px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-lg font-semibold">Security Operations Dashboard</h1>
            <span className="text-sm text-gray-500">
              Last updated: {new Date().toLocaleTimeString()}
            </span>
          </div>

          {/* Dashboard Controls */}
          <div className="flex items-center gap-2">
            {/* Add Widget Dropdown */}
            <div className="relative">
              <button
                onClick={() => setShowLayoutMenu(!showLayoutMenu)}
                className="px-3 py-1.5 bg-blue-500 text-white rounded hover:bg-blue-600 flex items-center gap-2 text-sm"
              >
                <Plus size={16} />
                Add Widget
              </button>

              {showLayoutMenu && (
                <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-50">
                  <div className="p-2">
                    <div className="text-xs font-medium text-gray-500 px-2 py-1">
                      Available Widgets
                    </div>
                    {availableWidgets.map(widget => (
                      <button
                        key={widget.name}
                        onClick={() => {
                          addWidget(widget.name.replace(/\s+/g, ''))
                          setShowLayoutMenu(false)
                        }}
                        className="w-full flex items-center gap-3 px-3 py-2 hover:bg-gray-100 rounded text-left text-sm"
                      >
                        <span className="text-lg">{widget.icon}</span>
                        <span>{widget.name}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Layout Actions */}
            <button
              onClick={saveLayout}
              className="p-2 hover:bg-gray-100 rounded"
              title="Save Layout"
            >
              <Save size={18} />
            </button>

            <button
              onClick={() => loadLayout(defaultLayout)}
              className="p-2 hover:bg-gray-100 rounded"
              title="Reset Layout"
            >
              <LayoutGrid size={18} />
            </button>

            <button
              className="p-2 hover:bg-gray-100 rounded"
              title="Layout Settings"
            >
              <Settings size={18} />
            </button>
          </div>
        </div>
      </div>

      {/* FlexLayout Dashboard */}
      <div className="flex-1 overflow-hidden">
        <FlexLayout.Layout
          model={model}
          factory={factory}
          classNameMapper={(className) => {
            // Custom styling for FlexLayout components
            if (className === 'flexlayout__tab') {
              return 'flexlayout__tab bg-white'
            }
            if (className === 'flexlayout__tabset-selected') {
              return 'flexlayout__tabset-selected border-blue-500'
            }
            return className
          }}
        />
      </div>
    </div>
  )
}

export default Dashboard