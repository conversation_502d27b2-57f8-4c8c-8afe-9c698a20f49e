# SIEMLess v2.0 - Database Schema Documentation

## Database Structure Overview

The SIEMLess v2.0 platform uses PostgreSQL as its primary database with **142 tables** organized into logical domains. This document provides comprehensive visualization of the schema structure.

## Database Entity Relationship Diagram

```mermaid
erDiagram
    %% Pattern & Intelligence Tables
    pattern_library ||--o{ pattern_versions : "has versions"
    pattern_library ||--o{ pattern_usage_log : "tracks usage"
    pattern_library ||--o{ pattern_performance : "monitors performance"
    pattern_library ||--o{ pattern_deployments : "deployed to SIEMs"

    crystallized_patterns ||--o{ ai_pattern_library : "AI-enhanced"
    crystallized_patterns ||--o{ intelligence_consensus_results : "validated by consensus"

    %% Detection & Rules
    detection_rules ||--o{ rule_test_cases : "has test cases"
    detection_rules ||--o{ rule_performance : "tracks performance"
    detection_rules ||--o{ rule_mitre_mappings : "mapped to MITRE"

    mitre_attack_framework ||--o{ rule_mitre_mappings : "framework reference"
    mitre_attack_framework ||--o{ log_source_mitre_mapping : "coverage mapping"
    mitre_attack_framework ||--o{ ai_technique_inferences : "AI inferences"

    %% Entity & Relationship System
    entities ||--o{ relationships : "source entity"
    entities ||--o{ relationships : "target entity"
    entities ||--o{ entity_enrichment_cache : "enriched data"
    entities ||--o{ entity_baselines : "behavioral baseline"
    entities ||--o{ entity_risk_factors : "risk scoring"
    entities ||--o{ event_entity : "linked to events"

    events ||--o{ event_entity : "contains entities"
    events ||--o{ session_instances : "grouped in sessions"

    %% Log Source Quality System
    log_sources ||--o{ log_source_quality_history : "quality metrics"
    log_sources ||--o{ log_source_mitre_mapping : "detection capability"
    log_sources ||--o{ log_source_recommendations : "improvement suggestions"
    log_sources ||--o{ detection_coverage_assessments : "coverage analysis"

    %% Investigation & Case Management
    cases ||--o{ investigations : "has investigations"
    investigations ||--o{ investigation_evidence : "collects evidence"
    investigations ||--o{ investigation_notes : "analyst notes"

    %% CTI & Enrichment
    enrichment_rules ||--o{ enrichment_audit : "audit trail"
    enrichment_rules ||--o{ correlation_requirements_cache : "correlation cache"

    %% Workflow & Engine Coordination
    engine_coordination ||--o{ workflow_instances : "orchestrates workflows"
    workflow_instances ||--o{ session_instances : "executes sessions"

    %% Parser & Ingestion
    parsers ||--o{ ingestion_logs : "processes logs"
    github_sources ||--o{ github_sync_history : "sync tracking"

    %% Cloud & Preview
    cloud_update_previews ||--o{ cloud_update_history : "update tracking"

    %% API Management
    api_endpoints ||--o{ api_versions : "versioned endpoints"
```

## Core Domain Schemas

### 1. Pattern & Intelligence Domain

```mermaid
graph TB
    subgraph "Pattern Management"
        PL[pattern_library<br/>Core pattern storage]
        PV[pattern_versions<br/>Version history]
        PU[pattern_usage_log<br/>Usage tracking]
        PP[pattern_performance<br/>Performance metrics]
        PD[pattern_deployments<br/>SIEM deployments]
    end

    subgraph "AI Intelligence"
        CP[crystallized_patterns<br/>Learned patterns]
        APL[ai_pattern_library<br/>AI-enhanced patterns]
        ICR[intelligence_consensus_results<br/>Multi-AI consensus]
        AIC[ai_intelligence_costs<br/>Cost tracking]
    end

    PL --> PV
    PL --> PU
    PL --> PP
    PL --> PD
    CP --> APL
    CP --> ICR
```

### 2. Detection & Rules Domain

```mermaid
graph TB
    subgraph "Detection Rules"
        DR[detection_rules<br/>SIEM rules]
        RTC[rule_test_cases<br/>Test validation]
        RP[rule_performance<br/>Performance tracking]
        RMM[rule_mitre_mappings<br/>MITRE ATT&CK mapping]
    end

    subgraph "MITRE Framework"
        MAF[mitre_attack_framework<br/>Techniques & Tactics]
        MCS[mitre_coverage_snapshots<br/>Coverage analysis]
        ATI[ai_technique_inferences<br/>AI-inferred techniques]
        LSMM[log_source_mitre_mapping<br/>Source capabilities]
    end

    DR --> RTC
    DR --> RP
    DR --> RMM
    MAF --> RMM
    MAF --> MCS
    MAF --> ATI
    MAF --> LSMM
```

### 3. Entity & Relationship Domain

```mermaid
graph TB
    subgraph "Core Entity System"
        E[entities<br/>Users, IPs, Hosts, Processes]
        R[relationships<br/>Entity connections]
        EEC[entity_enrichment_cache<br/>Enriched metadata]
        EB[entity_baselines<br/>Behavioral baselines]
        ERF[entity_risk_factors<br/>Risk scoring]
    end

    subgraph "Event System"
        EV[events<br/>Security events]
        EE[event_entity<br/>Event-Entity links]
        SI[session_instances<br/>Grouped activities]
    end

    E --> R
    E --> EEC
    E --> EB
    E --> ERF
    E --> EE
    EV --> EE
    EV --> SI
```

### 4. Investigation & Case Management Domain

```mermaid
graph TB
    subgraph "Case Management"
        C[cases<br/>Security cases]
        I[investigations<br/>Investigation workflows]
        IE[investigation_evidence<br/>Evidence collection]
        IN[investigation_notes<br/>Analyst notes]
    end

    C --> I
    I --> IE
    I --> IN
```

### 5. Log Source Quality Domain

```mermaid
graph TB
    subgraph "Log Source Management"
        LS[log_sources<br/>Configured sources]
        LSQH[log_source_quality_history<br/>Quality metrics over time]
        LSMM[log_source_mitre_mapping<br/>Detection capabilities]
        LSR[log_source_recommendations<br/>Improvement suggestions]
        DCA[detection_coverage_assessments<br/>Coverage analysis]
    end

    LS --> LSQH
    LS --> LSMM
    LS --> LSR
    LS --> DCA
```

### 6. Enrichment & CTI Domain

```mermaid
graph TB
    subgraph "Enrichment System"
        ER[enrichment_rules<br/>Enrichment logic]
        EA[enrichment_audit<br/>Audit trail]
        CRC[correlation_requirements_cache<br/>Correlation cache]
    end

    subgraph "AI Predictions"
        AFP[ai_fp_predictions<br/>False positive predictions]
        AGR[ai_gap_recommendations<br/>Gap analysis]
        ARO[ai_rule_overlap_analysis<br/>Rule overlap detection]
    end

    ER --> EA
    ER --> CRC
```

### 7. Engine Coordination Domain

```mermaid
graph TB
    subgraph "Workflow Orchestration"
        EC[engine_coordination<br/>Engine registry & heartbeat]
        WI[workflow_instances<br/>Active workflows]
        SI[session_instances<br/>Execution sessions]
    end

    EC --> WI
    WI --> SI
```

### 8. Ingestion & Parser Domain

```mermaid
graph TB
    subgraph "Ingestion"
        P[parsers<br/>Log parsers]
        IL[ingestion_logs<br/>Ingestion tracking]
        GS[github_sources<br/>GitHub patterns]
        GSH[github_sync_history<br/>Sync history]
    end

    P --> IL
    GS --> GSH
```

### 9. Cloud & Update Management

```mermaid
graph TB
    subgraph "Cloud Updates"
        CUP[cloud_update_previews<br/>Update previews]
        CUH[cloud_update_history<br/>Update history]
    end

    CUP --> CUH
```

### 10. Storage Tiers

```mermaid
graph LR
    subgraph "Storage Architecture"
        HOT[Redis<br/>Hot Storage<br/>24 hours<br/>Real-time ops]
        WARM[PostgreSQL<br/>Warm Storage<br/>30 days<br/>Searchable]
        COLD[S3/Object<br/>Cold Storage<br/>1+ years<br/>Archive]
    end

    HOT -->|Age out| WARM
    WARM -->|Archive| COLD
```

## Key Tables by Engine

### Ingestion Engine Tables
- `parsers` - Log format parsers
- `ingestion_logs` - Ingestion tracking
- `github_sources` - Pattern sources
- `github_sync_history` - Sync tracking

### Intelligence Engine Tables
- `crystallized_patterns` - Learned patterns
- `intelligence_consensus_results` - AI consensus
- `ai_pattern_library` - AI patterns
- `ai_intelligence_costs` - Cost tracking

### Contextualization Engine Tables
- `entities` - Extracted entities
- `relationships` - Entity relationships
- `events` - Security events
- `entity_enrichment_cache` - Enrichment data
- `entity_baselines` - Behavioral baselines
- `entity_risk_factors` - Risk scoring

### Delivery Engine Tables
- `cases` - Case management
- `investigations` - Investigation workflows
- `investigation_evidence` - Evidence collection
- `investigation_notes` - Analyst notes

### Backend Engine Tables
- `detection_rules` - SIEM rules
- `rule_test_cases` - Test cases
- `rule_performance` - Performance tracking
- `warm_storage` - Mid-term storage
- `pattern_library` - Pattern repository
- `pattern_deployments` - SIEM deployments

## Authentication & SSO Tables (Keycloak)

The database includes **80+ Keycloak tables** for SSO and authentication:

### Core Auth Tables
- `realm` - Keycloak realms
- `user_entity` - User accounts
- `client` - OAuth2 clients
- `keycloak_role` - Role definitions
- `user_role_mapping` - User-role assignments
- `credential` - User credentials
- `authentication_flow` - Auth flows
- `client_session` - Active sessions
- `user_session` - User sessions

### Federation & Identity
- `federated_user` - Federated identities
- `identity_provider` - External IdPs
- `user_federation_provider` - LDAP/AD
- `fed_user_role_mapping` - Federated roles

## MITRE ATT&CK Integration

```mermaid
graph TB
    subgraph "MITRE Coverage System"
        MAF[mitre_attack_framework<br/>Tactics: 14<br/>Techniques: 200+<br/>Sub-techniques: 400+]

        RMM[rule_mitre_mappings<br/>Detection rule mappings]
        LSMM[log_source_mitre_mapping<br/>Log source capabilities]
        MCS[mitre_coverage_snapshots<br/>Coverage over time]
        ATI[ai_technique_inferences<br/>AI-inferred techniques]
    end

    MAF --> RMM
    MAF --> LSMM
    MAF --> MCS
    MAF --> ATI
```

## API & Security Tables

```mermaid
graph TB
    subgraph "API Management"
        AE[api_endpoints<br/>Registered endpoints]
        AV[api_versions<br/>API versions]
    end

    subgraph "Security Zones"
        SZ[security_zones<br/>Network segmentation]
    end

    AE --> AV
```

## Data Flow Through Tables

### 1. Log Ingestion Flow
```mermaid
sequenceDiagram
    participant L as Raw Log
    participant P as parsers
    participant IL as ingestion_logs
    participant E as entities
    participant R as relationships
    participant EV as events

    L->>P: Parse log
    P->>IL: Track ingestion
    P->>E: Extract entities
    E->>R: Create relationships
    E->>EV: Create event
```

### 2. Pattern Crystallization Flow
```mermaid
sequenceDiagram
    participant U as Unknown Pattern
    participant ICR as intelligence_consensus_results
    participant CP as crystallized_patterns
    participant PL as pattern_library
    participant PD as pattern_deployments

    U->>ICR: AI consensus
    ICR->>CP: Crystallize
    CP->>PL: Store pattern
    PL->>PD: Deploy to SIEM
```

### 3. Investigation Flow
```mermaid
sequenceDiagram
    participant A as Alert
    participant C as cases
    participant I as investigations
    participant IE as investigation_evidence
    participant IN as investigation_notes

    A->>C: Create case
    C->>I: Start investigation
    I->>IE: Collect evidence
    I->>IN: Add notes
```

## Table Count by Domain

| Domain | Table Count | Purpose |
|--------|-------------|---------|
| **Keycloak Auth** | 80 | SSO, JWT, user management |
| **Pattern & Intelligence** | 10 | Pattern library, AI consensus |
| **Detection & MITRE** | 12 | Rules, MITRE mapping, coverage |
| **Entity & Relationships** | 8 | Entity extraction, graphs |
| **Case Management** | 4 | Cases, investigations, evidence |
| **Log Source Quality** | 5 | Source assessment, recommendations |
| **Enrichment & CTI** | 6 | Enrichment, correlation, predictions |
| **Engine & Workflow** | 3 | Engine coordination, workflows |
| **Ingestion & Parser** | 4 | Parsers, ingestion tracking |
| **Storage & Archive** | 3 | Warm storage, API management |
| **Cloud & Updates** | 2 | Update management |
| **Security** | 1 | Security zones |
| **Total** | **142** | Complete platform |

## Storage Optimization Strategy

### Pattern Library Growth Model
```mermaid
graph LR
    A[Year 1<br/>10K patterns] --> B[Year 2<br/>50K patterns]
    B --> C[Year 3<br/>500K patterns]
    C --> D[Year 4<br/>5M patterns]
    D --> E[Year 5<br/>50M patterns<br/>Global federation]
```

### Cost Reduction via Patterns
```mermaid
graph TB
    subgraph "First Encounter - EXPENSIVE"
        A[Unknown Log] --> B[AI Consensus<br/>$0.02]
        B --> C[Pattern Created]
    end

    subgraph "Subsequent Encounters - FREE"
        D[Same Log Type] --> E[Pattern Match<br/>$0.0001]
        E --> F[Instant Processing]
    end

    C -.->|Pattern reused| E
```

## Database Performance Considerations

### Indexes
- **entities**: Indexed on `entity_type`, `entity_value`, `created_at`
- **relationships**: Indexed on `source_entity_id`, `target_entity_id`, `relationship_type`
- **pattern_library**: Indexed on `pattern_hash`, `vendor`, `log_type`
- **detection_rules**: Indexed on `siem_platform`, `enabled`, `performance_score`
- **events**: Indexed on `event_time`, `severity`, `mitre_technique`

### Partitioning Strategy
- **events**: Partitioned by month
- **ingestion_logs**: Partitioned by week
- **warm_storage**: Partitioned by date range

### Retention Policies
- **Hot (Redis)**: 24 hours
- **Warm (PostgreSQL)**: 30 days (configurable)
- **Cold (S3)**: 1+ years (compliance-driven)

## Schema Version & Migration

**Current Schema Version**: v2.0.0
**Total Migrations**: Managed via Liquibase (see `databasechangelog` table)
**Migration Strategy**: Zero-downtime rolling updates

## Conclusion

The SIEMLess v2.0 database schema represents a comprehensive intelligence foundation with:

- **142 tables** across 12 domains
- **Pattern-based storage** for 99.97% cost reduction
- **Entity-relationship graphs** for contextual intelligence
- **Full MITRE ATT&CK integration** for coverage tracking
- **Multi-tier storage** for performance and cost optimization
- **Complete audit trail** for compliance and investigation

This schema enables the "learn expensive once, operate free forever" philosophy while providing enterprise-grade security intelligence capabilities.
