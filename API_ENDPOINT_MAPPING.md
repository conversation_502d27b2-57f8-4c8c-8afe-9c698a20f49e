# SIEMLess v2.0 - Complete API Endpoint Mapping
**Last Updated**: October 3, 2025

This document maps all frontend components to their backend API endpoints across the 5 engines.

---

## Table of Contents
1. [Alert & Investigation APIs](#alert--investigation-apis)
2. [Rule Management APIs](#rule-management-apis)
3. [Dashboard & Monitoring APIs](#dashboard--monitoring-apis)
4. [Entity & Enrichment APIs](#entity--enrichment-apis)
5. [Workflow & Case APIs](#workflow--case-apis)
6. [Cross-Engine Communication](#cross-engine-communication)

---

## Alert & Investigation APIs

### Frontend Component: `AlertQueueWidget.tsx` + `AlertInvestigationScreen.tsx`

| Endpoint | Method | Engine | Port | Purpose | Frontend Usage |
|----------|--------|--------|------|---------|----------------|
| `/api/alerts` | GET | Delivery | 8005 | List all alerts | Load alert queue, filter by status |
| `/api/alerts/{alert_id}` | GET | Delivery | 8005 | Get single alert | Load investigation details |
| `/api/alerts/{alert_id}/context` | GET | Delivery | 8005 | Get investigation context | Full context for investigation screen |
| `/api/alerts/{alert_id}/enrich` | POST | Delivery | 8005 | Trigger enrichment | Manual enrichment button |
| `/api/alerts/{alert_id}/correlate` | POST | Delivery | 8005 | Trigger correlation | Manual correlation button |
| `/api/alerts/{alert_id}/enrichment` | GET | Delivery | 8005 | Get enrichment status | Poll for enrichment completion |
| `/api/alerts/{alert_id}/correlation` | GET | Delivery | 8005 | Get correlation results | Poll for correlation completion |

**Query Parameters**:
- `GET /api/alerts?status=open&limit=50` - Filter alerts
- `GET /api/alerts?severity=critical` - Filter by severity

**Request Bodies**:
```json
// POST /api/alerts/{alert_id}/enrich
{
  "entities": {
    "ip": ["***********"],
    "user": ["admin"],
    "host": ["workstation-01"]
  }
}

// POST /api/alerts/{alert_id}/correlate
{
  "time_window_minutes": 60,
  "correlation_types": ["entity_match", "temporal", "mitre"]
}
```

**Response Formats**:
```json
// GET /api/alerts
{
  "alerts": [
    {
      "alert_id": "alert_123",
      "title": "Suspicious Login",
      "severity": "high",
      "status": "open",
      "timestamp": "2025-10-03T10:30:00Z",
      "entities": {...},
      "enrichment_status": {...},
      "correlation_status": {...}
    }
  ],
  "total": 25
}

// GET /api/alerts/{alert_id}/enrichment
{
  "status": "completed",
  "enrichment": {
    "summary": {
      "total_entities": 5,
      "enriched_count": 5,
      "threat_indicators_found": 2
    },
    "entities": [
      {
        "value": "***********",
        "type": "ip",
        "basic": {...},  // Layer 1
        "cti": {...},    // Layer 2
        "environmental": {...}  // Layer 3
      }
    ]
  }
}
```

**WebSocket Channels**:
- `contextualization.alert.enriched.{alert_id}` - Enrichment completion
- `backend.correlation.complete.{alert_id}` - Correlation completion

---

## Rule Management APIs

### Frontend Component: `RuleManagementDashboard.tsx` + subwidgets

| Endpoint | Method | Engine | Port | Purpose | Frontend Usage |
|----------|--------|--------|------|---------|----------------|
| `/api/rules` | GET | Delivery | 8005 | List all rules | Rule Library grid |
| `/api/rules/{rule_id}` | GET | Delivery | 8005 | Get single rule | Rule detail view |
| `/api/rules/{rule_id}` | PATCH | Delivery | 8005 | Update rule | Edit rule metadata |
| `/api/rules/{rule_id}` | DELETE | Delivery | 8005 | Delete rule | Remove rule |
| `/api/rules/bulk-delete` | POST | Delivery | 8005 | Delete multiple rules | Bulk actions |
| `/api/rules/test` | POST | Delivery | 8005 | Test rule | Validate rule syntax |

**Backend Engine Endpoints** (Port 8002):
| Endpoint | Method | Purpose | Frontend Usage |
|----------|--------|---------|----------------|
| `/api/pending-rules` | GET | List pending rules | Pending Rules Widget |
| `/api/pending-rules/{pending_id}` | GET | Get pending rule | Detail view |
| `/api/pending-rules/{pending_id}/approve` | POST | Approve rule | Approve button |
| `/api/pending-rules/{pending_id}/reject` | POST | Reject rule | Reject button |
| `/api/pending-rules/bulk-approve` | POST | Approve multiple | Bulk approve |
| `/api/rules/performance` | GET | Get performance metrics | Performance Widget |
| `/api/rules/{rule_id}/performance` | GET | Get rule performance | Individual rule stats |

**Query Parameters**:
```
GET /api/rules?quality=high&status=active&siem=splunk
GET /api/rules?search=malware&tag=ransomware
GET /api/pending-rules?cti_source=otx&quality_min=80
GET /api/rules/performance?period=7d
```

**Request Bodies**:
```json
// POST /api/pending-rules/{pending_id}/approve
{
  "deploy_to": ["splunk", "elastic"],
  "enable_immediately": true
}

// POST /api/pending-rules/{pending_id}/reject
{
  "reason": "Too many false positives",
  "auto_tune": true
}

// PATCH /api/rules/{rule_id}
{
  "status": "active",
  "enabled": true,
  "severity": "high",
  "tags": ["malware", "lateral-movement"]
}
```

---

## Dashboard & Monitoring APIs

### Frontend Component: `DashboardOverview.tsx`

| Endpoint | Method | Engine | Port | Purpose | Frontend Usage |
|----------|--------|--------|------|---------|----------------|
| `/api/dashboard/overview` | GET | Delivery | 8005 | System overview | Main dashboard |
| `/api/dashboard/cases` | GET | Delivery | 8005 | Active cases | Case summary |
| `/api/dashboard/stats` | GET | Delivery | 8005 | System stats | Metrics cards |
| `/api/system/status` | GET | Delivery | 8005 | System health | Health indicators |
| `/api/system/engines` | GET | Delivery | 8005 | Engine status | Engine monitoring |

**Response Formats**:
```json
// GET /api/dashboard/overview
{
  "alerts": {
    "total": 1245,
    "open": 42,
    "critical": 5,
    "high": 15
  },
  "cases": {
    "active": 12,
    "pending": 3,
    "resolved_today": 8
  },
  "processing": {
    "logs_per_hour": 45832,
    "entities_extracted": 1206,
    "patterns_matched": 847
  },
  "cost_savings": {
    "total_saved": "$1,247.50",
    "pattern_reuse_rate": 0.97
  },
  "engine_health": {
    "intelligence": "healthy",
    "backend": "healthy",
    "ingestion": "healthy",
    "contextualization": "healthy",
    "delivery": "healthy"
  }
}

// GET /api/system/engines
{
  "engines": [
    {
      "name": "intelligence",
      "port": 8001,
      "status": "healthy",
      "last_heartbeat": "2025-10-03T10:35:00Z",
      "uptime_seconds": 86400
    },
    // ... other engines
  ]
}
```

---

## Entity & Enrichment APIs

### Frontend Component: `EntityExplorer.tsx`, `EntitiesTab.tsx`

| Endpoint | Method | Engine | Port | Purpose | Frontend Usage |
|----------|--------|--------|------|---------|----------------|
| `/api/entities` | GET | Delivery | 8005 | List entities | Entity search/filter |
| `/api/entities/{entity_id}` | GET | Delivery | 8005 | Get entity | Entity detail view |
| `/api/entities/{entity_id}/enrichment` | GET | Delivery | 8005 | Get enrichment | Show enrichment layers |
| `/api/entities/{entity_id}/relationships` | GET | Delivery | 8005 | Get relationships | Relationship graph |
| `/api/entities/{entity_id}/timeline` | GET | Delivery | 8005 | Get timeline | Entity timeline |
| `/api/entities/{entity_id}/risk` | GET | Delivery | 8005 | Get risk score | Risk assessment |

**Backend Engine Endpoints** (Port 8002):
| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/api/graph/entities` | GET | Entity graph data |
| `/api/graph/relationships` | GET | Relationship data |
| `/api/graph/stats` | GET | Graph statistics |

**Query Parameters**:
```
GET /api/entities?type=ip&risk_min=0.7
GET /api/entities?search=192.168&limit=100
GET /api/entities/{entity_id}/relationships?depth=2&type=user_to_host
GET /api/entities/{entity_id}/timeline?start=2025-10-01&end=2025-10-03
```

**Response Formats**:
```json
// GET /api/entities/{entity_id}/enrichment
{
  "entity_id": "ip_***********",
  "value": "***********",
  "type": "ip",
  "basic_enrichment": {
    "geolocation": {...},
    "whois": {...},
    "dns": {...}
  },
  "cti_enrichment": {
    "threat_intel_sources": [
      {
        "source": "OTX",
        "verdict": "malicious",
        "threat_score": 95,
        "tags": ["c2", "apt29"]
      }
    ]
  },
  "environmental_enrichment": {
    "asset_info": {...},
    "baseline": {...}
  },
  "threat_score": 0.95,
  "is_malicious": true
}
```

---

## Workflow & Case APIs

### Frontend Component: `InvestigationWorkspace.tsx`

| Endpoint | Method | Engine | Port | Purpose | Frontend Usage |
|----------|--------|--------|------|---------|----------------|
| `/api/cases` | GET | Delivery | 8005 | List cases | Investigation list |
| `/api/cases` | POST | Delivery | 8005 | Create case | New investigation |
| `/api/cases/{case_id}` | GET | Delivery | 8005 | Get case | Case details |
| `/api/cases/{case_id}` | PUT | Delivery | 8005 | Update case | Assign/update |
| `/api/cases/{case_id}` | DELETE | Delivery | 8005 | Close case | Close investigation |
| `/api/cases/{case_id}/evidence` | POST | Delivery | 8005 | Add evidence | Upload evidence |
| `/api/workflows/start` | POST | Delivery | 8005 | Start workflow | Auto-workflow |
| `/api/workflows` | GET | Delivery | 8005 | List workflows | Workflow list |
| `/api/workflows/{workflow_id}` | GET | Delivery | 8005 | Get workflow | Workflow status |
| `/api/workflows/{workflow_id}/cancel` | POST | Delivery | 8005 | Cancel workflow | Stop workflow |

**Query Parameters**:
```
GET /api/cases?status=active&assigned_to=<EMAIL>
GET /api/cases?priority=critical&sla_status=breached
GET /api/workflows?status=running&template=incident_response
```

**Request Bodies**:
```json
// POST /api/cases
{
  "title": "Suspicious Login Investigation",
  "alert_id": "alert_123",
  "priority": "high",
  "assigned_to": "<EMAIL>",
  "auto_workflow": true
}

// PUT /api/cases/{case_id}
{
  "status": "investigating",
  "assigned_to": "<EMAIL>",
  "notes": "Enrichment complete, starting correlation"
}

// POST /api/workflows/start
{
  "template": "incident_response",
  "context": {
    "alert_id": "alert_123",
    "severity": "critical"
  }
}
```

---

## Cross-Engine Communication

### How Delivery Engine Integrates with Other Engines

```
Frontend Request Flow:
=====================

1. ALERT ENRICHMENT
   Frontend → Delivery (8005) → POST /api/alerts/{id}/enrich
   Delivery → Contextualization (Redis: contextualization.enrich_alert)
   Contextualization → processes entities
   Contextualization → Redis: contextualization.alert.enriched.{id}
   Delivery → listens → updates cache
   Frontend → polls → GET /api/alerts/{id}/enrichment

2. ALERT CORRELATION
   Frontend → Delivery (8005) → POST /api/alerts/{id}/correlate
   Delivery → Backend (Redis: backend.correlate_alert)
   Backend → finds related events
   Backend → Redis: backend.correlation.complete.{id}
   Delivery → listens → updates cache
   Frontend → polls → GET /api/alerts/{id}/correlation

3. RULE APPROVAL
   Frontend → Backend (8002) → POST /api/pending-rules/{id}/approve
   Backend → converts to active rule
   Backend → Redis: backend.rule.deployed
   Delivery → listens → updates dashboard

4. ENTITY ENRICHMENT
   Frontend → Delivery (8005) → GET /api/entities/{id}/enrichment
   Delivery → checks cache
   If not cached → Backend (8002): GET /api/graph/entities/{id}
   Backend → returns graph data
   Delivery → formats for frontend

5. CTI STATUS
   Frontend → Backend (8002) → GET /api/cti/status
   Backend → returns plugin status
   WebSocket → cti.update.complete (real-time updates)
```

### Redis Pub/Sub Channels

**Delivery Engine Subscriptions**:
```
contextualization.alert.enriched.*     → Alert enrichment complete
backend.correlation.complete.*         → Correlation complete
backend.rule.deployed                  → Rule deployed
backend.cti.update.complete           → CTI update complete
ingestion.alerts.received             → New alerts
investigation.create                   → New investigation
```

**Delivery Engine Publications**:
```
contextualization.enrich_alert        → Request enrichment
backend.correlate_alert               → Request correlation
backend.deploy_rule                   → Deploy rule
delivery.alert.delivered              → Alert sent to frontend
delivery.case.created                 → Case created
```

---

## API Response Standards

### Success Response Format
```json
{
  "success": true,
  "data": {...},
  "timestamp": "2025-10-03T10:30:00Z"
}
```

### Error Response Format
```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {...},
  "timestamp": "2025-10-03T10:30:00Z"
}
```

### Pagination Format
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "per_page": 50,
    "total": 1245,
    "pages": 25
  }
}
```

---

## WebSocket Events

### Frontend WebSocket Subscriptions

**Alert Updates**:
- `contextualization.alert.enriched.{alert_id}` - Enrichment complete
- `backend.correlation.complete.{alert_id}` - Correlation complete

**Rule Updates**:
- `backend.rule.deployed` - Rule deployed
- `backend.rule.generated` - New rule generated
- `backend.rule.performance.updated` - Performance metrics updated

**CTI Updates**:
- `cti.update.complete` - CTI feed updated
- `cti.plugin.status` - Plugin status changed

**Case Updates**:
- `delivery.case.created` - New case created
- `delivery.case.updated.{case_id}` - Case updated
- `delivery.case.closed.{case_id}` - Case closed

---

## Engine Health Check Endpoints

| Engine | Port | Health Endpoint | Metrics Endpoint |
|--------|------|----------------|------------------|
| Intelligence | 8001 | `/health` | `/metrics` |
| Backend | 8002 | `/health` | `/metrics` |
| Ingestion | 8003 | `/health` | `/metrics` |
| Contextualization | 8004 | `/health` | `/metrics` |
| Delivery | 8005 | `/health` | `/metrics` |

**Health Response**:
```json
{
  "status": "healthy",
  "engine": "delivery",
  "uptime": 86400,
  "database": "connected",
  "redis": "connected",
  "version": "2.0.0"
}
```

---

## Authentication (When Enabled)

All `/api/*` endpoints (except `/api/auth/login`) require authentication when enabled.

**Headers**:
```
Authorization: Bearer {jwt_token}
```

**Dev Mode** (ENABLE_DEV_API_KEYS=true):
```
X-API-Key: dev-admin-key
```

**Public Endpoints** (No Auth Required):
- `/health`
- `/metrics`
- `/api/auth/login`

---

## Frontend Service Layer

**AlertService.ts** uses these endpoints:
```typescript
listAlerts(params) → GET /api/alerts
getAlert(id) → GET /api/alerts/{id}
triggerEnrichment(id, entities) → POST /api/alerts/{id}/enrich
triggerCorrelation(id, params) → POST /api/alerts/{id}/correlate
getEnrichment(id) → GET /api/alerts/{id}/enrichment
getCorrelation(id) → GET /api/alerts/{id}/correlation
```

**RuleService.ts** uses these endpoints:
```typescript
listRules(params) → GET /api/rules
getPendingRules(params) → GET /api/pending-rules (Backend 8002)
approveRule(id) → POST /api/pending-rules/{id}/approve (Backend 8002)
getRulePerformance(period) → GET /api/rules/performance (Backend 8002)
```

**DashboardService.ts** uses these endpoints:
```typescript
getOverview() → GET /api/dashboard/overview
getStats() → GET /api/dashboard/stats
getEngineStatus() → GET /api/system/engines
```

---

## Summary

**Total API Endpoints**: 60+
- **Delivery Engine (8005)**: 35+ endpoints
- **Backend Engine (8002)**: 20+ endpoints
- **Other Engines**: 5+ endpoints each

**WebSocket Channels**: 15+ channels for real-time updates

**All Frontend Components Mapped**: ✅
- Alert Investigation Screen → 8 endpoints
- Rule Management Dashboard → 12 endpoints
- Dashboard Overview → 5 endpoints
- Entity Explorer → 6 endpoints
- Investigation Workspace → 10 endpoints

**Status**: All frontend components have complete backend API coverage.
