import React, { Suspense, lazy } from 'react'

// Lazy load widget components
const AlertQueue = lazy(() => import('./AlertQueue'))
const EntityGraph = lazy(() => import('./EntityGraph'))
const PatternLibrary = lazy(() => import('./PatternLibrary'))
const CTIFeeds = lazy(() => import('./CTIFeeds'))
const CaseTimeline = lazy(() => import('./CaseTimeline'))
const MetricsDashboard = lazy(() => import('./MetricsDashboard'))

// Widget component map
const componentMap: Record<string, React.LazyExoticComponent<React.ComponentType<any>>> = {
  AlertQueue,
  EntityGraph,
  PatternLibrary,
  CTIFeeds,
  CaseTimeline,
  MetricsDashboard,
}

interface WidgetFactoryProps {
  componentName: string
  config?: any
}

// Loading fallback component
const WidgetLoading: React.FC = () => (
  <div className="flex items-center justify-center h-full bg-gray-50">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
      <p className="mt-4 text-gray-600">Loading widget...</p>
    </div>
  </div>
)

// Error fallback component
const WidgetError: React.FC<{ componentName: string }> = ({ componentName }) => (
  <div className="flex items-center justify-center h-full bg-red-50">
    <div className="text-center text-red-600">
      <p className="font-semibold">Widget Not Found</p>
      <p className="text-sm mt-2">Component: {componentName}</p>
    </div>
  </div>
)

export const WidgetFactory: React.FC<WidgetFactoryProps> = ({ componentName, config }) => {
  const Component = componentMap[componentName]

  if (!Component) {
    return <WidgetError componentName={componentName} />
  }

  return (
    <Suspense fallback={<WidgetLoading />}>
      <Component {...config} />
    </Suspense>
  )
}

export default WidgetFactory