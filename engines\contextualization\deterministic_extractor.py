"""
Deterministic Entity Extractor
Extracts entities using pre-generated mappings (NO AI, FREE, FAST)
"""

from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


def extract_value_by_path(obj: Any, path: str) -> Optional[Any]:
    """
    Extract value from nested object using JSON path notation

    Supports:
    - Dot notation: "source.ip"
    - Array notation: "data[0].ip" or "data[].ip"
    - Mixed: "content.log.data[0].data.source.ip"

    Args:
        obj: Object to extract from
        path: JSON path string

    Returns:
        Extracted value or None if not found
    """
    if not path or not obj:
        return None

    try:
        # Split path into components
        # Handle both "data[0]" and "data[]" formats
        parts = path.replace('[', '.').replace(']', '').split('.')
        current = obj

        for part in parts:
            if not part:  # Skip empty parts from splitting
                continue

            # Check if it's an array index
            if part.isdigit():
                index = int(part)
                if isinstance(current, list) and len(current) > index:
                    current = current[index]
                else:
                    return None

            # Dictionary key
            elif isinstance(current, dict):
                current = current.get(part)
                if current is None:
                    return None

            # Array with [] notation - take first element
            elif isinstance(current, list) and len(current) > 0:
                current = current[0]
                # Try to get the part from first element
                if isinstance(current, dict):
                    current = current.get(part)
                    if current is None:
                        return None
                else:
                    return None

            else:
                return None

        return current

    except Exception as e:
        logger.debug(f"Failed to extract path '{path}': {e}")
        return None


def extract_entities_with_mapping(log: Dict[str, Any], entity_mapping: Dict[str, str]) -> List[Dict[str, Any]]:
    """
    Extract entities using stored mapping (deterministic, FREE, FAST)

    Cost: $0
    Time: <1ms per log

    Args:
        log: Log dictionary
        entity_mapping: Dict mapping entity_type -> JSON path

    Returns:
        List of extracted entities
    """
    entities = []

    for entity_type, json_path in entity_mapping.items():
        try:
            # Extract value
            value = extract_value_by_path(log, json_path)

            if value is not None:
                # Handle both single values and arrays
                if isinstance(value, list):
                    # Extract from each array element
                    for item in value:
                        if item:  # Skip None/empty values
                            entities.append({
                                'type': entity_type,
                                'value': str(item),
                                'extracted_from': json_path,
                                'extraction_method': 'deterministic'
                            })
                else:
                    # Single value
                    entities.append({
                        'type': entity_type,
                        'value': str(value),
                        'extracted_from': json_path,
                        'extraction_method': 'deterministic'
                    })

        except Exception as e:
            logger.debug(f"Failed to extract {entity_type} from {json_path}: {e}")
            continue

    # Deduplicate entities (same type + value)
    seen = set()
    unique_entities = []

    for entity in entities:
        key = f"{entity['type']}:{entity['value']}"
        if key not in seen:
            seen.add(key)
            unique_entities.append(entity)

    return unique_entities


class DeterministicExtractor:
    """
    Fast deterministic entity extraction using stored mappings
    """

    def __init__(self, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.extraction_stats = {
            'total_logs': 0,
            'total_entities': 0,
            'successful': 0,
            'failed': 0
        }

    def extract(self, log: Dict[str, Any], entity_mapping: Dict[str, str]) -> List[Dict[str, Any]]:
        """
        Extract entities from log using mapping

        Args:
            log: Log dictionary
            entity_mapping: Entity type -> JSON path mappings

        Returns:
            List of extracted entities
        """
        self.extraction_stats['total_logs'] += 1

        try:
            entities = extract_entities_with_mapping(log, entity_mapping)

            if entities:
                self.extraction_stats['successful'] += 1
                self.extraction_stats['total_entities'] += len(entities)
                self.logger.debug(f"Extracted {len(entities)} entities using deterministic mapping")
            else:
                self.extraction_stats['failed'] += 1
                self.logger.warning(f"No entities extracted (empty result)")

            return entities

        except Exception as e:
            self.extraction_stats['failed'] += 1
            self.logger.error(f"Extraction failed: {e}")
            return []

    def get_stats(self) -> Dict[str, Any]:
        """Get extraction statistics"""
        stats = self.extraction_stats.copy()

        if stats['total_logs'] > 0:
            stats['success_rate'] = (stats['successful'] / stats['total_logs']) * 100
            stats['avg_entities_per_log'] = stats['total_entities'] / stats['total_logs']
        else:
            stats['success_rate'] = 0.0
            stats['avg_entities_per_log'] = 0.0

        return stats

    def reset_stats(self):
        """Reset statistics"""
        self.extraction_stats = {
            'total_logs': 0,
            'total_entities': 0,
            'successful': 0,
            'failed': 0
        }
