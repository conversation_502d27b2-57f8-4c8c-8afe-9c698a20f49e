#!/usr/bin/env python3
"""
SIEMLess v2.0 Malware Analysis Engine

Specialized malware analysis, reverse engineering, and threat attribution capabilities.
Focused purely on technical malware analysis without case management overlap.

Author: SIEMLess Development Team
Version: 2.0.0
"""

import asyncio
import json
import uuid
import hashlib
import magic
import yara
from datetime import datetime, <PERSON><PERSON><PERSON>
from enum import Enum
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
import logging
import subprocess
import tempfile
import os
import requests

from shared.base_engine import BaseEngine
from shared.queue.queue_manager import QueueManager
from shared.logging.siemless_logger import SIEMLessLogger

class AnalysisType(Enum):
    """Types of malware analysis"""
    STATIC = "static"
    DYNAMIC = "dynamic"
    BEHAVIORAL = "behavioral"
    REVERSE_ENGINEERING = "reverse_engineering"
    SIGNATURE_DETECTION = "signature_detection"
    HEURISTIC = "heuristic"
    ML_CLASSIFICATION = "ml_classification"

class MalwareFamily(Enum):
    """Known malware families"""
    UNKNOWN = "unknown"
    TROJAN = "trojan"
    RANSOMWARE = "ransomware"
    BACKDOOR = "backdoor"
    SPYWARE = "spyware"
    ROOTKIT = "rootkit"
    WORM = "worm"
    ADWARE = "adware"
    LOADER = "loader"
    DROPPER = "dropper"
    RAT = "rat"
    CRYPTOMINER = "cryptominer"

class ThreatLevel(Enum):
    """Threat level assessment"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    BENIGN = "benign"
    UNKNOWN = "unknown"

class AnalysisStatus(Enum):
    """Analysis status"""
    QUEUED = "queued"
    PREPROCESSING = "preprocessing"
    ANALYZING = "analyzing"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"

@dataclass
class MalwareSignature:
    """Malware signature detection result"""
    signature_id: str
    signature_name: str
    signature_type: str  # yara, clamav, custom
    confidence: float
    description: str
    malware_family: str
    severity: str
    references: List[str]
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class StaticAnalysisResult:
    """Static analysis results"""
    file_hash: str
    file_type: str
    file_size: int
    entropy: float
    pe_info: Optional[Dict[str, Any]]
    strings: List[str]
    imports: List[str]
    exports: List[str]
    sections: List[Dict[str, Any]]
    certificates: List[Dict[str, Any]]
    suspicious_indicators: List[str]
    embedded_files: List[str]
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class DynamicAnalysisResult:
    """Dynamic analysis results from sandbox"""
    execution_time: int
    processes_created: List[Dict[str, Any]]
    files_created: List[str]
    files_modified: List[str]
    files_deleted: List[str]
    registry_keys: List[Dict[str, Any]]
    network_connections: List[Dict[str, Any]]
    dns_queries: List[str]
    api_calls: List[Dict[str, Any]]
    screenshots: List[str]
    memory_dumps: List[str]
    behavior_summary: str
    sandbox_environment: str
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class BehavioralPattern:
    """Behavioral pattern detected"""
    pattern_id: str
    pattern_name: str
    pattern_type: str
    confidence: float
    description: str
    mitre_techniques: List[str]
    indicators: List[str]
    risk_score: float

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class ThreatAttribution:
    """Threat attribution analysis"""
    threat_actor: Optional[str]
    campaign: Optional[str]
    attribution_confidence: float
    attribution_reasons: List[str]
    similar_samples: List[str]
    geographic_origin: Optional[str]
    target_sectors: List[str]
    first_seen: Optional[datetime]
    last_seen: Optional[datetime]

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        if self.first_seen:
            data['first_seen'] = self.first_seen.isoformat()
        if self.last_seen:
            data['last_seen'] = self.last_seen.isoformat()
        return data

@dataclass
class MalwareAnalysis:
    """Complete malware analysis result"""
    analysis_id: str
    sample_hash: str
    sample_name: str
    submitted_by: str
    submitted_at: datetime
    completed_at: Optional[datetime]
    analysis_status: AnalysisStatus
    analysis_types: List[AnalysisType]

    # File metadata
    file_size: int
    file_type: str
    mime_type: str
    first_seen: datetime

    # Analysis results
    static_analysis: Optional[StaticAnalysisResult]
    dynamic_analysis: Optional[DynamicAnalysisResult]
    signatures_detected: List[MalwareSignature]
    behavioral_patterns: List[BehavioralPattern]

    # Classification and attribution
    malware_family: MalwareFamily
    threat_level: ThreatLevel
    threat_attribution: Optional[ThreatAttribution]

    # Intelligence and context
    similar_samples: List[str]
    related_campaigns: List[str]
    iocs_extracted: List[Dict[str, Any]]
    yara_rules_matched: List[str]

    # Recommendations
    containment_actions: List[str]
    remediation_steps: List[str]
    detection_rules: List[Dict[str, Any]]

    # Metadata
    analysis_engine_version: str
    sandbox_used: Optional[str]
    analysis_duration: Optional[int]
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['submitted_at'] = self.submitted_at.isoformat()
        if self.completed_at:
            data['completed_at'] = self.completed_at.isoformat()
        data['first_seen'] = self.first_seen.isoformat()
        data['analysis_status'] = self.analysis_status.value
        data['analysis_types'] = [at.value for at in self.analysis_types]
        data['malware_family'] = self.malware_family.value
        data['threat_level'] = self.threat_level.value
        if self.static_analysis:
            data['static_analysis'] = self.static_analysis.to_dict()
        if self.dynamic_analysis:
            data['dynamic_analysis'] = self.dynamic_analysis.to_dict()
        if self.threat_attribution:
            data['threat_attribution'] = self.threat_attribution.to_dict()
        data['signatures_detected'] = [s.to_dict() for s in self.signatures_detected]
        data['behavioral_patterns'] = [p.to_dict() for p in self.behavioral_patterns]
        return data

class MalwareAnalysisEngine(BaseEngine):
    """
    Malware Analysis Engine for SIEMLess v2.0

    Provides comprehensive malware analysis capabilities including:
    - Static analysis (PE parsing, string extraction, etc.)
    - Dynamic analysis (sandbox execution)
    - Signature detection (YARA, ClamAV)
    - Behavioral pattern recognition
    - Threat attribution and intelligence
    """

    def __init__(self):
        super().__init__("malware_analysis", "2.0.0")
        self.analyses: Dict[str, MalwareAnalysis] = {}
        self.yara_rules: Dict[str, Any] = {}
        self.signature_database: Dict[str, List[MalwareSignature]] = {}
        self.behavioral_patterns: Dict[str, BehavioralPattern] = {}
        self.threat_intel_cache: Dict[str, Dict[str, Any]] = {}

        # Analysis queue
        self.analysis_queue: List[str] = []
        self.max_concurrent_analyses = 3
        self.current_analyses: Dict[str, asyncio.Task] = {}

        # Initialize analysis components
        self._initialize_signature_database()
        self._initialize_behavioral_patterns()
        self._load_yara_rules()

    async def initialize(self) -> bool:
        """Initialize the Malware Analysis Engine"""
        try:
            self.logger.info("Initializing Malware Analysis Engine v2.0")

            # Register message handlers
            await self.queue_manager.register_handler(
                "malware.submit_sample", self._handle_submit_sample
            )
            await self.queue_manager.register_handler(
                "malware.analyze", self._handle_analyze_request
            )
            await self.queue_manager.register_handler(
                "malware.get_results", self._handle_get_results
            )
            await self.queue_manager.register_handler(
                "investigation.sample_found", self._handle_investigation_sample
            )

            # Start background tasks
            asyncio.create_task(self._analysis_queue_processor())
            asyncio.create_task(self._threat_intel_updater())
            asyncio.create_task(self._signature_updater())

            self.logger.info("Malware Analysis Engine initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Malware Analysis Engine: {e}")
            return False

    def _initialize_signature_database(self):
        """Initialize signature database with known patterns"""
        self.signature_database = {
            "trojan": [
                MalwareSignature(
                    signature_id="trojan_001",
                    signature_name="Generic Trojan Pattern",
                    signature_type="heuristic",
                    confidence=0.7,
                    description="Generic trojan behavior pattern",
                    malware_family="trojan",
                    severity="high",
                    references=["internal"],
                    metadata={"created": "2024-01-01"}
                )
            ],
            "ransomware": [
                MalwareSignature(
                    signature_id="ransom_001",
                    signature_name="Ransomware File Encryption",
                    signature_type="behavioral",
                    confidence=0.9,
                    description="File encryption behavior typical of ransomware",
                    malware_family="ransomware",
                    severity="critical",
                    references=["mitre:T1486"],
                    metadata={"techniques": ["T1486"]}
                )
            ],
            "cryptominer": [
                MalwareSignature(
                    signature_id="miner_001",
                    signature_name="Cryptocurrency Mining",
                    signature_type="behavioral",
                    confidence=0.8,
                    description="Cryptocurrency mining behavior",
                    malware_family="cryptominer",
                    severity="medium",
                    references=["mitre:T1496"],
                    metadata={"techniques": ["T1496"]}
                )
            ]
        }

    def _initialize_behavioral_patterns(self):
        """Initialize behavioral pattern database"""
        self.behavioral_patterns = {
            "persistence_registry": BehavioralPattern(
                pattern_id="persist_001",
                pattern_name="Registry Persistence",
                pattern_type="persistence",
                confidence=0.8,
                description="Malware establishing persistence via registry",
                mitre_techniques=["T1547.001"],
                indicators=["registry_run_key", "startup_folder"],
                risk_score=7.5
            ),
            "data_exfiltration": BehavioralPattern(
                pattern_id="exfil_001",
                pattern_name="Data Exfiltration",
                pattern_type="exfiltration",
                confidence=0.9,
                description="Data being exfiltrated to external servers",
                mitre_techniques=["T1041"],
                indicators=["large_upload", "encrypted_traffic"],
                risk_score=9.0
            ),
            "process_injection": BehavioralPattern(
                pattern_id="inject_001",
                pattern_name="Process Injection",
                pattern_type="defense_evasion",
                confidence=0.85,
                description="Code injection into legitimate processes",
                mitre_techniques=["T1055"],
                indicators=["dll_injection", "process_hollowing"],
                risk_score=8.5
            )
        }

    def _load_yara_rules(self):
        """Load YARA rules for signature detection"""
        # This would load actual YARA rules from files
        # For demo purposes, we'll simulate rule loading
        self.yara_rules = {
            "malware_generic": {
                "rule_text": "rule MalwareGeneric { strings: $a = \"malware\" condition: $a }",
                "compiled": None  # Would be compiled YARA rule
            },
            "ransomware_strings": {
                "rule_text": "rule Ransomware { strings: $encrypt = \"encrypt\" $ransom = \"ransom\" condition: all of them }",
                "compiled": None
            }
        }

    async def submit_sample(self, sample_data: Dict[str, Any]) -> str:
        """Submit a malware sample for analysis"""
        try:
            analysis_id = str(uuid.uuid4())
            current_time = datetime.utcnow()

            # Calculate file hash if not provided
            file_hash = sample_data.get('file_hash')
            if not file_hash and sample_data.get('file_path'):
                file_hash = await self._calculate_file_hash(sample_data['file_path'])

            # Basic file analysis
            file_info = await self._get_file_info(sample_data.get('file_path', ''))

            analysis = MalwareAnalysis(
                analysis_id=analysis_id,
                sample_hash=file_hash or "unknown",
                sample_name=sample_data.get('sample_name', f"sample_{analysis_id[:8]}"),
                submitted_by=sample_data['submitted_by'],
                submitted_at=current_time,
                completed_at=None,
                analysis_status=AnalysisStatus.QUEUED,
                analysis_types=[AnalysisType(at) for at in sample_data.get('analysis_types', ['static', 'dynamic'])],
                file_size=file_info.get('size', 0),
                file_type=file_info.get('type', 'unknown'),
                mime_type=file_info.get('mime_type', 'unknown'),
                first_seen=current_time,
                static_analysis=None,
                dynamic_analysis=None,
                signatures_detected=[],
                behavioral_patterns=[],
                malware_family=MalwareFamily.UNKNOWN,
                threat_level=ThreatLevel.UNKNOWN,
                threat_attribution=None,
                similar_samples=[],
                related_campaigns=[],
                iocs_extracted=[],
                yara_rules_matched=[],
                containment_actions=[],
                remediation_steps=[],
                detection_rules=[],
                analysis_engine_version="2.0.0",
                sandbox_used=sample_data.get('sandbox', None),
                analysis_duration=None,
                metadata=sample_data.get('metadata', {})
            )

            # Store the analysis
            self.analyses[analysis_id] = analysis

            # Add to analysis queue
            self.analysis_queue.append(analysis_id)

            # Broadcast sample submission
            await self.queue_manager.publish("malware.sample_submitted", {
                "analysis_id": analysis_id,
                "sample_hash": file_hash,
                "submitted_by": sample_data['submitted_by']
            })

            self.logger.info(f"Submitted malware sample {analysis_id} for analysis")
            return analysis_id

        except Exception as e:
            self.logger.error(f"Failed to submit malware sample: {e}")
            raise

    async def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA256 hash of a file"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except Exception as e:
            self.logger.error(f"Failed to calculate file hash: {e}")
            return "unknown"

    async def _get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get basic file information"""
        try:
            if not file_path or not os.path.exists(file_path):
                return {"size": 0, "type": "unknown", "mime_type": "unknown"}

            # Get file size
            file_size = os.path.getsize(file_path)

            # Get file type using python-magic
            try:
                mime_type = magic.from_file(file_path, mime=True)
                file_type = magic.from_file(file_path)
            except:
                mime_type = "unknown"
                file_type = "unknown"

            return {
                "size": file_size,
                "type": file_type,
                "mime_type": mime_type
            }

        except Exception as e:
            self.logger.error(f"Failed to get file info: {e}")
            return {"size": 0, "type": "unknown", "mime_type": "unknown"}

    async def _analysis_queue_processor(self):
        """Process the malware analysis queue"""
        while True:
            try:
                # Check if we can start new analyses
                if (len(self.current_analyses) < self.max_concurrent_analyses and
                    len(self.analysis_queue) > 0):

                    analysis_id = self.analysis_queue.pop(0)

                    # Start analysis task
                    task = asyncio.create_task(self._perform_analysis(analysis_id))
                    self.current_analyses[analysis_id] = task

                # Clean up completed tasks
                completed_tasks = []
                for analysis_id, task in self.current_analyses.items():
                    if task.done():
                        completed_tasks.append(analysis_id)

                for analysis_id in completed_tasks:
                    del self.current_analyses[analysis_id]

                # Wait before next iteration
                await asyncio.sleep(5)

            except Exception as e:
                self.logger.error(f"Error in analysis queue processor: {e}")
                await asyncio.sleep(30)

    async def _perform_analysis(self, analysis_id: str):
        """Perform comprehensive malware analysis"""
        try:
            if analysis_id not in self.analyses:
                return

            analysis = self.analyses[analysis_id]
            start_time = datetime.utcnow()

            self.logger.info(f"Starting analysis for {analysis_id}")
            analysis.analysis_status = AnalysisStatus.PREPROCESSING

            # Phase 1: Static Analysis
            if AnalysisType.STATIC in analysis.analysis_types:
                analysis.analysis_status = AnalysisStatus.ANALYZING
                static_result = await self._perform_static_analysis(analysis)
                analysis.static_analysis = static_result

            # Phase 2: Signature Detection
            if AnalysisType.SIGNATURE_DETECTION in analysis.analysis_types:
                signatures = await self._perform_signature_detection(analysis)
                analysis.signatures_detected.extend(signatures)

            # Phase 3: Dynamic Analysis (if sandbox available)
            if AnalysisType.DYNAMIC in analysis.analysis_types and analysis.sandbox_used:
                dynamic_result = await self._perform_dynamic_analysis(analysis)
                analysis.dynamic_analysis = dynamic_result

            # Phase 4: Behavioral Analysis
            if AnalysisType.BEHAVIORAL in analysis.analysis_types:
                patterns = await self._perform_behavioral_analysis(analysis)
                analysis.behavioral_patterns.extend(patterns)

            # Phase 5: Classification and Attribution
            await self._classify_malware(analysis)
            await self._perform_threat_attribution(analysis)

            # Phase 6: IOC Extraction
            await self._extract_iocs(analysis)

            # Phase 7: Generate Recommendations
            await self._generate_recommendations(analysis)

            # Complete analysis
            analysis.completed_at = datetime.utcnow()
            analysis.analysis_duration = int((analysis.completed_at - start_time).total_seconds())
            analysis.analysis_status = AnalysisStatus.COMPLETED

            # Broadcast completion
            await self.queue_manager.publish("malware.analysis_completed", {
                "analysis_id": analysis_id,
                "threat_level": analysis.threat_level.value,
                "malware_family": analysis.malware_family.value,
                "iocs_extracted": len(analysis.iocs_extracted)
            })

            self.logger.info(f"Completed analysis for {analysis_id} in {analysis.analysis_duration}s")

        except Exception as e:
            self.logger.error(f"Failed to perform analysis {analysis_id}: {e}")
            if analysis_id in self.analyses:
                self.analyses[analysis_id].analysis_status = AnalysisStatus.FAILED

    async def _perform_static_analysis(self, analysis: MalwareAnalysis) -> StaticAnalysisResult:
        """Perform static analysis on the malware sample"""
        try:
            # Simulate static analysis
            # In real implementation, this would use tools like pefile, pyelftools, etc.

            result = StaticAnalysisResult(
                file_hash=analysis.sample_hash,
                file_type=analysis.file_type,
                file_size=analysis.file_size,
                entropy=6.5,  # Simulated entropy
                pe_info={
                    "entry_point": "0x1000",
                    "image_base": "0x400000",
                    "sections": 5,
                    "imports": 23,
                    "exports": 0
                } if "PE" in analysis.file_type else None,
                strings=["malware.exe", "encrypt", "bitcoin", "payload"],  # Simulated strings
                imports=["kernel32.dll", "user32.dll", "advapi32.dll"],  # Simulated imports
                exports=[],
                sections=[
                    {"name": ".text", "virtual_size": 4096, "entropy": 6.8},
                    {"name": ".data", "virtual_size": 2048, "entropy": 3.2}
                ],
                certificates=[],
                suspicious_indicators=[
                    "High entropy section detected",
                    "Suspicious API imports",
                    "No digital signature"
                ],
                embedded_files=[],
                metadata={"analysis_tool": "static_analyzer_v2"}
            )

            return result

        except Exception as e:
            self.logger.error(f"Static analysis failed: {e}")
            raise

    async def _perform_signature_detection(self, analysis: MalwareAnalysis) -> List[MalwareSignature]:
        """Perform signature-based detection"""
        try:
            detected_signatures = []

            # YARA rule matching (simulated)
            for rule_name, rule_data in self.yara_rules.items():
                # Simulate rule matching
                if "malware" in analysis.sample_name.lower():
                    detected_signatures.append(
                        MalwareSignature(
                            signature_id=f"yara_{rule_name}",
                            signature_name=rule_name,
                            signature_type="yara",
                            confidence=0.8,
                            description=f"YARA rule {rule_name} matched",
                            malware_family="unknown",
                            severity="medium",
                            references=[f"yara:{rule_name}"],
                            metadata={"rule_file": rule_name}
                        )
                    )

            # Database signature matching
            for family, signatures in self.signature_database.items():
                # Simulate signature matching based on static analysis
                if analysis.static_analysis:
                    for string in analysis.static_analysis.strings:
                        if family in string.lower():
                            detected_signatures.extend(signatures)

            return detected_signatures

        except Exception as e:
            self.logger.error(f"Signature detection failed: {e}")
            return []

    async def _perform_dynamic_analysis(self, analysis: MalwareAnalysis) -> DynamicAnalysisResult:
        """Perform dynamic analysis using sandbox"""
        try:
            # Simulate sandbox execution
            # In real implementation, this would integrate with Cuckoo, VMRay, etc.

            result = DynamicAnalysisResult(
                execution_time=30,
                processes_created=[
                    {"name": "malware.exe", "pid": 1234, "command_line": "malware.exe"},
                    {"name": "cmd.exe", "pid": 5678, "command_line": "cmd.exe /c whoami"}
                ],
                files_created=[
                    "C:\\temp\\malware_payload.dll",
                    "C:\\Users\\<USER>\\readme.txt"
                ],
                files_modified=[
                    "C:\\Windows\\System32\\hosts"
                ],
                files_deleted=[],
                registry_keys=[
                    {"action": "create", "key": "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\\Malware"}
                ],
                network_connections=[
                    {"protocol": "tcp", "dst_ip": "*************", "dst_port": 80, "direction": "outbound"},
                    {"protocol": "tcp", "dst_ip": "********", "dst_port": 443, "direction": "outbound"}
                ],
                dns_queries=["malicious-domain.com", "c2-server.net"],
                api_calls=[
                    {"api": "CreateFileA", "args": ["C:\\temp\\payload.dll"]},
                    {"api": "RegSetValueExA", "args": ["HKEY_CURRENT_USER\\Software\\..."]}
                ],
                screenshots=["screenshot_01.png", "screenshot_02.png"],
                memory_dumps=["memory_dump.dmp"],
                behavior_summary="Malware executed, created persistence mechanism, attempted network communication",
                sandbox_environment=analysis.sandbox_used or "cuckoo",
                metadata={"sandbox_version": "2.0.7", "analysis_duration": 30}
            )

            return result

        except Exception as e:
            self.logger.error(f"Dynamic analysis failed: {e}")
            raise

    async def _perform_behavioral_analysis(self, analysis: MalwareAnalysis) -> List[BehavioralPattern]:
        """Analyze behavioral patterns from dynamic analysis"""
        try:
            detected_patterns = []

            if not analysis.dynamic_analysis:
                return detected_patterns

            dynamic = analysis.dynamic_analysis

            # Check for persistence patterns
            for reg_key in dynamic.registry_keys:
                if "Run" in reg_key.get("key", ""):
                    detected_patterns.append(self.behavioral_patterns["persistence_registry"])

            # Check for data exfiltration patterns
            large_uploads = [conn for conn in dynamic.network_connections
                           if conn.get("direction") == "outbound" and conn.get("dst_port") in [80, 443]]
            if len(large_uploads) > 3:
                detected_patterns.append(self.behavioral_patterns["data_exfiltration"])

            # Check for process injection patterns
            injection_apis = ["CreateRemoteThread", "VirtualAllocEx", "WriteProcessMemory"]
            if any(api["api"] in injection_apis for api in dynamic.api_calls):
                detected_patterns.append(self.behavioral_patterns["process_injection"])

            return detected_patterns

        except Exception as e:
            self.logger.error(f"Behavioral analysis failed: {e}")
            return []

    async def _classify_malware(self, analysis: MalwareAnalysis):
        """Classify malware family and threat level"""
        try:
            # Classification based on signatures and patterns
            family_scores = {}

            # Score based on signatures
            for signature in analysis.signatures_detected:
                family = signature.malware_family
                if family not in family_scores:
                    family_scores[family] = 0
                family_scores[family] += signature.confidence

            # Score based on behavioral patterns
            for pattern in analysis.behavioral_patterns:
                if pattern.pattern_type == "persistence":
                    family_scores["trojan"] = family_scores.get("trojan", 0) + 0.3
                elif pattern.pattern_type == "exfiltration":
                    family_scores["spyware"] = family_scores.get("spyware", 0) + 0.4

            # Determine family
            if family_scores:
                best_family = max(family_scores.items(), key=lambda x: x[1])[0]
                try:
                    analysis.malware_family = MalwareFamily(best_family)
                except ValueError:
                    analysis.malware_family = MalwareFamily.UNKNOWN
            else:
                analysis.malware_family = MalwareFamily.UNKNOWN

            # Determine threat level
            max_signature_confidence = max([s.confidence for s in analysis.signatures_detected], default=0)
            pattern_risk = max([p.risk_score for p in analysis.behavioral_patterns], default=0)

            overall_risk = (max_signature_confidence * 10 + pattern_risk) / 2

            if overall_risk >= 8.5:
                analysis.threat_level = ThreatLevel.CRITICAL
            elif overall_risk >= 7.0:
                analysis.threat_level = ThreatLevel.HIGH
            elif overall_risk >= 5.0:
                analysis.threat_level = ThreatLevel.MEDIUM
            elif overall_risk >= 2.0:
                analysis.threat_level = ThreatLevel.LOW
            else:
                analysis.threat_level = ThreatLevel.BENIGN

        except Exception as e:
            self.logger.error(f"Malware classification failed: {e}")
            analysis.malware_family = MalwareFamily.UNKNOWN
            analysis.threat_level = ThreatLevel.UNKNOWN

    async def _perform_threat_attribution(self, analysis: MalwareAnalysis):
        """Perform threat attribution analysis"""
        try:
            # Simulate threat attribution
            # In real implementation, this would use threat intel databases

            attribution_confidence = 0.0
            threat_actor = None
            campaign = None
            reasons = []

            # Check for known indicators
            if analysis.dynamic_analysis:
                for domain in analysis.dynamic_analysis.dns_queries:
                    if "apt" in domain.lower():
                        threat_actor = "APT Group"
                        attribution_confidence += 0.4
                        reasons.append(f"DNS query to known APT domain: {domain}")

            # Check behavioral patterns for attribution
            for pattern in analysis.behavioral_patterns:
                if pattern.pattern_name == "Data Exfiltration" and pattern.confidence > 0.8:
                    attribution_confidence += 0.3
                    reasons.append("High-confidence data exfiltration behavior")

            if attribution_confidence > 0.5:
                analysis.threat_attribution = ThreatAttribution(
                    threat_actor=threat_actor,
                    campaign=campaign,
                    attribution_confidence=attribution_confidence,
                    attribution_reasons=reasons,
                    similar_samples=[],
                    geographic_origin=None,
                    target_sectors=["financial", "healthcare"],
                    first_seen=analysis.first_seen,
                    last_seen=analysis.submitted_at
                )

        except Exception as e:
            self.logger.error(f"Threat attribution failed: {e}")

    async def _extract_iocs(self, analysis: MalwareAnalysis):
        """Extract Indicators of Compromise (IOCs)"""
        try:
            iocs = []

            # Extract from static analysis
            if analysis.static_analysis:
                # File-based IOCs
                iocs.append({
                    "type": "file_hash",
                    "value": analysis.sample_hash,
                    "confidence": 1.0,
                    "description": "Sample file hash"
                })

                # String-based IOCs
                for string in analysis.static_analysis.strings:
                    if self._is_domain(string):
                        iocs.append({
                            "type": "domain",
                            "value": string,
                            "confidence": 0.7,
                            "description": "Domain found in strings"
                        })
                    elif self._is_ip(string):
                        iocs.append({
                            "type": "ip",
                            "value": string,
                            "confidence": 0.8,
                            "description": "IP address found in strings"
                        })

            # Extract from dynamic analysis
            if analysis.dynamic_analysis:
                # Network IOCs
                for conn in analysis.dynamic_analysis.network_connections:
                    iocs.append({
                        "type": "ip",
                        "value": conn.get("dst_ip"),
                        "confidence": 0.9,
                        "description": "Network connection destination"
                    })

                # DNS IOCs
                for domain in analysis.dynamic_analysis.dns_queries:
                    iocs.append({
                        "type": "domain",
                        "value": domain,
                        "confidence": 0.9,
                        "description": "DNS query observed"
                    })

                # File IOCs
                for file_path in analysis.dynamic_analysis.files_created:
                    iocs.append({
                        "type": "file_path",
                        "value": file_path,
                        "confidence": 0.8,
                        "description": "File created during execution"
                    })

            analysis.iocs_extracted = iocs

        except Exception as e:
            self.logger.error(f"IOC extraction failed: {e}")

    def _is_domain(self, string: str) -> bool:
        """Check if string is a domain name"""
        import re
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$'
        return bool(re.match(domain_pattern, string))

    def _is_ip(self, string: str) -> bool:
        """Check if string is an IP address"""
        import re
        ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        return bool(re.match(ip_pattern, string))

    async def _generate_recommendations(self, analysis: MalwareAnalysis):
        """Generate containment and remediation recommendations"""
        try:
            containment = []
            remediation = []
            detection_rules = []

            # Containment actions based on threat level
            if analysis.threat_level in [ThreatLevel.CRITICAL, ThreatLevel.HIGH]:
                containment.extend([
                    "Immediately isolate affected systems from network",
                    "Block all IOCs at network perimeter",
                    "Implement emergency incident response procedures"
                ])

            # Family-specific recommendations
            if analysis.malware_family == MalwareFamily.RANSOMWARE:
                containment.extend([
                    "Prevent lateral movement by disabling file shares",
                    "Backup critical systems immediately",
                    "Notify law enforcement if required"
                ])
                remediation.extend([
                    "Restore systems from clean backups",
                    "Implement application whitelisting",
                    "Review backup and recovery procedures"
                ])

            elif analysis.malware_family == MalwareFamily.BACKDOOR:
                containment.extend([
                    "Change all administrative passwords",
                    "Review user accounts for unauthorized access"
                ])
                remediation.extend([
                    "Rebuild compromised systems",
                    "Implement multi-factor authentication",
                    "Conduct full security audit"
                ])

            # Generate detection rules
            for ioc in analysis.iocs_extracted:
                if ioc["type"] == "domain":
                    detection_rules.append({
                        "rule_type": "dns_block",
                        "rule_content": f"block dns query {ioc['value']}",
                        "description": f"Block DNS queries to malicious domain: {ioc['value']}"
                    })
                elif ioc["type"] == "ip":
                    detection_rules.append({
                        "rule_type": "firewall",
                        "rule_content": f"block ip {ioc['value']}",
                        "description": f"Block communication with malicious IP: {ioc['value']}"
                    })

            analysis.containment_actions = containment
            analysis.remediation_steps = remediation
            analysis.detection_rules = detection_rules

        except Exception as e:
            self.logger.error(f"Recommendation generation failed: {e}")

    async def _handle_submit_sample(self, message: Dict[str, Any]):
        """Handle sample submission requests"""
        try:
            sample_data = message.get('sample_data', {})
            analysis_id = await self.submit_sample(sample_data)

            if 'reply_to' in message:
                await self.queue_manager.publish(message['reply_to'], {
                    "success": True,
                    "analysis_id": analysis_id
                })

        except Exception as e:
            self.logger.error(f"Error handling sample submission: {e}")
            if 'reply_to' in message:
                await self.queue_manager.publish(message['reply_to'], {
                    "success": False,
                    "error": str(e)
                })

    async def _handle_analyze_request(self, message: Dict[str, Any]):
        """Handle analysis requests"""
        try:
            analysis_id = message.get('analysis_id')

            if analysis_id in self.analyses:
                # Add to priority queue if not already analyzing
                if analysis_id not in self.current_analyses and analysis_id not in self.analysis_queue:
                    self.analysis_queue.insert(0, analysis_id)  # Priority queue

        except Exception as e:
            self.logger.error(f"Error handling analysis request: {e}")

    async def _handle_get_results(self, message: Dict[str, Any]):
        """Handle results retrieval requests"""
        try:
            analysis_id = message.get('analysis_id')

            if 'reply_to' in message:
                if analysis_id in self.analyses:
                    analysis = self.analyses[analysis_id]
                    await self.queue_manager.publish(message['reply_to'], {
                        "success": True,
                        "analysis": analysis.to_dict()
                    })
                else:
                    await self.queue_manager.publish(message['reply_to'], {
                        "success": False,
                        "error": "Analysis not found"
                    })

        except Exception as e:
            self.logger.error(f"Error handling results request: {e}")

    async def _handle_investigation_sample(self, message: Dict[str, Any]):
        """Handle samples found during investigations"""
        try:
            sample_data = message.get('sample_data', {})
            investigation_id = message.get('investigation_id')

            # Auto-submit sample for analysis
            sample_data['submitted_by'] = 'investigation_engine'
            sample_data['metadata'] = sample_data.get('metadata', {})
            sample_data['metadata']['investigation_id'] = investigation_id

            analysis_id = await self.submit_sample(sample_data)

            # Notify investigation engine
            await self.queue_manager.publish("investigation.malware_analysis_started", {
                "investigation_id": investigation_id,
                "analysis_id": analysis_id
            })

        except Exception as e:
            self.logger.error(f"Error handling investigation sample: {e}")

    async def _threat_intel_updater(self):
        """Update threat intelligence periodically"""
        while True:
            try:
                # Update threat intel cache
                # In real implementation, this would fetch from threat intel feeds
                self.logger.info("Updating threat intelligence database")

                # Sleep for 1 hour
                await asyncio.sleep(3600)

            except Exception as e:
                self.logger.error(f"Error updating threat intel: {e}")
                await asyncio.sleep(600)

    async def _signature_updater(self):
        """Update signature database periodically"""
        while True:
            try:
                # Update signatures
                # In real implementation, this would fetch from signature providers
                self.logger.info("Updating malware signatures")

                # Sleep for 4 hours
                await asyncio.sleep(14400)

            except Exception as e:
                self.logger.error(f"Error updating signatures: {e}")
                await asyncio.sleep(1800)

    async def get_analysis(self, analysis_id: str) -> Optional[MalwareAnalysis]:
        """Get analysis by ID"""
        return self.analyses.get(analysis_id)

    async def list_analyses(self, filters: Dict[str, Any] = None) -> List[MalwareAnalysis]:
        """List analyses with optional filters"""
        analyses = list(self.analyses.values())

        if filters:
            if 'status' in filters:
                analyses = [a for a in analyses if a.analysis_status.value == filters['status']]
            if 'threat_level' in filters:
                analyses = [a for a in analyses if a.threat_level.value == filters['threat_level']]
            if 'malware_family' in filters:
                analyses = [a for a in analyses if a.malware_family.value == filters['malware_family']]

        # Sort by submission date
        analyses.sort(key=lambda a: a.submitted_at, reverse=True)
        return analyses

    async def get_analysis_metrics(self) -> Dict[str, Any]:
        """Get malware analysis metrics"""
        total_analyses = len(self.analyses)
        if total_analyses == 0:
            return {"total_analyses": 0}

        # Status distribution
        status_dist = {}
        for analysis in self.analyses.values():
            status = analysis.analysis_status.value
            status_dist[status] = status_dist.get(status, 0) + 1

        # Threat level distribution
        threat_dist = {}
        for analysis in self.analyses.values():
            threat = analysis.threat_level.value
            threat_dist[threat] = threat_dist.get(threat, 0) + 1

        # Family distribution
        family_dist = {}
        for analysis in self.analyses.values():
            family = analysis.malware_family.value
            family_dist[family] = family_dist.get(family, 0) + 1

        # Analysis metrics
        completed_analyses = [a for a in self.analyses.values() if a.analysis_status == AnalysisStatus.COMPLETED]
        avg_duration = sum(a.analysis_duration or 0 for a in completed_analyses) / len(completed_analyses) if completed_analyses else 0

        return {
            "total_analyses": total_analyses,
            "status_distribution": status_dist,
            "threat_level_distribution": threat_dist,
            "family_distribution": family_dist,
            "completed_analyses": len(completed_analyses),
            "avg_analysis_duration": round(avg_duration, 2),
            "queue_length": len(self.analysis_queue),
            "active_analyses": len(self.current_analyses)
        }

async def main():
    """Main entry point for Malware Analysis Engine"""
    engine = MalwareAnalysisEngine()

    if await engine.initialize():
        print("Malware Analysis Engine v2.0 started successfully")
        try:
            await asyncio.sleep(float('inf'))
        except KeyboardInterrupt:
            print("Shutting down Malware Analysis Engine...")
    else:
        print("Failed to start Malware Analysis Engine")
        return 1

    return 0

if __name__ == "__main__":
    asyncio.run(main())