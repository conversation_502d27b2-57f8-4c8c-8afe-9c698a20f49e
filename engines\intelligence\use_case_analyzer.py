"""
AI Use Case Analyzer
Analyzes investigation context and provides actionable security insights
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum


class UseCaseCategory(Enum):
    """Categories of security use cases"""
    LATERAL_MOVEMENT = "lateral_movement"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    DATA_EXFILTRATION = "data_exfiltration"
    MALWARE_EXECUTION = "malware_execution"
    RECONNAISSANCE = "reconnaissance"
    PERSISTENCE = "persistence"
    CREDENTIAL_ACCESS = "credential_access"
    COMMAND_AND_CONTROL = "command_and_control"
    INITIAL_ACCESS = "initial_access"
    IMPACT = "impact"


class UseCaseSeverity(Enum):
    """Severity levels for use case findings"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class UseCaseAnalyzer:
    """
    Analyzes context data from plugins and generates security use case insights
    Uses AI when needed, crystallized patterns when available
    """

    def __init__(self, intelligence_client=None):
        self.intelligence_client = intelligence_client

        # Crystallized use case patterns (learn once, use forever)
        self.use_case_patterns = self._load_crystallized_patterns()

    def _load_crystallized_patterns(self) -> Dict:
        """Load pre-validated use case patterns from pattern library"""
        return {
            # Pattern-based use cases (deterministic, free)
            'lateral_movement': {
                'psexec_execution': {
                    'indicators': ['psexec', 'admin$', 'smb', 'remote_exec'],
                    'severity': UseCaseSeverity.HIGH,
                    'confidence': 0.90
                },
                'rdp_anomaly': {
                    'indicators': ['rdp', 'port_3389', 'unusual_time', 'unusual_source'],
                    'severity': UseCaseSeverity.MEDIUM,
                    'confidence': 0.75
                }
            },
            'privilege_escalation': {
                'uac_bypass': {
                    'indicators': ['fodhelper', 'eventvwr', 'registry_hijack'],
                    'severity': UseCaseSeverity.HIGH,
                    'confidence': 0.85
                },
                'token_manipulation': {
                    'indicators': ['SeDebugPrivilege', 'token_theft', 'process_injection'],
                    'severity': UseCaseSeverity.CRITICAL,
                    'confidence': 0.95
                }
            },
            'data_exfiltration': {
                'large_upload': {
                    'indicators': ['upload_size_anomaly', 'cloud_storage', 'after_hours'],
                    'severity': UseCaseSeverity.HIGH,
                    'confidence': 0.80
                },
                'compression_before_upload': {
                    'indicators': ['7zip', 'rar', 'followed_by_upload', 'unusual_destination'],
                    'severity': UseCaseSeverity.CRITICAL,
                    'confidence': 0.90
                }
            },
            'malware_execution': {
                'suspicious_process_tree': {
                    'indicators': ['office_spawning_powershell', 'powershell_encoded', 'wscript'],
                    'severity': UseCaseSeverity.CRITICAL,
                    'confidence': 0.95
                },
                'living_off_the_land': {
                    'indicators': ['certutil_download', 'bitsadmin', 'regsvr32_network'],
                    'severity': UseCaseSeverity.HIGH,
                    'confidence': 0.85
                }
            }
        }

    async def analyze_context(
        self,
        request_id: str,
        query_context: Dict[str, Any],
        context_results: Dict[str, List[Dict]]
    ) -> Dict[str, Any]:
        """
        Main analysis entry point

        Args:
            request_id: Investigation request ID
            query_context: Original query (hostname, IP, etc.)
            context_results: Results from all context plugins (CrowdStrike, Elastic, etc.)

        Returns:
            Use case analysis results with findings and recommendations
        """

        # Step 1: Extract key indicators from context
        indicators = self._extract_indicators(context_results)

        # Step 2: Check crystallized patterns first (fast, free)
        pattern_findings = self._check_patterns(indicators)

        # Step 3: If no patterns match or confidence low, use AI analysis
        ai_findings = []
        if not pattern_findings or self._needs_ai_analysis(pattern_findings):
            ai_findings = await self._ai_deep_analysis(
                query_context,
                context_results,
                indicators
            )

        # Step 4: Combine findings and generate recommendations
        all_findings = pattern_findings + ai_findings
        recommendations = self._generate_recommendations(all_findings)

        # Step 5: Calculate overall risk score
        risk_score = self._calculate_risk_score(all_findings)

        return {
            'request_id': request_id,
            'timestamp': datetime.utcnow().isoformat(),
            'query_context': query_context,
            'risk_score': risk_score,
            'findings': all_findings,
            'recommendations': recommendations,
            'analysis_method': {
                'pattern_based': len(pattern_findings),
                'ai_based': len(ai_findings)
            }
        }

    def _extract_indicators(self, context_results: Dict[str, List[Dict]]) -> Dict[str, Any]:
        """Extract security indicators from all context sources"""
        indicators = {
            'hosts': [],
            'users': [],
            'processes': [],
            'network': [],
            'detections': [],
            'behaviors': []
        }

        for source_name, results in context_results.items():
            for result in results:
                category = result.get('category')
                data = result.get('data', {})

                # Extract based on category
                if category == 'asset':
                    indicators['hosts'].append({
                        'hostname': data.get('hostname'),
                        'ip': data.get('local_ip') or data.get('all_ips', [None])[0],
                        'os': data.get('os_version'),
                        'users': data.get('users', []),
                        'processes': data.get('processes', []),
                        'source': source_name
                    })

                elif category == 'detection':
                    indicators['detections'].append({
                        'alert_id': data.get('alert_id') or data.get('detection_id'),
                        'severity': data.get('severity'),
                        'rule_name': data.get('rule_name'),
                        'tactic': data.get('tactic'),
                        'technique': data.get('technique'),
                        'hostname': data.get('hostname'),
                        'user': data.get('user_name') or data.get('user'),
                        'process': data.get('process'),
                        'file_hash': data.get('sha256') or data.get('file_hash'),
                        'source': source_name
                    })

                elif category == 'network':
                    indicators['network'].append({
                        'source_ip': data.get('source_ip'),
                        'dest_ip': data.get('destination_ip'),
                        'dest_domain': data.get('destination_domain'),
                        'protocol': data.get('protocol'),
                        'port': data.get('destination_port'),
                        'source': source_name
                    })

                elif category == 'log':
                    # Extract behavioral indicators from logs
                    process = data.get('process')
                    action = data.get('event_action')

                    if process and action:
                        indicators['behaviors'].append({
                            'process': process,
                            'action': action,
                            'user': data.get('user'),
                            'hostname': data.get('hostname'),
                            'source': source_name
                        })

        return indicators

    def _check_patterns(self, indicators: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check indicators against crystallized patterns"""
        findings = []

        # Check each use case category
        for category, patterns in self.use_case_patterns.items():
            for pattern_name, pattern_config in patterns.items():

                # Check if indicators match pattern
                match_score = self._pattern_match_score(
                    indicators,
                    pattern_config['indicators']
                )

                if match_score >= 0.7:  # 70% match threshold
                    findings.append({
                        'finding_id': f"{category}_{pattern_name}_{datetime.utcnow().timestamp()}",
                        'use_case': category,
                        'pattern': pattern_name,
                        'severity': pattern_config['severity'].value,
                        'confidence': pattern_config['confidence'] * match_score,
                        'method': 'pattern',
                        'matched_indicators': self._get_matched_indicators(
                            indicators,
                            pattern_config['indicators']
                        ),
                        'description': self._get_pattern_description(category, pattern_name),
                        'timestamp': datetime.utcnow().isoformat()
                    })

        return findings

    def _pattern_match_score(
        self,
        indicators: Dict[str, Any],
        required_indicators: List[str]
    ) -> float:
        """Calculate how well indicators match a pattern"""
        matches = 0

        # Convert all indicator data to searchable string
        indicator_str = json.dumps(indicators).lower()

        for req_indicator in required_indicators:
            if req_indicator.lower() in indicator_str:
                matches += 1

        return matches / len(required_indicators) if required_indicators else 0

    def _get_matched_indicators(
        self,
        indicators: Dict[str, Any],
        required_indicators: List[str]
    ) -> List[str]:
        """Get list of indicators that matched"""
        matched = []
        indicator_str = json.dumps(indicators).lower()

        for req_indicator in required_indicators:
            if req_indicator.lower() in indicator_str:
                matched.append(req_indicator)

        return matched

    def _get_pattern_description(self, category: str, pattern: str) -> str:
        """Get human-readable description of pattern"""
        descriptions = {
            'lateral_movement': {
                'psexec_execution': 'PsExec-based lateral movement detected',
                'rdp_anomaly': 'Anomalous RDP activity detected'
            },
            'privilege_escalation': {
                'uac_bypass': 'UAC bypass attempt detected',
                'token_manipulation': 'Token manipulation detected (critical)'
            },
            'data_exfiltration': {
                'large_upload': 'Large data upload to external destination',
                'compression_before_upload': 'Data compression followed by upload (critical)'
            },
            'malware_execution': {
                'suspicious_process_tree': 'Suspicious process execution chain',
                'living_off_the_land': 'Living-off-the-land technique detected'
            }
        }

        return descriptions.get(category, {}).get(
            pattern,
            f"{category} - {pattern}"
        )

    def _needs_ai_analysis(self, pattern_findings: List[Dict]) -> bool:
        """Determine if AI analysis is needed"""
        if not pattern_findings:
            return True  # No patterns matched, need AI

        # Check if any finding has low confidence
        for finding in pattern_findings:
            if finding.get('confidence', 0) < 0.8:
                return True  # Low confidence, need AI validation

        return False

    async def _ai_deep_analysis(
        self,
        query_context: Dict,
        context_results: Dict,
        indicators: Dict
    ) -> List[Dict[str, Any]]:
        """Use AI for deep analysis when patterns don't match"""

        if not self.intelligence_client:
            return []  # No AI available

        findings = []

        # Build AI prompt
        prompt = self._build_ai_analysis_prompt(
            query_context,
            context_results,
            indicators
        )

        # Send to Intelligence Engine for AI consensus
        ai_response = await self.intelligence_client.analyze(
            prompt=prompt,
            context={'type': 'use_case_analysis'},
            require_consensus=True  # Use multi-AI validation
        )

        # Parse AI response into findings
        if ai_response and ai_response.get('findings'):
            for finding in ai_response['findings']:
                findings.append({
                    'finding_id': f"ai_{finding.get('type')}_{datetime.utcnow().timestamp()}",
                    'use_case': finding.get('category'),
                    'severity': finding.get('severity'),
                    'confidence': finding.get('confidence'),
                    'method': 'ai_consensus',
                    'description': finding.get('description'),
                    'evidence': finding.get('evidence', []),
                    'ai_reasoning': finding.get('reasoning'),
                    'timestamp': datetime.utcnow().isoformat()
                })

        return findings

    def _build_ai_analysis_prompt(
        self,
        query_context: Dict,
        context_results: Dict,
        indicators: Dict
    ) -> str:
        """Build prompt for AI analysis"""

        query_type = query_context.get('query_type')
        query_value = query_context.get('query_value')

        return f"""
You are a security analyst reviewing investigation context for: {query_type}={query_value}

CONTEXT DATA:
{json.dumps(context_results, indent=2)}

EXTRACTED INDICATORS:
{json.dumps(indicators, indent=2)}

TASK:
Analyze this data and identify security use cases. For each finding, provide:
1. Category (lateral_movement, privilege_escalation, data_exfiltration, etc.)
2. Severity (critical, high, medium, low, info)
3. Confidence (0.0-1.0)
4. Description (what happened)
5. Evidence (specific indicators that support this finding)
6. Reasoning (why this is concerning)

Focus on actionable findings. Return as JSON array.
"""

    def _generate_recommendations(self, findings: List[Dict]) -> List[Dict]:
        """Generate actionable recommendations based on findings"""
        recommendations = []

        # Group by severity
        critical_findings = [f for f in findings if f.get('severity') == 'critical']
        high_findings = [f for f in findings if f.get('severity') == 'high']

        # Critical recommendations
        if critical_findings:
            recommendations.append({
                'priority': 'immediate',
                'action': 'Isolate affected systems',
                'reason': f'{len(critical_findings)} critical findings detected',
                'systems': list(set([
                    f.get('matched_indicators', [None])[0]
                    for f in critical_findings
                ]))
            })

        # High severity recommendations
        if high_findings:
            recommendations.append({
                'priority': 'urgent',
                'action': 'Begin incident response',
                'reason': f'{len(high_findings)} high-severity findings',
                'next_steps': [
                    'Collect memory dumps',
                    'Review recent user activity',
                    'Check for persistence mechanisms'
                ]
            })

        # General recommendations based on use case types
        use_case_types = set([f.get('use_case') for f in findings])

        if 'lateral_movement' in use_case_types:
            recommendations.append({
                'priority': 'high',
                'action': 'Review lateral movement paths',
                'reason': 'Lateral movement detected',
                'tasks': [
                    'Identify compromised credentials',
                    'Check for unauthorized access to sensitive systems',
                    'Review network segmentation'
                ]
            })

        if 'data_exfiltration' in use_case_types:
            recommendations.append({
                'priority': 'critical',
                'action': 'Assess data loss scope',
                'reason': 'Data exfiltration indicators found',
                'tasks': [
                    'Review network logs for upload destinations',
                    'Identify what data was accessed',
                    'Engage legal/compliance teams'
                ]
            })

        return recommendations

    def _calculate_risk_score(self, findings: List[Dict]) -> Dict[str, Any]:
        """Calculate overall risk score"""
        if not findings:
            return {
                'score': 0,
                'level': 'none',
                'breakdown': {}
            }

        # Severity weights
        severity_weights = {
            'critical': 10,
            'high': 7,
            'medium': 4,
            'low': 2,
            'info': 1
        }

        # Calculate weighted score
        total_score = 0
        severity_counts = {}

        for finding in findings:
            severity = finding.get('severity', 'info')
            confidence = finding.get('confidence', 0.5)

            weighted_score = severity_weights.get(severity, 1) * confidence
            total_score += weighted_score

            severity_counts[severity] = severity_counts.get(severity, 0) + 1

        # Normalize to 0-100 scale
        normalized_score = min(100, (total_score / len(findings)) * 10)

        # Determine risk level
        if normalized_score >= 80:
            level = 'critical'
        elif normalized_score >= 60:
            level = 'high'
        elif normalized_score >= 40:
            level = 'medium'
        elif normalized_score >= 20:
            level = 'low'
        else:
            level = 'info'

        return {
            'score': round(normalized_score, 1),
            'level': level,
            'breakdown': severity_counts,
            'total_findings': len(findings)
        }
