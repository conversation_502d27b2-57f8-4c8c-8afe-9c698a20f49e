"""
Ollama Provider - Local LLM models
Implements BaseAIProvider for locally hosted Ollama models
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, Any
import logging
import aiohttp

from .base_provider import BaseAIProvider, AIResponse


class OllamaProvider(BaseAIProvider):
    """
    Provider for Ollama local models

    Supports:
    - llama3:70b
    - mistral:7b
    - codellama:13b
    - Any locally installed Ollama model
    """

    def _get_provider_name(self) -> str:
        """Return provider name"""
        return 'ollama'

    async def call(
        self,
        model: str,
        prompt: str,
        temperature: float = 0.1,
        max_tokens: int = 4096,
        **kwargs
    ) -> AIResponse:
        """
        Call Ollama local model

        Args:
            model: Model name (llama3:70b, mistral:7b, etc.)
            prompt: The prompt text
            temperature: Temperature (0.0-1.0)
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters

        Returns:
            AIResponse

        Raises:
            Exception: If call fails
        """
        start_time = time.time()

        try:
            # Get endpoint (default to localhost)
            endpoint = self.get_endpoint() or 'http://localhost:11434'

            # Map model names if needed
            model_mapping = {
                "llama-3-70b": "llama3:70b",
                "llama3": "llama3:70b",
                "mistral": "mistral:7b",
                "codellama": "codellama:13b"
            }
            actual_model = model_mapping.get(model, model)

            # Prepare payload
            payload = {
                'model': actual_model,
                'prompt': prompt,
                'stream': False,
                'options': {
                    'temperature': temperature,
                    'num_predict': max_tokens
                }
            }

            # Make API call
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f'{endpoint}/api/generate',
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=300)  # Local models can be slow
                ) as response:
                    if response.status == 200:
                        result = await response.json()

                        # Extract content
                        content = result.get('response', '')

                        # Calculate latency
                        latency_ms = (time.time() - start_time) * 1000

                        # Ollama doesn't provide token counts, estimate roughly
                        estimated_input_tokens = len(prompt.split()) * 1.3  # Rough estimate
                        estimated_output_tokens = len(content.split()) * 1.3
                        total_tokens = int(estimated_input_tokens + estimated_output_tokens)

                        return AIResponse(
                            model=model,
                            content=content,
                            confidence=0.75,  # Local models typically lower confidence
                            reasoning=f"Ollama {actual_model} analysis (local)",
                            input_tokens=int(estimated_input_tokens),
                            output_tokens=int(estimated_output_tokens),
                            total_tokens=total_tokens,
                            provider=self.provider_name,
                            request_id="",
                            created_at=datetime.now(),
                            latency_ms=latency_ms,
                            raw_response=result,
                            error=None,
                            success=True
                        )

                    else:
                        # Error response
                        error_text = await response.text()
                        raise Exception(f"Ollama API error {response.status}: {error_text}")

        except aiohttp.ClientConnectorError as e:
            # Connection error - Ollama probably not running
            latency_ms = (time.time() - start_time) * 1000

            self.logger.error(f"Ollama connection failed (is Ollama running?): {e}")

            return AIResponse(
                model=model,
                content="",
                confidence=0.0,
                reasoning="",
                provider=self.provider_name,
                created_at=datetime.now(),
                latency_ms=latency_ms,
                error=f"Ollama not accessible at {endpoint}. Is Ollama running?",
                success=False
            )

        except Exception as e:
            latency_ms = (time.time() - start_time) * 1000

            self.logger.error(f"Ollama API call failed: {e}")

            return AIResponse(
                model=model,
                content="",
                confidence=0.0,
                reasoning="",
                provider=self.provider_name,
                created_at=datetime.now(),
                latency_ms=latency_ms,
                error=str(e),
                success=False
            )

    def _is_rate_limit_error(self, error: Exception) -> bool:
        """Check if error is a rate limit error (N/A for local Ollama)"""
        # Ollama is local, no rate limits
        return False

    def parse_response(self, raw_response: Any) -> AIResponse:
        """
        Parse Ollama API response

        Args:
            raw_response: Raw response from Ollama API

        Returns:
            Standardized AIResponse
        """
        # Not typically used since we parse in call() directly
        # But included for interface compliance
        if isinstance(raw_response, dict):
            content = raw_response.get('response', str(raw_response))

            return AIResponse(
                model=raw_response.get('model', 'unknown'),
                content=content,
                confidence=0.75,
                reasoning="Ollama local model analysis",
                provider=self.provider_name,
                created_at=datetime.now(),
                success=True
            )
        else:
            return AIResponse(
                model="unknown",
                content=str(raw_response),
                provider=self.provider_name,
                created_at=datetime.now(),
                success=True
            )

    def validate_connection(self) -> bool:
        """
        Validate that Ollama is accessible

        Returns:
            True if can connect, False otherwise
        """
        try:
            import requests

            endpoint = self.get_endpoint() or 'http://localhost:11434'

            # Try to get list of models
            response = requests.get(f'{endpoint}/api/tags', timeout=5)

            if response.status_code == 200:
                self.logger.info(f"Ollama connection successful at {endpoint}")
                return True
            else:
                self.logger.warning(f"Ollama returned status {response.status_code}")
                return False

        except Exception as e:
            self.logger.warning(f"Ollama connection failed: {e}")
            return False
