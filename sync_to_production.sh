#!/bin/bash

# SIEMLess v2.0 - Sync Development to Production Repository
# This script updates the production repository with changes from development
# while maintaining clean history and excluding dev-only files

echo "========================================="
echo "  SIEMLess - Sync to Production Repo"
echo "========================================="

# Configuration
DEV_REPO="/c/Users/<USER>/Documents/siemless_v2"
PROD_REPO="/c/Users/<USER>/Documents/siemless-production"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Check if production repo exists
if [ ! -d "$PROD_REPO" ]; then
    echo -e "${RED}Error: Production repository not found at $PROD_REPO${NC}"
    echo "Please clone it first:"
    echo "  <NAME_EMAIL>:crazyguy106/siemless-production.git"
    exit 1
fi

# Save current directory
CURRENT_DIR=$(pwd)

# Navigate to dev repo
cd "$DEV_REPO"

# Check for uncommitted changes in dev
if [ -n "$(git status --porcelain)" ]; then
    echo -e "${YELLOW}Warning: You have uncommitted changes in development repo${NC}"
    read -p "Continue anyway? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo -e "${GREEN}Starting sync from development to production...${NC}"

# Create temp directory for staging
TEMP_DIR=$(mktemp -d)
echo "Using temp directory: $TEMP_DIR"

# Copy files to temp (excluding dev-only items)
echo "Copying files..."
rsync -av --progress \
    --exclude='.git' \
    --exclude='documents/archive' \
    --exclude='CLAUDE.md' \
    --exclude='PROJECT_INDEX.md' \
    --exclude='sync_to_production.sh' \
    --exclude='test_*.py' \
    --exclude='test_*.js' \
    --exclude='tests/' \
    --exclude='*_STATUS.md' \
    --exclude='*_REPORT.md' \
    --exclude='*_COMPLETE.md' \
    --exclude='HANDOFF_*.md' \
    --exclude='IMMEDIATE_*.md' \
    --exclude='*.backup' \
    --exclude='__pycache__' \
    --exclude='*.pyc' \
    --exclude='.env' \
    --exclude='.env.local' \
    --exclude='*.log' \
    "$DEV_REPO/" "$TEMP_DIR/"

# Navigate to production repo
cd "$PROD_REPO"

# Pull latest from production
echo -e "${YELLOW}Pulling latest from production repository...${NC}"
git pull origin master

# Backup current state
echo "Creating backup..."
git add .
git stash

# Clear production repo (except .git)
echo "Clearing production directory..."
find . -maxdepth 1 ! -name '.git' ! -name '.' -exec rm -rf {} +

# Copy from temp to production
echo "Updating production files..."
cp -r "$TEMP_DIR"/* .
cp -r "$TEMP_DIR"/.* . 2>/dev/null || true

# Ensure LICENSE and README_COLLABORATORS exist
if [ ! -f "LICENSE" ]; then
    echo -e "${YELLOW}LICENSE file missing - please add it${NC}"
fi

if [ ! -f "README_COLLABORATORS.md" ]; then
    echo -e "${YELLOW}README_COLLABORATORS.md missing - please add it${NC}"
fi

# Clean up temp directory
rm -rf "$TEMP_DIR"

# Show what changed
echo -e "${GREEN}Changes to be committed:${NC}"
git status --short

# Count changes
CHANGES=$(git status --porcelain | wc -l)

if [ "$CHANGES" -eq 0 ]; then
    echo -e "${YELLOW}No changes to sync${NC}"
    cd "$CURRENT_DIR"
    exit 0
fi

# Commit changes
echo -e "${GREEN}Committing changes to production...${NC}"
git add -A

# Get latest commit message from dev repo
cd "$DEV_REPO"
LAST_COMMIT_MSG=$(git log -1 --pretty=%B)
cd "$PROD_REPO"

git commit -m "Sync from development: $(date '+%Y-%m-%d %H:%M')

$LAST_COMMIT_MSG

Synced from development repository
Excludes: test files, dev documentation, archive"

# Ask to push
echo -e "${GREEN}Changes committed locally.${NC}"
read -p "Push to remote production repository? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git push origin master
    echo -e "${GREEN}✅ Successfully synced to production!${NC}"
else
    echo -e "${YELLOW}Changes committed but not pushed.${NC}"
    echo "To push later: cd $PROD_REPO && git push origin master"
fi

# Return to original directory
cd "$CURRENT_DIR"

echo ""
echo "========================================="
echo "  Sync Complete"
echo "========================================="
echo ""
echo "Production repo: https://github.com/crazyguy106/siemless-production"
echo "Development continues in: $DEV_REPO"