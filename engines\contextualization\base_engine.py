"""
SIEMLess v2.0 - Base Engine Class
Common functionality for all 5 engines in the new architecture
"""

import asyncio
import json
import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime
import redis
import redis.asyncio as redis_async
import asyncpg
import os
from aiohttp import web

class BaseEngine(ABC):
    """Base class for all SIEMLess v2.0 engines"""

    def __init__(self, engine_name: str):
        self.engine_name = engine_name
        self.logger = self._setup_logging()
        self.redis_client = self._setup_redis()
        self.db_pool = None  # Will be initialized in async setup
        self.is_running = False
        self.start_time = None
        self.metrics = {
            'messages_processed': 0,
            'errors': 0,
            'last_heartbeat': None,
            'processing_times': []
        }

        # HTTP server for health checks
        self.http_port = self._get_http_port()
        self.http_app = None
        self.http_runner = None

    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the engine"""
        logger = logging.getLogger(f"siemless.{self.engine_name}")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                f'%(asctime)s - {self.engine_name} - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _get_http_port(self) -> int:
        """Get HTTP port for this engine"""
        port_map = {
            'intelligence': 8001,
            'backend': 8002,
            'ingestion': 8003,
            'contextualization': 8004,
            'delivery': 8005
        }
        return port_map.get(self.engine_name, 8000)

    def _setup_redis(self) -> redis.Redis:
        """Setup Redis connection for message queue"""
        try:
            client = redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            client.ping()
            self.logger.info("Redis connection established")
            return client
        except Exception as e:
            self.logger.error(f"Redis connection failed: {e}")
            raise

    async def _setup_database(self) -> asyncpg.Pool:
        """Setup PostgreSQL database connection pool with asyncpg"""
        try:
            pool = await asyncpg.create_pool(
                host=os.getenv('POSTGRES_HOST', 'localhost'),
                port=int(os.getenv('POSTGRES_PORT', 5432)),
                database=os.getenv('POSTGRES_DB', 'siemless_v2'),
                user=os.getenv('POSTGRES_USER', 'siemless'),
                password=os.getenv('POSTGRES_PASSWORD', 'password'),
                min_size=5,
                max_size=20,
                command_timeout=60
            )
            self.logger.info(f"Database pool created: min=5, max=20")
            return pool
        except Exception as e:
            self.logger.error(f"Database pool creation failed: {e}")
            raise

    async def start(self):
        """Start the engine"""
        self.logger.info(f"Starting {self.engine_name} engine...")
        self.is_running = True
        self.start_time = datetime.utcnow()

        # Initialize database pool (async operation)
        self.logger.info("Initializing database pool...")
        self.db_pool = await self._setup_database()
        self.logger.info("Database pool initialized")

        # Register engine in coordination table
        self.logger.info("Registering engine...")
        await self._register_engine()
        self.logger.info("Engine registered, creating tasks...")

        # Start heartbeat task
        self.logger.info("Creating heartbeat task...")
        heartbeat_task = asyncio.create_task(self._heartbeat_loop())

        # Start message processing task
        self.logger.info("Creating message task...")
        message_task = asyncio.create_task(self._message_loop())

        # Start HTTP health check server
        self.logger.info("Creating HTTP server task...")
        http_task = asyncio.create_task(self._start_http_server())
        self.logger.info("HTTP server task created")

        # Start engine-specific tasks
        self.logger.info("Getting engine-specific tasks...")
        engine_tasks = self.start_engine_tasks()
        self.logger.info(f"Got {len(engine_tasks)} engine-specific tasks")

        # Start all tasks concurrently and wait for completion
        # IMPORTANT: Task coordination pattern for concurrent services
        try:
            print(f"[DEBUG] Starting all tasks with asyncio.wait...")
            # Let all tasks run concurrently
            all_tasks = [heartbeat_task, message_task, http_task] + engine_tasks
            print(f"[DEBUG] Total tasks to run: {len(all_tasks)}")

            # CRITICAL: Yield control to allow tasks to actually start
            # Without this, the message loop might immediately block the event loop
            await asyncio.sleep(0.1)
            print(f"[DEBUG] Tasks scheduled, now waiting...")

            # Use asyncio.wait with FIRST_COMPLETED to detect any task failures
            # Note: asyncio.gather() would wait for ALL tasks, potentially hiding issues
            done, pending = await asyncio.wait(all_tasks, return_when=asyncio.FIRST_COMPLETED)
            print(f"[DEBUG] One task completed, {len(pending)} still running")

            # If any task completes, it's likely an error, so shut down
            for task in done:
                if task.exception():
                    print(f"[DEBUG] Task failed with: {task.exception()}")
                    self.logger.error(f"Task failed: {task.exception()}")

        except Exception as e:
            print(f"[DEBUG] Engine error: {e}")
            self.logger.error(f"Engine stopped with error: {e}")
        finally:
            print(f"[DEBUG] Shutting down engine...")
            await self.shutdown()

    async def shutdown(self):
        """Shutdown the engine gracefully"""
        self.logger.info(f"Shutting down {self.engine_name} engine...")
        self.is_running = False

        # Unregister engine
        await self._unregister_engine()

        # Close HTTP server
        if self.http_runner:
            await self.http_runner.cleanup()

        # Close connections
        if self.redis_client:
            self.redis_client.close()
        if self.db_pool:
            await self.db_pool.close()

        self.logger.info("Engine shutdown complete")

    async def _register_engine(self):
        """Register engine in coordination table"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO engine_coordination (engine_id, status, last_heartbeat, performance_metrics)
                    VALUES ($1, $2, $3, $4)
                    ON CONFLICT (engine_id)
                    DO UPDATE SET status = $2, last_heartbeat = $3, performance_metrics = $4
                """, self.engine_name, 'starting', datetime.utcnow(), json.dumps(self.metrics))
            self.logger.info("Engine registered in coordination table")
        except Exception as e:
            self.logger.error(f"Failed to register engine: {e}")

    async def _unregister_engine(self):
        """Unregister engine from coordination table"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE engine_coordination
                    SET status = 'stopped', last_heartbeat = $1
                    WHERE engine_id = $2
                """, datetime.utcnow(), self.engine_name)
            self.logger.info("Engine unregistered from coordination table")
        except Exception as e:
            self.logger.error(f"Failed to unregister engine: {e}")

    async def _heartbeat_loop(self):
        """Send periodic heartbeat"""
        while self.is_running:
            try:
                self.metrics['last_heartbeat'] = datetime.utcnow().isoformat()

                # Update coordination table with asyncpg
                async with self.db_pool.acquire() as conn:
                    await conn.execute("""
                        UPDATE engine_coordination
                        SET status = $1, last_heartbeat = $2, performance_metrics = $3
                        WHERE engine_id = $4
                    """, 'running', datetime.utcnow(), json.dumps(self.metrics), self.engine_name)

                # Publish heartbeat to Redis
                self.redis_client.publish(f'heartbeat.{self.engine_name}', json.dumps({
                    'engine': self.engine_name,
                    'status': 'running',
                    'metrics': self.metrics
                }))

            except Exception as e:
                self.logger.error(f"Heartbeat error: {e}")
                # Pool will automatically handle reconnection

            await asyncio.sleep(30)  # Heartbeat every 30 seconds

    async def _message_loop(self):
        """Process messages from Redis message queue

        CRITICAL: This method uses async Redis operations to prevent blocking
        the event loop. Previous versions used synchronous Redis which prevented
        the HTTP health server from starting.

        The fix:
        1. Uses redis.asyncio instead of standard redis
        2. All Redis operations are awaited
        3. Explicit yielding with asyncio.sleep ensures other tasks can run

        See ASYNC_FIX_DOCUMENTATION.md for full details.
        """
        # Subscribe to engine-specific channels
        channels = self.get_subscribed_channels()

        if not channels:
            self.logger.info("No channels to subscribe to")
            return

        # CRITICAL FIX: Use async Redis client for non-blocking operations
        # Without this, synchronous Redis blocks the entire event loop
        async_redis = await redis_async.Redis(
            host=os.getenv('REDIS_HOST', 'localhost'),
            port=int(os.getenv('REDIS_PORT', 6379)),
            decode_responses=True
        )

        pubsub = async_redis.pubsub()
        await pubsub.subscribe(*channels)  # Async subscribe - doesn't block

        self.logger.info(f"Subscribed to channels: {channels}")

        while self.is_running:
            try:
                # CRITICAL FIX: Async get_message that yields to event loop
                # Previous sync version: pubsub.get_message(timeout=1) BLOCKED!
                message = await pubsub.get_message(timeout=1.0)
                if message and message['type'] == 'message':
                    start_time = time.time()

                    try:
                        # Process the message
                        await self.process_message(message)
                        self.metrics['messages_processed'] += 1

                        # Track processing time
                        processing_time = time.time() - start_time
                        self.metrics['processing_times'].append(processing_time)

                        # Keep only last 100 processing times
                        if len(self.metrics['processing_times']) > 100:
                            self.metrics['processing_times'] = self.metrics['processing_times'][-100:]

                    except Exception as e:
                        self.logger.error(f"Message processing error: {e}")
                        self.metrics['errors'] += 1

                # Yield control to allow other tasks to run
                await asyncio.sleep(0.01)

            except Exception as e:
                self.logger.error(f"Message loop error: {e}")
                await asyncio.sleep(1)

        # Cleanup
        await pubsub.unsubscribe(*channels)
        await async_redis.close()

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        health = {
            'engine': self.engine_name,
            'status': 'healthy' if self.is_running else 'stopped',
            'uptime': None,
            'redis_connected': False,
            'database_connected': False,
            'metrics': self.metrics
        }

        # Calculate uptime
        if self.start_time:
            uptime = datetime.utcnow() - self.start_time
            health['uptime'] = str(uptime)

        # Check Redis connection
        try:
            self.redis_client.ping()
            health['redis_connected'] = True
        except:
            health['redis_connected'] = False

        # Check Database connection
        try:
            if self.db_pool:
                async with self.db_pool.acquire() as conn:
                    await conn.fetchval("SELECT 1")
                health['database_connected'] = True
                health['pool_size'] = self.db_pool.get_size()
                health['pool_free'] = self.db_pool.get_idle_size()
        except:
            health['database_connected'] = False

        return health

    def get_metrics(self) -> Dict[str, Any]:
        """Get engine performance metrics"""
        metrics = self.metrics.copy()

        # Calculate average processing time
        if metrics['processing_times']:
            metrics['avg_processing_time'] = sum(metrics['processing_times']) / len(metrics['processing_times'])
        else:
            metrics['avg_processing_time'] = 0

        # Calculate error rate
        total_processed = metrics['messages_processed'] + metrics['errors']
        if total_processed > 0:
            metrics['error_rate'] = metrics['errors'] / total_processed
        else:
            metrics['error_rate'] = 0

        return metrics

    def publish_message(self, channel: str, message: Dict[str, Any]):
        """Publish message to Redis channel"""
        try:
            message_data = {
                'source_engine': self.engine_name,
                'timestamp': datetime.utcnow().isoformat(),
                'data': message
            }
            self.redis_client.publish(channel, json.dumps(message_data))
        except Exception as e:
            self.logger.error(f"Failed to publish message to {channel}: {e}")

    async def store_workflow_instance(self, workflow_id: str, target_engine: str, data: Dict[str, Any]):
        """Store workflow instance for cross-engine coordination"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO workflow_instances (workflow_id, source_engine, target_engine, status, data)
                    VALUES ($1, $2, $3, $4, $5)
                """, workflow_id, self.engine_name, target_engine, 'pending', json.dumps(data))
        except Exception as e:
            self.logger.error(f"Failed to store workflow instance: {e}")

    async def _start_http_server(self):
        """Start HTTP server for health checks and API endpoints"""
        try:
            print(f"[DEBUG] Starting HTTP server setup on port {self.http_port}")
            self.http_app = web.Application()

            # Add health check endpoint
            self.http_app.router.add_get('/health', self._health_check)
            print(f"[DEBUG] Added /health endpoint")

            # Add metrics endpoint
            self.http_app.router.add_get('/metrics', self._metrics_endpoint)
            print(f"[DEBUG] Added /metrics endpoint")

            # Add engine-specific routes
            await self._setup_http_routes(self.http_app)
            print(f"[DEBUG] Engine-specific routes setup complete")

            self.http_runner = web.AppRunner(self.http_app)
            await self.http_runner.setup()
            print(f"[DEBUG] HTTP runner setup complete")

            site = web.TCPSite(self.http_runner, '0.0.0.0', self.http_port)
            await site.start()
            print(f"[DEBUG] HTTP server started successfully on port {self.http_port}")
            self.logger.info(f"HTTP server started successfully on port {self.http_port}")

            # Keep the server running
            print(f"[DEBUG] Entering keep-alive loop")
            while self.is_running:
                await asyncio.sleep(1)

        except Exception as e:
            print(f"[DEBUG] HTTP server error: {e}")
            self.logger.error(f"HTTP server error: {e}")
            import traceback
            print(f"[DEBUG] HTTP server traceback: {traceback.format_exc()}")
            self.logger.error(f"HTTP server traceback: {traceback.format_exc()}")

    async def _health_check(self, request):
        """Health check endpoint"""
        health_status = {
            'engine': self.engine_name,
            'status': 'healthy' if self.is_running else 'unhealthy',
            'uptime': str(datetime.utcnow() - self.start_time) if self.start_time else '0',
            'last_heartbeat': self.metrics.get('last_heartbeat'),
            'messages_processed': self.metrics.get('messages_processed', 0),
            'errors': self.metrics.get('errors', 0),
            'timestamp': datetime.utcnow().isoformat()
        }

        # Check database connection
        try:
            if self.db_pool:
                async with self.db_pool.acquire() as conn:
                    await conn.fetchval("SELECT 1")
                health_status['database'] = 'connected'
                health_status['pool'] = {
                    'size': self.db_pool.get_size(),
                    'free': self.db_pool.get_idle_size(),
                    'used': self.db_pool.get_size() - self.db_pool.get_idle_size()
                }
            else:
                health_status['database'] = 'not initialized'
                health_status['status'] = 'unhealthy'
        except Exception as e:
            health_status['database'] = f'error: {e}'
            health_status['status'] = 'unhealthy'

        # Check Redis connection
        try:
            self.redis_client.ping()
            health_status['redis'] = 'connected'
        except Exception as e:
            health_status['redis'] = f'error: {e}'
            health_status['status'] = 'unhealthy'

        status_code = 200 if health_status['status'] == 'healthy' else 503
        return web.json_response(health_status, status=status_code)

    async def _metrics_endpoint(self, request):
        """Metrics endpoint for monitoring"""
        metrics = {
            'engine': self.engine_name,
            'metrics': self.metrics,
            'uptime': str(datetime.utcnow() - self.start_time) if self.start_time else '0',
            'timestamp': datetime.utcnow().isoformat()
        }
        return web.json_response(metrics)

    async def _setup_http_routes(self, app):
        """Override this method in child classes to add engine-specific routes"""
        pass

    # Abstract methods that each engine must implement
    @abstractmethod
    async def process_message(self, message: Dict[str, Any]):
        """Process incoming message from message queue"""
        pass

    @abstractmethod
    def get_subscribed_channels(self) -> List[str]:
        """Return list of Redis channels this engine subscribes to"""
        pass

    @abstractmethod
    def start_engine_tasks(self) -> List[asyncio.Task]:
        """Start engine-specific background tasks"""
        pass

if __name__ == "__main__":
    # This should not be run directly
    print("BaseEngine is an abstract class. Use specific engine implementations.")