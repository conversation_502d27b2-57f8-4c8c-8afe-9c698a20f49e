import React, { useState } from 'react'
import { NavLink, useLocation } from 'react-router-dom'
import {
  Home, Search, Brain, Code, BarChart3, Settings,
  AlertCircle, Users, Target, GitBranch, Sparkles,
  Clock, Star, ChevronRight, ChevronDown, Lock,
  Unlock, LogOut, Shield, User, Key, Activity,
  FolderOpen, FileText, Database, Layers, Hash,
  TrendingUp, X
} from 'lucide-react'
import { useNavigationStore } from '../../stores/navigationStore'
import { useAuthStore } from '../../stores/authStore'
import clsx from 'clsx'

interface NavSection {
  title: string
  items: NavItem[]
}

interface NavItem {
  label: string
  path: string
  icon: React.ComponentType<{ size?: number }>
  badge?: string | number
  requiredRole?: string
  children?: NavItem[]
}

const navigationSections: NavSection[] = [
  {
    title: 'Operations',
    items: [
      { label: 'Dashboard', path: '/', icon: Home },
      { label: '<PERSON>ert Queue', path: '/alerts', icon: AlertCircle, badge: 12 },
      { label: 'Active Cases', path: '/cases', icon: FolderOpen, badge: 5 },
      { label: 'MITRE Overview', path: '/mitre', icon: Target }
    ]
  },
  {
    title: 'Investigation',
    items: [
      { label: 'New Investigation', path: '/investigation/new', icon: Search },
      { label: 'Entity Explorer', path: '/investigation/entities', icon: Database },
      { label: 'Relationship Mapper', path: '/investigation/relationships', icon: GitBranch },
      { label: 'Timeline Analysis', path: '/investigation/timeline', icon: Clock },
      { label: 'AI Guide', path: '/investigation/ai-guide', icon: Brain }
    ]
  },
  {
    title: 'Engineering',
    items: [
      { label: 'Pattern Library', path: '/engineering/patterns', icon: Layers },
      { label: 'Crystallization Queue', path: '/engineering/crystallization', icon: Sparkles, badge: 8 },
      { label: 'Rule Testing', path: '/engineering/rules', icon: Code },
      { label: 'GitHub Sync', path: '/engineering/github', icon: GitBranch }
    ]
  },
  {
    title: 'Analytics',
    items: [
      { label: 'Performance Metrics', path: '/analytics/performance', icon: Activity },
      { label: 'Cost Analysis', path: '/analytics/costs', icon: BarChart3 },
      { label: 'Capacity Planning', path: '/analytics/capacity', icon: Database },
      { label: 'Risk Velocity', path: '/analytics/risk', icon: TrendingUp }
    ]
  },
  {
    title: 'Administration',
    items: [
      { label: 'User Management', path: '/admin/users', icon: Users, requiredRole: 'admin' },
      { label: 'System Settings', path: '/admin/settings', icon: Settings, requiredRole: 'admin' },
      { label: 'Integrations', path: '/admin/integrations', icon: Layers, requiredRole: 'admin' },
      { label: 'Audit Logs', path: '/admin/audit', icon: FileText, requiredRole: 'admin' }
    ]
  }
]

interface SideBarProps {
  expanded: boolean
  user: any
}

const SideBar: React.FC<SideBarProps> = ({ expanded, user }) => {
  const location = useLocation()
  const { sidebarLocked, setSidebarLocked, favoriteRoutes, addFavorite, removeFavorite } = useNavigationStore()
  const { logout } = useAuthStore()
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['Operations', 'Investigation']))
  const [hovering, setHovering] = useState(false)

  const isExpanded = expanded || (hovering && !sidebarLocked)

  const toggleSection = (title: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(title)) {
        newSet.delete(title)
      } else {
        newSet.add(title)
      }
      return newSet
    })
  }

  const toggleFavorite = (path: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (favoriteRoutes.includes(path)) {
      removeFavorite(path)
    } else {
      addFavorite(path)
    }
  }

  const canAccessItem = (item: NavItem) => {
    if (!item.requiredRole) return true
    return user?.role === item.requiredRole || user?.role === 'admin'
  }

  const getSessionDuration = () => {
    if (!user?.sessionStartTime) return '0m'
    const start = new Date(user.sessionStartTime).getTime()
    const now = Date.now()
    const minutes = Math.floor((now - start) / 60000)

    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    return `${hours}h ${minutes % 60}m`
  }

  const getUserRoleColor = () => {
    const colors: Record<string, string> = {
      admin: 'bg-red-500',
      engineer: 'bg-purple-500',
      analyst: 'bg-blue-500',
      viewer: 'bg-gray-500'
    }
    return colors[user?.role] || 'bg-gray-500'
  }

  return (
    <aside
      className={clsx(
        'h-full bg-gray-900 text-gray-300 transition-all duration-200 flex flex-col',
        isExpanded ? 'w-60' : 'w-16'
      )}
      onMouseEnter={() => !sidebarLocked && setHovering(true)}
      onMouseLeave={() => !sidebarLocked && setHovering(false)}
    >
      {/* Lock/Unlock Button */}
      {isExpanded && (
        <div className="px-3 py-2 border-b border-gray-800">
          <button
            onClick={() => setSidebarLocked(!sidebarLocked)}
            className="flex items-center gap-2 text-xs text-gray-400 hover:text-white transition-colors"
            title={sidebarLocked ? 'Unlock sidebar' : 'Lock sidebar'}
          >
            {sidebarLocked ? <Lock size={14} /> : <Unlock size={14} />}
            {sidebarLocked ? 'Locked' : 'Auto-collapse'}
          </button>
        </div>
      )}

      {/* Navigation Sections */}
      <nav className="flex-1 overflow-y-auto py-4">
        {/* Favorites Section */}
        {favoriteRoutes.length > 0 && (
          <div className="mb-4">
            {isExpanded && (
              <div className="px-3 mb-2">
                <h3 className="text-xs font-semibold text-gray-500 uppercase">Favorites</h3>
              </div>
            )}
            {favoriteRoutes.map(path => {
              const item = navigationSections
                .flatMap(s => s.items)
                .find(i => i.path === path)

              if (!item) return null

              return (
                <NavLink
                  key={path}
                  to={path}
                  className={({ isActive }) => clsx(
                    'flex items-center gap-3 px-3 py-2 hover:bg-gray-800 transition-colors relative',
                    isActive ? 'bg-gray-800 text-white border-l-2 border-blue-500' : ''
                  )}
                  title={!isExpanded ? item.label : undefined}
                >
                  <Star size={20} className="text-yellow-500 flex-shrink-0" />
                  {isExpanded && (
                    <>
                      <span className="text-sm">{item.label}</span>
                      <button
                        onClick={(e) => toggleFavorite(path, e)}
                        className="ml-auto text-gray-500 hover:text-white"
                      >
                        <X size={14} />
                      </button>
                    </>
                  )}
                </NavLink>
              )
            })}
          </div>
        )}

        {/* Main Navigation */}
        {navigationSections.map(section => (
          <div key={section.title} className="mb-6">
            {isExpanded ? (
              <button
                onClick={() => toggleSection(section.title)}
                className="w-full px-3 mb-2 flex items-center justify-between hover:text-white transition-colors"
              >
                <h3 className="text-xs font-semibold text-gray-500 uppercase">{section.title}</h3>
                {expandedSections.has(section.title) ? (
                  <ChevronDown size={14} />
                ) : (
                  <ChevronRight size={14} />
                )}
              </button>
            ) : (
              <div className="h-8" />
            )}

            {(isExpanded ? expandedSections.has(section.title) : true) && (
              <div className="space-y-1">
                {section.items.map(item => {
                  if (!canAccessItem(item)) return null

                  return (
                    <NavLink
                      key={item.path}
                      to={item.path}
                      className={({ isActive }) => clsx(
                        'flex items-center gap-3 px-3 py-2 hover:bg-gray-800 transition-colors relative group',
                        isActive ? 'bg-gray-800 text-white border-l-2 border-blue-500' : ''
                      )}
                      title={!isExpanded ? item.label : undefined}
                    >
                      <item.icon size={20} className="flex-shrink-0" />
                      {isExpanded && (
                        <>
                          <span className="text-sm flex-1">{item.label}</span>
                          {item.badge && (
                            <span className="px-2 py-0.5 text-xs bg-blue-600 text-white rounded-full">
                              {item.badge}
                            </span>
                          )}
                          {!favoriteRoutes.includes(item.path) && (
                            <button
                              onClick={(e) => toggleFavorite(item.path, e)}
                              className="opacity-0 group-hover:opacity-100 text-gray-500 hover:text-yellow-500 transition-all"
                              title="Add to favorites"
                            >
                              <Star size={14} />
                            </button>
                          )}
                        </>
                      )}
                    </NavLink>
                  )
                })}
              </div>
            )}
          </div>
        ))}
      </nav>

      {/* User Info Section (Bottom) */}
      <div className="border-t border-gray-800 p-3">
        {isExpanded ? (
          <div className="space-y-3">
            {/* User Details */}
            <div className="flex items-center gap-3">
              <div className="relative">
                {user?.avatar ? (
                  <img src={user.avatar} alt={user.name} className="w-10 h-10 rounded-full" />
                ) : (
                  <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center">
                    <User size={20} />
                  </div>
                )}
                <div className={`absolute bottom-0 right-0 w-3 h-3 ${getUserRoleColor()} rounded-full border-2 border-gray-900`} />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-white truncate">{user?.name || 'User'}</div>
                <div className="text-xs text-gray-500 truncate">{user?.email}</div>
              </div>
            </div>

            {/* Role & Department */}
            <div className="space-y-1 text-xs">
              <div className="flex items-center gap-2 text-gray-400">
                <Shield size={14} />
                <span className="capitalize">{user?.role || 'viewer'}</span>
                {user?.department && (
                  <>
                    <span className="text-gray-600">|</span>
                    <span>{user.department}</span>
                  </>
                )}
              </div>
            </div>

            {/* Session Info */}
            <div className="space-y-1 text-xs border-t border-gray-800 pt-2">
              <div className="flex items-center justify-between text-gray-400">
                <span className="flex items-center gap-1">
                  <Clock size={12} />
                  Session
                </span>
                <span>{getSessionDuration()}</span>
              </div>
              {user?.sessionExpiry && (
                <div className="flex items-center justify-between text-gray-400">
                  <span className="flex items-center gap-1">
                    <Key size={12} />
                    Expires
                  </span>
                  <span>{new Date(user.sessionExpiry).toLocaleTimeString()}</span>
                </div>
              )}
            </div>

            {/* Permissions Summary */}
            <div className="text-xs text-gray-500">
              <div className="flex items-center gap-1 mb-1">
                <Hash size={12} />
                <span>Access Level: {user?.accessLevel || 'viewer'}</span>
              </div>
              {user?.enabledFeatures && user.enabledFeatures.length > 0 && (
                <div className="text-xs text-gray-600">
                  {user.enabledFeatures.slice(0, 3).join(', ')}
                  {user.enabledFeatures.length > 3 && ` +${user.enabledFeatures.length - 3} more`}
                </div>
              )}
            </div>

            {/* Logout Button */}
            <button
              onClick={logout}
              className="w-full flex items-center justify-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded text-sm text-gray-300 hover:text-white transition-colors"
            >
              <LogOut size={16} />
              Sign Out
            </button>
          </div>
        ) : (
          <div className="flex flex-col items-center gap-3">
            {/* Compact User Avatar */}
            <div className="relative">
              <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center">
                <User size={20} />
              </div>
              <div className={`absolute bottom-0 right-0 w-3 h-3 ${getUserRoleColor()} rounded-full border-2 border-gray-900`} />
            </div>

            {/* Logout Icon */}
            <button
              onClick={logout}
              className="p-2 hover:bg-gray-800 rounded transition-colors"
              title="Sign Out"
            >
              <LogOut size={18} />
            </button>
          </div>
        )}
      </div>
    </aside>
  )
}

export default SideBar