"""
Comprehensive test suite for SIEMLess v2.0 engines
Tests the core functionality of all 10 engines built so far
"""
import pytest
import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

# Add v2 to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import all engines
from engines.ingestion.ingestion import IngestionEngine
from engines.parser.parser import ParserEngine
from engines.entity_extractor.entity_extractor import EntityExtractionEngine
from engines.use_case_context.use_case_context import UseCaseContextEngine
from engines.graph_builder.graph_builder import GraphBuilderEngine
from engines.detection.detection import DetectionEngine
from engines.pattern_manager.pattern_manager import PatternManager
from engines.ai_consensus.ai_consensus import AIConsensusEngine
from engines.librarian.librarian import LibrarianEngine
from engines.api_gateway.api_gateway import APIGateway
from shared.base import BaseEngine
from shared.logging import UniversalLogger
from shared.queue import MessageQueue


@pytest.fixture
def mock_redis():
    """Mock Redis connection"""
    return AsyncMock()


@pytest.fixture
def mock_postgres():
    """Mock PostgreSQL connection"""
    return MagicMock()


@pytest.fixture
def sample_crowdstrike_log():
    """Sample CrowdStrike log for testing"""
    return {
        "detection_id": "ldt:12345678",
        "device": {
            "hostname": "WIN-SERVER01",
            "user_name": "CORP\\admin",
            "machine_domain": "CORP",
            "external_ip": "***********"
        },
        "behaviors": [
            {
                "filename": "mimikatz.exe",
                "filepath": "C:\\Temp\\mimikatz.exe",
                "cmdline": "mimikatz.exe sekurlsa::logonpasswords",
                "sha256": "abc123def456",
                "technique": "T1003",
                "tactic": "credential-access"
            }
        ],
        "max_severity": 90,
        "timestamp": "2024-01-27T10:30:00Z"
    }


@pytest.fixture
def sample_entities():
    """Sample extracted entities"""
    return [
        {"type": "hostname", "value": "WIN-SERVER01", "confidence": 1.0},
        {"type": "user", "value": "admin", "confidence": 0.9},
        {"type": "process", "value": "mimikatz.exe", "confidence": 1.0},
        {"type": "technique", "value": "T1003", "confidence": 1.0}
    ]


@pytest.fixture
def sample_relationships():
    """Sample relationships"""
    return [
        {
            "source": "admin",
            "target": "WIN-SERVER01",
            "type": "logged_into",
            "confidence": 0.9
        },
        {
            "source": "admin",
            "target": "mimikatz.exe",
            "type": "executed",
            "confidence": 1.0
        }
    ]


class TestBaseEngine:
    """Test the base engine functionality"""

    @pytest.mark.asyncio
    async def test_base_engine_initialization(self):
        """Test base engine can be initialized"""
        # Create a concrete implementation for testing
        class TestEngine(BaseEngine):
            async def process_message(self, message):
                return {"success": True, "processed": True}

            def get_capabilities(self):
                return {"test": "engine"}

            def validate_configuration(self, config):
                return True

        engine = TestEngine("test_engine", "1.0.0")
        assert engine.name == "test_engine"
        assert engine.version == "1.0.0"
        assert isinstance(engine.logger, UniversalLogger)
        assert isinstance(engine.message_queue, MessageQueue)


class TestIngestionEngine:
    """Test the Ingestion Engine"""

    @pytest.mark.asyncio
    async def test_ingestion_engine_init(self):
        """Test ingestion engine initialization"""
        engine = IngestionEngine()
        assert engine.name == "ingestion"
        assert engine.version == "2.0.0"
        assert engine.config["syslog_port"] == 514
        assert engine.config["http_port"] == 8514

    @pytest.mark.asyncio
    async def test_log_type_detection(self, sample_crowdstrike_log):
        """Test log type detection"""
        engine = IngestionEngine()
        log_str = json.dumps(sample_crowdstrike_log)

        detected_type = engine._detect_log_type(log_str)
        assert detected_type == "crowdstrike"

    @pytest.mark.asyncio
    async def test_file_ingestion(self):
        """Test file ingestion capability"""
        engine = IngestionEngine()

        # Test with non-existent file
        result = await engine._handle_file({"path": "/non/existent/file.json"})
        assert result["success"] == False
        assert "not found" in result["error"]

    @pytest.mark.asyncio
    async def test_rate_limiting(self):
        """Test rate limiting functionality"""
        engine = IngestionEngine()

        # Should allow requests initially
        assert engine._check_rate_limit() == True

        # Exhaust rate limit
        engine.rate_limiter["tokens"] = 0
        assert engine._check_rate_limit() == False


class TestParserEngine:
    """Test the Parser Engine"""

    @pytest.mark.asyncio
    async def test_parser_engine_init(self):
        """Test parser engine initialization"""
        with patch("builtins.open", mock_open_patterns()):
            engine = ParserEngine()
            assert engine.name == "parser"
            assert len(engine.patterns) >= 0  # Should load patterns

    @pytest.mark.asyncio
    async def test_pattern_loading(self):
        """Test pattern loading from files"""
        with patch("builtins.open", mock_open_patterns()):
            engine = ParserEngine()

            # Should have loaded at least some patterns
            capabilities = engine.get_capabilities()
            assert "pattern_matching" in capabilities["capabilities"]

    @pytest.mark.asyncio
    async def test_log_parsing(self, sample_crowdstrike_log):
        """Test log parsing with patterns"""
        with patch("builtins.open", mock_open_patterns()):
            engine = ParserEngine()

            message = {
                "id": "test_001",
                "data": {
                    "raw": json.dumps(sample_crowdstrike_log),
                    "detected_type": "crowdstrike"
                }
            }

            result = await engine.process_message(message)
            assert result["success"] == True


class TestEntityExtractionEngine:
    """Test the Entity Extraction Engine"""

    @pytest.mark.asyncio
    async def test_entity_extraction_init(self):
        """Test entity extraction engine initialization"""
        engine = EntityExtractionEngine()
        assert engine.name == "entity_extractor"
        assert len(engine.entity_types) > 0

    @pytest.mark.asyncio
    async def test_entity_extraction(self, sample_crowdstrike_log):
        """Test entity extraction from parsed log"""
        engine = EntityExtractionEngine()

        message = {
            "id": "test_001",
            "data": {
                "parsed_log": sample_crowdstrike_log,
                "original_log": sample_crowdstrike_log
            }
        }

        result = await engine.process_message(message)
        assert result["success"] == True
        assert "entities" in result["data"]

        entities = result["data"]["entities"]
        assert len(entities) > 0

        # Check for expected entity types
        entity_types = [e["type"] for e in entities]
        assert "hostname" in entity_types


class TestUseCaseContextEngine:
    """Test the Use Case Context Engine"""

    @pytest.mark.asyncio
    async def test_use_case_context_init(self):
        """Test use case context engine initialization"""
        engine = UseCaseContextEngine()
        assert engine.name == "use_case_context"
        assert len(engine.use_cases) > 0
        assert "credential_access" in engine.use_cases

    @pytest.mark.asyncio
    async def test_use_case_matching(self, sample_entities, sample_relationships):
        """Test use case matching"""
        engine = UseCaseContextEngine()

        message = {
            "id": "test_001",
            "data": {
                "entities": sample_entities,
                "relationships": sample_relationships,
                "original_log": {"behaviors": [{"technique": "T1003"}]}
            }
        }

        result = await engine.process_message(message)
        assert result["success"] == True
        assert len(result["use_cases"]) > 0

        # Should match credential access use case
        use_case_ids = [uc["use_case_id"] for uc in result["use_cases"]]
        assert "credential_access" in use_case_ids


class TestGraphBuilderEngine:
    """Test the Graph Builder Engine"""

    @pytest.mark.asyncio
    async def test_graph_builder_init(self):
        """Test graph builder engine initialization"""
        engine = GraphBuilderEngine()
        assert engine.name == "graph_builder"
        assert engine.graph is not None

    @pytest.mark.asyncio
    async def test_graph_building(self, sample_entities, sample_relationships):
        """Test graph building from entities and relationships"""
        engine = GraphBuilderEngine()

        message = {
            "id": "test_001",
            "data": {
                "entities": sample_entities,
                "relationships": sample_relationships
            }
        }

        result = await engine.process_message(message)
        assert result["success"] == True
        assert result["graph_stats"]["nodes"] > 0
        assert result["graph_stats"]["edges"] > 0


class TestDetectionEngine:
    """Test the Detection Engine"""

    @pytest.mark.asyncio
    async def test_detection_engine_init(self):
        """Test detection engine initialization"""
        engine = DetectionEngine()
        assert engine.name == "detection"
        assert len(engine.detection_rules) > 0

    @pytest.mark.asyncio
    async def test_pattern_detection(self, sample_entities, sample_relationships):
        """Test pattern-based detection"""
        engine = DetectionEngine()

        message = {
            "id": "test_001",
            "data": {
                "entities": sample_entities,
                "relationships": sample_relationships,
                "use_case_context": {
                    "security_context": [
                        {"context": "credential_access", "confidence": 0.95}
                    ]
                },
                "risk_score": 85
            }
        }

        result = await engine.process_message(message)
        assert result["success"] == True

        if result["detections"]:
            detection = result["detections"][0]
            assert "detection_id" in detection
            assert "severity" in detection
            assert "confidence" in detection


class TestPatternManager:
    """Test the Pattern Manager"""

    @pytest.mark.asyncio
    async def test_pattern_manager_init(self):
        """Test pattern manager initialization"""
        manager = PatternManager()
        assert manager.name == "pattern_manager"
        assert len(manager.validation_rules) > 0

    @pytest.mark.asyncio
    async def test_pattern_creation(self):
        """Test pattern creation"""
        manager = PatternManager()

        message = {
            "command": "create",
            "data": {
                "pattern_type": "parser",
                "name": "test_pattern",
                "content": {
                    "vendor": "test",
                    "product": "demo",
                    "entity_mappings": {
                        "hostname": {"primary": "host.name"}
                    }
                },
                "metadata": {
                    "author": "test_system",
                    "source": "api"
                }
            }
        }

        result = await manager.process_message(message)
        assert result["success"] == True
        assert "pattern_id" in result


class TestAIConsensusEngine:
    """Test the AI Consensus Engine"""

    @pytest.mark.asyncio
    async def test_ai_consensus_init(self):
        """Test AI consensus engine initialization"""
        engine = AIConsensusEngine()
        assert engine.name == "ai_consensus"
        assert len(engine.models) > 0

    @pytest.mark.asyncio
    async def test_consensus_request(self):
        """Test consensus request processing"""
        engine = AIConsensusEngine()

        message = {
            "id": "test_001",
            "command": "validate_pattern",
            "data": {
                "pattern": {
                    "entity_mappings": {"user": "user.name"},
                    "vendor": "test"
                },
                "sample_logs": ["test log"]
            }
        }

        # Mock AI responses since we don't want to call real APIs in tests
        with patch.object(engine, '_call_anthropic', return_value={"valid": True}):
            with patch.object(engine, '_call_openai', return_value={"valid": True}):
                with patch.object(engine, '_call_google', return_value={"valid": True}):
                    result = await engine.process_message(message)
                    assert result["success"] == True


class TestLibrarianEngine:
    """Test the Librarian Engine"""

    @pytest.mark.asyncio
    async def test_librarian_init(self):
        """Test librarian engine initialization"""
        engine = LibrarianEngine()
        assert engine.name == "librarian"
        assert engine.consensus_threshold == 0.8

    @pytest.mark.asyncio
    async def test_pattern_submission(self):
        """Test pattern submission to librarian"""
        engine = LibrarianEngine()

        message = {
            "id": "test_001",
            "command": "submit_pattern",
            "data": {
                "pattern": {
                    "name": "test_pattern",
                    "vendor": "test",
                    "entity_mappings": {"user": "user.name"}
                },
                "source_engine": "parser"
            }
        }

        result = await engine.process_message(message)
        assert result["success"] == True


class TestAPIGateway:
    """Test the API Gateway"""

    @pytest.mark.asyncio
    async def test_api_gateway_init(self):
        """Test API gateway initialization"""
        gateway = APIGateway()
        assert gateway.name == "api_gateway"
        assert gateway.config["port"] == 8000
        assert len(gateway.engine_routes) > 0

    def test_engine_routing(self):
        """Test engine routing configuration"""
        gateway = APIGateway()

        # Check that all engines have routes
        expected_engines = {
            'ingestion', 'parser', 'entity_extractor', 'use_case_context',
            'graph_builder', 'detection', 'pattern_manager', 'ai_consensus',
            'librarian'
        }

        routed_engines = set(gateway.engine_routes.values())
        assert expected_engines.issubset(routed_engines)


# Helper functions for testing

def mock_open_patterns():
    """Mock file opening for pattern loading"""
    sample_pattern = {
        "vendor": "test",
        "product": "demo",
        "entity_mappings": {
            "hostname": {"primary": "host.name"}
        }
    }

    from unittest.mock import mock_open
    return mock_open(read_data=json.dumps(sample_pattern))


# Integration tests

class TestPipelineIntegration:
    """Test the full pipeline integration"""

    @pytest.mark.asyncio
    async def test_full_pipeline_flow(self, sample_crowdstrike_log):
        """Test complete pipeline: Ingestion -> Parser -> Entity -> Context -> Graph -> Detection"""

        # Initialize all engines
        engines = {
            'ingestion': IngestionEngine(),
            'parser': ParserEngine(),
            'entity_extractor': EntityExtractionEngine(),
            'use_case_context': UseCaseContextEngine(),
            'graph_builder': GraphBuilderEngine(),
            'detection': DetectionEngine()
        }

        # Mock the pattern loading for parser
        with patch("builtins.open", mock_open_patterns()):
            # Step 1: Ingestion
            ingestion_message = {
                "id": "integration_test_001",
                "data": {
                    "source": "test",
                    "raw": json.dumps(sample_crowdstrike_log),
                    "timestamp": datetime.utcnow().isoformat()
                }
            }

            # Since we can't test the full async pipeline easily,
            # we'll test that each engine can process its expected input

            # Test that detection engine can handle the final message format
            detection_message = {
                "id": "test_001",
                "data": {
                    "entities": [
                        {"type": "process", "value": "mimikatz.exe"},
                        {"type": "hostname", "value": "WIN-SERVER01"},
                        {"type": "technique", "value": "T1003"}
                    ],
                    "relationships": [],
                    "use_case_context": {
                        "security_context": [
                            {"context": "credential_access", "confidence": 0.95}
                        ]
                    },
                    "risk_score": 85
                }
            }

            detection_result = await engines['detection'].process_message(detection_message)
            assert detection_result["success"] == True

            # If there are detections, verify the structure
            if detection_result.get("detections"):
                detection = detection_result["detections"][0]
                assert "detection_id" in detection
                assert "severity" in detection
                assert detection["confidence"] > 0


# Performance tests

class TestPerformance:
    """Test engine performance characteristics"""

    @pytest.mark.asyncio
    async def test_detection_engine_performance(self, sample_entities, sample_relationships):
        """Test detection engine can process messages quickly"""
        engine = DetectionEngine()

        start_time = asyncio.get_event_loop().time()

        message = {
            "id": "perf_test_001",
            "data": {
                "entities": sample_entities,
                "relationships": sample_relationships,
                "use_case_context": {},
                "risk_score": 50
            }
        }

        result = await engine.process_message(message)

        end_time = asyncio.get_event_loop().time()
        processing_time = end_time - start_time

        assert result["success"] == True
        assert processing_time < 1.0  # Should process in under 1 second

    @pytest.mark.asyncio
    async def test_pattern_manager_performance(self):
        """Test pattern manager performance"""
        manager = PatternManager()

        start_time = asyncio.get_event_loop().time()

        # Test pattern listing
        message = {
            "command": "list",
            "data": {}
        }

        result = await manager.process_message(message)

        end_time = asyncio.get_event_loop().time()
        processing_time = end_time - start_time

        assert result["success"] == True
        assert processing_time < 0.5  # Should be very fast


if __name__ == "__main__":
    # Run specific test suites
    print("🧪 Running SIEMLess v2.0 Engine Tests")
    print("====================================")

    # Run pytest with verbose output
    pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "-x"  # Stop on first failure
    ])