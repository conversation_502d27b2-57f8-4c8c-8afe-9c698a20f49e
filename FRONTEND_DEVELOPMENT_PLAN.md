# SIEMLess v2.0 - Frontend Development Plan

**Created:** October 3, 2025
**Status:** 📋 PLANNING PHASE
**Backend Status:** ✅ 100% PRODUCTION READY
**Frontend Status:** ⚠️ 70% FOUNDATION COMPLETE (Needs API Integration)

---

## 🎯 Strategic Overview

### The Opportunity

Your backend is **fully operational** with unique, market-differentiating capabilities:
- **Universal CTI Plugin Architecture** - 98% faster to add threat intelligence sources
- **Detection Fidelity Quantification** - Industry-first quantitative detection confidence
- **Investigation Context System** - Query 7 vendors, 6.95B events in unified interface
- **Pattern Crystallization** - 99.97% cost reduction through "learn expensive once, operate free forever"

**The frontend's mission:** Make these invisible backend superpowers **visible and actionable** to users.

---

## 📊 Current State Assessment

### ✅ Backend Infrastructure (100% Complete)

**5 Operational Engines:**
- **Intelligence Engine (8001)**: AI consensus, pattern crystallization, 11+ AI models
- **Backend Engine (8002)**: CTI processing, log source quality, detection fidelity, correlation
- **Ingestion Engine (8003)**: Multi-source ingestion, CTI plugins (4 operational), parser generation
- **Contextualization Engine (8004)**: Entity extraction, enrichment, relationship mapping
- **Delivery Engine (8005)**: Case management, workflows, alerts, frontend services

**60+ REST API Endpoints:**
- Complete documentation in [API_DOCUMENTATION.md](API_DOCUMENTATION.md)
- Request/response schemas defined
- WebSocket support for real-time updates
- Multi-engine architecture with clear separation

**Unique Systems Ready for Frontend:**
1. **CTI Plugin Status** (`/cti/status`, `/cti/manual_update`) - 4 plugins operational
2. **Log Source Quality** (`/api/log-sources/*`) - PLATINUM/GOLD/SILVER/BRONZE tiers
3. **Detection Fidelity** (`/api/detection/fidelity`) - Quantitative attack detection confidence
4. **Coverage Simulation** (`/api/coverage/simulate`) - "What if we add X?" analysis
5. **MITRE ATT&CK** (`/api/mitre/*`) - Complete technique coverage mapping
6. **Investigation Context** (`/api/investigations/*`) - Multi-vendor evidence collection
7. **Pattern Library** (`/api/patterns/*`) - Crystallized patterns with cost savings
8. **Workflow Orchestration** (`/api/workflows/*`) - 11 pre-configured templates

### ⚠️ Frontend Status (70% Complete)

**✅ What's Working:**
- React 18 + TypeScript + Vite build system
- React Router v6 with 20+ page routes configured
- Zustand state management (investigation, navigation, auth, notifications)
- UI component libraries installed:
  - AG-Grid Enterprise (tables)
  - FlexLayout (multi-panel layouts)
  - D3.js (graphs and visualizations)
  - Recharts (time-series charts)
  - Radix UI (modals, dropdowns, tabs)
  - Lucide React (icons)
- API client with axios + interceptors
- WebSocket client implementation
- Layout components (AppShell, TopBar, SideBar)

**❌ What's Missing:**
- ⚠️ **API Integration**: Components exist but aren't connected to backend
- ⚠️ **Widget Implementations**: Skeletal components need full functionality
- ⚠️ **Real-time Updates**: WebSocket connections not actively used
- ⚠️ **State Management**: Stores need expansion for new features
- ⚠️ **Type Definitions**: Backend response types not fully defined
- ⚠️ **Multi-Engine Routing**: No clear strategy for 5-engine architecture

---

## 🗺️ Development Phases

### Phase 1: Core Data Integration (Week 1-2)
**Goal:** Connect existing components to operational backend APIs

#### 1.1 Dashboard & Real-Time Data
- **Dashboard Overview Widget**
  - API: `GET /api/dashboard/overview` (Delivery:8005)
  - Display: Active cases, critical alerts, system health, recent activity
  - WebSocket: Subscribe to `delivery.case_created`, `delivery.alert_sent`
  - Priority: **CRITICAL** - First user touchpoint

- **Alert Queue Widget**
  - API: `GET /api/alerts` (Delivery:8005)
  - Features: Live streaming, severity filtering, quick triage (assign/dismiss/investigate)
  - WebSocket: Real-time alert updates
  - Priority: **CRITICAL** - Core SOC workflow

- **CTI Plugin Status Widget** ⭐ **UNIQUE FEATURE**
  - API: `GET /cti/status` (Ingestion:8003)
  - Display: Health of all 4 CTI plugins (OTX, ThreatFox, CrowdStrike, OpenCTI)
  - Features: Indicators fetched count, priority levels, manual update triggers
  - API: `POST /cti/manual_update` for refresh
  - Priority: **HIGH** - Showcases universal plugin architecture

#### 1.2 Investigation Workflow
- **Entity Explorer Integration**
  - Enhance existing `investigationStore.ts`
  - APIs:
    - `GET /api/entities/{id}` - Entity details
    - `GET /api/entities/{id}/enrichment` - Threat intel, geolocation
    - `GET /api/entities/{id}/relationships` - Connected entities
    - `GET /api/entities/{id}/timeline` - Activity timeline
    - `GET /api/entities/{id}/risk` - Risk scoring
  - Visualization: D3.js graph for relationships
  - Priority: **CRITICAL** - Core investigation capability

- **Investigation Context** ⭐ **UNIQUE FEATURE**
  - API: `POST /api/investigations` - Create investigation
  - API: `GET /api/investigations/{id}` - Get investigation details
  - API: `POST /api/investigations/{id}/findings` - Add findings
  - Features: Query 7 vendors in parallel (CrowdStrike, Elastic, TippingPoint, etc.)
  - Display: Unified timeline across vendors, evidence collection
  - Priority: **HIGH** - Showcases 6.95B event access

---

### Phase 2: Detection & Engineering Tools (Week 3-4)
**Goal:** Surface your unique detection fidelity and pattern crystallization systems

#### 2.1 Detection Coverage Dashboard ⭐ **MARKET DIFFERENTIATOR**

- **Log Source Quality Matrix**
  - API: `GET /api/log-sources/status` (Backend:8002)
  - Display: Quality tier classification (PLATINUM/GOLD/SILVER/BRONZE)
  - Features: Source capabilities, quality scores, registration date
  - Visualization: Color-coded matrix by source type
  - Priority: **HIGH** - No competitor has this

- **Detection Fidelity Calculator** ⭐ **UNIQUE FEATURE**
  - API: `POST /api/detection/fidelity` (Backend:8002)
  - Input: Attack types (ransomware, lateral_movement, data_exfiltration)
  - Output: Quantitative confidence percentages (e.g., "35% confidence for ransomware")
  - Display:
    - Confidence gauge per attack type
    - Missing sources with impact analysis
    - Requirements met/unmet breakdown
  - Priority: **CRITICAL** - Core product differentiator

- **Coverage Simulation Tool** ⭐ **BUSINESS VALUE**
  - API: `POST /api/coverage/simulate` (Backend:8002)
  - Features: "What if we add Wazuh?" → Show confidence increase
  - Display: Before/after comparison, cost-benefit analysis
  - Use case: Justify security tool purchases with data
  - Priority: **HIGH** - Drives customer ROI conversations

#### 2.2 Pattern Library & Crystallization ⭐ **COST SAVINGS**

- **Pattern Library Browser**
  - API: `GET /api/patterns` (Delivery:8005)
  - Display: Crystallized patterns (FREE operations after first use)
  - Metrics: Pattern performance, reuse count, cost savings
  - Visualization: Cost savings over time (99.97% reduction)
  - Priority: **MEDIUM** - Proves value proposition

- **Crystallization Queue**
  - Monitor: Patterns being learned by AI
  - Display: AI model selection (Gemma FREE vs Claude premium)
  - Metrics: Pattern reuse statistics, learning cost vs. reuse savings
  - Priority: **MEDIUM** - Transparency into AI operations

---

### Phase 3: MITRE ATT&CK Integration (Week 5)
**Goal:** Surface comprehensive MITRE technique coverage

#### 3.1 MITRE Heatmap

- **Technique Coverage Heatmap**
  - API: `GET /api/mitre/heatmap` (Backend:8002, via MITRE integration)
  - Display: Visual matrix of ATT&CK techniques
  - Color coding: Detection confidence per technique (high/medium/low)
  - Interactive: Click technique → Show required log sources
  - Priority: **MEDIUM** - Industry-standard framework

- **Technique-Specific Analysis**
  - API: `POST /api/detection/technique-coverage` (Backend:8002)
  - Input: Technique IDs (T1003, T1055, T1021, T1486)
  - Output: Per-technique detection capability, gap analysis
  - Features: Actionable recommendations for coverage improvement
  - Priority: **MEDIUM** - Detailed compliance reporting

---

### Phase 4: Advanced Features (Week 6-8)
**Goal:** Complete remaining high-value features

#### 4.1 Workflow Orchestration

- **Workflow Designer/Monitor**
  - API: `GET /api/workflows` (Delivery:8005)
  - API: `GET /api/workflows/templates` - List 11 pre-configured templates
  - API: `POST /api/workflows/start` - Start workflow
  - API: `GET /api/workflows/{id}` - Monitor execution
  - Display: Real-time workflow progress, step completion
  - Priority: **MEDIUM** - Automation showcase

- **Case Management Enhancement**
  - APIs: Full CRUD via `/api/cases/*` (Delivery:8005)
  - Features:
    - `GET /api/cases/{id}/investigation-guide` - AI-generated investigation steps
    - Evidence timeline visualization
    - Collaboration (comments, assignments)
  - Priority: **MEDIUM** - Core case workflow

#### 4.2 Cost & Performance Analytics

- **Cost Analysis Dashboard** ⭐ **ROI PROOF**
  - Track: AI model usage costs
  - Display: Pattern crystallization savings ($0.02 → $0.00 per pattern)
  - Visualization: Storage tier optimization (98.4% reduction: 447MB → 7MB)
  - Metrics: "Learn expensive once" success stories
  - Priority: **MEDIUM** - Customer retention tool

- **Performance Metrics**
  - Monitor: All 5 engine health (`GET /health` on each port)
  - Display: Processing latency, throughput, error rates
  - Metrics:
    - Entity extraction rates
    - CTI fetch performance per plugin
    - Pattern match vs. AI analysis ratio
  - Priority: **LOW** - Operational monitoring

---

## 🚀 Immediate Next Steps

### Step 1: Create Comprehensive API Service Layer

Create TypeScript service modules for all backend endpoints:

```typescript
// src/api/services/cti.ts
export const ctiAPI = {
  // CTI Plugin Management (Ingestion:8003)
  getPluginStatus: () =>
    apiClient.get('http://localhost:8003/cti/status'),

  listConnectors: () =>
    apiClient.get('http://localhost:8003/cti/connectors'),

  triggerManualUpdate: (source: string, sinceDays: number, limit: number) =>
    apiClient.post('http://localhost:8003/cti/manual_update', {
      source,
      since_days: sinceDays,
      limit
    })
}

// src/api/services/detection.ts
export const detectionAPI = {
  // Detection Fidelity (Backend:8002)
  calculateFidelity: (attackTypes: string[]) =>
    apiClient.post('http://localhost:8002/api/detection/fidelity', {
      attack_types: attackTypes
    }),

  getCoverage: () =>
    apiClient.get('http://localhost:8002/api/detection/coverage'),

  getTechniqueCoverage: (techniqueIds: string[]) =>
    apiClient.post('http://localhost:8002/api/detection/technique-coverage', {
      technique_ids: techniqueIds
    })
}

// src/api/services/logSources.ts
export const logSourceAPI = {
  // Log Source Quality (Backend:8002)
  getStatus: () =>
    apiClient.get('http://localhost:8002/api/log-sources/status'),

  registerSource: (sourceConfig: LogSourceConfig) =>
    apiClient.post('http://localhost:8002/api/log-sources/register', sourceConfig),

  removeSource: (sourceId: string) =>
    apiClient.delete(`http://localhost:8002/api/log-sources/${sourceId}`)
}

// src/api/services/coverage.ts
export const coverageAPI = {
  // Coverage Analysis (Backend:8002)
  getGaps: () =>
    apiClient.get('http://localhost:8002/api/coverage/gaps'),

  simulateImpact: (addSources: any[], removeSources: string[]) =>
    apiClient.post('http://localhost:8002/api/coverage/simulate', {
      add_sources: addSources,
      remove_sources: removeSources
    })
}

// src/api/services/correlation.ts
export const correlationAPI = {
  // Correlation Capability (Backend:8002)
  getCapability: () =>
    apiClient.get('http://localhost:8002/api/correlation/capability'),

  checkRequirements: (attackType: string) =>
    apiClient.post('http://localhost:8002/api/correlation/requirements', {
      attack_type: attackType
    }),

  getRecommendations: (targetAttacks: string[]) =>
    apiClient.post('http://localhost:8002/api/correlation/recommendations', {
      target_attacks: targetAttacks
    })
}

// src/api/services/investigations.ts
export const investigationAPI = {
  // Investigation Management (Delivery:8005)
  createInvestigation: (data: any) =>
    apiClient.post('http://localhost:8005/api/investigations', data),

  getInvestigation: (investigationId: string) =>
    apiClient.get(`http://localhost:8005/api/investigations/${investigationId}`),

  addFinding: (investigationId: string, finding: any) =>
    apiClient.post(`http://localhost:8005/api/investigations/${investigationId}/findings`, finding),

  listInvestigations: (filters?: any) =>
    apiClient.get('http://localhost:8005/api/investigations', { params: filters })
}

// src/api/services/dashboard.ts
export const dashboardAPI = {
  // Dashboard Data (Delivery:8005)
  getOverview: () =>
    apiClient.get('http://localhost:8005/api/dashboard/overview'),

  getCaseStats: () =>
    apiClient.get('http://localhost:8005/api/dashboard/cases'),

  getSystemStats: () =>
    apiClient.get('http://localhost:8005/api/dashboard/stats')
}
```

### Step 2: Build First High-Impact Widget

**Recommendation: CTI Plugin Status Dashboard**

Why this first?
- Showcases your **Universal Plugin Architecture** (unique selling point)
- Simple API integration (good learning example)
- Real operational value (monitoring 4 live plugins)
- Visual and impressive

```typescript
// src/widgets/CTIPluginStatus.tsx
import React, { useEffect, useState } from 'react'
import { ctiAPI } from '../api/services/cti'

interface PluginHealth {
  source: string
  healthy: boolean
  enabled: boolean
  priority: number
  type: 'community' | 'commercial' | 'internal'
  indicators_fetched?: number
  last_check?: string
  error?: string
}

interface CTIStatusResponse {
  plugin_count: number
  plugins: string[]
  health: Record<string, PluginHealth>
}

export const CTIPluginStatus: React.FC = () => {
  const [status, setStatus] = useState<CTIStatusResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState<string | null>(null)

  useEffect(() => {
    fetchStatus()
    const interval = setInterval(fetchStatus, 30000) // Poll every 30s
    return () => clearInterval(interval)
  }, [])

  const fetchStatus = async () => {
    try {
      const response = await ctiAPI.getPluginStatus()
      setStatus(response.data)
    } catch (error) {
      console.error('Failed to fetch CTI status:', error)
    } finally {
      setLoading(false)
    }
  }

  const triggerUpdate = async (source: string) => {
    setUpdating(source)
    try {
      await ctiAPI.triggerManualUpdate(source, 1, 1000)
      setTimeout(fetchStatus, 2000) // Refresh after 2s
    } catch (error) {
      console.error('Manual update failed:', error)
    } finally {
      setUpdating(null)
    }
  }

  if (loading) return <div>Loading CTI plugin status...</div>

  return (
    <div className="cti-plugin-status-widget">
      <div className="widget-header">
        <h2>CTI Plugin Status</h2>
        <span className="plugin-count">{status?.plugin_count} plugins registered</span>
      </div>

      <div className="plugin-grid">
        {status?.plugins.map(pluginName => {
          const health = status.health[pluginName]
          return (
            <div
              key={pluginName}
              className={`plugin-card ${health.healthy ? 'healthy' : 'unhealthy'}`}
            >
              <div className="plugin-header">
                <div className="plugin-info">
                  <h3>{pluginName}</h3>
                  <span className={`plugin-type badge-${health.type}`}>
                    {health.type}
                  </span>
                </div>
                <div className="plugin-priority">
                  Priority: {health.priority}
                </div>
              </div>

              <div className="plugin-body">
                <div className="stat-row">
                  <span className="stat-label">Status:</span>
                  <span className={`stat-value ${health.healthy ? 'status-ok' : 'status-error'}`}>
                    {health.healthy ? '✓ Healthy' : '✗ Unhealthy'}
                  </span>
                </div>

                {health.indicators_fetched !== undefined && (
                  <div className="stat-row">
                    <span className="stat-label">Indicators:</span>
                    <span className="stat-value">
                      {health.indicators_fetched.toLocaleString()}
                    </span>
                  </div>
                )}

                {health.last_check && (
                  <div className="stat-row">
                    <span className="stat-label">Last Check:</span>
                    <span className="stat-value">
                      {new Date(health.last_check).toLocaleString()}
                    </span>
                  </div>
                )}

                {health.error && (
                  <div className="error-message">
                    {health.error}
                  </div>
                )}

                <button
                  className="btn-update"
                  onClick={() => triggerUpdate(pluginName)}
                  disabled={updating === pluginName}
                >
                  {updating === pluginName ? 'Updating...' : 'Manual Update'}
                </button>
              </div>
            </div>
          )
        })}
      </div>

      <div className="widget-footer">
        <button className="btn-refresh" onClick={fetchStatus}>
          Refresh All
        </button>
      </div>
    </div>
  )
}
```

### Step 3: Update Environment Configuration

```env
# .env.local
VITE_API_URL=http://localhost:8005/api
VITE_BACKEND_URL=http://localhost:8002
VITE_INGESTION_URL=http://localhost:8003
VITE_CONTEXTUALIZATION_URL=http://localhost:8004
VITE_INTELLIGENCE_URL=http://localhost:8001
VITE_WS_URL=ws://localhost:8005
```

---

## 🏗️ Architecture Decisions

### Multi-Engine API Routing Strategy

Your backend has **5 separate engines on different ports**. Frontend options:

#### Option 1: Direct Multi-Port Access (Development)
```typescript
// Different baseURLs per engine
const backendClient = axios.create({ baseURL: 'http://localhost:8002' })
const ingestionClient = axios.create({ baseURL: 'http://localhost:8003' })
const deliveryClient = axios.create({ baseURL: 'http://localhost:8005' })
```

**Pros:** Simple, mirrors backend architecture
**Cons:** CORS configuration needed, not production-ready

#### Option 2: API Gateway Pattern (Production) ⭐ **RECOMMENDED**

Use Nginx or similar to route by path prefix:

```nginx
# nginx.conf
location /api/cti/ {
  proxy_pass http://ingestion:8003/cti/;
}

location /api/detection/ {
  proxy_pass http://backend:8002/api/detection/;
}

location /api/log-sources/ {
  proxy_pass http://backend:8002/api/log-sources/;
}

location /api/coverage/ {
  proxy_pass http://backend:8002/api/coverage/;
}

location /api/correlation/ {
  proxy_pass http://backend:8002/api/correlation/;
}

location /api/graph/ {
  proxy_pass http://backend:8002/api/graph/;
}

location /api/parsers/ {
  proxy_pass http://ingestion:8003/api/parsers/;
}

location /api/ {
  proxy_pass http://delivery:8005/api/;
}

location /ws {
  proxy_pass http://delivery:8005/ws;
  proxy_http_version 1.1;
  proxy_set_header Upgrade $http_upgrade;
  proxy_set_header Connection "upgrade";
}
```

**Pros:** Single frontend URL, production-ready, easy HTTPS
**Cons:** Requires deployment configuration

### State Management Strategy

Current: **Zustand** (already installed)

**Recommended Store Structure:**

```typescript
// src/stores/
├── authStore.ts          // ✅ Already exists
├── navigationStore.ts    // ✅ Already exists
├── notificationStore.ts  // ✅ Already exists
├── investigationStore.ts // ✅ Already exists (needs enhancement)
├── ctiStore.ts          // NEW - CTI plugin status, indicators
├── detectionStore.ts    // NEW - Log sources, fidelity, coverage
├── dashboardStore.ts    // NEW - Dashboard metrics, real-time updates
├── patternStore.ts      // NEW - Pattern library, crystallization queue
├── workflowStore.ts     // NEW - Workflow execution, templates
└── mitreStore.ts        // NEW - MITRE techniques, heatmap data
```

### Real-Time Updates Strategy

**WebSocket Architecture:**

```typescript
// src/hooks/useWebSocket.ts
export const useWebSocket = () => {
  useEffect(() => {
    wsClient.connect()

    // Subscribe to channels
    wsClient.on('delivery.case_created', handleNewCase)
    wsClient.on('delivery.alert_sent', handleNewAlert)
    wsClient.on('backend.log_source_registered', handleNewSource)
    wsClient.on('cti.indicators.updated', handleCTIUpdate)

    return () => {
      wsClient.off('delivery.case_created', handleNewCase)
      // ... cleanup
    }
  }, [])
}
```

---

## 📈 Success Metrics

Track these KPIs as you build:

### Development Progress
- [ ] **API Integration Coverage**: 0/60+ endpoints connected
- [ ] **Widget Completion**: 0/17+ widgets operational with live data
- [ ] **Page Completion**: 0/20+ routes with functional components
- [ ] **WebSocket Features**: 0 real-time channels active

### Performance Targets
- [ ] Dashboard load: <2 seconds
- [ ] API response time: <500ms (p95)
- [ ] Entity graph render: <1 second for 100 nodes
- [ ] Real-time alert latency: <100ms

### User Experience Goals
- [ ] Complete investigation workflow: Alert → Investigation → Case → Resolution
- [ ] CTI plugin management: View status → Trigger update → See indicators
- [ ] Detection assessment: View coverage → Simulate improvement → Justify purchase
- [ ] Pattern library: Browse patterns → View savings → Monitor crystallization

---

## 🗓️ Sprint Planning

### Sprint 1: Foundation (Week 1-2)
**Goal:** Connect to live backend data

**Day 1-2:** API Service Layer
- Create all service modules (cti, detection, logSources, etc.)
- Define TypeScript interfaces for all responses
- Test connectivity to all 5 engines

**Day 3-5:** Dashboard & Alerts
- Dashboard overview with real metrics
- Alert queue with live data
- WebSocket integration for real-time updates

**Day 6-8:** CTI Plugin Status
- Build CTI plugin status widget
- Manual update triggers
- Health monitoring visualization

**Day 9-10:** Entity Explorer
- Connect entity APIs to existing investigationStore
- Entity details, enrichment, relationships
- D3.js relationship graph

**Sprint 1 Deliverables:**
- ✅ All API services implemented
- ✅ Dashboard showing real data
- ✅ CTI plugin status visible
- ✅ Entity investigation functional

---

### Sprint 2: Detection Systems (Week 3-4)
**Goal:** Surface unique detection fidelity features

**Day 1-3:** Log Source Quality Matrix
- Display all registered sources
- Quality tier visualization (PLATINUM/GOLD/SILVER/BRONZE)
- Source registration form

**Day 4-6:** Detection Fidelity Calculator
- Attack type selection
- Confidence percentage display
- Missing sources breakdown
- Requirements analysis

**Day 7-9:** Coverage Simulation Tool
- "What if?" scenario builder
- Before/after confidence comparison
- Cost-benefit analysis visualization

**Day 10:** Testing & Refinement
- End-to-end detection workflow testing
- Performance optimization
- Bug fixes

**Sprint 2 Deliverables:**
- ✅ Log source quality visible
- ✅ Detection fidelity calculator functional
- ✅ Coverage simulation working
- ✅ Complete detection assessment workflow

---

### Sprint 3: MITRE & Workflows (Week 5-6)
**Goal:** Complete MITRE integration and workflow orchestration

**Day 1-4:** MITRE Heatmap
- ATT&CK technique coverage matrix
- Color-coded confidence visualization
- Technique drill-down details
- Gap analysis display

**Day 5-8:** Workflow Orchestration
- Workflow template browser
- Workflow execution monitor
- Real-time progress tracking
- Workflow results display

**Day 9-10:** Case Management Enhancement
- AI-generated investigation guide display
- Evidence timeline visualization
- Case collaboration features

**Sprint 3 Deliverables:**
- ✅ MITRE heatmap operational
- ✅ Workflow execution visible
- ✅ Case management enhanced
- ✅ Investigation guide integrated

---

### Sprint 4: Polish & Advanced (Week 7-8)
**Goal:** Complete remaining features and polish UX

**Day 1-4:** Cost & Performance Analytics
- AI model cost tracking
- Pattern crystallization savings visualization
- Storage optimization metrics
- Engine health monitoring

**Day 5-7:** Pattern Library
- Pattern browser with search/filter
- Pattern performance metrics
- Crystallization queue monitor
- Cost savings dashboard

**Day 8-10:** Integration Testing & Polish
- End-to-end workflow testing
- Performance optimization
- UI/UX refinements
- Bug fixes and edge cases

**Sprint 4 Deliverables:**
- ✅ Cost analytics complete
- ✅ Pattern library functional
- ✅ All major features operational
- ✅ Production-ready frontend

---

## 🎯 Priority Matrix

### Must-Have (Critical Path)
1. **Dashboard Overview** - First touchpoint, shows system value
2. **Alert Queue** - Core SOC workflow
3. **Entity Explorer** - Investigation capability
4. **CTI Plugin Status** - Showcases unique architecture
5. **Detection Fidelity Calculator** - Market differentiator
6. **Log Source Quality Matrix** - Business value demonstration

### Should-Have (High Value)
7. Coverage Simulation Tool
8. Investigation Context (multi-vendor)
9. MITRE Heatmap
10. Workflow Orchestration
11. Case Management

### Nice-to-Have (Polish)
12. Pattern Library Browser
13. Crystallization Queue
14. Cost Analytics
15. Performance Metrics
16. Advanced visualizations

---

## 🔑 Key Architectural Principles

### 1. Data-First Development
**Don't build components in isolation.** Always start with:
1. API endpoint documentation
2. Response data structure
3. Component data requirements
4. Then build the component

### 2. Multi-Engine Awareness
Remember: **5 engines, 5 ports, different responsibilities**

| Engine | Port | When to Use |
|--------|------|-------------|
| Delivery | 8005 | Cases, workflows, alerts, dashboard |
| Backend | 8002 | Log sources, detection, correlation |
| Ingestion | 8003 | CTI plugins, parsers, sources |
| Contextualization | 8004 | Rarely direct access (internal) |
| Intelligence | 8001 | Rarely direct access (internal) |

### 3. Showcase Unique Features
**Focus frontend effort on market differentiators:**
- Universal CTI Plugin Architecture (no competitor has this)
- Detection Fidelity Quantification (industry first)
- Coverage Simulation (drives sales conversations)
- Pattern Crystallization Savings (proves ROI)

### 4. Real-Time By Default
Use WebSocket for:
- New alerts
- Case updates
- CTI indicator updates
- Log source changes
- Workflow progress

Don't poll APIs when WebSocket channels exist.

### 5. Progressive Enhancement
Build in order:
1. **Static Data** - Hard-coded placeholder data
2. **API Integration** - Fetch real data
3. **Real-Time Updates** - WebSocket integration
4. **Interactions** - User actions (create, update, delete)
5. **Polish** - Animations, loading states, error handling

---

## 🚧 Known Challenges & Solutions

### Challenge 1: Multi-Engine CORS Configuration
**Problem:** Frontend needs to call 5 different ports in development

**Solution:**
```javascript
// vite.config.ts
export default defineConfig({
  server: {
    proxy: {
      '/api/cti': 'http://localhost:8003',
      '/api/detection': 'http://localhost:8002',
      '/api/log-sources': 'http://localhost:8002',
      '/api/coverage': 'http://localhost:8002',
      '/api/correlation': 'http://localhost:8002',
      '/api': 'http://localhost:8005'
    }
  }
})
```

### Challenge 2: TypeScript Types for 60+ APIs
**Problem:** Need type definitions for all backend responses

**Solution:** Generate types from API documentation
```bash
# Option 1: Manual type definitions
# Create src/types/api.ts with all interfaces

# Option 2: Use openapi-typescript if backend has OpenAPI spec
npx openapi-typescript http://localhost:8005/openapi.json -o src/types/api.ts
```

### Challenge 3: Real-Time Data Synchronization
**Problem:** WebSocket updates need to sync with API-fetched data

**Solution:** Use Zustand with subscriptions
```typescript
// Zustand store with WebSocket integration
export const useCTIStore = create<CTIState>((set, get) => ({
  plugins: [],

  // Initialize with API data
  fetchPlugins: async () => {
    const response = await ctiAPI.getPluginStatus()
    set({ plugins: response.data.health })
  },

  // Update from WebSocket
  handlePluginUpdate: (update) => {
    set(state => ({
      plugins: {
        ...state.plugins,
        [update.plugin_name]: update.health
      }
    }))
  }
}))

// Component: Subscribe to both API and WebSocket
useEffect(() => {
  fetchPlugins() // Initial load
  wsClient.on('cti.plugin.updated', handlePluginUpdate)
  return () => wsClient.off('cti.plugin.updated', handlePluginUpdate)
}, [])
```

---

## 📚 Additional Resources

### Documentation References
- **API Documentation**: [API_DOCUMENTATION.md](API_DOCUMENTATION.md) - Complete endpoint reference
- **Backend Architecture**: [FEATURES_AND_ARCHITECTURE_v2.md](FEATURES_AND_ARCHITECTURE_v2.md)
- **CTI Plugin System**: [UNIVERSAL_PLUGIN_ARCHITECTURE_SUMMARY.md](UNIVERSAL_PLUGIN_ARCHITECTURE_SUMMARY.md)
- **Codebase Index**: [CODEBASE_INDEX.md](CODEBASE_INDEX.md) - All backend classes and functions

### Component Libraries Already Installed
- **AG-Grid Enterprise**: Advanced data tables with filtering, sorting, grouping
- **FlexLayout**: Multi-panel layouts with drag-drop, tab management
- **D3.js**: Custom visualizations, graphs, force-directed layouts
- **Recharts**: Simple time-series and statistical charts
- **Radix UI**: Accessible modals, dropdowns, tabs, tooltips
- **Lucide React**: Icon library

### Suggested Additions
```bash
# Optional: Enhanced graph visualization
npm install react-force-graph cytoscape

# Optional: Advanced animations
npm install framer-motion

# Optional: Table alternative to AG-Grid
npm install @tanstack/react-table

# Optional: Form validation beyond react-hook-form
npm install yup
```

---

## ✅ Definition of Done

### Component Checklist
A component is "done" when:
- [ ] Connected to real backend API
- [ ] Displays live data (not placeholder)
- [ ] Handles loading states
- [ ] Handles error states
- [ ] Responsive design (desktop + tablet)
- [ ] TypeScript types defined
- [ ] WebSocket updates (if applicable)
- [ ] Basic interactions work (click, filter, search)

### Feature Checklist
A feature is "done" when:
- [ ] End-to-end workflow functional
- [ ] All related components operational
- [ ] Real-time updates working
- [ ] Error handling comprehensive
- [ ] Performance acceptable (<2s load)
- [ ] User feedback mechanisms (toasts, confirmations)

### Sprint Checklist
A sprint is "done" when:
- [ ] All planned components built
- [ ] Integration testing complete
- [ ] No blocking bugs
- [ ] Documentation updated
- [ ] Demo-ready for stakeholders

---

## 🎬 Getting Started Checklist

Before writing any component code:

- [ ] Read [API_DOCUMENTATION.md](API_DOCUMENTATION.md) completely
- [ ] Understand the 5-engine architecture
- [ ] Review existing frontend structure (`src/` folder)
- [ ] Understand Zustand state management pattern
- [ ] Set up environment variables (`.env.local`)
- [ ] Test backend API connectivity (all 5 engines healthy)
- [ ] Create API service layer (Step 1)
- [ ] Build first widget (CTI Plugin Status - Step 2)
- [ ] Verify WebSocket connectivity

---

## 🏆 Success Vision

**8 Weeks from Now:**

Your frontend will showcase:
- **Real-time SOC operations** with live alerts and case management
- **Industry-first detection fidelity** with quantitative confidence scores
- **Universal CTI plugin system** managing 4+ threat intelligence sources
- **Investigation workflows** querying 6.95B events across 7 vendors
- **Pattern crystallization** proving 99.97% cost savings
- **MITRE ATT&CK coverage** with gap analysis and recommendations

**The result:** A compelling, data-driven security intelligence platform that visually demonstrates capabilities no competitor can match.

---

## 📞 Questions to Resolve Before Starting

### Technical Questions
1. **Authentication:** Keycloak integration enabled or disabled for development?
2. **API Gateway:** Using direct multi-port access or setting up Nginx proxy?
3. **Environment:** Development, staging, or production deployment target?

### Product Questions
1. **Target User:** SOC analysts, security engineers, or executives?
2. **Primary Workflow:** Investigation-first or detection-first?
3. **Key Metrics:** What KPIs matter most to demonstrate success?

---

**Status:** Ready to begin frontend development
**Next Action:** Confirm technical/product questions, then proceed with API service layer (Step 1)
**Timeline:** 8 weeks to production-ready frontend
**Risk Level:** LOW (backend stable, architecture clear, libraries installed)

---

*Last Updated: October 3, 2025*
