/**
 * Alert Queue Widget Styles
 */

.alert-queue-widget {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.widget-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.auto-refresh {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
  user-select: none;
}

.auto-refresh input[type="checkbox"] {
  cursor: pointer;
}

.refresh-button {
  background: none;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.refresh-button:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.widget-filters {
  display: flex;
  gap: 8px;
  padding: 12px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #ffffff;
  overflow-x: auto;
}

.widget-filters button {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #ffffff;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s;
}

.widget-filters button:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.widget-filters button.active {
  background: #3b82f6;
  color: #ffffff;
  border-color: #3b82f6;
}

.widget-body {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

.loading, .error, .no-alerts {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
  font-size: 14px;
}

.error {
  color: #dc2626;
}

.retry-button {
  margin-top: 12px;
  padding: 8px 16px;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s;
}

.retry-button:hover {
  background: #2563eb;
}

.alert-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.alert-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s;
}

.alert-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.severity-badge {
  font-size: 13px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.alert-time {
  font-size: 12px;
  color: #6b7280;
}

.alert-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 6px;
}

.alert-summary {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 12px;
  font-family: 'Courier New', monospace;
}

.alert-status {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.status-line {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #374151;
}

.status-icon {
  font-size: 14px;
}

.status-text {
  flex: 1;
}

.status-completed .status-text {
  color: #059669;
  font-weight: 500;
}

.status-in-progress .status-text {
  color: #d97706;
  font-weight: 500;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.threat-badge {
  background: #fef2f2;
  color: #dc2626;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.stage-count {
  background: #eff6ff;
  color: #2563eb;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.alert-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.view-button {
  padding: 8px 16px;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.view-button:hover {
  background: #2563eb;
  transform: translateX(2px);
}

/* Scrollbar styling */
.widget-body::-webkit-scrollbar {
  width: 8px;
}

.widget-body::-webkit-scrollbar-track {
  background: #f3f4f6;
}

.widget-body::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
}

.widget-body::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Responsive */
@media (max-width: 768px) {
  .widget-header {
    padding: 12px 16px;
  }

  .widget-header h2 {
    font-size: 16px;
  }

  .widget-filters {
    padding: 8px 16px;
  }

  .widget-body {
    padding: 12px 16px;
  }

  .alert-card {
    padding: 12px;
  }

  .alert-title {
    font-size: 14px;
  }

  .alert-summary {
    font-size: 13px;
  }

  .status-line {
    font-size: 12px;
  }
}
