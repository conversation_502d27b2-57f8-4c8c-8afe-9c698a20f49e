"""
AI Plugin Factory
Automatically generates ingestion and contextualization plugins from vendor API samples
Uses AI to learn vendor formats and create standardized plugins
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime


class PluginFactory:
    """
    AI-powered factory for generating vendor plugins automatically

    Workflow:
    1. Analyst provides vendor API sample
    2. AI analyzes format and field mappings
    3. Factory generates ingestion plugin code
    4. Factory generates contextualization patterns
    5. Plugins deployed automatically
    """

    def __init__(self, intelligence_client):
        self.intelligence_client = intelligence_client
        self.logger = None  # Set from parent

    async def analyze_vendor_format(
        self,
        vendor_name: str,
        api_sample: Dict[str, Any],
        api_docs_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze vendor API format and extract field mappings

        Args:
            vendor_name: Name of vendor (e.g., 'SentinelOne', 'Palo Alto')
            api_sample: Sample API response from vendor
            api_docs_url: Optional URL to API documentation

        Returns:
            Field mapping analysis with AI-generated mappings
        """

        # Build AI prompt to analyze vendor format
        prompt = f"""
You are analyzing a security vendor API format to create a data integration plugin.

VENDOR: {vendor_name}

SAMPLE API RESPONSE:
{json.dumps(api_sample, indent=2)}

{f"API DOCUMENTATION: {api_docs_url}" if api_docs_url else ""}

TASK:
Analyze this API response and identify mappings to standard security fields:

STANDARD FIELDS TO MAP:
1. Asset/Host fields:
   - device_id (unique device identifier)
   - hostname (device name)
   - local_ip (internal IP address)
   - external_ip (public IP)
   - mac_address (MAC address)
   - os_version (operating system)
   - status (device status)
   - users (list of users)
   - processes (list of processes)

2. Detection/Alert fields:
   - alert_id (unique alert ID)
   - severity (critical/high/medium/low)
   - rule_name (detection rule name)
   - tactic (MITRE tactic)
   - technique (MITRE technique ID)
   - hostname (affected host)
   - user (affected user)
   - process (process involved)
   - file_hash (file hash if applicable)

3. Network fields:
   - source_ip
   - destination_ip
   - destination_domain
   - protocol
   - port

OUTPUT FORMAT (JSON):
{{
  "vendor_name": "{vendor_name}",
  "data_model": "vendor_specific|ecs|custom",
  "authentication": "api_key|oauth|basic_auth",
  "field_mappings": {{
    "asset": {{
      "hostname": "computerName",  // Vendor field path
      "local_ip": "networkInterfaces[0].inet",
      "device_id": "id"
    }},
    "detection": {{
      "alert_id": "alert.id",
      "severity": "alert.severity"
    }},
    "network": {{
      "source_ip": "src.ip"
    }}
  }},
  "supported_categories": ["asset", "detection", "network"],
  "query_capabilities": {{
    "by_hostname": true,
    "by_ip": true,
    "by_user": false
  }},
  "sample_queries": {{
    "get_host": "GET /devices?hostname={{hostname}}",
    "get_alerts": "GET /alerts?deviceId={{device_id}}"
  }}
}}
"""

        # Get AI analysis
        ai_response = await self.intelligence_client.analyze(
            prompt=prompt,
            context={'type': 'vendor_format_analysis'},
            require_consensus=True
        )

        return ai_response

    async def generate_ingestion_plugin(
        self,
        vendor_name: str,
        field_mappings: Dict[str, Any],
        api_info: Dict[str, Any]
    ) -> str:
        """
        Generate ingestion plugin Python code

        Args:
            vendor_name: Vendor name
            field_mappings: AI-generated field mappings
            api_info: API authentication and query info

        Returns:
            Complete Python plugin code
        """

        prompt = f"""
Generate a Python context plugin for {vendor_name} using this template structure.

FIELD MAPPINGS:
{json.dumps(field_mappings, indent=2)}

API INFO:
{json.dumps(api_info, indent=2)}

GENERATE COMPLETE PYTHON CODE following this structure:

```python
\"\"\"
{vendor_name} Context Source Plugin
Auto-generated by AI Plugin Factory
\"\"\"

from typing import Dict, Any, List
from context_source_plugin import (
    ContextSourcePlugin, ContextQuery, ContextResult, ContextCategory
)

class {vendor_name.replace(' ', '')}ContextPlugin(ContextSourcePlugin):

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get('api_key')
        self.base_url = config.get('base_url')
        self.client = None

    def get_source_name(self) -> str:
        return "{vendor_name.lower().replace(' ', '_')}"

    def get_supported_categories(self) -> List[ContextCategory]:
        # Based on field_mappings, return supported categories
        return [ContextCategory.ASSET, ContextCategory.DETECTION]

    def get_supported_query_types(self) -> List[str]:
        # Based on query_capabilities
        return ['hostname', 'ip', 'device_id']

    async def validate_credentials(self) -> bool:
        # Implement auth validation
        pass

    async def query_context(self, query: ContextQuery) -> List[ContextResult]:
        # Route to appropriate query method
        pass

    async def _query_assets(self, query: ContextQuery) -> List[ContextResult]:
        # Use field_mappings['asset'] to build query
        pass

    def _format_asset_result(self, data: Dict) -> ContextResult:
        # Map vendor fields to standard format using field_mappings
        return ContextResult(
            source_name=self.get_source_name(),
            category=ContextCategory.ASSET,
            confidence=0.90,
            data={{
                'hostname': data.get('{field_mappings.get('asset', {}).get('hostname')}'),
                'local_ip': data.get('{field_mappings.get('asset', {}).get('local_ip')}'),
                # ... map all fields
            }},
            timestamp=data.get('timestamp'),
            metadata={{'raw': data}}
        )
```

Generate the COMPLETE, WORKING plugin code with:
1. All imports
2. Authentication handling
3. Query methods for each supported category
4. Field mapping using the provided mappings
5. Error handling
6. Proper documentation
"""

        # Get AI to generate plugin code
        plugin_code = await self.intelligence_client.generate_code(
            prompt=prompt,
            language='python',
            validate=True
        )

        return plugin_code

    async def generate_contextualization_patterns(
        self,
        vendor_name: str,
        field_mappings: Dict[str, Any]
    ) -> Dict[str, List[str]]:
        """
        Generate field paths for entity_extractor.py

        Args:
            vendor_name: Vendor name
            field_mappings: AI-generated field mappings

        Returns:
            Field paths to add to ENTITY_FIELD_MAPPINGS
        """

        patterns = {}

        # Extract asset field paths
        if 'asset' in field_mappings:
            asset_fields = field_mappings['asset']

            if 'hostname' in asset_fields:
                if 'hostname' not in patterns:
                    patterns['hostname'] = []
                patterns['hostname'].append(asset_fields['hostname'])

            if 'local_ip' in asset_fields:
                if 'ip_address' not in patterns:
                    patterns['ip_address'] = []
                patterns['ip_address'].append(asset_fields['local_ip'])

            if 'mac_address' in asset_fields:
                if 'mac_address' not in patterns:
                    patterns['mac_address'] = []
                patterns['mac_address'].append(asset_fields['mac_address'])

        # Extract detection field paths
        if 'detection' in field_mappings:
            detection_fields = field_mappings['detection']

            if 'alert_id' in detection_fields:
                if 'detection_id' not in patterns:
                    patterns['detection_id'] = []
                patterns['detection_id'].append(detection_fields['alert_id'])

        return patterns

    async def deploy_plugin(
        self,
        vendor_name: str,
        plugin_code: str,
        patterns: Dict[str, List[str]]
    ) -> Dict[str, Any]:
        """
        Deploy generated plugin and patterns

        Args:
            vendor_name: Vendor name
            plugin_code: Generated plugin Python code
            patterns: Field patterns for contextualization

        Returns:
            Deployment status
        """

        import os

        vendor_slug = vendor_name.lower().replace(' ', '_')

        # Write plugin file
        plugin_path = f"engines/ingestion/{vendor_slug}_context_plugin.py"
        with open(plugin_path, 'w') as f:
            f.write(plugin_code)

        # Add to entity_extractor patterns (append to existing)
        extractor_path = "engines/contextualization/entity_extractor.py"

        # Read current patterns
        with open(extractor_path, 'r') as f:
            extractor_code = f.read()

        # Generate pattern additions
        pattern_additions = []
        for entity_type, field_paths in patterns.items():
            for field_path in field_paths:
                pattern_additions.append(
                    f"# {vendor_name} - Auto-generated\n"
                    f"'{field_path}',  # {vendor_slug}"
                )

        # Instructions for manual integration (safer than auto-editing)
        integration_guide = f"""
# Integration Instructions for {vendor_name}

## 1. Register Plugin (engines/ingestion/ingestion_engine.py)

Add to _setup_context_plugins():

```python
from {vendor_slug}_context_plugin import {vendor_name.replace(' ', '')}ContextPlugin

{vendor_slug}_plugin = {vendor_name.replace(' ', '')}ContextPlugin({{
    'enabled': bool(os.getenv('{vendor_slug.upper()}_API_KEY')),
    'api_key': os.getenv('{vendor_slug.upper()}_API_KEY'),
    'base_url': os.getenv('{vendor_slug.upper()}_URL')
}})
self.context_manager.register_plugin({vendor_slug}_plugin)
```

## 2. Add Environment Variables (docker-compose.yml)

Under ingestion_engine → environment:

```yaml
- {vendor_slug.upper()}_API_KEY=${{{vendor_slug.upper()}_API_KEY}}
- {vendor_slug.upper()}_URL=${{{vendor_slug.upper()}_URL}}
```

## 3. Add Field Patterns (engines/contextualization/entity_extractor.py)

Add these paths to ENTITY_FIELD_MAPPINGS:

{chr(10).join(pattern_additions)}

## 4. Test Plugin

```bash
docker-compose up -d --build --no-deps ingestion_engine

docker-compose exec -T redis redis-cli PUBLISH 'ingestion.pull_context' '{{
  "request_id": "test-{vendor_slug}",
  "query_type": "hostname",
  "query_value": "test-device",
  "categories": ["asset"]
}}'
```
"""

        return {
            'status': 'success',
            'plugin_file': plugin_path,
            'integration_guide': integration_guide,
            'patterns_generated': patterns
        }

    async def create_plugin_from_sample(
        self,
        vendor_name: str,
        api_sample: Dict[str, Any],
        api_docs_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Complete end-to-end plugin creation from API sample

        This is the main entry point for automated plugin generation

        Args:
            vendor_name: Name of security vendor
            api_sample: Sample API response (hosts, alerts, etc.)
            api_docs_url: Optional API documentation URL

        Returns:
            Complete plugin package with code and integration guide
        """

        # Step 1: Analyze vendor format with AI
        analysis = await self.analyze_vendor_format(
            vendor_name=vendor_name,
            api_sample=api_sample,
            api_docs_url=api_docs_url
        )

        field_mappings = analysis.get('field_mappings', {})
        api_info = {
            'authentication': analysis.get('authentication'),
            'sample_queries': analysis.get('sample_queries', {}),
            'query_capabilities': analysis.get('query_capabilities', {})
        }

        # Step 2: Generate ingestion plugin code
        plugin_code = await self.generate_ingestion_plugin(
            vendor_name=vendor_name,
            field_mappings=field_mappings,
            api_info=api_info
        )

        # Step 3: Generate contextualization patterns
        patterns = await self.generate_contextualization_patterns(
            vendor_name=vendor_name,
            field_mappings=field_mappings
        )

        # Step 4: Deploy plugin and patterns
        deployment = await self.deploy_plugin(
            vendor_name=vendor_name,
            plugin_code=plugin_code,
            patterns=patterns
        )

        return {
            'vendor_name': vendor_name,
            'status': 'generated',
            'plugin_file': deployment['plugin_file'],
            'field_mappings': field_mappings,
            'patterns': patterns,
            'integration_guide': deployment['integration_guide'],
            'next_steps': [
                '1. Review generated plugin code',
                '2. Add environment variables',
                '3. Register plugin in ingestion_engine.py',
                '4. Update entity_extractor.py with field patterns',
                '5. Rebuild and test'
            ]
        }


class UseCasePluginGenerator:
    """
    Extends use case analysis to generate plugins from investigation context
    """

    def __init__(self, use_case_analyzer, plugin_factory):
        self.use_case_analyzer = use_case_analyzer
        self.plugin_factory = plugin_factory

    async def learn_from_investigation(
        self,
        investigation_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Learn from investigation results and suggest plugin improvements

        When analyst investigates using a new vendor:
        1. Capture the API responses
        2. Analyze what fields were useful
        3. Suggest plugin improvements or create new plugin
        """

        context_results = investigation_context.get('context_results', {})

        suggestions = {}

        for source_name, results in context_results.items():
            if source_name not in ['crowdstrike', 'elastic']:
                # Unknown source - might be manual API query
                # Suggest creating a plugin

                sample_result = results[0] if results else {}

                suggestions[source_name] = {
                    'action': 'create_plugin',
                    'reason': 'Unknown source detected in investigation',
                    'sample_data': sample_result,
                    'next_step': 'Run plugin_factory.create_plugin_from_sample()'
                }

        return suggestions
