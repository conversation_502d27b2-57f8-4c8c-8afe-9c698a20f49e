"""
MITRE ATT&CK Mapping Engine
Maps detection rules to MITRE ATT&CK framework (Techniques, Sub-techniques, Tactics)

This module provides:
- Automatic MITRE mapping for detection rules
- Coverage analysis across ATT&CK matrix
- Gap identification (what attacks you can't detect)
- Technique overlap analysis (redundant vs complementary coverage)
- Sub-technique granularity for precision
"""

import json
import asyncio
import aiohttp
from typing import Dict, List, Optional, Set, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import re
import logging

logger = logging.getLogger(__name__)


@dataclass
class MITRETechnique:
    """MITRE ATT&CK Technique representation"""
    technique_id: str
    name: str
    tactics: List[str]
    sub_techniques: List[str]
    data_sources: List[str]
    platforms: List[str]
    description: str
    url: str
    deprecated: bool = False


@dataclass
class RuleMITREMapping:
    """Mapping between detection rule and MITRE techniques"""
    rule_id: str
    rule_name: str
    techniques: List[str]  # T1055, T1055.001
    tactics: List[str]     # Defense Evasion, Privilege Escalation
    confidence: float      # 0.0-1.0
    mapping_method: str    # 'explicit', 'data_source', 'ai_inferred'
    coverage_score: int    # 0-100


@dataclass
class CoverageAnalysis:
    """MITRE ATT&CK coverage analysis results"""
    total_techniques: int
    covered_techniques: int
    coverage_percentage: float
    gaps: List[str]  # Uncovered technique IDs
    overlaps: Dict[str, List[str]]  # technique_id -> [rule_ids]
    by_tactic: Dict[str, Dict]  # tactic -> coverage stats


class MITREAttackMapper:
    """
    MITRE ATT&CK Framework Integration

    Capabilities:
    1. Download and parse ATT&CK framework
    2. Map detection rules to techniques
    3. Calculate coverage across matrix
    4. Identify detection gaps
    5. Analyze log source overlap
    """

    # MITRE CTI STIX repository
    MITRE_ATTACK_URL = "https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json"

    # ATT&CK Tactics (ordered by kill chain)
    TACTICS = [
        "reconnaissance",
        "resource-development",
        "initial-access",
        "execution",
        "persistence",
        "privilege-escalation",
        "defense-evasion",
        "credential-access",
        "discovery",
        "lateral-movement",
        "collection",
        "command-and-control",
        "exfiltration",
        "impact"
    ]

    def __init__(self, redis_client=None, db_pool=None, logger=None):
        self.redis = redis_client
        self.db_pool = db_pool
        self.logger = logger or logging.getLogger(__name__)

        # In-memory cache
        self.techniques = {}  # technique_id -> MITRETechnique
        self.tactics_map = {}  # tactic -> [technique_ids]
        self.data_source_map = {}  # data_source -> [technique_ids]

        # Mapping cache
        self.rule_mappings = {}  # rule_id -> RuleMITREMapping

        # Last update tracking
        self.last_framework_update = None

    async def initialize(self):
        """Initialize MITRE ATT&CK framework data"""
        self.logger.info("Initializing MITRE ATT&CK mapper...")

        # Try to load from cache first
        cached = await self._load_from_cache()
        if cached:
            self.logger.info(f"Loaded {len(self.techniques)} techniques from cache")
            return

        # Fetch from MITRE repository
        await self.fetch_attack_framework()

        # Cache for future use
        await self._save_to_cache()

    async def fetch_attack_framework(self, force_update: bool = False) -> Dict:
        """
        Fetch latest MITRE ATT&CK framework from GitHub

        Args:
            force_update: Force update even if recently updated

        Returns:
            Framework data with techniques, tactics, groups, software
        """
        # Check if update needed
        if not force_update and self.last_framework_update:
            time_since_update = datetime.utcnow() - self.last_framework_update
            if time_since_update < timedelta(days=7):
                self.logger.info("MITRE framework recently updated, skipping")
                return {'status': 'cached', 'techniques': len(self.techniques)}

        self.logger.info("Fetching MITRE ATT&CK framework from GitHub...")

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.MITRE_ATTACK_URL) as response:
                    if response.status != 200:
                        raise Exception(f"Failed to fetch MITRE data: HTTP {response.status}")

                    # GitHub returns text/plain for raw content, parse manually
                    text = await response.text()
                    framework_data = json.loads(text)

            # Parse STIX data
            await self._parse_stix_bundle(framework_data)

            # Update timestamp
            self.last_framework_update = datetime.utcnow()

            # Store in database
            if self.db_pool:
                await self._store_framework_in_db(framework_data)

            self.logger.info(f"Loaded {len(self.techniques)} techniques from MITRE ATT&CK")

            return {
                'status': 'updated',
                'techniques': len(self.techniques),
                'tactics': len(self.tactics_map),
                'timestamp': self.last_framework_update.isoformat()
            }

        except Exception as e:
            self.logger.error(f"Failed to fetch MITRE framework: {e}")
            raise

    async def _parse_stix_bundle(self, stix_data: Dict):
        """Parse STIX bundle and extract techniques"""
        objects = stix_data.get('objects', [])

        # First pass: Extract techniques
        for obj in objects:
            if obj.get('type') == 'attack-pattern':
                await self._parse_technique(obj)

        # Second pass: Build indices
        self._build_indices()

    async def _parse_technique(self, stix_object: Dict):
        """Parse STIX attack-pattern object into MITRETechnique"""
        # Extract technique ID from external references
        technique_id = None
        for ref in stix_object.get('external_references', []):
            if ref.get('source_name') == 'mitre-attack':
                technique_id = ref.get('external_id')
                url = ref.get('url', '')
                break

        if not technique_id:
            return

        # Extract tactics (kill chain phases)
        tactics = []
        for phase in stix_object.get('kill_chain_phases', []):
            if phase.get('kill_chain_name') == 'mitre-attack':
                tactics.append(phase.get('phase_name'))

        # Extract data sources
        data_sources = []
        for source in stix_object.get('x_mitre_data_sources', []):
            data_sources.append(source)

        # Extract platforms
        platforms = stix_object.get('x_mitre_platforms', [])

        # Check if deprecated
        deprecated = stix_object.get('x_mitre_deprecated', False) or stix_object.get('revoked', False)

        # Determine if this is a sub-technique
        is_sub_technique = '.' in technique_id

        # Create technique object
        technique = MITRETechnique(
            technique_id=technique_id,
            name=stix_object.get('name', ''),
            tactics=tactics,
            sub_techniques=[],  # Will be populated in second pass
            data_sources=data_sources,
            platforms=platforms,
            description=stix_object.get('description', ''),
            url=url,
            deprecated=deprecated
        )

        self.techniques[technique_id] = technique

    def _build_indices(self):
        """Build lookup indices for fast querying"""
        # Clear existing indices
        self.tactics_map = {tactic: [] for tactic in self.TACTICS}
        self.data_source_map = {}

        # Build parent-child relationships for sub-techniques
        for tech_id, technique in self.techniques.items():
            if '.' in tech_id:
                # This is a sub-technique
                parent_id = tech_id.split('.')[0]
                if parent_id in self.techniques:
                    if tech_id not in self.techniques[parent_id].sub_techniques:
                        self.techniques[parent_id].sub_techniques.append(tech_id)

        # Build tactic index
        for tech_id, technique in self.techniques.items():
            for tactic in technique.tactics:
                if tactic in self.tactics_map:
                    self.tactics_map[tactic].append(tech_id)

        # Build data source index
        for tech_id, technique in self.techniques.items():
            for data_source in technique.data_sources:
                if data_source not in self.data_source_map:
                    self.data_source_map[data_source] = []
                self.data_source_map[data_source].append(tech_id)

    async def map_rule_to_mitre(self, rule: Dict) -> RuleMITREMapping:
        """
        Map a detection rule to MITRE ATT&CK techniques

        Mapping Methods (in order of precedence):
        1. Explicit: Rule contains MITRE tags
        2. Data Source: Match rule's log sources to technique data sources
        3. AI Inferred: Use AI to analyze rule logic and infer techniques
        """
        rule_id = rule.get('rule_id', rule.get('id', 'unknown'))
        rule_name = rule.get('name', rule.get('title', ''))

        # Method 1: Check for explicit MITRE tags
        explicit_techniques = self._extract_explicit_mitre_tags(rule)
        if explicit_techniques:
            mapping = await self._create_mapping(
                rule_id, rule_name, explicit_techniques,
                confidence=0.95, method='explicit'
            )
            self.rule_mappings[rule_id] = mapping
            return mapping

        # Method 2: Match by data sources
        data_source_techniques = await self._match_by_data_sources(rule)
        if data_source_techniques:
            mapping = await self._create_mapping(
                rule_id, rule_name, data_source_techniques,
                confidence=0.75, method='data_source'
            )
            self.rule_mappings[rule_id] = mapping
            return mapping

        # Method 3: AI inference (if Intelligence Engine available)
        ai_techniques = await self._infer_with_ai(rule)
        if ai_techniques:
            mapping = await self._create_mapping(
                rule_id, rule_name, ai_techniques,
                confidence=0.60, method='ai_inferred'
            )
            self.rule_mappings[rule_id] = mapping
            return mapping

        # No mapping found
        mapping = RuleMITREMapping(
            rule_id=rule_id,
            rule_name=rule_name,
            techniques=[],
            tactics=[],
            confidence=0.0,
            mapping_method='none',
            coverage_score=0
        )
        self.rule_mappings[rule_id] = mapping
        return mapping

    def _extract_explicit_mitre_tags(self, rule: Dict) -> List[str]:
        """Extract MITRE technique IDs from rule metadata"""
        techniques = []

        # Common field names for MITRE tags
        mitre_fields = ['mitre_attack', 'mitre', 'technique', 'techniques', 'tags']

        for field in mitre_fields:
            if field in rule:
                value = rule[field]

                # Handle different formats
                if isinstance(value, list):
                    for item in value:
                        tech_id = self._parse_technique_id(str(item))
                        if tech_id:
                            techniques.append(tech_id)
                elif isinstance(value, str):
                    tech_id = self._parse_technique_id(value)
                    if tech_id:
                        techniques.append(tech_id)

        # Search in rule text fields
        searchable_text = ' '.join([
            str(rule.get('description', '')),
            str(rule.get('name', '')),
            str(rule.get('title', ''))
        ])

        # Regex for T1234 or T1234.001 format
        pattern = r'T\d{4}(?:\.\d{3})?'
        found_techniques = re.findall(pattern, searchable_text)
        techniques.extend(found_techniques)

        # Deduplicate and validate
        valid_techniques = []
        for tech_id in set(techniques):
            if tech_id in self.techniques:
                valid_techniques.append(tech_id)

        return valid_techniques

    def _parse_technique_id(self, text: str) -> Optional[str]:
        """Parse technique ID from text"""
        # Match T1234 or T1234.001
        match = re.search(r'(T\d{4}(?:\.\d{3})?)', text)
        return match.group(1) if match else None

    async def _match_by_data_sources(self, rule: Dict) -> List[str]:
        """Match rule to techniques based on log sources"""
        rule_log_sources = self._extract_log_sources(rule)
        if not rule_log_sources:
            return []

        matched_techniques = set()

        for log_source in rule_log_sources:
            # Normalize log source name
            normalized = self._normalize_data_source_name(log_source)

            # Find techniques that use this data source
            for data_source, tech_ids in self.data_source_map.items():
                if normalized.lower() in data_source.lower() or data_source.lower() in normalized.lower():
                    matched_techniques.update(tech_ids)

        return list(matched_techniques)

    def _extract_log_sources(self, rule: Dict) -> List[str]:
        """Extract log sources from rule"""
        sources = []

        # Check common fields
        if 'logsource' in rule:
            logsource = rule['logsource']
            if isinstance(logsource, dict):
                # Handle category as list or string
                category = logsource.get('category', '')
                if isinstance(category, list):
                    sources.extend(category)
                else:
                    sources.append(category)

                # Add other fields
                sources.extend([
                    logsource.get('product', ''),
                    logsource.get('service', '')
                ])
            else:
                sources.append(str(logsource))

        if 'data_source' in rule:
            sources.append(str(rule['data_source']))

        return [s for s in sources if s]

    def _normalize_data_source_name(self, source: str) -> str:
        """Normalize data source name for matching"""
        # MITRE uses specific data source names
        mappings = {
            'windows': 'Windows',
            'linux': 'Linux',
            'process': 'Process',
            'network': 'Network Traffic',
            'file': 'File',
            'registry': 'Windows Registry',
            'authentication': 'Logon Session',
            'dns': 'DNS',
            'web': 'Web',
            'cloud': 'Cloud'
        }

        source_lower = source.lower()
        for key, value in mappings.items():
            if key in source_lower:
                return value

        return source

    async def _infer_with_ai(self, rule: Dict) -> List[str]:
        """Use AI to infer MITRE techniques from rule logic"""
        if not self.redis:
            return []

        # Build prompt for AI
        prompt = self._build_mitre_inference_prompt(rule)

        # Request AI analysis via Intelligence Engine
        request_id = f"mitre_inference_{rule.get('rule_id', 'unknown')}"

        try:
            await self.redis.publish('intelligence.mitre_inference', json.dumps({
                'request_id': request_id,
                'rule': rule,
                'prompt': prompt,
                'response_channel': f'backend.mitre_inference.response.{request_id}'
            }))

            # Wait for response (with timeout)
            pubsub = self.redis.pubsub()
            await pubsub.subscribe(f'backend.mitre_inference.response.{request_id}')

            # Wait up to 30 seconds for response
            timeout = 30
            start_time = asyncio.get_event_loop().time()

            async for message in pubsub.listen():
                if asyncio.get_event_loop().time() - start_time > timeout:
                    break

                if message['type'] == 'message':
                    response = json.loads(message['data'])
                    techniques = response.get('techniques', [])

                    # Validate techniques
                    valid = [t for t in techniques if t in self.techniques]
                    return valid

        except Exception as e:
            self.logger.error(f"AI inference failed: {e}")

        return []

    def _build_mitre_inference_prompt(self, rule: Dict) -> str:
        """Build prompt for AI-based MITRE technique inference"""
        return f"""Analyze this detection rule and identify applicable MITRE ATT&CK techniques.

Rule Name: {rule.get('name', rule.get('title', ''))}
Description: {rule.get('description', '')}
Log Source: {rule.get('logsource', {})}
Detection Logic: {rule.get('detection', rule.get('query', ''))}

Based on what this rule detects, which MITRE ATT&CK techniques does it cover?

Respond with ONLY a JSON array of technique IDs (format: ["T1055", "T1055.001"]):
"""

    async def _create_mapping(self, rule_id: str, rule_name: str,
                             techniques: List[str], confidence: float,
                             method: str) -> RuleMITREMapping:
        """Create RuleMITREMapping object with calculated fields"""
        # Extract tactics from techniques
        tactics = set()
        for tech_id in techniques:
            if tech_id in self.techniques:
                tactics.update(self.techniques[tech_id].tactics)

        # Calculate coverage score (0-100)
        # Based on: number of techniques, confidence, whether sub-techniques included
        base_score = min(len(techniques) * 10, 50)  # Max 50 points for quantity
        confidence_score = confidence * 30  # Max 30 points for confidence

        # Bonus for sub-technique coverage
        sub_tech_bonus = 0
        for tech_id in techniques:
            if '.' in tech_id:
                sub_tech_bonus += 2
        sub_tech_bonus = min(sub_tech_bonus, 20)  # Max 20 points

        coverage_score = int(base_score + confidence_score + sub_tech_bonus)

        return RuleMITREMapping(
            rule_id=rule_id,
            rule_name=rule_name,
            techniques=techniques,
            tactics=list(tactics),
            confidence=confidence,
            mapping_method=method,
            coverage_score=coverage_score
        )

    async def calculate_coverage(self, rule_mappings: Optional[List[RuleMITREMapping]] = None) -> CoverageAnalysis:
        """
        Calculate MITRE ATT&CK coverage based on mapped rules

        Returns:
            CoverageAnalysis with coverage stats and gaps
        """
        if rule_mappings is None:
            rule_mappings = list(self.rule_mappings.values())

        # Get all non-deprecated techniques
        active_techniques = {
            tech_id for tech_id, tech in self.techniques.items()
            if not tech.deprecated and '.' not in tech_id  # Only parent techniques
        }

        total_techniques = len(active_techniques)

        # Find covered techniques
        covered_techniques = set()
        technique_to_rules = {}  # For overlap analysis

        for mapping in rule_mappings:
            for tech_id in mapping.techniques:
                # Handle sub-techniques (count parent as covered)
                parent_id = tech_id.split('.')[0]
                covered_techniques.add(parent_id)

                # Track which rules cover this technique
                if parent_id not in technique_to_rules:
                    technique_to_rules[parent_id] = []
                technique_to_rules[parent_id].append(mapping.rule_id)

        covered_count = len(covered_techniques)
        coverage_percentage = (covered_count / total_techniques * 100) if total_techniques > 0 else 0

        # Identify gaps
        gaps = list(active_techniques - covered_techniques)

        # Identify overlaps (techniques covered by multiple rules)
        overlaps = {
            tech_id: rules for tech_id, rules in technique_to_rules.items()
            if len(rules) > 1
        }

        # Calculate coverage by tactic
        by_tactic = {}
        for tactic in self.TACTICS:
            tactic_techniques = set(self.tactics_map.get(tactic, []))
            tactic_techniques = {t for t in tactic_techniques if '.' not in t and not self.techniques[t].deprecated}

            covered_in_tactic = tactic_techniques & covered_techniques

            by_tactic[tactic] = {
                'total': len(tactic_techniques),
                'covered': len(covered_in_tactic),
                'coverage_percentage': (len(covered_in_tactic) / len(tactic_techniques) * 100) if len(tactic_techniques) > 0 else 0,
                'gaps': list(tactic_techniques - covered_in_tactic)
            }

        return CoverageAnalysis(
            total_techniques=total_techniques,
            covered_techniques=covered_count,
            coverage_percentage=coverage_percentage,
            gaps=gaps,
            overlaps=overlaps,
            by_tactic=by_tactic
        )

    async def analyze_log_source_overlap(self, log_sources: List[Dict]) -> Dict:
        """
        Analyze which log sources provide overlapping vs complementary coverage

        Args:
            log_sources: List of log source configurations with quality scores

        Returns:
            Analysis showing overlap, gaps, and redundancy
        """
        source_coverage = {}

        # For each log source, determine which techniques it can detect
        for source in log_sources:
            source_name = source.get('name', source.get('vendor', ''))
            source_type = source.get('type', '')

            # Map source capabilities to data sources
            data_sources = self._map_source_to_data_sources(source)

            # Find techniques detectable by this source
            detectable_techniques = set()
            for data_source in data_sources:
                normalized = self._normalize_data_source_name(data_source)
                for ds, tech_ids in self.data_source_map.items():
                    if normalized.lower() in ds.lower() or ds.lower() in normalized.lower():
                        detectable_techniques.update(tech_ids)

            source_coverage[source_name] = {
                'techniques': list(detectable_techniques),
                'count': len(detectable_techniques),
                'quality_score': source.get('quality_score', 0),
                'type': source_type
            }

        # Analyze overlap
        overlap_matrix = {}
        for source1_name, source1_data in source_coverage.items():
            overlap_matrix[source1_name] = {}
            source1_techs = set(source1_data['techniques'])

            for source2_name, source2_data in source_coverage.items():
                if source1_name == source2_name:
                    continue

                source2_techs = set(source2_data['techniques'])

                overlap_count = len(source1_techs & source2_techs)
                unique_to_source1 = len(source1_techs - source2_techs)

                overlap_matrix[source1_name][source2_name] = {
                    'overlap': overlap_count,
                    'unique_to_source1': unique_to_source1,
                    'overlap_percentage': (overlap_count / len(source1_techs) * 100) if len(source1_techs) > 0 else 0
                }

        # Calculate total coverage
        all_covered = set()
        for source_data in source_coverage.values():
            all_covered.update(source_data['techniques'])

        total_techniques = len([t for t in self.techniques.keys() if '.' not in t and not self.techniques[t].deprecated])

        return {
            'total_coverage': {
                'techniques_covered': len(all_covered),
                'total_techniques': total_techniques,
                'coverage_percentage': (len(all_covered) / total_techniques * 100) if total_techniques > 0 else 0
            },
            'by_source': source_coverage,
            'overlap_matrix': overlap_matrix,
            'recommendations': self._generate_coverage_recommendations(source_coverage, all_covered)
        }

    def _map_source_to_data_sources(self, source: Dict) -> List[str]:
        """Map log source to MITRE data sources"""
        source_type = source.get('type', '').lower()

        # Mapping of source types to MITRE data sources
        type_mappings = {
            'edr': ['Process', 'File', 'Windows Registry', 'Network Traffic', 'Module'],
            'firewall': ['Network Traffic', 'Firewall'],
            'ids': ['Network Traffic', 'Intrusion Detection System'],
            'auth': ['Logon Session', 'Active Directory'],
            'cloud': ['Cloud Service', 'Cloud Storage'],
            'email': ['Email', 'Application Log'],
            'web': ['Web', 'Application Log', 'Network Traffic'],
            'dns': ['DNS', 'Network Traffic'],
            'proxy': ['Web Proxy', 'Network Traffic']
        }

        return type_mappings.get(source_type, ['Application Log'])

    def _generate_coverage_recommendations(self, source_coverage: Dict, all_covered: Set[str]) -> List[Dict]:
        """Generate recommendations for improving coverage"""
        recommendations = []

        # Find high-value gaps (techniques with no coverage)
        active_techniques = {
            tech_id for tech_id, tech in self.techniques.items()
            if not tech.deprecated and '.' not in tech_id
        }

        gaps = active_techniques - all_covered

        if gaps:
            # Prioritize by tactic
            high_priority_tactics = ['initial-access', 'execution', 'persistence', 'privilege-escalation']

            for gap_id in gaps:
                technique = self.techniques[gap_id]
                priority = 'HIGH' if any(t in high_priority_tactics for t in technique.tactics) else 'MEDIUM'

                recommendations.append({
                    'type': 'gap',
                    'technique_id': gap_id,
                    'technique_name': technique.name,
                    'tactics': technique.tactics,
                    'priority': priority,
                    'required_data_sources': technique.data_sources,
                    'recommendation': f"Add log source providing: {', '.join(technique.data_sources[:2])}"
                })

        return recommendations

    async def _load_from_cache(self) -> bool:
        """Load MITRE framework from cache"""
        if not self.redis:
            return False

        try:
            cached_data = await self.redis.get('mitre_attack_framework')
            if cached_data:
                data = json.loads(cached_data)

                # Reconstruct technique objects
                for tech_id, tech_dict in data.get('techniques', {}).items():
                    self.techniques[tech_id] = MITRETechnique(**tech_dict)

                self.tactics_map = data.get('tactics_map', {})
                self.data_source_map = data.get('data_source_map', {})
                self.last_framework_update = datetime.fromisoformat(data.get('last_update', datetime.min.isoformat()))

                return True
        except Exception as e:
            self.logger.error(f"Failed to load from cache: {e}")

        return False

    async def _save_to_cache(self):
        """Save MITRE framework to cache"""
        if not self.redis:
            return

        try:
            cache_data = {
                'techniques': {tech_id: asdict(tech) for tech_id, tech in self.techniques.items()},
                'tactics_map': self.tactics_map,
                'data_source_map': self.data_source_map,
                'last_update': self.last_framework_update.isoformat() if self.last_framework_update else None
            }

            self.redis.setex(
                'mitre_attack_framework',
                604800,  # 7 days TTL
                json.dumps(cache_data)
            )
        except Exception as e:
            self.logger.error(f"Failed to save to cache: {e}")

    async def _store_framework_in_db(self, framework_data: Dict):
        """Store MITRE framework in database for persistence"""
        if not self.db_pool:
            return

        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO mitre_attack_framework (version, data, updated_at)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (version) DO UPDATE
                    SET data = $4, updated_at = $5
                """, 'enterprise-attack', json.dumps(framework_data), datetime.utcnow(),
                    json.dumps(framework_data), datetime.utcnow())

        except Exception as e:
            self.logger.error(f"Failed to store framework in database: {e}")

    async def get_technique_details(self, technique_id: str) -> Optional[MITRETechnique]:
        """Get detailed information about a technique"""
        return self.techniques.get(technique_id)

    async def get_techniques_by_tactic(self, tactic: str) -> List[MITRETechnique]:
        """Get all techniques for a specific tactic"""
        tech_ids = self.tactics_map.get(tactic, [])
        return [self.techniques[tid] for tid in tech_ids if tid in self.techniques]

    async def get_coverage_heatmap(self, rule_mappings: List[RuleMITREMapping]) -> Dict:
        """
        Generate heatmap data for visualizing coverage

        Returns:
            Dict with tactics as rows, technique counts as values
        """
        heatmap = {}

        # Build coverage map
        covered_techniques = set()
        for mapping in rule_mappings:
            covered_techniques.update(mapping.techniques)

        # Generate heatmap
        for tactic in self.TACTICS:
            tactic_techniques = set(self.tactics_map.get(tactic, []))
            tactic_techniques = {t for t in tactic_techniques if '.' not in t}  # Parents only

            covered_in_tactic = tactic_techniques & {t.split('.')[0] for t in covered_techniques}

            heatmap[tactic] = {
                'total': len(tactic_techniques),
                'covered': len(covered_in_tactic),
                'coverage_percentage': (len(covered_in_tactic) / len(tactic_techniques) * 100) if len(tactic_techniques) > 0 else 0,
                'techniques': [
                    {
                        'id': tech_id,
                        'name': self.techniques[tech_id].name,
                        'covered': tech_id in {t.split('.')[0] for t in covered_techniques}
                    }
                    for tech_id in sorted(tactic_techniques)
                ]
            }

        return heatmap
