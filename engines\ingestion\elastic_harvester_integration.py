"""
Integration module for Elastic Rule Harvester with Ingestion Engine
Converts harvested Elastic rules to SIEMLess pattern format
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from elastic_rule_harvester import ElasticRuleHarvester, ElasticRule, KibanaSavedSearch


class ElasticHarvesterIntegration:
    """
    Integrates Elastic Rule Harvester with SIEMLess v2.0 Ingestion Engine
    Converts harvested rules to pattern library format
    """

    def __init__(self, redis_client, db_connection, logger: Optional[logging.Logger] = None):
        self.redis_client = redis_client
        self.db_connection = db_connection
        self.logger = logger or logging.getLogger(__name__)
        self.harvester = ElasticRuleHarvester(self.logger)

    async def start_harvest(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Start harvesting from Elastic instance

        Args:
            config: Elastic connection configuration
                {
                    'url': 'https://elastic:9200',
                    'api_key': '...' or 'username'/'password',
                    'cloud_id': '...' (optional),
                    'verify_ssl': True/False
                }

        Returns:
            Statistics about harvested artifacts
        """
        try:
            # Configure harvester
            if not await self.harvester.configure(config):
                return {'success': False, 'error': 'Failed to configure Elastic connection'}

            # Harvest all artifacts
            results = await self.harvester.harvest_all()

            if not results['success']:
                return {'success': False, 'error': 'Harvest failed'}

            # Process and store harvested artifacts
            stats = await self._process_harvest_results(results)

            return {
                'success': True,
                'stats': stats,
                'harvest_time': results['harvest_time']
            }

        except Exception as e:
            self.logger.error(f"Harvest failed: {e}")
            return {'success': False, 'error': str(e)}
        finally:
            await self.harvester.close()

    async def _process_harvest_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process harvest results and store in SIEMLess database

        Args:
            results: Harvest results from ElasticRuleHarvester

        Returns:
            Statistics about processed artifacts
        """
        stats = {
            'detection_rules_processed': 0,
            'saved_searches_processed': 0,
            'patterns_created': 0,
            'errors': 0
        }

        # Process detection rules
        for rule in results.get('detection_rules', []):
            try:
                await self._store_detection_rule(rule)
                stats['detection_rules_processed'] += 1
            except Exception as e:
                self.logger.error(f"Error storing detection rule {rule.name}: {e}")
                stats['errors'] += 1

        # Process saved searches
        for search in results.get('saved_searches', []):
            try:
                await self._store_saved_search(search)
                stats['saved_searches_processed'] += 1
            except Exception as e:
                self.logger.error(f"Error storing saved search {search.title}: {e}")
                stats['errors'] += 1

        # Publish harvest completion event
        await self._publish_harvest_event(stats)

        return stats

    async def _store_detection_rule(self, rule: ElasticRule):
        """
        Store Elastic detection rule in pattern library

        Converts Elastic rule format to SIEMLess pattern format
        """
        # Convert to SIEMLess pattern format
        pattern_data = {
            'source': 'elastic_security',
            'rule_id': rule.rule_id,
            'name': rule.name,
            'description': rule.description,
            'query': {
                'text': rule.query,
                'language': rule.language,
                'type': rule.rule_type
            },
            'severity': rule.severity,
            'risk_score': rule.risk_score,
            'mitre_techniques': rule.mitre_techniques,
            'tags': rule.tags,
            'index_patterns': rule.index_patterns,
            'filters': rule.filters,
            'false_positives': rule.false_positives,
            'enabled': rule.enabled,
            'metadata': {
                'interval': rule.interval,
                'from_time': rule.from_time,
                'created_by': rule.created_by,
                'created_at': rule.created_at,
                'updated_by': rule.updated_by,
                'updated_at': rule.updated_at,
                'version': rule.version,
                'harvest_time': datetime.utcnow().isoformat()
            }
        }

        # Store in database
        cursor = self.db_connection.cursor()
        try:
            cursor.execute("""
                INSERT INTO pattern_library
                (pattern_id, pattern_type, pattern_name, pattern_data, source_type, is_active, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (pattern_id) DO UPDATE SET
                    pattern_data = EXCLUDED.pattern_data,
                    updated_at = CURRENT_TIMESTAMP
            """, (
                f"elastic_rule_{rule.rule_id}",
                'detection_rule',
                rule.name,
                json.dumps(pattern_data),
                'elastic_security',
                rule.enabled,
                datetime.utcnow()
            ))
            self.db_connection.commit()
            self.logger.debug(f"Stored detection rule: {rule.name}")
        except Exception as e:
            self.db_connection.rollback()
            raise e
        finally:
            cursor.close()

    async def _store_saved_search(self, search: KibanaSavedSearch):
        """
        Store Kibana saved search in pattern library
        """
        pattern_data = {
            'source': 'kibana_saved_search',
            'id': search.id,
            'title': search.title,
            'description': search.description,
            'query': search.query,
            'filters': search.filters,
            'columns': search.columns,
            'sort': search.sort,
            'index_pattern': search.index_pattern,
            'metadata': {
                'created_at': search.created_at,
                'updated_at': search.updated_at,
                'harvest_time': datetime.utcnow().isoformat()
            }
        }

        # Store in database
        cursor = self.db_connection.cursor()
        try:
            cursor.execute("""
                INSERT INTO pattern_library
                (pattern_id, pattern_type, pattern_name, pattern_data, source_type, is_active, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (pattern_id) DO UPDATE SET
                    pattern_data = EXCLUDED.pattern_data,
                    updated_at = CURRENT_TIMESTAMP
            """, (
                f"kibana_search_{search.id}",
                'saved_search',
                search.title,
                json.dumps(pattern_data),
                'kibana',
                True,
                datetime.utcnow()
            ))
            self.db_connection.commit()
            self.logger.debug(f"Stored saved search: {search.title}")
        except Exception as e:
            self.db_connection.rollback()
            raise e
        finally:
            cursor.close()

    async def _publish_harvest_event(self, stats: Dict[str, Any]):
        """
        Publish harvest completion event to Redis for other engines
        """
        try:
            event = {
                'type': 'elastic_harvest_complete',
                'timestamp': datetime.utcnow().isoformat(),
                'stats': stats
            }

            await self.redis_client.publish(
                'ingestion.harvest_complete',
                json.dumps(event)
            )

            # Also notify Intelligence Engine for pattern crystallization
            await self.redis_client.publish(
                'intelligence.new_patterns',
                json.dumps({
                    'source': 'elastic_harvest',
                    'pattern_count': stats['detection_rules_processed'] + stats['saved_searches_processed']
                })
            )

        except Exception as e:
            self.logger.error(f"Failed to publish harvest event: {e}")

    async def convert_rule_to_multi_siem(self, rule: ElasticRule) -> Dict[str, str]:
        """
        Convert Elastic rule to multiple SIEM formats
        Uses the query translator in backend engine

        Returns:
            Dictionary with rule in different SIEM formats
        """
        conversions = {
            'elastic_kql': rule.query,
            'splunk_spl': await self._convert_to_splunk(rule),
            'sentinel_kql': await self._convert_to_sentinel(rule),
            'qradar_aql': await self._convert_to_qradar(rule)
        }

        return conversions

    async def _convert_to_splunk(self, rule: ElasticRule) -> str:
        """
        Convert Elastic KQL rule to Splunk SPL
        This is a placeholder - actual implementation would use query translator
        """
        # TODO: Integrate with backend query_translator.py
        return f"# Converted from Elastic rule: {rule.name}\n# Original query: {rule.query}"

    async def _convert_to_sentinel(self, rule: ElasticRule) -> str:
        """
        Convert Elastic KQL rule to Sentinel KQL
        """
        # Sentinel also uses KQL, so minimal conversion needed
        return rule.query

    async def _convert_to_qradar(self, rule: ElasticRule) -> str:
        """
        Convert Elastic KQL rule to QRadar AQL
        """
        # TODO: Integrate with backend query_translator.py
        return f"/* Converted from Elastic rule: {rule.name} */\n/* Original query: {rule.query} */"

    def get_harvest_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about harvested Elastic rules from database
        """
        cursor = self.db_connection.cursor()
        try:
            cursor.execute("""
                SELECT
                    COUNT(*) as total_rules,
                    COUNT(CASE WHEN pattern_type = 'detection_rule' THEN 1 END) as detection_rules,
                    COUNT(CASE WHEN pattern_type = 'saved_search' THEN 1 END) as saved_searches,
                    MAX(created_at) as last_harvest
                FROM pattern_library
                WHERE source_type IN ('elastic_security', 'kibana')
            """)

            row = cursor.fetchone()

            return {
                'total_rules': row[0] if row else 0,
                'detection_rules': row[1] if row else 0,
                'saved_searches': row[2] if row else 0,
                'last_harvest': row[3].isoformat() if row and row[3] else None
            }
        finally:
            cursor.close()


# Example usage for testing
async def test_integration():
    """Test the integration with sample configuration"""
    import redis.asyncio as redis
    import psycopg2

    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # Sample configuration
    config = {
        'url': 'https://your-elastic:9200',
        'api_key': 'your_api_key',
        'verify_ssl': False
    }

    # Connect to Redis and PostgreSQL
    redis_client = await redis.Redis(host='localhost', port=6380, decode_responses=True)
    db_conn = psycopg2.connect(
        dbname='siemless_v2',
        user='siemless',
        password='siemless123',
        host='localhost',
        port=5433
    )

    # Create integration instance
    integration = ElasticHarvesterIntegration(redis_client, db_conn, logger)

    # Start harvest
    result = await integration.start_harvest(config)

    print("\nHarvest Result:")
    print(json.dumps(result, indent=2))

    # Get statistics
    stats = integration.get_harvest_statistics()
    print("\nHarvest Statistics:")
    print(json.dumps(stats, indent=2))

    # Cleanup
    await redis_client.close()
    db_conn.close()


if __name__ == "__main__":
    asyncio.run(test_integration())
