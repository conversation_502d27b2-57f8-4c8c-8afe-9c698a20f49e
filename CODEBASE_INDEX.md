# SIEMLess v2.0 - Comprehensive Codebase Index

**Generated:** 2025-10-02
**Purpose:** Complete index of all classes, functions, API endpoints, and Redis channels

---

## Table of Contents
- [Base Engine](#base-engine)
- [Intelligence Engine](#intelligence-engine)
- [Backend Engine](#backend-engine)
- [Ingestion Engine](#ingestion-engine)
- [Contextualization Engine](#contextualization-engine)
- [Delivery Engine](#delivery-engine)
- [Frontend Components](#frontend-components)

---

## Base Engine

### `engines/base_engine.py`

**Purpose:** Abstract base class for all 5 engines with common functionality

#### Classes

**`BaseEngine(ABC)`** - Base class for all SIEMLess v2.0 engines
- **Initialization:**
  - `__init__(engine_name: str)` - Initialize with engine name, setup logging, Redis, PostgreSQL

- **Core Methods:**
  - `async start()` - Start the engine with all tasks
  - `async shutdown()` - Graceful shutdown with cleanup
  - `async health_check() -> Dict[str, Any]` - Perform health check
  - `get_metrics() -> Dict[str, Any]` - Get engine performance metrics

- **Database & Redis:**
  - `_setup_redis() -> redis.Redis` - Setup Redis connection
  - `_setup_database() -> psycopg2.connection` - Setup PostgreSQL connection
  - `publish_message(channel: str, message: Dict)` - Publish message to Redis channel

- **HTTP Server:**
  - `async _start_http_server()` - Start HTTP server for health checks
  - `async _health_check(request)` - Health check endpoint handler
  - `async _metrics_endpoint(request)` - Metrics endpoint handler

- **Background Tasks:**
  - `async _heartbeat_loop()` - Send periodic heartbeat (every 30s)
  - `async _message_loop()` - Process messages from Redis (async, non-blocking)

- **Workflow & Coordination:**
  - `store_workflow_instance(workflow_id, target_engine, data)` - Store workflow instance
  - `_register_engine()` - Register engine in coordination table
  - `_unregister_engine()` - Unregister engine from coordination table

- **Abstract Methods (must be implemented by child classes):**
  - `async process_message(message: Dict[str, Any])` - Process incoming message
  - `get_subscribed_channels() -> List[str]` - Return subscribed Redis channels
  - `start_engine_tasks() -> List[asyncio.Task]` - Start engine-specific tasks
  - `async _setup_http_routes(app)` - Setup engine-specific HTTP routes

#### Configuration
- **Ports:** Intelligence=8001, Backend=8002, Ingestion=8003, Contextualization=8004, Delivery=8005
- **Database:** siemless_v2 (PostgreSQL)
- **Redis:** Port 6379 (configurable via REDIS_PORT env var)

#### Key Patterns

**Async Fix Pattern (CRITICAL):**
```python
# BROKEN: Synchronous Redis blocks event loop
pubsub.get_message(timeout=1)  # BLOCKS!

# FIXED: Async Redis yields to event loop
import redis.asyncio as redis_async
await pubsub.get_message(timeout=1.0)  # Yields!
await asyncio.sleep(0.01)  # Explicit yield
```

---

## Intelligence Engine

### `engines/intelligence/ai_models.py`

**Purpose:** AI model configuration, API calls, and provider integrations

#### Classes

**`AIModelManager`** - Manages AI model configurations and API calls

**Attributes:**
- `ai_models: Dict` - Model configurations by tier (free, low_cost, mid_quality, high_quality, premium, latest)

**Methods:**
- `select_models_for_task(complexity: str) -> List[str]` - Select models based on task complexity
- `get_model_info(model_tier: str) -> Dict[str, Any]` - Get model information
- `async call_ai_model(model_tier: str, data: Dict) -> Dict[str, Any]` - Call specific AI model
- `async _call_anthropic_api(prompt: str, model_name: str) -> Dict` - Call Anthropic Claude API
- `async _call_openai_api(prompt: str, model_name: str) -> Dict` - Call OpenAI GPT API
- `async _call_google_api(prompt: str, model_name: str) -> Dict` - Call Google Gemini API
- `_build_security_prompt(data: Dict) -> str` - Build security-focused prompt

**Model Configuration (Based on Testing):**
- **Free Tier:** gemma-3-27b (Quality: 75, Speed: 95, Cost: $0.00)
- **Low Cost:** gemini-2.5-flash (Quality: 60, Speed: 40, Cost: $0.002) - UNRELIABLE
- **Mid Quality:** claude-sonnet-4 (Quality: 92, Speed: 85, Cost: $0.008) - **RECOMMENDED**
- **High Quality:** gemini-2.5-pro (Quality: 85, Speed: 70, Cost: $0.015) - Overpriced
- **Premium:** claude-opus-4 (Quality: 95, Speed: 60, Cost: $0.020)
- **Latest:** gpt-5 (Quality: 97, Speed: 75, Cost: $0.025)

### `engines/intelligence/core/model_registry.py`

**Purpose:** Hot-reloadable AI model configuration with YAML-based config

#### Classes

**`ModelConfig`** - Dataclass for model configuration
- `name: str` - Model identifier
- `provider: str` - Provider (google, anthropic, openai)
- `tier: str` - Tier classification
- `cost_per_1k_tokens: float` - Token cost
- `quality_score: int` - Quality rating
- `capabilities: List[str]` - Model capabilities
- `recommended_for: List[str]` - Recommended use cases

**`ModelRegistry`** - Manages AI model configurations with hot-reload

**Methods:**
- `reload_config() -> bool` - Hot-reload model configurations from YAML
- `get_model(model_id: str) -> Optional[ModelConfig]` - Get model by ID or alias
- `get_model_for_task(task_type, max_cost, min_quality) -> Optional[ModelConfig]` - Get best model for task
- `list_available_models(tier, provider, min_quality) -> List[ModelConfig]` - List models with filtering
- `get_model_stats() -> Dict[str, Any]` - Get registry statistics
- `validate_model_id(model_id: str) -> bool` - Check if model ID exists
- `_validate_config(config: Dict) -> bool` - Validate config structure
- `_model_meets_requirements(model, max_cost, min_quality, caps) -> bool` - Check requirements

---

## Backend Engine

### `engines/backend/backend_engine_clean.py`

**Purpose:** Ruleset management, CTI processing, storage optimization, correlation, and detection fidelity

#### Classes

**`BackendEngine(BaseEngine)`** - Backend Engine for system services

**Initialization:**
- Correlation Engine integration
- IOC filtering and validation
- Log source quality assessment
- Detection fidelity calculation
- Storage tier management

**Storage Tiers:**
- **Hot (Redis):** 24 hours, $0.50/GB
- **Warm (PostgreSQL):** 30 days, $0.10/GB
- **Cold (S3):** 365 days, $0.02/GB

**Methods:**

*Core Handlers:*
- `async _handle_store_processed_log(data)` - Store lightweight metadata, NOT full logs
- `async _handle_cti_update(data)` - Process CTI updates from Ingestion
- `async _handle_cti_indicators(data)` - Process individual CTI indicators
- `async _handle_rule_generation(data)` - Generate detection rules
- `async _handle_correlation_event(data)` - Process events for correlation
- `async _handle_correlation_check(data)` - Explicit correlation check

*Rule Generation:*
- `async _process_cti_for_rules(cti_data, source) -> List[Dict]` - Generate rules from CTI
- `async _create_ioc_rule(ioc, source) -> Optional[Dict]` - Create rule from IOC
- `async _create_ttp_rule(ttp, source) -> Optional[Dict]` - Create rule from TTP
- `_generate_sigma_rule(cti_input, tactics, techniques) -> str` - Generate Sigma rule
- `_generate_sigma_from_ioc(ioc, value) -> str` - Generate Sigma from IOC

*Storage & Quality:*
- `async _store_rule(rule) -> str` - Store detection rule with duplicate detection
- `_calculate_rule_quality_score(rule) -> float` - Calculate quality score (0-1)
- `async _validate_rule_effectiveness(rule) -> Dict` - Validate rule effectiveness
- `_is_meaningful_update(existing_rule, new_rule) -> bool` - Check for meaningful updates

*Background Tasks:*
- `async _storage_management_task()` - Manage storage tiers, cleanup (every 6 hours)
- `async _cti_update_task()` - Monitor CTI updates (every hour)
- `async _rule_performance_task()` - Monitor rule performance (every 4 hours)
- `async _cost_monitoring_task()` - Monitor costs (every hour)

#### HTTP API Endpoints

**Log Source Management:**
- `POST /api/log-sources/register` - Register log source with quality assessment
- `GET /api/log-sources/status` - Get all log sources status
- `DELETE /api/log-sources/{source_id}` - Remove log source

**Detection Fidelity:**
- `POST /api/detection/fidelity` - Calculate detection fidelity for attacks
- `GET /api/detection/coverage` - Get overall detection coverage
- `POST /api/detection/technique-coverage` - Calculate MITRE technique coverage

**Correlation Capability:**
- `GET /api/correlation/capability` - Assess correlation capability
- `POST /api/correlation/requirements` - Check correlation requirements
- `POST /api/correlation/recommendations` - Get improvement recommendations

**Coverage Analysis:**
- `GET /api/coverage/gaps` - Analyze coverage gaps
- `POST /api/coverage/simulate` - Simulate coverage improvements

#### Redis Channels (Subscribed)
- `backend.store_data` - Data storage requests
- `backend.store_processed_log` - Processed logs from ingestion
- `ingestion.cti.update` - CTI updates from Ingestion
- `ingestion.cti.indicators` - CTI indicators
- `backend.rule_generation` - Rule generation requests
- `intelligence.pattern_crystallized` - New crystallized patterns
- `ingestion.events.parsed` - Raw events for correlation
- `contextualization.events.enriched` - Enriched events for correlation

### `engines/backend/correlation_engine.py`

**Purpose:** Multi-source correlation for advanced threat detection

#### Enums

**`CorrelationType(Enum)`**
- `SIMPLE` - Single source, single condition
- `CORRELATION` - Multiple sources, related events
- `SEQUENCE` - Ordered sequence of events
- `AGGREGATION` - Statistical/threshold based
- `BEHAVIORAL` - Baseline deviation

**`LogSourceType(Enum)`**
- `FIREWALL`, `EDR`, `PROXY`, `DNS`, `AUTH`, `CLOUD`, `EMAIL`, `DLP`

#### Classes

**`CorrelationRule`** - Dataclass for correlation rules
- `rule_id, rule_name, rule_type, use_case, description`
- `required_sources: List[LogSourceType]`
- `conditions: List[Dict]` - Conditions per source
- `correlation_logic: Dict` - Correlation logic
- `time_window: int` - Seconds
- `mitre_techniques: List[str]`

**`CorrelationContext`** - Maintains correlation state
- `context_id, rule, entity_id, entity_type`
- `events_matched: List[Dict]`
- `sources_seen: Set[str]`
- `correlation_score: float`

**`CorrelationEngine`** - Main correlation engine

**Methods:**
- `async process_event(event: Dict) -> Optional[List[Dict]]` - Process event for correlations
- `async _get_or_create_context(rule, entity_id, event)` - Get/create context
- `async _update_context(context, event, source_type)` - Update context with event
- `async _check_correlation(context) -> bool` - Check if correlation triggered
- `async _trigger_rule(context) -> Dict` - Trigger rule and generate alert
- `_calculate_score(context) -> float` - Calculate correlation score
- `_evaluate_condition(event, condition) -> bool` - Evaluate single condition
- `async get_active_correlations() -> List[Dict]` - Get active correlations
- `async get_rule_statistics() -> Dict` - Get correlation statistics

**Pre-defined Rules:**
1. **Lateral Movement (CORR_001):** Auth + Firewall + EDR, 5-minute window
2. **Data Exfiltration (CORR_002):** Proxy + Firewall + EDR, 1-hour window
3. **Ransomware Chain (CORR_003):** EDR behavioral patterns, 10-minute window

---

## Ingestion Engine

### `engines/ingestion/ingestion_engine.py`

**Purpose:** Multi-source data ingestion with modular architecture

#### Classes

**`IngestionEngine(BaseEngine)`** - Modular Ingestion Engine

**Core Modules:**
- `ConfigManager` - Configuration management
- `DataSourceManager` - Active data source lifecycle
- `LogRouter` - Intelligent log routing
- `StatsMonitor` - Statistics monitoring
- `TaskCoordinator` - Background task management
- `PatternMatcher` - FREE pattern processing
- `GitHubPatternSync` - GitHub pattern synchronization
- `ParserHotReload` - Parser hot-reload support
- `APIDocumentationGenerator` - API doc generation
- `CTIManager` - CTI feed management

**Message Handlers:**
- `SourceMessageHandler` - Source management messages
- `GitHubMessageHandler` - GitHub sync messages
- `ParserMessageHandler` - Parser reload messages
- `APIDocMessageHandler` - API doc messages
- `StatsMessageHandler` - Statistics messages

**Methods:**

*Initialization:*
- `_initialize_modules()` - Initialize core modules
- `_setup_component_integration()` - Setup specialized components
- `_setup_message_handlers()` - Setup message handling
- `_register_message_handlers()` - Register handlers with channels

*Background Tasks:*
- `_register_background_tasks(task_factory)` - Register all tasks
  - Component initialization (runs once)
  - Source monitoring (every 30s)
  - Ingestion processing (every 10s)
  - Statistics reporting (every 60s)
  - GitHub sync (every 3600s)
  - API doc update (every 3600s)

*Parser Generation:*
- `async _generate_parser_endpoint(request)` - Generate parser from log samples
- `async _wait_for_response(channel, timeout) -> Dict` - Wait for Redis response
- `async _get_parser_endpoint(request)` - Get parser by ID
- `async _list_parsers_endpoint(request)` - List all parsers
- `async _delete_parser_endpoint(request)` - Delete parser

#### HTTP API Endpoints

**Source Management:**
- `GET /sources` - Get source information
- `GET /stats` - Get statistics
- `GET /tasks` - Get task status
- `POST /sources/start` - Start a source
- `POST /sources/stop` - Stop a source

**CTI Management:**
- `POST /cti/manual_update` - Trigger manual CTI update
- `GET /cti/status` - Get CTI status
- `GET /cti/connectors` - Get CTI connectors

**Parser Generation:**
- `POST /api/parsers/generate` - Generate parser from log samples
- `GET /api/parsers/{parser_id}` - Get parser by ID
- `GET /api/parsers` - List all parsers
- `DELETE /api/parsers/{parser_id}` - Delete parser

#### Redis Channels (Subscribed)
- `ingestion.start_source` - Start source
- `ingestion.stop_source` - Stop source
- `ingestion.sync_github` - Sync GitHub patterns
- `ingestion.reload_parsers` - Reload parsers
- `ingestion.generate_api_docs` - Generate API docs
- `ingestion.get_stats` - Get statistics

### `engines/ingestion/cti_manager.py`

**Purpose:** CTI feed management for all threat intelligence sources

#### Classes

**`CTIManager`** - Manages all CTI feed sources

**Connectors:**
- `OpenCTIConnector` - OpenCTI integration
- `OTXConnector` - AlienVault OTX integration
- `ThreatFoxConnector` - abuse.ch ThreatFox integration

**Methods:**
- `async start(auto_update=False)` - Start CTI ingestion (manual or auto)
- `async _update_loop(source_name, connector)` - Continuous update loop
- `async _fetch_and_publish(source_name, connector)` - Fetch and publish data
- `async _process_opencti(connector)` - Process OpenCTI indicators
- `async _process_otx(connector)` - Process OTX indicators
- `async _process_threatfox(connector)` - Process ThreatFox indicators
- `async _publish_cti_data(cti_data)` - Publish to Redis channels
- `async get_status() -> Dict` - Get CTI source status
- `async manual_update(source: str) -> bool` - Manual update trigger

**Update Intervals:**
- OpenCTI: 3600s (1 hour)
- OTX: 3600s (1 hour)
- ThreatFox: 7200s (2 hours)

**Published Channels:**
- `ingestion.cti.update` - General CTI updates
- `ingestion.cti.indicators` - Specific indicators for rule generation

---

## Contextualization Engine

### `engines/contextualization/contextualization_engine.py`

**Purpose:** Entity enrichment, relationship mapping, and context building

#### Classes

**`ContextualizationEngine(BaseEngine)`** - Contextualization Engine

**Components:**
- `EntityExtractor` - Extract entities from logs
- `EnrichmentService` - Entity enrichment services

**Enrichment Sources:**
- Threat Intelligence (priority: 1)
- Geolocation (priority: 2)
- Asset Inventory (priority: 3)
- User Directory (priority: 4)

**Methods:**

*Message Handlers:*
- `async _handle_process_log(data)` - **PRIMARY:** Extract entities and store intelligence
- `async _handle_enrich_log(data)` - Enrich log with entities
- `async _handle_enrich_entity(data)` - Direct entity enrichment
- `async _handle_find_relationships(data)` - Find entity relationships
- `async _handle_get_context(data)` - Get entity context
- `async _handle_validate_parser(data)` - Validate parser with log samples

*Entity Extraction:*
- `async _extract_entities(log) -> Dict[str, str]` - Extract entities from log
- `_extract_entities_from_log(log_data) -> List[Dict]` - Extract from log structure
- `_extract_entities_from_parsed_fields(fields, types) -> List[Dict]` - Extract from parsed fields
- `_get_nested_value(data, field_path) -> Any` - Get nested dict values

*Enrichment:*
- `async _enrich_entity(entity_type, entity_value, level) -> Dict` - Enrich entity
- `async _enrich_ip_address(ip, level) -> Dict` - Enrich IP with geo/threat intel
- `async _enrich_hostname(hostname, level) -> Dict` - Enrich hostname
- `async _enrich_user(user, level) -> Dict` - Enrich user
- `async _enrich_process(process, level) -> Dict` - Enrich process
- `async _enrich_file_hash(file_hash, level) -> Dict` - Enrich file hash

*Storage:*
- `_store_entity(entity_type, entity_value, source_log_id) -> Optional[UUID]` - Store entity
- `async _store_entity_in_database(entity_type, entity_value, enriched_data)` - Store enriched entity
- `_create_entity_relationships(entities, source_log_id)` - Create relationships
- `async _enrich_stored_entity(entity_id, entity_type, entity_value)` - Enrich stored entity

*Parser Validation:*
- `_parse_log_sample(log_sample, parser) -> Dict` - Parse log with parser
- `_create_test_relationships(entities) -> List[Dict]` - Create test relationships

*Background Tasks:*
- `async _enrichment_loop()` - Main enrichment processing (every 30s)
- `async _cache_maintenance_loop()` - Cache cleanup (every 5 minutes)
- `async _relationship_analysis_loop()` - Relationship analysis (every 2 minutes)

#### Redis Channels (Subscribed)
- `contextualization.process_log` - **NEW:** Process logs and extract entities
- `contextualization.enrich_log` - Enrich log
- `contextualization.enrich_entity` - Enrich entity
- `contextualization.find_relationships` - Find relationships
- `contextualization.get_context` - Get context
- `contextualization.validate_parser` - Validate parser

**Published Channels:**
- `backend.store_intelligence` - Send enriched intelligence (NOT full logs)
- `contextualization.entity_enriched` - Entity enrichment complete
- `contextualization.relationships_found` - Relationships discovered
- `contextualization.context_response` - Context response

---

## Delivery Engine

### `engines/delivery/delivery_engine.py`

**Purpose:** Case management, workflow orchestration, frontend services

#### Classes

**`DeliveryEngine(BaseEngine)`** - Delivery Engine with workflow orchestration

**Components:**
- `WorkflowOrchestrator` - Workflow execution engine
- `WORKFLOW_TEMPLATES` - Pre-defined workflow templates

**Case Templates:**
- `security_incident` - High priority, auto-assign
- `anomaly_detection` - Medium priority

**Methods:**

*Case Management:*
- `async _handle_create_case(data)` - Create case from template
- `async _handle_update_case(data)` - Update case fields
- `async _handle_close_case(data)` - Close case with resolution
- `async _handle_store_enriched_log(data)` - Store enriched log
- `async _should_create_case(enriched_log) -> bool` - Determine case creation
- `async _generate_case_title(enriched_log) -> str` - Generate case title
- `async _generate_case_description(enriched_log) -> str` - Generate description

*Alert Delivery:*
- `async _handle_send_alert(data)` - Send alert to channels
- `async _deliver_alert_to_channel(alert, channel)` - **REAL** delivery (writes files/logs)
  - Email alerts → alerts/email_alerts.jsonl
  - Slack alerts → alerts/slack_alerts.jsonl
  - SMS alerts → alerts/sms_alerts.jsonl
  - Dashboard events → alerts/dashboard_events.jsonl

*Workflow Orchestration:*
- `async _handle_start_workflow(data)` - Start workflow execution
- `async _handle_cancel_workflow(data)` - Cancel running workflow
- `async _handle_get_workflow_status(data)` - Get workflow status
- `async _handle_workflow_step(channel, data)` - Execute workflow step
- `async _recover_workflows()` - Recover incomplete workflows on startup

*Dashboard:*
- `async _handle_get_dashboard_data(data)` - Get dashboard data
- `async _generate_dashboard_data(user_id, dashboard_type) -> Dict` - Generate dashboard

*Background Tasks:*
- `async _workflow_orchestration_loop()` - Monitor active workflows (every 30s)
- `async _workflow_monitoring_loop()` - Monitor workflow health (every 60s)
- `async _case_management_loop()` - Check stale cases (every 5 minutes)
- `async _dashboard_update_loop()` - Update dashboard (every 30s)
- `async _session_management_loop()` - Cleanup sessions (every 10 minutes)

#### HTTP API Endpoints

**Case Management:**
- `POST /api/cases` - Create case
- `GET /api/cases` - List cases (filter by status, priority)
- `GET /api/cases/{case_id}` - Get case details
- `PUT /api/cases/{case_id}` - Update case
- `DELETE /api/cases/{case_id}` - Close case
- `POST /api/cases/{case_id}/evidence` - Add evidence

**Dashboard:**
- `GET /api/dashboard/overview` - Dashboard overview
- `GET /api/dashboard/cases` - Cases dashboard
- `GET /api/dashboard/stats` - Statistics dashboard

**Alerts:**
- `POST /api/alerts` - Send alert
- `GET /api/alerts/history` - Alert history

**Authentication:**
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/sessions` - List active sessions

**System:**
- `GET /api/system/status` - System status
- `GET /api/system/engines` - All engine status

**Workflow Orchestration:**
- `POST /api/workflows/start` - Start workflow
- `GET /api/workflows` - List workflows (filter by status)
- `GET /api/workflows/{workflow_id}` - Get workflow details
- `POST /api/workflows/{workflow_id}/cancel` - Cancel workflow
- `GET /api/workflows/templates` - List workflow templates
- `GET /api/workflows/statistics` - Workflow statistics

**Pattern Management:**
- `GET /api/patterns` - List patterns
- `GET /api/patterns/{pattern_id}` - Get pattern
- `POST /api/patterns/test` - Test pattern
- `GET /api/patterns/{pattern_id}/performance` - Pattern performance

**Rule Management:**
- `GET /api/rules` - List rules (from Redis: frontend:rules:all)
- `GET /api/rules/{rule_id}` - Get rule
- `POST /api/rules/test` - Test rule

**Entity Management:**
- `GET /api/entities` - List entities
- `GET /api/entities/{entity_id}` - Get entity
- `GET /api/entities/{entity_id}/enrichment` - Get enrichment
- `GET /api/entities/{entity_id}/relationships` - Get relationships
- `GET /api/entities/{entity_id}/timeline` - Get timeline
- `GET /api/entities/{entity_id}/risk` - Get risk score

#### Redis Channels (Subscribed)
- `delivery.create_case` - Create case
- `delivery.update_case` - Update case
- `delivery.close_case` - Close case
- `delivery.store_enriched_log` - Store enriched log
- `delivery.send_alert` - Send alert
- `delivery.get_dashboard_data` - Get dashboard data
- `delivery.user_login` - User login
- `delivery.user_logout` - User logout
- `delivery.start_workflow` - Start workflow
- `delivery.cancel_workflow` - Cancel workflow
- `delivery.get_workflow_status` - Get workflow status
- `delivery.workflow.*` - Workflow step execution

**Published Channels:**
- `delivery.case_created` - Case created
- `delivery.case_updated` - Case updated
- `delivery.case_closed` - Case closed
- `delivery.alert_delivered` - Alert delivered
- `delivery.dashboard_updated` - Dashboard updated
- `delivery.workflow_started` - Workflow started
- `delivery.workflow_cancelled` - Workflow cancelled

---

## Frontend Components

### React Components (TypeScript/TSX)

#### Core Layout

**`frontend/src/layouts/DashboardLayout.tsx`**
- Main dashboard layout with navigation

**`frontend/src/components/layout/AppShell.tsx`**
- Application shell container

**`frontend/src/components/layout/TopBar.tsx`**
- Top navigation bar

**`frontend/src/components/layout/SideBar.tsx`**
- Side navigation menu

#### Pages

**Investigation:**
- `EntityExplorer.tsx` - Entity exploration interface
- `NewInvestigation.tsx` - Create new investigation
- `RelationshipMapper.tsx` - Entity relationship visualization
- `AIGuide.tsx` - AI-powered investigation guide
- `TimelineAnalysis.tsx` - Timeline analysis view

**Engineering:**
- `PatternLibrary.tsx` - Pattern library management
- `GitHubSync.tsx` - GitHub pattern sync
- `RuleTesting.tsx` - Rule testing interface
- `CrystallizationQueue.tsx` - Pattern crystallization queue

**Analytics:**
- `PerformanceMetrics.tsx` - Performance metrics dashboard
- `CostAnalysis.tsx` - Cost analysis and tracking

**Admin:**
- `UserManagement.tsx` - User management
- `SystemSettings.tsx` - System configuration

**Main:**
- `Dashboard.tsx` - Main dashboard
- `AlertQueue.tsx` - Alert queue
- `ActiveCases.tsx` - Active cases view
- `MITREOverview.tsx` - MITRE ATT&CK overview

#### Widgets

**`frontend/src/widgets/`**
- `AlertQueue.tsx` - Alert queue widget
- `PatternLibrary.tsx` - Pattern library widget
- `EntityGraph.tsx` - Entity graph visualization
- `CTIFeeds.tsx` - CTI feed display
- `CaseTimeline.tsx` - Case timeline widget
- `EntityExplorer.tsx` - Entity explorer widget
- `RelationshipGraph.tsx` - Relationship graph
- `AIInvestigationGuide.tsx` - AI guide widget
- `MITREHeatmap.tsx` - MITRE heatmap
- `MetricsDashboard.tsx` - Metrics dashboard
- `CrystallizationQueue.tsx` - Crystallization queue
- `CostSavingsTracker.tsx` - Cost savings tracker
- `RuleTestRunner.tsx` - Rule test runner
- `UserManagement.tsx` - User management widget
- `SystemSettings.tsx` - System settings widget

#### Stores (State Management)

**`frontend/src/stores/`**
- `investigationStore.ts` - Investigation state
- `navigationStore.ts` - Navigation state
- `notificationStore.ts` - Notification state
- `authStore.ts` - Authentication state

#### API Client

**`frontend/src/api/client.ts`**
- Centralized API client for backend communication

---

## Key Redis Channels Summary

### Intelligence Engine
- `intelligence.parse_log_sample` - Parse log samples
- `intelligence.pattern_crystallized` - Pattern crystallized

### Backend Engine
- `backend.store_data` - Store data
- `backend.store_processed_log` - Store processed log
- `backend.rule_generation` - Generate rule
- `backend.cti_update` - CTI update
- `ingestion.cti.update` - CTI update from ingestion
- `ingestion.cti.indicators` - CTI indicators
- `correlation.*` - Correlation events

### Ingestion Engine
- `ingestion.start_source` - Start source
- `ingestion.stop_source` - Stop source
- `ingestion.sync_github` - Sync GitHub
- `ingestion.reload_parsers` - Reload parsers
- `ingestion.cti.update` - Publish CTI update
- `ingestion.cti.indicators` - Publish CTI indicators

### Contextualization Engine
- `contextualization.process_log` - Process log (PRIMARY)
- `contextualization.enrich_log` - Enrich log
- `contextualization.enrich_entity` - Enrich entity
- `contextualization.find_relationships` - Find relationships
- `contextualization.validate_parser` - Validate parser

### Delivery Engine
- `delivery.create_case` - Create case
- `delivery.send_alert` - Send alert
- `delivery.start_workflow` - Start workflow
- `delivery.workflow.*` - Workflow steps

---

## Database Schema Summary

### Core Tables

**`engine_coordination`** - Engine status and heartbeat
- `engine_id`, `status`, `last_heartbeat`, `performance_metrics`

**`pattern_library`** - Crystallized patterns
- `pattern_id`, `pattern_data`, `pattern_type`, `confidence_score`, `usage_count`

**`detection_rules`** - Generated detection rules
- `rule_id`, `rule_data`, `created_at`, `updated_at`

**`rule_test_cases`** - Test cases for rules
- `rule_id`, `test_case_data`, `created_at`

**`entities`** - Extracted entities
- `entity_id`, `entity_type`, `entity_value`, `properties`, `confidence`

**`relationships`** - Entity relationships
- `relationship_id`, `source_entity_id`, `target_entity_id`, `relationship_type`, `properties`

**`warm_storage`** - Mid-term storage
- `storage_id`, `data`, `created_at`, `expires_at`

**`cases`** - Case management
- `case_id`, `title`, `severity`, `status`, `description`, `created_at`

**`log_sources`** - Registered log sources
- `source_id`, `source_name`, `source_type`, `quality_tier`, `quality_score`

**`correlation_contexts`** - Active correlation contexts
- `context_id`, `rule_id`, `entity_id`, `entity_type`, `status`

---

## Important Constants & Configuration

### Model Tiers (AI)
- **FREE:** gemma-3-27b-it (Quality: 75, Best free option)
- **PRODUCTION:** claude-sonnet-4 (Quality: 92, **RECOMMENDED**)
- **PREMIUM:** claude-opus-4 (Quality: 95)

### Storage Costs
- **Hot (Redis):** $0.50/GB, 24 hours
- **Warm (PostgreSQL):** $0.10/GB, 30 days
- **Cold (S3):** $0.02/GB, 365 days

### Update Intervals
- Heartbeat: 30 seconds
- Stats reporting: 60 seconds
- CTI updates: 3600 seconds (1 hour)
- Storage cleanup: 21600 seconds (6 hours)

### Quality Tiers (Log Sources)
- **PLATINUM:** 90-100 (Premium EDR)
- **GOLD:** 70-89 (Good quality)
- **SILVER:** 50-69 (Adequate)
- **BRONZE:** 30-49 (Basic)
- **MINIMAL:** 0-29 (Poor quality)

---

## Critical Patterns & Fixes

### Async Pattern (CRITICAL FIX)
**Problem:** Synchronous Redis blocks event loop
**Solution:** Use async Redis with explicit yielding

```python
# BROKEN
pubsub.get_message(timeout=1)  # Blocks!

# FIXED
import redis.asyncio as redis_async
await pubsub.get_message(timeout=1.0)  # Yields!
```

### Task Coordination Pattern
**Problem:** `asyncio.gather()` waits for ALL tasks
**Solution:** Use `asyncio.wait()` with FIRST_COMPLETED

```python
# BROKEN
await asyncio.gather(task1, task2)  # Waits for both

# FIXED
done, pending = await asyncio.wait(
    [task1, task2],
    return_when=asyncio.FIRST_COMPLETED
)
```

### Lightweight Storage Pattern
**Problem:** Storing full logs = 447MB for 45K logs
**Solution:** Extract intelligence, store only that

```python
# BROKEN: Store everything
store_full_log(log_data)  # 9KB per log

# FIXED: Extract and store intelligence
entities = extract_entities(log)  # 30 bytes
relationships = create_relationships(entities)  # 50 bytes
store_intelligence(entities, relationships)  # 98.4% reduction
```

---

## Development Notes

### Critical Files
- `base_engine.py` - Foundation for all engines
- `ai_models.py` - AI model configuration
- `correlation_engine.py` - Multi-source correlation
- `cti_manager.py` - CTI feed management
- `delivery_engine.py` - Workflow orchestration

### Key Directories
- `engines/` - All 5 engine implementations
- `engines/intelligence/core/` - AI model infrastructure
- `engines/backend/` - Rules, correlation, storage
- `engines/ingestion/` - Data sources, CTI, parsing
- `frontend/src/` - React UI components

### Environment Variables
- `REDIS_HOST`, `REDIS_PORT` - Redis connection
- `POSTGRES_*` - PostgreSQL connection
- `OPENAI_API_KEY`, `ANTHROPIC_API_KEY`, `GEMINI_API_KEY` - AI providers
- `OTX_API_KEY`, `OPENCTI_URL`, `OPENCTI_TOKEN` - CTI sources

---

**End of Index**
*For full implementation details, refer to individual source files*
