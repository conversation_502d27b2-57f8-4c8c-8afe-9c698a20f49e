@echo off
REM Start SIEMLess v2.0 with Full Authentication
REM This script starts both the main stack and authentication services

echo ==========================================
echo SIEMLess v2.0 - Starting with Authentication
echo ==========================================

REM Check Docker
docker info >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Docker is not running
    echo Please start Docker Desktop first
    pause
    exit /b 1
)

REM Step 1: Start infrastructure
echo.
echo Starting infrastructure services...
cd engines
docker-compose up -d postgres redis
cd ..
timeout /t 10 /nobreak >nul

REM Step 2: Start authentication stack
echo.
echo Starting authentication services...
cd engines
docker-compose -f docker-compose.keycloak.yml up -d
cd ..

REM Wait for Keycloak
echo.
echo Waiting for Keycloak to be ready...
set count=0
:keycloak_wait
set /a count+=1
if %count% GTR 30 goto keycloak_timeout

curl -s http://localhost:8080/health/ready | findstr "UP" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Keycloak is ready!
    goto keycloak_ready
)
echo Waiting... %count%/30
timeout /t 2 /nobreak >nul
goto keycloak_wait

:keycloak_timeout
echo Warning: Keycloak may not be fully ready
:keycloak_ready

REM Step 3: Start application engines
echo.
echo Starting application engines...
cd engines
docker-compose up -d
cd ..

REM Step 4: Wait for services
echo.
echo Waiting for all services to be healthy...
timeout /t 10 /nobreak >nul

REM Check services
echo.
echo Service Health Status:
echo ----------------------

REM Check PostgreSQL
nc -zv localhost 5433 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] PostgreSQL is healthy
) else (
    echo [WARN] PostgreSQL is not responding
)

REM Check Redis
nc -zv localhost 6380 >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Redis is healthy
) else (
    echo [WARN] Redis is not responding
)

REM Check Keycloak
curl -s http://localhost:8080/health/ready | findstr "UP" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Keycloak is healthy
) else (
    echo [WARN] Keycloak is not responding
)

REM Check Delivery Engine
curl -s http://localhost:8005/health | findstr "ok\|healthy" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Delivery Engine is healthy
) else (
    echo [WARN] Delivery Engine is not responding
)

REM Display summary
echo.
echo ==========================================
echo All services have been started!
echo ==========================================
echo.
echo Access Points:
echo Keycloak Admin: http://localhost:8080/admin (admin/admin123)
echo SIEMLess API:   http://localhost:8005/api/
echo Protected API:  http://localhost/api/ (via Nginx)
echo.
echo Test Credentials:
echo   Admin:    username=admin password=admin123
echo   Analyst:  username=analyst1 password=analyst123
echo   API Key:  dev-admin-key
echo.
echo To test authentication: python test_auth.py
echo To view logs: cd engines ^& docker-compose logs -f
echo To stop all: cd engines ^& docker-compose down ^& docker-compose -f docker-compose.keycloak.yml down
echo.
pause