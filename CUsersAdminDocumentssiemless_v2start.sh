#!/bin/bash

# SIEMLess v2.0 - Startup Script
echo "==============================================="
echo "       SIEMLess v2.0 - Starting Platform       "
echo "==============================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "Error: Docker is not running!"
    echo "Please start Docker Desktop and try again."
    exit 1
fi

# Navigate to engines directory
cd engines || exit 1

# Stop any existing containers
echo "Stopping existing containers..."
docker-compose down

# Start services
echo "Starting all services..."
docker-compose up -d --build

# Wait for services
echo "Waiting for services to be ready..."
sleep 10

# Check service health
echo "Checking service status..."
docker-compose ps

echo ""
echo "==============================================="
echo "       SIEMLess v2.0 - Access Points          "
echo "==============================================="
echo "Frontend: http://localhost:3000"
echo "Intelligence API: http://localhost:8001/docs"
echo "Backend API: http://localhost:8002/docs"
echo "PostgreSQL: localhost:5433"
echo "Redis: localhost:6380"
echo "==============================================="
