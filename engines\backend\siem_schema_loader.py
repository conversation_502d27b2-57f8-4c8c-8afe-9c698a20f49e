"""
SIEM Schema Loader
Loads SIEM-specific field mappings, operators, and syntax rules
"""

import os
import yaml
from typing import Dict, Any, Optional
import logging

class SIEMSchemaLoader:
    """Load and manage SIEM schema definitions"""

    def __init__(self, schema_dir: str = None, logger: logging.Logger = None):
        self.logger = logger or logging.getLogger(__name__)
        self.schema_dir = schema_dir or os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'siem_definitions'
        )
        self.schemas = {}
        self.load_all_schemas()

    def load_all_schemas(self):
        """Load all SIEM schemas from the schema directory"""
        try:
            if not os.path.exists(self.schema_dir):
                self.logger.error(f"SIEM schema directory not found: {self.schema_dir}")
                return

            yaml_files = [f for f in os.listdir(self.schema_dir) if f.endswith('.yaml')]

            for yaml_file in yaml_files:
                siem_name = yaml_file.replace('.yaml', '')
                schema_path = os.path.join(self.schema_dir, yaml_file)

                try:
                    with open(schema_path, 'r') as f:
                        schema = yaml.safe_load(f)
                        self.schemas[siem_name] = schema
                        self.logger.info(f"Loaded SIEM schema: {siem_name}")
                except Exception as e:
                    self.logger.error(f"Failed to load {yaml_file}: {e}")

            self.logger.info(f"Loaded {len(self.schemas)} SIEM schemas")

        except Exception as e:
            self.logger.error(f"Error loading SIEM schemas: {e}")

    def get_schema(self, siem_name: str) -> Optional[Dict[str, Any]]:
        """Get schema for a specific SIEM"""
        schema = self.schemas.get(siem_name.lower())
        if not schema:
            self.logger.warning(f"Schema not found for SIEM: {siem_name}")
            # Return default/generic schema
            return self._get_default_schema()
        return schema

    def get_field_mapping(self, siem_name: str, generic_field: str) -> str:
        """Map a generic field name to SIEM-specific field"""
        schema = self.get_schema(siem_name)
        if not schema:
            return generic_field

        field_mappings = schema.get('field_mappings', {})
        return field_mappings.get(generic_field, generic_field)

    def get_all_field_mappings(self, siem_name: str) -> Dict[str, str]:
        """Get all field mappings for a SIEM"""
        schema = self.get_schema(siem_name)
        if not schema:
            return {}
        return schema.get('field_mappings', {})

    def map_fields_to_siem(self, generic_fields: Dict[str, Any], siem_name: str) -> Dict[str, Any]:
        """Map a dictionary of generic fields to SIEM-specific fields"""
        field_mappings = self.get_all_field_mappings(siem_name)

        mapped_fields = {}
        for generic_key, value in generic_fields.items():
            siem_key = field_mappings.get(generic_key, generic_key)
            mapped_fields[siem_key] = value

        return mapped_fields

    def get_operator_mapping(self, siem_name: str, generic_operator: str) -> str:
        """Map a generic operator to SIEM-specific operator"""
        schema = self.get_schema(siem_name)
        if not schema:
            return generic_operator

        operator_mappings = schema.get('operator_mappings', {})
        return operator_mappings.get(generic_operator, generic_operator)

    def get_syntax_rules(self, siem_name: str) -> Dict[str, Any]:
        """Get syntax rules for a SIEM"""
        schema = self.get_schema(siem_name)
        if not schema:
            return {}
        return schema.get('syntax', {})

    def get_available_siems(self) -> list:
        """Get list of available SIEM names"""
        return list(self.schemas.keys())

    def is_siem_supported(self, siem_name: str) -> bool:
        """Check if a SIEM is supported"""
        return siem_name.lower() in self.schemas

    def _get_default_schema(self) -> Dict[str, Any]:
        """Return a default/generic schema"""
        return {
            'platform': {
                'name': 'generic',
                'display_name': 'Generic SIEM',
                'query_language': 'generic',
                'description': 'Generic field mappings'
            },
            'field_mappings': {
                'source_ip': 'src_ip',
                'destination_ip': 'dst_ip',
                'username': 'user',
                'hostname': 'host',
                'process_name': 'process',
                'file_path': 'file',
                'event_action': 'action',
                'event_outcome': 'result'
            },
            'operator_mappings': {
                'equals': '=',
                'not_equals': '!=',
                'contains': 'LIKE',
                'regex': 'REGEX'
            },
            'syntax': {
                'logical_and': 'AND',
                'logical_or': 'OR',
                'logical_not': 'NOT'
            }
        }

    def get_platform_info(self, siem_name: str) -> Dict[str, Any]:
        """Get platform information for a SIEM"""
        schema = self.get_schema(siem_name)
        if not schema:
            return {}
        return schema.get('platform', {})

    def get_metadata(self, siem_name: str) -> Dict[str, Any]:
        """Get metadata for a SIEM (capabilities, features, etc.)"""
        schema = self.get_schema(siem_name)
        if not schema:
            return {}
        return schema.get('metadata', {})


# Example usage and testing
if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    loader = SIEMSchemaLoader()

    print(f"Available SIEMs: {loader.get_available_siems()}")
    print()

    # Test Elastic mapping
    print("=== Elastic ECS Mapping ===")
    generic_fields = {
        'source_ip': '***********',
        'destination_ip': '********',
        'username': 'admin',
        'process_name': 'cmd.exe'
    }
    elastic_fields = loader.map_fields_to_siem(generic_fields, 'elastic')
    print(f"Generic: {generic_fields}")
    print(f"Elastic: {elastic_fields}")
    print()

    # Test Splunk mapping
    print("=== Splunk CIM Mapping ===")
    splunk_fields = loader.map_fields_to_siem(generic_fields, 'splunk')
    print(f"Generic: {generic_fields}")
    print(f"Splunk: {splunk_fields}")
    print()

    # Test operator mapping
    print("=== Operator Mapping (Elastic) ===")
    print(f"equals: {loader.get_operator_mapping('elastic', 'equals')}")
    print(f"contains: {loader.get_operator_mapping('elastic', 'contains')}")
    print(f"regex: {loader.get_operator_mapping('elastic', 'regex')}")
