import React, { useState, useEffect } from 'react'
import { AgGridReact } from 'ag-grid-react'
import { ColDef, ICellRendererParams } from 'ag-grid-community'
import {
  Users, UserPlus, UserX, Shield, Key,
  Edit, Trash2, MoreVertical, Search
} from 'lucide-react'
import 'ag-grid-community/styles/ag-grid.css'
import 'ag-grid-community/styles/ag-theme-material.css'

interface User {
  id: string
  name: string
  email: string
  role: string
  department: string
  status: 'active' | 'inactive' | 'suspended'
  lastLogin: string
  createdAt: string
  mfaEnabled: boolean
  accessLevel: string
}

const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Senior Analyst',
    department: 'SOC',
    status: 'active',
    lastLogin: '2025-09-29T10:30:00Z',
    createdAt: '2024-01-15T00:00:00Z',
    mfaEnabled: true,
    accessLevel: 'admin'
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Security Analyst',
    department: 'SOC',
    status: 'active',
    lastLogin: '2025-09-29T09:15:00Z',
    createdAt: '2024-03-20T00:00:00Z',
    mfaEnabled: true,
    accessLevel: 'operator'
  },
  {
    id: '3',
    name: 'Mike Johnson',
    email: '<EMAIL>',
    role: 'Junior Analyst',
    department: 'SOC',
    status: 'inactive',
    lastLogin: '2025-09-20T14:00:00Z',
    createdAt: '2024-06-10T00:00:00Z',
    mfaEnabled: false,
    accessLevel: 'viewer'
  }
]

const StatusRenderer: React.FC<ICellRendererParams> = ({ value }) => {
  const statusConfig = {
    active: { bg: 'bg-green-100', text: 'text-green-800' },
    inactive: { bg: 'bg-gray-100', text: 'text-gray-800' },
    suspended: { bg: 'bg-red-100', text: 'text-red-800' }
  }

  const config = statusConfig[value as keyof typeof statusConfig]

  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
      {value}
    </span>
  )
}

const MFARenderer: React.FC<ICellRendererParams> = ({ value }) => {
  return (
    <div className="flex items-center gap-2">
      <Shield size={16} className={value ? 'text-green-600' : 'text-gray-400'} />
      <span className="text-sm">{value ? 'Enabled' : 'Disabled'}</span>
    </div>
  )
}

const ActionsRenderer: React.FC<ICellRendererParams> = ({ data }) => {
  const [showMenu, setShowMenu] = useState(false)

  return (
    <div className="relative">
      <button
        onClick={() => setShowMenu(!showMenu)}
        className="p-1 hover:bg-gray-100 rounded"
      >
        <MoreVertical size={16} />
      </button>

      {showMenu && (
        <div className="absolute right-0 mt-1 bg-white border rounded-lg shadow-lg z-10 w-48">
          <button className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-2 text-sm">
            <Edit size={14} /> Edit User
          </button>
          <button className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-2 text-sm">
            <Key size={14} /> Reset Password
          </button>
          <button className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-2 text-sm">
            <UserX size={14} /> Suspend User
          </button>
          <button className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-2 text-sm text-red-600">
            <Trash2 size={14} /> Delete User
          </button>
        </div>
      )}
    </div>
  )
}

export const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [showAddUser, setShowAddUser] = useState(false)
  const [loading, setLoading] = useState(true)

  // Fetch users from API
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await fetch('/api/auth/sessions')
        if (response.ok) {
          const data = await response.json()
          if (data.users && data.users.length > 0) {
            setUsers(data.users)
          } else {
            // Use mock data if no real users
            setUsers(mockUsers)
          }
        } else {
          setUsers(mockUsers)
        }
      } catch (error) {
        console.error('Failed to fetch users:', error)
        setUsers(mockUsers)
      } finally {
        setLoading(false)
      }
    }

    fetchUsers()
  }, [])

  const columnDefs: ColDef[] = [
    { field: 'name', headerName: 'Name', sortable: true, filter: true },
    { field: 'email', headerName: 'Email', sortable: true, filter: true },
    { field: 'role', headerName: 'Role', sortable: true, filter: true },
    { field: 'department', headerName: 'Department', sortable: true, filter: true },
    {
      field: 'status',
      headerName: 'Status',
      cellRenderer: StatusRenderer,
      sortable: true,
      filter: true
    },
    {
      field: 'mfaEnabled',
      headerName: 'MFA',
      cellRenderer: MFARenderer,
      width: 120
    },
    { field: 'accessLevel', headerName: 'Access Level', sortable: true },
    {
      field: 'lastLogin',
      headerName: 'Last Login',
      valueFormatter: (params) => {
        return new Date(params.value).toLocaleString()
      },
      sortable: true
    },
    {
      field: 'actions',
      headerName: 'Actions',
      cellRenderer: ActionsRenderer,
      width: 100,
      sortable: false,
      filter: false
    }
  ]

  const defaultColDef = {
    resizable: true,
    sortable: false,
    filter: false
  }

  return (
    <div className="p-6 bg-gray-50 h-full overflow-auto">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Users className="text-blue-600" />
              User Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage system users, roles, and permissions
            </p>
          </div>

          <button
            onClick={() => setShowAddUser(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
          >
            <UserPlus size={20} />
            Add User
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Users</p>
                <p className="text-2xl font-bold">{users.length}</p>
              </div>
              <Users className="text-blue-600" size={24} />
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Users</p>
                <p className="text-2xl font-bold">
                  {users.filter(u => u.status === 'active').length}
                </p>
              </div>
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <div className="w-3 h-3 bg-green-500 rounded-full" />
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">MFA Enabled</p>
                <p className="text-2xl font-bold">
                  {users.filter(u => u.mfaEnabled).length}
                </p>
              </div>
              <Shield className="text-green-600" size={24} />
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Admin Users</p>
                <p className="text-2xl font-bold">
                  {users.filter(u => u.accessLevel === 'admin').length}
                </p>
              </div>
              <Key className="text-purple-600" size={24} />
            </div>
          </div>
        </div>

        {/* Search Bar */}
        <div className="bg-white p-4 rounded-lg shadow-sm border mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search users by name, email, or role..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Users Grid */}
      <div className="bg-white rounded-lg shadow-sm border" style={{ height: '500px' }}>
        <div className="ag-theme-material h-full">
          <AgGridReact
            rowData={users}
            columnDefs={columnDefs}
            defaultColDef={defaultColDef}
            animateRows={true}
            rowSelection="multiple"
            suppressRowClickSelection={true}
            pagination={true}
            paginationPageSize={10}
          />
        </div>
      </div>

      {/* Add User Modal (Placeholder) */}
      {showAddUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">Add New User</h2>
            <p className="text-gray-600 mb-4">User creation form would go here</p>
            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowAddUser(false)}
                className="px-4 py-2 border rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowAddUser(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Create User
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default UserManagement