"""
Parser Engine - Deterministic Log Parsing for SIEMLess v2.0
Uses crystallized patterns from Librarian for free, fast parsing
"""
import asyncio
import json
import re
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from pathlib import Path

# Add v2 to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.base import BaseEngine


class ParserEngine(BaseEngine):
    """
    Parser Engine: Uses deterministic patterns for log parsing

    Philosophy:
    - Use crystallized patterns from Librarian (free)
    - Fall back to AI only for unknown patterns (expensive)
    - Learn new patterns and submit to Librarian
    """

    def __init__(self):
        super().__init__('parser', '2.0.0')

        # Pattern cache from Librarian
        self.pattern_cache = {}
        self.pattern_stats = {
            'hits': 0,
            'misses': 0,
            'ai_fallbacks': 0,
            'patterns_learned': 0
        }

        # Compiled regex cache for performance
        self.compiled_patterns = {}

        # Unknown log buffer for batch AI processing
        self.unknown_buffer = []
        self.buffer_threshold = 10  # Process unknowns in batches

        # Load initial patterns (will be done when event loop starts)
        self.patterns_loaded = False

    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process incoming log for parsing
        """
        # Ensure patterns are loaded
        if not self.patterns_loaded:
            await self._load_patterns()
            self.patterns_loaded = True

        log_data = message.get('data', {})
        log_content = log_data.get('raw_log', '')
        log_source = log_data.get('source', 'unknown')

        self.logger.start_operation(f"parse_log_{message.get('id', 'unknown')}")

        try:
            # Try deterministic parsing first (FREE)
            parsed = self._parse_deterministic(log_content, log_source)

            if parsed:
                self.pattern_stats['hits'] += 1

                # Log successful parsing
                self.logger.log_decision(
                    'log_parsed_deterministic',
                    {'raw': log_content, 'source': log_source},
                    parsed,
                    reasoning='Parsed using crystallized pattern',
                    confidence=parsed.get('confidence', 1.0)
                )

                return {
                    'success': True,
                    'parsed': parsed,
                    'method': 'deterministic',
                    'pattern_id': parsed.get('pattern_id'),
                    'next_engine': 'entity_extractor',
                    'data': parsed
                }
            else:
                # Unknown pattern - add to buffer for AI processing
                self.pattern_stats['misses'] += 1

                self.unknown_buffer.append({
                    'log': log_content,
                    'source': log_source,
                    'message_id': message.get('id'),
                    'timestamp': datetime.utcnow().isoformat()
                })

                # Process buffer if threshold reached
                if len(self.unknown_buffer) >= self.buffer_threshold:
                    await self._process_unknown_buffer()

                return {
                    'success': False,
                    'message': 'Pattern not recognized, queued for AI processing',
                    'method': 'buffered',
                    'buffer_size': len(self.unknown_buffer)
                }

        except Exception as e:
            self.logger.log_error(e, {'log': log_content})
            return {
                'success': False,
                'error': str(e)
            }
        finally:
            self.logger.end_operation(f"parse_log_{message.get('id', 'unknown')}")

    def _parse_deterministic(self, log_content: str, log_source: str) -> Optional[Dict[str, Any]]:
        """
        Parse log using deterministic patterns
        """
        # Identify log type
        log_type = self._identify_log_type(log_content, log_source)

        if not log_type:
            return None

        # Get patterns for this log type
        patterns = self.pattern_cache.get(log_type, [])

        for pattern_data in patterns:
            pattern_id = pattern_data.get('pattern_id')

            # Get compiled regex
            if pattern_id not in self.compiled_patterns:
                regex_str = pattern_data.get('pattern', {}).get('regex')
                if regex_str:
                    try:
                        self.compiled_patterns[pattern_id] = re.compile(regex_str)
                    except Exception as e:
                        self.logger.log_error(e, {'pattern_id': pattern_id, 'regex': regex_str})
                        continue

            regex = self.compiled_patterns.get(pattern_id)
            if not regex:
                continue

            # Try to match
            match = regex.search(log_content)
            if match:
                # Extract fields
                extracted = match.groupdict()

                # Apply field mappings
                field_mappings = pattern_data.get('pattern', {}).get('fields', {})
                parsed_fields = {}

                for field_name, field_value in extracted.items():
                    if field_value is not None:
                        field_info = field_mappings.get(field_name, {})

                        # Type conversion
                        field_type = field_info.get('type', 'string')
                        converted_value = self._convert_field_type(field_value, field_type)

                        # Apply mapping if exists
                        if 'mapping' in field_info and converted_value in field_info['mapping']:
                            converted_value = field_info['mapping'][converted_value]

                        parsed_fields[field_name] = converted_value

                # Build parsed result
                parsed = {
                    'pattern_id': pattern_id,
                    'pattern_name': pattern_data.get('pattern', {}).get('name'),
                    'log_type': log_type,
                    'fields': parsed_fields,
                    'confidence': pattern_data.get('confidence', 1.0),
                    'timestamp': datetime.utcnow().isoformat(),
                    'original_log': log_content
                }

                # Add entity mappings if present
                entity_mappings = pattern_data.get('pattern', {}).get('entity_mappings', {})
                if entity_mappings:
                    parsed['entity_mappings'] = entity_mappings

                # Add relationship mappings if present
                relationship_mappings = pattern_data.get('pattern', {}).get('relationship_mappings', [])
                if relationship_mappings:
                    parsed['relationship_mappings'] = relationship_mappings

                # Add threat indicators if present
                threat_indicators = pattern_data.get('pattern', {}).get('threat_indicators', {})
                if threat_indicators:
                    parsed['threat_indicators'] = threat_indicators

                return parsed

        return None

    def _identify_log_type(self, log_content: str, log_source: str) -> Optional[str]:
        """
        Identify the type of log for pattern selection
        """
        # Check source hint first
        source_mappings = {
            'cisco_asa': 'cisco_asa',
            'cisco': 'cisco_asa',
            'asa': 'cisco_asa',
            'palo_alto': 'palo_alto',
            'panos': 'palo_alto',
            'fortinet': 'fortinet',
            'fortigate': 'fortinet',
            'checkpoint': 'checkpoint'
        }

        if log_source.lower() in source_mappings:
            return source_mappings[log_source.lower()]

        # Try pattern-based identification
        if '%ASA-' in log_content:
            return 'cisco_asa'
        elif 'PANOS' in log_content or 'PAN-OS' in log_content:
            return 'palo_alto'
        elif 'fortigate' in log_content.lower():
            return 'fortinet'
        elif 'CheckPoint' in log_content or 'fw1' in log_content:
            return 'checkpoint'

        # Check for common syslog patterns
        if re.match(r'<\d+>', log_content):
            return 'syslog'

        return None

    def _convert_field_type(self, value: str, field_type: str) -> Any:
        """
        Convert extracted field to appropriate type
        """
        try:
            if field_type == 'integer':
                return int(value)
            elif field_type == 'float':
                return float(value)
            elif field_type == 'boolean':
                return value.lower() in ['true', '1', 'yes', 'on']
            elif field_type == 'ip':
                # Basic IP validation
                parts = value.split('.')
                if len(parts) == 4 and all(0 <= int(p) <= 255 for p in parts):
                    return value
                return None
            elif field_type == 'duration':
                # Parse duration formats like "0:00:15" or "15s"
                return value  # Keep as string for now
            else:
                return value
        except Exception:
            return value  # Return original if conversion fails

    async def _process_unknown_buffer(self):
        """
        Process buffer of unknown logs using AI
        """
        if not self.unknown_buffer:
            return

        self.logger.log('processing_unknown_buffer', {
            'buffer_size': len(self.unknown_buffer)
        })

        # Send to AI consensus engine for pattern discovery
        batch_message = {
            'type': 'pattern_discovery',
            'logs': self.unknown_buffer,
            'source_engine': 'parser',
            'request_type': 'batch_parsing'
        }

        await self.message_queue.send_message('ai_consensus', batch_message)

        self.pattern_stats['ai_fallbacks'] += len(self.unknown_buffer)

        # Clear buffer
        self.unknown_buffer = []

    async def _load_patterns(self):
        """
        Load patterns from Librarian
        """
        self.logger.log('loading_patterns', {'source': 'librarian'})

        # Request patterns for each category
        categories = ['cisco_asa', 'palo_alto', 'fortinet', 'checkpoint', 'syslog']

        for category in categories:
            request = {
                'type': 'pattern_request',
                'category': category,
                'source': 'parser'
            }

            # Send request to Librarian
            success = await self.message_queue.send_message('librarian', request)

            if success:
                self.logger.log('pattern_request_sent', {
                    'category': category
                }, 'DEBUG')

        # Also load from local files for bootstrap
        await self._load_local_patterns()

    async def _load_local_patterns(self):
        """
        Load patterns from local files (bootstrap)
        """
        pattern_dir = Path(__file__).parent.parent.parent / 'patterns'

        if pattern_dir.exists():
            for pattern_file in pattern_dir.glob('*.json'):
                try:
                    with open(pattern_file, 'r') as f:
                        patterns = json.load(f)

                    # Determine category from filename
                    category = pattern_file.stem

                    # Store patterns
                    if category not in self.pattern_cache:
                        self.pattern_cache[category] = []

                    if isinstance(patterns, dict):
                        for pattern_id, pattern_data in patterns.items():
                            pattern_data['pattern_id'] = pattern_id
                            self.pattern_cache[category].append(pattern_data)

                    self.logger.log('local_patterns_loaded', {
                        'file': pattern_file.name,
                        'category': category,
                        'count': len(patterns) if isinstance(patterns, dict) else 0
                    })

                except Exception as e:
                    self.logger.log_error(e, {'file': str(pattern_file)})

    async def _handle_pattern_update(self, message: Dict[str, Any]):
        """
        Handle pattern update from Librarian
        """
        pattern_data = message.get('pattern')
        category = pattern_data.get('category')

        if category and pattern_data:
            if category not in self.pattern_cache:
                self.pattern_cache[category] = []

            # Check if pattern already exists
            pattern_id = pattern_data.get('pattern_id')
            existing = False

            for i, p in enumerate(self.pattern_cache[category]):
                if p.get('pattern_id') == pattern_id:
                    # Update existing pattern
                    self.pattern_cache[category][i] = pattern_data
                    existing = True
                    break

            if not existing:
                # Add new pattern
                self.pattern_cache[category].append(pattern_data)

            self.logger.log('pattern_cache_updated', {
                'category': category,
                'pattern_id': pattern_id,
                'action': 'updated' if existing else 'added'
            })

            # Clear compiled regex for this pattern
            if pattern_id in self.compiled_patterns:
                del self.compiled_patterns[pattern_id]

    def get_capabilities(self) -> Dict[str, Any]:
        """
        Return Parser Engine capabilities
        """
        return {
            'engine': 'parser',
            'version': self.version,
            'capabilities': [
                'deterministic_parsing',
                'pattern_recognition',
                'field_extraction',
                'type_conversion',
                'threat_detection',
                'ai_fallback'
            ],
            'supported_formats': list(self.pattern_cache.keys()),
            'statistics': self.pattern_stats,
            'cache_size': sum(len(patterns) for patterns in self.pattern_cache.values()),
            'buffer_size': len(self.unknown_buffer)
        }

    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """
        Validate Parser configuration
        """
        required_fields = ['buffer_threshold']

        for field in required_fields:
            if field not in config:
                return False

        # Validate threshold
        if not isinstance(config['buffer_threshold'], int) or config['buffer_threshold'] < 1:
            return False

        return True


async def main():
    """
    Main entry point for Parser Engine
    """
    engine = ParserEngine()

    # Log capabilities
    capabilities = engine.get_capabilities()
    engine.logger.log('engine_capabilities', capabilities)

    # Start processing
    await engine.start()


if __name__ == '__main__':
    asyncio.run(main())