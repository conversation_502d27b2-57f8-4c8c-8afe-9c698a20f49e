# CrowdStrike Falcon SIEM Configuration
# Query Language: FQL (Falcon Query Language)

platform:
  name: crowdstrike
  display_name: CrowdStrike Falcon
  query_language: fql
  description: CrowdStrike Falcon Query Language for endpoint detection
  vendor: CrowdStrike Inc.
  version: "1.0"
  active: true

# Field mappings: generic_field -> CrowdStrike field
field_mappings:
  source_ip: LocalAddressIP4
  destination_ip: RemoteAddressIP4
  username: UserName
  process_name: ImageFileName
  file_hash: SHA256HashData
  event_id: EventType
  hostname: ComputerName
  port: RemotePort
  source_port: LocalPort
  destination_port: RemotePort
  domain: DomainName
  url: ConnectionUrl
  file_name: TargetFileName
  file_path: TargetFilePath
  registry_path: RegObjectName
  command_line: CommandLine
  parent_process: ParentBaseFileName
  network_protocol: Protocol
  http_method: HttpMethod
  user_agent: UserAgent
  email_sender: EmailSender
  email_recipient: EmailRecipient
  dns_query: DomainName
  service_name: ServiceName
  account_name: UserSid
  process_id: ContextProcessId
  parent_process_id: ParentProcessId
  md5_hash: MD5HashData
  sha1_hash: SHA1HashData
  device_id: aid
  customer_id: cid
  detection_id: detection_id
  severity: Severity
  tactic: Tactic
  technique: Technique

# Operator mappings: generic_operator -> CrowdStrike operator
operator_mappings:
  equals: ":"
  not_equals: "-"
  contains: ":*"
  not_contains: "-*"
  regex: "~"
  greater_than: ">"
  less_than: "<"
  greater_equal: ">="
  less_equal: "<="
  in_list: IN
  exists: "*"

# Time field for temporal queries
time_field: ProcessStartTime

# Query syntax specifics
syntax:
  comment: "#"
  string_quote: "\""
  escape_char: "\\"
  wildcard: "*"
  field_separator: ":"
  logical_and: "+"
  logical_or: OR
  logical_not: "-"
  phrase_search: "\"\""
  fuzzy_search: "~"
  proximity_search: "~N"
  range_search: "[min TO max]"
  case_sensitive: false

# Platform-specific features
metadata:
  supports_regex: true
  supports_wildcards: true
  supports_real_time_response: true
  supports_threat_hunting: true
  supports_ioa: true  # Indicators of Attack
  max_search_results: 10000
  default_time_range: "24h"

  # Event types
  event_types:
    - ProcessRollup2
    - SyntheticProcessRollup2
    - NetworkConnectIP4
    - NetworkConnectIP6
    - DnsRequest
    - FileWritten
    - RegKeySecurityDecrease
    - UserLogon
    - UserLogoff
    - RemovableMediaVolumeMounted
    - RawDiskRead
    - RawDiskWrite
    - ExecutableDeleted
    - LoadLibraryFailed
    - SuspiciousRawDiskRead
    - ImageHash
    - ServiceStarted
    - ServiceStopped

  # Detection categories
  detection_categories:
    - Malware
    - Exploit
    - Command and Control
    - Lateral Movement
    - Privilege Escalation
    - Credential Access
    - Discovery
    - Execution
    - Persistence
    - Defense Evasion
    - Collection
    - Exfiltration
    - Impact

  # Query template
  rule_template: |
    event_simpleName:{event_type}
    {conditions}
    | groupby aid
    | stats count

  # Custom IOA rule template
  ioa_template: |
    event_simpleName={event_type}
    {field}={value}
    severity>=Medium
    tactic IN (Execution, Persistence)

  # Supported platforms
  platforms:
    - Windows
    - Linux
    - Mac
    - Mobile

  # Real-Time Response commands
  rtr_commands:
    - cat  # Read file
    - cd  # Change directory
    - clear  # Clear screen
    - cp  # Copy file
    - encrypt  # Encrypt file
    - env  # Show environment
    - eventlog  # Event log operations
    - filehash  # Calculate hash
    - get  # Download file
    - getsid  # Get user SID
    - help  # Show help
    - history  # Command history
    - ipconfig  # Network config
    - kill  # Terminate process
    - ls  # List directory
    - map  # Network drive
    - memdump  # Memory dump
    - mount  # Mount share
    - netstat  # Network stats
    - ps  # List processes
    - put  # Upload file
    - reg  # Registry operations
    - restart  # Restart system
    - rm  # Remove file
    - runscript  # Execute script
    - shutdown  # Shutdown system
    - unmap  # Unmap drive
    - xmemdump  # Extended memory dump
    - zip  # Compress files

  # Threat intelligence integration
  supports_threat_graph: true
  supports_adversary_tracking: true
  supports_sandbox_analysis: true

  # Machine learning detections
  ml_detection_types:
    - Malware  # File-based ML
    - Machine Learning  # Behavioral ML
    - Sensor ML  # On-sensor ML

  # Prevention policies
  prevention_types:
    - Next-Gen Antivirus
    - Indicator of Attack
    - Exploit Mitigation
    - Sensor Tampering Protection
    - Suspicious Processes
    - Unknown Executables
    - Script-based Execution

  # MITRE ATT&CK coverage
  supports_mitre: true
  mitre_tactics:
    - Initial Access
    - Execution
    - Persistence
    - Privilege Escalation
    - Defense Evasion
    - Credential Access
    - Discovery
    - Lateral Movement
    - Collection
    - Command and Control
    - Exfiltration
    - Impact
