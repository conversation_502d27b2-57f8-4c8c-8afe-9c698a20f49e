# SIEMLess v2.0 - Frontend Widgets Specification

## Overview
This document defines the comprehensive widget-based dashboard architecture for SIEMLess v2.0, designed to provide role-based views for different user personas using Storybook as the component development platform.

**Implementation Timeline**: After authentication layer completion

---

## 🏛️ 1. Situational Awareness Widgets
*High-level views for understanding the current state*

### LiveAlertQueue
- **User Personas**: Analyst, Leader
- **Purpose**: Displays a real-time, filterable list of all incoming cases and alerts. The primary starting point for any investigation.
- **Backend Data Sources**:
  - Case Management System (`/api/cases`)
  - Alert Delivery System (`/api/alerts/history`)
- **Primary Tech**: AG Grid
- **Key Features**:
  - Real-time WebSocket updates
  - Advanced filtering (severity, source, time range)
  - Sortable columns
  - Quick actions (assign, dismiss, escalate)
  - Bulk operations support

### MITREHeatmap
- **User Personas**: Leader, Analyst
- **Purpose**: Visualizes observed adversary tactics and techniques, highlighting the most active threats in the environment.
- **Backend Data Sources**:
  - MITRE ATT&CK Mapping (Contextualization Engine)
  - Pattern Library statistics
- **Primary Tech**: Custom React
- **Key Features**:
  - Interactive heatmap with drill-down capability
  - Time range selection
  - Tactic/technique frequency visualization
  - Click-through to related cases

### SystemHealth
- **User Personas**: All
- **Purpose**: Provides at-a-glance status (green/red) of the five core engines and key integrations.
- **Backend Data Sources**:
  - Health check endpoints (`/health` on ports 8001-8005)
  - Engine coordination table
- **Primary Tech**: Custom React
- **Key Features**:
  - Real-time engine status
  - Performance metrics (latency, throughput)
  - Error rate monitoring
  - Integration status (Redis, PostgreSQL, CTI feeds)

### ActiveInvestigations
- **User Personas**: Analyst, Leader
- **Purpose**: A simple list of cases currently assigned and being actively worked on, showing the assignee and time since last update.
- **Backend Data Sources**:
  - Case Management System (`/api/cases`)
  - User sessions
- **Primary Tech**: Custom React
- **Key Features**:
  - Live update counter
  - Assignee avatars
  - Priority indicators
  - Quick case preview on hover

---

## 🔎 2. Investigation & Triage Widgets
*The core components for an analyst's investigation workbench*

### EntityExplorer
- **User Personas**: Analyst
- **Purpose**: The cornerstone of the investigation. Displays a selected entity (IP, user, hash) and all of its enriched context, relationships, and risk score.
- **Backend Data Sources**:
  - Entity Extraction (Contextualization Engine)
  - Enrichment Service
  - Relationship Mapper
- **Primary Tech**: Custom React
- **Key Features**:
  - Entity type detection
  - Enrichment data display (GeoIP, threat intel, asset info)
  - Risk score visualization
  - Related entities navigation
  - Historical behavior baseline

### RelationshipGraph
- **User Personas**: Analyst
- **Purpose**: An interactive graph visualization showing the connections between all entities involved in a case.
- **Backend Data Sources**:
  - Relationship Mapper (Contextualization Engine)
  - Entity database
- **Primary Tech**: Custom React + Graph Library (e.g., Visx, D3)
- **Key Features**:
  - Force-directed graph layout
  - Entity clustering
  - Zoom and pan controls
  - Edge filtering by relationship type
  - Export to image/JSON

### CaseTimeline
- **User Personas**: Analyst, Leader
- **Purpose**: Chronologically displays every event and action associated with a case, from initial alert to closure.
- **Backend Data Sources**:
  - Case Management System
  - Workflow history
  - Audit logs
- **Primary Tech**: Custom React
- **Key Features**:
  - Chronological event display
  - Event type filtering
  - User action tracking
  - Evidence attachments
  - Timeline export

### LogViewer
- **User Personas**: Analyst, Engineer
- **Purpose**: Shows the raw and parsed log data for any piece of evidence, with syntax highlighting and search capabilities.
- **Backend Data Sources**:
  - Ingestion Logs
  - Pattern Library (for parsing)
- **Primary Tech**: Custom React
- **Key Features**:
  - Syntax highlighting
  - Pattern match highlighting
  - Search within logs
  - Copy to clipboard
  - Download raw/parsed versions

### AIInvestigationGuide
- **User Personas**: Analyst
- **Purpose**: Displays the step-by-step investigation procedure generated by the AI for the specific alert type.
- **Backend Data Sources**:
  - Investigation Guide Generator (Intelligence Engine)
- **Primary Tech**: Custom React
- **Key Features**:
  - Step-by-step checklist
  - Query templates for each SIEM
  - Evidence collection guidance
  - Escalation criteria
  - Progress tracking

### ActionToolbar
- **User Personas**: Analyst
- **Purpose**: A set of buttons to trigger critical response workflows (Isolate Host, Collect Forensics, etc.) for the active case.
- **Backend Data Sources**:
  - Workflow Orchestration (`/api/workflow/start`)
  - Workflow templates
- **Primary Tech**: Custom React
- **Key Features**:
  - One-click workflow triggers
  - Workflow status monitoring
  - Rollback capability
  - Action history
  - Custom workflow builder

---

## 🛠️ 3. Detection & Content Engineering Widgets
*Tools for engineers to build and manage detection logic*

### PatternPerformance
- **User Personas**: Engineer, Leader
- **Purpose**: Displays the performance metrics (hit rate, false positive rate, processing time) for a selected detection pattern.
- **Backend Data Sources**:
  - Pattern Performance Tracker (Backend Engine)
  - Pattern usage statistics
- **Primary Tech**: Grafana
- **Key Features**:
  - Hit rate trends
  - False positive tracking
  - Processing time analysis
  - Cost savings calculation
  - A/B testing results

### CrystallizationQueue
- **User Personas**: Engineer
- **Purpose**: The engineer's inbox to review, edit, and approve new patterns suggested by the AI for unknown logs.
- **Backend Data Sources**:
  - Unknown Log Collector (Ingestion Engine)
  - Multi-AI Consensus System (Intelligence Engine)
- **Primary Tech**: Custom React + AG Grid
- **Key Features**:
  - Pattern review interface
  - Consensus score display
  - Pattern editor
  - Test against sample logs
  - Approve/reject actions

### RuleTestRunner
- **User Personas**: Engineer
- **Purpose**: An interface to test a pattern or rule against synthetic logs generated by the backend.
- **Backend Data Sources**:
  - Test Case Generator (Backend Engine)
  - Rule test results
- **Primary Tech**: Custom React
- **Key Features**:
  - Test log generation
  - Rule execution
  - Result visualization
  - Performance benchmarking
  - Export test reports

### GitHubSyncStatus
- **User Personas**: Engineer, Leader
- **Purpose**: Monitors the status of pattern synchronization from GitHub repositories, showing last sync time and any errors.
- **Backend Data Sources**:
  - GitHub Pattern Synchronization (Ingestion Engine)
- **Primary Tech**: Custom React
- **Key Features**:
  - Repository status
  - Sync history
  - Error notifications
  - Pattern validation results
  - Manual sync trigger

---

## 🛡️ 4. Log Source Quality Widgets (NEW - September 2025)
*Visualize and manage log source quality and detection capabilities*

### DetectionFidelityDashboard
- **User Personas**: Leader, Engineer
- **Purpose**: Shows overall detection confidence for each attack type based on current log sources
- **Backend Data Sources**:
  - `/api/detection/fidelity`
  - `/api/detection/coverage`
- **Primary Tech**: Victory Charts
- **Key Features**:
  - Bar chart of detection confidence per attack type
  - Color coding (red <40%, yellow 40-70%, green >70%)
  - Drill-down to see which sources contribute
  - Export capability for reports

### LogSourceInventory
- **User Personas**: Engineer, Analyst
- **Purpose**: Comprehensive view of all registered log sources with quality tiers
- **Backend Data Sources**:
  - `/api/log-sources/status`
  - `/api/log-sources/metrics`
- **Primary Tech**: AG Grid
- **Key Features**:
  - Quality tier badges (PLATINUM/GOLD/SILVER/BRONZE)
  - Event volume metrics
  - Latency and availability indicators
  - Quick actions (disable, configure, test)
  - Bulk tier updates

### CoverageGapAnalysis
- **User Personas**: Leader, Engineer
- **Purpose**: Identifies and prioritizes detection gaps with recommendations
- **Backend Data Sources**:
  - `/api/coverage/gaps`
  - `/api/correlation/recommendations`
- **Primary Tech**: Custom React
- **Key Features**:
  - Visual gap severity (critical/high/medium)
  - Recommended sources to close gaps
  - Cost/benefit analysis per recommendation
  - "What-if" simulation mode
  - Export gap analysis report

### MITRETechniqueCoverage
- **User Personas**: Leader, Analyst
- **Purpose**: Heatmap showing MITRE ATT&CK technique coverage based on log sources
- **Backend Data Sources**:
  - `/api/detection/technique-coverage`
  - MITRE ATT&CK framework data
- **Primary Tech**: Custom React Heatmap
- **Key Features**:
  - Color intensity based on detection confidence
  - Technique details on hover
  - Filter by tactic
  - Compare coverage over time
  - Export coverage matrix

---

## 📈 5. Performance & ROI Widgets
*Components for leaders to measure value and efficiency*

### CapacityAmplifier
- **User Personas**: Leader
- **Purpose**: Quantifies operational leverage by translating automated work into "Virtual Analyst FTEs."
- **Backend Data Sources**:
  - Case Management System
  - Workflow Orchestration
  - Time tracking metrics
- **Primary Tech**: Custom React + Grafana
- **Key Features**:
  - FTE equivalent calculations
  - Automation metrics
  - Time saved visualization
  - Workflow efficiency tracking
  - Monthly/quarterly trends

### RiskVelocity
- **User Personas**: Leader
- **Purpose**: Measures the speed of the threat response lifecycle ("Time to Protection").
- **Backend Data Sources**:
  - CTI-to-Rule Automation (Backend Engine)
  - Case Management timelines
- **Primary Tech**: Grafana
- **Key Features**:
  - Mean time to detect (MTTD)
  - Mean time to respond (MTTR)
  - Rule deployment velocity
  - Protection coverage metrics
  - Industry benchmarking

### TCOForecast
- **User Personas**: Leader
- **Purpose**: Provides a financial breakdown of data storage and compute costs, enabling budget forecasting.
- **Backend Data Sources**:
  - Storage Tiering System (Backend Engine)
  - AI Model Performance metrics
  - Cost tracking database
- **Primary Tech**: Custom React + Grafana
- **Key Features**:
  - Storage cost breakdown
  - AI usage costs
  - Pattern vs AI cost comparison
  - Projected savings
  - Budget forecasting

---

## Technical Implementation Strategy

### Technology Stack
```javascript
{
  "framework": "React 18+",
  "component_library": "Storybook 7+",
  "state_management": "Redux Toolkit / Zustand",
  "data_grid": "AG Grid Enterprise",
  "charts": "Recharts / Visx",
  "graphs": "D3.js / Cytoscape",
  "websocket": "Socket.io-client",
  "styling": "Tailwind CSS / Styled Components",
  "testing": "Jest + React Testing Library"
}
```

### Widget Base Architecture
```javascript
// Base widget interface
interface BaseWidget {
  id: string;
  title: string;
  persona: UserPersona[];
  refreshInterval?: number;
  permissions: Permission[];
  dataSource: DataSource[];
  actions?: WidgetAction[];
  exportable: boolean;
  resizable: boolean;
}

// Widget container component
const WidgetContainer = ({ widget, data, onAction }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [liveData, setLiveData] = useState(data);

  useWebSocket(widget.dataSource, setLiveData);
  useAutoRefresh(widget.refreshInterval, fetchData);

  return (
    <Card className="widget-container">
      <WidgetHeader title={widget.title} onExport={handleExport} />
      <WidgetBody>
        {isLoading && <LoadingSpinner />}
        {error && <ErrorMessage error={error} />}
        {!isLoading && !error && renderWidget(widget, liveData)}
      </WidgetBody>
      <WidgetFooter actions={widget.actions} onAction={onAction} />
    </Card>
  );
};
```

### Dashboard Layout System
```javascript
// Responsive grid layout configuration
const dashboardLayouts = {
  analyst: {
    lg: [
      { i: 'LiveAlertQueue', x: 0, y: 0, w: 12, h: 4 },
      { i: 'EntityExplorer', x: 0, y: 4, w: 6, h: 6 },
      { i: 'RelationshipGraph', x: 6, y: 4, w: 6, h: 6 },
      { i: 'CaseTimeline', x: 0, y: 10, w: 12, h: 3 },
      { i: 'ActionToolbar', x: 0, y: 13, w: 12, h: 1 }
    ],
    md: [...],
    sm: [...]
  },
  leader: {
    lg: [
      { i: 'MITREHeatmap', x: 0, y: 0, w: 8, h: 4 },
      { i: 'SystemHealth', x: 8, y: 0, w: 4, h: 4 },
      { i: 'CapacityAmplifier', x: 0, y: 4, w: 6, h: 4 },
      { i: 'RiskVelocity', x: 6, y: 4, w: 6, h: 4 },
      { i: 'TCOForecast', x: 0, y: 8, w: 12, h: 4 }
    ]
  },
  engineer: {
    lg: [
      { i: 'CrystallizationQueue', x: 0, y: 0, w: 8, h: 5 },
      { i: 'GitHubSyncStatus', x: 8, y: 0, w: 4, h: 5 },
      { i: 'PatternPerformance', x: 0, y: 5, w: 6, h: 4 },
      { i: 'RuleTestRunner', x: 6, y: 5, w: 6, h: 4 }
    ]
  }
};
```

### Storybook Organization
```
stories/
├── 1-SituationalAwareness/
│   ├── LiveAlertQueue.stories.js
│   ├── MITREHeatmap.stories.js
│   ├── SystemHealth.stories.js
│   └── ActiveInvestigations.stories.js
├── 2-Investigation/
│   ├── EntityExplorer.stories.js
│   ├── RelationshipGraph.stories.js
│   ├── CaseTimeline.stories.js
│   ├── LogViewer.stories.js
│   ├── AIInvestigationGuide.stories.js
│   └── ActionToolbar.stories.js
├── 3-Engineering/
│   ├── PatternPerformance.stories.js
│   ├── CrystallizationQueue.stories.js
│   ├── RuleTestRunner.stories.js
│   └── GitHubSyncStatus.stories.js
├── 4-Performance/
│   ├── CapacityAmplifier.stories.js
│   ├── RiskVelocity.stories.js
│   └── TCOForecast.stories.js
└── 5-Dashboards/
    ├── AnalystDashboard.stories.js
    ├── LeaderDashboard.stories.js
    └── EngineerDashboard.stories.js
```

---

## Implementation Phases

### Phase 1: Foundation (Post-Authentication)
- Set up Storybook environment
- Create base widget architecture
- Implement WebSocket connectivity
- Build widget container system

### Phase 2: Core Widgets (Weeks 1-2)
- LiveAlertQueue (AG Grid integration)
- SystemHealth (health monitoring)
- EntityExplorer (investigation foundation)
- ActionToolbar (workflow triggers)

### Phase 3: Advanced Widgets (Weeks 3-4)
- RelationshipGraph (D3/Cytoscape)
- MITREHeatmap (custom visualization)
- PatternPerformance (Grafana embed)
- CrystallizationQueue (engineer workflow)
- DetectionFidelityDashboard (Victory Charts) - NEW
- LogSourceInventory (AG Grid) - NEW
- CoverageGapAnalysis (Custom React) - NEW
- MITRETechniqueCoverage (Heatmap) - NEW

### Phase 4: Analytics & ROI (Weeks 5-6)
- CapacityAmplifier (FTE calculations)
- RiskVelocity (MTTD/MTTR metrics)
- TCOForecast (cost analysis)

### Phase 5: Integration & Polish (Weeks 7-8)
- Role-based dashboards
- Widget persistence
- Export functionality
- Performance optimization

---

## Success Metrics

### Technical Metrics
- Widget load time < 500ms
- WebSocket latency < 100ms
- Dashboard render time < 2s
- 60 FPS for animations

### User Experience Metrics
- Time to first insight < 30s
- Click depth to critical action ≤ 3
- Widget configuration time < 1 minute
- Dashboard customization adoption > 80%

### Business Metrics
- Analyst efficiency increase > 40%
- Investigation time reduction > 50%
- Pattern adoption rate > 90%
- Cost visibility improvement > 100%

---

## Future Enhancements

### Advanced Features
- AI-powered widget recommendations
- Predictive widget pre-loading
- Cross-widget data correlation
- Natural language widget commands
- Mobile-responsive widgets

### Integration Points
- SOAR platform widgets
- Threat intelligence feed visualizations
- Compliance reporting dashboards
- Training mode for new analysts
- Widget marketplace for community sharing

---

*This specification will be implemented after the authentication layer is complete, providing a comprehensive, role-based dashboard experience for all SIEMLess users.*