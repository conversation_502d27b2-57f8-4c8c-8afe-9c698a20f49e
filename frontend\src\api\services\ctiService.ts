/**
 * CTI (Cyber Threat Intelligence) API Service
 * Handles all CTI plugin and indicator API calls
 */

import apiClient from '../client'
import type {
  CTIPluginInfo,
  CTIStats,
  APIResponse
} from '../../types/api'

export const ctiService = {
  /**
   * Get status of all CTI plugins
   */
  async getStatus(): Promise<CTIPluginInfo[]> {
    const response = await apiClient.get<APIResponse<CTIPluginInfo[]>>(
      '/cti/status'
    )
    return response.data.data
  },

  /**
   * Get CTI statistics
   */
  async getStats(): Promise<CTIStats> {
    const response = await apiClient.get<APIResponse<CTIStats>>(
      '/cti/stats'
    )
    return response.data.data
  },

  /**
   * Trigger manual CTI update
   */
  async triggerUpdate(
    source: string,
    sinceDays?: number,
    limit?: number
  ): Promise<{ task_id: string }> {
    const response = await apiClient.post<APIResponse<{ task_id: string }>>(
      '/cti/manual_update',
      {
        source,
        since_days: sinceDays || 7,
        limit: limit || 1000
      }
    )
    return response.data.data
  },

  /**
   * Test CTI plugin credentials
   */
  async testCredentials(source: string): Promise<{ valid: boolean }> {
    const response = await apiClient.post<APIResponse<{ valid: boolean }>>(
      '/cti/test_credentials',
      { source }
    )
    return response.data.data
  },

  /**
   * Get CTI connectors info
   */
  async getConnectors(): Promise<any[]> {
    const response = await apiClient.get<APIResponse<any[]>>(
      '/cti/connectors'
    )
    return response.data.data
  }
}
