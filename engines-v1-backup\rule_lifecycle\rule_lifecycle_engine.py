"""
SIEMLess v2.0 - Rule Lifecycle Management Engine
Addresses critical CTI rule lifecycle gap - first-to-market solution for continuous rule improvement.

Capabilities:
- Automated CTI-to-rule updates and recontextualization
- Rule performance tracking and effectiveness measurement
- AI-powered rule optimization and suggestion generation
- Threat context preservation throughout rule lifecycle
- Integration with SIEM platforms for deployment and feedback

Architecture: Information orchestrator, not rule executor
Integration: API Gateway + Redis message queue
"""

from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import json
import uuid
import asyncio
import redis
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RuleFormat(Enum):
    SIGMA = "sigma"
    YARA = "yara"
    SNORT = "snort"
    SURICATA = "suricata"
    SPLUNK = "splunk"
    ELASTICSEARCH = "elasticsearch"
    QR = "qradar"
    SENTINEL = "azure_sentinel"

class RuleStatus(Enum):
    ACTIVE = "active"
    TESTING = "testing"
    DEPRECATED = "deprecated"
    UNDER_REVIEW = "under_review"
    NEEDS_UPDATE = "needs_update"

class ThreatContext(Enum):
    APT = "advanced_persistent_threat"
    MALWARE = "malware_family"
    TECHNIQUE = "mitre_technique"
    CAMPAIGN = "threat_campaign"
    VULNERABILITY = "vulnerability_exploit"

class UpdateTrigger(Enum):
    NEW_CTI = "new_cti_intelligence"
    PERFORMANCE_DECLINE = "performance_decline"
    FALSE_POSITIVE_SPIKE = "false_positive_spike"
    THREAT_EVOLUTION = "threat_evolution"
    MANUAL_REQUEST = "manual_request"
    SCHEDULED_REVIEW = "scheduled_review"

@dataclass
class ThreatIntelligence:
    """CTI data point for rule context"""
    source: str
    ioc_type: str
    ioc_value: str
    threat_context: ThreatContext
    confidence_score: float
    first_seen: datetime
    last_seen: datetime
    mitre_tactics: List[str] = field(default_factory=list)
    mitre_techniques: List[str] = field(default_factory=list)
    kill_chain_phase: Optional[str] = None
    threat_actor: Optional[str] = None
    malware_family: Optional[str] = None
    campaign_name: Optional[str] = None
    raw_intelligence: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RulePerformanceMetrics:
    """Rule effectiveness and performance tracking"""
    rule_id: str
    true_positives: int = 0
    false_positives: int = 0
    false_negatives: int = 0
    total_alerts: int = 0
    avg_response_time: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0
    last_triggered: Optional[datetime] = None
    performance_trend: str = "stable"  # improving, declining, stable
    analyst_feedback_score: float = 0.0
    coverage_effectiveness: float = 0.0

@dataclass
class RuleUpdate:
    """Rule update/modification record"""
    update_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    rule_id: str = ""
    update_type: str = ""  # threshold_adjustment, logic_enhancement, ioc_update, context_expansion
    trigger: UpdateTrigger = UpdateTrigger.MANUAL_REQUEST
    original_rule: str = ""
    updated_rule: str = ""
    change_description: str = ""
    impact_assessment: str = ""
    validation_results: Dict[str, Any] = field(default_factory=dict)
    applied_date: Optional[datetime] = None
    rollback_available: bool = True
    rollback_reason: Optional[str] = None
    created_date: datetime = field(default_factory=datetime.now)
    created_by: str = "rule_lifecycle_engine"

@dataclass
class DetectionRule:
    """Comprehensive detection rule with lifecycle management"""
    rule_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    rule_format: RuleFormat = RuleFormat.SIGMA
    rule_content: str = ""
    status: RuleStatus = RuleStatus.TESTING

    # Threat context and intelligence
    source_cti: List[ThreatIntelligence] = field(default_factory=list)
    mitre_tactics: List[str] = field(default_factory=list)
    mitre_techniques: List[str] = field(default_factory=list)
    threat_context: ThreatContext = ThreatContext.TECHNIQUE
    threat_actors: List[str] = field(default_factory=list)
    malware_families: List[str] = field(default_factory=list)

    # Lifecycle management
    created_date: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    next_review_date: datetime = field(default_factory=lambda: datetime.now() + timedelta(days=90))
    version: str = "1.0"
    parent_rule_id: Optional[str] = None

    # Performance and effectiveness
    performance_metrics: RulePerformanceMetrics = field(default_factory=lambda: RulePerformanceMetrics(""))
    update_history: List[RuleUpdate] = field(default_factory=list)

    # Deployment and integration
    deployed_platforms: List[str] = field(default_factory=list)
    deployment_config: Dict[str, Any] = field(default_factory=dict)

    # Quality and validation
    validation_tests: List[str] = field(default_factory=list)
    quality_score: float = 0.0
    analyst_notes: str = ""
    tags: List[str] = field(default_factory=list)

@dataclass
class RuleImprovementSuggestion:
    """AI-generated rule improvement recommendations"""
    suggestion_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    rule_id: str = ""
    suggestion_type: str = ""  # performance_optimization, false_positive_reduction, coverage_expansion
    description: str = ""
    proposed_changes: str = ""
    confidence_score: float = 0.0
    expected_impact: str = ""
    risk_assessment: str = ""
    supporting_evidence: List[str] = field(default_factory=list)
    implementation_priority: str = "medium"  # low, medium, high, critical
    estimated_effort: str = "medium"  # low, medium, high
    created_date: datetime = field(default_factory=datetime.now)
    status: str = "pending"  # pending, approved, rejected, implemented

@dataclass
class RuleTestCase:
    """Test case for rule validation"""
    test_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    rule_id: str = ""
    test_name: str = ""
    test_type: str = ""  # positive_match, negative_match, edge_case, performance
    test_data: str = ""
    expected_result: bool = True
    actual_result: Optional[bool] = None
    test_status: str = "pending"  # pending, passed, failed, skipped
    execution_time: Optional[float] = None
    error_message: Optional[str] = None
    created_date: datetime = field(default_factory=datetime.now)
    last_run: Optional[datetime] = None

class RuleLifecycleEngine:
    """
    Core engine for comprehensive rule lifecycle management.
    Addresses the critical CTI rule lifecycle gap identified in market research.
    """

    def __init__(self, redis_client: Optional[redis.Redis] = None):
        self.redis_client = redis_client or redis.Redis(host='localhost', port=6379, db=0)
        self.rules: Dict[str, DetectionRule] = {}
        self.cti_sources: Dict[str, Any] = {}
        self.improvement_suggestions: Dict[str, RuleImprovementSuggestion] = {}
        self.test_cases: Dict[str, List[RuleTestCase]] = {}

        # Performance tracking
        self.performance_thresholds = {
            'min_precision': 0.8,
            'min_recall': 0.7,
            'max_false_positive_rate': 0.1,
            'max_response_time': 5.0
        }

        logger.info("Rule Lifecycle Management Engine initialized")

    # === CTI Integration and Intelligence Management ===

    async def ingest_threat_intelligence(self, cti_data: ThreatIntelligence) -> bool:
        """Ingest new CTI and trigger rule updates if relevant"""
        try:
            # Store CTI data
            cti_key = f"cti:{cti_data.source}:{cti_data.ioc_value}"
            await self._store_cti(cti_key, cti_data)

            # Find rules that might need updates based on this CTI
            affected_rules = await self._find_rules_for_cti_update(cti_data)

            # Queue update assessments for affected rules
            for rule_id in affected_rules:
                await self._queue_rule_update_assessment(rule_id, UpdateTrigger.NEW_CTI, cti_data)

            logger.info(f"Ingested CTI from {cti_data.source}, affected {len(affected_rules)} rules")
            return True

        except Exception as e:
            logger.error(f"Failed to ingest CTI: {str(e)}")
            return False

    async def _find_rules_for_cti_update(self, cti_data: ThreatIntelligence) -> List[str]:
        """Identify rules that should be updated based on new CTI"""
        affected_rules = []

        for rule_id, rule in self.rules.items():
            # Check if CTI matches rule's threat context
            if (rule.threat_context == cti_data.threat_context or
                any(tactic in rule.mitre_tactics for tactic in cti_data.mitre_tactics) or
                any(technique in rule.mitre_techniques for technique in cti_data.mitre_techniques)):

                affected_rules.append(rule_id)

        return affected_rules

    async def generate_rules_from_cti(self, cti_batch: List[ThreatIntelligence],
                                    rule_format: RuleFormat = RuleFormat.SIGMA) -> List[DetectionRule]:
        """Generate new detection rules from CTI intelligence"""
        generated_rules = []

        for cti in cti_batch:
            try:
                # Create rule based on CTI context
                rule = DetectionRule(
                    name=f"CTI-Generated: {cti.threat_context.value.title()} Detection",
                    description=f"Auto-generated rule for {cti.ioc_type} {cti.ioc_value}",
                    rule_format=rule_format,
                    source_cti=[cti],
                    mitre_tactics=cti.mitre_tactics,
                    mitre_techniques=cti.mitre_techniques,
                    threat_context=cti.threat_context
                )

                # Generate rule content based on CTI type and format
                rule.rule_content = await self._generate_rule_content(cti, rule_format)

                # Create initial test cases
                rule.validation_tests = await self._generate_test_cases(rule)

                generated_rules.append(rule)

            except Exception as e:
                logger.error(f"Failed to generate rule from CTI {cti.ioc_value}: {str(e)}")

        logger.info(f"Generated {len(generated_rules)} rules from {len(cti_batch)} CTI inputs")
        return generated_rules

    # === Rule Performance Tracking and Analytics ===

    async def update_rule_performance(self, rule_id: str, alert_data: Dict[str, Any]) -> bool:
        """Update rule performance metrics based on new alert data"""
        if rule_id not in self.rules:
            logger.warning(f"Rule {rule_id} not found for performance update")
            return False

        rule = self.rules[rule_id]
        metrics = rule.performance_metrics

        # Update counters based on alert classification
        if alert_data.get('classification') == 'true_positive':
            metrics.true_positives += 1
        elif alert_data.get('classification') == 'false_positive':
            metrics.false_positives += 1
        elif alert_data.get('classification') == 'false_negative':
            metrics.false_negatives += 1

        metrics.total_alerts += 1
        metrics.last_triggered = datetime.now()

        # Recalculate performance scores
        await self._calculate_performance_scores(metrics)

        # Check if rule needs attention
        await self._assess_rule_health(rule)

        logger.info(f"Updated performance metrics for rule {rule_id}")
        return True

    async def _calculate_performance_scores(self, metrics: RulePerformanceMetrics) -> None:
        """Calculate precision, recall, and F1 scores"""
        if metrics.total_alerts == 0:
            return

        # Precision = TP / (TP + FP)
        if (metrics.true_positives + metrics.false_positives) > 0:
            metrics.precision = metrics.true_positives / (metrics.true_positives + metrics.false_positives)

        # Recall = TP / (TP + FN)
        if (metrics.true_positives + metrics.false_negatives) > 0:
            metrics.recall = metrics.true_positives / (metrics.true_positives + metrics.false_negatives)

        # F1 Score = 2 * (precision * recall) / (precision + recall)
        if (metrics.precision + metrics.recall) > 0:
            metrics.f1_score = 2 * (metrics.precision * metrics.recall) / (metrics.precision + metrics.recall)

    async def _assess_rule_health(self, rule: DetectionRule) -> None:
        """Assess rule health and trigger improvements if needed"""
        metrics = rule.performance_metrics

        # Check for performance decline
        needs_attention = False
        trigger_reason = None

        if metrics.precision < self.performance_thresholds['min_precision']:
            needs_attention = True
            trigger_reason = UpdateTrigger.FALSE_POSITIVE_SPIKE

        elif metrics.recall < self.performance_thresholds['min_recall']:
            needs_attention = True
            trigger_reason = UpdateTrigger.PERFORMANCE_DECLINE

        elif metrics.avg_response_time > self.performance_thresholds['max_response_time']:
            needs_attention = True
            trigger_reason = UpdateTrigger.PERFORMANCE_DECLINE

        if needs_attention:
            await self._queue_rule_improvement(rule.rule_id, trigger_reason)

    # === Rule Update and Improvement System ===

    async def _queue_rule_update_assessment(self, rule_id: str, trigger: UpdateTrigger,
                                          context_data: Any = None) -> None:
        """Queue a rule for update assessment"""
        assessment_data = {
            'rule_id': rule_id,
            'trigger': trigger.value,
            'context_data': context_data,
            'queued_at': datetime.now().isoformat()
        }

        # Use Redis for queue management
        await self.redis_client.lpush('rule_update_queue', json.dumps(assessment_data, default=str))
        logger.info(f"Queued rule {rule_id} for update assessment (trigger: {trigger.value})")

    async def _queue_rule_improvement(self, rule_id: str, trigger: UpdateTrigger) -> None:
        """Queue a rule for AI-powered improvement suggestions"""
        improvement_data = {
            'rule_id': rule_id,
            'trigger': trigger.value,
            'queued_at': datetime.now().isoformat()
        }

        await self.redis_client.lpush('rule_improvement_queue', json.dumps(improvement_data))
        logger.info(f"Queued rule {rule_id} for improvement analysis")

    async def generate_improvement_suggestions(self, rule_id: str) -> List[RuleImprovementSuggestion]:
        """Generate AI-powered improvement suggestions for a rule"""
        if rule_id not in self.rules:
            return []

        rule = self.rules[rule_id]
        suggestions = []

        try:
            # Analyze performance issues
            performance_suggestions = await self._analyze_performance_issues(rule)
            suggestions.extend(performance_suggestions)

            # Analyze coverage gaps
            coverage_suggestions = await self._analyze_coverage_gaps(rule)
            suggestions.extend(coverage_suggestions)

            # Analyze false positive patterns
            fp_suggestions = await self._analyze_false_positive_patterns(rule)
            suggestions.extend(fp_suggestions)

            # Store suggestions
            for suggestion in suggestions:
                self.improvement_suggestions[suggestion.suggestion_id] = suggestion

            logger.info(f"Generated {len(suggestions)} improvement suggestions for rule {rule_id}")

        except Exception as e:
            logger.error(f"Failed to generate improvements for rule {rule_id}: {str(e)}")

        return suggestions

    async def _analyze_performance_issues(self, rule: DetectionRule) -> List[RuleImprovementSuggestion]:
        """Analyze rule performance and suggest optimizations"""
        suggestions = []
        metrics = rule.performance_metrics

        # Low precision suggests false positive issues
        if metrics.precision < 0.8:
            suggestion = RuleImprovementSuggestion(
                rule_id=rule.rule_id,
                suggestion_type="false_positive_reduction",
                description=f"Rule precision is {metrics.precision:.2f}, below threshold of 0.8",
                proposed_changes="Add additional context filters to reduce false positives",
                confidence_score=0.85,
                expected_impact="Reduce false positive rate by 20-30%",
                implementation_priority="high"
            )
            suggestions.append(suggestion)

        # Low recall suggests coverage gaps
        if metrics.recall < 0.7:
            suggestion = RuleImprovementSuggestion(
                rule_id=rule.rule_id,
                suggestion_type="coverage_expansion",
                description=f"Rule recall is {metrics.recall:.2f}, below threshold of 0.7",
                proposed_changes="Expand detection logic to catch more attack variants",
                confidence_score=0.75,
                expected_impact="Increase detection coverage by 15-25%",
                implementation_priority="medium"
            )
            suggestions.append(suggestion)

        return suggestions

    # === Rule Testing and Validation ===

    async def create_test_case(self, rule_id: str, test_case: RuleTestCase) -> bool:
        """Create a new test case for a rule"""
        if rule_id not in self.test_cases:
            self.test_cases[rule_id] = []

        test_case.rule_id = rule_id
        self.test_cases[rule_id].append(test_case)

        logger.info(f"Created test case {test_case.test_id} for rule {rule_id}")
        return True

    async def run_rule_tests(self, rule_id: str) -> Dict[str, Any]:
        """Run all test cases for a specific rule"""
        if rule_id not in self.test_cases:
            return {'status': 'no_tests', 'results': []}

        test_results = []
        passed_tests = 0
        total_tests = len(self.test_cases[rule_id])

        for test_case in self.test_cases[rule_id]:
            try:
                start_time = datetime.now()

                # Simulate rule execution against test data
                result = await self._execute_rule_test(rule_id, test_case)

                execution_time = (datetime.now() - start_time).total_seconds()

                test_case.actual_result = result
                test_case.execution_time = execution_time
                test_case.last_run = datetime.now()

                if result == test_case.expected_result:
                    test_case.test_status = "passed"
                    passed_tests += 1
                else:
                    test_case.test_status = "failed"

                test_results.append({
                    'test_id': test_case.test_id,
                    'test_name': test_case.test_name,
                    'status': test_case.test_status,
                    'execution_time': execution_time,
                    'expected': test_case.expected_result,
                    'actual': result
                })

            except Exception as e:
                test_case.test_status = "failed"
                test_case.error_message = str(e)
                test_results.append({
                    'test_id': test_case.test_id,
                    'test_name': test_case.test_name,
                    'status': "failed",
                    'error': str(e)
                })

        success_rate = passed_tests / total_tests if total_tests > 0 else 0

        return {
            'status': 'completed',
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': success_rate,
            'results': test_results
        }

    # === Rule Deployment and Integration ===

    async def deploy_rule_to_platform(self, rule_id: str, platform: str,
                                     config: Dict[str, Any] = None) -> bool:
        """Deploy rule to specific SIEM/security platform"""
        if rule_id not in self.rules:
            logger.error(f"Rule {rule_id} not found for deployment")
            return False

        rule = self.rules[rule_id]

        try:
            # Convert rule format if needed
            platform_rule = await self._convert_rule_format(rule, platform)

            # Deploy via platform-specific API
            deployment_result = await self._deploy_to_platform(platform_rule, platform, config)

            if deployment_result['success']:
                rule.deployed_platforms.append(platform)
                rule.deployment_config[platform] = config or {}

                logger.info(f"Successfully deployed rule {rule_id} to {platform}")
                return True
            else:
                logger.error(f"Failed to deploy rule {rule_id} to {platform}: {deployment_result['error']}")
                return False

        except Exception as e:
            logger.error(f"Deployment error for rule {rule_id}: {str(e)}")
            return False

    # === Utility and Helper Methods ===

    async def _store_cti(self, key: str, cti_data: ThreatIntelligence) -> None:
        """Store CTI data in Redis"""
        cti_json = {
            'source': cti_data.source,
            'ioc_type': cti_data.ioc_type,
            'ioc_value': cti_data.ioc_value,
            'threat_context': cti_data.threat_context.value,
            'confidence_score': cti_data.confidence_score,
            'first_seen': cti_data.first_seen.isoformat(),
            'last_seen': cti_data.last_seen.isoformat(),
            'mitre_tactics': cti_data.mitre_tactics,
            'mitre_techniques': cti_data.mitre_techniques,
            'raw_intelligence': cti_data.raw_intelligence
        }

        await self.redis_client.setex(key, 86400 * 30, json.dumps(cti_json))  # 30 days TTL

    async def _generate_rule_content(self, cti: ThreatIntelligence,
                                   rule_format: RuleFormat) -> str:
        """Generate rule content based on CTI and target format"""
        # This would integrate with AI models for rule generation
        # For now, return a template-based rule

        if rule_format == RuleFormat.SIGMA:
            return f"""
title: CTI-Generated Detection for {cti.ioc_value}
id: {str(uuid.uuid4())}
status: experimental
description: Auto-generated rule based on CTI intelligence
references:
    - {cti.source}
author: SIEMLess Rule Lifecycle Engine
date: {datetime.now().strftime('%Y/%m/%d')}
tags:
    - attack.{cti.mitre_techniques[0] if cti.mitre_techniques else 'unknown'}
logsource:
    category: security
detection:
    selection:
        - {cti.ioc_type}: '{cti.ioc_value}'
    condition: selection
falsepositives:
    - Unknown
level: medium
"""
        else:
            return f"# Auto-generated rule for {cti.ioc_value}"

    async def _generate_test_cases(self, rule: DetectionRule) -> List[str]:
        """Generate test cases for a rule"""
        # Return test case IDs that would be created
        return [f"test_{rule.rule_id}_positive", f"test_{rule.rule_id}_negative"]

    async def _execute_rule_test(self, rule_id: str, test_case: RuleTestCase) -> bool:
        """Execute a rule test case - simulation for now"""
        # In real implementation, this would execute the rule against test data
        # For now, simulate based on test type
        if test_case.test_type == "positive_match":
            return True  # Simulate positive detection
        elif test_case.test_type == "negative_match":
            return False  # Simulate no detection
        else:
            return test_case.expected_result  # Default to expected result

    async def _convert_rule_format(self, rule: DetectionRule, target_platform: str) -> Dict[str, Any]:
        """Convert rule to platform-specific format"""
        return {
            'rule_id': rule.rule_id,
            'name': rule.name,
            'content': rule.rule_content,
            'format': target_platform
        }

    async def _deploy_to_platform(self, rule: Dict[str, Any], platform: str,
                                 config: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy rule to specific platform - simulation"""
        # In real implementation, this would use platform APIs
        return {'success': True, 'deployment_id': str(uuid.uuid4())}

    async def _analyze_coverage_gaps(self, rule: DetectionRule) -> List[RuleImprovementSuggestion]:
        """Analyze rule coverage gaps"""
        return []  # Placeholder for AI-powered analysis

    async def _analyze_false_positive_patterns(self, rule: DetectionRule) -> List[RuleImprovementSuggestion]:
        """Analyze false positive patterns"""
        return []  # Placeholder for AI-powered analysis

    # === API Methods for External Integration ===

    async def get_rule_status(self, rule_id: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive rule status"""
        if rule_id not in self.rules:
            return None

        rule = self.rules[rule_id]
        return {
            'rule_id': rule_id,
            'name': rule.name,
            'status': rule.status.value,
            'version': rule.version,
            'performance': {
                'precision': rule.performance_metrics.precision,
                'recall': rule.performance_metrics.recall,
                'f1_score': rule.performance_metrics.f1_score,
                'total_alerts': rule.performance_metrics.total_alerts
            },
            'last_updated': rule.last_updated.isoformat(),
            'next_review': rule.next_review_date.isoformat(),
            'deployed_platforms': rule.deployed_platforms,
            'pending_improvements': len([s for s in self.improvement_suggestions.values()
                                       if s.rule_id == rule_id and s.status == 'pending'])
        }

    async def get_improvement_suggestions(self, rule_id: str) -> List[Dict[str, Any]]:
        """Get improvement suggestions for a rule"""
        suggestions = [s for s in self.improvement_suggestions.values()
                      if s.rule_id == rule_id]

        return [{
            'suggestion_id': s.suggestion_id,
            'type': s.suggestion_type,
            'description': s.description,
            'confidence': s.confidence_score,
            'priority': s.implementation_priority,
            'status': s.status,
            'created_date': s.created_date.isoformat()
        } for s in suggestions]

# Example usage and testing
async def main():
    """Example usage of the Rule Lifecycle Management Engine"""

    # Initialize engine
    engine = RuleLifecycleEngine()

    # Create sample CTI data
    sample_cti = ThreatIntelligence(
        source="threat_intel_feed",
        ioc_type="domain",
        ioc_value="malicious-domain.com",
        threat_context=ThreatContext.APT,
        confidence_score=0.9,
        first_seen=datetime.now() - timedelta(days=1),
        last_seen=datetime.now(),
        mitre_tactics=["TA0011"],
        mitre_techniques=["T1071.001"],
        campaign_name="APT29-Campaign-2024"
    )

    # Ingest CTI and generate rules
    await engine.ingest_threat_intelligence(sample_cti)
    generated_rules = await engine.generate_rules_from_cti([sample_cti])

    if generated_rules:
        rule = generated_rules[0]
        engine.rules[rule.rule_id] = rule

        # Simulate rule performance updates
        await engine.update_rule_performance(rule.rule_id, {'classification': 'true_positive'})
        await engine.update_rule_performance(rule.rule_id, {'classification': 'false_positive'})

        # Generate improvement suggestions
        suggestions = await engine.generate_improvement_suggestions(rule.rule_id)

        # Run tests
        test_case = RuleTestCase(
            rule_id=rule.rule_id,
            test_name="Positive Detection Test",
            test_type="positive_match",
            test_data="sample_log_data",
            expected_result=True
        )
        await engine.create_test_case(rule.rule_id, test_case)
        test_results = await engine.run_rule_tests(rule.rule_id)

        # Get rule status
        status = await engine.get_rule_status(rule.rule_id)

        print(f"Created rule: {rule.name}")
        print(f"Performance: P={rule.performance_metrics.precision:.2f}, R={rule.performance_metrics.recall:.2f}")
        print(f"Suggestions: {len(suggestions)}")
        print(f"Test results: {test_results['success_rate']:.2f} success rate")

if __name__ == "__main__":
    asyncio.run(main())