# MITRE ATT&CK-Specific Investigation Playbooks

## The Core Insight

**Generic investigation guide:** "Check threat intel, review logs..."
**MITRE-specific playbook:** Different questions for each technique!

## Example: T1046 - Network Service Discovery (Port Scanning)

### Key Investigation Questions
```yaml
technique: T1046
name: Network Service Discovery
tactic: Discovery

benign_scenarios:
  - Authorized vulnerability scanners
  - Network monitoring tools
  - Legitimate IT troubleshooting
  - Development/testing environments

malicious_scenarios:
  - Initial reconnaissance after breach
  - Lateral movement preparation
  - Pre-ransomware environment mapping
  - APT reconnaissance

critical_context_questions:
  - Is the source AUTHORIZED to perform network scanning?
  - What PORTS were scanned? (Critical vs. routine)
  - What TIME did this occur? (Maintenance window vs. off-hours)
  - What USER/account initiated it? (Service account vs. end user)
  - Is the source a WORKSTATION or SERVER?
  - Has this SOURCE scanned before? (Recurring pattern?)
  - Were OTHER suspicious activities detected from same source?

risk_scoring:
  high_risk_indicators:
    - Workstation scanning for SMB/RDP (445, 3389)
    - End-user account (not service account)
    - Off-hours scanning (nights/weekends)
    - First-time behavior (no history)
    - Followed by authentication attempts
    - Multiple protocols tested

  low_risk_indicators:
    - Known scanner IP
    - Service account
    - Maintenance window
    - Recurring weekly pattern
    - Standard web ports only
    - IT/Security department source

investigation_workflow:
  1. Check asset database for source IP role
  2. Verify user account type (service vs. human)
  3. Check maintenance calendar for authorized scans
  4. Analyze ports scanned for critical services
  5. Review timeline for follow-up lateral movement
  6. Check historical behavior for pattern match
```

## Example: T1059.001 - PowerShell Execution

### Key Investigation Questions
```yaml
technique: T1059.001
name: Command and Scripting Interpreter - PowerShell
tactic: Execution

benign_scenarios:
  - IT automation scripts
  - Software deployment (SCCM, Intune)
  - System administration
  - Monitoring/health checks

malicious_scenarios:
  - Fileless malware execution
  - Credential dumping (Mimikatz)
  - Lateral movement (PSRemoting)
  - Data exfiltration

critical_context_questions:
  - Is PowerShell execution COMMON on this host?
  - What SCRIPT was executed? (Obfuscated? Base64?)
  - Was it SIGNED or unsigned?
  - What PARENT PROCESS spawned PowerShell?
  - Were NETWORK connections made?
  - Were CREDENTIALS accessed?
  - Was it executed via SCHEDULED TASK or manual?

risk_scoring:
  high_risk_indicators:
    - Parent process: Excel/Word/Outlook (phishing)
    - Obfuscated commands (base64, compression)
    - Network connections to external IPs
    - Credential access (SAM, LSASS)
    - PowerShell.exe -ExecutionPolicy Bypass
    - Disabled logging/AMSI bypass
    - Non-admin user executing admin commands

  low_risk_indicators:
    - Parent process: SCCM/Intune agent
    - Signed by IT department
    - Executed by service account
    - Standard automation script
    - Recurring daily/weekly pattern
    - No network activity
    - Logged commands (not hidden)

investigation_workflow:
  1. Capture full command line from EDR/Sysmon
  2. Check script signing and publisher
  3. Identify parent process (Office app = suspicious)
  4. Review network connections made
  5. Check if credentials were accessed
  6. Compare to historical PowerShell baseline
  7. Analyze script content for malicious indicators
```

## Example: T1021.001 - Remote Desktop Protocol

### Key Investigation Questions
```yaml
technique: T1021.001
name: Remote Services - Remote Desktop Protocol
tactic: Lateral Movement

benign_scenarios:
  - IT support accessing user machines
  - Administrators managing servers
  - Remote work from home
  - Jump box connections

malicious_scenarios:
  - Lateral movement after initial compromise
  - Ransomware deployment
  - APT persistence
  - Data theft

critical_context_questions:
  - Is the SOURCE IP expected to use RDP?
  - Is the DESTINATION normally accessed remotely?
  - Is the USER authorized for RDP?
  - What TIME did connection occur?
  - Were FAILED attempts before success?
  - What ACTIONS occurred during session?
  - Is this GEOGRAPHIC location normal?

risk_scoring:
  high_risk_indicators:
    - Workstation-to-workstation RDP (unusual)
    - Service account used for RDP (bad practice)
    - Multiple failed attempts then success
    - Off-hours connection (2 AM)
    - Short session duration (<2 minutes)
    - Followed by file encryption/deletion
    - Geographic anomaly (VPN from foreign country)

  low_risk_indicators:
    - IT admin account
    - Jump box to server
    - Business hours
    - Normal session duration
    - Ticket number referenced
    - Expected geographic location
    - Recurring pattern

investigation_workflow:
  1. Verify user's job role and RDP authorization
  2. Check if source/destination pair is normal
  3. Review authentication logs for brute force
  4. Analyze session duration and activities
  5. Check file system changes during session
  6. Review network traffic for data exfil
  7. Correlate with other lateral movement indicators
```

## The Playbook System Architecture

### Database Schema
```sql
CREATE TABLE mitre_investigation_playbooks (
    technique_id VARCHAR(20) PRIMARY KEY,  -- e.g., T1046
    technique_name VARCHAR(200),
    tactic VARCHAR(100),

    -- Context questions (JSON array)
    context_questions JSONB,

    -- Risk indicators (JSON object)
    high_risk_indicators JSONB,
    low_risk_indicators JSONB,

    -- Investigation workflow (JSON array)
    investigation_steps JSONB,

    -- Expected data sources
    required_data_sources TEXT[],  -- ['EDR', 'Network', 'AD']

    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### Example Playbook Entry
```json
{
  "technique_id": "T1046",
  "technique_name": "Network Service Discovery",
  "context_questions": [
    {
      "question": "Is the source authorized to scan?",
      "data_source": "asset_database",
      "query": "SELECT role FROM assets WHERE ip = ?",
      "benign_values": ["Vulnerability Scanner", "Network Monitor"]
    },
    {
      "question": "What ports were scanned?",
      "data_source": "network_logs",
      "query": "SELECT DISTINCT dest_port FROM netflow WHERE src_ip = ?",
      "risk_analysis": {
        "critical_ports": [445, 3389, 135, 139],
        "risk_weight": -30
      }
    }
  ],
  "risk_scoring": {
    "base_score": 50,
    "adjustments": [
      {
        "condition": "source.role == 'Vulnerability Scanner'",
        "adjustment": +40,
        "reasoning": "Authorized scanner - expected behavior"
      },
      {
        "condition": "ports_scanned contains [445, 3389]",
        "adjustment": -30,
        "reasoning": "Scanning SMB/RDP - lateral movement indicator"
      }
    ]
  }
}
```

## Implementation: Dynamic Playbook Execution

```python
class MITREPlaybookEngine:
    """Execute MITRE-specific investigation playbooks"""

    async def execute_playbook(self, alert: Dict, mitre_technique: str) -> Dict:
        """
        Execute technique-specific playbook
        Returns context-aware verdict based on technique
        """
        # Load playbook from database
        playbook = await self.load_playbook(mitre_technique)

        if not playbook:
            # Fall back to generic investigation
            return await self.generic_investigation(alert)

        # Execute context questions
        context = {}
        for question in playbook['context_questions']:
            answer = await self.answer_question(
                question,
                alert
            )
            context[question['key']] = answer

        # Calculate risk score based on technique-specific logic
        risk_score = playbook['risk_scoring']['base_score']
        reasoning = []

        for adjustment in playbook['risk_scoring']['adjustments']:
            if self.evaluate_condition(adjustment['condition'], context):
                risk_score += adjustment['adjustment']
                reasoning.append(adjustment['reasoning'])

        # Determine verdict
        if risk_score >= 70:
            verdict = "LIKELY_BENIGN"
        elif risk_score >= 40:
            verdict = "REQUIRES_INVESTIGATION"
        else:
            verdict = "LIKELY_MALICIOUS"

        return {
            'technique': mitre_technique,
            'verdict': verdict,
            'risk_score': risk_score,
            'reasoning': reasoning,
            'context': context,
            'investigation_steps': playbook['investigation_workflow']
        }
```

## The Power of This Approach

### Before (Generic)
```
Alert: Port Scan Detected
Investigation Guide: "Check threat intel, review logs..."

Analyst: "Is this bad? I don't know..."
Time: 10 minutes of manual checking
```

### After (MITRE-Specific)
```
Alert: Port Scan Detected (T1046)
MITRE Playbook Applied:

✅ Source is authorized vulnerability scanner (prod-scanner-01)
✅ Occurred during maintenance window (Tuesday 2 AM)
✅ Scanned standard ports (22, 80, 443)
✅ Historical pattern matches (47 previous identical scans)

Verdict: LIKELY_BENIGN (95% confidence)
Action: AUTO_CLOSE

Analyst: "Clear verdict, move to next alert"
Time: 5 seconds
```

## Building the Playbook Library

### Priority Techniques (Top 20 most common)
1. T1046 - Network Service Discovery (Port Scan)
2. T1059.001 - PowerShell
3. T1021.001 - RDP
4. T1078 - Valid Accounts
5. T1071.001 - Web Protocols
6. T1055 - Process Injection
7. T1003 - Credential Dumping
8. T1486 - Data Encrypted for Impact (Ransomware)
9. T1570 - Lateral Tool Transfer
10. T1053 - Scheduled Task

Each technique gets:
- Specific context questions
- Risk indicators (benign vs. malicious)
- Investigation workflow
- Automated scoring logic

This is how we transform **"generic investigation guides"** into **"actionable, technique-specific intelligence"**.
