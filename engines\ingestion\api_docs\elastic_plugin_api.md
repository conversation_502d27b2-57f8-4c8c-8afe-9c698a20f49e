# Elastic Security Context Plugin API Documentation

## Overview

The Elastic Security Context Plugin enables SIEMLess to pull investigation context from Elastic Security/SIEM deployments. This plugin follows the community ingestion pattern, allowing security analysts to contribute and consume Elastic-based investigation data.

**Version**: 1.0.0
**Plugin Type**: Context Source
**Vendor**: Elastic Security
**Schema**: ECS (Elastic Common Schema)

---

## Table of Contents

1. [Configuration](#configuration)
2. [Supported Query Types](#supported-query-types)
3. [Context Categories](#context-categories)
4. [API Endpoints](#api-endpoints)
5. [Query Examples](#query-examples)
6. [Response Format](#response-format)
7. [ECS Field Mappings](#ecs-field-mappings)
8. [Community Contribution](#community-contribution)
9. [Troubleshooting](#troubleshooting)

---

## Configuration

### Environment Variables

```bash
# Elastic Cloud Configuration (Recommended)
ELASTIC_CLOUD_ID=deployment_name:base64_encoded_info
ELASTIC_API_KEY=your_api_key

# Self-Hosted Elastic Configuration (Alternative)
ELASTIC_URL=https://your-elastic-instance:9200
ELASTIC_USERNAME=your_username
ELASTIC_PASSWORD=your_password
ELASTIC_VERIFY_CERTS=true
```

### Plugin Registration

The plugin is automatically registered when the ingestion engine starts:

```python
{
    'enabled': bool(os.getenv('ELASTIC_CLOUD_ID') or os.getenv('ELASTIC_URL')),
    'cloud_id': os.getenv('ELASTIC_CLOUD_ID'),
    'elastic_url': os.getenv('ELASTIC_URL'),
    'api_key': os.getenv('ELASTIC_API_KEY'),
    'username': os.getenv('ELASTIC_USERNAME'),
    'password': os.getenv('ELASTIC_PASSWORD'),
    'verify_certs': os.getenv('ELASTIC_VERIFY_CERTS', 'true').lower() == 'true'
}
```

---

## Supported Query Types

The Elastic plugin supports the following investigation query types:

| Query Type | Description | Example Value |
|------------|-------------|---------------|
| `ip` | Search for IP addresses | `*************` |
| `hostname` | Search for hostnames | `DESKTOP-ABC123` |
| `user` | Search for user accounts | `john.doe` |
| `hash` | Search for file hashes | `d41d8cd98f00b204e9800998ecf8427e` |
| `domain` | Search for domains | `malicious.com` |
| `process` | Search for process names | `powershell.exe` |

---

## Context Categories

The plugin can retrieve different types of context data:

### 1. ASSET (Host/Endpoint Information)

Aggregates host information from multiple log sources:

**ECS Fields Used**:
- `host.name`, `host.hostname`
- `host.ip`, `host.mac`
- `host.os.name`, `host.os.version`
- `agent.version`, `agent.type`

**Use Cases**:
- Asset inventory
- Endpoint visibility
- Configuration baseline

---

### 2. DETECTION (Security Alerts)

Retrieves security detections and alerts:

**ECS Fields Used**:
- `event.kind: alert`
- `event.severity`, `event.risk_score`
- `rule.name`, `rule.id`
- `kibana.alert.*`

**Use Cases**:
- Investigation triage
- Alert correlation
- Threat hunting

---

### 3. NETWORK (Network Flows)

Queries network traffic and connections:

**ECS Fields Used**:
- `source.ip`, `source.port`
- `destination.ip`, `destination.port`
- `network.protocol`, `network.bytes`
- `network.direction`

**Use Cases**:
- Lateral movement detection
- C2 communication analysis
- Data exfiltration

---

### 4. LOG (Raw Event Logs)

Retrieves raw ECS-normalized logs:

**ECS Fields Used**:
- `@timestamp`
- `message`, `event.original`
- `event.action`, `event.category`
- `event.outcome`

**Use Cases**:
- Timeline reconstruction
- Detailed forensics
- Evidence collection

---

## API Endpoints

### Pull Investigation Context

**Endpoint**: `ingestion.pull_context` (Redis Channel)

**Request Format**:
```json
{
  "request_id": "uuid-v4",
  "query": {
    "query_type": "hostname",
    "query_value": "DESKTOP-ABC123",
    "categories": ["ASSET", "DETECTION", "NETWORK"],
    "time_range_hours": 24
  }
}
```

**Response Channel**: `ingestion.context_response.{request_id}`

**Response Format**:
```json
{
  "request_id": "uuid-v4",
  "status": "success",
  "context_results": {
    "elastic": [
      {
        "source_name": "elastic",
        "category": "ASSET",
        "confidence": 0.90,
        "data": {
          "hostname": "DESKTOP-ABC123",
          "ip_addresses": ["*************", "*********"],
          "os": "Windows 10 Pro",
          "first_seen": "2025-10-01T00:00:00Z",
          "last_seen": "2025-10-02T14:30:00Z",
          "log_count": 1234
        },
        "timestamp": "2025-10-02T14:30:00Z",
        "metadata": {
          "indices_searched": ["logs-*", "filebeat-*"],
          "aggregation_window": "24h"
        }
      }
    ]
  }
}
```

---

## Query Examples

### Example 1: Investigate Suspicious IP

```python
import redis
import json
from uuid import uuid4

redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)
request_id = str(uuid4())

query = {
    'request_id': request_id,
    'query': {
        'query_type': 'ip',
        'query_value': '*************',
        'categories': ['ASSET', 'DETECTION', 'NETWORK'],
        'time_range_hours': 24
    }
}

# Publish request
redis_client.publish('ingestion.pull_context', json.dumps(query))

# Subscribe to response
pubsub = redis_client.pubsub()
pubsub.subscribe(f'ingestion.context_response.{request_id}')

for message in pubsub.listen():
    if message['type'] == 'message':
        response = json.loads(message['data'])
        print(json.dumps(response, indent=2))
        break
```

---

### Example 2: Host Timeline Investigation

```python
query = {
    'request_id': str(uuid4()),
    'query': {
        'query_type': 'hostname',
        'query_value': 'WORKSTATION-042',
        'categories': ['LOG'],  # Get all logs for timeline
        'time_range_hours': 168  # Last 7 days
    }
}
```

---

### Example 3: User Activity Analysis

```python
query = {
    'request_id': str(uuid4()),
    'query': {
        'query_type': 'user',
        'query_value': 'admin',
        'categories': ['ASSET', 'DETECTION', 'LOG'],
        'time_range_hours': 24
    }
}
```

---

## Response Format

### Standard Context Result

Every Elastic query returns results in standardized `ContextResult` format:

```python
{
    "source_name": "elastic",           # Plugin identifier
    "category": "ASSET|DETECTION|NETWORK|LOG",
    "confidence": 0.0 - 1.0,            # Result confidence
    "data": {                            # Category-specific data
        # Extracted and normalized fields
    },
    "timestamp": "ISO-8601",            # Result timestamp
    "metadata": {                        # Query metadata
        "indices_searched": [...],
        "query_time_ms": 123,
        "result_count": 10
    }
}
```

### Asset Data Structure

```json
{
  "hostname": "DESKTOP-ABC123",
  "ip_addresses": ["*************"],
  "mac_addresses": ["00:11:22:33:44:55"],
  "os": "Windows 10 Pro",
  "os_version": "10.0.19045",
  "agent_type": "filebeat",
  "agent_version": "8.10.0",
  "first_seen": "2025-10-01T00:00:00Z",
  "last_seen": "2025-10-02T14:30:00Z",
  "log_count": 1234,
  "unique_users": 3,
  "unique_processes": 45
}
```

### Detection Data Structure

```json
{
  "alert_id": "abc-123",
  "rule_name": "Suspicious PowerShell Execution",
  "severity": "high",
  "risk_score": 75,
  "event_action": "process_creation",
  "timestamp": "2025-10-02T14:25:00Z",
  "host": "DESKTOP-ABC123",
  "user": "admin",
  "process": "powershell.exe",
  "command_line": "powershell.exe -enc <base64>",
  "parent_process": "outlook.exe"
}
```

### Network Data Structure

```json
{
  "source_ip": "*************",
  "source_port": 54321,
  "destination_ip": "***********",
  "destination_port": 445,
  "protocol": "tcp",
  "bytes_sent": 1024,
  "bytes_received": 2048,
  "direction": "outbound",
  "timestamp": "2025-10-02T14:20:00Z",
  "duration_ms": 5000
}
```

---

## ECS Field Mappings

### Core ECS Fields

The plugin maps ECS fields to standardized context data:

| ECS Field | Context Field | Category |
|-----------|---------------|----------|
| `host.name` | `hostname` | ASSET |
| `host.ip` | `ip_addresses[]` | ASSET |
| `host.mac` | `mac_addresses[]` | ASSET |
| `host.os.name` | `os` | ASSET |
| `source.ip` | `source_ip` | NETWORK |
| `destination.ip` | `destination_ip` | NETWORK |
| `user.name` | `user` | DETECTION/LOG |
| `process.name` | `process` | DETECTION/LOG |
| `event.action` | `event_action` | LOG |
| `@timestamp` | `timestamp` | ALL |

### Detection-Specific Fields

| ECS Field | Context Field |
|-----------|---------------|
| `kibana.alert.rule.name` | `rule_name` |
| `kibana.alert.severity` | `severity` |
| `kibana.alert.risk_score` | `risk_score` |
| `event.kind: alert` | Filter criteria |

---

## Community Contribution

### How Community Ingestion Works

1. **Analyst Runs Investigation**
   - Queries Elastic through SIEMLess
   - Receives contextualized results
   - Results automatically saved (anonymized)

2. **Pattern Crystallization**
   - Successful queries become patterns
   - Patterns shared with community (opt-in)
   - Other analysts benefit from learned patterns

3. **Query Optimization**
   - AI learns which Elastic queries work best
   - Community validates query effectiveness
   - System improves over time

### Sharing Your Elastic Queries

```python
# After successful investigation
await context_manager.share_pattern(
    vendor='elastic',
    query_type='ip',
    category='DETECTION',
    success_rate=0.95,
    community_contribution=True  # Opt-in to share
)
```

### Privacy & Anonymization

- **Data Shared**: Query patterns, field names, success rates
- **Data NOT Shared**: Actual IPs, hostnames, usernames, logs
- **Control**: Opt-in only, can disable anytime

---

## Troubleshooting

### Common Issues

#### 1. "Connection Failed" Error

**Problem**: Cannot connect to Elastic cluster

**Solutions**:
```bash
# Verify credentials
curl -u username:password https://your-elastic:9200

# Check Cloud ID format
echo $ELASTIC_CLOUD_ID | base64 -d

# Test API key
curl -H "Authorization: ApiKey $ELASTIC_API_KEY" https://your-elastic:9200
```

---

#### 2. "No Results Found" Error

**Problem**: Query returns empty results

**Solutions**:
- Verify data exists in Elastic
- Check index patterns (logs-*, filebeat-*, etc.)
- Expand time range
- Use wildcard queries for partial matches

```python
# Instead of exact match
'query_value': '*************'  # Might not match

# Use wildcard
'query_value': '192.168'  # Matches any IP in subnet
```

---

#### 3. "Permission Denied" Error

**Problem**: API key lacks required permissions

**Required Permissions**:
- `read` on indices (logs-*, filebeat-*, winlogbeat-*)
- `monitor` for cluster health
- `view_index_metadata` for mappings

**Fix**: Update API key permissions in Kibana:
```
Stack Management → Security → API Keys → Edit
Add indices: logs-*, filebeat-*, winlogbeat-*
Privileges: read, monitor, view_index_metadata
```

---

#### 4. Slow Query Performance

**Problem**: Queries taking >10 seconds

**Solutions**:
- Reduce time range
- Use specific indices instead of wildcards
- Enable query caching
- Check Elastic cluster health

```python
# Instead of 30 days
'time_range_hours': 720

# Use 24 hours
'time_range_hours': 24
```

---

## Advanced Features

### 1. Custom Index Patterns

Override default index patterns:

```python
context_plugin = ElasticContextPlugin({
    ...
    'custom_indices': {
        'ASSET': ['logs-endpoint-*'],
        'DETECTION': ['logs-security_detection-*'],
        'NETWORK': ['logs-network-*']
    }
})
```

### 2. Query Result Caching

Enable caching for repeated queries:

```python
# First query: Hits Elastic (2 seconds)
results1 = await context_manager.pull_context(query)

# Second query (same params): From cache (<100ms)
results2 = await context_manager.pull_context(query)
```

### 3. AI-Powered Query Generation

Let AI build optimal Elastic queries:

```python
from ai_query_builder import AIQueryBuilder

query_builder = AIQueryBuilder(redis_client, logger)

# AI generates best Elasticsearch query
elastic_query = await query_builder.build_query(
    vendor_name='elastic',
    query_type='hostname',
    query_value='WORKSTATION',
    category='ASSET'
)
```

---

## Integration Examples

### Integrate with SOAR Platforms

```python
# Phantom/Splunk SOAR
def investigate_ip(ip_address):
    query = {
        'request_id': str(uuid4()),
        'query': {
            'query_type': 'ip',
            'query_value': ip_address,
            'categories': ['ASSET', 'DETECTION', 'NETWORK'],
            'time_range_hours': 24
        }
    }

    redis_client.publish('ingestion.pull_context', json.dumps(query))
    # Wait for response and enrich incident
```

### Integrate with Case Management

```python
# TheHive Integration
def enrich_case(case_id, hostname):
    # Pull Elastic context
    context = pull_elastic_context(hostname)

    # Add observables to case
    for detection in context['detections']:
        thehive.create_observable(
            case_id=case_id,
            type='detection',
            value=detection['rule_name'],
            tags=['elastic', 'automated']
        )
```

---

## Performance Metrics

### Expected Performance

- **Query Latency**: 500ms - 3s (depending on time range)
- **Result Caching**: <100ms for cached queries
- **Max Results**: 1000 documents per query
- **Concurrent Queries**: 10+ simultaneous

### Optimization Tips

1. **Use Specific Time Ranges**: Smaller = faster
2. **Enable Caching**: 90%+ cache hit rate achievable
3. **Limit Categories**: Request only needed data
4. **Monitor Elastic Health**: Ensure cluster is healthy

---

## API Changelog

### Version 1.0.0 (October 2025)
- Initial release
- Support for Elastic Cloud and self-hosted
- Four context categories (ASSET, DETECTION, NETWORK, LOG)
- Six query types (ip, hostname, user, hash, domain, process)
- AI-powered query generation integration
- Community pattern sharing

---

## Support & Resources

- **Documentation**: [SIEMLess Docs](https://github.com/siemless/siemless-v2)
- **Elastic ECS Reference**: https://www.elastic.co/guide/en/ecs/current/
- **Issues**: [GitHub Issues](https://github.com/siemless/siemless-v2/issues)
- **Community**: Slack #siemless-elastic

---

**Last Updated**: October 2, 2025
**Maintainer**: SIEMLess Core Team
**License**: MIT
