#!/usr/bin/env python3
"""
Quick test to show endpoint status
"""

import requests
import json

BASE_URL = "http://localhost:8002"

endpoints = [
    ("GET", "/health", None, "Health Check"),
    ("GET", "/api/log-sources/status", None, "Get Log Source Status"),
    ("POST", "/api/log-sources/register", {"name": "TestEDR3", "type": "endpoint", "product": "test", "capabilities": []}, "Register Log Source"),
    ("POST", "/api/detection/fidelity", {"attack_types": ["ransomware"]}, "Calculate Detection Fidelity"),
    ("GET", "/api/detection/coverage", None, "Get Detection Coverage"),
    ("POST", "/api/detection/technique-coverage", {"techniques": ["T1055"]}, "Check MITRE Technique Coverage"),
    ("GET", "/api/correlation/capability", None, "Assess Correlation Capability"),
    ("POST", "/api/correlation/requirements", {"attack_type": "ransomware"}, "Check Correlation Requirements"),
    ("POST", "/api/correlation/recommendations", {"target_attacks": ["ransomware"]}, "Get Source Recommendations"),
    ("GET", "/api/coverage/gaps", None, "Analyze Coverage Gaps"),
    ("POST", "/api/coverage/simulate", {"add_sources": [{"name": "Test", "category": "test", "tier": "BRONZE"}]}, "Simulate Coverage"),
]

print("\n" + "="*60)
print("LOG SOURCE QUALITY API - ENDPOINT STATUS TEST")
print("="*60)

working = 0
failing = 0

for method, path, data, name in endpoints:
    try:
        if method == "GET":
            resp = requests.get(f"{BASE_URL}{path}", timeout=5)
        else:
            resp = requests.post(f"{BASE_URL}{path}", json=data, timeout=5)

        if resp.status_code in [200, 201]:
            print(f"[OK] {name:<40} [WORKING]")
            working += 1
        else:
            print(f"[X] {name:<40} [FAILED - {resp.status_code}]")
            failing += 1
            if resp.status_code == 500:
                try:
                    error = resp.json().get('error', 'Unknown error')
                    print(f"  Error: {error[:60]}...")
                except:
                    pass
    except Exception as e:
        print(f"[X] {name:<40} [ERROR - {str(e)[:30]}]")
        failing += 1

print("\n" + "-"*60)
print(f"SUMMARY: {working}/11 endpoints working ({working/11*100:.1f}% success rate)")
print(f"         {failing} endpoints need fixes")
print("="*60)