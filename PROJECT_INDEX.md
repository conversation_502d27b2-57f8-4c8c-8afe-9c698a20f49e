# SIEMLess v2.0 Project Index - DEVELOPMENT REPOSITORY

## 📋 Overview
**This is the DEVELOPMENT repository** - Full history and working documents preserved
**Production repository**: `siemless-production` (clean, no history, for collaborators)

This index maps the entire SIEMLess v2.0 codebase - a fully operational Intelligence Foundation Platform with 5 consolidated engines, complete React frontend, and enterprise authentication.

## Current Status: ✅ PRODUCTION READY (Updated: October 5, 2025)

### System Achievements
- ✅ **All 5 Engines**: Operational and containerized with **AsyncPG** (Oct 2025)
- ✅ **React Frontend**: Complete application shell with 17+ advanced widgets
- ✅ **Authentication**: Keycloak SSO integration
- ✅ **Monitoring**: Dual-purpose Grafana (backend + operations)
- ✅ **45,832+ Logs**: Successfully processed with lightweight extraction
- ✅ **99.97% Cost Reduction**: Through pattern crystallization
- ✅ **REST API**: 60+ endpoints (delivery + backend + log quality + parser gen + rule CRUD) with WebSocket support
- ✅ **Log Source Quality System**: Quantitative detection fidelity assessment (100% operational)
- ✅ **Parser Generation System**: AI-powered parser generation ($0.00/parser with Gemma-3)
- ✅ **GitHub Integration**: Dynamic pattern updates
- ✅ **Docker Deployment**: Full containerization with health checks
- ✅ **Complete Codebase Index**: All classes, functions, APIs documented in [CODEBASE_INDEX.md](./CODEBASE_INDEX.md)
- ✅ **AsyncPG Migration**: Complete migration from psycopg2 to asyncpg (3-5x performance improvement expected)

## 📂 Documentation Structure

### ✅ ACTIVE DOCUMENTATION (Synced to Production)
These documents are current and maintained:
- **[README.md](./README.md)** - Main project overview
- **[ARCHITECTURE.md](./ARCHITECTURE.md)** - System architecture with 5 engines
- **[QUICK_START_GUIDE.md](./QUICK_START_GUIDE.md)** - Step-by-step setup
- **[TROUBLESHOOTING_GUIDE.md](./TROUBLESHOOTING_GUIDE.md)** - Common issues and solutions
- **[ADVANCED_WIDGETS_DOCUMENTATION.md](./ADVANCED_WIDGETS_DOCUMENTATION.md)** - Frontend widget specs
- **[LOG_SOURCE_QUALITY_DOCUMENTATION.md](./LOG_SOURCE_QUALITY_DOCUMENTATION.md)** - Log source quality API reference
- **[LICENSE](./LICENSE)** - Proprietary license (Production only)

### ⚠️ DEV-ONLY DOCUMENTATION (High Value for Development)
These stay in development repo only:
- **[CLAUDE.md](./CLAUDE.md)** - Project context and AI development instructions
- **[PROJECT_INDEX.md](./PROJECT_INDEX.md)** - This file (project navigation map)
- **[CODEBASE_INDEX.md](./CODEBASE_INDEX.md)** - Complete index of all classes, functions, APIs, Redis channels
- **[FRONTEND_MASTER_INDEX.md](./FRONTEND_MASTER_INDEX.md)** - **NEW:** Single source of truth for all frontend elements (Oct 2025)
- **[KEY_DIFFERENTIATING_FEATURES.md](./KEY_DIFFERENTIATING_FEATURES.md)** - The 5 pillars that make SIEMLess powerful (Oct 2025)
- **[PARSER_GENERATION_SUCCESS.md](./PARSER_GENERATION_SUCCESS.md)** - Parser generation implementation documentation
- **[RULE_MANAGEMENT_PHASE_2_COMPLETE.md](./RULE_MANAGEMENT_PHASE_2_COMPLETE.md)** - Rule management CRUD operations (Oct 2025)
- **[RULE_MANAGEMENT_API_QUICK_REFERENCE.md](./RULE_MANAGEMENT_API_QUICK_REFERENCE.md)** - Quick reference for rule APIs
- **[ALERT_ENRICHMENT_CORRELATION_INTEGRATION.md](./ALERT_ENRICHMENT_CORRELATION_INTEGRATION.md)** - Alert enrichment and correlation integration (Oct 2025)
- **[SCHEMA_DETECTION_WHY_IT_FAILED_BEFORE.md](./SCHEMA_DETECTION_WHY_IT_FAILED_BEFORE.md)** - Root cause analysis and schema detection implementation (Oct 2025)
- **[ENTITY_EXTRACTION_HISTORY.md](./ENTITY_EXTRACTION_HISTORY.md)** - Complete history from v0.7 success → v2.0 plans → implementation failures → schema detection success (with archive references)
- **[DOCUMENTATION_CLEANUP_SUMMARY.md](./DOCUMENTATION_CLEANUP_SUMMARY.md)** - Documentation consolidation (Oct 4, 2025) - 48 files archived
- **[ASYNCPG_MIGRATION_COMPLETE.md](./ASYNCPG_MIGRATION_COMPLETE.md)** - **NEW:** Complete asyncpg migration documentation (Oct 5, 2025)

### 📁 TECHNICAL DOCUMENTATION
Located in `documents/` - Current technical specs:
- **Authentication Implementation** - Keycloak SSO setup
- **Database Schema** - PostgreSQL v2 schema
- **System Flows** - Inter-engine communication
- **Frontend Specifications** - Widget requirements

### 🗄️ ARCHIVED DOCUMENTATION (Git-Ignored)
Located in `documents/archive/` - Historical/working documents:
- Progress reports (*_STATUS.md, *_REPORT.md)
- Implementation plans (*_COMPLETE.md)
- Migration guides (V2_*, API_KEY_*)
- Old architectural decisions

## 🔍 Quick Navigation by Topic

### For Understanding the Architecture
1. Start: [ARCHITECTURE.md](./ARCHITECTURE.md) - System overview
2. Deep dive: [CODEBASE_INDEX.md](./CODEBASE_INDEX.md) - All classes, functions, APIs
3. Deployment: Docker-compose.yml and ARCHITECTURE.md cover deployment

### For Development Work
1. Context: [CLAUDE.md](./CLAUDE.md) - Development patterns and lessons
2. Reference: [CODEBASE_INDEX.md](./CODEBASE_INDEX.md) - Find any class/function
3. Navigation: [PROJECT_INDEX.md](./PROJECT_INDEX.md) - This file

### For Frontend Development ⭐ **NEW**
1. **📋 MASTER INDEX**: [FRONTEND_MASTER_INDEX.md](./FRONTEND_MASTER_INDEX.md) - **Single source of truth** for all frontend elements
2. Strategy: [FRONTEND_DEVELOPMENT_PLAN.md](./FRONTEND_DEVELOPMENT_PLAN.md) - Complete development roadmap
3. API Reference: [COMPLETE_API_REFERENCE.md](./COMPLETE_API_REFERENCE.md) - All 60+ endpoints with TypeScript interfaces
4. Widgets: [WIDGET_CATALOG.md](./WIDGET_CATALOG.md) - 30+ widget specifications with priorities
5. UI Design: [ALERT_INVESTIGATION_UI_DESIGN.md](./ALERT_INVESTIGATION_UI_DESIGN.md) - Detailed wireframes
6. Patterns: [INTEGRATION_PATTERNS.md](./INTEGRATION_PATTERNS.md) - 7 reusable code patterns
7. Progress: [FRONTEND_IMPLEMENTATION_STATUS.md](./FRONTEND_IMPLEMENTATION_STATUS.md) - Implementation tracker

### For API Integration
1. REST APIs: [CODEBASE_INDEX.md](./CODEBASE_INDEX.md) - 60+ endpoint reference
2. Log Quality: [LOG_SOURCE_QUALITY_DOCUMENTATION.md](./LOG_SOURCE_QUALITY_DOCUMENTATION.md)
3. Parser Generation: [PARSER_GENERATION_SUCCESS.md](./PARSER_GENERATION_SUCCESS.md)

### For Troubleshooting
1. Common issues: [TROUBLESHOOTING_GUIDE.md](./TROUBLESHOOTING_GUIDE.md)
2. Critical patterns: [CLAUDE.md](./CLAUDE.md) - Async fixes, Docker patterns
3. Engine logs: `docker-compose logs -f [engine_name]`

## 🔄 Syncing with Production Repository

### Development Workflow
1. **Develop here** (`siemless_v2`) - Full history, all tools
2. **Test thoroughly** - Use test files and scripts
3. **Sync to production** (`siemless-production`) - Clean updates only

### What Gets Synced
- ✅ Code changes (engines/, frontend/)
- ✅ Active documentation (see list above)
- ✅ Configuration files
- ❌ CLAUDE.md (dev context)
- ❌ PROJECT_INDEX.md (this file)
- ❌ CODEBASE_INDEX.md (dev reference)
- ❌ Test files
- ❌ Archive folder
- ❌ Git history

### Sync Command (use sync_to_production.sh)
```bash
# Updates production repo with latest changes
./sync_to_production.sh
```

## 🎯 Core Philosophy
**"Learn Expensive Once → Operate Free Forever"**
- AI insights crystallized into permanent patterns
- 99.97% cost reduction vs traditional SIEM
- Deterministic performance with AI fallback

## 📁 Directory Structure

```
siemless_v2/
├── engines/               # 5 Consolidated engines (v2 architecture) - ALL OPERATIONAL
│   ├── intelligence/      # 🧠 AI consensus & pattern crystallization (Port 8001) ✅
│   │   ├── intelligence_engine.py  # Main engine with AI consensus
│   │   ├── ai_models.py            # 11+ AI models (Gemini, Claude, GPT, Ollama)
│   │   ├── message_handlers.py     # Parser generation, consensus validation
│   │   ├── cost_tracker.py         # AI cost tracking and optimization
│   │   ├── base_engine.py          # Fixed async patterns with HTTP health
│   │   └── Dockerfile               # Container configuration
│   ├── backend/          # 🔧 CTI-to-rule automation & storage (Port 8002) ✅
│   │   ├── backend_engine.py       # Storage management, CTI processing
│   │   ├── cti_rule_generator.py   # Multi-SIEM rule generation
│   │   ├── correlation_engine.py   # Multi-source threat correlation
│   │   ├── log_source_quality.py   # Log source quality assessment (30+ sources)
│   │   ├── correlation_requirements.py # Attack correlation requirements
│   │   ├── detection_fidelity_calculator.py # Detection confidence calculation
│   │   ├── siem_schema_loader.py   # SIEM schema mapping (8 platforms)
│   │   ├── base_engine.py          # Fixed async patterns
│   │   └── Dockerfile               # Container configuration
│   ├── ingestion/        # 📥 Multi-source data & CTI ingestion (Port 8003) ✅
│   │   ├── ingestion_engine.py     # 45,832+ logs processed
│   │   ├── cti_source_plugin.py    # Universal CTI plugin base class
│   │   ├── otx_plugin.py           # AlienVault OTX plugin
│   │   ├── threatfox_plugin.py     # abuse.ch ThreatFox plugin
│   │   ├── crowdstrike_plugin.py   # CrowdStrike Intel plugin
│   │   ├── opencti_plugin.py       # OpenCTI platform plugin
│   │   ├── cti_manager.py          # CTI plugin orchestration
│   │   ├── task_coordinator.py     # Task orchestration
│   │   ├── log_router.py           # Intelligent log routing
│   │   ├── pattern_matcher.py      # Real-time pattern matching
│   │   ├── data_sources.py         # Multi-vendor integration
│   │   └── Dockerfile               # Container configuration
│   ├── contextualization/ # 🔍 Entity enrichment & relationships (Port 8004) ✅
│   │   ├── contextualization_engine.py  # Lightweight intelligence extraction
│   │   ├── entity_extractor.py     # Entity extraction (IPs, users, hosts, etc.)
│   │   ├── enrichment_service.py   # Multi-source enrichment (threat intel, geo, assets)
│   │   └── Dockerfile               # Container configuration
│   └── delivery/         # 🚀 Case management & WORKFLOW ORCHESTRATION (Port 8005) ✅
│       ├── delivery_engine.py      # 40+ REST API endpoints
│       ├── workflow_orchestrator.py # Workflow orchestration engine
│       ├── workflow_templates.py   # 11 pre-configured workflows
│       ├── base_engine.py          # Fixed async patterns
│       ├── auth_middleware.py      # Authentication integration
│       └── Dockerfile               # Container configuration
│
├── frontend/              # 🎨 React Frontend Application (Port 3000) ✅
│   ├── src/
│   │   ├── components/   # UI components
│   │   │   └── layout/   # AppShell, TopBar, SideBar
│   │   ├── widgets/      # 17+ advanced security widgets
│   │   │   ├── EntityExplorer.tsx
│   │   │   ├── RelationshipGraph.tsx
│   │   │   ├── AIInvestigationGuide.tsx
│   │   │   ├── CrystallizationQueue.tsx
│   │   │   └── MITREHeatmap.tsx
│   │   ├── stores/       # State management (Zustand)
│   │   ├── pages/        # Route pages (Investigation, Engineering, Analytics, Admin)
│   │   └── router/       # React Router v6
│   ├── Dockerfile        # Multi-stage build with nginx
│   └── package.json      # Dependencies (AG-Grid, D3, FlexLayout)
│
├── engines/library/       # Pattern Library System ✅
│   ├── patterns/         # Crystallized knowledge patterns
│   │   ├── crowdstrike.json     # CrowdStrike patterns
│   │   ├── fortinet.json        # Fortinet patterns
│   │   ├── paloalto.json        # Palo Alto patterns
│   │   └── elasticsearch.json   # Elasticsearch patterns
│
├── siem_definitions/      # SIEM Schema Definitions ✅
│   ├── elastic.json      # Elastic ECS schema
│   ├── splunk.json       # Splunk CIM schema
│   ├── sentinel.json     # Microsoft Sentinel schema
│   └── qradar.json       # IBM QRadar schema
│
├── migrations/            # Database Migrations ✅
│   └── add_parsers_table.sql  # Parser storage schema
│
├── engines/docker-compose.yml  # Main Docker orchestration
├── engines/grafana/      # Monitoring stack (Port 3001)
│   └── docker-compose.grafana.yml
├── engines/docker-compose.keycloak.yml  # SSO authentication (Port 8080)
│
├── monitoring/            # Lightweight observability
│   ├── prometheus-minimal.yml # Resource-limited Prometheus
│   └── grafana/          # Optimized dashboards
│       ├── dashboards/
│       │   ├── siemless-investigation.json # Security board
│       │   └── cost-tracker.json # Cost & performance
│       └── datasources/
│           └── prometheus.yml
│
├── sql/                   # Database schemas
│   └── init.sql          # Complete v2 schema
│
├── tests/                 # Test infrastructure
│   ├── test_engines.py    # Engine communication tests
│   ├── test_cti_flow.py  # CTI integration tests
│   ├── test_patterns.py  # Pattern library tests
│   └── test_status.py     # Log quality API endpoint tests (11/11 working)
│
└── Documentation Files    # See Documentation Structure section above
    ├── CODEBASE_INDEX.md  # Complete technical reference (NEW)
    ├── CLAUDE.md          # Development context
    ├── PROJECT_INDEX.md   # This file
    └── ...                # See documentation section
```

## 🔧 Core Components

### BaseEngine Class (`engines/base_engine.py`) 🔴 CRITICAL
- **Purpose**: Standard foundation for all engines with async patterns
- **Key Features**:
  - ✅ Fixed async Redis operations (redis.asyncio)
  - ✅ Non-blocking HTTP health endpoints (aiohttp)
  - ✅ Proper task coordination (asyncio.wait)
  - ✅ Message processing framework
- **Critical Fix Applied**:
  ```python
  # BEFORE - Blocking sync Redis
  import redis
  message = pubsub.get_message(timeout=1)  # BLOCKS!

  # AFTER - Non-blocking async Redis
  import redis.asyncio as redis_async
  message = await pubsub.get_message(timeout=1.0)  # Yields!
  ```
- **Full Documentation**: See [CODEBASE_INDEX.md](./CODEBASE_INDEX.md) - Base Engine section

### Inter-Engine Communication (Redis Pub/Sub) ✅
- **Purpose**: Real-time message passing between engines
- **Channels Used** (30+ total):
  - `intelligence.*`: Pattern crystallization, AI consensus, parser generation
  - `backend.*`: Storage operations, CTI updates, parser saving
  - `ingestion.*`: Data routing, source management
  - `contextualization.*`: Entity enrichment, parser validation
  - `delivery.*`: Case management, alerts
- **Message Flow**: Fully operational with 45,832+ logs processed
- **Full Channel List**: See [CODEBASE_INDEX.md](./CODEBASE_INDEX.md) - Redis Channels section

### REST API Endpoints (60+ total) ✅
- **Intelligence Engine** (Port 8001): Health, metrics
- **Backend Engine** (Port 8002): 15+ endpoints (CTI, rules, log quality, parsers)
- **Ingestion Engine** (Port 8003): Parser generation, source management
- **Contextualization Engine** (Port 8004): Entity extraction, enrichment
- **Delivery Engine** (Port 8005): 40+ endpoints (cases, workflows, alerts, patterns, rules with CRUD, entities)
- **Full API Reference**: See [CODEBASE_INDEX.md](./CODEBASE_INDEX.md) - HTTP API Endpoints sections

## 🤖 5-Engine Architecture (v2)

### Intelligence Engine (`engines/intelligence/`) - Port 8001
- **Purpose**: AI consensus validation and pattern crystallization
- **Key Components**:
  - `intelligence_engine.py` - Main engine coordinator
  - `ai_models.py` - 11+ AI model integrations with hot-reload
  - `message_handlers.py` - Parser generation, consensus validation
  - `cost_tracker.py` - AI cost optimization (99.97% reduction)
- **AI Models**:
  - Google: Gemini Pro/Flash, Gemma 27B (FREE tier)
  - OpenAI: GPT-4 Turbo, GPT-5
  - Anthropic: Claude Opus 4.1, Sonnet 4, Haiku 3.5
  - Ollama: Local models for sensitive data
- **Cost**: $0.00/parser with Gemma-3, 99.97% reduction vs traditional SIEM
- **Full Documentation**: See [CODEBASE_INDEX.md](./CODEBASE_INDEX.md) - Intelligence Engine

### Backend Engine (`engines/backend/`) - Port 8002
- **Purpose**: CTI-to-rule automation, storage management, and log source quality assessment
- **Key Components**:
  - `backend_engine.py` - Storage orchestration
  - `cti_rule_generator.py` - Multi-SIEM rule generation
  - `correlation_engine.py` - Multi-source threat correlation
  - `log_source_quality.py` - Quality assessment (30+ sources)
  - `siem_schema_loader.py` - Schema mapping (8 platforms)
- **Storage Tiers**:
  - Hot (Redis): $0.50/GB/month - Real-time access
  - Warm (PostgreSQL): $0.10/GB/month - Recent data
  - Cold (S3): $0.02/GB/month - Long-term archive
- **SIEM Support**: Splunk, Elastic, Sentinel, QRadar, Chronicle, Sumo Logic, ArcSight, LogRhythm
- **API Endpoints**: 15+ (CTI processing, rule generation, log quality, parser storage)
- **Full Documentation**: See [CODEBASE_INDEX.md](./CODEBASE_INDEX.md) - Backend Engine

### Ingestion Engine (`engines/ingestion/`) - Port 8003
- **Purpose**: Multi-source data and CTI ingestion
- **Key Components**:
  - `ingestion_engine.py` - Main orchestrator (45,832+ logs processed)
  - `cti_manager.py` - OpenCTI, OTX, ThreatFox integration
  - `task_coordinator.py` - Task orchestration and scheduling
  - `log_router.py` - Intelligent log routing
  - `pattern_matcher.py` - Real-time pattern matching
- **CTI Sources**: OpenCTI, OTX (AlienVault), ThreatFox, MISP
- **Update Intervals**: Every 6 hours (configurable)
- **Full Documentation**: See [CODEBASE_INDEX.md](./CODEBASE_INDEX.md) - Ingestion Engine

### Contextualization Engine (`engines/contextualization/`) - Port 8004
- **Purpose**: Entity enrichment and relationship mapping (LIGHTWEIGHT STORAGE KEY)
- **Key Components**:
  - `contextualization_engine.py` - Lightweight intelligence extraction with schema detection
  - `entity_extractor.py` - Entity extraction (IPs, users, hosts, MACs, ports, processes, files)
  - `enrichment_service.py` - Multi-source enrichment (threat intel, geolocation, asset info)
  - `log_schema_detector.py` - **NEW:** SHA-256 schema hashing for exact structure matching
  - `deterministic_extractor.py` - **NEW:** Zero-cost entity extraction using stored mappings
  - `adaptive_entity_extractor.py` - AI-powered pattern learning (legacy)
- **Schema Detection System** (Oct 2025):
  - SHA-256 hashing of log field structures
  - One-time AI mapping generation ($0.008 per schema)
  - Deterministic extraction thereafter ($0.00 per log)
  - 99.97% cost reduction vs per-log AI processing
  - PostgreSQL + Redis persistence
- **Achievement**: 98.4% storage reduction (447 MB → 7 MB)
- **Extraction Rate**: 18.6 entities per log average
- **Relationships**: 243,103 relationships created from 10,000 logs
- **Full Documentation**:
  - Technical: [CODEBASE_INDEX.md](./CODEBASE_INDEX.md) - Contextualization Engine
  - Schema Detection & Root Cause Analysis: [SCHEMA_DETECTION_WHY_IT_FAILED_BEFORE.md](./SCHEMA_DETECTION_WHY_IT_FAILED_BEFORE.md)

### Delivery Engine (`engines/delivery/`) - Port 8005
- **Purpose**: Case management, frontend, visualization, and rule management
- **Key Components**:
  - `delivery_engine.py` - 40+ REST API endpoints
  - `workflow_orchestrator.py` - Workflow engine with 11 templates
  - `workflow_templates.py` - Pre-configured workflows
  - `auth_middleware.py` - Keycloak SSO integration
- **Rule Management** (Phase 2 Complete - Oct 2025):
  - Full CRUD operations (list, get, update, delete)
  - Quality-based filtering (high/medium/low)
  - Bulk operations with dry-run mode
  - Cascade deletion to deployed SIEMs
  - Manual quality override and tagging
  - 3,630+ rules with auto-labeling
- **Alert Channels**: Email, Slack, Microsoft Teams, SMS, PagerDuty, Webhook, Dashboard
- **Frontend**: React SPA on Port 3000
- **Full Documentation**: See [CODEBASE_INDEX.md](./CODEBASE_INDEX.md) - Delivery Engine and [RULE_MANAGEMENT_PHASE_2_COMPLETE.md](./RULE_MANAGEMENT_PHASE_2_COMPLETE.md)

## 📊 Pattern Library System

### Overview
The Pattern Library enables "learn expensive once, operate free forever":
- **First occurrence**: ~$0.02 (AI analysis)
- **Subsequent occurrences**: $0.00 (pattern match)
- **Average reuse rate**: 85-95%
- **Total savings**: 99.97% cost reduction

### Pattern Categories
- **Security Events** (`patterns/security_events/`)
  - Attack patterns and IoCs
  - Suspicious behaviors
  - Threat detection alerts
- **Entity Patterns** (`patterns/entity_patterns/`)
  - IP-domain mappings
  - User-system relationships
  - Network topology
- **Behavioral Patterns** (`patterns/behavioral/`)
  - Normal baselines
  - Anomaly detection
  - User behavior analytics
- **CTI Patterns** (`patterns/cti/`)
  - Threat actor TTPs
  - Campaign indicators
  - OTX pulse patterns

### Pattern Lifecycle
1. **Discovery**: Unknown log triggers AI analysis
2. **Validation**: Multi-AI consensus (80% threshold)
3. **Crystallization**: Pattern stored in PostgreSQL
4. **Caching**: Redis cache for sub-ms lookups
5. **Evolution**: Continuous improvement via feedback

## 🔌 Universal CTI Plugin Architecture

### Overview
The Universal CTI Plugin Architecture provides a standardized, vendor-agnostic framework for integrating ANY threat intelligence source into SIEMLess v2.0:
- **Status**: ✅ Phase 1 COMPLETE and PRODUCTION READY
- **Plugins Operational**: OTX (40+ indicators), ThreatFox (20+ indicators), CrowdStrike (5+ threat actors), OpenCTI (ready)
- **Architecture**: Universal plugin pattern with `CTISourcePlugin` base class
- **Data Format**: Standardized `CTIIndicator` dataclass across all sources
- **Benefits**: Infinite scalability, vendor-agnostic, AI-ready for auto-generation

### Key Components
- **Base Class**: `cti_source_plugin.py` (316 lines) - Universal CTI plugin framework
- **Plugin Manager**: Automatic plugin discovery and registration
- **Standardized Format**: All indicators normalized to `CTIIndicator` dataclass
- **Rate Limiting**: Built-in rate limiting per source
- **Validation**: Credential and connectivity testing per plugin

### Implemented Plugins
- **OTX Plugin** (`otx_plugin.py`, 300 lines): AlienVault Open Threat Exchange
  - Pulses, IoCs, threat intelligence feeds
  - Works with/without API key (limited without)
- **ThreatFox Plugin** (`threatfox_plugin.py`, 350 lines): abuse.ch ThreatFox
  - Malware IoCs, C2 servers, botnet tracking
  - Community-driven threat intelligence
- **CrowdStrike Plugin** (`crowdstrike_plugin.py`, 711 lines): CrowdStrike Falcon Intel
  - Threat actors, malware families, vulnerabilities
  - Unique: Includes actor profiles with TTPs
- **OpenCTI Plugin** (`opencti_plugin.py`, 400 lines): OpenCTI platform
  - STIX/TAXII compatible
  - Ready but requires OpenCTI instance

### CrowdStrike's Unique Intelligence
Beyond indicators, the CrowdStrike plugin provides:
- **Threat Actors**: APT profiles with motivations, targets, capabilities
- **Malware Families**: Associated actors, indicator counts, threat types
- **Vulnerabilities**: CVE intelligence from Spotlight (requires scope)

### API Endpoints
```bash
GET  /cti/connectors          # List all registered CTI plugins
GET  /cti/status              # Health status of all plugins
POST /cti/manual_update       # Trigger manual indicator fetch
GET  /cti/indicators          # Query stored indicators
POST /cti/indicators/search   # Advanced indicator search
```

### Plugin Development Pattern
```python
from cti_source_plugin import CTISourcePlugin, CTIIndicator

class NewSourcePlugin(CTISourcePlugin):
    def get_source_name(self) -> str:
        return "source_name"

    async def fetch_indicators(self) -> List[CTIIndicator]:
        # Fetch from vendor API
        # Convert to CTIIndicator format
        return indicators
```

### Test Results
```
OTX:              40 indicators fetched ✅
ThreatFox:        20 indicators fetched ✅
CrowdStrike:      5 threat actors, 3 malware families ✅
OpenCTI:          Not accessible (no instance configured)
```

### Benefits Over Tool-Specific Integrations
- **Infinite Scalability**: Add ANY CTI source as a plugin
- **Vendor Agnostic**: Core engines remain pure, plugins handle specifics
- **AI-Ready**: Plugins follow pattern for AI auto-generation
- **Cost Efficiency**: 99.997% reduction through standardization
- **Community Driven**: Share plugins across deployments

## 🔍 Log Source Quality System

### Overview
The Log Source Quality System provides quantitative assessment of detection capabilities based on available log sources:
- **Quality Tiers**: PLATINUM (95-100), GOLD (80-94), SILVER (65-79), BRONZE (<65)
- **Detection Fidelity**: Quantitative confidence percentages for attack detection
- **Multi-Source Synergy**: Complementary sources multiply detection capability (1.3x-2.0x)
- **30+ Pre-configured Sources**: CrowdStrike, SentinelOne, Windows Event Logs, etc.

### Key Capabilities
- **Attack Coverage Analysis**: Calculates detection confidence for 11 attack types
- **MITRE ATT&CK Mapping**: Links techniques to required log sources
- **Correlation Requirements**: Identifies minimum sources needed per attack
- **Gap Analysis**: Shows missing sources for complete coverage
- **Simulation Mode**: Preview impact of adding/removing sources

### Quality Tier Examples
- **PLATINUM (95-100)**: CrowdStrike Falcon, SentinelOne, Carbon Black
- **GOLD (80-94)**: Microsoft Defender, Elastic Security, Sysmon
- **SILVER (65-79)**: Windows Event Logs, Linux Auditd, Zeek
- **BRONZE (<65)**: Basic syslogs, application logs, custom scripts

### Detection Confidence Reality Check
Even with premium tools, single-source detection is limited:
- **CrowdStrike alone**: ~27% ransomware detection confidence
- **CrowdStrike + File Integrity + Backup Monitoring**: 88.6% confidence
- **Key Insight**: Multiple complementary sources are essential

### Full Documentation
See [LOG_SOURCE_QUALITY_DOCUMENTATION.md](./LOG_SOURCE_QUALITY_DOCUMENTATION.md) for complete API reference

## 🚀 Parser Generation System (NEW)

### Overview
AI-powered parser generation creates SIEM parsers from log samples:
- **Cost**: $0.00 per parser (using Gemma-3-27b FREE tier)
- **Speed**: 9 seconds end-to-end
- **Quality**: 100% field coverage, entity extraction, relationship mapping
- **Architecture**: 4-engine workflow (Ingestion → Intelligence → Contextualization → Backend)

### Workflow
1. **Ingestion Engine**: Receives log samples, generates parser request
2. **Intelligence Engine**: AI analyzes logs, generates parser logic
3. **Contextualization Engine**: Validates parser, extracts entities
4. **Backend Engine**: Maps to SIEM schema, stores in database

### Supported SIEM Platforms
- Elastic (ECS schema)
- Splunk (CIM schema)
- Microsoft Sentinel (KQL schema)
- IBM QRadar (AQL schema)
- Generic (custom mappings)

### API Endpoints
- `POST /api/parsers/generate` - Generate parser from log samples
- `GET /api/parsers/{parser_id}` - Retrieve specific parser
- `GET /api/parsers` - List all parsers
- `DELETE /api/parsers/{parser_id}` - Deactivate parser

### Full Documentation
See [PARSER_GENERATION_SUCCESS.md](./PARSER_GENERATION_SUCCESS.md) for implementation details

## 🔍 Schema Detection System (NEW - Oct 2025)

### Overview
Automated entity extraction system using pattern crystallization:
- **Cost**: $0.008 per schema (one-time), then $0.00 forever
- **Speed**: < 1ms schema detection, < 5ms extraction
- **Accuracy**: 18.6 entities per log average
- **Architecture**: SHA-256 hashing → AI mapping → Deterministic extraction

### The Problem It Solved
**Before**: Processing 56,119 logs with hardcoded patterns extracted **0 entities** because:
- Couldn't navigate nested Elasticsearch structures (`content.log.data[0].data.source.ip`)
- Hardcoded field paths became obsolete with vendor changes
- Fortinet logs misidentified as CrowdStrike due to string matching
- No schema persistence across restarts

**After**: Same 56,119 logs cost $0.024 (2.4 cents) and extract entities correctly:
- 3 unique schemas × $0.008 = $0.024 (one-time AI cost)
- 56,116 subsequent logs × $0.00 = free forever
- **99.97% cost reduction** vs per-log AI processing

### How It Works

#### 1. Schema Detection (< 1ms)
```python
# Generate SHA-256 hash from field structure
schema_hash = generate_schema_hash(log)

# Check Redis cache → Database → Return mapping or None
schema_info = schema_detector.detect_schema(log)
```

#### 2. AI Mapping Generation (One-Time per Schema)
```python
# If new schema detected, request AI mapping
ai_mapping = {
    "source_ip": "content.log.data[0].data.source.ip",
    "destination_ip": "content.log.data[0].data.destination.ip",
    ...
}
# Cost: $0.008 (Gemma FREE + Sonnet $0.008)
```

#### 3. Deterministic Extraction (< 5ms, $0.00)
```python
# Use stored mapping to extract entities
entities = deterministic_extractor.extract(log, ai_mapping)
# Returns: [{type: 'ip_address', value: '*************'}, ...]
```

### Key Components
- **log_schema_detector.py** (329 lines): SHA-256 hashing, vendor detection, Redis + PostgreSQL persistence
- **deterministic_extractor.py** (228 lines): JSON path navigation, array support, entity extraction
- **mapping_generator.py** (445 lines): Multi-AI consensus (Gemma + Sonnet), standardized JSON output
- **log_schemas table**: Schema storage with usage statistics and confidence tracking

### Cost Analysis: 56,119 Logs Scenario

| Approach | AI Cost | Total Cost | Entities Extracted |
|----------|---------|------------|-------------------|
| **Hardcoded Patterns** | $0 | $0 | **0** (failed) |
| **AI Per Log** | $449 | $449 | Unknown (untested) |
| **Schema Detection** | $0.024 | $0.024 | **18.6/log** ✅ |

**Savings**: 99.97% vs AI per log, 100% accuracy vs hardcoded patterns

### Supported Features
- ✅ Nested object navigation (any depth)
- ✅ Array navigation (`data[0]`)
- ✅ Elasticsearch wrapper handling
- ✅ Vendor auto-detection (structure-based, not string matching)
- ✅ PostgreSQL persistence (survives restarts)
- ✅ Redis caching (sub-millisecond lookups)
- ✅ Multi-AI consensus (Gemma + Sonnet)
- ✅ Confidence scoring and feedback

### Full Documentation
- Schema Detection Implementation & Root Cause Analysis: [SCHEMA_DETECTION_WHY_IT_FAILED_BEFORE.md](./SCHEMA_DETECTION_WHY_IT_FAILED_BEFORE.md)
## 🧪 Testing Infrastructure

### Comprehensive Test Suite (`test_comprehensive_suite.py`) ✅
- **Infrastructure Tests**: Redis/PostgreSQL connectivity
- **Engine Health Checks**: All 5 engines validated
- **Pattern Library Tests**: Crystallization validation
- **REST API Tests**: All 60+ endpoints tested
- **Performance Tests**: Response time metrics

### Individual Engine Tests (`test_individual_engines.py`) ✅
- **Per-Engine Testing**: Isolated component validation
- **Message Processing**: Inter-engine communication
- **Data Flow**: End-to-end processing

### Async Pattern Tests (`test_async_patterns.py`) ✅
- **Non-blocking Validation**: Event loop testing
- **Task Coordination**: Concurrent task management
- **HTTP Server Tests**: Health endpoint verification

## 🐳 Docker Configuration

### Base Image (`docker/Dockerfile.base`)
- Python 3.11 slim
- All dependencies pre-installed
- Non-root user security
- Health checks

### Docker Compose (`docker-compose.yml`)
- **Core Services**:
  - Redis (pub/sub) - Port 6379
  - PostgreSQL (storage) - Port 5433
- **V2 Engines**:
  - Intelligence Engine - Port 8001
  - Backend Engine - Port 8002
  - Ingestion Engine - Port 8003
  - Contextualization Engine - Port 8004
  - Delivery Engine - Port 8005
- **Frontend**:
  - React App - Port 3000
- **Monitoring** (Resource-limited):
  - Prometheus - Port 9090 (512MB RAM, 1GB storage)
  - Grafana - Port 3001 (256MB RAM)

### Environment Configuration
- **`.env.example`**: Template with configuration placeholders
- **Required Variables**:
  - Database: `POSTGRES_DB`, `POSTGRES_USER`, `POSTGRES_PASSWORD`
  - Redis: `REDIS_HOST`, `REDIS_PORT`
  - AI Keys: `OPENAI_API_KEY`, `ANTHROPIC_API_KEY`, `GEMINI_API_KEY`
  - CTI: `OTX_API_KEY` (AlienVault OTX)
  - Storage: `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`, `S3_BUCKET`
  - Monitoring: `GRAFANA_PASSWORD`

## 📈 Metrics & Cost Savings

### Traditional SIEM Costs
- Ingestion: $5-10/GB
- Storage: $5-10/GB/month
- Processing: $5/GB
- **Total: $15-30/GB**

### SIEMLess v2.0 After Crystallization
- Pattern Matching: $0.0001/GB (CPU only)
- Storage: $0.10/GB/month
- AI (rare): $0.001/GB
- **Total: $0.001/GB (99.97% reduction)**

## 🚀 Implementation Status

### ✅ Completed Components
- **5-Engine Architecture**: Consolidated design operational
- **Docker Environment**: Complete containerization with health checks
- **Database Schema**: Full v2 schema implementation + log_schemas table
- **Redis Pub/Sub**: Inter-engine communication working
- **Pattern Library System**: Knowledge crystallization framework
- **Schema Detection System**: **NEW** (Oct 2025) - SHA-256 hashing, AI mapping generation, deterministic extraction
- **CTI Integration**: OpenCTI, OTX, ThreatFox integration implemented
- **Multi-SIEM Rules**: Splunk, Elastic, Sentinel, QRadar, +4 more platforms
- **Monitoring Stack**: Grafana + Prometheus (resource-optimized)
- **Cost Tracking**: AI usage and savings dashboards
- **Log Source Quality**: 11 API endpoints, 30+ pre-configured sources
- **Parser Generation**: AI-powered parser creation at $0.00/parser
- **Frontend**: React SPA with 17+ advanced widgets
- **Complete Documentation**: Full codebase index with all classes, functions, APIs

### 🚧 Active Development
- Advanced threat hunting workflows
- ML model training pipeline
- Global pattern sharing network
- Enhanced correlation rules

### 📋 Next Phase
- Production hardening
- Advanced case workflows
- Compliance reporting automation
- Multi-region deployment

## 🔑 Key Design Principles

1. **Single Responsibility**: One engine, one purpose
2. **Log Everything**: Universal logging for training
3. **Pattern Crystallization**: Learn once, operate forever
4. **Pattern Segregation**: Raw vs SIEM-normalized patterns
5. **v1 Reference**: Use successful patterns from production
6. **Test First**: No code without tests
7. **Incremental Progress**: Never break working system
8. **Frontend Manifestation**: Every capability visualized
9. **Complete Documentation**: Every class, function, API documented

## 📝 Development Workflow

### Local Development
```bash
cd siemless_v2
pip install -r requirements.txt
python -m pytest tests/
```

### Docker Deployment
```bash
docker-compose build
docker-compose up -d
docker-compose logs -f [engine_name]
```

### Finding Code
```bash
# Use CODEBASE_INDEX.md to find any class, function, or API
# Example: Looking for entity extraction?
# Check: CODEBASE_INDEX.md → Contextualization Engine → entity_extractor.py
```

### Adding New Patterns
1. Create pattern JSON in `patterns/`
2. Follow naming convention: `vendor_product.json`
3. Use Python regex syntax with `?P<name>` groups
4. Include entity and relationship mappings
5. Restart Librarian to load

### Creating New Engine
1. Copy template from existing engine
2. Inherit from `BaseEngine`
3. Implement abstract methods
4. Create Dockerfile
5. Add to docker-compose.yml
6. Write tests
7. Update CODEBASE_INDEX.md

## 📚 Related Documentation

### Quick Reference
- **Find any class/function**: [CODEBASE_INDEX.md](./CODEBASE_INDEX.md)
- **Development context**: [CLAUDE.md](./CLAUDE.md)
- **API endpoints**: [CODEBASE_INDEX.md](./CODEBASE_INDEX.md) - HTTP API Endpoints
- **Log quality**: [LOG_SOURCE_QUALITY_DOCUMENTATION.md](./LOG_SOURCE_QUALITY_DOCUMENTATION.md)
- **Parser generation**: [PARSER_GENERATION_SUCCESS.md](./PARSER_GENERATION_SUCCESS.md)

### v1 Context (Current Production)
- [../PROJECT_INDEX.md](../PROJECT_INDEX.md) - v1 system index
- [../CLAUDE.md](../CLAUDE.md) - v1 instructions and context

### v2 Architecture (This Rebuild)
- [ARCHITECTURE.md](./ARCHITECTURE.md) - Detailed v2 architecture
- [DEPLOYMENT.md](./DEPLOYMENT.md) - Production deployment guide
- [../SIEMLESS_V2_ARCHITECTURE.md](../SIEMLESS_V2_ARCHITECTURE.md) - Original vision

## 🎯 Success Metrics

- **Pattern Hit Rate**: Target >95%
- **AI Fallback Rate**: Target <5%
- **Cost per GB**: Target <$0.01
- **Processing Latency**: Target <100ms
- **Pattern Library Size**: Growing daily
- **Training Data Quality**: 100% decision logging
- **Documentation Coverage**: 100% (CODEBASE_INDEX.md)

## 🔮 Future Vision

### Phase 2: Advanced Capabilities
- Machine learning anomaly detection
- Natural language queries
- Automated threat hunting
- Compliance reporting

### Phase 3: Scale
- Kubernetes deployment
- Multi-region support
- 1M+ events/second
- Global pattern sharing

## 📞 Support & Contribution

- **Issues**: GitHub Issues
- **Logs**: `docker-compose logs -f [engine_name]`
- **Metrics**: Grafana dashboards (Port 3001)
- **Patterns**: Submit via PR to `patterns/`
- **Documentation**: Update CODEBASE_INDEX.md when adding features

---

*SIEMLess v2.0: Learn Expensive Once → Operate Free Forever*
