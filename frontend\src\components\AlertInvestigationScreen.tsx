/**
 * Alert Investigation Screen - Main investigation view with tabs
 */

import React, { useState, useEffect } from 'react';
import { Alert, EnrichmentData, CorrelationData } from '../types/investigation';
import { AlertService, AlertWebSocketService } from '../services/alertService';
import OverviewTab from './investigation/OverviewTab';
import EntitiesTab from './investigation/EntitiesTab';
import CorrelationTab from './investigation/CorrelationTab';
import MITRETab from './investigation/MITRETab';
import TimelineTab from './investigation/TimelineTab';
import AIAnalysisTab from './investigation/AIAnalysisTab';
import QueryGeneratorTab from './investigation/QueryGeneratorTab';
import '../styles/Investigation.css';

interface AlertInvestigationScreenProps {
  alertId: string;
  onBack?: () => void;
}

type TabType = 'overview' | 'entities' | 'correlation' | 'mitre' | 'timeline' | 'ai' | 'queries';

export const AlertInvestigationScreen: React.FC<AlertInvestigationScreenProps> = ({
  alertId,
  onBack
}) => {
  const [alert, setAlert] = useState<Alert | null>(null);
  const [enrichment, setEnrichment] = useState<EnrichmentData | null>(null);
  const [correlation, setCorrelation] = useState<CorrelationData | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [wsService] = useState(() => new AlertWebSocketService());

  useEffect(() => {
    loadInvestigation();

    // Connect WebSocket for real-time updates
    wsService.connect();

    // Subscribe to enrichment updates
    const unsubEnrich = wsService.subscribe(
      'contextualization.alert.enriched.*',
      handleEnrichmentUpdate
    );

    // Subscribe to correlation updates
    const unsubCorr = wsService.subscribe(
      'backend.correlation.complete.*',
      handleCorrelationUpdate
    );

    return () => {
      unsubEnrich();
      unsubCorr();
      wsService.disconnect();
    };
  }, [alertId]);

  const loadInvestigation = async () => {
    try {
      setLoading(true);

      // Load alert details
      const alertData = await AlertService.getAlert(alertId);
      setAlert(alertData);

      // Load enrichment data (if available)
      try {
        const enrichmentData = await AlertService.getEnrichment(alertId);
        if (enrichmentData.status === 'completed') {
          setEnrichment(enrichmentData);
        }
      } catch (err) {
        console.log('No enrichment data available');
      }

      // Load correlation data (if available)
      try {
        const correlationData = await AlertService.getCorrelation(alertId);
        if (correlationData.status === 'completed') {
          setCorrelation(correlationData);
        }
      } catch (err) {
        console.log('No correlation data available');
      }

      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load investigation');
    } finally {
      setLoading(false);
    }
  };

  const handleEnrichmentUpdate = (data: any) => {
    if (data.alert_id === alertId) {
      setEnrichment({
        alert_id: alertId,
        status: 'completed',
        enrichment: data
      });
      showNotification('🟣 Enrichment Complete', `${data.enrichment_summary?.enriched_count} entities enriched`);
    }
  };

  const handleCorrelationUpdate = (data: any) => {
    if (data.alert_id === alertId) {
      setCorrelation({
        alert_id: alertId,
        status: 'completed',
        correlation: data
      });
      showNotification('🟢 Correlation Complete', `${data.correlation_summary?.total_related_events} related events found`);
    }
  };

  const showNotification = (title: string, message: string) => {
    // Simple notification - in production use a proper notification library
    console.log(`[Notification] ${title}: ${message}`);
    // TODO: Implement toast notification
  };

  const handleManualEnrich = async () => {
    if (!alert) return;

    try {
      await AlertService.triggerEnrichment(alertId, alert.entities);
      showNotification('Enrichment Triggered', 'Enriching entities...');

      // Poll for completion
      const result = await AlertService.pollEnrichment(alertId);
      setEnrichment(result);
    } catch (err) {
      alert('Failed to trigger enrichment: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  const handleManualCorrelate = async () => {
    if (!alert) return;

    try {
      await AlertService.triggerCorrelation(alertId, {
        entities: alert.entities,
        timestamp: alert.timestamp,
        mitre_techniques: alert.mitre_techniques
      });
      showNotification('Correlation Triggered', 'Searching for related events...');

      // Poll for completion
      const result = await AlertService.pollCorrelation(alertId);
      setCorrelation(result);
    } catch (err) {
      alert('Failed to trigger correlation: ' + (err instanceof Error ? err.message : 'Unknown error'));
    }
  };

  const getSeverityColor = (severity: string): string => {
    switch (severity.toLowerCase()) {
      case 'critical': return '#dc2626';
      case 'high': return '#ea580c';
      case 'medium': return '#ca8a04';
      case 'low': return '#65a30d';
      default: return '#6b7280';
    }
  };

  const getSeverityIcon = (severity: string): string => {
    switch (severity.toLowerCase()) {
      case 'critical': return '🔴';
      case 'high': return '🟠';
      case 'medium': return '🟡';
      case 'low': return '🟢';
      default: return '⚪';
    }
  };

  if (loading) {
    return (
      <div className="investigation-screen">
        <div className="loading-screen">
          <div className="spinner-large">⏳</div>
          <p>Loading investigation...</p>
        </div>
      </div>
    );
  }

  if (error || !alert) {
    return (
      <div className="investigation-screen">
        <div className="error-screen">
          <p>Error: {error || 'Alert not found'}</p>
          <button onClick={onBack} className="back-button">← Back to Queue</button>
        </div>
      </div>
    );
  }

  return (
    <div className="investigation-screen">
      {/* Header */}
      <div className="investigation-header">
        <button onClick={onBack} className="back-link">
          ← Back to Queue
        </button>

        <div className="alert-header-info">
          <div className="severity-row">
            <span className="severity-badge-large" style={{ color: getSeverityColor(alert.severity) }}>
              {getSeverityIcon(alert.severity)} {alert.severity.toUpperCase()}
            </span>
          </div>

          <h1 className="alert-title-large">{alert.title}</h1>

          <div className="alert-metadata">
            <span>Alert ID: {alert.alert_id}</span>
            <span>•</span>
            <span>Created: {new Date(alert.timestamp).toLocaleString()}</span>
          </div>

          {alert.rule_name && (
            <div className="rule-info">
              <span>Rule: {alert.rule_name}</span>
              <span>•</span>
              <span>Source: {alert.source}</span>
            </div>
          )}
        </div>

        <div className="header-actions">
          <button onClick={handleManualEnrich} className="action-button">
            🔍 Re-enrich
          </button>
          <button onClick={handleManualCorrelate} className="action-button">
            🔗 Re-correlate
          </button>
          <button className="action-button">
            📋 Export
          </button>
          <button className="action-button danger">
            🚫 Close
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="tab-navigation">
        <button
          className={activeTab === 'overview' ? 'tab active' : 'tab'}
          onClick={() => setActiveTab('overview')}
        >
          📊 Overview
        </button>
        <button
          className={activeTab === 'entities' ? 'tab active' : 'tab'}
          onClick={() => setActiveTab('entities')}
        >
          🌐 Entities
          {enrichment && enrichment.enrichment && (
            <span className="tab-badge">{enrichment.enrichment.summary.enriched_count}</span>
          )}
        </button>
        <button
          className={activeTab === 'correlation' ? 'tab active' : 'tab'}
          onClick={() => setActiveTab('correlation')}
        >
          🔗 Correlation
          {correlation && correlation.correlation && (
            <span className="tab-badge">{correlation.correlation.summary.total_related_events}</span>
          )}
        </button>
        <button
          className={activeTab === 'mitre' ? 'tab active' : 'tab'}
          onClick={() => setActiveTab('mitre')}
        >
          ⚔️ MITRE
          {alert.mitre_techniques && (
            <span className="tab-badge">{alert.mitre_techniques.length}</span>
          )}
        </button>
        <button
          className={activeTab === 'timeline' ? 'tab active' : 'tab'}
          onClick={() => setActiveTab('timeline')}
        >
          📜 Timeline
        </button>
        <button
          className={activeTab === 'ai' ? 'tab active' : 'tab'}
          onClick={() => setActiveTab('ai')}
        >
          🤖 AI Analysis
        </button>
        <button
          className={activeTab === 'queries' ? 'tab active' : 'tab'}
          onClick={() => setActiveTab('queries')}
        >
          🔍 Query Generator
        </button>
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'overview' && (
          <OverviewTab
            alert={alert}
            enrichment={enrichment}
            correlation={correlation}
          />
        )}
        {activeTab === 'entities' && (
          <EntitiesTab
            alert={alert}
            enrichment={enrichment}
          />
        )}
        {activeTab === 'correlation' && (
          <CorrelationTab
            alert={alert}
            correlation={correlation}
          />
        )}
        {activeTab === 'mitre' && (
          <MITRETab
            alert={alert}
            correlation={correlation}
          />
        )}
        {activeTab === 'timeline' && (
          <TimelineTab
            alert={alert}
            enrichment={enrichment}
            correlation={correlation}
          />
        )}
        {activeTab === 'ai' && (
          <AIAnalysisTab
            alert={alert}
            enrichment={enrichment}
            correlation={correlation}
          />
        )}
        {activeTab === 'queries' && (
          <QueryGeneratorTab
            alert={alert}
          />
        )}
      </div>
    </div>
  );
};

export default AlertInvestigationScreen;
