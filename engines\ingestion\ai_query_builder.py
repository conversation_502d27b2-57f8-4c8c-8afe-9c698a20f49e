"""
AI-Powered Query Builder for Context Plugins
Uses Intelligence Engine to generate vendor-specific queries dynamically
"""

import json
import logging
from typing import Dict, Any, Optional
from uuid import uuid4
import redis.asyncio as redis_async


class AIQueryBuilder:
    """
    Generates vendor-specific queries using AI intelligence

    Instead of hardcoding queries for each vendor, we ask AI to generate
    the correct query format based on the vendor's schema and our needs.

    This enables:
    - Automatic adaptation to vendor schema changes
    - Support for new vendors without code changes
    - Optimized queries based on available data
    """

    def __init__(self, redis_client, logger=None):
        self.redis_client = redis_client
        self.logger = logger or logging.getLogger(__name__)

        # Cache for generated queries (learn once, use forever)
        self.query_cache = {}

    async def build_query(
        self,
        vendor_name: str,
        query_type: str,  # ip, hostname, user, hash, domain, process
        query_value: str,
        category: str,  # ASSET, DETECTION, NETWORK, LOG
        schema_sample: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Generate vendor-specific query using AI

        Args:
            vendor_name: elastic, splunk, sentinel, etc.
            query_type: Type of entity to search for
            query_value: The actual value to search
            category: What type of data to return
            schema_sample: Optional sample of vendor's data structure

        Returns:
            Dict containing the vendor-specific query
        """

        # Check cache first
        cache_key = f"{vendor_name}:{query_type}:{category}"
        if cache_key in self.query_cache:
            self.logger.info(f"Using cached query for {cache_key}")
            cached_query = self.query_cache[cache_key]
            # Substitute the actual value
            return self._substitute_value(cached_query, query_value)

        # Generate new query with AI
        self.logger.info(f"Generating new query for {cache_key} using AI")

        request_id = str(uuid4())

        # Build prompt for AI
        prompt = self._build_query_generation_prompt(
            vendor_name, query_type, query_value, category, schema_sample
        )

        # Request query generation from intelligence engine
        request = {
            'request_id': request_id,
            'task': 'generate_vendor_query',
            'prompt': prompt,
            'vendor': vendor_name,
            'query_type': query_type,
            'category': category
        }

        # Publish to intelligence engine
        await self.redis_client.publish(
            'intelligence.generate_query',
            json.dumps(request)
        )

        # Wait for response
        pubsub = self.redis_client.pubsub()
        await pubsub.subscribe(f'intelligence.query_response.{request_id}')

        query_result = None
        timeout = 30

        try:
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    response = json.loads(message['data'])
                    if response.get('status') == 'success':
                        query_result = response.get('query')
                        # Cache for future use
                        self.query_cache[cache_key] = query_result
                        self.logger.info(f"AI generated query cached for {cache_key}")
                    break
        finally:
            await pubsub.unsubscribe(f'intelligence.query_response.{request_id}')

        if not query_result:
            # Fallback to basic query
            self.logger.warning(f"AI query generation failed, using fallback for {vendor_name}")
            query_result = self._fallback_query(vendor_name, query_type, query_value, category)

        return query_result

    def _build_query_generation_prompt(
        self,
        vendor_name: str,
        query_type: str,
        query_value: str,
        category: str,
        schema_sample: Optional[Dict]
    ) -> str:
        """Build AI prompt for query generation"""

        prompt = f"""Generate a {vendor_name} query for the following investigation task:

TASK: Search for {query_type} = "{query_value}"
CATEGORY: {category} data (Asset info, Detections, Network flows, or Logs)
VENDOR: {vendor_name}

"""

        if vendor_name.lower() == 'elastic':
            prompt += """
VENDOR SCHEMA: Elastic uses ECS (Elastic Common Schema)
- IP fields: source.ip, destination.ip, host.ip, client.ip, server.ip
- Host fields: host.name, host.hostname, host.os.name
- User fields: user.name, user.domain
- Process fields: process.name, process.pid, process.executable
- Network fields: network.protocol, network.direction
- Timestamp: @timestamp

QUERY FORMAT: Elasticsearch Query DSL (JSON)
REQUIREMENTS:
1. Use appropriate query types (match, wildcard, term, range)
2. Search across multiple relevant fields
3. Include time range filter
4. Return useful aggregations
5. Handle partial matches intelligently

"""
        elif vendor_name.lower() == 'splunk':
            prompt += """
VENDOR SCHEMA: Splunk uses CIM (Common Information Model)
- IP fields: src_ip, dest_ip, src, dest
- Host fields: host, src_host, dest_host
- User fields: user, src_user, dest_user
- Process fields: process, process_name, process_id

QUERY FORMAT: SPL (Splunk Processing Language)
REQUIREMENTS:
1. Use search command with field filters
2. Include time range
3. Use stats/table for aggregation
4. Handle wildcards with *

"""

        if schema_sample:
            prompt += f"""
SAMPLE DATA STRUCTURE:
{json.dumps(schema_sample, indent=2)[:500]}

"""

        prompt += f"""
OUTPUT: Return ONLY a valid JSON object with the query structure.
For Elastic, return Elasticsearch Query DSL.
For Splunk, return {{"spl": "search command here"}}.

The query should be reusable - use {{{{VALUE}}}} as placeholder for the search value.

Example Elastic output:
{{
  "query": {{
    "bool": {{
      "should": [
        {{"wildcard": {{"source.ip": "{query_type}={{{{VALUE}}}}"}}}},
        {{"wildcard": {{"host.ip": "{query_type}={{{{VALUE}}}}"}}}}
      ]
    }}
  }}
}}
"""

        return prompt

    def _substitute_value(self, query_template: Dict, actual_value: str) -> Dict:
        """Substitute {{VALUE}} placeholder with actual search value"""
        query_str = json.dumps(query_template)
        query_str = query_str.replace('{{VALUE}}', actual_value)
        return json.loads(query_str)

    def _fallback_query(
        self,
        vendor_name: str,
        query_type: str,
        query_value: str,
        category: str
    ) -> Dict:
        """Fallback queries when AI is unavailable"""

        if vendor_name.lower() == 'elastic':
            # Basic Elastic query
            if query_type == 'ip':
                return {
                    "query": {
                        "bool": {
                            "should": [
                                {"wildcard": {"source.ip": f"*{query_value}*"}},
                                {"wildcard": {"destination.ip": f"*{query_value}*"}},
                                {"wildcard": {"host.ip": f"*{query_value}*"}}
                            ],
                            "minimum_should_match": 1
                        }
                    },
                    "size": 100
                }
            elif query_type == 'hostname':
                return {
                    "query": {
                        "bool": {
                            "should": [
                                {"wildcard": {"host.name": f"*{query_value}*"}},
                                {"wildcard": {"host.hostname": f"*{query_value}*"}}
                            ]
                        }
                    },
                    "size": 100
                }
            elif query_type == 'user':
                return {
                    "query": {
                        "bool": {
                            "should": [
                                {"wildcard": {"user.name": f"*{query_value}*"}}
                            ]
                        }
                    },
                    "size": 100
                }

        # Generic fallback
        return {
            "query": {"match_all": {}},
            "size": 10
        }

    async def learn_from_success(
        self,
        vendor_name: str,
        query_type: str,
        category: str,
        query: Dict,
        results_count: int
    ):
        """
        Learn from successful queries

        When a query returns good results, store it for future use.
        This builds our pattern library over time.
        """

        if results_count > 0:
            cache_key = f"{vendor_name}:{query_type}:{category}"
            self.query_cache[cache_key] = query
            self.logger.info(
                f"Learned successful query for {cache_key} "
                f"(returned {results_count} results)"
            )

            # Optionally save to database for persistence
            # TODO: Save to pattern_library table

    async def get_query_stats(self) -> Dict:
        """Get statistics about cached queries"""
        return {
            'cached_queries': len(self.query_cache),
            'cache_keys': list(self.query_cache.keys())
        }
