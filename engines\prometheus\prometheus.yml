global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'siemless-monitor'

scrape_configs:
  # Intelligence Engine metrics
  - job_name: 'intelligence_engine'
    static_configs:
      - targets: ['intelligence_engine:8001']
    metrics_path: '/metrics'

  # Backend Engine metrics
  - job_name: 'backend_engine'
    static_configs:
      - targets: ['backend_engine:8002']
    metrics_path: '/metrics'

  # Ingestion Engine metrics
  - job_name: 'ingestion_engine'
    static_configs:
      - targets: ['ingestion_engine:8003']
    metrics_path: '/metrics'

  # Contextualization Engine metrics
  - job_name: 'contextualization_engine'
    static_configs:
      - targets: ['contextualization_engine:8004']
    metrics_path: '/metrics'

  # Delivery Engine metrics
  - job_name: 'delivery_engine'
    static_configs:
      - targets: ['delivery_engine:8005']
    metrics_path: '/metrics'

  # PostgreSQL exporter (if deployed)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres_exporter:9187']

  # Redis exporter (if deployed)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis_exporter:9121']

  # Docker containers metrics
  - job_name: 'docker'
    static_configs:
      - targets: ['cadvisor:8080']

  # Node exporter for host metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node_exporter:9100']