"""
CrowdStrike CTI Plugin
Universal plugin implementation for CrowdStrike threat intelligence feed
Supports both INTEL_READ (threat intel) and IOCS_READ (custom IOCs) scopes
"""

import aiohttp
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from cti_source_plugin import (
    CTISourcePlugin, CTIIndicator, IndicatorType, ThreatType
)


class CrowdStrikePlugin(CTISourcePlugin):
    """
    CrowdStrike CTI Source Plugin

    Fetches threat intelligence from CrowdStrike Falcon Intelligence
    Requires API credentials with INTEL_READ and/or IOCS_READ scopes

    Two primary endpoints:
    1. /intel/combined/indicators/v1 - Threat intelligence indicators
    2. /indicators/combined/iocs/v1 - Custom IOCs
    """

    def __init__(self, config: dict, logger=None):
        super().__init__(config, logger)
        self.client_id = config.get('client_id')
        self.client_secret = config.get('client_secret')
        self.base_url = config.get('base_url', 'https://api.crowdstrike.com')
        self.access_token = None
        self.token_expiry = None

    def get_source_name(self) -> str:
        return "crowdstrike_intel"

    def get_source_type(self) -> str:
        return "commercial"

    async def _get_access_token(self) -> bool:
        """Authenticate with CrowdStrike API"""
        try:
            # Check if token is still valid
            if self.access_token and self.token_expiry:
                if datetime.utcnow() < self.token_expiry:
                    return True

            # Request new token
            auth_url = f"{self.base_url}/oauth2/token"

            async with aiohttp.ClientSession() as session:
                data = {
                    'client_id': self.client_id,
                    'client_secret': self.client_secret
                }

                async with session.post(auth_url, data=data) as response:
                    if response.status == 201:
                        token_data = await response.json()
                        self.access_token = token_data['access_token']
                        expires_in = token_data.get('expires_in', 1800)
                        self.token_expiry = datetime.utcnow() + timedelta(seconds=expires_in - 60)
                        self.logger.info("CrowdStrike authentication successful")
                        return True
                    else:
                        self.logger.error(f"CrowdStrike auth failed: {response.status}")
                        return False

        except Exception as e:
            self.logger.error(f"CrowdStrike authentication error: {e}")
            return False

    async def validate_credentials(self) -> bool:
        """Test connection to CrowdStrike API"""
        try:
            authenticated = await self._get_access_token()
            if not authenticated:
                return False

            # Test with a simple query
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                # Test INTEL scope with simple query
                url = f"{self.base_url}/intel/combined/indicators/v1"
                params = {'limit': 1}

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        self.logger.info("CrowdStrike credentials validated successfully")
                        return True
                    else:
                        self.logger.warning(f"CrowdStrike validation returned status {response.status}")
                        return False

        except Exception as e:
            self.logger.error(f"CrowdStrike credential validation error: {e}")
            return False

    async def fetch_indicators(
        self,
        since: Optional[datetime] = None,
        indicator_types: Optional[List[IndicatorType]] = None,
        limit: int = 1000
    ) -> List[CTIIndicator]:
        """
        Fetch indicators from CrowdStrike

        Returns standardized CTI indicators from both INTEL and IOCS endpoints
        """
        indicators = []

        try:
            # Fetch from threat intel endpoint
            intel_indicators = await self._fetch_intel_indicators(limit // 2, since)
            indicators.extend(intel_indicators)

            # Fetch from custom IOCs endpoint
            ioc_indicators = await self._fetch_custom_iocs(limit // 2, since)
            indicators.extend(ioc_indicators)

            self.logger.info(f"CrowdStrike: Fetched {len(indicators)} indicators "
                           f"({len(intel_indicators)} intel, {len(ioc_indicators)} custom)")

        except Exception as e:
            self.logger.error(f"CrowdStrike fetch error: {e}")

        return indicators

    async def _fetch_intel_indicators(
        self,
        limit: int,
        since: Optional[datetime] = None
    ) -> List[CTIIndicator]:
        """Fetch threat intelligence indicators from INTEL_READ scope"""
        indicators = []

        try:
            authenticated = await self._get_access_token()
            if not authenticated:
                self.logger.warning("CrowdStrike authentication failed")
                return indicators

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/intel/combined/indicators/v1"

                params = {
                    'limit': min(limit, 500),
                    'sort': 'published_date|desc'
                }

                # Build filter query
                filter_parts = []

                # Default: High confidence indicators
                filter_parts.append("malicious_confidence:'high'")

                # Time filter if provided
                if since:
                    days_ago = (datetime.utcnow() - since).days
                    filter_parts.append(f"published_date:>='{days_ago} days ago'")
                else:
                    # Default: Last 7 days
                    filter_parts.append("published_date:>='7 days ago'")

                params['filter'] = '+'.join(filter_parts)

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        resources = data.get('resources', [])

                        for resource in resources:
                            standardized = self._standardize_intel_indicator(resource)
                            if standardized:
                                indicators.append(standardized)

                        self.logger.info(f"Retrieved {len(indicators)} intel indicators from CrowdStrike")
                    else:
                        self.logger.warning(f"CrowdStrike intel query returned status {response.status}")

        except Exception as e:
            self.logger.error(f"Error fetching CrowdStrike intel indicators: {e}")

        return indicators

    async def _fetch_custom_iocs(
        self,
        limit: int,
        since: Optional[datetime] = None
    ) -> List[CTIIndicator]:
        """Fetch custom IOCs from IOCS_READ scope"""
        iocs = []

        try:
            authenticated = await self._get_access_token()
            if not authenticated:
                self.logger.warning("CrowdStrike authentication failed")
                return iocs

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/indicators/combined/iocs/v1"

                params = {
                    'limit': min(limit, 500),
                    'sort': 'created_on|desc'
                }

                # Time filter if provided
                if since:
                    timestamp = since.strftime('%Y-%m-%dT%H:%M:%SZ')
                    params['filter'] = f"created_on:>='{timestamp}'"

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        resources = data.get('resources', [])

                        for resource in resources:
                            standardized = self._standardize_custom_ioc(resource)
                            if standardized:
                                iocs.append(standardized)

                        self.logger.info(f"Retrieved {len(iocs)} custom IOCs from CrowdStrike")
                    else:
                        self.logger.warning(f"CrowdStrike IOCs query returned status {response.status}")

        except Exception as e:
            self.logger.error(f"Error fetching CrowdStrike custom IOCs: {e}")

        return iocs

    def _standardize_intel_indicator(self, resource: dict) -> Optional[CTIIndicator]:
        """
        Convert CrowdStrike intel indicator to standardized CTI format

        CrowdStrike types: domain, ipv4, ipv6, md5, sha1, sha256, url, email_address, etc.
        """
        try:
            cs_type = resource.get('type', '').lower()
            indicator_value = resource.get('indicator', '')

            if not indicator_value:
                return None

            # Map CrowdStrike types to standard types
            indicator_type = self._map_indicator_type(cs_type)
            if not indicator_type:
                return None  # Skip unsupported types

            # Determine threat type from resource data
            threat_type = self._determine_threat_type(resource)

            # Map confidence level to 0.0-1.0 scale
            confidence = self._map_confidence(resource.get('malicious_confidence', 'medium'))

            # Parse timestamps
            first_seen = self._parse_timestamp(resource.get('published_date'))
            last_seen = self._parse_timestamp(resource.get('last_updated'))

            # Extract tags
            tags = self._extract_intel_tags(resource)

            # Extract MITRE techniques
            mitre_techniques = []
            kill_chains = resource.get('kill_chains', [])
            for kc in kill_chains:
                if 'mitre_attack' in kc.lower():
                    # Extract technique IDs from kill chain
                    phases = kc.get('phase_name', '')
                    if phases.startswith('T'):
                        mitre_techniques.append(phases)

            # Calculate severity
            severity = self._calculate_severity(resource)

            # Build standardized indicator
            return CTIIndicator({
                'indicator_type': indicator_type,
                'indicator_value': indicator_value,
                'threat_type': threat_type,
                'confidence': confidence,
                'first_seen': first_seen,
                'last_seen': last_seen,
                'tags': tags,
                'description': resource.get('labels', [''])[0] if resource.get('labels') else '',
                'source_reference': resource.get('id', ''),
                'mitre_techniques': mitre_techniques,
                'severity': severity,
                'raw_data': {
                    'threat_actors': resource.get('actors', []),
                    'malware_families': resource.get('malware_families', []),
                    'threat_types': resource.get('threat_types', []),
                    'kill_chains': resource.get('kill_chains', []),
                    'reports': resource.get('reports', []),
                    'labels': resource.get('labels', [])
                }
            })

        except Exception as e:
            self.logger.error(f"Error standardizing CrowdStrike intel indicator: {e}")
            return None

    def _standardize_custom_ioc(self, resource: dict) -> Optional[CTIIndicator]:
        """Convert CrowdStrike custom IOC to standardized CTI format"""
        try:
            cs_type = resource.get('type', '').lower()
            indicator_value = resource.get('value', '')

            if not indicator_value:
                return None

            # Map CrowdStrike types to standard types
            indicator_type = self._map_indicator_type(cs_type)
            if not indicator_type:
                return None

            # Custom IOCs are always high confidence
            confidence = 0.95

            # Parse timestamps
            first_seen = self._parse_timestamp(resource.get('created_on'))
            last_seen = self._parse_timestamp(resource.get('modified_on'))

            # Extract tags
            tags = resource.get('tags', []) + ['custom_ioc']

            # Severity mapping
            severity = resource.get('severity', 'medium').lower()

            # Build standardized indicator
            return CTIIndicator({
                'indicator_type': indicator_type,
                'indicator_value': indicator_value,
                'threat_type': ThreatType.UNKNOWN.value,  # Custom IOCs don't have threat type
                'confidence': confidence,
                'first_seen': first_seen,
                'last_seen': last_seen,
                'tags': tags,
                'description': resource.get('description', ''),
                'source_reference': resource.get('id', ''),
                'mitre_techniques': [],
                'severity': severity,
                'raw_data': {
                    'action': resource.get('action'),
                    'platforms': resource.get('platforms', []),
                    'source': resource.get('source'),
                    'applied_globally': resource.get('applied_globally', False)
                }
            })

        except Exception as e:
            self.logger.error(f"Error standardizing CrowdStrike custom IOC: {e}")
            return None

    def _map_indicator_type(self, cs_type: str) -> Optional[str]:
        """Map CrowdStrike indicator types to standardized IndicatorType enum"""
        type_mapping = {
            'domain': IndicatorType.DOMAIN.value,
            'ipv4': IndicatorType.IP.value,
            'ipv6': IndicatorType.IP.value,
            'md5': IndicatorType.FILE_HASH.value,
            'sha1': IndicatorType.FILE_HASH.value,
            'sha256': IndicatorType.FILE_HASH.value,
            'hash_md5': IndicatorType.FILE_HASH.value,
            'hash_sha1': IndicatorType.FILE_HASH.value,
            'hash_sha256': IndicatorType.FILE_HASH.value,
            'url': IndicatorType.URL.value,
            'email_address': IndicatorType.EMAIL.value,
            'email': IndicatorType.EMAIL.value,
            'registry': IndicatorType.REGISTRY_KEY.value,
            'mutex_name': IndicatorType.MUTEX.value,
        }
        return type_mapping.get(cs_type)

    def _map_confidence(self, cs_confidence: str) -> float:
        """
        Map CrowdStrike confidence levels to 0.0-1.0 scale

        CrowdStrike levels: high, medium, low, unverified
        Standard scale: 0.0 (no confidence) to 1.0 (certain)
        """
        confidence_mapping = {
            'high': 0.9,
            'medium': 0.6,
            'low': 0.3,
            'unverified': 0.2
        }
        return confidence_mapping.get(cs_confidence.lower(), 0.5)

    def _determine_threat_type(self, resource: dict) -> str:
        """Determine threat type from CrowdStrike resource data"""
        # Check malware families
        malware_families = resource.get('malware_families', [])
        if malware_families:
            malware_lower = [m.lower() for m in malware_families]
            if any('ransom' in m for m in malware_lower):
                return ThreatType.RANSOMWARE.value
            return ThreatType.MALWARE.value

        # Check threat types
        threat_types = resource.get('threat_types', [])
        if threat_types:
            threat_lower = [t.lower() for t in threat_types]
            if any('apt' in t for t in threat_lower):
                return ThreatType.APT.value
            if any('phish' in t for t in threat_lower):
                return ThreatType.PHISHING.value
            if any('c2' in t or 'command' in t for t in threat_lower):
                return ThreatType.C2.value
            if any('exploit' in t for t in threat_lower):
                return ThreatType.EXPLOIT.value

        # Check labels
        labels = resource.get('labels', [])
        if labels:
            label_lower = [l.lower() for l in labels]
            if any('botnet' in l for l in label_lower):
                return ThreatType.BOTNET.value
            if any('spam' in l for l in label_lower):
                return ThreatType.SPAM.value

        return ThreatType.UNKNOWN.value

    def _extract_intel_tags(self, resource: dict) -> List[str]:
        """Extract and normalize tags from CrowdStrike intel resource"""
        tags = []

        # Add threat types as tags
        tags.extend(resource.get('threat_types', []))

        # Add labels
        tags.extend(resource.get('labels', []))

        # Add malware families
        malware_families = resource.get('malware_families', [])
        tags.extend([f"malware:{fam}" for fam in malware_families])

        # Add actors
        actors = resource.get('actors', [])
        tags.extend([f"actor:{actor}" for actor in actors])

        # Add confidence level
        confidence = resource.get('malicious_confidence')
        if confidence:
            tags.append(f"confidence:{confidence}")

        return list(set(tags))  # Deduplicate

    def _calculate_severity(self, resource: dict) -> str:
        """Calculate severity from CrowdStrike resource data"""
        confidence = resource.get('malicious_confidence', 'medium').lower()

        # High confidence + threat actors = high severity
        if confidence == 'high' and resource.get('actors'):
            return 'high'

        # Multiple malware families = high severity
        if len(resource.get('malware_families', [])) > 1:
            return 'high'

        # APT indicators = high severity
        threat_types = [t.lower() for t in resource.get('threat_types', [])]
        if any('apt' in t for t in threat_types):
            return 'high'

        # High confidence = medium severity
        if confidence == 'high':
            return 'medium'

        # Default to low
        return 'low'

    def _parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """Parse CrowdStrike timestamp string to datetime"""
        if not timestamp_str:
            return None

        try:
            # CrowdStrike format: 2024-01-15T12:34:56Z
            return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        except:
            return None

    async def get_indicator_context(self, indicator: str) -> Optional[Dict[str, Any]]:
        """Get additional context for an indicator from CrowdStrike"""
        try:
            authenticated = await self._get_access_token()
            if not authenticated:
                return None

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                # Search for indicator in intel feed
                url = f"{self.base_url}/intel/combined/indicators/v1"
                params = {
                    'filter': f"indicator:'{indicator}'",
                    'limit': 1
                }

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        resources = data.get('resources', [])

                        if resources:
                            resource = resources[0]
                            return {
                                'threat_actors': resource.get('actors', []),
                                'malware_families': resource.get('malware_families', []),
                                'threat_types': resource.get('threat_types', []),
                                'reports': resource.get('reports', []),
                                'confidence': resource.get('malicious_confidence', 'unknown')
                            }

        except Exception as e:
            self.logger.error(f"CrowdStrike context fetch error: {e}")

        return None

    async def get_threat_actors(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Fetch threat actor information from CrowdStrike INTEL scope

        Note: This is a bonus method not required by base plugin class
        """
        actors = []

        try:
            authenticated = await self._get_access_token()
            if not authenticated:
                return actors

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/intel/combined/actors/v1"
                params = {'limit': min(limit, 100)}

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        resources = data.get('resources', [])

                        for resource in resources:
                            actor = {
                                'id': resource.get('id'),
                                'name': resource.get('name'),
                                'description': resource.get('short_description'),
                                'aliases': resource.get('known_as', []),
                                'first_seen': resource.get('first_activity_date'),
                                'last_updated': resource.get('last_modified_date'),
                                'target_countries': resource.get('target_countries', []),
                                'target_industries': resource.get('target_industries', []),
                                'motivations': resource.get('motivations', []),
                                'capabilities': resource.get('capability', [])
                            }
                            actors.append(actor)

                        self.logger.info(f"Retrieved {len(actors)} threat actors from CrowdStrike")

        except Exception as e:
            self.logger.error(f"Error fetching CrowdStrike threat actors: {e}")

        return actors

    async def get_malware_families(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Fetch malware family information from CrowdStrike INTEL scope

        Note: This is a bonus method not required by base plugin class
        """
        malware_families = []

        try:
            authenticated = await self._get_access_token()
            if not authenticated:
                return malware_families

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/intel/combined/indicators/v1"

                # Query for indicators with malware families
                params = {
                    'filter': "malware_families:*",
                    'limit': min(limit, 500),
                    'sort': 'published_date|desc'
                }

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        resources = data.get('resources', [])

                        # Extract unique malware families
                        malware_map = {}
                        for resource in resources:
                            for family in resource.get('malware_families', []):
                                if family not in malware_map:
                                    malware_map[family] = {
                                        'name': family,
                                        'associated_actors': set(),
                                        'indicator_count': 0,
                                        'threat_types': set(),
                                        'first_seen': resource.get('published_date')
                                    }

                                malware_map[family]['indicator_count'] += 1
                                malware_map[family]['associated_actors'].update(resource.get('actors', []))
                                malware_map[family]['threat_types'].update(resource.get('threat_types', []))

                        # Convert to list format
                        for family, data in malware_map.items():
                            malware_families.append({
                                'name': family,
                                'associated_actors': list(data['associated_actors']),
                                'indicator_count': data['indicator_count'],
                                'threat_types': list(data['threat_types']),
                                'first_seen': data['first_seen']
                            })

                        self.logger.info(f"Retrieved {len(malware_families)} malware families from CrowdStrike")

        except Exception as e:
            self.logger.error(f"Error fetching CrowdStrike malware families: {e}")

        return malware_families

    async def get_vulnerabilities(self, limit: int = 50, severity: str = None) -> List[Dict[str, Any]]:
        """
        Fetch vulnerability information from CrowdStrike Spotlight

        Note: This requires SPOTLIGHT_READ scope
        """
        vulnerabilities = []

        try:
            authenticated = await self._get_access_token()
            if not authenticated:
                return vulnerabilities

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/spotlight/combined/vulnerabilities/v1"

                params = {
                    'limit': min(limit, 100),
                    'sort': 'created_timestamp|desc'
                }

                if severity:
                    params['filter'] = f"severity:'{severity}'"

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        resources = data.get('resources', [])

                        for resource in resources:
                            vuln = {
                                'cve_id': resource.get('cve', {}).get('id'),
                                'cve_description': resource.get('cve', {}).get('description'),
                                'severity': resource.get('severity'),
                                'cvss_score': resource.get('cve', {}).get('base_score'),
                                'published_date': resource.get('cve', {}).get('published_date'),
                                'exploited_status': resource.get('cve', {}).get('exploited_status', []),
                                'vendor_references': resource.get('cve', {}).get('vendor_references', []),
                                'affected_products': resource.get('apps', {}).get('product_name_version', [])
                            }
                            vulnerabilities.append(vuln)

                        self.logger.info(f"Retrieved {len(vulnerabilities)} vulnerabilities from CrowdStrike Spotlight")
                    elif response.status == 403:
                        self.logger.warning("CrowdStrike Spotlight access denied (requires SPOTLIGHT_READ scope)")
                    else:
                        self.logger.warning(f"CrowdStrike vulnerability query returned status {response.status}")

        except Exception as e:
            self.logger.error(f"Error fetching CrowdStrike vulnerabilities: {e}")

        return vulnerabilities
