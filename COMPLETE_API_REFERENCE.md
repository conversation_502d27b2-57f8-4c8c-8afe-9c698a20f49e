# SIEMLess v2.0 - Complete API Reference for Frontend Development

**Created:** October 3, 2025
**Purpose:** Exhaustive API catalog for systematic widget development
**Target:** Frontend developers building against production environment

---

## 📋 Table of Contents

1. [API Overview & Architecture](#api-overview--architecture)
2. [Delivery Engine APIs (Port 8005)](#delivery-engine-port-8005) - **PRIMARY FRONTEND INTERFACE**
3. [Backend Engine APIs (Port 8002)](#backend-engine-port-8002) - **DETECTION & QUALITY**
4. [Ingestion Engine APIs (Port 8003)](#ingestion-engine-port-8003) - **CTI & PARSERS**
5. [Investigation APIs (Delivery Sub-system)](#investigation-apis) - **MULTI-VENDOR CONTEXT**
6. [TypeScript Interface Definitions](#typescript-interface-definitions)
7. [Widget-to-API Mapping Matrix](#widget-to-api-mapping-matrix)
8. [Integration Patterns](#integration-patterns)

---

## API Overview & Architecture

### Engine Distribution

| Engine | Port | HTTP Endpoints | Purpose | Frontend Access |
|--------|------|----------------|---------|-----------------|
| **Delivery** | 8005 | 38 endpoints | Cases, alerts, dashboard, workflows, entities | **PRIMARY** - Most widgets |
| **Backend** | 8002 | 15 endpoints | Log quality, detection fidelity, correlation | **SECONDARY** - Detection widgets |
| **Ingestion** | 8003 | 10 endpoints | CTI plugins, parsers, source management | **SECONDARY** - CTI widgets |
| Intelligence | 8001 | 0 (Redis only) | AI consensus, pattern crystallization | **NO DIRECT ACCESS** |
| Contextualization | 8004 | 0 (Redis only) | Entity extraction, enrichment | **NO DIRECT ACCESS** |

###

 Production Routing Strategy

**Development (Multi-Port):**
```typescript
const DELIVERY_URL = 'http://localhost:8005'
const BACKEND_URL = 'http://localhost:8002'
const INGESTION_URL = 'http://localhost:8003'
```

**Production (Nginx Proxy):**
```nginx
# Single entry point: https://siemless.company.com
location /api/cti/ { proxy_pass http://ingestion:8003/cti/; }
location /api/detection/ { proxy_pass http://backend:8002/api/detection/; }
location /api/log-sources/ { proxy_pass http://backend:8002/api/log-sources/; }
location /api/correlation/ { proxy_pass http://backend:8002/api/correlation/; }
location /api/coverage/ { proxy_pass http://backend:8002/api/coverage/; }
location /api/graph/ { proxy_pass http://backend:8002/api/graph/; }
location /api/parsers/ { proxy_pass http://ingestion:8003/api/parsers/; }
location /api/ { proxy_pass http://delivery:8005/api/; }
```

---

## Delivery Engine (Port 8005)

**Base URL:** `http://localhost:8005`

### 🎯 CRITICAL ENDPOINTS (Build These First)

#### 1. Dashboard Overview
**GET /api/dashboard/overview**

**Purpose:** Primary dashboard data - first screen users see

**Response:**
```typescript
{
  total_cases: number
  open_cases: number
  critical_alerts: number
  recent_activity: Activity[]
  system_health: 'healthy' | 'degraded' | 'critical'
  active_investigations: number
  cti_indicators_active: number
  entities_processed_today: number
}
```

**Widget:** Dashboard Overview Card
**Priority:** CRITICAL
**Enrichment:** None

---

#### 2. Alert Queue
**GET /api/alerts**

**Purpose:** Real-time alert feed for SOC analysts

**Query Parameters:**
- `status`: 'new' | 'acknowledged' | 'investigating' | 'closed'
- `severity`: 'low' | 'medium' | 'high' | 'critical'
- `limit`: number (default: 50)
- `offset`: number (pagination)

**Response:**
```typescript
{
  alerts: Alert[]
  total: number
  filters: {
    status: string
    severity: string
  }
}

interface Alert {
  alert_id: string
  title: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  source: string  // 'crowdstrike', 'elastic', 'otx'
  created_at: string  // ISO timestamp
  status: 'new' | 'acknowledged' | 'investigating' | 'closed'
  entity_count: number
  has_cti_match: boolean  // CTI threat intelligence match
  threat_score?: number  // 0-100 if has_cti_match
  mitre_techniques?: string[]  // ['T1059.001']
}
```

**Widget:** Alert Queue Table
**Priority:** CRITICAL
**Enrichment:** CTI Layer 2 (threat_score, has_cti_match)

---

#### 3. Alert Context (Investigation Trigger)
**GET /api/alerts/{alert_id}/context**

**Purpose:** Get enriched context when analyst clicks alert (investigation start)

**Response:**
```typescript
{
  alert_id: string
  entities: Entity[]  // Extracted entities with full enrichment
  relationships: Relationship[]
  timeline: TimelineEvent[]
  cti_matches: CTIMatch[]  // Threat intelligence matches
  investigation_guide?: InvestigationGuide  // AI-generated steps
  risk_assessment: {
    overall_risk: number  // 0-100
    factors: string[]
    recommendations: string[]
  }
}
```

**Widget:** Alert Details Modal → Investigation Creation
**Priority:** CRITICAL
**Enrichment:** ALL THREE LAYERS (Business + CTI + Investigation)

---

#### 4. Case Management
**POST /api/cases**

**Purpose:** Create case from alert or manual triage

**Request:**
```typescript
{
  title: string
  description: string
  case_type: 'security_incident' | 'anomaly_detection' | 'threat_hunt'
  priority: 'low' | 'medium' | 'high' | 'critical'
  assigned_to?: string
  alert_id?: string  // Link to alert
  entities?: string[]  // Entity IDs
}
```

**Response:**
```typescript
{
  case_id: string  // 'case_1696300000'
  title: string
  status: 'open'
  workflow_stage: 'triage' | 'investigation' | 'remediation' | 'closed'
  created_at: string
  workflow_id?: string  // If auto-workflow triggered
}
```

---

**GET /api/cases**

**Purpose:** List all cases with filtering

**Query Parameters:**
- `status`: 'open' | 'closed' | 'in_progress'
- `priority`: 'low' | 'medium' | 'high' | 'critical'
- `assigned_to`: string
- `limit`: number
- `offset`: number

**Response:**
```typescript
{
  cases: Case[]
  total: number
  filters: object
}

interface Case {
  case_id: string
  title: string
  description: string
  status: 'open' | 'closed' | 'in_progress'
  priority: 'low' | 'medium' | 'high' | 'critical'
  assigned_to: string
  created_at: string
  updated_at: string
  workflow_stage: string
  evidence_count: number
  entity_count: number
}
```

**Widget:** Active Cases Table
**Priority:** HIGH

---

**GET /api/cases/{case_id}**

**Purpose:** Get full case details

**Response:**
```typescript
{
  case: {
    case_id: string
    title: string
    description: string
    status: string
    priority: string
    assigned_to: string
    created_at: string
    updated_at: string
    workflow_stage: string
    entities: Entity[]
    evidence: Evidence[]
    timeline: TimelineEvent[]
    investigation_guide?: InvestigationGuide
    mitre_techniques: string[]
  }
}
```

**Widget:** Case Details Page
**Priority:** HIGH
**Enrichment:** Investigation Layer 3

---

**PUT /api/cases/{case_id}**

**Purpose:** Update case (change status, priority, assignee)

**Request:**
```typescript
{
  status?: 'open' | 'closed' | 'in_progress'
  priority?: 'low' | 'medium' | 'high' | 'critical'
  assigned_to?: string
  notes?: string
}
```

---

**POST /api/cases/{case_id}/evidence**

**Purpose:** Add evidence to case

**Request:**
```typescript
{
  evidence_type: 'log' | 'screenshot' | 'file' | 'note'
  content: string | object
  source: string
  timestamp?: string
}
```

---

#### 5. Entity Management
**GET /api/entities**

**Purpose:** List all entities (for entity explorer)

**Query Parameters:**
- `type`: 'ip' | 'user' | 'host' | 'process' | 'file_hash' | 'domain'
- `threat_score_min`: number (0-100)
- `is_threat`: boolean
- `has_cti_match`: boolean
- `limit`: number
- `offset`: number

**Response:**
```typescript
{
  entities: Entity[]
  total: number
  filters: object
}

interface Entity {
  entity_id: string
  entity_type: 'ip' | 'user' | 'host' | 'process' | 'file_hash' | 'domain'
  entity_value: string
  first_seen: string
  last_seen: string
  occurrence_count: number
  threat_score: number  // 0-100
  is_threat: boolean
  tags: string[]
  risk_level: 'low' | 'medium' | 'high' | 'critical'
}
```

**Widget:** Entity Explorer Table
**Priority:** CRITICAL
**Enrichment:** CTI Layer 2 (threat_score, is_threat)

---

**GET /api/entities/{entity_id}**

**Purpose:** Get full entity details with enrichment

**Response:**
```typescript
{
  entity_id: string
  entity_type: string
  entity_value: string
  enrichments: {
    // Layer 1: Business & Technical
    geolocation?: {
      country: string
      city: string
      asn: string
      organization: string
    }
    threat_intel?: {
      is_malicious: boolean
      reputation_score: number
    }
    network_info?: {
      is_internal: boolean
      network_segment: string
    }

    // ✨ Layer 2: CTI Intelligence (NEW!)
    cti_threat_intelligence?: {
      entity: string
      entity_type: string
      source: 'otx' | 'threatfox' | 'crowdstrike' | 'opencti'
      threat_score: number  // 0-100
      tags: string[]  // ['malware', 'botnet', 'c2']
      campaign?: string  // 'APT28'
      threat_actor?: string  // 'Fancy Bear'
      first_seen: string
      match_type: 'exact' | 'fuzzy'
    }
  }
  threat_score: number
  is_threat: boolean
  last_updated: string
}
```

**Widget:** Entity Details Card
**Priority:** CRITICAL
**Enrichment:** ALL (Business + CTI + optional Investigation)

---

**GET /api/entities/{entity_id}/enrichment**

**Purpose:** Get detailed enrichment data (separate call for performance)

**Response:** Same as enrichments field above but with more detail

---

**GET /api/entities/{entity_id}/relationships**

**Purpose:** Get entity relationship graph data

**Response:**
```typescript
{
  entity_id: string
  relationships: Relationship[]
  entities: Entity[]  // Related entities
}

interface Relationship {
  source: string  // entity_id
  target: string  // entity_id
  type: 'connected_to' | 'accessed' | 'owns' | 'executes'
  weight: number  // 0-1 (connection strength)
  first_seen: string
  last_seen: string
  occurrence_count: number
}
```

**Widget:** Relationship Graph (D3.js visualization)
**Priority:** HIGH

---

**GET /api/entities/{entity_id}/timeline**

**Purpose:** Get entity activity timeline

**Response:**
```typescript
{
  entity_id: string
  timeline: TimelineEvent[]
}

interface TimelineEvent {
  event_id: string
  timestamp: string
  event_type: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  source: string
  related_entities: string[]
}
```

**Widget:** Entity Timeline View
**Priority:** MEDIUM

---

**GET /api/entities/{entity_id}/risk**

**Purpose:** Get risk assessment for entity

**Response:**
```typescript
{
  entity_id: string
  overall_risk: number  // 0-100
  risk_factors: {
    cti_threat_match: number  // 0-100
    behavior_anomaly: number
    privilege_level: number
    criticality: number
  }
  risk_trend: 'increasing' | 'stable' | 'decreasing'
  recommendations: string[]
}
```

**Widget:** Risk Dashboard, Entity Risk Card
**Priority:** MEDIUM
**Enrichment:** Layers 1 + 2

---

### 🔄 Workflow Orchestration

#### 6. Workflow Management
**POST /api/workflows/start**

**Purpose:** Start automated workflow

**Request:**
```typescript
{
  workflow_type: string  // 'incident_response', 'threat_hunt', 'compliance_check'
  params: {
    case_id?: string
    alert_id?: string
    entity_ids?: string[]
    // Workflow-specific params
  }
}
```

**Response:**
```typescript
{
  workflow_id: string
  workflow_type: string
  status: 'running'
  started_at: string
  estimated_completion: string
  steps: WorkflowStep[]
}
```

---

**GET /api/workflows**

**Purpose:** List active workflows

**Response:**
```typescript
{
  workflows: Workflow[]
  total: number
}

interface Workflow {
  workflow_id: string
  workflow_type: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number  // 0-100
  started_at: string
  completed_at?: string
  steps_completed: number
  steps_total: number
}
```

**Widget:** Workflow Monitor
**Priority:** MEDIUM

---

**GET /api/workflows/{workflow_id}**

**Purpose:** Get workflow status and details

**Response:**
```typescript
{
  workflow_id: string
  workflow_type: string
  status: string
  progress: number
  steps: WorkflowStep[]
  results?: object
  error?: string
}

interface WorkflowStep {
  step_id: string
  step_name: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  started_at?: string
  completed_at?: string
  result?: object
}
```

**Widget:** Workflow Details Modal
**Priority:** MEDIUM

---

**GET /api/workflows/templates**

**Purpose:** List available workflow templates (11 pre-configured)

**Response:**
```typescript
{
  templates: WorkflowTemplate[]
}

interface WorkflowTemplate {
  template_id: string
  name: string
  description: string
  category: 'incident_response' | 'threat_hunt' | 'compliance'
  steps_count: number
  estimated_duration: string
  required_params: string[]
}
```

**Widget:** Workflow Template Selector
**Priority:** LOW

---

**POST /api/workflows/{workflow_id}/cancel**

**Purpose:** Cancel running workflow

---

**GET /api/workflows/statistics**

**Purpose:** Get workflow execution statistics

**Response:**
```typescript
{
  total_executed: number
  success_rate: number
  avg_duration: number
  by_type: {
    [workflow_type: string]: {
      executed: number
      success_rate: number
    }
  }
}
```

---

### 📊 Pattern & Rule Management

#### 7. Pattern Library
**GET /api/patterns**

**Purpose:** List crystallized patterns (99.97% cost savings showcase)

**Query Parameters:**
- `type`: 'security_event' | 'attack_pattern' | 'baseline'
- `limit`: number
- `offset`: number

**Response:**
```typescript
{
  patterns: Pattern[]
  total: number
  cost_savings: {
    total_patterns: number
    patterns_reused: number
    cost_saved: number  // in dollars
    savings_percent: number  // 99.97
  }
}

interface Pattern {
  pattern_id: string
  pattern_type: string
  description: string
  created_at: string
  reuse_count: number
  match_count: number
  cost_per_use: number  // $0.00 after crystallization
  original_cost: number  // $0.02 during learning
}
```

**Widget:** Pattern Library Browser
**Priority:** MEDIUM
**Business Value:** Shows "learn expensive once, operate free forever"

---

**GET /api/patterns/{pattern_id}**

**Purpose:** Get pattern details

---

**POST /api/patterns/test**

**Purpose:** Test pattern against sample data

**Request:**
```typescript
{
  pattern: string
  test_data: string
}
```

---

**GET /api/patterns/{pattern_id}/performance**

**Purpose:** Get pattern performance metrics

**Response:**
```typescript
{
  pattern_id: string
  performance: {
    match_rate: number
    false_positive_rate: number
    avg_match_time: number  // milliseconds
    last_30_days_matches: number
    cost_savings: number
  }
}
```

---

#### 8. Rule Management
**GET /api/rules**

**Purpose:** List detection rules (generated from CTI)

**Response:**
```typescript
{
  rules: Rule[]
  total: number
}

interface Rule {
  rule_id: string
  rule_name: string
  rule_type: 'sigma' | 'yara' | 'snort'
  severity: 'low' | 'medium' | 'high' | 'critical'
  enabled: boolean
  created_at: string
  source: 'cti' | 'manual' | 'ai_generated'
  cti_source?: string  // 'otx', 'crowdstrike'
  match_count: number
  false_positive_rate: number
}
```

**Widget:** Rule Management Table
**Priority:** LOW

---

**GET /api/rules/{rule_id}**

**Purpose:** Get rule details

---

**POST /api/rules/test**

**Purpose:** Test rule against sample logs

---

### 🔐 User Management

#### 9. Authentication
**POST /api/auth/login**

**Purpose:** User login

**Request:**
```typescript
{
  username: string
  password: string
}
```

**Response:**
```typescript
{
  session_id: string
  user: {
    username: string
    role: string
    permissions: string[]
  }
  expires_at: string
}
```

---

**POST /api/auth/logout**

**Purpose:** User logout

---

**GET /api/auth/sessions**

**Purpose:** List active sessions

---

### 📈 System Status

#### 10. System Monitoring
**GET /api/system/status**

**Purpose:** Overall system health

**Response:**
```typescript
{
  status: 'healthy' | 'degraded' | 'critical'
  uptime: string
  components: {
    database: 'connected' | 'disconnected'
    redis: 'connected' | 'disconnected'
    engines: {
      intelligence: 'healthy' | 'degraded' | 'down'
      backend: 'healthy' | 'degraded' | 'down'
      ingestion: 'healthy' | 'degraded' | 'down'
      contextualization: 'healthy' | 'degraded' | 'down'
      delivery: 'healthy' | 'degraded' | 'down'
    }
  }
  performance: {
    logs_processed_today: number
    avg_processing_time: number
    error_rate: number
  }
}
```

**Widget:** System Status Dashboard
**Priority:** LOW

---

**GET /api/system/engines**

**Purpose:** Detailed engine status

**Response:**
```typescript
{
  engines: EngineStatus[]
}

interface EngineStatus {
  engine_name: string
  status: 'healthy' | 'degraded' | 'down'
  uptime: string
  messages_processed: number
  errors: number
  last_heartbeat: string
}
```

---

## Backend Engine (Port 8002)

**Base URL:** `http://localhost:8002`

### 🎯 UNIQUE FEATURES (Market Differentiators)

#### 11. Log Source Quality Assessment
**POST /api/log-sources/register**

**Purpose:** Register log source with automatic quality assessment

**Request:**
```typescript
{
  name: string  // 'CrowdStrike Falcon'
  type: 'endpoint' | 'network' | 'authentication' | 'cloud'
  product: string  // 'crowdstrike', 'wazuh', 'elastic'
  version: string
  capabilities: string[]  // ['kernel_visibility', 'ml_detection']
  api_enabled: boolean
  real_time: boolean
}
```

**Response:**
```typescript
{
  source_id: string
  name: string
  type: string
  tier: 'PLATINUM' | 'GOLD' | 'SILVER' | 'BRONZE'
  quality_score: number  // 0-100
  capabilities: string[]
  assessment: {
    strengths: string[]
    weaknesses: string[]
    recommendations: string[]
  }
  detection_capability: {
    ransomware: number  // 0-100 confidence
    lateral_movement: number
    data_exfiltration: number
    privilege_escalation: number
  }
}
```

**Widget:** Log Source Registration Form → Quality Assessment Card
**Priority:** HIGH
**Business Value:** **NO COMPETITOR HAS THIS**

---

**GET /api/log-sources/status**

**Purpose:** Get all log sources with quality metrics

**Response:**
```typescript
{
  total_sources: number
  sources: LogSource[]
  quality_summary: {
    platinum_count: number
    gold_count: number
    silver_count: number
    bronze_count: number
    avg_quality_score: number
  }
}

interface LogSource {
  source_id: string
  name: string
  type: string
  tier: string
  quality_score: number
  capabilities: string[]
  registered_at: string
  last_check: string
  status: 'active' | 'inactive'
}
```

**Widget:** Log Source Quality Matrix
**Priority:** HIGH
**Business Value:** **INDUSTRY FIRST - Quantitative quality assessment**

---

#### 12. Detection Fidelity Calculator ⭐ **CRITICAL DIFFERENTIATOR**
**POST /api/detection/fidelity**

**Purpose:** Calculate detection confidence for attacks based on log sources

**Request:**
```typescript
{
  attack_types: string[]  // ['ransomware', 'lateral_movement', 'data_exfiltration']
}
```

**Response:**
```typescript
{
  assessments: DetectionAssessment[]
  overall_confidence: number  // 0-100
}

interface DetectionAssessment {
  attack_type: string
  detection_confidence: number  // 0-100 percentage
  requirements_met: string[]
  requirements_missing: string[]
  current_sources: string[]
  recommended_sources: string[]
  impact_analysis: string
}

// Example response:
{
  "assessments": [
    {
      "attack_type": "ransomware",
      "detection_confidence": 35,  // ⚠️ LOW
      "requirements_met": ["endpoint_logs"],
      "requirements_missing": ["file_integrity", "backup_monitoring"],
      "current_sources": ["Wazuh"],
      "recommended_sources": ["CrowdStrike", "Varonis"],
      "impact_analysis": "Cannot detect file encryption in real-time"
    },
    {
      "attack_type": "lateral_movement",
      "detection_confidence": 95,  // ✅ HIGH
      "requirements_met": ["auth_logs", "network_logs", "endpoint_logs"],
      "requirements_missing": [],
      "current_sources": ["CrowdStrike", "Palo Alto", "AD"],
      "recommended_sources": [],
      "impact_analysis": "Excellent coverage - can correlate across sources"
    }
  ]
}
```

**Widget:** Detection Fidelity Calculator
**Priority:** **CRITICAL**
**Business Value:** **UNIQUE TO SIEMLESS - Quantifies "Can we detect X?"**

---

**GET /api/detection/coverage**

**Purpose:** Get overall detection coverage

**Response:**
```typescript
{
  overall_coverage: number  // 0-100
  coverage_by_category: {
    ransomware: number
    lateral_movement: number
    data_exfiltration: number
    privilege_escalation: number
    persistence: number
    credential_access: number
  }
  gaps: string[]
  strengths: string[]
}
```

**Widget:** Detection Coverage Heatmap
**Priority:** HIGH

---

**POST /api/detection/technique-coverage**

**Purpose:** MITRE ATT&CK technique coverage

**Request:**
```typescript
{
  technique_ids: string[]  // ['T1003', 'T1055', 'T1021', 'T1486']
}
```

**Response:**
```typescript
{
  techniques: TechniqueCoverage[]
}

interface TechniqueCoverage {
  technique_id: string
  technique_name: string
  detection_confidence: number  // 0-100
  required_sources: string[]
  available_sources: string[]
  coverage_level: 'none' | 'low' | 'medium' | 'high'
  recommendations: string[]
}
```

**Widget:** MITRE Heatmap, Technique Details
**Priority:** MEDIUM

---

#### 13. Coverage Simulation ⭐ **BUSINESS VALUE**
**POST /api/coverage/simulate**

**Purpose:** "What if we add/remove log sources?" analysis

**Request:**
```typescript
{
  add_sources?: LogSourceConfig[]  // Simulate adding CrowdStrike
  remove_sources?: string[]  // Simulate removing Wazuh
}
```

**Response:**
```typescript
{
  current_coverage: {
    overall: number
    by_attack_type: object
  }
  simulated_coverage: {
    overall: number  // NEW coverage after changes
    by_attack_type: object
  }
  delta: {
    overall_change: number  // +45 percentage points
    improvements: string[]
    degradations: string[]
  }
  roi_analysis: {
    cost_estimate: number  // Estimated cost of new source
    confidence_gain: number  // Percentage points gained
    cost_per_confidence_point: number
    recommendation: string
  }
}

// Example:
{
  "current_coverage": {"overall": 55},
  "simulated_coverage": {"overall": 88},  // +33 points!
  "delta": {
    "overall_change": 33,
    "improvements": ["Ransomware: 35% → 95%", "Lateral Movement: 60% → 100%"],
    "degradations": []
  },
  "roi_analysis": {
    "cost_estimate": 15000,  // $15k/year for CrowdStrike
    "confidence_gain": 33,
    "cost_per_confidence_point": 455,  // $455 per point
    "recommendation": "HIGH VALUE - Significant ransomware protection gain"
  }
}
```

**Widget:** Coverage Simulation Tool
**Priority:** HIGH
**Business Value:** **DRIVES TOOL PURCHASE DECISIONS**

---

**GET /api/coverage/gaps**

**Purpose:** Analyze coverage gaps

**Response:**
```typescript
{
  critical_gaps: Gap[]
  recommended_actions: Action[]
}

interface Gap {
  attack_type: string
  current_confidence: number
  target_confidence: number
  gap_size: number
  business_impact: 'low' | 'medium' | 'high' | 'critical'
  recommended_sources: string[]
}
```

---

#### 14. Correlation Capability
**GET /api/correlation/capability**

**Purpose:** Assess multi-source correlation ability

**Response:**
```typescript
{
  correlation_enabled: boolean
  available_correlations: CorrelationRule[]
  active_correlations: number
  sources_integrated: string[]
}
```

---

**POST /api/correlation/requirements**

**Purpose:** Check requirements for specific attack correlation

**Request:**
```typescript
{
  attack_type: string
}
```

---

**POST /api/correlation/recommendations**

**Purpose:** Get recommendations for correlation improvements

---

#### 15. Graph Analytics (Entity Relationships)
**GET /api/graph/explore**

**Purpose:** Explore entity graph

**Query Parameters:**
- `entity_id`: Starting entity
- `depth`: Relationship depth (1-3)
- `types`: Entity types to include

---

**GET /api/graph/path**

**Purpose:** Find paths between entities

**Query Parameters:**
- `source_entity`: Starting entity
- `target_entity`: Ending entity
- `max_hops`: Maximum path length

---

**GET /api/graph/stats**

**Purpose:** Graph statistics

**Response:**
```typescript
{
  total_entities: number
  total_relationships: number
  most_connected_entities: Entity[]
  relationship_distribution: object
}
```

---

**GET /api/graph/high-risk**

**Purpose:** Get high-risk entities

**Query Parameters:**
- `min_risk_score`: number (0-100)
- `limit`: number

---

**GET /api/graph/centrality**

**Purpose:** Get central entities (most connected)

---

## Ingestion Engine (Port 8003)

**Base URL:** `http://localhost:8003`

### 🔌 CTI Plugin Management (Universal Architecture)

#### 16. CTI Plugin Status ⭐ **SHOWCASE PLUGIN ARCHITECTURE**
**GET /cti/connectors**

**Purpose:** List all CTI plugins (shows universal architecture)

**Response:**
```typescript
{
  plugins: string[]  // ['otx', 'threatfox', 'crowdstrike', 'opencti']
  count: number
  source_types: {
    [plugin_name: string]: 'community' | 'commercial' | 'internal'
  }
}
```

**Widget:** CTI Plugin List
**Priority:** HIGH
**Business Value:** **Shows 4+ sources, not hardcoded integration**

---

**GET /cti/status**

**Purpose:** Health status of all CTI plugins

**Response:**
```typescript
{
  plugin_count: number
  plugins: string[]
  health: {
    [plugin_name: string]: {
      source: string
      healthy: boolean
      enabled: boolean
      priority: number  // 0-100
      type: 'community' | 'commercial' | 'internal'
      indicators_fetched?: number
      last_check?: string
      error?: string
    }
  }
}

// Example:
{
  "plugin_count": 4,
  "plugins": ["otx", "threatfox", "crowdstrike", "opencti"],
  "health": {
    "otx": {
      "source": "otx",
      "healthy": true,
      "enabled": true,
      "priority": 50,
      "type": "community",
      "indicators_fetched": 1250,
      "last_check": "2025-10-03T10:00:00Z"
    },
    "crowdstrike": {
      "source": "crowdstrike",
      "healthy": true,
      "enabled": true,
      "priority": 80,  // Higher priority = more trusted
      "type": "commercial",
      "indicators_fetched": 2500,
      "last_check": "2025-10-03T10:10:00Z"
    },
    "opencti": {
      "source": "opencti",
      "healthy": false,
      "enabled": true,
      "priority": 70,
      "type": "internal",
      "error": "Connection timeout"
    }
  }
}
```

**Widget:** CTI Plugin Status Dashboard
**Priority:** HIGH
**Business Value:** **Live demo of plugin architecture**

---

**POST /cti/manual_update**

**Purpose:** Manually trigger CTI data fetch

**Request:**
```typescript
{
  source: 'all' | 'otx' | 'threatfox' | 'crowdstrike' | 'opencti'
  since_days: number  // Fetch last N days
  limit: number  // Max indicators to fetch
}
```

**Response:**
```typescript
{
  status: 'success' | 'failed'
  indicators_fetched: number
  sources: string[]  // Which plugins were updated
  errors?: string[]
}
```

**Widget:** CTI Manual Refresh Button
**Priority:** MEDIUM

---

### 📝 Parser Management

#### 17. Parser Generation (AI-Powered)
**POST /api/parsers/generate**

**Purpose:** AI-generated parser from log samples

**Request:**
```typescript
{
  log_samples: string[]  // Array of raw log examples
  parser_name: string
  extraction_fields?: string[]  // Optional hints
}
```

**Response:**
```typescript
{
  parser_id: string
  parser_name: string
  format_type: string
  field_mappings: object
  entity_types: string[]
  validation_results: {
    coverage: number  // 0-100%
    entities_extracted: number
    confidence: number
  }
}
```

---

**GET /api/parsers**

**Purpose:** List all parsers

---

**GET /api/parsers/{parser_id}**

**Purpose:** Get parser details

---

**DELETE /api/parsers/{parser_id}**

**Purpose:** Delete parser

---

### 📊 Source Management

#### 18. Data Source Control
**GET /sources**

**Purpose:** List ingestion sources

---

**POST /sources/start**

**Purpose:** Start a data source

---

**POST /sources/stop**

**Purpose:** Stop a data source

---

**GET /stats**

**Purpose:** Ingestion statistics

**Response:**
```typescript
{
  stats: {
    logs_processed: number
    sources_active: number
    patterns_matched: number
  }
  performance_summary: {
    avg_processing_time: number
    throughput: number
  }
  health_status: string
}
```

---

**GET /tasks**

**Purpose:** Background task status

---

## Investigation APIs (Delivery Sub-system)

**Base URL:** `http://localhost:8005/api/v1/investigations`

### 🔍 Multi-Vendor Investigation (7 Vendors, 6.95B Events)

#### 19. Investigation Creation
**POST /api/v1/investigations**

**Purpose:** Create investigation with auto-enrichment

**Request:**
```typescript
{
  title: string
  alert_id?: string
  source_siem?: string  // 'elastic', 'splunk'
  severity: 'low' | 'medium' | 'high' | 'critical'
  entities: {
    ips?: string[]
    users?: string[]
    hosts?: string[]
    domains?: string[]
  }
  mitre_techniques?: string[]
  description?: string
  auto_enrich?: boolean  // Default: true
}
```

**Response:**
```typescript
{
  investigation_id: string
  status: 'created'
  enrichment_summary: {
    entities_enriched: number
    total_events: number  // Events found across all vendors
    risk_trend: 'increasing' | 'stable' | 'decreasing'
    vendors_queried: string[]  // ['crowdstrike', 'elastic', 'tippingpoint']
  }
}
```

**Widget:** Investigation Creation Modal
**Priority:** HIGH
**Enrichment:** **Layer 3 - Multi-Vendor Context**

---

**GET /api/v1/investigations/{investigation_id}**

**Purpose:** Get investigation details

**Response:**
```typescript
{
  investigation_id: string
  title: string
  status: 'open' | 'investigating' | 'closed'
  severity: string
  entities: object
  enrichment: {
    entity_contexts: {
      [entity_value: string]: {
        entity_type: string
        entity_value: string
        recent_events: Event[]  // From all vendors
        risk_score: number
        anomalies: Anomaly[]
        related_entities: Entity[]
      }
    }
    timeline: TimelineEvent[]
    summary: {
      total_events: number
      high_risk_entities: number
      overall_risk_trend: string
      key_findings: string[]
    }
  }
  created_at: string
  updated_at: string
  created_by: string
}
```

**Widget:** Investigation Details Page
**Priority:** HIGH

---

**GET /api/v1/investigations**

**Purpose:** List investigations

**Query Parameters:**
- `status`: 'open' | 'investigating' | 'closed'
- `severity`: 'low' | 'medium' | 'high' | 'critical'
- `limit`: number
- `offset`: number

---

**POST /api/v1/investigations/{investigation_id}/enrich**

**Purpose:** Re-enrich investigation with latest data

**Request:**
```typescript
{
  hours_back?: number  // How far back to query (default: 24)
  vendors?: string[]  // Specific vendors to query
}
```

---

**GET /api/v1/investigations/{investigation_id}/timeline**

**Purpose:** Get investigation timeline

**Response:**
```typescript
{
  investigation_id: string
  timeline: TimelineEvent[]
  summary: {
    total_events: number
    timespan: string
    event_types: string[]
  }
}
```

**Widget:** Investigation Timeline Visualization
**Priority:** HIGH

---

**GET /api/v1/investigations/{investigation_id}/evidence**

**Purpose:** Get collected evidence

---

**POST /api/v1/investigations/{investigation_id}/entities**

**Purpose:** Add entity to investigation

---

**PUT /api/v1/investigations/{investigation_id}/status**

**Purpose:** Update investigation status

---

## TypeScript Interface Definitions

```typescript
// ============================================
// CORE DATA TYPES
// ============================================

// Entity with Full Enrichment
interface Entity {
  entity_id: string
  entity_type: 'ip' | 'user' | 'host' | 'process' | 'file_hash' | 'domain' | 'port'
  entity_value: string
  first_seen: string
  last_seen: string
  occurrence_count: number

  // Enrichment Data
  enrichments: {
    // Layer 1: Business & Technical
    geolocation?: GeolocationData
    threat_intel?: ThreatIntelData
    network_info?: NetworkInfo
    asset_info?: AssetInfo
    user_info?: UserInfo
    process_info?: ProcessInfo

    // Layer 2: CTI Intelligence
    cti_threat_intelligence?: CTIMatch
  }

  // Top-level threat indicators
  threat_score: number  // 0-100
  is_threat: boolean
  risk_level: 'low' | 'medium' | 'high' | 'critical'
  tags: string[]
  last_updated: string
}

// CTI Match (Layer 2 Enrichment)
interface CTIMatch {
  entity: string
  entity_type: string
  source: 'otx' | 'threatfox' | 'crowdstrike' | 'opencti'
  threat_score: number  // 0-100
  tags: string[]  // ['malware', 'botnet', 'c2', 'ransomware']
  campaign?: string  // 'APT28', 'Emotet'
  threat_actor?: string  // 'Fancy Bear', 'Lazarus Group'
  first_seen: string
  last_seen?: string
  match_type: 'exact' | 'fuzzy' | 'pattern'
  description?: string
  mitre_techniques?: string[]
}

// Geolocation Data (Layer 1)
interface GeolocationData {
  country: string
  region?: string
  city?: string
  asn: string
  organization: string
  latitude?: number
  longitude?: number
}

// Threat Intel Data (Layer 1)
interface ThreatIntelData {
  is_malicious: boolean
  reputation_score: number  // 0-100
  threat_types: string[]
  last_seen?: string
  sources: string[]
}

// Network Info (Layer 1)
interface NetworkInfo {
  is_internal: boolean
  network_segment: 'dmz' | 'internal' | 'external' | 'guest'
  trust_level: number  // 0-100
}

// Asset Info (Layer 1)
interface AssetInfo {
  asset_type: string
  os?: string
  owner?: string
  location?: string
  criticality: 'low' | 'medium' | 'high' | 'critical'
  department?: string
}

// User Info (Layer 1)
interface UserInfo {
  full_name?: string
  department?: string
  manager?: string
  last_login?: string
  access_level: 'standard' | 'elevated' | 'admin'
  is_service_account: boolean
}

// Process Info (Layer 1)
interface ProcessInfo {
  executable_path?: string
  digital_signature?: string
  version?: string
  risk_score: number  // 0-100
  is_suspicious: boolean
}

// ============================================
// RELATIONSHIPS
// ============================================

interface Relationship {
  source: string  // entity_id
  target: string  // entity_id
  type: 'connected_to' | 'accessed' | 'owns' | 'executes' | 'communicates_with'
  weight: number  // 0-1 (connection strength)
  first_seen: string
  last_seen: string
  occurrence_count: number
  metadata?: object
}

// ============================================
// TIMELINE & EVENTS
// ============================================

interface TimelineEvent {
  event_id: string
  timestamp: string
  event_type: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  source: string  // 'crowdstrike', 'elastic', 'palo_alto'
  source_vendor?: string
  related_entities: string[]  // entity_ids
  raw_data?: object
  mitre_techniques?: string[]
}

// ============================================
// ALERTS & CASES
// ============================================

interface Alert {
  alert_id: string
  title: string
  description?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  source: string
  created_at: string
  updated_at?: string
  status: 'new' | 'acknowledged' | 'investigating' | 'closed'

  // Enrichment indicators
  entity_count: number
  has_cti_match: boolean
  threat_score?: number  // 0-100 if has_cti_match
  mitre_techniques?: string[]

  // Assignment
  assigned_to?: string
  case_id?: string  // If escalated to case
}

interface Case {
  case_id: string
  title: string
  description: string
  status: 'open' | 'investigating' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'critical'
  case_type: 'security_incident' | 'anomaly_detection' | 'threat_hunt' | 'compliance_violation'

  // Assignment & tracking
  assigned_to: string
  created_by: string
  created_at: string
  updated_at: string
  closed_at?: string

  // Content
  entities: Entity[]
  evidence: Evidence[]
  timeline: TimelineEvent[]
  mitre_techniques: string[]

  // Workflow
  workflow_stage: 'triage' | 'investigation' | 'remediation' | 'documentation' | 'closed'
  workflow_id?: string

  // Investigation
  investigation_guide?: InvestigationGuide
  findings?: string
  resolution?: string
}

interface Evidence {
  evidence_id: string
  evidence_type: 'log' | 'screenshot' | 'file' | 'note' | 'command_output'
  content: string | object
  source: string
  collected_at: string
  collected_by: string
  metadata?: object
}

// ============================================
// INVESTIGATION
// ============================================

interface Investigation {
  investigation_id: string
  title: string
  alert_id?: string
  source_siem?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  status: 'open' | 'investigating' | 'closed'

  entities: {
    ips?: string[]
    users?: string[]
    hosts?: string[]
    domains?: string[]
  }

  mitre_techniques: string[]
  description: string

  // Layer 3: Multi-Vendor Enrichment
  enrichment?: InvestigationEnrichment

  created_at: string
  updated_at: string
  created_by: string
  assigned_to?: string
}

interface InvestigationEnrichment {
  entity_contexts: {
    [entity_value: string]: EntityContext
  }
  timeline: TimelineEvent[]
  summary: {
    total_events: number
    vendors_queried: string[]
    high_risk_entities: number
    overall_risk_trend: 'increasing' | 'stable' | 'decreasing'
    key_findings: string[]
  }
}

interface EntityContext {
  entity_type: string
  entity_value: string
  recent_events: TimelineEvent[]  // From all vendors
  risk_score: number
  anomalies: Anomaly[]
  related_entities: Entity[]
  vendor_data: {
    [vendor: string]: object  // Raw vendor-specific data
  }
}

interface Anomaly {
  anomaly_type: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  detected_at: string
  baseline_value?: number
  current_value?: number
  deviation_percent?: number
}

interface InvestigationGuide {
  investigation_id: string
  generated_at: string
  ai_model: string
  steps: InvestigationStep[]
  estimated_time: string
  complexity: 'low' | 'medium' | 'high'
}

interface InvestigationStep {
  step_id: string
  step_number: number
  title: string
  description: string

  // Actionable queries per SIEM
  queries: {
    [siem: string]: string  // 'elastic': 'KQL query', 'splunk': 'SPL query'
  }

  evidence_required: string[]
  completed: boolean
  completed_at?: string
  completed_by?: string
  findings?: string
}

// ============================================
// WORKFLOW
// ============================================

interface Workflow {
  workflow_id: string
  workflow_type: string
  workflow_name: string
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled'
  progress: number  // 0-100

  started_at: string
  completed_at?: string
  paused_at?: string

  steps: WorkflowStep[]
  steps_completed: number
  steps_total: number

  params: object
  results?: object
  error?: string

  // Related entities
  case_id?: string
  alert_id?: string
  investigation_id?: string
}

interface WorkflowStep {
  step_id: string
  step_name: string
  step_type: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'

  started_at?: string
  completed_at?: string
  duration?: number  // seconds

  input?: object
  output?: object
  error?: string

  retry_count: number
  max_retries: number
}

interface WorkflowTemplate {
  template_id: string
  name: string
  description: string
  category: 'incident_response' | 'threat_hunt' | 'compliance' | 'forensics'

  steps_count: number
  estimated_duration: string

  required_params: string[]
  optional_params?: string[]

  tags: string[]
  created_at: string
  usage_count: number
}

// ============================================
// PATTERNS & RULES
// ============================================

interface Pattern {
  pattern_id: string
  pattern_type: 'security_event' | 'attack_pattern' | 'baseline' | 'anomaly'
  description: string
  pattern_logic: string | object

  created_at: string
  created_by: 'ai' | 'manual' | 'system'

  // Pattern crystallization metrics
  reuse_count: number
  match_count: number
  cost_per_use: number  // $0.00 after crystallization
  original_cost: number  // $0.02 during learning
  total_cost_saved: number

  performance: {
    match_rate: number
    false_positive_rate: number
    avg_match_time: number  // milliseconds
  }

  last_used: string
  enabled: boolean
}

interface Rule {
  rule_id: string
  rule_name: string
  rule_type: 'sigma' | 'yara' | 'snort' | 'suricata' | 'kql' | 'spl'
  rule_content: string

  severity: 'low' | 'medium' | 'high' | 'critical'
  enabled: boolean

  created_at: string
  updated_at: string
  created_by: string
  source: 'cti' | 'manual' | 'ai_generated' | 'harvested'
  cti_source?: string  // 'otx', 'crowdstrike'

  // Performance
  match_count: number
  false_positive_count: number
  false_positive_rate: number
  last_match?: string

  // MITRE mapping
  mitre_techniques: string[]
  tags: string[]
}

// ============================================
// LOG SOURCES & DETECTION
// ============================================

interface LogSource {
  source_id: string
  name: string
  type: 'endpoint' | 'network' | 'authentication' | 'cloud' | 'application'
  product: string  // 'crowdstrike', 'wazuh', 'palo_alto'
  vendor: string
  version: string

  // Quality assessment
  tier: 'PLATINUM' | 'GOLD' | 'SILVER' | 'BRONZE'
  quality_score: number  // 0-100

  capabilities: string[]  // ['kernel_visibility', 'ml_detection', 'behavioral']

  assessment: {
    strengths: string[]
    weaknesses: string[]
    recommendations: string[]
  }

  // Detection capability per attack type
  detection_capability: {
    ransomware: number  // 0-100
    lateral_movement: number
    data_exfiltration: number
    privilege_escalation: number
    persistence: number
    credential_access: number
  }

  registered_at: string
  last_check: string
  status: 'active' | 'inactive' | 'degraded'

  // Configuration
  api_enabled: boolean
  real_time: boolean
  retention_days: number
}

interface DetectionAssessment {
  attack_type: string
  detection_confidence: number  // 0-100 percentage

  requirements_met: string[]
  requirements_missing: string[]

  current_sources: string[]
  recommended_sources: string[]

  impact_analysis: string
  mitre_techniques: string[]
}

interface CoverageSimulation {
  current_coverage: {
    overall: number
    by_attack_type: {
      [attack_type: string]: number
    }
  }

  simulated_coverage: {
    overall: number
    by_attack_type: {
      [attack_type: string]: number
    }
  }

  delta: {
    overall_change: number  // Percentage points
    improvements: string[]
    degradations: string[]
    attack_type_changes: {
      [attack_type: string]: number  // Change in confidence
    }
  }

  roi_analysis: {
    cost_estimate: number  // Annual cost
    confidence_gain: number  // Total percentage points gained
    cost_per_confidence_point: number
    payback_period?: string
    recommendation: string
  }
}

// ============================================
// CTI PLUGINS
// ============================================

interface CTIPluginHealth {
  source: string  // 'otx', 'threatfox', 'crowdstrike', 'opencti'
  healthy: boolean
  enabled: boolean
  priority: number  // 0-100 (higher = more trusted)
  type: 'community' | 'commercial' | 'internal'

  indicators_fetched?: number
  last_check?: string
  last_update?: string
  error?: string

  rate_limit?: {
    limit: number
    remaining: number
    reset_at: string
  }
}

// ============================================
// SYSTEM STATUS
// ============================================

interface SystemStatus {
  status: 'healthy' | 'degraded' | 'critical'
  uptime: string

  components: {
    database: 'connected' | 'disconnected' | 'degraded'
    redis: 'connected' | 'disconnected' | 'degraded'
    engines: {
      intelligence: 'healthy' | 'degraded' | 'down'
      backend: 'healthy' | 'degraded' | 'down'
      ingestion: 'healthy' | 'degraded' | 'down'
      contextualization: 'healthy' | 'degraded' | 'down'
      delivery: 'healthy' | 'degraded' | 'down'
    }
  }

  performance: {
    logs_processed_today: number
    entities_extracted_today: number
    avg_processing_time: number  // milliseconds
    error_rate: number  // percentage
    throughput: number  // logs/second
  }

  storage: {
    database_size: number  // bytes
    redis_memory: number  // bytes
    entities_count: number
    relationships_count: number
  }
}

interface EngineStatus {
  engine_name: string
  status: 'healthy' | 'degraded' | 'down'
  uptime: string
  last_heartbeat: string

  messages_processed: number
  errors: number
  error_rate: number

  performance: {
    avg_processing_time: number
    messages_per_second: number
  }

  health_checks: {
    database: boolean
    redis: boolean
    http_server: boolean
  }
}

// ============================================
// DASHBOARD DATA
// ============================================

interface DashboardOverview {
  total_cases: number
  open_cases: number
  critical_alerts: number
  active_investigations: number

  recent_activity: Activity[]

  system_health: 'healthy' | 'degraded' | 'critical'

  // CTI metrics
  cti_indicators_active: number
  cti_matches_today: number

  // Processing metrics
  logs_processed_today: number
  entities_extracted_today: number

  // Threat landscape
  high_risk_entities: number
  active_threat_actors: string[]
}

interface Activity {
  activity_id: string
  activity_type: 'alert' | 'case_created' | 'case_updated' | 'investigation' | 'workflow'
  title: string
  description: string
  severity?: 'low' | 'medium' | 'high' | 'critical'
  timestamp: string
  user?: string
  entity_id?: string
}
```

---

## Widget-to-API Mapping Matrix

| Widget Name | Priority | APIs Required | Enrichment Layers | Complexity |
|-------------|----------|---------------|-------------------|------------|
| **Dashboard Overview** | CRITICAL | `GET /api/dashboard/overview` | None | Low |
| **Alert Queue Table** | CRITICAL | `GET /api/alerts` | CTI (Layer 2) | Low |
| **Alert Details Modal** | CRITICAL | `GET /api/alerts/{id}/context` | All 3 Layers | High |
| **CTI Plugin Status** | HIGH | `GET /cti/status`, `GET /cti/connectors`, `POST /cti/manual_update` | None | Medium |
| **Entity Explorer Table** | CRITICAL | `GET /api/entities` | CTI (Layer 2) | Low |
| **Entity Details Card** | CRITICAL | `GET /api/entities/{id}`, `GET /api/entities/{id}/enrichment` | All 3 Layers | High |
| **Entity Relationship Graph** | HIGH | `GET /api/entities/{id}/relationships` | Business (Layer 1) | High |
| **Entity Timeline** | MEDIUM | `GET /api/entities/{id}/timeline` | Business (Layer 1) | Medium |
| **Entity Risk Card** | MEDIUM | `GET /api/entities/{id}/risk` | Layers 1 + 2 | Medium |
| **Log Source Quality Matrix** | HIGH | `GET /api/log-sources/status` | None | Low |
| **Log Source Registration** | MEDIUM | `POST /api/log-sources/register` | None | Medium |
| **Detection Fidelity Calculator** | CRITICAL | `POST /api/detection/fidelity` | None | Medium |
| **Detection Coverage Heatmap** | HIGH | `GET /api/detection/coverage` | None | Medium |
| **Coverage Simulation Tool** | HIGH | `POST /api/coverage/simulate` | None | High |
| **MITRE Heatmap** | MEDIUM | `POST /api/detection/technique-coverage` | None | High |
| **Active Cases Table** | HIGH | `GET /api/cases` | None | Low |
| **Case Details Page** | HIGH | `GET /api/cases/{id}` | Investigation (Layer 3) | High |
| **Case Creation Form** | MEDIUM | `POST /api/cases` | None | Medium |
| **Investigation Dashboard** | HIGH | `GET /api/v1/investigations`, `GET /api/v1/investigations/{id}` | Investigation (Layer 3) | High |
| **Investigation Timeline** | HIGH | `GET /api/v1/investigations/{id}/timeline` | Investigation (Layer 3) | High |
| **Investigation Creation** | MEDIUM | `POST /api/v1/investigations` | Investigation (Layer 3) | High |
| **Workflow Monitor** | MEDIUM | `GET /api/workflows`, `GET /api/workflows/{id}` | None | Medium |
| **Workflow Template Selector** | LOW | `GET /api/workflows/templates` | None | Low |
| **Workflow Statistics** | LOW | `GET /api/workflows/statistics` | None | Low |
| **Pattern Library Browser** | MEDIUM | `GET /api/patterns`, `GET /api/patterns/{id}/performance` | None | Medium |
| **Pattern Test Tool** | LOW | `POST /api/patterns/test` | None | Low |
| **Rule Management Table** | LOW | `GET /api/rules` | None | Low |
| **Rule Details** | LOW | `GET /api/rules/{id}` | None | Low |
| **Parser Generation** | LOW | `POST /api/parsers/generate` | None | Medium |
| **Parser List** | LOW | `GET /api/parsers` | None | Low |
| **System Status Dashboard** | LOW | `GET /api/system/status`, `GET /api/system/engines` | None | Low |
| **Graph Explorer** | MEDIUM | `GET /api/graph/explore`, `GET /api/graph/path` | Business (Layer 1) | High |
| **High-Risk Entities** | MEDIUM | `GET /api/graph/high-risk` | CTI (Layer 2) | Medium |

---

## Integration Patterns

### Pattern 1: Simple Data Fetch
**Use Case:** Dashboard overview, status displays

```typescript
// src/api/services/dashboard.ts
import apiClient from '../client'

export const dashboardAPI = {
  getOverview: () => apiClient.get('/api/dashboard/overview')
}

// Component
import { dashboardAPI } from '@/api/services/dashboard'

const DashboardOverview = () => {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    dashboardAPI.getOverview()
      .then(res => setData(res.data))
      .finally(() => setLoading(false))
  }, [])

  if (loading) return <LoadingSpinner />
  return <DashboardCard data={data} />
}
```

---

### Pattern 2: Filtered Lists
**Use Case:** Alert queue, entity explorer, case management

```typescript
// Zustand store
interface AlertStore {
  alerts: Alert[]
  filters: {
    status: string
    severity: string
  }
  fetchAlerts: (filters?) => Promise<void>
}

export const useAlertStore = create<AlertStore>((set, get) => ({
  alerts: [],
  filters: { status: 'all', severity: 'all' },

  fetchAlerts: async (filters) => {
    const response = await apiClient.get('/api/alerts', { params: filters })
    set({ alerts: response.data.alerts, filters })
  }
}))

// Component
const AlertQueue = () => {
  const { alerts, filters, fetchAlerts } = useAlertStore()

  useEffect(() => {
    fetchAlerts(filters)
  }, [filters])

  return (
    <div>
      <FilterBar filters={filters} onFilterChange={fetchAlerts} />
      <AlertTable alerts={alerts} />
    </div>
  )
}
```

---

### Pattern 3: Multi-Layer Enrichment Display
**Use Case:** Entity details, alert context

```typescript
// Entity Details with all 3 enrichment layers
const EntityDetails = ({ entityId }: { entityId: string }) => {
  const [entity, setEntity] = useState<Entity | null>(null)

  useEffect(() => {
    apiClient.get(`/api/entities/${entityId}`)
      .then(res => setEntity(res.data))
  }, [entityId])

  if (!entity) return <LoadingSpinner />

  return (
    <div className="entity-details">
      {/* Header with threat score */}
      <EntityHeader
        value={entity.entity_value}
        type={entity.entity_type}
        threatScore={entity.threat_score}
        isThreat={entity.is_threat}
      />

      {/* Layer 1: Business & Technical */}
      <EnrichmentSection title="Technical Context">
        {entity.enrichments.geolocation && (
          <GeolocationCard data={entity.enrichments.geolocation} />
        )}
        {entity.enrichments.network_info && (
          <NetworkInfoCard data={entity.enrichments.network_info} />
        )}
      </EnrichmentSection>

      {/* Layer 2: CTI Intelligence */}
      {entity.enrichments.cti_threat_intelligence && (
        <EnrichmentSection title="Threat Intelligence" severity="critical">
          <CTIMatchCard data={entity.enrichments.cti_threat_intelligence} />
        </EnrichmentSection>
      )}

      {/* Actions */}
      <EntityActions entityId={entityId} />
    </div>
  )
}
```

---

### Pattern 4: Real-Time Updates
**Use Case:** Alert queue, workflow monitor

```typescript
import { useEffect } from 'react'
import { wsClient } from '@/api/client'

const AlertQueueLive = () => {
  const { alerts, addAlert, fetchAlerts } = useAlertStore()

  useEffect(() => {
    // Initial fetch
    fetchAlerts()

    // Subscribe to real-time updates
    wsClient.connect()
    wsClient.on('delivery.alert_sent', (data) => {
      addAlert(data.alert)
    })

    return () => {
      wsClient.off('delivery.alert_sent')
    }
  }, [])

  return <AlertTable alerts={alerts} />
}
```

---

### Pattern 5: Multi-Endpoint Data Aggregation
**Use Case:** Investigation dashboard, case details

```typescript
const InvestigationDetails = ({ investigationId }: { investigationId: string }) => {
  const [investigation, setInvestigation] = useState(null)
  const [timeline, setTimeline] = useState([])
  const [evidence, setEvidence] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Parallel fetches
    Promise.all([
      apiClient.get(`/api/v1/investigations/${investigationId}`),
      apiClient.get(`/api/v1/investigations/${investigationId}/timeline`),
      apiClient.get(`/api/v1/investigations/${investigationId}/evidence`)
    ])
      .then(([invRes, timelineRes, evidenceRes]) => {
        setInvestigation(invRes.data)
        setTimeline(timelineRes.data.timeline)
        setEvidence(evidenceRes.data)
      })
      .finally(() => setLoading(false))
  }, [investigationId])

  if (loading) return <LoadingSpinner />

  return (
    <div>
      <InvestigationHeader data={investigation} />
      <Timeline events={timeline} />
      <EvidenceList items={evidence} />
    </div>
  )
}
```

---

## Error Handling Patterns

```typescript
// Centralized error handler
apiClient.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // Unauthorized - redirect to login
      window.location.href = '/login'
    } else if (error.response?.status === 403) {
      // Forbidden - show permission error
      toast.error('You do not have permission to access this resource')
    } else if (error.response?.status === 404) {
      // Not found - show not found message
      toast.error('Resource not found')
    } else if (error.response?.status >= 500) {
      // Server error
      toast.error('Server error. Please try again later.')
    }
    return Promise.reject(error)
  }
)
```

---

## Loading State Patterns

```typescript
// Skeleton loading for tables
const AlertQueueSkeleton = () => (
  <div className="skeleton-table">
    {[...Array(5)].map((_, i) => (
      <div key={i} className="skeleton-row">
        <div className="skeleton-cell" />
        <div className="skeleton-cell" />
        <div className="skeleton-cell" />
      </div>
    ))}
  </div>
)

// Suspense wrapper
const AlertQueue = React.lazy(() => import('./AlertQueue'))

const App = () => (
  <Suspense fallback={<AlertQueueSkeleton />}>
    <AlertQueue />
  </Suspense>
)
```

---

## Priority Build Order

### Week 1-2: Foundation
1. Dashboard Overview
2. Alert Queue
3. Entity Explorer Table
4. CTI Plugin Status

### Week 3-4: Detection
5. Log Source Quality Matrix
6. Detection Fidelity Calculator
7. Coverage Simulation Tool
8. Entity Details Card (all 3 layers)

### Week 5-6: Investigation
9. Alert Details Modal
10. Case Management
11. Investigation Dashboard
12. Entity Relationship Graph

### Week 7-8: Advanced
13. MITRE Heatmap
14. Workflow Monitor
15. Pattern Library
16. Investigation Timeline

---

**Total APIs Documented:** 60+
**Total Widgets Mapped:** 30+
**TypeScript Interfaces:** Complete
**Integration Patterns:** 5 core patterns
**Status:** ✅ **READY FOR SYSTEMATIC FRONTEND DEVELOPMENT**

*Last Updated: October 3, 2025*
