import React, { useState } from 'react'
import { Plus, Shield, Search, FileText, AlertTriangle } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

const QuickActions: React.FC = () => {
  const navigate = useNavigate()
  const [showMenu, setShowMenu] = useState(false)

  const actions = [
    {
      label: 'New Investigation',
      icon: Search,
      path: '/investigation/new',
      shortcut: 'Ctrl+N'
    },
    {
      label: 'Create Case',
      icon: FileText,
      path: '/cases/new',
      shortcut: 'Ctrl+C'
    },
    {
      label: 'Isolate Host',
      icon: Shield,
      action: () => console.log('Isolate host'),
      shortcut: 'Ctrl+I'
    },
    {
      label: 'Report Incident',
      icon: AlertTriangle,
      path: '/incidents/new',
      shortcut: 'Ctrl+R'
    }
  ]

  const handleAction = (action: any) => {
    if (action.path) {
      navigate(action.path)
    } else if (action.action) {
      action.action()
    }
    setShowMenu(false)
  }

  return (
    <div className="relative">
      <button
        onClick={() => setShowMenu(!showMenu)}
        className="p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
        title="Quick Actions (Ctrl+Q)"
      >
        <Plus size={20} />
      </button>

      {showMenu && (
        <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border z-50">
          <div className="p-2">
            {actions.map((action, idx) => (
              <button
                key={idx}
                onClick={() => handleAction(action)}
                className="w-full flex items-center gap-3 px-3 py-2 hover:bg-gray-100 rounded text-left"
              >
                <action.icon size={18} className="text-gray-600" />
                <span className="flex-1 text-sm">{action.label}</span>
                <kbd className="text-xs text-gray-500">{action.shortcut}</kbd>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default QuickActions