import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface Breadcrumb {
  label: string
  path: string
  icon?: React.ComponentType
}

interface RecentItem {
  id: string
  label: string
  path: string
  timestamp: number
  type: 'case' | 'entity' | 'pattern' | 'document'
}

interface QuickAction {
  id: string
  label: string
  icon: React.ComponentType
  shortcut?: string
  action: () => void
  requiredRole?: string
}

interface NavigationStore {
  // Sidebar state
  sidebarExpanded: boolean
  sidebarLocked: boolean

  // Navigation context
  currentRoute: string
  breadcrumbs: Breadcrumb[]
  recentItems: RecentItem[]

  // User preferences
  favoriteRoutes: string[]
  quickActions: QuickAction[]

  // Actions
  toggleSidebar: () => void
  setSidebarLocked: (locked: boolean) => void
  navigateTo: (route: string, breadcrumb?: Breadcrumb) => void
  addToRecent: (item: Omit<RecentItem, 'timestamp'>) => void
  addFavorite: (route: string) => void
  removeFavorite: (route: string) => void
  setBreadcrumbs: (breadcrumbs: Breadcrumb[]) => void
  clearBreadcrumbs: () => void
}

export const useNavigationStore = create<NavigationStore>()(
  persist(
    (set, get) => ({
      // Initial state
      sidebarExpanded: true,
      sidebarLocked: false,
      currentRoute: '/',
      breadcrumbs: [],
      recentItems: [],
      favoriteRoutes: [],
      quickActions: [],

      // Actions
      toggleSidebar: () => set((state) => ({
        sidebarExpanded: !state.sidebarExpanded
      })),

      setSidebarLocked: (locked) => set({ sidebarLocked: locked }),

      navigateTo: (route, breadcrumb) => {
        set((state) => {
          const newBreadcrumbs = breadcrumb
            ? [...state.breadcrumbs, breadcrumb]
            : state.breadcrumbs

          return {
            currentRoute: route,
            breadcrumbs: newBreadcrumbs
          }
        })
      },

      addToRecent: (item) => {
        set((state) => {
          const newItem: RecentItem = {
            ...item,
            timestamp: Date.now()
          }

          // Keep only last 10 recent items
          const filtered = state.recentItems.filter(i => i.id !== item.id)
          const updated = [newItem, ...filtered].slice(0, 10)

          return { recentItems: updated }
        })
      },

      addFavorite: (route) => {
        set((state) => ({
          favoriteRoutes: [...new Set([...state.favoriteRoutes, route])]
        }))
      },

      removeFavorite: (route) => {
        set((state) => ({
          favoriteRoutes: state.favoriteRoutes.filter(r => r !== route)
        }))
      },

      setBreadcrumbs: (breadcrumbs) => set({ breadcrumbs }),

      clearBreadcrumbs: () => set({ breadcrumbs: [] })
    }),
    {
      name: 'navigation-storage',
      partialize: (state) => ({
        sidebarLocked: state.sidebarLocked,
        favoriteRoutes: state.favoriteRoutes,
        recentItems: state.recentItems
      })
    }
  )
)

// Layout store for managing panels and widgets
interface LayoutStore {
  // Layout state
  rightDrawerOpen: boolean
  bottomPanelOpen: boolean
  fullscreenWidget: string | null

  // Dashboard configuration
  dashboardLayout: any // FlexLayout model
  savedLayouts: Array<{
    id: string
    name: string
    layout: any
    isDefault?: boolean
  }>

  // Actions
  toggleRightDrawer: () => void
  toggleBottomPanel: () => void
  setFullscreenWidget: (widgetId: string | null) => void
  saveDashboardLayout: (name: string) => void
  loadDashboardLayout: (id: string) => void
  setDefaultLayout: (id: string) => void
}

export const useLayoutStore = create<LayoutStore>()(
  persist(
    (set, get) => ({
      // Initial state
      rightDrawerOpen: false,
      bottomPanelOpen: false,
      fullscreenWidget: null,
      dashboardLayout: null,
      savedLayouts: [],

      // Actions
      toggleRightDrawer: () => set((state) => ({
        rightDrawerOpen: !state.rightDrawerOpen
      })),

      toggleBottomPanel: () => set((state) => ({
        bottomPanelOpen: !state.bottomPanelOpen
      })),

      setFullscreenWidget: (widgetId) => set({
        fullscreenWidget: widgetId
      }),

      saveDashboardLayout: (name) => {
        set((state) => {
          const newLayout = {
            id: `layout-${Date.now()}`,
            name,
            layout: state.dashboardLayout
          }

          return {
            savedLayouts: [...state.savedLayouts, newLayout]
          }
        })
      },

      loadDashboardLayout: (id) => {
        const layout = get().savedLayouts.find(l => l.id === id)
        if (layout) {
          set({ dashboardLayout: layout.layout })
        }
      },

      setDefaultLayout: (id) => {
        set((state) => ({
          savedLayouts: state.savedLayouts.map(layout => ({
            ...layout,
            isDefault: layout.id === id
          }))
        }))
      }
    }),
    {
      name: 'layout-storage',
      partialize: (state) => ({
        savedLayouts: state.savedLayouts
      })
    }
  )
)