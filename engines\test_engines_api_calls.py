#!/usr/bin/env python3
"""
Test Real API Integration Through Engines (via Redis)
Engines have the API keys loaded - we just send them requests
"""

import json
import redis
import time

class EngineAPITester:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)

    def test_crowdstrike_via_ingestion(self):
        """Test CrowdStrike API through Ingestion Engine"""
        print("\n[CROWDSTRIKE] Testing via Ingestion Engine...")

        request = {
            'request_id': f'cs_test_{int(time.time())}',
            'source': 'crowdstrike',
            'action': 'fetch_recent_detections',
            'params': {
                'limit': 10,
                'time_range': '1h'
            }
        }

        self.redis_client.publish('ingestion.crowdstrike_request', json.dumps(request))
        print(f"   [SENT] CrowdStrike request: {request['request_id']}")

    def test_opencti_via_backend(self):
        """Test OpenCTI API through Backend Engine"""
        print("\n[OPENCTI] Testing via Backend Engine...")

        request = {
            'request_id': f'opencti_test_{int(time.time())}',
            'source': 'opencti',
            'action': 'fetch_indicators',
            'params': {
                'limit': 5,
                'types': ['malware', 'indicator']
            }
        }

        self.redis_client.publish('backend.cti_request', json.dumps(request))
        print(f"   [SENT] OpenCTI request: {request['request_id']}")

    def test_elasticsearch_via_ingestion(self):
        """Test ElasticSearch API through Ingestion Engine"""
        print("\n[ELASTICSEARCH] Testing via Ingestion Engine...")

        request = {
            'request_id': f'es_test_{int(time.time())}',
            'source': 'elasticsearch',
            'action': 'search_security_events',
            'params': {
                'time_range': '1h',
                'event_types': ['authentication', 'network', 'process'],
                'limit': 20
            }
        }

        self.redis_client.publish('ingestion.elasticsearch_request', json.dumps(request))
        print(f"   [SENT] ElasticSearch request: {request['request_id']}")

    def test_ai_contextualization_via_intelligence(self):
        """Test AI contextualization through Intelligence Engine"""
        print("\n[AI] Testing Real AI Models via Intelligence Engine...")

        # Test with actual security event
        request = {
            'request_id': f'ai_test_{int(time.time())}',
            'pattern_data': {
                'event_type': 'failed_authentication',
                'source_ip': '*************',
                'target_user': 'administrator',
                'failure_count': 15,
                'time_window': '5_minutes',
                'additional_context': 'Multiple failed login attempts from internal IP to domain admin account'
            },
            'complexity': 'complex',  # This will use Claude/GPT-4 - real API calls
            'task': 'threat_assessment_and_pattern_analysis'
        }

        self.redis_client.publish('intelligence.consensus', json.dumps(request))
        print(f"   [SENT] AI contextualization request: {request['request_id']}")

    def monitor_engine_responses(self, duration=45):
        """Monitor Redis for engine responses"""
        print(f"\n[MONITOR] Listening for engine responses ({duration}s)...")

        pubsub = self.redis_client.pubsub()

        # Subscribe to all engine response channels
        channels = [
            'ingestion.crowdstrike_response',
            'ingestion.elasticsearch_response',
            'backend.cti_response',
            'intelligence.consensus_result',
            'intelligence.pattern_crystallized',
            'contextualization.entities_extracted',
            'delivery.alert_created'
        ]

        for channel in channels:
            pubsub.subscribe(channel)

        print(f"   [SUBSCRIBED] to {len(channels)} response channels")

        start_time = time.time()
        responses = []

        for message in pubsub.listen():
            if time.time() - start_time > duration:
                break

            if message['type'] == 'message':
                channel = message['channel']
                try:
                    data = json.loads(message['data'])
                    responses.append({
                        'channel': channel,
                        'data': data,
                        'timestamp': time.time()
                    })

                    print(f"\n   [RESPONSE] {channel}")
                    print(f"   [DATA] {json.dumps(data, indent=4)[:300]}...")

                except Exception as e:
                    print(f"   [ERROR] Failed to parse response: {e}")

        print(f"\n[SUMMARY] Received {len(responses)} responses from engines")
        return responses

    def run_api_tests(self):
        """Run all API tests through engines"""
        print("=== Testing Real APIs Through SIEMLess v2.0 Engines ===")
        print("Engines have API keys loaded - sending requests via Redis")
        print("=" * 60)

        # Test Redis connection
        try:
            self.redis_client.ping()
            print("[OK] Redis connection established")
        except Exception as e:
            print(f"[FAIL] Redis connection failed: {e}")
            return False

        # Send all API test requests
        self.test_crowdstrike_via_ingestion()
        self.test_opencti_via_backend()
        self.test_elasticsearch_via_ingestion()
        self.test_ai_contextualization_via_intelligence()

        print(f"\n[INFO] All API requests sent to engines")
        print("[INFO] Engines will make actual API calls with their loaded credentials")

        # Monitor for responses
        responses = self.monitor_engine_responses(45)

        # Analyze results
        if responses:
            print(f"\n[SUCCESS] Engines responded with real API data!")
            print(f"[DETAILS] Check engine logs for full API call details:")
            print("   docker-compose logs ingestion_engine")
            print("   docker-compose logs backend_engine")
            print("   docker-compose logs intelligence_engine")
        else:
            print(f"\n[WARNING] No responses received - engines may need implementation")
            print("[INFO] This is expected if engines don't have API call handlers yet")

        return len(responses) > 0

if __name__ == "__main__":
    tester = EngineAPITester()
    tester.run_api_tests()