# Correlation Engine Integration

## Overview

The Correlation Engine is now fully integrated into the Backend Engine, providing multi-source event correlation, detection fidelity assessment, and log source quality evaluation. This integration enables SIEMLess v2 to detect complex attacks that require correlation across multiple log sources.

## Architecture

### Integration Points

1. **Backend Engine** (Port 8002)
   - Hosts the correlation engine as a core component
   - Manages correlation rules and contexts
   - Assesses detection fidelity based on available log sources

2. **Redis Channels** (Port 6380)
   - Inter-engine communication for correlation events
   - Real-time alert distribution
   - Capability queries and assessments

## Key Features

### 1. Multi-Source Correlation

The system correlates events across multiple log sources to detect complex attack patterns:

- **Lateral Movement**: Correlates auth, firewall, and EDR events
- **Data Exfiltration**: Combines proxy, firewall, and EDR signals
- **Ransomware**: Analyzes EDR behavioral patterns
- **Privilege Escalation**: Links auth and EDR events

### 2. Log Source Quality Assessment

Different security products provide vastly different quality of telemetry that directly impacts detection fidelity:

#### Quality Tier System

| Tier | Score | Characteristics | Examples |
|------|-------|----------------|----------|
| **PLATINUM** | 95-100 | Kernel-level visibility, real-time streaming, behavioral detection | CrowdStrike (98), SentinelOne (96), Active Directory (92) |
| **GOLD** | 80-94 | System-level visibility, structured data, near real-time | Zeek (88), Palo Alto (85), AWS CloudTrail (87), Sysmon (78) |
| **SILVER** | 65-79 | Application-level logs, periodic collection, semi-structured | Windows Events (70), Cisco ASA (72), Web Server logs (65) |
| **BRONZE** | <65 | Basic logging, unstructured, delayed collection | Syslog (45), Basic firewall (55), Application logs (50) |

#### Source Quality Impact on Detection

```python
# Detection Confidence Formula
detection_confidence = base_score * tier_multiplier * synergy_bonus

# Example: Ransomware Detection
With CrowdStrike (PLATINUM):  95% confidence
With Sysmon (GOLD):           70% confidence
With Windows Events (SILVER):  45% confidence
With Syslog (BRONZE):         20% confidence
```

### 3. Detection Fidelity Assessment

The system calculates detection confidence based on available log sources and their quality:

#### Multi-Source Correlation Requirements

```python
# Lateral Movement Detection
REQUIRED_SOURCES = {
    'primary': {'type': 'identity', 'min_tier': 'GOLD'},
    'secondary': {'type': 'network', 'min_tier': 'SILVER'},
    'tertiary': {'type': 'endpoint', 'min_tier': 'GOLD'}
}

# Detection Confidence by Source Availability
- All PLATINUM sources: 98% confidence
- All GOLD sources: 85% confidence
- Mixed tiers: 60-75% confidence
- Single source: 20-40% confidence
```

#### Attack Detection Requirements Matrix

| Attack Type | Required Sources | Minimum Tier | Detection Confidence |
|------------|-----------------|--------------|---------------------|
| **Ransomware** | EDR + File Integrity + Network | GOLD + SILVER + SILVER | 85-95% |
| **Lateral Movement** | Identity + Network + EDR | GOLD + SILVER + GOLD | 75-90% |
| **Data Exfiltration** | Network + DLP + Cloud | GOLD + SILVER + SILVER | 70-85% |
| **Insider Threat** | Identity + Database + File Access | PLATINUM + GOLD + SILVER | 80-92% |
| **APT Campaign** | Full Stack (4+ sources) | Mixed PLATINUM/GOLD | 90-98% |

## Redis Channel Architecture

### Incoming Channels (Backend Engine subscribes)

```
ingestion.events.parsed       # Raw events from Ingestion Engine
contextualization.events.enriched  # Enriched events with context
correlation.check_event       # Explicit correlation check requests
correlation.assess_fidelity   # Fidelity assessment requests
correlation.get_capabilities  # Capability query requests
```

### Outgoing Channels (Backend Engine publishes)

```
correlation.alert            # Correlation-detected alerts
correlation.check_result     # Results of correlation checks
correlation.fidelity_result  # Fidelity assessment results
correlation.capabilities     # Current correlation capabilities
delivery.high_priority_alert # Critical alerts for immediate action
delivery.ransomware_alert    # Ransomware-specific alerts
```

## Correlation Rules with Source Quality Requirements

### Rule Types

1. **Simple Rules**: Single event matching (any tier)
2. **Correlation Rules**: Multi-source event correlation (requires SILVER+)
3. **Sequence Rules**: Ordered event sequences (requires GOLD+)
4. **Aggregation Rules**: Threshold-based detection (requires SILVER+)
5. **Behavioral Rules**: Pattern-based anomaly detection (requires PLATINUM)

### Pre-Built Rules with Minimum Source Requirements

```python
# Lateral Movement Detection
RULE = {
    'name': 'lateral_movement',
    'required_sources': [
        {'type': 'identity', 'min_tier': 'GOLD', 'events': ['4624', '4648', '4672']},
        {'type': 'network', 'min_tier': 'SILVER', 'events': ['rdp', 'smb', 'ssh']},
        {'type': 'endpoint', 'min_tier': 'GOLD', 'events': ['process_creation', 'psexec']}
    ],
    'time_window': 300,  # 5 minutes
    'min_confidence': 75,
    'detection_quality': {
        'all_platinum': 98,
        'all_gold': 85,
        'mixed': 70,
        'minimum': 60
    }
}

# Data Exfiltration Detection
RULE = {
    'name': 'data_exfiltration',
    'required_sources': [
        {'type': 'network', 'min_tier': 'GOLD', 'events': ['large_upload', 'dns_tunnel']},
        {'type': 'dlp', 'min_tier': 'SILVER', 'events': ['sensitive_data_movement']},
        {'type': 'cloud', 'min_tier': 'SILVER', 'events': ['unauthorized_share']}
    ],
    'time_window': 3600,  # 1 hour
    'thresholds': {'data_volume': '100MB', 'unique_destinations': 3}
}

# Ransomware Detection
RULE = {
    'name': 'ransomware',
    'required_sources': [
        {'type': 'endpoint', 'min_tier': 'GOLD', 'events': ['mass_file_modification']},
        {'type': 'file_integrity', 'min_tier': 'SILVER', 'events': ['encryption_detected']},
        {'type': 'backup', 'min_tier': 'BRONZE', 'events': ['shadow_copy_deleted']}
    ],
    'time_window': 600,  # 10 minutes
    'behavioral_indicators': ['file_entropy_increase', 'ransom_note_creation']
}
```

### Source Synergy Bonuses

```python
SYNERGY_MATRIX = {
    'endpoint_network': {
        'sources': ['EDR', 'Network_IDS'],
        'bonus_multiplier': 1.5,
        'detection_improvement': '+50% for C2, lateral movement'
    },
    'identity_endpoint': {
        'sources': ['AD/Azure_AD', 'EDR'],
        'bonus_multiplier': 1.6,
        'detection_improvement': '+60% for credential attacks'
    },
    'full_stack': {
        'sources': ['EDR', 'Network', 'Identity', 'Cloud'],
        'bonus_multiplier': 2.0,
        'detection_improvement': '+100% for APT campaigns'
    }
}
```

## Testing

### Test Script Usage

```bash
# Run correlation integration tests
python test_correlation_integration.py
```

The test suite verifies:
1. Basic correlation event flow
2. Detection fidelity assessment
3. Capability queries
4. Enriched event correlation
5. Batch correlation checks

### Expected Test Results

```
TEST 1: BASIC CORRELATION EVENT FLOW
[PUBLISHED] Events for lateral movement
[RESULT] Correlation alert triggered

TEST 2: DETECTION FIDELITY ASSESSMENT
Lateral Movement: 95% confidence (high fidelity)
Ransomware: 98% confidence (high fidelity)
Data Exfiltration: 55% confidence (moderate fidelity)

TEST 3: CORRELATION CAPABILITY QUERY
Total Rules: 15
Active Correlations: 3
Detectable Attacks: [lateral_movement, ransomware, data_exfiltration]
```

## Deployment

### Starting the Integrated System

```bash
# Start Backend Engine with correlation
docker-compose up backend_engine -d

# Verify correlation is active
docker-compose logs backend_engine | grep "Correlation"
```

### Configuration

Environment variables for correlation tuning:

```bash
# Correlation sensitivity
CORRELATION_SENSITIVITY=medium  # low, medium, high

# Time window defaults (seconds)
CORRELATION_WINDOW_DEFAULT=300
CORRELATION_WINDOW_MAX=3600

# Alert thresholds
CORRELATION_MIN_CONFIDENCE=0.5
CORRELATION_AUTO_ESCALATE=0.8
```

## Use Cases

### 1. APT Detection

Correlates multiple subtle indicators across extended time periods:
- Unusual authentication patterns
- Lateral movement attempts
- Data staging activities
- Exfiltration behaviors

### 2. Ransomware Prevention

Rapid detection through behavioral correlation:
- Shadow copy deletion
- Mass file modifications
- Encryption patterns
- Network scanning

### 3. Insider Threat Detection

Analyzes user behavior across multiple data sources:
- Abnormal access patterns
- Data collection activities
- Privilege escalation attempts
- Exfiltration indicators

## Performance Considerations

### Resource Usage

- **Memory**: ~200MB for correlation contexts
- **CPU**: <5% during normal operation
- **Storage**: Minimal (rules and active contexts only)

### Optimization Tips

1. **Rule Pruning**: Disable unused correlation rules
2. **Time Windows**: Adjust based on environment
3. **Source Quality**: Prioritize high-quality log sources
4. **Context Limits**: Set maximum active contexts

## Integration with Other Engines

### Intelligence Engine
- Receives pattern validation requests
- Provides AI consensus on correlations
- Enhances confidence scoring

### Ingestion Engine
- Sends parsed events for correlation
- Provides real-time event stream
- Maintains source metadata

### Contextualization Engine
- Sends enriched events with entity context
- Provides relationship mappings
- Enhances correlation accuracy

### Delivery Engine
- Receives correlation alerts
- Manages case creation from correlations
- Provides analyst feedback loop

## Future Enhancements

### Planned Features

1. **Machine Learning Integration**
   - Adaptive correlation thresholds
   - Anomaly-based correlation
   - Automated rule generation

2. **Advanced Correlation Types**
   - Graph-based correlation
   - Time-series analysis
   - Predictive correlation

3. **Enhanced Fidelity Assessment**
   - Dynamic source weighting
   - Historical accuracy tracking
   - Automated gap remediation

## Troubleshooting

### Common Issues

1. **No Correlation Alerts**
   - Verify Backend Engine is running
   - Check Redis connectivity
   - Review correlation rule configuration

2. **Low Fidelity Scores**
   - Assess available log sources
   - Review source quality mappings
   - Consider adding missing sources

3. **High False Positive Rate**
   - Adjust correlation sensitivity
   - Tune time windows
   - Review rule logic

### Debug Commands

```bash
# Check correlation rules in database
docker-compose exec postgres psql -U siemless -d siemless_v2 \
  -c "SELECT rule_name, use_case, confidence FROM correlation_rules;"

# Monitor correlation events in Redis
docker-compose exec redis redis-cli \
  SUBSCRIBE correlation.alert

# View correlation logs
docker-compose logs backend_engine | grep -i correlation
```

## Summary

The correlation engine integration provides SIEMLess v2 with advanced multi-source detection capabilities. By understanding log source quality and assessing detection fidelity, the system can provide accurate confidence scores for security alerts. This integration represents a significant enhancement to the platform's detection capabilities, enabling it to identify complex attack patterns that single-source detection would miss.