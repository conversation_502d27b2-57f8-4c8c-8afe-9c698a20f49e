"""
SIEMLess v2.0 - Workflow Templates
Predefined workflows for common security operations and platform capabilities
"""

# Core workflow templates for the platform

WORKFLOW_TEMPLATES = {
    # ============================================
    # INCIDENT RESPONSE WORKFLOWS
    # ============================================

    'full_incident_response': {
        'name': 'Full Incident Response',
        'description': 'Complete incident response from detection to resolution',
        'category': 'incident_response',
        'severity': 'critical',
        'timeout': 3600,  # 1 hour
        'steps': [
            {
                'id': 'initial_triage',
                'engine': 'ingestion',
                'action': 'collect_initial_evidence',
                'required': True,
                'timeout': 120,
                'parameters': {
                    'time_window': 3600,  # Look back 1 hour
                    'include_related': True
                }
            },
            {
                'id': 'threat_intelligence',
                'engine': 'intelligence',
                'action': 'analyze_threat_indicators',
                'required': True,
                'timeout': 180,
                'depends_on': ['initial_triage']
            },
            {
                'id': 'context_enrichment',
                'engine': 'contextualization',
                'action': 'enrich_all_entities',
                'required': True,
                'timeout': 120,
                'depends_on': ['initial_triage']
            },
            {
                'id': 'impact_analysis',
                'engine': 'contextualization',
                'action': 'analyze_business_impact',
                'required': True,
                'timeout': 90,
                'depends_on': ['context_enrichment']
            },
            {
                'id': 'detection_rules',
                'engine': 'backend',
                'action': 'generate_incident_rules',
                'required': False,
                'timeout': 60,
                'depends_on': ['threat_intelligence']
            },
            {
                'id': 'case_creation',
                'engine': 'delivery',
                'action': 'create_incident_case',
                'required': True,
                'timeout': 30,
                'depends_on': ['context_enrichment', 'threat_intelligence']
            },
            {
                'id': 'notification',
                'engine': 'delivery',
                'action': 'send_incident_notifications',
                'required': True,
                'timeout': 30,
                'depends_on': ['case_creation'],
                'parameters': {
                    'channels': ['email', 'slack'],
                    'priority': 'high'
                }
            }
        ]
    },

    'rapid_triage': {
        'name': 'Rapid Triage',
        'description': 'Quick triage for potential incidents',
        'category': 'incident_response',
        'severity': 'high',
        'timeout': 300,  # 5 minutes
        'steps': [
            {
                'id': 'quick_collect',
                'engine': 'ingestion',
                'action': 'quick_log_collection',
                'required': True,
                'timeout': 30
            },
            {
                'id': 'ai_analysis',
                'engine': 'intelligence',
                'action': 'quick_threat_assessment',
                'required': True,
                'timeout': 60,
                'depends_on': ['quick_collect']
            },
            {
                'id': 'context_check',
                'engine': 'contextualization',
                'action': 'quick_entity_check',
                'required': True,
                'timeout': 30,
                'depends_on': ['quick_collect']
            },
            {
                'id': 'decision',
                'engine': 'delivery',
                'action': 'triage_decision',
                'required': True,
                'timeout': 30,
                'depends_on': ['ai_analysis', 'context_check']
            }
        ]
    },

    # ============================================
    # PATTERN CRYSTALLIZATION WORKFLOWS
    # ============================================

    'pattern_crystallization_full': {
        'name': 'Full Pattern Crystallization',
        'description': 'Complete pattern learning and deployment cycle',
        'category': 'pattern_management',
        'severity': 'medium',
        'timeout': 900,  # 15 minutes
        'steps': [
            {
                'id': 'identify_unknown',
                'engine': 'ingestion',
                'action': 'identify_unknown_patterns',
                'required': True,
                'timeout': 60
            },
            {
                'id': 'ai_consensus',
                'engine': 'intelligence',
                'action': 'multi_ai_consensus',
                'required': True,
                'timeout': 300,
                'depends_on': ['identify_unknown'],
                'parameters': {
                    'models': ['gemma', 'gemini-flash', 'claude'],
                    'consensus_threshold': 0.8
                }
            },
            {
                'id': 'validate_pattern',
                'engine': 'intelligence',
                'action': 'validate_pattern_effectiveness',
                'required': True,
                'timeout': 120,
                'depends_on': ['ai_consensus']
            },
            {
                'id': 'store_pattern',
                'engine': 'backend',
                'action': 'crystallize_pattern',
                'required': True,
                'timeout': 30,
                'depends_on': ['validate_pattern']
            },
            {
                'id': 'deploy_pattern',
                'engine': 'ingestion',
                'action': 'hot_reload_pattern',
                'required': True,
                'timeout': 60,
                'depends_on': ['store_pattern']
            },
            {
                'id': 'update_mappings',
                'engine': 'contextualization',
                'action': 'update_pattern_mappings',
                'required': False,
                'timeout': 60,
                'depends_on': ['store_pattern']
            },
            {
                'id': 'notify_deployment',
                'engine': 'delivery',
                'action': 'notify_pattern_deployment',
                'required': False,
                'timeout': 30,
                'depends_on': ['deploy_pattern']
            }
        ]
    },

    'pattern_optimization': {
        'name': 'Pattern Optimization',
        'description': 'Optimize existing patterns for better performance',
        'category': 'pattern_management',
        'severity': 'low',
        'timeout': 600,  # 10 minutes
        'steps': [
            {
                'id': 'analyze_performance',
                'engine': 'backend',
                'action': 'analyze_pattern_performance',
                'required': True,
                'timeout': 60
            },
            {
                'id': 'identify_optimizations',
                'engine': 'intelligence',
                'action': 'suggest_pattern_optimizations',
                'required': True,
                'timeout': 120,
                'depends_on': ['analyze_performance']
            },
            {
                'id': 'test_optimizations',
                'engine': 'backend',
                'action': 'test_optimized_patterns',
                'required': True,
                'timeout': 90,
                'depends_on': ['identify_optimizations']
            },
            {
                'id': 'deploy_optimized',
                'engine': 'ingestion',
                'action': 'deploy_optimized_patterns',
                'required': True,
                'timeout': 60,
                'depends_on': ['test_optimizations']
            }
        ]
    },

    # ============================================
    # CTI INTEGRATION WORKFLOWS
    # ============================================

    'cti_to_detection_full': {
        'name': 'CTI to Detection Pipeline',
        'description': 'Convert threat intelligence to detection rules',
        'category': 'threat_intelligence',
        'severity': 'high',
        'timeout': 1200,  # 20 minutes
        'steps': [
            {
                'id': 'fetch_cti',
                'engine': 'backend',
                'action': 'fetch_all_cti_feeds',
                'required': True,
                'timeout': 180,
                'parameters': {
                    'sources': ['otx', 'misp', 'threatfox'],
                    'max_age_hours': 24
                }
            },
            {
                'id': 'deduplicate',
                'engine': 'backend',
                'action': 'deduplicate_indicators',
                'required': True,
                'timeout': 60,
                'depends_on': ['fetch_cti']
            },
            {
                'id': 'validate_iocs',
                'engine': 'intelligence',
                'action': 'validate_threat_indicators',
                'required': True,
                'timeout': 120,
                'depends_on': ['deduplicate']
            },
            {
                'id': 'contextualize_threats',
                'engine': 'contextualization',
                'action': 'add_threat_context',
                'required': True,
                'timeout': 90,
                'depends_on': ['validate_iocs']
            },
            {
                'id': 'generate_rules',
                'engine': 'backend',
                'action': 'generate_multi_siem_rules',
                'required': True,
                'timeout': 120,
                'depends_on': ['contextualize_threats'],
                'parameters': {
                    'platforms': ['splunk', 'elastic', 'sentinel', 'qradar']
                }
            },
            {
                'id': 'test_rules',
                'engine': 'backend',
                'action': 'test_generated_rules',
                'required': True,
                'timeout': 180,
                'depends_on': ['generate_rules']
            },
            {
                'id': 'deploy_rules',
                'engine': 'ingestion',
                'action': 'deploy_detection_rules',
                'required': True,
                'timeout': 60,
                'depends_on': ['test_rules']
            },
            {
                'id': 'document_rules',
                'engine': 'delivery',
                'action': 'create_rule_documentation',
                'required': False,
                'timeout': 60,
                'depends_on': ['deploy_rules']
            }
        ]
    },

    'ioc_sweep': {
        'name': 'IOC Sweep',
        'description': 'Sweep environment for specific IOCs',
        'category': 'threat_intelligence',
        'severity': 'critical',
        'timeout': 600,  # 10 minutes
        'steps': [
            {
                'id': 'prepare_iocs',
                'engine': 'backend',
                'action': 'prepare_ioc_list',
                'required': True,
                'timeout': 30
            },
            {
                'id': 'historical_search',
                'engine': 'ingestion',
                'action': 'search_historical_logs',
                'required': True,
                'timeout': 300,
                'depends_on': ['prepare_iocs']
            },
            {
                'id': 'analyze_matches',
                'engine': 'intelligence',
                'action': 'analyze_ioc_matches',
                'required': True,
                'timeout': 120,
                'depends_on': ['historical_search']
            },
            {
                'id': 'create_alert',
                'engine': 'delivery',
                'action': 'create_ioc_alerts',
                'required': True,
                'timeout': 30,
                'depends_on': ['analyze_matches']
            }
        ]
    },

    # ============================================
    # INVESTIGATION WORKFLOWS
    # ============================================

    'deep_investigation': {
        'name': 'Deep Investigation',
        'description': 'Comprehensive security investigation',
        'category': 'investigation',
        'severity': 'high',
        'timeout': 3600,  # 1 hour
        'steps': [
            {
                'id': 'collect_evidence',
                'engine': 'ingestion',
                'action': 'comprehensive_log_collection',
                'required': True,
                'timeout': 300,
                'parameters': {
                    'time_window': 86400,  # 24 hours
                    'include_network': True,
                    'include_endpoint': True
                }
            },
            {
                'id': 'timeline_construction',
                'engine': 'contextualization',
                'action': 'build_detailed_timeline',
                'required': True,
                'timeout': 180,
                'depends_on': ['collect_evidence']
            },
            {
                'id': 'entity_mapping',
                'engine': 'contextualization',
                'action': 'map_entity_relationships',
                'required': True,
                'timeout': 120,
                'depends_on': ['collect_evidence']
            },
            {
                'id': 'threat_analysis',
                'engine': 'intelligence',
                'action': 'deep_threat_analysis',
                'required': True,
                'timeout': 300,
                'depends_on': ['timeline_construction', 'entity_mapping']
            },
            {
                'id': 'attack_reconstruction',
                'engine': 'intelligence',
                'action': 'reconstruct_attack_chain',
                'required': True,
                'timeout': 180,
                'depends_on': ['threat_analysis']
            },
            {
                'id': 'impact_assessment',
                'engine': 'contextualization',
                'action': 'assess_full_impact',
                'required': True,
                'timeout': 120,
                'depends_on': ['attack_reconstruction']
            },
            {
                'id': 'generate_report',
                'engine': 'delivery',
                'action': 'generate_investigation_report',
                'required': True,
                'timeout': 120,
                'depends_on': ['attack_reconstruction', 'impact_assessment']
            }
        ]
    },

    'user_behavior_analysis': {
        'name': 'User Behavior Analysis',
        'description': 'Analyze user behavior for anomalies',
        'category': 'investigation',
        'severity': 'medium',
        'timeout': 1800,  # 30 minutes
        'steps': [
            {
                'id': 'collect_user_data',
                'engine': 'ingestion',
                'action': 'collect_user_activity',
                'required': True,
                'timeout': 180
            },
            {
                'id': 'baseline_analysis',
                'engine': 'contextualization',
                'action': 'analyze_user_baseline',
                'required': True,
                'timeout': 120,
                'depends_on': ['collect_user_data']
            },
            {
                'id': 'anomaly_detection',
                'engine': 'intelligence',
                'action': 'detect_behavior_anomalies',
                'required': True,
                'timeout': 180,
                'depends_on': ['baseline_analysis']
            },
            {
                'id': 'risk_scoring',
                'engine': 'intelligence',
                'action': 'calculate_user_risk_score',
                'required': True,
                'timeout': 60,
                'depends_on': ['anomaly_detection']
            },
            {
                'id': 'create_alert',
                'engine': 'delivery',
                'action': 'create_user_risk_alert',
                'required': True,
                'timeout': 30,
                'depends_on': ['risk_scoring']
            }
        ]
    },

    # ============================================
    # REMEDIATION WORKFLOWS
    # ============================================

    'automated_remediation': {
        'name': 'Automated Remediation',
        'description': 'Automated response and remediation',
        'category': 'remediation',
        'severity': 'critical',
        'timeout': 900,  # 15 minutes
        'steps': [
            {
                'id': 'validate_threat',
                'engine': 'intelligence',
                'action': 'validate_threat_severity',
                'required': True,
                'timeout': 60
            },
            {
                'id': 'identify_affected',
                'engine': 'contextualization',
                'action': 'identify_affected_assets',
                'required': True,
                'timeout': 90,
                'depends_on': ['validate_threat']
            },
            {
                'id': 'generate_response',
                'engine': 'backend',
                'action': 'generate_response_actions',
                'required': True,
                'timeout': 60,
                'depends_on': ['identify_affected']
            },
            {
                'id': 'execute_containment',
                'engine': 'backend',
                'action': 'execute_containment_actions',
                'required': True,
                'timeout': 180,
                'depends_on': ['generate_response']
            },
            {
                'id': 'verify_containment',
                'engine': 'ingestion',
                'action': 'verify_containment_success',
                'required': True,
                'timeout': 120,
                'depends_on': ['execute_containment']
            },
            {
                'id': 'document_response',
                'engine': 'delivery',
                'action': 'document_remediation',
                'required': True,
                'timeout': 60,
                'depends_on': ['verify_containment']
            }
        ]
    },

    # ============================================
    # REPORTING WORKFLOWS
    # ============================================

    'executive_report': {
        'name': 'Executive Security Report',
        'description': 'Generate executive-level security report',
        'category': 'reporting',
        'severity': 'low',
        'timeout': 1800,  # 30 minutes
        'steps': [
            {
                'id': 'collect_metrics',
                'engine': 'backend',
                'action': 'collect_security_metrics',
                'required': True,
                'timeout': 180
            },
            {
                'id': 'analyze_trends',
                'engine': 'intelligence',
                'action': 'analyze_security_trends',
                'required': True,
                'timeout': 240,
                'depends_on': ['collect_metrics']
            },
            {
                'id': 'risk_assessment',
                'engine': 'contextualization',
                'action': 'assess_organizational_risk',
                'required': True,
                'timeout': 180,
                'depends_on': ['collect_metrics']
            },
            {
                'id': 'generate_insights',
                'engine': 'intelligence',
                'action': 'generate_executive_insights',
                'required': True,
                'timeout': 180,
                'depends_on': ['analyze_trends', 'risk_assessment']
            },
            {
                'id': 'create_report',
                'engine': 'delivery',
                'action': 'create_executive_report',
                'required': True,
                'timeout': 120,
                'depends_on': ['generate_insights']
            },
            {
                'id': 'distribute_report',
                'engine': 'delivery',
                'action': 'distribute_report',
                'required': True,
                'timeout': 60,
                'depends_on': ['create_report'],
                'parameters': {
                    'recipients': ['executives', 'board'],
                    'format': 'pdf'
                }
            }
        ]
    },

    # ============================================
    # MAINTENANCE WORKFLOWS
    # ============================================

    'platform_health_check': {
        'name': 'Platform Health Check',
        'description': 'Comprehensive platform health assessment',
        'category': 'maintenance',
        'severity': 'low',
        'timeout': 600,  # 10 minutes
        'steps': [
            {
                'id': 'check_ingestion',
                'engine': 'ingestion',
                'action': 'health_check',
                'required': True,
                'timeout': 30
            },
            {
                'id': 'check_intelligence',
                'engine': 'intelligence',
                'action': 'health_check',
                'required': True,
                'timeout': 30
            },
            {
                'id': 'check_contextualization',
                'engine': 'contextualization',
                'action': 'health_check',
                'required': True,
                'timeout': 30
            },
            {
                'id': 'check_backend',
                'engine': 'backend',
                'action': 'health_check',
                'required': True,
                'timeout': 30
            },
            {
                'id': 'check_delivery',
                'engine': 'delivery',
                'action': 'health_check',
                'required': True,
                'timeout': 30
            },
            {
                'id': 'generate_health_report',
                'engine': 'delivery',
                'action': 'generate_health_report',
                'required': True,
                'timeout': 60,
                'depends_on': [
                    'check_ingestion',
                    'check_intelligence',
                    'check_contextualization',
                    'check_backend',
                    'check_delivery'
                ]
            }
        ]
    }
}


def get_workflow_template(workflow_type: str) -> dict:
    """Get a workflow template by type"""
    return WORKFLOW_TEMPLATES.get(workflow_type)


def get_workflow_categories() -> list:
    """Get all workflow categories"""
    categories = set()
    for workflow in WORKFLOW_TEMPLATES.values():
        categories.add(workflow.get('category', 'uncategorized'))
    return list(categories)


def get_workflows_by_category(category: str) -> dict:
    """Get all workflows in a specific category"""
    workflows = {}
    for key, workflow in WORKFLOW_TEMPLATES.items():
        if workflow.get('category') == category:
            workflows[key] = workflow
    return workflows


def get_workflows_by_severity(severity: str) -> dict:
    """Get all workflows with specific severity"""
    workflows = {}
    for key, workflow in WORKFLOW_TEMPLATES.items():
        if workflow.get('severity') == severity:
            workflows[key] = workflow
    return workflows