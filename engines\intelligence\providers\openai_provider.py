"""
OpenAI Provider - GPT models
Implements BaseAIProvider for OpenAI's GPT models
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, Any
import logging
import aiohttp

from .base_provider import BaseAIProvider, AIResponse


class OpenAIProvider(BaseAIProvider):
    """
    Provider for OpenAI GPT models

    Supports:
    - gpt-4-turbo
    - gpt-5
    - gpt-4o
    """

    def _get_provider_name(self) -> str:
        """Return provider name"""
        return 'openai'

    async def call(
        self,
        model: str,
        prompt: str,
        temperature: float = 0.1,
        max_tokens: int = 4096,
        **kwargs
    ) -> AIResponse:
        """
        Call OpenAI GPT model

        Args:
            model: Model name (gpt-4-turbo, gpt-5, etc.)
            prompt: The prompt text
            temperature: Temperature (0.0-1.0)
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters

        Returns:
            AIResponse

        Raises:
            Exception: If call fails
        """
        start_time = time.time()

        try:
            # Get API key
            api_key = self.get_credential()
            if not api_key:
                raise Exception(f"No API key found for {self.provider_name}")

            # Get endpoint and metadata
            endpoint = self.get_endpoint() or 'https://api.openai.com/v1'
            metadata = self.credential_manager.get_metadata(self.provider_name)
            org_id = metadata.get('organization')

            # Map model names to actual API names
            model_mapping = {
                "gpt-4-turbo": "gpt-4-turbo-preview",
                "gpt-5": "gpt-5",
                "gpt-4o": "gpt-4o"
            }
            actual_model = model_mapping.get(model, model)

            # Prepare headers
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            if org_id:
                headers['OpenAI-Organization'] = org_id

            # Prepare payload
            payload = {
                'model': actual_model,
                'messages': [
                    {'role': 'user', 'content': prompt}
                ],
                'max_tokens': max_tokens,
                'temperature': temperature
            }

            # Make API call
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f'{endpoint}/chat/completions',
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:
                    if response.status == 200:
                        result = await response.json()

                        # Extract content
                        content = result['choices'][0]['message']['content']

                        # Calculate latency
                        latency_ms = (time.time() - start_time) * 1000

                        # Extract token usage
                        usage = result.get('usage', {})
                        input_tokens = usage.get('prompt_tokens', 0)
                        output_tokens = usage.get('completion_tokens', 0)
                        total_tokens = usage.get('total_tokens', input_tokens + output_tokens)

                        # Track usage if cost tracker available
                        if self.cost_tracker:
                            self._track_usage(model, input_tokens, output_tokens, "general")

                        return AIResponse(
                            model=model,
                            content=content,
                            confidence=0.85,  # GPT-4 high confidence
                            reasoning=f"OpenAI {actual_model} analysis",
                            input_tokens=input_tokens,
                            output_tokens=output_tokens,
                            total_tokens=total_tokens,
                            provider=self.provider_name,
                            request_id=result.get('id', ''),
                            created_at=datetime.now(),
                            latency_ms=latency_ms,
                            raw_response=result,
                            error=None,
                            success=True
                        )

                    else:
                        # Error response
                        error_text = await response.text()
                        raise Exception(f"OpenAI API error {response.status}: {error_text}")

        except Exception as e:
            # Mark key as failed if it's a credential issue
            error_str = str(e).lower()
            if 'api_key' in error_str or 'unauthorized' in error_str or 'authentication' in error_str:
                api_key = self.get_credential()
                if api_key:
                    self.credential_manager.mark_key_failed(self.provider_name, api_key)

            latency_ms = (time.time() - start_time) * 1000

            self.logger.error(f"OpenAI API call failed: {e}")

            return AIResponse(
                model=model,
                content="",
                confidence=0.0,
                reasoning="",
                provider=self.provider_name,
                created_at=datetime.now(),
                latency_ms=latency_ms,
                error=str(e),
                success=False
            )

    def _is_rate_limit_error(self, error: Exception) -> bool:
        """Check if error is a rate limit error"""
        error_str = str(error).lower()
        return any(phrase in error_str for phrase in [
            'rate_limit',
            'rate limit exceeded',
            'too many requests',
            '429'
        ])

    def parse_response(self, raw_response: Any) -> AIResponse:
        """
        Parse OpenAI API response

        Args:
            raw_response: Raw response from OpenAI API

        Returns:
            Standardized AIResponse
        """
        # Not typically used since we parse in call() directly
        # But included for interface compliance
        if isinstance(raw_response, dict):
            content = raw_response.get('choices', [{}])[0].get('message', {}).get('content', '')
            usage = raw_response.get('usage', {})

            return AIResponse(
                model=raw_response.get('model', 'unknown'),
                content=content,
                confidence=0.85,
                reasoning="OpenAI GPT analysis",
                input_tokens=usage.get('prompt_tokens', 0),
                output_tokens=usage.get('completion_tokens', 0),
                total_tokens=usage.get('total_tokens', 0),
                provider=self.provider_name,
                created_at=datetime.now(),
                success=True
            )
        else:
            return AIResponse(
                model="unknown",
                content=str(raw_response),
                provider=self.provider_name,
                created_at=datetime.now(),
                success=True
            )
