#!/bin/bash

# SIEMLess v2.0 - Comprehensive Test Runner
# ==========================================
# Runs all test suites and generates a report

set -e

echo "=========================================="
echo "SIEMLess v2.0 - Test Suite Runner"
echo "=========================================="
echo "Date: $(date)"
echo "=========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to run a test suite
run_test() {
    local test_name=$1
    local test_file=$2

    echo ""
    echo "=========================================="
    echo "Running: $test_name"
    echo "=========================================="

    if python "$test_file" > "test_${test_file%.py}.log" 2>&1; then
        echo -e "${GREEN}✅ $test_name: PASSED${NC}"
        ((PASSED_TESTS++))
    else
        echo -e "${RED}❌ $test_name: FAILED${NC}"
        echo "   See test_${test_file%.py}.log for details"
        ((FAILED_TESTS++))
    fi

    ((TOTAL_TESTS++))
}

# Check Python availability
echo "Checking Python environment..."
python --version || python3 --version

# Check if we're in the right directory
if [ ! -f "test_comprehensive_suite.py" ]; then
    echo -e "${RED}Error: Test files not found. Please run from siemless_v2 directory${NC}"
    exit 1
fi

# Check Docker services
echo ""
echo "Checking Docker services..."
docker-compose ps 2>/dev/null || echo -e "${YELLOW}Warning: Docker services may not be running${NC}"

# Run infrastructure tests first
echo ""
echo "=========================================="
echo "PHASE 1: Infrastructure Tests"
echo "=========================================="

run_test "Comprehensive System Test" "test_comprehensive_suite.py"

# Run individual engine tests
echo ""
echo "=========================================="
echo "PHASE 2: Individual Engine Tests"
echo "=========================================="

run_test "All Engine Tests" "test_individual_engines.py"

# Test each engine separately
for engine in intelligence backend ingestion contextualization delivery; do
    run_test "$engine Engine Test" "test_individual_engines.py $engine"
done

# Run async pattern tests
echo ""
echo "=========================================="
echo "PHASE 3: Async Pattern Tests"
echo "=========================================="

run_test "Async Pattern Tests" "test_async_patterns.py"

# Generate summary report
echo ""
echo "=========================================="
echo "TEST SUMMARY REPORT"
echo "=========================================="
echo "Total Test Suites Run: $TOTAL_TESTS"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed: ${RED}$FAILED_TESTS${NC}"
echo ""

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}✅ ALL TESTS PASSED!${NC}"
    echo "SIEMLess v2.0 is fully operational"
else
    echo -e "${RED}❌ SOME TESTS FAILED${NC}"
    echo "Please review the log files for details"
fi

echo "=========================================="
echo ""

# Generate detailed report file
cat > test_report.txt <<EOF
SIEMLess v2.0 Test Report
Generated: $(date)
==========================================

Test Summary:
- Total Test Suites: $TOTAL_TESTS
- Passed: $PASSED_TESTS
- Failed: $FAILED_TESTS

Test Results:
EOF

for log_file in test_*.log; do
    if [ -f "$log_file" ]; then
        echo "" >> test_report.txt
        echo "=== $log_file ===" >> test_report.txt
        tail -20 "$log_file" >> test_report.txt
    fi
done

echo ""
echo "Detailed report saved to: test_report.txt"
echo "Individual test logs: test_*.log"

# Exit with appropriate code
if [ $FAILED_TESTS -eq 0 ]; then
    exit 0
else
    exit 1
fi