"""
Comprehensive System Test Suite for SIEMLess v2.0
Tests all 11 features and logs detailed results
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'test_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


class SystemTester:
    """Comprehensive system tester"""

    def __init__(self):
        self.results = {
            'test_started': datetime.now().isoformat(),
            'tests_passed': 0,
            'tests_failed': 0,
            'tests_total': 0,
            'details': []
        }

        self.engines = {
            'intelligence': 'http://localhost:8001',
            'backend': 'http://localhost:8002',
            'ingestion': 'http://localhost:8003',
            'contextualization': 'http://localhost:8004',
            'delivery': 'http://localhost:8005'
        }

    async def run_all_tests(self):
        """Run all system tests"""
        logger.info("=" * 80)
        logger.info("SIEMLESS v2.0 - COMPREHENSIVE SYSTEM TEST")
        logger.info("=" * 80)

        # Test 1: Engine Health
        await self.test_engine_health()

        # Test 2: Database Connectivity
        await self.test_database_connectivity()

        # Test 3: Redis Connectivity
        await self.test_redis_connectivity()

        # Test 4: Investigation Evidence System
        await self.test_investigation_evidence()

        # Test 5: Log Retention Policy
        await self.test_log_retention()

        # Test 6: Preview-Before-Download
        await self.test_preview_before_download()

        # Test 7: Firehose Management
        await self.test_firehose_management()

        # Test 8: Auto-Investigation Dashboard
        await self.test_investigation_dashboard()

        # Test 9: SIEM Alert Polling
        await self.test_siem_alert_polling()

        # Test 10: Historical Context
        await self.test_historical_context()

        # Test 11: CTI Integration
        await self.test_cti_integration()

        # Generate summary
        self.generate_summary()

    async def test_engine_health(self):
        """Test 1: All engine health endpoints"""
        logger.info("\n" + "=" * 80)
        logger.info("TEST 1: Engine Health Checks")
        logger.info("=" * 80)

        for engine_name, base_url in self.engines.items():
            self.results['tests_total'] += 1
            test_name = f"Engine Health: {engine_name}"

            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{base_url}/health", timeout=aiohttp.ClientTimeout(total=5)) as response:
                        if response.status == 200:
                            data = await response.json()
                            logger.info(f"✅ PASS: {engine_name} engine is healthy - {data}")
                            self.results['tests_passed'] += 1
                            self.results['details'].append({
                                'test': test_name,
                                'status': 'PASS',
                                'response': data,
                                'url': f"{base_url}/health"
                            })
                        else:
                            logger.error(f"❌ FAIL: {engine_name} engine returned {response.status}")
                            self.results['tests_failed'] += 1
                            self.results['details'].append({
                                'test': test_name,
                                'status': 'FAIL',
                                'error': f"HTTP {response.status}",
                                'url': f"{base_url}/health"
                            })
            except Exception as e:
                logger.error(f"❌ FAIL: {engine_name} engine error - {e}")
                self.results['tests_failed'] += 1
                self.results['details'].append({
                    'test': test_name,
                    'status': 'FAIL',
                    'error': str(e),
                    'url': f"{base_url}/health"
                })

    async def test_database_connectivity(self):
        """Test 2: Database connectivity"""
        logger.info("\n" + "=" * 80)
        logger.info("TEST 2: Database Connectivity")
        logger.info("=" * 80)

        self.results['tests_total'] += 1
        test_name = "Database Connectivity"

        try:
            import psycopg2
            conn = psycopg2.connect(
                host='localhost',
                port=5433,
                database='siemless_v2',
                user='siemless',
                password='siemless123'
            )
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            cursor.close()
            conn.close()

            logger.info(f"✅ PASS: Database connected - {version[0][:50]}...")
            self.results['tests_passed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'PASS',
                'response': {'version': version[0][:100]}
            })
        except Exception as e:
            logger.error(f"❌ FAIL: Database connection - {e}")
            self.results['tests_failed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'FAIL',
                'error': str(e)
            })

    async def test_redis_connectivity(self):
        """Test 3: Redis connectivity"""
        logger.info("\n" + "=" * 80)
        logger.info("TEST 3: Redis Connectivity")
        logger.info("=" * 80)

        self.results['tests_total'] += 1
        test_name = "Redis Connectivity"

        try:
            import redis
            r = redis.Redis(host='localhost', port=6380, db=0)
            r.ping()
            info = r.info('server')

            logger.info(f"✅ PASS: Redis connected - version {info['redis_version']}")
            self.results['tests_passed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'PASS',
                'response': {'version': info['redis_version']}
            })
        except Exception as e:
            logger.error(f"❌ FAIL: Redis connection - {e}")
            self.results['tests_failed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'FAIL',
                'error': str(e)
            })

    async def test_investigation_evidence(self):
        """Test 4: Investigation Evidence Log System"""
        logger.info("\n" + "=" * 80)
        logger.info("TEST 4: Investigation Evidence Log System")
        logger.info("=" * 80)

        # Test evidence logger exists
        self.results['tests_total'] += 1
        test_name = "Evidence Logger Module"

        try:
            import sys
            import os
            sys.path.append('c:/Users/<USER>/Documents/siemless_v2/engines/delivery')
            from investigation_evidence_logger import InvestigationEvidenceLogger

            logger.info("✅ PASS: Evidence logger module imports successfully")
            self.results['tests_passed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'PASS',
                'response': {'module': 'InvestigationEvidenceLogger'}
            })
        except Exception as e:
            logger.error(f"❌ FAIL: Evidence logger import - {e}")
            self.results['tests_failed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'FAIL',
                'error': str(e)
            })

    async def test_log_retention(self):
        """Test 5: Log Retention Policy Engine"""
        logger.info("\n" + "=" * 80)
        logger.info("TEST 5: Log Retention Policy Engine")
        logger.info("=" * 80)

        self.results['tests_total'] += 1
        test_name = "Retention Policy Module"

        try:
            import sys
            sys.path.append('c:/Users/<USER>/Documents/siemless_v2/engines/backend')
            from log_retention_policy_engine import LogRetentionPolicyEngine

            logger.info("✅ PASS: Retention policy module imports successfully")
            self.results['tests_passed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'PASS',
                'response': {'module': 'LogRetentionPolicyEngine'}
            })
        except Exception as e:
            logger.error(f"❌ FAIL: Retention policy import - {e}")
            self.results['tests_failed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'FAIL',
                'error': str(e)
            })

    async def test_preview_before_download(self):
        """Test 6: Preview-Before-Download"""
        logger.info("\n" + "=" * 80)
        logger.info("TEST 6: Preview-Before-Download System")
        logger.info("=" * 80)

        self.results['tests_total'] += 1
        test_name = "Preview-Before-Download Module"

        try:
            import sys
            sys.path.append('c:/Users/<USER>/Documents/siemless_v2/engines/backend')
            from preview_before_download import PreviewBeforeDownload

            logger.info("✅ PASS: Preview-Before-Download module imports successfully")
            self.results['tests_passed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'PASS',
                'response': {'module': 'PreviewBeforeDownload'}
            })
        except Exception as e:
            logger.error(f"❌ FAIL: Preview-Before-Download import - {e}")
            self.results['tests_failed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'FAIL',
                'error': str(e)
            })

    async def test_firehose_management(self):
        """Test 7: Firehose Management"""
        logger.info("\n" + "=" * 80)
        logger.info("TEST 7: Firehose Management System")
        logger.info("=" * 80)

        self.results['tests_total'] += 1
        test_name = "Firehose Manager Module"

        try:
            import sys
            sys.path.append('c:/Users/<USER>/Documents/siemless_v2/engines/ingestion')
            # Note: Will need bloom-filter2 package
            # from firehose_manager import FirehoseManager

            logger.info("⚠️  SKIP: Firehose manager requires bloom-filter2 package (pip install bloom-filter2)")
            self.results['tests_total'] -= 1  # Don't count as failed
            self.results['details'].append({
                'test': test_name,
                'status': 'SKIP',
                'reason': 'Requires bloom-filter2 package'
            })
        except Exception as e:
            logger.error(f"❌ FAIL: Firehose manager import - {e}")
            self.results['tests_failed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'FAIL',
                'error': str(e)
            })

    async def test_investigation_dashboard(self):
        """Test 8: Auto-Investigation Dashboard"""
        logger.info("\n" + "=" * 80)
        logger.info("TEST 8: Auto-Investigation Dashboard")
        logger.info("=" * 80)

        # Test investigations list endpoint
        self.results['tests_total'] += 1
        test_name = "Investigation List API"

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    "http://localhost:8005/api/v1/investigations",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status in [200, 404]:  # 404 is ok if no investigations yet
                        data = await response.json()
                        logger.info(f"✅ PASS: Investigation API responding - {response.status}")
                        self.results['tests_passed'] += 1
                        self.results['details'].append({
                            'test': test_name,
                            'status': 'PASS',
                            'response': data
                        })
                    else:
                        logger.error(f"❌ FAIL: Investigation API returned {response.status}")
                        self.results['tests_failed'] += 1
                        self.results['details'].append({
                            'test': test_name,
                            'status': 'FAIL',
                            'error': f"HTTP {response.status}"
                        })
        except Exception as e:
            logger.error(f"❌ FAIL: Investigation API - {e}")
            self.results['tests_failed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'FAIL',
                'error': str(e)
            })

    async def test_siem_alert_polling(self):
        """Test 9: SIEM Alert Polling"""
        logger.info("\n" + "=" * 80)
        logger.info("TEST 9: SIEM Alert Polling")
        logger.info("=" * 80)

        self.results['tests_total'] += 1
        test_name = "SIEM Alert Listener Module"

        try:
            import sys
            sys.path.append('c:/Users/<USER>/Documents/siemless_v2/engines/ingestion')
            from siem_alert_listener import SIEMAlertListener

            logger.info("✅ PASS: SIEM alert listener module imports successfully")
            self.results['tests_passed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'PASS',
                'response': {'module': 'SIEMAlertListener'}
            })
        except Exception as e:
            logger.error(f"❌ FAIL: SIEM alert listener import - {e}")
            self.results['tests_failed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'FAIL',
                'error': str(e)
            })

    async def test_historical_context(self):
        """Test 10: Historical Context Manager"""
        logger.info("\n" + "=" * 80)
        logger.info("TEST 10: Historical Context Manager")
        logger.info("=" * 80)

        self.results['tests_total'] += 1
        test_name = "Historical Context Module"

        try:
            import sys
            sys.path.append('c:/Users/<USER>/Documents/siemless_v2/engines/backend')
            from historical_context_manager import HistoricalContextManager

            logger.info("✅ PASS: Historical context manager module imports successfully")
            self.results['tests_passed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'PASS',
                'response': {'module': 'HistoricalContextManager'}
            })
        except Exception as e:
            logger.error(f"❌ FAIL: Historical context manager import - {e}")
            self.results['tests_failed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'FAIL',
                'error': str(e)
            })

    async def test_cti_integration(self):
        """Test 11: CTI Integration"""
        logger.info("\n" + "=" * 80)
        logger.info("TEST 11: CTI Integration")
        logger.info("=" * 80)

        self.results['tests_total'] += 1
        test_name = "CTI Integration Modules"

        try:
            import sys
            sys.path.append('c:/Users/<USER>/Documents/siemless_v2/engines/ingestion')
            from cti_manager import CTIManager

            logger.info("✅ PASS: CTI manager module imports successfully")
            self.results['tests_passed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'PASS',
                'response': {'module': 'CTIManager'}
            })
        except Exception as e:
            logger.error(f"❌ FAIL: CTI manager import - {e}")
            self.results['tests_failed'] += 1
            self.results['details'].append({
                'test': test_name,
                'status': 'FAIL',
                'error': str(e)
            })

    def generate_summary(self):
        """Generate test summary"""
        self.results['test_completed'] = datetime.now().isoformat()

        logger.info("\n" + "=" * 80)
        logger.info("TEST SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Total Tests: {self.results['tests_total']}")
        logger.info(f"✅ Passed: {self.results['tests_passed']}")
        logger.info(f"❌ Failed: {self.results['tests_failed']}")

        pass_rate = (self.results['tests_passed'] / self.results['tests_total'] * 100) if self.results['tests_total'] > 0 else 0
        logger.info(f"Pass Rate: {pass_rate:.1f}%")

        # Save results to JSON
        results_file = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)

        logger.info(f"\nDetailed results saved to: {results_file}")
        logger.info("=" * 80)

        return self.results


async def main():
    """Main test runner"""
    tester = SystemTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
