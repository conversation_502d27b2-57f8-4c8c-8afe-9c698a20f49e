/**
 * Dashboard Overview Widget
 * Executive/Analyst single-pane-of-glass security posture view
 *
 * Features:
 * - Real-time KPI tracking
 * - Critical alerts preview
 * - Detection confidence display
 * - CTI status monitoring
 * - Auto-refresh every 30 seconds
 */

import React, { useEffect, useState } from 'react'
import {
  AlertTriangle,
  Shield,
  Activity,
  Database,
  RefreshCw,
  TrendingUp,
  Users,
  Target
} from 'lucide-react'
import { useDashboardStore } from '../stores/dashboardStore'
import { StatCard, StatCardSkeleton } from '../components/common/StatCard'
import { LoadingOverlay, SkeletonAlertList } from '../components/common/LoadingStates'
import { wsClient } from '../api/client'
import type { WebSocketMessage, Alert } from '../types/api'

const DashboardOverview: React.FC = () => {
  const {
    stats,
    recentAlerts,
    detectionFidelity,
    ctiStatus,
    loading,
    error,
    fetchDashboard,
    refreshAll
  } = useDashboardStore()

  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [autoRefresh, setAutoRefresh] = useState(true)

  // Initial load
  useEffect(() => {
    fetchDashboard()
  }, [fetchDashboard])

  // Auto-refresh every 30 seconds
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      fetchDashboard()
      setLastRefresh(new Date())
    }, 30000) // 30 seconds

    return () => clearInterval(interval)
  }, [autoRefresh, fetchDashboard])

  // WebSocket real-time updates
  useEffect(() => {
    // Connect WebSocket
    if (!wsClient) return

    const handleDashboardUpdate = (message: WebSocketMessage) => {
      console.log('Dashboard update received:', message)
      fetchDashboard()
      setLastRefresh(new Date())
    }

    const handleNewAlert = (message: WebSocketMessage<Alert>) => {
      console.log('New alert received:', message)
      // Refresh alerts section
      useDashboardStore.getState().fetchRecentAlerts()
    }

    // Subscribe to dashboard updates
    wsClient.on('dashboard.stats', handleDashboardUpdate)
    wsClient.on('alert.new', handleNewAlert)

    // Connect if not already connected
    // TODO: Enable WebSocket when backend /ws endpoint is implemented
    // try {
    //   wsClient.connect()
    // } catch (error) {
    //   console.error('WebSocket connection failed:', error)
    // }

    // Cleanup
    return () => {
      wsClient.off('dashboard.stats', handleDashboardUpdate)
      wsClient.off('alert.new', handleNewAlert)
    }
  }, [fetchDashboard])

  // Manual refresh handler
  const handleRefresh = async () => {
    setIsRefreshing(true)
    await refreshAll()
    setLastRefresh(new Date())
    setIsRefreshing(false)
  }

  // Get CTI indicator count
  const getTotalCTIIndicators = () => {
    if (!ctiStatus || ctiStatus.length === 0) return 0
    return ctiStatus.reduce((sum, plugin) => sum + plugin.total_indicators, 0)
  }

  // Determine detection confidence color
  const getDetectionColor = (confidence: number) => {
    if (confidence >= 80) return 'green'
    if (confidence >= 60) return 'yellow'
    return 'red'
  }

  return (
    <div className="h-full flex flex-col bg-gray-50 p-6 overflow-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Security Operations Dashboard</h1>
          <p className="text-sm text-gray-500 mt-1">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </p>
        </div>

        <div className="flex items-center gap-3">
          {/* Auto-refresh toggle */}
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded"
            />
            <span className="text-gray-600">Auto-refresh (30s)</span>
          </label>

          {/* Manual refresh button */}
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center gap-2 disabled:opacity-50"
          >
            <RefreshCw size={16} className={isRefreshing ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Error Display */}
      {Object.values(error).some(e => e !== null) && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800 font-medium">Some data failed to load:</p>
          <ul className="text-sm text-red-600 mt-2 space-y-1">
            {Object.entries(error).map(([key, value]) =>
              value && <li key={key}>• {key}: {value}</li>
            )}
          </ul>
        </div>
      )}

      {/* KPI Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {/* Critical Alerts */}
        {loading.alerts ? (
          <StatCardSkeleton />
        ) : (
          <StatCard
            title="Critical Alerts"
            value={stats?.critical_alerts || 0}
            subtitle={`${stats?.total_alerts || 0} total alerts`}
            icon={<AlertTriangle size={24} />}
            color="red"
            onClick={() => window.location.href = '/alerts'}
          />
        )}

        {/* Open Cases */}
        {loading.cases ? (
          <StatCardSkeleton />
        ) : (
          <StatCard
            title="Open Cases"
            value={stats?.open_cases || 0}
            subtitle="Investigating"
            icon={<Users size={24} />}
            color="blue"
            onClick={() => window.location.href = '/cases'}
          />
        )}

        {/* Detection Confidence */}
        {loading.fidelity ? (
          <StatCardSkeleton />
        ) : (
          <StatCard
            title="Detection Confidence"
            value={`${detectionFidelity?.overall_confidence || 0}%`}
            subtitle={`${detectionFidelity?.covered_techniques || 0}/${detectionFidelity?.total_techniques || 0} techniques`}
            icon={<Shield size={24} />}
            color={getDetectionColor(detectionFidelity?.overall_confidence || 0)}
            trend={{
              value: 2.5,
              isPositive: true
            }}
          />
        )}

        {/* CTI Indicators */}
        {loading.cti ? (
          <StatCardSkeleton />
        ) : (
          <StatCard
            title="Threat Indicators"
            value={getTotalCTIIndicators()}
            subtitle={ctiStatus && ctiStatus.length > 0
              ? `${ctiStatus.filter(p => p.status === 'active').length}/${ctiStatus.length} sources active`
              : 'No CTI sources configured'}
            icon={<Database size={24} />}
            color="purple"
          />
        )}

        {/* Entities Tracked */}
        {loading.stats ? (
          <StatCardSkeleton />
        ) : (
          <StatCard
            title="Entities Tracked"
            value={stats?.entities_tracked || 0}
            subtitle="Unique entities"
            icon={<Activity size={24} />}
            color="green"
          />
        )}

        {/* Coverage Score */}
        {loading.stats ? (
          <StatCardSkeleton />
        ) : (
          <StatCard
            title="Coverage Score"
            value={`${stats?.coverage_score || 0}%`}
            subtitle="Attack surface"
            icon={<Target size={24} />}
            color="blue"
            trend={{
              value: 1.2,
              isPositive: true
            }}
          />
        )}
      </div>

      {/* Bottom Section: Recent Alerts + CTI Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Critical Alerts */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Recent Critical Alerts</h2>
            <a
              href="/alerts"
              className="text-sm text-blue-600 hover:text-blue-700 font-medium"
            >
              View All →
            </a>
          </div>

          {loading.alerts ? (
            <SkeletonAlertList />
          ) : !recentAlerts || recentAlerts.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <AlertTriangle size={48} className="mx-auto mb-2 opacity-20" />
              <p>No critical alerts</p>
            </div>
          ) : (
            <div className="space-y-3">
              {recentAlerts.slice(0, 5).map((alert) => (
                <div
                  key={alert.alert_id}
                  className="border-l-4 border-red-500 bg-red-50 p-3 rounded cursor-pointer hover:bg-red-100 transition-colors"
                  onClick={() => window.location.href = `/alerts/${alert.alert_id}`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="font-medium text-gray-900 text-sm">{alert.title}</h3>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      alert.severity === 'critical'
                        ? 'bg-red-600 text-white'
                        : 'bg-orange-600 text-white'
                    }`}>
                      {alert.severity.toUpperCase()}
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">{alert.description}</p>
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <span>{new Date(alert.created_at).toLocaleString()}</span>
                    {alert.mitre_techniques && alert.mitre_techniques.length > 0 && (
                      <>
                        <span>•</span>
                        <span>{alert.mitre_techniques[0]}</span>
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* CTI Plugin Status */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Threat Intelligence Sources</h2>
            <a
              href="/cti"
              className="text-sm text-blue-600 hover:text-blue-700 font-medium"
            >
              Manage →
            </a>
          </div>

          {loading.cti ? (
            <div className="space-y-3">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="animate-pulse bg-gray-50 rounded p-3">
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-3">
              {ctiStatus.map((plugin) => (
                <div
                  key={plugin.source}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${
                      plugin.status === 'active' ? 'bg-green-500' :
                      plugin.status === 'updating' ? 'bg-yellow-500' :
                      'bg-red-500'
                    }`}></div>
                    <div>
                      <h3 className="font-medium text-gray-900 text-sm">
                        {plugin.source.toUpperCase()}
                      </h3>
                      <p className="text-xs text-gray-500">
                        {plugin.total_indicators.toLocaleString()} indicators
                        {plugin.new_indicators_today > 0 && (
                          <span className="text-green-600 ml-1">
                            (+{plugin.new_indicators_today} today)
                          </span>
                        )}
                      </p>
                    </div>
                  </div>

                  <div className="text-right">
                    <p className="text-xs text-gray-500">
                      {new Date(plugin.last_update).toLocaleTimeString()}
                    </p>
                    {plugin.error_message && (
                      <p className="text-xs text-red-600 mt-1">{plugin.error_message}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Loading overlay during refresh */}
      {isRefreshing && <LoadingOverlay message="Refreshing dashboard..." />}
    </div>
  )
}

export default DashboardOverview
