import React, { useState, useEffect } from 'react'
import { TrendingUp, TrendingDown, DollarSign, Activity, Users, Shield } from 'lucide-react'

interface MetricCardProps {
  title: string
  value: string | number
  change?: number
  icon: React.ReactNode
  color: string
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, change, icon, color }) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border">
      <div className="flex justify-between items-start">
        <div>
          <p className="text-sm text-gray-600">{title}</p>
          <p className="text-2xl font-bold mt-1">{value}</p>
          {change !== undefined && (
            <div className="flex items-center gap-1 mt-2">
              {change > 0 ? (
                <TrendingUp size={16} className="text-green-500" />
              ) : (
                <TrendingDown size={16} className="text-red-500" />
              )}
              <span className={`text-sm ${change > 0 ? 'text-green-500' : 'text-red-500'}`}>
                {Math.abs(change)}%
              </span>
            </div>
          )}
        </div>
        <div className={`p-2 rounded-lg ${color}`}>
          {icon}
        </div>
      </div>
    </div>
  )
}

interface MetricsDashboardProps {
  view?: 'operational' | 'executive'
}

export const MetricsDashboard: React.FC<MetricsDashboardProps> = ({ view = 'operational' }) => {
  const [stats, setStats] = useState({
    alerts_delivered: 0,
    cases_created: 0,
    active_cases: 0,
    active_sessions: 0,
    workflows_executed: 0
  })

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/dashboard/stats')
        if (response.ok) {
          const data = await response.json()
          setStats(data.stats || stats)
        }
      } catch (error) {
        console.error('Failed to fetch stats:', error)
      }
    }

    fetchStats()
    // Refresh stats every 30 seconds
    const interval = setInterval(fetchStats, 30000)
    return () => clearInterval(interval)
  }, [])

  const metrics = [
    {
      title: 'Total Alerts',
      value: stats.alerts_delivered || '0',
      change: 12,
      icon: <Shield size={20} className="text-white" />,
      color: 'bg-blue-500'
    },
    {
      title: 'Cost Saved',
      value: '$45,678',
      change: 34,
      icon: <DollarSign size={20} className="text-white" />,
      color: 'bg-green-500'
    },
    {
      title: 'Active Cases',
      value: stats.active_cases || '0',
      change: -5,
      icon: <Activity size={20} className="text-white" />,
      color: 'bg-orange-500'
    },
    {
      title: 'Active Sessions',
      value: stats.active_sessions || '0',
      change: 0,
      icon: <Users size={20} className="text-white" />,
      color: 'bg-purple-500'
    }
  ]

  return (
    <div className="p-6 bg-gray-50 h-full overflow-auto">
      <h2 className="text-2xl font-bold mb-6">
        {view === 'executive' ? 'Executive Overview' : 'Operational Metrics'}
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {metrics.map((metric, idx) => (
          <MetricCard key={idx} {...metric} />
        ))}
      </div>

      {/* Additional charts would go here */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="font-semibold mb-4">Alert Trend (7 Days)</h3>
          <div className="h-48 flex items-center justify-center border-2 border-dashed border-gray-300 rounded">
            <span className="text-gray-500">Chart placeholder (Recharts)</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="font-semibold mb-4">Pattern Usage</h3>
          <div className="h-48 flex items-center justify-center border-2 border-dashed border-gray-300 rounded">
            <span className="text-gray-500">Chart placeholder (Recharts)</span>
          </div>
        </div>
      </div>

      {view === 'executive' && (
        <div className="mt-6 bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="font-semibold mb-4">Key Performance Indicators</h3>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-3xl font-bold text-green-600">99.97%</p>
              <p className="text-sm text-gray-600">Cost Reduction</p>
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold text-blue-600">4.8x</p>
              <p className="text-sm text-gray-600">Entity Extraction</p>
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold text-purple-600">&lt;5s</p>
              <p className="text-sm text-gray-600">Avg Response Time</p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default MetricsDashboard