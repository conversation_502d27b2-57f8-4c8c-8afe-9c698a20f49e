#!/usr/bin/env python3
"""
Query Generator Validation Test
Validates Phase 2 implementation is working correctly
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8005"

def test_endpoints_exist():
    """Test that all endpoints exist and return valid responses"""
    print("\n" + "="*60)
    print("ENDPOINT VALIDATION TEST")
    print("="*60)

    tests_passed = 0
    tests_total = 3

    # Test 1: GET /api/investigation/sources
    print("\n[1/3] Testing GET /api/investigation/sources...")
    try:
        response = requests.get(f"{BASE_URL}/api/investigation/sources")
        if response.status_code == 200:
            data = response.json()
            assert 'total_sources' in data
            assert 'sources' in data
            print(f"    PASS - Status 200, returned {data['total_sources']} sources")
            tests_passed += 1
        else:
            print(f"    FAIL - Status {response.status_code}")
    except Exception as e:
        print(f"    FAIL - Error: {e}")

    # Test 2: GET /api/investigation/guidance/{type}/{value}
    print("\n[2/3] Testing GET /api/investigation/guidance/host/SERVER-01...")
    try:
        response = requests.get(f"{BASE_URL}/api/investigation/guidance/host/SERVER-01")
        if response.status_code == 200:
            data = response.json()
            assert 'entity_type' in data
            assert 'entity_value' in data
            assert 'total_templates' in data
            assert data['entity_type'] == 'host'
            assert data['entity_value'] == 'SERVER-01'
            print(f"    PASS - Status 200, found {data['total_templates']} templates")
            tests_passed += 1
        else:
            print(f"    FAIL - Status {response.status_code}")
    except Exception as e:
        print(f"    FAIL - Error: {e}")

    # Test 3: POST /api/investigation/generate-queries
    print("\n[3/3] Testing POST /api/investigation/generate-queries...")
    try:
        payload = {
            "entity_type": "host",
            "entity_value": "SERVER-01"
        }
        response = requests.post(
            f"{BASE_URL}/api/investigation/generate-queries",
            json=payload
        )
        if response.status_code == 200:
            data = response.json()
            assert 'entity_type' in data
            assert 'entity_value' in data
            assert 'total_queries' in data
            assert 'queries' in data
            print(f"    PASS - Status 200, generated {data['total_queries']} queries")
            tests_passed += 1
        else:
            print(f"    FAIL - Status {response.status_code}")
    except Exception as e:
        print(f"    FAIL - Error: {e}")

    # Summary
    print("\n" + "="*60)
    print(f"RESULTS: {tests_passed}/{tests_total} tests passed")
    print("="*60)

    return tests_passed == tests_total


def test_template_data():
    """Test that templates are properly seeded"""
    print("\n" + "="*60)
    print("TEMPLATE DATA VALIDATION")
    print("="*60)

    # Get guidance for all entity types
    entity_types = ['host', 'user', 'ip', 'process', 'hash']

    for entity_type in entity_types:
        response = requests.get(
            f"{BASE_URL}/api/investigation/guidance/{entity_type}/test-value"
        )
        if response.status_code == 200:
            data = response.json()
            templates = data.get('total_templates', 0)
            print(f"  {entity_type:10s} -> {templates} templates available")
        else:
            print(f"  {entity_type:10s} -> ERROR (status {response.status_code})")

    print("\n" + "="*60)


def test_query_structure():
    """Test that generated queries have correct structure"""
    print("\n" + "="*60)
    print("QUERY STRUCTURE VALIDATION")
    print("="*60)

    payload = {
        "entity_type": "ip",
        "entity_value": "*************",
        "time_window": {
            "start": (datetime.now() - timedelta(hours=1)).isoformat(),
            "end": datetime.now().isoformat()
        }
    }

    response = requests.post(
        f"{BASE_URL}/api/investigation/generate-queries",
        json=payload
    )

    if response.status_code == 200:
        data = response.json()

        print(f"\nEntity: {data['entity_type']}={data['entity_value']}")
        print(f"Total Queries: {data['total_queries']}")
        print(f"Available Sources: {data['available_sources']}")

        # Check first query structure
        if data['queries']:
            query = data['queries'][0]
            required_fields = [
                'source_type', 'source_name', 'query_language',
                'query', 'what_we_have', 'what_to_look_for',
                'limitations', 'available', 'time_range'
            ]

            missing_fields = [f for f in required_fields if f not in query]

            if not missing_fields:
                print("\nQuery Structure: VALID")
                print(f"  Source: {query['source_name']}")
                print(f"  Language: {query['query_language']}")
                print(f"  Available: {query['available']}")
                print(f"  Query: {query['query'][:80]}...")
            else:
                print(f"\nQuery Structure: INVALID - Missing fields: {missing_fields}")
        else:
            print("\nNo queries generated (expected if no ingestion logs)")
    else:
        print(f"ERROR - Status {response.status_code}")

    print("\n" + "="*60)


def main():
    """Run all validation tests"""
    print("\n" + "="*60)
    print("QUERY GENERATOR VALIDATION TEST SUITE")
    print("Phase 2: Deterministic Query Generator")
    print("="*60)

    # Test 1: Endpoints exist and respond correctly
    endpoints_ok = test_endpoints_exist()

    # Test 2: Template data is properly seeded
    test_template_data()

    # Test 3: Query structure is correct
    test_query_structure()

    # Final summary
    print("\n" + "="*60)
    print("VALIDATION COMPLETE")
    print("="*60)

    if endpoints_ok:
        print("\nSUCCESS: All API endpoints operational!")
        print("Phase 2 Query Generator is ready for production use.")
        print("\nNote: 0 queries available because no ingestion_logs exist yet.")
        print("This is expected in development environment.")
    else:
        print("\nFAILURE: Some endpoints are not working correctly.")
        print("Check Delivery Engine logs: docker-compose logs delivery_engine")

    print("\n" + "="*60)


if __name__ == "__main__":
    main()
