#!/usr/bin/env python3
"""
Test Prompt Library - Verify prompt loading and rendering
"""

import sys
import os
import json

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from core.prompt_library import PromptLibrary


def test_prompt_library():
    """Test prompt library functionality"""

    print("[TEST] Initializing Prompt Library...")
    library = PromptLibrary()

    print(f"\n[INFO] {library}")
    print(f"[INFO] Loaded {len(library.prompts)} task types")

    # List all prompts
    print("\n[TEST] Listing all prompts:")
    all_prompts = library.list_prompts()
    for prompt in all_prompts:
        active_marker = " [ACTIVE]" if prompt['is_active'] else ""
        print(f"  - {prompt['task_type']}: {prompt['name']} v{prompt['version']}{active_marker}")
        print(f"    Variables: {', '.join(prompt['variables'])}")

    # Test Sigma Enhancement prompt
    print("\n[TEST] Testing Sigma Enhancement prompt...")
    sigma_prompt = library.get_prompt(
        task_type='sigma_enhancement',
        rule_title='Test Rule',
        rule_description='Test description',
        level='high',
        tags='test.tag1, test.tag2',
        detection='condition: selection',
        source_platform='elastic',
        target_platforms='wazuh, splunk'
    )

    if sigma_prompt:
        print("[PASS] Sigma enhancement prompt rendered successfully")
        print(f"[INFO] Prompt length: {len(sigma_prompt)} characters")
        print(f"[INFO] First 200 chars: {sigma_prompt[:200]}...")
    else:
        print("[FAIL] Failed to render sigma enhancement prompt")
        return False

    # Test Log Parsing prompt
    print("\n[TEST] Testing Log Parsing prompt...")
    log_prompt = library.get_prompt(
        task_type='log_parsing',
        log_sample='2025-09-30 12:00:00 User admin logged in from *************',
        log_source='custom_app',
        vendor='acme_corp'
    )

    if log_prompt:
        print("[PASS] Log parsing prompt rendered successfully")
        print(f"[INFO] Prompt length: {len(log_prompt)} characters")
    else:
        print("[FAIL] Failed to render log parsing prompt")
        return False

    # Test Pattern Validation prompt
    print("\n[TEST] Testing Pattern Validation prompt...")
    pattern_prompt = library.get_prompt(
        task_type='pattern_validation',
        pattern_name='Suspicious PowerShell',
        pattern_description='Detects encoded PowerShell commands',
        pattern_logic='process.name = "powershell.exe" AND process.command_line contains "-enc"',
        occurrences='127',
        false_positive_rate='2.3'
    )

    if pattern_prompt:
        print("[PASS] Pattern validation prompt rendered successfully")
        print(f"[INFO] Prompt length: {len(pattern_prompt)} characters")
    else:
        print("[FAIL] Failed to render pattern validation prompt")
        return False

    # Test Threat Analysis prompt
    print("\n[TEST] Testing Threat Analysis prompt...")
    threat_data = {
        'indicators': ['*********', 'evil.com'],
        'threat_type': 'malware',
        'confidence': 0.90
    }
    threat_prompt = library.get_prompt(
        task_type='threat_analysis',
        threat_data=json.dumps(threat_data, indent=2),
        ioc_count='2',
        source='AlienVault OTX'
    )

    if threat_prompt:
        print("[PASS] Threat analysis prompt rendered successfully")
        print(f"[INFO] Prompt length: {len(threat_prompt)} characters")
    else:
        print("[FAIL] Failed to render threat analysis prompt")
        return False

    # Test prompt statistics
    print("\n[TEST] Checking prompt statistics...")
    stats = library.get_prompt_statistics()
    print(f"[INFO] Recorded {len(stats)} prompt usages")

    # Test version switching
    print("\n[TEST] Testing version management...")
    template = library.get_template('sigma_enhancement')
    if template:
        print(f"[INFO] Active template: {template.name} v{template.version}")
        print(f"[INFO] Required variables: {template.variables}")

    # Test hot-reload
    print("\n[TEST] Testing hot-reload...")
    reload_success = library.reload_prompts()
    if reload_success:
        print("[PASS] Hot-reload successful")
        print(f"[INFO] Reloaded {sum(len(v) for v in library.prompts.values())} prompts")
    else:
        print("[WARN] Hot-reload had issues (may be expected if no prompts directory)")

    print("\n" + "=" * 60)
    print("[SUCCESS] All Prompt Library tests passed!")
    print("=" * 60)
    return True


if __name__ == "__main__":
    try:
        success = test_prompt_library()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n[ERROR] Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
