/**
 * AI Analysis Tab - AI-Powered Insights and Reasoning
 */

.ai-analysis-tab {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
}

/* No AI Analysis State */
.no-ai-analysis {
  text-align: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.no-ai-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.no-ai-analysis h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.no-ai-analysis p {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0;
}

.no-ai-analysis .hint {
  font-size: 13px;
  font-style: italic;
  color: #9ca3af;
  margin-top: 12px;
}

/* AI Header */
.ai-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  padding: 24px;
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-title h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
}

.ai-title p {
  margin: 0;
  font-size: 13px;
  opacity: 0.9;
}

.model-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.15);
  padding: 12px 16px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.model-selector label {
  font-size: 13px;
  font-weight: 600;
  opacity: 0.9;
}

.model-selector select {
  padding: 6px 12px;
  background: #ffffff;
  color: #111827;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
}

.model-info {
  display: flex;
  gap: 6px;
}

.info-badge {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  white-space: nowrap;
}

/* AI Summary Card */
.ai-summary-card {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.summary-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.summary-meta {
  display: flex;
  gap: 16px;
}

.meta-item {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

.summary-content {
  padding: 20px;
}

.summary-content p {
  margin: 0;
  font-size: 14px;
  color: #374151;
  line-height: 1.6;
}

/* Insights Grid */
.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.insight-card {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  border-left: 4px solid;
  overflow: hidden;
  transition: all 0.2s;
}

.insight-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.insight-header {
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.insight-title-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
}

.insight-icon {
  font-size: 24px;
}

.insight-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  flex: 1;
}

.insight-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.confidence-badge {
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 700;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.category-badge {
  padding: 4px 10px;
  background: #f3f4f6;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.insight-description {
  padding: 16px 20px;
  font-size: 14px;
  color: #374151;
  line-height: 1.6;
}

.insight-footer {
  padding: 12px 20px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.evidence-toggle {
  background: transparent;
  color: #3b82f6;
  border: none;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: color 0.2s;
}

.evidence-toggle:hover {
  color: #2563eb;
}

.evidence-list {
  padding: 16px 20px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.evidence-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 6px 0;
  font-size: 13px;
  color: #374151;
  line-height: 1.5;
}

.evidence-bullet {
  color: #3b82f6;
  font-weight: 700;
  flex-shrink: 0;
}

.evidence-text {
  flex: 1;
}

/* Crystallization Section */
.crystallization-section {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.crystallization-section h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.crystallization-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.crystallization-card {
  padding: 20px;
  background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  text-align: center;
}

.crystal-icon {
  font-size: 40px;
  margin-bottom: 12px;
}

.crystal-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.crystal-value {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 8px;
}

.crystal-desc {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

/* Reasoning Section */
.reasoning-section {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.reasoning-section h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.reasoning-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
  padding-left: 40px;
}

.reasoning-steps::before {
  content: '';
  position: absolute;
  left: 18px;
  top: 30px;
  bottom: 30px;
  width: 2px;
  background: linear-gradient(to bottom, #667eea 0%, #764ba2 100%);
}

.reasoning-step {
  display: flex;
  gap: 16px;
  position: relative;
}

.step-number {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 700;
  flex-shrink: 0;
  z-index: 1;
  position: absolute;
  left: -40px;
}

.step-content {
  flex: 1;
  padding: 8px 0;
}

.step-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.step-desc {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.5;
}

/* AI Actions */
.ai-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.ai-action-btn {
  padding: 12px 24px;
  background: #ffffff;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.ai-action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.ai-action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
}

.ai-action-btn.primary:hover {
  background: linear-gradient(135deg, #5568d3 0%, #6a3f8f 100%);
}

/* Responsive */
@media (max-width: 1200px) {
  .insights-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .ai-analysis-tab {
    padding: 16px;
  }

  .ai-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .model-selector {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
  }

  .model-selector select {
    width: 100%;
  }

  .summary-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .reasoning-steps {
    padding-left: 50px;
  }

  .step-number {
    left: -50px;
  }

  .ai-actions {
    flex-direction: column;
  }

  .ai-action-btn {
    width: 100%;
  }
}

/* Print Styles */
@media print {
  .ai-header,
  .model-selector,
  .evidence-toggle,
  .ai-actions {
    display: none;
  }

  .evidence-list {
    display: block !important;
  }

  .insight-card {
    page-break-inside: avoid;
  }
}
