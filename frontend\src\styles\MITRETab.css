/**
 * MITRE ATT&CK Tab - Attack Chain and Matrix Visualization
 */

.mitre-tab {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
}

/* No MITRE State */
.no-mitre {
  text-align: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.no-mitre-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.no-mitre h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.no-mitre p {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0;
}

.no-mitre .hint {
  font-size: 13px;
  font-style: italic;
  color: #9ca3af;
  margin-top: 12px;
}

/* MITRE Header */
.mitre-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.mitre-summary h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.mitre-stats {
  display: flex;
  gap: 32px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #3b82f6;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mitre-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.view-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 6px;
  padding: 2px;
}

.toggle-btn {
  padding: 8px 16px;
  background: transparent;
  color: #6b7280;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.toggle-btn.active {
  background: #ffffff;
  color: #111827;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.export-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.export-btn:hover {
  background: #2563eb;
}

/* Attack Chain View */
.attack-chain {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.attack-chain h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.chain-timeline {
  display: flex;
  align-items: center;
  gap: 16px;
  overflow-x: auto;
  padding: 20px 0;
}

.chain-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
  border-left: 4px solid;
  min-width: 180px;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.2s;
}

.chain-step:hover {
  background: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.chain-step.selected {
  background: #eff6ff;
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.step-number {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 24px;
  height: 24px;
  background: #3b82f6;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
}

.step-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.step-content {
  text-align: center;
  width: 100%;
}

.step-id {
  font-size: 11px;
  font-weight: 700;
  color: #6b7280;
  margin-bottom: 4px;
}

.step-name {
  font-size: 13px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 6px;
  line-height: 1.3;
}

.step-tactic {
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.chain-connector {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.connector-line {
  width: 40px;
  height: 2px;
  background: #d1d5db;
}

.connector-arrow {
  font-size: 20px;
  color: #9ca3af;
  margin-top: -2px;
}

/* Matrix View */
.attack-matrix {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.attack-matrix h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.matrix-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.tactic-column {
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.tactic-header {
  padding: 12px;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.tactic-icon {
  font-size: 24px;
}

.tactic-name {
  font-size: 13px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.tactic-count {
  font-size: 11px;
  opacity: 0.9;
}

.technique-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: #f9fafb;
}

.technique-card {
  padding: 12px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  border-left: 3px solid;
  cursor: pointer;
  transition: all 0.2s;
}

.technique-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateX(4px);
}

.technique-card.selected {
  background: #eff6ff;
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.technique-card .technique-id {
  font-size: 11px;
  font-weight: 700;
  color: #6b7280;
  margin-bottom: 4px;
}

.technique-card .technique-name {
  font-size: 12px;
  font-weight: 600;
  color: #111827;
  line-height: 1.3;
}

/* Kill Chain Section */
.kill-chain-section {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.kill-chain-section h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.kill-chain {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding: 12px 0;
}

.kill-chain-phase {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
  min-width: 140px;
  flex-shrink: 0;
  opacity: 0.4;
  transition: all 0.3s;
}

.kill-chain-phase.active {
  opacity: 1;
  background: #ffffff;
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

.phase-name {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  text-align: center;
  margin-bottom: 8px;
}

.phase-tactics {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.phase-tactic {
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* Technique Details */
.technique-details-section {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.technique-details-section h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.technique-details {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.detail-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.detail-icon {
  font-size: 28px;
}

.detail-name {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.detail-tactic {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-body {
  padding: 20px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.detail-section p {
  font-size: 14px;
  color: #374151;
  line-height: 1.6;
  margin: 0;
}

.detail-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.detail-section li {
  padding: 8px 0;
  padding-left: 24px;
  font-size: 13px;
  color: #374151;
  line-height: 1.5;
  position: relative;
}

.detail-section li::before {
  content: "•";
  position: absolute;
  left: 8px;
  color: #3b82f6;
  font-weight: 700;
}

.detail-actions {
  display: flex;
  gap: 8px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.detail-action-btn {
  padding: 10px 16px;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.detail-action-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

/* Coverage Section */
.coverage-section {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.coverage-section h4 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.coverage-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.coverage-card {
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  text-align: center;
}

.coverage-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.coverage-value {
  font-size: 28px;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 8px;
}

.coverage-description {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

/* Responsive */
@media (max-width: 1200px) {
  .matrix-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }

  .chain-timeline {
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .mitre-tab {
    padding: 16px;
  }

  .mitre-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .mitre-actions {
    width: 100%;
    justify-content: space-between;
  }

  .chain-timeline {
    flex-direction: column;
    align-items: stretch;
  }

  .chain-step {
    min-width: auto;
  }

  .chain-connector {
    transform: rotate(90deg);
    margin: 8px 0;
  }

  .kill-chain {
    flex-direction: column;
  }

  .kill-chain-phase {
    min-width: auto;
  }

  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .detail-actions {
    flex-direction: column;
  }

  .detail-action-btn {
    width: 100%;
  }
}

/* Print Styles */
@media print {
  .mitre-actions,
  .view-toggle,
  .export-btn {
    display: none;
  }

  .technique-details-section {
    page-break-inside: avoid;
  }

  .chain-step,
  .technique-card {
    break-inside: avoid;
  }
}
