# SIEMLess v2.0 - Backend Engine Docker Image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    cron \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy base engine
COPY base_engine.py ./base_engine.py

# Copy backend engine code and supporting modules
COPY backend_engine.py .
COPY correlation_engine.py .
COPY log_source_identifier.py .
COPY source_update_manager.py .
COPY opencti_integration.py .
COPY otx_integration.py .
COPY ioc_filters.py .
COPY ioc_validator.py .
COPY rule_monitor.py .
COPY query_translator.py .
# Copy log source quality modules
COPY log_source_quality.py .
COPY detection_fidelity_calculator.py .
COPY correlation_requirements.py .
# Copy SIEM schema loader for parser generation
COPY siem_schema_loader.py .
# Copy Apache AGE graph service
COPY age_graph_service.py .
# Copy authentication middleware
COPY auth_middleware.py .
# Copy MITRE ATT&CK mapper modules
COPY mitre_attack_mapper.py .
COPY mitre_http_handlers.py .
COPY mitre_ai_intelligence.py .
COPY mitre_ai_http_handlers.py .
# Copy Update Scheduler
COPY update_scheduler.py .
# Copy Log Retention Policy Engine
COPY log_retention_policy_engine.py .

# Create directories for data and logs
RUN mkdir -p /tmp/claude/backend /tmp/claude/training_data /var/log/siemless

# Create directories for storage tiers
RUN mkdir -p /data/cold_storage /data/backups

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import asyncio; from backend_engine import BackendEngine; engine = BackendEngine(); print('Health check passed')" || exit 1

# Run the backend engine
CMD ["python", "backend_engine.py"]