# Context Plugin Templates

Quick-start templates for creating new context source plugins.

---

## Template 1: EDR/Endpoint Security (CrowdStrike, SentinelOne, Carbon Black)

```python
"""
{SOURCE_NAME} Context Source Plugin
Provides asset inventory, detections, and incident data
"""

import aiohttp
from datetime import datetime
from typing import Dict, Any, List
from context_source_plugin import (
    ContextSourcePlugin, ContextQuery, ContextResult, ContextCategory
)


class {SourceName}ContextPlugin(ContextSourcePlugin):
    """
    {SOURCE_NAME} plugin supporting:
    - Asset inventory (hosts/agents)
    - Security detections/alerts
    - Incidents/cases
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_token = config.get('api_token')
        self.console_url = config.get('console_url', 'https://api.{source}.com')
        self.api_version = config.get('api_version', 'v1')

    def get_source_name(self) -> str:
        return "{source_name}"

    def get_supported_categories(self) -> List[ContextCategory]:
        return [
            ContextCategory.ASSET,      # Endpoints/agents
            ContextCategory.DETECTION,  # Threats/alerts
            ContextCategory.INCIDENT    # Incidents/storylines
        ]

    def get_supported_query_types(self) -> List[str]:
        return ['ip', 'hostname', 'user', 'file_hash']

    async def validate_credentials(self) -> bool:
        """Test API connection"""
        try:
            headers = {'Authorization': f'Bearer {self.api_token}'}
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f'{self.console_url}/api/{self.api_version}/ping',
                    headers=headers
                ) as resp:
                    return resp.status == 200
        except Exception as e:
            self.logger.error(f"Credential validation failed: {e}")
            return False

    async def query_context(self, query: ContextQuery) -> List[ContextResult]:
        """Query {SOURCE_NAME} for context data"""
        results = []

        for category in query.categories:
            if category == ContextCategory.ASSET:
                asset_results = await self._query_assets(query)
                results.extend(asset_results)

            elif category == ContextCategory.DETECTION:
                detection_results = await self._query_detections(query)
                results.extend(detection_results)

            elif category == ContextCategory.INCIDENT:
                incident_results = await self._query_incidents(query)
                results.extend(incident_results)

        return results

    async def _query_assets(self, query: ContextQuery) -> List[ContextResult]:
        """Query asset/agent inventory"""
        results = []
        try:
            headers = {'Authorization': f'Bearer {self.api_token}'}

            # Build query params based on query type
            params = {}
            if query.query_type == 'ip':
                params['ip_address'] = query.query_value
            elif query.query_type == 'hostname':
                params['hostname'] = query.query_value
            elif query.query_type == 'user':
                params['user'] = query.query_value

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f'{self.console_url}/api/{self.api_version}/agents',
                    headers=headers,
                    params=params
                ) as resp:
                    if resp.status == 200:
                        data = await resp.json()

                        # Map {SOURCE_NAME} fields to standard format
                        for agent in data.get('data', []):
                            results.append(ContextResult(
                                source_name=self.get_source_name(),
                                category=ContextCategory.ASSET,
                                data={
                                    'hostname': agent.get('computerName'),
                                    'local_ip': agent.get('networkInterfaces', [{}])[0].get('inet'),
                                    'os_version': agent.get('osName'),
                                    'last_login_user': agent.get('lastLoggedInUserName'),
                                    'agent_version': agent.get('agentVersion'),
                                    'status': agent.get('isActive'),
                                    'last_seen': agent.get('lastActiveDate'),
                                    # Add more fields as needed
                                },
                                confidence=1.0,
                                timestamp=datetime.utcnow().isoformat()
                            ))

        except Exception as e:
            self.logger.error(f"Error querying {self.get_source_name()} assets: {e}")

        return results

    async def _query_detections(self, query: ContextQuery) -> List[ContextResult]:
        """Query security detections/threats"""
        results = []
        try:
            headers = {'Authorization': f'Bearer {self.api_token}'}

            # Build filter based on query and time range
            params = {'limit': query.max_results}
            if query.time_range:
                params['createdAt__gte'] = query.time_range.get('start', 'now-24h')

            # Add query-specific filters
            if query.query_type == 'hostname':
                params['agentComputerName'] = query.query_value
            elif query.query_type == 'file_hash':
                params['contentHash'] = query.query_value

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f'{self.console_url}/api/{self.api_version}/threats',
                    headers=headers,
                    params=params
                ) as resp:
                    if resp.status == 200:
                        data = await resp.json()

                        for threat in data.get('data', []):
                            results.append(ContextResult(
                                source_name=self.get_source_name(),
                                category=ContextCategory.DETECTION,
                                data={
                                    'type': 'detection',
                                    'detection_id': threat.get('id'),
                                    'threat_name': threat.get('threatName'),
                                    'severity': threat.get('threatClassification'),
                                    'status': threat.get('mitigationStatus'),
                                    'created_timestamp': threat.get('createdAt'),
                                    'device_hostname': threat.get('agentComputerName'),
                                    'device_ip': threat.get('agentNetworkStatus', {}).get('ip'),
                                    'file_path': threat.get('filePath'),
                                    'file_hash': threat.get('fileContentHash'),
                                    'mitre_techniques': threat.get('mitreTechniques', [])
                                },
                                confidence=0.95,
                                timestamp=threat.get('createdAt', datetime.utcnow().isoformat())
                            ))

        except Exception as e:
            self.logger.error(f"Error querying {self.get_source_name()} detections: {e}")

        return results

    async def _query_incidents(self, query: ContextQuery) -> List[ContextResult]:
        """Query incidents/storylines"""
        # Similar pattern to _query_detections
        # Return ContextResult objects with category=ContextCategory.INCIDENT
        return []
```

**Usage**:
1. Replace `{SOURCE_NAME}`, `{SourceName}`, `{source_name}` with your source
2. Update API endpoints to match vendor's API
3. Map vendor fields to standard fields in `data` dict
4. Register in `ingestion_engine.py`

---

## Template 2: Identity Provider (Active Directory, Okta, Azure AD)

```python
"""
{SOURCE_NAME} Context Source Plugin
Provides user and group identity information
"""

import aiohttp
from datetime import datetime
from typing import Dict, Any, List
from context_source_plugin import (
    ContextSourcePlugin, ContextQuery, ContextResult, ContextCategory
)


class {SourceName}ContextPlugin(ContextSourcePlugin):
    """
    {SOURCE_NAME} plugin for identity context:
    - User profiles
    - Group memberships
    - Authentication events
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.domain = config.get('domain')
        self.api_key = config.get('api_key')
        self.base_url = config.get('base_url', 'https://{domain}.{provider}.com')

    def get_source_name(self) -> str:
        return "{source_name}"

    def get_supported_categories(self) -> List[ContextCategory]:
        return [ContextCategory.IDENTITY]

    def get_supported_query_types(self) -> List[str]:
        return ['user', 'email']

    async def validate_credentials(self) -> bool:
        """Test API connection"""
        try:
            headers = {'Authorization': f'SSWS {self.api_key}'}
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f'{self.base_url}/api/v1/users/me',
                    headers=headers
                ) as resp:
                    return resp.status == 200
        except:
            return False

    async def query_context(self, query: ContextQuery) -> List[ContextResult]:
        """Query identity information"""
        results = []

        if ContextCategory.IDENTITY in query.categories:
            results.extend(await self._query_user(query))

        return results

    async def _query_user(self, query: ContextQuery) -> List[ContextResult]:
        """Query user profile and group membership"""
        results = []
        try:
            headers = {'Authorization': f'SSWS {self.api_key}'}

            # Search for user
            params = {}
            if query.query_type == 'user':
                params['q'] = query.query_value
            elif query.query_type == 'email':
                params['filter'] = f'profile.email eq "{query.query_value}"'

            async with aiohttp.ClientSession() as session:
                # Get user profile
                async with session.get(
                    f'{self.base_url}/api/v1/users',
                    headers=headers,
                    params=params
                ) as resp:
                    if resp.status == 200:
                        users = await resp.json()

                        for user in users:
                            user_id = user.get('id')

                            # Get user groups
                            groups = []
                            async with session.get(
                                f'{self.base_url}/api/v1/users/{user_id}/groups',
                                headers=headers
                            ) as group_resp:
                                if group_resp.status == 200:
                                    groups = await group_resp.json()

                            results.append(ContextResult(
                                source_name=self.get_source_name(),
                                category=ContextCategory.IDENTITY,
                                data={
                                    'user_id': user_id,
                                    'username': user.get('profile', {}).get('login'),
                                    'email': user.get('profile', {}).get('email'),
                                    'first_name': user.get('profile', {}).get('firstName'),
                                    'last_name': user.get('profile', {}).get('lastName'),
                                    'status': user.get('status'),
                                    'groups': [g.get('profile', {}).get('name') for g in groups],
                                    'created': user.get('created'),
                                    'last_login': user.get('lastLogin')
                                },
                                confidence=1.0,
                                timestamp=datetime.utcnow().isoformat()
                            ))

        except Exception as e:
            self.logger.error(f"Error querying {self.get_source_name()} identity: {e}")

        return results
```

---

## Template 3: Vulnerability Scanner (Tenable, Qualys, Rapid7)

```python
"""
{SOURCE_NAME} Context Source Plugin
Provides vulnerability assessment data
"""

import aiohttp
from datetime import datetime
from typing import Dict, Any, List
from context_source_plugin import (
    ContextSourcePlugin, ContextQuery, ContextResult, ContextCategory
)


class {SourceName}ContextPlugin(ContextSourcePlugin):
    """
    {SOURCE_NAME} plugin for vulnerability context
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.access_key = config.get('access_key')
        self.secret_key = config.get('secret_key')
        self.base_url = config.get('base_url', 'https://cloud.{provider}.com')

    def get_source_name(self) -> str:
        return "{source_name}"

    def get_supported_categories(self) -> List[ContextCategory]:
        return [ContextCategory.VULNERABILITY, ContextCategory.ASSET]

    def get_supported_query_types(self) -> List[str]:
        return ['ip', 'hostname']

    async def validate_credentials(self) -> bool:
        """Test API connection"""
        try:
            headers = {
                'X-ApiKeys': f'accessKey={self.access_key};secretKey={self.secret_key}'
            }
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f'{self.base_url}/session',
                    headers=headers
                ) as resp:
                    return resp.status == 200
        except:
            return False

    async def query_context(self, query: ContextQuery) -> List[ContextResult]:
        """Query vulnerability data"""
        results = []

        if ContextCategory.VULNERABILITY in query.categories:
            results.extend(await self._query_vulnerabilities(query))

        if ContextCategory.ASSET in query.categories:
            results.extend(await self._query_asset_info(query))

        return results

    async def _query_vulnerabilities(self, query: ContextQuery) -> List[ContextResult]:
        """Query vulnerabilities for IP/hostname"""
        results = []
        try:
            headers = {
                'X-ApiKeys': f'accessKey={self.access_key};secretKey={self.secret_key}'
            }

            # Build filter
            if query.query_type == 'ip':
                filter_param = f'host.target == "{query.query_value}"'
            elif query.query_type == 'hostname':
                filter_param = f'host.fqdn == "{query.query_value}"'
            else:
                return results

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f'{self.base_url}/workbenches/vulnerabilities',
                    headers=headers,
                    params={'filter': filter_param}
                ) as resp:
                    if resp.status == 200:
                        data = await resp.json()

                        for vuln in data.get('vulnerabilities', []):
                            results.append(ContextResult(
                                source_name=self.get_source_name(),
                                category=ContextCategory.VULNERABILITY,
                                data={
                                    'plugin_id': vuln.get('plugin_id'),
                                    'plugin_name': vuln.get('plugin_name'),
                                    'severity': vuln.get('severity'),
                                    'vulnerability_state': vuln.get('vulnerability_state'),
                                    'cvss_score': vuln.get('cvss_base_score'),
                                    'cve': vuln.get('cve', []),
                                    'first_found': vuln.get('first_found'),
                                    'last_found': vuln.get('last_found')
                                },
                                confidence=0.95,
                                timestamp=datetime.utcnow().isoformat()
                            ))

        except Exception as e:
            self.logger.error(f"Error querying {self.get_source_name()} vulnerabilities: {e}")

        return results

    async def _query_asset_info(self, query: ContextQuery) -> List[ContextResult]:
        """Query asset information from vulnerability scanner"""
        # Similar to _query_vulnerabilities
        # Return asset details (OS, services, open ports, etc.)
        return []
```

---

## Template 4: Network Security (Firewall, IDS/IPS, Proxy)

```python
"""
{SOURCE_NAME} Context Source Plugin
Provides network flow and security event data
"""

import aiohttp
from datetime import datetime
from typing import Dict, Any, List
from context_source_plugin import (
    ContextSourcePlugin, ContextQuery, ContextResult, ContextCategory
)


class {SourceName}ContextPlugin(ContextSourcePlugin):
    """
    {SOURCE_NAME} plugin for network context
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get('api_key')
        self.base_url = config.get('base_url')

    def get_source_name(self) -> str:
        return "{source_name}"

    def get_supported_categories(self) -> List[ContextCategory]:
        return [ContextCategory.NETWORK, ContextCategory.DETECTION]

    def get_supported_query_types(self) -> List[str]:
        return ['ip', 'domain']

    async def validate_credentials(self) -> bool:
        """Test API connection"""
        try:
            headers = {'X-API-Key': self.api_key}
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f'{self.base_url}/api/status',
                    headers=headers
                ) as resp:
                    return resp.status == 200
        except:
            return False

    async def query_context(self, query: ContextQuery) -> List[ContextResult]:
        """Query network data"""
        results = []

        if ContextCategory.NETWORK in query.categories:
            results.extend(await self._query_network_flows(query))

        if ContextCategory.DETECTION in query.categories:
            results.extend(await self._query_security_events(query))

        return results

    async def _query_network_flows(self, query: ContextQuery) -> List[ContextResult]:
        """Query network flow logs"""
        results = []
        try:
            headers = {'X-API-Key': self.api_key}

            # Build query
            params = {
                'start_time': query.time_range.get('start', 'now-1h') if query.time_range else 'now-1h',
                'end_time': query.time_range.get('end', 'now') if query.time_range else 'now'
            }

            if query.query_type == 'ip':
                params['ip'] = query.query_value
            elif query.query_type == 'domain':
                params['domain'] = query.query_value

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f'{self.base_url}/api/v1/logs/traffic',
                    headers=headers,
                    params=params
                ) as resp:
                    if resp.status == 200:
                        data = await resp.json()

                        # Aggregate flows
                        flow_summary = {
                            'total_flows': len(data.get('logs', [])),
                            'unique_destinations': set(),
                            'protocols': {},
                            'ports': {},
                            'total_bytes': 0
                        }

                        for log in data.get('logs', []):
                            flow_summary['unique_destinations'].add(log.get('dst_ip'))
                            proto = log.get('proto', 'unknown')
                            flow_summary['protocols'][proto] = flow_summary['protocols'].get(proto, 0) + 1
                            port = log.get('dst_port')
                            flow_summary['ports'][port] = flow_summary['ports'].get(port, 0) + 1
                            flow_summary['total_bytes'] += log.get('bytes', 0)

                        results.append(ContextResult(
                            source_name=self.get_source_name(),
                            category=ContextCategory.NETWORK,
                            data={
                                'query_ip': query.query_value,
                                'total_flows': flow_summary['total_flows'],
                                'unique_destinations': len(flow_summary['unique_destinations']),
                                'top_protocols': dict(sorted(flow_summary['protocols'].items(),
                                                           key=lambda x: x[1], reverse=True)[:5]),
                                'top_ports': dict(sorted(flow_summary['ports'].items(),
                                                       key=lambda x: x[1], reverse=True)[:5]),
                                'total_bytes': flow_summary['total_bytes']
                            },
                            confidence=0.9,
                            timestamp=datetime.utcnow().isoformat()
                        ))

        except Exception as e:
            self.logger.error(f"Error querying {self.get_source_name()} network flows: {e}")

        return results

    async def _query_security_events(self, query: ContextQuery) -> List[ContextResult]:
        """Query security events (blocks, threats, etc.)"""
        # Similar pattern - query threat logs
        return []
```

---

## Quick Reference: Category → Fields Mapping

### ContextCategory.ASSET
**Standard Fields**:
- `hostname` - Device hostname
- `local_ip` - Internal IP address
- `os_version` - Operating system version
- `last_login_user` - Last logged-in user
- `mac_address` - MAC address
- `machine_domain` - Domain membership
- `status` - Online/offline status
- `last_seen` - Last activity timestamp

### ContextCategory.DETECTION
**Standard Fields**:
- `detection_id` or `alert_id` - Unique identifier
- `severity` - Critical/high/medium/low
- `status` - Open/in_progress/closed
- `created_timestamp` - When detected
- `device_hostname` - Affected device
- `device_ip` - Affected IP
- `user_name` - Affected user
- `mitre_techniques` - Array of MITRE ATT&CK IDs
- `mitre_tactics` - Array of tactics

### ContextCategory.INCIDENT
**Standard Fields**:
- `incident_id` - Unique identifier
- `name` - Incident name/title
- `state` - Open/investigating/closed
- `severity` or `fine_score` - Severity metric
- `assigned_to` - Assigned analyst
- `tactics` - Array of MITRE tactics
- `techniques` - Array of MITRE techniques
- `hosts` - Array of affected hosts

### ContextCategory.IDENTITY
**Standard Fields**:
- `user_id` - Unique user ID
- `username` - Username/login
- `email` - Email address
- `first_name`, `last_name` - Name fields
- `status` - Active/suspended/locked
- `groups` - Array of group names
- `last_login` - Last authentication

### ContextCategory.VULNERABILITY
**Standard Fields**:
- `plugin_id` or `vuln_id` - Scanner plugin ID
- `plugin_name` or `title` - Vulnerability name
- `severity` - Critical/high/medium/low
- `cvss_score` - CVSS score
- `cve` - Array of CVE IDs
- `first_found` - First detection date
- `last_found` - Most recent scan

### ContextCategory.NETWORK
**Standard Fields**:
- `source_ip`, `destination_ip` - Flow endpoints
- `source_port`, `destination_port` - Ports
- `protocol` - TCP/UDP/ICMP
- `bytes_sent`, `bytes_received` - Traffic volume
- `duration` - Connection duration
- `action` - Allow/block/drop

---

## Registration Pattern

Add to `ingestion_engine.py`:

```python
def _setup_context_plugins(self):
    """Setup context source plugins"""

    # CrowdStrike
    if os.getenv('CROWDSTRIKE_CLIENT_ID'):
        crowdstrike_plugin = CrowdStrikeContextPlugin({
            'enabled': True,
            'client_id': os.getenv('CROWDSTRIKE_CLIENT_ID'),
            'client_secret': os.getenv('CROWDSTRIKE_CLIENT_SECRET')
        })
        self.context_manager.register_plugin(crowdstrike_plugin)

    # SentinelOne
    if os.getenv('SENTINELONE_API_TOKEN'):
        sentinelone_plugin = SentinelOneContextPlugin({
            'enabled': True,
            'api_token': os.getenv('SENTINELONE_API_TOKEN'),
            'console_url': os.getenv('SENTINELONE_CONSOLE_URL')
        })
        self.context_manager.register_plugin(sentinelone_plugin)

    # Active Directory
    if os.getenv('AD_USERNAME'):
        ad_plugin = ActiveDirectoryContextPlugin({
            'enabled': True,
            'username': os.getenv('AD_USERNAME'),
            'password': os.getenv('AD_PASSWORD'),
            'domain': os.getenv('AD_DOMAIN')
        })
        self.context_manager.register_plugin(ad_plugin)

    # Tenable
    if os.getenv('TENABLE_ACCESS_KEY'):
        tenable_plugin = TenableContextPlugin({
            'enabled': True,
            'access_key': os.getenv('TENABLE_ACCESS_KEY'),
            'secret_key': os.getenv('TENABLE_SECRET_KEY')
        })
        self.context_manager.register_plugin(tenable_plugin)

    self.logger.info(f"Registered {len(self.context_manager.plugins)} context plugins")
```

---

## Testing Your Plugin

```python
# Test script: test_my_plugin.py

import asyncio
from my_plugin import MyContextPlugin
from context_source_plugin import create_context_query

async def test():
    # Initialize plugin
    plugin = MyContextPlugin({
        'enabled': True,
        'api_token': 'your_test_token'
    })

    # Validate credentials
    valid = await plugin.validate_credentials()
    print(f"Credentials valid: {valid}")

    if valid:
        # Create test query
        query = create_context_query('ip', '*************', ['asset', 'detection'])

        # Query plugin
        results = await plugin.query_context(query)

        print(f"Got {len(results)} results:")
        for result in results:
            print(f"  - {result.category.value}: {result.data.get('hostname', 'N/A')}")

asyncio.run(test())
```

Run: `python test_my_plugin.py`

---

These templates cover 90% of use cases. Just replace placeholders and map API fields!
