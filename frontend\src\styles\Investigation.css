/**
 * Alert Investigation Screen - Main Styles
 */

.investigation-screen {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f9fafb;
  overflow: hidden;
}

/* Loading & Error States */
.loading-screen,
.error-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #ffffff;
}

.spinner-large {
  font-size: 48px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-screen p,
.error-screen p {
  margin-top: 20px;
  font-size: 16px;
  color: #6b7280;
}

.error-screen p {
  color: #dc2626;
  margin-bottom: 20px;
}

.back-button {
  padding: 10px 20px;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.back-button:hover {
  background: #2563eb;
}

/* Investigation Header */
.investigation-header {
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  padding: 20px 24px;
}

.back-link {
  display: inline-flex;
  align-items: center;
  color: #3b82f6;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  transition: color 0.2s;
}

.back-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

.alert-header-info {
  margin-bottom: 16px;
}

.severity-row {
  margin-bottom: 8px;
}

.severity-badge-large {
  display: inline-block;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 0.5px;
  padding: 6px 12px;
  border-radius: 6px;
  background: #fef2f2;
}

.alert-title-large {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin: 8px 0;
}

.alert-metadata,
.rule-info {
  display: flex;
  gap: 12px;
  font-size: 13px;
  color: #6b7280;
  margin-top: 8px;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-button {
  padding: 8px 16px;
  background: #ffffff;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.action-button:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.action-button.danger {
  color: #dc2626;
  border-color: #fca5a5;
}

.action-button.danger:hover {
  background: #fef2f2;
  border-color: #dc2626;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background: #ffffff;
  border-bottom: 2px solid #e5e7eb;
  overflow-x: auto;
  scrollbar-width: thin;
}

.tab-navigation::-webkit-scrollbar {
  height: 4px;
}

.tab-navigation::-webkit-scrollbar-track {
  background: #f3f4f6;
}

.tab-navigation::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.tab {
  padding: 12px 20px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  position: relative;
  margin-bottom: -2px;
}

.tab:hover {
  color: #111827;
  background: #f9fafb;
}

.tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: #eff6ff;
}

.tab-badge {
  background: #3b82f6;
  color: #ffffff;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.tab.active .tab-badge {
  background: #ffffff;
  color: #3b82f6;
}

/* Tab Content */
.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  background: #f9fafb;
}

.tab-content::-webkit-scrollbar {
  width: 10px;
}

.tab-content::-webkit-scrollbar-track {
  background: #f3f4f6;
}

.tab-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 5px;
}

.tab-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Responsive */
@media (max-width: 1024px) {
  .investigation-header {
    padding: 16px 20px;
  }

  .alert-title-large {
    font-size: 20px;
  }

  .header-actions {
    margin-top: 12px;
  }

  .tab-content {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .investigation-header {
    padding: 12px 16px;
  }

  .alert-title-large {
    font-size: 18px;
  }

  .alert-metadata,
  .rule-info {
    flex-direction: column;
    gap: 4px;
  }

  .tab {
    padding: 10px 16px;
    font-size: 13px;
  }

  .tab-content {
    padding: 12px;
  }

  .action-button {
    flex: 1;
    justify-content: center;
  }
}

/* Print Styles */
@media print {
  .investigation-header {
    border-bottom: 2px solid #000000;
  }

  .back-link,
  .header-actions,
  .tab-navigation {
    display: none;
  }

  .tab-content {
    overflow: visible;
  }
}

/* ========================================
   Query Generator Tab Styles
   ======================================== */

.query-generator-tab {
  padding: 20px;
}

/* Entity Selector Bar */
.entity-selector-bar {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.entity-selector-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.entity-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex: 1;
}

.entity-chip {
  padding: 6px 12px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.entity-chip:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.entity-chip.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: #ffffff;
}

.time-range-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-range-selector label {
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
}

.time-range-selector select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  background: #ffffff;
  cursor: pointer;
}

/* Loading/Error States */
.query-loading,
.query-error,
.no-queries {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  text-align: center;
}

.query-loading .spinner {
  font-size: 48px;
  margin-bottom: 16px;
  animation: spin 2s linear infinite;
}

.query-error {
  color: #dc2626;
}

.no-queries {
  color: #6b7280;
}

/* Queries Container */
.queries-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Summary Header */
.queries-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 16px;
  margin-bottom: 8px;
}

.summary-stat {
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.summary-stat.available {
  border-color: #10b981;
  background: #f0fdf4;
}

.summary-stat.unavailable {
  border-color: #f59e0b;
  background: #fffbeb;
}

.stat-value {
  display: block;
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 4px;
}

.stat-label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Queries Section */
.queries-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.queries-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: 16px;
}

/* Query Card */
.query-card {
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  transition: box-shadow 0.2s;
}

.query-card.available {
  border-color: #10b981;
}

.query-card.unavailable {
  border-color: #f59e0b;
  opacity: 0.75;
}

.query-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Card Header */
.query-card-header {
  padding: 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.source-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.source-name {
  font-size: 15px;
  font-weight: 600;
  color: #111827;
}

.source-language {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  background: #e5e7eb;
  padding: 2px 8px;
  border-radius: 4px;
}

.source-details {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.detail-badge {
  font-size: 12px;
  color: #6b7280;
  background: #ffffff;
  border: 1px solid #d1d5db;
  padding: 2px 8px;
  border-radius: 4px;
}

.log-count {
  font-size: 12px;
  font-weight: 500;
  color: #10b981;
}

/* Query Display */
.query-display {
  padding: 16px;
}

.query-text {
  background: #1f2937;
  color: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  overflow-x: auto;
  margin-bottom: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.query-actions {
  display: flex;
  gap: 8px;
}

.copy-button,
.deep-link-button {
  flex: 1;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  text-align: center;
  border: none;
}

.copy-button {
  background: #3b82f6;
  color: #ffffff;
}

.copy-button:hover:not(:disabled) {
  background: #2563eb;
}

.copy-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.deep-link-button {
  background: #10b981;
  color: #ffffff;
}

.deep-link-button:hover {
  background: #059669;
}

/* Guidance Section */
.query-guidance {
  border-top: 1px solid #e5e7eb;
}

.guidance-toggle {
  width: 100%;
  padding: 12px 16px;
  background: #f9fafb;
  border: none;
  font-size: 13px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  text-align: left;
  transition: background 0.2s;
}

.guidance-toggle:hover {
  background: #f3f4f6;
}

.guidance-content {
  padding: 16px;
  background: #ffffff;
}

.guidance-section {
  margin-bottom: 16px;
}

.guidance-section:last-child {
  margin-bottom: 0;
}

.guidance-section h4 {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.guidance-section ul {
  margin: 0;
  padding-left: 20px;
}

.guidance-section li {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 4px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .queries-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .entity-selector-bar {
    flex-direction: column;
    align-items: flex-start;
  }

  .queries-summary {
    grid-template-columns: 1fr;
  }
}
