"""
ThreatFox Integration for SIEMLess v2.0
Connects to abuse.ch ThreatFox API for malware IOCs
"""

import aiohttp
import asyncio
import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class ThreatFoxConnector:
    """
    Connects to ThreatFox (abuse.ch) to retrieve malware IOCs
    Free threat intelligence feed focused on malware indicators
    """

    def __init__(self, api_key: str = None):
        """Initialize ThreatFox connector"""
        self.api_key = api_key or os.getenv('THREATFOX_AUTH_KEY', '')
        self.base_url = "https://threatfox-api.abuse.ch/api/v1/"

        # Headers for ThreatFox API - requires Auth-Key header
        self.headers = {
            'Auth-Key': self.api_key,  # Correct header name for ThreatFox
            'Content-Type': 'application/json'
        }

    async def test_connection(self) -> bool:
        """Test connection to ThreatFox API"""
        try:
            # Create SSL context that doesn't verify certificates (for testing)
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context)) as session:
                # Test with a simple query
                data = {
                    "query": "get_iocs",
                    "days": 1  # Get IOCs from last 24 hours
                }

                async with session.post(
                    self.base_url,
                    json=data,
                    headers=self.headers if self.api_key else {},
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get('query_status') == 'ok':
                            logger.info("ThreatFox connection successful")
                            return True

                    logger.warning(f"ThreatFox connection failed with status {response.status}")
                    return False

        except Exception as e:
            logger.error(f"Failed to connect to ThreatFox: {e}")
            return False

    async def get_recent_iocs(self, days: int = 1, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get recent IOCs from ThreatFox

        Args:
            days: Number of days to look back (max 7 for free API)
            limit: Maximum number of IOCs to return

        Returns:
            List of normalized IOC dictionaries
        """
        try:
            # Create SSL context that doesn't verify certificates
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context)) as session:
                data = {
                    "query": "get_iocs",
                    "days": min(days, 7)  # Free API limits to 7 days
                }

                async with session.post(
                    self.base_url,
                    json=data,
                    headers=self.headers if self.api_key else {},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()

                        if result.get('query_status') == 'ok':
                            iocs = result.get('data', [])
                            logger.info(f"Retrieved {len(iocs)} IOCs from ThreatFox")

                            # Normalize and limit IOCs
                            normalized = []
                            for ioc in iocs[:limit]:
                                normalized.append(self._normalize_ioc(ioc))

                            return normalized
                        else:
                            logger.warning(f"ThreatFox query failed: {result.get('query_status')}")
                            return []
                    else:
                        logger.error(f"ThreatFox API error: HTTP {response.status}")
                        return []

        except Exception as e:
            logger.error(f"Error fetching ThreatFox IOCs: {e}")
            return []

    async def get_malware_iocs(self, malware_family: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get IOCs for specific malware family

        Args:
            malware_family: Name of malware family (e.g., 'Cobalt Strike', 'Emotet')
            limit: Maximum number of IOCs to return

        Returns:
            List of normalized IOC dictionaries
        """
        try:
            # Create SSL context that doesn't verify certificates
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context)) as session:
                if malware_family:
                    data = {
                        "query": "malwareinfo",
                        "malware": malware_family,
                        "limit": limit
                    }
                else:
                    # Get recent malware samples if no family specified
                    data = {
                        "query": "get_iocs",
                        "days": 1
                    }

                async with session.post(
                    self.base_url,
                    json=data,
                    headers=self.headers if self.api_key else {},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()

                        if result.get('query_status') == 'ok':
                            iocs = result.get('data', [])

                            # Normalize IOCs
                            normalized = []
                            for ioc in iocs[:limit]:
                                normalized.append(self._normalize_ioc(ioc))

                            logger.info(f"Retrieved {len(normalized)} malware IOCs from ThreatFox")
                            return normalized

                    return []

        except Exception as e:
            logger.error(f"Error fetching malware IOCs: {e}")
            return []

    async def search_ioc(self, ioc_value: str) -> Dict[str, Any]:
        """
        Search for specific IOC in ThreatFox database

        Args:
            ioc_value: The IOC value to search for (hash, domain, IP, etc.)

        Returns:
            IOC details if found, empty dict otherwise
        """
        try:
            # Create SSL context that doesn't verify certificates
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context)) as session:
                data = {
                    "query": "search_ioc",
                    "search_term": ioc_value
                }

                async with session.post(
                    self.base_url,
                    json=data,
                    headers=self.headers if self.api_key else {},
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        result = await response.json()

                        if result.get('query_status') == 'ok' and result.get('data'):
                            ioc_data = result['data'][0] if isinstance(result['data'], list) else result['data']
                            return self._normalize_ioc(ioc_data)

                    return {}

        except Exception as e:
            logger.error(f"Error searching IOC: {e}")
            return {}

    def _normalize_ioc(self, ioc_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalize ThreatFox IOC to common format

        Args:
            ioc_data: Raw IOC data from ThreatFox

        Returns:
            Normalized IOC dictionary
        """
        # Determine IOC type
        ioc_value = ioc_data.get('ioc', '')
        ioc_type = ioc_data.get('ioc_type', 'unknown')

        # Map ThreatFox types to standard types
        type_mapping = {
            'md5_hash': 'hash',
            'sha256_hash': 'hash',
            'sha1_hash': 'hash',
            'domain': 'domain',
            'ip:port': 'ip',
            'url': 'url'
        }

        standard_type = type_mapping.get(ioc_type, ioc_type)

        # Extract labels/tags
        labels = []
        if ioc_data.get('malware'):
            labels.append(ioc_data['malware'])
        if ioc_data.get('malware_printable'):
            labels.append(ioc_data['malware_printable'])
        if ioc_data.get('tags'):
            labels.extend(ioc_data['tags'])

        # Build STIX-like pattern
        pattern = self._build_stix_pattern(ioc_value, standard_type, ioc_type)

        return {
            'id': str(ioc_data.get('id', '')),
            'name': f"{ioc_data.get('malware_printable', 'ThreatFox')} - {ioc_value[:50]}",
            'pattern': pattern,
            'pattern_type': 'stix',
            'value': ioc_value,
            'type': standard_type,
            'confidence': self._calculate_confidence(ioc_data),
            'labels': labels,
            'source': 'threatfox',
            'threat_type': ioc_data.get('threat_type', 'unknown'),
            'malware_family': ioc_data.get('malware_printable', ''),
            'first_seen': ioc_data.get('first_seen_utc', ''),
            'last_seen': ioc_data.get('last_seen_utc', ''),
            'reference': ioc_data.get('reference', ''),
            'reporter': ioc_data.get('reporter', ''),
            'created_at': datetime.utcnow().isoformat()
        }

    def _build_stix_pattern(self, value: str, ioc_type: str, original_type: str) -> str:
        """
        Build STIX pattern from IOC value and type

        Args:
            value: The IOC value
            ioc_type: Normalized IOC type
            original_type: Original ThreatFox IOC type

        Returns:
            STIX pattern string
        """
        if ioc_type == 'hash':
            if 'md5' in original_type.lower():
                return f"[file:hashes.'MD5' = '{value}']"
            elif 'sha256' in original_type.lower():
                return f"[file:hashes.'SHA-256' = '{value}']"
            elif 'sha1' in original_type.lower():
                return f"[file:hashes.'SHA-1' = '{value}']"
            else:
                return f"[file:hashes = '{value}']"

        elif ioc_type == 'domain':
            return f"[domain-name:value = '{value}']"

        elif ioc_type == 'ip':
            # Handle IP:port format
            if ':' in value:
                ip, port = value.split(':', 1)
                return f"[network-traffic:dst_ref.value = '{ip}' AND network-traffic:dst_port = {port}]"
            else:
                return f"[ipv4-addr:value = '{value}']"

        elif ioc_type == 'url':
            return f"[url:value = '{value}']"

        else:
            # Generic pattern
            return f"[{ioc_type}:value = '{value}']"

    def _calculate_confidence(self, ioc_data: Dict[str, Any]) -> int:
        """
        Calculate confidence score for IOC

        Args:
            ioc_data: Raw IOC data from ThreatFox

        Returns:
            Confidence score (0-100)
        """
        confidence = 50  # Base confidence

        # Increase confidence based on various factors
        if ioc_data.get('confidence_level'):
            conf_map = {'high': 90, 'medium': 70, 'low': 30}
            confidence = conf_map.get(ioc_data['confidence_level'], 50)

        # Boost for verified reporters
        if ioc_data.get('reporter') and '@' in ioc_data.get('reporter', ''):
            confidence += 10

        # Boost for recent IOCs
        if ioc_data.get('first_seen_utc'):
            try:
                first_seen = datetime.fromisoformat(ioc_data['first_seen_utc'].replace('Z', '+00:00'))
                days_old = (datetime.utcnow() - first_seen.replace(tzinfo=None)).days
                if days_old < 7:
                    confidence += 10
                elif days_old < 30:
                    confidence += 5
            except:
                pass

        # Boost for known malware families
        known_families = ['cobalt strike', 'emotet', 'trickbot', 'qakbot', 'lockbit']
        if any(family in ioc_data.get('malware_printable', '').lower() for family in known_families):
            confidence += 10

        return min(confidence, 100)  # Cap at 100

