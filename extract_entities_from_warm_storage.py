"""
Extract and persist entities from warm_storage processed logs

This script extracts the 118,911 entities that were trapped in warm_storage
and inserts them into the entities table.
"""

import psycopg2
import json
from uuid import uuid4
from datetime import datetime

# Database connection
conn = psycopg2.connect(
    host="localhost",
    port=5433,
    database="siemless_v2",
    user="siemless",
    password="siemless123"
)

cursor = conn.cursor()

print("Fetching processed logs with entities...")

# Get all processed logs with entities
cursor.execute("""
    SELECT
        storage_id,
        data->'content'->'entities' as entities,
        created_at
    FROM warm_storage
    WHERE data->>'type' = 'processed_log'
    AND data->'content'->'entities' IS NOT NULL
    AND jsonb_array_length(data->'content'->'entities') > 0
    ORDER BY created_at
""")

rows = cursor.fetchall()
print(f"Found {len(rows)} logs with entities")

total_entities = 0
inserted_entities = 0
duplicate_entities = 0

for storage_id, entities_json, log_created_at in rows:
    if not entities_json:
        continue

    entities = json.loads(entities_json) if isinstance(entities_json, str) else entities_json

    for entity in entities:
        total_entities += 1

        entity_type = entity.get('type')
        entity_value = entity.get('value')

        if not entity_type or not entity_value:
            continue

        # Check if entity already exists
        cursor.execute("""
            SELECT entity_id FROM entities
            WHERE entity_type = %s AND entity_value = %s
        """, (entity_type, entity_value))

        existing = cursor.fetchone()

        if existing:
            duplicate_entities += 1
            continue

        # Insert entity
        entity_id = str(uuid4())
        cursor.execute("""
            INSERT INTO entities (
                entity_id, entity_type, entity_value,
                first_seen, last_seen, event_count,
                risk_score, tags, created_at
            ) VALUES (
                %s, %s, %s, %s, %s, 1, 0, ARRAY[]::text[], %s
            )
        """, (
            entity_id,
            entity_type,
            entity_value,
            log_created_at,
            log_created_at,
            log_created_at
        ))

        inserted_entities += 1

        if inserted_entities % 1000 == 0:
            conn.commit()
            print(f"Progress: {inserted_entities} inserted, {duplicate_entities} duplicates, {total_entities} processed")

# Final commit
conn.commit()

print("\n" + "="*60)
print("ENTITY EXTRACTION COMPLETE")
print("="*60)
print(f"Total entities found:     {total_entities}")
print(f"New entities inserted:    {inserted_entities}")
print(f"Duplicate entities:       {duplicate_entities}")
print(f"Final entity count:       {inserted_entities + duplicate_entities}")
print("="*60)

cursor.close()
conn.close()
