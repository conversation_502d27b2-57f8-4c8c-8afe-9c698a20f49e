/**
 * Detection Fidelity API Service
 * Handles detection fidelity and coverage API calls
 */

import apiClient from '../client'
import type {
  DetectionFidelity,
  TechniqueCoverage,
  APIResponse
} from '../../types/api'

export const detectionService = {
  /**
   * Get current detection fidelity assessment
   */
  async getFidelity(): Promise<DetectionFidelity> {
    const response = await apiClient.post<APIResponse<DetectionFidelity>>(
      '/detection/fidelity',
      {}
    )
    return response.data.data
  },

  /**
   * Get overall detection coverage
   */
  async getCoverage(): Promise<DetectionFidelity> {
    const response = await apiClient.post<APIResponse<DetectionFidelity>>(
      '/detection/coverage',
      {}
    )
    return response.data.data
  },

  /**
   * Get MITRE technique coverage details
   */
  async getTechniqueCoverage(): Promise<TechniqueCoverage[]> {
    const response = await apiClient.post<APIResponse<TechniqueCoverage[]>>(
      '/detection/technique-coverage',
      {}
    )
    return response.data.data
  }
}
