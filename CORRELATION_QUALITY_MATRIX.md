# Correlation Quality Matrix - Detection Fidelity Framework

## Executive Summary

This document defines how log source quality and diversity directly impact correlation effectiveness and detection fidelity. It provides a quantitative framework for assessing detection capabilities based on available log sources.

---

## Detection Fidelity Scoring System

### Core Formula

```python
def calculate_detection_fidelity(attack_scenario, available_sources):
    """
    Calculate detection fidelity score (0-100) for a specific attack scenario
    based on available log sources
    """

    # Base score from primary detection source
    primary_score = get_primary_source_score(attack_scenario, available_sources)

    # Correlation multiplier from complementary sources
    correlation_multiplier = calculate_correlation_multiplier(attack_scenario, available_sources)

    # Coverage completeness factor
    coverage_factor = calculate_coverage_factor(attack_scenario, available_sources)

    # Time-to-detection factor
    speed_factor = calculate_speed_factor(available_sources)

    # Final fidelity score
    fidelity_score = (primary_score * correlation_multiplier * coverage_factor * speed_factor)

    return min(fidelity_score, 100)  # Cap at 100
```

---

## Attack Scenario Detection Requirements

### 1. Ransomware Attack

```python
RANSOMWARE_DETECTION = {
    'detection_stages': [
        {
            'stage': 'Initial Access',
            'required_visibility': ['Email gateway', 'Web proxy', 'Endpoint'],
            'confidence_contribution': 15
        },
        {
            'stage': 'Execution',
            'required_visibility': ['Process creation', 'Script execution'],
            'confidence_contribution': 20
        },
        {
            'stage': 'Defense Evasion',
            'required_visibility': ['Process injection', 'Service creation'],
            'confidence_contribution': 15
        },
        {
            'stage': 'Discovery',
            'required_visibility': ['File system enumeration', 'Network shares'],
            'confidence_contribution': 10
        },
        {
            'stage': 'Encryption',
            'required_visibility': ['Mass file modification', 'Process I/O'],
            'confidence_contribution': 30
        },
        {
            'stage': 'C2 Communication',
            'required_visibility': ['Network connections', 'DNS queries'],
            'confidence_contribution': 10
        }
    ],

    'log_source_effectiveness': {
        'CrowdStrike': {
            'stages_covered': ['all'],
            'fidelity': 95,
            'latency': 'real-time'
        },
        'Windows Events + Sysmon': {
            'stages_covered': ['Execution', 'Discovery', 'Encryption'],
            'fidelity': 70,
            'latency': '1-5 minutes'
        },
        'Network IDS': {
            'stages_covered': ['C2 Communication'],
            'fidelity': 60,
            'latency': 'real-time'
        },
        'File Integrity Monitoring': {
            'stages_covered': ['Encryption'],
            'fidelity': 80,
            'latency': '5-15 minutes'
        }
    },

    'correlation_requirements': {
        'minimum_sources': 2,
        'optimal_sources': 4,
        'critical_correlations': [
            'Process → File modifications',
            'Network → Known ransomware C2',
            'Identity → Unusual access patterns'
        ]
    }
}
```

### 2. Insider Threat

```python
INSIDER_THREAT_DETECTION = {
    'behavioral_indicators': [
        {
            'indicator': 'After-hours access',
            'sources_required': ['VPN logs', 'AD authentication'],
            'weight': 15
        },
        {
            'indicator': 'Bulk data access',
            'sources_required': ['Database audit', 'File access logs'],
            'weight': 25
        },
        {
            'indicator': 'Unauthorized application use',
            'sources_required': ['Endpoint monitoring', 'Proxy logs'],
            'weight': 20
        },
        {
            'indicator': 'Data staging',
            'sources_required': ['Endpoint monitoring', 'DLP'],
            'weight': 20
        },
        {
            'indicator': 'Exfiltration attempt',
            'sources_required': ['Network monitoring', 'Cloud storage logs', 'Email logs'],
            'weight': 20
        }
    ],

    'baseline_requirements': {
        'user_behavior_profile': True,
        'minimum_history_days': 30,
        'peer_group_analysis': True
    },

    'detection_confidence_by_sources': {
        1: 20,   # Single source
        2: 45,   # Two sources
        3: 70,   # Three sources
        4: 85,   # Four sources
        5: 95    # Five+ sources
    }
}
```

### 3. Advanced Persistent Threat (APT)

```python
APT_DETECTION = {
    'kill_chain_visibility': {
        'reconnaissance': {
            'sources': ['DNS logs', 'Web logs', 'Network flow'],
            'importance': 'MEDIUM'
        },
        'weaponization': {
            'sources': ['Email gateway', 'Sandbox analysis'],
            'importance': 'HIGH'
        },
        'delivery': {
            'sources': ['Email logs', 'Web proxy', 'Endpoint'],
            'importance': 'HIGH'
        },
        'exploitation': {
            'sources': ['Endpoint EDR', 'IDS/IPS', 'Application logs'],
            'importance': 'CRITICAL'
        },
        'installation': {
            'sources': ['Endpoint EDR', 'Registry monitoring', 'File integrity'],
            'importance': 'CRITICAL'
        },
        'command_control': {
            'sources': ['Network monitoring', 'DNS logs', 'Proxy logs'],
            'importance': 'CRITICAL'
        },
        'actions_on_objectives': {
            'sources': ['Data access logs', 'Identity logs', 'Network monitoring'],
            'importance': 'CRITICAL'
        }
    },

    'minimum_detection_capability': {
        'endpoint_visibility': 'GOLD',
        'network_visibility': 'GOLD',
        'identity_visibility': 'PLATINUM',
        'cloud_visibility': 'SILVER'
    }
}
```

---

## Log Source Correlation Synergies

### Synergy Matrix

```python
SYNERGY_MATRIX = {
    'endpoint_plus_network': {
        'sources': ['EDR', 'Network IDS'],
        'synergy_multiplier': 1.5,
        'use_cases': [
            'C2 detection',
            'Lateral movement',
            'Data exfiltration'
        ]
    },

    'identity_plus_endpoint': {
        'sources': ['AD/Azure AD', 'EDR'],
        'synergy_multiplier': 1.6,
        'use_cases': [
            'Credential theft',
            'Privilege escalation',
            'Account compromise'
        ]
    },

    'network_plus_identity': {
        'sources': ['Firewall/Proxy', 'AD/Azure AD'],
        'synergy_multiplier': 1.4,
        'use_cases': [
            'Lateral movement',
            'Unauthorized access',
            'Data theft'
        ]
    },

    'full_stack': {
        'sources': ['EDR', 'Network', 'Identity', 'Cloud'],
        'synergy_multiplier': 2.0,
        'use_cases': [
            'Complete kill chain visibility',
            'APT detection',
            'Unknown threat detection'
        ]
    }
}
```

### Correlation Value Calculator

```python
def calculate_correlation_value(sources, attack_type):
    """
    Calculate the correlation value based on available sources
    """
    base_value = 0
    synergies_applied = []

    # Calculate base value from individual sources
    for source in sources:
        source_value = SOURCE_VALUES[source][attack_type]
        base_value += source_value

    # Apply synergy bonuses
    for synergy_name, synergy_config in SYNERGY_MATRIX.items():
        required_sources = synergy_config['sources']
        if all(s in sources for s in required_sources):
            base_value *= synergy_config['synergy_multiplier']
            synergies_applied.append(synergy_name)

    # Apply diminishing returns for redundant sources
    redundancy_factor = calculate_redundancy_factor(sources)
    base_value *= redundancy_factor

    return {
        'correlation_value': min(base_value, 100),
        'synergies': synergies_applied,
        'confidence': calculate_confidence(base_value)
    }
```

---

## Quality-Based Correlation Scenarios

### Scenario 1: High-Quality Limited Sources

```python
HIGH_QUALITY_LIMITED = {
    'available_sources': [
        {'name': 'CrowdStrike', 'tier': 'PLATINUM', 'coverage': 'Endpoint'},
        {'name': 'Palo Alto', 'tier': 'GOLD', 'coverage': 'Network'}
    ],

    'detection_capabilities': {
        'ransomware': 88,
        'lateral_movement': 75,
        'data_exfiltration': 70,
        'insider_threat': 60,
        'apt': 72
    },

    'gaps': [
        'Limited identity visibility',
        'No email security',
        'Minimal cloud visibility'
    ],

    'recommendation': 'Add identity source (AD/Azure AD) for 15-20% improvement'
}
```

### Scenario 2: Comprehensive Lower-Quality Sources

```python
COMPREHENSIVE_LOWER_QUALITY = {
    'available_sources': [
        {'name': 'Windows Events', 'tier': 'SILVER'},
        {'name': 'Basic Firewall', 'tier': 'BRONZE'},
        {'name': 'Syslog', 'tier': 'BRONZE'},
        {'name': 'Basic AD Logs', 'tier': 'SILVER'},
        {'name': 'Web Server Logs', 'tier': 'SILVER'}
    ],

    'detection_capabilities': {
        'ransomware': 45,
        'lateral_movement': 50,
        'data_exfiltration': 40,
        'insider_threat': 35,
        'apt': 30
    },

    'gaps': [
        'No process-level visibility',
        'Limited behavioral detection',
        'High false positive rate',
        'Slow detection times'
    ],

    'recommendation': 'Upgrade to EDR for 40-50% improvement'
}
```

### Scenario 3: Optimal Mixed Sources

```python
OPTIMAL_MIXED = {
    'available_sources': [
        {'name': 'SentinelOne', 'tier': 'PLATINUM', 'coverage': 'Endpoint'},
        {'name': 'Zeek', 'tier': 'GOLD', 'coverage': 'Network'},
        {'name': 'Active Directory', 'tier': 'PLATINUM', 'coverage': 'Identity'},
        {'name': 'AWS CloudTrail', 'tier': 'GOLD', 'coverage': 'Cloud'},
        {'name': 'Office 365', 'tier': 'GOLD', 'coverage': 'Email/Collaboration'}
    ],

    'detection_capabilities': {
        'ransomware': 95,
        'lateral_movement': 92,
        'data_exfiltration': 90,
        'insider_threat': 88,
        'apt': 93
    },

    'strengths': [
        'Complete kill chain visibility',
        'Multiple correlation points',
        'Fast detection times',
        'Low false positive rate'
    ]
}
```

---

## Time-Based Correlation Impact

### Detection Latency by Source Quality

```python
DETECTION_LATENCY = {
    'real_time': {
        'sources': ['EDR (Platinum)', 'Network IDS', 'DLP'],
        'detection_time': '< 1 minute',
        'correlation_window': '5 minutes',
        'fidelity_impact': 1.0
    },

    'near_real_time': {
        'sources': ['EDR (Gold)', 'Firewall logs', 'SIEM'],
        'detection_time': '1-5 minutes',
        'correlation_window': '15 minutes',
        'fidelity_impact': 0.9
    },

    'delayed': {
        'sources': ['Windows Events', 'Scheduled log collection'],
        'detection_time': '5-30 minutes',
        'correlation_window': '1 hour',
        'fidelity_impact': 0.7
    },

    'batch': {
        'sources': ['Daily reports', 'Backup logs'],
        'detection_time': '> 1 hour',
        'correlation_window': '24 hours',
        'fidelity_impact': 0.4
    }
}
```

---

## Practical Detection Examples

### Example 1: Detecting Lateral Movement

```python
def detect_lateral_movement(available_logs):
    """
    Calculate ability to detect lateral movement based on available logs
    """
    detection_score = 0
    detected_techniques = []
    missed_techniques = []

    # Check RDP detection capability
    if has_log_type(available_logs, 'Windows Security Events'):
        if '4624' in available_logs['windows']['event_ids']:
            detection_score += 20
            detected_techniques.append('RDP authentication')
    else:
        missed_techniques.append('RDP authentication')

    # Check network movement
    if has_log_type(available_logs, 'Network Flow'):
        detection_score += 25
        detected_techniques.append('Unusual internal connections')
    else:
        missed_techniques.append('Network patterns')

    # Check process execution
    if has_log_type(available_logs, 'EDR') or has_log_type(available_logs, 'Sysmon'):
        detection_score += 30
        detected_techniques.append('Remote process execution')
    else:
        missed_techniques.append('Process execution')

    # Check credential usage
    if has_log_type(available_logs, 'AD Authentication'):
        detection_score += 25
        detected_techniques.append('Pass-the-hash/ticket')
    else:
        missed_techniques.append('Credential attacks')

    return {
        'detection_fidelity': detection_score,
        'detected': detected_techniques,
        'missed': missed_techniques,
        'recommendation': generate_improvement_recommendation(missed_techniques)
    }
```

### Example 2: Correlating Multi-Stage Attack

```python
class MultiStageCorrelator:
    def __init__(self, log_sources):
        self.sources = log_sources
        self.correlation_window = 4 * 3600  # 4 hours
        self.confidence_threshold = 70

    def correlate_attack_chain(self, events):
        """
        Correlate events across multiple sources to detect attack chain
        """
        chain = {
            'stages_detected': [],
            'confidence': 0,
            'evidence': [],
            'gaps': []
        }

        # Stage 1: Initial Access
        initial_access = self.detect_initial_access(events)
        if initial_access['confidence'] > 50:
            chain['stages_detected'].append('initial_access')
            chain['evidence'].append(initial_access['evidence'])

        # Stage 2: Execution
        if 'EDR' in self.sources or 'Sysmon' in self.sources:
            execution = self.detect_execution(events)
            if execution['confidence'] > 60:
                chain['stages_detected'].append('execution')
                chain['evidence'].append(execution['evidence'])
        else:
            chain['gaps'].append('No execution visibility')

        # Stage 3: Persistence
        if 'EDR' in self.sources:
            persistence = self.detect_persistence(events)
            if persistence['confidence'] > 70:
                chain['stages_detected'].append('persistence')
                chain['evidence'].append(persistence['evidence'])

        # Calculate overall confidence
        chain['confidence'] = self.calculate_chain_confidence(chain)

        return chain

    def calculate_chain_confidence(self, chain):
        """
        Calculate confidence based on stages detected and source quality
        """
        stage_count = len(chain['stages_detected'])
        source_quality = self.assess_source_quality()

        # Base confidence from stage detection
        base_confidence = min(stage_count * 20, 80)

        # Adjust for source quality
        quality_multiplier = source_quality / 100

        # Adjust for evidence correlation
        correlation_bonus = len(chain['evidence']) * 5

        return min(base_confidence * quality_multiplier + correlation_bonus, 98)
```

---

## Correlation Quality Metrics

### Key Performance Indicators

```python
CORRELATION_KPIS = {
    'coverage': {
        'metric': 'Percentage of MITRE techniques detectable',
        'calculation': 'detected_techniques / total_techniques',
        'target': {
            'basic': 30,
            'intermediate': 60,
            'advanced': 85
        }
    },

    'fidelity': {
        'metric': 'True positive rate for detections',
        'calculation': 'true_positives / (true_positives + false_positives)',
        'target': {
            'basic': 40,
            'intermediate': 70,
            'advanced': 90
        }
    },

    'correlation_depth': {
        'metric': 'Average number of sources per detection',
        'calculation': 'total_sources_used / total_detections',
        'target': {
            'basic': 1.5,
            'intermediate': 2.5,
            'advanced': 4.0
        }
    },

    'time_to_correlation': {
        'metric': 'Time from first event to correlated detection',
        'calculation': 'correlation_time - first_event_time',
        'target': {
            'basic': '30 minutes',
            'intermediate': '10 minutes',
            'advanced': '2 minutes'
        }
    }
}
```

---

## Improvement Roadmap

### Progressive Enhancement Strategy

```python
ENHANCEMENT_ROADMAP = {
    'phase_1': {
        'name': 'Foundation',
        'duration': '1-2 months',
        'actions': [
            'Enable Windows Security auditing',
            'Deploy Sysmon with basic config',
            'Centralize firewall logs',
            'Enable AD auditing'
        ],
        'expected_improvement': '+25% detection capability'
    },

    'phase_2': {
        'name': 'Visibility',
        'duration': '3-4 months',
        'actions': [
            'Deploy EDR solution',
            'Add network visibility (IDS/Zeek)',
            'Integrate cloud logs',
            'Enable PowerShell logging'
        ],
        'expected_improvement': '+40% detection capability'
    },

    'phase_3': {
        'name': 'Correlation',
        'duration': '5-6 months',
        'actions': [
            'Build correlation rules',
            'Create behavioral baselines',
            'Implement threat hunting',
            'Add deception technology'
        ],
        'expected_improvement': '+20% detection capability'
    },

    'phase_4': {
        'name': 'Optimization',
        'duration': 'Ongoing',
        'actions': [
            'Tune detection rules',
            'Reduce false positives',
            'Automate response',
            'Continuous improvement'
        ],
        'expected_improvement': '+10% detection capability'
    }
}
```

---

## ROI Analysis

### Cost vs Detection Capability

```python
ROI_ANALYSIS = {
    'investment_levels': [
        {
            'level': 'Basic',
            'annual_cost': '$50,000',
            'detection_capability': 35,
            'attacks_prevented': 3,
            'savings': '$150,000',
            'roi': '200%'
        },
        {
            'level': 'Intermediate',
            'annual_cost': '$200,000',
            'detection_capability': 70,
            'attacks_prevented': 8,
            'savings': '$600,000',
            'roi': '200%'
        },
        {
            'level': 'Advanced',
            'annual_cost': '$500,000',
            'detection_capability': 90,
            'attacks_prevented': 15,
            'savings': '$2,000,000',
            'roi': '300%'
        }
    ],

    'break_even_analysis': {
        'prevented_ransomware': 1,  # One prevented attack pays for advanced logging
        'prevented_data_breach': 0.5,  # Half a breach pays for everything
        'compliance_fines_avoided': 2  # Two avoided fines cover costs
    }
}
```

---

## Conclusion

The Correlation Quality Matrix demonstrates that:

1. **Source Quality Matters More Than Quantity**: A few high-quality sources outperform many poor sources
2. **Synergy Is Critical**: Combining complementary sources multiplies detection capability
3. **Coverage Gaps Are Exploitable**: Attackers will find and use blind spots
4. **Time Is A Factor**: Real-time correlation dramatically improves detection
5. **Investment Pays Off**: Proper logging infrastructure has clear ROI

Organizations should focus on:
- Getting the best EDR they can afford
- Ensuring identity visibility (AD/Azure AD)
- Adding network context (firewall minimum, IDS preferred)
- Maintaining proper retention for correlation windows
- Continuously improving based on missed detections