"""
Parser Hot Reload Module
Enables dynamic loading and updating of parsers without service restart
"""

import json
import asyncio
import importlib
import sys
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
import hashlib
import inspect
from pathlib import Path
import logging

class ParserHotReload:
    """Manage dynamic parser loading and updates"""

    def __init__(self, redis_client, postgres_conn, logger):
        self.redis = redis_client
        self.db = postgres_conn
        self.logger = logger

        # Active parsers cache
        self.active_parsers: Dict[str, Dict[str, Any]] = {}

        # Parser performance metrics
        self.parser_metrics: Dict[str, Dict[str, Any]] = {}

        # Parser update queue
        self.update_queue = asyncio.Queue()

        # Reload lock for thread safety
        self.reload_lock = asyncio.Lock()

    async def initialize(self):
        """Initialize hot reload system"""
        try:
            # Load existing parsers from database
            await self._load_parsers_from_db()

            # Start background reload monitor
            asyncio.create_task(self._monitor_parser_updates())

            # Subscribe to parser update events
            # Note: Using sync pubsub for now, will convert to async when redis.asyncio is setup
            # asyncio.create_task(self._handle_update_events())

            self.logger.info(f"Parser hot reload initialized with {len(self.active_parsers)} parsers")

        except Exception as e:
            self.logger.error(f"Failed to initialize hot reload: {e}")
            raise

    async def _load_parsers_from_db(self):
        """Load all active parsers from database"""
        try:
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT pattern_id, pattern_type, pattern_data
                FROM pattern_library
                WHERE is_active = TRUE
                AND pattern_type IN ('log_parser', 'entity_extractor')
            """)

            for row in cursor.fetchall():
                pattern_id, pattern_type, pattern_data = row
                await self._load_parser(pattern_id, pattern_type, pattern_data)

        except Exception as e:
            self.logger.error(f"Failed to load parsers from database: {e}")

    async def _load_parser(self, parser_id: str, parser_type: str,
                          parser_data: Dict) -> bool:
        """Load a single parser into memory"""
        try:
            async with self.reload_lock:
                # Create parser instance based on type
                if parser_type == 'log_parser':
                    parser = self._create_log_parser(parser_id, parser_data)
                elif parser_type == 'entity_extractor':
                    parser = self._create_entity_extractor(parser_id, parser_data)
                else:
                    self.logger.warning(f"Unknown parser type: {parser_type}")
                    return False

                # Store in active parsers
                self.active_parsers[parser_id] = {
                    'type': parser_type,
                    'parser': parser,
                    'data': parser_data,
                    'loaded_at': datetime.now(),
                    'version': self._get_parser_version(parser_data),
                    'usage_count': 0,
                    'success_count': 0,
                    'error_count': 0
                }

                # Initialize metrics
                self.parser_metrics[parser_id] = {
                    'total_processing_time': 0,
                    'avg_processing_time': 0,
                    'last_used': None,
                    'error_rate': 0
                }

                self.logger.info(f"Loaded parser: {parser_id} (type: {parser_type})")
                return True

        except Exception as e:
            self.logger.error(f"Failed to load parser {parser_id}: {e}")
            return False

    def _create_log_parser(self, parser_id: str, parser_data: Dict) -> Callable:
        """Create a log parser function from configuration"""
        import re

        regex_pattern = parser_data.get('regex', '')
        keywords = parser_data.get('keywords', [])
        field_mappings = parser_data.get('field_mappings', {})

        def parse_log(log_entry: str) -> Optional[Dict]:
            """Dynamic log parser function"""
            try:
                # Check keywords first for quick filtering
                if keywords:
                    if not any(keyword.lower() in log_entry.lower() for keyword in keywords):
                        return None

                # Apply regex pattern
                if regex_pattern:
                    match = re.search(regex_pattern, log_entry, re.IGNORECASE)
                    if match:
                        parsed = match.groupdict() if match.groupdict() else {}

                        # Apply field mappings
                        if field_mappings:
                            mapped = {}
                            for source_field, target_field in field_mappings.items():
                                if source_field in parsed:
                                    mapped[target_field] = parsed[source_field]
                            return mapped

                        return parsed

                return None

            except Exception as e:
                logging.error(f"Parser {parser_id} error: {e}")
                return None

        return parse_log

    def _create_entity_extractor(self, parser_id: str, parser_data: Dict) -> Callable:
        """Create an entity extractor function from configuration"""
        import re

        entity_extractors = parser_data.get('entity_extractors', {})

        # Precompile regex patterns
        ip_pattern = re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b')
        email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        url_pattern = re.compile(r'https?://[^\s]+')
        hash_md5_pattern = re.compile(r'\b[a-f0-9]{32}\b', re.IGNORECASE)
        hash_sha256_pattern = re.compile(r'\b[a-f0-9]{64}\b', re.IGNORECASE)

        def extract_entities(data: Dict) -> Dict[str, List]:
            """Dynamic entity extractor function"""
            entities = {
                'ips': [],
                'hosts': [],
                'users': [],
                'emails': [],
                'urls': [],
                'hashes': [],
                'ports': [],
                'files': []
            }

            try:
                # Extract from specified fields
                for entity_type, field_paths in entity_extractors.items():
                    if entity_type == 'ip_fields':
                        for field_path in field_paths:
                            value = self._get_nested_value(data, field_path)
                            if value:
                                # Extract IPs from value
                                if isinstance(value, str):
                                    ips = ip_pattern.findall(value)
                                    entities['ips'].extend(ips)
                                elif isinstance(value, (list, tuple)):
                                    entities['ips'].extend(value)

                    elif entity_type == 'host_fields':
                        for field_path in field_paths:
                            value = self._get_nested_value(data, field_path)
                            if value:
                                if isinstance(value, str):
                                    entities['hosts'].append(value)
                                elif isinstance(value, (list, tuple)):
                                    entities['hosts'].extend(value)

                    elif entity_type == 'user_fields':
                        for field_path in field_paths:
                            value = self._get_nested_value(data, field_path)
                            if value:
                                if isinstance(value, str):
                                    entities['users'].append(value)
                                    # Also check for emails
                                    emails = email_pattern.findall(value)
                                    entities['emails'].extend(emails)

                    elif entity_type == 'hash_fields':
                        for field_path in field_paths:
                            value = self._get_nested_value(data, field_path)
                            if value and isinstance(value, str):
                                # Check for MD5
                                md5_hashes = hash_md5_pattern.findall(value)
                                # Check for SHA256
                                sha256_hashes = hash_sha256_pattern.findall(value)
                                entities['hashes'].extend(md5_hashes + sha256_hashes)

                    elif entity_type == 'url_fields':
                        for field_path in field_paths:
                            value = self._get_nested_value(data, field_path)
                            if value and isinstance(value, str):
                                urls = url_pattern.findall(value)
                                entities['urls'].extend(urls)

                    elif entity_type == 'port_fields':
                        for field_path in field_paths:
                            value = self._get_nested_value(data, field_path)
                            if value:
                                if isinstance(value, (int, str)):
                                    try:
                                        port = int(value)
                                        if 0 < port < 65536:
                                            entities['ports'].append(port)
                                    except:
                                        pass

                # Remove duplicates
                for key in entities:
                    entities[key] = list(set(entities[key]))

                return entities

            except Exception as e:
                logging.error(f"Entity extractor {parser_id} error: {e}")
                return entities

        return extract_entities

    def _get_nested_value(self, data: Dict, field_path: str) -> Any:
        """Get value from nested dictionary using dot notation"""
        try:
            keys = field_path.split('.')
            value = data

            for key in keys:
                if isinstance(value, dict):
                    value = value.get(key)
                    if value is None:
                        return None
                else:
                    return None

            return value

        except:
            return None

    def _get_parser_version(self, parser_data: Dict) -> str:
        """Generate version hash for parser data"""
        content_hash = hashlib.md5(
            json.dumps(parser_data, sort_keys=True).encode()
        ).hexdigest()[:8]
        return content_hash

    async def _monitor_parser_updates(self):
        """Background task to monitor and apply parser updates"""
        while True:
            try:
                # Check for updates every 30 seconds
                await asyncio.sleep(30)

                # Check database for updated parsers
                cursor = self.db.cursor()
                cursor.execute("""
                    SELECT pattern_id, pattern_type, pattern_data, updated_at
                    FROM pattern_library
                    WHERE is_active = TRUE
                    AND pattern_type IN ('log_parser', 'entity_extractor')
                    AND updated_at > NOW() - INTERVAL '1 minute'
                """)

                for row in cursor.fetchall():
                    pattern_id, pattern_type, pattern_data, updated_at = row

                    # Check if parser needs update
                    if pattern_id in self.active_parsers:
                        current_version = self.active_parsers[pattern_id]['version']
                        new_version = self._get_parser_version(pattern_data)

                        if current_version != new_version:
                            await self.update_queue.put({
                                'parser_id': pattern_id,
                                'parser_type': pattern_type,
                                'parser_data': pattern_data
                            })
                    else:
                        # New parser
                        await self.update_queue.put({
                            'parser_id': pattern_id,
                            'parser_type': pattern_type,
                            'parser_data': pattern_data
                        })

                # Process update queue
                while not self.update_queue.empty():
                    update = await self.update_queue.get()
                    await self.reload_parser(
                        update['parser_id'],
                        update['parser_type'],
                        update['parser_data']
                    )

            except Exception as e:
                self.logger.error(f"Parser monitor error: {e}")

    async def _handle_update_events(self):
        """Handle parser update events from Redis"""
        try:
            # Create a separate sync Redis connection for pub/sub
            import redis
            sync_redis = redis.Redis(host='localhost', port=6380, decode_responses=True)
            pubsub = sync_redis.pubsub()
            pubsub.subscribe('ingestion.pattern_updated')

            # Poll for messages
            while True:
                message = pubsub.get_message(timeout=1.0)
                if message and message['type'] == 'message':
                    try:
                        event_data = json.loads(message['data'])

                        if event_data.get('type') in ['log_parser', 'entity_extractor']:
                            # Fetch updated parser from database
                            cursor = self.db.cursor()
                            cursor.execute("""
                                SELECT pattern_type, pattern_data
                                FROM pattern_library
                                WHERE pattern_id = %s AND is_active = TRUE
                            """, (event_data['pattern_id'],))

                            row = cursor.fetchone()
                            if row:
                                await self.reload_parser(
                                    event_data['pattern_id'],
                                    row[0],
                                    row[1]
                                )

                    except Exception as e:
                        self.logger.error(f"Failed to handle update event: {e}")

                await asyncio.sleep(0.1)  # Yield to other tasks

        except Exception as e:
            self.logger.error(f"Update event handler error: {e}")

    async def reload_parser(self, parser_id: str, parser_type: str,
                           parser_data: Dict) -> bool:
        """Hot reload a parser without service restart"""
        try:
            self.logger.info(f"Hot reloading parser: {parser_id}")

            # Store old parser for rollback
            old_parser = self.active_parsers.get(parser_id)

            # Load new parser
            success = await self._load_parser(parser_id, parser_type, parser_data)

            if success:
                # Preserve metrics
                if old_parser:
                    self.active_parsers[parser_id]['usage_count'] = old_parser['usage_count']
                    self.active_parsers[parser_id]['success_count'] = old_parser['success_count']
                    self.active_parsers[parser_id]['error_count'] = old_parser['error_count']

                # Publish reload event
                await self.redis.publish('ingestion.parser_reloaded', json.dumps({
                    'parser_id': parser_id,
                    'version': self._get_parser_version(parser_data),
                    'timestamp': datetime.now().isoformat()
                }))

                return True
            else:
                # Rollback to old parser if reload failed
                if old_parser:
                    self.active_parsers[parser_id] = old_parser
                return False

        except Exception as e:
            self.logger.error(f"Failed to reload parser {parser_id}: {e}")
            return False

    async def parse_log(self, log_entry: str, parser_hint: Optional[str] = None) -> Dict:
        """Parse a log entry using appropriate parser"""
        result = {
            'parsed': False,
            'parser_used': None,
            'data': {},
            'entities': {},
            'metadata': {}
        }

        try:
            # Try specific parser if hint provided
            if parser_hint and parser_hint in self.active_parsers:
                parser_info = self.active_parsers[parser_hint]
                if parser_info['type'] == 'log_parser':
                    parsed_data = parser_info['parser'](log_entry)
                    if parsed_data:
                        result['parsed'] = True
                        result['parser_used'] = parser_hint
                        result['data'] = parsed_data

                        # Update metrics
                        await self._update_parser_metrics(parser_hint, success=True)
                        return result

            # Try all parsers
            for parser_id, parser_info in self.active_parsers.items():
                if parser_info['type'] == 'log_parser':
                    try:
                        parsed_data = parser_info['parser'](log_entry)
                        if parsed_data:
                            result['parsed'] = True
                            result['parser_used'] = parser_id
                            result['data'] = parsed_data

                            # Update metrics
                            await self._update_parser_metrics(parser_id, success=True)

                            # Extract entities if extractors available
                            entities = await self.extract_entities(parsed_data)
                            if entities:
                                result['entities'] = entities

                            break
                    except Exception as e:
                        await self._update_parser_metrics(parser_id, success=False)
                        self.logger.debug(f"Parser {parser_id} failed: {e}")

        except Exception as e:
            self.logger.error(f"Log parsing error: {e}")

        return result

    async def extract_entities(self, data: Dict) -> Dict[str, List]:
        """Extract entities from parsed data using entity extractors"""
        all_entities = {
            'ips': [],
            'hosts': [],
            'users': [],
            'emails': [],
            'urls': [],
            'hashes': [],
            'ports': [],
            'files': []
        }

        try:
            # Run all entity extractors
            for parser_id, parser_info in self.active_parsers.items():
                if parser_info['type'] == 'entity_extractor':
                    try:
                        entities = parser_info['parser'](data)

                        # Merge entities
                        for entity_type, entity_list in entities.items():
                            if entity_type in all_entities:
                                all_entities[entity_type].extend(entity_list)

                        await self._update_parser_metrics(parser_id, success=True)

                    except Exception as e:
                        await self._update_parser_metrics(parser_id, success=False)
                        self.logger.debug(f"Entity extractor {parser_id} failed: {e}")

            # Remove duplicates
            for key in all_entities:
                all_entities[key] = list(set(all_entities[key]))

        except Exception as e:
            self.logger.error(f"Entity extraction error: {e}")

        return all_entities

    async def _update_parser_metrics(self, parser_id: str, success: bool,
                                    processing_time: float = 0):
        """Update parser performance metrics"""
        try:
            if parser_id in self.active_parsers:
                parser_info = self.active_parsers[parser_id]
                parser_info['usage_count'] += 1

                if success:
                    parser_info['success_count'] += 1
                else:
                    parser_info['error_count'] += 1

                # Update metrics
                metrics = self.parser_metrics[parser_id]
                metrics['last_used'] = datetime.now()

                if processing_time > 0:
                    metrics['total_processing_time'] += processing_time
                    metrics['avg_processing_time'] = (
                        metrics['total_processing_time'] / parser_info['usage_count']
                    )

                # Calculate error rate
                if parser_info['usage_count'] > 0:
                    metrics['error_rate'] = (
                        parser_info['error_count'] / parser_info['usage_count']
                    )

                # Persist metrics periodically (every 100 uses)
                if parser_info['usage_count'] % 100 == 0:
                    await self._persist_metrics(parser_id)

        except Exception as e:
            self.logger.error(f"Failed to update metrics for {parser_id}: {e}")

    async def _persist_metrics(self, parser_id: str):
        """Persist parser metrics to database"""
        try:
            if parser_id in self.active_parsers and parser_id in self.parser_metrics:
                parser_info = self.active_parsers[parser_id]
                metrics = self.parser_metrics[parser_id]

                cursor = self.db.cursor()
                cursor.execute("""
                    UPDATE pattern_library
                    SET usage_count = %s,
                        success_rate = %s,
                        avg_processing_time = %s,
                        updated_at = NOW()
                    WHERE pattern_id = %s
                """, (
                    parser_info['usage_count'],
                    1.0 - metrics['error_rate'],
                    metrics['avg_processing_time'],
                    parser_id
                ))

                self.db.commit()

        except Exception as e:
            self.logger.error(f"Failed to persist metrics for {parser_id}: {e}")
            self.db.rollback()

    async def get_parser_status(self) -> Dict[str, Any]:
        """Get status of all active parsers"""
        status = {
            'total_parsers': len(self.active_parsers),
            'log_parsers': 0,
            'entity_extractors': 0,
            'parsers': []
        }

        for parser_id, parser_info in self.active_parsers.items():
            if parser_info['type'] == 'log_parser':
                status['log_parsers'] += 1
            elif parser_info['type'] == 'entity_extractor':
                status['entity_extractors'] += 1

            metrics = self.parser_metrics.get(parser_id, {})

            status['parsers'].append({
                'id': parser_id,
                'type': parser_info['type'],
                'version': parser_info['version'],
                'loaded_at': parser_info['loaded_at'].isoformat(),
                'usage_count': parser_info['usage_count'],
                'success_rate': 1.0 - metrics.get('error_rate', 0),
                'avg_processing_time': metrics.get('avg_processing_time', 0),
                'last_used': metrics['last_used'].isoformat() if metrics.get('last_used') else None
            })

        return status

    async def disable_parser(self, parser_id: str) -> bool:
        """Disable a parser without removing it"""
        try:
            async with self.reload_lock:
                if parser_id in self.active_parsers:
                    # Remove from active parsers
                    del self.active_parsers[parser_id]

                    # Update database
                    cursor = self.db.cursor()
                    cursor.execute("""
                        UPDATE pattern_library
                        SET is_active = FALSE, updated_at = NOW()
                        WHERE pattern_id = %s
                    """, (parser_id,))

                    self.db.commit()

                    self.logger.info(f"Disabled parser: {parser_id}")
                    return True

        except Exception as e:
            self.logger.error(f"Failed to disable parser {parser_id}: {e}")
            self.db.rollback()

        return False