# Context Building Workflow - Alert Investigation

## The Existing Pipeline (We Don't Need to Rebuild!)

```
Ingestion Engine (Port 8003)
    ↓ (pulls from Elastic)
Contextualization Engine (Port 8004)
    ↓ (extracts entities, enriches)
Backend Engine (Port 8002)
    ↓ (stores intelligence)
PostgreSQL (entities, relationships, events)
```

## New Trigger: Investigation Context Request

When analyst opens an alert in Delivery Engine, we trigger this existing pipeline:

```
Delivery Engine (Port 8005)
    ↓ (publishes to Redis)

Redis Channel: "ingestion.pull_context"
    ↓

Ingestion Engine (Port 8003)
    ↓ (queries Elastic for last 1 hour)
    ↓ (sends logs through normal pipeline)

Contextualization Engine (Port 8004)
    ↓ (extracts entities, builds relationships)

Backend Engine (Port 8002)
    ↓ (stores in PostgreSQL)

PostgreSQL
    ↓ (entities, relationships, sessions)

Delivery Engine queries DB
    ↓

Returns enriched context to frontend
```

## Redis Message Format

### Request (Delivery → Ingestion)
```json
{
  "channel": "ingestion.pull_context",
  "request_id": "ctx-12345",
  "data": {
    "alert_id": "4d06e21e00330f033c410c1fbc35eed32445f9bd",
    "source_ip": "*************",
    "time_range": {
      "start": "2025-10-02T10:37:21Z",  // 1 hour before alert
      "end": "2025-10-02T11:37:21Z"     // alert time
    },
    "entity_types": ["ip", "user", "host", "process"],
    "priority": "high"  // Investigation context is high priority
  }
}
```

### Response (Ingestion → Delivery)
```json
{
  "channel": "delivery.context_ready",
  "request_id": "ctx-12345",
  "data": {
    "alert_id": "4d06e21e00330f033c410c1fbc35eed32445f9bd",
    "status": "completed",
    "logs_processed": 1547,
    "entities_extracted": 234,
    "relationships_created": 456,
    "timeline_events": 89,
    "query_time_ms": 2340
  }
}
```

## Implementation

### 1. Delivery Engine - Trigger Context Pull

```python
# engines/delivery/delivery_engine.py

async def _api_get_alert_context(self, request: web.Request):
    """Enhanced to trigger context building"""

    alert_id = request.match_info['alert_id']

    # Fetch the alert
    alerts = await self._fetch_elastic_alerts('all', 100)
    alert = next((a for a in alerts if a.get('alert_id') == alert_id), None)

    if not alert:
        return web.json_response({'error': 'Alert not found'}, status=404)

    # Step 1: Trigger context pull from Elastic
    context_request_id = str(uuid.uuid4())

    await self.redis_client.publish('ingestion.pull_context', json.dumps({
        'request_id': context_request_id,
        'alert_id': alert_id,
        'source_ip': alert['entities']['ips'][0] if alert['entities']['ips'] else None,
        'time_range': {
            'start': (datetime.fromisoformat(alert['timestamp'].replace('Z', '+00:00')) - timedelta(hours=1)).isoformat(),
            'end': alert['timestamp']
        },
        'entity_types': ['ip', 'user', 'host', 'process', 'file']
    }))

    # Step 2: Wait for context to be built (with timeout)
    context_ready = await self._wait_for_context(context_request_id, timeout=10)

    # Step 3: Query enriched data from database
    enriched_context = await self._query_enriched_context(alert)

    # Step 4: Generate investigation walkthrough
    investigation_context = await self.context_generator.generate_investigation_context(
        alert,
        enriched_context
    )

    return web.json_response(investigation_context)

async def _wait_for_context(self, request_id: str, timeout: int = 10):
    """Wait for context building to complete"""

    pubsub = self.redis_client.pubsub()
    await pubsub.subscribe('delivery.context_ready')

    start_time = time.time()

    while time.time() - start_time < timeout:
        message = await pubsub.get_message(timeout=1)

        if message and message['type'] == 'message':
            data = json.loads(message['data'])
            if data.get('request_id') == request_id:
                return data

        await asyncio.sleep(0.1)

    # Timeout - proceed with what we have
    self.logger.warning(f"Context building timed out for {request_id}")
    return None

async def _query_enriched_context(self, alert: Dict) -> Dict:
    """Query the enriched data from database after contextualization"""

    source_ip = alert['entities']['ips'][0] if alert['entities']['ips'] else None

    if not source_ip:
        return {}

    # Query entities
    entities = await self.db_connection.fetch("""
        SELECT entity_type, entity_value, risk_score, first_seen, last_seen
        FROM entities
        WHERE entity_value = $1
           OR entity_id IN (
               SELECT entity2_id FROM relationships
               WHERE entity1_id IN (
                   SELECT entity_id FROM entities WHERE entity_value = $1
               )
           )
    """, source_ip)

    # Query relationships
    relationships = await self.db_connection.fetch("""
        SELECT r.relationship_type,
               e1.entity_value as source,
               e2.entity_value as target,
               r.confidence_score
        FROM relationships r
        JOIN entities e1 ON r.entity1_id = e1.entity_id
        JOIN entities e2 ON r.entity2_id = e2.entity_id
        WHERE e1.entity_value = $1 OR e2.entity_value = $1
    """, source_ip)

    # Query timeline events
    timeline = await self.db_connection.fetch("""
        SELECT event_type, timestamp, severity, description
        FROM events
        WHERE source_ip = $1
        ORDER BY timestamp DESC
        LIMIT 100
    """, source_ip)

    return {
        'entities': [dict(e) for e in entities],
        'relationships': [dict(r) for r in relationships],
        'timeline': [dict(t) for t in timeline]
    }
```

### 2. Ingestion Engine - Listen for Context Requests

```python
# engines/ingestion/ingestion_engine.py

async def _handle_pull_context_request(self, message_data: Dict):
    """Handle context pull requests from Delivery Engine"""

    request_id = message_data.get('request_id')
    alert_id = message_data.get('alert_id')
    source_ip = message_data.get('source_ip')
    time_range = message_data.get('time_range')

    self.logger.info(f"Context pull requested for {source_ip} - request {request_id}")

    # Build Elastic query for last 1 hour
    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "range": {
                            "@timestamp": {
                                "gte": time_range['start'],
                                "lte": time_range['end']
                            }
                        }
                    }
                ],
                "should": [
                    {"term": {"source.ip": source_ip}},
                    {"term": {"destination.ip": source_ip}},
                    {"term": {"host.ip": source_ip}},
                    {"term": {"client.ip": source_ip}},
                    {"term": {"server.ip": source_ip}}
                ],
                "minimum_should_match": 1
            }
        },
        "size": 1000,  # Max 1000 logs
        "sort": [{"@timestamp": "asc"}]
    }

    # Query Elastic
    try:
        async with ClientSession() as session:
            elastic_url = f"{self.elastic_url}/logs-*/_search"
            headers = {
                'Authorization': f"ApiKey {self.elastic_api_key}",
                'Content-Type': 'application/json'
            }

            async with session.post(elastic_url, headers=headers, json=query) as response:
                if response.status == 200:
                    data = await response.json()
                    hits = data.get('hits', {}).get('hits', [])

                    self.logger.info(f"Retrieved {len(hits)} logs for context building")

                    # Send each log through the normal pipeline
                    for hit in hits:
                        log = hit['_source']

                        # Publish to contextualization engine
                        await self.redis_client.publish('contextualization.extract_entities', json.dumps({
                            'log_id': hit['_id'],
                            'log_data': log,
                            'context_request_id': request_id,
                            'alert_id': alert_id
                        }))

                    # Notify completion
                    await self.redis_client.publish('delivery.context_ready', json.dumps({
                        'request_id': request_id,
                        'alert_id': alert_id,
                        'status': 'completed',
                        'logs_processed': len(hits)
                    }))

                else:
                    self.logger.error(f"Elastic query failed: {response.status}")

    except Exception as e:
        self.logger.error(f"Error pulling context: {e}")

# Add to message handlers
async def _process_redis_messages(self):
    """Process messages from Redis"""

    pubsub = self.redis_client.pubsub()

    await pubsub.subscribe(
        'ingestion.pull_context',  # NEW - context requests
        'ingestion.ingest_log',
        'ingestion.alerts.received'
    )

    while True:
        message = await pubsub.get_message(timeout=1)

        if message and message['type'] == 'message':
            channel = message['channel'].decode('utf-8')
            data = json.loads(message['data'])

            if channel == 'ingestion.pull_context':
                await self._handle_pull_context_request(data)
            elif channel == 'ingestion.ingest_log':
                await self._handle_ingest_log(data)
            # ... other handlers

        await asyncio.sleep(0.01)
```

### 3. Contextualization Engine - Already Handles This!

No changes needed - it already:
- Extracts entities from logs
- Creates relationships
- Enriches with context
- Stores in PostgreSQL via Backend

### 4. What Gets Built (Example for *************)

After pulling 1 hour of logs:

```sql
-- Entities extracted
INSERT INTO entities (entity_type, entity_value, first_seen, last_seen)
VALUES
  ('ip', '*************', '2025-10-02 10:37:21', '2025-10-02 11:37:21'),
  ('ip', '**************', ...), -- destination IPs
  ('ip', '**************', ...),
  ('user', 'SYSTEM', ...),
  ('host', 'WORKSTATION-42', ...),
  ('process', 'svchost.exe', ...);

-- Relationships created
INSERT INTO relationships (entity1_id, entity2_id, relationship_type, confidence)
VALUES
  (ip:*************, host:WORKSTATION-42, 'located_on', 0.95),
  (ip:*************, ip:**************, 'connected_to', 0.90),
  (process:svchost.exe, ip:*************, 'bound_to', 0.85);

-- Timeline events
INSERT INTO events (source_ip, event_type, timestamp, description)
VALUES
  ('*************', 'network_connection', '10:37:21', 'Connected to **************:7680'),
  ('*************', 'network_connection', '10:37:22', 'Connected to **************:7680'),
  ('*************', 'process_start', '10:35:00', 'svchost.exe started');
```

## What Delivery Engine Returns to Frontend

```json
{
  "alert_id": "...",
  "investigation_context": {
    "entities_found": {
      "source_device": {
        "ip": "*************",
        "hostname": "WORKSTATION-42",  // Found from logs!
        "first_seen": "2025-10-02 10:35:00",
        "last_seen": "2025-10-02 11:37:21",
        "risk_score": 15
      },
      "connected_ips": [
        "**************",
        "**************",
        // ... 126 total
      ]
    },
    "relationships": [
      {
        "type": "located_on",
        "source": "*************",
        "target": "WORKSTATION-42",
        "confidence": 0.95
      },
      {
        "type": "process_initiated",
        "source": "svchost.exe",
        "target": "network_scan",
        "confidence": 0.85
      }
    ],
    "timeline": [
      {
        "time": "10:35:00",
        "event": "Process Start",
        "detail": "svchost.exe (Windows Update Delivery Optimization)"
      },
      {
        "time": "10:37:21",
        "event": "Port Scan Started",
        "detail": "Began scanning port 7680 across subnet"
      },
      {
        "time": "10:47:21",
        "event": "Port Scan Completed",
        "detail": "126 hosts scanned, 315 connection attempts"
      }
    ],
    "context_quality": {
      "logs_analyzed": 1547,
      "entities_extracted": 234,
      "relationships_mapped": 456,
      "confidence": 0.85
    }
  }
}
```

## Benefits

✅ **No New Pipeline** - Uses existing ingestion → contextualization → backend flow
✅ **Real-Time Context** - Pulls fresh data on demand
✅ **Enriched Intelligence** - Entities, relationships, timeline all built automatically
✅ **Async** - Doesn't block the API response (10 second timeout)
✅ **Redis Pub/Sub** - Clean inter-engine communication

This gives you the REAL context you were asking for - hostname, process, connections, timeline - all from the last hour of logs! 🎯
