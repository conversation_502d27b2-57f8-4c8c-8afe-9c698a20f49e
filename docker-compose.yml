# SIEMLess v2.0 - Complete Platform Stack
# Single orchestration file for entire cluster

services:
  # ===========================================
  # INFRASTRUCTURE SERVICES
  # ===========================================

  redis:
    image: redis:7-alpine
    container_name: siemless_redis
    ports:
      - "6380:6379"  # Changed to avoid conflicts
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    networks:
      - siemless_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  postgres:
    build:
      context: ./docker/postgres-age
      dockerfile: Dockerfile
    container_name: siemless_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-siemless_v2}
      POSTGRES_USER: ${POSTGRES_USER:-siemless}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-siemless123}
    ports:
      - "5433:5432"  # Changed to avoid conflicts
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./engines/init_db.sql:/docker-entrypoint-initdb.d/02-init-schema.sql:ro
    networks:
      - siemless_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-siemless} -d ${POSTGRES_DB:-siemless_v2}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 60s  # Increased for AGE build time

  # ===========================================
  # BACKEND ENGINES
  # ===========================================

  intelligence_engine:
    build:
      context: ./engines/intelligence
      dockerfile: Dockerfile
    container_name: siemless_intelligence
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=${POSTGRES_DB:-siemless_v2}
      - POSTGRES_USER=${POSTGRES_USER:-siemless}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-siemless123}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    ports:
      - "8001:8001"
    networks:
      - siemless_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  backend_engine:
    build:
      context: ./engines/backend
      dockerfile: Dockerfile
    container_name: siemless_backend
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=${POSTGRES_DB:-siemless_v2}
      - POSTGRES_USER=${POSTGRES_USER:-siemless}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-siemless123}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - OPENCTI_ENABLED=${OPENCTI_ENABLED:-false}
      - OPENCTI_URL=${OPENCTI_URL}
      - OPENCTI_TOKEN=${OPENCTI_TOKEN}
      - OTX_API_KEY=${OTX_API_KEY}
      - THREATFOX_AUTH_KEY=${THREATFOX_AUTH_KEY}
      - KEYCLOAK_URL=http://keycloak:8080
      - ENABLE_DEV_API_KEYS=true
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    ports:
      - "8002:8002"
    networks:
      - siemless_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  ingestion_engine:
    build:
      context: ./engines/ingestion
      dockerfile: Dockerfile
    container_name: siemless_ingestion
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=${POSTGRES_DB:-siemless_v2}
      - POSTGRES_USER=${POSTGRES_USER:-siemless}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-siemless123}
      - OPENCTI_ENABLED=${OPENCTI_ENABLED:-false}
      - OPENCTI_URL=${OPENCTI_URL}
      - OPENCTI_TOKEN=${OPENCTI_TOKEN}
      - OTX_API_KEY=${OTX_API_KEY}
      - THREATFOX_AUTH_KEY=${THREATFOX_AUTH_KEY}
      - CROWDSTRIKE_CLIENT_ID=${CROWDSTRIKE_CLIENT_ID}
      - CROWDSTRIKE_CLIENT_SECRET=${CROWDSTRIKE_CLIENT_SECRET}
      - CROWDSTRIKE_BASE_URL=${CROWDSTRIKE_BASE_URL:-https://api.crowdstrike.com}
      - ELASTIC_CLOUD_ID=${ELASTIC_CLOUD_ID}
      - ELASTIC_URL=${ELASTIC_URL}
      - ELASTIC_API_KEY=${ELASTIC_API_KEY}
      - ELASTIC_USERNAME=${ELASTIC_USERNAME}
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD}
      - ELASTIC_VERIFY_CERTS=${ELASTIC_VERIFY_CERTS:-true}
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    ports:
      - "8003:8003"
    networks:
      - siemless_network
    dns:
      - *******
      - *******
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  contextualization_engine:
    build:
      context: ./engines/contextualization
      dockerfile: Dockerfile
    container_name: siemless_contextualization
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=${POSTGRES_DB:-siemless_v2}
      - POSTGRES_USER=${POSTGRES_USER:-siemless}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-siemless123}
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    ports:
      - "8004:8004"
    networks:
      - siemless_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  delivery_engine:
    build:
      context: ./engines/delivery
      dockerfile: Dockerfile
    container_name: siemless_delivery
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=${POSTGRES_DB:-siemless_v2}
      - POSTGRES_USER=${POSTGRES_USER:-siemless}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-siemless123}
      - ELASTIC_CLOUD_ID=${ELASTIC_CLOUD_ID}
      - ELASTIC_API_KEY=${ELASTIC_API_KEY}
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
      intelligence_engine:
        condition: service_healthy
      backend_engine:
        condition: service_healthy
    ports:
      - "8005:8005"
    networks:
      - siemless_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ===========================================
  # FRONTEND
  # ===========================================

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: siemless_frontend
    ports:
      - "3000:3000"
    depends_on:
      - delivery_engine
    networks:
      - siemless_network
    environment:
      - VITE_API_BASE_URL=http://localhost:8005
      - VITE_INTELLIGENCE_URL=http://localhost:8001
      - VITE_BACKEND_URL=http://localhost:8002
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ===========================================
  # MONITORING (OPTIONAL - Uncomment to enable)
  # ===========================================

  # grafana:
  #   image: grafana/grafana:10.2.0
  #   container_name: siemless_grafana
  #   ports:
  #     - "3001:3000"
  #   environment:
  #     - GF_SECURITY_ADMIN_USER=admin
  #     - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
  #     - GF_INSTALL_PLUGINS=redis-datasource,redis-app
  #   volumes:
  #     - grafana_data:/var/lib/grafana
  #     - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
  #     - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
  #   networks:
  #     - siemless_network
  #   depends_on:
  #     - postgres
  #     - redis

  # prometheus:
  #   image: prom/prometheus:v2.48.0
  #   container_name: siemless_prometheus
  #   ports:
  #     - "9090:9090"
  #   volumes:
  #     - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
  #     - prometheus_data:/prometheus
  #   command:
  #     - '--config.file=/etc/prometheus/prometheus.yml'
  #     - '--storage.tsdb.path=/prometheus'
  #   networks:
  #     - siemless_network

  # ===========================================
  # AUTHENTICATION
  # ===========================================

  keycloak:
    image: quay.io/keycloak/keycloak:22.0
    container_name: siemless_keycloak
    environment:
      - KEYCLOAK_ADMIN=admin
      - KEYCLOAK_ADMIN_PASSWORD=${KEYCLOAK_PASSWORD:-admin}
      - KC_DB=postgres
      - KC_DB_URL=*******************************************
      - KC_DB_USERNAME=${POSTGRES_USER:-siemless}
      - KC_DB_PASSWORD=${POSTGRES_PASSWORD:-siemless123}
      - KC_HEALTH_ENABLED=true
    command: start-dev
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - siemless_network
    healthcheck:
      test: ["CMD-SHELL", "exec 3<>/dev/tcp/localhost/8080 && echo -e 'GET /health/ready HTTP/1.1\r\nHost: localhost\r\nConnection: close\r\n\r\n' >&3 && timeout 2 cat <&3 | grep -q 'UP'"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 60s

# ===========================================
# VOLUMES
# ===========================================
volumes:
  redis_data:
  postgres_data:
    external: true
    name: engines_postgres_data
  grafana_data:
  prometheus_data:

# ===========================================
# NETWORKS
# ===========================================
networks:
  siemless_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16