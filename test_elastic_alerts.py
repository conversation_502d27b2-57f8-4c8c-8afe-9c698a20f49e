"""
Test script to verify Elastic alert pulling capability
"""
import asyncio
import os
import base64
from aiohttp import ClientSession
from datetime import datetime, timedelta
import json

async def test_elastic_connection():
    """Test connection to Elastic and fetch alerts"""

    # Get credentials from environment
    cloud_id = os.getenv('ELASTIC_CLOUD_ID', 'Dacta_Global:YXAtc291dGhlYXN0LTEuYXdzLmZvdW5kLmlvJDZiNjdmNDdhZGY4MjQ1ZGQ4NzA1ZWY3ZjNlM2UxNTRmJDQ1MTM1MDg0YzhmZjRkMGJhMWFhYWZmNmU2MmZhNDQz')
    api_key = os.getenv('ELASTIC_API_KEY', 'OTk0Y2Jaa0JfVndFYTRsdERnRnk6OEE2d2F2TnZyRXZMNUJFeXhnUXAzQQ==')

    # Decode cloud ID to get Elastic URL
    # Format: cluster_name:base64(cloud_host$es_uuid$kibana_uuid)
    try:
        cloud_data = base64.b64decode(cloud_id.split(':')[1]).decode('utf-8')
        parts = cloud_data.split('$')
        cloud_host = parts[0]
        es_uuid = parts[1]
        elastic_url = f"https://{es_uuid}.{cloud_host}"
    except Exception as e:
        print(f"Error decoding cloud ID: {e}")
        return

    print(f"Elastic URL: {elastic_url}")
    print(f"API Key: {api_key[:20]}...")

    headers = {
        'Authorization': f"ApiKey {api_key}",
        'Content-Type': 'application/json'
    }

    # Test 1: Check cluster health
    print("\n=== Test 1: Cluster Health ===")
    try:
        async with ClientSession() as session:
            async with session.get(f"{elastic_url}/_cluster/health", headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"Cluster Status: {data.get('status')}")
                    print(f"Cluster Name: {data.get('cluster_name')}")
                else:
                    print(f"Health check failed: {response.status}")
                    text = await response.text()
                    print(f"Response: {text[:200]}")
    except Exception as e:
        print(f"Connection error: {e}")
        return

    # Test 2: List indices
    print("\n=== Test 2: List Alert Indices ===")
    try:
        async with ClientSession() as session:
            async with session.get(f"{elastic_url}/_cat/indices/.alerts*?format=json", headers=headers) as response:
                if response.status == 200:
                    indices = await response.json()
                    print(f"Found {len(indices)} alert indices:")
                    for idx in indices[:5]:
                        print(f"  - {idx['index']} ({idx.get('docs.count', 0)} docs)")
                else:
                    print(f"Failed to list indices: {response.status}")
    except Exception as e:
        print(f"Error listing indices: {e}")

    # Test 3: Query for recent alerts
    print("\n=== Test 3: Query Recent Alerts ===")

    # Try different index patterns
    index_patterns = [
        ".alerts-security.alerts-default",
        ".internal.alerts-security.alerts-default*",
        ".alerts-*"
    ]

    for index_pattern in index_patterns:
        print(f"\nTrying index: {index_pattern}")

        query = {
            "query": {
                "bool": {
                    "must": [
                        {"range": {"@timestamp": {"gte": "now-7d"}}}
                    ]
                }
            },
            "size": 10,
            "sort": [{"@timestamp": "desc"}]
        }

        try:
            async with ClientSession() as session:
                url = f"{elastic_url}/{index_pattern}/_search"
                async with session.post(url, headers=headers, json=query) as response:
                    if response.status == 200:
                        data = await response.json()
                        total = data.get('hits', {}).get('total', {})
                        hits = data.get('hits', {}).get('hits', [])

                        if isinstance(total, dict):
                            total_count = total.get('value', 0)
                        else:
                            total_count = total

                        print(f"  Total alerts: {total_count}")
                        print(f"  Retrieved: {len(hits)}")

                        if hits:
                            print("\n  Sample alerts:")
                            for i, hit in enumerate(hits[:3], 1):
                                source = hit.get('_source', {})
                                timestamp = source.get('@timestamp', 'N/A')
                                rule_name = source.get('kibana.alert.rule.name', source.get('signal.rule.name', 'Unknown'))
                                severity = source.get('kibana.alert.severity', source.get('signal.rule.severity', 'unknown'))

                                print(f"\n  Alert {i}:")
                                print(f"    Timestamp: {timestamp}")
                                print(f"    Rule: {rule_name}")
                                print(f"    Severity: {severity}")

                                # Show available fields
                                print(f"    Available fields: {list(source.keys())[:10]}")

                            # Success! Use this index pattern
                            return index_pattern, total_count
                        else:
                            print("  No alerts found in this index")
                    else:
                        error_text = await response.text()
                        print(f"  Query failed: {response.status}")
                        if "no such index" in error_text.lower():
                            print("  Index does not exist")
                        else:
                            print(f"  Error: {error_text[:200]}")
        except Exception as e:
            print(f"  Error: {e}")

    print("\n=== No alerts found in any index ===")
    return None, 0

if __name__ == "__main__":
    result = asyncio.run(test_elastic_connection())
    if result:
        index, count = result
        print(f"\n✅ Successfully connected to Elastic!")
        print(f"   Index: {index}")
        print(f"   Alert count: {count}")
    else:
        print(f"\n❌ Could not retrieve alerts from Elastic")
