"""
CrowdStrike Full Host Pull
Pull all hosts from CrowdStrike Falcon API
"""

import os
from dotenv import load_dotenv
from falconpy import Hosts
import json
from datetime import datetime

load_dotenv()

# CrowdStrike credentials
CLIENT_ID = os.getenv('CROWDSTRIKE_CLIENT_ID')
CLIENT_SECRET = os.getenv('CROWDSTRIKE_CLIENT_SECRET')
CLOUD_REGION = os.getenv('CROWDSTRIKE_CLOUD_REGION', 'us-2')

print("="*80)
print("CROWDSTRIKE FULL HOST PULL")
print("="*80)

# Initialize Falcon SDK
print("\n[1] Connecting to CrowdStrike Falcon API...")
falcon = Hosts(
    client_id=CLIENT_ID,
    client_secret=CLIENT_SECRET,
    member_cid=os.getenv('CROWDSTRIKE_MEMBER_CID'),
    base_url=CLOUD_REGION
)

# Get all host IDs
print("[2] Querying all host IDs...")
response = falcon.query_devices_by_filter_scroll(limit=5000)

if response['status_code'] != 200:
    print(f"[ERROR] Failed to query hosts: {response['body']['errors']}")
    exit(1)

host_ids = response['body']['resources']
total_hosts = response['body']['meta']['pagination']['total']

print(f"[OK] Found {total_hosts} hosts")

# Fetch full host details
print("\n[3] Fetching full details for all hosts...")
all_hosts = []
batch_size = 100

for i in range(0, len(host_ids), batch_size):
    batch = host_ids[i:i+batch_size]
    print(f"  Batch {i//batch_size + 1}/{(len(host_ids) + batch_size - 1)//batch_size} ({len(batch)} hosts)...")

    details_response = falcon.get_device_details(ids=batch)

    if details_response['status_code'] == 200:
        hosts = details_response['body']['resources']
        all_hosts.extend(hosts)
    else:
        print(f"  [WARNING] Batch failed: {details_response['body'].get('errors')}")

print(f"\n[OK] Retrieved {len(all_hosts)} complete host records")

# Display summary statistics
print("\n" + "="*80)
print("HOST SUMMARY")
print("="*80)

# OS breakdown
os_counts = {}
for host in all_hosts:
    os_version = host.get('os_version', 'Unknown')
    os_counts[os_version] = os_counts.get(os_version, 0) + 1

print(f"\n[OS Distribution]")
for os_name, count in sorted(os_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
    print(f"  {os_name}: {count}")

# Status breakdown
status_counts = {}
for host in all_hosts:
    status = host.get('status', 'Unknown')
    status_counts[status] = status_counts.get(status, 0) + 1

print(f"\n[Host Status]")
for status, count in status_counts.items():
    print(f"  {status}: {count}")

# Last seen breakdown
now = datetime.utcnow()
last_seen_ranges = {
    'Online (< 1 hour)': 0,
    'Recent (< 24 hours)': 0,
    'This week': 0,
    'This month': 0,
    'Older': 0
}

for host in all_hosts:
    last_seen = host.get('last_seen')
    if last_seen:
        last_seen_dt = datetime.fromisoformat(last_seen.replace('Z', '+00:00'))
        hours_ago = (now - last_seen_dt.replace(tzinfo=None)).total_seconds() / 3600

        if hours_ago < 1:
            last_seen_ranges['Online (< 1 hour)'] += 1
        elif hours_ago < 24:
            last_seen_ranges['Recent (< 24 hours)'] += 1
        elif hours_ago < 168:  # 7 days
            last_seen_ranges['This week'] += 1
        elif hours_ago < 720:  # 30 days
            last_seen_ranges['This month'] += 1
        else:
            last_seen_ranges['Older'] += 1

print(f"\n[Last Seen]")
for range_name, count in last_seen_ranges.items():
    print(f"  {range_name}: {count}")

# Sample hosts
print(f"\n[Sample Hosts]")
for host in all_hosts[:5]:
    print(f"\n  Hostname: {host.get('hostname', 'N/A')}")
    print(f"  Device ID: {host.get('device_id', 'N/A')}")
    print(f"  OS: {host.get('os_version', 'N/A')}")
    print(f"  Local IP: {host.get('local_ip', 'N/A')}")
    print(f"  External IP: {host.get('external_ip', 'N/A')}")
    print(f"  Agent Version: {host.get('agent_version', 'N/A')}")
    print(f"  Last Seen: {host.get('last_seen', 'N/A')}")
    print(f"  Status: {host.get('status', 'N/A')}")

# Save to file
output_file = f"crowdstrike_hosts_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
with open(output_file, 'w') as f:
    json.dump(all_hosts, f, indent=2, default=str)

print(f"\n[OK] Full host data saved to: {output_file}")
print(f"     File size: {os.path.getsize(output_file) / 1024:.2f} KB")

print("\n" + "="*80)
print("PULL COMPLETE")
print("="*80)
