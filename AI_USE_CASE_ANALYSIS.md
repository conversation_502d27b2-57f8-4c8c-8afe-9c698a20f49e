# AI Use Case Analysis System
**Intelligent Security Investigation with Context Plugins**

## Overview

The **AI Use Case Analyzer** transforms investigation context from multiple sources (CrowdStrike, Elastic, etc.) into actionable security insights using a hybrid approach:

1. **Pattern-Based Analysis** (Fast, Free) - Crystallized patterns
2. **AI-Powered Analysis** (Deep, Smart) - Multi-AI consensus when needed

## Architecture

```
┌─────────────────────────────────────────────────────────┐
│                   Investigation Flow                     │
└─────────────────────────────────────────────────────────┘

1. Query Trigger
   └─> "Get context for hostname=server-01"

2. Context Plugins (CrowdStrike + Elastic)
   ├─> CrowdStrike: Device info, detections, incidents
   └─> Elastic: Logs, alerts, network flows

3. Entity Extraction
   └─> Extract: IPs, users, processes, files, etc.

4. Use Case Analysis ← NEW!
   ├─> Pattern Match (70% cases, free, instant)
   │   ├─> Lateral Movement patterns
   │   ├─> Privilege Escalation patterns
   │   ├─> Data Exfiltration patterns
   │   └─> Malware Execution patterns
   │
   └─> AI Deep Analysis (30% cases, when patterns don't match)
       ├─> Multi-AI consensus
       ├─> Novel threat detection
       └─> Complex behavior analysis

5. Risk Scoring & Recommendations
   └─> Actionable next steps for analyst
```

## Use Case Categories

### 1. Lateral Movement
**What**: Attacker moving between systems
**Patterns**:
- PsExec execution (admin$ share + SMB)
- RDP anomalies (unusual time/source)
- Pass-the-Hash attacks
- WMI remote execution

### 2. Privilege Escalation
**What**: Gaining higher privileges
**Patterns**:
- UAC bypass (fodhelper, eventvwr)
- Token manipulation (SeDebugPrivilege)
- DLL hijacking
- Exploiting vulnerable services

### 3. Data Exfiltration
**What**: Stealing data
**Patterns**:
- Large uploads to cloud storage
- Compression before upload (7zip + upload)
- DNS tunneling
- Unusual external connections

### 4. Malware Execution
**What**: Running malicious code
**Patterns**:
- Office spawning PowerShell
- Encoded PowerShell commands
- Living-off-the-land techniques (certutil, bitsadmin)
- Suspicious process trees

### 5. Reconnaissance
**What**: Gathering information
**Patterns**:
- Network scanning
- Active Directory enumeration
- Service discovery
- Credential dumping attempts

### 6. Persistence
**What**: Maintaining access
**Patterns**:
- Registry Run keys
- Scheduled tasks creation
- Service installation
- Startup folder modifications

### 7. Credential Access
**What**: Stealing credentials
**Patterns**:
- LSASS memory dumping
- SAM database access
- Keylogging
- Credential harvesting tools

### 8. Command and Control
**What**: Communicating with attacker infrastructure
**Patterns**:
- Beaconing behavior
- Known C2 domains/IPs
- Unusual outbound connections
- Protocol anomalies

## How It Works

### Step 1: Extract Indicators

From context plugin results, extract:

```python
indicators = {
    'hosts': [
        {
            'hostname': '010117039050LN1',
            'ip': '*************',
            'os': 'Windows 10',
            'users': ['admin', 'jsmith'],
            'processes': ['powershell.exe', 'psexec.exe'],
            'source': 'crowdstrike'
        }
    ],
    'detections': [
        {
            'alert_id': 'CS-12345',
            'severity': 'high',
            'tactic': 'Lateral Movement',
            'technique': 'T1021.002',
            'process': 'psexec.exe',
            'source': 'crowdstrike'
        }
    ],
    'network': [
        {
            'source_ip': '*************',
            'dest_ip': '*************',
            'dest_port': 445,
            'protocol': 'SMB',
            'source': 'elastic'
        }
    ]
}
```

### Step 2: Pattern Matching (Free, Instant)

Check against crystallized patterns:

```python
# Example: PsExec lateral movement pattern
pattern = {
    'indicators': ['psexec', 'admin$', 'smb', 'port_445'],
    'severity': 'high',
    'confidence': 0.90
}

# Match indicators from context
matches = ['psexec', 'smb', 'port_445']  # 3 out of 4
match_score = 0.75  # 75% match

if match_score >= 0.70:
    # Pattern matched!
    finding = {
        'use_case': 'lateral_movement',
        'pattern': 'psexec_execution',
        'severity': 'high',
        'confidence': 0.90 * 0.75 = 0.675,
        'method': 'pattern',
        'description': 'PsExec-based lateral movement detected'
    }
```

### Step 3: AI Analysis (When Needed)

If patterns don't match or confidence is low:

```python
# Build AI prompt with context
prompt = """
You are analyzing security investigation context.

INDICATORS:
- Host: 010117039050LN1 (Windows 10)
- Process: powershell.exe -enc <base64>
- Network: Connection to ************:443
- User: admin
- Detection: CrowdStrike alert (high severity)

TASK: Identify security use cases and provide:
1. Category (e.g., malware_execution)
2. Severity (critical/high/medium/low)
3. Confidence (0.0-1.0)
4. Description
5. Evidence
6. Reasoning
"""

# Send to Intelligence Engine (multi-AI consensus)
ai_response = intelligence_engine.analyze(prompt)

# Parse findings
findings = [
    {
        'use_case': 'malware_execution',
        'severity': 'critical',
        'confidence': 0.92,
        'method': 'ai_consensus',
        'description': 'Encoded PowerShell execution with suspicious network activity',
        'evidence': ['encoded_powershell', 'external_c2_connection', 'admin_context'],
        'ai_reasoning': 'Combination of encoded PowerShell + external connection suggests command & control'
    }
]
```

### Step 4: Generate Recommendations

Based on findings, create actionable recommendations:

```python
recommendations = [
    {
        'priority': 'immediate',
        'action': 'Isolate affected systems',
        'reason': '2 critical findings detected',
        'systems': ['010117039050LN1', '*************']
    },
    {
        'priority': 'urgent',
        'action': 'Begin incident response',
        'reason': 'Lateral movement + malware execution detected',
        'next_steps': [
            'Collect memory dumps from affected hosts',
            'Review recent user activity for admin account',
            'Check for persistence mechanisms',
            'Identify compromised credentials'
        ]
    },
    {
        'priority': 'high',
        'action': 'Review lateral movement paths',
        'tasks': [
            'Identify all systems accessed by admin account',
            'Check for unauthorized access to sensitive systems',
            'Review network segmentation'
        ]
    }
]
```

### Step 5: Calculate Risk Score

```python
risk_score = {
    'score': 85.3,  # 0-100 scale
    'level': 'critical',  # critical/high/medium/low/info
    'breakdown': {
        'critical': 2,
        'high': 3,
        'medium': 1
    },
    'total_findings': 6
}
```

## Complete Output Example

### Input (Context from CrowdStrike + Elastic):

```json
{
  "request_id": "inv-001",
  "query_type": "hostname",
  "query_value": "server-01",
  "context_results": {
    "crowdstrike": [
      {
        "category": "asset",
        "data": {
          "hostname": "server-01",
          "local_ip": "*************",
          "os_version": "Windows Server 2019",
          "users": ["admin"],
          "processes": ["psexec.exe", "powershell.exe"]
        }
      },
      {
        "category": "detection",
        "data": {
          "detection_id": "CS-12345",
          "severity": "high",
          "tactic": "Lateral Movement",
          "technique": "T1021.002",
          "process": "psexec.exe"
        }
      }
    ],
    "elastic": [
      {
        "category": "network",
        "data": {
          "source_ip": "*************",
          "dest_ip": "*************",
          "dest_port": 445,
          "protocol": "SMB"
        }
      },
      {
        "category": "log",
        "data": {
          "process": "powershell.exe",
          "action": "process_create",
          "user": "admin",
          "message": "powershell.exe -enc <base64>"
        }
      }
    ]
  }
}
```

### Output (AI Use Case Analysis):

```json
{
  "request_id": "inv-001",
  "timestamp": "2025-10-02T13:30:00Z",
  "query_context": {
    "query_type": "hostname",
    "query_value": "server-01"
  },
  "risk_score": {
    "score": 85.3,
    "level": "critical",
    "breakdown": {
      "critical": 1,
      "high": 2,
      "medium": 0
    },
    "total_findings": 3
  },
  "findings": [
    {
      "finding_id": "lateral_movement_psexec_1727880600.123",
      "use_case": "lateral_movement",
      "pattern": "psexec_execution",
      "severity": "high",
      "confidence": 0.88,
      "method": "pattern",
      "matched_indicators": ["psexec", "smb", "admin$"],
      "description": "PsExec-based lateral movement detected",
      "timestamp": "2025-10-02T13:30:00Z"
    },
    {
      "finding_id": "ai_malware_execution_1727880600.456",
      "use_case": "malware_execution",
      "severity": "critical",
      "confidence": 0.92,
      "method": "ai_consensus",
      "description": "Encoded PowerShell execution with suspicious characteristics",
      "evidence": ["encoded_powershell", "admin_context", "lateral_movement"],
      "ai_reasoning": "Encoded PowerShell execution in context of lateral movement suggests remote command execution or malware deployment",
      "timestamp": "2025-10-02T13:30:00Z"
    },
    {
      "finding_id": "pattern_credential_access_1727880600.789",
      "use_case": "credential_access",
      "pattern": "smb_admin_share",
      "severity": "high",
      "confidence": 0.80,
      "method": "pattern",
      "matched_indicators": ["admin$", "smb", "remote_access"],
      "description": "Admin share access pattern indicating potential credential use",
      "timestamp": "2025-10-02T13:30:00Z"
    }
  ],
  "recommendations": [
    {
      "priority": "immediate",
      "action": "Isolate affected systems",
      "reason": "1 critical and 2 high-severity findings detected",
      "systems": ["server-01", "*************"]
    },
    {
      "priority": "urgent",
      "action": "Begin incident response",
      "reason": "Lateral movement + malware execution detected",
      "next_steps": [
        "Collect memory dumps from affected hosts",
        "Review recent user activity for admin account",
        "Check for persistence mechanisms on target systems",
        "Identify all systems accessed via lateral movement"
      ]
    },
    {
      "priority": "high",
      "action": "Review lateral movement paths",
      "reason": "Lateral movement detected",
      "tasks": [
        "Identify all compromised credentials (admin account)",
        "Check for unauthorized access to sensitive systems",
        "Review network segmentation between affected systems"
      ]
    },
    {
      "priority": "critical",
      "action": "Assess malware deployment scope",
      "reason": "Malware execution indicators found",
      "tasks": [
        "Identify what PowerShell script was executed",
        "Check for malware artifacts on target systems",
          "Review file creation/modification events"
      ]
    }
  ],
  "analysis_method": {
    "pattern_based": 2,
    "ai_based": 1
  }
}
```

## Cost Optimization

### Pattern-Based (Free):
- **70% of cases** match crystallized patterns
- **$0.00 cost** per analysis
- **< 100ms** response time
- Patterns from previous AI validations

### AI-Based (When Needed):
- **30% of cases** require AI
- **$0.001-0.01** per analysis (depending on model)
- **1-3 seconds** response time
- Multi-AI consensus for accuracy

### Total Savings:
- **Without patterns**: $10 per 1,000 investigations
- **With patterns**: $3 per 1,000 investigations
- **70% cost reduction**

## Integration Flow

```
1. User queries: "Investigate hostname=server-01"
   └─> Delivery Engine

2. Context pulled from plugins
   └─> Ingestion Engine
       ├─> CrowdStrike plugin
       └─> Elastic plugin

3. Entities extracted
   └─> Contextualization Engine
       └─> Extracts: IPs, users, processes

4. Use cases analyzed ← NEW!
   └─> Use Case Analyzer
       ├─> Pattern match (free)
       └─> AI analysis (when needed)

5. Results delivered to analyst
   └─> Delivery Engine
       ├─> Risk score
       ├─> Findings
       └─> Recommendations
```

## Example Use Cases

### Case 1: Ransomware Attack

**Context**:
- CrowdStrike: Multiple detections on server-01
- Elastic: Large file operations, encryption processes

**Findings**:
1. **Malware Execution** (Critical) - Ransomware binary detected
2. **Lateral Movement** (High) - Spread to 5 other systems
3. **Impact** (Critical) - File encryption activity

**Recommendations**:
- IMMEDIATE: Isolate all affected systems
- URGENT: Restore from backups
- HIGH: Identify initial infection vector

### Case 2: Insider Threat

**Context**:
- CrowdStrike: Normal user activity
- Elastic: Large data downloads, USB usage, compression

**Findings**:
1. **Data Exfiltration** (High) - 50GB compressed files
2. **Collection** (Medium) - Sensitive folder access
3. **Exfiltration** (High) - USB device data transfer

**Recommendations**:
- IMMEDIATE: Disable user account
- URGENT: Review data access logs
- HIGH: Engage HR/Legal teams

### Case 3: APT Activity

**Context**:
- CrowdStrike: Low-severity alerts over 30 days
- Elastic: Unusual network patterns, beaconing

**Findings**:
1. **Command & Control** (Critical) - Beaconing to external IP
2. **Persistence** (High) - Scheduled task creation
3. **Reconnaissance** (Medium) - AD enumeration

**Recommendations**:
- IMMEDIATE: Block C2 communication
- URGENT: Hunt for persistence across environment
- HIGH: Full forensic investigation

## File Structure

```
engines/intelligence/
├── use_case_analyzer.py          # Main analyzer
├── patterns/
│   ├── lateral_movement.json     # Pattern library
│   ├── privilege_escalation.json
│   ├── data_exfiltration.json
│   └── malware_execution.json
└── ai_prompts/
    └── use_case_templates.py      # AI prompt templates
```

## Configuration

No configuration needed! The analyzer:
- ✅ Loads crystallized patterns automatically
- ✅ Connects to Intelligence Engine for AI
- ✅ Falls back gracefully if AI unavailable

## Benefits

### For Analysts:
- **Faster Investigations**: Automated use case identification
- **Better Context**: See the "why" not just the "what"
- **Actionable Recommendations**: Clear next steps
- **Risk Scoring**: Prioritize investigations

### For Organizations:
- **Cost Efficient**: 70% savings via pattern matching
- **Scalable**: Handle 10x more investigations
- **Consistent**: Same analysis quality every time
- **Learning System**: Patterns improve over time

## Next Steps

1. **Add More Patterns**: Expand pattern library (currently 12 patterns)
2. **Tune AI Prompts**: Improve AI analysis quality
3. **Custom Use Cases**: Add organization-specific patterns
4. **MITRE ATT&CK Mapping**: Link findings to techniques
5. **Automated Playbooks**: Trigger response actions

## Summary

The **AI Use Case Analyzer** transforms raw context data from security tools into actionable intelligence:

✅ **Hybrid Approach**: Patterns (70%, free) + AI (30%, when needed)
✅ **10 Use Case Categories**: Lateral movement, privilege escalation, etc.
✅ **Risk Scoring**: Prioritize investigations
✅ **Actionable Recommendations**: Clear next steps
✅ **Cost Optimized**: 70% savings vs pure AI

**The result**: Faster, smarter, cheaper security investigations.
