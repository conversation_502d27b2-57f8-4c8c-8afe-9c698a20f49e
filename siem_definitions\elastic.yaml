# Elasticsearch / Elastic Security SIEM Configuration
# Query Language: KQL (Kibana Query Language) / Lucene

platform:
  name: elastic
  display_name: Elastic Security
  query_language: kql
  description: Elastic's Kibana Query Language and Lucene syntax
  vendor: Elastic N.V.
  version: "1.0"
  active: true

# Field mappings: generic_field -> Elastic ECS field
field_mappings:
  source_ip: source.ip
  destination_ip: destination.ip
  username: user.name
  process_name: process.name
  file_hash: file.hash.sha256
  event_id: event.code
  hostname: host.name
  port: destination.port
  source_port: source.port
  destination_port: destination.port
  domain: dns.question.name
  url: url.full
  file_name: file.name
  file_path: file.path
  registry_path: registry.path
  command_line: process.command_line
  parent_process: process.parent.name
  network_protocol: network.protocol
  http_method: http.request.method
  user_agent: user_agent.original
  email_sender: email.from.address
  email_recipient: email.to.address
  dns_query: dns.question.name
  service_name: service.name
  account_name: user.name
  process_id: process.pid
  parent_process_id: process.parent.pid
  user_domain: user.domain
  host_os: host.os.name
  event_action: event.action
  event_outcome: event.outcome

# Operator mappings: generic_operator -> Elastic operator
operator_mappings:
  equals: ":"
  not_equals: NOT
  contains: "*value*"
  not_contains: "NOT *value*"
  regex: "/.*/"
  greater_than: ">"
  less_than: "<"
  greater_equal: ">="
  less_equal: "<="
  in_list: "in"
  not_in_list: "not in"
  exists: "*"
  not_exists: "NOT *"

# Time field for temporal queries
time_field: "@timestamp"

# Query syntax specifics
syntax:
  comment: "//"
  string_quote: "\""
  escape_char: "\\"
  wildcard: "*"
  field_separator: ":"
  logical_and: AND
  logical_or: OR
  logical_not: NOT
  phrase_operator: "\"\""
  range_operator: "[]"
  exists_operator: "_exists_"
  case_sensitive: false
  fuzzy_operator: "~"

# Platform-specific features
metadata:
  supports_regex: true
  supports_wildcards: true
  supports_aggregations: true
  supports_scripting: true
  supports_painless: true
  max_result_window: 10000
  default_time_range: "15m"
  index_pattern_prefix: "logs-*"

  # ECS (Elastic Common Schema) categories
  ecs_categories:
    - network
    - process
    - file
    - registry
    - authentication
    - host
    - user
    - event

  # Common index patterns
  common_index_patterns:
    - logs-*
    - logs-endpoint.*
    - logs-windows.*
    - logs-system.*
    - filebeat-*
    - winlogbeat-*
    - packetbeat-*
    - auditbeat-*

  # Detection rule template
  rule_template: |
    {field}:{value} AND {field2}:{value2}
    NOT {exclude_field}:{exclude_value}

  # EQL (Event Query Language) support
  supports_eql: true
  eql_template: |
    process where process.name == "{value}"
    and process.command_line like "*{pattern}*"

  # Detection rule types
  rule_types:
    - query  # KQL/Lucene query
    - eql    # Event Query Language
    - threshold  # Count-based detection
    - machine_learning  # ML-based anomaly detection
    - indicator_match  # IOC matching
