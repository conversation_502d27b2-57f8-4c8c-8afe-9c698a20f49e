"""
Log Source Quality Redis Pub/Sub Handler
Provides Redis interface for all 11 log quality endpoints
Clean, modular implementation separate from main backend engine
"""

import json
import uuid
import logging
from typing import Dict, Any, List
from datetime import datetime
from decimal import Decimal


class LogQualityRedisHandler:
    """
    Redis pub/sub handler for log source quality API
    Translates Redis messages to HTTP handler calls
    """

    def __init__(self, backend_engine):
        """Initialize with reference to backend engine"""
        self.backend = backend_engine
        self.logger = logging.getLogger("backend.redis.log_quality")

        # Map Redis channels to handler methods
        self.channel_handlers = {
            'backend.log_quality.status': self._handle_status,
            'backend.log_quality.register': self._handle_register,
            'backend.log_quality.detection_fidelity': self._handle_detection_fidelity,
            'backend.log_quality.coverage': self._handle_coverage,
            'backend.log_quality.technique_coverage': self._handle_technique_coverage,
            'backend.log_quality.correlation_capability': self._handle_correlation_capability,
            'backend.log_quality.correlation_requirements': self._handle_correlation_requirements,
            'backend.log_quality.recommendations': self._handle_recommendations,
            'backend.log_quality.coverage_gaps': self._handle_coverage_gaps,
            'backend.log_quality.simulate': self._handle_simulate
        }

    def get_channels(self) -> List[str]:
        """Return list of channels this handler subscribes to"""
        return list(self.channel_handlers.keys())

    async def handle_message(self, channel: str, data: Dict[str, Any]) -> bool:
        """
        Handle incoming Redis message
        Returns True if handled, False otherwise
        """
        if channel not in self.channel_handlers:
            return False

        try:
            handler = self.channel_handlers[channel]
            await handler(data)
            return True
        except Exception as e:
            self.logger.error(f"Error handling {channel}: {e}")
            request_id = data.get('request_id', str(uuid.uuid4()))
            await self._send_error_response(request_id, str(e))
            return True

    def _serialize_for_json(self, data: Any) -> Any:
        """
        Recursively serialize data for JSON, handling special types
        """
        if isinstance(data, Decimal):
            return float(data)
        elif isinstance(data, datetime):
            return data.isoformat()
        elif isinstance(data, dict):
            return {k: self._serialize_for_json(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._serialize_for_json(item) for item in data]
        elif hasattr(data, '__dict__'):
            # Handle objects with __dict__
            return self._serialize_for_json(data.__dict__)
        return data

    async def _send_response(self, request_id: str, response_data: Dict[str, Any]):
        """Send response back via Redis"""
        try:
            channel = f'backend.log_quality.response.{request_id}'
            # Ensure data is JSON serializable
            serialized_data = self._serialize_for_json(response_data)
            message = json.dumps(serialized_data)
            self.backend.redis_client.publish(channel, message)
        except Exception as e:
            self.logger.error(f"Failed to send response: {e}")

    async def _send_error_response(self, request_id: str, error_message: str):
        """Send error response"""
        await self._send_response(request_id, {
            'request_id': request_id,
            'status': 'error',
            'error': error_message,
            'timestamp': datetime.utcnow().isoformat()
        })

    # Handler methods for each endpoint

    async def _handle_status(self, data: Dict[str, Any]):
        """Handle log source status request"""
        request_id = data.get('request_id', str(uuid.uuid4()))

        try:
            # Get all registered log sources
            cursor = self.backend.db_connection.cursor()
            cursor.execute("""
                SELECT source_id, source_name, source_type, quality_tier, quality_score, created_at
                FROM log_sources
                ORDER BY created_at DESC
            """)

            sources = []
            rows = cursor.fetchall()
            if rows:
                for row in rows:
                    source_data = self.backend._extract_row_data(row, {
                        'source_id': 0,
                        'source_name': 1,
                        'source_type': 2,
                        'quality_tier': 3,
                        'quality_score': 4,
                        'created_at': 5
                    })
                    sources.append(source_data)

            await self._send_response(request_id, {
                'request_id': request_id,
                'status': 'success',
                'sources': sources,
                'count': len(sources)
            })

        except Exception as e:
            self.logger.error(f"Status handler error: {e}")
            await self._send_error_response(request_id, str(e))

    async def _handle_register(self, data: Dict[str, Any]):
        """Handle log source registration"""
        request_id = data.get('request_id', str(uuid.uuid4()))

        try:
            # Generate unique source_id
            source_id = f"{data.get('type', 'unknown')}-{str(uuid.uuid4())[:8]}"

            # Register with quality engine
            source_config = {
                'type': data.get('type', 'endpoint'),
                'product': data.get('product', ''),
                'capabilities': data.get('capabilities', [])
            }

            result = await self.backend.log_source_quality.register_source(
                source_name=data.get('name'),
                source_config=source_config
            )

            # Extract values
            tier = result.get('tier', 'BRONZE') if result else 'BRONZE'
            score = result.get('score', 50) if result else 50

            # Store in database
            cursor = self.backend.db_connection.cursor()
            cursor.execute("""
                INSERT INTO log_sources (source_id, source_name, source_type, quality_tier, quality_score)
                VALUES (%s, %s, %s, %s, %s)
                ON CONFLICT (source_name) DO UPDATE SET
                    source_type = EXCLUDED.source_type,
                    quality_tier = EXCLUDED.quality_tier,
                    quality_score = EXCLUDED.quality_score,
                    updated_at = CURRENT_TIMESTAMP
                RETURNING source_id
            """, (
                source_id,
                data.get('name'),
                data.get('type', 'endpoint'),
                tier,
                score
            ))

            # Get the actual source_id
            row = cursor.fetchone()
            if row:
                source_id = row[0] if isinstance(row, (tuple, list)) else row.get('source_id', source_id)

            self.backend.db_connection.commit()

            await self._send_response(request_id, {
                'request_id': request_id,
                'status': 'success',
                'source_id': source_id,
                'name': data.get('name'),
                'type': data.get('type', 'endpoint'),
                'tier': tier,
                'score': score
            })

        except Exception as e:
            self.logger.error(f"Register handler error: {e}")
            await self._send_error_response(request_id, str(e))

    async def _handle_detection_fidelity(self, data: Dict[str, Any]):
        """Handle detection fidelity calculation"""
        request_id = data.get('request_id', str(uuid.uuid4()))

        try:
            attack_types = data.get('attack_types', [])
            if not attack_types:
                raise ValueError("attack_types list is required")

            # Get current log sources
            cursor = self.backend.db_connection.cursor()
            cursor.execute("SELECT source_name FROM log_sources")

            available_sources = []
            rows = cursor.fetchall()
            if rows:
                for row in rows:
                    source_name = row[0] if isinstance(row, (tuple, list)) else row.get('source_name', '')
                    if source_name:
                        available_sources.append(source_name)

            # Calculate fidelity
            fidelity = self.backend.detection_fidelity.calculate_detection_fidelity(
                attack_types, available_sources
            )

            await self._send_response(request_id, {
                'request_id': request_id,
                'status': 'success',
                'attack_types': attack_types,
                'available_sources': available_sources,
                'fidelity': fidelity
            })

        except Exception as e:
            self.logger.error(f"Detection fidelity handler error: {e}")
            await self._send_error_response(request_id, str(e))

    async def _handle_coverage(self, data: Dict[str, Any]):
        """Handle overall detection coverage"""
        request_id = data.get('request_id', str(uuid.uuid4()))

        try:
            # Get current log sources
            cursor = self.backend.db_connection.cursor()
            cursor.execute("SELECT source_name FROM log_sources")

            available_sources = []
            rows = cursor.fetchall()
            if rows:
                for row in rows:
                    source_name = row[0] if isinstance(row, (tuple, list)) else row.get('source_name', '')
                    if source_name:
                        available_sources.append(source_name)

            # Calculate coverage for all attack types
            all_attacks = list(self.backend.correlation_requirements.attack_requirements.keys())
            coverage_map = {}

            for attack_type in all_attacks:
                fidelity = self.backend.detection_fidelity.calculate_detection_fidelity(
                    [attack_type], available_sources
                )
                confidence = fidelity.get('attack_fidelity', {}).get(attack_type, {}).get('confidence', 0)
                coverage_map[attack_type] = {
                    'covered': confidence > 70,
                    'confidence': confidence
                }

            # Overall metrics
            covered_count = sum(1 for v in coverage_map.values() if v['covered'])
            avg_confidence = sum(v['confidence'] for v in coverage_map.values()) / len(coverage_map) if coverage_map else 0

            await self._send_response(request_id, {
                'request_id': request_id,
                'status': 'success',
                'coverage': coverage_map,
                'overall_coverage': f"{covered_count}/{len(all_attacks)}",
                'average_confidence': avg_confidence,
                'source_count': len(available_sources)
            })

        except Exception as e:
            self.logger.error(f"Coverage handler error: {e}")
            await self._send_error_response(request_id, str(e))

    async def _handle_technique_coverage(self, data: Dict[str, Any]):
        """Handle MITRE technique coverage"""
        request_id = data.get('request_id', str(uuid.uuid4()))

        try:
            techniques = data.get('techniques', [])
            if not techniques:
                raise ValueError("techniques list is required")

            # Get current log sources
            cursor = self.backend.db_connection.cursor()
            cursor.execute("SELECT source_name FROM log_sources")

            current_sources = []
            rows = cursor.fetchall()
            if rows:
                for row in rows:
                    source_name = row[0] if isinstance(row, (tuple, list)) else row.get('source_name', '')
                    if source_name:
                        current_sources.append(source_name)

            # Check coverage for each technique
            coverage_results = {}
            for technique in techniques:
                attack_types = self.backend._map_technique_to_attacks(technique)

                if attack_types and current_sources:
                    fidelity_calc = self.backend.detection_fidelity.calculate_detection_fidelity(
                        attack_types, current_sources
                    )
                    attack_fidelity = fidelity_calc.get('attack_fidelity', {})

                    confidences = []
                    for at in attack_types:
                        if at in attack_fidelity:
                            conf = attack_fidelity[at].get('confidence', 0)
                            confidences.append(conf)

                    max_confidence = max(confidences) if confidences else 0

                    coverage_results[technique] = {
                        'covered': max_confidence > 70,
                        'confidence': max_confidence,
                        'attack_types': attack_types
                    }
                else:
                    coverage_results[technique] = {
                        'covered': False,
                        'confidence': 0,
                        'attack_types': attack_types if attack_types else []
                    }

            await self._send_response(request_id, {
                'request_id': request_id,
                'status': 'success',
                'coverage': coverage_results
            })

        except Exception as e:
            self.logger.error(f"Technique coverage handler error: {e}")
            await self._send_error_response(request_id, str(e))

    async def _handle_correlation_capability(self, data: Dict[str, Any]):
        """Handle correlation capability assessment"""
        request_id = data.get('request_id', str(uuid.uuid4()))

        try:
            # Get all log sources with details
            cursor = self.backend.db_connection.cursor()
            cursor.execute("""
                SELECT source_name, source_type, quality_tier, quality_score
                FROM log_sources
            """)

            available_sources = []
            rows = cursor.fetchall()

            if rows:
                for row in rows:
                    if row:
                        if isinstance(row, (tuple, list)) and len(row) >= 4:
                            source_name = row[0]
                            source_type = row[1]
                            tier = row[2]
                            score = row[3]
                        else:
                            source_name = row.get('source_name', '')
                            source_type = row.get('source_type', 'unknown')
                            tier = row.get('quality_tier', 'BRONZE')
                            score = row.get('quality_score', 50)

                        if source_name:
                            # Register with quality engine if needed
                            if source_name not in self.backend.log_source_quality.active_sources:
                                source_config = {
                                    'type': source_type,
                                    'tier': tier,
                                    'base_score': score
                                }
                                await self.backend.log_source_quality.register_source(
                                    source_name, source_config
                                )

                            available_sources.append(source_name)

            # Assess capability
            capability = {}
            if available_sources:
                capability = self.backend.log_source_quality.assess_correlation_capability(available_sources)

            await self._send_response(request_id, {
                'request_id': request_id,
                'status': 'success',
                'source_count': len(available_sources),
                'sources': available_sources,
                'capability': capability
            })

        except Exception as e:
            self.logger.error(f"Correlation capability handler error: {e}")
            await self._send_error_response(request_id, str(e))

    async def _handle_correlation_requirements(self, data: Dict[str, Any]):
        """Handle correlation requirements"""
        request_id = data.get('request_id', str(uuid.uuid4()))

        try:
            attack_type = data.get('attack_type')
            if not attack_type:
                raise ValueError("attack_type is required")

            # Get requirements
            requirements = self.backend.correlation_requirements.attack_requirements.get(attack_type, {})

            # Check current sources
            cursor = self.backend.db_connection.cursor()
            cursor.execute("SELECT source_name FROM log_sources")

            current_sources = []
            rows = cursor.fetchall()
            if rows:
                for row in rows:
                    source_name = row[0] if isinstance(row, (tuple, list)) else row.get('source_name', '')
                    if source_name:
                        current_sources.append(source_name)

            # Check which requirements are met
            met_requirements = []
            missing_requirements = []

            for req in requirements.get('required_sources', []):
                category = req.get('category')
                min_tier = req.get('min_tier')

                # Check if we have a source matching this requirement
                has_match = False
                for source in current_sources:
                    if self.backend._source_matches_requirement(source, category, min_tier):
                        has_match = True
                        met_requirements.append({
                            'requirement': req,
                            'matched_source': source
                        })
                        break

                if not has_match:
                    missing_requirements.append(req)

            await self._send_response(request_id, {
                'request_id': request_id,
                'status': 'success',
                'attack_type': attack_type,
                'requirements': requirements,
                'met_requirements': met_requirements,
                'missing_requirements': missing_requirements,
                'coverage_complete': len(missing_requirements) == 0
            })

        except Exception as e:
            self.logger.error(f"Correlation requirements handler error: {e}")
            await self._send_error_response(request_id, str(e))

    async def _handle_recommendations(self, data: Dict[str, Any]):
        """Handle source recommendations"""
        request_id = data.get('request_id', str(uuid.uuid4()))

        try:
            target_attacks = data.get('target_attacks', [])
            if not target_attacks:
                raise ValueError("target_attacks list is required")

            # Get current sources
            cursor = self.backend.db_connection.cursor()
            cursor.execute("SELECT source_name FROM log_sources")

            current_sources = []
            rows = cursor.fetchall()
            if rows:
                for row in rows:
                    source_name = row[0] if isinstance(row, (tuple, list)) else row.get('source_name', '')
                    if source_name:
                        current_sources.append(source_name)

            # Get recommendations
            recommendations = self.backend.correlation_requirements.recommend_sources(
                target_attacks, current_sources
            )

            await self._send_response(request_id, {
                'request_id': request_id,
                'status': 'success',
                'recommendations': recommendations
            })

        except Exception as e:
            self.logger.error(f"Recommendations handler error: {e}")
            await self._send_error_response(request_id, str(e))

    async def _handle_coverage_gaps(self, data: Dict[str, Any]):
        """Handle coverage gap analysis"""
        request_id = data.get('request_id', str(uuid.uuid4()))

        try:
            # Get current sources
            cursor = self.backend.db_connection.cursor()
            cursor.execute("SELECT source_name FROM log_sources")

            current_sources = []
            rows = cursor.fetchall()
            if rows:
                for row in rows:
                    source_name = row[0] if isinstance(row, (tuple, list)) else row.get('source_name', '')
                    if source_name:
                        current_sources.append(source_name)

            # Analyze gaps for all attack types
            gaps = {}
            all_attacks = list(self.backend.correlation_requirements.attack_requirements.keys())

            for attack_type in all_attacks:
                # Calculate current fidelity
                fidelity = self.backend.detection_fidelity.calculate_detection_fidelity(
                    [attack_type], current_sources
                )
                confidence = fidelity.get('attack_fidelity', {}).get(attack_type, {}).get('confidence', 0)

                if confidence < 80:  # Consider <80% as a gap
                    requirements = self.backend.correlation_requirements.attack_requirements.get(attack_type, {})
                    missing = []

                    for req in requirements.get('required_sources', []):
                        has_match = False
                        for source in current_sources:
                            if self.backend._source_matches_requirement(
                                source, req.get('category'), req.get('min_tier')
                            ):
                                has_match = True
                                break
                        if not has_match:
                            missing.append(req)

                    gaps[attack_type] = {
                        'current_confidence': confidence,
                        'target_confidence': 80,
                        'missing_sources': missing,
                        'severity': 'critical' if confidence < 40 else 'high' if confidence < 60 else 'medium'
                    }

            await self._send_response(request_id, {
                'request_id': request_id,
                'status': 'success',
                'gaps': gaps,
                'total_gaps': len(gaps),
                'critical_gaps': sum(1 for g in gaps.values() if g['severity'] == 'critical')
            })

        except Exception as e:
            self.logger.error(f"Coverage gaps handler error: {e}")
            await self._send_error_response(request_id, str(e))

    async def _handle_simulate(self, data: Dict[str, Any]):
        """Handle coverage simulation"""
        request_id = data.get('request_id', str(uuid.uuid4()))

        try:
            add_sources = data.get('add_sources', [])
            remove_sources = data.get('remove_sources', [])

            # Get current sources
            cursor = self.backend.db_connection.cursor()
            cursor.execute("SELECT source_name FROM log_sources")

            current_sources = []
            rows = cursor.fetchall()
            if rows:
                for row in rows:
                    source_name = row[0] if isinstance(row, (tuple, list)) else row.get('source_name', '')
                    if source_name:
                        current_sources.append(source_name)

            # Simulate changes
            simulated_sources = current_sources.copy()

            # Add new sources
            for source in add_sources:
                source_name = source.get('name')
                if source_name and source_name not in simulated_sources:
                    simulated_sources.append(source_name)
                    # Register temporarily with quality engine
                    await self.backend.log_source_quality.register_source(
                        source_name,
                        {'type': source.get('category', 'unknown'),
                         'tier': source.get('tier', 'BRONZE')}
                    )

            # Remove sources
            for source_name in remove_sources:
                if source_name in simulated_sources:
                    simulated_sources.remove(source_name)

            # Calculate new coverage
            all_attacks = list(self.backend.correlation_requirements.attack_requirements.keys())
            before_coverage = {}
            after_coverage = {}

            for attack_type in all_attacks:
                # Before
                before_fidelity = self.backend.detection_fidelity.calculate_detection_fidelity(
                    [attack_type], current_sources
                )
                before_conf = before_fidelity.get('attack_fidelity', {}).get(attack_type, {}).get('confidence', 0)
                before_coverage[attack_type] = before_conf

                # After
                after_fidelity = self.backend.detection_fidelity.calculate_detection_fidelity(
                    [attack_type], simulated_sources
                )
                after_conf = after_fidelity.get('attack_fidelity', {}).get(attack_type, {}).get('confidence', 0)
                after_coverage[attack_type] = after_conf

            # Calculate impact
            improvements = {k: after_coverage[k] - before_coverage[k]
                          for k in all_attacks if after_coverage[k] > before_coverage[k]}
            degradations = {k: before_coverage[k] - after_coverage[k]
                          for k in all_attacks if after_coverage[k] < before_coverage[k]}

            await self._send_response(request_id, {
                'request_id': request_id,
                'status': 'success',
                'simulation': {
                    'before': {
                        'source_count': len(current_sources),
                        'average_confidence': sum(before_coverage.values()) / len(before_coverage) if before_coverage else 0
                    },
                    'after': {
                        'source_count': len(simulated_sources),
                        'average_confidence': sum(after_coverage.values()) / len(after_coverage) if after_coverage else 0
                    },
                    'improvements': improvements,
                    'degradations': degradations,
                    'net_change': sum(after_coverage.values()) - sum(before_coverage.values())
                }
            })

        except Exception as e:
            self.logger.error(f"Simulate handler error: {e}")
            await self._send_error_response(request_id, str(e))