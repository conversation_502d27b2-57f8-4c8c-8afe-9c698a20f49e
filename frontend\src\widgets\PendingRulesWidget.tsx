import React, { useEffect, useState } from 'react'
import { useRuleStore } from '../stores/ruleStore'
import { wsClient } from '../api/client'
import { PendingRule } from '../api/types/api'
import {
  CheckCircle, XCircle, Edit, Eye, Filter,
  Shield, AlertTriangle, RefreshCw, ChevronDown
} from 'lucide-react'

/**
 * Pending Rules Widget
 *
 * Purpose: Review and approve CTI-generated detection rules
 *
 * Features:
 * - List pending rules from CTI sources (OTX, ThreatFox, CrowdStrike, OpenCTI)
 * - Filter by source, quality score, indicator type
 * - Preview Sigma rule + translated SIEM formats
 * - View original CTI indicator context
 * - Approve/Reject/Edit workflow
 * - Bulk actions for multiple rules
 * - Real-time updates via WebSocket
 */

const PendingRulesWidget: React.FC = () => {
  const {
    pendingRules,
    selectedPendingRule,
    loading,
    error,
    fetchPendingRules,
    selectPendingRule,
    approvePendingRule,
    rejectPendingRule,
    setPendingFilters
  } = useRuleStore()

  const [showFilters, setShowFilters] = useState(false)
  const [selectedRuleIds, setSelectedRuleIds] = useState<Set<string>>(new Set())
  const [previewFormat, setPreviewFormat] = useState<'sigma' | 'splunk' | 'elastic' | 'sentinel'>('sigma')

  const [filters, setFilters] = useState({
    cti_source: '',
    quality_min: 70,
    indicator_type: ''
  })

  // Initial load
  useEffect(() => {
    fetchPendingRules()
  }, [fetchPendingRules])

  // WebSocket updates for new rules
  useEffect(() => {
    const handleRuleGenerated = (data: any) => {
      if (data.type === 'rule.generated') {
        fetchPendingRules()
      }
    }

    wsClient.on('rule.generated', handleRuleGenerated)

    return () => {
      wsClient.off('rule.generated', handleRuleGenerated)
    }
  }, [fetchPendingRules])

  const handleApprove = async (pendingId: string) => {
    const success = await approvePendingRule(pendingId)
    if (success) {
      setSelectedRuleIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(pendingId)
        return newSet
      })
    }
  }

  const handleReject = async (pendingId: string) => {
    const reason = prompt('Rejection reason (optional):')
    const success = await rejectPendingRule(pendingId, reason || undefined)
    if (success) {
      setSelectedRuleIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(pendingId)
        return newSet
      })
    }
  }

  const handleBulkApprove = async () => {
    const count = await useRuleStore.getState().bulkApprovePendingRules(Array.from(selectedRuleIds))
    if (count > 0) {
      setSelectedRuleIds(new Set())
    }
  }

  const applyFilters = () => {
    setPendingFilters(filters)
    fetchPendingRules(filters)
    setShowFilters(false)
  }

  const getQualityColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-50 border-green-200'
    if (score >= 75) return 'text-blue-600 bg-blue-50 border-blue-200'
    if (score >= 60) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
    return 'text-red-600 bg-red-50 border-red-200'
  }

  const getSourceIcon = (source: string) => {
    const icons: Record<string, string> = {
      otx: '🌐',
      threatfox: '🦊',
      crowdstrike: '🦅',
      opencti: '🔍'
    }
    return icons[source] || '📡'
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <Shield className="text-blue-500" size={20} />
              CTI-Generated Rules (Pending Approval)
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              Review and approve detection rules generated from threat intelligence
            </p>
          </div>

          <div className="flex items-center gap-2">
            {selectedRuleIds.size > 0 && (
              <>
                <span className="text-sm text-gray-600">
                  {selectedRuleIds.size} selected
                </span>
                <button
                  onClick={handleBulkApprove}
                  disabled={loading.bulkAction}
                  className="px-3 py-1.5 bg-green-500 text-white rounded hover:bg-green-600 text-sm flex items-center gap-1 disabled:opacity-50"
                >
                  <CheckCircle size={16} />
                  Approve All
                </button>
              </>
            )}

            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`p-2 rounded hover:bg-gray-100 ${showFilters ? 'bg-gray-100' : ''}`}
            >
              <Filter size={18} />
            </button>

            <button
              onClick={() => fetchPendingRules()}
              disabled={loading.pendingRules}
              className="p-2 rounded hover:bg-gray-100 disabled:opacity-50"
            >
              <RefreshCw size={18} className={loading.pendingRules ? 'animate-spin' : ''} />
            </button>
          </div>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  CTI Source
                </label>
                <select
                  value={filters.cti_source}
                  onChange={(e) => setFilters({ ...filters, cti_source: e.target.value })}
                  className="w-full px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Sources</option>
                  <option value="otx">OTX (AlienVault)</option>
                  <option value="threatfox">ThreatFox</option>
                  <option value="crowdstrike">CrowdStrike</option>
                  <option value="opencti">OpenCTI</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Minimum Quality Score
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={filters.quality_min}
                  onChange={(e) => setFilters({ ...filters, quality_min: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Indicator Type
                </label>
                <select
                  value={filters.indicator_type}
                  onChange={(e) => setFilters({ ...filters, indicator_type: e.target.value })}
                  className="w-full px-3 py-2 border rounded focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Types</option>
                  <option value="ip">IP Address</option>
                  <option value="domain">Domain</option>
                  <option value="hash">File Hash</option>
                  <option value="url">URL</option>
                  <option value="email">Email</option>
                </select>
              </div>
            </div>

            <div className="mt-3 flex justify-end gap-2">
              <button
                onClick={() => setShowFilters(false)}
                className="px-3 py-1.5 text-gray-600 hover:bg-gray-200 rounded text-sm"
              >
                Cancel
              </button>
              <button
                onClick={applyFilters}
                className="px-3 py-1.5 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
              >
                Apply Filters
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-hidden flex">
        {/* Rule List */}
        <div className="w-1/2 border-r overflow-y-auto">
          {loading.pendingRules && pendingRules.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <RefreshCw className="animate-spin text-gray-400" size={24} />
            </div>
          ) : error.pendingRules ? (
            <div className="p-6 text-center text-red-600">
              <AlertTriangle className="mx-auto mb-2" size={24} />
              <p>{error.pendingRules}</p>
            </div>
          ) : pendingRules.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <Shield className="mx-auto mb-2" size={32} />
              <p>No pending rules</p>
              <p className="text-sm mt-1">
                New rules will appear here as CTI indicators are processed
              </p>
            </div>
          ) : (
            <div className="divide-y">
              {pendingRules.map((rule) => (
                <div
                  key={rule.pending_id}
                  className={`p-4 hover:bg-gray-50 cursor-pointer ${
                    selectedPendingRule?.pending_id === rule.pending_id ? 'bg-blue-50' : ''
                  }`}
                  onClick={() => selectPendingRule(rule.pending_id)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-start gap-3">
                      <input
                        type="checkbox"
                        checked={selectedRuleIds.has(rule.pending_id)}
                        onChange={(e) => {
                          e.stopPropagation()
                          setSelectedRuleIds(prev => {
                            const newSet = new Set(prev)
                            if (e.target.checked) {
                              newSet.add(rule.pending_id)
                            } else {
                              newSet.delete(rule.pending_id)
                            }
                            return newSet
                          })
                        }}
                        className="mt-1"
                      />
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{getSourceIcon(rule.cti_source)}</span>
                          <span className="font-medium">
                            {rule.cti_indicator.metadata.threat_actor || rule.cti_indicator.metadata.malware_family || 'Threat'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          {rule.cti_indicator.value} ({rule.cti_indicator.indicator_type.toUpperCase()})
                        </p>
                      </div>
                    </div>

                    <div className={`px-2 py-1 rounded text-xs font-medium border ${getQualityColor(rule.quality_score)}`}>
                      {rule.quality_score}/100
                    </div>
                  </div>

                  <div className="flex items-center gap-4 text-xs text-gray-500 ml-8">
                    <span>Source: {rule.cti_source.toUpperCase()}</span>
                    <span>Threat Score: {rule.cti_indicator.threat_score}/100</span>
                    <span>Generated: {new Date(rule.generated_at).toLocaleDateString()}</span>
                  </div>

                  {rule.cti_indicator.metadata.campaign && (
                    <div className="mt-2 ml-8">
                      <span className="inline-block px-2 py-0.5 bg-purple-100 text-purple-700 text-xs rounded">
                        Campaign: {rule.cti_indicator.metadata.campaign}
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Rule Detail Panel */}
        <div className="w-1/2 overflow-y-auto bg-white">
          {!selectedPendingRule ? (
            <div className="flex items-center justify-center h-full text-gray-400">
              <div className="text-center">
                <Eye size={48} className="mx-auto mb-2" />
                <p>Select a rule to view details</p>
              </div>
            </div>
          ) : (
            <div className="p-6">
              {/* CTI Indicator Context */}
              <div className="mb-6">
                <h3 className="font-semibold mb-3 flex items-center gap-2">
                  <span className="text-lg">{getSourceIcon(selectedPendingRule.cti_source)}</span>
                  Original CTI Indicator
                </h3>
                <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-gray-500">Type:</span>
                      <span className="ml-2 font-medium">{selectedPendingRule.cti_indicator.indicator_type.toUpperCase()}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Value:</span>
                      <span className="ml-2 font-mono text-xs">{selectedPendingRule.cti_indicator.value}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Threat Score:</span>
                      <span className="ml-2 font-medium">{selectedPendingRule.cti_indicator.threat_score}/100</span>
                    </div>
                    <div>
                      <span className="text-gray-500">Confidence:</span>
                      <span className="ml-2 font-medium">{selectedPendingRule.cti_indicator.confidence}%</span>
                    </div>
                  </div>

                  {selectedPendingRule.cti_indicator.metadata.threat_actor && (
                    <div className="pt-2 border-t">
                      <span className="text-gray-500 text-sm">Threat Actor:</span>
                      <span className="ml-2 font-medium">{selectedPendingRule.cti_indicator.metadata.threat_actor}</span>
                    </div>
                  )}

                  {selectedPendingRule.cti_indicator.metadata.malware_family && (
                    <div>
                      <span className="text-gray-500 text-sm">Malware Family:</span>
                      <span className="ml-2 font-medium">{selectedPendingRule.cti_indicator.metadata.malware_family}</span>
                    </div>
                  )}

                  {selectedPendingRule.cti_indicator.metadata.campaign && (
                    <div>
                      <span className="text-gray-500 text-sm">Campaign:</span>
                      <span className="ml-2 font-medium">{selectedPendingRule.cti_indicator.metadata.campaign}</span>
                    </div>
                  )}

                  {selectedPendingRule.cti_indicator.metadata.tags && selectedPendingRule.cti_indicator.metadata.tags.length > 0 && (
                    <div className="pt-2">
                      <span className="text-gray-500 text-sm block mb-1">Tags:</span>
                      <div className="flex flex-wrap gap-1">
                        {selectedPendingRule.cti_indicator.metadata.tags.map((tag, idx) => (
                          <span key={idx} className="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Generated Rule */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold">Generated Detection Rule</h3>
                  <div className="flex items-center gap-2">
                    <select
                      value={previewFormat}
                      onChange={(e) => setPreviewFormat(e.target.value as any)}
                      className="px-2 py-1 border rounded text-sm"
                    >
                      <option value="sigma">Sigma (Universal)</option>
                      <option value="splunk">Splunk SPL</option>
                      <option value="elastic">Elastic KQL</option>
                      <option value="sentinel">Sentinel KQL</option>
                    </select>
                  </div>
                </div>

                <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-xs">
                  {previewFormat === 'sigma'
                    ? selectedPendingRule.sigma_rule
                    : selectedPendingRule.translated_rules[previewFormat] || 'Translation not available'}
                </pre>
              </div>

              {/* Quality & Test Cases */}
              <div className="mb-6">
                <h3 className="font-semibold mb-3">Quality Assessment</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Quality Score</span>
                    <span className={`px-2 py-1 rounded text-sm font-medium ${getQualityColor(selectedPendingRule.quality_score)}`}>
                      {selectedPendingRule.quality_score}/100
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Generation Method</span>
                    <span className="text-sm">{selectedPendingRule.generation_method.replace('_', ' ').toUpperCase()}</span>
                  </div>
                  {selectedPendingRule.ai_model && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">AI Model</span>
                      <span className="text-sm">{selectedPendingRule.ai_model}</span>
                    </div>
                  )}
                </div>

                {selectedPendingRule.test_cases && selectedPendingRule.test_cases.length > 0 && (
                  <div className="mt-3">
                    <span className="text-sm text-gray-600">Test Cases:</span>
                    <div className="mt-2 space-y-1">
                      {selectedPendingRule.test_cases.map((test, idx) => (
                        <div key={idx} className="text-sm flex items-start gap-2">
                          <CheckCircle size={16} className="text-green-500 mt-0.5" />
                          <span>{test.description}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center gap-3 pt-4 border-t">
                <button
                  onClick={() => handleApprove(selectedPendingRule.pending_id)}
                  disabled={loading.approve}
                  className="flex-1 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 flex items-center justify-center gap-2 disabled:opacity-50"
                >
                  <CheckCircle size={18} />
                  Approve
                </button>

                <button
                  onClick={() => {/* TODO: Edit modal */}}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 flex items-center gap-2"
                >
                  <Edit size={18} />
                  Edit
                </button>

                <button
                  onClick={() => handleReject(selectedPendingRule.pending_id)}
                  disabled={loading.reject}
                  className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 flex items-center gap-2 disabled:opacity-50"
                >
                  <XCircle size={18} />
                  Reject
                </button>
              </div>

              {error.approve && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded text-red-600 text-sm">
                  {error.approve}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default PendingRulesWidget
