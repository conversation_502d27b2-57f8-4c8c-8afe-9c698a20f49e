"""
Cloud Update Preview System
Preview changes before applying updates from cloud sources

Features:
- Preview diffs for MITRE ATT&CK updates
- Preview Sigma rule changes
- Preview CTI feed updates
- Approve/reject mechanism
- Rollback capability
"""

import json
import hashlib
from typing import Dict, List, Optional
from datetime import datetime
from dataclasses import dataclass
import logging
import difflib


@dataclass
class UpdatePreview:
    """Update preview"""
    id: str
    source: str  # 'mitre', 'sigma', 'otx', 'elastic_rules'
    update_type: str  # 'new', 'modified', 'deleted'
    version_current: str
    version_new: str
    changes_summary: Dict
    diff: str
    risk_level: str  # 'low', 'medium', 'high'
    requires_approval: bool
    approved: bool
    applied: bool
    created_at: datetime


class CloudUpdatePreview:
    """
    Manages preview and approval of cloud updates
    - Shows diffs before applying
    - Risk assessment
    - Approval workflow
    - Rollback capability
    """

    def __init__(self, db_connection, logger: Optional[logging.Logger] = None):
        self.db = db_connection
        self.logger = logger or logging.getLogger(__name__)

    async def preview_mitre_update(self, current_version: str, new_data: Dict) -> UpdatePreview:
        """
        Preview MITRE ATT&CK framework update

        Args:
            current_version: Current MITRE version
            new_data: New framework data from GitHub

        Returns:
            UpdatePreview with diff and risk assessment
        """
        # Load current framework
        current_data = await self._load_current_mitre_framework()

        if not current_data:
            # First install
            return UpdatePreview(
                id=self._generate_preview_id(),
                source='mitre',
                update_type='new',
                version_current='none',
                version_new=new_data.get('spec_version', 'unknown'),
                changes_summary={
                    'new_techniques': len(new_data.get('objects', [])),
                    'modified_techniques': 0,
                    'deleted_techniques': 0
                },
                diff='Initial MITRE ATT&CK installation',
                risk_level='low',
                requires_approval=False,
                approved=True,
                applied=False,
                created_at=datetime.utcnow()
            )

        # Compare versions
        changes = self._compare_mitre_versions(current_data, new_data)

        # Generate diff
        diff = self._generate_mitre_diff(changes)

        # Assess risk
        risk_level = self._assess_mitre_update_risk(changes)

        preview = UpdatePreview(
            id=self._generate_preview_id(),
            source='mitre',
            update_type='modified',
            version_current=current_data.get('spec_version', 'unknown'),
            version_new=new_data.get('spec_version', 'unknown'),
            changes_summary={
                'new_techniques': len(changes.get('new_techniques', [])),
                'modified_techniques': len(changes.get('modified_techniques', [])),
                'deleted_techniques': len(changes.get('deleted_techniques', [])),
                'deprecated_techniques': len(changes.get('deprecated_techniques', []))
            },
            diff=diff,
            risk_level=risk_level,
            requires_approval=risk_level in ['medium', 'high'],
            approved=False,
            applied=False,
            created_at=datetime.utcnow()
        )

        # Store preview
        await self._store_preview(preview)

        return preview

    def _compare_mitre_versions(self, current: Dict, new: Dict) -> Dict:
        """Compare MITRE framework versions"""
        current_techniques = {
            obj['id']: obj for obj in current.get('objects', [])
            if obj.get('type') == 'attack-pattern'
        }

        new_techniques = {
            obj['id']: obj for obj in new.get('objects', [])
            if obj.get('type') == 'attack-pattern'
        }

        changes = {
            'new_techniques': [],
            'modified_techniques': [],
            'deleted_techniques': [],
            'deprecated_techniques': []
        }

        # Find new techniques
        for tech_id, tech_data in new_techniques.items():
            if tech_id not in current_techniques:
                changes['new_techniques'].append({
                    'id': tech_id,
                    'name': tech_data.get('name'),
                    'tactics': [k.get('phase_name') for k in tech_data.get('kill_chain_phases', [])]
                })

        # Find modified techniques
        for tech_id in current_techniques:
            if tech_id in new_techniques:
                if current_techniques[tech_id] != new_techniques[tech_id]:
                    changes['modified_techniques'].append({
                        'id': tech_id,
                        'name': new_techniques[tech_id].get('name'),
                        'changes': self._describe_technique_changes(
                            current_techniques[tech_id],
                            new_techniques[tech_id]
                        )
                    })

                # Check if deprecated
                if new_techniques[tech_id].get('x_mitre_deprecated'):
                    changes['deprecated_techniques'].append({
                        'id': tech_id,
                        'name': new_techniques[tech_id].get('name')
                    })

        # Find deleted techniques
        for tech_id in current_techniques:
            if tech_id not in new_techniques:
                changes['deleted_techniques'].append({
                    'id': tech_id,
                    'name': current_techniques[tech_id].get('name')
                })

        return changes

    def _describe_technique_changes(self, old: Dict, new: Dict) -> List[str]:
        """Describe what changed in a technique"""
        changes = []

        if old.get('name') != new.get('name'):
            changes.append(f"Name: '{old.get('name')}' -> '{new.get('name')}'")

        if old.get('description') != new.get('description'):
            changes.append("Description updated")

        old_tactics = {k.get('phase_name') for k in old.get('kill_chain_phases', [])}
        new_tactics = {k.get('phase_name') for k in new.get('kill_chain_phases', [])}

        if old_tactics != new_tactics:
            added = new_tactics - old_tactics
            removed = old_tactics - new_tactics
            if added:
                changes.append(f"Tactics added: {', '.join(added)}")
            if removed:
                changes.append(f"Tactics removed: {', '.join(removed)}")

        return changes

    def _generate_mitre_diff(self, changes: Dict) -> str:
        """Generate human-readable diff"""
        diff_lines = []

        if changes['new_techniques']:
            diff_lines.append(f"\n+ NEW TECHNIQUES ({len(changes['new_techniques'])})")
            for tech in changes['new_techniques'][:10]:  # Show first 10
                diff_lines.append(f"  + {tech['id']}: {tech['name']}")
                diff_lines.append(f"    Tactics: {', '.join(tech['tactics'])}")

        if changes['modified_techniques']:
            diff_lines.append(f"\n~ MODIFIED TECHNIQUES ({len(changes['modified_techniques'])})")
            for tech in changes['modified_techniques'][:10]:
                diff_lines.append(f"  ~ {tech['id']}: {tech['name']}")
                for change in tech['changes']:
                    diff_lines.append(f"    - {change}")

        if changes['deprecated_techniques']:
            diff_lines.append(f"\n! DEPRECATED TECHNIQUES ({len(changes['deprecated_techniques'])})")
            for tech in changes['deprecated_techniques'][:10]:
                diff_lines.append(f"  ! {tech['id']}: {tech['name']}")

        if changes['deleted_techniques']:
            diff_lines.append(f"\n- DELETED TECHNIQUES ({len(changes['deleted_techniques'])})")
            for tech in changes['deleted_techniques'][:10]:
                diff_lines.append(f"  - {tech['id']}: {tech['name']}")

        return '\n'.join(diff_lines)

    def _assess_mitre_update_risk(self, changes: Dict) -> str:
        """Assess risk level of MITRE update"""
        # High risk: Many deleted or deprecated techniques
        if len(changes['deleted_techniques']) > 10 or len(changes['deprecated_techniques']) > 10:
            return 'high'

        # Medium risk: Significant modifications
        if len(changes['modified_techniques']) > 50:
            return 'medium'

        # Low risk: Minor updates
        return 'low'

    async def preview_sigma_rule_update(self, rule_id: str, current_rule: Dict, new_rule: Dict) -> UpdatePreview:
        """
        Preview Sigma rule update

        Args:
            rule_id: Rule identifier
            current_rule: Current rule data
            new_rule: New rule data from cloud source

        Returns:
            UpdatePreview with diff
        """
        # Generate diff
        current_yaml = json.dumps(current_rule, indent=2, sort_keys=True)
        new_yaml = json.dumps(new_rule, indent=2, sort_keys=True)

        diff = '\n'.join(difflib.unified_diff(
            current_yaml.splitlines(),
            new_yaml.splitlines(),
            fromfile='current',
            tofile='new',
            lineterm=''
        ))

        # Assess risk based on changes
        risk_level = self._assess_rule_update_risk(current_rule, new_rule)

        preview = UpdatePreview(
            id=self._generate_preview_id(),
            source='sigma',
            update_type='modified',
            version_current=current_rule.get('modified', ''),
            version_new=new_rule.get('modified', ''),
            changes_summary={
                'title_changed': current_rule.get('title') != new_rule.get('title'),
                'detection_changed': current_rule.get('detection') != new_rule.get('detection'),
                'severity_changed': current_rule.get('level') != new_rule.get('level')
            },
            diff=diff,
            risk_level=risk_level,
            requires_approval=risk_level in ['medium', 'high'],
            approved=False,
            applied=False,
            created_at=datetime.utcnow()
        )

        await self._store_preview(preview)

        return preview

    def _assess_rule_update_risk(self, current: Dict, new: Dict) -> str:
        """Assess risk of rule update"""
        # High risk: Detection logic changed
        if current.get('detection') != new.get('detection'):
            return 'high'

        # Medium risk: Severity changed
        if current.get('level') != new.get('level'):
            return 'medium'

        # Low risk: Description or metadata changes
        return 'low'

    async def approve_update(self, preview_id: str, approver: str) -> bool:
        """
        Approve an update preview

        Args:
            preview_id: Preview identifier
            approver: Who approved it

        Returns:
            Success status
        """
        if not self.db:
            return False

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                UPDATE cloud_update_previews
                SET approved = TRUE,
                    approved_by = %s,
                    approved_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (approver, preview_id))

            self.db.commit()

            self.logger.info(f"Update {preview_id} approved by {approver}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to approve update: {e}")
            return False

    async def reject_update(self, preview_id: str, reason: str) -> bool:
        """Reject an update"""
        if not self.db:
            return False

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                UPDATE cloud_update_previews
                SET rejected = TRUE,
                    rejection_reason = %s,
                    rejected_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (reason, preview_id))

            self.db.commit()

            self.logger.info(f"Update {preview_id} rejected: {reason}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to reject update: {e}")
            return False

    async def apply_update(self, preview_id: str) -> bool:
        """
        Apply approved update

        This would trigger the actual update process
        """
        preview = await self._get_preview(preview_id)

        if not preview or not preview.approved:
            self.logger.error(f"Cannot apply unapproved update: {preview_id}")
            return False

        # Mark as applied
        if self.db:
            cursor = self.db.cursor()
            cursor.execute("""
                UPDATE cloud_update_previews
                SET applied = TRUE,
                    applied_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """, (preview_id,))
            self.db.commit()

        # Publish to update channel
        # The actual update would be handled by respective engines
        self.logger.info(f"Update {preview_id} applied")

        return True

    async def rollback_update(self, preview_id: str) -> bool:
        """
        Rollback an applied update

        This would restore previous version
        """
        # Implementation would restore from backup
        self.logger.info(f"Rolling back update {preview_id}")
        return True

    # Helper methods
    async def _load_current_mitre_framework(self) -> Optional[Dict]:
        """Load current MITRE framework from database"""
        if not self.db:
            return None

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT data
                FROM mitre_attack_framework
                ORDER BY updated_at DESC
                LIMIT 1
            """)

            row = cursor.fetchone()
            return row[0] if row else None

        except Exception as e:
            self.logger.error(f"Failed to load MITRE framework: {e}")
            return None

    async def _store_preview(self, preview: UpdatePreview):
        """Store preview in database"""
        if not self.db:
            return

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO cloud_update_previews
                (id, source, update_type, version_current, version_new,
                 changes_summary, diff, risk_level, requires_approval, approved, applied, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                preview.id,
                preview.source,
                preview.update_type,
                preview.version_current,
                preview.version_new,
                json.dumps(preview.changes_summary),
                preview.diff,
                preview.risk_level,
                preview.requires_approval,
                preview.approved,
                preview.applied,
                preview.created_at
            ))
            self.db.commit()

        except Exception as e:
            self.logger.error(f"Failed to store preview: {e}")

    async def _get_preview(self, preview_id: str) -> Optional[UpdatePreview]:
        """Get preview by ID"""
        if not self.db:
            return None

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT id, source, update_type, version_current, version_new,
                       changes_summary, diff, risk_level, requires_approval,
                       approved, applied, created_at
                FROM cloud_update_previews
                WHERE id = %s
            """, (preview_id,))

            row = cursor.fetchone()
            if row:
                return UpdatePreview(
                    id=row[0],
                    source=row[1],
                    update_type=row[2],
                    version_current=row[3],
                    version_new=row[4],
                    changes_summary=row[5],
                    diff=row[6],
                    risk_level=row[7],
                    requires_approval=row[8],
                    approved=row[9],
                    applied=row[10],
                    created_at=row[11]
                )

            return None

        except Exception as e:
            self.logger.error(f"Failed to get preview: {e}")
            return None

    def _generate_preview_id(self) -> str:
        """Generate unique preview ID"""
        import uuid
        return str(uuid.uuid4())
