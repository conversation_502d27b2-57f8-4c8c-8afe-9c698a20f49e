"""
View detailed enhancement responses from all AI models
Shows actual content so you can judge quality yourself
"""

import json
import sys
import os

# Add paths
sys.path.append(os.path.join(os.path.dirname(__file__), 'engines'))

from ai_models import AIModelManager
from sigma_enhancement import SigmaRuleEnhancer
import logging
import asyncio

# Setup logging
logging.basicConfig(level=logging.WARNING)  # Suppress info logs
logger = logging.getLogger(__name__)

# Real Sigma rule for testing
POWERSHELL_RULE = {
    'title': 'Suspicious PowerShell Encoded Command',
    'id': 'f4bbd493-b796-416e-bbf2-121235348529',
    'description': 'Detects PowerShell execution with encoded commands (-enc, -e, -EncodedCommand)',
    'tags': ['attack.t1059.001', 'attack.execution', 'attack.defense_evasion'],
    'level': 'high',
    'logsource': {
        'product': 'windows',
        'service': 'powershell',
        'category': 'process_creation'
    },
    'detection': {
        'selection': {
            'Image|endswith': 'powershell.exe',
            'CommandLine|contains': ['-enc', '-e ', '-EncodedCommand']
        },
        'condition': 'selection'
    }
}


def print_divider(char="=", length=100):
    print(char * length)


def print_section(title):
    print("\n" + "=" * 100)
    print(f"  {title}")
    print("=" * 100)


def print_enhancement_details(model_name, cost, enhancement):
    """Print full enhancement details from a model"""

    print(f"\n{'='*100}")
    print(f"MODEL: {model_name} (Cost: {cost})")
    print(f"{'='*100}")

    # Overall Assessment
    assessment = enhancement.get('overall_assessment', {})
    print(f"\nOVERALL QUALITY:")
    print(f"  Original: {assessment.get('original_quality', 'N/A')}")
    print(f"  Enhanced: {assessment.get('enhanced_quality', 'N/A')}")
    print(f"  Improvement: {assessment.get('improvement_summary', 'N/A')}")

    # Evasion Variants
    evasions = enhancement.get('evasion_variants', [])
    print(f"\n1. EVASION VARIANTS ({len(evasions)} total):")
    print("-" * 100)
    for i, evasion in enumerate(evasions, 1):
        print(f"\n  [{i}] {evasion.get('technique', 'N/A')}")
        print(f"      Confidence: {evasion.get('confidence', 0)*100:.0f}%")
        print(f"      Description: {evasion.get('description', 'N/A')[:150]}...")
        detection = evasion.get('detection_addition', 'N/A')
        if len(detection) > 200:
            detection = detection[:200] + "..."
        print(f"      Detection Logic:\n{detection}")

    # False Positive Filters
    fp_filters = enhancement.get('false_positive_filters', [])
    print(f"\n2. FALSE POSITIVE FILTERS ({len(fp_filters)} total):")
    print("-" * 100)
    for i, fp in enumerate(fp_filters, 1):
        print(f"\n  [{i}] {fp.get('source', 'N/A')}")
        print(f"      Reason: {fp.get('reason', 'N/A')[:150]}...")
        filter_logic = fp.get('filter_logic', 'N/A')
        if len(filter_logic) > 200:
            filter_logic = filter_logic[:200] + "..."
        print(f"      Filter Logic:\n{filter_logic}")

    # Missing Context
    missing = enhancement.get('missing_context', [])
    print(f"\n3. MISSING CONTEXT ({len(missing)} total):")
    print("-" * 100)
    for i, context in enumerate(missing, 1):
        indicator = context.get('indicator', context.get('context', 'N/A'))
        field = context.get('field_name', 'N/A')
        reasoning = context.get('reasoning', context.get('recommendation', 'N/A'))
        importance = context.get('importance', 'N/A')

        print(f"\n  [{i}] {indicator}")
        if importance != 'N/A':
            print(f"      Importance: {importance}")
        print(f"      Field: {field}")
        print(f"      Reasoning: {reasoning[:200]}{'...' if len(str(reasoning)) > 200 else ''}")

    # Platform Optimizations (if any)
    platforms = enhancement.get('platform_optimizations', {})
    if platforms and any(platforms.values()):
        print(f"\n4. PLATFORM OPTIMIZATIONS:")
        print("-" * 100)
        for platform, opts in platforms.items():
            if opts:
                print(f"\n  {platform.upper()}: {len(opts)} optimizations")


async def compare_model_responses():
    """Fetch and compare full responses from all models"""

    print("\n" + "=" * 100)
    print("DETAILED AI MODEL COMPARISON - FULL ENHANCEMENT CONTENT")
    print("Rule: Suspicious PowerShell Encoded Command")
    print("=" * 100)

    # Initialize
    ai_manager = AIModelManager(logger)
    enhancer = SigmaRuleEnhancer(ai_manager, logger)

    # Models to test
    models = [
        ('free', 'Gemma 27B (FREE)', '$0.00'),
        ('low_cost', 'Gemini 2.5 Flash', '$0.002'),
        ('high_quality', 'Gemini 2.5 Pro', '$0.015'),
        ('mid_quality', 'Claude Sonnet 4', '$0.008'),
    ]

    results = {}

    for tier, name, cost in models:
        print(f"\n\nFetching enhancements from {name}...")

        try:
            enhancement = await enhancer.enhance_sigma_rule(
                sigma_rule=POWERSHELL_RULE,
                source_platform='elastic',
                target_platforms=['wazuh', 'splunk', 'sentinel'],
                ai_tier=tier
            )

            results[name] = {
                'cost': cost,
                'enhancement': enhancement
            }

            print(f"SUCCESS - Retrieved {len(enhancement.get('evasion_variants', []))} evasions, "
                  f"{len(enhancement.get('false_positive_filters', []))} FP filters, "
                  f"{len(enhancement.get('missing_context', []))} context items")

        except Exception as e:
            print(f"FAILED: {e}")
            results[name] = {
                'cost': cost,
                'error': str(e)
            }

    # Print detailed comparison
    print("\n\n" + "=" * 100)
    print("DETAILED ENHANCEMENT CONTENT BY MODEL")
    print("=" * 100)

    for model_name, result in results.items():
        if 'error' in result:
            print(f"\n\n{model_name}: FAILED - {result['error']}")
            continue

        print_enhancement_details(
            model_name,
            result['cost'],
            result['enhancement']
        )

    # Summary comparison
    print("\n\n" + "=" * 100)
    print("SIDE-BY-SIDE SUMMARY")
    print("=" * 100)

    print(f"\n{'Model':<25} {'Evasions':<12} {'FP Filters':<12} {'Context':<12} {'Quality':<12}")
    print("-" * 100)

    for model_name, result in results.items():
        if 'error' in result:
            print(f"{model_name:<25} {'FAILED':<12}")
            continue

        enhancement = result['enhancement']
        evasion_count = len(enhancement.get('evasion_variants', []))
        fp_count = len(enhancement.get('false_positive_filters', []))
        context_count = len(enhancement.get('missing_context', []))
        quality = enhancement.get('overall_assessment', {}).get('enhanced_quality', 0)

        print(f"{model_name:<25} {evasion_count:<12} {fp_count:<12} {context_count:<12} {quality:<12.2f}")

    print("\n" + "=" * 100)
    print("Compare the actual enhancement content above to judge quality yourself!")
    print("=" * 100)


if __name__ == "__main__":
    print("\nThis will take ~3-5 minutes to fetch all AI responses...")
    print("You'll see the FULL enhancement content from each model.\n")

    asyncio.run(compare_model_responses())
