# Documentation Cleanup Plan
**Generated**: October 4, 2025
**Total MD Files**: 100+
**Redundant/Obsolete**: ~40 files

## 🎯 Cleanup Strategy

### Keep These (Core Documentation)
- **CLAUDE.md** - Master project instructions ✅
- **README.md** - Primary readme ✅
- **PROJECT_INDEX.md** - Codebase index ✅
- **ARCHITECTURE.md** - Current architecture ✅
- **DATABASE_SCHEMA.md** - Schema reference ✅

### Keep These (Current Status)
- **HANDOFF_NEXT_SESSION.md** - Active handoff (Oct 4) ✅
- **SESSION_SUMMARY_OCT_3_2025.md** - Latest session (Oct 3) ✅
- **INVESTIGATION_LIFECYCLE_STATUS.md** - Current work status ✅
- **AUTHENTICATION_STATUS.md** - Auth implementation status ✅
- **FEATURE_STATUS.md** - Feature tracking ✅

### Keep These (Technical References)
- **COMPLETE_API_REFERENCE.md** - API docs ✅
- **WIDGET_CATALOG.md** - Widget specs ✅
- **INTEGRATION_PATTERNS.md** - Integration patterns ✅
- **SCHEMA_DETECTION_WHY_IT_FAILED_BEFORE.md** - Important technical analysis ✅
- **INTELLIGENCE_ENGINE_FIXES_COMPLETE.md** - Fix reference ✅

### Keep These (Implementation Guides)
- **RULE_MANAGEMENT_API_QUICK_REFERENCE.md** - Quick ref ✅
- **CONTEXT_PLUGIN_IMPLEMENTATION_SUMMARY.md** - Plugin guide ✅
- **PARSER_GENERATION_COMPLETE.md** - Parser guide ✅
- **QUICK_START_GUIDE.md** - User guide ✅

---

## 🗑️ REMOVE - Obsolete Session Summaries (Oct 2 and earlier)
SESSION_SUMMARY.md (Oct 2)
SESSION_SUMMARY_OCT_2_2025.md (Oct 2)
SESSION_SUMMARY_OCT_2_CONTINUATION.md (Oct 2)
SESSION_SUMMARY_INVESTIGATION_CONTEXT.md (Oct 2)
SESSION_COMPLETE_SUMMARY.md (Oct 3 - superseded)
FINAL_SESSION_SUMMARY.md (Oct 2)
FINAL_SESSION_SUMMARY_OCT_2_2025.md (Oct 2)
FINAL_STATUS.md (Oct 2)

**Reason**: Superseded by SESSION_SUMMARY_OCT_3_2025.md and HANDOFF_NEXT_SESSION.md

---

## 🗑️ REMOVE - Duplicate Architecture Docs
ENHANCED_ARCHITECTURE.md (superseded by ARCHITECTURE.md)
FEATURES_AND_ARCHITECTURE_v2.md (old version)
FEATURES_AND_ARCHITECTURE_v2_UPDATED.md (superseded)
DEPLOYMENT_ARCHITECTURE.md (covered in ARCHITECTURE.md)
ARCHITECTURE_FLOWS.md (covered in ARCHITECTURE.md)

**Keep**: ARCHITECTURE.md (primary), specialized ones like LIGHTWEIGHT_ARCHITECTURE.md

---

## 🗑️ REMOVE - Duplicate "Complete" Docs
COMPLETE_IMPLEMENTATION_SUMMARY.md (generic, no unique info)
COMPLETE_WORKFLOW_SUMMARY.md (workflow covered elsewhere)
IMPLEMENTATION_COMPLETE_SUMMARY.md (duplicate)
FRONTEND_FOUNDATION_COMPLETE.md (superseded by current frontend docs)
AI_PLUGIN_FACTORY_COMPLETE.md (functionality covered in plugin docs)
ELASTIC_PLUGIN_COMPLETE.md (covered in context plugin summary)
SIEM_ALERT_POLLING_COMPLETE.md (functionality complete, no longer needed)
ENRICHMENT_INTEGRATION_COMPLETE.md (covered in architecture)

**Keep**: Feature-specific "complete" docs that serve as references

---

## 🗑️ REMOVE - Duplicate CTI Docs
CTI_PLUGIN_SYSTEM_COMPLETE.md (duplicate of architecture complete)
CTI_SEGREGATION_IMPLEMENTATION_COMPLETE.md (implementation done, no longer needed)
CTI_UPDATE_DELEGATION_COMPLETE.md (implementation done)
CTI_FLOW_ANALYSIS.md (analysis complete, findings in CLAUDE.md)
CTI_DATA_FLOW_SEGREGATION.md (implementation complete)

**Keep**: CTI_PLUGIN_ARCHITECTURE_COMPLETE.md (reference), CTI_AGGREGATOR_FINAL_ARCHITECTURE.md

---

## 🗑️ REMOVE - Redundant Schema Detection Docs
SCHEMA_DETECTION_COMPLETE.md (keep the "WHY IT FAILED" one instead)
SCHEMA_DETECTION_IMPLEMENTATION_COMPLETE.md (implementation done)
SESSION_SUMMARY_SCHEMA_DETECTION.md (superseded by session summaries)

**Keep**: SCHEMA_DETECTION_WHY_IT_FAILED_BEFORE.md (valuable analysis)

---

## 🗑️ REMOVE - Duplicate Rule Management Docs
RULE_MANAGEMENT_IMPLEMENTATION_COMPLETE.md (superseded by Phase 2)
RULE_CREATION_WORKFLOW.md (workflow covered in API docs)
RULE_DEPLOYMENT_INTEGRATION.md (integration covered in architecture)

**Keep**: 
- RULE_MANAGEMENT_PHASE_2_COMPLETE.md (latest)
- RULE_MANAGEMENT_API_QUICK_REFERENCE.md (quick ref)
- RULE_MANAGEMENT_ARCHITECTURE.md (architecture)
- RULE_MANAGEMENT_API.md (full API docs)

---

## 🗑️ REMOVE - Duplicate Quick Start Docs
QUICK_START.md (superseded by QUICK_START_GUIDE.md)
QUICK_START_NEXT_SESSION.md (old, superseded by HANDOFF_NEXT_SESSION.md)

**Keep**: QUICK_START_GUIDE.md, HANDOFF_NEXT_SESSION.md

---

## 🗑️ REMOVE - Miscellaneous Obsolete Docs
ARCHITECTURE_FIX_UPDATE_SCHEDULER.md (fix applied, no longer needed)
DATABASE_FIX_SUMMARY.md (fixes applied, documented in CLAUDE.md)
ARCHITECTURAL_FIX_RULE_DEPLOYMENT.md (fix applied)
ARCHITECTURAL_FIX_TEST_RESULTS.md (results documented, tests pass)
TODO_MASTER_TRACKER.md (likely obsolete, check vs PENDING_TASKS.md)
TEST_RESULTS_SUMMARY.md (test results should be in code/logs, not docs)
DOCUMENTATION_SUMMARY.md (meta-doc, likely obsolete)
README_UPDATES_SEP30.md (superseded by current README.md)
README_CONTEXT_PLUGINS.md (covered in plugin docs)

---

## 📊 Summary

**Total Files to Remove**: ~40 files
**Disk Space Saved**: ~500KB (mostly small docs, but reduces clutter)
**Benefit**: Much clearer documentation structure

**Categories**:
- Obsolete session summaries: 8 files
- Duplicate architectures: 5 files
- Duplicate "complete" docs: 8 files
- Duplicate CTI docs: 5 files
- Duplicate schema detection: 3 files
- Duplicate rule management: 3 files
- Duplicate quick starts: 2 files
- Miscellaneous obsolete: 9 files

---

## 🚀 Execution Plan

Review this list, then I can create a script to:
1. Create a backup archive of all files to be removed
2. Remove the obsolete files
3. Update any internal references (if needed)
4. Verify no broken links

