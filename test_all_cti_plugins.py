#!/usr/bin/env python3
"""
Test All CTI Plugins (OTX, ThreatFox, CrowdStrike)
"""
import requests
import json

INGESTION_URL = "http://localhost:8003"

def test_individual_plugins():
    """Test each plugin individually"""
    print("\n" + "="*60)
    print("Testing Individual CTI Plugins")
    print("="*60)

    plugins = ["otx", "threatfox", "crowdstrike_intel"]

    for plugin in plugins:
        print(f"\n--- Testing {plugin.upper()} ---")
        response = requests.post(
            f"{INGESTION_URL}/cti/manual_update",
            json={"source": plugin, "since_days": 1, "limit": 20}
        )

        if response.status_code == 200:
            data = response.json()
            print(f"Status: SUCCESS")
            print(f"Indicators fetched: {data.get('indicators_fetched', 0)}")
        else:
            print(f"Status: FAILED ({response.status_code})")
            print(f"Error: {response.text}")

def test_aggregate_all():
    """Test aggregating from all plugins"""
    print("\n" + "="*60)
    print("Testing Aggregate from ALL Plugins")
    print("="*60)

    response = requests.post(
        f"{INGESTION_URL}/cti/manual_update",
        json={"source": "all", "since_days": 1, "limit": 50}
    )

    if response.status_code == 200:
        data = response.json()
        print(f"\nStatus: SUCCESS")
        print(f"Total indicators (deduplicated): {data.get('indicators_fetched', 0)}")
        print(f"Sources used: {', '.join(data.get('sources', []))}")
    else:
        print(f"\nStatus: FAILED ({response.status_code})")
        print(f"Error: {response.text}")

def check_status():
    """Check plugin health status"""
    print("\n" + "="*60)
    print("CTI Plugin Health Status")
    print("="*60)

    response = requests.get(f"{INGESTION_URL}/cti/status")
    if response.status_code == 200:
        data = response.json()
        print(f"\nTotal plugins: {data['plugin_count']}")
        print(f"\nHealth Details:")
        for plugin, health in data['health'].items():
            status = "HEALTHY" if health['healthy'] else "UNHEALTHY"
            print(f"  {plugin:20} - {status:10} (type: {health['type']})")
    else:
        print(f"Failed to get status: {response.text}")

if __name__ == "__main__":
    check_status()
    test_individual_plugins()
    test_aggregate_all()

    print("\n" + "="*60)
    print("All Tests Complete!")
    print("="*60)
