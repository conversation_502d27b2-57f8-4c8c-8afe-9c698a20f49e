"""
Investigation Context Generator
Transforms generic investigation guides into actionable, contextualized intelligence
"""
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from aiohttp import ClientSession
import logging


class InvestigationContextGenerator:
    """
    Automatically enriches alerts with contextual intelligence to answer:
    - "What is this IP/user/host?"
    - "Is this malicious?" (threat intel)
    - "Has this happened before?" (historical)
    - "What else is related?" (entity graph)
    - "What should I do?" (AI recommendation)
    """

    def __init__(self, db_connection, redis_client, logger: Optional[logging.Logger] = None):
        self.db = db_connection
        self.redis = redis_client
        self.logger = logger or logging.getLogger(__name__)

    async def generate_investigation_context(self, alert: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate complete investigation context for an alert
        This answers all the questions in the generic investigation guide
        """
        context = {
            'alert_id': alert.get('alert_id'),
            'generated_at': datetime.utcnow().isoformat(),
            'entities': {},
            'threat_intelligence': {},
            'historical_behavior': {},
            'timeline': [],
            'ai_verdict': {},
            'siem_linkback': {}
        }

        # Extract entities from alert
        entities = alert.get('entities', {})
        ips = entities.get('ips', [])
        users = entities.get('users', [])
        hosts = entities.get('hosts', [])

        # Enrich all entities in parallel
        enrichment_tasks = []

        for ip in ips:
            enrichment_tasks.append(self._enrich_ip_context(ip))

        for user in users:
            enrichment_tasks.append(self._enrich_user_context(user))

        for host in hosts:
            enrichment_tasks.append(self._enrich_host_context(host))

        # Wait for all enrichments
        if enrichment_tasks:
            enrichments = await asyncio.gather(*enrichment_tasks, return_exceptions=True)

            # Organize by entity type
            for enrichment in enrichments:
                if isinstance(enrichment, Exception):
                    continue
                if enrichment:
                    entity_type = enrichment.get('type')
                    entity_value = enrichment.get('value')
                    context['entities'][f"{entity_type}:{entity_value}"] = enrichment

        # Perform threat intelligence checks for IPs
        if ips:
            context['threat_intelligence'] = await self._check_threat_intelligence(ips[0])

        # Get historical behavior
        if ips:
            context['historical_behavior'] = await self._get_historical_behavior(
                ips[0],
                alert.get('title', '')
            )

        # Build timeline from recent events
        context['timeline'] = await self._build_timeline(alert)

        # Generate AI verdict
        context['ai_verdict'] = await self._generate_ai_verdict(
            alert,
            context['entities'],
            context['threat_intelligence'],
            context['historical_behavior']
        )

        # Generate SIEM link-back query
        context['siem_linkback'] = self._generate_siem_linkback(alert)

        return context

    async def _enrich_ip_context(self, ip: str) -> Dict[str, Any]:
        """
        Enrich IP with full context
        Answers: "What is this IP? Internal or external? Who owns it?"
        """
        context = {
            'type': 'ip',
            'value': ip,
            'enriched_at': datetime.utcnow().isoformat()
        }

        # Check if internal IP (RFC1918)
        if ip.startswith('192.168.') or ip.startswith('10.') or ip.startswith('172.'):
            context['ip_type'] = 'internal'
            context['location'] = 'Your Network (Internal)'

            # Check asset database for ownership
            try:
                async with self.db.cursor() as cursor:
                    await cursor.execute("""
                        SELECT entity_value, first_seen, last_seen, risk_score
                        FROM entities
                        WHERE entity_type = 'ip' AND entity_value = %s
                    """, (ip,))
                    result = await cursor.fetchone()

                    if result:
                        context['first_seen'] = result[1].isoformat() if result[1] else None
                        context['last_seen'] = result[2].isoformat() if result[2] else None
                        context['risk_score'] = result[3] or 0

                        # Calculate how long we've known about this IP
                        if result[1]:
                            days_known = (datetime.utcnow() - result[1]).days
                            context['days_known'] = days_known
            except Exception as e:
                self.logger.error(f"Error enriching IP from DB: {e}")
        else:
            context['ip_type'] = 'external'
            context['location'] = 'External'

        # Add GeoIP data (placeholder - would call real GeoIP service)
        context['geoip'] = {
            'country': 'Unknown',
            'city': 'Unknown',
            'asn': 'Unknown'
        }

        return context

    async def _enrich_user_context(self, user: str) -> Dict[str, Any]:
        """
        Enrich user with context
        Answers: "Who is this user? What do they normally do?"
        """
        context = {
            'type': 'user',
            'value': user,
            'enriched_at': datetime.utcnow().isoformat()
        }

        # Check user in database
        try:
            async with self.db.cursor() as cursor:
                await cursor.execute("""
                    SELECT entity_value, first_seen, last_seen, risk_score
                    FROM entities
                    WHERE entity_type = 'user' AND entity_value = %s
                """, (user,))
                result = await cursor.fetchone()

                if result:
                    context['first_seen'] = result[1].isoformat() if result[1] else None
                    context['last_seen'] = result[2].isoformat() if result[2] else None
                    context['risk_score'] = result[3] or 0
        except Exception as e:
            self.logger.error(f"Error enriching user: {e}")

        return context

    async def _enrich_host_context(self, host: str) -> Dict[str, Any]:
        """
        Enrich host with context
        Answers: "What is this host? What does it do?"
        """
        context = {
            'type': 'host',
            'value': host,
            'enriched_at': datetime.utcnow().isoformat()
        }

        # Check host in database
        try:
            async with self.db.cursor() as cursor:
                await cursor.execute("""
                    SELECT entity_value, first_seen, last_seen, risk_score
                    FROM entities
                    WHERE entity_type = 'host' AND entity_value = %s
                """, (host,))
                result = await cursor.fetchone()

                if result:
                    context['first_seen'] = result[1].isoformat() if result[1] else None
                    context['last_seen'] = result[2].isoformat() if result[2] else None
                    context['risk_score'] = result[3] or 0
        except Exception as e:
            self.logger.error(f"Error enriching host: {e}")

        return context

    async def _check_threat_intelligence(self, ip: str) -> Dict[str, Any]:
        """
        Check IP against threat intelligence sources
        Answers: "Is this IP malicious? What do threat feeds say?"
        """
        threat_intel = {
            'checked_at': datetime.utcnow().isoformat(),
            'ip': ip,
            'sources': [],
            'verdict': 'UNKNOWN',
            'risk_score': 0,
            'details': []
        }

        # Check internal IOC database
        try:
            async with self.db.cursor() as cursor:
                # Check if this IP is in our IOC database (from CTI feeds)
                await cursor.execute("""
                    SELECT ioc_value, source, confidence, first_seen, last_updated
                    FROM iocs
                    WHERE ioc_type = 'ip' AND ioc_value = %s
                """, (ip,))
                result = await cursor.fetchone()

                if result:
                    threat_intel['sources'].append({
                        'name': 'SIEMLess CTI Database',
                        'result': 'MALICIOUS',
                        'confidence': result[2],
                        'source': result[1],
                        'first_seen': result[3].isoformat() if result[3] else None
                    })
                    threat_intel['verdict'] = 'MALICIOUS'
                    threat_intel['risk_score'] = 90
                else:
                    threat_intel['sources'].append({
                        'name': 'SIEMLess CTI Database',
                        'result': 'CLEAN',
                        'confidence': 100
                    })
        except Exception as e:
            self.logger.error(f"Error checking threat intel: {e}")

        # If no malicious indicators found
        if threat_intel['verdict'] == 'UNKNOWN':
            threat_intel['verdict'] = 'BENIGN'
            threat_intel['risk_score'] = 0

        # Add note about external checks
        threat_intel['external_checks_available'] = [
            'VirusTotal', 'AbuseIPDB', 'GreyNoise', 'AlienVault OTX'
        ]
        threat_intel['note'] = 'Real-time external checks would be performed here'

        return threat_intel

    async def _get_historical_behavior(self, ip: str, alert_title: str) -> Dict[str, Any]:
        """
        Get historical behavior for this IP
        Answers: "Has this happened before? Is this normal?"
        """
        historical = {
            'checked_at': datetime.utcnow().isoformat(),
            'ip': ip,
            'similar_alerts': 0,
            'pattern': None,
            'previous_resolutions': [],
            'is_recurring': False
        }

        # This would query historical alerts from database
        # For now, return structure showing what would be checked
        historical['note'] = 'Historical behavior analysis would check past 30 days of similar activity'

        return historical

    async def _build_timeline(self, alert: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Build timeline of events around this alert
        Answers: "What happened before and after?"
        """
        timeline = []

        # Get alert timestamp
        alert_time = datetime.fromisoformat(alert.get('timestamp', datetime.utcnow().isoformat()).replace('Z', '+00:00'))

        timeline.append({
            'timestamp': alert_time.isoformat(),
            'event': 'ALERT_TRIGGERED',
            'description': alert.get('title', 'Alert triggered'),
            'severity': alert.get('severity', 'unknown')
        })

        # This would query logs/events before and after the alert
        timeline.append({
            'timestamp': (alert_time - timedelta(minutes=5)).isoformat(),
            'event': 'ACTIVITY_STARTED',
            'description': 'Related activity detected (would query from SIEM)',
            'note': 'Timeline would show events from Elastic logs'
        })

        return timeline

    async def _generate_ai_verdict(
        self,
        alert: Dict[str, Any],
        entities: Dict[str, Any],
        threat_intel: Dict[str, Any],
        historical: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate AI-powered investigation verdict
        Answers: "What should I do? Is this malicious or benign?"
        """
        verdict = {
            'generated_at': datetime.utcnow().isoformat(),
            'verdict': 'UNKNOWN',
            'confidence': 0,
            'reasoning': [],
            'recommended_action': 'INVESTIGATE',
            'priority': alert.get('severity', 'medium')
        }

        # Simple rule-based logic (would use AI in production)
        reasoning = []
        is_internal = False

        # Check threat intel
        if threat_intel.get('verdict') == 'MALICIOUS':
            reasoning.append('IP found in threat intelligence database')
            verdict['verdict'] = 'LIKELY_MALICIOUS'
            verdict['confidence'] = 85
            verdict['recommended_action'] = 'BLOCK_AND_INVESTIGATE'
        elif threat_intel.get('verdict') == 'BENIGN':
            reasoning.append('No malicious indicators in threat intelligence')
            verdict['confidence'] += 40  # Increased from 30

        # Check if internal IP
        for entity_key, entity_data in entities.items():
            if entity_data.get('type') == 'ip' and entity_data.get('ip_type') == 'internal':
                is_internal = True
                reasoning.append('Source IP is internal to network')
                verdict['confidence'] += 25  # NEW: Internal IPs get credit

                # If internal and has been seen before, likely benign
                days_known = entity_data.get('days_known', 0)
                if days_known > 30:
                    reasoning.append(f"IP has been in network for {days_known} days")
                    verdict['confidence'] += 20
                elif days_known > 0:
                    reasoning.append(f"IP known for {days_known} days")
                    verdict['confidence'] += 10

        # Check alert severity (lower severity = higher confidence it's benign)
        alert_severity = alert.get('severity', 'medium')
        if alert_severity == 'low':
            reasoning.append('Alert severity is low')
            verdict['confidence'] += 10
        elif alert_severity == 'critical' or alert_severity == 'high':
            reasoning.append(f'Alert severity is {alert_severity}')
            verdict['confidence'] -= 15  # High severity alerts need more scrutiny

        # Check historical behavior
        if historical.get('is_recurring'):
            reasoning.append('This is a recurring pattern')
            verdict['confidence'] += 15

        # Check for previous similar alerts
        similar_count = historical.get('similar_alerts', 0)
        if similar_count > 5:
            reasoning.append(f'{similar_count} similar alerts seen before')
            # Check if all previous were false positives
            prev_resolutions = historical.get('previous_resolutions', [])
            if prev_resolutions and all('false positive' in str(r).lower() for r in prev_resolutions):
                reasoning.append('Previous similar alerts were all false positives')
                verdict['confidence'] += 20

        # Final verdict based on confidence score
        if verdict['verdict'] == 'UNKNOWN':
            if verdict['confidence'] >= 70:
                verdict['verdict'] = 'LIKELY_BENIGN'
                verdict['recommended_action'] = 'REVIEW_AND_CLOSE'
            elif verdict['confidence'] >= 40:
                verdict['verdict'] = 'POSSIBLY_BENIGN'
                verdict['recommended_action'] = 'QUICK_REVIEW'
            else:
                verdict['verdict'] = 'REQUIRES_INVESTIGATION'
                verdict['recommended_action'] = 'INVESTIGATE'

        # Add confidence explanation
        verdict['confidence_breakdown'] = {
            'threat_intel_clean': 40 if threat_intel.get('verdict') == 'BENIGN' else 0,
            'internal_ip': 25 if is_internal else 0,
            'low_severity': 10 if alert_severity == 'low' else 0,
            'total': verdict['confidence']
        }

        verdict['reasoning'] = reasoning

        return verdict

    def _generate_siem_linkback(self, alert: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate SIEM link-back query
        Answers: "Where can I see the raw logs?"
        """
        entities = alert.get('entities', {})
        timestamp = alert.get('timestamp', datetime.utcnow().isoformat())

        # Parse timestamp
        alert_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

        # Generate Elastic query
        query_parts = []

        if entities.get('ips'):
            query_parts.append(f'source.ip:"{entities["ips"][0]}"')

        if entities.get('users'):
            query_parts.append(f'user.name:"{entities["users"][0]}"')

        # Time range: 1 hour before to 10 minutes after alert
        start_time = (alert_time - timedelta(hours=1)).isoformat()
        end_time = (alert_time + timedelta(minutes=10)).isoformat()

        query = ' AND '.join(query_parts) if query_parts else '*'

        return {
            'siem': 'elastic',
            'query': query,
            'time_range': {
                'start': start_time,
                'end': end_time
            },
            'url_template': f'https://your-elastic-instance/app/discover#/?query={query}',
            'note': 'Use this query in Elastic to see raw logs around this alert'
        }
