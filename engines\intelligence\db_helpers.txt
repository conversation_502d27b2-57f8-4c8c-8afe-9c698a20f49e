

# Database helper functions (psycopg2 pattern)
def db_execute(connection, query: str, *params):
    """Helper to execute non-query commands with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    cursor.close()  # CRITICAL: Always close to prevent leaks


def db_fetchone(connection, query: str, *params):
    """Helper to fetch a single row with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    result = cursor.fetchone()
    cursor.close()
    return result


def db_fetchall(connection, query: str, *params):
    """Helper to fetch all rows with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    results = cursor.fetchall()
    cursor.close()
    return results

