# SIEMLess v2.0 - Delivery Engine Docker Image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy base engine
COPY base_engine.py ./base_engine.py

# Copy all delivery engine code
COPY *.py ./

# Create directories for data and logs
RUN mkdir -p /tmp/claude/delivery /var/log/siemless /app/frontend

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import asyncio; from delivery_engine import DeliveryEngine; engine = DeliveryEngine(); print('Health check passed')" || exit 1

# Run the delivery engine
CMD ["python", "delivery_engine.py"]