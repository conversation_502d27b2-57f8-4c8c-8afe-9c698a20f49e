# Intelligence Engine Refactoring Plan
**Date**: 2025-09-30
**Goal**: Modular, hot-reloadable AI system with clean separation of concerns

## Current State Problems
1. **Hardcoded model configurations** - Can't adapt to new AI models without code changes
2. **Mixed responsibilities** - API keys, model selection, prompts, and costs all mixed together
3. **No hot-reload** - Need to restart entire engine to change models
4. **No usage tracking** - Can't see actual cost vs budget in real-time
5. **Tight coupling** - Sigma enhancement logic mixed with AI model logic

## Proposed Architecture

```
engines/intelligence/
├── intelligence_engine.py          # Main engine (orchestrator only)
├── config/
│   ├── ai_models.yaml             # Hot-reloadable model definitions
│   ├── api_credentials.yaml       # Hot-reloadable API keys (gitignored)
│   └── cost_budgets.yaml          # Hot-reloadable cost limits
├── core/
│   ├── model_registry.py          # Model selection & hot-reload logic
│   ├── credential_manager.py      # API key management & rotation
│   ├── cost_tracker.py            # Usage tracking & budget enforcement
│   └── prompt_library.py          # Reusable prompts for different use cases
├── providers/
│   ├── base_provider.py           # Abstract base for all AI providers
│   ├── google_provider.py         # Google AI (<PERSON>, <PERSON>)
│   ├── anthropic_provider.py      # Anthropic (Claude models)
│   ├── openai_provider.py         # OpenAI (GPT models)
│   └── ollama_provider.py         # Local Ollama models
├── use_cases/
│   ├── sigma_enhancement.py       # Sigma rule enhancement logic
│   ├── log_parsing.py             # Log parsing/normalization
│   ├── threat_analysis.py         # Threat intelligence analysis
│   └── pattern_validation.py      # Pattern crystallization validation
└── utils/
    ├── hot_reload.py              # File watcher for config changes
    └── response_parser.py         # Universal JSON/text parsing
```

## Module Breakdown

### 1. Model Registry (`core/model_registry.py`)
**Purpose**: Universal model selection and hot-reload management

**Responsibilities**:
- Load model configurations from YAML
- Watch config file for changes (hot-reload)
- Select best model based on task complexity, cost constraints, quality needs
- Validate model availability before use
- Support model aliasing (e.g., "best_free" → current best free model)

**Key Functions**:
```python
class ModelRegistry:
    def __init__(self, config_path: str):
        """Initialize with path to ai_models.yaml"""

    def get_model(self, tier: str = None, task: str = None, max_cost: float = None) -> ModelConfig:
        """Get best model matching criteria"""

    def reload_config(self):
        """Hot-reload model configurations"""

    def list_available_models(self) -> List[ModelConfig]:
        """Get all currently available models"""

    def get_model_for_task(self, task_type: str, quality_required: int) -> ModelConfig:
        """Intelligent model selection based on task requirements"""
```

**Config Format** (`config/ai_models.yaml`):
```yaml
models:
  # Google AI
  gemma-27b:
    provider: google
    model_name: gemma-3-27b-it
    tier: free
    cost_per_1k_tokens: 0.0
    quality_score: 75
    speed_score: 95
    max_tokens: 8192
    capabilities: [text, json, reasoning]
    recommended_for: [testing, development, simple_analysis]

  gemini-flash:
    provider: google
    model_name: gemini-2.5-flash
    tier: low_cost
    cost_per_1k_tokens: 0.002
    quality_score: 60
    speed_score: 40
    max_tokens: 32768
    capabilities: [text, json, code]
    recommended_for: []  # Not recommended based on test
    warnings: [unreliable_json, slow_response]

  claude-sonnet-4:
    provider: anthropic
    model_name: claude-sonnet-4-20250514
    tier: production
    cost_per_1k_tokens: 0.008
    quality_score: 92
    speed_score: 85
    max_tokens: 200000
    capabilities: [text, json, reasoning, code, security_analysis]
    recommended_for: [sigma_enhancement, threat_analysis, production]

  # Model aliases for easy switching
  aliases:
    default: claude-sonnet-4
    best_free: gemma-27b
    production: claude-sonnet-4
    development: gemma-27b
```

### 2. Credential Manager (`core/credential_manager.py`)
**Purpose**: API key management with hot-reload and rotation support

**Responsibilities**:
- Load API credentials from secure config
- Support multiple key rotation (for rate limit distribution)
- Hot-reload credentials without restart
- Validate keys on load
- Support environment variables AND config file
- Mask keys in logs

**Key Functions**:
```python
class CredentialManager:
    def __init__(self, config_path: str = None):
        """Initialize with optional config path (defaults to env vars)"""

    def get_credential(self, provider: str) -> str:
        """Get API key for provider, with rotation if multiple keys"""

    def reload_credentials(self):
        """Hot-reload credentials from config"""

    def validate_credential(self, provider: str, key: str) -> bool:
        """Test if API key is valid"""

    def rotate_key(self, provider: str):
        """Switch to next available key (for rate limit handling)"""

    def mask_key(self, key: str) -> str:
        """Return masked version for logging (e.g., 'sk-...xyz123')"""
```

**Config Format** (`config/api_credentials.yaml`):
```yaml
# Gitignored - never commit this file
credentials:
  google:
    primary: ${GOOGLE_API_KEY}  # Can use env vars
    secondary: AIza...fallback  # Rotation key if primary hits rate limit

  anthropic:
    primary: ${ANTHROPIC_API_KEY}

  openai:
    primary: ${OPENAI_API_KEY}

# Key rotation strategy
rotation:
  enabled: true
  strategy: round_robin  # or: failover, least_used
  rate_limit_pause: 60  # seconds to wait before retrying failed key
```

### 3. Cost Tracker (`core/cost_tracker.py`)
**Purpose**: Real-time cost tracking and budget enforcement

**Responsibilities**:
- Track token usage per model
- Calculate actual costs in real-time
- Enforce budget limits
- Generate cost reports
- Store usage history for analysis
- Alert on budget thresholds

**Key Functions**:
```python
class CostTracker:
    def __init__(self, db_connection, budget_config: str):
        """Initialize with DB connection and budget config"""

    def record_usage(self, model: str, input_tokens: int, output_tokens: int, task: str):
        """Record usage and calculate cost"""

    def get_current_spend(self, timeframe: str = 'month') -> float:
        """Get total spend in timeframe"""

    def check_budget(self, model: str, estimated_tokens: int) -> bool:
        """Check if request would exceed budget"""

    def get_usage_report(self, group_by: str = 'model') -> Dict:
        """Generate usage report grouped by model/task/date"""

    def set_budget_alert(self, threshold: float, callback: Callable):
        """Alert when spending reaches threshold"""
```

**Config Format** (`config/cost_budgets.yaml`):
```yaml
budgets:
  monthly_total: 50.00  # USD
  per_model:
    claude-sonnet-4: 30.00
    gemini-flash: 5.00
    free_models: 0.00

  alerts:
    - threshold: 0.8  # 80% of budget
      action: email
      recipient: <EMAIL>
    - threshold: 0.95  # 95% of budget
      action: throttle
      fallback_model: gemma-27b
    - threshold: 1.0  # 100% of budget
      action: block

  tracking:
    store_history: true
    retention_days: 90
```

### 4. Prompt Library (`core/prompt_library.py`)
**Purpose**: Centralized, reusable prompts for different use cases

**Responsibilities**:
- Store prompt templates
- Version prompts (A/B testing)
- Support dynamic prompt generation
- Track prompt effectiveness
- Easy prompt updates without code changes

**Key Functions**:
```python
class PromptLibrary:
    def __init__(self, prompts_dir: str):
        """Load prompts from directory"""

    def get_prompt(self, use_case: str, version: str = 'latest', **kwargs) -> str:
        """Get formatted prompt for use case"""

    def register_prompt(self, use_case: str, template: str, version: str):
        """Add new prompt template"""

    def test_prompt(self, use_case: str, test_data: Dict) -> str:
        """Generate prompt with test data (for validation)"""

    def get_prompt_stats(self, use_case: str) -> Dict:
        """Get usage stats and effectiveness metrics"""
```

**Prompt Format** (`prompts/sigma_enhancement_v2.txt`):
```python
# Sigma Enhancement Prompt v2
# Last Updated: 2025-09-30
# Tested Quality: 92% (Claude Sonnet 4)

# Security Detection Rule Enhancement Task

## Rule to Enhance
**Title**: {{rule.title}}
**Description**: {{rule.description}}
**Severity**: {{rule.level}}
**Tags**: {{', '.join(rule.tags)}}

**Detection Logic**:
```yaml
{{rule.detection | to_yaml}}
```

## Source Platform
Harvested from: **{{source_platform}}**

## Target Platforms
Will be deployed to: **{{', '.join(target_platforms)}}**

## Enhancement Requirements
[... rest of prompt ...]

## Response Format
Provide ONLY valid JSON in this exact structure:
```json
{
  "evasion_variants": [...],
  "false_positive_filters": [...],
  "platform_optimizations": {...},
  "missing_context": [...],
  "overall_assessment": {...}
}
```
```

### 5. Provider Abstraction (`providers/base_provider.py`)
**Purpose**: Unified interface for all AI providers

**Responsibilities**:
- Abstract provider-specific API differences
- Handle retries and rate limits
- Parse responses uniformly
- Support streaming and batch requests

**Key Functions**:
```python
class BaseAIProvider(ABC):
    def __init__(self, credential_manager: CredentialManager, cost_tracker: CostTracker):
        """Initialize with credential and cost tracking"""

    @abstractmethod
    async def call(self, model: str, prompt: str, **kwargs) -> AIResponse:
        """Call AI model with prompt"""

    @abstractmethod
    def parse_response(self, raw_response: Any) -> AIResponse:
        """Parse provider-specific response into standard format"""

    async def call_with_retry(self, model: str, prompt: str, retries: int = 3) -> AIResponse:
        """Call with automatic retry on failure"""
```

### 6. Use Case Modules (`use_cases/`)
**Purpose**: Business logic separated from AI infrastructure

**Example** (`use_cases/sigma_enhancement.py`):
```python
class SigmaEnhancer:
    def __init__(self, model_registry: ModelRegistry, prompt_library: PromptLibrary):
        """Initialize with registry and prompts"""

    async def enhance_rule(
        self,
        rule: Dict,
        quality: str = 'high',  # high, medium, low
        max_cost: float = 0.01
    ) -> Dict:
        """Enhance rule using best available model within constraints"""

        # Get appropriate model
        model = self.model_registry.get_model_for_task(
            task_type='sigma_enhancement',
            quality_required=quality
        )

        # Get prompt
        prompt = self.prompt_library.get_prompt(
            'sigma_enhancement',
            rule=rule,
            source_platform='elastic',
            target_platforms=['wazuh', 'splunk']
        )

        # Call AI
        response = await model.provider.call(model.name, prompt)

        # Parse and return
        return self._parse_enhancement(response)
```

### 7. Hot Reload System (`utils/hot_reload.py`)
**Purpose**: Watch config files and reload on changes

**Responsibilities**:
- Watch YAML config files
- Detect changes
- Trigger reload in respective managers
- Validate configs before applying
- Rollback on invalid config

**Key Functions**:
```python
class HotReloadManager:
    def __init__(self):
        """Initialize file watchers"""

    def watch_file(self, path: str, callback: Callable):
        """Watch file and call callback on change"""

    def watch_directory(self, path: str, pattern: str, callback: Callable):
        """Watch all files matching pattern in directory"""

    async def reload_all(self):
        """Reload all watched configs"""
```

## Implementation Plan

### Phase 1: Foundation (Week 1)
**Goal**: Set up core infrastructure

1. **Create directory structure**
   - Set up new folders: `config/`, `core/`, `providers/`, `use_cases/`

2. **Implement Model Registry**
   - Create `ModelRegistry` class
   - Design `ai_models.yaml` schema
   - Implement hot-reload functionality
   - Write unit tests

3. **Implement Credential Manager**
   - Create `CredentialManager` class
   - Support env vars + YAML
   - Add key rotation logic
   - Write unit tests

4. **Create base provider abstraction**
   - Define `BaseAIProvider` interface
   - Create `AIResponse` data class
   - Design error handling strategy

**Deliverables**:
- [ ] `core/model_registry.py` (with tests)
- [ ] `core/credential_manager.py` (with tests)
- [ ] `providers/base_provider.py`
- [ ] `config/ai_models.yaml` (schema + examples)
- [ ] `config/api_credentials.yaml.example`

### Phase 2: Providers (Week 2)
**Goal**: Implement all AI provider adapters

1. **Implement Google Provider**
   - Support Gemini and Gemma models
   - Handle google-genai SDK quirks
   - Add response parsing

2. **Implement Anthropic Provider**
   - Support Claude models (Sonnet, Opus)
   - Handle anthropic SDK
   - Add streaming support

3. **Implement OpenAI Provider**
   - Support GPT models
   - Handle openai SDK

4. **Implement Ollama Provider**
   - Support local models
   - Handle connection failures gracefully

**Deliverables**:
- [ ] `providers/google_provider.py` (with tests)
- [ ] `providers/anthropic_provider.py` (with tests)
- [ ] `providers/openai_provider.py` (with tests)
- [ ] `providers/ollama_provider.py` (with tests)

### Phase 3: Cost & Prompts (Week 3)
**Goal**: Add cost tracking and prompt management

1. **Implement Cost Tracker**
   - PostgreSQL schema for usage tracking
   - Real-time cost calculation
   - Budget enforcement
   - Usage reports

2. **Implement Prompt Library**
   - Load prompts from files
   - Template rendering (Jinja2)
   - Version management
   - Prompt effectiveness tracking

3. **Implement Hot Reload**
   - File watchers for configs
   - Validation before reload
   - Graceful error handling

**Deliverables**:
- [ ] `core/cost_tracker.py` (with tests)
- [ ] `core/prompt_library.py` (with tests)
- [ ] `utils/hot_reload.py` (with tests)
- [ ] PostgreSQL migration for cost tracking
- [ ] Prompt templates in `prompts/`

### Phase 4: Use Cases (Week 4)
**Goal**: Migrate existing functionality to new architecture

1. **Migrate Sigma Enhancement**
   - Move logic from `sigma_enhancement.py` to `use_cases/sigma_enhancement.py`
   - Use new Model Registry
   - Use new Prompt Library
   - Add cost tracking

2. **Add other use cases**
   - Log parsing
   - Threat analysis
   - Pattern validation

3. **Update Intelligence Engine**
   - Refactor to use new modules
   - Add HTTP endpoints for config management
   - Add Redis handlers for hot-reload triggers

**Deliverables**:
- [ ] `use_cases/sigma_enhancement.py` (refactored)
- [ ] `use_cases/log_parsing.py`
- [ ] `use_cases/threat_analysis.py`
- [ ] Updated `intelligence_engine.py`
- [ ] API endpoints for model/cost management

### Phase 5: Testing & Documentation (Week 5)
**Goal**: Ensure quality and usability

1. **Integration tests**
   - Test full workflow with real API calls
   - Test hot-reload functionality
   - Test budget enforcement
   - Test failover scenarios

2. **Documentation**
   - API documentation
   - Configuration guide
   - Migration guide from old system
   - Runbook for operators

3. **Performance testing**
   - Benchmark model selection
   - Test concurrent requests
   - Measure hot-reload impact

**Deliverables**:
- [ ] Integration test suite
- [ ] API documentation
- [ ] Configuration guide
- [ ] Performance benchmarks
- [ ] Migration script from old to new system

## Success Metrics

### Functional
- [ ] Can change AI models without code restart
- [ ] Can update API keys without restart
- [ ] Can set and enforce cost budgets
- [ ] Can track cost per use case
- [ ] All existing functionality works with new system

### Performance
- [ ] Model selection < 10ms
- [ ] Hot-reload config changes < 1s
- [ ] Cost tracking adds < 5ms overhead
- [ ] No regression in AI call latency

### Quality
- [ ] 90%+ test coverage on new modules
- [ ] Zero hardcoded API keys in code
- [ ] All configs in YAML/env vars
- [ ] Clean separation of concerns

## Migration Strategy

### Backward Compatibility
- Keep old `ai_models.py` during transition
- Add deprecation warnings
- Gradual migration over 2 weeks
- Feature flag for new vs old system

### Rollback Plan
- Keep old code in `legacy/` directory
- Environment variable to switch between systems
- Database schema supports both systems
- Can rollback in < 5 minutes

## Future Enhancements (Post-MVP)

1. **Model Performance Analytics**
   - Track quality scores per model
   - A/B test prompts
   - Auto-select best model based on historical performance

2. **Multi-Model Consensus**
   - Call multiple models for critical decisions
   - Ensemble voting
   - Confidence scoring

3. **Prompt Optimization**
   - Automatic prompt tuning
   - Prompt compression for cost savings
   - Prompt caching

4. **Advanced Cost Optimization**
   - Predictive budget management
   - Auto-throttling based on budget
   - Cost-aware request batching

## Questions to Resolve

1. **Config Storage**: YAML files vs Redis vs PostgreSQL?
   - **Recommendation**: YAML for model/credential config, PostgreSQL for usage/cost data

2. **Hot-reload granularity**: Reload entire config or just changed sections?
   - **Recommendation**: Reload only changed sections to avoid disruption

3. **Provider fallback**: Auto-fallback to different provider on failure?
   - **Recommendation**: Yes, with configurable fallback chain

4. **Prompt versioning**: Git-based or database-based?
   - **Recommendation**: Git for version control, database for A/B test results

5. **Cost tracking granularity**: Per-request, per-hour, per-day?
   - **Recommendation**: Per-request for accuracy, aggregated for reporting

---

**This plan provides a clean, modular architecture that solves all the pain points:**
- ✅ Hot-reload for models, credentials, and budgets
- ✅ Clear separation of concerns
- ✅ Universal AI provider interface
- ✅ Real-time cost tracking
- ✅ Centralized prompt management
- ✅ Easy to extend with new models/providers
- ✅ Production-ready with proper testing and documentation
