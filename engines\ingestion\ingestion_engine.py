"""
SIEMLess v2.0 - Ingestion Engine (Modular Architecture)
Multi-source data ingestion with intelligent routing and normalization

This is a refactored version using clean modular architecture principles:
- Separate modules for each major concern
- Clear interfaces between modules
- Thin main engine class that coordinates modules
- Proper separation of configuration from business logic
- Clean message handler patterns
"""

import asyncio
import json
import os
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from aiohttp import web
from base_engine import BaseEngine
# from auth_middleware import KeycloakAuthMiddleware  # COMMENTED OUT - Auth disabled for now

# Import modular components
from config_manager import ConfigManager
from data_source_manager import DataSourceManager
from log_router import LogRouter
from stats_monitor import StatsMonitor
from task_coordinator import TaskCoordinator, IngestionTaskFactory
from message_handler import (
    MessageHandler, SourceMessageHandler, GitHubMessageHandler,
    ParserMessageHandler, APIDocMessageHandler, StatsMessageHandler
)

# Import existing specialized components
from pattern_matcher import PatternMatcher
from github_pattern_sync import GitHubPatternSync
from parser_hot_reload import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from api_doc_generator import APIDocumentationGenerator
# from cti_manager import CTIManager  # OLD - tool-specific integrations
from cti_source_plugin import CTIPluginManager
from otx_plugin import OTXPlugin
from threatfox_plugin import ThreatFoxPlugin
from opencti_plugin import OpenCTIPlugin
from crowdstrike_plugin import CrowdStrikePlugin

# Import context plugin system
from context_source_plugin import ContextSourceManager, create_context_query
from crowdstrike_context_plugin import CrowdStrikeContextPlugin

# Import rule deployment service (EXTERNAL I/O - belongs in Ingestion)
from rule_deployment_service import RuleDeploymentService


class IngestionEngine(BaseEngine):
    """
    Modular Ingestion Engine for SIEMLess v2.0

    This engine coordinates multiple specialized modules:
    - ConfigManager: Configuration and settings management
    - DataSourceManager: Active data source lifecycle
    - LogRouter: Intelligent log routing based on patterns
    - StatsMonitor: Statistics and performance monitoring
    - TaskCoordinator: Background task management
    - MessageHandler: Message processing coordination
    """

    def __init__(self):
        super().__init__("ingestion")

        # Authentication middleware (COMMENTED OUT)
        self.auth_middleware = None  # Will be initialized in _setup_http_routes

        # Initialize core modules
        self._initialize_modules()

        # Setup component integration
        self._setup_component_integration()

        # Initialize message handling
        self._setup_message_handlers()

    def _initialize_modules(self):
        """Initialize all modular components"""
        # Configuration management
        self.config_manager = ConfigManager(self.logger)

        # Data source management
        self.data_source_manager = DataSourceManager(self.config_manager, self.logger)

        # Statistics and monitoring
        self.stats_monitor = StatsMonitor(self.publish_message, self.logger)

        # Initialize pattern matcher for FREE processing
        self.pattern_matcher = PatternMatcher(
            self.db_pool,
            self.redis_client,
            self.logger
        )

        # Log routing with pattern matching
        self.log_router = LogRouter(self.pattern_matcher, self.publish_message, self.logger)

        # Task coordination
        self.task_coordinator = TaskCoordinator(self.config_manager, self.logger)

        self.logger.info("Core modules initialized")

    def _setup_component_integration(self):
        """Setup integration with existing specialized components"""
        # Initialize GitHub pattern sync
        self.github_sync = GitHubPatternSync(
            self.redis_client,
            self.db_pool,
            self.logger
        )

        # Initialize parser hot reload
        self.parser_hot_reload = ParserHotReload(
            self.redis_client,
            self.db_pool,
            self.logger
        )

        # Initialize API documentation generator
        self.api_doc_generator = APIDocumentationGenerator(
            self.redis_client,
            self.db_pool,
            self.logger
        )

        # Initialize CTI Plugin Manager (universal plugin system)
        self.cti_plugin_manager = CTIPluginManager(self.logger)

        # Register OTX Plugin
        if os.getenv('OTX_API_KEY'):
            otx_plugin = OTXPlugin(
                config={'api_key': os.getenv('OTX_API_KEY')},
                logger=self.logger
            )
            self.cti_plugin_manager.register_plugin(otx_plugin)
            self.logger.info("OTX plugin registered")

        # Register ThreatFox Plugin
        threatfox_key = os.getenv('THREATFOX_API_KEY') or os.getenv('THREATFOX_AUTH_KEY')
        if threatfox_key:
            threatfox_plugin = ThreatFoxPlugin(
                config={
                    'api_key': threatfox_key,
                    'verify_ssl': os.getenv('THREATFOX_VERIFY_SSL', 'true').lower() == 'true'
                },
                logger=self.logger
            )
            self.cti_plugin_manager.register_plugin(threatfox_plugin)
            self.logger.info("ThreatFox plugin registered")

        # Register OpenCTI Plugin
        if os.getenv('OPENCTI_URL') and os.getenv('OPENCTI_TOKEN'):
            opencti_plugin = OpenCTIPlugin(
                config={
                    'api_url': os.getenv('OPENCTI_URL'),
                    'api_key': os.getenv('OPENCTI_TOKEN')
                },
                logger=self.logger
            )
            self.cti_plugin_manager.register_plugin(opencti_plugin)
            self.logger.info("OpenCTI plugin registered")

        # Register CrowdStrike CTI Plugin (INTEL + IOCS scopes)
        if os.getenv('CROWDSTRIKE_CLIENT_ID') and os.getenv('CROWDSTRIKE_CLIENT_SECRET'):
            crowdstrike_plugin = CrowdStrikePlugin(
                config={
                    'client_id': os.getenv('CROWDSTRIKE_CLIENT_ID'),
                    'client_secret': os.getenv('CROWDSTRIKE_CLIENT_SECRET'),
                    'base_url': os.getenv('CROWDSTRIKE_BASE_URL', 'https://api.crowdstrike.com'),
                    'priority': 80  # Higher priority than community sources
                },
                logger=self.logger
            )
            self.cti_plugin_manager.register_plugin(crowdstrike_plugin)
            self.logger.info("CrowdStrike CTI plugin registered")

        self.logger.info(f"CTI Plugin Manager initialized with {len(self.cti_plugin_manager.plugins)} plugins")

        # Initialize Context Source Manager (plugin system)
        self.context_manager = ContextSourceManager()
        self._setup_context_plugins()

        # Initialize Rule Deployment Service (EXTERNAL I/O)
        self.rule_deployment = RuleDeploymentService(
            config={
                'elastic_cloud_id': os.getenv('ELASTIC_CLOUD_ID'),  # Support Elastic Cloud
                'elastic_kibana_url': os.getenv('ELASTIC_KIBANA_URL'),  # Or direct Kibana URL
                'elastic_api_key': os.getenv('ELASTIC_API_KEY'),
                'elastic_username': os.getenv('ELASTIC_USERNAME'),
                'elastic_password': os.getenv('ELASTIC_PASSWORD'),
                'splunk_url': os.getenv('SPLUNK_URL'),
                'splunk_token': os.getenv('SPLUNK_TOKEN'),
                'sentinel_workspace_id': os.getenv('SENTINEL_WORKSPACE_ID'),
                'sentinel_tenant_id': os.getenv('SENTINEL_TENANT_ID'),
                'sentinel_client_id': os.getenv('SENTINEL_CLIENT_ID'),
                'sentinel_client_secret': os.getenv('SENTINEL_CLIENT_SECRET'),
                'qradar_url': os.getenv('QRADAR_URL'),
                'qradar_token': os.getenv('QRADAR_TOKEN')
            },
            logger=self.logger
        )
        self.logger.info("Rule Deployment Service initialized")

        # Create component collection for task factory
        self.components = {
            'github_sync': self.github_sync,
            'parser_hot_reload': self.parser_hot_reload,
            'api_doc_generator': self.api_doc_generator,
            'pattern_matcher': self.pattern_matcher
        }

        self.logger.info("Component integration complete")

    def _setup_context_plugins(self):
        """Setup context source plugins"""
        # Register CrowdStrike plugin
        crowdstrike_plugin = CrowdStrikeContextPlugin({
            'enabled': bool(os.getenv('CROWDSTRIKE_CLIENT_ID') and os.getenv('CROWDSTRIKE_CLIENT_SECRET')),
            'client_id': os.getenv('CROWDSTRIKE_CLIENT_ID'),
            'client_secret': os.getenv('CROWDSTRIKE_CLIENT_SECRET'),
            'base_url': os.getenv('CROWDSTRIKE_BASE_URL', 'https://api.crowdstrike.com')
        })
        self.context_manager.register_plugin(crowdstrike_plugin)

        # Register Elastic Security plugin
        from elastic_context_plugin import ElasticContextPlugin
        elastic_plugin = ElasticContextPlugin({
            'enabled': bool(os.getenv('ELASTIC_CLOUD_ID') or os.getenv('ELASTIC_URL')),
            'cloud_id': os.getenv('ELASTIC_CLOUD_ID'),  # Elastic Cloud support
            'elastic_url': os.getenv('ELASTIC_URL'),
            'api_key': os.getenv('ELASTIC_API_KEY'),
            'username': os.getenv('ELASTIC_USERNAME'),
            'password': os.getenv('ELASTIC_PASSWORD'),
            'verify_certs': os.getenv('ELASTIC_VERIFY_CERTS', 'true').lower() == 'true'
        })
        self.context_manager.register_plugin(elastic_plugin)

        # Add more plugins here as they're implemented
        # Example:
        # sentinelone_plugin = SentinelOneContextPlugin({...})
        # self.context_manager.register_plugin(sentinelone_plugin)

        self.logger.info(f"Registered {len(self.context_manager.plugins)} context plugins")

        # Schedule plugin initialization as a background task
        asyncio.create_task(self._initialize_context_plugins())

    async def _initialize_context_plugins(self):
        """Initialize all context plugins (runs as background task)"""
        try:
            init_results = await self.context_manager.initialize_all()
            for source, success in init_results.items():
                if success:
                    self.logger.info(f"✓ {source} plugin initialized successfully")
                else:
                    self.logger.warning(f"✗ {source} plugin initialization failed")
        except Exception as e:
            self.logger.error(f"Error initializing context plugins: {e}", exc_info=True)

    def _setup_message_handlers(self):
        """Setup message handling coordination"""
        # Main message handler
        self.message_handler = MessageHandler(self.logger)

        # Specialized message handlers
        self.source_handler = SourceMessageHandler(
            self.data_source_manager, self.publish_message, self.logger
        )

        self.github_handler = GitHubMessageHandler(
            self.github_sync, self.config_manager, self.publish_message, self.logger
        )

        self.parser_handler = ParserMessageHandler(
            self.parser_hot_reload, self.stats_monitor, self.publish_message,
            self.db_pool, self.logger
        )

        self.api_doc_handler = APIDocMessageHandler(
            self.api_doc_generator, self.publish_message, self.logger
        )

        self.stats_handler = StatsMessageHandler(
            self.stats_monitor, self.data_source_manager, self.config_manager,
            self.publish_message, self.logger
        )

        # Register message handlers
        self._register_message_handlers()

        self.logger.info("Message handlers configured")

    def _register_message_handlers(self):
        """Register all message handlers with their channels"""
        # Source management handlers
        self.message_handler.register_handler('ingestion.start_source', self.source_handler.handle_start_source)
        self.message_handler.register_handler('ingestion.stop_source', self.source_handler.handle_stop_source)
        self.message_handler.register_handler('ingestion.configure_source', self.source_handler.handle_configure_source)

        # GitHub handlers
        self.message_handler.register_handler('ingestion.sync_github', self.github_handler.handle_sync_github)
        self.message_handler.register_handler('ingestion.add_github_repo', self.github_handler.handle_add_github_repo)

        # Parser handlers
        self.message_handler.register_handler('ingestion.reload_parsers', self.parser_handler.handle_reload_parsers)

        # API documentation handlers
        self.message_handler.register_handler('ingestion.generate_api_docs', self.api_doc_handler.handle_generate_api_docs)

        # Statistics handlers
        self.message_handler.register_handler('ingestion.get_stats', self.stats_handler.handle_get_stats)

        # CTI update handlers (delegation from Backend Scheduler)
        self.message_handler.register_handler('ingestion.cti.update', self._handle_cti_update_trigger)

        # Context building handler (for alert investigation)
        self.message_handler.register_handler('ingestion.pull_context', self._handle_pull_context)

        # Rule deployment handler (listens for approved rules from Backend)
        self.message_handler.register_handler('backend.rule.approved', self._handle_rule_deployment)

    def start_engine_tasks(self) -> List[asyncio.Task]:
        """Start ingestion-specific background tasks using task coordinator"""
        # Create task factory with all components
        task_factory = IngestionTaskFactory(self.components, self.logger)

        # Register all background tasks
        self._register_background_tasks(task_factory)

        # Start all coordinator tasks
        tasks = self.task_coordinator.start_all_tasks()

        # Validate CTI plugins on startup (one-time check, not a long-running task)
        asyncio.create_task(self._check_cti_plugin_health())

        return tasks

    def _register_background_tasks(self, task_factory: IngestionTaskFactory):
        """Register all background tasks with the task coordinator"""

        # Component initialization (runs once at startup)
        init_task = task_factory.create_component_initialization_task(
            self.github_sync, self.parser_hot_reload, self.api_doc_generator,
            self.config_manager, self.stats_monitor
        )
        self.task_coordinator.register_task(
            'component_initialization', init_task,
            interval=0,  # Only runs once
            enabled=True
        )

        # Source monitoring task
        source_monitor_task = task_factory.create_source_monitor_task(self.data_source_manager)
        source_monitor_interval = self.config_manager.get_engine_setting('source_monitor_interval', 30)
        self.task_coordinator.register_task(
            'source_monitor', source_monitor_task,
            interval=source_monitor_interval,
            enabled=True,
            dependencies=['component_initialization']
        )

        # Main ingestion processing task
        ingestion_task = task_factory.create_ingestion_task(
            self.data_source_manager, self.log_router, self.stats_monitor
        )
        ingestion_interval = self.config_manager.get_engine_setting('ingestion_loop_interval', 10)
        self.task_coordinator.register_task(
            'ingestion_processing', ingestion_task,
            interval=ingestion_interval,
            enabled=True,
            dependencies=['component_initialization']
        )

        # Statistics reporting task
        stats_task = task_factory.create_stats_reporting_task(
            self.stats_monitor, self.publish_message
        )
        stats_interval = self.config_manager.get_engine_setting('stats_reporting_interval', 60)
        self.task_coordinator.register_task(
            'stats_reporting', stats_task,
            interval=stats_interval,
            enabled=True
        )

        # GitHub sync task
        github_task = task_factory.create_github_sync_task(
            self.github_sync, self.config_manager, self.stats_monitor
        )
        github_interval = self.config_manager.get_engine_setting('github_sync_check_interval', 3600)
        self.task_coordinator.register_task(
            'github_sync', github_task,
            interval=github_interval,
            enabled=True,
            dependencies=['component_initialization']
        )

        # API documentation update task
        api_doc_task = task_factory.create_api_doc_update_task(self.api_doc_generator)
        api_doc_interval = self.config_manager.get_engine_setting('api_doc_update_interval', 3600)
        self.task_coordinator.register_task(
            'api_doc_update', api_doc_task,
            interval=api_doc_interval,
            enabled=True,
            dependencies=['component_initialization']
        )

        # Database log processor task
        db_log_task = task_factory.create_database_log_processor_task(
            self.log_router, self.db_pool
        )
        self.task_coordinator.register_task(
            'database_log_processing', db_log_task,
            interval=60,  # Process database logs every minute
            enabled=True,
            dependencies=['component_initialization']
        )

        self.logger.info("All background tasks registered")

    def get_subscribed_channels(self) -> List[str]:
        """Return list of Redis channels this engine subscribes to"""
        return self.message_handler.get_subscribed_channels()

    async def process_message(self, message: Dict[str, Any]):
        """Process incoming message from message queue"""
        await self.message_handler.process_message(message)

    async def _setup_http_routes(self, app):
        """Setup HTTP routes for the ingestion engine"""

        # ============================================
        # AUTHENTICATION MIDDLEWARE (COMMENTED OUT)
        # ============================================
        # Uncomment the following lines to enable authentication
        # if self.auth_middleware is None:
        #     import redis.asyncio as redis_async
        #     async_redis = await redis_async.Redis(
        #         host=os.getenv('REDIS_HOST', 'localhost'),
        #         port=int(os.getenv('REDIS_PORT', 6379)),
        #         decode_responses=True
        #     )
        #
        #     from auth_middleware import KeycloakAuthMiddleware
        #     self.auth_middleware = KeycloakAuthMiddleware(
        #         keycloak_url=os.getenv('KEYCLOAK_URL', 'http://keycloak:8080'),
        #         realm='siemless',
        #         client_id='siemless-api',
        #         redis_client=async_redis
        #     )
        #     self.logger.info("Authentication middleware initialized")
        #
        # # Add middleware to app
        # app.middlewares.append(self.auth_middleware.auth_middleware)
        # self.logger.info("Authentication middleware added to HTTP app")
        # ============================================

        # Add ingestion-specific endpoints
        app.router.add_get('/sources', self._sources_endpoint)
        app.router.add_get('/stats', self._stats_endpoint)
        app.router.add_get('/tasks', self._tasks_endpoint)
        app.router.add_post('/sources/start', self._start_source_endpoint)
        app.router.add_post('/sources/stop', self._stop_source_endpoint)

        # Add CTI endpoints
        app.router.add_post('/cti/manual_update', self._cti_manual_update_endpoint)
        app.router.add_get('/cti/status', self._cti_status_endpoint)
        app.router.add_get('/cti/connectors', self._cti_connectors_endpoint)

        # Add Parser Generation endpoints
        app.router.add_post('/api/parsers/generate', self._generate_parser_endpoint)
        app.router.add_get('/api/parsers/{parser_id}', self._get_parser_endpoint)
        app.router.add_get('/api/parsers', self._list_parsers_endpoint)
        app.router.add_delete('/api/parsers/{parser_id}', self._delete_parser_endpoint)

        # Add Rule Deployment endpoints (EXTERNAL I/O - belongs in Ingestion)
        app.router.add_post('/api/rules/{rule_id}/deploy/elastic', self._deploy_to_elastic_endpoint)
        app.router.add_post('/api/rules/{rule_id}/deploy/{target}', self._deploy_to_siem_endpoint)
        app.router.add_post('/api/rules/deploy/bulk', self._bulk_deploy_endpoint)
        app.router.add_put('/api/rules/{rule_id}/deployment/elastic/{elastic_rule_id}', self._update_elastic_rule_endpoint)
        app.router.add_delete('/api/rules/deployment/elastic/{elastic_rule_id}', self._delete_elastic_rule_endpoint)

        self.logger.info("HTTP routes configured")

    async def _sources_endpoint(self, request):
        """HTTP endpoint for source information"""
        sources = {
            'active_sources': self.data_source_manager.get_active_sources(),
            'source_configs': self.config_manager.get_all_source_configs(),
            'health_summary': self.data_source_manager.get_source_health_summary()
        }
        return web.json_response(sources)

    async def _stats_endpoint(self, request):
        """HTTP endpoint for statistics"""
        stats = {
            'stats': self.stats_monitor.get_all_stats(),
            'performance_summary': self.stats_monitor.get_performance_summary(hours=1),
            'health_status': self.stats_monitor.get_health_status(self.data_source_manager)
        }
        return web.json_response(stats)

    async def _tasks_endpoint(self, request):
        """HTTP endpoint for task status"""
        task_status = self.task_coordinator.get_task_status()
        return web.json_response({'tasks': task_status})

    async def _start_source_endpoint(self, request):
        """HTTP endpoint to start a source"""
        try:
            data = await request.json()
            source_id = data.get('source_id')
            source_type = data.get('source_type', 'elasticsearch')

            if not source_id:
                return web.json_response({'error': 'source_id required'}, status=400)

            success = self.data_source_manager.start_source(source_id, source_type)

            if success:
                # Update active source count in stats
                active_count = len(self.data_source_manager.get_active_sources())
                self.stats_monitor.update_stat('sources_active', active_count)

                return web.json_response({'status': 'success', 'source_id': source_id})
            else:
                return web.json_response({'error': 'Failed to start source'}, status=500)

        except Exception as e:
            self.logger.error(f"Start source endpoint error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _stop_source_endpoint(self, request):
        """HTTP endpoint to stop a source"""
        try:
            data = await request.json()
            source_id = data.get('source_id')

            if not source_id:
                return web.json_response({'error': 'source_id required'}, status=400)

            success = self.data_source_manager.stop_source(source_id)

            if success:
                # Update active source count in stats
                active_count = len(self.data_source_manager.get_active_sources())
                self.stats_monitor.update_stat('sources_active', active_count)

                return web.json_response({'status': 'success', 'source_id': source_id})
            else:
                return web.json_response({'error': 'Source not found or failed to stop'}, status=404)

        except Exception as e:
            self.logger.error(f"Stop source endpoint error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    # Public interface methods for external access

    def get_engine_stats(self) -> Dict[str, Any]:
        """Get comprehensive engine statistics"""
        return {
            'stats': self.stats_monitor.get_all_stats(),
            'sources': self.data_source_manager.get_source_health_summary(),
            'tasks': self.task_coordinator.get_task_status(),
            'config': {
                'enabled_sources': self.config_manager.get_enabled_sources(),
                'engine_settings': self.config_manager.engine_settings
            }
        }

    def get_active_sources_info(self) -> Dict[str, Any]:
        """Get detailed information about active sources"""
        return self.data_source_manager.get_active_sources()

    def configure_source_type(self, source_type: str, config: Dict[str, Any]) -> bool:
        """Configure a source type"""
        return self.config_manager.update_source_config(source_type, config)

    def add_github_repository(self, repo_url: str, branch: str = 'main') -> bool:
        """Add a GitHub repository for pattern sync"""
        return self.config_manager.add_github_repository(repo_url, branch)

    async def manual_sync_github(self, repository: str = None) -> Dict[str, Any]:
        """Manually trigger GitHub sync"""
        if repository:
            result = await self.github_sync.sync_repository(repository)
        else:
            results = await self.github_sync.sync_all_repositories()
            result = {'repositories': len(results), 'status': 'completed'}

        return result

    async def _cti_manual_update_endpoint(self, request):
        """HTTP endpoint for manual CTI update using plugin manager"""
        from aiohttp import web
        from datetime import datetime, timedelta
        try:
            data = await request.json()
            source = data.get('source', 'all')
            since_days = data.get('since_days', 1)  # Default: fetch last 1 day
            limit = data.get('limit', 1000)

            since = datetime.utcnow() - timedelta(days=since_days)

            if source == 'all':
                # Fetch from all plugins
                all_indicators = await self.cti_plugin_manager.aggregate_indicators(
                    since=since,
                    limit=limit,
                    deduplicate=True
                )

                # Publish to backend for processing
                self.publish_message('ingestion.cti.indicators', {
                    'timestamp': datetime.now().isoformat(),
                    'indicators': [ind.to_dict() for ind in all_indicators],
                    'total_count': len(all_indicators)
                })

                return web.json_response({
                    'status': 'success',
                    'indicators_fetched': len(all_indicators),
                    'sources': list(self.cti_plugin_manager.plugins.keys())
                })
            else:
                # Fetch from specific plugin
                plugin = self.cti_plugin_manager.plugins.get(source)
                if not plugin:
                    return web.json_response({
                        'status': 'error',
                        'error': f'Plugin {source} not found'
                    }, status=404)

                indicators = await plugin.fetch_indicators(since=since, limit=limit)

                # Publish to backend (Backend listens on ingestion.cti.indicators)
                self.publish_message('ingestion.cti.indicators', {
                    'timestamp': datetime.now().isoformat(),
                    'source': source,
                    'indicators': [ind.to_dict() for ind in indicators],
                    'total_count': len(indicators)
                })

                return web.json_response({
                    'status': 'success',
                    'source': source,
                    'indicators_fetched': len(indicators)
                })

        except Exception as e:
            self.logger.error(f"CTI manual update error: {e}", exc_info=True)
            return web.json_response({
                'status': 'error',
                'error': str(e)
            }, status=500)

    async def _cti_status_endpoint(self, request):
        """HTTP endpoint for CTI status using plugin manager"""
        from aiohttp import web
        try:
            health = await self.cti_plugin_manager.health_check_all()

            status = {
                'plugin_count': len(self.cti_plugin_manager.plugins),
                'plugins': list(self.cti_plugin_manager.plugins.keys()),
                'health': health
            }

            return web.json_response(status)
        except Exception as e:
            self.logger.error(f"CTI status error: {e}", exc_info=True)
            return web.json_response({
                'status': 'error',
                'error': str(e)
            }, status=500)

    async def _cti_connectors_endpoint(self, request):
        """Simple endpoint to check CTI plugins"""
        from aiohttp import web
        return web.json_response({
            'plugins': list(self.cti_plugin_manager.plugins.keys()),
            'count': len(self.cti_plugin_manager.plugins),
            'source_types': {
                name: plugin.get_source_type()
                for name, plugin in self.cti_plugin_manager.plugins.items()
            }
        })

    async def reload_parsers(self, parser_id: str = None) -> Dict[str, Any]:
        """Reload parsers"""
        if parser_id:
            # Reload specific parser
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT pattern_type, pattern_data
                    FROM pattern_library
                    WHERE pattern_id = $1 AND is_active = TRUE
                """, parser_id)

                if row:
                    success = await self.parser_hot_reload.reload_parser(parser_id, row['pattern_type'], row['pattern_data'])
                    status = 'success' if success else 'failed'
                else:
                    status = 'not_found'
        else:
            # Reload all parsers
            await self.parser_hot_reload.initialize()
            status = 'success'

        # Update stats
        parser_status = await self.parser_hot_reload.get_parser_status()
        self.stats_monitor.update_stat('patterns_loaded', parser_status['total_parsers'])

        return {'status': status, 'total_parsers': parser_status['total_parsers']}

    async def _check_cti_plugin_health(self):
        """Check CTI plugin health on startup (runs once, doesn't block)"""
        try:
            self.logger.info("Checking CTI plugin health...")
            health_results = await self.cti_plugin_manager.health_check_all()

            for plugin_name, health in health_results.items():
                if health.get('healthy'):
                    self.logger.info(f"✓ {plugin_name} plugin healthy")
                else:
                    self.logger.warning(f"✗ {plugin_name} plugin unhealthy: {health.get('error', 'Unknown')}")

            healthy_count = sum(1 for h in health_results.values() if h.get('healthy'))
            if healthy_count > 0:
                self.logger.info(f"CTI Plugin Manager ready with {healthy_count}/{len(health_results)} healthy plugins")
            else:
                self.logger.warning("No CTI plugins are healthy")
        except Exception as e:
            self.logger.error(f"CTI plugin health check error: {e}", exc_info=True)

    async def _handle_cti_update_trigger(self, message_data: Dict[str, Any]):
        """
        Handle CTI update trigger from Backend Scheduler

        Message format:
        {
            'sources': ['otx', 'threatfox'],  # or ['all']
            'triggered_by': 'scheduler',
            'since_days': 1,
            'limit': 1000
        }
        """
        try:
            from datetime import datetime, timedelta

            sources = message_data.get('sources', ['all'])
            triggered_by = message_data.get('triggered_by', 'unknown')
            since_days = message_data.get('since_days', 1)
            limit = message_data.get('limit', 1000)

            self.logger.info(f"CTI update triggered by {triggered_by} for sources: {sources}")

            since = datetime.utcnow() - timedelta(days=since_days)

            if 'all' in sources or sources == ['all']:
                # Fetch from all plugins
                all_indicators = await self.cti_plugin_manager.aggregate_indicators(
                    since=since,
                    limit=limit,
                    deduplicate=True
                )

                # Publish to backend for processing
                self.publish_message('ingestion.cti.indicators', {
                    'timestamp': datetime.now().isoformat(),
                    'triggered_by': triggered_by,
                    'indicators': [ind.to_dict() for ind in all_indicators],
                    'total_count': len(all_indicators)
                })

                self.logger.info(f"Fetched {len(all_indicators)} indicators from all plugins")
            else:
                # Fetch from specific plugins
                total_indicators = []
                for source_name in sources:
                    plugin = self.cti_plugin_manager.plugins.get(source_name)
                    if plugin:
                        indicators = await plugin.fetch_indicators(since=since, limit=limit)
                        total_indicators.extend(indicators)
                        self.logger.info(f"Fetched {len(indicators)} indicators from {source_name}")
                    else:
                        self.logger.warning(f"Plugin {source_name} not found, skipping")

                # Publish to backend
                self.publish_message('ingestion.cti.indicators', {
                    'timestamp': datetime.now().isoformat(),
                    'triggered_by': triggered_by,
                    'indicators': [ind.to_dict() for ind in total_indicators],
                    'total_count': len(total_indicators)
                })

        except Exception as e:
            self.logger.error(f"CTI update trigger handler error: {e}", exc_info=True)

    # ============================================
    # Parser Generation Endpoints
    # ============================================

    async def _generate_parser_endpoint(self, request):
        """
        Generate a parser from sample logs
        POST /api/parsers/generate
        {
            "log_samples": ["log1", "log2", "log3"],
            "log_source": "firewall",
            "vendor": "Palo Alto",
            "target_siem": "elastic"
        }
        """
        try:
            data = await request.json()

            # Validate input
            if 'log_samples' not in data or not data['log_samples']:
                return web.json_response(
                    {'error': 'log_samples required (array of sample logs)'},
                    status=400
                )

            if 'target_siem' not in data:
                return web.json_response(
                    {'error': 'target_siem required (elastic, splunk, sentinel, etc.)'},
                    status=400
                )

            log_samples = data['log_samples']
            log_source = data.get('log_source', 'unknown')
            vendor = data.get('vendor', 'unknown')
            target_siem = data['target_siem']

            self.logger.info(f"Parser generation request: {log_source} ({vendor}) -> {target_siem}")

            # Generate unique parser ID
            import uuid
            parser_id = f"parser-{log_source}-{uuid.uuid4().hex[:8]}"

            # Step 1: Send to Intelligence Engine for AI analysis
            self.logger.info("Sending to Intelligence Engine for analysis...")
            self.publish_message('intelligence.parse_log_sample', {
                'parser_id': parser_id,
                'log_samples': log_samples,
                'log_source': log_source,
                'vendor': vendor,
                'target_siem': target_siem,
                'response_channel': f'ingestion.parser.{parser_id}.analysis'
            })

            # Wait for Intelligence response (with timeout)
            analysis_response = await self._wait_for_response(
                f'ingestion.parser.{parser_id}.analysis',
                timeout=60
            )

            if not analysis_response or 'error' in analysis_response:
                error_msg = analysis_response.get('error', 'Intelligence Engine timeout') if analysis_response else 'Intelligence Engine timeout'
                return web.json_response(
                    {'error': f'AI analysis failed: {error_msg}'},
                    status=500
                )

            self.logger.info(f"Intelligence response keys: {list(analysis_response.keys())}")

            # Extract parser from response
            parser = analysis_response.get('parser')
            if not parser:
                self.logger.error(f"No parser in analysis response: {analysis_response}")
                return web.json_response(
                    {'error': 'Intelligence Engine returned invalid response (no parser)'},
                    status=500
                )

            # Step 2: Send to Contextualization for validation
            self.logger.info("Sending to Contextualization for validation...")
            self.publish_message('contextualization.validate_parser', {
                'parser_id': parser_id,
                'parser': parser,
                'log_samples': log_samples,
                'response_channel': f'ingestion.parser.{parser_id}.validation'
            })

            validation_response = await self._wait_for_response(
                f'ingestion.parser.{parser_id}.validation',
                timeout=30
            )

            if not validation_response or 'error' in validation_response:
                error_msg = validation_response.get('error', 'Contextualization timeout') if validation_response else 'Contextualization timeout'
                return web.json_response(
                    {'error': f'Validation failed: {error_msg}'},
                    status=500
                )

            # Step 3: Send to Backend for SIEM mapping and storage
            self.logger.info("Sending to Backend for SIEM mapping and storage...")
            self.publish_message('backend.save_parser', {
                'parser_id': parser_id,
                'parser': analysis_response['parser'],
                'validation': validation_response,
                'target_siem': target_siem,
                'log_source': log_source,
                'vendor': vendor,
                'response_channel': f'ingestion.parser.{parser_id}.saved'
            })

            save_response = await self._wait_for_response(
                f'ingestion.parser.{parser_id}.saved',
                timeout=10
            )

            if not save_response or 'error' in save_response:
                error_msg = save_response.get('error', 'Backend save timeout') if save_response else 'Backend save timeout'
                return web.json_response(
                    {'error': f'Save failed: {error_msg}'},
                    status=500
                )

            # Success!
            self.logger.info(f"Parser generation complete: {parser_id}")
            return web.json_response({
                'parser_id': parser_id,
                'log_source': log_source,
                'vendor': vendor,
                'target_siem': target_siem,
                'coverage': validation_response.get('coverage', 0),
                'entity_count': validation_response.get('entity_count', 0),
                'relationship_count': validation_response.get('relationship_count', 0),
                'status': 'active',
                'field_mappings': save_response.get('field_mappings', {}),
                'created_at': datetime.now().isoformat()
            })

        except Exception as e:
            self.logger.error(f"Parser generation error: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def _wait_for_response(self, channel: str, timeout: int = 30):
        """Wait for a response on a specific Redis channel"""
        try:
            # Subscribe to response channel
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe(channel)

            # Wait for response with timeout
            start_time = time.time()
            while time.time() - start_time < timeout:
                message = pubsub.get_message(timeout=1)
                if message and message['type'] == 'message':
                    data = json.loads(message['data'])
                    pubsub.unsubscribe(channel)
                    # Extract actual data from message envelope
                    return data.get('data', data)
                await asyncio.sleep(0.1)

            # Timeout
            pubsub.unsubscribe(channel)
            return None

        except Exception as e:
            self.logger.error(f"Error waiting for response on {channel}: {e}")
            return None

    async def _get_parser_endpoint(self, request):
        """Get parser by ID"""
        parser_id = request.match_info['parser_id']

        try:
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT parser_id, pattern_type, pattern_data, is_active, usage_count, created_at
                    FROM pattern_library
                    WHERE pattern_id = $1
                """, parser_id)

                if not row:
                    return web.json_response(
                        {'error': 'Parser not found'},
                        status=404
                    )

                parser = {
                    'parser_id': row['parser_id'],
                    'pattern_type': row['pattern_type'],
                    'pattern_data': row['pattern_data'],
                    'is_active': row['is_active'],
                    'usage_count': row['usage_count'],
                    'created_at': row['created_at'].isoformat() if row['created_at'] else None
                }

            return web.json_response(parser)

        except Exception as e:
            self.logger.error(f"Error fetching parser: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def _list_parsers_endpoint(self, request):
        """List all parsers"""
        try:
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT pattern_id, pattern_type, is_active, usage_count, created_at
                    FROM pattern_library
                    WHERE pattern_type = 'parser'
                    ORDER BY created_at DESC
                    LIMIT 100
                """)

                parsers = []
                for row in rows:
                    parsers.append({
                        'parser_id': row['pattern_id'],
                        'pattern_type': row['pattern_type'],
                        'is_active': row['is_active'],
                        'usage_count': row['usage_count'],
                        'created_at': row['created_at'].isoformat() if row['created_at'] else None
                    })

            return web.json_response({
                'parsers': parsers,
                'count': len(parsers)
            })

        except Exception as e:
            self.logger.error(f"Error listing parsers: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def _delete_parser_endpoint(self, request):
        """Delete parser by ID"""
        parser_id = request.match_info['parser_id']

        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE pattern_library
                    SET is_active = false
                    WHERE pattern_id = $1
                """, parser_id)

            return web.json_response({
                'parser_id': parser_id,
                'status': 'deactivated'
            })

        except Exception as e:
            self.logger.error(f"Error deleting parser: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def _handle_pull_context(self, message_data: Dict[str, Any]):
        """
        Pull context using plugin system (supports all sources)

        Message format:
        {
            'request_id': 'uuid',
            'alert_id': 'alert-123',
            'query_type': 'ip',
            'query_value': '*************',
            'categories': ['asset', 'detection'],
            'time_range': {'start': '...', 'end': '...'}
        }
        """
        try:
            request_id = message_data.get('request_id')
            query_type = message_data.get('query_type', 'ip')
            query_value = message_data.get('query_value')
            categories = message_data.get('categories', ['asset', 'detection'])
            time_range = message_data.get('time_range')

            self.logger.info(f"[{request_id}] Pulling context for {query_type}={query_value}")

            # Create standardized query
            query = create_context_query(
                query_type=query_type,
                query_value=query_value,
                categories=categories,
                time_range=time_range
            )

            # Query all applicable plugins
            results = await self.context_manager.query_context(query)

            if not results:
                self.logger.warning(f"[{request_id}] No context found from any source")
                self.publish_message(f'delivery.context.{request_id}.complete', {
                    'request_id': request_id,
                    'query_type': query_type,
                    'query_value': query_value,
                    'results': {}
                })
                return

            # Log what we got
            for source_name, source_results in results.items():
                self.logger.info(f"[{request_id}] Got {len(source_results)} results from {source_name}")

            # Convert ContextResult dataclass objects to dicts for JSON serialization
            from dataclasses import asdict
            from enum import Enum

            def serialize_result(result):
                """Convert dataclass to dict, handling Enum values"""
                data = asdict(result)
                # Convert enum to string value
                if isinstance(data.get('category'), Enum):
                    data['category'] = data['category'].value
                elif hasattr(data.get('category'), 'value'):
                    data['category'] = data['category'].value
                return data

            serializable_results = {
                source_name: [serialize_result(result) for result in source_results]
                for source_name, source_results in results.items()
            }

            # Send raw data to Contextualization Engine for entity extraction
            self.logger.info(f"[{request_id}] Sending {sum(len(r) for r in results.values())} results to Contextualization")
            self.publish_message('contextualization.extract_from_context', {
                'request_id': request_id,
                'query_type': query_type,
                'query_value': query_value,
                'context_results': serializable_results,
                'response_channel': f'delivery.context.{request_id}.complete'
            })

        except Exception as e:
            self.logger.error(f"Error in _handle_pull_context: {e}", exc_info=True)
            # Publish error back to delivery
            self.publish_message(f'delivery.context.{message_data.get("request_id")}.error', {
                'error': str(e)
            })

    # OLD METHODS REMOVED - Now using plugin system
    # CrowdStrike queries handled by CrowdStrikeContextPlugin
    # Elastic queries can be added as ElasticContextPlugin in future

    # ========================================================================
    # Rule Deployment Handlers (EXTERNAL I/O - correct architectural location)
    # ========================================================================

    async def _handle_rule_deployment(self, message_data: Dict[str, Any]):
        """
        Handle rule deployment when Backend approves a rule

        Workflow:
        1. Backend generates rule → backend.rule.approved
        2. Ingestion listens → fetches rule from DB
        3. Ingestion deploys to external SIEM (Elastic/Splunk/etc.)
        4. Ingestion publishes: ingestion.rule.deployed
        """
        try:
            self.logger.info(f"_handle_rule_deployment called with data type: {type(message_data)}, keys: {list(message_data.keys()) if isinstance(message_data, dict) else 'NOT A DICT'}")
            rule_id = message_data.get('rule_id')
            target_siem = message_data.get('target_siem', 'elastic')
            auto_deploy = message_data.get('auto_deploy', True)

            if not auto_deploy:
                self.logger.info(f"Rule {rule_id} approved but auto_deploy=False, skipping deployment")
                return

            self.logger.info(f"Deploying approved rule {rule_id} to {target_siem}...")

            # Fetch rule details from Backend via Redis request/response
            rule = await self._fetch_rule_from_backend(rule_id)

            if not rule:
                self.logger.error(f"Failed to fetch rule {rule_id} from Backend")
                return

            # Deploy to target SIEM
            if target_siem == 'elastic':
                result = await self.rule_deployment.deploy_to_elastic(rule)
            elif target_siem == 'splunk':
                result = await self.rule_deployment.deploy_to_splunk(rule)
            elif target_siem == 'sentinel':
                result = await self.rule_deployment.deploy_to_sentinel(rule)
            elif target_siem == 'qradar':
                result = await self.rule_deployment.deploy_to_qradar(rule)
            elif target_siem == 'all':
                result = await self.rule_deployment.deploy_to_all_configured(rule)
            else:
                self.logger.error(f"Unknown SIEM target: {target_siem}")
                return

            # Publish deployment result
            self.publish_message('ingestion.rule.deployed', {
                'rule_id': rule_id,
                'target_siem': target_siem,
                'success': result.get('success'),
                'elastic_rule_id': result.get('elastic_rule_id'),
                'error': result.get('error'),
                'deployed_at': datetime.now().isoformat()
            })

        except Exception as e:
            self.logger.error(f"Error in _handle_rule_deployment: {e}", exc_info=True)

    async def _fetch_rule_from_backend(self, rule_id: str) -> Optional[Dict[str, Any]]:
        """Fetch rule from Backend Engine database"""
        try:
            # Fetch rule data from JSONB column
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    SELECT rule_data FROM detection_rules WHERE rule_id = $1
                """, rule_id)

                if not row:
                    self.logger.warning(f"Rule {rule_id} not found in database")
                    return None

                rule_data = row['rule_data']

            # Ensure rule_id is present in the dict
            if 'id' not in rule_data:
                rule_data['id'] = rule_id

            # Map JSONB fields to expected deployment format
            return {
                'rule_id': rule_id,
                'rule_name': rule_data.get('name') or rule_data.get('title', 'Unknown Rule'),
                'rule_type': rule_data.get('type', 'query'),
                'rule_content': rule_data.get('rule', ''),  # The actual Sigma/KQL rule
                'description': rule_data.get('description', ''),
                'severity': rule_data.get('level') or rule_data.get('severity', 'medium'),
                'mitre_tactics': rule_data.get('mitre_tactics', []),
                'mitre_techniques': rule_data.get('mitre_techniques', []),
                'enabled': rule_data.get('enabled', False),
                'tags': rule_data.get('tags') or rule_data.get('labels', []),
                'cti_source': rule_data.get('source', 'unknown'),
                'ioc_value': rule_data.get('ioc_value'),
                'ioc_type': rule_data.get('ioc_type'),
            }

        except Exception as e:
            self.logger.error(f"Error fetching rule from database: {e}", exc_info=True)
            return None

    # HTTP Endpoints for Rule Deployment

    async def _deploy_to_elastic_endpoint(self, request):
        """Deploy rule to Elastic Security"""
        try:
            rule_id = request.match_info['rule_id']
            rule = await self._fetch_rule_from_backend(rule_id)

            if not rule:
                return web.json_response({'error': 'Rule not found'}, status=404)

            result = await self.rule_deployment.deploy_to_elastic(rule)

            if result['success']:
                # Update database with deployment status
                await self._update_deployment_status(rule_id, 'elastic', result)

            return web.json_response(result)

        except Exception as e:
            self.logger.error(f"Deploy to Elastic endpoint error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _deploy_to_siem_endpoint(self, request):
        """Deploy rule to specified SIEM"""
        try:
            rule_id = request.match_info['rule_id']
            target = request.match_info['target']

            rule = await self._fetch_rule_from_backend(rule_id)

            if not rule:
                return web.json_response({'error': 'Rule not found'}, status=404)

            if target == 'elastic':
                result = await self.rule_deployment.deploy_to_elastic(rule)
            elif target == 'splunk':
                result = await self.rule_deployment.deploy_to_splunk(rule)
            elif target == 'sentinel':
                result = await self.rule_deployment.deploy_to_sentinel(rule)
            elif target == 'qradar':
                result = await self.rule_deployment.deploy_to_qradar(rule)
            else:
                return web.json_response({'error': f'Unknown target: {target}'}, status=400)

            if result['success']:
                await self._update_deployment_status(rule_id, target, result)

            return web.json_response(result)

        except Exception as e:
            self.logger.error(f"Deploy to SIEM endpoint error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _bulk_deploy_endpoint(self, request):
        """Deploy multiple rules in bulk"""
        try:
            data = await request.json()
            rule_ids = data.get('rule_ids', [])
            target = data.get('target', 'elastic')

            # Fetch all rules
            rules = []
            for rule_id in rule_ids:
                rule = await self._fetch_rule_from_backend(rule_id)
                if rule:
                    rules.append(rule)

            # Bulk deploy
            results = await self.rule_deployment.bulk_deploy(rules, target)

            # Update deployment statuses
            for result in results:
                if result.get('success'):
                    await self._update_deployment_status(
                        result.get('rule_id'),
                        target,
                        result
                    )

            return web.json_response({
                'total': len(results),
                'successful': len([r for r in results if r.get('success')]),
                'failed': len([r for r in results if not r.get('success')]),
                'results': results
            })

        except Exception as e:
            self.logger.error(f"Bulk deploy endpoint error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _update_elastic_rule_endpoint(self, request):
        """Update existing rule in Elastic Security"""
        try:
            rule_id = request.match_info['rule_id']
            elastic_rule_id = request.match_info['elastic_rule_id']

            rule = await self._fetch_rule_from_backend(rule_id)

            if not rule:
                return web.json_response({'error': 'Rule not found'}, status=404)

            result = await self.rule_deployment.update_elastic_rule(rule, elastic_rule_id)

            return web.json_response(result)

        except Exception as e:
            self.logger.error(f"Update Elastic rule endpoint error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _delete_elastic_rule_endpoint(self, request):
        """Delete rule from Elastic Security"""
        try:
            elastic_rule_id = request.match_info['elastic_rule_id']

            result = await self.rule_deployment.delete_elastic_rule(elastic_rule_id)

            return web.json_response(result)

        except Exception as e:
            self.logger.error(f"Delete Elastic rule endpoint error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _update_deployment_status(self, rule_id: str, target: str, result: Dict):
        """Update rule deployment status in database"""
        try:
            async with self.db_pool.acquire() as conn:
                if target == 'elastic':
                    await conn.execute("""
                        UPDATE detection_rules
                        SET deployed_to_elastic = true,
                            elastic_rule_id = $1,
                            elastic_version = $2,
                            last_deployed_at = NOW()
                        WHERE rule_id = $3
                    """, result.get('elastic_rule_id'), result.get('elastic_version', 1), rule_id)

                elif target == 'splunk':
                    await conn.execute("""
                        UPDATE detection_rules
                        SET deployed_to_splunk = true,
                            splunk_rule_id = $1,
                            last_deployed_at = NOW()
                        WHERE rule_id = $2
                    """, result.get('splunk_rule_id'), rule_id)

                elif target == 'sentinel':
                    await conn.execute("""
                        UPDATE detection_rules
                        SET deployed_to_sentinel = true,
                            sentinel_rule_id = $1,
                            last_deployed_at = NOW()
                        WHERE rule_id = $2
                    """, result.get('sentinel_rule_id'), rule_id)

                elif target == 'qradar':
                    await conn.execute("""
                        UPDATE detection_rules
                        SET deployed_to_qradar = true,
                            qradar_rule_id = $1,
                            last_deployed_at = NOW()
                        WHERE rule_id = $2
                    """, result.get('qradar_rule_id'), rule_id)

                else:
                    self.logger.warning(f"Unknown target for deployment status update: {target}")
                    return

        except Exception as e:
            self.logger.error(f"Error updating deployment status: {e}")


if __name__ == "__main__":
    async def main():
        engine = IngestionEngine()
        await engine.start()

    asyncio.run(main())