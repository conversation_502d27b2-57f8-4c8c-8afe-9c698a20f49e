/**
 * <PERSON>ert <PERSON>ustand Store
 * Manages alert queue state with enrichment
 */

import { create } from 'zustand'
import { alertService, entityService } from '../api/services'
import type {
  Alert,
  AlertDetail,
  AlertFilters,
  Entity,
  UpdateAlertRequest
} from '../types/api'

interface AlertState {
  // Data
  alerts: Alert[]
  selectedAlert: AlertDetail | null
  enrichedEntities: Map<string, Entity>  // entityId -> Entity

  filters: AlertFilters
  pagination: {
    page: number
    pageSize: number
    totalItems: number
    totalPages: number
  }

  // Loading states
  loading: {
    alerts: boolean
    detail: boolean
    entities: boolean
    update: boolean
  }

  // Error states
  error: {
    alerts: string | null
    detail: string | null
    entities: string | null
    update: string | null
  }

  // Actions
  fetchAlerts: (filters?: AlertFilters) => Promise<void>
  selectAlert: (alertId: string) => Promise<void>
  updateAlert: (alertId: string, updates: UpdateAlertRequest) => Promise<void>
  clearSelectedAlert: () => void
  setFilters: (filters: Partial<AlertFilters>) => void
  setPage: (page: number) => Promise<void>
  refreshAlerts: () => Promise<void>
}

export const useAlertStore = create<AlertState>((set, get) => ({
  // Initial state
  alerts: [],
  selectedAlert: null,
  enrichedEntities: new Map(),
  filters: {},
  pagination: {
    page: 1,
    pageSize: 50,
    totalItems: 0,
    totalPages: 0
  },
  loading: {
    alerts: false,
    detail: false,
    entities: false,
    update: false
  },
  error: {
    alerts: null,
    detail: null,
    entities: null,
    update: null
  },

  // Fetch alerts with filters
  fetchAlerts: async (filters?: AlertFilters) => {
    set((state) => ({
      loading: { ...state.loading, alerts: true },
      error: { ...state.error, alerts: null }
    }))

    try {
      const currentFilters = filters || get().filters
      const { page, pageSize } = get().pagination

      const response = await alertService.getAlerts(
        currentFilters,
        page,
        pageSize
      )

      set({
        alerts: response.data,
        filters: currentFilters,
        pagination: {
          ...get().pagination,
          totalItems: response.pagination.total_items,
          totalPages: response.pagination.total_pages
        },
        loading: { ...get().loading, alerts: false }
      })
    } catch (error) {
      set((state) => ({
        loading: { ...state.loading, alerts: false },
        error: {
          ...state.error,
          alerts: error instanceof Error ? error.message : 'Failed to load alerts'
        }
      }))
    }
  },

  // Select and load alert details with enriched entities
  selectAlert: async (alertId: string) => {
    set((state) => ({
      loading: { ...state.loading, detail: true, entities: true },
      error: { ...state.error, detail: null, entities: null }
    }))

    try {
      // Fetch alert detail
      const alert = await alertService.getAlertDetail(alertId)

      set({
        selectedAlert: alert,
        loading: { ...get().loading, detail: false }
      })

      // Fetch enriched entities in parallel
      if (alert.entities && alert.entities.length > 0) {
        try {
          const entityIds = alert.entities.map(e => e.entity_id)
          const entities = await Promise.all(
            entityIds.map(id => entityService.getEntity(id))
          )

          const enrichedMap = new Map<string, Entity>()
          entities.forEach(entity => {
            enrichedMap.set(entity.entity_id, entity)
          })

          set({
            enrichedEntities: enrichedMap,
            loading: { ...get().loading, entities: false }
          })
        } catch (error) {
          set((state) => ({
            loading: { ...state.loading, entities: false },
            error: {
              ...state.error,
              entities: error instanceof Error ? error.message : 'Failed to load enriched entities'
            }
          }))
        }
      } else {
        set({
          loading: { ...get().loading, entities: false }
        })
      }
    } catch (error) {
      set((state) => ({
        loading: { ...state.loading, detail: false, entities: false },
        error: {
          ...state.error,
          detail: error instanceof Error ? error.message : 'Failed to load alert'
        }
      }))
    }
  },

  // Update alert
  updateAlert: async (alertId: string, updates: UpdateAlertRequest) => {
    set((state) => ({
      loading: { ...state.loading, update: true },
      error: { ...state.error, update: null }
    }))

    try {
      const updatedAlert = await alertService.updateAlert(alertId, updates)

      // Update in list
      set((state) => ({
        alerts: state.alerts.map((a) =>
          a.alert_id === alertId ? { ...a, ...updatedAlert } : a
        ),
        selectedAlert: state.selectedAlert?.alert_id === alertId
          ? { ...state.selectedAlert, ...updatedAlert }
          : state.selectedAlert,
        loading: { ...state.loading, update: false }
      }))
    } catch (error) {
      set((state) => ({
        loading: { ...state.loading, update: false },
        error: {
          ...state.error,
          update: error instanceof Error ? error.message : 'Failed to update alert'
        }
      }))
    }
  },

  // Clear selected alert
  clearSelectedAlert: () => {
    set({
      selectedAlert: null,
      enrichedEntities: new Map()
    })
  },

  // Update filters
  setFilters: (filters: Partial<AlertFilters>) => {
    set((state) => ({
      filters: { ...state.filters, ...filters },
      pagination: { ...state.pagination, page: 1 } // Reset to page 1
    }))
    get().fetchAlerts()
  },

  // Change page
  setPage: async (page: number) => {
    set((state) => ({
      pagination: { ...state.pagination, page }
    }))
    await get().fetchAlerts()
  },

  // Refresh current view
  refreshAlerts: async () => {
    await get().fetchAlerts()
  }
}))
