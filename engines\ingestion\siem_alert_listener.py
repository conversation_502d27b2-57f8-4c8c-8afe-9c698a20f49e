"""
SIEM Alert Listener - Hybrid Architecture
Supports webhooks (primary) + polling (fallback) for multiple SIEMs

Supported SIEMs:
- Elastic Security (webhook + REST API)
- Splunk (webhook + REST API)
- Microsoft Sentinel (webhook + Azure API)
- QRadar (webhook + REST API)
- Chronicle (webhook + API)
"""

import asyncio
import json
import hashlib
from typing import Dict, List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging
from aiohttp import web, ClientSession
import redis.asyncio as redis_async


@dataclass
class Alert:
    """Normalized alert structure"""
    id: str
    source_siem: str
    title: str
    description: str
    severity: str
    timestamp: datetime
    rule_id: Optional[str]
    rule_name: Optional[str]
    entities: Dict  # IPs, users, hosts, etc.
    raw_alert: Dict
    mitre_techniques: List[str]
    investigation_id: Optional[str] = None


class SIEMAlertListener:
    """
    Hybrid SIEM alert ingestion:
    - Webhook receivers (real-time, preferred)
    - Polling fallback (for SIEMs without webhook support)
    - Alert normalization
    - Deduplication
    - Auto-investigation triggering
    """

    def __init__(self, redis_client, logger: Optional[logging.Logger] = None):
        self.redis = redis_client
        self.logger = logger or logging.getLogger(__name__)

        # Alert cache for deduplication (5-minute window)
        self.seen_alerts: Set[str] = set()
        self.cache_ttl = 300  # 5 minutes

        # Polling configurations per SIEM
        self.polling_configs = {}

        # Webhook authentication tokens
        self.webhook_tokens = {}

    async def start_webhook_server(self, host: str = '0.0.0.0', port: int = 9000):
        """Start webhook receiver server"""
        app = web.Application()

        # Webhook endpoints for each SIEM
        app.router.add_post('/webhook/elastic', self.handle_elastic_webhook)
        app.router.add_post('/webhook/splunk', self.handle_splunk_webhook)
        app.router.add_post('/webhook/sentinel', self.handle_sentinel_webhook)
        app.router.add_post('/webhook/qradar', self.handle_qradar_webhook)
        app.router.add_post('/webhook/chronicle', self.handle_chronicle_webhook)

        # Health check
        app.router.add_get('/health', self.health_check)

        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, host, port)
        await site.start()

        self.logger.info(f"SIEM webhook server started on {host}:{port}")

        # Keep server running
        while True:
            await asyncio.sleep(3600)

    async def health_check(self, request: web.Request) -> web.Response:
        """Health check endpoint"""
        return web.json_response({'status': 'healthy', 'service': 'siem_alert_listener'})

    # Webhook Handlers
    async def handle_elastic_webhook(self, request: web.Request) -> web.Response:
        """Handle Elastic Security webhook"""
        try:
            # Verify webhook token
            token = request.headers.get('Authorization', '').replace('Bearer ', '')
            if not self._verify_webhook_token('elastic', token):
                return web.json_response({'error': 'Unauthorized'}, status=401)

            data = await request.json()

            # Elastic sends alerts as array
            alerts = data.get('alerts', [data])

            processed_count = 0
            for alert_data in alerts:
                alert = self._normalize_elastic_alert(alert_data)
                if await self._process_alert(alert):
                    processed_count += 1

            return web.json_response({
                'status': 'success',
                'processed': processed_count,
                'total': len(alerts)
            })

        except Exception as e:
            self.logger.error(f"Error handling Elastic webhook: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def handle_splunk_webhook(self, request: web.Request) -> web.Response:
        """Handle Splunk webhook"""
        try:
            token = request.headers.get('Authorization', '').replace('Bearer ', '')
            if not self._verify_webhook_token('splunk', token):
                return web.json_response({'error': 'Unauthorized'}, status=401)

            data = await request.json()

            # Splunk sends results array
            results = data.get('results', [data.get('result', {})])

            processed_count = 0
            for result in results:
                alert = self._normalize_splunk_alert(result)
                if await self._process_alert(alert):
                    processed_count += 1

            return web.json_response({
                'status': 'success',
                'processed': processed_count
            })

        except Exception as e:
            self.logger.error(f"Error handling Splunk webhook: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def handle_sentinel_webhook(self, request: web.Request) -> web.Response:
        """Handle Microsoft Sentinel webhook"""
        try:
            token = request.headers.get('Authorization', '').replace('Bearer ', '')
            if not self._verify_webhook_token('sentinel', token):
                return web.json_response({'error': 'Unauthorized'}, status=401)

            data = await request.json()

            # Sentinel sends incidents
            alert = self._normalize_sentinel_alert(data)
            processed = await self._process_alert(alert)

            return web.json_response({
                'status': 'success',
                'processed': 1 if processed else 0
            })

        except Exception as e:
            self.logger.error(f"Error handling Sentinel webhook: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def handle_qradar_webhook(self, request: web.Request) -> web.Response:
        """Handle QRadar webhook"""
        try:
            token = request.headers.get('Authorization', '').replace('Bearer ', '')
            if not self._verify_webhook_token('qradar', token):
                return web.json_response({'error': 'Unauthorized'}, status=401)

            data = await request.json()

            # QRadar sends offense data
            alert = self._normalize_qradar_alert(data)
            processed = await self._process_alert(alert)

            return web.json_response({
                'status': 'success',
                'processed': 1 if processed else 0
            })

        except Exception as e:
            self.logger.error(f"Error handling QRadar webhook: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def handle_chronicle_webhook(self, request: web.Request) -> web.Response:
        """Handle Google Chronicle webhook"""
        try:
            token = request.headers.get('Authorization', '').replace('Bearer ', '')
            if not self._verify_webhook_token('chronicle', token):
                return web.json_response({'error': 'Unauthorized'}, status=401)

            data = await request.json()

            # Chronicle sends detection data
            alert = self._normalize_chronicle_alert(data)
            processed = await self._process_alert(alert)

            return web.json_response({
                'status': 'success',
                'processed': 1 if processed else 0
            })

        except Exception as e:
            self.logger.error(f"Error handling Chronicle webhook: {e}")
            return web.json_response({'error': str(e)}, status=500)

    # Alert Normalization
    def _normalize_elastic_alert(self, alert_data: Dict) -> Alert:
        """Normalize Elastic Security alert"""
        # Extract MITRE techniques
        mitre_techniques = []
        for threat in alert_data.get('kibana.alert.rule.threat', []):
            for technique in threat.get('technique', []):
                mitre_techniques.append(technique.get('id'))

        return Alert(
            id=alert_data.get('kibana.alert.uuid', alert_data.get('_id', '')),
            source_siem='elastic',
            title=alert_data.get('kibana.alert.rule.name', 'Unknown'),
            description=alert_data.get('kibana.alert.rule.description', ''),
            severity=alert_data.get('kibana.alert.severity', 'medium'),
            timestamp=datetime.fromisoformat(alert_data.get('@timestamp', datetime.utcnow().isoformat()).replace('Z', '+00:00')),
            rule_id=alert_data.get('kibana.alert.rule.uuid'),
            rule_name=alert_data.get('kibana.alert.rule.name'),
            entities=self._extract_elastic_entities(alert_data),
            raw_alert=alert_data,
            mitre_techniques=mitre_techniques
        )

    def _normalize_splunk_alert(self, result: Dict) -> Alert:
        """Normalize Splunk alert"""
        return Alert(
            id=result.get('_key', result.get('sid', '')),
            source_siem='splunk',
            title=result.get('search_name', 'Unknown'),
            description=result.get('description', ''),
            severity=result.get('severity', 'medium'),
            timestamp=datetime.fromtimestamp(float(result.get('_time', 0))),
            rule_id=result.get('search_id'),
            rule_name=result.get('search_name'),
            entities=self._extract_splunk_entities(result),
            raw_alert=result,
            mitre_techniques=result.get('mitre_technique', '').split(',') if result.get('mitre_technique') else []
        )

    def _normalize_sentinel_alert(self, incident: Dict) -> Alert:
        """Normalize Microsoft Sentinel incident"""
        properties = incident.get('properties', {})

        return Alert(
            id=incident.get('name', ''),
            source_siem='sentinel',
            title=properties.get('title', 'Unknown'),
            description=properties.get('description', ''),
            severity=properties.get('severity', 'Medium').lower(),
            timestamp=datetime.fromisoformat(properties.get('createdTimeUtc', datetime.utcnow().isoformat()).replace('Z', '+00:00')),
            rule_id=properties.get('alertId'),
            rule_name=properties.get('title'),
            entities=self._extract_sentinel_entities(properties),
            raw_alert=incident,
            mitre_techniques=self._extract_sentinel_mitre(properties)
        )

    def _normalize_qradar_alert(self, offense: Dict) -> Alert:
        """Normalize QRadar offense"""
        return Alert(
            id=str(offense.get('id', '')),
            source_siem='qradar',
            title=offense.get('description', 'Unknown'),
            description=offense.get('offense_source', ''),
            severity=self._map_qradar_severity(offense.get('severity', 5)),
            timestamp=datetime.fromtimestamp(offense.get('start_time', 0) / 1000),
            rule_id=str(offense.get('id')),
            rule_name=offense.get('description'),
            entities=self._extract_qradar_entities(offense),
            raw_alert=offense,
            mitre_techniques=[]  # QRadar doesn't provide MITRE out of box
        )

    def _normalize_chronicle_alert(self, detection: Dict) -> Alert:
        """Normalize Chronicle detection"""
        return Alert(
            id=detection.get('id', ''),
            source_siem='chronicle',
            title=detection.get('detection', {}).get('ruleName', 'Unknown'),
            description=detection.get('detection', {}).get('description', ''),
            severity=detection.get('detection', {}).get('severity', 'MEDIUM').lower(),
            timestamp=datetime.fromisoformat(detection.get('createdTime', datetime.utcnow().isoformat()).replace('Z', '+00:00')),
            rule_id=detection.get('detection', {}).get('ruleId'),
            rule_name=detection.get('detection', {}).get('ruleName'),
            entities=self._extract_chronicle_entities(detection),
            raw_alert=detection,
            mitre_techniques=detection.get('detection', {}).get('mitreTechniques', [])
        )

    # Entity Extraction
    def _extract_elastic_entities(self, alert: Dict) -> Dict:
        """Extract entities from Elastic alert"""
        entities = {
            'ips': set(),
            'users': set(),
            'hosts': set(),
            'processes': set(),
            'files': set()
        }

        # IP addresses
        if 'source.ip' in alert:
            entities['ips'].add(alert['source.ip'])
        if 'destination.ip' in alert:
            entities['ips'].add(alert['destination.ip'])

        # Users
        if 'user.name' in alert:
            entities['users'].add(alert['user.name'])

        # Hosts
        if 'host.name' in alert:
            entities['hosts'].add(alert['host.name'])

        # Processes
        if 'process.name' in alert:
            entities['processes'].add(alert['process.name'])

        # Files
        if 'file.path' in alert:
            entities['files'].add(alert['file.path'])

        # Convert sets to lists for JSON serialization
        return {k: list(v) for k, v in entities.items()}

    def _extract_splunk_entities(self, result: Dict) -> Dict:
        """Extract entities from Splunk result"""
        entities = {
            'ips': [],
            'users': [],
            'hosts': [],
            'processes': [],
            'files': []
        }

        # Common Splunk fields
        if 'src_ip' in result:
            entities['ips'].append(result['src_ip'])
        if 'dest_ip' in result:
            entities['ips'].append(result['dest_ip'])
        if 'user' in result:
            entities['users'].append(result['user'])
        if 'host' in result:
            entities['hosts'].append(result['host'])

        return entities

    def _extract_sentinel_entities(self, properties: Dict) -> Dict:
        """Extract entities from Sentinel incident"""
        entities = {
            'ips': [],
            'users': [],
            'hosts': [],
            'processes': [],
            'files': []
        }

        # Sentinel provides structured entities
        for entity in properties.get('entities', []):
            entity_type = entity.get('kind', '').lower()

            if entity_type == 'ip':
                entities['ips'].append(entity.get('properties', {}).get('address'))
            elif entity_type == 'account':
                entities['users'].append(entity.get('properties', {}).get('accountName'))
            elif entity_type == 'host':
                entities['hosts'].append(entity.get('properties', {}).get('hostName'))
            elif entity_type == 'process':
                entities['processes'].append(entity.get('properties', {}).get('processId'))
            elif entity_type == 'file':
                entities['files'].append(entity.get('properties', {}).get('fileName'))

        return entities

    def _extract_qradar_entities(self, offense: Dict) -> Dict:
        """Extract entities from QRadar offense"""
        return {
            'ips': [offense.get('offense_source', '')],
            'users': [],
            'hosts': [],
            'processes': [],
            'files': []
        }

    def _extract_chronicle_entities(self, detection: Dict) -> Dict:
        """Extract entities from Chronicle detection"""
        entities = {
            'ips': [],
            'users': [],
            'hosts': [],
            'processes': [],
            'files': []
        }

        collectionElements = detection.get('collectionElements', [])
        for element in collectionElements:
            references = element.get('references', [])
            for ref in references:
                if 'principalAssetIdentifier' in ref:
                    entities['hosts'].append(ref['principalAssetIdentifier'])

        return entities

    def _extract_sentinel_mitre(self, properties: Dict) -> List[str]:
        """Extract MITRE techniques from Sentinel alert"""
        techniques = []
        for tactic in properties.get('additionalData', {}).get('tactics', []):
            # Sentinel stores tactics, not techniques - would need enrichment
            pass
        return techniques

    def _map_qradar_severity(self, severity: int) -> str:
        """Map QRadar severity (1-10) to standard"""
        if severity >= 8:
            return 'critical'
        elif severity >= 6:
            return 'high'
        elif severity >= 4:
            return 'medium'
        else:
            return 'low'

    # Alert Processing
    async def _process_alert(self, alert: Alert) -> bool:
        """Process normalized alert"""
        try:
            # Deduplication check
            alert_hash = self._hash_alert(alert)
            if alert_hash in self.seen_alerts:
                self.logger.debug(f"Duplicate alert skipped: {alert.id}")
                return False

            self.seen_alerts.add(alert_hash)

            # Publish to Redis for investigation engine
            await self.redis.publish('ingestion.alerts.received', json.dumps({
                'alert_id': alert.id,
                'source_siem': alert.source_siem,
                'title': alert.title,
                'severity': alert.severity,
                'timestamp': alert.timestamp.isoformat(),
                'rule_id': alert.rule_id,
                'entities': alert.entities,
                'mitre_techniques': alert.mitre_techniques,
                'raw_alert': alert.raw_alert
            }))

            self.logger.info(f"Alert processed: {alert.source_siem} - {alert.title} ({alert.severity})")

            # Auto-create investigation for high/critical alerts
            if alert.severity in ['high', 'critical']:
                await self._trigger_investigation(alert)

            return True

        except Exception as e:
            self.logger.error(f"Error processing alert {alert.id}: {e}")
            return False

    def _hash_alert(self, alert: Alert) -> str:
        """Generate hash for deduplication"""
        # Hash based on SIEM, rule, and key entities
        hash_input = f"{alert.source_siem}:{alert.rule_id}:{alert.timestamp.strftime('%Y%m%d%H%M')}"
        return hashlib.md5(hash_input.encode()).hexdigest()

    async def _trigger_investigation(self, alert: Alert):
        """Trigger auto-investigation for high-severity alerts"""
        try:
            investigation_data = {
                'alert_id': alert.id,
                'source_siem': alert.source_siem,
                'title': f"Investigation: {alert.title}",
                'severity': alert.severity,
                'entities': alert.entities,
                'mitre_techniques': alert.mitre_techniques,
                'timestamp': alert.timestamp.isoformat()
            }

            # Publish to investigation channel
            await self.redis.publish('investigation.create', json.dumps(investigation_data))

            self.logger.info(f"Auto-investigation triggered for alert: {alert.id}")

        except Exception as e:
            self.logger.error(f"Failed to trigger investigation: {e}")

    # Webhook Security
    def _verify_webhook_token(self, siem: str, token: str) -> bool:
        """Verify webhook authentication token"""
        expected_token = self.webhook_tokens.get(siem)
        if not expected_token:
            # If no token configured, allow (for development)
            return True
        return token == expected_token

    def register_webhook_token(self, siem: str, token: str):
        """Register webhook authentication token"""
        self.webhook_tokens[siem] = token
        self.logger.info(f"Webhook token registered for {siem}")

    # Polling Fallback (for SIEMs without webhook support)
    async def start_polling(self, siem: str, config: Dict):
        """Start polling for a SIEM"""
        self.polling_configs[siem] = config
        asyncio.create_task(self._poll_siem(siem))

    async def _poll_siem(self, siem: str):
        """Poll SIEM for new alerts"""
        config = self.polling_configs.get(siem)
        if not config:
            return

        interval = config.get('interval', 60)  # Default 60 seconds

        while True:
            try:
                if siem == 'elastic':
                    await self._poll_elastic(config)
                elif siem == 'splunk':
                    await self._poll_splunk(config)
                elif siem == 'sentinel':
                    await self._poll_sentinel(config)
                elif siem == 'qradar':
                    await self._poll_qradar(config)
                elif siem == 'chronicle':
                    await self._poll_chronicle(config)
                else:
                    self.logger.warning(f"Unknown SIEM type for polling: {siem}")

                await asyncio.sleep(interval)

            except Exception as e:
                self.logger.error(f"Error polling {siem}: {e}")
                await asyncio.sleep(interval)

    async def _poll_elastic(self, config: Dict):
        """Poll Elastic for new alerts"""
        try:
            async with ClientSession() as session:
                # Get last poll checkpoint
                last_poll = config.get('last_poll', datetime.utcnow() - timedelta(minutes=5))

                headers = {
                    'Authorization': f"ApiKey {config['api_key']}",
                    'Content-Type': 'application/json'
                }

                # Query for new alerts since last poll
                query = {
                    "query": {
                        "bool": {
                            "must": [
                                {"term": {"kibana.alert.workflow_status": "open"}},
                                {"range": {"@timestamp": {"gte": last_poll.isoformat()}}}
                            ]
                        }
                    },
                    "sort": [{"@timestamp": "desc"}],
                    "size": 100
                }

                url = f"{config['url']}/.alerts-security.alerts-default/_search"

                async with session.post(url, headers=headers, json=query) as response:
                    if response.status == 200:
                        data = await response.json()
                        hits = data.get('hits', {}).get('hits', [])

                        for hit in hits:
                            alert = self._normalize_elastic_alert(hit['_source'])
                            await self._process_alert(alert)

                        # Update checkpoint
                        if hits:
                            config['last_poll'] = datetime.utcnow()

                        self.logger.info(f"Polled Elastic: {len(hits)} alerts")
                    else:
                        self.logger.error(f"Elastic poll failed: {response.status}")

        except Exception as e:
            self.logger.error(f"Error polling Elastic: {e}")

    async def _poll_splunk(self, config: Dict):
        """Poll Splunk for new alerts"""
        try:
            async with ClientSession() as session:
                last_poll = config.get('last_poll', datetime.utcnow() - timedelta(minutes=5))

                headers = {
                    'Authorization': f"Bearer {config['api_token']}",
                    'Content-Type': 'application/json'
                }

                # Splunk search query for notable events
                search_query = f'search index=notable earliest={int(last_poll.timestamp())} | head 100'

                # Create search job
                search_url = f"{config['url']}/services/search/jobs"
                search_data = {'search': search_query, 'output_mode': 'json'}

                async with session.post(search_url, headers=headers, data=search_data) as response:
                    if response.status == 201:
                        job_data = await response.json()
                        sid = job_data.get('sid')

                        # Wait for results
                        await asyncio.sleep(2)

                        # Get results
                        results_url = f"{config['url']}/services/search/jobs/{sid}/results"
                        async with session.get(results_url, headers=headers, params={'output_mode': 'json'}) as results_response:
                            if results_response.status == 200:
                                results = await results_response.json()

                                for result in results.get('results', []):
                                    alert = self._normalize_splunk_alert(result)
                                    await self._process_alert(alert)

                                config['last_poll'] = datetime.utcnow()
                                self.logger.info(f"Polled Splunk: {len(results.get('results', []))} alerts")

        except Exception as e:
            self.logger.error(f"Error polling Splunk: {e}")

    async def _poll_sentinel(self, config: Dict):
        """Poll Microsoft Sentinel for new incidents"""
        try:
            async with ClientSession() as session:
                last_poll = config.get('last_poll', datetime.utcnow() - timedelta(minutes=5))

                headers = {
                    'Authorization': f"Bearer {config['access_token']}",
                    'Content-Type': 'application/json'
                }

                # Sentinel API endpoint
                url = f"https://management.azure.com/subscriptions/{config['subscription_id']}/resourceGroups/{config['resource_group']}/providers/Microsoft.OperationalInsights/workspaces/{config['workspace_id']}/providers/Microsoft.SecurityInsights/incidents"

                params = {
                    'api-version': '2021-04-01',
                    '$filter': f"properties/createdTimeUtc ge {last_poll.isoformat()}",
                    '$orderby': 'properties/createdTimeUtc desc',
                    '$top': 100
                }

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        incidents = data.get('value', [])

                        for incident in incidents:
                            alert = self._normalize_sentinel_alert(incident)
                            await self._process_alert(alert)

                        config['last_poll'] = datetime.utcnow()
                        self.logger.info(f"Polled Sentinel: {len(incidents)} incidents")

        except Exception as e:
            self.logger.error(f"Error polling Sentinel: {e}")

    async def _poll_qradar(self, config: Dict):
        """Poll QRadar for new offenses"""
        try:
            async with ClientSession() as session:
                last_poll = config.get('last_poll', datetime.utcnow() - timedelta(minutes=5))

                headers = {
                    'SEC': config['api_token'],
                    'Content-Type': 'application/json'
                }

                # QRadar API endpoint for offenses
                url = f"{config['url']}/api/siem/offenses"

                params = {
                    'filter': f"start_time >= {int(last_poll.timestamp() * 1000)}",
                    'sort': '-start_time',
                    'range': '0-99'
                }

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        offenses = await response.json()

                        for offense in offenses:
                            alert = self._normalize_qradar_alert(offense)
                            await self._process_alert(alert)

                        config['last_poll'] = datetime.utcnow()
                        self.logger.info(f"Polled QRadar: {len(offenses)} offenses")

        except Exception as e:
            self.logger.error(f"Error polling QRadar: {e}")

    async def _poll_chronicle(self, config: Dict):
        """Poll Google Chronicle for new detections"""
        try:
            async with ClientSession() as session:
                last_poll = config.get('last_poll', datetime.utcnow() - timedelta(minutes=5))

                headers = {
                    'Authorization': f"Bearer {config['access_token']}",
                    'Content-Type': 'application/json'
                }

                # Chronicle API endpoint
                url = f"{config['url']}/v2/detect/rules:listDetections"

                payload = {
                    'startTime': last_poll.isoformat(),
                    'pageSize': 100
                }

                async with session.post(url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        detections = data.get('detections', [])

                        for detection in detections:
                            alert = self._normalize_chronicle_alert(detection)
                            await self._process_alert(alert)

                        config['last_poll'] = datetime.utcnow()
                        self.logger.info(f"Polled Chronicle: {len(detections)} detections")

        except Exception as e:
            self.logger.error(f"Error polling Chronicle: {e}")
