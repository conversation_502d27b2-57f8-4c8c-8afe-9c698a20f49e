/**
 * Overview Tab - Executive Summary Styles
 */

.overview-tab {
  max-width: 1400px;
  margin: 0 auto;
}

/* Summary Grid */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.summary-card {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: box-shadow 0.2s;
}

.summary-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.threat-card {
  border-left: 4px solid;
}

.card-header {
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.card-body {
  padding: 20px;
}

/* Threat Assessment Card */
.threat-level {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
}

.confidence {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 16px;
}

.threat-indicators {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #374151;
}

.indicator-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #10b981;
  color: #ffffff;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.view-details-btn {
  margin-top: 16px;
  width: 100%;
  padding: 8px 16px;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.view-details-btn:hover {
  background: #2563eb;
}

/* Correlation Summary Card */
.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.stat-row:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.attack-stages {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.stages-label {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.stage-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  font-size: 13px;
  color: #374151;
}

.stage-check {
  color: #10b981;
  font-weight: 700;
}

.no-data {
  text-align: center;
  padding: 32px 16px;
  color: #9ca3af;
}

.no-data p {
  margin: 4px 0;
}

.no-data-hint {
  font-size: 12px;
  font-style: italic;
}

/* AI Verdict Section */
.ai-verdict-section {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.section-header {
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.section-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.verdict-card {
  padding: 20px;
}

.verdict-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  margin-bottom: 24px;
}

.verdict-label {
  font-size: 13px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.verdict-value {
  font-size: 20px;
  font-weight: 700;
  flex: 1;
}

.verdict-confidence {
  font-size: 14px;
  color: #6b7280;
  background: #ffffff;
  padding: 4px 12px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.verdict-body {
  display: grid;
  gap: 24px;
}

.findings-section h4,
.recommended-actions h4 {
  font-size: 15px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 12px;
}

.findings-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.findings-list li {
  padding: 16px;
  background: #f9fafb;
  border-left: 3px solid #3b82f6;
  border-radius: 4px;
}

.findings-list li strong {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 6px;
}

.findings-list li p {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
  line-height: 1.5;
}

.action-recommendation {
  padding: 16px;
  background: #f9fafb;
  border-radius: 6px;
  margin-bottom: 16px;
}

.action-priority {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  margin-bottom: 8px;
}

.action-priority.critical {
  background: #fef2f2;
  color: #dc2626;
}

.action-priority.warning {
  background: #fef3c7;
  color: #d97706;
}

.action-priority.info {
  background: #eff6ff;
  color: #3b82f6;
}

.action-recommendation p {
  margin: 0;
  font-size: 14px;
  color: #374151;
}

.suggested-steps h5 {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 10px;
}

.suggested-steps ol {
  margin: 0;
  padding-left: 20px;
}

.suggested-steps li {
  font-size: 13px;
  color: #374151;
  line-height: 1.6;
  margin-bottom: 6px;
}

/* Playbook Section */
.playbook-section {
  margin-top: 24px;
  padding: 20px;
  background: #eff6ff;
  border: 2px solid #3b82f6;
  border-radius: 8px;
}

.playbook-available {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
}

.playbook-icon {
  font-size: 24px;
}

.playbook-text {
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
}

.playbook-details {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 16px;
}

.playbook-actions {
  display: flex;
  gap: 12px;
}

.execute-playbook-btn {
  flex: 1;
  padding: 10px 20px;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}

.execute-playbook-btn:hover {
  background: #2563eb;
}

.view-playbook-btn {
  padding: 10px 20px;
  background: #ffffff;
  color: #3b82f6;
  border: 2px solid #3b82f6;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.view-playbook-btn:hover {
  background: #f0f9ff;
}

/* Responsive */
@media (max-width: 768px) {
  .summary-grid {
    grid-template-columns: 1fr;
  }

  .verdict-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .verdict-value {
    font-size: 18px;
  }

  .playbook-actions {
    flex-direction: column;
  }

  .view-playbook-btn {
    width: 100%;
  }
}

/* Print Styles */
@media print {
  .view-details-btn,
  .execute-playbook-btn,
  .view-playbook-btn {
    display: none;
  }

  .summary-card,
  .ai-verdict-section {
    border: 1px solid #000000;
    page-break-inside: avoid;
  }
}
