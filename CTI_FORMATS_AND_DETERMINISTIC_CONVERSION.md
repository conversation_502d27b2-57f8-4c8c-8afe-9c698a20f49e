# CTI Formats and Deterministic Conversion Architecture

## Complete List of Structured CTI Formats (90% Deterministic)

### Currently Implemented Plugins

#### 1. **OTX (AlienVault Open Threat Exchange)** - JSON Format
**Plugin**: `otx_plugin.py`
**Format**: Proprietary JSON (OTX Pulse format)
**Structure**:
```json
{
  "indicators": [
    {
      "type": "IPv4",
      "indicator": "*******",
      "description": "C2 server",
      "created": "2025-10-03T10:00:00Z"
    }
  ],
  "pulse_info": {
    "name": "APT Campaign",
    "tags": ["apt", "malware"],
    "TLP": "white"
  }
}
```
**Deterministic Conversion**: ✅ Template-based IP/Domain/Hash → Sigma/KQL rules

#### 2. **ThreatFox (abuse.ch)** - JSON Format
**Plugin**: `threatfox_plugin.py`
**Format**: Proprietary JSON (ThreatFox IOC format)
**Structure**:
```json
{
  "query_status": "ok",
  "data": [
    {
      "ioc_type": "sha256_hash",
      "ioc_value": "abc123...",
      "malware": "Emotet",
      "confidence_level": 100,
      "first_seen": "2025-10-01",
      "tags": ["exe", "Emotet"]
    }
  ]
}
```
**Deterministic Conversion**: ✅ Template-based malware IOC → YARA/Sigma rules

#### 3. **CrowdStrike Falcon Intelligence** - JSON Format
**Plugin**: `crowdstrike_plugin.py`
**Format**: Proprietary JSON (CrowdStrike Indicator API format)
**Structure**:
```json
{
  "resources": [
    {
      "type": "domain",
      "indicator": "evil.com",
      "malicious_confidence": "high",
      "labels": [{"name": "Malware/Family", "value": "BazarLoader"}],
      "threat_types": ["malware_family"],
      "kill_chains": ["reconnaissance", "weaponization"]
    }
  ]
}
```
**Deterministic Conversion**: ✅ Template-based with MITRE mapping → Multi-SIEM rules

#### 4. **OpenCTI (STIX 2.1)** - GraphQL + STIX Format ⚠️
**Plugin**: `opencti_plugin.py`
**Format**: STIX 2.1 JSON via GraphQL
**Structure**:
```json
{
  "data": {
    "indicators": {
      "edges": [
        {
          "node": {
            "id": "indicator--uuid",
            "pattern": "[ipv4-addr:value = '*******']",
            "pattern_type": "stix",
            "valid_from": "2025-10-01T00:00:00Z",
            "indicator_types": ["malicious-activity"],
            "objectLabel": [{"value": "apt"}]
          }
        }
      ]
    }
  }
}
```
**Deterministic Conversion**: ⚠️ **PARTIALLY IMPLEMENTED**
- Current: Extracts indicator value, generates rule template
- **MISSING**: Direct STIX pattern → Sigma/KQL translation

---

### Additional Structured CTI Formats (NOT YET IMPLEMENTED)

#### 5. **MISP (Malware Information Sharing Platform)** - JSON/XML/STIX
**Status**: ❌ No plugin yet
**Format**: MISP JSON + STIX 2.0/2.1 export
**Structure**:
```json
{
  "Event": {
    "id": "123",
    "info": "APT Campaign",
    "Attribute": [
      {
        "type": "ip-dst",
        "value": "*******",
        "category": "Network activity",
        "to_ids": true,
        "Galaxy": [{"name": "APT28"}]
      }
    ],
    "Tag": [{"name": "tlp:white"}]
  }
}
```
**Deterministic Conversion Needed**:
- MISP Attribute → Sigma detection (90% deterministic)
- Galaxy mapping → MITRE ATT&CK (100% deterministic)
- to_ids flag → Auto-deploy to SIEM (100% deterministic)

#### 6. **TAXII 2.1 (Trusted Automated eXchange of Indicator Information)**
**Status**: ❌ No plugin yet (OpenCTI supports TAXII but we don't use it directly)
**Format**: STIX 2.1 over TAXII transport
**Structure**:
```json
{
  "type": "bundle",
  "id": "bundle--uuid",
  "objects": [
    {
      "type": "indicator",
      "spec_version": "2.1",
      "pattern": "[file:hashes.MD5 = 'abc123']",
      "pattern_type": "stix",
      "valid_from": "2025-10-01T00:00:00Z"
    }
  ]
}
```
**Deterministic Conversion Needed**: STIX pattern parser (see below)

#### 7. **Sigma Rules** - YAML Format
**Status**: ✅ Partially supported via Community Engine
**Format**: Sigma YAML detection rules
**Structure**:
```yaml
title: Suspicious PowerShell Download
status: stable
logsource:
  product: windows
  service: powershell
detection:
  selection:
    EventID: 4104
    ScriptBlockText|contains:
      - 'Net.WebClient'
      - 'DownloadString'
  condition: selection
level: high
tags:
  - attack.execution
  - attack.t1059.001
```
**Deterministic Conversion**: ✅ Sigma → Splunk/Elastic/Sentinel (sigmac compiler)

#### 8. **YARA Rules** - YARA Format
**Status**: ⚠️ Generated but not consumed
**Format**: YARA malware detection rules
**Structure**:
```yara
rule Emotet_Variant {
    meta:
        description = "Detects Emotet malware"
        author = "CTI Team"
        date = "2025-10-03"
    strings:
        $s1 = "C:\\ProgramData\\emotet" wide ascii
        $s2 = {6A 40 68 00 30 00 00 6A 14 8D 45}
    condition:
        uint16(0) == 0x5A4D and filesize < 500KB and all of ($s*)
}
```
**Deterministic Conversion**: ✅ YARA → EDR/SIEM file monitoring rules

#### 9. **Snort/Suricata Rules** - IDS Rule Format
**Status**: ❌ Not implemented
**Format**: Snort/Suricata IDS rule syntax
**Structure**:
```
alert tcp any any -> $HOME_NET 80 (msg:"ET MALWARE Emotet C2 Checkin"; \
  flow:established,to_server; content:"POST"; http_method; \
  content:"/wp-admin/"; http_uri; pcre:"/\/[a-zA-Z0-9]{32}\//U"; \
  reference:md5,abc123def456; classtype:trojan-activity; sid:2028371; rev:1;)
```
**Deterministic Conversion Needed**: Snort → Sigma → Multi-SIEM (95% deterministic)

#### 10. **CVE/NVD (National Vulnerability Database)** - JSON Format
**Status**: ⚠️ Supported as indicator type, not as feed source
**Format**: NVD CVE JSON
**Structure**:
```json
{
  "cve": {
    "id": "CVE-2025-12345",
    "sourceIdentifier": "<EMAIL>",
    "published": "2025-10-01T10:00:00.000",
    "lastModified": "2025-10-02T12:00:00.000",
    "vulnStatus": "Analyzed",
    "descriptions": [{
      "lang": "en",
      "value": "Remote code execution in product X"
    }],
    "metrics": {
      "cvssMetricV31": [{
        "cvssData": {"baseScore": 9.8, "baseSeverity": "CRITICAL"}
      }]
    }
  }
}
```
**Deterministic Conversion Needed**: CVE → Vulnerability scan rules (100% deterministic)

---

## STIX 2.x Pattern Language → SIEM Rule Conversion (CRITICAL MISSING PIECE)

### STIX Pattern Examples and Conversion Requirements

#### Example 1: IP Address Pattern
**STIX Pattern**:
```
[ipv4-addr:value = '*******']
```
**Deterministic Conversion**:
- **Sigma**:
  ```yaml
  detection:
    selection:
      DestinationIp: '*******'
  ```
- **KQL (Elastic)**:
  ```
  destination.ip: "*******"
  ```
- **Splunk SPL**:
  ```
  dest_ip="*******"
  ```

#### Example 2: File Hash Pattern
**STIX Pattern**:
```
[file:hashes.MD5 = 'abc123def456' OR file:hashes.'SHA-256' = 'xyz789']
```
**Deterministic Conversion**:
- **Sigma**:
  ```yaml
  detection:
    selection:
      Hashes|contains:
        - 'MD5=abc123def456'
        - 'SHA256=xyz789'
  ```
- **KQL**:
  ```
  file.hash.md5: "abc123def456" or file.hash.sha256: "xyz789"
  ```

#### Example 3: Complex Pattern (Process + Network)
**STIX Pattern**:
```
[process:name = 'cmd.exe' AND process:command_line MATCHES '.*powershell.*']
FOLLOWED BY
[network-traffic:dst_ref.value = '*******' AND network-traffic:dst_port = 443]
```
**Deterministic Conversion**:
- **Sigma** (Correlation Rule):
  ```yaml
  detection:
    selection1:
      Image|endswith: '\\cmd.exe'
      CommandLine|contains: 'powershell'
    selection2:
      DestinationIp: '*******'
      DestinationPort: 443
    timeframe: 5m
    condition: selection1 | near selection2
  ```
- **KQL** (Elastic Sequence):
  ```
  sequence by host.name
    [process where process.name == "cmd.exe" and process.command_line contains "powershell"]
    [network where destination.ip == "*******" and destination.port == 443]
  ```

---

## Architecture: 90% Deterministic + 10% AI Enhancement

### Phase 1: Deterministic Converters (Rule-Based)

```python
class DeterministicCTIConverter:
    """
    90% of CTI-to-rule conversion using deterministic templates

    Handles:
    - OTX JSON → Sigma/KQL/SPL
    - ThreatFox JSON → YARA/Sigma
    - CrowdStrike JSON → Multi-SIEM with MITRE
    - STIX 2.x patterns → Sigma/KQL/SPL/AQL
    - MISP JSON → Sigma + Galaxy mapping
    - Sigma YAML → Splunk/Elastic/Sentinel/QRadar
    - Snort rules → Sigma → Multi-SIEM
    """

    def convert_otx_to_sigma(self, otx_indicator: Dict) -> str:
        """100% deterministic template"""

    def convert_stix_pattern_to_sigma(self, stix_pattern: str) -> str:
        """90% deterministic STIX parser"""

    def convert_misp_to_sigma(self, misp_event: Dict) -> str:
        """100% deterministic with Galaxy mapping"""

    def convert_sigma_to_kql(self, sigma_rule: str) -> str:
        """100% deterministic (sigmac)"""

    def convert_snort_to_sigma(self, snort_rule: str) -> str:
        """95% deterministic with AI fallback"""
```

### Phase 2: AI Enhancement Layer (Intelligence Engine)

```python
class AIRuleEnhancer:
    """
    10% of cases requiring AI intelligence

    Handles:
    - Obfuscated patterns (PowerShell encoding, etc.)
    - Context-aware tuning (environment-specific false positive reduction)
    - Natural language threat descriptions → Detection logic
    - Complex behavioral patterns (not expressible in STIX)
    - Novel attack techniques (zero-day)
    """

    async def enhance_rule(self, base_rule: Dict, context: Dict) -> Dict:
        """
        AI adds:
        - False positive filters based on environment
        - Additional detection variations (obfuscation bypass)
        - Recommended severity/priority
        - Investigation playbook suggestions
        """

    async def generate_from_description(self, description: str) -> Dict:
        """
        Edge case: No structured format available
        User provides: "Detect lateral movement using PsExec to domain controllers"
        AI generates: Sigma rule from natural language
        """
```

### Workflow Integration

```
CTI Source (OTX/STIX/MISP/etc.)
    ↓
Ingestion Engine (fetch structured data)
    ↓
Deterministic Converter (90% of cases)
    ├─ Template Match? → Generate Rule (0ms, $0 cost)
    └─ No Template? → Send to AI Enhancement
           ↓
    Intelligence Engine (10% of cases)
           ├─ AI analyzes pattern
           ├─ Generates detection logic
           ├─ Crystallizes pattern for future (learn once, reuse forever)
           └─ Returns enhanced rule
    ↓
Backend Engine (store rule)
    ↓
Deployment Service (push to SIEM)
```

---

## Implementation Priority

### P0: STIX Pattern Parser (CRITICAL)
**Why**: OpenCTI uses STIX 2.1, this unlocks enterprise CTI platforms
**Effort**: 2-3 days
**Files to Create**:
- `engines/ingestion/stix_pattern_parser.py` - STIX → Abstract Syntax Tree
- `engines/backend/stix_to_sigma_converter.py` - AST → Sigma YAML
- `engines/backend/stix_to_kql_converter.py` - AST → KQL query

### P1: MISP Plugin
**Why**: Widely used in SOCs, supports STIX export
**Effort**: 1-2 days
**Files to Create**:
- `engines/ingestion/misp_plugin.py` - Following CTISourcePlugin pattern

### P2: AI Enhancement Integration
**Why**: Handles 10% edge cases + learns new patterns
**Effort**: 2-3 days
**Files to Create**:
- `engines/intelligence/ai_rule_enhancer.py`
- Integration with pattern crystallization

### P3: Sigma Compiler Integration
**Why**: Community has thousands of Sigma rules
**Effort**: 1 day
**Note**: Use existing `sigmac` or `pySigma` library

---

## Architectural Fix: Rule Deployment Location

### Current Issue
**Backend Engine** pushes rules to external SIEMs (Elastic, Splunk, etc.)
- ❌ Backend should NOT have internet/LAN access
- ❌ Backend is for storage, not external communication

### Correct Architecture
**Ingestion Engine** should handle rule deployment
- ✅ Already has internet/LAN access (needs it for CTI feeds)
- ✅ Separation of concerns: Ingestion = External I/O
- ✅ Backend = Internal storage only

### Migration Required

**Move from Backend → Ingestion**:
1. `rule_deployment_service.py` → Move to `engines/ingestion/`
2. HTTP endpoints `/api/rules/{id}/deploy/*` → Move to Ingestion Engine (port 8003)
3. Backend keeps: Rule storage, CTI-to-rule generation (internal only)
4. Ingestion gets: Rule deployment to external SIEMs

**New Flow**:
```
CTI Source → Ingestion Engine (fetch + deploy)
                ↓
           Deterministic Converter
                ↓
           Backend Engine (store rule)
                ↓
           Publish: backend.rule.approved
                ↓
           Ingestion Engine (listens for approved rules)
                ↓
           Deploy to External SIEM (Elastic/Splunk/etc.)
```

---

## Summary: Complete CTI Format Coverage

| Format | Plugin Status | Deterministic Conversion | AI Enhancement |
|--------|--------------|-------------------------|----------------|
| **OTX JSON** | ✅ Implemented | ✅ Template-based (100%) | ❌ Not needed |
| **ThreatFox JSON** | ✅ Implemented | ✅ Template-based (100%) | ❌ Not needed |
| **CrowdStrike JSON** | ✅ Implemented | ✅ Template + MITRE (100%) | ❌ Not needed |
| **STIX 2.1** | ⚠️ Partial (OpenCTI) | ⚠️ **MISSING PARSER** | ✅ Fallback ready |
| **MISP JSON** | ❌ Not implemented | ⚠️ Need plugin + converter | ✅ Fallback ready |
| **TAXII 2.1** | ❌ Not implemented | ⚠️ STIX parser required | ✅ Fallback ready |
| **Sigma YAML** | ✅ Community Engine | ✅ sigmac (100%) | ⚠️ Tuning only |
| **YARA Rules** | ⚠️ Generate only | ✅ Template-based (100%) | ❌ Not needed |
| **Snort/Suricata** | ❌ Not implemented | ⚠️ Parser needed (95%) | ✅ 5% edge cases |
| **CVE/NVD JSON** | ⚠️ Indicator only | ⚠️ Need feed plugin | ✅ Context aware |

**Total Coverage**:
- ✅ Operational: 4 sources (OTX, ThreatFox, CrowdStrike, Sigma)
- ⚠️ Partial: 2 sources (STIX, CVE)
- ❌ Missing: 4 sources (MISP, TAXII, Snort, NVD feed)

**Deterministic Conversion Status**:
- ✅ Working: Simple IOCs (IP, domain, hash, URL)
- ⚠️ **CRITICAL GAP**: STIX pattern language parser
- ⚠️ **IMPORTANT**: MISP attribute → detection logic
- ⚠️ **NICE-TO-HAVE**: Snort → Sigma converter

**AI Enhancement**:
- ✅ Architecture designed (Intelligence Engine)
- ⚠️ Integration incomplete (pattern crystallization exists, rule enhancement missing)
- 🎯 Target: 10% of conversions use AI, 90% pure deterministic
