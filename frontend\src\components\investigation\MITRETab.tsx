import React, { useState } from 'react';
import { Alert, CorrelationData } from '../../types/investigation';
import '../../styles/MITRETab.css';

interface MITRETabProps {
  alert: Alert;
  correlation: CorrelationData | null;
}

interface TechniqueDetail {
  id: string;
  name: string;
  tactic: string;
  description?: string;
  detection?: string;
  mitigation?: string;
  examples?: string[];
}

export const MITRETab: React.FC<MITRETabProps> = ({ alert, correlation }) => {
  const [selectedTechnique, setSelectedTechnique] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'chain' | 'matrix'>('chain');

  const mitreChain = correlation?.correlation?.mitre_chain || [];

  if (mitreChain.length === 0) {
    return (
      <div className="no-mitre">
        <div className="no-mitre-icon">🎯</div>
        <h3>No MITRE ATT&CK Mapping Available</h3>
        <p>No MITRE ATT&CK techniques have been identified for this alert.</p>
        <p className="hint">Correlation analysis may reveal technique chains when related events are found.</p>
      </div>
    );
  }

  // Get unique tactics in order
  const tacticOrder = ['initial-access', 'execution', 'persistence', 'privilege-escalation',
                       'defense-evasion', 'credential-access', 'discovery', 'lateral-movement',
                       'collection', 'command-and-control', 'exfiltration', 'impact'];

  const uniqueTactics = Array.from(new Set(mitreChain.map(t => t.tactic)))
    .sort((a, b) => {
      const aIndex = tacticOrder.indexOf(a.toLowerCase());
      const bIndex = tacticOrder.indexOf(b.toLowerCase());
      return (aIndex === -1 ? 999 : aIndex) - (bIndex === -1 ? 999 : bIndex);
    });

  const techniquesByTactic = mitreChain.reduce((acc, technique) => {
    if (!acc[technique.tactic]) {
      acc[technique.tactic] = [];
    }
    acc[technique.tactic].push(technique);
    return acc;
  }, {} as Record<string, typeof mitreChain>);

  const getTacticIcon = (tactic: string) => {
    const icons: Record<string, string> = {
      'initial-access': '🚪',
      'execution': '⚙️',
      'persistence': '🔒',
      'privilege-escalation': '⬆️',
      'defense-evasion': '🛡️',
      'credential-access': '🔑',
      'discovery': '🔍',
      'lateral-movement': '↔️',
      'collection': '📦',
      'command-and-control': '📡',
      'exfiltration': '📤',
      'impact': '💥'
    };
    return icons[tactic.toLowerCase()] || '🎯';
  };

  const getTacticColor = (tactic: string) => {
    const colors: Record<string, string> = {
      'initial-access': '#ef4444',
      'execution': '#f59e0b',
      'persistence': '#eab308',
      'privilege-escalation': '#84cc16',
      'defense-evasion': '#22c55e',
      'credential-access': '#10b981',
      'discovery': '#14b8a6',
      'lateral-movement': '#06b6d4',
      'collection': '#0ea5e9',
      'command-and-control': '#3b82f6',
      'exfiltration': '#6366f1',
      'impact': '#8b5cf6'
    };
    return colors[tactic.toLowerCase()] || '#6b7280';
  };

  const formatTacticName = (tactic: string) => {
    return tactic.split('-').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const getKillChainPhase = (tactic: string) => {
    const phases: Record<string, string> = {
      'initial-access': 'Reconnaissance',
      'execution': 'Weaponization',
      'persistence': 'Delivery',
      'privilege-escalation': 'Exploitation',
      'defense-evasion': 'Installation',
      'credential-access': 'Installation',
      'discovery': 'Command & Control',
      'lateral-movement': 'Command & Control',
      'collection': 'Actions on Objectives',
      'command-and-control': 'Command & Control',
      'exfiltration': 'Actions on Objectives',
      'impact': 'Actions on Objectives'
    };
    return phases[tactic.toLowerCase()] || 'Unknown';
  };

  const exportMITREData = () => {
    const data = {
      alert_id: alert.alert_id,
      alert_title: alert.title,
      mitre_techniques: mitreChain,
      tactics: uniqueTactics,
      kill_chain_coverage: uniqueTactics.map(t => getKillChainPhase(t))
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `mitre_attack_${alert.alert_id}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="mitre-tab">
      {/* Header */}
      <div className="mitre-header">
        <div className="mitre-summary">
          <h3>🎯 MITRE ATT&CK Analysis</h3>
          <div className="mitre-stats">
            <div className="stat">
              <span className="stat-value">{mitreChain.length}</span>
              <span className="stat-label">Techniques</span>
            </div>
            <div className="stat">
              <span className="stat-value">{uniqueTactics.length}</span>
              <span className="stat-label">Tactics</span>
            </div>
            <div className="stat">
              <span className="stat-value">{new Set(uniqueTactics.map(t => getKillChainPhase(t))).size}</span>
              <span className="stat-label">Kill Chain Phases</span>
            </div>
          </div>
        </div>

        <div className="mitre-actions">
          <div className="view-toggle">
            <button
              className={`toggle-btn ${viewMode === 'chain' ? 'active' : ''}`}
              onClick={() => setViewMode('chain')}
            >
              📊 Chain View
            </button>
            <button
              className={`toggle-btn ${viewMode === 'matrix' ? 'active' : ''}`}
              onClick={() => setViewMode('matrix')}
            >
              🔲 Matrix View
            </button>
          </div>
          <button className="export-btn" onClick={exportMITREData}>
            📥 Export
          </button>
        </div>
      </div>

      {/* Attack Chain View */}
      {viewMode === 'chain' && (
        <div className="attack-chain">
          <h4>Attack Progression</h4>
          <div className="chain-timeline">
            {mitreChain.map((technique, index) => (
              <React.Fragment key={`${technique.technique_id}-${index}`}>
                <div
                  className={`chain-step ${selectedTechnique === technique.technique_id ? 'selected' : ''}`}
                  onClick={() => setSelectedTechnique(technique.technique_id)}
                  style={{ borderColor: getTacticColor(technique.tactic) }}
                >
                  <div className="step-number">{index + 1}</div>
                  <div className="step-icon">{getTacticIcon(technique.tactic)}</div>
                  <div className="step-content">
                    <div className="step-id">{technique.technique_id}</div>
                    <div className="step-name">{technique.technique_name}</div>
                    <div className="step-tactic" style={{ color: getTacticColor(technique.tactic) }}>
                      {formatTacticName(technique.tactic)}
                    </div>
                  </div>
                </div>
                {index < mitreChain.length - 1 && (
                  <div className="chain-connector">
                    <div className="connector-line"></div>
                    <div className="connector-arrow">▶</div>
                  </div>
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      )}

      {/* Matrix View */}
      {viewMode === 'matrix' && (
        <div className="attack-matrix">
          <h4>Tactic-Technique Matrix</h4>
          <div className="matrix-grid">
            {uniqueTactics.map(tactic => (
              <div key={tactic} className="tactic-column">
                <div
                  className="tactic-header"
                  style={{ background: getTacticColor(tactic) }}
                >
                  <div className="tactic-icon">{getTacticIcon(tactic)}</div>
                  <div className="tactic-name">{formatTacticName(tactic)}</div>
                  <div className="tactic-count">{techniquesByTactic[tactic].length}</div>
                </div>
                <div className="technique-list">
                  {techniquesByTactic[tactic].map(technique => (
                    <div
                      key={technique.technique_id}
                      className={`technique-card ${selectedTechnique === technique.technique_id ? 'selected' : ''}`}
                      onClick={() => setSelectedTechnique(technique.technique_id)}
                      style={{ borderLeftColor: getTacticColor(tactic) }}
                    >
                      <div className="technique-id">{technique.technique_id}</div>
                      <div className="technique-name">{technique.technique_name}</div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Kill Chain Mapping */}
      <div className="kill-chain-section">
        <h4>🔗 Cyber Kill Chain Mapping</h4>
        <div className="kill-chain">
          {['Reconnaissance', 'Weaponization', 'Delivery', 'Exploitation', 'Installation', 'Command & Control', 'Actions on Objectives'].map(phase => {
            const phaseTactics = uniqueTactics.filter(t => getKillChainPhase(t) === phase);
            const isActive = phaseTactics.length > 0;

            return (
              <div key={phase} className={`kill-chain-phase ${isActive ? 'active' : ''}`}>
                <div className="phase-name">{phase}</div>
                {isActive && (
                  <div className="phase-tactics">
                    {phaseTactics.map(tactic => (
                      <div
                        key={tactic}
                        className="phase-tactic"
                        style={{ background: getTacticColor(tactic) }}
                      >
                        {getTacticIcon(tactic)} {formatTacticName(tactic)}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Technique Details */}
      {selectedTechnique && (
        <div className="technique-details-section">
          <h4>📖 Technique Details: {selectedTechnique}</h4>
          {mitreChain
            .filter(t => t.technique_id === selectedTechnique)
            .map((technique, index) => (
              <div key={`${technique.technique_id}-detail-${index}`} className="technique-details">
                <div className="detail-header">
                  <div className="detail-title">
                    <span className="detail-icon">{getTacticIcon(technique.tactic)}</span>
                    <span className="detail-name">{technique.technique_name}</span>
                  </div>
                  <div
                    className="detail-tactic"
                    style={{ background: getTacticColor(technique.tactic) }}
                  >
                    {formatTacticName(technique.tactic)}
                  </div>
                </div>

                <div className="detail-body">
                  <div className="detail-section">
                    <h5>Description</h5>
                    <p>
                      This technique represents a specific method adversaries use to accomplish their objectives.
                      It is part of the {formatTacticName(technique.tactic)} tactic in the MITRE ATT&CK framework.
                    </p>
                  </div>

                  <div className="detail-section">
                    <h5>Detection Guidance</h5>
                    <ul>
                      <li>Monitor for unusual process executions or command-line arguments</li>
                      <li>Look for anomalous network connections or data transfers</li>
                      <li>Review authentication logs for suspicious access patterns</li>
                      <li>Correlate with other security events across your environment</li>
                    </ul>
                  </div>

                  <div className="detail-section">
                    <h5>Recommended Actions</h5>
                    <ul>
                      <li>Investigate the source and target of the activity</li>
                      <li>Review related security events in the correlation timeline</li>
                      <li>Check for indicators of compromise (IoCs) in threat intelligence</li>
                      <li>Consider containment actions if malicious activity is confirmed</li>
                    </ul>
                  </div>

                  <div className="detail-actions">
                    <button className="detail-action-btn">
                      🔗 View in MITRE ATT&CK
                    </button>
                    <button className="detail-action-btn">
                      📚 See Detection Rules
                    </button>
                    <button className="detail-action-btn">
                      🛡️ View Mitigations
                    </button>
                  </div>
                </div>
              </div>
            ))}
        </div>
      )}

      {/* Coverage Analysis */}
      <div className="coverage-section">
        <h4>📊 ATT&CK Coverage Analysis</h4>
        <div className="coverage-grid">
          <div className="coverage-card">
            <div className="coverage-label">Attack Progression</div>
            <div className="coverage-value">
              {uniqueTactics.length >= 3 ? 'Multi-Stage' : uniqueTactics.length >= 2 ? 'Two-Stage' : 'Single-Stage'}
            </div>
            <div className="coverage-description">
              {uniqueTactics.length >= 3
                ? 'Complex attack with multiple phases detected'
                : uniqueTactics.length >= 2
                ? 'Attack involves multiple tactics'
                : 'Attack uses a single tactic'}
            </div>
          </div>

          <div className="coverage-card">
            <div className="coverage-label">Kill Chain Phases</div>
            <div className="coverage-value">
              {new Set(uniqueTactics.map(t => getKillChainPhase(t))).size} / 7
            </div>
            <div className="coverage-description">
              Cyber Kill Chain phases covered by this attack
            </div>
          </div>

          <div className="coverage-card">
            <div className="coverage-label">Threat Sophistication</div>
            <div className="coverage-value">
              {mitreChain.length >= 5 ? 'High' : mitreChain.length >= 3 ? 'Medium' : 'Low'}
            </div>
            <div className="coverage-description">
              Based on number and variety of techniques used
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
