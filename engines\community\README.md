# Community Engine - GitHub Detection Rule Integration

## Overview

The Community Engine automatically syncs detection rules from open-source GitHub repositories into SIEMLess. **100% Configurable** - no hardcoded repositories!

## Features

- ✅ **Configurable Repository Sources** - Add any GitHub repo via YAML
- ✅ **Auto-Sync Scheduler** - Hourly, daily, or weekly syncing
- ✅ **Multi-Format Support** - Sigma, Splunk, Elastic, Sentinel, KQL, Wazuh
- ✅ **Quality Filtering** - Priority scoring and quality thresholds
- ✅ **Deduplication** - SHA-256 hash-based duplicate detection
- ✅ **15+ Pre-configured Sources** - Ready to use community repositories

---

## Quick Start

### 1. Configure Repositories

Edit `repository_config.yaml`:

```yaml
repositories:
  - repo_url: https://github.com/SigmaHQ/sigma
    name: Sigma HQ Rules
    enabled: true
    rule_paths:
      - rules/
    rule_formats:
      - sigma
    sync_frequency: daily
    priority: 90
    tags:
      - sigma
      - community
```

### 2. (Optional) Add GitHub Token

For higher API rate limits:

```bash
export GITHUB_TOKEN=your_github_personal_access_token
```

### 3. Start Community Engine

```bash
cd engines/community
python community_engine.py
```

### 4. Trigger Manual Sync

```bash
curl -X POST http://localhost:8006/api/community/repositories/sigma/sync
```

---

## Pre-Configured Repositories (15+)

### Enabled by Default (7 sources)

1. **SigmaHQ/sigma** (Priority: 90)
   - 3,000+ universal SIEM rules
   - Most popular detection rule format
   - Community-driven, peer-reviewed

2. **elastic/detection-rules** (Priority: 85)
   - 1,500+ Elastic Security rules
   - EDR and SIEM coverage
   - Maintained by Elastic

3. **splunk/security_content** (Priority: 85)
   - 2,000+ Splunk ESCU rules
   - Enterprise security content
   - MITRE ATT&CK mapped

4. **Azure/Azure-Sentinel** (Priority: 80)
   - 800+ Microsoft Sentinel rules
   - Cloud-native detections
   - KQL-based queries

5. **mdecrevoisier/SIGMA-detection-rules** (Priority: 85)
   - 350+ Sigma rules
   - Fully MITRE ATT&CK mapped
   - Community-verified

6. **Loginsoft-Research/detection-rules** (Priority: 75)
   - Emerging threat detections
   - Open-source component coverage
   - Sigma format

7. **logpai/loghub** (Priority: 60)
   - Log datasets for research
   - Parsing examples
   - Training data

8. **parsavares/firewall-ids-log-analysis** (Priority: 65)
   - Network security analysis
   - Firewall and IDS logs
   - Analysis tools

### Disabled by Default (Enable as Needed)

9. **panther-labs/panther-analysis** (Priority: 75)
   - Cloud-native SIEM rules
   - Python-based detections

10. **chronicle/detection-rules** (Priority: 75)
    - Google Chronicle rules
    - YARA-L format

11. **falcosecurity/falco** (Priority: 70)
    - Kubernetes runtime security
    - Container security

12. **socprime/SigmaUI** (Priority: 80)
    - Curated Sigma rules
    - SOC Prime community

13. **wazuh/wazuh** (Priority: 80)
    - Open-source XDR/SIEM
    - XML-based rules
    - Large repository

14. **socfortress/Wazuh-Rules** (Priority: 75)
    - Advanced Wazuh rules
    - SOCFortress community

---

## Configuration Guide

### Repository Configuration

```yaml
repositories:
  - repo_url: https://github.com/OWNER/REPO
    name: Display Name
    enabled: true  # or false to disable
    rule_paths:
      - path/to/rules/
      - another/path/
    rule_formats:
      - sigma
      - splunk
      - elastic
    sync_frequency: daily  # hourly, daily, weekly
    priority: 85  # 0-100, higher = more trusted
    tags:
      - custom-tag
      - another-tag
```

### Global Settings

```yaml
settings:
  auto_sync: true  # Auto-sync on schedule
  deduplicate: true  # Prevent duplicate imports
  quality_threshold: 70  # Minimum priority score
  max_rules_per_repo: 10000  # Limit per repository
  import_status: pending  # pending or active
  notify_on_import: true  # Notifications
```

---

## API Endpoints

### List Repositories
```bash
GET /api/community/repositories
```

**Response**:
```json
{
  "repositories": [
    {
      "repo_url": "https://github.com/SigmaHQ/sigma",
      "name": "Sigma HQ Rules",
      "enabled": true,
      "rule_count": 3042,
      "last_sync": "2025-10-03T10:30:00Z",
      "priority": 90,
      "tags": ["sigma", "community"]
    }
  ]
}
```

### Add Repository
```bash
POST /api/community/repositories
Content-Type: application/json

{
  "repo_url": "https://github.com/YOUR_ORG/YOUR_REPO",
  "name": "Your Rules",
  "enabled": true,
  "rule_paths": ["rules/"],
  "rule_formats": ["sigma"],
  "sync_frequency": "daily",
  "priority": 75,
  "tags": ["custom"]
}
```

### Trigger Sync
```bash
POST /api/community/repositories/{repo_id}/sync
```

**Response**:
```json
{
  "success": true,
  "rules_found": 3042,
  "rules_imported": 2891,
  "rules_duplicated": 151,
  "errors": []
}
```

### Remove Repository
```bash
DELETE /api/community/repositories/{repo_url}
```

---

## Supported Rule Formats

### Sigma (Universal)
- Extensions: `.yml`, `.yaml`
- Most common format
- Converts to all SIEMs

### Splunk
- Extensions: `.spl`, `.yml`, `.yaml`
- SPL queries
- ESCU content

### Elastic
- Extensions: `.json`, `.toml`, `.yml`
- KQL/DSL queries
- ECS-based

### Sentinel
- Extensions: `.kql`, `.json`, `.yaml`
- KQL queries
- Azure cloud

### Wazuh
- Extensions: `.xml`
- XML-based rules
- Open-source SIEM

### Others
- YARA-L (Chronicle)
- Python (Panther)
- Logs/Datasets (LogHub)

---

## How It Works

### 1. Repository Scanning
```
GitHub API → Fetch Repository Contents → Recursively Scan Paths
```

### 2. Rule Detection
```
Check File Extension → Parse Content → Detect Format
```

### 3. Deduplication
```
Calculate SHA-256 Hash → Check Database → Import if New
```

### 4. Import Process
```
Extract Metadata → Apply Tags → Quality Score → Pending Queue
```

### 5. Sync Schedule
```
Check Last Sync → Compare Frequency → Trigger if Due → Update Timestamp
```

---

## Best Practices

### 1. Start Small
Enable 2-3 repositories initially, then expand:
```yaml
# Good for starting
- SigmaHQ/sigma (enabled: true)
- elastic/detection-rules (enabled: true)
- mdecrevoisier/SIGMA-detection-rules (enabled: true)
```

### 2. Use GitHub Token
Increase rate limits from 60/hour to 5,000/hour:
```bash
export GITHUB_TOKEN=ghp_yourtoken
```

### 3. Set Quality Threshold
Filter low-quality rules:
```yaml
settings:
  quality_threshold: 75  # Only import priority >= 75
```

### 4. Monitor Imports
Check pending rules regularly:
```bash
curl http://localhost:8002/api/rules/pending | jq '.items | length'
```

### 5. Tag Appropriately
Use tags for organization:
```yaml
tags:
  - vendor-name
  - use-case
  - environment  # prod, dev, test
```

---

## Troubleshooting

### Issue: No Rules Imported

**Check**:
1. Is repository enabled? (`enabled: true`)
2. Are paths correct? (`rule_paths: ['rules/']`)
3. Is format supported? (`rule_formats: ['sigma']`)
4. GitHub API rate limit? (add `GITHUB_TOKEN`)

**Solution**:
```bash
# Check sync status
curl http://localhost:8006/api/community/repositories/{repo_id}/sync

# View errors
docker-compose logs community_engine | grep ERROR
```

### Issue: Duplicate Rules

**Cause**: Same rule imported from multiple sources

**Solution**: Deduplication is automatic (SHA-256 hash)
```yaml
settings:
  deduplicate: true  # Enabled by default
```

### Issue: Too Many Rules

**Solution**: Set limits
```yaml
settings:
  max_rules_per_repo: 5000  # Reduce from 10000
  quality_threshold: 80  # Increase from 70
```

### Issue: Slow Sync

**Solution**:
1. Add GitHub token for faster API
2. Reduce sync frequency
3. Limit paths scanned

---

## Integration with Rule Management

### Workflow
```
Community Engine → Pending Rules → Review → Approve → Deploy to SIEM
```

### 1. Community Rules Imported
- Automatic sync to `pending_rules` table
- Tagged with source repository
- Quality score from repository priority

### 2. Analyst Reviews
- Use Pending Rules Widget
- See original GitHub source
- Preview rule in target format

### 3. Approve & Deploy
- Click "Approve"
- Optionally edit rule
- Auto-deploy to Elastic Security

### 4. Performance Tracking
- Monitor TP/FP rates
- Tune underperforming rules
- Share feedback with community

---

## Statistics

### Total Available Rules
```
SigmaHQ:           3,000+ rules
Elastic:           1,500+ rules
Splunk:            2,000+ rules
Sentinel:            800+ rules
mdecrevoisier:       350+ rules
Loginsoft:           200+ rules
---------------------------------
TOTAL:            7,850+ rules
```

### Coverage
- **MITRE ATT&CK**: 90%+ techniques covered
- **Platforms**: Windows, Linux, macOS, Cloud
- **Use Cases**: APT, Ransomware, Insider Threats, Cloud Attacks

---

## Advanced Configuration

### Custom Private Repository

```yaml
- repo_url: https://github.com/yourorg/private-rules
  name: Internal Rules
  enabled: true
  rule_paths:
    - production/
    - staging/
  rule_formats:
    - sigma
  sync_frequency: hourly
  priority: 95  # Higher than public sources
  tags:
    - internal
    - production
```

**Note**: Set `GITHUB_TOKEN` with access to private repos

### Multi-Path Scanning

```yaml
rule_paths:
  - rules/windows/
  - rules/linux/
  - rules/cloud/
  - detections/network/
```

### Format-Specific Tags

```yaml
tags:
  - sigma
  - windows
  - lateral-movement
  - ta0008  # MITRE tactic ID
```

---

## Development

### Add New Format Support

Edit `community_engine.py`:

```python
def _is_rule_file(self, filename: str, formats: List[str]) -> bool:
    extensions = {
        'sigma': ['.yml', '.yaml'],
        'your_format': ['.ext1', '.ext2']  # Add here
    }
```

### Custom Rule Parser

```python
def _fetch_rule_file(self, repo, file_path):
    # Add custom parsing logic
    if file_path.endswith('.custom'):
        parsed = your_custom_parser(content)
```

---

## License

Community Engine code: MIT License

**Note**: Each repository has its own license. Check before use:
- SigmaHQ: Detection Rule License (DRL) 1.1
- Elastic: Elastic License 2.0
- Splunk: Apache 2.0
- Sentinel: MIT

---

## Support

**Issues**: https://github.com/siemless/siemless-v2/issues
**Docs**: See [COMPLETE_IMPLEMENTATION_SUMMARY.md](../../COMPLETE_IMPLEMENTATION_SUMMARY.md)
**Community**: Join our Discord for support

---

## Roadmap

- [ ] UI for repository management
- [ ] Rule voting/rating system
- [ ] Community feedback integration
- [ ] Auto-translation between formats
- [ ] Rule effectiveness tracking per source
- [ ] Marketplace for sharing custom rules

---

**Built with ❤️ by the SIEMLess community**
