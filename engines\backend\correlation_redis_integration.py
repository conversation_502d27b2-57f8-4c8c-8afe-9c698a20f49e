"""
Redis Integration for Correlation Engine and Detection Fidelity
Connects correlation capabilities with the 5-engine architecture
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
import redis.asyncio as redis_async
from dataclasses import asdict

from correlation_engine import CorrelationEngine, CorrelationRule, DetectionAssessment
from backend_engine import BackendEngine

logger = logging.getLogger(__name__)


class CorrelationRedisIntegration:
    """
    Integrates correlation engine with Redis pub/sub for inter-engine communication
    """

    def __init__(self, redis_host: str = 'redis', redis_port: int = 6379):
        self.redis_client = None
        self.pubsub = None
        self.correlation_engine = CorrelationEngine()
        self.subscriptions = []

        # Redis channel definitions for correlation
        self.channels = {
            # Incoming event channels from different engines
            'ingestion.events.parsed': self._handle_ingestion_event,
            'contextualization.events.enriched': self._handle_enriched_event,
            'backend.cti.indicator': self._handle_cti_indicator,

            # Correlation-specific channels
            'correlation.check_event': self._handle_correlation_check,
            'correlation.assess_fidelity': self._handle_fidelity_assessment,
            'correlation.get_capabilities': self._handle_capability_query,

            # Outgoing correlation alerts
            'correlation.alert': None,  # Published to, not subscribed
            'correlation.context_update': None,  # Published to, not subscribed
        }

    async def connect(self):
        """Connect to Redis and set up subscriptions"""
        self.redis_client = await redis_async.from_url(
            f'redis://{redis_host}:{redis_port}',
            decode_responses=True
        )
        self.pubsub = self.redis_client.pubsub()

        # Subscribe to relevant channels
        for channel in self.channels.keys():
            if self.channels[channel] is not None:  # Only subscribe if we have a handler
                await self.pubsub.subscribe(channel)
                logger.info(f"Subscribed to channel: {channel}")

    async def start_listening(self):
        """Main message processing loop"""
        logger.info("Correlation Redis Integration started listening...")

        async for message in self.pubsub.listen():
            if message['type'] == 'message':
                channel = message['channel']
                data = message['data']

                # Route to appropriate handler
                handler = self.channels.get(channel)
                if handler:
                    try:
                        await handler(json.loads(data))
                    except Exception as e:
                        logger.error(f"Error processing message from {channel}: {e}")

    # ========================================
    # INCOMING MESSAGE HANDLERS
    # ========================================

    async def _handle_ingestion_event(self, event: Dict):
        """
        Handle parsed events from Ingestion Engine
        These are raw events that need correlation checking
        """
        logger.debug(f"Received ingestion event: {event.get('event_id')}")

        # Add source quality metadata
        event['source_quality'] = await self._get_source_quality(event.get('source_type'))

        # Check for correlations
        correlation_results = await self.correlation_engine.process_event(event)

        if correlation_results:
            # Correlation detected! Publish alert
            for result in correlation_results:
                await self._publish_correlation_alert(result)

    async def _handle_enriched_event(self, event: Dict):
        """
        Handle enriched events from Contextualization Engine
        These have additional context that improves correlation
        """
        logger.debug(f"Received enriched event: {event.get('event_id')}")

        # Enriched events have better context for correlation
        event['enriched'] = True
        event['confidence_boost'] = 0.1  # Enrichment increases confidence

        # Check for correlations with enriched data
        correlation_results = await self.correlation_engine.process_event(event)

        if correlation_results:
            for result in correlation_results:
                # Enriched correlations have higher confidence
                result['confidence'] = min(1.0, result.get('confidence', 0.5) + 0.1)
                await self._publish_correlation_alert(result)

    async def _handle_cti_indicator(self, indicator: Dict):
        """
        Handle CTI indicators from Backend Engine
        Update correlation rules based on new threat intelligence
        """
        logger.debug(f"Received CTI indicator: {indicator.get('indicator')}")

        # Create or update correlation rules based on CTI
        if indicator.get('threat_type') == 'apt':
            # APT indicators might need multi-source correlation
            await self._create_apt_correlation_rule(indicator)
        elif indicator.get('threat_type') == 'ransomware':
            # Ransomware needs behavioral correlation
            await self._create_ransomware_correlation_rule(indicator)

    async def _handle_correlation_check(self, request: Dict):
        """
        Handle explicit correlation check requests
        Used for retroactive correlation on historical data
        """
        events = request.get('events', [])
        time_window = request.get('time_window', 300)

        # Batch correlation check
        correlations = []
        for event in events:
            results = await self.correlation_engine.process_event(event)
            if results:
                correlations.extend(results)

        # Publish results
        await self.redis_client.publish(
            'correlation.check_result',
            json.dumps({
                'request_id': request.get('request_id'),
                'correlations': correlations,
                'events_checked': len(events)
            })
        )

    async def _handle_fidelity_assessment(self, request: Dict):
        """
        Handle detection fidelity assessment requests
        Returns capability assessment for specific attack scenarios
        """
        attack_type = request.get('attack_type')
        available_sources = request.get('available_sources', [])

        # Assess detection capability
        assessment = await self._assess_detection_fidelity(attack_type, available_sources)

        # Publish assessment results
        await self.redis_client.publish(
            'correlation.fidelity_result',
            json.dumps({
                'request_id': request.get('request_id'),
                'attack_type': attack_type,
                'confidence_score': assessment['confidence'],
                'fidelity_level': assessment['fidelity'],
                'gaps': assessment['gaps'],
                'recommendations': assessment['recommendations']
            })
        )

    async def _handle_capability_query(self, request: Dict):
        """
        Handle capability queries from other engines
        Returns what attacks can be detected with current sources
        """
        # Get current capabilities
        capabilities = await self._get_current_capabilities()

        # Publish capability report
        await self.redis_client.publish(
            'correlation.capabilities',
            json.dumps({
                'request_id': request.get('request_id'),
                'total_rules': capabilities['total_rules'],
                'active_correlations': capabilities['active_correlations'],
                'detectable_attacks': capabilities['detectable_attacks'],
                'coverage_by_mitre': capabilities['mitre_coverage'],
                'required_sources': capabilities['required_sources']
            })
        )

    # ========================================
    # OUTGOING MESSAGE PUBLISHERS
    # ========================================

    async def _publish_correlation_alert(self, alert: Dict):
        """
        Publish correlation alert for consumption by other engines
        """
        # Add metadata for downstream processing
        alert['correlation_metadata'] = {
            'engine': 'correlation',
            'timestamp': datetime.now().isoformat(),
            'correlation_id': alert.get('alert_id'),
            'fidelity_score': await self._calculate_fidelity_score(alert)
        }

        # Publish to multiple channels based on severity
        channels_to_publish = ['correlation.alert']

        if alert.get('severity') == 'critical':
            channels_to_publish.append('delivery.high_priority_alert')

        if 'ransomware' in alert.get('use_case', ''):
            channels_to_publish.append('delivery.ransomware_alert')

        for channel in channels_to_publish:
            await self.redis_client.publish(channel, json.dumps(alert))
            logger.warning(f"Published correlation alert to {channel}: {alert.get('rule_name')}")

    async def _publish_context_update(self, context: Dict):
        """
        Publish correlation context updates for stateful tracking
        """
        await self.redis_client.publish(
            'correlation.context_update',
            json.dumps({
                'context_id': context.get('context_id'),
                'entity': context.get('entity_id'),
                'score': context.get('correlation_score'),
                'events_matched': context.get('event_count'),
                'status': context.get('status')
            })
        )

    # ========================================
    # INTEGRATION WITH OTHER ENGINES
    # ========================================

    async def integrate_with_intelligence_engine(self):
        """
        Integration with Intelligence Engine for AI-enhanced correlation
        """
        # Request AI consensus on correlation confidence
        await self.redis_client.publish(
            'intelligence.correlation.validate',
            json.dumps({
                'correlation_rules': self._get_active_rules(),
                'request_type': 'validate_correlation_logic'
            })
        )

    async def integrate_with_backend_engine(self):
        """
        Integration with Backend Engine for rule storage and CTI
        """
        # Store correlation rules in backend
        rules = self.correlation_engine.rules
        for rule_id, rule in rules.items():
            await self.redis_client.publish(
                'backend.rules.store',
                json.dumps({
                    'rule_id': rule_id,
                    'rule_type': 'correlation',
                    'rule_data': asdict(rule) if hasattr(rule, '__dict__') else rule
                })
            )

    async def integrate_with_delivery_engine(self):
        """
        Integration with Delivery Engine for alert presentation
        """
        # Register correlation alert templates
        templates = {
            'lateral_movement': {
                'template': 'Lateral movement detected from {entity} across {sources_count} sources',
                'severity_modifier': 1.2,
                'auto_case': True
            },
            'data_exfiltration': {
                'template': 'Potential data exfiltration by {entity}, {bytes_out} bytes transferred',
                'severity_modifier': 1.5,
                'auto_case': True
            },
            'ransomware': {
                'template': 'CRITICAL: Ransomware behavior detected on {entity}',
                'severity_modifier': 2.0,
                'auto_case': True,
                'auto_isolate': True
            }
        }

        await self.redis_client.publish(
            'delivery.register_templates',
            json.dumps({
                'engine': 'correlation',
                'templates': templates
            })
        )

    # ========================================
    # HELPER METHODS
    # ========================================

    async def _get_source_quality(self, source_type: str) -> Dict:
        """
        Get quality metrics for a log source
        """
        # Query from Redis cache or database
        quality_key = f"source_quality:{source_type}"
        quality_data = await self.redis_client.get(quality_key)

        if quality_data:
            return json.loads(quality_data)

        # Default quality scores
        default_qualities = {
            'crowdstrike': {'score': 10, 'tier': 'premium'},
            'sentinel_one': {'score': 9, 'tier': 'premium'},
            'defender': {'score': 8, 'tier': 'good'},
            'elastic': {'score': 7, 'tier': 'good'},
            'wazuh': {'score': 6, 'tier': 'basic'},
            'sysmon': {'score': 7, 'tier': 'basic'},
            'firewall': {'score': 6, 'tier': 'basic'},
            'proxy': {'score': 6, 'tier': 'basic'}
        }

        return default_qualities.get(source_type, {'score': 5, 'tier': 'minimal'})

    async def _calculate_fidelity_score(self, alert: Dict) -> float:
        """
        Calculate detection fidelity score for an alert
        """
        base_score = alert.get('confidence', 0.5)

        # Adjust based on source quality
        sources = alert.get('evidence', {}).get('sources', [])
        quality_scores = []

        for source in sources:
            quality = await self._get_source_quality(source)
            quality_scores.append(quality.get('score', 5) / 10)

        if quality_scores:
            avg_quality = sum(quality_scores) / len(quality_scores)
            fidelity_score = base_score * avg_quality
        else:
            fidelity_score = base_score * 0.5

        return min(1.0, fidelity_score)

    async def _assess_detection_fidelity(self, attack_type: str, sources: List[str]) -> Dict:
        """
        Assess detection fidelity for specific attack with given sources
        """
        # This would connect to the fidelity assessment system
        fidelity_map = {
            'lateral_movement': {
                'required': ['edr', 'auth', 'network'],
                'confidence': {
                    'all': 0.95,
                    'partial': 0.60,
                    'minimal': 0.30
                }
            },
            'ransomware': {
                'required': ['edr'],
                'confidence': {
                    'all': 0.98,
                    'partial': 0.70,
                    'minimal': 0.40
                }
            },
            'data_exfiltration': {
                'required': ['proxy', 'dlp', 'edr'],
                'confidence': {
                    'all': 0.85,
                    'partial': 0.55,
                    'minimal': 0.35
                }
            }
        }

        attack_reqs = fidelity_map.get(attack_type, {})
        required = attack_reqs.get('required', [])

        # Calculate coverage
        coverage = len(set(sources) & set(required)) / len(required) if required else 0

        if coverage >= 0.8:
            level = 'all'
            fidelity = 'high'
        elif coverage >= 0.5:
            level = 'partial'
            fidelity = 'moderate'
        else:
            level = 'minimal'
            fidelity = 'low'

        confidence = attack_reqs.get('confidence', {}).get(level, 0.5)

        # Identify gaps
        gaps = list(set(required) - set(sources))

        # Generate recommendations
        recommendations = []
        if gaps:
            recommendations.append(f"Add {', '.join(gaps)} for better {attack_type} detection")

        return {
            'confidence': confidence,
            'fidelity': fidelity,
            'gaps': gaps,
            'recommendations': recommendations
        }

    async def _get_current_capabilities(self) -> Dict:
        """
        Get current correlation capabilities
        """
        # Get stats from correlation engine
        stats = await self.correlation_engine.get_rule_statistics()

        # Get active correlations
        active = await self.correlation_engine.get_active_correlations()

        # Determine detectable attacks
        detectable = []
        for rule in self.correlation_engine.rules.values():
            if rule.use_case not in detectable:
                detectable.append(rule.use_case)

        # Get MITRE coverage
        mitre_coverage = {}
        for rule in self.correlation_engine.rules.values():
            for technique in rule.mitre_techniques:
                mitre_coverage[technique] = mitre_coverage.get(technique, 0) + 1

        return {
            'total_rules': stats['total_rules'],
            'active_correlations': len(active),
            'detectable_attacks': detectable,
            'mitre_coverage': mitre_coverage,
            'required_sources': stats['coverage']['sources_required']
        }

    def _get_active_rules(self) -> List[Dict]:
        """
        Get active correlation rules for export
        """
        rules = []
        for rule in self.correlation_engine.rules.values():
            rules.append({
                'rule_id': rule.rule_id,
                'name': rule.name,
                'type': rule.rule_type.value,
                'use_case': rule.use_case,
                'required_sources': [s.value for s in rule.required_sources]
            })
        return rules

    async def _create_apt_correlation_rule(self, indicator: Dict):
        """
        Create APT-specific correlation rule from CTI
        """
        # This would create a new correlation rule based on APT indicators
        logger.info(f"Creating APT correlation rule for: {indicator.get('indicator')}")

        # Publish to backend for storage
        await self.redis_client.publish(
            'backend.rules.create',
            json.dumps({
                'rule_type': 'correlation_apt',
                'indicator': indicator.get('indicator'),
                'threat_actor': indicator.get('metadata', {}).get('actor'),
                'techniques': indicator.get('mitre_techniques', [])
            })
        )

    async def _create_ransomware_correlation_rule(self, indicator: Dict):
        """
        Create ransomware-specific correlation rule from CTI
        """
        logger.info(f"Creating ransomware correlation rule for: {indicator.get('indicator')}")

        # Publish to backend for storage
        await self.redis_client.publish(
            'backend.rules.create',
            json.dumps({
                'rule_type': 'correlation_ransomware',
                'indicator': indicator.get('indicator'),
                'family': indicator.get('metadata', {}).get('family'),
                'behavior_patterns': ['shadow_deletion', 'mass_encryption', 'ransom_note']
            })
        )


# ========================================
# ENGINE STARTUP
# ========================================

async def main():
    """
    Main execution for correlation Redis integration
    """
    integration = CorrelationRedisIntegration()

    # Connect to Redis
    await integration.connect()

    # Set up integrations with other engines
    await integration.integrate_with_intelligence_engine()
    await integration.integrate_with_backend_engine()
    await integration.integrate_with_delivery_engine()

    # Start listening for messages
    await integration.start_listening()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    logger.info("Starting Correlation Redis Integration...")

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Correlation Redis Integration stopped by user")
    except Exception as e:
        logger.error(f"Critical error: {e}")
        import traceback
        traceback.print_exc()