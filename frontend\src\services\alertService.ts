/**
 * Alert Service - API interactions for alerts, enrichment, and correlation
 */

import { Alert, EnrichmentData, CorrelationData, AlertEntities } from '../types/investigation';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8005/api';

export class AlertService {
  /**
   * List all alerts
   */
  static async listAlerts(params?: {
    status?: string;
    limit?: number;
  }): Promise<Alert[]> {
    const queryParams = new URLSearchParams();
    if (params?.status) queryParams.append('status', params.status);
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const response = await fetch(`${API_BASE_URL}/alerts?${queryParams}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch alerts: ${response.statusText}`);
    }

    const data = await response.json();
    return data.alerts || [];
  }

  /**
   * Get a single alert by ID
   */
  static async getAlert(alertId: string): Promise<Alert> {
    const response = await fetch(`${API_BASE_URL}/alerts/${alertId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch alert: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Trigger manual enrichment for an alert
   */
  static async triggerEnrichment(
    alertId: string,
    entities: AlertEntities
  ): Promise<{ success: boolean; request_id: string; message: string }> {
    const response = await fetch(`${API_BASE_URL}/alerts/${alertId}/enrich`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ entities })
    });

    if (!response.ok) {
      throw new Error(`Failed to trigger enrichment: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Trigger manual correlation for an alert
   */
  static async triggerCorrelation(
    alertId: string,
    params: {
      entities: AlertEntities;
      timestamp?: string;
      mitre_techniques?: string[];
    }
  ): Promise<{ success: boolean; request_id: string; message: string }> {
    const response = await fetch(`${API_BASE_URL}/alerts/${alertId}/correlate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      throw new Error(`Failed to trigger correlation: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get enrichment status/results for an alert
   */
  static async getEnrichment(alertId: string): Promise<EnrichmentData> {
    const response = await fetch(`${API_BASE_URL}/alerts/${alertId}/enrichment`);
    if (!response.ok) {
      throw new Error(`Failed to fetch enrichment: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get correlation results for an alert
   */
  static async getCorrelation(alertId: string): Promise<CorrelationData> {
    const response = await fetch(`${API_BASE_URL}/alerts/${alertId}/correlation`);
    if (!response.ok) {
      throw new Error(`Failed to fetch correlation: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get full investigation context for an alert
   */
  static async getAlertContext(alertId: string): Promise<any> {
    const response = await fetch(`${API_BASE_URL}/alerts/${alertId}/context`);
    if (!response.ok) {
      throw new Error(`Failed to fetch alert context: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Poll for enrichment completion
   */
  static async pollEnrichment(
    alertId: string,
    maxAttempts: number = 30,
    interval: number = 2000
  ): Promise<EnrichmentData> {
    for (let i = 0; i < maxAttempts; i++) {
      const enrichment = await this.getEnrichment(alertId);

      if (enrichment.status === 'completed' || enrichment.status === 'not_found') {
        return enrichment;
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, interval));
    }

    throw new Error('Enrichment polling timed out');
  }

  /**
   * Poll for correlation completion
   */
  static async pollCorrelation(
    alertId: string,
    maxAttempts: number = 30,
    interval: number = 2000
  ): Promise<CorrelationData> {
    for (let i = 0; i < maxAttempts; i++) {
      const correlation = await this.getCorrelation(alertId);

      if (correlation.status === 'completed' || correlation.status === 'not_found') {
        return correlation;
      }

      // Wait before next poll
      await new Promise(resolve => setTimeout(resolve, interval));
    }

    throw new Error('Correlation polling timed out');
  }
}

/**
 * WebSocket service for real-time updates
 */
export class AlertWebSocketService {
  private ws: WebSocket | null = null;
  private listeners: Map<string, Set<(data: any) => void>> = new Map();

  constructor(private url: string = 'ws://localhost:8005/ws') {}

  connect(): void {
    this.ws = new WebSocket(this.url);

    this.ws.onopen = () => {
      console.log('WebSocket connected');
    };

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.notifyListeners(message.channel, message.data);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    this.ws.onclose = () => {
      console.log('WebSocket disconnected');
      // Attempt to reconnect after 5 seconds
      setTimeout(() => this.connect(), 5000);
    };
  }

  subscribe(channel: string, callback: (data: any) => void): () => void {
    if (!this.listeners.has(channel)) {
      this.listeners.set(channel, new Set());
    }

    this.listeners.get(channel)!.add(callback);

    // Return unsubscribe function
    return () => {
      const channelListeners = this.listeners.get(channel);
      if (channelListeners) {
        channelListeners.delete(callback);
        if (channelListeners.size === 0) {
          this.listeners.delete(channel);
        }
      }
    };
  }

  private notifyListeners(channel: string, data: any): void {
    // Exact channel match
    const listeners = this.listeners.get(channel);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }

    // Pattern match (e.g., "contextualization.alert.enriched.*")
    this.listeners.forEach((callbacks, pattern) => {
      if (pattern.includes('*')) {
        const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');
        if (regex.test(channel)) {
          callbacks.forEach(callback => callback(data));
        }
      }
    });
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

export default AlertService;
