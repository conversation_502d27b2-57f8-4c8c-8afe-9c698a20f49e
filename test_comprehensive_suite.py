"""
SIEMLess v2.0 - Comprehensive Test Suite
========================================
Tests all 5 engines, infrastructure, and integrations
"""

import asyncio
import json
import logging
import time
import unittest
import requests
import redis
import psycopg2
from datetime import datetime
from typing import Dict, Any, List, Tuple
from psycopg2.extras import RealDictCursor
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_suite')


class TestConfig:
    """Test configuration"""
    # Engine ports
    INTELLIGENCE_PORT = 8001
    BACKEND_PORT = 8002
    INGESTION_PORT = 8003
    CONTEXTUALIZATION_PORT = 8004
    DELIVERY_PORT = 8005

    # Infrastructure
    REDIS_HOST = 'localhost'
    REDIS_PORT = 6380

    POSTGRES_HOST = 'localhost'
    POSTGRES_PORT = 5433
    POSTGRES_DB = 'siemless_v2'
    POSTGRES_USER = 'siemless'
    POSTGRES_PASSWORD = 'siemless123'

    # Timeouts
    HTTP_TIMEOUT = 10
    REDIS_TIMEOUT = 5
    DB_TIMEOUT = 5

    # Test data
    TEST_LOG = {
        "timestamp": datetime.utcnow().isoformat(),
        "source_ip": "*************",
        "destination_ip": "********",
        "action": "ALLOW",
        "protocol": "TCP",
        "port": 443,
        "message": "Test security event"
    }


class InfrastructureTests(unittest.TestCase):
    """Test infrastructure services"""

    @classmethod
    def setUpClass(cls):
        """Setup test connections"""
        cls.config = TestConfig()

    def test_redis_connectivity(self):
        """Test Redis connection and basic operations"""
        try:
            client = redis.Redis(
                host=self.config.REDIS_HOST,
                port=self.config.REDIS_PORT,
                decode_responses=True,
                socket_connect_timeout=self.config.REDIS_TIMEOUT
            )

            # Test ping
            result = client.ping()
            self.assertTrue(result, "Redis ping failed")

            # Test set/get
            test_key = f"test_key_{datetime.utcnow().timestamp()}"
            test_value = "test_value"
            client.set(test_key, test_value)
            retrieved = client.get(test_key)
            self.assertEqual(retrieved, test_value, "Redis set/get failed")

            # Test pub/sub
            pubsub = client.pubsub()
            channel = f"test_channel_{datetime.utcnow().timestamp()}"
            pubsub.subscribe(channel)

            # Publish message
            message = {"test": "data"}
            client.publish(channel, json.dumps(message))

            # Cleanup
            client.delete(test_key)
            pubsub.unsubscribe(channel)

            logger.info("✅ Redis connectivity test passed")

        except Exception as e:
            self.fail(f"Redis connectivity test failed: {e}")

    def test_postgresql_connectivity(self):
        """Test PostgreSQL connection and schema"""
        try:
            conn = psycopg2.connect(
                host=self.config.POSTGRES_HOST,
                port=self.config.POSTGRES_PORT,
                database=self.config.POSTGRES_DB,
                user=self.config.POSTGRES_USER,
                password=self.config.POSTGRES_PASSWORD,
                cursor_factory=RealDictCursor,
                connect_timeout=self.config.DB_TIMEOUT
            )

            cursor = conn.cursor()

            # Test basic query
            cursor.execute("SELECT 1 as test")
            result = cursor.fetchone()
            self.assertEqual(result['test'], 1, "PostgreSQL basic query failed")

            # Test critical tables exist
            tables_to_check = [
                'engine_coordination',
                'workflow_instances',
                'pattern_library',
                'crystallized_patterns',
                'detection_rules',
                'entities',
                'relationships',
                'warm_storage',
                'ingestion_logs',
                'cases'
            ]

            for table in tables_to_check:
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = %s
                    )
                """, (table,))
                exists = cursor.fetchone()['exists']
                self.assertTrue(exists, f"Table {table} does not exist")

            cursor.close()
            conn.close()

            logger.info("✅ PostgreSQL connectivity and schema test passed")

        except Exception as e:
            self.fail(f"PostgreSQL connectivity test failed: {e}")


class EngineHealthTests(unittest.TestCase):
    """Test engine health endpoints"""

    @classmethod
    def setUpClass(cls):
        """Setup test configuration"""
        cls.config = TestConfig()
        cls.base_url = "http://localhost"

    def _test_engine_health(self, port: int, engine_name: str):
        """Generic engine health test"""
        url = f"{self.base_url}:{port}/health"

        try:
            response = requests.get(url, timeout=self.config.HTTP_TIMEOUT)

            # Check response code
            self.assertEqual(
                response.status_code, 200,
                f"{engine_name} health check returned {response.status_code}"
            )

            # Check response format
            data = response.json()
            self.assertIn('status', data, f"{engine_name} health missing status")
            self.assertIn('engine', data, f"{engine_name} health missing engine name")

            # Check healthy status
            self.assertIn(
                data['status'], ['healthy', 'running'],
                f"{engine_name} status is {data['status']}"
            )

            logger.info(f"✅ {engine_name} Engine health check passed")
            return data

        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ {engine_name} Engine health check failed: {e}")
            self.skipTest(f"{engine_name} Engine not accessible at port {port}")

    def test_intelligence_engine_health(self):
        """Test Intelligence Engine health endpoint"""
        self._test_engine_health(self.config.INTELLIGENCE_PORT, "Intelligence")

    def test_backend_engine_health(self):
        """Test Backend Engine health endpoint"""
        self._test_engine_health(self.config.BACKEND_PORT, "Backend")

    def test_ingestion_engine_health(self):
        """Test Ingestion Engine health endpoint"""
        self._test_engine_health(self.config.INGESTION_PORT, "Ingestion")

    def test_contextualization_engine_health(self):
        """Test Contextualization Engine health endpoint"""
        self._test_engine_health(self.config.CONTEXTUALIZATION_PORT, "Contextualization")

    def test_delivery_engine_health(self):
        """Test Delivery Engine health endpoint"""
        self._test_engine_health(self.config.DELIVERY_PORT, "Delivery")


class EngineCoordinationTests(unittest.TestCase):
    """Test engine coordination and registration"""

    @classmethod
    def setUpClass(cls):
        """Setup database connection"""
        cls.config = TestConfig()
        cls.conn = psycopg2.connect(
            host=cls.config.POSTGRES_HOST,
            port=cls.config.POSTGRES_PORT,
            database=cls.config.POSTGRES_DB,
            user=cls.config.POSTGRES_USER,
            password=cls.config.POSTGRES_PASSWORD,
            cursor_factory=RealDictCursor
        )

    @classmethod
    def tearDownClass(cls):
        """Close database connection"""
        if cls.conn:
            cls.conn.close()

    def test_engine_registration(self):
        """Test engines are registered in coordination table"""
        cursor = self.conn.cursor()

        cursor.execute("""
            SELECT engine_id, status, last_heartbeat
            FROM engine_coordination
            WHERE status IN ('running', 'starting')
        """)

        engines = cursor.fetchall()

        # Check if any engines are registered
        if len(engines) > 0:
            logger.info(f"✅ Found {len(engines)} registered engines")

            for engine in engines:
                logger.info(f"  - {engine['engine_id']}: {engine['status']}")

                # Check heartbeat is recent (within last 5 minutes)
                if engine['last_heartbeat']:
                    age = datetime.utcnow() - engine['last_heartbeat']
                    self.assertLess(
                        age.total_seconds(), 300,
                        f"{engine['engine_id']} heartbeat is stale"
                    )
        else:
            logger.warning("⚠️ No engines registered in coordination table")

        cursor.close()


class MessageQueueTests(unittest.TestCase):
    """Test inter-engine message communication"""

    @classmethod
    def setUpClass(cls):
        """Setup Redis connection"""
        cls.config = TestConfig()
        cls.redis_client = redis.Redis(
            host=cls.config.REDIS_HOST,
            port=cls.config.REDIS_PORT,
            decode_responses=True
        )

    def test_message_channels(self):
        """Test message channel subscriptions"""
        channels = [
            'intelligence.pattern_crystallized',
            'backend.store_data',
            'ingestion.start_source',
            'contextualization.enrich_entity',
            'delivery.case_created'
        ]

        for channel in channels:
            # Test publishing
            test_message = {
                'test': True,
                'timestamp': datetime.utcnow().isoformat(),
                'channel': channel
            }

            result = self.redis_client.publish(channel, json.dumps(test_message))
            logger.info(f"✅ Published to {channel} (subscribers: {result})")

    def test_heartbeat_channels(self):
        """Test heartbeat channels"""
        engines = ['intelligence', 'backend', 'ingestion', 'contextualization', 'delivery']

        for engine in engines:
            channel = f'heartbeat.{engine}'

            # Check for recent heartbeat messages
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe(channel)

            # Give time for any heartbeat
            message = pubsub.get_message(timeout=2)

            if message and message['type'] == 'message':
                logger.info(f"✅ Received heartbeat from {engine}")
            else:
                logger.warning(f"⚠️ No heartbeat from {engine}")

            pubsub.unsubscribe(channel)


class PatternLibraryTests(unittest.TestCase):
    """Test pattern library functionality"""

    @classmethod
    def setUpClass(cls):
        """Setup database connection"""
        cls.config = TestConfig()
        cls.conn = psycopg2.connect(
            host=cls.config.POSTGRES_HOST,
            port=cls.config.POSTGRES_PORT,
            database=cls.config.POSTGRES_DB,
            user=cls.config.POSTGRES_USER,
            password=cls.config.POSTGRES_PASSWORD,
            cursor_factory=RealDictCursor
        )

    @classmethod
    def tearDownClass(cls):
        """Close database connection"""
        if cls.conn:
            cls.conn.close()

    def test_pattern_library_status(self):
        """Test pattern library contains patterns"""
        cursor = self.conn.cursor()

        # Check active patterns
        cursor.execute("""
            SELECT COUNT(*) as total,
                   COUNT(*) FILTER (WHERE is_active = true) as active
            FROM pattern_library
        """)

        result = cursor.fetchone()
        logger.info(f"✅ Pattern Library: {result['total']} total, {result['active']} active")

        # Check pattern structure
        cursor.execute("""
            SELECT pattern_id, pattern_name, pattern_data
            FROM pattern_library
            WHERE is_active = true
            LIMIT 1
        """)

        pattern = cursor.fetchone()
        if pattern:
            self.assertIsNotNone(pattern['pattern_id'], "Pattern ID missing")
            self.assertIsNotNone(pattern['pattern_name'], "Pattern name missing")
            self.assertIsNotNone(pattern['pattern_data'], "Pattern data missing")
            logger.info(f"✅ Pattern structure validated: {pattern['pattern_name']}")
        else:
            logger.warning("⚠️ No active patterns found")

        cursor.close()

    def test_crystallized_patterns(self):
        """Test crystallized patterns storage"""
        cursor = self.conn.cursor()

        cursor.execute("""
            SELECT COUNT(*) as total,
                   AVG(confidence_score) as avg_confidence
            FROM crystallized_patterns
        """)

        result = cursor.fetchone()

        if result['total'] > 0:
            logger.info(
                f"✅ Crystallized Patterns: {result['total']} patterns, "
                f"avg confidence: {result['avg_confidence']:.2f}"
            )
        else:
            logger.warning("⚠️ No crystallized patterns found")

        cursor.close()


class DeliveryEngineAPITests(unittest.TestCase):
    """Test Delivery Engine REST API endpoints"""

    @classmethod
    def setUpClass(cls):
        """Setup test configuration"""
        cls.config = TestConfig()
        cls.base_url = f"http://localhost:{cls.config.DELIVERY_PORT}"

    def test_api_status(self):
        """Test API status endpoint"""
        url = f"{self.base_url}/api/status"

        try:
            response = requests.get(url, timeout=self.config.HTTP_TIMEOUT)

            if response.status_code == 200:
                data = response.json()
                self.assertIn('status', data)
                logger.info(f"✅ API Status: {data['status']}")
            else:
                logger.warning(f"⚠️ API Status returned {response.status_code}")

        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ Delivery Engine API not accessible: {e}")
            self.skipTest("Delivery Engine API not accessible")

    def test_case_management_endpoints(self):
        """Test case management CRUD operations"""
        # Test case creation
        case_data = {
            'title': 'Test Case',
            'description': 'Automated test case',
            'priority': 'medium',
            'status': 'open'
        }

        try:
            # Create case
            response = requests.post(
                f"{self.base_url}/api/cases",
                json=case_data,
                timeout=self.config.HTTP_TIMEOUT
            )

            if response.status_code in [200, 201]:
                created_case = response.json()
                case_id = created_case.get('id')
                logger.info(f"✅ Case created: {case_id}")

                # Get case
                response = requests.get(
                    f"{self.base_url}/api/cases/{case_id}",
                    timeout=self.config.HTTP_TIMEOUT
                )

                if response.status_code == 200:
                    logger.info(f"✅ Case retrieved: {case_id}")

                # Update case
                update_data = {'status': 'in_progress'}
                response = requests.put(
                    f"{self.base_url}/api/cases/{case_id}",
                    json=update_data,
                    timeout=self.config.HTTP_TIMEOUT
                )

                if response.status_code == 200:
                    logger.info(f"✅ Case updated: {case_id}")

                # Delete case
                response = requests.delete(
                    f"{self.base_url}/api/cases/{case_id}",
                    timeout=self.config.HTTP_TIMEOUT
                )

                if response.status_code in [200, 204]:
                    logger.info(f"✅ Case deleted: {case_id}")
            else:
                logger.warning(f"⚠️ Case creation returned {response.status_code}")

        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ Case management API test failed: {e}")
            self.skipTest("Case management API not accessible")


class DataProcessingTests(unittest.TestCase):
    """Test data processing functionality"""

    @classmethod
    def setUpClass(cls):
        """Setup connections"""
        cls.config = TestConfig()
        cls.redis_client = redis.Redis(
            host=cls.config.REDIS_HOST,
            port=cls.config.REDIS_PORT,
            decode_responses=True
        )
        cls.conn = psycopg2.connect(
            host=cls.config.POSTGRES_HOST,
            port=cls.config.POSTGRES_PORT,
            database=cls.config.POSTGRES_DB,
            user=cls.config.POSTGRES_USER,
            password=cls.config.POSTGRES_PASSWORD,
            cursor_factory=RealDictCursor
        )

    @classmethod
    def tearDownClass(cls):
        """Close connections"""
        if cls.conn:
            cls.conn.close()

    def test_log_ingestion_stats(self):
        """Test log ingestion statistics"""
        cursor = self.conn.cursor()

        cursor.execute("""
            SELECT
                COUNT(*) as total_logs,
                COUNT(DISTINCT source_type) as sources,
                COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '1 hour') as recent_logs
            FROM ingestion_logs
        """)

        stats = cursor.fetchone()

        logger.info(f"✅ Ingestion Stats: {stats['total_logs']} total logs from {stats['sources']} sources")
        logger.info(f"   Recent logs (last hour): {stats['recent_logs']}")

        cursor.close()

    def test_entity_extraction_stats(self):
        """Test entity extraction statistics"""
        cursor = self.conn.cursor()

        cursor.execute("""
            SELECT
                COUNT(*) as total_entities,
                COUNT(DISTINCT entity_type) as entity_types
            FROM entities
        """)

        stats = cursor.fetchone()

        if stats['total_entities'] > 0:
            logger.info(
                f"✅ Entity Extraction: {stats['total_entities']} entities, "
                f"{stats['entity_types']} types"
            )

            # Check entity types distribution
            cursor.execute("""
                SELECT entity_type, COUNT(*) as count
                FROM entities
                GROUP BY entity_type
                ORDER BY count DESC
                LIMIT 5
            """)

            types = cursor.fetchall()
            for entity_type in types:
                logger.info(f"   - {entity_type['entity_type']}: {entity_type['count']}")
        else:
            logger.warning("⚠️ No entities extracted yet")

        cursor.close()

    def test_relationship_mapping(self):
        """Test relationship mapping statistics"""
        cursor = self.conn.cursor()

        cursor.execute("""
            SELECT
                COUNT(*) as total_relationships,
                COUNT(DISTINCT relationship_type) as relationship_types
            FROM relationships
        """)

        stats = cursor.fetchone()

        if stats['total_relationships'] > 0:
            logger.info(
                f"✅ Relationships: {stats['total_relationships']} relationships, "
                f"{stats['relationship_types']} types"
            )
        else:
            logger.warning("⚠️ No relationships mapped yet")

        cursor.close()


class PerformanceTests(unittest.TestCase):
    """Test system performance metrics"""

    @classmethod
    def setUpClass(cls):
        """Setup connections"""
        cls.config = TestConfig()
        cls.conn = psycopg2.connect(
            host=cls.config.POSTGRES_HOST,
            port=cls.config.POSTGRES_PORT,
            database=cls.config.POSTGRES_DB,
            user=cls.config.POSTGRES_USER,
            password=cls.config.POSTGRES_PASSWORD,
            cursor_factory=RealDictCursor
        )

    @classmethod
    def tearDownClass(cls):
        """Close connections"""
        if cls.conn:
            cls.conn.close()

    def test_processing_performance(self):
        """Test processing performance metrics"""
        cursor = self.conn.cursor()

        # Check warm storage for processing times
        cursor.execute("""
            SELECT
                COUNT(*) as total_processed,
                AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_processing_time
            FROM warm_storage
            WHERE updated_at IS NOT NULL
        """)

        stats = cursor.fetchone()

        if stats['total_processed'] > 0:
            logger.info(
                f"✅ Processing Performance: {stats['total_processed']} items, "
                f"avg time: {stats['avg_processing_time']:.2f}s"
            )
        else:
            logger.warning("⚠️ No processing metrics available")

        cursor.close()

    def test_storage_distribution(self):
        """Test storage distribution across tiers"""
        cursor = self.conn.cursor()

        # Check warm storage size
        cursor.execute("""
            SELECT
                pg_size_pretty(pg_total_relation_size('warm_storage')) as warm_size,
                COUNT(*) as warm_records
            FROM warm_storage
        """)

        warm_stats = cursor.fetchone()

        logger.info(
            f"✅ Storage Distribution: Warm storage - "
            f"{warm_stats['warm_records']} records, {warm_stats['warm_size']}"
        )

        cursor.close()


class IntegrationTests(unittest.TestCase):
    """Test end-to-end integration scenarios"""

    @classmethod
    def setUpClass(cls):
        """Setup connections"""
        cls.config = TestConfig()
        cls.redis_client = redis.Redis(
            host=cls.config.REDIS_HOST,
            port=cls.config.REDIS_PORT,
            decode_responses=True
        )

    def test_log_to_pattern_flow(self):
        """Test log processing flow from ingestion to pattern matching"""
        test_log = self.config.TEST_LOG

        # Simulate log ingestion
        channel = 'ingestion.new_log'
        message = {
            'source_engine': 'test_suite',
            'timestamp': datetime.utcnow().isoformat(),
            'data': test_log
        }

        # Publish log to ingestion channel
        result = self.redis_client.publish(channel, json.dumps(message))

        logger.info(f"✅ Published test log to ingestion channel (subscribers: {result})")

        # Give time for processing
        time.sleep(2)

        # Check if pattern matching occurred
        # This would be more complete with actual pattern checking
        logger.info("✅ Log-to-pattern flow test completed")

    def test_cti_to_rule_flow(self):
        """Test CTI to rule generation flow"""
        # Simulate CTI indicator
        cti_indicator = {
            'type': 'ip',
            'value': '*************',
            'threat_type': 'malware',
            'confidence': 0.8
        }

        channel = 'backend.cti_update'
        message = {
            'source_engine': 'test_suite',
            'timestamp': datetime.utcnow().isoformat(),
            'data': cti_indicator
        }

        # Publish CTI update
        result = self.redis_client.publish(channel, json.dumps(message))

        logger.info(f"✅ Published CTI indicator (subscribers: {result})")


def run_comprehensive_tests():
    """Run all test suites"""
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add test classes
    test_classes = [
        InfrastructureTests,
        EngineHealthTests,
        EngineCoordinationTests,
        MessageQueueTests,
        PatternLibraryTests,
        DeliveryEngineAPITests,
        DataProcessingTests,
        PerformanceTests,
        IntegrationTests
    ]

    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)

    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2)

    print("\n" + "="*60)
    print("SIEMLess v2.0 - Comprehensive Test Suite")
    print("="*60)
    print(f"Testing {len(test_classes)} components...")
    print("="*60 + "\n")

    result = runner.run(suite)

    # Summary
    print("\n" + "="*60)
    print("Test Summary")
    print("="*60)
    print(f"Tests Run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped)}")

    if result.wasSuccessful():
        print("\n✅ All tests passed successfully!")
    else:
        print("\n❌ Some tests failed. Review the output above.")

    print("="*60)

    return result.wasSuccessful()


if __name__ == "__main__":
    # Run comprehensive tests
    success = run_comprehensive_tests()

    # Exit with appropriate code
    sys.exit(0 if success else 1)