# SIEMLess v2.0 Environment Configuration

# Database
POSTGRES_PASSWORD=siemless123
POSTGRES_DB=siemless_v2
POSTGRES_USER=siemless

# Redis
REDIS_HOST=redis
REDIS_PORT=6379

# Security
JWT_SECRET=siemless-v2-secret-key-2024
SIEMLESS_MASTER_KEY=DvX+ObFPVMKgMgb5v9R6GbnYmbqndW3fqlqpQQnHb70=

# AI Providers (Optional - for AI Consensus Engine)
GEMINI_API_KEY=
ANTHROPIC_API_KEY=
OPENAI_API_KEY=

# Monitoring
GRAFANA_PASSWORD=admin
LOG_LEVEL=INFO

# Performance
PARSER_REPLICAS=2
BUFFER_THRESHOLD=10
CONSENSUS_THRESHOLD=0.8
CONFIDENCE_THRESHOLD=0.7