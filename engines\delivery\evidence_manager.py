"""
Investigation Evidence Manager
Handles evidence collection and link-back to source SIEMs

Features:
- Query language generation for each SIEM
- URL-based filtering back to SIEMs
- Evidence retention with intelligent policies
- On-demand log retrieval (no permanent storage)
"""

import json
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging
from urllib.parse import urlencode, quote


@dataclass
class Evidence:
    """Evidence item"""
    id: str
    investigation_id: str
    source_siem: str
    evidence_type: str  # 'log', 'alert', 'query_result'
    query: str
    siem_url: str
    event_count: int
    timestamp_start: datetime
    timestamp_end: datetime
    entities_involved: Dict
    retention_days: int
    created_at: datetime


class SIEMQueryGenerator:
    """Generate SIEM-specific queries for evidence retrieval"""

    @staticmethod
    def generate_elastic_query(entities: Dict, time_range: Dict) -> Dict:
        """
        Generate Elasticsearch query (KQL or DSL)

        Args:
            entities: {'ips': [...], 'users': [...], 'hosts': [...]}
            time_range: {'start': datetime, 'end': datetime}

        Returns:
            {'kql': '...', 'dsl': {...}, 'url': '...'}
        """
        # Build KQL query
        kql_parts = []

        if entities.get('ips'):
            ip_clause = ' or '.join([f'source.ip:"{ip}" or destination.ip:"{ip}"' for ip in entities['ips']])
            kql_parts.append(f"({ip_clause})")

        if entities.get('users'):
            user_clause = ' or '.join([f'user.name:"{user}"' for user in entities['users']])
            kql_parts.append(f"({user_clause})")

        if entities.get('hosts'):
            host_clause = ' or '.join([f'host.name:"{host}"' for host in entities['hosts']])
            kql_parts.append(f"({host_clause})")

        if entities.get('processes'):
            proc_clause = ' or '.join([f'process.name:"{proc}"' for proc in entities['processes']])
            kql_parts.append(f"({proc_clause})")

        kql_query = ' and '.join(kql_parts) if kql_parts else '*'

        # Build DSL query for API
        must_clauses = []

        if entities.get('ips'):
            must_clauses.append({
                "bool": {
                    "should": [
                        {"term": {"source.ip": ip}} for ip in entities['ips']
                    ] + [
                        {"term": {"destination.ip": ip}} for ip in entities['ips']
                    ]
                }
            })

        if entities.get('users'):
            must_clauses.append({
                "terms": {"user.name": entities['users']}
            })

        if entities.get('hosts'):
            must_clauses.append({
                "terms": {"host.name": entities['hosts']}
            })

        # Add time range
        must_clauses.append({
            "range": {
                "@timestamp": {
                    "gte": time_range['start'].isoformat(),
                    "lte": time_range['end'].isoformat()
                }
            }
        })

        dsl_query = {
            "query": {
                "bool": {
                    "must": must_clauses
                }
            },
            "sort": [{"@timestamp": "desc"}],
            "size": 10000
        }

        # Generate Kibana URL
        kibana_url = SIEMQueryGenerator._generate_elastic_url(kql_query, time_range)

        return {
            'kql': kql_query,
            'dsl': dsl_query,
            'url': kibana_url
        }

    @staticmethod
    def _generate_elastic_url(kql_query: str, time_range: Dict) -> str:
        """Generate Kibana URL with embedded query"""
        # Base Kibana URL (customize per deployment)
        base_url = "https://kibana.company.com/app/discover#/"

        # Build query state
        query_state = {
            'query': {
                'query': kql_query,
                'language': 'kuery'
            },
            'filters': [],
            'time': {
                'from': time_range['start'].isoformat(),
                'to': time_range['end'].isoformat()
            }
        }

        # URL encode
        encoded_state = quote(json.dumps(query_state))
        return f"{base_url}?_g=(time:(from:'{time_range['start'].isoformat()}',to:'{time_range['end'].isoformat()}'))&_a=(query:(language:kuery,query:'{quote(kql_query)}'))"

    @staticmethod
    def generate_splunk_query(entities: Dict, time_range: Dict) -> Dict:
        """
        Generate Splunk SPL query

        Returns:
            {'spl': '...', 'url': '...'}
        """
        # Build SPL query
        spl_parts = ['search']

        # Index specification
        spl_parts.append('index=* OR index=_*')

        # Entity filters
        entity_filters = []

        if entities.get('ips'):
            ip_filter = ' OR '.join([f'src_ip="{ip}" OR dest_ip="{ip}"' for ip in entities['ips']])
            entity_filters.append(f"({ip_filter})")

        if entities.get('users'):
            user_filter = ' OR '.join([f'user="{user}"' for user in entities['users']])
            entity_filters.append(f"({user_filter})")

        if entities.get('hosts'):
            host_filter = ' OR '.join([f'host="{host}"' for host in entities['hosts']])
            entity_filters.append(f"({host_filter})")

        if entity_filters:
            spl_parts.append('(' + ' AND '.join(entity_filters) + ')')

        # Time range in Splunk format
        earliest = time_range['start'].strftime('%m/%d/%Y:%H:%M:%S')
        latest = time_range['end'].strftime('%m/%d/%Y:%H:%M:%S')

        spl_query = ' '.join(spl_parts)
        full_spl = f"{spl_query} earliest={earliest} latest={latest}"

        # Generate Splunk URL
        splunk_url = SIEMQueryGenerator._generate_splunk_url(full_spl, time_range)

        return {
            'spl': full_spl,
            'url': splunk_url
        }

    @staticmethod
    def _generate_splunk_url(spl_query: str, time_range: Dict) -> str:
        """Generate Splunk URL with embedded search"""
        base_url = "https://splunk.company.com/en-US/app/search/search"

        params = {
            'q': spl_query,
            'earliest': time_range['start'].isoformat(),
            'latest': time_range['end'].isoformat(),
            'display.page.search.mode': 'verbose'
        }

        return f"{base_url}?{urlencode(params)}"

    @staticmethod
    def generate_sentinel_query(entities: Dict, time_range: Dict) -> Dict:
        """
        Generate Microsoft Sentinel KQL query

        Returns:
            {'kql': '...', 'url': '...'}
        """
        # Build KQL query
        kql_parts = []

        # Table selection
        kql_parts.append("SecurityEvent")

        # Time filter
        time_filter = f"TimeGenerated between (datetime({time_range['start'].isoformat()}) .. datetime({time_range['end'].isoformat()}))"
        kql_parts.append(f"| where {time_filter}")

        # Entity filters
        where_clauses = []

        if entities.get('ips'):
            ip_clause = ' or '.join([f'IpAddress == "{ip}"' for ip in entities['ips']])
            where_clauses.append(f"({ip_clause})")

        if entities.get('users'):
            user_clause = ' or '.join([f'Account == "{user}"' for user in entities['users']])
            where_clauses.append(f"({user_clause})")

        if entities.get('hosts'):
            host_clause = ' or '.join([f'Computer == "{host}"' for host in entities['hosts']])
            where_clauses.append(f"({host_clause})")

        if where_clauses:
            kql_parts.append(f"| where {' and '.join(where_clauses)}")

        kql_parts.append("| order by TimeGenerated desc")
        kql_parts.append("| limit 10000")

        kql_query = '\n'.join(kql_parts)

        # Generate Azure portal URL
        sentinel_url = SIEMQueryGenerator._generate_sentinel_url(kql_query, time_range)

        return {
            'kql': kql_query,
            'url': sentinel_url
        }

    @staticmethod
    def _generate_sentinel_url(kql_query: str, time_range: Dict) -> str:
        """Generate Sentinel URL"""
        # Sentinel uses Azure portal with encoded query
        base_url = "https://portal.azure.com/#blade/Microsoft_Azure_Security_Insights/MainMenuBlade/Logs"

        # Query parameter
        query_param = quote(kql_query)

        return f"{base_url}?query={query_param}"

    @staticmethod
    def generate_qradar_query(entities: Dict, time_range: Dict) -> Dict:
        """
        Generate QRadar AQL query

        Returns:
            {'aql': '...', 'url': '...'}
        """
        # Build AQL query
        aql_parts = ['SELECT *', 'FROM events']

        # WHERE clause
        where_clauses = []

        if entities.get('ips'):
            ip_clause = ' OR '.join([f"sourceip='{ip}' OR destinationip='{ip}'" for ip in entities['ips']])
            where_clauses.append(f"({ip_clause})")

        if entities.get('users'):
            user_clause = ' OR '.join([f"username='{user}'" for user in entities['users']])
            where_clauses.append(f"({user_clause})")

        # Time range (QRadar uses milliseconds)
        start_ms = int(time_range['start'].timestamp() * 1000)
        end_ms = int(time_range['end'].timestamp() * 1000)
        where_clauses.append(f"starttime >= {start_ms} AND endtime <= {end_ms}")

        if where_clauses:
            aql_parts.append(f"WHERE {' AND '.join(where_clauses)}")

        aql_parts.append("ORDER BY starttime DESC")
        aql_parts.append("LIMIT 10000")

        aql_query = ' '.join(aql_parts)

        # Generate QRadar console URL
        qradar_url = SIEMQueryGenerator._generate_qradar_url(aql_query)

        return {
            'aql': aql_query,
            'url': qradar_url
        }

    @staticmethod
    def _generate_qradar_url(aql_query: str) -> str:
        """Generate QRadar console URL"""
        base_url = "https://qradar.company.com/console/do/ariel/arielSearch"

        params = {
            'appName': 'EventViewer',
            'pageId': 'EventList',
            'query': aql_query
        }

        return f"{base_url}?{urlencode(params)}"


class EvidenceManager:
    """
    Manages investigation evidence with SIEM link-back
    - Generates SIEM-specific queries
    - Creates URLs for direct access
    - Implements retention policies
    - NO permanent storage (on-demand retrieval)
    """

    def __init__(self, db_connection, logger: Optional[logging.Logger] = None):
        self.db = db_connection
        self.logger = logger or logging.getLogger(__name__)
        self.query_generator = SIEMQueryGenerator()

    async def create_evidence(
        self,
        investigation_id: str,
        source_siem: str,
        entities: Dict,
        time_range: Dict,
        evidence_type: str = 'query_result'
    ) -> Evidence:
        """
        Create evidence item with SIEM link-back

        Args:
            investigation_id: UUID of investigation
            source_siem: 'elastic', 'splunk', 'sentinel', 'qradar', 'chronicle'
            entities: Entities to search for
            time_range: {'start': datetime, 'end': datetime}
            evidence_type: Type of evidence

        Returns:
            Evidence object with SIEM query and URL
        """
        # Generate SIEM-specific query
        if source_siem == 'elastic':
            query_data = self.query_generator.generate_elastic_query(entities, time_range)
            query = query_data['kql']
            siem_url = query_data['url']

        elif source_siem == 'splunk':
            query_data = self.query_generator.generate_splunk_query(entities, time_range)
            query = query_data['spl']
            siem_url = query_data['url']

        elif source_siem == 'sentinel':
            query_data = self.query_generator.generate_sentinel_query(entities, time_range)
            query = query_data['kql']
            siem_url = query_data['url']

        elif source_siem == 'qradar':
            query_data = self.query_generator.generate_qradar_query(entities, time_range)
            query = query_data['aql']
            siem_url = query_data['url']

        else:
            raise ValueError(f"Unsupported SIEM: {source_siem}")

        # Calculate retention based on investigation priority
        # (Would integrate with EPSS scoring for CVEs)
        retention_days = self._calculate_retention(investigation_id)

        # Create evidence record
        evidence = Evidence(
            id=str(self._generate_evidence_id()),
            investigation_id=investigation_id,
            source_siem=source_siem,
            evidence_type=evidence_type,
            query=query,
            siem_url=siem_url,
            event_count=0,  # Populated on execution
            timestamp_start=time_range['start'],
            timestamp_end=time_range['end'],
            entities_involved=entities,
            retention_days=retention_days,
            created_at=datetime.utcnow()
        )

        # Store in database
        await self._store_evidence(evidence)

        self.logger.info(f"Evidence created for investigation {investigation_id}: {source_siem}")

        return evidence

    def _calculate_retention(self, investigation_id: str) -> int:
        """
        Calculate retention period based on investigation characteristics

        Factors:
        - Severity (high = longer retention)
        - MITRE techniques (some require longer retention for compliance)
        - EPSS scores for CVEs (high EPSS = longer retention)
        - Regulatory requirements

        Returns:
            Retention days
        """
        # Base retention: 30 days
        base_retention = 30

        # TODO: Query investigation severity, MITRE techniques, CVEs
        # For now, return base
        return base_retention

    async def _store_evidence(self, evidence: Evidence):
        """Store evidence in database"""
        if not self.db:
            return

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO investigation_evidence
                (id, investigation_id, source_siem, evidence_type, siem_query,
                 siem_url, event_count, timestamp_start, timestamp_end, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                evidence.id,
                evidence.investigation_id,
                evidence.source_siem,
                evidence.evidence_type,
                evidence.query,
                evidence.siem_url,
                evidence.event_count,
                evidence.timestamp_start,
                evidence.timestamp_end,
                evidence.created_at
            ))
            self.db.commit()

        except Exception as e:
            self.logger.error(f"Failed to store evidence: {e}")

    def _generate_evidence_id(self) -> int:
        """Generate unique evidence ID"""
        # Simple implementation - would use sequence in production
        import random
        return random.randint(1000, 999999)

    async def get_evidence(self, investigation_id: str) -> List[Evidence]:
        """Get all evidence for an investigation"""
        if not self.db:
            return []

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT id, investigation_id, source_siem, evidence_type, siem_query,
                       siem_url, event_count, timestamp_start, timestamp_end, created_at
                FROM investigation_evidence
                WHERE investigation_id = %s
                ORDER BY created_at DESC
            """, (investigation_id,))

            evidence_list = []
            for row in cursor.fetchall():
                evidence_list.append(Evidence(
                    id=str(row[0]),
                    investigation_id=row[1],
                    source_siem=row[2],
                    evidence_type=row[3],
                    query=row[4],
                    siem_url=row[5],
                    event_count=row[6],
                    timestamp_start=row[7],
                    timestamp_end=row[8],
                    entities_involved={},  # Not stored currently
                    retention_days=30,  # Default
                    created_at=row[9]
                ))

            return evidence_list

        except Exception as e:
            self.logger.error(f"Failed to get evidence: {e}")
            return []

    async def execute_evidence_query(self, evidence_id: str) -> Dict:
        """
        Execute evidence query against SIEM (on-demand retrieval)

        This would make API call to SIEM to retrieve actual events
        Returns count and summary, NOT full logs (link-back strategy)
        """
        # This is a placeholder - would implement actual SIEM API calls
        return {
            'evidence_id': evidence_id,
            'execution_time': datetime.utcnow().isoformat(),
            'event_count': 0,
            'status': 'Use siem_url to view full results in SIEM'
        }

    async def cleanup_expired_evidence(self):
        """Clean up evidence past retention period"""
        if not self.db:
            return

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                DELETE FROM investigation_evidence
                WHERE created_at < NOW() - INTERVAL '30 days'
            """)
            deleted_count = cursor.rowcount
            self.db.commit()

            self.logger.info(f"Cleaned up {deleted_count} expired evidence items")

        except Exception as e:
            self.logger.error(f"Evidence cleanup failed: {e}")
