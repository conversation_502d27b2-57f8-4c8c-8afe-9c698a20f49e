"""
Elastic Rule Harvester - Real Implementation for v2.0
Extracts detection rules, saved searches, and alerts from Elasticsearch/Elastic Security

This is a REAL implementation that connects to actual Elastic instances.
"""

import aiohttp
import asyncio
import json
import base64
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass, asdict


@dataclass
class ElasticRule:
    """Represents an Elastic Security detection rule"""
    rule_id: str
    name: str
    description: str
    query: str
    language: str  # kuery, eql, lucene, esql
    rule_type: str  # query, eql, threshold, machine_learning, etc.
    severity: str
    risk_score: int
    enabled: bool
    tags: List[str]
    mitre_techniques: List[str]
    index_patterns: List[str]
    interval: str
    from_time: str
    filters: List[Dict]
    false_positives: List[str]
    created_by: Optional[str] = None
    created_at: Optional[str] = None
    updated_by: Optional[str] = None
    updated_at: Optional[str] = None
    version: Optional[int] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class KibanaSavedSearch:
    """Represents a Kibana saved search"""
    id: str
    title: str
    description: str
    query: Dict[str, Any]
    filters: List[Dict]
    columns: List[str]
    sort: List[List]
    index_pattern: str
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class WatcherAlert:
    """Represents an Elastic Watcher alert"""
    id: str
    name: str
    trigger: Dict[str, Any]
    input: Dict[str, Any]
    condition: Dict[str, Any]
    actions: Dict[str, Any]
    metadata: Dict[str, Any]
    active: bool
    created_at: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class ElasticRuleHarvester:
    """
    Production-ready Elastic rule harvester with real API implementation
    Supports Elasticsearch/Elastic Cloud/Elastic Security
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.session: Optional[aiohttp.ClientSession] = None
        self.base_url: Optional[str] = None
        self.headers: Dict[str, str] = {}

    async def configure(self, config: Dict[str, Any]) -> bool:
        """
        Configure connection to Elastic instance

        Args:
            config: {
                'url': 'https://your-elastic-instance:9200',
                'api_key': 'base64_encoded_key',  # OR
                'username': 'elastic',
                'password': 'password',
                'cloud_id': 'deployment:base64string',  # For Elastic Cloud
                'verify_ssl': True/False
            }
        """
        try:
            # Handle Elastic Cloud ID
            if config.get('cloud_id'):
                self.base_url = self._decode_cloud_id(config['cloud_id'])
            else:
                self.base_url = config.get('url', 'http://localhost:9200')

            # Remove trailing slash
            self.base_url = self.base_url.rstrip('/')

            # Setup authentication
            if config.get('api_key'):
                # API Key authentication (recommended)
                self.headers['Authorization'] = f"ApiKey {config['api_key']}"
            elif config.get('username') and config.get('password'):
                # Basic authentication
                auth_string = f"{config['username']}:{config['password']}"
                auth_bytes = auth_string.encode('utf-8')
                auth_b64 = base64.b64encode(auth_bytes).decode('utf-8')
                self.headers['Authorization'] = f"Basic {auth_b64}"
            else:
                self.logger.error("No authentication provided (api_key or username/password required)")
                return False

            self.headers['Content-Type'] = 'application/json'
            self.headers['kbn-xsrf'] = 'true'  # Required for Kibana API

            # Create session with SSL settings
            connector = aiohttp.TCPConnector(
                ssl=config.get('verify_ssl', False)
            )
            self.session = aiohttp.ClientSession(
                connector=connector,
                headers=self.headers
            )

            # Test connection
            await self._test_connection()

            self.logger.info(f"Successfully configured Elastic connection to {self.base_url}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to configure Elastic connection: {e}")
            return False

    def _decode_cloud_id(self, cloud_id: str) -> str:
        """Decode Elastic Cloud ID to get the Elasticsearch URL"""
        try:
            parts = cloud_id.split(':')
            if len(parts) >= 2:
                decoded = base64.b64decode(parts[1]).decode('utf-8')
                # Format: es_host$kibana_host or es_host$kibana_host$apm_host
                es_host = decoded.split('$')[0]
                return f"https://{es_host}"
        except Exception as e:
            self.logger.error(f"Failed to decode cloud ID: {e}")
            raise ValueError(f"Invalid cloud_id format: {cloud_id}")

    async def _test_connection(self) -> bool:
        """Test connection to Elastic instance"""
        try:
            async with self.session.get(f"{self.base_url}/") as response:
                if response.status == 200:
                    data = await response.json()
                    version = data.get('version', {}).get('number', 'unknown')
                    self.logger.info(f"Connected to Elasticsearch version {version}")
                    return True
                else:
                    self.logger.error(f"Connection test failed: {response.status}")
                    return False
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False

    async def harvest_detection_rules(self) -> List[ElasticRule]:
        """
        Harvest detection rules from Elastic Security
        Uses the Detection Engine Rules API
        """
        rules = []

        try:
            # Elastic Security Detection Engine API endpoint
            url = f"{self.base_url}/api/detection_engine/rules/_find"

            # Pagination parameters
            page = 1
            per_page = 100
            total_harvested = 0

            while True:
                params = {
                    'page': page,
                    'per_page': per_page,
                    'sort_field': 'updated_at',
                    'sort_order': 'desc'
                }

                self.logger.info(f"Fetching detection rules page {page}...")

                async with self.session.get(url, params=params) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        self.logger.error(f"Failed to fetch rules: {response.status} - {error_text}")
                        break

                    data = await response.json()

                    # Extract rules from response
                    rule_data = data.get('data', [])
                    total = data.get('total', 0)

                    if not rule_data:
                        break

                    for rule_obj in rule_data:
                        try:
                            # Extract MITRE techniques
                            mitre_techniques = []
                            threat_info = rule_obj.get('threat', [])
                            for threat in threat_info:
                                techniques = threat.get('technique', [])
                                for technique in techniques:
                                    tech_id = technique.get('id', '')
                                    if tech_id:
                                        mitre_techniques.append(tech_id)
                                    # Also get subtechniques
                                    subtechniques = technique.get('subtechnique', [])
                                    for sub in subtechniques:
                                        sub_id = sub.get('id', '')
                                        if sub_id:
                                            mitre_techniques.append(sub_id)

                            rule = ElasticRule(
                                rule_id=rule_obj.get('rule_id', rule_obj.get('id', '')),
                                name=rule_obj.get('name', ''),
                                description=rule_obj.get('description', ''),
                                query=rule_obj.get('query', ''),
                                language=rule_obj.get('language', 'kuery'),
                                rule_type=rule_obj.get('type', 'query'),
                                severity=rule_obj.get('severity', 'medium'),
                                risk_score=rule_obj.get('risk_score', 50),
                                enabled=rule_obj.get('enabled', False),
                                tags=rule_obj.get('tags', []),
                                mitre_techniques=mitre_techniques,
                                index_patterns=rule_obj.get('index', []),
                                interval=rule_obj.get('interval', '5m'),
                                from_time=rule_obj.get('from', 'now-6m'),
                                filters=rule_obj.get('filters', []),
                                false_positives=rule_obj.get('false_positives', []),
                                created_by=rule_obj.get('created_by'),
                                created_at=rule_obj.get('created_at'),
                                updated_by=rule_obj.get('updated_by'),
                                updated_at=rule_obj.get('updated_at'),
                                version=rule_obj.get('version')
                            )

                            rules.append(rule)
                            total_harvested += 1

                        except Exception as e:
                            self.logger.error(f"Error parsing rule: {e}")
                            continue

                    self.logger.info(f"Harvested {len(rule_data)} rules from page {page} (total: {total_harvested}/{total})")

                    # Check if we have more pages
                    if total_harvested >= total:
                        break

                    page += 1

            self.logger.info(f"Successfully harvested {len(rules)} detection rules")
            return rules

        except Exception as e:
            self.logger.error(f"Error harvesting detection rules: {e}")
            return rules

    async def harvest_saved_searches(self) -> List[KibanaSavedSearch]:
        """
        Harvest saved searches from Kibana
        Uses the Saved Objects API
        """
        searches = []

        try:
            # Kibana Saved Objects API endpoint
            url = f"{self.base_url}/api/saved_objects/_find"

            params = {
                'type': 'search',
                'per_page': 1000,
                'sort_field': 'updated_at'
            }

            self.logger.info("Fetching saved searches from Kibana...")

            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    error_text = await response.text()
                    self.logger.error(f"Failed to fetch saved searches: {response.status} - {error_text}")
                    return searches

                data = await response.json()
                saved_objects = data.get('saved_objects', [])

                for obj in saved_objects:
                    try:
                        attributes = obj.get('attributes', {})

                        # Parse the search source JSON
                        search_source_json = attributes.get('kibanaSavedObjectMeta', {}).get('searchSourceJSON', '{}')
                        search_source = json.loads(search_source_json)

                        search = KibanaSavedSearch(
                            id=obj.get('id', ''),
                            title=attributes.get('title', ''),
                            description=attributes.get('description', ''),
                            query=search_source.get('query', {}),
                            filters=search_source.get('filter', []),
                            columns=attributes.get('columns', []),
                            sort=attributes.get('sort', []),
                            index_pattern=search_source.get('index', ''),
                            created_at=obj.get('created_at'),
                            updated_at=obj.get('updated_at')
                        )

                        searches.append(search)

                    except Exception as e:
                        self.logger.error(f"Error parsing saved search: {e}")
                        continue

                self.logger.info(f"Successfully harvested {len(searches)} saved searches")
                return searches

        except Exception as e:
            self.logger.error(f"Error harvesting saved searches: {e}")
            return searches

    async def harvest_watcher_alerts(self) -> List[WatcherAlert]:
        """
        Harvest Watcher alerts (legacy alerting system)
        Uses the Watcher API
        """
        alerts = []

        try:
            # Watcher API endpoint
            url = f"{self.base_url}/_watcher/watch"

            self.logger.info("Fetching Watcher alerts...")

            async with self.session.get(url) as response:
                if response.status == 404:
                    self.logger.info("Watcher not available (may not be installed or using new Alerting API)")
                    return alerts

                if response.status != 200:
                    error_text = await response.text()
                    self.logger.warning(f"Failed to fetch Watcher alerts: {response.status} - {error_text}")
                    return alerts

                data = await response.json()
                watches = data.get('watches', [])

                for watch in watches:
                    try:
                        watch_id = watch.get('_id', '')

                        # Get detailed watch information
                        detail_url = f"{self.base_url}/_watcher/watch/{watch_id}"
                        async with self.session.get(detail_url) as detail_response:
                            if detail_response.status == 200:
                                watch_detail = await detail_response.json()
                                watch_data = watch_detail.get('watch', {})

                                alert = WatcherAlert(
                                    id=watch_id,
                                    name=watch_data.get('metadata', {}).get('name', watch_id),
                                    trigger=watch_data.get('trigger', {}),
                                    input=watch_data.get('input', {}),
                                    condition=watch_data.get('condition', {}),
                                    actions=watch_data.get('actions', {}),
                                    metadata=watch_data.get('metadata', {}),
                                    active=watch.get('status', {}).get('state', {}).get('active', False),
                                    created_at=watch.get('status', {}).get('last_checked')
                                )

                                alerts.append(alert)

                    except Exception as e:
                        self.logger.error(f"Error parsing watcher alert: {e}")
                        continue

                self.logger.info(f"Successfully harvested {len(alerts)} Watcher alerts")
                return alerts

        except Exception as e:
            self.logger.error(f"Error harvesting Watcher alerts: {e}")
            return alerts

    async def harvest_all(self) -> Dict[str, Any]:
        """
        Harvest all available artifacts from Elastic

        Returns:
            Dictionary with all harvested artifacts
        """
        self.logger.info("Starting comprehensive harvest from Elastic instance...")

        results = {
            'detection_rules': [],
            'saved_searches': [],
            'watcher_alerts': [],
            'harvest_time': datetime.utcnow().isoformat(),
            'success': False
        }

        try:
            # Harvest detection rules
            results['detection_rules'] = await self.harvest_detection_rules()

            # Harvest saved searches
            results['saved_searches'] = await self.harvest_saved_searches()

            # Harvest watcher alerts
            results['watcher_alerts'] = await self.harvest_watcher_alerts()

            results['success'] = True

            total_artifacts = (
                len(results['detection_rules']) +
                len(results['saved_searches']) +
                len(results['watcher_alerts'])
            )

            self.logger.info(f"Harvest complete! Total artifacts: {total_artifacts}")

        except Exception as e:
            self.logger.error(f"Error during comprehensive harvest: {e}")

        return results

    async def close(self):
        """Close the HTTP session"""
        if self.session:
            await self.session.close()


async def main():
    """Example usage and testing"""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # Example configuration
    config = {
        'url': 'https://your-elastic-instance:9200',
        'api_key': 'your_base64_api_key',  # OR use username/password
        # 'username': 'elastic',
        # 'password': 'your_password',
        # 'cloud_id': 'deployment:base64string',  # For Elastic Cloud
        'verify_ssl': False
    }

    harvester = ElasticRuleHarvester(logger)

    try:
        # Configure connection
        if await harvester.configure(config):
            # Harvest all artifacts
            results = await harvester.harvest_all()

            print("\n" + "="*60)
            print("HARVEST RESULTS")
            print("="*60)
            print(f"Detection Rules: {len(results['detection_rules'])}")
            print(f"Saved Searches: {len(results['saved_searches'])}")
            print(f"Watcher Alerts: {len(results['watcher_alerts'])}")
            print(f"Harvest Time: {results['harvest_time']}")
            print("="*60 + "\n")

            # Display first detection rule as example
            if results['detection_rules']:
                rule = results['detection_rules'][0]
                print("Example Detection Rule:")
                print(f"  Name: {rule.name}")
                print(f"  Severity: {rule.severity}")
                print(f"  MITRE Techniques: {rule.mitre_techniques}")
                print(f"  Query: {rule.query[:100]}...")

    finally:
        await harvester.close()


if __name__ == "__main__":
    asyncio.run(main())
