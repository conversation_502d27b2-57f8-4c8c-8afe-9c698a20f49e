"""
Pattern Evolution and Versioning System for SIEMLess v2.0
Handles pattern lifecycle, versioning, and continuous improvement
"""

import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from uuid import uuid4
import semantic_version
import logging
from enum import Enum
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

class EvolutionReason(Enum):
    """Reasons for pattern evolution"""
    PERFORMANCE_OPTIMIZATION = "performance_optimization"
    FALSE_POSITIVE_REDUCTION = "false_positive_reduction"
    NEW_VARIANT_COVERAGE = "new_variant_coverage"
    BUG_FIX = "bug_fix"
    FEATURE_ENHANCEMENT = "feature_enhancement"
    SECURITY_UPDATE = "security_update"
    DEPRECATION = "deprecation"
    COMMUNITY_FEEDBACK = "community_feedback"
    AI_RECOMMENDATION = "ai_recommendation"

@dataclass
class EvolutionMetrics:
    """Metrics for pattern evolution decisions"""
    usage_count: int
    success_rate: float
    false_positive_rate: float
    avg_execution_time_ms: float
    last_used: datetime
    feedback_score: float
    evolution_priority: float = 0.0


class PatternEvolution:
    """Manages pattern evolution and versioning"""

    def __init__(self, storage, intelligence_engine=None, pattern_tester=None):
        self.storage = storage
        self.intelligence_engine = intelligence_engine
        self.pattern_tester = pattern_tester
        self.evolution_queue = asyncio.Queue()

    async def evolve_pattern(self, pattern_id: str, reason: EvolutionReason,
                            changes: Dict = None) -> Dict[str, Any]:
        """Evolve a pattern to a new version"""
        # Get current pattern
        current_pattern = await self.storage.get_pattern(pattern_id)
        if not current_pattern:
            raise ValueError(f"Pattern {pattern_id} not found")

        # Generate new version number
        current_version = semantic_version.Version(current_pattern['pattern_version'])
        new_version = self._calculate_new_version(current_version, reason)

        # Apply changes
        evolved_pattern = await self._apply_evolution(
            current_pattern, changes, reason
        )

        # Test evolved pattern
        if self.pattern_tester:
            test_result = await self.pattern_tester.test_pattern(evolved_pattern)
            if test_result['summary']['overall_status'] != 'passed':
                logger.warning(f"Evolution failed testing for pattern {pattern_id}")
                return {
                    'success': False,
                    'reason': 'Failed testing',
                    'test_result': test_result
                }

        # Create version record
        version_record = {
            'pattern_id': pattern_id,
            'old_version': str(current_version),
            'new_version': str(new_version),
            'reason': reason.value,
            'changes': changes,
            'evolved_at': datetime.utcnow().isoformat(),
            'test_result': test_result if self.pattern_tester else None
        }

        # Update pattern with new version
        evolved_pattern['pattern_version'] = str(new_version)
        evolved_pattern['metadata']['evolution_history'] = (
            evolved_pattern.get('metadata', {}).get('evolution_history', []) +
            [version_record]
        )

        # Store evolved pattern
        await self.storage.update_pattern_version(
            pattern_id, str(new_version), changes,
            f"Evolution: {reason.value}"
        )

        logger.info(f"Pattern {pattern_id} evolved from {current_version} to {new_version}")

        return {
            'success': True,
            'pattern_id': pattern_id,
            'old_version': str(current_version),
            'new_version': str(new_version),
            'reason': reason.value,
            'test_result': test_result if self.pattern_tester else None
        }

    async def analyze_evolution_candidates(self) -> List[Dict]:
        """Identify patterns that need evolution"""
        candidates = []

        # Get all active patterns
        patterns = await self.storage.search_patterns({'status': 'active'})

        for pattern in patterns:
            metrics = await self._get_evolution_metrics(pattern['pattern_id'])

            # Check evolution criteria
            evolution_needed, reasons = self._check_evolution_criteria(metrics)

            if evolution_needed:
                candidates.append({
                    'pattern_id': pattern['pattern_id'],
                    'pattern_name': pattern['pattern_name'],
                    'current_version': pattern['pattern_version'],
                    'metrics': asdict(metrics),
                    'reasons': reasons,
                    'priority': metrics.evolution_priority
                })

        # Sort by priority
        candidates.sort(key=lambda x: x['priority'], reverse=True)

        return candidates

    async def auto_evolve_patterns(self, max_patterns: int = 10):
        """Automatically evolve patterns based on metrics"""
        candidates = await self.analyze_evolution_candidates()

        evolved = []
        for candidate in candidates[:max_patterns]:
            try:
                # Use AI to suggest improvements
                if self.intelligence_engine:
                    suggestions = await self._get_ai_evolution_suggestions(
                        candidate['pattern_id']
                    )
                else:
                    suggestions = None

                # Apply evolution
                for reason in candidate['reasons']:
                    result = await self.evolve_pattern(
                        candidate['pattern_id'],
                        reason,
                        suggestions
                    )
                    if result['success']:
                        evolved.append(result)

            except Exception as e:
                logger.error(f"Failed to evolve pattern {candidate['pattern_id']}: {e}")

        return evolved

    async def _get_evolution_metrics(self, pattern_id: str) -> EvolutionMetrics:
        """Get metrics for evolution decisions"""
        analytics = await self.storage.get_pattern_analytics(pattern_id)

        usage_stats = analytics.get('usage', {})

        return EvolutionMetrics(
            usage_count=usage_stats.get('total_uses', 0),
            success_rate=usage_stats.get('successful_uses', 0) / max(usage_stats.get('total_uses', 1), 1),
            false_positive_rate=await self._calculate_false_positive_rate(pattern_id),
            avg_execution_time_ms=usage_stats.get('avg_execution_time', 0),
            last_used=usage_stats.get('last_used', datetime.utcnow() - timedelta(days=365)),
            feedback_score=await self._get_feedback_score(pattern_id)
        )

    def _check_evolution_criteria(self, metrics: EvolutionMetrics) -> Tuple[bool, List[EvolutionReason]]:
        """Check if pattern needs evolution"""
        evolution_needed = False
        reasons = []

        # Performance criteria
        if metrics.avg_execution_time_ms > 100:
            evolution_needed = True
            reasons.append(EvolutionReason.PERFORMANCE_OPTIMIZATION)
            metrics.evolution_priority += 0.3

        # False positive criteria
        if metrics.false_positive_rate > 0.05:
            evolution_needed = True
            reasons.append(EvolutionReason.FALSE_POSITIVE_REDUCTION)
            metrics.evolution_priority += 0.4

        # Success rate criteria
        if metrics.success_rate < 0.95 and metrics.usage_count > 100:
            evolution_needed = True
            reasons.append(EvolutionReason.BUG_FIX)
            metrics.evolution_priority += 0.5

        # Feedback criteria
        if metrics.feedback_score < 3.0:  # Out of 5
            evolution_needed = True
            reasons.append(EvolutionReason.COMMUNITY_FEEDBACK)
            metrics.evolution_priority += 0.2

        # Age criteria (patterns not updated in 90 days)
        if (datetime.utcnow() - metrics.last_used).days > 90:
            evolution_needed = True
            reasons.append(EvolutionReason.FEATURE_ENHANCEMENT)
            metrics.evolution_priority += 0.1

        return evolution_needed, reasons

    def _calculate_new_version(self, current: semantic_version.Version,
                              reason: EvolutionReason) -> semantic_version.Version:
        """Calculate new version based on evolution reason"""
        if reason in [EvolutionReason.SECURITY_UPDATE, EvolutionReason.BUG_FIX]:
            # Patch version for fixes
            return current.next_patch()
        elif reason in [EvolutionReason.FEATURE_ENHANCEMENT, EvolutionReason.NEW_VARIANT_COVERAGE]:
            # Minor version for features
            return current.next_minor()
        elif reason == EvolutionReason.DEPRECATION:
            # Major version for breaking changes
            return current.next_major()
        else:
            # Default to patch
            return current.next_patch()

    async def _apply_evolution(self, pattern: Dict, changes: Dict,
                              reason: EvolutionReason) -> Dict:
        """Apply evolution changes to pattern"""
        evolved = pattern.copy()

        if reason == EvolutionReason.PERFORMANCE_OPTIMIZATION:
            evolved = await self._optimize_performance(evolved)

        elif reason == EvolutionReason.FALSE_POSITIVE_REDUCTION:
            evolved = await self._reduce_false_positives(evolved)

        elif reason == EvolutionReason.NEW_VARIANT_COVERAGE:
            evolved = await self._expand_coverage(evolved)

        elif reason == EvolutionReason.BUG_FIX:
            evolved = await self._fix_bugs(evolved, changes)

        elif reason == EvolutionReason.SECURITY_UPDATE:
            evolved = await self._apply_security_updates(evolved)

        # Apply manual changes if provided
        if changes:
            evolved = self._merge_changes(evolved, changes)

        # Update metadata
        evolved['metadata']['last_evolved'] = datetime.utcnow().isoformat()
        evolved['metadata']['evolution_reason'] = reason.value

        return evolved

    async def _optimize_performance(self, pattern: Dict) -> Dict:
        """Optimize pattern for better performance"""
        pattern_data = pattern.get('pattern_data', {})

        # Simplify regex patterns
        if 'regex' in pattern_data:
            pattern_data['regex'] = self._optimize_regex(pattern_data['regex'])

        # Add caching hints
        pattern_data['cache_enabled'] = True
        pattern_data['cache_ttl'] = 300  # 5 minutes

        # Optimize field access
        if 'fields' in pattern_data:
            pattern_data['fields'] = self._optimize_field_access(pattern_data['fields'])

        pattern['pattern_data'] = pattern_data
        return pattern

    async def _reduce_false_positives(self, pattern: Dict) -> Dict:
        """Reduce pattern false positives"""
        pattern_data = pattern.get('pattern_data', {})

        # Add additional filtering conditions
        if 'filters' not in pattern_data:
            pattern_data['filters'] = []

        # Add context-based filtering
        pattern_data['filters'].append({
            'type': 'context',
            'min_confidence': 0.8
        })

        # Increase threshold values
        if 'threshold' in pattern_data:
            pattern_data['threshold'] *= 1.2  # Increase by 20%

        pattern['pattern_data'] = pattern_data
        return pattern

    async def _expand_coverage(self, pattern: Dict) -> Dict:
        """Expand pattern coverage for new variants"""
        pattern_data = pattern.get('pattern_data', {})

        # Add variant patterns
        if 'variants' not in pattern_data:
            pattern_data['variants'] = []

        # Use AI to suggest variants if available
        if self.intelligence_engine:
            variants = await self.intelligence_engine.suggest_variants(pattern)
            pattern_data['variants'].extend(variants)

        pattern['pattern_data'] = pattern_data
        return pattern

    async def _fix_bugs(self, pattern: Dict, bug_fixes: Dict) -> Dict:
        """Apply bug fixes to pattern"""
        if bug_fixes:
            # Apply specific bug fixes
            for path, fix in bug_fixes.items():
                pattern = self._apply_fix_at_path(pattern, path, fix)

        return pattern

    async def _apply_security_updates(self, pattern: Dict) -> Dict:
        """Apply security updates to pattern"""
        pattern_data = pattern.get('pattern_data', {})

        # Add input validation
        pattern_data['input_validation'] = True

        # Add resource limits
        pattern_data['resource_limits'] = {
            'max_execution_time_ms': 5000,
            'max_memory_mb': 100
        }

        # Remove dangerous operations
        pattern_data = self._sanitize_pattern_data(pattern_data)

        pattern['pattern_data'] = pattern_data
        return pattern

    async def _get_ai_evolution_suggestions(self, pattern_id: str) -> Dict:
        """Get AI suggestions for pattern evolution"""
        if not self.intelligence_engine:
            return {}

        pattern = await self.storage.get_pattern(pattern_id)
        analytics = await self.storage.get_pattern_analytics(pattern_id)

        # Ask AI for suggestions
        suggestions = await self.intelligence_engine.analyze_pattern(
            pattern=pattern,
            analytics=analytics,
            request_type='evolution_suggestions'
        )

        return suggestions

    async def _calculate_false_positive_rate(self, pattern_id: str) -> float:
        """Calculate false positive rate from usage data"""
        # Simplified calculation - would be more complex in production
        analytics = await self.storage.get_pattern_analytics(pattern_id)
        usage = analytics.get('usage', {})

        total = usage.get('total_uses', 0)
        successful = usage.get('successful_uses', 0)

        if total == 0:
            return 0.0

        # Assume failures are false positives (simplified)
        false_positives = total - successful
        return false_positives / total

    async def _get_feedback_score(self, pattern_id: str) -> float:
        """Get community feedback score"""
        # TODO: Implement community feedback system
        # For now, return a default score
        return 4.0

    def _optimize_regex(self, regex: str) -> str:
        """Optimize regex pattern for performance"""
        # Simplified optimization
        # In production, would use regex optimization libraries
        optimized = regex

        # Remove unnecessary capturing groups
        optimized = optimized.replace('(', '(?:')

        # Use atomic groups where possible
        # optimized = add_atomic_groups(optimized)

        return optimized

    def _optimize_field_access(self, fields: List[str]) -> List[str]:
        """Optimize field access patterns"""
        # Remove duplicates
        fields = list(set(fields))

        # Sort by access frequency (if known)
        # fields = sort_by_frequency(fields)

        return fields

    def _merge_changes(self, pattern: Dict, changes: Dict) -> Dict:
        """Merge manual changes into pattern"""
        import copy

        result = copy.deepcopy(pattern)

        for key, value in changes.items():
            if isinstance(value, dict) and key in result and isinstance(result[key], dict):
                # Recursive merge for nested dicts
                result[key] = self._merge_changes(result[key], value)
            else:
                # Direct replacement
                result[key] = value

        return result

    def _apply_fix_at_path(self, pattern: Dict, path: str, fix: Any) -> Dict:
        """Apply fix at specific path in pattern"""
        import copy

        result = copy.deepcopy(pattern)
        keys = path.split('.')

        # Navigate to the target location
        target = result
        for key in keys[:-1]:
            if key not in target:
                target[key] = {}
            target = target[key]

        # Apply fix
        target[keys[-1]] = fix

        return result

    def _sanitize_pattern_data(self, pattern_data: Dict) -> Dict:
        """Remove dangerous operations from pattern"""
        dangerous_keys = ['eval', 'exec', 'compile', '__import__']

        def sanitize_dict(d):
            sanitized = {}
            for key, value in d.items():
                if key not in dangerous_keys:
                    if isinstance(value, dict):
                        sanitized[key] = sanitize_dict(value)
                    elif isinstance(value, str):
                        # Check for dangerous content in strings
                        if not any(danger in value for danger in dangerous_keys):
                            sanitized[key] = value
                    else:
                        sanitized[key] = value
            return sanitized

        return sanitize_dict(pattern_data)


class PatternLifecycle:
    """Manages complete pattern lifecycle"""

    def __init__(self, storage, evolution, tester):
        self.storage = storage
        self.evolution = evolution
        self.tester = tester
        self.lifecycle_states = [
            'draft', 'testing', 'candidate', 'active',
            'optimizing', 'deprecated', 'archived'
        ]

    async def transition_pattern(self, pattern_id: str, new_state: str) -> Dict:
        """Transition pattern to new lifecycle state"""
        if new_state not in self.lifecycle_states:
            raise ValueError(f"Invalid state: {new_state}")

        pattern = await self.storage.get_pattern(pattern_id)
        if not pattern:
            raise ValueError(f"Pattern {pattern_id} not found")

        current_state = pattern.get('status', 'draft')

        # Validate transition
        if not self._is_valid_transition(current_state, new_state):
            raise ValueError(f"Invalid transition from {current_state} to {new_state}")

        # Perform state-specific actions
        if new_state == 'testing':
            # Run tests before moving to testing
            test_result = await self.tester.test_pattern(pattern)
            if test_result['summary']['overall_status'] != 'passed':
                return {
                    'success': False,
                    'reason': 'Failed testing',
                    'test_result': test_result
                }

        elif new_state == 'active':
            # Ensure pattern passed testing
            if current_state != 'candidate':
                return {
                    'success': False,
                    'reason': 'Pattern must be in candidate state before activation'
                }

        elif new_state == 'deprecated':
            # Mark as deprecated
            pattern['metadata']['deprecated_at'] = datetime.utcnow().isoformat()

        elif new_state == 'archived':
            # Archive pattern
            await self.storage.archive_pattern(pattern_id, 'Lifecycle transition')

        # Update pattern state
        pattern['status'] = new_state
        await self.storage.store_pattern(pattern)

        logger.info(f"Pattern {pattern_id} transitioned from {current_state} to {new_state}")

        return {
            'success': True,
            'pattern_id': pattern_id,
            'old_state': current_state,
            'new_state': new_state
        }

    def _is_valid_transition(self, current: str, new: str) -> bool:
        """Check if state transition is valid"""
        valid_transitions = {
            'draft': ['testing', 'archived'],
            'testing': ['candidate', 'draft', 'archived'],
            'candidate': ['active', 'testing', 'archived'],
            'active': ['optimizing', 'deprecated', 'archived'],
            'optimizing': ['active', 'deprecated'],
            'deprecated': ['archived'],
            'archived': []  # No transitions from archived
        }

        return new in valid_transitions.get(current, [])

    async def get_lifecycle_report(self) -> Dict:
        """Get report on pattern lifecycle states"""
        report = {
            'by_state': {},
            'transitions_today': 0,
            'pending_reviews': []
        }

        for state in self.lifecycle_states:
            patterns = await self.storage.search_patterns({'status': state})
            report['by_state'][state] = len(patterns)

            # Find patterns needing review
            if state == 'candidate':
                for pattern in patterns:
                    created_at = datetime.fromisoformat(pattern['created_at'])
                    if (datetime.utcnow() - created_at).days > 7:
                        report['pending_reviews'].append({
                            'pattern_id': pattern['pattern_id'],
                            'pattern_name': pattern['pattern_name'],
                            'days_in_candidate': (datetime.utcnow() - created_at).days
                        })

        return report