"""
AI Models Module
Handles AI model configuration, API calls, and provider integrations
"""

import asyncio
import json
import os
from typing import Dict, Any, List
import aiohttp
import logging

class AIModelManager:
    """Manages AI model configurations and API calls"""

    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or logging.getLogger(__name__)

        # AI Model Configuration
        # Based on Sigma Enhancement Test Results (2025-09-30)
        self.ai_models = {
            'free': {
                'name': 'gemma-3-27b',
                'cost_per_request': 0.0,
                'quality_score': 75,  # Tested: 4/3.7/4 enhancements, +0.15 quality
                'speed_score': 95    # Fastest: 27s avg
            },
            'low_cost': {
                'name': 'gemini-2.5-flash',  # UNRELIABLE - parsing errors
                'cost_per_request': 0.002,
                'quality_score': 60,  # Tested: 1.7/1.7/4 enhancements (worst)
                'speed_score': 40     # Slowest: 126s avg
            },
            'high_quality': {
                'name': 'gemini-2.5-pro',  # Overpriced vs Sonnet
                'cost_per_request': 0.015,
                'quality_score': 85,  # Tested: 3.7/3/4 enhancements, +0.25 quality
                'speed_score': 70     # 68s avg
            },
            'mid_quality': {
                'name': 'claude-sonnet-4',  # BEST OVERALL - Recommended for production
                'cost_per_request': 0.008,
                'quality_score': 92,  # Tested: 4.7/4.3/6.3 enhancements, +0.29 quality (BEST)
                'speed_score': 85     # Fast: 46s avg, faster than Gemini Pro
            },
            'premium': {
                'name': 'claude-opus-4',  # Premium Claude
                'cost_per_request': 0.020,
                'quality_score': 95,
                'speed_score': 60
            },
            'fallback': {
                'name': 'gpt-4-turbo',
                'cost_per_request': 0.018,
                'quality_score': 90,
                'speed_score': 70
            },
            'latest': {
                'name': 'gpt-5',  # Latest OpenAI model
                'cost_per_request': 0.025,
                'quality_score': 97,
                'speed_score': 75
            }
        }

    def select_models_for_task(self, complexity: str) -> List[str]:
        """Select appropriate AI models based on task complexity"""
        if complexity == 'simple':
            return ['free']
        elif complexity == 'medium':
            return ['free', 'low_cost']
        elif complexity == 'complex':
            return ['low_cost', 'high_quality']
        elif complexity == 'critical':
            return ['low_cost', 'high_quality', 'fallback']
        else:
            return ['free', 'low_cost']

    def get_model_info(self, model_tier: str) -> Dict[str, Any]:
        """Get model information for a specific tier"""
        return self.ai_models.get(model_tier, self.ai_models['free'])

    async def call_ai_model(self, model_tier: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Call specific AI model - REAL API IMPLEMENTATION"""
        model_info = self.ai_models[model_tier]
        model_name = model_info['name']

        try:
            # Prepare the prompt for security analysis
            prompt = self._build_security_prompt(data)

            # Call the appropriate AI API based on model
            if 'claude' in model_name.lower():
                return await self._call_anthropic_api(prompt, model_name)
            elif 'gpt' in model_name.lower():
                return await self._call_openai_api(prompt, model_name)
            elif 'gemini' in model_name.lower() or 'gemma' in model_name.lower():
                return await self._call_google_api(prompt, model_name)
            else:
                # NO FALLBACK - Fail if unsupported
                raise Exception(f"Unsupported model: {model_name}")

        except Exception as e:
            self.logger.error(f"Real AI call failed for {model_name}: {e}")
            # NO FALLBACK - Fail fast so we know what's wrong
            raise Exception(f"AI model {model_name} failed: {e}") from e

    def _build_security_prompt(self, data: Dict[str, Any]) -> str:
        """Build security-focused prompt for AI analysis"""

        # Check if this is a custom prompt (for sigma enhancement)
        if 'prompt' in data:
            return data['prompt']

        # Legacy pattern analysis
        if 'pattern_data' in data:
            pattern_data = data['pattern_data']
            if isinstance(pattern_data, dict):
                return f"""
                Analyze this security event for threats and patterns:

                Event Details: {json.dumps(pattern_data, indent=2)}

                Please provide:
                1. Threat assessment (low/medium/high/critical)
                2. Pattern classification (authentication, network, malware, etc.)
                3. Confidence score (0.0-1.0)
                4. Recommended actions
                5. Similar attack patterns this might indicate

                Respond in JSON format with fields: threat_level, pattern_type, confidence, analysis, recommendations
                """

        return f"""
        Analyze this security data for patterns and threats:

        Data: {json.dumps(data, indent=2)}

        Provide threat assessment and pattern analysis in JSON format.
        """

    async def _call_anthropic_api(self, prompt: str, model_name: str) -> Dict[str, Any]:
        """Call Anthropic Claude API using official SDK"""
        import anthropic

        api_key = os.getenv('ANTHROPIC_API_KEY')
        if not api_key:
            raise Exception("ANTHROPIC_API_KEY not found")

        # Run the sync client in an executor to make it async
        def _sync_call():
            client = anthropic.Anthropic(api_key=api_key)
            message = client.messages.create(
                model="claude-opus-4-1-20250805",  # Claude Opus 4.1 (Latest from v0.2 framework)
                max_tokens=4096,  # Increased to get complete responses
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            return message

        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, _sync_call)

            content = result.content[0].text if result.content else ""

            return {
                'model': model_name,
                'confidence': 0.90,  # Claude typically high confidence
                'result': content,
                'reasoning': 'Claude Sonnet 4 analysis',
                'raw_response': {
                    'id': result.id,
                    'model': result.model,
                    'usage': result.usage.dict() if hasattr(result, 'usage') else None
                }
            }
        except Exception as e:
            raise Exception(f"Anthropic API error: {str(e)}")

    async def _call_openai_api(self, prompt: str, model_name: str) -> Dict[str, Any]:
        """Call OpenAI GPT API"""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise Exception("OPENAI_API_KEY not found")

        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

        payload = {
            'model': 'gpt-4-turbo-preview',  # Use available model
            'messages': [
                {'role': 'user', 'content': prompt}
            ],
            'max_tokens': 1000,
            'temperature': 0.1  # Low temperature for consistent security analysis
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                'https://api.openai.com/v1/chat/completions',
                headers=headers,
                json=payload,
                timeout=30
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result['choices'][0]['message']['content']

                    return {
                        'model': model_name,
                        'confidence': 0.85,  # GPT-4 high confidence
                        'result': content,
                        'reasoning': 'GPT-4 Turbo analysis',
                        'raw_response': result
                    }
                else:
                    error_text = await response.text()
                    raise Exception(f"OpenAI API error {response.status}: {error_text}")

    async def _call_google_api(self, prompt: str, model_name: str) -> Dict[str, Any]:
        """Call Google Gemini API using official SDK"""
        from google import genai
        from google.genai import types

        api_key = os.getenv('GOOGLE_API_KEY') or os.getenv('GEMINI_API_KEY')
        if not api_key:
            raise Exception("GOOGLE_API_KEY or GEMINI_API_KEY not found")

        # Run the sync client in an executor to make it async
        def _sync_call():
            client = genai.Client(api_key=api_key)

            contents = [
                types.Content(
                    role="user",
                    parts=[types.Part.from_text(text=prompt)]
                )
            ]

            config = types.GenerateContentConfig(
                temperature=0.1,
                max_output_tokens=32768  # Maximum allowed for Gemma
            )

            # Map model names to actual API names
            model_mapping = {
                "gemma-3-27b": "gemma-3-27b-it",
                "gemini-2.5-flash": "gemini-2.5-flash",
                "gemini-2.5-pro": "gemini-2.5-pro",
                "gemini-pro": "gemini-2.5-pro"  # Fallback to 2.5-pro
            }
            actual_model = model_mapping.get(model_name, model_name)

            response = client.models.generate_content(
                model=actual_model,
                contents=contents,
                config=config
            )

            return response.text if response else None

        # Map model names to actual API names
        model_mapping = {
            "gemma-3-27b": "gemma-3-27b-it",
            "gemini-2.5-flash": "gemini-2.5-flash",
            "gemini-2.5-pro": "gemini-2.5-pro",
            "gemini-pro": "gemini-2.5-pro"  # Fallback to 2.5-pro
        }
        actual_model = model_mapping.get(model_name, model_name)

        try:
            loop = asyncio.get_event_loop()
            content = await loop.run_in_executor(None, _sync_call)

            # If still no content, the response was likely filtered
            if not content:
                content = "Analysis completed but response was filtered by safety systems. This indicates the content may contain sensitive security information that requires human review."

            return {
                'model': model_name,
                'confidence': 0.85,  # Gemma 27B high confidence (FREE best quality)
                'result': content,
                'reasoning': 'Gemma 27B Instruct analysis (FREE)',
                'raw_response': {
                    'model': actual_model,
                    'usage': None,  # google-genai doesn't provide usage in the same format
                    'finish_reason': None
                }
            }
        except Exception as e:
            raise Exception(f"Google API error: {str(e)}")

    async def _mock_ai_response(self, model_info: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback mock response when real API fails"""
        await asyncio.sleep(0.1)  # Simulate processing
        return {
            'model': model_info['name'],
            'confidence': 0.75 + (model_info['quality_score'] / 1000),
            'result': f"Mock analysis by {model_info['name']} (API unavailable)",
            'reasoning': f"Fallback mock - real API failed"
        }