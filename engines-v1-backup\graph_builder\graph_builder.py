"""
Graph Builder Engine - Constructs entity relationship graphs

Builds on v1's successful graph_manager.py but as a dedicated microservice.
Handles relationship creation, graph traversal, and pattern detection.
"""
import asyncio
import json
from typing import Dict, Any, List, Optional, Tuple, Set
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import hashlib
from collections import defaultdict
import networkx as nx

# Add v2 to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.base import BaseEngine


class GraphBuilderEngine(BaseEngine):
    """
    Graph Builder Engine: Constructs and analyzes entity relationship graphs
    
    Key responsibilities:
    - Build relationships from enriched entities
    - Detect graph patterns (kill chains, lateral movement)
    - Calculate graph metrics (centrality, clustering)
    - Identify anomalies in graph structure
    """
    
    def __init__(self):
        super().__init__('graph_builder', '2.0.0')
        
        # Graph storage (in production, this would be Neo4j or similar)
        self.graphs = {}  # session_id -> nx.DiGraph
        self.entity_index = defaultdict(set)  # entity_id -> set of session_ids
        
        # Relationship type definitions
        self.relationship_types = {
            # User relationships
            'logged_into': {'source': 'user', 'target': 'hostname', 'weight': 1.0},
            'executed': {'source': 'user', 'target': 'process', 'weight': 0.9},
            'accessed': {'source': 'user', 'target': 'file', 'weight': 0.8},
            'owns': {'source': 'user', 'target': 'device', 'weight': 0.7},
            
            # Process relationships
            'spawned': {'source': 'process', 'target': 'process', 'weight': 1.0},
            'created': {'source': 'process', 'target': 'file', 'weight': 0.9},
            'modified': {'source': 'process', 'target': 'file', 'weight': 0.8},
            'deleted': {'source': 'process', 'target': 'file', 'weight': 0.7},
            'loaded': {'source': 'process', 'target': 'dll', 'weight': 0.8},
            'injected_into': {'source': 'process', 'target': 'process', 'weight': 0.95},
            
            # Network relationships
            'connected_to': {'source': 'ip', 'target': 'ip', 'weight': 0.9},
            'communicated_with': {'source': 'hostname', 'target': 'hostname', 'weight': 0.85},
            'resolved_to': {'source': 'domain', 'target': 'ip', 'weight': 1.0},
            'has_ip': {'source': 'hostname', 'target': 'ip', 'weight': 1.0},
            
            # Detection relationships
            'generated_detection': {'source': 'hostname', 'target': 'detection_id', 'weight': 1.0},
            'detected_threat': {'source': 'detection_id', 'target': 'threat_name', 'weight': 1.0},
            'uses_technique': {'source': 'process', 'target': 'technique', 'weight': 0.9},
            'implements_tactic': {'source': 'process', 'target': 'tactic', 'weight': 0.85},
            
            # File relationships
            'located_at': {'source': 'file', 'target': 'path', 'weight': 1.0},
            'has_hash': {'source': 'file', 'target': 'hash', 'weight': 1.0},
            'dropped': {'source': 'process', 'target': 'file', 'weight': 0.9},
            
            # Temporal relationships
            'preceded': {'source': 'event', 'target': 'event', 'weight': 1.0},
            'followed_by': {'source': 'event', 'target': 'event', 'weight': 1.0},
            'concurrent_with': {'source': 'event', 'target': 'event', 'weight': 0.8}
        }
        
        # Graph patterns to detect
        self.attack_patterns = {
            'kill_chain': {
                'pattern': ['initial_access', 'execution', 'persistence', 'privilege_escalation'],
                'risk_multiplier': 2.5
            },
            'lateral_movement': {
                'pattern': ['credential_access', 'discovery', 'lateral_movement'],
                'risk_multiplier': 2.0
            },
            'data_exfiltration': {
                'pattern': ['collection', 'command_and_control', 'exfiltration'],
                'risk_multiplier': 2.2
            },
            'ransomware': {
                'pattern': ['execution', 'defense_evasion', 'impact'],
                'risk_multiplier': 3.0
            }
        }
        
        # Graph statistics
        self.graph_stats = {
            'total_graphs': 0,
            'total_nodes': 0,
            'total_edges': 0,
            'patterns_detected': 0,
            'anomalies_found': 0
        }
    
    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process enriched entities and build relationship graph
        """
        entities = message.get('data', {}).get('entities', [])
        relationships = message.get('data', {}).get('relationships', [])
        use_case_context = message.get('data', {}).get('use_case_context', {})
        session_id = message.get('session_id', self._generate_session_id())
        
        self.logger.start_operation(f"build_graph_{session_id}")
        
        try:
            # Create or get graph for this session
            graph = self._get_or_create_graph(session_id)
            
            # Add entities as nodes
            nodes_added = self._add_nodes(graph, entities)
            
            # Build relationships as edges
            edges_added = self._add_edges(graph, relationships, entities)
            
            # Detect implicit relationships
            implicit_edges = self._detect_implicit_relationships(graph, entities)
            
            # Calculate graph metrics
            metrics = self._calculate_graph_metrics(graph)
            
            # Detect attack patterns
            patterns = self._detect_attack_patterns(graph, use_case_context)
            
            # Find anomalies
            anomalies = self._detect_anomalies(graph, metrics)
            
            # Generate graph summary
            summary = self._generate_graph_summary(graph, metrics, patterns, anomalies)
            
            # Update statistics
            self.graph_stats['total_graphs'] = len(self.graphs)
            self.graph_stats['total_nodes'] += nodes_added
            self.graph_stats['total_edges'] += edges_added + implicit_edges
            self.graph_stats['patterns_detected'] += len(patterns)
            self.graph_stats['anomalies_found'] += len(anomalies)
            
            # Log graph construction
            self.logger.log_decision(
                'graph_built',
                {'entities': len(entities), 'relationships': len(relationships)},
                {
                    'nodes_added': nodes_added,
                    'edges_added': edges_added + implicit_edges,
                    'patterns': patterns,
                    'anomalies': anomalies,
                    'metrics': metrics
                },
                reasoning=f"Built graph with {nodes_added} nodes and {edges_added + implicit_edges} edges",
                confidence=0.95
            )
            
            result = {
                'success': True,
                'session_id': session_id,
                'graph_metrics': metrics,
                'patterns_detected': patterns,
                'anomalies': anomalies,
                'summary': summary,
                'next_engine': 'detection_engine',
                'data': {
                    'entities': entities,
                    'relationships': relationships + 
                                   [self._edge_to_relationship(e) for e in graph.edges(data=True)],
                    'graph_metrics': metrics,
                    'attack_patterns': patterns,
                    'risk_score': self._calculate_risk_score(patterns, anomalies, metrics),
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
            
            return result
        
        except Exception as e:
            self.logger.log_error(e, {'message': message})
            return {
                'success': False,
                'error': str(e)
            }
        finally:
            self.logger.end_operation(f"build_graph_{session_id}")
    
    def _generate_session_id(self) -> str:
        """Generate unique session ID"""
        return hashlib.md5(f"{datetime.utcnow().isoformat()}".encode()).hexdigest()[:16]
    
    def _get_or_create_graph(self, session_id: str) -> nx.DiGraph:
        """Get existing graph or create new one"""
        if session_id not in self.graphs:
            self.graphs[session_id] = nx.DiGraph(session_id=session_id)
        return self.graphs[session_id]
    
    def _add_nodes(self, graph: nx.DiGraph, entities: List[Dict]) -> int:
        """Add entities as nodes to the graph"""
        nodes_added = 0
        
        for entity in entities:
            node_id = self._get_node_id(entity)
            
            if not graph.has_node(node_id):
                graph.add_node(
                    node_id,
                    entity_type=entity.get('type'),
                    value=entity.get('value'),
                    confidence=entity.get('confidence', 0.8),
                    properties=entity.get('properties', {}),
                    first_seen=datetime.utcnow().isoformat(),
                    last_seen=datetime.utcnow().isoformat()
                )
                nodes_added += 1
                
                # Update entity index
                self.entity_index[node_id].add(graph.graph['session_id'])
            else:
                # Update last_seen
                graph.nodes[node_id]['last_seen'] = datetime.utcnow().isoformat()
        
        return nodes_added
    
    def _add_edges(self, graph: nx.DiGraph, relationships: List[Dict], entities: List[Dict]) -> int:
        """Add relationships as edges to the graph"""
        edges_added = 0
        entity_lookup = {e.get('value'): e for e in entities}
        
        for rel in relationships:
            source_id = self._get_node_id_from_value(rel.get('source'), entity_lookup)
            target_id = self._get_node_id_from_value(rel.get('target'), entity_lookup)
            
            if source_id and target_id:
                if not graph.has_edge(source_id, target_id):
                    rel_type = rel.get('type', 'related_to')
                    weight = self.relationship_types.get(rel_type, {}).get('weight', 0.5)
                    
                    graph.add_edge(
                        source_id,
                        target_id,
                        relationship_type=rel_type,
                        confidence=rel.get('confidence', 0.8),
                        weight=weight,
                        properties=rel.get('properties', {}),
                        timestamp=datetime.utcnow().isoformat()
                    )
                    edges_added += 1
        
        return edges_added
    
    def _detect_implicit_relationships(self, graph: nx.DiGraph, entities: List[Dict]) -> int:
        """Detect and add implicit relationships based on entity properties"""
        implicit_edges = 0
        
        # Group entities by type
        entities_by_type = defaultdict(list)
        for entity in entities:
            entities_by_type[entity.get('type')].append(entity)
        
        # Detect temporal relationships (events close in time)
        if 'event' in entities_by_type:
            events = sorted(entities_by_type['event'], 
                          key=lambda x: x.get('properties', {}).get('timestamp', ''))
            
            for i in range(len(events) - 1):
                source_id = self._get_node_id(events[i])
                target_id = self._get_node_id(events[i + 1])
                
                if not graph.has_edge(source_id, target_id):
                    graph.add_edge(
                        source_id,
                        target_id,
                        relationship_type='preceded',
                        confidence=0.9,
                        weight=1.0,
                        implicit=True
                    )
                    implicit_edges += 1
        
        # Detect same-subnet relationships
        if 'ip' in entities_by_type:
            ips = entities_by_type['ip']
            for i, ip1 in enumerate(ips):
                for ip2 in ips[i+1:]:
                    if self._same_subnet(ip1.get('value'), ip2.get('value')):
                        source_id = self._get_node_id(ip1)
                        target_id = self._get_node_id(ip2)
                        
                        if not graph.has_edge(source_id, target_id):
                            graph.add_edge(
                                source_id,
                                target_id,
                                relationship_type='same_subnet',
                                confidence=0.7,
                                weight=0.5,
                                implicit=True
                            )
                            implicit_edges += 1
        
        return implicit_edges
    
    def _calculate_graph_metrics(self, graph: nx.DiGraph) -> Dict[str, Any]:
        """Calculate graph metrics for analysis"""
        metrics = {
            'node_count': graph.number_of_nodes(),
            'edge_count': graph.number_of_edges(),
            'density': nx.density(graph) if graph.number_of_nodes() > 0 else 0,
            'components': nx.number_weakly_connected_components(graph),
            'avg_degree': sum(dict(graph.degree()).values()) / graph.number_of_nodes() 
                         if graph.number_of_nodes() > 0 else 0
        }
        
        # Calculate centrality for important nodes
        if graph.number_of_nodes() > 0:
            try:
                degree_centrality = nx.degree_centrality(graph)
                betweenness_centrality = nx.betweenness_centrality(graph)
                
                # Find most central nodes
                metrics['most_connected'] = sorted(
                    degree_centrality.items(), 
                    key=lambda x: x[1], 
                    reverse=True
                )[:5]
                
                metrics['most_between'] = sorted(
                    betweenness_centrality.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:5]
            except:
                pass
        
        # Calculate clustering coefficient
        if graph.number_of_nodes() > 2:
            try:
                metrics['clustering_coefficient'] = nx.average_clustering(graph.to_undirected())
            except:
                metrics['clustering_coefficient'] = 0
        
        return metrics
    
    def _detect_attack_patterns(self, graph: nx.DiGraph, context: Dict) -> List[Dict]:
        """Detect known attack patterns in the graph"""
        detected_patterns = []
        
        # Get tactics from nodes
        tactic_nodes = [n for n, d in graph.nodes(data=True) 
                       if d.get('entity_type') == 'tactic']
        
        # Check for kill chain patterns
        for pattern_name, pattern_def in self.attack_patterns.items():
            pattern_tactics = pattern_def['pattern']
            found_tactics = []
            
            for tactic in pattern_tactics:
                for node in tactic_nodes:
                    if tactic.lower() in graph.nodes[node].get('value', '').lower():
                        found_tactics.append(tactic)
                        break
            
            if len(found_tactics) >= len(pattern_tactics) * 0.6:  # 60% threshold
                detected_patterns.append({
                    'pattern': pattern_name,
                    'confidence': len(found_tactics) / len(pattern_tactics),
                    'tactics_found': found_tactics,
                    'risk_multiplier': pattern_def['risk_multiplier']
                })
        
        # Detect lateral movement by graph structure
        if self._detect_lateral_movement_structure(graph):
            detected_patterns.append({
                'pattern': 'lateral_movement_graph',
                'confidence': 0.8,
                'description': 'Graph structure indicates lateral movement',
                'risk_multiplier': 1.8
            })
        
        return detected_patterns
    
    def _detect_lateral_movement_structure(self, graph: nx.DiGraph) -> bool:
        """Detect lateral movement based on graph structure"""
        # Look for patterns like: host1 -> host2 -> host3
        hostname_nodes = [n for n, d in graph.nodes(data=True) 
                         if d.get('entity_type') == 'hostname']
        
        if len(hostname_nodes) < 3:
            return False
        
        # Check for paths between hostnames
        for i, host1 in enumerate(hostname_nodes[:-1]):
            for host2 in hostname_nodes[i+1:]:
                try:
                    path = nx.shortest_path(graph, host1, host2)
                    if len(path) > 2:  # Indicates movement through multiple hops
                        return True
                except nx.NetworkXNoPath:
                    continue
        
        return False
    
    def _detect_anomalies(self, graph: nx.DiGraph, metrics: Dict) -> List[Dict]:
        """Detect anomalies in graph structure"""
        anomalies = []
        
        # High degree nodes (potential C2 or scanning)
        if metrics.get('most_connected'):
            for node, centrality in metrics['most_connected'][:3]:
                if centrality > 0.5:  # Connected to >50% of nodes
                    anomalies.append({
                        'type': 'high_connectivity',
                        'node': node,
                        'value': centrality,
                        'description': f"Node {node} has unusually high connections",
                        'risk_impact': 1.5
                    })
        
        # Isolated components (potential backdoor)
        if metrics.get('components', 1) > 1:
            anomalies.append({
                'type': 'isolated_components',
                'count': metrics['components'],
                'description': "Graph has isolated components",
                'risk_impact': 1.2
            })
        
        # Low clustering with high connectivity (potential scanning)
        if metrics.get('clustering_coefficient', 1) < 0.1 and metrics.get('avg_degree', 0) > 5:
            anomalies.append({
                'type': 'scanning_pattern',
                'description': "Low clustering with high connectivity suggests scanning",
                'risk_impact': 1.3
            })
        
        return anomalies
    
    def _generate_graph_summary(self, graph: nx.DiGraph, metrics: Dict, 
                               patterns: List[Dict], anomalies: List[Dict]) -> str:
        """Generate human-readable graph summary"""
        summary = f"Graph contains {metrics['node_count']} entities and {metrics['edge_count']} relationships. "
        
        if patterns:
            pattern_names = [p['pattern'] for p in patterns]
            summary += f"Detected attack patterns: {', '.join(pattern_names)}. "
        
        if anomalies:
            anomaly_types = list(set(a['type'] for a in anomalies))
            summary += f"Found anomalies: {', '.join(anomaly_types)}. "
        
        if metrics.get('most_connected'):
            top_node = metrics['most_connected'][0][0]
            summary += f"Most connected entity: {top_node}."
        
        return summary
    
    def _calculate_risk_score(self, patterns: List[Dict], anomalies: List[Dict], 
                             metrics: Dict) -> float:
        """Calculate risk score based on graph analysis"""
        base_score = 0.0
        
        # Pattern contribution
        for pattern in patterns:
            base_score += pattern['confidence'] * pattern['risk_multiplier'] * 10
        
        # Anomaly contribution
        for anomaly in anomalies:
            base_score += anomaly.get('risk_impact', 1.0) * 5
        
        # Metric-based adjustments
        if metrics.get('density', 0) > 0.5:
            base_score += 10  # High density indicates coordinated activity
        
        if metrics.get('components', 1) > 3:
            base_score += 15  # Multiple isolated components
        
        return min(base_score, 100.0)
    
    def _get_node_id(self, entity: Dict) -> str:
        """Generate unique node ID for entity"""
        return f"{entity.get('type')}:{entity.get('value')}"
    
    def _get_node_id_from_value(self, value: Any, entity_lookup: Dict) -> Optional[str]:
        """Get node ID from entity value"""
        if isinstance(value, dict):
            return self._get_node_id(value)
        elif value in entity_lookup:
            return self._get_node_id(entity_lookup[value])
        else:
            # Try to find by value
            for entity in entity_lookup.values():
                if entity.get('value') == value:
                    return self._get_node_id(entity)
        return None
    
    def _same_subnet(self, ip1: str, ip2: str, mask: int = 24) -> bool:
        """Check if two IPs are in the same subnet"""
        try:
            parts1 = ip1.split('.')
            parts2 = ip2.split('.')
            
            # Check /24 subnet by default
            return parts1[:3] == parts2[:3]
        except:
            return False
    
    def _edge_to_relationship(self, edge: Tuple) -> Dict:
        """Convert NetworkX edge to relationship dict"""
        return {
            'source': edge[0],
            'target': edge[1],
            'type': edge[2].get('relationship_type', 'related_to'),
            'confidence': edge[2].get('confidence', 0.8),
            'properties': edge[2]
        }
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Return Graph Builder capabilities"""
        return {
            'engine': 'graph_builder',
            'version': self.version,
            'capabilities': [
                'relationship_building',
                'graph_metrics',
                'pattern_detection',
                'anomaly_detection',
                'risk_scoring'
            ],
            'supported_relationships': list(self.relationship_types.keys()),
            'attack_patterns': list(self.attack_patterns.keys()),
            'statistics': self.graph_stats
        }
    
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate Graph Builder configuration"""
        # No specific configuration required
        return True


async def main():
    """
    Main entry point for Graph Builder Engine
    """
    engine = GraphBuilderEngine()
    
    # Log capabilities
    capabilities = engine.get_capabilities()
    engine.logger.log('engine_capabilities', capabilities)
    
    # Start processing
    await engine.start()


if __name__ == '__main__':
    asyncio.run(main())