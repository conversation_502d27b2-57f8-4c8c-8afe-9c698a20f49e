"""
Live Ingestion Test - Real Pattern Learning
Query logs from ALL vendors and watch adaptive learning in action
"""

import os
import json
import psycopg2
from elasticsearch import Elasticsearch
from dotenv import load_dotenv
from redis import Redis
from uuid import uuid4
from datetime import datetime
import time

load_dotenv()

# Configuration
ELASTIC_CLOUD_ID = os.getenv('ELASTIC_CLOUD_ID')
ELASTIC_API_KEY = os.getenv('ELASTIC_API_KEY')
REDIS_HOST = 'localhost'
REDIS_PORT = 6380

DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'siemless_v2',
    'user': 'siemless',
    'password': 'siemless123'
}

# All vendors we discovered
VENDORS = [
    ('logs-tippingpoint-*', 'TippingPoint IPS', 'tippingpoint'),
    ('logs-threatlocker-*', 'ThreatLocker', 'threatlocker'),
    ('logs-fortinet_fortigate*', 'Fortinet FortiGate', 'fortinet'),
    ('logs-panw.panos-*', 'Palo Alto', 'palo_alto'),
    ('logs-crowdstrike*', 'CrowdStrike', 'crowdstrike'),
    ('logs-microsoft_defender*', 'Microsoft Defender', 'defender'),
    ('logs-endpoint.events*', 'Elastic Endpoint', 'elastic_endpoint')
]

print("="*80)
print("LIVE ADAPTIVE INGESTION TEST")
print("Testing pattern learning with ALL 7 vendors")
print("="*80)

# Connect to services
print("\n[1] Connecting to services...")
elastic = Elasticsearch(cloud_id=ELASTIC_CLOUD_ID, api_key=ELASTIC_API_KEY, request_timeout=30)
redis_client = Redis(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)
db_conn = psycopg2.connect(**DB_CONFIG)
cursor = db_conn.cursor()

print("[OK] Elastic, Redis, and Database connected")

# Get baseline
print("\n[2] Getting baseline counts...")
cursor.execute("SELECT COUNT(*) FROM entities")
baseline_entities = cursor.fetchone()[0]

cursor.execute("SELECT COUNT(*) FROM relationships")
baseline_relationships = cursor.fetchone()[0]

cursor.execute("SELECT entity_type, COUNT(*) FROM entities GROUP BY entity_type")
baseline_types = dict(cursor.fetchall())

print(f"[OK] Baseline:")
print(f"  Entities: {baseline_entities}")
print(f"  Relationships: {baseline_relationships}")
print(f"  Types: {baseline_types}")

# Ingest logs from each vendor
print("\n" + "="*80)
print("[3] INGESTING LOGS FROM ALL VENDORS")
print("="*80)

total_logs_sent = 0

for index_pattern, vendor_name, vendor_code in VENDORS:
    print(f"\n[{vendor_name}]")

    # Query recent logs
    query = {
        "query": {"range": {"@timestamp": {"gte": "now-7d"}}},
        "size": 3,  # Small sample to test learning
        "sort": [{"@timestamp": {"order": "desc"}}]
    }

    try:
        response = elastic.search(index=index_pattern, body=query)
        hits = response['hits']['hits']

        if not hits:
            print(f"  [SKIP] No recent logs found")
            continue

        print(f"  [OK] Found {len(hits)} logs")

        # Send to ingestion engine
        request_id = str(uuid4())

        ingestion_request = {
            'request_id': request_id,
            'source': vendor_code,
            'vendor': vendor_name,
            'logs': []
        }

        for hit in hits:
            ingestion_request['logs'].append({
                'timestamp': hit['_source'].get('@timestamp'),
                'raw': hit['_source'],
                'index': hit['_index'],
                'id': hit['_id']
            })

        # Publish to contextualization engine for extraction
        redis_client.publish(
            'contextualization.extract_entities',
            json.dumps(ingestion_request)
        )

        print(f"  [OK] Sent {len(hits)} logs to contextualization")
        print(f"       Request ID: {request_id}")
        total_logs_sent += len(hits)

        # Small delay between vendors
        time.sleep(0.5)

    except Exception as e:
        print(f"  [FAIL] Error: {e}")

print(f"\n[OK] Total logs sent: {total_logs_sent}")

# Wait for processing
print("\n[4] Waiting for contextualization to process...")
print("    (Adaptive learning happening in background...)")
time.sleep(10)  # Give engines time to process

# Check for updates
print("\n[5] Checking for new entities and patterns...")

cursor.execute("SELECT COUNT(*) FROM entities")
new_entity_count = cursor.fetchone()[0]

cursor.execute("SELECT COUNT(*) FROM relationships")
new_relationship_count = cursor.fetchone()[0]

cursor.execute("SELECT entity_type, COUNT(*) FROM entities GROUP BY entity_type")
new_types = dict(cursor.fetchall())

entities_added = new_entity_count - baseline_entities
relationships_added = new_relationship_count - baseline_relationships

print(f"\n[RESULTS]")
print(f"  Entities: {baseline_entities} → {new_entity_count} (+{entities_added})")
print(f"  Relationships: {baseline_relationships} → {new_relationship_count} (+{relationships_added})")

if entities_added > 0:
    print(f"\n[NEW ENTITY TYPES]")
    for entity_type, count in new_types.items():
        old_count = baseline_types.get(entity_type, 0)
        if count > old_count:
            print(f"  {entity_type}: {old_count} → {count} (+{count - old_count})")

    # Show sample new entities
    print(f"\n[SAMPLE NEW ENTITIES]")
    cursor.execute("""
        SELECT entity_type, entity_value, confidence, first_seen, properties
        FROM entities
        ORDER BY first_seen DESC
        LIMIT 10
    """)

    for row in cursor.fetchall():
        entity_type, value, confidence, first_seen, props = row
        vendor = ''
        if props and 'source' in props:
            vendor = f" (from {props['source']})"
        print(f"  {entity_type}: {value}{vendor} - confidence: {confidence:.2f}")
else:
    print(f"\n[INFO] No new entities added yet")
    print(f"       This could mean:")
    print(f"       1. Contextualization engine is still processing")
    print(f"       2. Entities already exist in database")
    print(f"       3. Need to wait longer for async processing")

# Check ingestion_logs for vendor tracking
print(f"\n[6] Checking ingestion logs...")
cursor.execute("""
    SELECT source_type, COUNT(*) as count, MAX(created_at) as latest
    FROM ingestion_logs
    GROUP BY source_type
    ORDER BY count DESC
    LIMIT 10
""")

print(f"\n[LOGS IN DATABASE BY VENDOR]")
for source_type, count, latest in cursor.fetchall():
    print(f"  {source_type}: {count:,} logs (latest: {latest})")

cursor.close()
db_conn.close()

print("\n" + "="*80)
print("ADAPTIVE LEARNING STATUS")
print("="*80)

print(f"""
What just happened:
1. ✅ Queried logs from ALL 7 vendors in Elastic
2. ✅ Sent {total_logs_sent} logs to contextualization engine
3. ⏳ Adaptive extractor auto-detecting vendors
4. ⏳ Checking for hardcoded patterns
5. ⏳ For unknown vendors → Triggering AI learning
6. ⏳ Crystallizing patterns for future use

Expected outcomes (may take a few minutes):
- TippingPoint: Pattern learned (first time)
- ThreatLocker: Pattern learned (first time)
- Defender: Pattern learned (first time)
- Fortinet: Uses existing pattern (already known)
- Palo Alto: Uses existing pattern (already known)
- CrowdStrike: Uses existing pattern (already known)

Check contextualization engine logs:
  docker-compose logs contextualization_engine | grep -i "learning"
  docker-compose logs contextualization_engine | grep -i "pattern"

Check for learned patterns in database:
  SELECT * FROM pattern_library WHERE pattern_type = 'entity_extraction';
""")

print("\n" + "="*80)
print("TEST COMPLETE")
print("="*80)
