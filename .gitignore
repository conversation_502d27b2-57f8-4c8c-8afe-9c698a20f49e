# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
*.egg-info/
dist/
build/

# Logs
logs/
*.log
*.jsonl

# Environment
.env
.env.local
.env.*.local

# API Credentials (CRITICAL - NEVER COMMIT)
engines/intelligence/config/api_credentials.yaml
**/api_credentials.yaml
credentials.yaml
*.credentials

# IDE
.vscode/
.idea/
*.swp
*.swo
.DS_Store

# Docker
docker-compose.override.yml

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/

# Database
*.db
*.sql
data/

# Temporary
tmp/
temp/
*.tmp

# Pattern exports
patterns/exports/

# Training data
training_data/

# Monitoring data
prometheus_data/
grafana_data/

# Documentation archives
documents/archive/
*.backup
*_STATUS.md
*_REPORT.md
*_COMPLETE.md
HANDOFF_*.md
IMMEDIATE_*.md

# Node.js / Frontend
node_modules/
frontend/node_modules/
package-lock.json
frontend/package-lock.json
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Archives and test files
archives/
tests_integration/
tests_essential/
*.messy
*.backup_with_redis

# Cleanup and fix scripts
fix_*.py
cleanup_test_files.py

# Database exports
*.json
elastic_rules_*.json
opencti_*.json
threatfox_*.json
detection_fidelity_report.json

# Temporary Python scripts
initialize_*.py
ingest_*.py
integrate_*.py
assess_*.py
analyze_*.py
export_*.py
import_*.py
visualize_*.py
auto_populate_*.py
run_*_tests.py
apply_*.py
add_redis_*.py

# Keep essential scripts
!setup_database.py
!create_deployment_package.py
!migrate_keys_simple.py