/**
 * Business Context Editor
 * Allows analysts to add organizational context to entities for better FP reduction
 */

import React, { useState } from 'react'
import { X, Save, AlertCircle, CheckCircle } from 'lucide-react'

interface BusinessContextFormData {
  entity_type: 'host' | 'user' | 'ip' | 'service_account' | 'application'
  entity_value: string
  context_label: string
  context_description: string
  business_unit: string
  owner: string
  criticality: 'critical' | 'high' | 'medium' | 'low'
  security_zone: 'dmz' | 'internal' | 'restricted' | 'public'
  behavior_pattern?: {
    scheduled_jobs?: string[]
    normal_times?: string[]
    expected_traffic?: string[]
  }
}

interface BusinessContextEditorProps {
  onClose: () => void
  onSave: (context: BusinessContextFormData) => Promise<void>
  initialEntity?: {
    type: string
    value: string
  }
  existingContext?: Partial<BusinessContextFormData>
  mode?: 'create' | 'edit'
}

export const BusinessContextEditor: React.FC<BusinessContextEditorProps> = ({
  onClose,
  onSave,
  initialEntity,
  existingContext,
  mode = 'create'
}) => {
  const [formData, setFormData] = useState<BusinessContextFormData>({
    entity_type: (initialEntity?.type as any) || existingContext?.entity_type || 'host',
    entity_value: initialEntity?.value || existingContext?.entity_value || '',
    context_label: existingContext?.context_label || '',
    context_description: existingContext?.context_description || '',
    business_unit: existingContext?.business_unit || '',
    owner: existingContext?.owner || '',
    criticality: existingContext?.criticality || 'medium',
    security_zone: existingContext?.security_zone || 'internal',
    behavior_pattern: existingContext?.behavior_pattern || {}
  })

  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const [scheduledJobs, setScheduledJobs] = useState<string>(
    formData.behavior_pattern?.scheduled_jobs?.join(', ') || ''
  )
  const [normalTimes, setNormalTimes] = useState<string>(
    formData.behavior_pattern?.normal_times?.join(', ') || ''
  )
  const [expectedTraffic, setExpectedTraffic] = useState<string>(
    formData.behavior_pattern?.expected_traffic?.join(', ') || ''
  )

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setSaving(true)

    try {
      // Build behavior pattern from comma-separated strings
      const behavior_pattern = {
        scheduled_jobs: scheduledJobs.split(',').map(s => s.trim()).filter(Boolean),
        normal_times: normalTimes.split(',').map(s => s.trim()).filter(Boolean),
        expected_traffic: expectedTraffic.split(',').map(s => s.trim()).filter(Boolean)
      }

      await onSave({
        ...formData,
        behavior_pattern
      })

      setSuccess(true)
      setTimeout(() => {
        onClose()
      }, 1500)
    } catch (err: any) {
      setError(err.message || 'Failed to save context')
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-blue-600 text-white p-4 rounded-t-lg flex items-center justify-between">
          <h2 className="text-xl font-semibold">
            {mode === 'create' ? 'Add Business Context' : 'Edit Business Context'}
          </h2>
          <button
            onClick={onClose}
            className="text-white hover:bg-blue-700 p-1 rounded"
          >
            <X size={20} />
          </button>
        </div>

        {/* Success Message */}
        {success && (
          <div className="bg-green-50 border-l-4 border-green-500 p-4 m-4">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle size={20} />
              <span className="font-medium">Context saved successfully!</span>
            </div>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 m-4">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle size={20} />
              <span className="font-medium">{error}</span>
            </div>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Entity Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
              Entity Information
            </h3>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Entity Type <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.entity_type}
                  onChange={(e) => setFormData({ ...formData, entity_type: e.target.value as any })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  required
                  disabled={mode === 'edit'}
                >
                  <option value="host">Host / Device</option>
                  <option value="user">User</option>
                  <option value="ip">IP Address</option>
                  <option value="service_account">Service Account</option>
                  <option value="application">Application</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Entity Value <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.entity_value}
                  onChange={(e) => setFormData({ ...formData, entity_value: e.target.value })}
                  placeholder="e.g., BACKUP-SERVER-01, john.doe, *************"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  required
                  disabled={mode === 'edit'}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Context Label <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.context_label}
                onChange={(e) => setFormData({ ...formData, context_label: e.target.value })}
                placeholder="e.g., Primary Backup Server, VIP Executive Laptop"
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Short, descriptive label for this entity
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                value={formData.context_description}
                onChange={(e) => setFormData({ ...formData, context_description: e.target.value })}
                placeholder="Detailed description of this entity's purpose, normal behavior, etc."
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
                rows={3}
              />
            </div>
          </div>

          {/* Business Metadata */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
              Business Information
            </h3>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Business Unit
                </label>
                <input
                  type="text"
                  value={formData.business_unit}
                  onChange={(e) => setFormData({ ...formData, business_unit: e.target.value })}
                  placeholder="e.g., IT Operations, Finance, HR"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Owner
                </label>
                <input
                  type="text"
                  value={formData.owner}
                  onChange={(e) => setFormData({ ...formData, owner: e.target.value })}
                  placeholder="e.g., <EMAIL>"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Criticality
                </label>
                <select
                  value={formData.criticality}
                  onChange={(e) => setFormData({ ...formData, criticality: e.target.value as any })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                >
                  <option value="critical">Critical</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Security Zone
                </label>
                <select
                  value={formData.security_zone}
                  onChange={(e) => setFormData({ ...formData, security_zone: e.target.value as any })}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2"
                >
                  <option value="dmz">DMZ</option>
                  <option value="internal">Internal</option>
                  <option value="restricted">Restricted</option>
                  <option value="public">Public</option>
                </select>
              </div>
            </div>
          </div>

          {/* Behavior Pattern */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
              Behavior Pattern (Optional)
            </h3>
            <p className="text-sm text-gray-600">
              Define normal behavior for this entity to reduce false positives
            </p>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Scheduled Jobs
              </label>
              <input
                type="text"
                value={scheduledJobs}
                onChange={(e) => setScheduledJobs(e.target.value)}
                placeholder="e.g., weekly_backup, nightly_maintenance (comma separated)"
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Normal Activity Times
              </label>
              <input
                type="text"
                value={normalTimes}
                onChange={(e) => setNormalTimes(e.target.value)}
                placeholder="e.g., Sunday 02:00-04:00, Weekdays 09:00-17:00 (comma separated)"
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Expected Traffic/Activity
              </label>
              <input
                type="text"
                value={expectedTraffic}
                onChange={(e) => setExpectedTraffic(e.target.value)}
                placeholder="e.g., large_file_operations, database_queries (comma separated)"
                className="w-full border border-gray-300 rounded-lg px-3 py-2"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end gap-3 pt-4 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              disabled={saving}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
              disabled={saving}
            >
              <Save size={16} />
              {saving ? 'Saving...' : 'Save Context'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
