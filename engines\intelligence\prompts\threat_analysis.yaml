# Threat Analysis Prompt Template
# Version 1.0 - Analyze threat intelligence and generate actionable insights

name: threat_intelligence_analysis
version: "1.0"
task_type: threat_analysis
active: true

variables:
  - threat_data
  - ioc_count
  - source

metadata:
  author: SIEMLess Intelligence Engine
  created: 2025-09-30
  description: Analyzes threat intelligence to generate detection rules and response actions
  response_format: json

template: |
  # Threat Intelligence Analysis Task

  ## Threat Data
  **Source**: {source}
  **IOC Count**: {ioc_count}

  ```json
  {threat_data}
  ```

  ## Analysis Requirements

  ### 1. Threat Classification
  Classify the threat:
  - Threat actor/group (if known)
  - Campaign name (if applicable)
  - Attack type (malware, phishing, ransomware, APT, etc.)
  - Target industries/sectors
  - Geographic focus

  ### 2. MITRE ATT&CK Mapping
  Map to MITRE ATT&CK framework:
  - Tactics used
  - Techniques employed
  - Sub-techniques identified
  - Detection opportunities per technique

  ### 3. IOC Analysis
  Analyze indicators of compromise:
  - IP addresses (malicious infrastructure)
  - Domains (C2, phishing, distribution)
  - File hashes (malware samples)
  - URLs (phishing, exploits)
  - Email addresses (phishing senders)
  - Certificates (SSL/TLS indicators)

  ### 4. Detection Opportunities
  Identify where this threat can be detected:
  - Network traffic patterns
  - Endpoint behaviors
  - Authentication anomalies
  - File system changes
  - Registry modifications
  - Process execution chains

  ### 5. Recommended Actions
  Provide actionable recommendations:
  - Detection rules to create
  - IOCs to block immediately
  - Threat hunting queries
  - Response playbooks
  - Preventive controls

  ## Response Format

  ```json
  {
    "threat_classification": {
      "threat_actor": "APT28 / Fancy Bear",
      "campaign": "Operation XYZ",
      "attack_type": "targeted_espionage",
      "target_sectors": ["government", "defense"],
      "target_regions": ["US", "EU"],
      "severity": "critical"
    },
    "mitre_attack": {
      "tactics": [
        {
          "id": "TA0001",
          "name": "Initial Access",
          "techniques": [
            {
              "id": "T1566.001",
              "name": "Spearphishing Attachment",
              "confidence": 0.95
            }
          ]
        }
      ]
    },
    "ioc_analysis": {
      "ips": [
        {
          "value": "*********",
          "type": "c2_server",
          "confidence": 0.90,
          "action": "block"
        }
      ],
      "domains": [
        {
          "value": "evil.com",
          "type": "c2_domain",
          "confidence": 0.85,
          "action": "block"
        }
      ],
      "hashes": [
        {
          "value": "abc123def456",
          "algorithm": "sha256",
          "type": "malware",
          "family": "Emotet",
          "action": "quarantine"
        }
      ]
    },
    "detection_opportunities": [
      {
        "location": "network",
        "indicator": "DNS queries to known C2 domains",
        "data_source": "dns_logs",
        "detection_rule": "Query for domains matching evil.com",
        "priority": "high"
      },
      {
        "location": "endpoint",
        "indicator": "Suspicious process execution chain",
        "data_source": "process_creation",
        "detection_rule": "cmd.exe spawned by outlook.exe",
        "priority": "critical"
      }
    ],
    "recommended_actions": {
      "immediate": [
        {
          "action": "block_iocs",
          "targets": ["*********", "evil.com"],
          "system": "firewall",
          "priority": "critical"
        }
      ],
      "detection_rules": [
        {
          "name": "APT28 C2 Communication",
          "type": "sigma",
          "platform": ["splunk", "elastic"],
          "priority": "high"
        }
      ],
      "threat_hunting": [
        {
          "query": "Search for historical connections to *********",
          "timeframe": "90_days",
          "data_source": "netflow"
        }
      ],
      "response_playbook": {
        "name": "APT28 Incident Response",
        "steps": [
          "Isolate affected systems",
          "Collect forensic images",
          "Reset compromised credentials",
          "Review lateral movement"
        ]
      }
    },
    "risk_assessment": {
      "likelihood": "medium",
      "impact": "critical",
      "overall_risk": "high",
      "justification": "Known APT group targeting our sector"
    }
  }
  ```

  Focus on **actionable intelligence**. Provide specific, implementable recommendations.
