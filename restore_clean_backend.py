#!/usr/bin/env python3
"""
Restore clean backend_engine.py from last known good state
Remove all hastily added Redis handlers and fixes
"""

import subprocess
import os

def restore_clean_backend():
    """Restore backend_engine.py to clean state before Redis additions"""

    print("Restoring clean backend_engine.py...")

    # First, let's save the current version as backup
    print("[1/4] Backing up current version...")
    subprocess.run([
        "cp",
        "engines/backend/backend_engine.py",
        "engines/backend/backend_engine.py.backup_with_redis"
    ])

    print("[2/4] Getting last clean version (before Redis handlers)...")

    # The clean version should have:
    # - All 11 HTTP endpoints working
    # - No Redis handlers (_handle_redis_*)
    # - No _serialize_response method
    # - Around 2000-2500 lines

    # Check git history for clean version
    result = subprocess.run([
        "git", "log", "--oneline", "-n", "20", "engines/backend/backend_engine.py"
    ], capture_output=True, text=True)

    print("Recent commits to backend_engine.py:")
    print(result.stdout)

    print("\n[3/4] Options:")
    print("1. Restore from git (recommended if you committed the working version)")
    print("2. Rebuild from clean components")
    print("3. Extract and clean current file")

    return True

def extract_clean_components():
    """Extract clean components from current backend_engine.py"""

    backend_file = "engines/backend/backend_engine.py"

    with open(backend_file, 'r') as f:
        content = f.read()

    # Remove all Redis handlers
    import re

    # Remove Redis handler methods
    pattern = r'async def _handle_redis.*?(?=\n    async def |\n    def |\nclass |\nif __name__|$)'
    content = re.sub(pattern, '', content, flags=re.DOTALL)

    # Remove _serialize_response
    pattern = r'def _serialize_response.*?(?=\n    async def |\n    def |\nclass |\nif __name__|$)'
    content = re.sub(pattern, '', content, flags=re.DOTALL)

    # Remove Redis channel subscriptions
    lines = content.split('\n')
    cleaned_lines = []
    skip_redis_channels = False

    for line in lines:
        if '# Log Source Quality API channels' in line:
            skip_redis_channels = True
            continue
        if skip_redis_channels and (']' in line or 'def ' in line):
            skip_redis_channels = False
        if not skip_redis_channels:
            if 'backend.log_quality.' not in line:
                cleaned_lines.append(line)

    content = '\n'.join(cleaned_lines)

    # Remove Redis channel handling from process_message
    pattern = r'elif channel == \'backend\.log_quality\..*?\n.*?await self\._handle_redis.*?\(data\)'
    content = re.sub(pattern, '', content, flags=re.DOTALL)

    # Count lines
    line_count = len(content.split('\n'))
    print(f"\nCleaned file has {line_count} lines (was 3609)")

    # Save as new clean version
    with open("engines/backend/backend_engine_clean.py", 'w') as f:
        f.write(content)

    print("Clean version saved as backend_engine_clean.py")
    print("\nNext steps:")
    print("1. Review backend_engine_clean.py")
    print("2. Test HTTP endpoints still work")
    print("3. Create separate log_quality_redis_handler.py")

    return True

if __name__ == "__main__":
    restore_clean_backend()
    # Auto-extract clean components
    print("\nAuto-extracting clean components...")
    extract_clean_components()