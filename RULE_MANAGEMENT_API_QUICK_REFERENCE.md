# Rule Management API - Quick Reference

## Base URL
```
http://localhost:8005/api
```

## Endpoints

### 1. List Rules (GET /rules)
```bash
# Get all rules (default limit: 100)
curl "http://localhost:8005/api/rules"

# Filter by quality
curl "http://localhost:8005/api/rules?quality_label=medium&limit=10"

# Filter by source
curl "http://localhost:8005/api/rules?source=otx&limit=20"

# Combine filters
curl "http://localhost:8005/api/rules?quality_label=low&source=unknown&limit=50"
```

**Response:**
```json
{
  "rules": [...],
  "total": 3517,
  "filters": {
    "quality_label": "medium",
    "source": null
  }
}
```

### 2. Update Rule (PATCH /rules/{rule_id})
```bash
# Override quality label
curl -X PATCH http://localhost:8005/api/rules/{rule_id} \
  -H "Content-Type: application/json" \
  -d '{
    "quality_label": "high",
    "custom_tags": ["reviewed", "confirmed-threat"],
    "notes": "Verified with threat intel team",
    "reviewed_by": "<EMAIL>"
  }'
```

**Response:**
```json
{
  "success": true,
  "rule_id": "uuid",
  "quality_label": "high",
  "custom_tags": ["reviewed", "confirmed-threat"],
  "notes": "Verified with threat intel team",
  "reviewed_by": "<EMAIL>",
  "reviewed_at": "2025-10-03T09:00:00"
}
```

### 3. Delete Single Rule (DELETE /rules/{rule_id})
```bash
curl -X DELETE http://localhost:8005/api/rules/{rule_id}
```

**Response:**
```json
{
  "success": true,
  "rule_id": "uuid",
  "deleted_from_siems": ["elastic"],
  "message": "Rule deleted from database and 1 SIEM(s)"
}
```

### 4. Bulk Delete (POST /rules/bulk-delete)
```bash
# Dry-run (preview only, default)
curl -X POST http://localhost:8005/api/rules/bulk-delete \
  -H "Content-Type: application/json" \
  -d '{
    "quality_label": "low",
    "deployed": false,
    "dry_run": true
  }'

# Execute deletion (CAUTION!)
curl -X POST http://localhost:8005/api/rules/bulk-delete \
  -H "Content-Type: application/json" \
  -d '{
    "quality_label": "low",
    "deployed": false,
    "dry_run": false
  }'
```

**Dry-Run Response:**
```json
{
  "dry_run": true,
  "total_matches": 107,
  "sample_rules": [...],
  "message": "Would delete 107 rules. Set dry_run=false to execute."
}
```

**Execute Response:**
```json
{
  "success": true,
  "deleted_count": 107,
  "filters_used": {
    "quality_label": "low",
    "source": null,
    "deployed": false
  },
  "message": "Successfully deleted 107 rules"
}
```

## Quality Labels
- **high**: quality_score >= 0.7 (3 rules)
- **medium**: 0.4 <= quality_score < 0.7 (3,517 rules)
- **low**: quality_score < 0.4 (110 rules)

## Common Workflows

### Workflow 1: Review and Promote Low-Quality Rule
```bash
# 1. Find low-quality rules
curl "http://localhost:8005/api/rules?quality_label=low&limit=10"

# 2. Review specific rule (manual process)

# 3. Promote to high quality
curl -X PATCH http://localhost:8005/api/rules/{rule_id} \
  -H "Content-Type: application/json" \
  -d '{
    "quality_label": "high",
    "custom_tags": ["reviewed", "false-positive"],
    "notes": "Confirmed safe after manual review",
    "reviewed_by": "<EMAIL>"
  }'
```

### Workflow 2: Cleanup Unknown Source Rules
```bash
# 1. Preview what would be deleted
curl -X POST http://localhost:8005/api/rules/bulk-delete \
  -H "Content-Type: application/json" \
  -d '{
    "quality_label": "low",
    "source": "unknown",
    "deployed": false,
    "dry_run": true
  }'

# 2. Review the sample rules

# 3. Execute if satisfied
curl -X POST http://localhost:8005/api/rules/bulk-delete \
  -H "Content-Type: application/json" \
  -d '{
    "quality_label": "low",
    "source": "unknown",
    "deployed": false,
    "dry_run": false
  }'
```

### Workflow 3: Tag Rules for Future Review
```bash
# Mark rule for later review
curl -X PATCH http://localhost:8005/api/rules/{rule_id} \
  -H "Content-Type: application/json" \
  -d '{
    "custom_tags": ["needs-review", "potential-fp"],
    "notes": "Flagged for review - high volume of alerts"
  }'
```

## Safety Features

1. **Dry-Run Default**: Bulk delete requires explicit `dry_run: false`
2. **Filter Required**: Bulk delete needs at least one filter
3. **Cascade Deletion**: Automatically removes from deployed SIEMs
4. **Audit Trail**: reviewed_by and reviewed_at track manual changes
5. **Quality Override**: Users can override AI-generated labels

## Database Columns

### Auto-Generated
- `rule_id` (UUID)
- `rule_data` (JSONB - contains name, source, ioc_type, ioc_value, quality_score, sigma_rule)
- `deployed_to_elastic`, `deployed_to_splunk`, `deployed_to_sentinel`, `deployed_to_qradar` (boolean)
- `elastic_rule_id`, `splunk_rule_id`, etc. (text - SIEM-specific IDs)
- `created_at`, `updated_at` (timestamp)

### User-Managed (via PATCH)
- `quality_label` (high/medium/low)
- `custom_tags` (text array)
- `notes` (text)
- `reviewed_by` (text)
- `reviewed_at` (timestamp - auto-set when reviewed_by is updated)

## Error Codes

- **404**: Rule not found
- **400**: Invalid quality_label or missing required filter
- **500**: Database error or internal server error

## Testing

Run the comprehensive test suite:
```bash
python test_rule_management.py
```

Expected output:
```
[OK] PASS List Rules with Filters
[OK] PASS Update Rule Metadata
[OK] PASS Bulk Delete (Dry-Run)
[OK] PASS Delete Single Rule
Results: 4/4 tests passed
```

## Full Documentation

For complete details, see:
- [RULE_MANAGEMENT_PHASE_2_COMPLETE.md](./RULE_MANAGEMENT_PHASE_2_COMPLETE.md) - Full implementation docs
- [PROJECT_INDEX.md](./PROJECT_INDEX.md) - Project navigation
- [CLAUDE.md](./CLAUDE.md) - Development context
