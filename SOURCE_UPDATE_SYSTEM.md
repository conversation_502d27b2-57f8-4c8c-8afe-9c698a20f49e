# Log Source Update System

## Overview

The Log Source Update System ensures that SIEMLess v2 stays current with the latest security products, detection capabilities, and threat intelligence. It automatically learns, updates, and improves log source identification and correlation rules.

## How The System Updates With Latest Information

### 1. **Multiple Update Sources**

The system pulls updates from various sources to stay current:

#### **Community Updates (GitHub)**
- **Frequency**: Daily
- **Source**: Community-contributed definitions
- **Content**: New product patterns, field mappings, quality scores
- **Trust Level**: Medium (requires review)
- **Auto-Apply**: No (manual approval needed)

#### **CTI Feeds**
- **Frequency**: Every 6 hours
- **Sources**: MITRE ATT&CK, AlienVault OTX, vendor feeds
- **Content**: New attack techniques, IoCs, detection mappings
- **Trust Level**: High
- **Auto-Apply**: Yes (trusted sources)

#### **Vendor APIs**
- **Frequency**: Product-specific (6-24 hours)
- **Sources**: CrowdStrike, Elastic, Splunk, etc.
- **Content**: New detection capabilities, rule updates
- **Trust Level**: Highest
- **Auto-Apply**: Yes (official sources)

#### **Machine Learning from Logs**
- **Frequency**: Every 12 hours
- **Source**: Your actual log data
- **Content**: New patterns from unidentified logs
- **Trust Level**: Variable (based on confidence)
- **Auto-Apply**: No (requires validation)

#### **CVE Monitoring**
- **Frequency**: Every 4 hours
- **Source**: NVD, vendor security advisories
- **Content**: Security vulnerabilities affecting products
- **Trust Level**: Critical
- **Auto-Apply**: Yes (security updates)

### 2. **Automated Update Process**

```mermaid
graph TD
    A[Update Scheduler] --> B{Check Sources}
    B --> C[Community]
    B --> D[CTI Feeds]
    B --> E[Vendor APIs]
    B --> F[Learn from Logs]
    B --> G[CVE Monitor]

    C --> H[Validate]
    D --> I[Auto-Apply]
    E --> I
    F --> H
    G --> I

    H --> J[Approval Queue]
    J --> K[Manual Review]
    K --> L[Apply Updates]
    I --> L

    L --> M[Update Database]
    M --> N[Refresh Cache]
    N --> O[Notify Systems]
```

### 3. **Learning from Your Environment**

The system continuously learns from unidentified logs:

1. **Pattern Recognition**
   - Analyzes logs marked as "unknown"
   - Identifies common fields and patterns
   - Groups similar unidentified logs

2. **Confidence Building**
   - Requires 100+ samples before creating pattern
   - Validates against known good samples
   - Increases confidence with more matches

3. **Automatic Pattern Creation**
   - Creates new identification patterns
   - Assigns quality scores based on analysis
   - Queues for approval when confidence > 80%

### 4. **Update Types and Handling**

| Update Type | Description | Auto-Apply | Example |
|-------------|-------------|------------|---------|
| **New Product** | Previously unknown security product | No | New EDR vendor |
| **Pattern Update** | Improved identification pattern | Depends on source | Better regex for Sysmon |
| **Quality Adjustment** | Change in product quality score | Yes (CVE) / No (other) | CVE reduces score |
| **MITRE Mapping** | New technique coverage | Yes | Product now detects T1055 |
| **Field Mapping** | New field definitions | No | Updated log format |

### 5. **Version Control and Rollback**

Every update is versioned and can be rolled back:

```sql
-- View update history
SELECT * FROM source_update_history
WHERE vendor = 'CrowdStrike'
ORDER BY created_at DESC;

-- Rollback to previous version
SELECT rollback_pattern_version('pattern_id', 'version_number');
```

### 6. **Performance-Based Adjustments**

The system automatically adjusts based on real-world performance:

- **Pattern Accuracy Tracking**
  - Monitors successful identifications
  - Tracks false positives/negatives
  - Adjusts confidence scores accordingly

- **Quality Score Updates**
  - Reviews detection effectiveness
  - Adjusts scores based on actual performance
  - Considers CVE impacts

### 7. **Manual Update Triggers**

Administrators can trigger updates manually:

```python
# Trigger via Redis
redis_client.publish('backend.trigger_update', json.dumps({
    'update_type': 'all'  # or 'community', 'cti', 'learned'
}))

# Trigger specific source
redis_client.publish('backend.trigger_update', json.dumps({
    'update_type': 'vendor',
    'vendor': 'crowdstrike'
}))
```

## Update Schedule

| Schedule | Interval | What It Does | Auto-Apply |
|----------|----------|--------------|------------|
| **Community** | 24 hours | Check GitHub for community updates | No |
| **CTI** | 6 hours | Update from threat intelligence | Yes |
| **Learning** | 12 hours | Analyze unidentified patterns | No |
| **CVE** | 4 hours | Check security vulnerabilities | Yes |
| **Performance** | 24 hours | Review pattern effectiveness | No |

## Configuration

### Environment Variables

```bash
# Enable/disable update sources
ENABLE_COMMUNITY_UPDATES=true
ENABLE_CTI_UPDATES=true
ENABLE_LEARNING=true
ENABLE_CVE_MONITORING=true

# API Keys for vendor updates
CROWDSTRIKE_API_KEY=your_key_here
ELASTIC_API_KEY=your_key_here
OTX_API_KEY=your_key_here

# Update behavior
AUTO_APPLY_CTI=true
AUTO_APPLY_CVE=true
MIN_CONFIDENCE_FOR_LEARNING=0.8
```

### Database Configuration

```sql
-- Configure update source
INSERT INTO update_sources (
    source_name, source_type, url,
    update_frequency, auto_apply, trust_level
) VALUES (
    'Custom CTI Feed', 'api', 'https://your-cti-feed.com/api',
    '4 hours', FALSE, 7
);

-- Set learning thresholds
UPDATE learning_config
SET min_samples = 200,
    confidence_threshold = 0.85,
    auto_approve = FALSE;
```

## Monitoring Updates

### Dashboard Metrics

The system provides metrics for monitoring:

- **Updates Applied**: Count per source per day
- **Pending Approvals**: Updates awaiting review
- **Learning Progress**: Patterns being learned
- **Pattern Performance**: Accuracy rates
- **CVE Impacts**: Products affected by vulnerabilities

### Redis Channels for Monitoring

```python
# Subscribe to update notifications
channels = [
    'backend.source_updates',      # Update results
    'backend.approval_needed',     # Pending approvals
    'backend.critical_cve_alert',  # CVE alerts
    'backend.update_metrics'       # Performance metrics
]
```

## Approval Workflow

### Viewing Pending Updates

```sql
-- View pending updates
SELECT * FROM v_pending_updates;

-- Review specific update
SELECT * FROM update_approval_queue
WHERE vendor = 'NewVendor' AND status = 'pending';
```

### Approving Updates

```sql
-- Approve an update
UPDATE update_approval_queue
SET status = 'approved',
    reviewed_by = 'admin',
    review_notes = 'Validated against test logs',
    reviewed_at = NOW()
WHERE queue_id = 'uuid-here';

-- Reject an update
UPDATE update_approval_queue
SET status = 'rejected',
    reviewed_by = 'admin',
    review_notes = 'Pattern too broad',
    reviewed_at = NOW()
WHERE queue_id = 'uuid-here';
```

## Examples

### Example 1: New Product Detection

When a new security product is detected:

1. **Learning Phase**
   - System sees 100+ unidentified logs with pattern
   - Analyzes common fields: `agent_id`, `scan_type`, `threat_level`
   - Hypothesizes: "NewVendor AntiVirus"

2. **Validation**
   - Confidence reaches 85%
   - Creates pattern: `"agent_id".*"scan_type"`
   - Queues for approval

3. **Approval**
   - Admin reviews pattern
   - Tests against sample logs
   - Approves with quality score 7/10

4. **Application**
   - Pattern added to database
   - Future logs automatically identified
   - Quality metrics tracked

### Example 2: CVE Impact

When a critical CVE affects a product:

1. **Detection**
   - CVE-2024-12345 affects "Product X"
   - Severity: Critical
   - Affects detection capability

2. **Automatic Adjustment**
   - Quality score reduced: 8 → 6
   - Alert sent to administrators
   - Correlation confidence adjusted

3. **Resolution**
   - Vendor releases patch
   - Admin confirms patch applied
   - Quality score restored

### Example 3: MITRE Update

When new attack techniques are mapped:

1. **CTI Feed Update**
   - MITRE adds T1234 technique
   - Maps to CrowdStrike, SentinelOne

2. **Automatic Application**
   - Coverage added to products
   - Correlation rules updated
   - Detection fidelity recalculated

3. **Impact**
   - "Lateral Movement" detection: 85% → 92%
   - New correlation rules activated
   - Alerts enriched with technique info

## Best Practices

1. **Review Pending Updates Weekly**
   - Check approval queue
   - Validate learned patterns
   - Test before approving

2. **Monitor Pattern Performance**
   - Review accuracy metrics
   - Adjust confidence thresholds
   - Remove underperforming patterns

3. **Keep API Keys Current**
   - Update vendor API credentials
   - Enable important feeds
   - Monitor API rate limits

4. **Backup Before Major Updates**
   - Export pattern definitions
   - Save correlation rules
   - Document custom modifications

5. **Test in Staging First**
   - Apply updates to test environment
   - Validate detection accuracy
   - Check for false positives

## Troubleshooting

### Updates Not Applying

```bash
# Check scheduler status
docker-compose logs backend_engine | grep "Update Scheduler"

# Check for errors
docker-compose logs backend_engine | grep ERROR | grep update

# Manually trigger update
python -c "import redis; r=redis.Redis(port=6380); r.publish('backend.trigger_update', '{\"update_type\":\"all\"}')"
```

### Pattern Not Identifying Logs

```sql
-- Check pattern performance
SELECT * FROM pattern_performance_metrics
WHERE pattern_id = 'your_pattern_id'
ORDER BY metric_date DESC;

-- Review failed identifications
SELECT * FROM ingestion_logs
WHERE source_vendor = 'unknown'
AND log_data::text LIKE '%expected_pattern%'
LIMIT 10;
```

### High False Positive Rate

```sql
-- Reduce pattern confidence
UPDATE source_identification_patterns
SET confidence_score = confidence_score * 0.8
WHERE pattern_id = 'problematic_pattern';

-- Or disable pattern
UPDATE source_identification_patterns
SET enabled = FALSE
WHERE pattern_id = 'problematic_pattern';
```

## Summary

The Log Source Update System ensures SIEMLess v2 remains current through:

1. **Multiple Update Sources**: Community, CTI, vendors, learning
2. **Automated Processing**: Scheduled checks and updates
3. **Machine Learning**: Learns from your actual logs
4. **Version Control**: Track and rollback changes
5. **Performance Monitoring**: Adjusts based on real results
6. **Security Focus**: Immediate CVE response

This creates a self-improving system that gets better at identifying and correlating logs over time, while staying current with the evolving security landscape.