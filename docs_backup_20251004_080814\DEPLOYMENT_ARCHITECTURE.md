# SIEMLess v2.0 - Deployment Architecture

## 🏗️ Component Overview

### Core Platform Services
Located in `./engines/docker-compose.yml`:
- **5 Backend Engines**: Intelligence, Backend, Ingestion, Contextualization, Delivery
- **Frontend**: React application (port 3000)
- **Redis**: Message broker (port 6380)
- **PostgreSQL**: Primary database (port 5433)

### Optional Services

#### 1. Monitoring Stack (`./engines/grafana/docker-compose.grafana.yml`)
- **Grafana**: Dashboards and visualization (port 3001)
- **Prometheus**: Metrics collection (port 9090)
- **Purpose**: Backend monitoring + operational dashboards

#### 2. Authentication (`./engines/docker-compose.keycloak.yml`)
- **Keycloak**: SSO and identity management (port 8080)
- **Purpose**: Enterprise authentication

## ⚠️ Important: No Overlap!

### Grafana Clarification
- **ONLY ONE Grafana instance** at `./engines/grafana/`
- The `./monitoring/` folder is legacy/backup - DO NOT USE
- Grafana serves **dual purpose**:
  1. Backend monitoring (engine metrics, system health)
  2. User-facing dashboards (security metrics, cost tracking)

### Frontend Architecture
- **React Frontend (port 3000)**: Complex interactive widgets
  - Entity Explorer with drill-downs
  - Relationship graphs (D3.js)
  - AI Investigation Guide
  - Pattern crystallization queue
  - FlexLayout dashboards

- **Grafana (port 3001)**: Standard visualizations
  - Time series metrics
  - Standard charts and graphs
  - System monitoring
  - Cost tracking dashboards

Both complement each other - no overlap in functionality.

## 🚀 Deployment Options

### 1. Development (Minimal)
```bash
cd engines
docker-compose up --build
```
**Gets you**:
- All backend engines
- React frontend
- Redis + PostgreSQL

### 2. With Monitoring
```bash
cd engines
docker-compose -f docker-compose.yml -f grafana/docker-compose.grafana.yml up --build
```
**Adds**:
- Grafana dashboards
- Prometheus metrics

### 3. Full Production
```bash
cd engines
docker-compose \
  -f docker-compose.yml \
  -f grafana/docker-compose.grafana.yml \
  -f docker-compose.keycloak.yml \
  up --build
```
**Adds**:
- Keycloak SSO
- Full authentication flow

## 📊 Service Map

```
┌─────────────────────────────────────────────────────────────┐
│                     User Browser                            │
└─────────────┬────────────────┬────────────────┬────────────┘
              │                │                │
         Port 3000        Port 3001        Port 8080
              │                │                │
    ┌─────────▼──────┐ ┌──────▼──────┐ ┌──────▼──────┐
    │ React Frontend │ │   Grafana   │ │   Keycloak  │
    │   (Widgets)    │ │ (Dashboards)│ │    (SSO)    │
    └────────┬───────┘ └──────┬──────┘ └─────────────┘
             │                │
    ┌────────▼────────────────▼───────────────────────┐
    │         Docker Network: siemless_v2_network     │
    ├──────────────────────────────────────────────────┤
    │  Intelligence (8001)  │  Backend (8002)         │
    │  Ingestion (8003)     │  Contextualization(8004)│
    │  Delivery (8005)      │                         │
    ├──────────────────────────────────────────────────┤
    │     Redis (6380)      │   PostgreSQL (5433)     │
    └──────────────────────────────────────────────────┘
```

## 🔧 Configuration Files

### Required Environment Variables
Create `.env` file in `/engines/`:
```env
# Database
POSTGRES_DB=siemless_v2
POSTGRES_USER=siemless
POSTGRES_PASSWORD=siemless123

# AI Keys (optional for basic operation)
OPENAI_API_KEY=your_key
ANTHROPIC_API_KEY=your_key
GOOGLE_API_KEY=your_key

# CTI Integration
OTX_API_KEY=your_otx_key

# Grafana
GRAFANA_PASSWORD=admin123

# Keycloak (if using)
KEYCLOAK_ADMIN=admin
KEYCLOAK_ADMIN_PASSWORD=admin
```

## 🎯 Key Design Decisions

1. **Why separate docker-compose files?**
   - Modularity: Can run core without heavy monitoring/auth
   - Resource management: Grafana/Prometheus can be resource-intensive
   - Development flexibility: Faster iteration on core features

2. **Why both React Frontend and Grafana?**
   - React: Interactive security widgets (drag-drop, real-time updates)
   - Grafana: Standard metrics and time-series visualization
   - Together: Complete operational picture

3. **Why custom nginx in frontend?**
   - API proxying to backend engines
   - WebSocket support for real-time updates
   - SPA routing support
   - Security headers

## 🚨 Common Issues & Solutions

### Port Conflicts
- Redis: Using 6380 instead of default 6379
- PostgreSQL: Using 5433 instead of default 5432
- Frontend: 3000 (React), 3001 (Grafana)

### Container Name Conflicts
- All containers prefixed with `siemless_`
- Version suffix `_v2` to avoid conflicts with v1

### Network Issues
- All services on same network: `siemless_v2_network`
- Frontend nginx proxies `/api/*` to backend engines
- WebSocket support at `/ws`

## 📝 Quick Commands

```bash
# Start core only
./start_consolidated.bat

# Start with Grafana
./start_consolidated.bat --with-grafana

# Start everything
./start_consolidated.bat --full

# Check status
docker-compose ps

# View logs
docker-compose logs -f frontend
docker-compose logs -f intelligence_engine

# Stop everything
docker-compose down

# Clean restart (removes volumes)
docker-compose down -v
docker-compose up --build
```

## ✅ Health Checks

All services have health endpoints:
- Frontend: http://localhost:3000/health
- Engines: http://localhost:800X/health (where X is 1-5)
- Grafana: http://localhost:3001/api/health
- Keycloak: http://localhost:8080/health

## 🔄 Update Strategy

1. **Frontend updates**: Rebuild only frontend container
2. **Engine updates**: Rebuild specific engine
3. **Full update**: `docker-compose down && docker-compose up --build`

This architecture ensures no overlapping services and clear separation of concerns.