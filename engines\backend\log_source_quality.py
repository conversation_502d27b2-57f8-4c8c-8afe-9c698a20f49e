"""
Log Source Quality Assessment Engine
Implements the log source quality framework for SIEMLess v2.0
"""

import json
import asyncio
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum
import logging

class LogSourceTier(Enum):
    """Log source quality tiers"""
    PLATINUM = "PLATINUM"  # 95-100 score
    GOLD = "GOLD"         # 80-94 score
    SILVER = "SILVER"     # 65-79 score
    BRONZE = "BRONZE"     # <65 score

class LogSourceQualityEngine:
    """
    Assesses and tracks log source quality for correlation effectiveness
    """

    def __init__(self, redis_client=None, db_connection=None, logger=None):
        self.redis = redis_client
        self.db = db_connection
        self.logger = logger or logging.getLogger(__name__)

        # Initialize log source catalog
        self.source_catalog = self._initialize_catalog()

        # Track active sources
        self.active_sources = {}

        # Cache for quality scores
        self.quality_cache = {}

    def _initialize_catalog(self) -> Dict:
        """Initialize the log source catalog with quality scores"""
        return {
            # EDR Solutions - PLATINUM TIER
            'crowdstrike': {
                'type': 'edr',
                'tier': LogSourceTier.PLATINUM.value,
                'base_score': 98,
                'capabilities': {
                    'process_injection': 95,
                    'file_operations': 95,
                    'network_connections': 90,
                    'memory_analysis': 95,
                    'behavioral_detection': 95
                },
                'latency': 'real-time',
                'structure': 'json',
                'enrichment': 'built-in'
            },
            'sentinelone': {
                'type': 'edr',
                'tier': LogSourceTier.PLATINUM.value,
                'base_score': 96,
                'capabilities': {
                    'process_injection': 93,
                    'file_operations': 94,
                    'network_connections': 88,
                    'memory_analysis': 92,
                    'behavioral_detection': 94
                }
            },
            'carbon_black': {
                'type': 'edr',
                'tier': LogSourceTier.PLATINUM.value,
                'base_score': 90,
                'capabilities': {
                    'process_injection': 90,
                    'file_operations': 90,
                    'network_connections': 85,
                    'memory_analysis': 88,
                    'behavioral_detection': 90
                }
            },

            # EDR Solutions - GOLD TIER
            'microsoft_defender': {
                'type': 'edr',
                'tier': LogSourceTier.GOLD.value,
                'base_score': 82,
                'capabilities': {
                    'process_injection': 75,
                    'file_operations': 80,
                    'network_connections': 75,
                    'memory_analysis': 70,
                    'behavioral_detection': 80
                }
            },

            # System Monitoring - GOLD TIER
            'sysmon': {
                'type': 'system_monitor',
                'tier': LogSourceTier.GOLD.value,
                'base_score': 78,
                'capabilities': {
                    'process_creation': 90,
                    'network_connections': 85,
                    'registry_changes': 85,
                    'file_creation': 80,
                    'process_injection': 70
                }
            },

            # Network Security - GOLD TIER
            'paloalto': {
                'type': 'firewall',
                'tier': LogSourceTier.GOLD.value,
                'base_score': 85,
                'capabilities': {
                    'network_flows': 90,
                    'application_identification': 85,
                    'threat_prevention': 80,
                    'url_filtering': 85,
                    'user_identification': 75
                }
            },
            'zeek': {
                'type': 'network_monitor',
                'tier': LogSourceTier.GOLD.value,
                'base_score': 88,
                'capabilities': {
                    'protocol_analysis': 95,
                    'metadata_extraction': 90,
                    'file_extraction': 85,
                    'dns_analysis': 90,
                    'ssl_inspection': 85
                }
            },

            # Identity - PLATINUM TIER
            'active_directory': {
                'type': 'identity',
                'tier': LogSourceTier.PLATINUM.value,
                'base_score': 92,
                'capabilities': {
                    'authentication': 95,
                    'kerberos_monitoring': 95,
                    'privilege_changes': 90,
                    'group_management': 90,
                    'account_changes': 90
                }
            },

            # Identity - GOLD TIER
            'azure_ad': {
                'type': 'identity',
                'tier': LogSourceTier.GOLD.value,
                'base_score': 84,
                'capabilities': {
                    'authentication': 90,
                    'conditional_access': 85,
                    'risk_detection': 80,
                    'mfa_events': 85
                }
            },
            'okta': {
                'type': 'identity',
                'tier': LogSourceTier.GOLD.value,
                'base_score': 86,
                'capabilities': {
                    'authentication': 90,
                    'sso_events': 85,
                    'mfa_events': 85,
                    'user_lifecycle': 80
                }
            },

            # Operating System - SILVER TIER
            'windows_events': {
                'type': 'os_logs',
                'tier': LogSourceTier.SILVER.value,
                'base_score': 70,
                'capabilities': {
                    'authentication': 75,
                    'process_creation': 60,
                    'account_management': 70,
                    'service_events': 65,
                    'audit_events': 70
                }
            },

            # Cloud - GOLD TIER
            'aws_cloudtrail': {
                'type': 'cloud',
                'tier': LogSourceTier.GOLD.value,
                'base_score': 87,
                'capabilities': {
                    'api_calls': 95,
                    'resource_changes': 90,
                    'authentication': 85,
                    'data_access': 80
                }
            },

            # Basic Logs - BRONZE TIER
            'syslog': {
                'type': 'system_logs',
                'tier': LogSourceTier.BRONZE.value,
                'base_score': 45,
                'capabilities': {
                    'basic_events': 50,
                    'authentication': 40,
                    'system_events': 45
                }
            }
        }

    async def register_source(self, source_name: str, source_config: Dict) -> Dict:
        """Register a new log source with quality assessment"""

        # Check if source is in catalog
        if source_name in self.source_catalog:
            catalog_entry = self.source_catalog[source_name].copy()
            # Ensure tier is a string for JSON serialization
            if 'tier' in catalog_entry and hasattr(catalog_entry['tier'], 'value'):
                catalog_entry['tier'] = catalog_entry['tier'].value
            source_config.update(catalog_entry)
        else:
            # Calculate quality score for unknown source
            source_config['base_score'] = self._calculate_unknown_source_score(source_config)
            tier = self._determine_tier(source_config['base_score'])
            # Ensure tier is a string
            source_config['tier'] = tier.value if hasattr(tier, 'value') else str(tier)

        # Store in active sources
        self.active_sources[source_name] = {
            'config': source_config,
            'registered_at': datetime.utcnow(),
            'last_seen': datetime.utcnow(),
            'event_count': 0,
            'quality_score': source_config.get('base_score', 50),
            'tier': str(source_config.get('tier', LogSourceTier.BRONZE.value))
        }

        # Store in database
        if self.db:
            await self._store_source_registration(source_name, source_config)

        # Cache quality score
        if self.redis:
            await self._cache_quality_score(source_name, source_config['base_score'])

        self.logger.info(f"Registered log source: {source_name} (Tier: {source_config.get('tier', 'UNKNOWN')}, Score: {source_config.get('base_score', 'N/A')})")

        return {
            'status': 'registered',
            'source': source_name,
            'tier': str(source_config.get('tier', 'UNKNOWN')),
            'score': source_config.get('base_score', 50),
            'capabilities': source_config.get('capabilities', {})
        }

    def calculate_detection_confidence(self, attack_type: str, available_sources: List[str]) -> Dict:
        """
        Calculate detection confidence for a specific attack based on available sources
        """

        # Define attack requirements
        attack_requirements = {
            'ransomware': {
                'required': [('edr', 'GOLD'), ('file_integrity', 'SILVER'), ('network', 'SILVER')],
                'optional': [('backup', 'BRONZE'), ('identity', 'SILVER')],
                'synergies': ['edr+network', 'edr+file_integrity']
            },
            'lateral_movement': {
                'required': [('identity', 'GOLD'), ('network', 'SILVER'), ('edr', 'GOLD')],
                'optional': [('firewall', 'SILVER')],
                'synergies': ['identity+network', 'identity+edr']
            },
            'data_exfiltration': {
                'required': [('network', 'GOLD'), ('dlp', 'SILVER'), ('cloud', 'SILVER')],
                'optional': [('edr', 'SILVER'), ('proxy', 'SILVER')],
                'synergies': ['network+dlp', 'network+cloud']
            },
            'insider_threat': {
                'required': [('identity', 'PLATINUM'), ('data_access', 'GOLD'), ('file_access', 'SILVER')],
                'optional': [('email', 'SILVER'), ('dlp', 'GOLD')],
                'synergies': ['identity+data_access', 'identity+file_access']
            },
            'credential_compromise': {
                'required': [('identity', 'GOLD'), ('edr', 'GOLD')],
                'optional': [('network', 'SILVER'), ('mfa', 'SILVER')],
                'synergies': ['identity+edr']
            }
        }

        if attack_type not in attack_requirements:
            return {'confidence': 0, 'reason': 'Unknown attack type'}

        requirements = attack_requirements[attack_type]
        base_confidence = 0
        sources_matched = []
        missing_requirements = []

        # Check required sources
        for req_type, min_tier in requirements['required']:
            matched = False
            for source in available_sources:
                if source in self.active_sources:
                    source_info = self.active_sources[source]
                    source_type = source_info['config'].get('type', '')
                    source_tier = source_info.get('tier', LogSourceTier.BRONZE)

                    if req_type in source_type or source_type == req_type:
                        if self._compare_tiers(source_tier, min_tier):
                            matched = True
                            sources_matched.append((source, source_tier))
                            # Add confidence based on tier
                            tier_confidence = {
                                LogSourceTier.PLATINUM: 30,
                                LogSourceTier.GOLD: 25,
                                LogSourceTier.SILVER: 15,
                                LogSourceTier.BRONZE: 10
                            }
                            base_confidence += tier_confidence.get(source_tier, 5)
                            break

            if not matched:
                missing_requirements.append((req_type, min_tier))

        # Check optional sources for bonus
        for opt_type, min_tier in requirements.get('optional', []):
            for source in available_sources:
                if source in self.active_sources:
                    source_info = self.active_sources[source]
                    source_type = source_info['config'].get('type', '')
                    source_tier = source_info.get('tier', LogSourceTier.BRONZE)

                    if opt_type in source_type or source_type == opt_type:
                        if self._compare_tiers(source_tier, min_tier):
                            base_confidence += 5  # Bonus for optional sources

        # Apply synergy bonuses
        synergy_bonus = self._calculate_synergy_bonus(sources_matched, requirements.get('synergies', []))

        # Calculate final confidence
        final_confidence = min(base_confidence * (1 + synergy_bonus), 98)

        return {
            'attack_type': attack_type,
            'confidence': final_confidence,
            'sources_used': [s[0] for s in sources_matched],
            'missing_requirements': missing_requirements,
            'synergies_applied': synergy_bonus > 0,
            'recommendation': self._generate_recommendation(missing_requirements, final_confidence)
        }

    def assess_correlation_capability(self, available_sources: List[str]) -> Dict:
        """
        Assess overall correlation capability based on available sources
        """

        # Group sources by type and tier
        source_breakdown = {
            'by_type': {},
            'by_tier': {
                LogSourceTier.PLATINUM: [],
                LogSourceTier.GOLD: [],
                LogSourceTier.SILVER: [],
                LogSourceTier.BRONZE: []
            }
        }

        total_score = 0
        capabilities = {}

        for source in available_sources:
            if source in self.active_sources:
                source_info = self.active_sources[source]
                source_type = source_info['config'].get('type', 'unknown')
                source_tier = source_info.get('tier', LogSourceTier.BRONZE)
                source_score = source_info.get('quality_score', 50)

                # Track by type
                if source_type not in source_breakdown['by_type']:
                    source_breakdown['by_type'][source_type] = []
                source_breakdown['by_type'][source_type].append(source)

                # Track by tier
                source_breakdown['by_tier'][source_tier].append(source)

                # Add to total score (with diminishing returns for same type)
                type_count = len(source_breakdown['by_type'][source_type])
                diminished_score = source_score * (1 / type_count)  # Diminishing returns
                total_score += diminished_score

                # Aggregate capabilities
                for cap, score in source_info['config'].get('capabilities', {}).items():
                    if cap not in capabilities or score > capabilities[cap]:
                        capabilities[cap] = score

        # Calculate composite score
        composite_score = min(total_score / len(available_sources) if available_sources else 0, 100)

        # Assess detection coverage
        attack_coverage = {}
        for attack_type in ['ransomware', 'lateral_movement', 'data_exfiltration', 'insider_threat', 'credential_compromise']:
            confidence = self.calculate_detection_confidence(attack_type, available_sources)
            attack_coverage[attack_type] = confidence['confidence']

        # Identify gaps
        gaps = self._identify_coverage_gaps(source_breakdown, capabilities)

        return {
            'composite_score': composite_score,
            'source_count': len(available_sources),
            'tier_distribution': {str(k): len(v) for k, v in source_breakdown['by_tier'].items()},
            'type_coverage': list(source_breakdown['by_type'].keys()),
            'attack_coverage': attack_coverage,
            'top_capabilities': dict(sorted(capabilities.items(), key=lambda x: x[1], reverse=True)[:10]),
            'critical_gaps': gaps,
            'overall_assessment': self._generate_overall_assessment(composite_score, attack_coverage)
        }

    def _compare_tiers(self, source_tier, required_tier: str) -> bool:
        """Compare if source tier meets requirement"""
        tier_values = {
            'PLATINUM': 4,
            'GOLD': 3,
            'SILVER': 2,
            'BRONZE': 1,
            LogSourceTier.PLATINUM: 4,
            LogSourceTier.GOLD: 3,
            LogSourceTier.SILVER: 2,
            LogSourceTier.BRONZE: 1
        }

        # Handle both string and enum types
        if hasattr(source_tier, 'value'):
            source_tier = source_tier.value
        if hasattr(required_tier, 'value'):
            required_tier = required_tier.value

        return tier_values.get(source_tier, 0) >= tier_values.get(required_tier, 0)

    def _calculate_synergy_bonus(self, sources_matched: List[Tuple], synergies: List[str]) -> float:
        """Calculate synergy bonus for source combinations"""
        bonus = 0.0
        source_types = [s[0] for s in sources_matched]

        synergy_multipliers = {
            'edr+network': 0.5,
            'identity+network': 0.4,
            'identity+edr': 0.6,
            'network+dlp': 0.3,
            'full_stack': 1.0  # All major types present
        }

        for synergy in synergies:
            if '+' in synergy:
                required_types = synergy.split('+')
                if all(any(rt in st for st in source_types) for rt in required_types):
                    bonus += synergy_multipliers.get(synergy, 0.2)

        # Check for full stack bonus
        major_types = ['edr', 'network', 'identity', 'cloud']
        if all(any(mt in ' '.join(source_types) for mt in major_types)):
            bonus += synergy_multipliers['full_stack']

        return bonus

    def _identify_coverage_gaps(self, source_breakdown: Dict, capabilities: Dict) -> List[str]:
        """Identify critical coverage gaps"""
        gaps = []

        # Check for essential source types
        essential_types = ['edr', 'identity', 'network']
        for essential in essential_types:
            if essential not in source_breakdown['by_type']:
                gaps.append(f"Missing {essential} visibility")

        # Check for critical capabilities
        critical_capabilities = ['process_injection', 'authentication', 'network_connections']
        for capability in critical_capabilities:
            if capability not in capabilities or capabilities[capability] < 70:
                gaps.append(f"Weak {capability} detection")

        # Check tier distribution
        if not source_breakdown['by_tier'][LogSourceTier.PLATINUM] and not source_breakdown['by_tier'][LogSourceTier.GOLD]:
            gaps.append("No high-quality sources (GOLD+ tier)")

        return gaps[:5]  # Return top 5 gaps

    def _generate_recommendation(self, missing_requirements: List, confidence: float) -> str:
        """Generate recommendation based on gaps"""
        if confidence >= 85:
            return "Excellent coverage for this attack type"
        elif confidence >= 70:
            return "Good coverage, consider adding: " + ', '.join([f"{req[0]} ({req[1]})" for req in missing_requirements[:2]])
        elif confidence >= 50:
            return "Moderate coverage, critical gaps: " + ', '.join([f"{req[0]} ({req[1]})" for req in missing_requirements])
        else:
            return "Poor coverage, urgent improvements needed: " + ', '.join([f"{req[0]} ({req[1]})" for req in missing_requirements])

    def _generate_overall_assessment(self, composite_score: float, attack_coverage: Dict) -> str:
        """Generate overall assessment text"""
        avg_coverage = sum(attack_coverage.values()) / len(attack_coverage) if attack_coverage else 0

        if composite_score >= 85 and avg_coverage >= 80:
            return "EXCELLENT: Comprehensive coverage with high-quality sources"
        elif composite_score >= 70 and avg_coverage >= 65:
            return "GOOD: Solid coverage with room for improvement"
        elif composite_score >= 50 and avg_coverage >= 50:
            return "MODERATE: Basic coverage, significant gaps remain"
        else:
            return "POOR: Critical gaps in coverage, immediate action required"

    def _get_tier_from_score(self, score: float) -> str:
        """Get tier name from quality score"""
        if score >= 95:
            return 'PLATINUM'
        elif score >= 80:
            return 'GOLD'
        elif score >= 65:
            return 'SILVER'
        else:
            return 'BRONZE'

    def _calculate_unknown_source_score(self, source_config: Dict) -> int:
        """Calculate quality score for unknown source"""
        score = 50  # Base score

        # Add points for capabilities
        if source_config.get('real_time', False):
            score += 10
        if source_config.get('structured_data', False):
            score += 10
        if source_config.get('enrichment', False):
            score += 5

        # Deduct for limitations
        if source_config.get('high_latency', False):
            score -= 10
        if source_config.get('unstructured', False):
            score -= 15

        return max(min(score, 100), 0)

    def _determine_tier(self, score: int) -> LogSourceTier:
        """Determine tier based on score"""
        if score >= 95:
            return LogSourceTier.PLATINUM
        elif score >= 80:
            return LogSourceTier.GOLD
        elif score >= 65:
            return LogSourceTier.SILVER
        else:
            return LogSourceTier.BRONZE

    async def _store_source_registration(self, source_name: str, config: Dict):
        """Store source registration in database"""
        if not self.db:
            return

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO log_sources (
                    source_name, source_type, tier, quality_score,
                    capabilities, registered_at
                ) VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (source_name) DO UPDATE SET
                    tier = EXCLUDED.tier,
                    quality_score = EXCLUDED.quality_score,
                    capabilities = EXCLUDED.capabilities,
                    updated_at = NOW()
            """, (
                source_name,
                config.get('type', 'unknown'),
                str(config.get('tier', LogSourceTier.BRONZE)),
                config.get('base_score', 50),
                json.dumps(config.get('capabilities', {})),
                datetime.utcnow()
            ))
            self.db.commit()
        except Exception as e:
            self.logger.error(f"Failed to store source registration: {e}")

    async def _cache_quality_score(self, source_name: str, score: int):
        """Cache quality score in Redis"""
        if not self.redis:
            return

        try:
            await self.redis.setex(
                f"log_source:quality:{source_name}",
                timedelta(hours=1),
                score
            )
        except Exception as e:
            self.logger.error(f"Failed to cache quality score: {e}")

    async def update_source_metrics(self, source_name: str, event_count: int = 1):
        """Update metrics for active source"""
        if source_name in self.active_sources:
            self.active_sources[source_name]['last_seen'] = datetime.utcnow()
            self.active_sources[source_name]['event_count'] += event_count

    # ============================================================
    # TELEMETRY MODE DETECTION (Alert-Only vs Full Telemetry)
    # ============================================================

    async def analyze_telemetry_mode(self, source_name: str, sample_logs: List[Dict]) -> Dict:
        """
        Detect if log source is in alert-only or full telemetry mode

        Uses HYBRID approach:
        1. Quick deterministic check (FREE, FAST)
        2. If uncertain, use AI analysis (Gemma-3, FREE)
        3. Crystallize result for future use

        CRITICAL IMPACT:
        - Alert-only sources lose 50+ quality points
        - Can't write custom SIEM rules
        - Can't baseline normal behavior
        - Detection confidence drops drastically

        Args:
            source_name: Name of the log source
            sample_logs: List of sample log dictionaries

        Returns:
            {
                'mode': 'alert_only|full_telemetry|partial',
                'confidence': 0.0-1.0,
                'reasoning': 'explanation',
                'penalty_applied': -50 or 0,
                'recommendation': 'how to fix'
            }
        """

        if not sample_logs:
            return {
                'mode': 'unknown',
                'confidence': 0.0,
                'reasoning': 'No sample logs provided'
            }

        self.logger.info(f"Analyzing telemetry mode for {source_name} ({len(sample_logs)} samples)")

        # STEP 1: Check for crystallized pattern (deterministic, FREE)
        pattern_key = f"telemetry_pattern:{source_name}"
        if self.redis:
            try:
                cached_pattern = await self.redis.get(pattern_key)
                if cached_pattern:
                    self.logger.info(f"Using crystallized telemetry pattern for {source_name}")
                    pattern = json.loads(cached_pattern)
                    return self._apply_telemetry_pattern(pattern, sample_logs)
            except Exception as e:
                self.logger.warning(f"Error reading cached pattern: {e}")

        # STEP 2: Quick deterministic analysis
        quick_result = self._quick_telemetry_check(source_name, sample_logs)
        if quick_result['confidence'] > 0.85:
            # High confidence from deterministic check - skip AI
            self.logger.info(f"Quick check: {source_name} is {quick_result['mode']} (confidence: {quick_result['confidence']})")

            # Crystallize this pattern
            if self.redis:
                await self._crystallize_telemetry_pattern(source_name, quick_result, sample_logs)

            return quick_result

        # STEP 3: Need AI analysis for uncertain cases
        self.logger.info(f"Quick check uncertain ({quick_result['confidence']}), requesting AI analysis")

        # Publish to Intelligence Engine for AI analysis
        ai_result = await self._request_ai_telemetry_analysis(source_name, sample_logs)

        # STEP 4: Crystallize AI result for future use
        if self.redis and ai_result['confidence'] > 0.7:
            await self._crystallize_telemetry_pattern(source_name, ai_result, sample_logs)

        return ai_result

    def _quick_telemetry_check(self, source_name: str, sample_logs: List[Dict]) -> Dict:
        """
        Quick deterministic telemetry mode detection

        WHAT THIS CHECKS:
        - Field count: Alert-only has few fields (~10), Full telemetry has many (~50+)
        - Event type diversity: Alerts have 2-5 types, Full telemetry has 15-30 types
        - Alert keywords: Presence of "alert", "detection", "incident" vs raw event fields
        - Field richness: CommandLine, ParentProcess, SHA256 = telemetry

        Returns high confidence (>0.85) if clear pattern, low if uncertain
        """

        # Statistical analysis
        total_fields = []
        event_types = set()
        alert_keywords = ['alert', 'detection', 'incident', 'severity', 'threat']
        telemetry_fields = ['commandline', 'parentprocess', 'sha256', 'md5', 'targetfilename',
                           'destinationip', 'sourceip', 'protocol', 'imageloaded']

        alert_keyword_count = 0
        telemetry_field_count = 0

        for log in sample_logs[:20]:  # Check first 20 samples
            # Field count
            if isinstance(log, dict):
                total_fields.append(len(log.keys()))

                # Event type
                if 'event_type' in log:
                    event_types.add(log['event_type'])
                elif 'eventType' in log:
                    event_types.add(log['eventType'])

                # Check for alert keywords in keys
                log_str_lower = json.dumps(log).lower()
                for keyword in alert_keywords:
                    if keyword in log_str_lower:
                        alert_keyword_count += 1
                        break

                # Check for telemetry fields
                for field in telemetry_fields:
                    if field in log_str_lower:
                        telemetry_field_count += 1
                        break

        # Calculate metrics
        avg_field_count = sum(total_fields) / len(total_fields) if total_fields else 0
        event_type_count = len(event_types)
        alert_ratio = alert_keyword_count / min(len(sample_logs), 20) if sample_logs else 0
        telemetry_ratio = telemetry_field_count / min(len(sample_logs), 20) if sample_logs else 0

        self.logger.debug(f"Quick check metrics: avg_fields={avg_field_count}, event_types={event_type_count}, "
                         f"alert_ratio={alert_ratio}, telemetry_ratio={telemetry_ratio}")

        # Decision logic
        if avg_field_count < 12 and event_type_count < 5 and alert_ratio > 0.6:
            # Clear alert-only pattern
            return {
                'mode': 'alert_only',
                'confidence': 0.90,
                'reasoning': f'Low field count ({avg_field_count:.1f}), few event types ({event_type_count}), '
                           f'high alert keywords ({alert_ratio:.0%})',
                'source': 'deterministic'
            }

        elif avg_field_count > 35 and event_type_count > 12 and telemetry_ratio > 0.6:
            # Clear full telemetry pattern
            return {
                'mode': 'full_telemetry',
                'confidence': 0.90,
                'reasoning': f'High field count ({avg_field_count:.1f}), many event types ({event_type_count}), '
                           f'rich telemetry fields ({telemetry_ratio:.0%})',
                'source': 'deterministic'
            }

        else:
            # Uncertain - need AI
            return {
                'mode': 'uncertain',
                'confidence': 0.50,
                'reasoning': f'Mixed signals: fields={avg_field_count:.1f}, types={event_type_count}',
                'source': 'deterministic'
            }

    async def _request_ai_telemetry_analysis(self, source_name: str, sample_logs: List[Dict]) -> Dict:
        """
        Request AI analysis from Intelligence Engine for telemetry mode detection

        WHAT THIS DOES:
        - Sends log samples to Intelligence Engine via Redis
        - AI (Gemma-3 FREE) analyzes the samples
        - Returns detailed analysis with reasoning and recommendations

        This is the "Learn Once" part of "Learn Once, Run Free Forever"
        """

        if not self.redis:
            self.logger.warning("Redis not available for AI analysis")
            return {
                'mode': 'unknown',
                'confidence': 0.0,
                'reasoning': 'Redis not available for AI communication'
            }

        # Create unique response channel
        from uuid import uuid4
        request_id = str(uuid4())
        response_channel = f'backend.telemetry_analysis.response.{request_id}'

        # Publish request to Intelligence Engine
        request_data = {
            'request_id': request_id,
            'source_name': source_name,
            'sample_logs': sample_logs[:5],  # Send first 5 samples to AI
            'response_channel': response_channel
        }

        self.logger.info(f"Requesting AI telemetry analysis for {source_name} on channel: intelligence.analyze_telemetry")
        await self.redis.publish('intelligence.analyze_telemetry', json.dumps(request_data))

        # Wait for response (timeout 30 seconds)
        import asyncio
        pubsub = self.redis.pubsub()
        await pubsub.subscribe(response_channel)

        timeout = 30
        start_time = asyncio.get_event_loop().time()

        while (asyncio.get_event_loop().time() - start_time) < timeout:
            message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)

            if message and message['type'] == 'message':
                response = json.loads(message['data'])
                await pubsub.unsubscribe(response_channel)
                self.logger.info(f"Received AI analysis: {response.get('mode')} (confidence: {response.get('confidence')})")
                return response

            await asyncio.sleep(0.1)

        # Timeout
        await pubsub.unsubscribe(response_channel)
        self.logger.error(f"AI telemetry analysis timeout for {source_name}")
        return {
            'mode': 'unknown',
            'confidence': 0.0,
            'reasoning': 'AI analysis timeout'
        }

    async def _crystallize_telemetry_pattern(self, source_name: str, analysis: Dict, sample_logs: List[Dict]):
        """
        Crystallize telemetry detection pattern for future FREE use

        WHAT THIS DOES:
        - Saves the learned pattern to Redis
        - Future detections use this pattern (deterministic, FREE)
        - This is the "Run Free Forever" part
        """

        if not self.redis:
            return

        pattern = {
            'vendor': source_name,
            'mode': analysis['mode'],
            'confidence': analysis['confidence'],
            'reasoning': analysis.get('reasoning', ''),
            'learned_from_ai': analysis.get('source') == 'ai',
            'created_at': datetime.utcnow().isoformat(),
            'sample_count': len(sample_logs)
        }

        pattern_key = f"telemetry_pattern:{source_name}"
        try:
            await self.redis.setex(pattern_key, timedelta(days=7), json.dumps(pattern))  # Cache 7 days
            self.logger.info(f"Crystallized telemetry pattern for {source_name}: {analysis['mode']}")
        except Exception as e:
            self.logger.error(f"Failed to crystallize pattern: {e}")

    def _apply_telemetry_pattern(self, pattern: Dict, sample_logs: List[Dict]) -> Dict:
        """
        Apply crystallized telemetry pattern (deterministic, FREE, FAST)

        WHAT THIS DOES:
        - Uses previously learned pattern
        - No AI calls needed
        - Sub-millisecond execution
        """

        return {
            'mode': pattern['mode'],
            'confidence': pattern['confidence'],
            'reasoning': f"Cached pattern: {pattern.get('reasoning', 'N/A')}",
            'source': 'crystallized_pattern',
            'pattern_age_days': (datetime.utcnow() - datetime.fromisoformat(pattern['created_at'])).days
        }

    def apply_telemetry_penalty(self, source_name: str, telemetry_result: Dict) -> Dict:
        """
        Apply quality penalty for alert-only mode

        WHY THIS MATTERS:
        Alert-only mode severely limits detection capabilities:
        - Can't write custom correlation rules (no raw events)
        - Can't baseline normal behavior (no continuous data)
        - Can't hunt for unknown threats (only pre-defined alerts)
        - Can't do advanced analytics (missing metadata)

        PENALTY:
        - Alert-only: -50 points (PLATINUM → BRONZE)
        - Partial: -25 points (depends on missing types)
        - Full telemetry: +0 points (no penalty)

        REAL IMPACT:
        CrowdStrike alert-only: 98 → 48 points
        Ransomware detection: 95% → 40% confidence
        """

        if source_name not in self.active_sources:
            self.logger.warning(f"Cannot apply penalty: {source_name} not in active sources")
            return {'status': 'error', 'reason': 'source_not_found'}

        mode = telemetry_result.get('mode')
        original_score = self.active_sources[source_name]['quality_score']

        # Determine penalty
        if mode == 'alert_only':
            penalty = -50
            impact = "CRITICAL: Cannot write custom rules, no baseline data"
        elif mode == 'partial':
            penalty = -25
            impact = "HIGH: Some event types missing, limited correlation"
        else:  # full_telemetry
            penalty = 0
            impact = "None: Full telemetry available"

        # Apply penalty
        new_score = max(original_score + penalty, 10)  # Minimum score of 10
        self.active_sources[source_name]['quality_score'] = new_score
        self.active_sources[source_name]['telemetry_mode'] = mode
        self.active_sources[source_name]['telemetry_penalty'] = penalty
        self.active_sources[source_name]['original_score'] = original_score

        # Update tier based on new score
        new_tier = self._determine_tier(new_score)
        original_tier = self.active_sources[source_name]['tier']
        self.active_sources[source_name]['tier'] = new_tier.value if hasattr(new_tier, 'value') else str(new_tier)

        # Log the change
        if penalty < 0:
            self.logger.warning(
                f"⚠️ TELEMETRY PENALTY: {source_name}\n"
                f"   Mode: {mode}\n"
                f"   Score: {original_score} → {new_score} ({penalty:+d})\n"
                f"   Tier: {original_tier} → {new_tier.value if hasattr(new_tier, 'value') else new_tier}\n"
                f"   Impact: {impact}\n"
                f"   Recommendation: {telemetry_result.get('recommendation', 'Enable full telemetry')}"
            )
        else:
            self.logger.info(f"✓ {source_name} has full telemetry - no penalty applied")

        return {
            'status': 'applied',
            'source': source_name,
            'mode': mode,
            'original_score': original_score,
            'new_score': new_score,
            'penalty': penalty,
            'original_tier': original_tier,
            'new_tier': new_tier.value if hasattr(new_tier, 'value') else str(new_tier),
            'impact': impact,
            'recommendation': telemetry_result.get('recommendation', '')
        }

    def get_source_status(self) -> Dict:
        """Get current status of all registered sources"""
        status = {
            'active_sources': len(self.active_sources),
            'sources': [],
            'tier_summary': {
                'PLATINUM': 0,
                'GOLD': 0,
                'SILVER': 0,
                'BRONZE': 0
            },
            'type_summary': {}
        }

        for name, info in self.active_sources.items():
            source_status = {
                'name': name,
                'type': info['config'].get('type', 'unknown'),
                'tier': str(info.get('tier', 'UNKNOWN')),
                'score': info.get('quality_score', 0),
                'last_seen': info['last_seen'].isoformat() if info.get('last_seen') else None,
                'event_count': info.get('event_count', 0)
            }
            status['sources'].append(source_status)

            # Update summaries
            tier = str(info.get('tier', LogSourceTier.BRONZE))
            if tier in status['tier_summary']:
                status['tier_summary'][tier] += 1

            source_type = info['config'].get('type', 'unknown')
            if source_type not in status['type_summary']:
                status['type_summary'][source_type] = 0
            status['type_summary'][source_type] += 1

        return status


# Example usage and testing
if __name__ == "__main__":
    import asyncio

    async def test_quality_engine():
        """Test the log source quality engine"""

        # Create engine
        engine = LogSourceQualityEngine()

        # Register some sources
        sources = [
            ('crowdstrike', {'type': 'edr', 'real_time': True}),
            ('windows_dc', {'type': 'active_directory', 'structured_data': True}),
            ('paloalto_fw', {'type': 'firewall', 'real_time': True}),
            ('app_logs', {'type': 'application', 'unstructured': True})
        ]

        print("Registering log sources...")
        for name, config in sources:
            result = await engine.register_source(name, config)
            print(f"  {name}: Tier={result['tier']}, Score={result['score']}")

        # Test detection confidence
        print("\nTesting detection confidence...")
        available = ['crowdstrike', 'windows_dc', 'paloalto_fw']

        for attack in ['ransomware', 'lateral_movement', 'insider_threat']:
            confidence = engine.calculate_detection_confidence(attack, available)
            print(f"\n{attack.upper()}:")
            print(f"  Confidence: {confidence['confidence']:.1f}%")
            print(f"  Recommendation: {confidence['recommendation']}")

        # Assess overall capability
        print("\nOverall Correlation Capability:")
        assessment = engine.assess_correlation_capability(available)
        print(f"  Composite Score: {assessment['composite_score']:.1f}")
        print(f"  Assessment: {assessment['overall_assessment']}")
        print(f"  Critical Gaps: {', '.join(assessment['critical_gaps'])}")

        # Get status
        print("\nSource Status:")
        status = engine.get_source_status()
        print(f"  Active Sources: {status['active_sources']}")
        print(f"  Tier Distribution: {status['tier_summary']}")
        print(f"  Type Coverage: {list(status['type_summary'].keys())}")

    # Run test
    asyncio.run(test_quality_engine())