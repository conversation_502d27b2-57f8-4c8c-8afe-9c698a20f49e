import React from 'react'
import { Clock, User, MessageSquare, FileText } from 'lucide-react'

export const CaseTimeline: React.FC = () => {
  const events = [
    { time: '10:45 AM', type: 'alert', user: 'System', message: 'Critical alert triggered' },
    { time: '10:47 AM', type: 'assign', user: '<PERSON>', message: 'Case assigned to analyst' },
    { time: '10:52 AM', type: 'comment', user: '<PERSON>', message: 'Initial investigation started' },
    { time: '11:15 AM', type: 'evidence', user: '<PERSON>', message: 'Log evidence collected' },
    { time: '11:30 AM', type: 'escalate', user: '<PERSON>', message: 'Escalated to Tier 2' }
  ]

  const iconMap = {
    alert: <Clock className="text-red-500" />,
    assign: <User className="text-blue-500" />,
    comment: <MessageSquare className="text-gray-500" />,
    evidence: <FileText className="text-green-500" />,
    escalate: <Clock className="text-orange-500" />
  }

  return (
    <div className="flex flex-col h-full bg-white p-4">
      <h3 className="text-lg font-semibold mb-4">Case Timeline</h3>

      <div className="flex-1 overflow-y-auto">
        <div className="relative">
          <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

          {events.map((event, idx) => (
            <div key={idx} className="relative flex items-start mb-6">
              <div className="absolute left-4 -translate-x-1/2 bg-white p-1">
                {iconMap[event.type as keyof typeof iconMap]}
              </div>
              <div className="ml-12 flex-1">
                <div className="flex justify-between items-start mb-1">
                  <span className="font-medium text-sm">{event.user}</span>
                  <span className="text-xs text-gray-500">{event.time}</span>
                </div>
                <p className="text-sm text-gray-700">{event.message}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default CaseTimeline