import React, { useEffect, useRef, useState } from 'react'
import * as d3 from 'd3'
import { useInvestigationStore } from '../stores/investigationStore'
import {
  ZoomIn, ZoomOut, Maximize2, Filter, Download,
  RefreshCw, Loader, Settings
} from 'lucide-react'

interface Node {
  id: string
  label: string
  type: string
  risk_score?: number
  group: number
  x?: number
  y?: number
  fx?: number | null
  fy?: number | null
}

interface Link {
  source: string | Node
  target: string | Node
  type: string
  weight: number
  timestamp?: string
}

interface RelationshipGraphProps {
  entityId?: string
  height?: number
  onNodeClick?: (nodeId: string) => void
}

export const RelationshipGraph: React.FC<RelationshipGraphProps> = ({
  entityId,
  height = 600,
  onNodeClick
}) => {
  const svgRef = useRef<SVGSVGElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const {
    entities,
    relationships,
    selectedEntity,
    loadingRelationships,
    loadRelationships
  } = useInvestigationStore()

  const [zoom, setZoom] = useState(1)
  const [filterType, setFilterType] = useState<string>('all')
  const [showLabels, setShowLabels] = useState(true)
  const [simulation, setSimulation] = useState<d3.Simulation<Node, Link> | null>(null)

  useEffect(() => {
    if (entityId) {
      loadRelationships(entityId)
    }
  }, [entityId, loadRelationships])

  useEffect(() => {
    if (!relationships.length || !svgRef.current) return

    // Clear previous graph
    d3.select(svgRef.current).selectAll('*').remove()

    // Prepare nodes and links
    const nodes: Node[] = Array.from(entities.values()).map(entity => ({
      id: entity.id,
      label: entity.value,
      type: entity.type,
      risk_score: entity.risk_score,
      group: getEntityGroup(entity.type)
    }))

    const links: Link[] = relationships
      .filter(rel => filterType === 'all' || rel.type === filterType)
      .map(rel => ({
        source: rel.source,
        target: rel.target,
        type: rel.type,
        weight: rel.weight
      }))

    // Set dimensions
    const width = containerRef.current?.clientWidth || 800
    const graphHeight = height

    const svg = d3.select(svgRef.current)
      .attr('width', width)
      .attr('height', graphHeight)

    // Create zoom behavior
    const zoomBehavior = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on('zoom', (event) => {
        container.attr('transform', event.transform)
        setZoom(event.transform.k)
      })

    svg.call(zoomBehavior)

    // Create container for zoom
    const container = svg.append('g')

    // Create arrow markers for directed edges
    svg.append('defs').selectAll('marker')
      .data(['resolved', 'suit', 'resolvedsuit'])
      .enter().append('marker')
      .attr('id', d => d)
      .attr('viewBox', '0 -5 10 10')
      .attr('refX', 25)
      .attr('refY', 0)
      .attr('markerWidth', 8)
      .attr('markerHeight', 8)
      .attr('orient', 'auto')
      .append('path')
      .attr('d', 'M0,-5L10,0L0,5')
      .attr('fill', '#999')

    // Create force simulation
    const forceSimulation = d3.forceSimulation<Node>(nodes)
      .force('link', d3.forceLink<Node, Link>(links)
        .id(d => d.id)
        .distance(d => 100 / d.weight)
        .strength(d => d.weight * 0.5))
      .force('charge', d3.forceManyBody().strength(-500))
      .force('center', d3.forceCenter(width / 2, graphHeight / 2))
      .force('collision', d3.forceCollide().radius(30))

    setSimulation(forceSimulation)

    // Create links
    const link = container.append('g')
      .attr('class', 'links')
      .selectAll('line')
      .data(links)
      .enter().append('line')
      .attr('stroke', d => getRelationshipColor(d.type))
      .attr('stroke-width', d => Math.sqrt(d.weight * 2))
      .attr('stroke-opacity', 0.6)
      .attr('marker-end', 'url(#resolved)')

    // Create link labels
    const linkLabel = container.append('g')
      .attr('class', 'link-labels')
      .selectAll('text')
      .data(links)
      .enter().append('text')
      .attr('font-size', 10)
      .attr('fill', '#666')
      .attr('text-anchor', 'middle')
      .text(d => d.type)
      .style('visibility', showLabels ? 'visible' : 'hidden')

    // Create nodes
    const node = container.append('g')
      .attr('class', 'nodes')
      .selectAll('g')
      .data(nodes)
      .enter().append('g')
      .attr('class', 'node')
      .call(d3.drag<SVGGElement, Node>()
        .on('start', dragStarted)
        .on('drag', dragged)
        .on('end', dragEnded))

    // Add circles for nodes
    node.append('circle')
      .attr('r', d => 10 + (d.risk_score || 0) / 10)
      .attr('fill', d => getNodeColor(d.type))
      .attr('stroke', d => d.id === selectedEntity?.id ? '#3b82f6' : '#fff')
      .attr('stroke-width', d => d.id === selectedEntity?.id ? 3 : 1.5)
      .on('click', (event, d) => {
        event.stopPropagation()
        onNodeClick?.(d.id)
      })

    // Add icons for nodes
    node.append('text')
      .attr('font-family', 'Lucide')
      .attr('font-size', 12)
      .attr('text-anchor', 'middle')
      .attr('dy', 4)
      .attr('fill', '#fff')
      .text(d => getNodeIcon(d.type))

    // Add labels
    const labels = node.append('text')
      .attr('x', 15)
      .attr('y', 3)
      .attr('font-size', 11)
      .attr('fill', '#333')
      .text(d => d.label)
      .style('visibility', showLabels ? 'visible' : 'hidden')

    // Add tooltips
    node.append('title')
      .text(d => `${d.label}\nType: ${d.type}\nRisk: ${d.risk_score || 0}`)

    // Update positions on simulation tick
    forceSimulation.on('tick', () => {
      link
        .attr('x1', d => (d.source as Node).x!)
        .attr('y1', d => (d.source as Node).y!)
        .attr('x2', d => (d.target as Node).x!)
        .attr('y2', d => (d.target as Node).y!)

      linkLabel
        .attr('x', d => ((d.source as Node).x! + (d.target as Node).x!) / 2)
        .attr('y', d => ((d.source as Node).y! + (d.target as Node).y!) / 2)

      node.attr('transform', d => `translate(${d.x},${d.y})`)
    })

    // Drag functions
    function dragStarted(event: d3.D3DragEvent<SVGGElement, Node, Node>, d: Node) {
      if (!event.active) forceSimulation.alphaTarget(0.3).restart()
      d.fx = d.x
      d.fy = d.y
    }

    function dragged(event: d3.D3DragEvent<SVGGElement, Node, Node>, d: Node) {
      d.fx = event.x
      d.fy = event.y
    }

    function dragEnded(event: d3.D3DragEvent<SVGGElement, Node, Node>, d: Node) {
      if (!event.active) forceSimulation.alphaTarget(0)
      d.fx = null
      d.fy = null
    }

    // Cleanup
    return () => {
      forceSimulation.stop()
    }
  }, [entities, relationships, selectedEntity, filterType, showLabels, height, onNodeClick])

  const getEntityGroup = (type: string): number => {
    const groups: Record<string, number> = {
      ip: 1,
      user: 2,
      domain: 3,
      hash: 4,
      file: 5
    }
    return groups[type] || 0
  }

  const getNodeColor = (type: string): string => {
    const colors: Record<string, string> = {
      ip: '#3b82f6',      // blue
      user: '#10b981',    // green
      domain: '#8b5cf6',  // purple
      hash: '#f97316',    // orange
      file: '#6b7280'     // gray
    }
    return colors[type] || '#6b7280'
  }

  const getNodeIcon = (type: string): string => {
    const icons: Record<string, string> = {
      ip: '🌐',
      user: '👤',
      domain: '🖥️',
      hash: '#',
      file: '📄'
    }
    return icons[type] || '❓'
  }

  const getRelationshipColor = (type: string): string => {
    const colors: Record<string, string> = {
      'connects_to': '#3b82f6',
      'owns': '#10b981',
      'communicates_with': '#8b5cf6',
      'contains': '#f97316',
      'related_to': '#6b7280'
    }
    return colors[type] || '#6b7280'
  }

  const handleZoomIn = () => {
    if (!svgRef.current) return
    const svg = d3.select(svgRef.current)
    svg.transition().call(
      d3.zoom<SVGSVGElement, unknown>().scaleTo as any,
      zoom * 1.2
    )
  }

  const handleZoomOut = () => {
    if (!svgRef.current) return
    const svg = d3.select(svgRef.current)
    svg.transition().call(
      d3.zoom<SVGSVGElement, unknown>().scaleTo as any,
      zoom * 0.8
    )
  }

  const handleReset = () => {
    if (!svgRef.current) return
    const svg = d3.select(svgRef.current)
    svg.transition().call(
      d3.zoom<SVGSVGElement, unknown>().transform as any,
      d3.zoomIdentity
    )
  }

  const handleRestart = () => {
    if (simulation) {
      simulation.alpha(1).restart()
    }
  }

  const exportGraph = () => {
    if (!svgRef.current) return
    const svgData = new XMLSerializer().serializeToString(svgRef.current)
    const blob = new Blob([svgData], { type: 'image/svg+xml' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'entity-relationship-graph.svg'
    a.click()
    URL.revokeObjectURL(url)
  }

  const relationshipTypes = Array.from(new Set(relationships.map(r => r.type)))

  if (loadingRelationships) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader className="animate-spin text-blue-500" size={48} />
        <span className="ml-4 text-gray-600">Loading relationships...</span>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-lg font-semibold">Entity Relationship Graph</h3>
            <p className="text-sm text-gray-600">
              {entities.size} entities, {relationships.length} relationships
            </p>
          </div>
          <div className="flex gap-2">
            {/* Filter dropdown */}
            <select
              className="px-3 py-1 border rounded text-sm"
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
            >
              <option value="all">All Relationships</option>
              {relationshipTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>

            {/* Toggle labels */}
            <button
              onClick={() => setShowLabels(!showLabels)}
              className={`px-3 py-1 border rounded text-sm ${showLabels ? 'bg-blue-500 text-white' : ''}`}
            >
              Labels
            </button>

            {/* Control buttons */}
            <button
              onClick={handleZoomIn}
              className="p-1 border rounded hover:bg-gray-50"
              title="Zoom In"
            >
              <ZoomIn size={18} />
            </button>
            <button
              onClick={handleZoomOut}
              className="p-1 border rounded hover:bg-gray-50"
              title="Zoom Out"
            >
              <ZoomOut size={18} />
            </button>
            <button
              onClick={handleReset}
              className="p-1 border rounded hover:bg-gray-50"
              title="Reset View"
            >
              <Maximize2 size={18} />
            </button>
            <button
              onClick={handleRestart}
              className="p-1 border rounded hover:bg-gray-50"
              title="Restart Simulation"
            >
              <RefreshCw size={18} />
            </button>
            <button
              onClick={exportGraph}
              className="p-1 border rounded hover:bg-gray-50"
              title="Export Graph"
            >
              <Download size={18} />
            </button>
          </div>
        </div>
      </div>

      {/* Graph Container */}
      <div ref={containerRef} className="flex-1 overflow-hidden">
        <svg ref={svgRef} className="w-full h-full" />
      </div>

      {/* Legend */}
      <div className="border-t p-3">
        <div className="flex gap-4 text-sm">
          <span className="font-medium">Entity Types:</span>
          <span className="flex items-center gap-1">
            <span className="w-3 h-3 rounded-full bg-blue-500"></span> IP
          </span>
          <span className="flex items-center gap-1">
            <span className="w-3 h-3 rounded-full bg-green-500"></span> User
          </span>
          <span className="flex items-center gap-1">
            <span className="w-3 h-3 rounded-full bg-purple-500"></span> Domain
          </span>
          <span className="flex items-center gap-1">
            <span className="w-3 h-3 rounded-full bg-orange-500"></span> Hash
          </span>
          <span className="flex items-center gap-1">
            <span className="w-3 h-3 rounded-full bg-gray-500"></span> File
          </span>
          <span className="ml-auto text-gray-600">
            Zoom: {(zoom * 100).toFixed(0)}%
          </span>
        </div>
      </div>
    </div>
  )
}

export default RelationshipGraph