import axios, { AxiosInstance } from 'axios'

// API client configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || '/api'
const WS_BASE_URL = import.meta.env.VITE_WS_URL || `ws://${window.location.host}`

// Create axios instance with defaults
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for auth
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Token expired, try to refresh
      localStorage.removeItem('access_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Entity API
export const entityAPI = {
  getEntity: (entityId: string) =>
    apiClient.get(`/entities/${entityId}`),

  getEnrichment: (entityId: string) =>
    apiClient.get(`/entities/${entityId}/enrichment`),

  getRelationships: (entityId: string) =>
    apiClient.get(`/entities/${entityId}/relationships`),

  getTimeline: (entityId: string) =>
    apiClient.get(`/entities/${entityId}/timeline`),

  getRiskScore: (entityId: string) =>
    apiClient.get(`/entities/${entityId}/risk`)
}

// Case API
export const caseAPI = {
  getCases: (params?: any) =>
    apiClient.get('/cases', { params }),

  getCase: (caseId: string) =>
    apiClient.get(`/cases/${caseId}`),

  updateCase: (caseId: string, data: any) =>
    apiClient.patch(`/cases/${caseId}`, data),

  assignCase: (caseId: string, assignee: string) =>
    apiClient.post(`/cases/${caseId}/assign`, { assignee }),

  getInvestigationGuide: (caseId: string) =>
    apiClient.get(`/cases/${caseId}/investigation-guide`)
}

// Workflow API
export const workflowAPI = {
  getWorkflows: () =>
    apiClient.get('/workflows'),

  startWorkflow: (workflowType: string, params: any) =>
    apiClient.post('/workflow/start', { workflow_type: workflowType, ...params }),

  getWorkflowStatus: (workflowId: string) =>
    apiClient.get(`/workflow/status/${workflowId}`),

  stopWorkflow: (workflowId: string) =>
    apiClient.post(`/workflow/stop/${workflowId}`)
}

// Pattern API
export const patternAPI = {
  getPatterns: (params?: any) =>
    apiClient.get('/patterns', { params }),

  getPattern: (patternId: string) =>
    apiClient.get(`/patterns/${patternId}`),

  getPatternPerformance: (patternId: string) =>
    apiClient.get(`/patterns/${patternId}/performance`),

  testPattern: (pattern: string, testData: string) =>
    apiClient.post('/patterns/test', { pattern, test_data: testData })
}

// MITRE API
export const mitreAPI = {
  getTechniques: () =>
    apiClient.get('/mitre/techniques'),

  getTechniqueHeatmap: (timeRange: string) =>
    apiClient.get('/mitre/heatmap', { params: { time_range: timeRange } }),

  getTechniqueDetails: (techniqueId: string) =>
    apiClient.get(`/mitre/techniques/${techniqueId}`)
}

// WebSocket connection for real-time updates
export class WebSocketClient {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private listeners: Map<string, Set<Function>> = new Map()

  connect() {
    const token = localStorage.getItem('access_token')
    const wsUrl = `${WS_BASE_URL}/ws${token ? `?token=${token}` : ''}`

    this.ws = new WebSocket(wsUrl)

    this.ws.onopen = () => {
      console.log('WebSocket connected')
      this.reconnectAttempts = 0
      this.emit('connected', null)
    }

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.emit(data.type || 'message', data)
      } catch (error) {
        console.error('WebSocket message parse error:', error)
      }
    }

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error)
      this.emit('error', error)
    }

    this.ws.onclose = () => {
      console.log('WebSocket disconnected')
      this.emit('disconnected', null)
      this.attemptReconnect()
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      setTimeout(() => this.connect(), this.reconnectDelay * this.reconnectAttempts)
    }
  }

  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event)!.add(callback)
  }

  off(event: string, callback: Function) {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      callbacks.delete(callback)
    }
  }

  private emit(event: string, data: any) {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      callbacks.forEach(callback => callback(data))
    }
  }

  send(data: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }
}

export const wsClient = new WebSocketClient()

export default apiClient