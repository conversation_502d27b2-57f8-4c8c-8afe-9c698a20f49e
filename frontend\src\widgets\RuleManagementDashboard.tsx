import React, { useState, useEffect } from 'react'
import { useRuleStore } from '../stores/ruleStore'
import PendingRulesWidget from './PendingRulesWidget'
import RuleLibraryWidget from './RuleLibraryWidget'
import RulePerformanceWidget from './RulePerformanceWidget'
import {
  Shield, Library, BarChart3, Settings, Download, Upload,
  RefreshCw, AlertCircle, CheckCircle, TrendingUp, Search,
  Filter, Grid, List, Layers
} from 'lucide-react'

/**
 * Rule Management Dashboard
 *
 * Purpose: Central command center for all detection rule operations
 *
 * Features:
 * - Unified interface for pending rules, rule library, and performance
 * - Tab navigation with badge counts
 * - Quick stats overview
 * - Global search and filter
 * - Import/Export capabilities
 * - Real-time updates across all sections
 * - Workflow: CTI → Pending → Library → Performance
 */

type DashboardTab = 'pending' | 'library' | 'performance'

interface QuickStats {
  total_rules: number
  active_rules: number
  pending_approval: number
  avg_quality_score: number
  true_positive_rate: number
  false_positive_rate: number
}

export const RuleManagementDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<DashboardTab>('pending')
  const [quickStats, setQuickStats] = useState<QuickStats>({
    total_rules: 0,
    active_rules: 0,
    pending_approval: 0,
    avg_quality_score: 0,
    true_positive_rate: 0,
    false_positive_rate: 0
  })
  const [globalSearch, setGlobalSearch] = useState('')
  const [showGlobalFilters, setShowGlobalFilters] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  const {
    pendingRules,
    rules,
    performanceSummary,
    fetchPendingRules,
    fetchRules,
    fetchPerformanceSummary
  } = useRuleStore()

  // Fetch quick stats on mount
  useEffect(() => {
    fetchQuickStats()
  }, [])

  // Update stats when data changes
  useEffect(() => {
    if (pendingRules && rules && performanceSummary) {
      setQuickStats({
        total_rules: rules.length,
        active_rules: rules.filter(r => r.status === 'active').length,
        pending_approval: pendingRules.length,
        avg_quality_score: calculateAvgQuality(),
        true_positive_rate: performanceSummary?.overall_tp_rate || 0,
        false_positive_rate: performanceSummary?.overall_fp_rate || 0
      })
    }
  }, [pendingRules, rules, performanceSummary])

  const fetchQuickStats = async () => {
    // Fetch all data sources in parallel
    await Promise.all([
      fetchPendingRules(),
      fetchRules(),
      fetchPerformanceSummary('7d')
    ])
  }

  const calculateAvgQuality = () => {
    if (!rules || rules.length === 0) return 0
    const total = rules.reduce((sum, r) => sum + (r.quality_score || 0), 0)
    return Math.round(total / rules.length)
  }

  const handleRefreshAll = async () => {
    setRefreshing(true)
    await fetchQuickStats()
    setRefreshing(false)
  }

  const handleGlobalSearch = (query: string) => {
    setGlobalSearch(query)
    // Propagate search to active tab
    // This will be handled by each widget's own search/filter logic
  }

  const getTabIcon = (tab: DashboardTab) => {
    switch (tab) {
      case 'pending':
        return <Shield size={18} />
      case 'library':
        return <Library size={18} />
      case 'performance':
        return <BarChart3 size={18} />
    }
  }

  const getStatColor = (value: number, type: 'quality' | 'tp' | 'fp') => {
    if (type === 'quality') {
      if (value >= 85) return 'text-green-600'
      if (value >= 70) return 'text-blue-600'
      if (value >= 50) return 'text-yellow-600'
      return 'text-red-600'
    } else if (type === 'tp') {
      if (value >= 0.85) return 'text-green-600'
      if (value >= 0.70) return 'text-blue-600'
      return 'text-yellow-600'
    } else {
      // FP - lower is better
      if (value <= 0.05) return 'text-green-600'
      if (value <= 0.15) return 'text-yellow-600'
      return 'text-red-600'
    }
  }

  return (
    <div className="rule-management-dashboard">
      {/* Dashboard Header */}
      <div className="dashboard-header">
        <div className="header-content">
          <div className="header-title-section">
            <div className="header-icon">
              <Shield size={28} />
            </div>
            <div>
              <h1 className="header-title">Rule Management Center</h1>
              <p className="header-subtitle">
                CTI-driven detection engineering and rule lifecycle management
              </p>
            </div>
          </div>

          <div className="header-actions">
            <button
              onClick={handleRefreshAll}
              disabled={refreshing}
              className="action-btn refresh-btn"
              title="Refresh all data"
            >
              <RefreshCw size={18} className={refreshing ? 'animate-spin' : ''} />
            </button>

            <button
              onClick={() => setShowGlobalFilters(!showGlobalFilters)}
              className={`action-btn filter-btn ${showGlobalFilters ? 'active' : ''}`}
              title="Global filters"
            >
              <Filter size={18} />
            </button>

            <button className="action-btn" title="Import rules">
              <Upload size={18} />
              <span>Import</span>
            </button>

            <button className="action-btn" title="Export rules">
              <Download size={18} />
              <span>Export</span>
            </button>

            <button className="action-btn settings-btn" title="Settings">
              <Settings size={18} />
            </button>
          </div>
        </div>

        {/* Quick Stats Bar */}
        <div className="quick-stats-bar">
          <div className="stat-card">
            <div className="stat-icon total">
              <Layers size={20} />
            </div>
            <div className="stat-content">
              <div className="stat-value">{quickStats.total_rules}</div>
              <div className="stat-label">Total Rules</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon active">
              <CheckCircle size={20} />
            </div>
            <div className="stat-content">
              <div className="stat-value">{quickStats.active_rules}</div>
              <div className="stat-label">Active</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon pending">
              <AlertCircle size={20} />
            </div>
            <div className="stat-content">
              <div className="stat-value">{quickStats.pending_approval}</div>
              <div className="stat-label">Pending Approval</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon quality">
              <TrendingUp size={20} />
            </div>
            <div className="stat-content">
              <div className={`stat-value ${getStatColor(quickStats.avg_quality_score, 'quality')}`}>
                {quickStats.avg_quality_score}/100
              </div>
              <div className="stat-label">Avg Quality</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon tp">
              <CheckCircle size={20} />
            </div>
            <div className="stat-content">
              <div className={`stat-value ${getStatColor(quickStats.true_positive_rate, 'tp')}`}>
                {(quickStats.true_positive_rate * 100).toFixed(1)}%
              </div>
              <div className="stat-label">True Positive Rate</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon fp">
              <AlertCircle size={20} />
            </div>
            <div className="stat-content">
              <div className={`stat-value ${getStatColor(quickStats.false_positive_rate, 'fp')}`}>
                {(quickStats.false_positive_rate * 100).toFixed(1)}%
              </div>
              <div className="stat-label">False Positive Rate</div>
            </div>
          </div>
        </div>

        {/* Global Search/Filter Bar */}
        {showGlobalFilters && (
          <div className="global-filters">
            <div className="search-bar">
              <Search size={18} />
              <input
                type="text"
                placeholder="Search across all rules..."
                value={globalSearch}
                onChange={(e) => handleGlobalSearch(e.target.value)}
                className="search-input"
              />
            </div>
            <div className="filter-quick-actions">
              <button className="filter-chip">All Sources</button>
              <button className="filter-chip">High Quality Only</button>
              <button className="filter-chip">Active Rules</button>
              <button className="filter-chip">Needs Attention</button>
            </div>
          </div>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="tab-navigation">
        <button
          className={`tab ${activeTab === 'pending' ? 'active' : ''}`}
          onClick={() => setActiveTab('pending')}
        >
          <div className="tab-icon">{getTabIcon('pending')}</div>
          <div className="tab-content">
            <span className="tab-label">Pending Rules</span>
            {quickStats.pending_approval > 0 && (
              <span className="tab-badge pending">{quickStats.pending_approval}</span>
            )}
          </div>
        </button>

        <button
          className={`tab ${activeTab === 'library' ? 'active' : ''}`}
          onClick={() => setActiveTab('library')}
        >
          <div className="tab-icon">{getTabIcon('library')}</div>
          <div className="tab-content">
            <span className="tab-label">Rule Library</span>
            <span className="tab-badge library">{quickStats.total_rules}</span>
          </div>
        </button>

        <button
          className={`tab ${activeTab === 'performance' ? 'active' : ''}`}
          onClick={() => setActiveTab('performance')}
        >
          <div className="tab-icon">{getTabIcon('performance')}</div>
          <div className="tab-content">
            <span className="tab-label">Performance</span>
            {quickStats.false_positive_rate > 0.15 && (
              <span className="tab-badge alert">⚠</span>
            )}
          </div>
        </button>
      </div>

      {/* Tab Content */}
      <div className="tab-content-area">
        {activeTab === 'pending' && <PendingRulesWidget />}
        {activeTab === 'library' && <RuleLibraryWidget />}
        {activeTab === 'performance' && <RulePerformanceWidget />}
      </div>

      {/* Workflow Help Panel (Optional) */}
      <div className="workflow-hint">
        <div className="workflow-steps">
          <span className="workflow-step">1. CTI Indicators</span>
          <span className="workflow-arrow">→</span>
          <span className="workflow-step active">2. Pending Rules</span>
          <span className="workflow-arrow">→</span>
          <span className="workflow-step">3. Rule Library</span>
          <span className="workflow-arrow">→</span>
          <span className="workflow-step">4. Performance Tracking</span>
        </div>
      </div>
    </div>
  )
}

export default RuleManagementDashboard
