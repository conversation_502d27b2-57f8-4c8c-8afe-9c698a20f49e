"""
Multi-SIEM Query Translation Module
Based on v0.7's proven cross-platform query support
NOW USES SIEM REGISTRY for extensibility!

Supports: <PERSON><PERSON><PERSON><PERSON>, QRadar, Elastic, Sentinel, Chronicle, CrowdStrike, Wazuh, OpenSearch
Add new SIEMs via YAML configuration - no code changes needed!
"""
import logging
import json
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path

# Import SIEM Registry
from siem_registry import SIEMRegistry, get_registry

logger = logging.getLogger(__name__)


class SIEMPlatform(Enum):
    """Supported SIEM platforms - now dynamically loaded from registry!"""
    SPLUNK = "splunk"
    QRADAR = "qradar"
    ELASTIC = "elastic"
    SENTINEL = "sentinel"
    CHRONICLE = "chronicle"
    CROWDSTRIKE = "crowdstrike"
    WAZUH = "wazuh"  # NEW: Added via YAML config
    OPENSEARCH = "opensearch"  # NEW: Added via YAML config
    GENERIC = "generic"


@dataclass
class QueryComponent:
    """Represents a component of a security query"""
    field: str
    operator: str
    value: Any
    logical_operator: Optional[str] = "AND"
    is_negated: bool = False
    is_regex: bool = False
    is_wildcard: bool = False


class QueryTranslator:
    """
    Translates security queries between different SIEM platforms
    Based on v0.7's proven multi-vendor support

    NOW USES SIEM REGISTRY - add new platforms via YAML!
    """

    def __init__(self, registry: Optional[SIEMRegistry] = None):
        # Use provided registry or get singleton
        self.registry = registry or get_registry()

        logger.info(f"QueryTranslator initialized with {len(self.registry.list_siems())} SIEM platforms")

        # DEPRECATED: Hardcoded mappings below kept for backward compatibility
        # New code should use self.registry.get_field_mapping() instead
        # Field mappings for different SIEM platforms
        self.field_mappings = {
            'source_ip': {
                SIEMPlatform.SPLUNK: 'src_ip',
                SIEMPlatform.QRADAR: 'sourceip',
                SIEMPlatform.ELASTIC: 'source.ip',
                SIEMPlatform.SENTINEL: 'SourceIP',
                SIEMPlatform.CHRONICLE: 'principal.ip',
                SIEMPlatform.CROWDSTRIKE: 'LocalAddressIP4'
            },
            'destination_ip': {
                SIEMPlatform.SPLUNK: 'dest_ip',
                SIEMPlatform.QRADAR: 'destinationip',
                SIEMPlatform.ELASTIC: 'destination.ip',
                SIEMPlatform.SENTINEL: 'DestinationIP',
                SIEMPlatform.CHRONICLE: 'target.ip',
                SIEMPlatform.CROWDSTRIKE: 'RemoteAddressIP4'
            },
            'username': {
                SIEMPlatform.SPLUNK: 'user',
                SIEMPlatform.QRADAR: 'username',
                SIEMPlatform.ELASTIC: 'user.name',
                SIEMPlatform.SENTINEL: 'Account',
                SIEMPlatform.CHRONICLE: 'principal.user.userid',
                SIEMPlatform.CROWDSTRIKE: 'UserName'
            },
            'process_name': {
                SIEMPlatform.SPLUNK: 'process_name',
                SIEMPlatform.QRADAR: 'processname',
                SIEMPlatform.ELASTIC: 'process.name',
                SIEMPlatform.SENTINEL: 'ProcessName',
                SIEMPlatform.CHRONICLE: 'target.process.file.full_path',
                SIEMPlatform.CROWDSTRIKE: 'ImageFileName'
            },
            'file_hash': {
                SIEMPlatform.SPLUNK: 'file_hash',
                SIEMPlatform.QRADAR: 'filehash',
                SIEMPlatform.ELASTIC: 'file.hash.sha256',
                SIEMPlatform.SENTINEL: 'FileHash',
                SIEMPlatform.CHRONICLE: 'target.file.sha256',
                SIEMPlatform.CROWDSTRIKE: 'SHA256HashData'
            },
            'event_id': {
                SIEMPlatform.SPLUNK: 'EventCode',
                SIEMPlatform.QRADAR: 'EventID',
                SIEMPlatform.ELASTIC: 'event.code',
                SIEMPlatform.SENTINEL: 'EventID',
                SIEMPlatform.CHRONICLE: 'metadata.event_type',
                SIEMPlatform.CROWDSTRIKE: 'EventType'
            },
            'hostname': {
                SIEMPlatform.SPLUNK: 'ComputerName',
                SIEMPlatform.QRADAR: 'hostname',
                SIEMPlatform.ELASTIC: 'host.name',
                SIEMPlatform.SENTINEL: 'Computer',
                SIEMPlatform.CHRONICLE: 'principal.hostname',
                SIEMPlatform.CROWDSTRIKE: 'ComputerName'
            },
            'port': {
                SIEMPlatform.SPLUNK: 'dest_port',
                SIEMPlatform.QRADAR: 'destinationport',
                SIEMPlatform.ELASTIC: 'destination.port',
                SIEMPlatform.SENTINEL: 'DestinationPort',
                SIEMPlatform.CHRONICLE: 'target.port',
                SIEMPlatform.CROWDSTRIKE: 'RemotePort'
            }
        }

        # Time field mappings
        self.time_fields = {
            SIEMPlatform.SPLUNK: '_time',
            SIEMPlatform.QRADAR: 'starttime',
            SIEMPlatform.ELASTIC: '@timestamp',
            SIEMPlatform.SENTINEL: 'TimeGenerated',
            SIEMPlatform.CHRONICLE: 'metadata.event_timestamp',
            SIEMPlatform.CROWDSTRIKE: 'ProcessStartTime'
        }

        # Operator mappings
        self.operator_mappings = {
            '=': {
                SIEMPlatform.SPLUNK: '=',
                SIEMPlatform.QRADAR: '=',
                SIEMPlatform.ELASTIC: ':',
                SIEMPlatform.SENTINEL: '==',
                SIEMPlatform.CHRONICLE: '=',
                SIEMPlatform.CROWDSTRIKE: '='
            },
            '!=': {
                SIEMPlatform.SPLUNK: '!=',
                SIEMPlatform.QRADAR: '!=',
                SIEMPlatform.ELASTIC: 'NOT',
                SIEMPlatform.SENTINEL: '!=',
                SIEMPlatform.CHRONICLE: '!=',
                SIEMPlatform.CROWDSTRIKE: '!='
            },
            'contains': {
                SIEMPlatform.SPLUNK: 'LIKE',
                SIEMPlatform.QRADAR: 'ILIKE',
                SIEMPlatform.ELASTIC: '*value*',
                SIEMPlatform.SENTINEL: 'contains',
                SIEMPlatform.CHRONICLE: 'LIKE',
                SIEMPlatform.CROWDSTRIKE: 'ContainsAny'
            },
            'regex': {
                SIEMPlatform.SPLUNK: 'rex',
                SIEMPlatform.QRADAR: 'MATCHES',
                SIEMPlatform.ELASTIC: '/.*/​',
                SIEMPlatform.SENTINEL: 'matches regex',
                SIEMPlatform.CHRONICLE: 'REGEXP',
                SIEMPlatform.CROWDSTRIKE: 'RegexMatch'
            }
        }

    def parse_generic_query(self, query: str) -> List[QueryComponent]:
        """
        Parse a generic security query into components
        Supports common query patterns from multiple platforms
        """
        components = []

        # Remove extra whitespace
        query = ' '.join(query.split())

        # Split by logical operators (AND, OR)
        # Pattern matches field operator value combinations
        pattern = r'(\w+)\s*(=|!=|contains|regex|>|<|>=|<=)\s*(["\']?)([^"\'\s]+)\3'

        matches = re.finditer(pattern, query, re.IGNORECASE)

        for match in matches:
            field = match.group(1)
            operator = match.group(2)
            value = match.group(4)

            # Determine logical operator (simplified - looks ahead)
            logical_op = "AND"  # Default
            remaining = query[match.end():]
            if remaining.strip().startswith('OR'):
                logical_op = "OR"

            component = QueryComponent(
                field=field,
                operator=operator,
                value=value,
                logical_operator=logical_op,
                is_regex='regex' in operator.lower(),
                is_wildcard='*' in value or '?' in value
            )
            components.append(component)

        return components

    def translate_query(self, query: str, source_platform: SIEMPlatform,
                       target_platform: SIEMPlatform) -> str:
        """
        Translate a query from source platform to target platform
        """
        if source_platform == target_platform:
            return query

        # Parse the query based on source platform
        if source_platform == SIEMPlatform.GENERIC:
            components = self.parse_generic_query(query)
        else:
            components = self._parse_platform_query(query, source_platform)

        # Translate components to target platform
        translated = self._build_platform_query(components, target_platform)

        return translated

    def _parse_platform_query(self, query: str, platform: SIEMPlatform) -> List[QueryComponent]:
        """Parse platform-specific query into components"""
        components = []

        if platform == SIEMPlatform.SPLUNK:
            components = self._parse_splunk_query(query)
        elif platform == SIEMPlatform.ELASTIC:
            components = self._parse_elastic_query(query)
        elif platform == SIEMPlatform.SENTINEL:
            components = self._parse_kql_query(query)
        elif platform == SIEMPlatform.QRADAR:
            components = self._parse_aql_query(query)
        else:
            # Fallback to generic parser
            components = self.parse_generic_query(query)

        return components

    def _parse_splunk_query(self, query: str) -> List[QueryComponent]:
        """Parse Splunk SPL query"""
        components = []

        # Basic SPL pattern matching
        # Example: index=security src_ip=*********** dest_port=445
        parts = query.split()

        for part in parts:
            if '=' in part and not part.startswith('|'):
                field, value = part.split('=', 1)
                # Skip index and sourcetype
                if field not in ['index', 'sourcetype', 'source']:
                    components.append(QueryComponent(
                        field=field,
                        operator='=',
                        value=value.strip('"'),
                        logical_operator='AND'
                    ))

        return components

    def _parse_elastic_query(self, query: str) -> List[QueryComponent]:
        """Parse Elasticsearch/Lucene query"""
        components = []

        # Basic Lucene pattern matching
        # Example: source.ip:*********** AND destination.port:445
        parts = re.split(r'\s+(AND|OR)\s+', query)

        for i, part in enumerate(parts):
            if ':' in part and part not in ['AND', 'OR']:
                field, value = part.split(':', 1)

                # Determine if it's a NOT query
                is_negated = field.startswith('NOT ')
                if is_negated:
                    field = field[4:]

                # Determine logical operator
                logical_op = 'AND'
                if i > 0 and parts[i-1] in ['AND', 'OR']:
                    logical_op = parts[i-1]

                components.append(QueryComponent(
                    field=field.strip(),
                    operator='=' if not is_negated else '!=',
                    value=value.strip('"'),
                    logical_operator=logical_op,
                    is_negated=is_negated
                ))

        return components

    def _parse_kql_query(self, query: str) -> List[QueryComponent]:
        """Parse Kusto Query Language (KQL) for Sentinel"""
        components = []

        # Basic KQL pattern
        # Example: SecurityEvent | where EventID == 4625 and TargetAccount == "admin"

        # Extract where clause
        where_match = re.search(r'where\s+(.+)', query, re.IGNORECASE)
        if where_match:
            where_clause = where_match.group(1)

            # Parse conditions
            conditions = re.split(r'\s+(and|or)\s+', where_clause, flags=re.IGNORECASE)

            for i, condition in enumerate(conditions):
                if condition.lower() not in ['and', 'or']:
                    # Parse field operator value
                    match = re.match(r'(\w+)\s*(==|!=|contains|matches)\s*["\']?([^"\']+)["\']?', condition)
                    if match:
                        logical_op = 'AND'
                        if i > 0 and conditions[i-1].lower() in ['and', 'or']:
                            logical_op = conditions[i-1].upper()

                        components.append(QueryComponent(
                            field=match.group(1),
                            operator=match.group(2).replace('==', '='),
                            value=match.group(3),
                            logical_operator=logical_op
                        ))

        return components

    def _parse_aql_query(self, query: str) -> List[QueryComponent]:
        """Parse Ariel Query Language (AQL) for QRadar"""
        components = []

        # Basic AQL pattern
        # Example: SELECT * FROM events WHERE sourceip='***********' AND eventid='4625'

        # Extract WHERE clause
        where_match = re.search(r'WHERE\s+(.+)', query, re.IGNORECASE)
        if where_match:
            where_clause = where_match.group(1)

            # Parse conditions
            conditions = re.split(r'\s+(AND|OR)\s+', where_clause, flags=re.IGNORECASE)

            for i, condition in enumerate(conditions):
                if condition.upper() not in ['AND', 'OR']:
                    # Parse field operator value
                    match = re.match(r'(\w+)\s*(=|!=|ILIKE)\s*["\']?([^"\']+)["\']?', condition)
                    if match:
                        logical_op = 'AND'
                        if i > 0 and conditions[i-1].upper() in ['AND', 'OR']:
                            logical_op = conditions[i-1].upper()

                        components.append(QueryComponent(
                            field=match.group(1),
                            operator=match.group(2) if match.group(2) != 'ILIKE' else 'contains',
                            value=match.group(3),
                            logical_operator=logical_op
                        ))

        return components

    def _build_platform_query(self, components: List[QueryComponent],
                             platform: SIEMPlatform) -> str:
        """Build platform-specific query from components"""

        if platform == SIEMPlatform.SPLUNK:
            return self._build_splunk_query(components)
        elif platform == SIEMPlatform.ELASTIC:
            return self._build_elastic_query(components)
        elif platform == SIEMPlatform.SENTINEL:
            return self._build_kql_query(components)
        elif platform == SIEMPlatform.QRADAR:
            return self._build_aql_query(components)
        elif platform == SIEMPlatform.CHRONICLE:
            return self._build_chronicle_query(components)
        elif platform == SIEMPlatform.CROWDSTRIKE:
            return self._build_crowdstrike_query(components)
        elif platform == SIEMPlatform.WAZUH:
            return self._build_wazuh_query(components)
        elif platform == SIEMPlatform.OPENSEARCH:
            return self._build_opensearch_query(components)
        else:
            return self._build_generic_query(components)

    def _build_splunk_query(self, components: List[QueryComponent]) -> str:
        """Build Splunk SPL query"""
        query_parts = ['search']

        for i, comp in enumerate(components):
            # Translate field name
            field = self._translate_field(comp.field, SIEMPlatform.SPLUNK)

            # Build condition
            if comp.operator == '=':
                condition = f'{field}="{comp.value}"'
            elif comp.operator == '!=':
                condition = f'NOT {field}="{comp.value}"'
            elif comp.operator == 'contains':
                condition = f'{field}="*{comp.value}*"'
            elif comp.operator == 'regex':
                condition = f'| rex field={field} "{comp.value}"'
            else:
                condition = f'{field}{comp.operator}"{comp.value}"'

            # Add logical operator if not first component
            if i > 0:
                if comp.logical_operator == 'OR':
                    query_parts.append('OR')

            query_parts.append(condition)

        return ' '.join(query_parts)

    def _build_elastic_query(self, components: List[QueryComponent]) -> str:
        """Build Elasticsearch query"""
        query_parts = []

        for i, comp in enumerate(components):
            # Translate field name
            field = self._translate_field(comp.field, SIEMPlatform.ELASTIC)

            # Build condition
            if comp.operator == '=':
                condition = f'{field}:"{comp.value}"'
            elif comp.operator == '!=':
                condition = f'NOT {field}:"{comp.value}"'
            elif comp.operator == 'contains':
                condition = f'{field}:*{comp.value}*'
            elif comp.operator == 'regex':
                condition = f'{field}:/{comp.value}/'
            else:
                condition = f'{field}:{comp.value}'

            # Add logical operator if not first component
            if i > 0:
                query_parts.append(comp.logical_operator)

            query_parts.append(condition)

        return ' '.join(query_parts)

    def _build_kql_query(self, components: List[QueryComponent]) -> str:
        """Build KQL query for Sentinel"""
        table = "SecurityEvent"  # Default table
        conditions = []

        for comp in components:
            # Translate field name
            field = self._translate_field(comp.field, SIEMPlatform.SENTINEL)

            # Build condition
            if comp.operator == '=':
                condition = f'{field} == "{comp.value}"'
            elif comp.operator == '!=':
                condition = f'{field} != "{comp.value}"'
            elif comp.operator == 'contains':
                condition = f'{field} contains "{comp.value}"'
            elif comp.operator == 'regex':
                condition = f'{field} matches regex "{comp.value}"'
            else:
                condition = f'{field} {comp.operator} "{comp.value}"'

            conditions.append((comp.logical_operator.lower(), condition))

        # Build where clause
        where_clause = conditions[0][1] if conditions else ""
        for logical_op, condition in conditions[1:]:
            where_clause += f' {logical_op} {condition}'

        return f'{table} | where {where_clause}'

    def _build_aql_query(self, components: List[QueryComponent]) -> str:
        """Build AQL query for QRadar"""
        conditions = []

        for comp in components:
            # Translate field name
            field = self._translate_field(comp.field, SIEMPlatform.QRADAR)

            # Build condition
            if comp.operator == '=':
                condition = f"{field}='{comp.value}'"
            elif comp.operator == '!=':
                condition = f"{field}!='{comp.value}'"
            elif comp.operator == 'contains':
                condition = f"{field} ILIKE '%{comp.value}%'"
            elif comp.operator == 'regex':
                condition = f"{field} MATCHES '{comp.value}'"
            else:
                condition = f"{field}{comp.operator}'{comp.value}'"

            conditions.append((comp.logical_operator, condition))

        # Build where clause
        where_clause = conditions[0][1] if conditions else ""
        for logical_op, condition in conditions[1:]:
            where_clause += f' {logical_op} {condition}'

        return f'SELECT * FROM events WHERE {where_clause} LAST 1 HOURS'

    def _build_chronicle_query(self, components: List[QueryComponent]) -> str:
        """Build Chronicle YARA-L query"""
        conditions = []

        for comp in components:
            # Translate field name
            field = self._translate_field(comp.field, SIEMPlatform.CHRONICLE)

            # Build condition
            if comp.operator == '=':
                condition = f'{field} = "{comp.value}"'
            elif comp.operator == '!=':
                condition = f'{field} != "{comp.value}"'
            elif comp.operator == 'contains':
                condition = f'{field} =~ ".*{comp.value}.*"'
            elif comp.operator == 'regex':
                condition = f'{field} =~ "{comp.value}"'
            else:
                condition = f'{field} {comp.operator} "{comp.value}"'

            conditions.append((comp.logical_operator.lower(), condition))

        # Build rule
        rule = "rule detection_rule {\n  meta:\n    description = \"Translated query\"\n  events:\n"

        # Add conditions
        rule += "    $e.metadata.event_type = \"NETWORK_CONNECTION\"\n"
        for logical_op, condition in conditions:
            rule += f"    {logical_op} {condition}\n"

        rule += "  condition:\n    $e\n}"

        return rule

    def _build_crowdstrike_query(self, components: List[QueryComponent]) -> str:
        """Build CrowdStrike Falcon query"""
        query_parts = []

        for i, comp in enumerate(components):
            # Translate field name
            field = self._translate_field(comp.field, SIEMPlatform.CROWDSTRIKE)

            # Build condition
            if comp.operator == '=':
                condition = f'{field}:"{comp.value}"'
            elif comp.operator == '!=':
                condition = f'-{field}:"{comp.value}"'
            elif comp.operator == 'contains':
                condition = f'{field}:*{comp.value}*'
            elif comp.operator == 'regex':
                condition = f'{field}~"{comp.value}"'
            else:
                condition = f'{field}:{comp.value}'

            # Add logical operator
            if i > 0:
                if comp.logical_operator == 'OR':
                    query_parts.append('OR')
                else:
                    query_parts.append('+')  # AND in CrowdStrike

            query_parts.append(condition)

        return ' '.join(query_parts)

    def _build_generic_query(self, components: List[QueryComponent]) -> str:
        """Build generic query format"""
        query_parts = []

        for i, comp in enumerate(components):
            # Build condition
            if comp.operator == 'contains':
                condition = f'{comp.field} CONTAINS "{comp.value}"'
            elif comp.operator == 'regex':
                condition = f'{comp.field} MATCHES "{comp.value}"'
            else:
                condition = f'{comp.field} {comp.operator} "{comp.value}"'

            # Add logical operator
            if i > 0:
                query_parts.append(comp.logical_operator)

            query_parts.append(condition)

        return ' '.join(query_parts)

    def _build_wazuh_query(self, components: List[QueryComponent]) -> str:
        """Build Wazuh query (OpenSearch DSL / Lucene)

        Wazuh uses OpenSearch backend, supports both Lucene and DSL
        """
        # Use Lucene syntax (simpler for basic queries)
        query_parts = []

        for i, comp in enumerate(components):
            # Translate field name
            field = self._translate_field(comp.field, SIEMPlatform.WAZUH)

            # Build condition
            if comp.operator == '=':
                condition = f'{field}:"{comp.value}"'
            elif comp.operator == '!=':
                condition = f'NOT {field}:"{comp.value}"'
            elif comp.operator == 'contains':
                condition = f'{field}:*{comp.value}*'
            elif comp.operator == 'regex':
                condition = f'{field}:/{comp.value}/'
            else:
                condition = f'{field}:{comp.value}'

            # Add logical operator
            if i > 0:
                query_parts.append(comp.logical_operator)

            query_parts.append(condition)

        return ' '.join(query_parts)

    def _build_opensearch_query(self, components: List[QueryComponent]) -> str:
        """Build OpenSearch query (DSL / Lucene)

        OpenSearch is AWS fork of Elasticsearch, similar syntax
        """
        # Use Lucene syntax (simpler for basic queries)
        query_parts = []

        for i, comp in enumerate(components):
            # Translate field name
            field = self._translate_field(comp.field, SIEMPlatform.OPENSEARCH)

            # Build condition
            if comp.operator == '=':
                condition = f'{field}:"{comp.value}"'
            elif comp.operator == '!=':
                condition = f'NOT {field}:"{comp.value}"'
            elif comp.operator == 'contains':
                condition = f'{field}:*{comp.value}*'
            elif comp.operator == 'regex':
                condition = f'{field}:/{comp.value}/'
            else:
                condition = f'{field}:{comp.value}'

            # Add logical operator
            if i > 0:
                query_parts.append(comp.logical_operator)

            query_parts.append(condition)

        return ' '.join(query_parts)

    def _translate_field(self, field: str, platform: SIEMPlatform) -> str:
        """Translate field name to platform-specific name

        NOW USES SIEM REGISTRY - tries registry first, falls back to hardcoded mappings
        """
        # Try registry first (supports all platforms including new ones like Wazuh)
        siem_name = platform.value if platform != SIEMPlatform.GENERIC else None

        if siem_name:
            # Try direct field mapping from registry
            registry_field = self.registry.get_field_mapping(siem_name, field)
            if registry_field:
                return registry_field

            # Try lowercase field name
            registry_field = self.registry.get_field_mapping(siem_name, field.lower())
            if registry_field:
                return registry_field

        # Fallback to hardcoded mappings for backward compatibility
        for generic_field, mappings in self.field_mappings.items():
            if field.lower() == generic_field or field in mappings.values():
                return mappings.get(platform, field)

        # Return original field if no mapping found
        return field

    def generate_detection_rule(self, ioc_type: str, ioc_value: str,
                               platform: SIEMPlatform) -> Dict[str, str]:
        """
        Generate detection rule for specific IOC type and platform
        This implements v0.7's CTI-to-rule capability
        """
        rules = {}

        # Define IOC type to field mapping
        ioc_field_map = {
            'ip': 'source_ip',
            'domain': 'dns_query',
            'hash': 'file_hash',
            'email': 'sender_address',
            'url': 'url_path',
            'filename': 'file_name',
            'registry': 'registry_path'
        }

        field = ioc_field_map.get(ioc_type, 'value')

        # Create generic query
        generic_query = f'{field}={ioc_value}'

        # Generate rules for all platforms
        for platform in SIEMPlatform:
            if platform != SIEMPlatform.GENERIC:
                rules[platform.value] = self.translate_query(
                    generic_query,
                    SIEMPlatform.GENERIC,
                    platform
                )

        return rules


def test_query_translator():
    """Test query translation between platforms"""
    translator = QueryTranslator()

    print("Query Translation Tests")
    print("=" * 60)

    # Test 1: Generic to multiple platforms
    generic_query = "source_ip=*********** AND destination_port=445 AND username=admin"
    print(f"\nGeneric Query: {generic_query}")
    print("-" * 40)

    platforms_to_test = [
        SIEMPlatform.SPLUNK,
        SIEMPlatform.ELASTIC,
        SIEMPlatform.SENTINEL,
        SIEMPlatform.WAZUH,  # NEW
        SIEMPlatform.OPENSEARCH  # NEW
    ]

    for platform in platforms_to_test:
        translated = translator.translate_query(
            generic_query,
            SIEMPlatform.GENERIC,
            platform
        )
        print(f"{platform.value:15}: {translated}")

    # Test 2: Splunk to Elastic
    splunk_query = "search src_ip=******** dest_port=22 user=root"
    print(f"\n\nSplunk Query: {splunk_query}")
    elastic_query = translator.translate_query(
        splunk_query,
        SIEMPlatform.SPLUNK,
        SIEMPlatform.ELASTIC
    )
    print(f"Elastic Query: {elastic_query}")

    # Test 3: IOC to detection rules
    print("\n\nIOC to Detection Rules")
    print("-" * 40)

    rules = translator.generate_detection_rule('ip', '*************', SIEMPlatform.SPLUNK)
    for platform, rule in rules.items():
        print(f"{platform:12}: {rule[:80]}..." if len(rule) > 80 else f"{platform:12}: {rule}")


if __name__ == "__main__":
    test_query_translator()