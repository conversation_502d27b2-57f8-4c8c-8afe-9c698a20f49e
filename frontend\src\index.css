@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #3b82f6;
  --primary-dark: #2563eb;
  --secondary: #10b981;
  --danger: #ef4444;
  --warning: #f59e0b;
  --info: #06b6d4;
  --dark: #1f2937;
  --light: #f9fafb;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* AG-Grid Theme Overrides */
.ag-theme-alpine {
  --ag-header-background-color: var(--light);
  --ag-odd-row-background-color: #fafafa;
}

.ag-theme-alpine-dark {
  --ag-header-background-color: var(--dark);
}

/* FlexLayout Theme Overrides */
.flexlayout__layout {
  background: var(--light);
}

.flexlayout__tab {
  background: white;
  border-radius: 4px 4px 0 0;
}

.flexlayout__tab_button--selected {
  background: var(--primary);
  color: white;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}