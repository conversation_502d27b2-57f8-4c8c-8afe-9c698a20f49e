"""
Debug script to capture raw Gemini Pro response
This will show exactly what format <PERSON> is returning
"""

import asyncio
import os
import json
from datetime import datetime

# Use google-genai package (same as ai_models.py)
from google import genai
from google.genai import types

async def test_gemini_raw():
    """Test Gemini Pro with simple Sigma rule and capture raw response"""

    # Get API key
    api_key = os.getenv('GOOGLE_API_KEY')
    if not api_key:
        print("ERROR: GOOGLE_API_KEY not found in environment")
        return

    print(f"API Key found: {api_key[:8]}...")

    # Simple Sigma rule
    sigma_rule = {
        'title': 'Suspicious PowerShell Encoded Command',
        'description': 'Detects PowerShell execution with encoded commands',
        'tags': ['attack.t1059.001', 'attack.execution'],
        'level': 'high',
        'detection': {
            'selection': {
                'Image|endswith': 'powershell.exe',
                'CommandLine|contains': ['-enc', '-e ', '-EncodedCommand']
            },
            'condition': 'selection'
        }
    }

    # Build prompt
    prompt = f"""# Security Detection Rule Enhancement Task

## Rule to Enhance
**Title**: {sigma_rule['title']}
**Description**: {sigma_rule['description']}
**Severity**: {sigma_rule['level']}
**Tags**: {', '.join(sigma_rule['tags'])}

**Detection Logic**:
```yaml
{json.dumps(sigma_rule['detection'], indent=2)}
```

## Enhancement Requirements

Please analyze this detection rule and provide enhancements in JSON format:

```json
{{
  "evasion_variants": [
    {{
      "technique": "Name of evasion technique",
      "description": "How attackers use this",
      "detection_addition": "Additional Sigma detection logic",
      "confidence": 0.85
    }}
  ],
  "false_positive_filters": [
    {{
      "source": "Legitimate source of FP",
      "filter_logic": "Sigma filter logic to exclude",
      "reason": "Why this is a false positive"
    }}
  ],
  "platform_optimizations": {{}},
  "missing_context": [],
  "overall_assessment": {{
    "original_quality": 0.75,
    "enhanced_quality": 0.90,
    "improvement_summary": "Brief summary"
  }}
}}
```

Provide ONLY the JSON response, no additional text.
"""

    print("\n" + "="*80)
    print("CALLING GEMINI 2.5 PRO")
    print("="*80)

    try:
        # Call Gemini using google-genai SDK (same as ai_models.py)
        client = genai.Client(api_key=api_key)

        contents = [
            types.Content(
                role="user",
                parts=[types.Part.from_text(text=prompt)]
            )
        ]

        config = types.GenerateContentConfig(
            temperature=0.1,
            max_output_tokens=32768
        )

        start_time = datetime.now()
        response = client.models.generate_content(
            model="gemini-2.5-pro",
            contents=contents,
            config=config
        )
        duration = (datetime.now() - start_time).total_seconds()

        print(f"\nResponse received in {duration:.2f}s")
        print("\n" + "="*80)
        print("RAW RESPONSE TEXT:")
        print("="*80)
        print(response.text)
        print("\n" + "="*80)

        # Try to parse as JSON
        print("\nATTEMPTING JSON PARSE:")
        print("="*80)

        try:
            parsed = json.loads(response.text)
            print("✅ Successfully parsed as JSON!")
            print(json.dumps(parsed, indent=2)[:500])
        except json.JSONDecodeError as e:
            print(f"❌ JSON parse failed: {e}")
            print(f"First character: {repr(response.text[0])}")
            print(f"First 100 chars: {response.text[:100]}")

    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_gemini_raw())
