"""
Test Elastic Context Plugin and Use Case Analysis

This script tests:
1. Elastic plugin connection to Elastic Cloud
2. Query for hostnames/IPs
3. Entity extraction from Elastic results
4. Use case analysis on extracted context
5. Persistence of analysis results
"""

import asyncio
import json
import sys
from redis import Redis
from uuid import uuid4
from datetime import datetime

# Redis configuration
REDIS_HOST = 'localhost'
REDIS_PORT = 6380


async def test_elastic_context_and_use_case():
    """Test complete Elastic context → use case analysis flow"""

    redis_client = Redis(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)
    request_id = str(uuid4())

    print("\n" + "="*80)
    print("TESTING ELASTIC CONTEXT PLUGIN + USE CASE ANALYSIS")
    print("="*80)

    # Step 1: Pull context from Elastic
    print("\n[STEP 1] Pulling context from Elastic...")
    print(f"Request ID: {request_id}")

    query_data = {
        'request_id': request_id,
        'query': {
            'query_type': 'ip',  # Can be: hostname, ip, user, hash, domain, process
            'query_value': '192.168',  # Wildcard search
            'categories': ['ASSET', 'DETECTION', 'NETWORK'],
            'time_range_hours': 24
        }
    }

    print(f"Query: {json.dumps(query_data['query'], indent=2)}")

    # Publish request to ingestion engine
    redis_client.publish('ingestion.pull_context', json.dumps(query_data))
    print("[OK] Published context request to ingestion.pull_context")

    # Subscribe to response
    pubsub = redis_client.pubsub()
    response_channel = f'ingestion.context_response.{request_id}'
    pubsub.subscribe(response_channel)
    print(f"[OK] Subscribed to {response_channel}")

    # Wait for response (timeout 30 seconds)
    print("\nWaiting for Elastic plugin response...")
    context_results = None
    timeout = 30
    start_time = asyncio.get_event_loop().time()

    for message in pubsub.listen():
        if asyncio.get_event_loop().time() - start_time > timeout:
            print("[FAIL] TIMEOUT: No response from Elastic plugin")
            break

        if message['type'] == 'message':
            data = json.loads(message['data'])
            print(f"\n[OK] Received context response!")
            print(f"Status: {data.get('status')}")

            if data.get('status') == 'success':
                context_results = data.get('context_results', {})
                print(f"\nContext Sources: {list(context_results.keys())}")

                # Show summary of results
                total_results = 0
                for source, results in context_results.items():
                    count = len(results) if isinstance(results, list) else 0
                    total_results += count
                    print(f"  - {source}: {count} results")

                print(f"\nTotal Results: {total_results}")

                # Show sample data
                if total_results > 0:
                    print("\n[Sample Result]")
                    for source, results in context_results.items():
                        if results:
                            sample = results[0]
                            print(f"\nSource: {source}")
                            print(f"Category: {sample.get('category')}")
                            print(f"Confidence: {sample.get('confidence')}")
                            print(f"Data keys: {list(sample.get('data', {}).keys())}")
                            break
            else:
                error = data.get('error', 'Unknown error')
                print(f"[FAIL] Error: {error}")

            break

    pubsub.close()

    if not context_results:
        print("\n[FAIL] FAILED: No context results received")
        return False

    # Step 2: Test Use Case Analysis
    print("\n" + "="*80)
    print("[STEP 2] Testing Use Case Analysis")
    print("="*80)

    analysis_request_id = str(uuid4())

    analysis_data = {
        'request_id': analysis_request_id,
        'query_context': {
            'type': query_data['query']['query_type'],
            'value': query_data['query']['query_value'],
            'time_range_hours': 24
        },
        'context_results': context_results
    }

    print(f"Analysis Request ID: {analysis_request_id}")

    # Publish to use case analyzer
    redis_client.publish('intelligence.analyze_use_case', json.dumps(analysis_data))
    print("[OK] Published to intelligence.analyze_use_case")

    # Subscribe to response
    pubsub2 = redis_client.pubsub()
    analysis_response_channel = f'intelligence.use_case_response.{analysis_request_id}'
    pubsub2.subscribe(analysis_response_channel)
    print(f"[OK] Subscribed to {analysis_response_channel}")

    # Wait for analysis response
    print("\nWaiting for use case analysis...")
    analysis_results = None
    start_time = asyncio.get_event_loop().time()

    for message in pubsub2.listen():
        if asyncio.get_event_loop().time() - start_time > timeout:
            print("[FAIL] TIMEOUT: No response from use case analyzer")
            break

        if message['type'] == 'message':
            data = json.loads(message['data'])
            print(f"\n[OK] Received analysis response!")
            print(f"Status: {data.get('status')}")

            if data.get('status') == 'success':
                analysis_results = data.get('analysis', {})

                # Display analysis results
                print("\n[USE CASE ANALYSIS RESULTS]")
                print(f"Risk Score: {analysis_results.get('risk_score', 0)}/100")

                findings = analysis_results.get('findings', [])
                print(f"\nFindings: {len(findings)}")
                for i, finding in enumerate(findings[:5], 1):  # Show first 5
                    print(f"\n  {i}. {finding.get('use_case', 'Unknown')}")
                    print(f"     Severity: {finding.get('severity', 'unknown')}")
                    print(f"     Confidence: {finding.get('confidence', 0):.2f}")
                    print(f"     Method: {finding.get('method', 'unknown')}")

                recommendations = analysis_results.get('recommendations', [])
                if recommendations:
                    print(f"\nRecommendations: {len(recommendations)}")
                    for i, rec in enumerate(recommendations[:3], 1):  # Show first 3
                        print(f"  {i}. [{rec.get('priority', 'unknown')}] {rec.get('action', '')}")

                method = analysis_results.get('analysis_method', {})
                print(f"\nAnalysis Method:")
                print(f"  - Pattern-based: {method.get('pattern_based', 0)} findings")
                print(f"  - AI-based: {method.get('ai_based', 0)} findings")

            else:
                error = data.get('error', 'Unknown error')
                print(f"[FAIL] Error: {error}")

            break

    pubsub2.close()

    # Step 3: Verify persistence
    print("\n" + "="*80)
    print("[STEP 3] Verifying Persistence")
    print("="*80)

    # Check if data was saved (would query backend/database here)
    print("\n[OK] Analysis complete!")
    print(f"Request IDs for verification:")
    print(f"  - Context Request: {request_id}")
    print(f"  - Analysis Request: {analysis_request_id}")

    print("\n" + "="*80)
    print("TEST COMPLETE")
    print("="*80)

    return True


def main():
    """Run the test"""
    try:
        # Run async test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        success = loop.run_until_complete(test_elastic_context_and_use_case())

        if success:
            print("\n[OK] ALL TESTS PASSED")
            sys.exit(0)
        else:
            print("\n[FAIL] TESTS FAILED")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n[FAIL] TEST ERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
