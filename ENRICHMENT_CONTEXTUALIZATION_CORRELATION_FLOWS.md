# Enrichment, Contextualization & Correlation Flows

**Complete Architecture Documentation**

This document explains when enrichment, entity extraction (contextualization), and correlation happen in SIEMLess v2.0, and how to independently call these processes.

---

## Table of Contents

1. [Overview - Three Core Processes](#overview)
2. [When These Processes Happen (Automatic Flows)](#automatic-flows)
3. [How to Independently Call Them](#independent-calling)
4. [Entity Extraction (Contextualization)](#entity-extraction)
5. [Enrichment](#enrichment)
6. [Correlation](#correlation)
7. [Complete Data Flow Diagram](#data-flow)
8. [API Reference](#api-reference)

---

## Overview - Three Core Processes {#overview}

### 1. **Entity Extraction (Contextualization)**
**What**: Extract entities (IPs, users, hosts, processes, hashes) from raw logs
**Where**: `contextualization_engine.py`
**Output**: Structured entity records with metadata

### 2. **Enrichment**
**What**: Add contextual information to entities (geolocation, threat intel, business context)
**Where**: `contextualization_engine.py`
**Output**: Enriched entities with multi-layer context (Layer 1: Basic, Layer 2: CTI, Layer 3: Business)

### 3. **Correlation**
**What**: Detect multi-source attack patterns (lateral movement, exfiltration, ransomware)
**Where**: `correlation_engine.py` (called by Backend Engine)
**Output**: Correlation alerts when patterns match across log sources

---

## When These Processes Happen (Automatic Flows) {#automatic-flows}

### Automatic Flow 1: **Log Ingestion → Entity Extraction → Enrichment → Storage**

**Trigger**: Ingestion Engine receives logs

**Flow**:
```
1. Ingestion Engine receives log
   ↓
2. Publishes to Redis: "contextualization.process_log"
   ↓
3. Contextualization Engine receives message
   ↓
4. Entity Extraction (_handle_process_log)
   - Extracts entities from log structure
   - Stores entities in database (entities table)
   - Creates relationships between entities
   ↓
5. Enrichment (_enrich_stored_entity)
   - Layer 1: Geolocation, DNS, WHOIS
   - Layer 2: CTI threat intelligence (check against IOC cache)
   - Layer 3: Business context (query entities.business_context)
   ↓
6. Publishes to Backend: "backend.store_intelligence"
   - Sends enriched entities + relationships
   - NOT the full log (lightweight architecture)
```

**File**: [contextualization_engine.py:138-208](contextualization_engine.py#L138-L208)

**Channel**: `contextualization.process_log`

**Example Redis Message**:
```json
{
  "log": { "source.ip": "*******", "user.name": "admin" },
  "pattern_type": "authentication",
  "entity_hints": [
    { "type": "ip_address", "value": "*******" },
    { "type": "username", "value": "admin" }
  ],
  "log_id": "abc123"
}
```

---

### Automatic Flow 2: **Alert Enrichment (Investigation Workflow)**

**Trigger**: Delivery Engine receives alert from Elastic/SIEM

**Flow**:
```
1. Delivery Engine fetches alert from Elastic
   ↓
2. Publishes to Redis: "contextualization.enrich_alert"
   ↓
3. Contextualization Engine receives message
   ↓
4. Three-Layer Enrichment (_handle_enrich_alert)
   - Layer 1: Basic enrichment (geolocation, DNS)
   - Layer 2: CTI enrichment (threat intelligence)
   - Layer 3: Environmental context (asset info, user directory)
   ↓
5. Publishes enriched result back to Delivery
   - Channel: "contextualization.alert.enriched.{request_id}"
   ↓
6. Delivery Engine receives enriched entities
   - Displays in investigation UI
   - Stores enrichment in cache
```

**File**: [contextualization_engine.py:1604-1706](contextualization_engine.py#L1604-L1706)

**Channel**: `contextualization.enrich_alert`

**Example Redis Message**:
```json
{
  "alert_id": "alert_123",
  "entities": {
    "source_ip": ["*******"],
    "destination_ip": ["*******"],
    "user": ["admin"]
  },
  "request_id": "alert_enrich_alert_123"
}
```

---

### Automatic Flow 3: **Real-time CTI IOC Updates (Cache Population)**

**Trigger**: Ingestion Engine receives new CTI indicators from OTX, ThreatFox, CrowdStrike, etc.

**Flow**:
```
1. Ingestion Engine fetches CTI indicators
   ↓
2. Publishes to Redis: "cti.enrichment.iocs"
   ↓
3. Contextualization Engine receives IOC
   ↓
4. Caches IOC (_handle_cti_ioc_update)
   - Stores in Redis for sub-millisecond lookups
   - Key format: "cti:ioc:{type}:{value}"
   ↓
5. Future enrichments check this cache
   - If entity matches IOC → instant threat detection
```

**File**: [contextualization_engine.py:1574-1602](contextualization_engine.py#L1574-L1602)

**Channel**: `cti.enrichment.iocs`

**Example Redis Message**:
```json
{
  "ioc_type": "ip_address",
  "value": "*******",
  "source": "otx",
  "threat_score": 85,
  "tags": ["malware", "botnet"],
  "threat_actor": "APT28",
  "campaign": "Operation XYZ"
}
```

---

### Automatic Flow 4: **Correlation (Multi-Source Attack Detection)**

**Trigger**: Backend Engine receives enriched intelligence from Contextualization

**Flow**:
```
1. Backend Engine receives enriched entities
   ↓
2. Sends events to Correlation Engine
   ↓
3. Correlation Engine checks active rules
   - Lateral Movement Detection
   - Data Exfiltration Detection
   - Ransomware Kill Chain Detection
   ↓
4. If pattern matches across multiple sources:
   - Creates correlation alert
   - Stores in cases table
   - Publishes to delivery.case_created
```

**File**: [correlation_engine.py:255-285](correlation_engine.py#L255-L285)

**Example Correlation Rule**: Lateral Movement
```python
# Detects: Auth logon + Internal network traffic + Remote execution tool
required_sources = [AUTH, FIREWALL, EDR]
time_window = 300  # 5 minutes
correlation_field = "source_ip"  # Correlate on source IP
minimum_sources = 2  # Need at least 2/3 sources
```

---

## How to Independently Call Them {#independent-calling}

### Independent Call 1: **Extract Entities from Specific Log**

**Method**: Publish to `contextualization.process_log` via Redis

**Command**:
```bash
docker-compose exec -T redis redis-cli PUBLISH "contextualization.process_log" '{
  "log": {
    "source.ip": "*************",
    "user.name": "john.doe",
    "host.name": "WORKSTATION-01",
    "process.name": "powershell.exe",
    "timestamp": "2025-10-03T14:30:00Z"
  },
  "pattern_type": "process_execution",
  "entity_hints": [],
  "log_id": "manual_extract_001"
}'
```

**Result**:
- Entities extracted and stored in `entities` table
- Relationships created in `relationships` table
- Enrichment applied automatically
- Published to `backend.store_intelligence`

**Query Results**:
```sql
SELECT entity_type, entity_value, properties
FROM entities
WHERE properties->>'source_log' = 'manual_extract_001';
```

---

### Independent Call 2: **Enrich a Specific Entity**

**Method**: Publish to `contextualization.enrich_entity` via Redis

**Command**:
```bash
docker-compose exec -T redis redis-cli PUBLISH "contextualization.enrich_entity" '{
  "entity_type": "ip",
  "entity_value": "*******",
  "enrich_level": "deep"
}'
```

**Enrichment Levels**:
- **standard**: Basic enrichment (geolocation, threat intel)
- **deep**: Full enrichment (includes network info, historical data)

**Result**:
- Entity enriched with all available context
- Published to `contextualization.entity_enriched` channel

**Listen for Response**:
```bash
docker-compose exec redis redis-cli SUBSCRIBE "contextualization.entity_enriched"
```

**Response Format**:
```json
{
  "entity_type": "ip",
  "entity_value": "*******",
  "enriched_data": {
    "enrichments": {
      "geolocation": {
        "country": "US",
        "city": "San Francisco",
        "asn": "AS13335"
      },
      "cti_threat_intelligence": {
        "is_threat": true,
        "threat_score": 85,
        "source": "otx",
        "tags": ["malware", "botnet"]
      },
      "business_context": {
        "context_label": "Corporate VPN Exit Node",
        "criticality_score": 80
      }
    },
    "threat_score": 85,
    "is_threat": true
  }
}
```

---

### Independent Call 3: **Enrich Specific Chunk of Data (Multiple Entities)**

**Method**: Publish to `contextualization.enrich_log` via Redis

**Command**:
```bash
docker-compose exec -T redis redis-cli PUBLISH "contextualization.enrich_log" '{
  "log": {
    "source_ip": "*************",
    "destination_ip": "*******",
    "user": "admin",
    "hostname": "SERVER-01"
  },
  "enrich_level": "standard"
}'
```

**Result**:
- All entities extracted from log
- Each entity enriched
- Enriched log published to `delivery.store_enriched_log`

**Use Case**: Enrich historical data, test enrichment quality, manual investigation

---

### Independent Call 4: **Adaptive Entity Extraction (AI-Powered)**

**Method**: Publish to `contextualization.extract_entities` via Redis

**Command**:
```bash
docker-compose exec -T redis redis-cli PUBLISH "contextualization.extract_entities" '{
  "request_id": "adaptive_001",
  "source": "crowdstrike",
  "logs": [
    {
      "raw": "2025-10-03 14:30:00 User admin logged in from ************* to SERVER-01"
    }
  ]
}
```

**Result**:
- Adaptive extractor detects vendor format
- Learns new patterns if needed (AI-powered)
- Extracts entities
- Publishes to `backend.store_intelligence`

**File**: [contextualization_engine.py:1482-1572](contextualization_engine.py#L1482-L1572)

**Method Used**:
- **pattern**: Matched known crystallized pattern (instant, free)
- **ai**: Used AI to learn new pattern (expensive, then free forever)
- **hybrid**: Combination of both

---

### Independent Call 5: **Extract Entities from Context Plugin Results**

**Method**: Publish to `contextualization.extract_from_context` via Redis

**Use Case**: After querying CrowdStrike, Elastic, or other context sources

**Command**:
```bash
docker-compose exec -T redis redis-cli PUBLISH "contextualization.extract_from_context" '{
  "request_id": "context_extract_001",
  "query_type": "hostname",
  "query_value": "SERVER-01",
  "context_results": {
    "crowdstrike": [
      {
        "category": "asset",
        "data": {
          "hostname": "SERVER-01",
          "local_ip": "************",
          "os_version": "Windows Server 2019",
          "last_login_user": "admin"
        },
        "confidence": 1.0
      }
    ]
  },
  "response_channel": "contextualization.context_extracted.context_extract_001"
}'
```

**Result**:
- Entities extracted from vendor-specific context data
- Stored in entities table
- Relationships created
- Published to response_channel

**File**: [contextualization_engine.py:1208-1299](contextualization_engine.py#L1208-L1299)

---

### Independent Call 6: **Trigger Correlation Analysis**

**Method**: Send events to Correlation Engine

**Context**: Correlation is typically called by Backend Engine automatically, but can be triggered manually:

```python
from correlation_engine import CorrelationEngine

# Initialize
correlation = CorrelationEngine(db_connection)

# Process event
event = {
    'source_type': 'auth',
    'event_id': '4624',
    'source_ip': '*************',
    'user': 'admin',
    'timestamp': '2025-10-03T14:30:00Z'
}

# Check for correlations
triggered_rules = await correlation.process_event(event)

if triggered_rules:
    for rule in triggered_rules:
        print(f"CORRELATION ALERT: {rule['rule_name']}")
        print(f"Severity: {rule['severity']}")
        print(f"Evidence: {rule['evidence']}")
```

**File**: [correlation_engine.py:255-285](correlation_engine.py#L255-L285)

**Active Correlation Rules**:
1. **Lateral Movement Detection** (CORR_001)
   - Sources: AUTH + FIREWALL + EDR
   - Time Window: 5 minutes
   - Detects: Logon + Network traffic + Remote execution

2. **Data Exfiltration Detection** (CORR_002)
   - Sources: PROXY + FIREWALL + EDR
   - Time Window: 1 hour
   - Detects: Large uploads + Unknown destinations + File access spike

3. **Ransomware Kill Chain** (CORR_003)
   - Sources: EDR
   - Time Window: 10 minutes
   - Detects: Shadow copy deletion + Mass file encryption + Process anomaly

---

## Entity Extraction (Contextualization) {#entity-extraction}

### Where It Happens

**File**: `engines/contextualization/contextualization_engine.py`

**Key Methods**:
- `_handle_process_log()` - Line 138
- `_handle_extract_entities_adaptive()` - Line 1482
- `_handle_extract_from_context()` - Line 1208
- `_extract_entities()` - Line 287
- `_extract_entities_from_log()` - Line 849

### Entity Types Extracted

```python
entity_types = [
    'ip_address',      # IPs (source, destination)
    'hostname',        # Hosts, devices
    'username',        # Users, accounts
    'process',         # Processes, executables
    'file_hash',       # MD5, SHA1, SHA256
    'port',           # Network ports
    'mac_address',    # MAC addresses
    'email',          # Email addresses
    'domain',         # Domain names
    'url',            # URLs
    'mitre_technique', # MITRE ATT&CK techniques
    'detection_id',   # Alert/detection IDs
    'incident_id'     # Incident IDs
]
```

### Extraction Methods

1. **Pattern-Based Extraction** (Free, instant)
   - Crystallized patterns from past AI learning
   - Field mappings (e.g., `source.ip` → IP entity)
   - Regex patterns for IPs, emails, hashes

2. **AI-Powered Extraction** (Expensive first time, free forever)
   - Adaptive Entity Extractor learns new vendor formats
   - AI detects vendor from log structure
   - Crystallizes pattern for future reuse

3. **Hybrid Extraction**
   - Combines pattern matching + AI enhancement
   - Pattern for known fields, AI for unknown

### Storage

**Entities Table**:
```sql
CREATE TABLE entities (
    entity_id UUID PRIMARY KEY,
    entity_type VARCHAR(50),
    entity_value VARCHAR(500),
    properties JSONB,  -- Enrichment data stored here
    business_context JSONB,  -- Analyst-added context
    behavioral_profile JSONB,  -- Learned behavior patterns
    confidence NUMERIC(3,2),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

**Relationships Table**:
```sql
CREATE TABLE relationships (
    relationship_id UUID PRIMARY KEY,
    source_entity_id UUID,
    target_entity_id UUID,
    relationship_type VARCHAR(50),  -- e.g., "user_logs_into_host"
    properties JSONB,
    confidence NUMERIC(3,2),
    created_at TIMESTAMP
);
```

---

## Enrichment {#enrichment}

### Where It Happens

**File**: `engines/contextualization/contextualization_engine.py`

**Key Methods**:
- `_enrich_entity()` - Line 373 (Main enrichment orchestrator)
- `_enrich_ip_address()` - Line 436
- `_enrich_hostname()` - Line 466
- `_enrich_user()` - Line 488
- `_enrich_process()` - Line 510
- `_enrich_file_hash()` - Line 524
- `_get_business_context()` - Line 538

### Three-Layer Enrichment Model

#### **Layer 1: Basic Context (Deterministic)**
**Always Applied** - Free, instant lookups

- **IP Addresses**:
  - Geolocation (country, city, region, ASN)
  - Network info (internal/external, segment)
  - WHOIS data (registrar, registrant)
  - Reverse DNS

- **Hostnames**:
  - Asset inventory (asset type, OS, owner, location)
  - DNS info (FQDN, IP addresses)

- **Users**:
  - User directory (full name, department, manager)
  - Access level (standard, admin)

- **Processes**:
  - Executable path
  - Digital signature
  - Version info
  - Risk score

- **File Hashes**:
  - File type
  - VirusTotal score (if integrated)
  - First/last seen

#### **Layer 2: Threat Intelligence (CTI)**
**Conditional** - Applied if IOC matches

**Sources**:
- OTX (AlienVault Open Threat Exchange)
- ThreatFox (abuse.ch)
- CrowdStrike Threat Intelligence
- OpenCTI (if available)

**Enrichment Data**:
```json
{
  "cti_threat_intelligence": {
    "is_threat": true,
    "threat_score": 85,
    "source": "otx",
    "match_type": "exact",
    "confidence": 95,
    "threat_actor": "APT28",
    "campaign": "Operation XYZ",
    "malware_family": "TrickBot",
    "tags": ["malware", "botnet", "credential_theft"]
  }
}
```

**File**: `engines/contextualization/cti_enrichment_pipeline.py`

**Method**: `_check_ioc_cache()` - Checks Redis cache for IOC match

#### **Layer 3: Business Context (Organizational Knowledge)**
**Conditional** - Applied if analyst added context

**Source**: `entities.business_context` (JSONB field)

**Enrichment Data**:
```json
{
  "business_context": {
    "context_label": "Backup Server - Scheduled Nightly Jobs",
    "context_description": "Runs automated backups daily 02:00-04:00",
    "business_unit": "IT Operations",
    "owner": "<EMAIL>",
    "security_zone": "internal",
    "criticality_score": 70,
    "behavioral_profile": {
      "scheduled_jobs": ["nightly_backup"],
      "normal_times": ["Daily 02:00-04:00"],
      "expected_traffic": ["Internal file server access"],
      "learned": false,
      "confidence": 100
    },
    "added_by": "<EMAIL>",
    "added_at": "2025-10-03T12:00:00Z"
  }
}
```

**File**: [contextualization_engine.py:538-599](contextualization_engine.py#L538-L599)

**Method**: `_get_business_context()` - Queries database for analyst-added context

### Enrichment Cache

**Purpose**: Speed up repeated enrichment lookups

**Storage**: In-memory cache (`self.entity_cache`)

**TTL**: 1 hour

**Key Format**: `{entity_type}:{entity_value}`

**Example**:
```python
cache_key = "ip:*******"
enriched_data = self.entity_cache.get(cache_key)  # Instant lookup
```

**Cache Cleanup**: Every 5 minutes via `_cache_maintenance_loop()`

---

## Correlation {#correlation}

### Where It Happens

**File**: `engines/backend/correlation_engine.py`

**Called By**: Backend Engine when processing enriched intelligence

### How Correlation Works

**Concept**: Detect multi-source attack patterns by correlating events across different log sources within a time window.

**Example - Lateral Movement**:
```
1. Auth log: User "admin" logs into SERVER-01 from *************
   ↓
2. Firewall: ************* → SERVER-01:445 (SMB) - ALLOW
   ↓
3. EDR: psexec.exe spawned on SERVER-01
   ↓
4. Correlation: All 3 events within 5 minutes + same source IP
   → ALERT: Lateral Movement Detected
```

### Correlation Rules

**Rule Structure**:
```python
@dataclass
class CorrelationRule:
    rule_id: str                          # CORR_001
    rule_name: str                        # "Lateral Movement Detection"
    rule_type: CorrelationType            # SEQUENCE, CORRELATION, AGGREGATION
    required_sources: List[LogSourceType] # [AUTH, FIREWALL, EDR]
    conditions: List[Dict]                # Per-source conditions
    correlation_logic: Dict               # How to correlate
    time_window: int                      # Seconds
    severity: str                         # critical, high, medium, low
    confidence: float                     # 0.0 - 1.0
    mitre_techniques: List[str]          # MITRE ATT&CK IDs
```

### Active Correlation Rules

**1. Lateral Movement Detection (CORR_001)**
```python
required_sources = [AUTH, FIREWALL, EDR]
correlation_field = "source_ip"
time_window = 300  # 5 minutes
minimum_sources = 2  # Need 2 out of 3

conditions:
- Auth: Event ID 4624 (Windows logon)
- Firewall: Allow traffic on ports 445/139/3389
- EDR: psexec.exe, wmic.exe, or powershell.exe
```

**2. Data Exfiltration Detection (CORR_002)**
```python
required_sources = [PROXY, FIREWALL, EDR]
correlation_field = "user"
time_window = 3600  # 1 hour
threshold = 3  # At least 3 uploads

conditions:
- Proxy: bytes_out > 100MB
- Firewall: Destination not in known_services
- EDR: File access spike vs baseline
```

**3. Ransomware Kill Chain (CORR_003)**
```python
required_sources = [EDR]
correlation_field = "hostname"
time_window = 600  # 10 minutes
behavioral_score_threshold = 0.8

conditions:
- EDR: "vssadmin delete shadows" command
- EDR: Rapid file rename operations
- EDR: Process tree anomaly vs baseline
```

### Correlation Context

**Purpose**: Track ongoing correlations that haven't triggered yet

**Storage**: In-memory (`self.active_contexts`)

**Structure**:
```python
@dataclass
class CorrelationContext:
    context_id: str                     # Unique ID
    rule: CorrelationRule               # Which rule
    entity_id: str                      # What we're correlating on (IP, user, host)
    entity_type: str                    # ip_address, username, hostname
    events_matched: List[Dict]          # Events collected so far
    sources_seen: Set[str]              # Which sources have matched
    conditions_met: Set[int]            # Which conditions satisfied
    first_seen: datetime                # First matching event
    last_seen: datetime                 # Last matching event
    expires_at: datetime                # When context expires
    correlation_score: float            # Current score (0.0 - 1.0)
    triggered: bool                     # Has this triggered already?
```

**Example Active Context**:
```json
{
  "context_id": "abc123def456",
  "rule_id": "CORR_001",
  "entity_id": "*************",
  "entity_type": "ip_address",
  "events_matched": [
    {
      "timestamp": "2025-10-03T14:30:00Z",
      "source": "auth",
      "event_id": "4624",
      "summary": "Windows Logon: admin -> SERVER-01"
    },
    {
      "timestamp": "2025-10-03T14:32:15Z",
      "source": "firewall",
      "event_id": "fw_123",
      "summary": "Allow: ************* -> SERVER-01:445"
    }
  ],
  "sources_seen": ["auth", "firewall"],
  "correlation_score": 0.67,
  "expires_at": "2025-10-03T14:35:00Z"
}
```

**Cleanup**: Expired contexts removed every cycle (see `_cleanup_contexts()`)

### Correlation Alert Output

When correlation triggers:

```json
{
  "alert_id": "corr_alert_abc123",
  "rule_id": "CORR_001",
  "rule_name": "Lateral Movement Detection",
  "use_case": "lateral_movement",
  "severity": "high",
  "confidence": 0.85,
  "entity": {
    "id": "*************",
    "type": "ip_address"
  },
  "evidence": {
    "events_count": 3,
    "sources": ["auth", "firewall", "edr"],
    "first_seen": "2025-10-03T14:30:00Z",
    "last_seen": "2025-10-03T14:34:30Z",
    "events": [
      "Windows Logon: admin -> SERVER-01",
      "Allow: ************* -> SERVER-01:445",
      "Process: psexec.exe spawned on SERVER-01"
    ]
  },
  "mitre_techniques": ["T1021", "T1077"],
  "description": "Potential lateral movement detected for *************. Observed 3 suspicious events across 3 log sources including authentication, network connections, and process execution.",
  "recommended_actions": [
    "Isolate affected systems from network",
    "Reset credentials for affected accounts",
    "Review authentication logs for anomalies",
    "Check for persistence mechanisms",
    "Perform memory analysis on affected hosts"
  ],
  "timestamp": "2025-10-03T14:34:30Z"
}
```

---

## Complete Data Flow Diagram {#data-flow}

```
┌─────────────────────────────────────────────────────────────────┐
│                    LOG SOURCES                                   │
│  (Elastic, Splunk, CrowdStrike, Fortinet, Palo Alto, etc.)     │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                 INGESTION ENGINE (Port 8003)                     │
│  - Receives logs from multiple sources                          │
│  - Routes to appropriate processing channels                    │
└─────────────────────────┬───────────────────────────────────────┘
                          │
                          │ Redis: "contextualization.process_log"
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│           CONTEXTUALIZATION ENGINE (Port 8004)                   │
│  ┌───────────────────────────────────────────────────────────┐  │
│  │ PHASE 1: Entity Extraction                                │  │
│  │  - Extract IPs, users, hosts, processes, hashes           │  │
│  │  - Methods: Pattern-based, AI-powered, Hybrid             │  │
│  │  - Create entity relationships                            │  │
│  │  - Store in entities + relationships tables               │  │
│  └───────────────────────┬───────────────────────────────────┘  │
│                          │                                       │
│  ┌───────────────────────▼───────────────────────────────────┐  │
│  │ PHASE 2: Enrichment (Three Layers)                        │  │
│  │                                                            │  │
│  │  Layer 1: Basic Context (Geolocation, DNS, WHOIS)        │  │
│  │  Layer 2: CTI Threat Intel (Check IOC cache)             │  │
│  │  Layer 3: Business Context (Query analyst-added context)  │  │
│  │                                                            │  │
│  │  Output: Enriched entities with full context             │  │
│  └───────────────────────┬───────────────────────────────────┘  │
└────────────────────────────┬─────────────────────────────────────┘
                             │
                             │ Redis: "backend.store_intelligence"
                             ▼
┌─────────────────────────────────────────────────────────────────┐
│              BACKEND ENGINE (Port 8002)                          │
│  ┌───────────────────────────────────────────────────────────┐  │
│  │ Storage: Store enriched entities (NOT raw logs)           │  │
│  │  - entities table: Entity data + enrichments              │  │
│  │  - relationships table: Entity relationships              │  │
│  └───────────────────────┬───────────────────────────────────┘  │
│                          │                                       │
│  ┌───────────────────────▼───────────────────────────────────┐  │
│  │ CORRELATION ENGINE                                         │  │
│  │  - Check events against correlation rules                 │  │
│  │  - Track active contexts                                  │  │
│  │  - Detect multi-source attack patterns                    │  │
│  │                                                            │  │
│  │  Rules:                                                    │  │
│  │  - Lateral Movement (AUTH + FW + EDR)                     │  │
│  │  - Data Exfiltration (PROXY + FW + EDR)                   │  │
│  │  - Ransomware Chain (EDR behavioral)                      │  │
│  └───────────────────────┬───────────────────────────────────┘  │
└────────────────────────────┬─────────────────────────────────────┘
                             │
                             │ Redis: "delivery.case_created"
                             ▼
┌─────────────────────────────────────────────────────────────────┐
│              DELIVERY ENGINE (Port 8005)                         │
│  - Case Management                                              │
│  - Investigation Workflow                                       │
│  - Alert Queue                                                  │
│  - Frontend APIs                                                │
│  - Business Context Management (CRUD)                           │
└────────────────────────────┬─────────────────────────────────────┘
                             │
                             ▼
┌─────────────────────────────────────────────────────────────────┐
│                    FRONTEND (Port 3000)                          │
│  - Alert Queue Widget (shows enriched alerts)                   │
│  - Investigation Guide (with enriched entities)                 │
│  - Business Context Editor                                      │
│  - Rule Management                                              │
└─────────────────────────────────────────────────────────────────┘
```

---

## API Reference {#api-reference}

### Redis Pub/Sub Channels

#### **Contextualization Engine Subscribed Channels**:

| Channel | Purpose | Input Format |
|---------|---------|--------------|
| `contextualization.process_log` | Process log and extract entities | `{log: {}, pattern_type: "", log_id: ""}` |
| `contextualization.extract_entities` | Adaptive entity extraction | `{request_id: "", source: "", logs: []}` |
| `contextualization.enrich_log` | Enrich all entities in log | `{log: {}, enrich_level: "standard"}` |
| `contextualization.enrich_entity` | Enrich single entity | `{entity_type: "", entity_value: "", enrich_level: ""}` |
| `contextualization.enrich_alert` | Enrich alert entities for investigation | `{alert_id: "", entities: {}, request_id: ""}` |
| `contextualization.extract_from_context` | Extract entities from context plugin results | `{request_id: "", context_results: {}}` |
| `cti.enrichment.iocs` | Cache CTI IOC for enrichment | `{ioc_type: "", value: "", source: ""}` |

#### **Contextualization Engine Published Channels**:

| Channel | Purpose | Output Format |
|---------|---------|---------------|
| `backend.store_intelligence` | Send enriched entities to backend | `{entities: [], relationships: [], entity_count: 0}` |
| `contextualization.entity_enriched` | Entity enrichment complete | `{entity_type: "", enriched_data: {}}` |
| `contextualization.alert.enriched.{request_id}` | Alert enrichment complete | `{alert_id: "", enriched_entities: {}}` |
| `delivery.store_enriched_log` | Store enriched log | `{enriched_log: {}, priority: ""}` |

### HTTP API Endpoints

#### **Business Context Management (Delivery Engine - Port 8005)**:

| Method | Endpoint | Purpose |
|--------|----------|---------|
| POST | `/api/entities/{type}/{value}/context` | Add business context to entity |
| GET | `/api/entities/{type}/{value}/context` | Get entity business context |
| PUT | `/api/entities/{type}/{value}/context` | Update business context |
| DELETE | `/api/entities/{type}/{value}/context` | Remove business context |
| GET | `/api/entities/with-context` | List all entities with business context |
| POST | `/api/entities/check-context` | Bulk check which entities have context |

#### **Correlation (Backend Engine - Port 8002)**:

| Method | Endpoint | Purpose |
|--------|----------|---------|
| GET | `/api/correlation/active` | Get active correlation contexts |
| GET | `/api/correlation/stats` | Get correlation rule statistics |
| GET | `/api/correlation/rules` | List all correlation rules |

---

## Summary: When & How

### **When (Automatic)**:

1. **Entity Extraction**: Every log ingested → contextualization.process_log
2. **Enrichment**: After every entity extraction (automatic chaining)
3. **CTI Cache Update**: When CTI plugins fetch new IOCs → cti.enrichment.iocs
4. **Alert Enrichment**: When alert opened for investigation → contextualization.enrich_alert
5. **Correlation**: When enriched intelligence stored → correlation checks all events

### **How (Independent Calls)**:

1. **Extract entities from log**: Publish to `contextualization.process_log`
2. **Enrich single entity**: Publish to `contextualization.enrich_entity`
3. **Enrich chunk of data**: Publish to `contextualization.enrich_log`
4. **Adaptive extraction**: Publish to `contextualization.extract_entities`
5. **Extract from context**: Publish to `contextualization.extract_from_context`
6. **Trigger correlation**: Send events to `CorrelationEngine.process_event()`

---

**All processes are modular, independently callable, and composable.**

**Architecture Principle**: Separation of concerns - each engine does one thing well, communicates via Redis pub/sub for loose coupling.
