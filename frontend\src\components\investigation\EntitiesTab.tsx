/**
 * Entities Tab - Display enriched entities with 3-layer enrichment
 */

import React, { useState } from 'react';
import { Alert, EnrichmentData, EnrichedEntity } from '../../types/investigation';
import '../../styles/EntitiesTab.css';

interface EntitiesTabProps {
  alert: Alert;
  enrichment: EnrichmentData | null;
}

type EntityFilter = 'all' | 'ip' | 'user' | 'host' | 'domain' | 'malicious';

export const EntitiesTab: React.FC<EntitiesTabProps> = ({ alert, enrichment }) => {
  const [filter, setFilter] = useState<EntityFilter>('all');

  if (!enrichment?.enrichment) {
    return (
      <div className="entities-tab">
        <div className="no-enrichment">
          <div className="no-enrichment-icon">🔍</div>
          <h3>No Enrichment Data Available</h3>
          <p>Entities have not been enriched yet.</p>
          <p className="hint">Enrichment may still be in progress. Check back in a few moments.</p>
        </div>
      </div>
    );
  }

  const { entities } = enrichment.enrichment;

  // Flatten all entities into a single array with type information
  const allEntities: Array<EnrichedEntity & { entityType: string }> = [];
  Object.entries(entities).forEach(([type, entityList]) => {
    entityList.forEach(entity => {
      allEntities.push({ ...entity, entityType: type });
    });
  });

  // Apply filter
  const filteredEntities = allEntities.filter(entity => {
    if (filter === 'all') return true;
    if (filter === 'malicious') return entity.is_malicious;
    return entity.entityType === filter;
  });

  // Count entities by type
  const counts = {
    all: allEntities.length,
    ip: entities.ip?.length || 0,
    user: entities.user?.length || 0,
    host: entities.host?.length || 0,
    domain: entities.domain?.length || 0,
    malicious: allEntities.filter(e => e.is_malicious).length
  };

  const getThreatColor = (score: number): string => {
    if (score >= 0.7) return '#dc2626'; // Red
    if (score >= 0.4) return '#ea580c'; // Orange
    return '#10b981'; // Green
  };

  const getThreatLevel = (score: number): string => {
    if (score >= 0.7) return 'MALICIOUS';
    if (score >= 0.4) return 'SUSPICIOUS';
    return 'BENIGN';
  };

  const formatDate = (dateStr?: string): string => {
    if (!dateStr) return 'Unknown';
    try {
      return new Date(dateStr).toLocaleDateString();
    } catch {
      return dateStr;
    }
  };

  const renderIPEntity = (entity: EnrichedEntity) => {
    const geo = entity.basic?.geolocation;
    const whois = entity.basic?.whois;
    const cti = entity.cti;
    const env = entity.environmental;

    return (
      <div className="entity-card" style={{ borderColor: getThreatColor(entity.threat_score) }}>
        <div className="entity-header">
          <div className="entity-title">
            <span className="entity-icon">🌐</span>
            <span className="entity-value">{entity.value}</span>
          </div>
          <div className="threat-badge" style={{ background: getThreatColor(entity.threat_score), color: '#ffffff' }}>
            Threat: {entity.threat_score.toFixed(2)}/1.0
          </div>
        </div>

        <div className="entity-body">
          {/* Layer 1: Basic Enrichment */}
          <div className="enrichment-section">
            <h4>🌍 Geolocation</h4>
            {geo ? (
              <div className="enrichment-data">
                <div className="data-row">
                  <span className="label">Country:</span>
                  <span className="value">{geo.country || 'Unknown'}</span>
                </div>
                {geo.city && (
                  <div className="data-row">
                    <span className="label">City:</span>
                    <span className="value">{geo.city}</span>
                  </div>
                )}
                {geo.asn && (
                  <div className="data-row">
                    <span className="label">ASN:</span>
                    <span className="value">{geo.asn}</span>
                  </div>
                )}
                {geo.org && (
                  <div className="data-row">
                    <span className="label">Organization:</span>
                    <span className="value">{geo.org}</span>
                  </div>
                )}
                {entity.basic?.ip_type && (
                  <div className="data-row">
                    <span className="label">IP Type:</span>
                    <span className="value ip-type">{entity.basic.ip_type}</span>
                  </div>
                )}
              </div>
            ) : (
              <div className="no-data-inline">No geolocation data available</div>
            )}
          </div>

          {/* Layer 2: Threat Intelligence */}
          {cti && cti.sources && cti.sources.length > 0 && (
            <div className="enrichment-section threat-section">
              <h4>⚠️ Threat Intelligence ({cti.sources.length} sources)</h4>
              <div className="enrichment-data">
                {cti.sources.map((source, idx) => (
                  <div key={idx} className="threat-source">
                    <div className="source-header">
                      <span className="source-name">{source.name}</span>
                      <span className="source-verdict">{source.verdict}</span>
                    </div>
                    {source.description && (
                      <div className="source-description">{source.description}</div>
                    )}
                    {source.tags && source.tags.length > 0 && (
                      <div className="source-tags">
                        {source.tags.map((tag, tidx) => (
                          <span key={tidx} className="tag">{tag}</span>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
                {cti.first_seen && (
                  <div className="data-row">
                    <span className="label">First Seen:</span>
                    <span className="value">{formatDate(cti.first_seen)}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* WHOIS Data */}
          {whois && (
            <div className="enrichment-section">
              <h4>🔍 WHOIS</h4>
              <div className="enrichment-data">
                {whois.registrar && (
                  <div className="data-row">
                    <span className="label">Registrar:</span>
                    <span className="value">{whois.registrar}</span>
                  </div>
                )}
                {whois.created && (
                  <div className="data-row">
                    <span className="label">Created:</span>
                    <span className="value">{formatDate(whois.created)}</span>
                  </div>
                )}
                {whois.updated && (
                  <div className="data-row">
                    <span className="label">Updated:</span>
                    <span className="value">{formatDate(whois.updated)}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Layer 3: Environmental Context */}
          {env && (
            <div className="enrichment-section">
              <h4>🏢 Asset Information</h4>
              <div className="enrichment-data">
                {env.asset_name && (
                  <div className="data-row">
                    <span className="label">Asset Name:</span>
                    <span className="value">{env.asset_name}</span>
                  </div>
                )}
                {env.asset_type && (
                  <div className="data-row">
                    <span className="label">Type:</span>
                    <span className="value">{env.asset_type}</span>
                  </div>
                )}
                {env.criticality && (
                  <div className="data-row">
                    <span className="label">Criticality:</span>
                    <span className={`value criticality-${env.criticality}`}>
                      {env.criticality.toUpperCase()}
                    </span>
                  </div>
                )}
                {env.owner && (
                  <div className="data-row">
                    <span className="label">Owner:</span>
                    <span className="value">{env.owner}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="entity-actions">
          {entity.is_malicious && (
            <button className="action-btn block-btn">🚫 Block IP</button>
          )}
          <button className="action-btn">🔎 Pivot Search</button>
          <button className="action-btn">📋 Copy</button>
        </div>
      </div>
    );
  };

  const renderUserEntity = (entity: EnrichedEntity) => {
    const env = entity.environmental;
    const cti = entity.cti;

    return (
      <div className="entity-card" style={{ borderColor: getThreatColor(entity.threat_score) }}>
        <div className="entity-header">
          <div className="entity-title">
            <span className="entity-icon">👤</span>
            <span className="entity-value">{entity.value}</span>
          </div>
          <div className="threat-badge" style={{ background: getThreatColor(entity.threat_score), color: '#ffffff' }}>
            Threat: {entity.threat_score.toFixed(2)}/1.0
          </div>
        </div>

        <div className="entity-body">
          {/* User Information */}
          {env?.user_info && (
            <div className="enrichment-section">
              <h4>👤 User Information</h4>
              <div className="enrichment-data">
                {env.user_info.full_name && (
                  <div className="data-row">
                    <span className="label">Full Name:</span>
                    <span className="value">{env.user_info.full_name}</span>
                  </div>
                )}
                {env.user_info.email && (
                  <div className="data-row">
                    <span className="label">Email:</span>
                    <span className="value">{env.user_info.email}</span>
                  </div>
                )}
                {env.user_info.department && (
                  <div className="data-row">
                    <span className="label">Department:</span>
                    <span className="value">{env.user_info.department}</span>
                  </div>
                )}
                {env.user_info.privilege_level && (
                  <div className="data-row">
                    <span className="label">Privilege Level:</span>
                    <span className="value privilege-level">{env.user_info.privilege_level}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Risk Indicators */}
          {entity.is_malicious && cti && (
            <div className="enrichment-section threat-section">
              <h4>⚠️ Risk Indicators</h4>
              <div className="enrichment-data">
                <div className="risk-indicator">
                  <span className="risk-icon">⚠️</span>
                  <span>Account flagged by threat intelligence</span>
                </div>
                {cti.sources?.map((source, idx) => (
                  <div key={idx} className="risk-indicator">
                    <span className="risk-icon">•</span>
                    <span>{source.name}: {source.verdict}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Behavioral Analysis */}
          {env?.baseline && (
            <div className="enrichment-section">
              <h4>📊 Behavioral Analysis</h4>
              <div className="enrichment-data">
                {env.baseline.typical_login_times && (
                  <div className="data-row">
                    <span className="label">Typical Login Times:</span>
                    <span className="value">{env.baseline.typical_login_times}</span>
                  </div>
                )}
                {env.baseline.typical_sources && (
                  <div className="data-row">
                    <span className="label">Typical Login Location:</span>
                    <span className="value">{env.baseline.typical_sources.join(', ')}</span>
                  </div>
                )}
                {env.baseline.unusual_activity && env.baseline.unusual_activity.length > 0 && (
                  <div className="unusual-activities">
                    {env.baseline.unusual_activity.map((activity, idx) => (
                      <div key={idx} className="unusual-activity">
                        <span className="warning-icon">⚠️</span>
                        <span>{activity}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="entity-actions">
          <button className="action-btn">🔐 Reset Password</button>
          {entity.is_malicious && (
            <button className="action-btn block-btn">🚫 Disable Account</button>
          )}
          <button className="action-btn">🔎 Pivot Search</button>
          <button className="action-btn">📋 Copy</button>
        </div>
      </div>
    );
  };

  const renderHostEntity = (entity: EnrichedEntity) => {
    const env = entity.environmental;

    return (
      <div className="entity-card" style={{ borderColor: getThreatColor(entity.threat_score) }}>
        <div className="entity-header">
          <div className="entity-title">
            <span className="entity-icon">🖥️</span>
            <span className="entity-value">{entity.value}</span>
          </div>
          <div className="threat-badge" style={{ background: getThreatColor(entity.threat_score), color: '#ffffff' }}>
            Threat: {entity.threat_score.toFixed(2)}/1.0
          </div>
        </div>

        <div className="entity-body">
          {env && (
            <div className="enrichment-section">
              <h4>🏢 Asset Information</h4>
              <div className="enrichment-data">
                {env.asset_name && (
                  <div className="data-row">
                    <span className="label">Hostname:</span>
                    <span className="value">{env.asset_name}</span>
                  </div>
                )}
                {env.asset_type && (
                  <div className="data-row">
                    <span className="label">Asset Type:</span>
                    <span className="value">{env.asset_type}</span>
                  </div>
                )}
                {env.criticality && (
                  <div className="data-row">
                    <span className="label">Criticality:</span>
                    <span className={`value criticality-${env.criticality}`}>
                      {env.criticality.toUpperCase()}
                    </span>
                  </div>
                )}
                {env.owner && (
                  <div className="data-row">
                    <span className="label">Owner:</span>
                    <span className="value">{env.owner}</span>
                  </div>
                )}
                {env.department && (
                  <div className="data-row">
                    <span className="label">Department:</span>
                    <span className="value">{env.department}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="entity-actions">
          {entity.is_malicious && (
            <button className="action-btn block-btn">🔒 Isolate Host</button>
          )}
          <button className="action-btn">🔎 Pivot Search</button>
          <button className="action-btn">📊 View Baseline</button>
          <button className="action-btn">📋 Copy</button>
        </div>
      </div>
    );
  };

  const renderGenericEntity = (entity: EnrichedEntity & { entityType: string }) => {
    return (
      <div className="entity-card" style={{ borderColor: getThreatColor(entity.threat_score) }}>
        <div className="entity-header">
          <div className="entity-title">
            <span className="entity-icon">📄</span>
            <span className="entity-value">{entity.value}</span>
            <span className="entity-type-badge">{entity.entityType}</span>
          </div>
          <div className="threat-badge" style={{ background: getThreatColor(entity.threat_score), color: '#ffffff' }}>
            Threat: {entity.threat_score.toFixed(2)}/1.0
          </div>
        </div>

        <div className="entity-body">
          {entity.cti && entity.cti.sources && entity.cti.sources.length > 0 && (
            <div className="enrichment-section threat-section">
              <h4>⚠️ Threat Intelligence</h4>
              <div className="enrichment-data">
                {entity.cti.sources.map((source, idx) => (
                  <div key={idx} className="threat-source">
                    <div className="source-header">
                      <span className="source-name">{source.name}</span>
                      <span className="source-verdict">{source.verdict}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {!entity.cti?.sources?.length && (
            <div className="no-data-inline">No enrichment data available for this entity type</div>
          )}
        </div>

        <div className="entity-actions">
          <button className="action-btn">🔎 Pivot Search</button>
          <button className="action-btn">📋 Copy</button>
        </div>
      </div>
    );
  };

  const renderEntity = (entity: EnrichedEntity & { entityType: string }) => {
    switch (entity.entityType) {
      case 'ip':
        return renderIPEntity(entity);
      case 'user':
        return renderUserEntity(entity);
      case 'host':
        return renderHostEntity(entity);
      default:
        return renderGenericEntity(entity);
    }
  };

  return (
    <div className="entities-tab">
      <div className="entities-header">
        <div className="filter-buttons">
          <button
            className={filter === 'all' ? 'filter-btn active' : 'filter-btn'}
            onClick={() => setFilter('all')}
          >
            All ({counts.all})
          </button>
          {counts.ip > 0 && (
            <button
              className={filter === 'ip' ? 'filter-btn active' : 'filter-btn'}
              onClick={() => setFilter('ip')}
            >
              IPs ({counts.ip})
            </button>
          )}
          {counts.user > 0 && (
            <button
              className={filter === 'user' ? 'filter-btn active' : 'filter-btn'}
              onClick={() => setFilter('user')}
            >
              Users ({counts.user})
            </button>
          )}
          {counts.host > 0 && (
            <button
              className={filter === 'host' ? 'filter-btn active' : 'filter-btn'}
              onClick={() => setFilter('host')}
            >
              Hosts ({counts.host})
            </button>
          )}
          {counts.domain > 0 && (
            <button
              className={filter === 'domain' ? 'filter-btn active' : 'filter-btn'}
              onClick={() => setFilter('domain')}
            >
              Domains ({counts.domain})
            </button>
          )}
          {counts.malicious > 0 && (
            <button
              className={filter === 'malicious' ? 'filter-btn active malicious' : 'filter-btn malicious'}
              onClick={() => setFilter('malicious')}
            >
              Malicious Only ({counts.malicious})
            </button>
          )}
        </div>
      </div>

      <div className="entities-list">
        {filteredEntities.length === 0 ? (
          <div className="no-entities">
            <p>No entities match the selected filter.</p>
          </div>
        ) : (
          filteredEntities.map((entity, idx) => (
            <div key={`${entity.entityType}-${entity.value}-${idx}`}>
              {renderEntity(entity)}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default EntitiesTab;
