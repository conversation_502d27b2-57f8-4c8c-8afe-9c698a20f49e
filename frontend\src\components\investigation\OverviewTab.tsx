/**
 * Overview Tab - Executive summary of the investigation
 */

import React from 'react';
import { Alert, EnrichmentData, CorrelationData } from '../../types/investigation';
import '../../styles/OverviewTab.css';

interface OverviewTabProps {
  alert: Alert;
  enrichment: EnrichmentData | null;
  correlation: CorrelationData | null;
}

export const OverviewTab: React.FC<OverviewTabProps> = ({
  alert,
  enrichment,
  correlation
}) => {
  const calculateThreatLevel = (): { level: string; confidence: number; color: string } => {
    let threatScore = 0;
    let factors = 0;

    // Factor 1: Alert severity
    if (alert.severity === 'critical') {
      threatScore += 0.4;
      factors++;
    } else if (alert.severity === 'high') {
      threatScore += 0.3;
      factors++;
    }

    // Factor 2: Enrichment threat indicators
    if (enrichment?.enrichment?.summary.threat_indicators_found) {
      const threats = enrichment.enrichment.summary.threat_indicators_found;
      threatScore += Math.min(threats / 5, 0.3);
      factors++;
    }

    // Factor 3: Correlation score
    if (correlation?.correlation?.score) {
      threatScore += correlation.correlation.score * 0.3;
      factors++;
    }

    const confidence = factors > 0 ? (factors / 3) * 100 : 0;
    const avgScore = factors > 0 ? threatScore : 0.5;

    let level = 'UNKNOWN';
    let color = '#6b7280';

    if (avgScore >= 0.7) {
      level = 'HIGH';
      color = '#dc2626';
    } else if (avgScore >= 0.4) {
      level = 'MEDIUM';
      color = '#ea580c';
    } else if (avgScore < 0.4) {
      level = 'LOW';
      color = '#65a30d';
    }

    return { level, confidence: Math.round(confidence), color };
  };

  const threat = calculateThreatLevel();

  const maliciousIPs = enrichment?.enrichment?.entities.ip?.filter(e => e.is_malicious).length || 0;
  const attackStages = correlation?.correlation?.attack_stages?.length || 0;
  const relatedEvents = correlation?.correlation?.summary.total_related_events || 0;

  return (
    <div className="overview-tab">
      {/* Summary Cards */}
      <div className="summary-grid">
        {/* Threat Assessment Card */}
        <div className="summary-card threat-card" style={{ borderColor: threat.color }}>
          <div className="card-header">
            <h3>🎯 Threat Assessment</h3>
          </div>
          <div className="card-body">
            <div className="threat-level" style={{ color: threat.color }}>
              Overall Threat: {threat.level}
            </div>
            <div className="confidence">
              Confidence: {threat.confidence}%
            </div>

            <div className="threat-indicators">
              {maliciousIPs > 0 && (
                <div className="indicator">
                  <span className="indicator-icon">✓</span>
                  <span>{maliciousIPs} Malicious IP{maliciousIPs > 1 ? 's' : ''}</span>
                </div>
              )}
              {attackStages > 0 && (
                <div className="indicator">
                  <span className="indicator-icon">✓</span>
                  <span>{attackStages} Attack Stage{attackStages > 1 ? 's' : ''}</span>
                </div>
              )}
              {relatedEvents > 3 && (
                <div className="indicator">
                  <span className="indicator-icon">✓</span>
                  <span>Multi-event correlation</span>
                </div>
              )}
              {(!maliciousIPs && !attackStages && !relatedEvents) && (
                <div className="indicator">
                  <span className="indicator-icon">○</span>
                  <span>No threat indicators found</span>
                </div>
              )}
            </div>

            <button className="view-details-btn">View Details →</button>
          </div>
        </div>

        {/* Correlation Summary Card */}
        <div className="summary-card correlation-card">
          <div className="card-header">
            <h3>📈 Correlation Summary</h3>
          </div>
          <div className="card-body">
            {correlation?.correlation ? (
              <>
                <div className="stat-row">
                  <span className="stat-label">Related Events:</span>
                  <span className="stat-value">{correlation.correlation.summary.total_related_events}</span>
                </div>
                <div className="stat-row">
                  <span className="stat-label">Time Span:</span>
                  <span className="stat-value">{correlation.correlation.time_window.duration_minutes} min</span>
                </div>
                <div className="stat-row">
                  <span className="stat-label">Correlation Score:</span>
                  <span className="stat-value">
                    {correlation.correlation.score.toFixed(2)}
                  </span>
                </div>

                {correlation.correlation.attack_stages.length > 0 && (
                  <div className="attack-stages">
                    <div className="stages-label">Attack Stages:</div>
                    {correlation.correlation.attack_stages.map((stage, idx) => (
                      <div key={idx} className="stage-item">
                        <span className="stage-check">✓</span>
                        <span>{stage}</span>
                      </div>
                    ))}
                  </div>
                )}
              </>
            ) : (
              <div className="no-data">
                <p>No correlation data available</p>
                <p className="no-data-hint">Correlation may still be in progress</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* AI Verdict Section */}
      <div className="ai-verdict-section">
        <div className="section-header">
          <h2>🤖 AI Verdict</h2>
        </div>

        <div className="verdict-card">
          <div className="verdict-header">
            <div className="verdict-label">Verdict:</div>
            <div className="verdict-value" style={{ color: threat.color }}>
              {threat.level === 'HIGH' ? 'LIKELY MALICIOUS' :
               threat.level === 'MEDIUM' ? 'SUSPICIOUS' :
               threat.level === 'LOW' ? 'LIKELY BENIGN' : 'INSUFFICIENT DATA'}
            </div>
            <div className="verdict-confidence">
              Confidence: {threat.confidence}%
            </div>
          </div>

          <div className="verdict-body">
            <div className="findings-section">
              <h4>Key Findings:</h4>
              <ul className="findings-list">
                {maliciousIPs > 0 && (
                  <li>
                    <strong>Malicious Infrastructure Detected</strong>
                    <p>
                      {maliciousIPs} IP address{maliciousIPs > 1 ? 'es' : ''} identified as malicious
                      by threat intelligence sources
                    </p>
                  </li>
                )}

                {attackStages > 0 && (
                  <li>
                    <strong>Multi-Stage Attack Pattern</strong>
                    <p>
                      {attackStages} distinct attack stage{attackStages > 1 ? 's' : ''} detected: {' '}
                      {correlation?.correlation?.attack_stages.join(', ')}
                    </p>
                  </li>
                )}

                {relatedEvents > 5 && (
                  <li>
                    <strong>Correlated Activity</strong>
                    <p>
                      {relatedEvents} related events found within the time window,
                      suggesting coordinated activity
                    </p>
                  </li>
                )}

                {alert.mitre_techniques && alert.mitre_techniques.length > 0 && (
                  <li>
                    <strong>MITRE ATT&CK Techniques</strong>
                    <p>
                      Matches {alert.mitre_techniques.length} known attack technique{alert.mitre_techniques.length > 1 ? 's' : ''}: {' '}
                      {alert.mitre_techniques.join(', ')}
                    </p>
                  </li>
                )}

                {(!maliciousIPs && !attackStages && !relatedEvents) && (
                  <li>
                    <strong>Limited Context Available</strong>
                    <p>
                      No significant threat indicators found. This may be a false positive
                      or require manual investigation.
                    </p>
                  </li>
                )}
              </ul>
            </div>

            <div className="recommended-actions">
              <h4>Recommended Action:</h4>
              <div className="action-recommendation">
                {threat.level === 'HIGH' && (
                  <>
                    <div className="action-priority critical">IMMEDIATE CONTAINMENT</div>
                    <p>This appears to be a genuine threat requiring immediate action.</p>
                  </>
                )}
                {threat.level === 'MEDIUM' && (
                  <>
                    <div className="action-priority warning">INVESTIGATE FURTHER</div>
                    <p>Suspicious activity detected. Investigate to confirm threat.</p>
                  </>
                )}
                {threat.level === 'LOW' && (
                  <>
                    <div className="action-priority info">REVIEW AND CLOSE</div>
                    <p>Likely benign activity. Review for false positive tuning.</p>
                  </>
                )}
                {threat.level === 'UNKNOWN' && (
                  <>
                    <div className="action-priority">MANUAL INVESTIGATION</div>
                    <p>Insufficient data for automated assessment. Manual review required.</p>
                  </>
                )}
              </div>

              <div className="suggested-steps">
                <h5>Suggested Steps:</h5>
                <ol>
                  {threat.level === 'HIGH' && (
                    <>
                      <li>Isolate affected systems from network</li>
                      <li>Block malicious IP addresses at firewall</li>
                      <li>Reset compromised credentials</li>
                      <li>Capture forensic evidence (memory dumps, disk images)</li>
                      <li>Escalate to Incident Response team</li>
                    </>
                  )}
                  {threat.level === 'MEDIUM' && (
                    <>
                      <li>Review entity behavior in SIEM</li>
                      <li>Check for additional related events</li>
                      <li>Validate threat intelligence findings</li>
                      <li>Monitor for escalation</li>
                    </>
                  )}
                  {threat.level === 'LOW' && (
                    <>
                      <li>Verify entities are authorized</li>
                      <li>Check for policy violations</li>
                      <li>Tune detection rule if false positive</li>
                      <li>Document findings and close</li>
                    </>
                  )}
                  {threat.level === 'UNKNOWN' && (
                    <>
                      <li>Manually enrich entities</li>
                      <li>Search for additional context in SIEM</li>
                      <li>Consult with asset owners</li>
                      <li>Determine appropriate action</li>
                    </>
                  )}
                </ol>
              </div>
            </div>
          </div>

          {/* Playbook Button */}
          {threat.level === 'HIGH' && (
            <div className="playbook-section">
              <div className="playbook-available">
                <span className="playbook-icon">📋</span>
                <span className="playbook-text">Automated Playbook Available</span>
              </div>
              <div className="playbook-details">
                <span>Playbook: Critical_Threat_Response_v3</span>
                <span>•</span>
                <span>12 automated steps, 4 require approval</span>
                <span>•</span>
                <span>Estimated time: 45 minutes</span>
              </div>
              <div className="playbook-actions">
                <button className="execute-playbook-btn">
                  🚀 Execute Playbook
                </button>
                <button className="view-playbook-btn">
                  📄 View Playbook Details
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OverviewTab;
