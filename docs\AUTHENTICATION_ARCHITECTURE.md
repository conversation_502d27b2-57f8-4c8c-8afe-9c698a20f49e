# Authentication Architecture for SIEMLess v2.0

## The Challenge

Your system has two communication patterns:
1. **Direct HTTP** (Frontend → Backend/Graph APIs) - Needs auth
2. **Redis Pub/Sub** (Engine-to-Engine) - Needs auth
3. **WebSockets** (Frontend → Delivery Engine for real-time) - Needs auth

## Solution: JWT + Redis Session Store

### Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    USER BROWSER                              │
│  ┌────────────────────────────────────────────────────┐     │
│  │  Frontend (React)                                  │     │
│  │  - Stores JWT in memory/localStorage              │     │
│  │  - Sends JWT in Authorization header              │     │
│  └────────────────────────────────────────────────────┘     │
└──────────────────────┬──────────────────────────────────────┘
                       │
                       │ All requests include:
                       │ Authorization: Bearer <JWT>
                       ↓
┌─────────────────────────────────────────────────────────────┐
│              Delivery Engine (Port 8005)                     │
│  ┌────────────────────────────────────────────────────┐     │
│  │  Authentication Service                            │     │
│  │  POST /auth/login    → Issues JWT                 │     │
│  │  POST /auth/logout   → Invalidates session        │     │
│  │  POST /auth/refresh  → Refreshes JWT              │     │
│  └────────────────────────────────────────────────────┘     │
└────────────────────┬────────────────────────────────────────┘
                     │
                     │ Stores sessions in Redis
                     ↓
┌─────────────────────────────────────────────────────────────┐
│                Redis (Port 6380)                             │
│  ┌────────────────────────────────────────────────────┐     │
│  │  Session Store                                     │     │
│  │  Key: session:{user_id}:{session_id}              │     │
│  │  Value: {user_data, permissions, expires}         │     │
│  │  TTL: 24 hours (sliding window)                   │     │
│  └────────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────┘
                     ↑
                     │ All engines verify JWT
                     │ and check session
                     │
┌────────────────────┴────────────────────────────────────────┐
│         Backend Engine (Port 8002)                           │
│  ┌────────────────────────────────────────────────────┐     │
│  │  Auth Middleware                                   │     │
│  │  - Validates JWT signature                         │     │
│  │  - Checks session in Redis                         │     │
│  │  - Verifies permissions                            │     │
│  └────────────────────────────────────────────────────┘     │
│                                                              │
│  If auth passes → Process request                           │
│  If auth fails  → Return 401 Unauthorized                   │
└─────────────────────────────────────────────────────────────┘
```

## Implementation: JWT + Redis Sessions

### Why This Approach?

✅ **JWT for stateless verification** - Each engine can verify token independently
✅ **Redis for session management** - Centralized session store, instant revocation
✅ **No database hits** - Auth check uses Redis only (< 1ms)
✅ **Works for both HTTP and WebSocket** - Same token format
✅ **Instant logout** - Delete Redis key, all engines reject token immediately

### JWT Structure

```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "user_id": "uuid-1234",
    "username": "<EMAIL>",
    "roles": ["analyst", "investigator"],
    "permissions": ["read:alerts", "write:cases", "read:graph"],
    "session_id": "uuid-5678",
    "iat": 1696234567,
    "exp": 1696320967  // 24 hours
  },
  "signature": "HMAC-SHA256(...)"
}
```

### Redis Session Structure

```
Key: session:user_id:session_id
TTL: 86400 seconds (24 hours, sliding)

Value (JSON):
{
  "user_id": "uuid-1234",
  "username": "<EMAIL>",
  "roles": ["analyst", "investigator"],
  "permissions": ["read:alerts", "write:cases", "read:graph"],
  "created_at": "2024-10-01T12:00:00Z",
  "last_active": "2024-10-01T15:30:00Z",
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0...",
  "active": true
}
```

## Delivery Engine: Authentication Service

### Login Flow

```python
# engines/delivery/auth_service.py

import jwt
import bcrypt
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

class AuthService:
    """
    Centralized authentication service in Delivery Engine
    Issues JWTs and manages sessions in Redis
    """

    def __init__(self, redis_client, db_connection, logger):
        self.redis = redis_client
        self.db = db_connection
        self.logger = logger

        # JWT secret (should be in environment variable!)
        self.jwt_secret = os.getenv('JWT_SECRET', 'change-me-in-production')
        self.jwt_algorithm = 'HS256'
        self.jwt_expiry = timedelta(hours=24)

    async def login(self, username: str, password: str, ip_address: str, user_agent: str) -> Optional[Dict[str, Any]]:
        """
        Authenticate user and issue JWT

        Returns:
            {
                'access_token': 'jwt...',
                'token_type': 'Bearer',
                'expires_in': 86400,
                'user': {username, roles, permissions}
            }
        """
        try:
            # 1. Verify credentials against database
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT user_id, username, password_hash, roles, permissions, active
                FROM users
                WHERE username = %s
            """, (username,))

            user_record = cursor.fetchone()

            if not user_record:
                self.logger.warning(f"Login failed: User not found - {username}")
                return None

            user_id, db_username, password_hash, roles, permissions, active = user_record

            if not active:
                self.logger.warning(f"Login failed: User inactive - {username}")
                return None

            # 2. Verify password
            if not bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8')):
                self.logger.warning(f"Login failed: Invalid password - {username}")
                return None

            # 3. Create session
            session_id = str(uuid.uuid4())
            now = datetime.utcnow()
            expires_at = now + self.jwt_expiry

            # 4. Store session in Redis
            session_key = f"session:{user_id}:{session_id}"
            session_data = {
                'user_id': user_id,
                'username': db_username,
                'roles': roles,
                'permissions': permissions,
                'created_at': now.isoformat(),
                'last_active': now.isoformat(),
                'ip_address': ip_address,
                'user_agent': user_agent,
                'active': True
            }

            # Store in Redis with TTL
            self.redis.setex(
                session_key,
                int(self.jwt_expiry.total_seconds()),
                json.dumps(session_data)
            )

            # 5. Create JWT
            jwt_payload = {
                'user_id': user_id,
                'username': db_username,
                'roles': roles,
                'permissions': permissions,
                'session_id': session_id,
                'iat': int(now.timestamp()),
                'exp': int(expires_at.timestamp())
            }

            access_token = jwt.encode(
                jwt_payload,
                self.jwt_secret,
                algorithm=self.jwt_algorithm
            )

            # 6. Log successful login
            self.logger.info(f"Successful login: {username} from {ip_address}")

            # 7. Publish login event to Redis for other engines
            await self.redis.publish('auth.user_login', json.dumps({
                'user_id': user_id,
                'username': db_username,
                'session_id': session_id,
                'timestamp': now.isoformat()
            }))

            return {
                'access_token': access_token,
                'token_type': 'Bearer',
                'expires_in': int(self.jwt_expiry.total_seconds()),
                'user': {
                    'user_id': user_id,
                    'username': db_username,
                    'roles': roles,
                    'permissions': permissions
                }
            }

        except Exception as e:
            self.logger.error(f"Login error: {e}")
            return None

    async def logout(self, token: str) -> bool:
        """
        Invalidate session (instant across all engines)
        """
        try:
            # Decode JWT to get session_id
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            user_id = payload['user_id']
            session_id = payload['session_id']

            # Delete session from Redis
            session_key = f"session:{user_id}:{session_id}"
            self.redis.delete(session_key)

            # Publish logout event
            await self.redis.publish('auth.user_logout', json.dumps({
                'user_id': user_id,
                'session_id': session_id,
                'timestamp': datetime.utcnow().isoformat()
            }))

            self.logger.info(f"User logged out: {user_id}")
            return True

        except Exception as e:
            self.logger.error(f"Logout error: {e}")
            return False

    async def refresh_token(self, token: str) -> Optional[str]:
        """
        Refresh JWT (extend session)
        """
        try:
            # Verify current token
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])

            # Check session still exists
            session_key = f"session:{payload['user_id']}:{payload['session_id']}"
            session_data = self.redis.get(session_key)

            if not session_data:
                return None  # Session expired or logged out

            # Create new token with extended expiry
            now = datetime.utcnow()
            expires_at = now + self.jwt_expiry

            new_payload = {
                **payload,
                'iat': int(now.timestamp()),
                'exp': int(expires_at.timestamp())
            }

            new_token = jwt.encode(
                new_payload,
                self.jwt_secret,
                algorithm=self.jwt_algorithm
            )

            # Update session last_active time
            session_obj = json.loads(session_data)
            session_obj['last_active'] = now.isoformat()

            self.redis.setex(
                session_key,
                int(self.jwt_expiry.total_seconds()),
                json.dumps(session_obj)
            )

            return new_token

        except jwt.ExpiredSignatureError:
            return None
        except Exception as e:
            self.logger.error(f"Token refresh error: {e}")
            return None

    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Verify JWT and check session (used by all engines)

        Returns user payload if valid, None if invalid
        """
        try:
            # 1. Verify JWT signature and expiry
            payload = jwt.decode(
                token,
                self.jwt_secret,
                algorithms=[self.jwt_algorithm]
            )

            # 2. Check session exists in Redis
            session_key = f"session:{payload['user_id']}:{payload['session_id']}"
            session_data = self.redis.get(session_key)

            if not session_data:
                self.logger.warning(f"Session not found: {session_key}")
                return None

            session = json.loads(session_data)

            if not session.get('active', True):
                self.logger.warning(f"Session inactive: {session_key}")
                return None

            # 3. Update last_active (sliding window)
            session['last_active'] = datetime.utcnow().isoformat()
            self.redis.setex(
                session_key,
                int(self.jwt_expiry.total_seconds()),
                json.dumps(session)
            )

            return payload

        except jwt.ExpiredSignatureError:
            self.logger.warning("JWT expired")
            return None
        except jwt.InvalidTokenError as e:
            self.logger.warning(f"Invalid JWT: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Token verification error: {e}")
            return None

    def check_permission(self, user_payload: Dict[str, Any], required_permission: str) -> bool:
        """
        Check if user has required permission
        """
        user_permissions = user_payload.get('permissions', [])
        return required_permission in user_permissions
```

## Backend Engine: Auth Middleware

### Protecting Graph APIs

```python
# engines/backend/auth_middleware.py

from functools import wraps
from aiohttp import web
import jwt
import json

class AuthMiddleware:
    """
    Reusable auth middleware for Backend Engine (and other engines)
    Validates JWT and checks Redis session
    """

    def __init__(self, redis_client, logger):
        self.redis = redis_client
        self.logger = logger
        self.jwt_secret = os.getenv('JWT_SECRET', 'change-me-in-production')
        self.jwt_algorithm = 'HS256'

    def require_auth(self, required_permissions: list = None):
        """
        Decorator for HTTP endpoints that require authentication

        Usage:
            @auth_middleware.require_auth(['read:graph'])
            async def _handle_graph_explore(self, request):
                # User is authenticated and has permission
                user = request['user']  # Contains user_id, username, roles, permissions
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(self, request):
                # 1. Extract JWT from Authorization header
                auth_header = request.headers.get('Authorization', '')

                if not auth_header.startswith('Bearer '):
                    self.logger.warning("Missing or invalid Authorization header")
                    return web.json_response(
                        {'error': 'Unauthorized', 'message': 'Missing authentication token'},
                        status=401
                    )

                token = auth_header[7:]  # Remove 'Bearer ' prefix

                # 2. Verify JWT
                try:
                    payload = jwt.decode(
                        token,
                        self.jwt_secret,
                        algorithms=[self.jwt_algorithm]
                    )
                except jwt.ExpiredSignatureError:
                    return web.json_response(
                        {'error': 'Unauthorized', 'message': 'Token expired'},
                        status=401
                    )
                except jwt.InvalidTokenError:
                    return web.json_response(
                        {'error': 'Unauthorized', 'message': 'Invalid token'},
                        status=401
                    )

                # 3. Check session in Redis
                session_key = f"session:{payload['user_id']}:{payload['session_id']}"
                session_data = self.redis.get(session_key)

                if not session_data:
                    self.logger.warning(f"Session not found: {session_key}")
                    return web.json_response(
                        {'error': 'Unauthorized', 'message': 'Session expired or invalid'},
                        status=401
                    )

                session = json.loads(session_data)

                if not session.get('active', True):
                    return web.json_response(
                        {'error': 'Unauthorized', 'message': 'Session inactive'},
                        status=401
                    )

                # 4. Check permissions
                if required_permissions:
                    user_permissions = payload.get('permissions', [])

                    for perm in required_permissions:
                        if perm not in user_permissions:
                            self.logger.warning(
                                f"Permission denied: {payload['username']} needs {perm}"
                            )
                            return web.json_response(
                                {'error': 'Forbidden', 'message': f'Missing permission: {perm}'},
                                status=403
                            )

                # 5. Update last_active (sliding window)
                session['last_active'] = datetime.utcnow().isoformat()
                self.redis.setex(
                    session_key,
                    86400,  # 24 hours
                    json.dumps(session)
                )

                # 6. Add user to request context
                request['user'] = payload

                # 7. Call the actual endpoint handler
                return await func(self, request)

            return wrapper
        return decorator
```

### Updated Backend Engine with Auth

```python
# engines/backend/backend_engine.py

from auth_middleware import AuthMiddleware

class BackendEngine(BaseEngine):
    def __init__(self):
        super().__init__("backend")

        # Initialize auth middleware
        self.auth_middleware = None  # Will be set after Redis connection

    def start_engine_tasks(self):
        # ... existing code ...

        # Initialize auth middleware
        self.auth_middleware = AuthMiddleware(self.redis_client, self.logger)
        self.logger.info("Auth middleware initialized")

        # ... rest of initialization ...

    # ============================================
    # Protected Graph API Endpoints
    # ============================================

    @auth_middleware.require_auth(['read:graph'])
    async def _handle_graph_explore(self, request):
        """API: Explore entity relationships (PROTECTED)"""
        try:
            # User is authenticated - payload in request['user']
            user = request['user']
            self.logger.info(f"Graph explore by {user['username']}")

            entity_id = request.query.get('entity_id')
            depth = int(request.query.get('depth', 2))

            if not entity_id:
                return web.json_response({'error': 'entity_id required'}, status=400)

            graph_data = self.age_service.explore_entity(entity_id, depth)

            # Log access for audit
            await self.redis.publish('audit.graph_access', json.dumps({
                'user_id': user['user_id'],
                'username': user['username'],
                'entity_id': entity_id,
                'depth': depth,
                'timestamp': datetime.utcnow().isoformat()
            }))

            return web.json_response(graph_data)

        except Exception as e:
            self.logger.error(f"Graph explore error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    @auth_middleware.require_auth(['read:graph'])
    async def _handle_graph_path(self, request):
        """API: Find path (PROTECTED)"""
        user = request['user']
        # ... implementation ...

    @auth_middleware.require_auth(['read:stats'])
    async def _handle_graph_stats(self, request):
        """API: Graph stats (PROTECTED)"""
        user = request['user']
        # ... implementation ...
```

## Frontend: Token Management

### React Auth Context

```typescript
// frontend/src/contexts/AuthContext.tsx

import React, { createContext, useContext, useState, useEffect } from 'react';

interface User {
    user_id: string;
    username: string;
    roles: string[];
    permissions: string[];
}

interface AuthContextType {
    user: User | null;
    token: string | null;
    login: (username: string, password: string) => Promise<boolean>;
    logout: () => Promise<void>;
    isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [user, setUser] = useState<User | null>(null);
    const [token, setToken] = useState<string | null>(null);

    useEffect(() => {
        // Check for existing token on mount
        const savedToken = localStorage.getItem('access_token');
        if (savedToken) {
            // Verify token is still valid
            verifyToken(savedToken);
        }
    }, []);

    const verifyToken = async (tokenToVerify: string) => {
        try {
            // Call a verify endpoint or just try to use the token
            const response = await fetch('http://localhost:8002/api/graph/stats', {
                headers: {
                    'Authorization': `Bearer ${tokenToVerify}`
                }
            });

            if (response.ok) {
                // Token is valid, decode to get user info
                const payload = JSON.parse(atob(tokenToVerify.split('.')[1]));
                setUser({
                    user_id: payload.user_id,
                    username: payload.username,
                    roles: payload.roles,
                    permissions: payload.permissions
                });
                setToken(tokenToVerify);
            } else {
                // Token invalid, clear
                localStorage.removeItem('access_token');
            }
        } catch (error) {
            localStorage.removeItem('access_token');
        }
    };

    const login = async (username: string, password: string): Promise<boolean> => {
        try {
            const response = await fetch('http://localhost:8005/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            if (!response.ok) {
                return false;
            }

            const data = await response.json();

            // Store token
            localStorage.setItem('access_token', data.access_token);
            setToken(data.access_token);
            setUser(data.user);

            return true;

        } catch (error) {
            console.error('Login failed:', error);
            return false;
        }
    };

    const logout = async () => {
        if (token) {
            try {
                await fetch('http://localhost:8005/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
            } catch (error) {
                console.error('Logout error:', error);
            }
        }

        localStorage.removeItem('access_token');
        setToken(null);
        setUser(null);
    };

    return (
        <AuthContext.Provider
            value={{
                user,
                token,
                login,
                logout,
                isAuthenticated: !!token
            }}
        >
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within AuthProvider');
    }
    return context;
};
```

### Using Auth in Components

```typescript
// frontend/src/pages/investigation/EntityInvestigationGraph.tsx

import { useAuth } from '../../contexts/AuthContext';

const EntityInvestigationGraph: React.FC<EntityGraphProps> = ({ entityId }) => {
    const { token, isAuthenticated } = useAuth();
    const [graphData, setGraphData] = useState({ nodes: [], edges: [] });

    useEffect(() => {
        if (isAuthenticated) {
            fetchGraphData();
        }
    }, [entityId, isAuthenticated]);

    const fetchGraphData = async () => {
        try {
            // Include JWT in Authorization header
            const response = await fetch(
                `http://localhost:8002/api/graph/explore?entity_id=${entityId}&depth=2`,
                {
                    headers: {
                        'Authorization': `Bearer ${token}`  // JWT included!
                    }
                }
            );

            if (response.status === 401) {
                // Token expired or invalid - redirect to login
                window.location.href = '/login';
                return;
            }

            const data = await response.json();
            setGraphData(data);
            renderGraph(data);

        } catch (error) {
            console.error('Failed to fetch graph:', error);
        }
    };

    // ... rest of component ...
};
```

## Engine-to-Engine Auth (Redis Pub/Sub)

### For Redis Messages

```python
# All engines should sign Redis messages with engine secret

class BaseEngine:
    def __init__(self, engine_name):
        self.engine_name = engine_name
        self.engine_secret = os.getenv('ENGINE_SECRET', 'shared-engine-secret')

    async def publish_message(self, channel: str, data: dict):
        """Publish authenticated message to Redis"""

        # Add authentication metadata
        message = {
            'source_engine': self.engine_name,
            'timestamp': datetime.utcnow().isoformat(),
            'data': data,
            'signature': self._sign_message(data)
        }

        await self.redis_client.publish(channel, json.dumps(message))

    def _sign_message(self, data: dict) -> str:
        """Sign message with engine secret (HMAC)"""
        import hmac
        import hashlib

        message_bytes = json.dumps(data, sort_keys=True).encode('utf-8')
        signature = hmac.new(
            self.engine_secret.encode('utf-8'),
            message_bytes,
            hashlib.sha256
        ).hexdigest()

        return signature

    def _verify_message(self, message: dict) -> bool:
        """Verify message signature"""
        import hmac

        expected_signature = self._sign_message(message['data'])
        actual_signature = message.get('signature', '')

        return hmac.compare_digest(expected_signature, actual_signature)
```

## Summary: Complete Auth Flow

### User Login
```
1. Frontend → POST /auth/login (Delivery Engine)
2. Delivery → Verify password (PostgreSQL)
3. Delivery → Create session (Redis)
4. Delivery → Generate JWT
5. Delivery → Return JWT to Frontend
6. Frontend → Store JWT in localStorage
```

### Graph Query (Direct HTTP)
```
1. Frontend → GET /api/graph/explore + Authorization: Bearer JWT (Backend Engine)
2. Backend → Verify JWT signature (local check)
3. Backend → Check session exists (Redis - 1ms)
4. Backend → Verify permission (from JWT)
5. Backend → Update last_active (Redis)
6. Backend → Process query
7. Backend → Return graph data
```

### Logout
```
1. Frontend → POST /auth/logout (Delivery Engine)
2. Delivery → Delete session from Redis
3. Delivery → Publish logout event to Redis
4. All engines → Immediately reject that JWT
5. Frontend → Clear localStorage
```

## Performance Impact

**Auth check overhead per request:**
- JWT signature verification: ~0.5ms (local)
- Redis session lookup: ~1ms
- **Total: ~1.5ms** ✅

**Without Redis (database sessions):**
- Database query: ~10-50ms ❌

## Security Benefits

✅ **Instant revocation** - Delete Redis key, token invalid everywhere
✅ **No database bottleneck** - Redis handles millions of auth checks
✅ **Stateless + Stateful** - JWT for verification, Redis for control
✅ **Audit trail** - All auth events published to Redis
✅ **Permission enforcement** - Fine-grained access control

This design gives you **fast, secure authentication** that works for both direct HTTP (graph queries) and async Redis (engine messaging)! 🔐
