#!/usr/bin/env python3
"""
Simple AI Function Test for SIEMLess v2.0 Intelligence Engine
"""

import json
import redis
import time

def test_ai_functions():
    print("Starting AI Function Tests for SIEMLess v2.0")
    print("=" * 50)

    try:
        # Connect to Redis
        redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)

        # Test connection
        response = redis_client.ping()
        print(f"[OK] Redis connection: {response}")

        # Test AI Consensus
        print("\n[AI] Testing AI Consensus...")
        test_data = {
            'request_id': 'test_consensus_001',
            'pattern_data': {
                'log_sample': 'Failed login attempt from *************',
                'source': 'auth_logs'
            },
            'complexity': 'medium'
        }

        # Send consensus request
        redis_client.publish('intelligence.consensus', json.dumps(test_data))
        print("   [SEND] Sent consensus request")

        # Test Pattern Crystallization
        print("\n[CRYSTAL] Testing Pattern Crystallization...")
        crystal_data = {
            'pattern_id': 'test_pattern_crystal_001',
            'ai_insights': {
                'confidence': 0.85,
                'pattern_type': 'authentication_failure',
                'responses': [
                    {'model': 'gemini-pro', 'confidence': 0.82},
                    {'model': 'claude-opus-4', 'confidence': 0.88}
                ]
            }
        }

        redis_client.publish('intelligence.crystallize', json.dumps(crystal_data))
        print("   [SEND] Sent crystallization request")

        # Test Pattern Validation
        print("\n[VALIDATE] Testing Pattern Validation...")
        validation_data = {
            'pattern_id': 'test_validation_001',
            'pattern': {
                'regex': r'Failed login.*from (\d+\.){3}\d+',
                'description': 'Authentication failure pattern'
            },
            'type': 'general'
        }

        redis_client.publish('intelligence.validate', json.dumps(validation_data))
        print("   [SEND] Sent validation request")

        print("\n[INFO] Tests sent successfully!")
        print("[INFO] Check Intelligence Engine logs for processing results:")
        print("       docker-compose logs intelligence_engine")

        return True

    except Exception as e:
        print(f"[FAIL] Test failed: {e}")
        return False

if __name__ == "__main__":
    test_ai_functions()