#!/usr/bin/env python3
"""
Test Script for Elastic Rule Harvester
Run this to verify connectivity and harvest rules from your Elastic instance
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add engines/ingestion to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'engines', 'ingestion'))

from elastic_rule_harvester import ElasticRuleHarvester
import logging


async def test_elastic_connection():
    """Test connectivity to Elastic instance"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    print("\n" + "="*70)
    print("ELASTIC RULE HARVESTER TEST")
    print("="*70 + "\n")

    # Configuration - EDIT THESE VALUES FOR YOUR ELASTIC INSTANCE
    config = {
        # Option 1: Direct URL with API Key (RECOMMENDED)
        'url': 'https://your-elastic-instance.com:9200',
        'api_key': 'your_base64_encoded_api_key',

        # Option 2: Direct URL with Username/Password
        # 'url': 'https://your-elastic-instance.com:9200',
        # 'username': 'elastic',
        # 'password': 'your_password',

        # Option 3: Elastic Cloud with Cloud ID
        # 'cloud_id': 'deployment-name:base64string',
        # 'api_key': 'your_api_key',

        'verify_ssl': False  # Set to True in production
    }

    print("Configuration:")
    print(f"  URL: {config.get('url', 'N/A')}")
    print(f"  Cloud ID: {config.get('cloud_id', 'N/A')}")
    print(f"  Auth Method: {'API Key' if config.get('api_key') else 'Username/Password'}")
    print(f"  SSL Verification: {config.get('verify_ssl', False)}")
    print()

    harvester = ElasticRuleHarvester(logger)

    try:
        # Test 1: Connection
        print("TEST 1: Testing connection to Elastic...")
        print("-" * 70)
        if not await harvester.configure(config):
            print("❌ FAILED: Could not connect to Elastic instance")
            print("\nTroubleshooting:")
            print("  1. Check that your Elastic instance is running")
            print("  2. Verify URL/Cloud ID is correct")
            print("  3. Verify API key or credentials are correct")
            print("  4. Check network connectivity and firewall rules")
            return

        print("✅ SUCCESS: Connected to Elastic instance\n")

        # Test 2: Harvest Detection Rules
        print("\nTEST 2: Harvesting Detection Rules...")
        print("-" * 70)
        detection_rules = await harvester.harvest_detection_rules()

        print(f"✅ Harvested {len(detection_rules)} detection rules\n")

        if detection_rules:
            print("Sample Detection Rules:")
            for i, rule in enumerate(detection_rules[:5], 1):  # Show first 5
                print(f"\n  {i}. {rule.name}")
                print(f"     Severity: {rule.severity} | Risk Score: {rule.risk_score}")
                print(f"     Type: {rule.rule_type} | Language: {rule.language}")
                print(f"     Enabled: {rule.enabled}")
                print(f"     MITRE Techniques: {', '.join(rule.mitre_techniques) if rule.mitre_techniques else 'None'}")
                print(f"     Query: {rule.query[:100]}..." if len(rule.query) > 100 else f"     Query: {rule.query}")

        # Test 3: Harvest Saved Searches
        print("\n\nTEST 3: Harvesting Saved Searches...")
        print("-" * 70)
        saved_searches = await harvester.harvest_saved_searches()

        print(f"✅ Harvested {len(saved_searches)} saved searches\n")

        if saved_searches:
            print("Sample Saved Searches:")
            for i, search in enumerate(saved_searches[:5], 1):  # Show first 5
                print(f"\n  {i}. {search.title}")
                print(f"     Description: {search.description or 'N/A'}")
                print(f"     Index Pattern: {search.index_pattern}")
                print(f"     Columns: {', '.join(search.columns) if search.columns else 'Default'}")

        # Test 4: Harvest Watcher Alerts
        print("\n\nTEST 4: Harvesting Watcher Alerts...")
        print("-" * 70)
        watcher_alerts = await harvester.harvest_watcher_alerts()

        print(f"✅ Harvested {len(watcher_alerts)} watcher alerts\n")

        if watcher_alerts:
            print("Sample Watcher Alerts:")
            for i, alert in enumerate(watcher_alerts[:5], 1):  # Show first 5
                print(f"\n  {i}. {alert.name}")
                print(f"     Active: {alert.active}")
                print(f"     Actions: {len(alert.actions)} configured")

        # Summary
        print("\n" + "="*70)
        print("HARVEST SUMMARY")
        print("="*70)
        print(f"Detection Rules: {len(detection_rules)}")
        print(f"Saved Searches: {len(saved_searches)}")
        print(f"Watcher Alerts: {len(watcher_alerts)}")
        print(f"Total Artifacts: {len(detection_rules) + len(saved_searches) + len(watcher_alerts)}")
        print(f"Harvest Time: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print("="*70 + "\n")

        # Export to JSON
        export_results = {
            'harvest_time': datetime.utcnow().isoformat(),
            'summary': {
                'detection_rules_count': len(detection_rules),
                'saved_searches_count': len(saved_searches),
                'watcher_alerts_count': len(watcher_alerts),
                'total_artifacts': len(detection_rules) + len(saved_searches) + len(watcher_alerts)
            },
            'detection_rules': [rule.to_dict() for rule in detection_rules],
            'saved_searches': [search.to_dict() for search in saved_searches],
            'watcher_alerts': [alert.to_dict() for alert in watcher_alerts]
        }

        output_file = 'elastic_harvest_results.json'
        with open(output_file, 'w') as f:
            json.dump(export_results, f, indent=2)

        print(f"✅ Results exported to: {output_file}")
        print("\nYou can now:")
        print("  1. Review the exported JSON file")
        print("  2. Import these rules into SIEMLess pattern library")
        print("  3. Use the Intelligence Engine to enhance/optimize rules")
        print("  4. Deploy to multiple SIEM platforms via Delivery Engine\n")

    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

    finally:
        await harvester.close()


async def test_with_environment_vars():
    """Test using environment variables for configuration"""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # Load from environment variables
    config = {
        'url': os.getenv('ELASTIC_URL', 'http://localhost:9200'),
        'api_key': os.getenv('ELASTIC_API_KEY'),
        'username': os.getenv('ELASTIC_USERNAME'),
        'password': os.getenv('ELASTIC_PASSWORD'),
        'cloud_id': os.getenv('ELASTIC_CLOUD_ID'),
        'verify_ssl': os.getenv('ELASTIC_VERIFY_SSL', 'false').lower() == 'true'
    }

    # Remove None values
    config = {k: v for k, v in config.items() if v is not None}

    if not config.get('api_key') and not (config.get('username') and config.get('password')):
        print("❌ ERROR: No authentication configured")
        print("\nSet environment variables:")
        print("  ELASTIC_URL=https://your-elastic:9200")
        print("  ELASTIC_API_KEY=your_api_key")
        print("  # OR")
        print("  ELASTIC_USERNAME=elastic")
        print("  ELASTIC_PASSWORD=your_password")
        return

    harvester = ElasticRuleHarvester(logger)

    try:
        if await harvester.configure(config):
            results = await harvester.harvest_all()
            print(json.dumps({
                'success': results['success'],
                'detection_rules': len(results['detection_rules']),
                'saved_searches': len(results['saved_searches']),
                'watcher_alerts': len(results['watcher_alerts'])
            }, indent=2))
    finally:
        await harvester.close()


def main():
    """Main entry point"""
    if len(sys.argv) > 1 and sys.argv[1] == '--env':
        # Use environment variables
        asyncio.run(test_with_environment_vars())
    else:
        # Use hardcoded config (edit in script)
        asyncio.run(test_elastic_connection())


if __name__ == "__main__":
    print("""
    ╔══════════════════════════════════════════════════════════════════╗
    ║              ELASTIC RULE HARVESTER TEST SCRIPT                  ║
    ║                      SIEMLess v2.0                               ║
    ╚══════════════════════════════════════════════════════════════════╝

    This script will:
      1. Connect to your Elastic instance
      2. Harvest detection rules from Elastic Security
      3. Harvest saved searches from Kibana
      4. Harvest Watcher alerts (if available)
      5. Export results to JSON

    Usage:
      python test_elastic_harvester.py              # Use hardcoded config
      python test_elastic_harvester.py --env        # Use environment variables

    Before running, edit the configuration in the script or set environment
    variables with your Elastic connection details.
    """)

    main()
