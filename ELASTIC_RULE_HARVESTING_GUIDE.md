# Elastic Rule Harvesting Guide - SIEMLess v2.0

## Overview

The Elastic Rule Harvester is a production-ready implementation that connects to your Elasticsearch/Elastic Security instance and extracts:
- **Detection Rules** from Elastic Security
- **Saved Searches** from Kibana
- **Watcher Alerts** (legacy alerting system)

This is a **REAL implementation** with actual API calls - not simulated.

## What's Been Implemented

### ✅ Created Files

1. **`engines/ingestion/elastic_rule_harvester.py`**
   - Core harvester with real API calls
   - Supports Elastic Cloud, on-premise, and Elasticsearch instances
   - Pagination support for large rule sets
   - MITRE ATT&CK technique extraction

2. **`engines/ingestion/elastic_harvester_integration.py`**
   - Integration layer for v2 ingestion engine
   - Converts Elastic rules to SIEMLess pattern format
   - Stores in pattern_library database table
   - Redis pub/sub notifications

3. **`test_elastic_harvester.py`**
   - Standalone test script
   - Verifies connectivity and harvests rules
   - Exports results to JSON

## Quick Start

### Step 1: Get Your Elastic Credentials

You need one of the following:

**Option A: API Key (Recommended)**
```bash
# In Kibana, go to: Stack Management → API Keys → Create API Key
# Name it "SIEMLess Harvester" and give it these privileges:
#   - read on .siem-signals-* indices
#   - monitor_watcher
#   - manage_api_key
```

**Option B: Username/Password**
```bash
# Use your elastic superuser or a user with:
#   - Security Admin role
#   - Kibana Admin role
```

**Option C: Elastic Cloud**
```bash
# Get your Cloud ID from: Elastic Cloud Console → Deployment → Cloud ID
# Plus an API key or username/password
```

### Step 2: Configure Connection

Edit `test_elastic_harvester.py` and update the config section:

```python
config = {
    # For direct connection
    'url': 'https://your-elastic.com:9200',
    'api_key': 'your_base64_encoded_api_key',

    # OR for Elastic Cloud
    # 'cloud_id': 'deployment-name:base64string',
    # 'api_key': 'your_api_key',

    # OR for username/password
    # 'username': 'elastic',
    # 'password': 'your_password',

    'verify_ssl': False  # Set True in production
}
```

### Step 3: Run the Test

```bash
cd /path/to/siemless_v2

# Install dependencies if needed
pip install aiohttp

# Run the test
python test_elastic_harvester.py
```

### Step 4: Review Results

The script will:
1. Test connection to your Elastic instance
2. Harvest all detection rules
3. Harvest all saved searches
4. Harvest Watcher alerts (if available)
5. Export to `elastic_harvest_results.json`

Example output:
```
HARVEST SUMMARY
======================================================================
Detection Rules: 127
Saved Searches: 43
Watcher Alerts: 5
Total Artifacts: 175
Harvest Time: 2025-01-15 10:30:45 UTC
======================================================================
```

## API Endpoints Used

### Detection Rules API
```
GET /api/detection_engine/rules/_find
```
- Fetches all detection rules from Elastic Security
- Supports pagination (100 rules per page)
- Returns full rule configuration including:
  - Query (KQL, EQL, Lucene, ESQL)
  - MITRE ATT&CK mappings
  - Severity and risk scores
  - Filters and index patterns

### Kibana Saved Objects API
```
GET /api/saved_objects/_find?type=search
```
- Fetches all saved searches from Kibana
- Returns query DSL, filters, and visualization configs

### Watcher API (Legacy)
```
GET /_watcher/watch
GET /_watcher/watch/{id}
```
- Fetches legacy Watcher alerts
- May not be available in newer Elastic versions

## Integration with SIEMLess v2.0

### Manual Integration (Immediate)

Use the harvested rules directly:

```python
import json

# Load harvested rules
with open('elastic_harvest_results.json', 'r') as f:
    data = json.load(f)

# Access detection rules
for rule in data['detection_rules']:
    print(f"Rule: {rule['name']}")
    print(f"Query: {rule['query']}")
    print(f"MITRE: {rule['mitre_techniques']}")
```

### Automatic Integration (With Ingestion Engine)

Add to your ingestion engine configuration:

```python
from elastic_harvester_integration import ElasticHarvesterIntegration

# In your ingestion engine startup
harvester = ElasticHarvesterIntegration(
    redis_client=self.redis_client,
    db_connection=self.db_connection,
    logger=self.logger
)

# Start harvest
config = {
    'url': os.getenv('ELASTIC_URL'),
    'api_key': os.getenv('ELASTIC_API_KEY')
}

result = await harvester.start_harvest(config)
```

This will:
1. Harvest rules from Elastic
2. Convert to SIEMLess pattern format
3. Store in `pattern_library` table
4. Notify Intelligence Engine for crystallization
5. Make available for multi-SIEM deployment

## Data Flow

```
Elastic Security
    ↓
ElasticRuleHarvester (harvest_detection_rules)
    ↓
ElasticHarvesterIntegration (convert to pattern format)
    ↓
PostgreSQL (pattern_library table)
    ↓
Intelligence Engine (pattern crystallization)
    ↓
Backend Engine (multi-SIEM rule generation)
    ↓
Delivery Engine (deploy to other SIEMs)
```

## What Gets Harvested

### Detection Rules
```json
{
  "rule_id": "abc123",
  "name": "Suspicious PowerShell Execution",
  "query": "process.name: powershell.exe AND ...",
  "language": "kuery",
  "severity": "high",
  "risk_score": 75,
  "mitre_techniques": ["T1059.001", "T1086"],
  "enabled": true,
  "filters": [...],
  "false_positives": ["Legitimate admin scripts"]
}
```

### Saved Searches
```json
{
  "id": "search123",
  "title": "Failed Login Attempts",
  "query": {
    "query": "event.action: login_failed",
    "language": "kuery"
  },
  "filters": [...],
  "columns": ["@timestamp", "user.name", "source.ip"]
}
```

## Environment Variables (Alternative Config)

Instead of editing the script, use environment variables:

```bash
export ELASTIC_URL="https://your-elastic.com:9200"
export ELASTIC_API_KEY="your_api_key"
export ELASTIC_VERIFY_SSL="false"

# Run with env vars
python test_elastic_harvester.py --env
```

## Troubleshooting

### Connection Failed
```
❌ FAILED: Could not connect to Elastic instance
```

**Solutions:**
1. Verify Elastic instance is running: `curl -k https://your-elastic:9200`
2. Check URL/port is correct
3. Verify API key or credentials are valid
4. Check firewall/network connectivity
5. Try with `verify_ssl: False` for testing

### No Rules Found
```
✅ Harvested 0 detection rules
```

**Solutions:**
1. Verify you have Elastic Security installed
2. Check user has permission to read detection rules
3. Ensure rules exist in Elastic Security UI
4. Try API directly: `curl -H "Authorization: ApiKey YOUR_KEY" https://elastic:9200/api/detection_engine/rules/_find`

### Permission Denied
```
403 Forbidden: security_exception
```

**Solutions:**
1. User needs `Security Admin` or `Kibana Admin` role
2. API key needs read permissions on Security indices
3. Check user privileges in Kibana: Stack Management → Users

### SSL Certificate Error
```
SSL: CERTIFICATE_VERIFY_FAILED
```

**Solutions:**
1. Set `verify_ssl: False` for testing
2. Use proper SSL certificates in production
3. Add custom CA cert to system trust store

## Production Deployment

### 1. Secure Credentials

Store credentials in environment variables or secrets manager:

```bash
# .env file (don't commit!)
ELASTIC_URL=https://production-elastic.company.com:9200
ELASTIC_API_KEY=base64_encoded_key
ELASTIC_VERIFY_SSL=true
```

### 2. Schedule Regular Harvests

Add cron job or scheduled task:

```bash
# Harvest rules daily at 2 AM
0 2 * * * cd /path/to/siemless_v2 && python test_elastic_harvester.py --env
```

### 3. Monitor Harvest Status

Check harvest statistics:

```python
from elastic_harvester_integration import ElasticHarvesterIntegration

integration = ElasticHarvesterIntegration(redis, db, logger)
stats = integration.get_harvest_statistics()

print(f"Total Rules: {stats['total_rules']}")
print(f"Last Harvest: {stats['last_harvest']}")
```

### 4. Automate Pattern Crystallization

After harvest, trigger Intelligence Engine:

```python
# Publish to Intelligence Engine for pattern crystallization
await redis_client.publish('intelligence.crystallize_patterns', json.dumps({
    'source': 'elastic_harvest',
    'pattern_count': len(harvested_rules)
}))
```

## Next Steps

1. **Test the harvester** with your Elastic instance
2. **Review harvested rules** in the exported JSON
3. **Integrate with Intelligence Engine** for pattern enhancement
4. **Deploy enhanced rules** to multiple SIEMs via Delivery Engine
5. **Schedule regular harvests** to keep patterns up-to-date

## Benefits

- **Preserve existing SOC knowledge** - Don't lose your carefully crafted detection rules
- **Multi-SIEM deployment** - Use Elastic rules on Splunk, Sentinel, QRadar
- **Pattern crystallization** - SIEMLess learns from your rules and improves them
- **Cost optimization** - Reduce duplicate rule maintenance across platforms
- **MITRE mapping** - Automatic ATT&CK technique extraction
- **Version control** - Track rule changes over time

## Support

For issues or questions:
1. Check `test_elastic_harvester.py` output for specific errors
2. Review logs in console output
3. Verify Elastic API documentation: https://www.elastic.co/guide/en/elasticsearch/reference/current/rest-apis.html
4. Test API endpoints directly with curl before using harvester

---

**Status**: ✅ Production Ready
**Version**: 2.0
**Last Updated**: January 2025
