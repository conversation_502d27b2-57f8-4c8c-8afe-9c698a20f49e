"""
Query Generator Service
Phase 2: Deterministic Query Generator

Generates vendor-specific queries from database templates based on:
1. What sources we actually have logs from (ingestion_logs)
2. Entity type and value (IP, user, host, process, hash)
3. Time window for investigation

"""

import asyncpg
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging


class QueryGeneratorService:
    """Generate queries ONLY for sources we have logs from"""

    def __init__(self, db_pool: asyncpg.Pool, logger: logging.Logger = None):
        self.db_pool = db_pool
        self.logger = logger or logging.getLogger(__name__)
        self._available_sources_cache = None
        self._cache_timestamp = None
        self._cache_ttl = 3600  # 1 hour

    async def get_available_sources(self, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """
        Detect which sources we actually have logs from

        Extracts detailed source information from log_data JSONB:
        - platform: Collection platform (elastic, splunk, fortinet)
        - vendor: Actual vendor (Fortinet, CrowdStrike, Microsoft)
        - product: Specific product (<PERSON><PERSON>, Falcon, Sysmon)

        Returns:
            [
                {
                    'platform': 'fortinet',
                    'vendor': 'Fortinet',
                    'product': 'Fortigate',
                    'oldest_log': datetime,
                    'newest_log': datetime,
                    'log_count_7d': 39090
                },
                ...
            ]
        """
        # Check cache
        if not force_refresh and self._available_sources_cache:
            cache_age = (datetime.now() - self._cache_timestamp).total_seconds()
            if cache_age < self._cache_ttl:
                self.logger.debug("Returning cached available sources")
                return self._available_sources_cache

        self.logger.info("Querying ingestion_logs to detect available sources")

        try:
            # Extract detailed source info from JSONB log_data
            async with self.db_pool.acquire() as conn:
                results = await conn.fetch("""
                    SELECT
                        source_type as platform,
                        COALESCE(
                            log_data->'log'->'data'->'observer'->>'vendor',
                            log_data->'log'->'data'->'agent'->>'type',
                            source_type
                        ) as vendor,
                        COALESCE(
                            log_data->'log'->'data'->'observer'->>'product',
                            log_data->'log'->'data'->'event'->>'dataset',
                            'unknown'
                        ) as product,
                        MIN(created_at) as oldest_log,
                        MAX(created_at) as newest_log,
                        COUNT(*) as log_count_7d
                    FROM ingestion_logs
                    WHERE created_at > NOW() - INTERVAL '7 days'
                    GROUP BY platform, vendor, product
                    ORDER BY vendor, product
                """)

            sources = []
            for row in results:
                # Handle both tuple and dict cursor results
                if isinstance(row, dict):
                    sources.append({
                        'platform': row['platform'],
                        'vendor': row['vendor'],
                        'product': row['product'],
                        'oldest_log': row['oldest_log'].isoformat() if row['oldest_log'] else None,
                        'newest_log': row['newest_log'].isoformat() if row['newest_log'] else None,
                        'log_count_7d': row['log_count_7d']
                    })
                else:
                    sources.append({
                        'platform': row[0],
                        'vendor': row[1],
                        'product': row[2],
                        'oldest_log': row[3].isoformat() if row[3] else None,
                        'newest_log': row[4].isoformat() if row[4] else None,
                        'log_count_7d': row[5]
                    })

            # Update cache
            self._available_sources_cache = sources
            self._cache_timestamp = datetime.now()

            self.logger.info(f"Found {len(sources)} available sources")
            return sources

        except Exception as e:
            self.logger.error(f"Failed to get available sources: {e}")
            return []

    async def get_templates_for_entity(self, entity_type: str, source_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get query templates for specific entity type

        Args:
            entity_type: 'host', 'user', 'ip', 'process', 'hash'
            source_type: Optional filter for specific source (e.g., 'elastic_security')

        Returns:
            List of template dictionaries
        """
        try:
            async with self.db_pool.acquire() as conn:
                if source_type:
                    results = await conn.fetch("""
                        SELECT
                            template_id, source_type, source_name, query_language,
                            query_type, use_case, query_template, deep_link_template,
                            what_we_have, what_to_look_for, limitations,
                            example_query, example_entity_type
                        FROM query_templates
                        WHERE example_entity_type = $1
                          AND source_type = $2
                          AND active = true
                        ORDER BY source_type, use_case
                    """, entity_type, source_type)
                else:
                    self.logger.debug(f"Fetching templates for entity_type={entity_type}")
                    results = await conn.fetch("""
                        SELECT
                            template_id, source_type, source_name, query_language,
                            query_type, use_case, query_template, deep_link_template,
                            what_we_have, what_to_look_for, limitations,
                            example_query, example_entity_type
                        FROM query_templates
                        WHERE example_entity_type = $1
                          AND active = true
                        ORDER BY source_type, use_case
                    """, entity_type)
                    self.logger.debug(f"Got {len(results) if results else 0} results from database")

            templates = []
            for row in results:
                # Handle both tuple and dict cursor results
                if isinstance(row, dict):
                    templates.append({
                        'template_id': str(row['template_id']),
                        'source_type': row['source_type'],
                        'source_name': row['source_name'],
                        'query_language': row['query_language'],
                        'query_type': row['query_type'],
                        'use_case': row['use_case'],
                        'query_template': row['query_template'],
                        'deep_link_template': row['deep_link_template'],
                        'what_we_have': row['what_we_have'],
                        'what_to_look_for': row['what_to_look_for'],
                        'limitations': row['limitations'],
                        'example_query': row['example_query'],
                        'example_entity_type': row['example_entity_type']
                    })
                else:
                    templates.append({
                        'template_id': str(row[0]),
                        'source_type': row[1],
                        'source_name': row[2],
                        'query_language': row[3],
                        'query_type': row[4],
                        'use_case': row[5],
                        'query_template': row[6],
                        'deep_link_template': row[7],
                        'what_we_have': row[8],
                        'what_to_look_for': row[9],
                        'limitations': row[10],
                        'example_query': row[11],
                        'example_entity_type': row[12]
                    })

            self.logger.debug(f"Found {len(templates)} templates for entity_type={entity_type}")
            return templates

        except Exception as e:
            import traceback
            self.logger.error(f"Failed to get templates: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    async def generate_queries_for_entity(
        self,
        entity_type: str,
        entity_value: str,
        time_window: Optional[Dict[str, datetime]] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate queries for an entity across ALL available sources

        Args:
            entity_type: 'host', 'user', 'ip', 'process', 'hash'
            entity_value: Actual value (e.g., 'SERVER-01', 'jdoe', '*************')
            time_window: {'start': datetime, 'end': datetime} or None for defaults

        Returns:
            [
                {
                    'source_type': 'elastic_security',
                    'source_name': 'Elastic Security Alerts',
                    'query_language': 'KQL',
                    'query': 'host.name:"SERVER-01" AND @timestamp:[...]',
                    'deep_link': 'http://kibana:5601/...',
                    'what_we_have': '...',
                    'what_to_look_for': [...],
                    'limitations': '...',
                    'available': true,  # Do we have logs from this source?
                    'time_range': {'start': '...', 'end': '...'}
                },
                ...
            ]
        """
        # Default time window: ±30 minutes from now
        if not time_window:
            now = datetime.now()
            time_window = {
                'start': now - timedelta(minutes=30),
                'end': now + timedelta(minutes=30)
            }

        # Format times for templates
        start_time = time_window['start'].isoformat()
        end_time = time_window['end'].isoformat()

        # Get available sources with detailed vendor/product info
        available_sources = await self.get_available_sources()

        # Create matching keys: check both platform and vendor/product combinations
        available_keys = set()
        for s in available_sources:
            # Add platform (for backwards compatibility)
            available_keys.add(s['platform'])
            # Add vendor name (case-insensitive)
            if s['vendor']:
                available_keys.add(s['vendor'].lower())
            # Add vendor+product combination
            if s['vendor'] and s['product']:
                available_keys.add(f"{s['vendor'].lower()}_{s['product'].lower()}")

        self.logger.debug(f"Available source keys: {available_keys}")

        # Get templates for this entity type
        templates = await self.get_templates_for_entity(entity_type)

        generated_queries = []

        for template in templates:
            # Check if we have logs from this source (flexible matching)
            source_type_lower = template['source_type'].lower()
            source_name_lower = template['source_name'].lower() if template['source_name'] else ''

            is_available = (
                template['source_type'] in available_keys or
                source_type_lower in available_keys or
                any(vendor in source_name_lower for vendor in available_keys if vendor)
            )

            # Generate query by replacing variables
            query = template['query_template']
            query = query.replace('{entity_value}', entity_value)
            query = query.replace('{entity_type}', entity_type)
            query = query.replace('{start_time}', start_time)
            query = query.replace('{end_time}', end_time)

            # Generate deep link
            deep_link = None
            if template['deep_link_template']:
                deep_link = template['deep_link_template']
                deep_link = deep_link.replace('{entity_value}', entity_value)
                deep_link = deep_link.replace('{entity_type}', entity_type)
                deep_link = deep_link.replace('{start_time}', start_time)
                deep_link = deep_link.replace('{end_time}', end_time)

            # Find matching source details for available queries
            source_details = None
            if is_available:
                for source in available_sources:
                    if (source['platform'] == template['source_type'] or
                        source['vendor'].lower() in source_type_lower or
                        source['vendor'].lower() in source_name_lower):
                        source_details = source
                        break

            generated_queries.append({
                'source_type': template['source_type'],
                'source_name': template['source_name'],
                'query_language': template['query_language'],
                'use_case': template['use_case'],
                'query': query,
                'deep_link': deep_link,
                'what_we_have': template['what_we_have'],
                'what_to_look_for': template['what_to_look_for'],
                'limitations': template['limitations'],
                'available': is_available,  # CRITICAL: Do we have logs?
                'time_range': {
                    'start': start_time,
                    'end': end_time
                },
                # Add detailed source info if available
                'source_details': {
                    'platform': source_details['platform'] if source_details else None,
                    'vendor': source_details['vendor'] if source_details else None,
                    'product': source_details['product'] if source_details else None,
                    'log_count': source_details['log_count_7d'] if source_details else 0
                } if source_details else None
            })

        # Sort: available sources first, then by source_type
        generated_queries.sort(key=lambda x: (not x['available'], x['source_type']))

        self.logger.info(
            f"Generated {len(generated_queries)} queries for {entity_type}={entity_value}, "
            f"{sum(1 for q in generated_queries if q['available'])} sources available"
        )

        return generated_queries

    async def generate_multi_entity_query(
        self,
        entities: Dict[str, str],
        time_window: Optional[Dict[str, datetime]] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate queries for multiple entities (e.g., user + host + IP)

        Args:
            entities: {'user': 'jdoe', 'host': 'SERVER-01', 'ip': '*************'}
            time_window: Time range

        Returns:
            Combined queries where possible (future enhancement)
        """
        # For now, generate individual queries and mark for combination
        # Future: Smart query combining (e.g., "user X on host Y")

        all_queries = []

        for entity_type, entity_value in entities.items():
            queries = await self.generate_queries_for_entity(
                entity_type, entity_value, time_window
            )
            all_queries.extend(queries)

        self.logger.info(f"Generated {len(all_queries)} queries for multi-entity investigation")
        return all_queries

    async def get_query_guidance(self, entity_type: str, entity_value: str) -> Dict[str, Any]:
        """
        Get investigation guidance without generating full queries

        Returns summary of what to investigate and where
        """
        available_sources = await self.get_available_sources()
        templates = await self.get_templates_for_entity(entity_type)

        available_count = sum(
            1 for t in templates
            if any(s['source_type'] == t['source_type'] for s in available_sources)
        )

        return {
            'entity_type': entity_type,
            'entity_value': entity_value,
            'total_templates': len(templates),
            'available_sources': available_count,
            'missing_sources': len(templates) - available_count,
            'sources': [
                {
                    'source_type': t['source_type'],
                    'source_name': t['source_name'],
                    'available': any(s['source_type'] == t['source_type'] for s in available_sources),
                    'what_we_have': t['what_we_have'],
                    'limitations': t['limitations']
                }
                for t in templates
            ]
        }
