#!/bin/bash
# SIEMLess v2.0 - Startup Script

set -e

echo "🚀 Starting SIEMLess v2.0 - 5-Engine Architecture"
echo "=================================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from example..."
    cp .env.example .env
    echo "✅ Created .env file. Please edit it with your configuration."
    echo "📝 Edit .env file with your API keys and database settings"
    exit 1
fi

# Create logs directory
mkdir -p logs

echo "🧹 Cleaning up any existing containers..."
docker-compose down -v --remove-orphans 2>/dev/null || true

echo "🏗️  Building and starting infrastructure services..."
docker-compose up -d redis postgres

echo "⏳ Waiting for infrastructure to be ready..."
sleep 10

# Wait for PostgreSQL to be ready
echo "🔍 Waiting for PostgreSQL..."
for i in {1..30}; do
    if docker-compose exec -T postgres pg_isready -U siemless >/dev/null 2>&1; then
        echo "✅ PostgreSQL is ready"
        break
    fi
    echo "   Waiting for PostgreSQL... ($i/30)"
    sleep 2
done

# Wait for Redis to be ready
echo "🔍 Waiting for Redis..."
for i in {1..30}; do
    if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
        echo "✅ Redis is ready"
        break
    fi
    echo "   Waiting for Redis... ($i/30)"
    sleep 2
done

echo "🧠 Starting Intelligence Engine (Priority 1)..."
docker-compose up -d intelligence_engine

echo "⏳ Waiting for Intelligence Engine to initialize..."
sleep 15

echo "💾 Starting Backend Engine (Priority 2)..."
docker-compose up -d backend_engine

echo "⏳ Waiting for Backend Engine to initialize..."
sleep 15

echo "📊 Starting monitoring and coordination..."
docker-compose up -d engine_coordinator

echo "🔍 Checking engine status..."
docker-compose ps

echo ""
echo "✅ SIEMLess v2.0 engines are starting up!"
echo ""
echo "📊 Engine Status:"
echo "  - Intelligence Engine: Starting (AI consensus & pattern crystallization)"
echo "  - Backend Engine: Starting (storage & ruleset management)"
echo "  - Ingestion Engine: Placeholder (future implementation)"
echo "  - Contextualization Engine: Placeholder (future implementation)"
echo "  - Delivery Engine: Placeholder (future implementation)"
echo ""
echo "🧪 Run tests with: python test_engines.py"
echo "📋 View logs with: docker-compose logs -f [service_name]"
echo "🛑 Stop with: docker-compose down"
echo ""
echo "⏳ Engines may take 1-2 minutes to fully initialize..."