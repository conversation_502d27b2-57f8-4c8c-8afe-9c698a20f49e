import React, { lazy, Suspense } from 'react'
import { createBrowserRouter, Navigate } from 'react-router-dom'
import AppShell from '../components/layout/AppShell'
import Dashboard from '../pages/Dashboard'
import LoadingSpinner from '../components/common/LoadingSpinner'

// Lazy load pages for better performance
const AlertQueue = lazy(() => import('../pages/AlertQueue'))
const ActiveCases = lazy(() => import('../pages/ActiveCases'))
const MITREOverview = lazy(() => import('../pages/MITREOverview'))
const NewInvestigation = lazy(() => import('../pages/investigation/NewInvestigation'))
const EntityExplorer = lazy(() => import('../pages/investigation/EntityExplorer'))
const RelationshipMapper = lazy(() => import('../pages/investigation/RelationshipMapper'))
const TimelineAnalysis = lazy(() => import('../pages/investigation/TimelineAnalysis'))
const AIGuide = lazy(() => import('../pages/investigation/AIGuide'))
const PatternLibrary = lazy(() => import('../pages/engineering/PatternLibrary'))
const CrystallizationQueue = lazy(() => import('../pages/engineering/CrystallizationQueue'))
const RuleTesting = lazy(() => import('../pages/engineering/RuleTesting'))
const GitHubSync = lazy(() => import('../pages/engineering/GitHubSync'))
const PerformanceMetrics = lazy(() => import('../pages/analytics/PerformanceMetrics'))
const CostAnalysis = lazy(() => import('../pages/analytics/CostAnalysis'))
const UserManagement = lazy(() => import('../pages/admin/UserManagement'))
const SystemSettings = lazy(() => import('../pages/admin/SystemSettings'))

// Wrapper for lazy loaded components
const LazyWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Suspense fallback={<LoadingSpinner />}>
    {children}
  </Suspense>
)

export const router = createBrowserRouter([
  {
    path: '/',
    element: <AppShell />,
    children: [
      {
        index: true,
        element: <Dashboard />
      },
      {
        path: 'alerts',
        element: <LazyWrapper><AlertQueue /></LazyWrapper>
      },
      {
        path: 'cases',
        children: [
          {
            index: true,
            element: <LazyWrapper><ActiveCases /></LazyWrapper>
          },
          {
            path: ':caseId',
            element: <LazyWrapper><ActiveCases /></LazyWrapper>
          }
        ]
      },
      {
        path: 'mitre',
        element: <LazyWrapper><MITREOverview /></LazyWrapper>
      },
      {
        path: 'investigation',
        children: [
          {
            path: 'new',
            element: <LazyWrapper><NewInvestigation /></LazyWrapper>
          },
          {
            path: 'entities',
            children: [
              {
                index: true,
                element: <LazyWrapper><EntityExplorer /></LazyWrapper>
              },
              {
                path: ':entityId',
                element: <LazyWrapper><EntityExplorer /></LazyWrapper>
              }
            ]
          },
          {
            path: 'relationships',
            element: <LazyWrapper><RelationshipMapper /></LazyWrapper>
          },
          {
            path: 'timeline',
            element: <LazyWrapper><TimelineAnalysis /></LazyWrapper>
          },
          {
            path: 'ai-guide',
            element: <LazyWrapper><AIGuide /></LazyWrapper>
          }
        ]
      },
      {
        path: 'engineering',
        children: [
          {
            path: 'patterns',
            element: <LazyWrapper><PatternLibrary /></LazyWrapper>
          },
          {
            path: 'crystallization',
            element: <LazyWrapper><CrystallizationQueue /></LazyWrapper>
          },
          {
            path: 'rules',
            element: <LazyWrapper><RuleTesting /></LazyWrapper>
          },
          {
            path: 'github',
            element: <LazyWrapper><GitHubSync /></LazyWrapper>
          }
        ]
      },
      {
        path: 'analytics',
        children: [
          {
            path: 'performance',
            element: <LazyWrapper><PerformanceMetrics /></LazyWrapper>
          },
          {
            path: 'costs',
            element: <LazyWrapper><CostAnalysis /></LazyWrapper>
          },
          {
            path: 'capacity',
            element: <LazyWrapper><PerformanceMetrics /></LazyWrapper>
          },
          {
            path: 'risk',
            element: <LazyWrapper><PerformanceMetrics /></LazyWrapper>
          }
        ]
      },
      {
        path: 'admin',
        children: [
          {
            path: 'users',
            element: <LazyWrapper><UserManagement /></LazyWrapper>
          },
          {
            path: 'settings',
            element: <LazyWrapper><SystemSettings /></LazyWrapper>
          },
          {
            path: 'integrations',
            element: <LazyWrapper><SystemSettings /></LazyWrapper>
          },
          {
            path: 'audit',
            element: <LazyWrapper><SystemSettings /></LazyWrapper>
          }
        ]
      }
    ]
  },
  {
    path: '*',
    element: <Navigate to="/" replace />
  }
])