#!/usr/bin/env python3
"""
SOC Playbook & Investigation Guide Extractor

Extracts and standardizes SOC operational knowledge:
- Incident response playbooks
- Investigation procedures
- Escalation workflows
- Response templates
- Decision trees
- Standard operating procedures (SOPs)

This provides deep SOC operational intelligence for automation and knowledge transfer.
"""

import asyncio
import json
import yaml
import re
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import requests
from pathlib import Path


class PlaybookType(Enum):
    """Types of SOC playbooks and procedures"""
    INCIDENT_RESPONSE = "incident_response"
    INVESTIGATION_GUIDE = "investigation_guide"
    ESCALATION_PROCEDURE = "escalation_procedure"
    RESPONSE_TEMPLATE = "response_template"
    DECISION_TREE = "decision_tree"
    SOP = "standard_operating_procedure"
    WORKFLOW = "workflow"
    RUNBOOK = "runbook"
    COMMUNICATION_PLAN = "communication_plan"
    RECOVERY_PROCEDURE = "recovery_procedure"
    THREAT_HUNTING = "threat_hunting"
    FORENSICS_GUIDE = "forensics_guide"


class PlaybookSource(Enum):
    """Sources where playbooks can be found"""
    CONFLUENCE = "confluence"
    SHAREPOINT = "sharepoint"
    JIRA = "jira"
    SERVICENOW = "servicenow"
    PHANTOM = "phantom"
    DEMISTO = "demisto"
    SIEMPLIFY = "siemplify"
    SPLUNK_SOAR = "splunk_soar"
    SWIMLANE = "swimlane"
    RESILIENT = "resilient"
    THREATCONNECT = "threatconnect"
    FILE_SYSTEM = "file_system"
    GITHUB = "github"
    GITLAB = "gitlab"
    CUSTOM_WIKI = "custom_wiki"


@dataclass
class PlaybookStep:
    """Individual step in a playbook"""
    step_id: str
    title: str
    description: str
    action_type: str  # manual, automated, decision, query, etc.
    estimated_time: Optional[int] = None  # minutes
    required_tools: List[str] = None
    required_permissions: List[str] = None
    inputs: List[str] = None
    outputs: List[str] = None
    decision_criteria: Optional[str] = None
    automation_command: Optional[str] = None
    verification_method: Optional[str] = None

    def __post_init__(self):
        if self.required_tools is None:
            self.required_tools = []
        if self.required_permissions is None:
            self.required_permissions = []
        if self.inputs is None:
            self.inputs = []
        if self.outputs is None:
            self.outputs = []


@dataclass
class SOCPlaybook:
    """Complete SOC playbook with metadata"""
    playbook_id: str
    playbook_type: PlaybookType
    source: PlaybookSource
    title: str
    description: str
    severity_levels: List[str]
    incident_types: List[str]
    mitre_techniques: List[str]
    steps: List[PlaybookStep]
    metadata: Dict[str, Any]
    author: Optional[str] = None
    version: Optional[str] = None
    last_updated: Optional[datetime] = None
    approval_status: str = "draft"
    average_execution_time: Optional[int] = None  # minutes
    success_rate: Optional[float] = None
    usage_frequency: Optional[int] = None
    escalation_triggers: List[str] = None
    communication_channels: List[str] = None
    required_roles: List[str] = None
    tags: List[str] = None

    def __post_init__(self):
        if self.escalation_triggers is None:
            self.escalation_triggers = []
        if self.communication_channels is None:
            self.communication_channels = []
        if self.required_roles is None:
            self.required_roles = []
        if self.tags is None:
            self.tags = []


class SOCPlaybookExtractor:
    """
    Extracts SOC playbooks and procedures from various sources
    """

    def __init__(self):
        self.extracted_playbooks = []
        self.source_extractors = {
            PlaybookSource.CONFLUENCE: self._extract_from_confluence,
            PlaybookSource.SHAREPOINT: self._extract_from_sharepoint,
            PlaybookSource.JIRA: self._extract_from_jira,
            PlaybookSource.SERVICENOW: self._extract_from_servicenow,
            PlaybookSource.PHANTOM: self._extract_from_phantom,
            PlaybookSource.SPLUNK_SOAR: self._extract_from_splunk_soar,
            PlaybookSource.GITHUB: self._extract_from_github,
            PlaybookSource.FILE_SYSTEM: self._extract_from_filesystem
        }

    async def extract_playbooks(self, source: PlaybookSource,
                               credentials: Dict[str, Any],
                               playbook_types: List[PlaybookType] = None) -> List[SOCPlaybook]:
        """
        Extract playbooks from specified source

        Args:
            source: Source system to extract from
            credentials: Authentication credentials
            playbook_types: Specific types to extract (default: all)

        Returns:
            List of extracted playbooks
        """
        if playbook_types is None:
            playbook_types = list(PlaybookType)

        try:
            if source in self.source_extractors:
                extractor = self.source_extractors[source]
                playbooks = await extractor(credentials, playbook_types)
                self.extracted_playbooks.extend(playbooks)
                return playbooks
            else:
                raise ValueError(f"No extractor available for source: {source.value}")

        except Exception as e:
            print(f"Failed to extract from {source.value}: {e}")
            return []

    async def _extract_from_confluence(self, credentials: Dict[str, Any],
                                      playbook_types: List[PlaybookType]) -> List[SOCPlaybook]:
        """Extract playbooks from Atlassian Confluence"""
        playbooks = []

        base_url = credentials.get('base_url')
        username = credentials.get('username')
        api_token = credentials.get('api_token')
        space_key = credentials.get('space_key', 'SOC')

        headers = {'Content-Type': 'application/json'}
        auth = (username, api_token)

        try:
            # Search for SOC-related pages
            search_terms = [
                'incident response', 'playbook', 'investigation', 'procedure',
                'runbook', 'escalation', 'workflow', 'SOP'
            ]

            for term in search_terms:
                # Search Confluence pages
                search_url = f"{base_url}/rest/api/content/search"
                params = {
                    'cql': f'space={space_key} AND text~"{term}"',
                    'expand': 'body.storage,version,space'
                }

                response = requests.get(search_url, headers=headers, auth=auth, params=params)

                if response.status_code == 200:
                    results = response.json().get('results', [])

                    for page in results:
                        playbook = await self._parse_confluence_page(page, PlaybookSource.CONFLUENCE)
                        if playbook and playbook.playbook_type in playbook_types:
                            playbooks.append(playbook)

        except Exception as e:
            print(f"Error extracting from Confluence: {e}")

        return playbooks

    async def _extract_from_phantom(self, credentials: Dict[str, Any],
                                   playbook_types: List[PlaybookType]) -> List[SOCPlaybook]:
        """Extract playbooks from Splunk Phantom/SOAR"""
        playbooks = []

        base_url = credentials.get('base_url')
        api_token = credentials.get('api_token')

        headers = {
            'Authorization': f'Bearer {api_token}',
            'Content-Type': 'application/json'
        }

        try:
            # Get all playbooks from Phantom
            response = requests.get(f"{base_url}/rest/playbook", headers=headers)

            if response.status_code == 200:
                phantom_playbooks = response.json().get('data', [])

                for pb in phantom_playbooks:
                    # Get detailed playbook information
                    detail_response = requests.get(
                        f"{base_url}/rest/playbook/{pb['id']}", headers=headers)

                    if detail_response.status_code == 200:
                        playbook_detail = detail_response.json()
                        playbook = await self._parse_phantom_playbook(
                            playbook_detail, PlaybookSource.PHANTOM)

                        if playbook and playbook.playbook_type in playbook_types:
                            playbooks.append(playbook)

        except Exception as e:
            print(f"Error extracting from Phantom: {e}")

        return playbooks

    async def _extract_from_github(self, credentials: Dict[str, Any],
                                  playbook_types: List[PlaybookType]) -> List[SOCPlaybook]:
        """Extract playbooks from GitHub repositories"""
        playbooks = []

        github_token = credentials.get('github_token')
        repositories = credentials.get('repositories', [])

        headers = {
            'Authorization': f'token {github_token}',
            'Accept': 'application/vnd.github.v3+json'
        }

        try:
            for repo in repositories:
                # Search for playbook files in repository
                search_url = f"https://api.github.com/search/code"
                params = {
                    'q': f'repo:{repo} filename:playbook OR filename:runbook OR filename:procedure',
                    'per_page': 100
                }

                response = requests.get(search_url, headers=headers, params=params)

                if response.status_code == 200:
                    files = response.json().get('items', [])

                    for file_info in files:
                        # Download file content
                        content_response = requests.get(file_info['download_url'])

                        if content_response.status_code == 200:
                            content = content_response.text
                            playbook = await self._parse_github_file(
                                content, file_info, PlaybookSource.GITHUB)

                            if playbook and playbook.playbook_type in playbook_types:
                                playbooks.append(playbook)

        except Exception as e:
            print(f"Error extracting from GitHub: {e}")

        return playbooks

    async def _extract_from_servicenow(self, credentials: Dict[str, Any],
                                      playbook_types: List[PlaybookType]) -> List[SOCPlaybook]:
        """Extract workflows and procedures from ServiceNow"""
        playbooks = []

        instance_url = credentials.get('instance_url')
        username = credentials.get('username')
        password = credentials.get('password')

        auth = (username, password)
        headers = {'Content-Type': 'application/json', 'Accept': 'application/json'}

        try:
            # Get workflow definitions
            workflow_url = f"{instance_url}/api/now/table/wf_workflow"
            params = {'sysparm_query': 'active=true'}

            response = requests.get(workflow_url, headers=headers, auth=auth, params=params)

            if response.status_code == 200:
                workflows = response.json().get('result', [])

                for wf in workflows:
                    playbook = await self._parse_servicenow_workflow(wf, PlaybookSource.SERVICENOW)
                    if playbook and playbook.playbook_type in playbook_types:
                        playbooks.append(playbook)

        except Exception as e:
            print(f"Error extracting from ServiceNow: {e}")

        return playbooks

    async def _parse_confluence_page(self, page: Dict[str, Any],
                                    source: PlaybookSource) -> Optional[SOCPlaybook]:
        """Parse Confluence page into SOCPlaybook format"""
        try:
            content = page.get('body', {}).get('storage', {}).get('value', '')
            title = page.get('title', '')

            # Detect playbook type from title and content
            playbook_type = self._detect_playbook_type(title, content)

            # Extract steps from content
            steps = self._extract_steps_from_html(content)

            # Extract MITRE techniques
            mitre_techniques = self._extract_mitre_techniques_from_text(content)

            # Extract metadata
            metadata = {
                'confluence_id': page.get('id'),
                'space_key': page.get('space', {}).get('key'),
                'version': page.get('version', {}).get('number'),
                'created': page.get('history', {}).get('createdDate'),
                'creator': page.get('history', {}).get('createdBy', {}).get('displayName')
            }

            playbook = SOCPlaybook(
                playbook_id=f"confluence_{page.get('id')}",
                playbook_type=playbook_type,
                source=source,
                title=title,
                description=self._extract_description_from_content(content),
                severity_levels=self._extract_severity_levels(content),
                incident_types=self._extract_incident_types(content),
                mitre_techniques=mitre_techniques,
                steps=steps,
                metadata=metadata,
                last_updated=datetime.fromisoformat(
                    page.get('version', {}).get('when', '').replace('Z', '+00:00')
                ) if page.get('version', {}).get('when') else None,
                tags=['confluence', 'soc', 'playbook']
            )

            return playbook

        except Exception as e:
            print(f"Error parsing Confluence page: {e}")
            return None

    async def _parse_phantom_playbook(self, playbook_data: Dict[str, Any],
                                     source: PlaybookSource) -> Optional[SOCPlaybook]:
        """Parse Phantom/SOAR playbook into SOCPlaybook format"""
        try:
            # Extract steps from Phantom playbook structure
            steps = []

            # Phantom playbooks have a different structure with blocks and connections
            blocks = playbook_data.get('draft', {}).get('blocks', [])

            for block in blocks:
                step = PlaybookStep(
                    step_id=block.get('id', ''),
                    title=block.get('name', ''),
                    description=block.get('description', ''),
                    action_type=block.get('type', 'action'),
                    automation_command=json.dumps(block.get('parameters', {}))
                )
                steps.append(step)

            playbook = SOCPlaybook(
                playbook_id=f"phantom_{playbook_data.get('id')}",
                playbook_type=PlaybookType.INCIDENT_RESPONSE,  # Most Phantom playbooks are IR
                source=source,
                title=playbook_data.get('name', ''),
                description=playbook_data.get('description', ''),
                severity_levels=[],  # Extract from playbook logic
                incident_types=[],   # Extract from playbook categories
                mitre_techniques=[], # Extract from playbook actions
                steps=steps,
                metadata={
                    'phantom_id': playbook_data.get('id'),
                    'category': playbook_data.get('category'),
                    'tags': playbook_data.get('tags', []),
                    'version': playbook_data.get('version')
                },
                tags=['phantom', 'soar', 'automation']
            )

            return playbook

        except Exception as e:
            print(f"Error parsing Phantom playbook: {e}")
            return None

    def _detect_playbook_type(self, title: str, content: str) -> PlaybookType:
        """Detect playbook type from title and content"""
        title_lower = title.lower()
        content_lower = content.lower()

        # Keywords for different playbook types
        type_keywords = {
            PlaybookType.INCIDENT_RESPONSE: ['incident response', 'ir', 'breach', 'compromise'],
            PlaybookType.INVESTIGATION_GUIDE: ['investigation', 'investigate', 'forensic', 'analysis'],
            PlaybookType.ESCALATION_PROCEDURE: ['escalation', 'escalate', 'notify', 'alert'],
            PlaybookType.THREAT_HUNTING: ['hunting', 'hunt', 'proactive', 'threat intelligence'],
            PlaybookType.FORENSICS_GUIDE: ['forensics', 'forensic', 'evidence', 'preservation'],
            PlaybookType.SOP: ['sop', 'standard operating', 'procedure', 'process'],
            PlaybookType.WORKFLOW: ['workflow', 'flow', 'process', 'sequence'],
            PlaybookType.RUNBOOK: ['runbook', 'run book', 'operational', 'maintenance']
        }

        # Score each type based on keyword matches
        scores = {}
        for ptype, keywords in type_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in title_lower:
                    score += 3  # Title matches are weighted more
                if keyword in content_lower:
                    score += 1
            scores[ptype] = score

        # Return the type with highest score, defaulting to INCIDENT_RESPONSE
        if scores:
            return max(scores, key=scores.get)
        else:
            return PlaybookType.INCIDENT_RESPONSE

    def _extract_steps_from_html(self, content: str) -> List[PlaybookStep]:
        """Extract procedural steps from HTML/text content"""
        steps = []

        # Look for numbered lists, bullet points, or step markers
        step_patterns = [
            r'(?:Step\s*)?(\d+)\.?\s*([^\n]+)',  # "Step 1. Do something"
            r'(?:^|\n)\s*[-*]\s*([^\n]+)',       # "- Do something"
            r'(?:^|\n)\s*\d+\.\s*([^\n]+)'       # "1. Do something"
        ]

        step_counter = 1
        for pattern in step_patterns:
            matches = re.findall(pattern, content, re.MULTILINE | re.IGNORECASE)

            for match in matches:
                if isinstance(match, tuple):
                    step_text = match[-1] if len(match) > 1 else match[0]
                else:
                    step_text = match

                step = PlaybookStep(
                    step_id=f"step_{step_counter}",
                    title=f"Step {step_counter}",
                    description=step_text.strip(),
                    action_type="manual"  # Default to manual unless automation detected
                )

                # Detect if step involves automation
                if any(keyword in step_text.lower() for keyword in
                       ['script', 'command', 'execute', 'run', 'api', 'query']):
                    step.action_type = "automated"

                steps.append(step)
                step_counter += 1

        return steps

    def _extract_mitre_techniques_from_text(self, text: str) -> List[str]:
        """Extract MITRE ATT&CK technique IDs from text"""
        if not text:
            return []

        # Pattern to match MITRE technique IDs
        pattern = r'T\d{4}(?:\.\d{3})?'
        matches = re.findall(pattern, text, re.IGNORECASE)
        return list(set(matches))

    def _extract_severity_levels(self, content: str) -> List[str]:
        """Extract severity levels mentioned in content"""
        severity_keywords = ['critical', 'high', 'medium', 'low', 'info', 'informational']
        found_severities = []

        content_lower = content.lower()
        for severity in severity_keywords:
            if severity in content_lower:
                found_severities.append(severity.capitalize())

        return list(set(found_severities))

    def _extract_incident_types(self, content: str) -> List[str]:
        """Extract incident types mentioned in content"""
        incident_keywords = [
            'malware', 'phishing', 'breach', 'ransomware', 'ddos', 'insider threat',
            'data leak', 'unauthorized access', 'social engineering', 'apt'
        ]
        found_types = []

        content_lower = content.lower()
        for incident_type in incident_keywords:
            if incident_type in content_lower:
                found_types.append(incident_type.title())

        return list(set(found_types))

    def _extract_description_from_content(self, content: str) -> str:
        """Extract description from content"""
        # Remove HTML tags for plain text
        clean_content = re.sub(r'<[^>]+>', '', content)

        # Take first paragraph or first 200 characters
        paragraphs = clean_content.split('\n\n')
        if paragraphs:
            description = paragraphs[0].strip()
            if len(description) > 200:
                description = description[:197] + "..."
            return description

        return "Extracted SOC playbook"

    async def get_extraction_statistics(self) -> Dict[str, Any]:
        """Get statistics about extracted playbooks"""
        stats = {
            'total_playbooks': len(self.extracted_playbooks),
            'playbooks_by_type': {},
            'playbooks_by_source': {},
            'mitre_technique_coverage': set(),
            'average_steps_per_playbook': 0,
            'automation_percentage': 0
        }

        total_steps = 0
        automated_steps = 0

        for playbook in self.extracted_playbooks:
            # Count by type
            ptype = playbook.playbook_type.value
            stats['playbooks_by_type'][ptype] = \
                stats['playbooks_by_type'].get(ptype, 0) + 1

            # Count by source
            source = playbook.source.value
            stats['playbooks_by_source'][source] = \
                stats['playbooks_by_source'].get(source, 0) + 1

            # Collect MITRE techniques
            stats['mitre_technique_coverage'].update(playbook.mitre_techniques)

            # Count steps and automation
            total_steps += len(playbook.steps)
            automated_steps += sum(1 for step in playbook.steps
                                 if step.action_type in ['automated', 'query'])

        # Calculate averages
        if self.extracted_playbooks:
            stats['average_steps_per_playbook'] = total_steps / len(self.extracted_playbooks)

        if total_steps > 0:
            stats['automation_percentage'] = (automated_steps / total_steps) * 100

        # Convert set to list for JSON serialization
        stats['mitre_technique_coverage'] = list(stats['mitre_technique_coverage'])

        return stats

    async def export_for_automation(self) -> List[Dict[str, Any]]:
        """Export playbooks in format suitable for automation platforms"""
        automation_data = []

        for playbook in self.extracted_playbooks:
            automation_format = {
                'playbook_id': playbook.playbook_id,
                'name': playbook.title,
                'description': playbook.description,
                'triggers': {
                    'incident_types': playbook.incident_types,
                    'severity_levels': playbook.severity_levels,
                    'mitre_techniques': playbook.mitre_techniques
                },
                'workflow': {
                    'steps': [asdict(step) for step in playbook.steps],
                    'decision_points': [step.step_id for step in playbook.steps
                                      if step.action_type == 'decision'],
                    'automation_commands': [step.automation_command for step in playbook.steps
                                          if step.automation_command]
                },
                'metadata': {
                    'source': playbook.source.value,
                    'original_playbook': asdict(playbook),
                    'extracted_at': datetime.utcnow().isoformat(),
                    'ready_for_automation': True
                }
            }

            automation_data.append(automation_format)

        return automation_data


if __name__ == "__main__":
    # Test the SOC Playbook Extractor
    async def test_extractor():
        extractor = SOCPlaybookExtractor()
        print("✅ SOC Playbook Extractor initialized successfully")
        print("Ready to extract from: Confluence, GitHub, Phantom, ServiceNow, etc.")

    asyncio.run(test_extractor())