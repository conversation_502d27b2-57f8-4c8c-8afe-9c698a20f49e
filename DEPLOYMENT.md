# SIEMLess v2.0 Deployment Guide

## Overview
SIEMLess v2.0 is fully containerized using Docker, with each engine running as an independent microservice. The architecture uses Redis for message passing and PostgreSQL for pattern storage.

## Prerequisites

- Docker Engine 24.0+
- Docker Compose 2.20+
- 8GB RAM minimum (16GB recommended)
- 20GB free disk space

## Quick Start

### 1. <PERSON><PERSON> and Setup
```bash
# Clone the repository
git clone https://github.com/yourusername/siemless-v2.git
cd siemless-v2/v2

# Copy environment template
cp .env.example .env

# Edit .env with your configuration
nano .env
```

### 2. Build and Deploy
```bash
# Build all services
docker-compose build

# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f librarian
```

### 3. Verify Deployment
```bash
# Check Redis
docker exec siemless-v2-redis redis-cli ping
# Should return: PONG

# Check PostgreSQL
docker exec siemless-v2-postgres psql -U siemless -d siemless_v2 -c "SELECT 1"
# Should return: 1

# Check Librarian Engine
curl http://localhost:8000/health
# Should return: {"status": "healthy"}
```

## Service Architecture

### Core Services
| Service | Container | Port | Purpose |
|---------|-----------|------|---------|
| Redis | siemless-v2-redis | 6379 | Message queue |
| PostgreSQL | siemless-v2-postgres | 5433 | Pattern storage |
| Librarian | siemless-v2-librarian | - | Knowledge authority |
| Parser | siemless-v2-parser-1/2 | - | Log parsing (2 replicas) |
| API Gateway | siemless-v2-api | 8000 | Frontend API |

### Monitoring Services
| Service | Container | Port | Purpose |
|---------|-----------|------|---------|
| Prometheus | siemless-v2-prometheus | 9090 | Metrics collection |
| Grafana | siemless-v2-grafana | 3001 | Visualization |

## Scaling

### Horizontal Scaling
```bash
# Scale parser engines
docker-compose up -d --scale parser=4

# Scale specific engine
docker-compose up -d --scale entity_extractor=3
```

### Vertical Scaling
Edit `docker-compose.yml` to add resource limits:
```yaml
services:
  parser:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

## Configuration

### Environment Variables

Key variables in `.env`:

```bash
# Database
POSTGRES_PASSWORD=strong_password_here
POSTGRES_DB=siemless_v2

# Security
JWT_SECRET=generate_random_secret_here
SIEMLESS_MASTER_KEY=generate_random_key_here

# AI Providers (Optional)
GEMINI_API_KEY=your_key_here
ANTHROPIC_API_KEY=your_key_here

# Performance Tuning
PARSER_REPLICAS=2
BUFFER_THRESHOLD=10
```

### Pattern Library

Add custom patterns to `v2/patterns/`:
```bash
# Add new pattern file
cp my_patterns.json v2/patterns/

# Restart Librarian to load
docker-compose restart librarian
```

## Monitoring

### Grafana Dashboards
1. Access Grafana: http://localhost:3001
2. Login: admin / [GRAFANA_PASSWORD]
3. Import dashboards from `monitoring/grafana/dashboards/`

### Prometheus Metrics
- Access: http://localhost:9090
- Key metrics:
  - `pattern_hit_rate` - Pattern match success rate
  - `ai_fallback_rate` - AI usage frequency
  - `cost_per_gb` - Processing cost metrics
  - `message_queue_size` - Queue backlog

### Health Checks
```bash
# Check all services health
for service in librarian parser api_gateway; do
  echo "Checking $service..."
  curl -s http://localhost:8000/$service/health | jq .
done
```

## Backup & Recovery

### Backup Pattern Library
```bash
# Backup PostgreSQL
docker exec siemless-v2-postgres pg_dump -U siemless siemless_v2 > backup_$(date +%Y%m%d).sql

# Backup patterns
tar -czf patterns_backup_$(date +%Y%m%d).tar.gz v2/patterns/
```

### Restore
```bash
# Restore database
docker exec -i siemless-v2-postgres psql -U siemless siemless_v2 < backup_20240101.sql

# Restore patterns
tar -xzf patterns_backup_20240101.tar.gz
```

## Troubleshooting

### Common Issues

#### Services not starting
```bash
# Check logs
docker-compose logs [service_name]

# Check resource usage
docker stats

# Restart specific service
docker-compose restart [service_name]
```

#### Redis connection issues
```bash
# Test Redis connectivity
docker exec -it siemless-v2-redis redis-cli
> PING
> INFO replication
```

#### PostgreSQL connection issues
```bash
# Check PostgreSQL logs
docker logs siemless-v2-postgres

# Test connection
docker exec -it siemless-v2-postgres psql -U siemless -d siemless_v2
```

### Debug Mode
Enable debug logging:
```bash
# In .env
LOG_LEVEL=DEBUG

# Restart services
docker-compose restart
```

## Production Deployment

### Security Hardening

1. **Use secrets management**:
```yaml
# docker-compose.prod.yml
secrets:
  postgres_password:
    external: true
  jwt_secret:
    external: true
```

2. **Enable TLS**:
```yaml
services:
  api_gateway:
    environment:
      - TLS_CERT=/certs/cert.pem
      - TLS_KEY=/certs/key.pem
```

3. **Network isolation**:
```yaml
networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true
```

### Kubernetes Deployment

For production, consider Kubernetes:
```bash
# Convert docker-compose to K8s manifests
kompose convert -f docker-compose.yml

# Deploy to Kubernetes
kubectl apply -f k8s/
```

### High Availability

1. **Redis Sentinel** for failover
2. **PostgreSQL replication** for backup
3. **Load balancer** for API gateway
4. **Multi-region** deployment

## Performance Tuning

### Redis Optimization
```bash
# Edit redis.conf
maxmemory 2gb
maxmemory-policy allkeys-lru
```

### PostgreSQL Tuning
```sql
-- Increase shared buffers
ALTER SYSTEM SET shared_buffers = '1GB';

-- Optimize for SSD
ALTER SYSTEM SET random_page_cost = 1.1;
```

### Engine Tuning
```python
# In engine configuration
BUFFER_THRESHOLD = 20  # Process unknowns in larger batches
PATTERN_CACHE_SIZE = 10000  # Cache more patterns in memory
```

## Maintenance

### Log Rotation
```bash
# Add to crontab
0 0 * * * find /var/log/siemless-v2 -name "*.log" -mtime +7 -delete
```

### Pattern Updates
```bash
# Update patterns from repository
git pull origin main
docker-compose restart librarian
```

### System Updates
```bash
# Update base images
docker-compose pull
docker-compose up -d
```

## Support

- Documentation: `/v2/ARCHITECTURE.md`
- Issues: GitHub Issues
- Logs: `v2/logs/`
- Metrics: Grafana dashboards

## License

SIEMLess v2.0 is licensed under MIT License.