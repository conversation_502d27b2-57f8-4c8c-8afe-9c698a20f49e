/**
 * Entity API Service
 * Handles all entity-related API calls including three-layer enrichment
 */

import apiClient from '../client'
import type {
  Entity,
  EntityReference,
  Relationship,
  TimelineEvent,
  EntitySearchRequest,
  PaginatedResponse,
  APIResponse
} from '../../types/api'

export const entityService = {
  /**
   * Search entities
   */
  async searchEntities(
    request: EntitySearchRequest
  ): Promise<PaginatedResponse<Entity>> {
    const response = await apiClient.get<PaginatedResponse<Entity>>(
      '/entities',
      {
        params: {
          query: request.query,
          entity_type: request.entity_type,
          ...request.filters,
          limit: request.limit || 50,
          offset: request.offset || 0
        }
      }
    )
    return response.data
  },

  /**
   * Get entity by ID
   */
  async getEntity(entityId: string): Promise<Entity> {
    const response = await apiClient.get<APIResponse<Entity>>(
      `/api/entities/${entityId}`
    )
    return response.data.data
  },

  /**
   * Get entity enrichment (all layers)
   */
  async getEnrichment(entityId: string): Promise<Entity['enrichments']> {
    const response = await apiClient.get<APIResponse<Entity['enrichments']>>(
      `/api/entities/${entityId}/enrichment`
    )
    return response.data.data
  },

  /**
   * Get entity relationships
   */
  async getRelationships(entityId: string): Promise<Relationship[]> {
    const response = await apiClient.get<APIResponse<Relationship[]>>(
      `/api/entities/${entityId}/relationships`
    )
    return response.data.data
  },

  /**
   * Get entity timeline
   */
  async getTimeline(
    entityId: string,
    days?: number
  ): Promise<TimelineEvent[]> {
    const response = await apiClient.get<APIResponse<TimelineEvent[]>>(
      `/api/entities/${entityId}/timeline`,
      {
        params: days ? { days } : {}
      }
    )
    return response.data.data
  },

  /**
   * Get entity risk score
   */
  async getRiskScore(entityId: string): Promise<{ risk_score: number }> {
    const response = await apiClient.get<APIResponse<{ risk_score: number }>>(
      `/api/entities/${entityId}/risk`
    )
    return response.data.data
  },

  /**
   * Get list of entities (for batch operations)
   */
  async getEntities(
    entityIds: string[]
  ): Promise<Entity[]> {
    const response = await apiClient.post<APIResponse<Entity[]>>(
      '/entities/batch',
      { entity_ids: entityIds }
    )
    return response.data.data
  }
}
