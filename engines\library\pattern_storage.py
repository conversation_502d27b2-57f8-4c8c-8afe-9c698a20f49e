"""
Pattern Storage Layer for SIEMLess v2.0
Handles persistence, versioning, and retrieval of patterns
"""

import json
import asyncio
import asyncpg
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4
import redis.asyncio as redis
from pathlib import Path
import hashlib
import logging

logger = logging.getLogger(__name__)

class PatternStorage:
    """Manages pattern storage across hot/warm/cold tiers"""

    def __init__(self, postgres_url: str, redis_url: str, s3_config: Optional[Dict] = None):
        self.postgres_url = postgres_url
        self.redis_url = redis_url
        self.s3_config = s3_config
        self.redis_client = None
        self.pg_pool = None

        # Storage tiers
        self.HOT_CACHE_TTL = 3600  # 1 hour in Redis
        self.WARM_RETENTION_DAYS = 30  # 30 days in PostgreSQL

    async def initialize(self):
        """Initialize storage connections"""
        # Connect to Redis
        self.redis_client = await redis.from_url(self.redis_url)

        # Create PostgreSQL connection pool
        self.pg_pool = await asyncpg.create_pool(
            self.postgres_url,
            min_size=5,
            max_size=20
        )

        # Create tables if not exist
        await self._create_tables()

    async def _create_tables(self):
        """Create pattern storage tables"""
        async with self.pg_pool.acquire() as conn:
            # Main pattern table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS patterns (
                    pattern_id UUID PRIMARY KEY,
                    pattern_name VARCHAR(255) NOT NULL,
                    pattern_version VARCHAR(20) NOT NULL,
                    pattern_type VARCHAR(50) NOT NULL,
                    pattern_category VARCHAR(100),
                    pattern_data JSONB NOT NULL,
                    metadata JSONB,
                    validation JSONB,
                    performance JSONB,
                    distribution JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by VARCHAR(255),
                    status VARCHAR(50) DEFAULT 'draft',
                    checksum VARCHAR(64),
                    UNIQUE(pattern_name, pattern_version)
                )
            """)

            # Pattern versions table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS pattern_versions (
                    version_id UUID PRIMARY KEY,
                    pattern_id UUID REFERENCES patterns(pattern_id),
                    version_number VARCHAR(20) NOT NULL,
                    changes JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by VARCHAR(255),
                    commit_message TEXT
                )
            """)

            # Pattern usage tracking
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS pattern_usage (
                    usage_id UUID PRIMARY KEY,
                    pattern_id UUID REFERENCES patterns(pattern_id),
                    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    organization_id VARCHAR(255),
                    success BOOLEAN DEFAULT TRUE,
                    execution_time_ms INTEGER,
                    error_message TEXT
                )
            """)

            # Pattern relationships
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS pattern_relationships (
                    relationship_id UUID PRIMARY KEY,
                    parent_pattern_id UUID REFERENCES patterns(pattern_id),
                    child_pattern_id UUID REFERENCES patterns(pattern_id),
                    relationship_type VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Create indexes
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_patterns_type ON patterns(pattern_type);
                CREATE INDEX IF NOT EXISTS idx_patterns_category ON patterns(pattern_category);
                CREATE INDEX IF NOT EXISTS idx_patterns_status ON patterns(status);
                CREATE INDEX IF NOT EXISTS idx_pattern_usage_pattern ON pattern_usage(pattern_id);
                CREATE INDEX IF NOT EXISTS idx_pattern_usage_time ON pattern_usage(used_at);
            """)

    async def store_pattern(self, pattern: Dict[str, Any]) -> str:
        """Store a pattern across storage tiers"""
        pattern_id = pattern.get('pattern_id', str(uuid4()))

        # Calculate checksum
        pattern_checksum = self._calculate_checksum(pattern['pattern_data'])

        # Store in hot cache (Redis)
        await self._store_in_cache(pattern_id, pattern)

        # Store in warm storage (PostgreSQL)
        async with self.pg_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO patterns (
                    pattern_id, pattern_name, pattern_version, pattern_type,
                    pattern_category, pattern_data, metadata, validation,
                    performance, distribution, created_by, status, checksum
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                ON CONFLICT (pattern_name, pattern_version)
                DO UPDATE SET
                    pattern_data = EXCLUDED.pattern_data,
                    metadata = EXCLUDED.metadata,
                    validation = EXCLUDED.validation,
                    performance = EXCLUDED.performance,
                    distribution = EXCLUDED.distribution,
                    updated_at = CURRENT_TIMESTAMP,
                    checksum = EXCLUDED.checksum
            """,
                UUID(pattern_id),
                pattern['pattern_name'],
                pattern['pattern_version'],
                pattern['pattern_type'],
                pattern.get('pattern_category'),
                json.dumps(pattern['pattern_data']),
                json.dumps(pattern.get('metadata', {})),
                json.dumps(pattern.get('validation', {})),
                json.dumps(pattern.get('performance', {})),
                json.dumps(pattern.get('distribution', {})),
                pattern.get('created_by', 'system'),
                pattern.get('status', 'draft'),
                pattern_checksum
            )

        logger.info(f"Stored pattern {pattern_id} with checksum {pattern_checksum}")
        return pattern_id

    async def get_pattern(self, pattern_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve pattern from storage tiers"""
        # Try hot cache first
        pattern = await self._get_from_cache(pattern_id)
        if pattern:
            logger.debug(f"Pattern {pattern_id} retrieved from cache")
            return pattern

        # Try warm storage
        async with self.pg_pool.acquire() as conn:
            row = await conn.fetchrow("""
                SELECT * FROM patterns WHERE pattern_id = $1
            """, UUID(pattern_id))

            if row:
                pattern = self._row_to_pattern(row)
                # Refresh cache
                await self._store_in_cache(pattern_id, pattern)
                return pattern

        # TODO: Try cold storage (S3) if configured

        return None

    async def search_patterns(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search patterns based on criteria"""
        query = "SELECT * FROM patterns WHERE 1=1"
        params = []
        param_count = 0

        # Build dynamic query
        if 'pattern_type' in criteria:
            param_count += 1
            query += f" AND pattern_type = ${param_count}"
            params.append(criteria['pattern_type'])

        if 'pattern_category' in criteria:
            param_count += 1
            query += f" AND pattern_category = ${param_count}"
            params.append(criteria['pattern_category'])

        if 'status' in criteria:
            param_count += 1
            query += f" AND status = ${param_count}"
            params.append(criteria['status'])

        if 'tags' in criteria:
            param_count += 1
            query += f" AND metadata->'tags' @> ${param_count}::jsonb"
            params.append(json.dumps(criteria['tags']))

        if 'min_confidence' in criteria:
            param_count += 1
            query += f" AND (metadata->>'confidence')::float >= ${param_count}"
            params.append(criteria['min_confidence'])

        # Add ordering
        query += " ORDER BY updated_at DESC"

        if 'limit' in criteria:
            param_count += 1
            query += f" LIMIT ${param_count}"
            params.append(criteria['limit'])

        async with self.pg_pool.acquire() as conn:
            rows = await conn.fetch(query, *params)
            return [self._row_to_pattern(row) for row in rows]

    async def update_pattern_version(self, pattern_id: str, new_version: str,
                                    changes: Dict, commit_message: str = None) -> str:
        """Create new version of pattern"""
        # Get current pattern
        current = await self.get_pattern(pattern_id)
        if not current:
            raise ValueError(f"Pattern {pattern_id} not found")

        # Create version record
        version_id = str(uuid4())
        async with self.pg_pool.acquire() as conn:
            # Store version history
            await conn.execute("""
                INSERT INTO pattern_versions (
                    version_id, pattern_id, version_number,
                    changes, created_by, commit_message
                ) VALUES ($1, $2, $3, $4, $5, $6)
            """,
                UUID(version_id), UUID(pattern_id), new_version,
                json.dumps(changes),
                changes.get('created_by', 'system'),
                commit_message
            )

            # Update pattern version
            await conn.execute("""
                UPDATE patterns
                SET pattern_version = $1,
                    pattern_data = $2,
                    updated_at = CURRENT_TIMESTAMP
                WHERE pattern_id = $3
            """,
                new_version,
                json.dumps({**current['pattern_data'], **changes.get('pattern_data', {})}),
                UUID(pattern_id)
            )

        # Invalidate cache
        await self._invalidate_cache(pattern_id)

        return version_id

    async def track_usage(self, pattern_id: str, organization_id: str,
                         success: bool, execution_time_ms: int,
                         error_message: str = None):
        """Track pattern usage for analytics"""
        usage_id = str(uuid4())

        async with self.pg_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO pattern_usage (
                    usage_id, pattern_id, organization_id,
                    success, execution_time_ms, error_message
                ) VALUES ($1, $2, $3, $4, $5, $6)
            """,
                UUID(usage_id), UUID(pattern_id), organization_id,
                success, execution_time_ms, error_message
            )

            # Update pattern performance metrics
            await conn.execute("""
                UPDATE patterns
                SET performance = performance || jsonb_build_object(
                    'usage_count', COALESCE((performance->>'usage_count')::int, 0) + 1,
                    'success_rate', (
                        SELECT AVG(CASE WHEN success THEN 1 ELSE 0 END)
                        FROM pattern_usage WHERE pattern_id = $1
                    ),
                    'avg_execution_time_ms', (
                        SELECT AVG(execution_time_ms)
                        FROM pattern_usage WHERE pattern_id = $1 AND success = true
                    )
                )
                WHERE pattern_id = $1
            """, UUID(pattern_id))

    async def get_pattern_analytics(self, pattern_id: str) -> Dict[str, Any]:
        """Get analytics for a pattern"""
        async with self.pg_pool.acquire() as conn:
            # Get usage stats
            usage_stats = await conn.fetchrow("""
                SELECT
                    COUNT(*) as total_uses,
                    SUM(CASE WHEN success THEN 1 ELSE 0 END) as successful_uses,
                    AVG(execution_time_ms) as avg_execution_time,
                    MIN(used_at) as first_used,
                    MAX(used_at) as last_used,
                    COUNT(DISTINCT organization_id) as unique_orgs
                FROM pattern_usage
                WHERE pattern_id = $1
            """, UUID(pattern_id))

            # Get version history
            versions = await conn.fetch("""
                SELECT version_number, created_at, commit_message
                FROM pattern_versions
                WHERE pattern_id = $1
                ORDER BY created_at DESC
            """, UUID(pattern_id))

            return {
                'usage': dict(usage_stats) if usage_stats else {},
                'versions': [dict(v) for v in versions],
                'performance_trend': await self._get_performance_trend(pattern_id)
            }

    async def _get_performance_trend(self, pattern_id: str) -> List[Dict]:
        """Get performance trend over time"""
        async with self.pg_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT
                    DATE(used_at) as date,
                    COUNT(*) as uses,
                    AVG(CASE WHEN success THEN 1 ELSE 0 END) as success_rate,
                    AVG(execution_time_ms) as avg_time
                FROM pattern_usage
                WHERE pattern_id = $1
                    AND used_at >= CURRENT_DATE - INTERVAL '30 days'
                GROUP BY DATE(used_at)
                ORDER BY date
            """, UUID(pattern_id))

            return [dict(row) for row in rows]

    async def archive_pattern(self, pattern_id: str, reason: str = None):
        """Archive a pattern to cold storage"""
        # Get pattern
        pattern = await self.get_pattern(pattern_id)
        if not pattern:
            raise ValueError(f"Pattern {pattern_id} not found")

        # Update status
        async with self.pg_pool.acquire() as conn:
            await conn.execute("""
                UPDATE patterns
                SET status = 'archived',
                    metadata = metadata || jsonb_build_object(
                        'archived_at', $1,
                        'archive_reason', $2
                    )
                WHERE pattern_id = $3
            """,
                datetime.utcnow().isoformat(),
                reason,
                UUID(pattern_id)
            )

        # TODO: Move to S3 cold storage if configured

        # Remove from cache
        await self._invalidate_cache(pattern_id)

        logger.info(f"Archived pattern {pattern_id}")

    # Cache operations
    async def _store_in_cache(self, pattern_id: str, pattern: Dict):
        """Store pattern in Redis cache"""
        if self.redis_client:
            await self.redis_client.setex(
                f"pattern:{pattern_id}",
                self.HOT_CACHE_TTL,
                json.dumps(pattern)
            )

    async def _get_from_cache(self, pattern_id: str) -> Optional[Dict]:
        """Get pattern from Redis cache"""
        if self.redis_client:
            data = await self.redis_client.get(f"pattern:{pattern_id}")
            if data:
                return json.loads(data)
        return None

    async def _invalidate_cache(self, pattern_id: str):
        """Remove pattern from cache"""
        if self.redis_client:
            await self.redis_client.delete(f"pattern:{pattern_id}")

    def _calculate_checksum(self, pattern_data: Dict) -> str:
        """Calculate checksum for pattern data"""
        data_str = json.dumps(pattern_data, sort_keys=True)
        return hashlib.sha256(data_str.encode()).hexdigest()

    def _row_to_pattern(self, row) -> Dict[str, Any]:
        """Convert database row to pattern dict"""
        return {
            'pattern_id': str(row['pattern_id']),
            'pattern_name': row['pattern_name'],
            'pattern_version': row['pattern_version'],
            'pattern_type': row['pattern_type'],
            'pattern_category': row['pattern_category'],
            'pattern_data': json.loads(row['pattern_data']) if row['pattern_data'] else {},
            'metadata': json.loads(row['metadata']) if row['metadata'] else {},
            'validation': json.loads(row['validation']) if row['validation'] else {},
            'performance': json.loads(row['performance']) if row['performance'] else {},
            'distribution': json.loads(row['distribution']) if row['distribution'] else {},
            'created_at': row['created_at'].isoformat() if row['created_at'] else None,
            'updated_at': row['updated_at'].isoformat() if row['updated_at'] else None,
            'status': row['status'],
            'checksum': row['checksum']
        }

    async def cleanup_old_patterns(self, days: int = 90):
        """Clean up old unused patterns"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)

        async with self.pg_pool.acquire() as conn:
            # Find patterns to archive
            old_patterns = await conn.fetch("""
                SELECT p.pattern_id
                FROM patterns p
                LEFT JOIN pattern_usage u ON p.pattern_id = u.pattern_id
                WHERE p.status != 'archived'
                    AND p.updated_at < $1
                GROUP BY p.pattern_id
                HAVING MAX(u.used_at) < $1 OR MAX(u.used_at) IS NULL
            """, cutoff_date)

            for row in old_patterns:
                await self.archive_pattern(str(row['pattern_id']),
                                         f"Auto-archived after {days} days of inactivity")

        logger.info(f"Archived {len(old_patterns)} inactive patterns")

    async def close(self):
        """Close storage connections"""
        if self.redis_client:
            await self.redis_client.close()
        if self.pg_pool:
            await self.pg_pool.close()


class PatternReplication:
    """Handles pattern replication and federation"""

    def __init__(self, storage: PatternStorage):
        self.storage = storage
        self.replication_peers = []

    async def replicate_pattern(self, pattern_id: str, peer_url: str):
        """Replicate pattern to peer instance"""
        pattern = await self.storage.get_pattern(pattern_id)
        if not pattern:
            raise ValueError(f"Pattern {pattern_id} not found")

        # TODO: Implement peer replication protocol
        # This would involve:
        # 1. Authenticate with peer
        # 2. Check if peer already has pattern
        # 3. Send pattern if needed
        # 4. Track replication status

        logger.info(f"Pattern {pattern_id} replicated to {peer_url}")

    async def sync_with_community(self):
        """Sync patterns with community repository"""
        # TODO: Implement community sync
        # This would involve:
        # 1. Connect to community repository
        # 2. Get list of new/updated patterns
        # 3. Download approved patterns
        # 4. Upload shared patterns
        pass