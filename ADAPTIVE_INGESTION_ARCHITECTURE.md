## Adaptive Ingestion & Contextualization Architecture
**Auto-Detection and AI-Powered Learning for Unknown Patterns**

---

## The Problem You Identified

**Current State** (Hardcoded Patterns):
```python
# Works for CrowdStrike, Fortinet, Palo Alto
if vendor == 'crowdstrike':
    extract_ip_from('LocalAddressIP4')
elif vendor == 'fortinet':
    extract_ip_from('source.ip')
# ... but what about TippingPoint? ThreatLocker? Future vendors?
```

**Your Insight**:
> "I should be able to autodetect any out-of-use-case or scope cases and use AI to populate the info"

**The Gap**:
- We have 6.95B logs from 7 vendors
- Only 3 vendors have hardcoded extraction patterns
- TippingPoint (578M logs), ThreatLocker (211K logs) have ZERO patterns
- Every new vendor requires developer to write extraction code
- Analysts can't self-service new data sources

---

## The Solution: Adaptive Entity Extraction

### Three-Tier Extraction System

```
┌────────────────────────────────────────────────────────┐
│  TIER 1: Known Patterns (Free, Instant)                │
│  ┌──────────────────────────────────────────────────┐ │
│  │ CrowdStrike → extract_ip('LocalAddressIP4')      │ │
│  │ Fortinet    → extract_ip('source.ip')            │ │
│  │ Palo Alto   → extract_ip('source.ip')            │ │
│  │                                                   │ │
│  │ Cost: $0.00                                       │ │
│  │ Speed: <10ms                                      │ │
│  │ Confidence: 95%                                   │ │
│  └──────────────────────────────────────────────────┘ │
└────────────────────────────────────────────────────────┘
                            │
                            │ Pattern not found
                            ▼
┌────────────────────────────────────────────────────────┐
│  TIER 2: AI Learning (First Time Only)                 │
│  ┌──────────────────────────────────────────────────┐ │
│  │ TippingPoint → Ask AI: "What fields have IPs?"   │ │
│  │ AI Response: "source.ip, destination.ip found"   │ │
│  │                                                   │ │
│  │ ThreatLocker → Ask AI: "Extract entities"        │ │
│  │ AI Response: "user: threatlocker.user.name"      │ │
│  │              "app: threatlocker.application"     │ │
│  │                                                   │ │
│  │ Cost: $0.02 per vendor (first time)              │ │
│  │ Speed: 2-3 seconds                               │ │
│  │ Confidence: 85%                                   │ │
│  └──────────────────────────────────────────────────┘ │
└────────────────────────────────────────────────────────┘
                            │
                            │ Crystallize pattern
                            ▼
┌────────────────────────────────────────────────────────┐
│  TIER 3: Learned Patterns (Free Forever)               │
│  ┌──────────────────────────────────────────────────┐ │
│  │ TippingPoint → extract_ip('source.ip')  [LEARNED]│ │
│  │ ThreatLocker → extract_user('threatlocker...')   │ │
│  │                                                   │ │
│  │ ALL future TippingPoint/ThreatLocker logs:       │ │
│  │ Cost: $0.00                                       │ │
│  │ Speed: <10ms                                      │ │
│  │ Confidence: 85%                                   │ │
│  └──────────────────────────────────────────────────┘ │
└────────────────────────────────────────────────────────┘
```

---

## How Auto-Detection Works

### 1. Vendor Auto-Detection

```python
# System receives log from Elastic
log = {
    "data_stream": {"dataset": "tippingpoint"},
    "observer": {"type": "ips"},
    "source": {"ip": "*************"},
    "rule": {"name": "SQL_Injection_Attempt"}
}

# Auto-detect vendor
vendor = adaptive_extractor.detect_vendor(log)
# → "tippingpoint"

# Check if we have patterns
if vendor in known_patterns:
    # Use hardcoded pattern (fast)
elif vendor in learned_patterns:
    # Use AI-learned pattern (fast)
else:
    # Trigger AI learning (first time)
```

**Vendor Detection Signatures**:
```python
{
    'tippingpoint': ['observer.type', 'tippingpoint'],
    'threatlocker': ['threatlocker', 'application'],
    'crowdstrike': ['agent.type', 'crowdstrike'],
    'fortinet': ['fortinet', 'fortigate'],
    'palo_alto': ['panw', 'panos']
}
```

### 2. Pattern Confidence Evaluation

```python
# Extract with known pattern
entities = extract_with_pattern(log, vendor)
# → [{'type': 'ip_address', 'value': '*************'}]

# Evaluate if AI needed
should_use_ai = evaluate_confidence(entities)

# Triggers AI if:
# - No entities found
# - Unknown vendor
# - Less than 2 entities extracted
# - Average confidence < 60%
# - New field structures detected
```

### 3. AI Extraction & Learning

```python
# When AI is triggered:
prompt = f"""
Extract entities from this {vendor} log:
{log_sample}

Find: IPs, users, hosts, processes, threats, etc.

Also provide extraction_rules for future logs.
"""

# AI analyzes and responds:
{
    "entities": [
        {"type": "ip_address", "value": "*************",
         "source_field": "source.ip", "confidence": 0.95},
        {"type": "ip_address", "value": "***********",
         "source_field": "destination.ip", "confidence": 0.95},
        {"type": "threat", "value": "SQL_Injection_Attempt",
         "source_field": "rule.name", "confidence": 0.90}
    ],
    "extraction_rules": {
        "ip_address": ["source.ip", "destination.ip"],
        "threat": ["rule.name", "rule.id"],
        "port": ["source.port", "destination.port"]
    },
    "confidence": 0.90
}

# System crystallizes this pattern for future use
learned_patterns['tippingpoint'] = extraction_rules
```

---

## Real-World Example: TippingPoint

### First TippingPoint Log (No Pattern Exists)

**Input Log**:
```json
{
    "data_stream": {"dataset": "tippingpoint"},
    "source": {"ip": "*************", "port": 54321},
    "destination": {"ip": "************", "port": 445},
    "rule": {
        "name": "SMB_Exploit_Attempt",
        "id": "12345",
        "severity": "high"
    },
    "network": {"protocol": "tcp", "bytes": 2048},
    "observer": {"type": "tippingpoint", "name": "IPS-01"}
}
```

**Processing Flow**:

1. **Vendor Detection** ✅
   ```
   Detected: "tippingpoint" (from data_stream.dataset)
   ```

2. **Pattern Check** ❌
   ```
   Known patterns: None
   Learned patterns: None
   → Trigger AI extraction
   ```

3. **AI Analysis** (First time, costs $0.02)
   ```
   AI analyzes log structure...
   Found entities:
   - IPs: source.ip, destination.ip
   - Ports: source.port, destination.port
   - Threat: rule.name, rule.id, rule.severity
   - Network: network.protocol, network.bytes
   ```

4. **Pattern Crystallization** ✅
   ```
   Learned pattern saved:
   tippingpoint:
     ip_address: [source.ip, destination.ip]
     port: [source.port, destination.port]
     threat: [rule.name, rule.id]
     severity: [rule.severity]
   ```

5. **Entities Extracted**:
   ```
   - ip_address: ************* (confidence: 0.95)
   - ip_address: ************ (confidence: 0.95)
   - port: 54321 (confidence: 0.90)
   - port: 445 (confidence: 0.90)
   - threat: SMB_Exploit_Attempt (confidence: 0.95)
   - threat_id: 12345 (confidence: 0.95)
   ```

### Second TippingPoint Log (Pattern Exists)

**Processing Flow**:

1. **Vendor Detection** ✅
   ```
   Detected: "tippingpoint"
   ```

2. **Pattern Check** ✅
   ```
   Learned pattern: FOUND
   → Use learned pattern (free, instant)
   ```

3. **Extraction** (0 seconds, $0.00)
   ```
   Applied learned pattern
   Extracted 6 entities in <10ms
   Cost: $0.00
   ```

**Result**: 578 MILLION TippingPoint logs processed for FREE after learning from the first one!

---

## ThreatLocker Example

### First ThreatLocker Log

**Input**:
```json
{
    "data_stream": {"dataset": "threatlocker"},
    "@timestamp": "2025-10-02T14:30:00Z",
    "event": {"action": "blocked"},
    "threatlocker": {
        "user": {"name": "john.doe", "domain": "CORP"},
        "application": {
            "name": "ransomware.exe",
            "path": "C:\\Users\\<USER>\\Downloads\\ransomware.exe",
            "hash": "d41d8cd98f00b204e9800998ecf8427e"
        },
        "policy": {"name": "Block_Unknown_Apps", "action": "deny"}
    }
}
```

**AI Learning**:
```
AI discovers ThreatLocker structure:
- user: threatlocker.user.name
- domain: threatlocker.user.domain
- process: threatlocker.application.name
- file_path: threatlocker.application.path
- hash: threatlocker.application.hash
- policy: threatlocker.policy.name
```

**Pattern Crystallized** → All future 211K ThreatLocker logs use this pattern for FREE

---

## Cost Analysis

### Traditional Approach (Hardcoded Only)

```
Vendors supported: 3 (CrowdStrike, Fortinet, Palo Alto)
TippingPoint:  578M logs × NO EXTRACTION = Lost intelligence
ThreatLocker:  211K logs × NO EXTRACTION = Lost intelligence
New vendor:    Requires developer (4 hours @ $100/hr = $400)
```

### Adaptive Approach (AI Learning)

```
Vendors supported: Infinite
TippingPoint:
  - First log: AI learning = $0.02
  - Next 578M logs: Pattern matching = $0.00
  Total cost: $0.02

ThreatLocker:
  - First log: AI learning = $0.02
  - Next 211K logs: Pattern matching = $0.00
  Total cost: $0.02

New vendor (e.g., Zscaler):
  - First log: AI learning = $0.02
  - All future logs: Free
  - No developer needed
  Total cost: $0.02 vs $400 (99.995% savings)
```

---

## Implementation Components

### 1. AdaptiveEntityExtractor

**File**: `engines/contextualization/adaptive_entity_extractor.py`

**Key Methods**:
```python
async def extract_entities(log_data, vendor=None):
    # 1. Auto-detect vendor
    # 2. Try known patterns
    # 3. Evaluate if AI needed
    # 4. Use AI if necessary
    # 5. Learn from AI extraction
    # 6. Return entities

async def _extract_with_ai(log_data, vendor):
    # Send to intelligence engine
    # AI analyzes log structure
    # Returns entities + extraction rules

async def _learn_pattern(vendor, log_sample, ai_result):
    # Crystallize pattern from AI
    # Save to learned_patterns
    # Persist to database
```

### 2. UnknownPatternDetector

**Detects anomalies and triggers learning**:
```python
# After processing 5 unknown logs from same vendor
if detector.should_trigger_learning(vendor):
    # Batch learn from 5 samples
    await extractor.learn_pattern(vendor, samples)
```

### 3. Intelligence Engine Handler

**File**: `engines/intelligence/entity_extraction_handler.py` (to be created)

**Handles AI extraction requests**:
```python
async def handle_extraction_request(request):
    # Receive log sample + vendor
    # Analyze with AI
    # Extract entities
    # Generate extraction rules
    # Return structured response
```

---

## Integration with Existing System

### Contextualization Engine Update

```python
# engines/contextualization/contextualization_engine.py

from adaptive_entity_extractor import AdaptiveEntityExtractor

class ContextualizationEngine(BaseEngine):
    def __init__(self):
        # ... existing code ...

        # Add adaptive extractor
        self.adaptive_extractor = AdaptiveEntityExtractor(
            self.redis_client,
            self.logger
        )

    async def _handle_extract_entities(self, message):
        logs = message.get('logs', [])

        for log in logs:
            # Use adaptive extraction (auto-detects + learns)
            result = await self.adaptive_extractor.extract_entities(
                log_data=log['raw'],
                vendor=log.get('source')  # Optional hint
            )

            # Store entities
            await self._store_entities(result['entities'])

            # Track if pattern was learned
            if result['learned']:
                self.logger.info(f"Learned new pattern for {result['vendor']}")
```

---

## Benefits

### 1. Self-Service for Analysts ✅
```
Old: "We need to add Zscaler logs"
     → File ticket
     → Wait for developer
     → 4 hours of coding
     → Testing
     → Deploy
     → Total time: 1-2 weeks

New: "We need to add Zscaler logs"
     → Send 1 sample log
     → AI learns in 3 seconds
     → All future logs auto-extract
     → Total time: 3 seconds
```

### 2. Infinite Vendor Support ✅
```
Hardcoded: 3 vendors supported
Adaptive:  ∞ vendors supported
Cost:      $0.02 per new vendor
```

### 3. Automatic Updates ✅
```
Vendor changes log format:
- Old: Extraction breaks, wait for fix
- New: AI detects change, learns new format automatically
```

### 4. Community Learning ✅
```
Analyst A learns TippingPoint pattern → Shares (opt-in)
Analyst B gets pattern for FREE
Community builds library of all vendor patterns
```

---

## Deployment

### Phase 1: Core Implementation ✅
- [x] adaptive_entity_extractor.py created
- [ ] Intelligence engine handler (AI extraction)
- [ ] Database schema for learned patterns
- [ ] Integration with contextualization engine

### Phase 2: TippingPoint/ThreatLocker Test
- [ ] Query TippingPoint logs from Elastic
- [ ] Trigger AI learning
- [ ] Verify pattern crystallization
- [ ] Confirm future logs use learned pattern

### Phase 3: Community Features
- [ ] Pattern sharing opt-in
- [ ] Pattern validation (vote on quality)
- [ ] Pattern evolution (improve over time)

---

## Success Metrics

**Goal**: Handle ANY log format automatically

| Metric | Target | Current |
|--------|--------|---------|
| Vendors with patterns | 100% | 42% (3/7) |
| Avg entities per log | 5+ | 1.8 |
| Pattern learn time | <5 sec | N/A |
| Cost per new vendor | <$0.05 | $400 |
| Analyst self-service | 100% | 0% |

**After Implementation**:
- TippingPoint: 578M logs fully extracted
- ThreatLocker: 211K logs fully extracted
- Future vendors: Auto-supported
- Analyst autonomy: 100%

---

## Summary

**The Problem**:
- 6.95B logs, only 3 vendors have extraction patterns
- TippingPoint (578M), ThreatLocker (211K) = NO extraction
- Every new vendor needs developer time

**The Solution**:
- Auto-detect vendor from log structure
- Try hardcoded patterns first (free, fast)
- If no pattern exists → Ask AI (costs $0.02, takes 3 sec)
- AI learns extraction rules
- Crystallize pattern for future use (free forever)
- 578M future TippingPoint logs: $0.00
- Analyst can add ANY vendor in 3 seconds

**The Result**:
- ∞ vendor support
- 99.995% cost reduction ($0.02 vs $400 per vendor)
- Analyst self-service (no developer needed)
- Community pattern library
- "Learn expensive once, operate free forever"

This is exactly the intelligence foundation approach - solve the root cause (manual pattern creation) rather than the symptom (missing extractions).

