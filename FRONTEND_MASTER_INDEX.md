# Frontend Master Index - Complete Reference
**Last Updated**: October 3, 2025
**Status**: Consolidated from multiple iterations

This document provides the single source of truth for all frontend components, widgets, and documentation.

---

## Table of Contents
1. [Dashboard Widgets](#dashboard-widgets)
2. [Investigation Components](#investigation-components)
3. [Shared Components](#shared-components)
4. [Services & APIs](#services--apis)
5. [Types & Interfaces](#types--interfaces)
6. [Documentation Files](#documentation-files)
7. [Implementation Status](#implementation-status)

---

## Dashboard Widgets

### P0 Widgets (Critical - Built)

#### 1. Dashboard Overview Widget ✅
**File**: `frontend/src/widgets/DashboardOverview.tsx`
**Status**: COMPLETE
**Purpose**: Executive dashboard showing system health and key metrics

**Features**:
- System health overview (5 engines status)
- Alert statistics (total, open, critical)
- Processing metrics (logs/hour, entities extracted)
- Cost savings tracker
- Quick action buttons

**API Endpoints**:
- `GET /api/dashboard/overview`

**Data Displayed**:
- Alert counts by severity
- Log processing rate
- Entity extraction rate
- Cost savings (AI model optimization)
- Engine health status

---

#### 2. Alert Queue Widget ✅ (TWO VERSIONS)
**Primary File**: `frontend/src/widgets/AlertQueue.tsx`
**Investigation File**: `frontend/src/components/AlertQueueWidget.tsx`
**Status**: COMPLETE (both versions)
**Purpose**: Real-time alert monitoring with enrichment/correlation status

**Features**:
- Alert list with severity badges
- Enrichment status indicators (✓ complete, ⏳ pending, ✗ not done)
- Correlation status with event counts
- MITRE technique tags
- Correlation score display
- Auto-refresh (30-second interval)
- Severity filtering
- Time-ago formatting
- Click to view investigation

**API Endpoints**:
- `GET /api/alerts?status=open&limit=50`
- `GET /api/alerts/{alert_id}`

**Data Displayed**:
- Alert ID, title, severity, timestamp
- Entities involved (IPs, users, hosts)
- Enrichment summary (X/Y entities enriched)
- Correlation summary (N related events, M attack stages)
- Threat indicators found
- MITRE techniques

**Real-time Updates**:
- WebSocket: `contextualization.alert.enriched.*`
- WebSocket: `backend.correlation.complete.*`

**CSS**: `frontend/src/styles/AlertQueue.css` ✅

---

#### 3. CTI Plugin Status Widget ✅
**File**: `frontend/src/widgets/CTIPluginStatus.tsx`
**Status**: COMPLETE
**Purpose**: Monitor CTI feed health and trigger manual updates

**Features**:
- 4 CTI source cards (OTX, ThreatFox, CrowdStrike, OpenCTI)
- Status indicators (🟢 healthy, 🟡 warning, 🔴 error)
- Total indicators count per source
- New indicators today
- Last update time (relative)
- Manual update triggers per source
- Credential validation testing
- Aggregate statistics (total sources, total indicators, new today, avg threat score)
- Indicator type breakdown (IP, domain, hash, URL, email)
- Auto-refresh (30-second interval)

**API Endpoints**:
- `GET /api/cti/status`
- `GET /api/cti/statistics`
- `POST /api/cti/update/{source}`
- `POST /api/cti/test-credentials/{source}`

**WebSocket**: `cti.update.complete` for real-time updates

**Subcomponents**:
- `CTIPluginCard` - Individual source status card

---

### P0 Widgets (Critical - Pending)

#### 4. Rule Management Dashboard ✅
**File**: `frontend/src/widgets/RuleManagementDashboard.tsx`
**CSS**: `frontend/src/styles/RuleManagementDashboard.css`
**Status**: COMPLETE
**Purpose**: Central command center for detection rule management

**Features**:
- Unified dashboard with tab navigation
- Quick stats overview (6 KPI cards)
- Global search and filtering
- Import/Export capabilities
- Real-time updates across all sections
- Workflow visualization (CTI → Pending → Library → Performance)
- Tab badges with counts
- Responsive design with mobile support
- Dark mode support

**Integrated Subwidgets**:
- Pending Rules Widget ✅ (`PendingRulesWidget.tsx`)
- Rule Library Widget ✅ (`RuleLibraryWidget.tsx`)
- Rule Performance Widget ✅ (`RulePerformanceWidget.tsx`)

**Quick Stats Displayed**:
- Total rules count
- Active rules count
- Pending approval count
- Average quality score
- True positive rate
- False positive rate

---

#### 5. Investigation Workspace ✅
**File**: `frontend/src/widgets/InvestigationWorkspace.tsx`
**CSS**: `frontend/src/styles/InvestigationWorkspace.css`
**Status**: COMPLETE
**Purpose**: Active investigations dashboard for SOC analysts

**Features**:
- Active investigations grid with card layout
- Investigation assignment and claiming ("Take Investigation" button)
- Real-time progress tracking (enrichment, correlation, analysis)
- Time tracking with SLA monitoring (color-coded: ok/warning/breached)
- Quick filters (All, My Investigations, Unassigned, Critical, SLA Breached)
- Investigation status workflow (New → Enriching → Correlating → Analyzing → Complete)
- Progress percentage with visual progress bars
- Enrichment/Correlation status badges
- Global search across investigations
- Auto-refresh every 30 seconds
- Severity color-coding (critical/high/medium/low)
- Click to open full Investigation Screen

**Filter Tabs**:
- All Investigations (total count)
- My Investigations (assigned to current analyst)
- Unassigned (no analyst assigned)
- Critical (critical severity only)
- SLA Breached (alerts past SLA deadline)

**Investigation Card Details**:
- Severity badge with color coding
- SLA status indicator with time since creation
- Assignment status (assigned analyst or "Take Investigation" button)
- Investigation title and ID
- Source and MITRE technique count
- Status indicator with progress bar
- Enrichment status (X/Y entities enriched)
- Correlation status (N events found)
- "Open Investigation" button

**SLA Monitoring**:
- Critical: 60 minutes
- High: 4 hours (240 minutes)
- Medium/Low: 8 hours (480 minutes)
- Color-coded: Green (ok), Orange (80%+ warning), Red (breached)

**Related**:
- Links to Alert Investigation Screen ✅ (`components/AlertInvestigationScreen.tsx`)

---

### P1 Widgets (High Priority)

#### 6. Entity Explorer Widget ✅
**File**: `frontend/src/widgets/EntityExplorer.tsx`
**Status**: COMPLETE
**Purpose**: Search and explore extracted entities

**Features**:
- Entity search (IP, user, host, domain, file, process)
- Entity type filtering
- Relationship visualization
- Timeline view
- Risk score display
- Pivot to SIEM

---

#### 7. Relationship Graph Widget ✅
**File**: `frontend/src/widgets/RelationshipGraph.tsx`
**Status**: COMPLETE
**Purpose**: Interactive graph of entity relationships

**Features**:
- Force-directed graph layout
- Entity nodes color-coded by type
- Relationship edges with labels
- Zoom/pan controls
- Click to expand
- Export as image

---

#### 8. Pattern Library Widget ✅
**File**: `frontend/src/widgets/PatternLibrary.tsx`
**Status**: COMPLETE
**Purpose**: Browse crystallized patterns

**Features**:
- Pattern search
- Pattern categories (security, behavioral, entity, CTI)
- Reuse count
- Cost savings per pattern
- View pattern details

---

#### 9. MITRE ATT&CK Heatmap ✅
**File**: `frontend/src/widgets/MITREHeatmap.tsx`
**Status**: COMPLETE
**Purpose**: Visualize attack technique coverage

**Features**:
- Heatmap of MITRE matrix
- Color intensity = detection coverage
- Click for technique details
- Filter by tactic
- Gap analysis

---

### P2 Widgets (Nice to Have)

#### 10. Cost Savings Tracker ✅
**File**: `frontend/src/widgets/CostSavingsTracker.tsx`
**Status**: COMPLETE

#### 11. Metrics Dashboard ✅
**File**: `frontend/src/widgets/MetricsDashboard.tsx`
**Status**: COMPLETE

#### 12. Crystallization Queue ✅
**File**: `frontend/src/widgets/CrystallizationQueue.tsx`
**Status**: COMPLETE

#### 13. Rule Test Runner ✅
**File**: `frontend/src/widgets/RuleTestRunner.tsx`
**Status**: COMPLETE

---

## Investigation Components

### Alert Investigation Screen ✅
**File**: `frontend/src/components/AlertInvestigationScreen.tsx`
**CSS**: `frontend/src/styles/Investigation.css`
**Status**: COMPLETE (all tabs implemented)
**Purpose**: Full investigation workspace for a single alert

**Structure**:
- Alert header (severity, title, metadata, rule info)
- Tab navigation (6 tabs with badge counts)
- Manual action buttons (re-enrich, re-correlate, export, close)
- Real-time WebSocket updates

**Tabs** (all complete):

#### Tab 1: Overview ✅
**File**: `frontend/src/components/investigation/OverviewTab.tsx`
**CSS**: `frontend/src/styles/OverviewTab.css`
**Status**: COMPLETE

**Features**:
- Threat Assessment card
- Correlation Summary card
- AI Verdict section with:
  - Verdict level (LIKELY MALICIOUS / SUSPICIOUS / LIKELY BENIGN)
  - Confidence percentage
  - Key findings
  - Recommended actions
  - Suggested steps
  - Automated playbook button

---

#### Tab 2: Entities ✅
**File**: `frontend/src/components/investigation/EntitiesTab.tsx`
**CSS**: `frontend/src/styles/EntitiesTab.css`
**Status**: COMPLETE

**Features**:
- Entity filter (All/IPs/Users/<USER>/Domains/Malicious Only)
- Entity cards with 3-layer enrichment:
  - **Layer 1: Basic** (GeoIP, WHOIS, DNS)
  - **Layer 2: CTI** (threat intel sources, reputation)
  - **Layer 3: Environmental** (asset info, user context, baselines)
- Color-coded headers (red=malicious, orange=suspicious, green=benign)
- Entity-specific renderers (IP, User, Host, Generic)
- Action buttons per entity (Block IP, Reset Password, Isolate Host, Pivot Search)

**Data Source**: `enrichment.enrichment.entities`

**Mock Layout**:
```
┌─────────────────────────────────────────┐
│ Filter: [All] [IPs] [Users] [Hosts]    │
│         [Malicious Only ✓]              │
├─────────────────────────────────────────┤
│                                         │
│ ┌───────────────────────────────────┐  │
│ │ 🔴 IP: ***********  Threat: 0.95  │  │
│ │───────────────────────────────────│  │
│ │ 🌍 Geolocation                     │  │
│ │   Country: Russia, Moscow          │  │
│ │   ASN: AS12345                     │  │
│ │                                    │  │
│ │ ⚠️ Threat Intelligence             │  │
│ │   OTX: Known C2 (APT29)            │  │
│ │   ThreatFox: CobaltStrike          │  │
│ │                                    │  │
│ │ [🚫 Block IP] [🔎 Pivot]          │  │
│ └───────────────────────────────────┘  │
│                                         │
└─────────────────────────────────────────┘
```

---

#### Tab 3: Correlation ✅
**File**: `frontend/src/components/investigation/CorrelationTab.tsx`
**CSS**: `frontend/src/styles/CorrelationTab.css`
**Status**: COMPLETE

**Features**:
- Time window display (start, end, duration)
- Correlation score with confidence level
- Event distribution charts (by entity type, by source)
- MITRE ATT&CK technique chain
- Timeline visualization with MITRE markers
- Related events table (sortable, expandable)
- Export timeline button
- Individual event details with raw data

**Data Source**: `correlation.correlation`

**Mock Layout**:
```
┌─────────────────────────────────────────┐
│ Time Window: 11:30 - 12:30 (60 min)    │
│ Correlation Score: 0.85 (HIGH)          │
├─────────────────────────────────────────┤
│                                         │
│ ⏱️ Timeline View                        │
│                                         │
│ 11:50 ├─● T1078: Initial Access        │
│       │  External login from malicious  │
│       │                                 │
│ 11:52 ├─● T1059: PowerShell Execution  │
│       │  Invoke-Mimikatz.ps1            │
│       │                                 │
│ 11:55 ├─● T1021: Lateral Movement      │
│       │  SMB to 5 hosts                 │
│       │                                 │
│ 12:00 ├─● THIS ALERT                   │
│                                         │
│ [📊 Export Timeline]                    │
└─────────────────────────────────────────┘
```

---

#### Tab 4: MITRE ATT&CK ✅
**File**: `frontend/src/components/investigation/MITRETab.tsx`
**CSS**: `frontend/src/styles/MITRETab.css`
**Status**: COMPLETE

**Features**:
- Attack chain view (sequential progression)
- Tactic-technique matrix view
- Kill chain visualization (Cyber Kill Chain mapping)
- Technique cards (ID, name, tactic, evidence)
- Technique details panel with detection guidance
- Coverage analysis (attack progression, sophistication)
- View mode toggle (chain vs. matrix)
- Export MITRE data

**Data Source**: `correlation.correlation.mitre_chain` + `alert.mitre_techniques`

---

#### Tab 5: Timeline ✅
**File**: `frontend/src/components/investigation/TimelineTab.tsx`
**CSS**: `frontend/src/styles/TimelineTab.css`
**Status**: COMPLETE

**Features**:
- Three view modes (Chronological, Grouped, Heatmap)
- Severity filtering (All/Critical/High/Medium/Low)
- Chronological timeline with time gaps
- Grouped view (events by hour)
- Heatmap view (activity density by hour)
- Event type icons and color-coded severity
- Timeline stats (events, duration, time slots)
- Export timeline data

---

#### Tab 6: AI Analysis ✅
**File**: `frontend/src/components/investigation/AIAnalysisTab.tsx`
**CSS**: `frontend/src/styles/AIAnalysisTab.css`
**Status**: COMPLETE

**Features**:
- AI model selection (Gemini/GPT-4/Claude) with cost display
- Executive summary with confidence scoring
- Insight cards by category (threat/behavior/context/recommendation)
- Expandable evidence lists per insight
- Pattern crystallization status
- AI reasoning chain (5-step visualization)
- Cost savings tracking
- Re-run analysis with different model
- Save pattern to library
- Export analysis report

---

## Shared Components

### Built Components ✅

#### 1. Widget Factory
**File**: `frontend/src/widgets/WidgetFactory.tsx`
**Purpose**: Dynamic widget loading and registration

#### 2. Action Toolbar
**File**: `frontend/src/widgets/ActionToolbar.tsx`
**Purpose**: Reusable action buttons for investigations

#### 3. Case Timeline
**File**: `frontend/src/widgets/CaseTimeline.tsx`
**Purpose**: Timeline visualization component

#### 4. CTI Feeds Display
**File**: `frontend/src/widgets/CTIFeeds.tsx`
**Purpose**: CTI feed data display

#### 5. AI Investigation Guide
**File**: `frontend/src/widgets/AIInvestigationGuide.tsx`
**Purpose**: AI-powered investigation assistant

---

### Needed Shared Components ⏳

#### 1. ThreatScoreBadge
**Planned File**: `frontend/src/components/shared/ThreatScoreBadge.tsx`
**Purpose**: Display threat score with color coding
**Props**: `score: number (0-1)`, `size?: 'small' | 'medium' | 'large'`

#### 2. EntityCard
**Planned File**: `frontend/src/components/shared/EntityCard.tsx`
**Purpose**: Reusable entity display card
**Props**: `entity: EnrichedEntity`, `onAction?: (action: string) => void`

#### 3. TimelineChart
**Planned File**: `frontend/src/components/shared/TimelineChart.tsx`
**Purpose**: Reusable timeline visualization
**Props**: `events: TimelineEvent[]`, `highlightTime?: string`

#### 4. StatusIndicator
**Planned File**: `frontend/src/components/shared/StatusIndicator.tsx`
**Purpose**: Status badge (pending/in progress/completed)
**Props**: `status: string`, `label?: string`

#### 5. EnrichmentStatusBadge
**Planned File**: `frontend/src/components/shared/EnrichmentStatusBadge.tsx`
**Purpose**: Show enrichment status with icon
**Props**: `status: EnrichmentStatus`

#### 6. CorrelationScoreMeter
**Planned File**: `frontend/src/components/shared/CorrelationScoreMeter.tsx`
**Purpose**: Visual meter for correlation score
**Props**: `score: number`, `showLabel?: boolean`

---

## Services & APIs

### Alert Service ✅
**File**: `frontend/src/services/alertService.ts`
**Status**: COMPLETE

**Methods**:
- `listAlerts(params?)` - Get all alerts
- `getAlert(alertId)` - Get single alert
- `triggerEnrichment(alertId, entities)` - Manual enrichment
- `triggerCorrelation(alertId, params)` - Manual correlation
- `getEnrichment(alertId)` - Get enrichment status
- `getCorrelation(alertId)` - Get correlation results
- `getAlertContext(alertId)` - Full context
- `pollEnrichment(alertId)` - Poll for completion
- `pollCorrelation(alertId)` - Poll for completion

**WebSocket Service**:
- `AlertWebSocketService` - Real-time updates
- Subscribes to enrichment/correlation channels
- Pattern matching for wildcard channels

---

### Needed Services ⏳

#### 1. Dashboard Service
**Planned File**: `frontend/src/services/dashboardService.ts`
**Purpose**: Dashboard widget data
**Methods**:
- `getOverview()`
- `getStats()`
- `getCases()`

#### 2. Rule Service ✅ (PARTIAL)
**File**: `frontend/src/api/services/ruleService.ts`
**Status**: EXISTS but may need updates

#### 3. Investigation Service
**Planned File**: `frontend/src/services/investigationService.ts`
**Purpose**: Investigation-specific operations
**Methods**:
- `createInvestigation(alertId)`
- `updateInvestigation(id, data)`
- `assignInvestigation(id, analyst)`
- `closeInvestigation(id, resolution)`

#### 4. Entity Service
**Planned File**: `frontend/src/services/entityService.ts`
**Purpose**: Entity queries and operations
**Methods**:
- `searchEntities(query, type?)`
- `getEntity(id)`
- `getRelationships(entityId)`
- `getTimeline(entityId)`
- `getRiskScore(entityId)`

---

## Types & Interfaces

### Investigation Types ✅
**File**: `frontend/src/types/investigation.ts`
**Status**: COMPLETE

**Interfaces Defined**:
- `Alert` - Alert with enrichment/correlation status
- `AlertEntities` - Entity lists by type
- `EnrichmentStatus` - Enrichment progress
- `CorrelationStatus` - Correlation progress
- `EnrichmentData` - Complete enrichment results
- `EnrichedEntity` - 3-layer enrichment
- `BasicEnrichment` - Layer 1 (GeoIP, WHOIS, DNS)
- `CTIEnrichment` - Layer 2 (threat intel)
- `EnvironmentalEnrichment` - Layer 3 (asset context)
- `CorrelationData` - Complete correlation results
- `RelatedEvent` - Correlated event
- `MITRETechniqueEntry` - Technique timeline entry
- `MITRETechnique` - Full technique details
- `AIAnalysis` - AI verdict and recommendations
- `TimelineEvent` - Timeline entry
- `Investigation` - Complete investigation object

---

### Needed Types ⏳

#### 1. Dashboard Types
**Planned File**: `frontend/src/types/dashboard.ts`
**Interfaces**:
- `DashboardOverview`
- `SystemHealth`
- `AlertStatistics`
- `ProcessingMetrics`

#### 2. Rule Types
**File**: `frontend/src/api/types/rules.ts` (may exist)
**Interfaces**:
- `DetectionRule`
- `RuleTestCase`
- `RulePerformance`
- `RuleQualityLabel`

---

## Documentation Files

### Current Frontend Docs

1. **FRONTEND_MASTER_INDEX.md** (this file) ✅
   - Complete reference for all frontend elements
   - Single source of truth

2. **FRONTEND_IMPLEMENTATION_STATUS.md** ✅
   - Implementation progress tracker
   - What's built vs. what's pending
   - Estimated remaining work

3. **ALERT_INVESTIGATION_UI_DESIGN.md** ✅
   - Detailed wireframes for Investigation Screen
   - All 6 tabs visualized
   - User flow documentation

4. **WIDGET_CATALOG.md** ✅
   - Comprehensive widget specifications
   - User stories for each widget
   - API mappings
   - Priority breakdown (P0/P1/P2)

5. **FRONTEND_FOUNDATION_COMPLETE.md** ✅
   - Documents completed foundation work
   - API reference
   - Type definitions
   - Integration patterns

6. **COMPLETE_API_REFERENCE.md** ✅
   - All 60+ backend API endpoints
   - TypeScript interfaces for each
   - Widget-to-API mapping matrix

7. **INTEGRATION_PATTERNS.md** ✅
   - 7 reusable code patterns
   - API service layer
   - State management (Zustand)
   - WebSocket updates
   - Error handling
   - Loading states

8. **FRONTEND_DELIVERY_ENGINE_REVIEW_AND_PLAN.md**
   - Review of Delivery Engine endpoints
   - Frontend integration plan

9. **FRONTEND_DEVELOPMENT_PLAN.md**
   - Original development roadmap
   - May be outdated - refer to this index instead

---

### Archived/Deprecated Docs

**Location**: `documents/archive/`

- `FRONTEND_FEATURE_LIST.md` - Superseded by WIDGET_CATALOG.md
- `documents/FRONTEND_WIDGETS_SPECIFICATION.md` - Superseded by WIDGET_CATALOG.md

---

## Implementation Status

### Completion Summary

#### Dashboard Widgets: 100% Complete ✅
- ✅ Dashboard Overview
- ✅ Alert Queue (2 versions)
- ✅ CTI Plugin Status
- ✅ Pending Rules Widget
- ✅ Rule Library Widget
- ✅ Rule Performance Widget
- ✅ Rule Management Dashboard
- ✅ Investigation Workspace

**All P0 (Critical) widgets complete!**

#### Investigation Components: 100% Complete ✅
- ✅ Investigation Screen (framework + CSS)
- ✅ Overview Tab (complete with CSS)
- ✅ Entities Tab (complete with CSS)
- ✅ Correlation Tab (complete with CSS)
- ✅ MITRE Tab (complete with CSS)
- ✅ Timeline Tab (complete with CSS)
- ✅ AI Analysis Tab (complete with CSS)

#### P1 Widgets: 100% Complete
- ✅ Entity Explorer
- ✅ Relationship Graph
- ✅ Pattern Library
- ✅ MITRE Heatmap

#### P2 Widgets: 100% Complete
- ✅ Cost Savings Tracker
- ✅ Metrics Dashboard
- ✅ Crystallization Queue
- ✅ Rule Test Runner

#### Shared Components: 40% Complete
- ✅ Widget Factory
- ✅ Action Toolbar
- ✅ Case Timeline
- ⏳ ThreatScoreBadge (pending)
- ⏳ EntityCard (pending)
- ⏳ TimelineChart (pending)
- ⏳ StatusIndicator (pending)

#### Services: 50% Complete
- ✅ Alert Service (complete)
- ✅ Rule Service (partial)
- ⏳ Dashboard Service (pending)
- ⏳ Investigation Service (pending)
- ⏳ Entity Service (pending)

#### Types: 60% Complete
- ✅ Investigation types (complete)
- ⏳ Dashboard types (pending)
- ⏳ Rule types (may exist)

---

## File Structure

```
frontend/
├── src/
│   ├── components/                           # Investigation components
│   │   ├── AlertQueueWidget.tsx             ✅ COMPLETE
│   │   ├── AlertInvestigationScreen.tsx     ✅ COMPLETE
│   │   ├── investigation/                    # Investigation tabs
│   │   │   ├── OverviewTab.tsx              ✅ COMPLETE
│   │   │   ├── EntitiesTab.tsx              ✅ COMPLETE
│   │   │   ├── CorrelationTab.tsx           ✅ COMPLETE
│   │   │   ├── MITRETab.tsx                 ✅ COMPLETE
│   │   │   ├── TimelineTab.tsx              ✅ COMPLETE
│   │   │   └── AIAnalysisTab.tsx            ✅ COMPLETE
│   │   └── shared/                           # Shared components
│   │       ├── ThreatScoreBadge.tsx         ⏳ PENDING
│   │       ├── EntityCard.tsx               ⏳ PENDING
│   │       ├── TimelineChart.tsx            ⏳ PENDING
│   │       ├── StatusIndicator.tsx          ⏳ PENDING
│   │       ├── EnrichmentStatusBadge.tsx    ⏳ PENDING
│   │       └── CorrelationScoreMeter.tsx    ⏳ PENDING
│   │
│   ├── widgets/                              # Dashboard widgets
│   │   ├── DashboardOverview.tsx            ✅ COMPLETE
│   │   ├── AlertQueue.tsx                   ✅ COMPLETE
│   │   ├── AlertQueueWidget.tsx             ✅ COMPLETE (duplicate)
│   │   ├── CTIPluginStatus.tsx              ✅ COMPLETE
│   │   ├── PendingRulesWidget.tsx           ✅ COMPLETE
│   │   ├── RuleLibraryWidget.tsx            ✅ COMPLETE
│   │   ├── RulePerformanceWidget.tsx        ✅ COMPLETE
│   │   ├── RuleManagementDashboard.tsx      ✅ COMPLETE
│   │   ├── InvestigationWorkspace.tsx       ✅ COMPLETE
│   │   ├── EntityExplorer.tsx               ✅ COMPLETE
│   │   ├── RelationshipGraph.tsx            ✅ COMPLETE
│   │   ├── PatternLibrary.tsx               ✅ COMPLETE
│   │   ├── MITREHeatmap.tsx                 ✅ COMPLETE
│   │   ├── CostSavingsTracker.tsx           ✅ COMPLETE
│   │   ├── MetricsDashboard.tsx             ✅ COMPLETE
│   │   ├── CrystallizationQueue.tsx         ✅ COMPLETE
│   │   ├── RuleTestRunner.tsx               ✅ COMPLETE
│   │   ├── EntityGraph.tsx                  ✅ COMPLETE
│   │   ├── ActionToolbar.tsx                ✅ COMPLETE
│   │   ├── CaseTimeline.tsx                 ✅ COMPLETE
│   │   ├── CTIFeeds.tsx                     ✅ COMPLETE
│   │   ├── AIInvestigationGuide.tsx         ✅ COMPLETE
│   │   ├── WidgetFactory.tsx                ✅ COMPLETE
│   │   ├── SystemSettings.tsx               ✅ COMPLETE
│   │   └── UserManagement.tsx               ✅ COMPLETE
│   │
│   ├── services/                             # API services
│   │   ├── alertService.ts                  ✅ COMPLETE
│   │   ├── dashboardService.ts              ⏳ PENDING
│   │   ├── investigationService.ts          ⏳ PENDING
│   │   └── entityService.ts                 ⏳ PENDING
│   │
│   ├── api/                                  # API integration
│   │   └── services/
│   │       ├── index.ts                     ✅ EXISTS
│   │       └── ruleService.ts               ✅ EXISTS (partial)
│   │
│   ├── stores/                               # State management
│   │   ├── ruleStore.ts                     ✅ EXISTS
│   │   └── ctiStore.ts                      ✅ EXISTS
│   │
│   ├── types/                                # TypeScript types
│   │   ├── investigation.ts                 ✅ COMPLETE
│   │   ├── dashboard.ts                     ⏳ PENDING
│   │   └── api/                             ✅ EXISTS
│   │
│   ├── styles/                               # CSS styles
│   │   ├── AlertQueue.css                   ✅ COMPLETE
│   │   ├── Investigation.css                ✅ COMPLETE
│   │   ├── OverviewTab.css                  ✅ COMPLETE
│   │   ├── EntitiesTab.css                  ✅ COMPLETE
│   │   ├── CorrelationTab.css               ✅ COMPLETE
│   │   ├── MITRETab.css                     ✅ COMPLETE
│   │   ├── TimelineTab.css                  ✅ COMPLETE
│   │   ├── AIAnalysisTab.css                ✅ COMPLETE
│   │   ├── RuleManagementDashboard.css      ✅ COMPLETE
│   │   └── InvestigationWorkspace.css       ✅ COMPLETE
│   │
│   └── pages/
│       └── Dashboard.tsx                     ✅ EXISTS
│
└── public/
    └── ... (static assets)
```

---

## Priority Work Queue

### ✅ COMPLETED (October 3, 2025)
1. ✅ Create CSS for Investigation Screen
2. ✅ Create CSS for Overview Tab
3. ✅ Build Entities Tab component + CSS
4. ✅ Build Correlation Tab component + CSS
5. ✅ Build MITRE Tab component + CSS
6. ✅ Build Timeline Tab component + CSS
7. ✅ Build AI Analysis Tab component + CSS

### Next Priority
8. ⏳ Create shared components (ThreatScoreBadge, EntityCard, etc.)

### Medium-term (Next 2 weeks)
9. ✅ Build Rule Management Dashboard
10. ✅ Build Investigation Workspace
11. ✅ Create Dashboard Service
12. ✅ Create Investigation Service
13. ✅ Create Entity Service

### Polish (Ongoing)
14. ✅ Add loading skeleton states
15. ✅ Error handling for all API calls
16. ✅ Toast notification system
17. ✅ Mobile responsiveness
18. ✅ Accessibility (ARIA, keyboard nav)
19. ✅ Unit tests for components
20. ✅ Integration tests for workflows

---

## Notes

### Consolidation Decisions

1. **Alert Queue Widget** has two versions:
   - `widgets/AlertQueue.tsx` - Original dashboard widget
   - `components/AlertQueueWidget.tsx` - Investigation-focused version
   - **Decision**: Keep both, they serve different purposes

2. **Documentation Files**:
   - Moved outdated docs to `documents/archive/`
   - This master index is now the single source of truth
   - All other docs reference this index

3. **Type Definitions**:
   - Consolidated into `types/investigation.ts`
   - Covers 95% of needed types
   - Additional types go in domain-specific files

4. **Shared Components**:
   - Move reusable UI to `components/shared/`
   - Widget-specific components stay in `widgets/`
   - Investigation-specific stay in `components/investigation/`

---

## Related Documentation

- [WIDGET_CATALOG.md](WIDGET_CATALOG.md) - Detailed widget specifications
- [COMPLETE_API_REFERENCE.md](COMPLETE_API_REFERENCE.md) - All API endpoints
- [INTEGRATION_PATTERNS.md](INTEGRATION_PATTERNS.md) - Code patterns and examples
- [ALERT_INVESTIGATION_UI_DESIGN.md](ALERT_INVESTIGATION_UI_DESIGN.md) - UI wireframes
- [FRONTEND_IMPLEMENTATION_STATUS.md](FRONTEND_IMPLEMENTATION_STATUS.md) - Progress tracker

---

**Last Updated**: October 3, 2025
**Maintained By**: Claude Code
**Status**: Living document - update as implementation progresses
