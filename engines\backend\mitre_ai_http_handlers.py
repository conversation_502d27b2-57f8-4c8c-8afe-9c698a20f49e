"""
HTTP handlers for MITRE AI Intelligence features
"""

import json
from aiohttp import web
from typing import Dict, List


class MITREAIHTTPHandlers:
    """HTTP endpoints for AI-powered MITRE intelligence"""

    def __init__(self, ai_intelligence, logger):
        self.ai = ai_intelligence
        self.logger = logger

    def get_routes(self):
        """Get all HTTP routes"""
        return [
            web.post('/api/v1/mitre/ai/infer_technique', self.infer_technique),
            web.post('/api/v1/mitre/ai/prioritize_gaps', self.prioritize_gaps),
            web.post('/api/v1/mitre/ai/predict_fp', self.predict_false_positives),
            web.get('/api/v1/mitre/ai/cost_savings', self.get_cost_savings),
            web.get('/api/v1/mitre/ai/model_performance', self.get_model_performance),
            web.get('/api/v1/mitre/ai/top_patterns', self.get_top_patterns),
        ]

    async def infer_technique(self, request: web.Request) -> web.Response:
        """
        POST /api/v1/mitre/ai/infer_technique

        AI-powered tier 3 mapping for rules without explicit MITRE tags

        Body:
        {
            "rule": {
                "title": "Suspicious PowerShell",
                "description": "...",
                "logsource": {...},
                "detection": {...}
            }
        }

        Returns:
        {
            "inferences": [
                {
                    "technique_id": "T1059.001",
                    "confidence": 0.85,
                    "reasoning": "Rule detects...",
                    "ai_model": "gemini-2.5-flash",
                    "cost": 0.000045
                }
            ],
            "cached": false,
            "total_cost": 0.000045
        }
        """
        try:
            data = await request.json()
            rule = data.get('rule')

            if not rule:
                return web.json_response(
                    {'error': 'rule is required'},
                    status=400
                )

            inferences = await self.ai.infer_technique_mapping(rule)

            return web.json_response({
                'inferences': [
                    {
                        'technique_id': inf.technique_id,
                        'confidence': inf.confidence,
                        'reasoning': inf.reasoning,
                        'ai_model': inf.ai_model,
                        'cost': inf.cost
                    }
                    for inf in inferences
                ],
                'count': len(inferences),
                'total_cost': sum(inf.cost for inf in inferences)
            })

        except Exception as e:
            self.logger.error(f"Error in infer_technique: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def prioritize_gaps(self, request: web.Request) -> web.Response:
        """
        POST /api/v1/mitre/ai/prioritize_gaps

        AI-powered gap prioritization with environment context

        Body:
        {
            "gaps": ["T1078", "T1134", "T1136"],
            "environment": {
                "type": "cloud-heavy",
                "os_primary": "windows",
                "industry": "finance"
            },
            "threat_intel": [
                {"technique": "T1078", "actor": "APT29", "recent": true}
            ]
        }

        Returns:
        {
            "recommendations": [
                {
                    "technique_id": "T1078",
                    "priority_score": 95,
                    "reasoning": "Critical because...",
                    "effort": "medium",
                    "fp_estimate": "low"
                }
            ],
            "cached": false,
            "cost": 0.000250
        }
        """
        try:
            data = await request.json()
            gaps = data.get('gaps', [])
            environment = data.get('environment', {})
            threat_intel = data.get('threat_intel', [])
            current_coverage = data.get('current_coverage')

            if not gaps:
                return web.json_response(
                    {'error': 'gaps list is required'},
                    status=400
                )

            recommendations = await self.ai.prioritize_gaps(
                gaps=gaps,
                environment_context=environment,
                threat_intel=threat_intel,
                current_coverage=current_coverage
            )

            return web.json_response({
                'recommendations': [
                    {
                        'technique_id': rec.technique_id,
                        'technique_name': rec.technique_name,
                        'tactics': rec.tactics,
                        'priority_score': rec.priority_score,
                        'reasoning': rec.reasoning,
                        'environment_context': rec.environment_context,
                        'threat_intel_context': rec.threat_intel_context,
                        'attack_chain_context': rec.attack_chain_context,
                        'required_data_sources': rec.required_data_sources,
                        'effort': rec.effort,
                        'fp_estimate': rec.fp_estimate
                    }
                    for rec in recommendations
                ],
                'count': len(recommendations)
            })

        except Exception as e:
            self.logger.error(f"Error in prioritize_gaps: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def predict_false_positives(self, request: web.Request) -> web.Response:
        """
        POST /api/v1/mitre/ai/predict_fp

        Predict false positive rate for a rule before deployment

        Body:
        {
            "rule": {...},
            "similar_rules": [
                {"rule_id": "123", "fp_rate": 0.15, "description": "..."}
            ]
        }

        Returns:
        {
            "predicted_fp_rate": 0.15,
            "confidence": 0.80,
            "risk_factors": ["Broad matching", "No exclusions"],
            "tuning_suggestions": [...]
        }
        """
        try:
            data = await request.json()
            rule = data.get('rule')
            similar_rules = data.get('similar_rules', [])

            if not rule:
                return web.json_response(
                    {'error': 'rule is required'},
                    status=400
                )

            prediction = await self.ai.predict_false_positives(rule, similar_rules)

            if not prediction:
                return web.json_response(
                    {'error': 'AI model not available'},
                    status=503
                )

            return web.json_response({
                'rule_id': prediction.rule_id,
                'predicted_fp_rate': prediction.predicted_fp_rate,
                'confidence': prediction.confidence,
                'risk_factors': prediction.risk_factors,
                'tuning_suggestions': prediction.tuning_suggestions
            })

        except Exception as e:
            self.logger.error(f"Error in predict_fp: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def get_cost_savings(self, request: web.Request) -> web.Response:
        """
        GET /api/v1/mitre/ai/cost_savings

        Get AI cost savings from pattern reuse

        Returns:
        {
            "total_spent": 1.25,
            "total_saved": 12.50,
            "net_cost": -11.25,
            "savings_percentage": 90.9,
            "pattern_reuse_count": 543
        }
        """
        try:
            if not self.ai.db:
                return web.json_response(
                    {'error': 'Database not available'},
                    status=503
                )

            cursor = self.ai.db.cursor()
            cursor.execute("SELECT * FROM get_ai_cost_savings()")
            row = cursor.fetchone()

            if row:
                return web.json_response({
                    'total_spent': float(row[0]),
                    'total_saved': float(row[1]),
                    'net_cost': float(row[2]),
                    'savings_percentage': float(row[3])
                })
            else:
                return web.json_response({
                    'total_spent': 0.0,
                    'total_saved': 0.0,
                    'net_cost': 0.0,
                    'savings_percentage': 0.0
                })

        except Exception as e:
            self.logger.error(f"Error in get_cost_savings: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def get_model_performance(self, request: web.Request) -> web.Response:
        """
        GET /api/v1/mitre/ai/model_performance

        Get performance metrics for each AI model

        Returns:
        {
            "models": [
                {
                    "name": "gemini-2.5-flash",
                    "operations": 1234,
                    "total_cost": 0.05,
                    "avg_latency_ms": 450,
                    "success_rate": 99.2
                }
            ]
        }
        """
        try:
            if not self.ai.db:
                return web.json_response(
                    {'error': 'Database not available'},
                    status=503
                )

            cursor = self.ai.db.cursor()
            cursor.execute("SELECT * FROM get_ai_model_performance()")
            rows = cursor.fetchall()

            models = []
            for row in rows:
                models.append({
                    'name': row[0],
                    'operations': int(row[1]),
                    'total_cost': float(row[2]),
                    'avg_latency_ms': float(row[3]) if row[3] else 0.0,
                    'success_rate': float(row[4]) if row[4] else 0.0
                })

            return web.json_response({
                'models': models,
                'count': len(models)
            })

        except Exception as e:
            self.logger.error(f"Error in get_model_performance: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def get_top_patterns(self, request: web.Request) -> web.Response:
        """
        GET /api/v1/mitre/ai/top_patterns?limit=20

        Get top reusable patterns (highest savings)

        Returns:
        {
            "patterns": [
                {
                    "type": "tier3_mapping",
                    "hash": "abc123...",
                    "reuse_count": 145,
                    "savings_usd": 6.53,
                    "created_at": "2025-10-01T..."
                }
            ]
        }
        """
        try:
            limit = int(request.query.get('limit', 20))

            if not self.ai.db:
                return web.json_response(
                    {'error': 'Database not available'},
                    status=503
                )

            cursor = self.ai.db.cursor()
            cursor.execute("SELECT * FROM get_top_reusable_patterns(%s)", (limit,))
            rows = cursor.fetchall()

            patterns = []
            for row in rows:
                patterns.append({
                    'type': row[0],
                    'hash': row[1],
                    'reuse_count': int(row[2]),
                    'savings_usd': float(row[3]),
                    'created_at': row[4].isoformat() if row[4] else None
                })

            return web.json_response({
                'patterns': patterns,
                'count': len(patterns)
            })

        except Exception as e:
            self.logger.error(f"Error in get_top_patterns: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )
