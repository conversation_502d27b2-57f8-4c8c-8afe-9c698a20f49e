# SIEMLess v2.0 - Frontend Integration Patterns

## Purpose
Reusable code patterns for integrating frontend widgets with the backend API. These patterns ensure consistency, type safety, and proper error handling across all widgets.

---

## Table of Contents
1. [API Service Layer Pattern](#api-service-layer-pattern)
2. [State Management Pattern](#state-management-pattern)
3. [Real-time Updates Pattern](#real-time-updates-pattern)
4. [<PERSON><PERSON><PERSON>](#error-handling-pattern)
5. [Loading States Pattern](#loading-states-pattern)
6. [Enrichment Display Pattern](#enrichment-display-pattern)
7. [Multi-endpoint Aggregation Pattern](#multi-endpoint-aggregation-pattern)

---

## API Service Layer Pattern

### Purpose
Centralized API calls with consistent error handling and response typing.

### Implementation

```typescript
// frontend/src/api/services/alertService.ts
import { apiClient } from '../client'
import type {
  Alert,
  AlertDetail,
  AlertFilters,
  PaginatedResponse,
  APIResponse,
  CreateAlertRequest,
  UpdateAlertRequest
} from '../../types/api'

export const alertService = {
  /**
   * Get paginated list of alerts
   */
  async getAlerts(
    filters?: AlertFilters,
    page: number = 1,
    pageSize: number = 50
  ): Promise<PaginatedResponse<Alert>> {
    const response = await apiClient.get('/api/alerts', {
      params: {
        ...filters,
        page,
        page_size: pageSize
      }
    })
    return response.data
  },

  /**
   * Get detailed alert information
   */
  async getAlertDetail(alertId: string): Promise<AlertDetail> {
    const response = await apiClient.get<APIResponse<AlertDetail>>(
      `/api/alerts/${alertId}`
    )
    return response.data.data
  },

  /**
   * Update alert status
   */
  async updateAlert(
    alertId: string,
    updates: UpdateAlertRequest
  ): Promise<Alert> {
    const response = await apiClient.patch<APIResponse<Alert>>(
      `/api/alerts/${alertId}`,
      updates
    )
    return response.data.data
  },

  /**
   * Create new alert
   */
  async createAlert(alert: CreateAlertRequest): Promise<Alert> {
    const response = await apiClient.post<APIResponse<Alert>>(
      '/api/alerts',
      alert
    )
    return response.data.data
  },

  /**
   * Assign alert to case
   */
  async assignToCase(alertId: string, caseId: string): Promise<Alert> {
    const response = await apiClient.post<APIResponse<Alert>>(
      `/api/alerts/${alertId}/assign`,
      { case_id: caseId }
    )
    return response.data.data
  }
}
```

### Usage Example

```typescript
// In a component
import { alertService } from '@/api/services/alertService'

const MyComponent = () => {
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchAlerts = async () => {
      setLoading(true)
      setError(null)

      try {
        const response = await alertService.getAlerts({
          severity: ['critical', 'high'],
          status: ['open']
        })
        setAlerts(response.data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load alerts')
      } finally {
        setLoading(false)
      }
    }

    fetchAlerts()
  }, [])

  // Render logic
}
```

---

## State Management Pattern

### Purpose
Zustand stores for widget-specific state with consistent structure.

### Implementation

```typescript
// frontend/src/stores/alertStore.ts
import { create } from 'zustand'
import { alertService } from '../api/services/alertService'
import type {
  Alert,
  AlertDetail,
  AlertFilters,
  UpdateAlertRequest
} from '../types/api'

interface AlertState {
  // Data
  alerts: Alert[]
  selectedAlert: AlertDetail | null
  filters: AlertFilters
  pagination: {
    page: number
    pageSize: number
    totalItems: number
    totalPages: number
  }

  // Loading states
  loading: {
    alerts: boolean
    detail: boolean
    update: boolean
  }

  // Error states
  error: {
    alerts: string | null
    detail: string | null
    update: string | null
  }

  // Actions
  fetchAlerts: (filters?: AlertFilters) => Promise<void>
  selectAlert: (alertId: string) => Promise<void>
  updateAlert: (alertId: string, updates: UpdateAlertRequest) => Promise<void>
  clearSelectedAlert: () => void
  setFilters: (filters: Partial<AlertFilters>) => void
  setPage: (page: number) => Promise<void>
  refreshAlerts: () => Promise<void>
}

export const useAlertStore = create<AlertState>((set, get) => ({
  // Initial state
  alerts: [],
  selectedAlert: null,
  filters: {},
  pagination: {
    page: 1,
    pageSize: 50,
    totalItems: 0,
    totalPages: 0
  },
  loading: {
    alerts: false,
    detail: false,
    update: false
  },
  error: {
    alerts: null,
    detail: null,
    update: null
  },

  // Fetch alerts with filters
  fetchAlerts: async (filters?: AlertFilters) => {
    set((state) => ({
      loading: { ...state.loading, alerts: true },
      error: { ...state.error, alerts: null }
    }))

    try {
      const currentFilters = filters || get().filters
      const { page, pageSize } = get().pagination

      const response = await alertService.getAlerts(
        currentFilters,
        page,
        pageSize
      )

      set({
        alerts: response.data,
        filters: currentFilters,
        pagination: {
          ...get().pagination,
          totalItems: response.pagination.total_items,
          totalPages: response.pagination.total_pages
        },
        loading: { ...get().loading, alerts: false }
      })
    } catch (error) {
      set((state) => ({
        loading: { ...state.loading, alerts: false },
        error: {
          ...state.error,
          alerts: error instanceof Error ? error.message : 'Failed to load alerts'
        }
      }))
    }
  },

  // Select and load alert details
  selectAlert: async (alertId: string) => {
    set((state) => ({
      loading: { ...state.loading, detail: true },
      error: { ...state.error, detail: null }
    }))

    try {
      const alert = await alertService.getAlertDetail(alertId)

      set({
        selectedAlert: alert,
        loading: { ...get().loading, detail: false }
      })
    } catch (error) {
      set((state) => ({
        loading: { ...state.loading, detail: false },
        error: {
          ...state.error,
          detail: error instanceof Error ? error.message : 'Failed to load alert'
        }
      }))
    }
  },

  // Update alert
  updateAlert: async (alertId: string, updates: UpdateAlertRequest) => {
    set((state) => ({
      loading: { ...state.loading, update: true },
      error: { ...state.error, update: null }
    }))

    try {
      const updatedAlert = await alertService.updateAlert(alertId, updates)

      // Update in list
      set((state) => ({
        alerts: state.alerts.map((a) =>
          a.alert_id === alertId ? { ...a, ...updatedAlert } : a
        ),
        selectedAlert: state.selectedAlert?.alert_id === alertId
          ? { ...state.selectedAlert, ...updatedAlert }
          : state.selectedAlert,
        loading: { ...state.loading, update: false }
      }))
    } catch (error) {
      set((state) => ({
        loading: { ...state.loading, update: false },
        error: {
          ...state.error,
          update: error instanceof Error ? error.message : 'Failed to update alert'
        }
      }))
    }
  },

  // Clear selected alert
  clearSelectedAlert: () => set({ selectedAlert: null }),

  // Update filters
  setFilters: (filters: Partial<AlertFilters>) => {
    set((state) => ({
      filters: { ...state.filters, ...filters },
      pagination: { ...state.pagination, page: 1 } // Reset to page 1
    }))
    get().fetchAlerts()
  },

  // Change page
  setPage: async (page: number) => {
    set((state) => ({
      pagination: { ...state.pagination, page }
    }))
    await get().fetchAlerts()
  },

  // Refresh current view
  refreshAlerts: async () => {
    await get().fetchAlerts()
  }
}))
```

### Usage Example

```typescript
import { useAlertStore } from '@/stores/alertStore'

const AlertQueueWidget = () => {
  const {
    alerts,
    loading,
    error,
    fetchAlerts,
    selectAlert,
    setFilters
  } = useAlertStore()

  useEffect(() => {
    fetchAlerts()
  }, [fetchAlerts])

  const handleFilterChange = (newFilters: Partial<AlertFilters>) => {
    setFilters(newFilters)
  }

  const handleAlertClick = (alertId: string) => {
    selectAlert(alertId)
  }

  // Render logic
}
```

---

## Real-time Updates Pattern

### Purpose
WebSocket integration for real-time data updates without full page refresh.

### Implementation

```typescript
// frontend/src/hooks/useRealtimeUpdates.ts
import { useEffect } from 'react'
import { wsClient } from '../api/client'
import type { WebSocketMessage, WebSocketMessageType } from '../types/api'

interface UseRealtimeUpdatesOptions<T> {
  messageType: WebSocketMessageType
  onMessage: (data: T) => void
  enabled?: boolean
}

export function useRealtimeUpdates<T = any>({
  messageType,
  onMessage,
  enabled = true
}: UseRealtimeUpdatesOptions<T>) {
  useEffect(() => {
    if (!enabled) return

    const handleMessage = (message: WebSocketMessage<T>) => {
      if (message.type === messageType) {
        onMessage(message.data)
      }
    }

    // Subscribe to message type
    wsClient.on(messageType, handleMessage)

    // Connect WebSocket if not already connected
    if (!wsClient.isConnected()) {
      wsClient.connect()
    }

    // Cleanup
    return () => {
      wsClient.off(messageType, handleMessage)
    }
  }, [messageType, onMessage, enabled])
}
```

### Usage Example

```typescript
// In alert store - add real-time updates
import { useRealtimeUpdates } from '../hooks/useRealtimeUpdates'
import type { Alert } from '../types/api'

const AlertQueueComponent = () => {
  const { alerts, fetchAlerts } = useAlertStore()

  // Real-time new alerts
  useRealtimeUpdates<Alert>({
    messageType: 'alert.new',
    onMessage: (newAlert) => {
      // Update store
      useAlertStore.setState((state) => ({
        alerts: [newAlert, ...state.alerts]
      }))

      // Show notification
      toast.success(`New ${newAlert.severity} alert: ${newAlert.title}`)
    }
  })

  // Real-time alert updates
  useRealtimeUpdates<Alert>({
    messageType: 'alert.updated',
    onMessage: (updatedAlert) => {
      useAlertStore.setState((state) => ({
        alerts: state.alerts.map((a) =>
          a.alert_id === updatedAlert.alert_id ? updatedAlert : a
        )
      }))
    }
  })

  // Component logic
}
```

---

## Error Handling Pattern

### Purpose
Consistent error handling and user feedback across all API calls.

### Implementation

```typescript
// frontend/src/utils/errorHandler.ts
import { toast } from 'react-hot-toast'
import type { ErrorResponse } from '../types/api'

export class APIError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public errorCode?: string,
    public details?: Record<string, any>
  ) {
    super(message)
    this.name = 'APIError'
  }
}

export function handleAPIError(error: unknown): never {
  if (error instanceof APIError) {
    throw error
  }

  if (typeof error === 'object' && error !== null) {
    const err = error as any

    // Axios error response
    if (err.response?.data) {
      const errorData = err.response.data as ErrorResponse

      throw new APIError(
        errorData.error || 'An error occurred',
        err.response.status,
        errorData.error_code,
        errorData.details
      )
    }

    // Network error
    if (err.request) {
      throw new APIError('Network error - please check your connection')
    }
  }

  // Unknown error
  throw new APIError('An unexpected error occurred')
}

export function displayError(error: unknown, fallbackMessage: string = 'An error occurred') {
  let message = fallbackMessage

  if (error instanceof APIError) {
    message = error.message
  } else if (error instanceof Error) {
    message = error.message
  }

  toast.error(message)
}

// React error boundary hook
export function useErrorBoundary() {
  return {
    handleError: (error: unknown, context: string) => {
      console.error(`Error in ${context}:`, error)
      displayError(error, `Failed to ${context}`)
    }
  }
}
```

### Usage Example

```typescript
// In API service
import { handleAPIError } from '../utils/errorHandler'

export const entityService = {
  async getEntity(entityId: string): Promise<Entity> {
    try {
      const response = await apiClient.get(`/api/entities/${entityId}`)
      return response.data.data
    } catch (error) {
      handleAPIError(error)
    }
  }
}

// In component
import { useErrorBoundary } from '@/utils/errorHandler'

const MyComponent = () => {
  const { handleError } = useErrorBoundary()

  const handleAction = async () => {
    try {
      await someAPICall()
    } catch (error) {
      handleError(error, 'perform action')
    }
  }
}
```

---

## Loading States Pattern

### Purpose
Skeleton loaders and loading indicators for smooth UX.

### Implementation

```typescript
// frontend/src/components/common/LoadingStates.tsx
import React from 'react'

// Generic loading spinner
export const LoadingSpinner: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }

  return (
    <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]}`} />
  )
}

// Skeleton loader for cards
export const SkeletonCard: React.FC = () => (
  <div className="animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
    <div className="h-4 bg-gray-200 rounded w-full"></div>
  </div>
)

// Skeleton loader for table rows
export const SkeletonTableRow: React.FC = () => (
  <tr className="animate-pulse">
    <td className="px-4 py-2">
      <div className="h-4 bg-gray-200 rounded"></div>
    </td>
    <td className="px-4 py-2">
      <div className="h-4 bg-gray-200 rounded"></div>
    </td>
    <td className="px-4 py-2">
      <div className="h-4 bg-gray-200 rounded"></div>
    </td>
  </tr>
)

// Full-page loader
export const PageLoader: React.FC = () => (
  <div className="flex items-center justify-center h-screen">
    <div className="text-center">
      <LoadingSpinner size="lg" />
      <p className="mt-4 text-gray-600">Loading...</p>
    </div>
  </div>
)

// Loading overlay
export const LoadingOverlay: React.FC<{ message?: string }> = ({ message }) => (
  <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
    <div className="text-center">
      <LoadingSpinner size="lg" />
      {message && <p className="mt-4 text-gray-600">{message}</p>}
    </div>
  </div>
)
```

### Usage Pattern

```typescript
// frontend/src/hooks/useLoadingState.ts
import { useState, useCallback } from 'react'

export function useLoadingState() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const withLoading = useCallback(async <T,>(
    fn: () => Promise<T>
  ): Promise<T | undefined> => {
    setIsLoading(true)
    setError(null)

    try {
      const result = await fn()
      return result
    } catch (err) {
      const message = err instanceof Error ? err.message : 'An error occurred'
      setError(message)
      return undefined
    } finally {
      setIsLoading(false)
    }
  }, [])

  return { isLoading, error, withLoading, setError }
}
```

### Usage Example

```typescript
import { useLoadingState } from '@/hooks/useLoadingState'
import { LoadingSpinner, SkeletonCard } from '@/components/common/LoadingStates'

const MyWidget = () => {
  const [data, setData] = useState(null)
  const { isLoading, withLoading } = useLoadingState()

  useEffect(() => {
    withLoading(async () => {
      const result = await fetchData()
      setData(result)
    })
  }, [withLoading])

  if (isLoading) {
    return (
      <div className="grid grid-cols-3 gap-4">
        <SkeletonCard />
        <SkeletonCard />
        <SkeletonCard />
      </div>
    )
  }

  return <div>{/* Render data */}</div>
}
```

---

## Enrichment Display Pattern

### Purpose
Consistent display of three-layer enrichment data across all entity views.

### Implementation

```typescript
// frontend/src/components/enrichment/EnrichmentDisplay.tsx
import React from 'react'
import type { Entity } from '@/types/api'

interface EnrichmentDisplayProps {
  entity: Entity
  layers?: ('layer1' | 'layer2' | 'layer3')[]
  compact?: boolean
}

export const EnrichmentDisplay: React.FC<EnrichmentDisplayProps> = ({
  entity,
  layers = ['layer1', 'layer2', 'layer3'],
  compact = false
}) => {
  const { enrichments, threat_score, is_threat } = entity

  return (
    <div className={`space-y-4 ${compact ? 'text-sm' : ''}`}>
      {/* Threat Score Header */}
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded">
        <div>
          <h3 className="font-semibold">Threat Assessment</h3>
          <p className="text-gray-600">
            {is_threat ? 'Known Threat' : 'Clean'}
          </p>
        </div>
        <ThreatScoreBadge score={threat_score} />
      </div>

      {/* Layer 1: Business & Technical Context */}
      {layers.includes('layer1') && (
        <div className="border-l-4 border-blue-500 pl-4">
          <h4 className="font-semibold mb-2">Business Context</h4>

          {/* Geolocation */}
          {enrichments.geolocation && (
            <GeolocationCard data={enrichments.geolocation} compact={compact} />
          )}

          {/* Asset Info */}
          {enrichments.asset_info && (
            <AssetInfoCard data={enrichments.asset_info} compact={compact} />
          )}

          {/* Network Info */}
          {enrichments.network_info && (
            <NetworkInfoCard data={enrichments.network_info} compact={compact} />
          )}
        </div>
      )}

      {/* Layer 2: CTI Threat Intelligence */}
      {layers.includes('layer2') && enrichments.cti_threat_intelligence && (
        <div className="border-l-4 border-red-500 pl-4">
          <h4 className="font-semibold mb-2">Threat Intelligence</h4>
          <CTIMatchCard match={enrichments.cti_threat_intelligence} compact={compact} />
        </div>
      )}

      {/* Layer 3: Vendor Context */}
      {layers.includes('layer3') && enrichments.vendor_context && (
        <div className="border-l-4 border-green-500 pl-4">
          <h4 className="font-semibold mb-2">Vendor Context</h4>
          {Object.entries(enrichments.vendor_context).map(([vendor, context]) => (
            <VendorContextCard
              key={vendor}
              vendor={vendor}
              context={context}
              compact={compact}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Threat Score Badge
export const ThreatScoreBadge: React.FC<{ score: number }> = ({ score }) => {
  const getColor = (score: number) => {
    if (score >= 80) return 'bg-red-500'
    if (score >= 60) return 'bg-orange-500'
    if (score >= 40) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  return (
    <div className="text-center">
      <div className={`${getColor(score)} text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold`}>
        {score}
      </div>
      <p className="text-xs text-gray-600 mt-1">Threat Score</p>
    </div>
  )
}

// Geolocation Card
export const GeolocationCard: React.FC<{ data: GeolocationData; compact?: boolean }> = ({
  data,
  compact
}) => (
  <div className={`bg-white p-3 rounded shadow-sm ${compact ? 'text-xs' : ''}`}>
    <h5 className="font-medium mb-2">Location</h5>
    <div className="space-y-1">
      <p><span className="text-gray-600">Country:</span> {data.country}</p>
      {data.city && <p><span className="text-gray-600">City:</span> {data.city}</p>}
      {data.isp && <p><span className="text-gray-600">ISP:</span> {data.isp}</p>}
      {data.is_vpn && <span className="inline-block px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">VPN</span>}
      {data.is_proxy && <span className="inline-block px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs ml-1">Proxy</span>}
      {data.is_tor && <span className="inline-block px-2 py-1 bg-red-100 text-red-800 rounded text-xs ml-1">TOR</span>}
    </div>
  </div>
)

// CTI Match Card
export const CTIMatchCard: React.FC<{ match: CTIMatch; compact?: boolean }> = ({
  match,
  compact
}) => (
  <div className={`bg-red-50 p-3 rounded border border-red-200 ${compact ? 'text-xs' : ''}`}>
    <div className="flex items-center justify-between mb-2">
      <h5 className="font-medium text-red-800">CTI Match</h5>
      <span className="px-2 py-1 bg-red-600 text-white rounded text-xs">
        {match.source.toUpperCase()}
      </span>
    </div>
    <div className="space-y-1">
      <p><span className="text-gray-600">Match Type:</span> {match.match_type}</p>
      <p><span className="text-gray-600">Confidence:</span> {match.confidence}%</p>
      {match.threat_actor && (
        <p><span className="text-gray-600">Threat Actor:</span> {match.threat_actor}</p>
      )}
      {match.campaign && (
        <p><span className="text-gray-600">Campaign:</span> {match.campaign}</p>
      )}
      <div className="flex flex-wrap gap-1 mt-2">
        {match.tags.map((tag) => (
          <span key={tag} className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
            {tag}
          </span>
        ))}
      </div>
    </div>
  </div>
)
```

### Usage Example

```typescript
import { EnrichmentDisplay } from '@/components/enrichment/EnrichmentDisplay'

const EntityDetailPanel = ({ entity }: { entity: Entity }) => {
  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">{entity.entity_value}</h2>

      {/* Display all enrichment layers */}
      <EnrichmentDisplay entity={entity} />
    </div>
  )
}

// Compact version for entity list
const EntityListItem = ({ entity }: { entity: Entity }) => {
  return (
    <div className="p-2">
      <EnrichmentDisplay
        entity={entity}
        layers={['layer2']}  // Only show threats
        compact={true}
      />
    </div>
  )
}
```

---

## Multi-endpoint Aggregation Pattern

### Purpose
Efficiently aggregate data from multiple API endpoints with parallel fetching.

### Implementation

```typescript
// frontend/src/hooks/useAggregatedData.ts
import { useState, useEffect, useCallback } from 'react'

interface AggregatedDataOptions<T> {
  fetchers: Array<() => Promise<any>>
  combiner: (...results: any[]) => T
  enabled?: boolean
  dependencies?: any[]
}

export function useAggregatedData<T>({
  fetchers,
  combiner,
  enabled = true,
  dependencies = []
}: AggregatedDataOptions<T>) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchData = useCallback(async () => {
    if (!enabled) return

    setLoading(true)
    setError(null)

    try {
      // Fetch all in parallel
      const results = await Promise.all(
        fetchers.map((fetcher) => fetcher())
      )

      // Combine results
      const combined = combiner(...results)
      setData(combined)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data')
    } finally {
      setLoading(false)
    }
  }, [fetchers, combiner, enabled])

  useEffect(() => {
    fetchData()
  }, [fetchData, ...dependencies])

  return { data, loading, error, refetch: fetchData }
}
```

### Usage Example

```typescript
// Dashboard that aggregates multiple metrics
import { useAggregatedData } from '@/hooks/useAggregatedData'
import { alertService } from '@/api/services/alertService'
import { caseService } from '@/api/services/caseService'
import { ctiService } from '@/api/services/ctiService'
import { detectionService } from '@/api/services/detectionService'

interface DashboardData {
  stats: DashboardStats
  criticalAlerts: Alert[]
  openCases: Case[]
  ctiStatus: CTIPluginInfo[]
  detectionFidelity: DetectionFidelity
}

const DashboardWidget = () => {
  const { data, loading, error } = useAggregatedData<DashboardData>({
    fetchers: [
      () => dashboardService.getStats(),
      () => alertService.getAlerts({ severity: ['critical'], status: ['open'] }),
      () => caseService.getCases({ status: ['open'] }),
      () => ctiService.getStatus(),
      () => detectionService.getFidelity()
    ],
    combiner: (stats, alerts, cases, cti, fidelity) => ({
      stats: stats.data,
      criticalAlerts: alerts.data,
      openCases: cases.data,
      ctiStatus: cti.data,
      detectionFidelity: fidelity.data
    })
  })

  if (loading) return <LoadingSpinner />
  if (error) return <ErrorDisplay message={error} />
  if (!data) return null

  return (
    <div className="grid grid-cols-2 gap-4">
      <StatCard title="Critical Alerts" value={data.criticalAlerts.length} />
      <StatCard title="Open Cases" value={data.openCases.length} />
      <StatCard title="Detection Confidence" value={`${data.detectionFidelity.overall_confidence}%`} />
      <StatCard title="CTI Indicators" value={data.stats.cti_indicators} />
    </div>
  )
}
```

---

## Complete Service Layer Structure

```typescript
// frontend/src/api/services/index.ts

// Export all services
export { alertService } from './alertService'
export { caseService } from './caseService'
export { entityService } from './entityService'
export { ctiService } from './ctiService'
export { detectionService } from './detectionService'
export { logSourceService } from './logSourceService'
export { investigationService } from './investigationService'
export { workflowService } from './workflowService'
export { mitreService } from './mitreService'
export { patternService } from './patternService'
export { ruleService } from './ruleService'
export { dashboardService } from './dashboardService'

// Service layer structure
/**
 * frontend/src/api/
 * ├── client.ts              # Axios instance + WebSocket client
 * ├── services/
 * │   ├── index.ts           # Export all services
 * │   ├── alertService.ts    # Alert CRUD operations
 * │   ├── caseService.ts     # Case management
 * │   ├── entityService.ts   # Entity operations
 * │   ├── ctiService.ts      # CTI plugin management
 * │   ├── detectionService.ts # Detection fidelity
 * │   ├── logSourceService.ts # Log source quality
 * │   ├── investigationService.ts # Investigation context
 * │   ├── workflowService.ts # Workflow orchestration
 * │   ├── mitreService.ts    # MITRE ATT&CK data
 * │   ├── patternService.ts  # Pattern library
 * │   ├── ruleService.ts     # Detection rules
 * │   └── dashboardService.ts # Dashboard data
 * └── types/
 *     └── api.ts             # All TypeScript types
 */
```

---

## Summary of Patterns

| Pattern | Purpose | Complexity |
|---------|---------|------------|
| API Service Layer | Centralized API calls with typing | Low |
| State Management | Zustand stores for widget state | Medium |
| Real-time Updates | WebSocket integration | Medium |
| Error Handling | Consistent error display | Low |
| Loading States | Skeleton loaders and spinners | Low |
| Enrichment Display | Three-layer entity enrichment | High |
| Multi-endpoint Aggregation | Parallel data fetching | Medium |

---

## Next Steps

1. ✅ Complete API Reference (Done)
2. ✅ Widget Catalog (Done)
3. ✅ TypeScript Types (Done)
4. ✅ Integration Patterns (This Document)
5. ⏳ Create Reusable Components (Week 1)
6. ⏳ Implement P0 Widgets (Week 1-2)

---

**Document Status**: Complete
**Last Updated**: October 3, 2025
**Next Review**: After first widget implementation
