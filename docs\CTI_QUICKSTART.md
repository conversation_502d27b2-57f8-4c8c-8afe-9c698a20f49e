# CTI Integration Quick Start Guide

## 🚀 Quick Setup

### 1. Configure Environment Variables

Add to your `.env` file:

```bash
# Required for OpenCTI
OPENCTI_ENABLED=true
OPENCTI_URL=http://your-opencti-server:8080/
OPENCTI_TOKEN=your-opencti-api-token

# Required for OTX (AlienVault)
OTX_API_KEY=your-otx-api-key

# Optional - ThreatFox
THREATFOX_AUTH_KEY=your-threatfox-key
```

### 2. Start the System

```bash
# Start all services
docker-compose up -d

# Verify CTI Manager is running
docker-compose logs ingestion_engine | grep "Started.*CTI"
```

### 3. Verify CTI Data Flow

```bash
# Check rules being generated
docker-compose exec postgres psql -U siemless -d siemless_v2 -c \
"SELECT rule_data->>'source' as source, COUNT(*) FROM detection_rules GROUP BY 1;"
```

Expected output:
```
 source  | count
---------+-------
 opencti |   110
 otx     |   431
```

## 🔍 Testing the Integration

### Run Test Script

```bash
python test_cti_flow.py
```

This will:
1. Send test CTI data through the pipeline
2. Verify rule generation
3. Check database storage

### Manual CTI Update Trigger

```python
# test_manual_cti.py
import asyncio
import redis.asyncio as redis_async
import json

async def trigger_manual_update():
    redis_client = await redis_async.Redis(host='localhost', port=6380)

    # Trigger OTX update
    message = {'command': 'manual_update', 'source': 'otx'}
    await redis_client.publish('ingestion.cti.manual_update', json.dumps(message))

    print("Manual update triggered")
    await redis_client.aclose()

asyncio.run(trigger_manual_update())
```

## 📊 Monitor CTI Activity

### Real-time Logs

```bash
# Watch CTI activity
docker-compose logs -f ingestion_engine | grep -E "CTI|OTX|OpenCTI"

# Check Backend Engine rule generation
docker-compose logs -f backend_engine | grep "Generated.*rules"
```

### Database Queries

```sql
-- Latest rules
SELECT
    rule_data->>'title' as title,
    rule_data->>'source' as source,
    created_at
FROM detection_rules
ORDER BY created_at DESC
LIMIT 10;

-- Rules by confidence
SELECT
    rule_data->>'title' as title,
    (rule_data->>'confidence')::float as confidence
FROM detection_rules
WHERE (rule_data->>'confidence')::float > 0.8
ORDER BY confidence DESC;

-- Count by indicator type
SELECT
    rule_data->>'ioc_type' as type,
    COUNT(*) as count
FROM detection_rules
GROUP BY 1
ORDER BY 2 DESC;
```

## 🛠️ Troubleshooting

### CTI Manager Not Starting

```bash
# Check environment variables
docker-compose exec ingestion_engine env | grep -E "OPENCTI|OTX"

# Check CTI configuration
docker-compose logs ingestion_engine | grep "CTI Configuration"
```

### No Rules Generated

1. Check CTI source connectivity:
```bash
# Test OpenCTI connection
curl -H "Authorization: Bearer YOUR_TOKEN" http://opencti-server:8080/graphql

# Test OTX API
curl -H "X-OTX-API-KEY: YOUR_KEY" https://otx.alienvault.com/api/v1/user/me
```

2. Check Redis connectivity:
```bash
docker-compose exec redis redis-cli ping
```

3. Verify Backend Engine subscription:
```bash
docker-compose logs backend_engine | grep "Subscribed to channels"
```

### Rules Not Stored

```bash
# Check database connection
docker-compose exec postgres psql -U siemless -d siemless_v2 -c "\dt"

# Check for storage errors
docker-compose logs backend_engine | grep -E "Failed to store|ERROR"
```

## 📈 Performance Tuning

### Adjust Update Intervals

Edit `engines/ingestion/cti_manager.py`:

```python
self.update_intervals = {
    'opencti': 1800,  # 30 minutes (faster)
    'otx': 900,       # 15 minutes (faster)
    'threatfox': 3600,  # 1 hour
    'misp': 3600
}
```

### Limit Indicators Per Batch

Edit `engines/ingestion/cti_manager.py`:

```python
# In _process_opencti method
indicators = await connector.get_indicators(limit=50)  # Reduce for testing

# In _publish_cti_data method
for indicator in cti_data['indicators'][:5]:  # Reduce for performance
```

### Enable Debug Logging

```bash
# Set in .env
LOG_LEVEL=DEBUG

# Restart engines
docker-compose restart ingestion_engine backend_engine
```

## 🔗 Integration Points

### Frontend Dashboard

Access CTI metrics at:
- http://localhost:3000/dashboard (when frontend is running)
- Grafana: http://localhost:3001 (if configured)

### API Endpoints

```bash
# Get CTI status
curl http://localhost:8003/cti/status

# Get rule statistics
curl http://localhost:8002/rules/stats
```

### Message Queue Channels

Monitor Redis channels:
```bash
# Subscribe to CTI updates
docker-compose exec redis redis-cli
> SUBSCRIBE ingestion.cti.update

# Monitor indicators
> SUBSCRIBE ingestion.cti.indicators
```

## 🎯 Next Steps

1. **Configure Real CTI Sources**
   - Get OpenCTI API token from your instance
   - Register for free OTX account at https://otx.alienvault.com

2. **Customize Rule Generation**
   - Edit sigma templates in Backend Engine
   - Add custom confidence scoring

3. **Set Up Monitoring**
   - Configure Grafana dashboards
   - Set up alerting for CTI failures

4. **Production Deployment**
   - Use secrets management for API keys
   - Configure SSL/TLS for CTI connections
   - Set up log rotation