"""
SIEMLess v2.0 - Intelligence Engine
AI-powered pattern discovery, validation, and knowledge crystallization
Consolidates: AI Consensus Engine + Librarian Engine + Pattern Crystallization
"""

import asyncio
import json
from typing import Dict, Any, List
from datetime import datetime
import sys
import os

# Add parent directory to path for base_engine import
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from base_engine import BaseEngine

# Import modular components
from ai_models import AIModelManager
from pattern_manager import Pattern<PERSON>anager
from consensus_engine import ConsensusEngine
from cost_tracker import CostTracker
from message_handlers import MessageHandlers

class IntelligenceEngine(BaseEngine):
    """
    Intelligence Engine: The central nervous system for AI operations and pattern management

    Core Functions:
    - Multi-AI consensus validation
    - Pattern crystallization (expensive AI → deterministic rules)
    - Knowledge evolution through real-world feedback
    - Cost optimization through intelligent model selection
    """

    def __init__(self):
        super().__init__("intelligence")

        # Initialize AI and cost tracking (don't need DB)
        self.ai_model_manager = AIModelManager(self.logger)
        self.cost_tracker = CostTracker(self.logger)

        # These will be initialized in start_engine_tasks() after DB pool is ready
        self.consensus_engine = None
        self.pattern_manager = None
        self.message_handlers = None

        self.logger.info("Intelligence Engine initialized with modular components")

    def get_subscribed_channels(self) -> List[str]:
        """Subscribe to channels for pattern discovery and validation"""
        return [
            'intelligence.consensus',      # AI consensus requests
            'intelligence.crystallize',    # Pattern crystallization requests
            'intelligence.validate',       # Pattern validation requests
            'intelligence.parse_log_sample',  # Parser generation requests
            'intelligence.analyze_telemetry',  # Telemetry mode detection
            'intelligence.extract_entities_ai',  # NEW: AI-powered entity extraction
            'intelligence.generate_log_mapping',  # NEW: Schema mapping generation
            'ingestion.unknown_patterns',  # Unknown patterns from ingestion
            'contextualization.new_entities'  # New entity patterns
        ]

    def start_engine_tasks(self) -> List[asyncio.Task]:
        """Start Intelligence Engine specific tasks"""
        # Initialize components now that DB pool is ready
        self.consensus_engine = ConsensusEngine(
            self.ai_model_manager,
            self.cost_tracker,
            self.db_pool,
            self.logger
        )
        self.pattern_manager = PatternManager(
            self.db_pool,
            self.ai_model_manager,
            self.logger
        )
        self.message_handlers = MessageHandlers(
            self.ai_model_manager,
            self.pattern_manager,
            self.consensus_engine,
            self.cost_tracker,
            self.publish_message,
            self.logger
        )
        self.logger.info("Intelligence components initialized with DB pool")

        tasks = [
            asyncio.create_task(self._pattern_cleanup_task()),
            asyncio.create_task(self._cost_optimization_task()),
            asyncio.create_task(self._library_maintenance_task())
        ]
        return tasks

    async def process_message(self, message: Dict[str, Any]):
        """Process incoming messages using message handlers"""
        try:
            channel = message['channel']
            data = json.loads(message['data'])

            self.logger.info(f"Processing message from {channel}")

            # Delegate to message handlers
            await self.message_handlers.process_message(channel, data)

        except Exception as e:
            self.logger.error(f"Error processing message: {e}")

    # API methods for accessing module functionality
    def get_cost_metrics(self) -> Dict[str, Any]:
        """Get current cost metrics"""
        return self.cost_tracker.get_cost_metrics()

    def get_consensus_statistics(self) -> Dict[str, Any]:
        """Get consensus statistics"""
        return self.consensus_engine.get_consensus_statistics()

    def list_patterns(self, pattern_type: str = None) -> List[Dict[str, Any]]:
        """List available patterns"""
        return self.pattern_manager.list_patterns(pattern_type)

    async def analyze_pattern(self, pattern_data: Dict[str, Any], complexity: str = 'medium') -> Dict[str, Any]:
        """Analyze a pattern using AI consensus"""
        models = self.ai_model_manager.select_models_for_task(complexity)
        return await self.consensus_engine.get_ai_consensus(pattern_data, models)

    # Background tasks using modular components
    async def _pattern_cleanup_task(self):
        """Periodic cleanup of old patterns"""
        while self.is_running:
            try:
                await self.pattern_manager.cleanup_patterns()
            except Exception as e:
                self.logger.error(f"Pattern cleanup error: {e}")

            # Run cleanup every 24 hours
            await asyncio.sleep(86400)

    async def _cost_optimization_task(self):
        """Monitor and optimize costs"""
        while self.is_running:
            try:
                await self.cost_tracker.optimize_costs()
            except Exception as e:
                self.logger.error(f"Cost optimization error: {e}")

            # Run optimization check every hour
            await asyncio.sleep(3600)

    async def _library_maintenance_task(self):
        """Maintain pattern library performance"""
        while self.is_running:
            try:
                await self.pattern_manager.maintain_library()
            except Exception as e:
                self.logger.error(f"Library maintenance error: {e}")

            # Run maintenance every 6 hours
            await asyncio.sleep(21600)

if __name__ == "__main__":
    async def main():
        engine = IntelligenceEngine()
        await engine.start()

    asyncio.run(main())