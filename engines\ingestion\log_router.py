"""
SIEMLess v2.0 - Log Router
Intelligent routing of logs to appropriate engines based on pattern matching and use cases
"""

import json
import asyncio
from datetime import datetime
from typing import Dict, Any, List
import logging


class LogRouter:
    """Routes logs to appropriate engines based on pattern matching and use cases"""

    def __init__(self, pattern_matcher, publisher, logger: logging.Logger):
        self.pattern_matcher = pattern_matcher
        self.publisher = publisher
        self.logger = logger
        self.use_case_routes = self._initialize_routing_rules()

    def _initialize_routing_rules(self) -> Dict[str, List[str]]:
        """LIGHTWEIGHT routing - contextualization first for entity extraction"""
        return {
            # Security events → Extract entities + intelligence (backend only for critical)
            'authentication': ['contextualization'],  # Extract entities only
            'failed_authentication': ['contextualization', 'intelligence', 'delivery'],
            'privilege_escalation': ['contextualization', 'intelligence', 'backend', 'delivery'],
            'malware': ['contextualization', 'intelligence', 'backend', 'delivery'],

            # Network → Extract entities (no storage for normal traffic)
            'network_anomaly': ['contextualization', 'intelligence'],
            'data_exfiltration': ['contextualization', 'intelligence', 'delivery'],
            'traffic': ['contextualization'],  # Entity extraction only

            # Compliance → Minimal processing
            'compliance_violation': ['contextualization', 'delivery'],
            'audit_event': ['contextualization'],  # Extract entities only

            # Detection → Security events
            'detection': ['contextualization', 'intelligence'],
            'threat_detection': ['contextualization', 'intelligence', 'backend', 'delivery'],

            # System events → Extract entities only (no storage)
            'system': ['contextualization'],  # Most common - extract entities only
            'performance': [],  # Skip performance logs entirely

            # Unknown → Learn patterns, extract entities
            'unknown': ['contextualization']  # No backend storage for unknown
        }

    async def process_log_batch(self, logs: List[Dict[str, Any]], parser_hot_reload=None) -> Dict[str, int]:
        """Process a batch of logs with pattern matching and routing"""
        stats = {
            'pattern_matched': 0,
            'unknown_patterns': 0,
            'total_processed': len(logs),
            'routing_errors': 0
        }

        try:
            for log in logs:
                await self._process_single_log(log, parser_hot_reload, stats)

            # Log processing summary
            match_rate = (stats['pattern_matched'] / len(logs)) * 100 if logs else 0
            self.logger.info(
                f"Processed {len(logs)} logs: "
                f"{stats['pattern_matched']} matched ({match_rate:.1f}%), "
                f"{stats['unknown_patterns']} unknown"
            )

            # Alert if too many unknowns (indicates we need more patterns)
            unknown_threshold = 10  # Should come from config
            if stats['unknown_patterns'] > unknown_threshold:
                self.logger.warning(f"High unknown pattern rate: {stats['unknown_patterns']} in this batch")

        except Exception as e:
            self.logger.error(f"Error processing log batch: {e}")
            stats['routing_errors'] += len(logs)

        return stats

    async def _process_single_log(self, log: Dict[str, Any], parser_hot_reload, stats: Dict[str, int]):
        """Process a single log through the routing pipeline"""
        try:
            # STEP 1: Try hot-reloaded parsers first
            if parser_hot_reload:
                log = await self._process_log_with_parsers(log, parser_hot_reload)

            # STEP 2: Try pattern matching (99.9% of logs should match)
            log_data = log.get('parsed_data', log.get('data', log))
            pattern_match = self.pattern_matcher.match(log_data)

            if pattern_match:
                # Pattern matched! Process for FREE
                stats['pattern_matched'] += 1
                await self._route_matched_log(log, pattern_match)
            else:
                # Unknown pattern (0.1% of logs) - needs AI analysis
                stats['unknown_patterns'] += 1
                await self._route_unknown_log(log)

        except Exception as e:
            self.logger.error(f"Error processing single log: {e}")
            stats['routing_errors'] += 1

    async def _process_log_with_parsers(self, log: Dict[str, Any], parser_hot_reload) -> Dict[str, Any]:
        """Process log using hot-reloaded parsers"""
        try:
            # Try to parse the log
            log_text = json.dumps(log.get('data', {})) if isinstance(log.get('data'), dict) else str(log.get('data', ''))

            # Use parser hot reload to parse
            parse_result = await parser_hot_reload.parse_log(log_text, log.get('source_type'))

            if parse_result['parsed']:
                # Extract entities if parsed
                entities = await parser_hot_reload.extract_entities(parse_result['data'])

                # Enrich log with parsed data and entities
                log['parsed_data'] = parse_result['data']
                log['entities'] = entities
                log['parser_used'] = parse_result['parser_used']

        except Exception as e:
            self.logger.error(f"Parser processing error: {e}")

        return log

    async def _route_matched_log(self, log: Dict[str, Any], pattern_match: Dict[str, Any]):
        """Route a log that matched a known pattern"""
        pattern_type = pattern_match.get('pattern_type', 'unknown')

        # Prepare enriched log with pattern match info
        enriched_log = {
            'log': log,
            'pattern_id': pattern_match['pattern_id'],
            'pattern_type': pattern_type,
            'entities': pattern_match['entities'],  # Entity hints from pattern
            'matched_at': datetime.utcnow().isoformat(),
            'processing_type': 'pattern_match'
        }

        # Route to appropriate engines based on pattern type
        await self._route_by_use_case(enriched_log, pattern_match)

    async def _route_unknown_log(self, log: Dict[str, Any]):
        """Route a log with unknown pattern for AI analysis"""
        # Buffer unknown patterns to avoid overwhelming AI
        self.publisher('intelligence.unknown_pattern', {
            'log': log,
            'priority': 'low',  # Don't rush, learn properly
            'source': log.get('source_id', 'unknown')
        })

        # Still store raw for audit
        self.publisher('backend.store_raw_log', log)

    async def _route_by_use_case(self, enriched_log: Dict[str, Any], pattern_match: Dict[str, Any]):
        """Route logs to engines based on use case type"""
        pattern_type = pattern_match.get('pattern_type', 'unknown')

        # Get routing for this pattern type
        routes = self.use_case_routes.get(pattern_type, self.use_case_routes['unknown'])

        # Apply routing
        for route in routes:
            try:
                await self._send_to_engine(route, enriched_log, pattern_match, pattern_type)
            except Exception as e:
                self.logger.error(f"Failed to route to {route}: {e}")

    async def _send_to_engine(self, engine: str, enriched_log: Dict[str, Any],
                             pattern_match: Dict[str, Any], pattern_type: str):
        """Send log to a specific engine"""
        if engine == 'backend':
            # Always store processed logs
            self.publisher('backend.store_processed_log', enriched_log)

        elif engine == 'contextualization':
            # Send log to contextualization for entity extraction and enrichment
            self.publisher('contextualization.process_log', {
                'log': enriched_log['log'],
                'pattern_id': pattern_match.get('pattern_id'),
                'pattern_type': pattern_type,
                'entity_hints': pattern_match.get('entities', []),  # Pattern's entity suggestions
                'log_id': enriched_log['log'].get('log_id'),
                'source_type': enriched_log['log'].get('source_type')
            })

        elif engine == 'intelligence':
            # Only send security-relevant patterns to expensive AI
            security_patterns = ['authentication', 'failed_authentication', 'malware', 'threat_detection']
            if pattern_type in security_patterns:
                self.publisher('intelligence.security_analysis', {
                    'log': enriched_log['log'],
                    'pattern': pattern_match,
                    'priority': 'high' if pattern_type in ['malware', 'threat_detection'] else 'medium'
                })

        elif engine == 'delivery':
            # Create cases for important events
            critical_patterns = ['failed_authentication', 'privilege_escalation', 'malware',
                               'threat_detection', 'compliance_violation']
            if pattern_type in critical_patterns:
                self.publisher('delivery.create_case', {
                    'pattern_type': pattern_type,
                    'severity': self._get_severity(pattern_type),
                    'entities': pattern_match.get('entities', []),
                    'log_id': enriched_log['log'].get('log_id')
                })

    def _get_severity(self, pattern_type: str) -> str:
        """Map pattern types to severity levels"""
        severity_map = {
            'malware': 'critical',
            'privilege_escalation': 'high',
            'threat_detection': 'high',
            'failed_authentication': 'medium',
            'compliance_violation': 'medium',
            'network_anomaly': 'low',
            'system': 'info'
        }
        return severity_map.get(pattern_type, 'low')

    def add_routing_rule(self, pattern_type: str, engines: List[str]):
        """Add or update a routing rule"""
        self.use_case_routes[pattern_type] = engines
        self.logger.info(f"Added routing rule for {pattern_type}: {engines}")

    def remove_routing_rule(self, pattern_type: str):
        """Remove a routing rule"""
        if pattern_type in self.use_case_routes:
            del self.use_case_routes[pattern_type]
            self.logger.info(f"Removed routing rule for {pattern_type}")

    def get_routing_rules(self) -> Dict[str, List[str]]:
        """Get all current routing rules"""
        return self.use_case_routes.copy()

    def get_pattern_route(self, pattern_type: str) -> List[str]:
        """Get the routing for a specific pattern type"""
        return self.use_case_routes.get(pattern_type, self.use_case_routes['unknown'])


class DatabaseLogRouter:
    """Specialized router for processing logs already in the database"""

    def __init__(self, db_connection, log_router: LogRouter, logger: logging.Logger):
        self.db_connection = db_connection
        self.log_router = log_router
        self.logger = logger

    async def process_database_logs(self, limit: int = 100) -> Dict[str, int]:
        """Process unprocessed logs from the database"""
        stats = {'processed': 0, 'errors': 0}

        try:
            # Fetch unprocessed logs
            cursor = self.db_connection.cursor()
            cursor.execute("""
                SELECT log_id, source_type, log_data, created_at
                FROM ingestion_logs
                WHERE processed = false
                LIMIT %s
            """, (limit,))

            db_logs = cursor.fetchall()

            if not db_logs:
                return stats

            # Convert database logs to standard format
            logs = []
            log_ids = []

            for log_row in db_logs:
                log_id, source_type, log_data, created_at = log_row
                log = {
                    'source_id': 'database',
                    'source_type': source_type or 'database',
                    'timestamp': created_at.isoformat() if created_at else datetime.utcnow().isoformat(),
                    'data': log_data or {},
                    'log_id': str(log_id) if log_id else f"db_{int(time.time())}"
                }
                logs.append(log)
                log_ids.append(log_id)

            # Process logs through router
            routing_stats = await self.log_router.process_log_batch(logs)

            # Mark logs as processed
            if log_ids:
                cursor.execute("""
                    UPDATE ingestion_logs
                    SET processed = true, processed_at = %s
                    WHERE log_id = ANY(%s)
                """, (datetime.utcnow(), log_ids))

            stats['processed'] = len(logs)
            self.logger.info(f"Processed {len(logs)} database logs")

            cursor.close()

        except Exception as e:
            self.logger.error(f"Error processing database logs: {e}")
            stats['errors'] += 1

        return stats