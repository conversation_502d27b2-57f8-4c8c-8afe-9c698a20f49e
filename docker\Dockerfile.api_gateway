# API Gateway Dockerfile
FROM siemless-v2-base:latest

# Copy API Gateway specific code
COPY engines/api_gateway/ /app/engines/api_gateway/

# Expose ports
EXPOSE 8000 8001

# Health check specific to API Gateway
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Set the command to run API Gateway
CMD ["python", "engines/api_gateway/api_gateway.py"]