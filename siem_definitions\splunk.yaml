# Splunk Enterprise SIEM Configuration
# Query Language: SPL (Search Processing Language)

platform:
  name: splunk
  display_name: Splunk Enterprise
  query_language: spl
  description: Splunk's Search Processing Language for log analysis
  vendor: Splunk Inc.
  version: "1.0"
  active: true

# Field mappings: generic_field -> Splunk field
field_mappings:
  source_ip: src_ip
  destination_ip: dest_ip
  username: user
  process_name: process_name
  file_hash: file_hash
  event_id: EventCode
  hostname: ComputerName
  port: dest_port
  source_port: src_port
  destination_port: dest_port
  domain: domain
  url: url
  file_name: file_name
  file_path: file_path
  registry_path: registry_path
  command_line: CommandLine
  parent_process: parent_process_name
  network_protocol: protocol
  http_method: http_method
  user_agent: http_user_agent
  email_sender: sender
  email_recipient: recipient
  dns_query: query
  service_name: service_name
  account_name: Account_Name

# Operator mappings: generic_operator -> Splunk operator
operator_mappings:
  equals: "="
  not_equals: "!="
  contains: LIKE
  not_contains: NOT LIKE
  regex: rex
  greater_than: ">"
  less_than: "<"
  greater_equal: ">="
  less_equal: "<="
  in_list: IN
  not_in_list: NOT IN
  starts_with: "^"
  ends_with: "$"

# Time field for temporal queries
time_field: _time

# Query syntax specifics
syntax:
  comment: "#"
  string_quote: "\""
  escape_char: "\\"
  wildcard: "*"
  field_separator: "="
  logical_and: AND
  logical_or: OR
  logical_not: NOT
  pipe_operator: "|"
  search_command: search
  stats_command: stats
  eval_command: eval
  where_command: where
  regex_delimiter: "/"
  case_sensitive: false

# Platform-specific features
metadata:
  supports_regex: true
  supports_wildcards: true
  supports_subsearches: true
  supports_lookups: true
  supports_macros: true
  supports_calculated_fields: true
  max_search_results: 50000
  default_time_range: "24h"
  index_prefix: "index="
  sourcetype_prefix: "sourcetype="

  # Common indexes
  common_indexes:
    - main
    - security
    - windows
    - linux
    - firewall
    - proxy
    - wineventlog

  # Common sourcetypes
  common_sourcetypes:
    - WinEventLog:Security
    - WinEventLog:System
    - linux_secure
    - firewall
    - proxy
    - json

  # Detection rule template
  rule_template: |
    index={index} sourcetype={sourcetype}
    {conditions}
    | stats count by {group_fields}
    | where count > {threshold}
