# SIEMLess v2.0 - Plugin Architecture Audit Report
**Date**: October 2, 2025
**Purpose**: Systematic audit of tool-specific vs universal plugin implementations

## Executive Summary

**Status**: Mixed architecture - some plugins implemented correctly, some tool-specific code exists
**Goal**: Convert all tool-specific implementations to universal plugin architecture
**Impact**: Infinite vendor scalability through AI-powered plugin generation

---

## Audit Findings

### ✅ CORRECT: Universal Plugin Architecture (Already Implemented)

#### 1. Investigation Context Plugins
**Location**: `engines/ingestion/`
**Status**: ✅ **CORRECT** - Following universal plugin pattern

| File | Type | Status | Notes |
|------|------|--------|-------|
| `context_source_plugin.py` | Base Class | ✅ Perfect | Universal interface |
| `crowdstrike_context_plugin.py` | Plugin | ✅ Perfect | Investigation context |
| `elastic_context_plugin.py` | Plugin | ✅ Perfect | Investigation context |
| `plugin_generator.py` | AI Generator | ✅ Perfect | Auto-generates plugins |

**Why Correct**:
- Inherit from universal `ContextSourcePlugin` base
- Registered with `ContextManager`
- Core engine remains vendor-agnostic
- Easy to add new vendors (just add new plugin file)

---

### ⚠️ MIXED: CTI Integration (Partially Plugin-Based)

#### 2. CTI Feed Integrations
**Location**: `engines/ingestion/` + `engines/backend/`
**Status**: ⚠️ **NEEDS CONVERSION** - Direct integration, should be plugins

| File | Type | Current Status | Recommended Action |
|------|------|----------------|-------------------|
| `otx_integration.py` (ingestion) | Direct Integration | ⚠️ Tool-specific | Convert to CTI Plugin |
| `otx_integration.py` (backend) | Direct Integration | ⚠️ Tool-specific | Convert to CTI Plugin |
| `threatfox_integration.py` | Direct Integration | ⚠️ Tool-specific | Convert to CTI Plugin |
| `crowdstrike_cti_integration.py` | Direct Integration | ⚠️ Tool-specific | Convert to CTI Plugin |

**Why Needs Conversion**:
- Each CTI source has separate integration file
- No universal `CTISourcePlugin` base class
- Adding new CTI source requires modifying core engine
- Can't easily swap CTI providers per environment

**Recommended Architecture**:
```python
# Create base CTI plugin
class CTISourcePlugin:
    def get_source_name(self) -> str
    def fetch_indicators(self) -> List[IOC]
    def validate_credentials(self) -> bool

# Convert existing integrations to plugins
class OTXPlugin(CTISourcePlugin): pass
class ThreatFoxPlugin(CTISourcePlugin): pass
class CrowdStrikeCTIPlugin(CTISourcePlugin): pass
```

---

### ❌ WRONG: Elastic Rule Harvesting (Tool-Specific)

#### 3. Elastic-Specific Code
**Location**: `engines/ingestion/`
**Status**: ❌ **TOOL-SPECIFIC** - Needs plugin conversion

| File | Type | Issue | Recommended Fix |
|------|------|-------|-----------------|
| `elastic_harvester_final.py` | Direct Integration | Elastic-specific harvesting logic | `RuleHarvesterPlugin` base class |
| `elastic_harvester_integration.py` | Direct Integration | Hardcoded Elastic API calls | Convert to plugin |
| `elastic_harvester_integration_v2.py` | Direct Integration | Version-specific code | Unified plugin approach |
| `elastic_rule_harvester.py` | Direct Integration | Elasticsearch dependency | Abstract to plugin |

**Why This Is Wrong**:
- Can't harvest rules from other SIEM platforms (Splunk, Sentinel, QRadar)
- Elastic library (`elasticsearch`) hardcoded in core engine
- Adding new rule source requires rewriting core logic

**Recommended Architecture**:
```python
class RuleHarvesterPlugin:
    """Universal rule harvesting interface"""
    def get_source_name(self) -> str
    def connect(self, credentials: Dict) -> bool
    def list_rules(self) -> List[Rule]
    def get_rule_details(self, rule_id: str) -> RuleDetails
    def convert_to_sigma(self, rule: Dict) -> SigmaRule

# Plugins for each SIEM
class ElasticRuleHarvester(RuleHarvesterPlugin): pass
class SplunkRuleHarvester(RuleHarvesterPlugin): pass  # Future
class SentinelRuleHarvester(RuleHarvesterPlugin): pass  # Future
```

---

### ⚠️ SCOPE CONFIG (Environment-Specific, Acceptable)

#### 4. Vendor Scope Configurations
**Location**: `engines/ingestion/`
**Status**: ⚠️ **ACCEPTABLE** but could be improved

| File | Purpose | Status | Notes |
|------|---------|--------|-------|
| `crowdstrike_scope_config.py` | API scope definitions | ⚠️ OK | Config file, not logic |

**Why Acceptable**:
- Configuration, not business logic
- Doesn't prevent plugin architecture
- Could be moved to config files later

---

## Architecture Assessment

### Current State

```
✅ Investigation Context: Plugin-Based
    - CrowdStrike Plugin
    - Elastic Plugin
    - Universal ContextManager

⚠️ CTI Integration: Direct Integration
    - OTX Integration (2 copies)
    - ThreatFox Integration
    - CrowdStrike CTI Integration

❌ Rule Harvesting: Tool-Specific
    - Elastic Harvester (4 versions)
    - No other SIEMs supported

⚠️ Entity Extraction: Partially Universal
    - Adaptive Entity Extractor (learns any vendor) ✅
    - Some hardcoded patterns remain ⚠️
```

### Desired State

```
✅ ALL Vendor Integrations: Plugin-Based

Universal Engines:
- Ingestion Engine (vendor-agnostic)
- Contextualization Engine (format-agnostic)
- Backend Engine (storage-universal)

Plugin Categories:
1. Investigation Context Plugins ✅
2. CTI Source Plugins (to create)
3. Rule Harvester Plugins (to create)
4. Entity Extraction Plugins ✅ (AI-powered)
5. Enrichment Plugins (future)
```

---

## Recommended Refactoring Priority

### Phase 1: Create CTI Plugin System (HIGH PRIORITY)
**Effort**: 2-3 hours
**Impact**: Enables any CTI source (MISP, ThreatStream, Recorded Future, etc.)

**Tasks**:
1. Create `cti_source_plugin.py` base class
2. Convert `otx_integration.py` → `otx_plugin.py`
3. Convert `threatfox_integration.py` → `threatfox_plugin.py`
4. Convert `crowdstrike_cti_integration.py` → `crowdstrike_cti_plugin.py`
5. Update ingestion engine to use `CTIPluginManager`

**Benefits**:
- Add new CTI sources without modifying core
- Enable/disable CTI sources via configuration
- Different CTI sources per environment

---

### Phase 2: Create Rule Harvester Plugin System (MEDIUM PRIORITY)
**Effort**: 4-5 hours
**Impact**: Harvest detection rules from ANY SIEM platform

**Tasks**:
1. Create `rule_harvester_plugin.py` base class
2. Consolidate 4 Elastic harvesters → `elastic_rule_harvester_plugin.py`
3. Create plugin framework for Splunk, Sentinel, QRadar
4. Update ingestion engine to use `RuleHarvesterManager`

**Benefits**:
- Harvest from multiple SIEMs simultaneously
- Convert any SIEM rules to Sigma format
- Community can contribute SIEM-specific plugins

---

### Phase 3: Enhance Entity Extraction Plugins (LOW PRIORITY)
**Effort**: 2-3 hours
**Impact**: Further standardize entity extraction

**Tasks**:
1. Review `adaptive_entity_extractor.py` for hardcoded patterns
2. Convert remaining hardcoded patterns to learned patterns
3. Ensure 100% vendor-agnostic extraction

**Benefits**:
- Already mostly universal via AI learning
- Just cleanup remaining hardcoded logic

---

## Plugin Template Library

### 1. CTI Source Plugin Template

```python
from typing import List, Dict, Any
from datetime import datetime

class CTISourcePlugin:
    """Universal CTI source interface"""

    def __init__(self, config: Dict[str, Any]):
        self.api_key = config.get('api_key')
        self.enabled = config.get('enabled', True)

    def get_source_name(self) -> str:
        """Return CTI source name (e.g., 'otx', 'threatfox')"""
        raise NotImplementedError

    async def validate_credentials(self) -> bool:
        """Test API connectivity"""
        raise NotImplementedError

    async def fetch_indicators(self, since: datetime) -> List[Dict]:
        """
        Fetch IOCs since timestamp

        Returns standardized format:
        {
            'indicator_type': 'ip' | 'domain' | 'hash' | 'url',
            'indicator_value': '*******',
            'threat_type': 'malware' | 'phishing' | 'c2',
            'confidence': 0.0-1.0,
            'first_seen': datetime,
            'last_seen': datetime,
            'tags': ['apt28', 'ransomware'],
            'source_reference': 'pulse_id' | 'report_id'
        }
        """
        raise NotImplementedError

    async def get_context(self, indicator: str) -> Dict:
        """Get additional context for an indicator"""
        raise NotImplementedError
```

### 2. Rule Harvester Plugin Template

```python
from typing import List, Dict, Any

class RuleHarvesterPlugin:
    """Universal SIEM rule harvesting interface"""

    def __init__(self, config: Dict[str, Any]):
        self.credentials = config
        self.client = None

    def get_source_name(self) -> str:
        """Return SIEM name (e.g., 'elastic', 'splunk')"""
        raise NotImplementedError

    async def connect(self) -> bool:
        """Establish connection to SIEM"""
        raise NotImplementedError

    async def list_rules(self, filters: Dict = None) -> List[Dict]:
        """
        List all detection rules

        Returns standardized format:
        {
            'rule_id': 'abc123',
            'name': 'Suspicious PowerShell Activity',
            'description': 'Detects...',
            'severity': 'high',
            'enabled': True,
            'tags': ['execution', 'powershell'],
            'mitre_techniques': ['T1059.001']
        }
        """
        raise NotImplementedError

    async def get_rule_details(self, rule_id: str) -> Dict:
        """Get full rule definition"""
        raise NotImplementedError

    async def convert_to_sigma(self, rule: Dict) -> Dict:
        """Convert vendor rule to Sigma format"""
        raise NotImplementedError
```

---

## Benefits of Full Plugin Architecture

### 1. Infinite Vendor Support
- Add new vendors = create new plugin file
- No core engine modifications needed
- Community can contribute plugins

### 2. Environment Flexibility
```yaml
# Environment A
plugins:
  - crowdstrike_context
  - elastic_rules
  - otx_cti

# Environment B
plugins:
  - sentinelone_context
  - splunk_rules
  - threatfox_cti
```

### 3. AI-Powered Plugin Generation
```
Human: "Add support for Splunk"
AI: Analyzes elastic_rule_harvester_plugin.py
AI: Generates splunk_rule_harvester_plugin.py
AI: Tests with sample Splunk API calls
Result: New plugin ready in 5 minutes
```

### 4. Testing & Isolation
- Test individual plugins without risking core
- Mock plugins for unit tests
- Swap plugins based on environment

### 5. Maintenance
- Update vendor API = update one plugin file
- Core engines remain stable and tested
- Plugin bugs don't affect core

---

## Success Metrics

**Before Refactoring**:
- ❌ 11 tool-specific integration files
- ❌ Adding new vendor requires core changes
- ❌ Can't test vendors independently
- ❌ Environment-specific deployments difficult

**After Refactoring**:
- ✅ 3 universal plugin systems (Context, CTI, Rules)
- ✅ Adding new vendor = create plugin file only
- ✅ Each plugin testable independently
- ✅ Same core, different plugin configs per environment
- ✅ AI can generate new plugins from templates

---

## Next Steps

1. **Review this audit** with team
2. **Prioritize Phase 1** (CTI Plugin System)
3. **Create plugin base classes** first
4. **Convert existing integrations** one by one
5. **Test each plugin** independently
6. **Document plugin development** guide
7. **Enable AI plugin generation** workflow

---

## Conclusion

**Current State**: 20% plugin-based (Investigation Context only)
**Target State**: 100% plugin-based (all vendor integrations)
**Estimated Effort**: 8-10 hours total
**ROI**: Infinite vendor scalability + AI-powered expansion

**The universal plugin architecture is THE foundation for making SIEMLess truly vendor-agnostic and infinitely extensible.**
