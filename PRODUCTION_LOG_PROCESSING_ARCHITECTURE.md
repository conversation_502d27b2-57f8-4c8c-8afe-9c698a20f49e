# Production Log Processing Architecture
**Complete System for Schema Detection → AI Mapping → Deterministic Extraction**

## Overview

This is the **production-ready** architecture for processing logs with:
1. **Log Schema Detection** - Exact header matching for known schemas
2. **Log Source Tracking** - Registry of all log sources being processed
3. **AI Mapper Generation** - One-time AI analysis to create mappings
4. **Deterministic Extraction** - Fast, free extraction using stored mappings

---

## Architecture Components

### Component 1: Log Schema Detector

**Purpose**: Detect if a log schema is already known (exact header match)

**Database Table**: `log_schemas`
```sql
CREATE TABLE log_schemas (
    schema_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    schema_hash VARCHAR(64) UNIQUE NOT NULL,  -- SHA-256 of sorted field names
    schema_name VARCHAR(255),                  -- e.g., "elasticsearch_fortinet_firewall"
    field_structure JSONB NOT NULL,            -- Hierarchical field structure
    sample_log JSONB NOT NULL,                 -- One representative log
    vendor VARCHAR(100),                       -- Detected vendor
    log_type VARCHAR(100),                     -- firewall, edr, auth, etc.

    -- Mapping information
    entity_mapping JSONB NOT NULL,             -- Field paths for entity extraction
    mapping_generated_by VARCHAR(50),          -- 'ai' or 'manual'
    mapping_confidence DECIMAL(3,2),           -- 0.0-1.0

    -- Usage tracking
    log_count INTEGER DEFAULT 0,               -- How many logs match this schema
    first_seen TIMESTAMP DEFAULT NOW(),
    last_seen TIMESTAMP DEFAULT NOW(),

    -- Quality metadata
    extraction_success_rate DECIMAL(5,2),      -- % of logs successfully extracted
    avg_entities_per_log DECIMAL(5,2),         -- Average entities extracted

    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_log_schemas_hash ON log_schemas(schema_hash);
CREATE INDEX idx_log_schemas_vendor ON log_schemas(vendor);
CREATE INDEX idx_log_schemas_count ON log_schemas(log_count DESC);
```

**Schema Hash Generation**:
```python
def generate_schema_hash(log: Dict) -> str:
    """
    Generate deterministic hash from log field structure

    - Extracts all field paths (e.g., 'content.log.data[].data.source.ip')
    - Sorts alphabetically
    - Hashes with SHA-256
    - Same structure = Same hash = Known schema
    """
    field_paths = extract_all_field_paths(log)
    sorted_paths = sorted(field_paths)
    schema_string = '|'.join(sorted_paths)
    return hashlib.sha256(schema_string.encode()).hexdigest()


def extract_all_field_paths(obj, prefix=''):
    """
    Extract all field paths from nested object

    Examples:
    - {"source": {"ip": "*******"}} → ['source', 'source.ip']
    - {"data": [{"ip": "*******"}]} → ['data[]', 'data[].ip']
    """
    paths = []

    if isinstance(obj, dict):
        for key, value in obj.items():
            current_path = f"{prefix}.{key}" if prefix else key
            paths.append(current_path)
            paths.extend(extract_all_field_paths(value, current_path))

    elif isinstance(obj, list) and len(obj) > 0:
        paths.append(f"{prefix}[]")
        # Analyze first element (assume homogeneous arrays)
        paths.extend(extract_all_field_paths(obj[0], f"{prefix}[]"))

    return paths
```

**Schema Detection Flow**:
```python
async def detect_log_schema(log: Dict) -> Optional[Dict]:
    """
    Check if log matches a known schema

    Returns:
    - schema_id, entity_mapping if known
    - None if new/unknown schema
    """
    # Generate hash
    schema_hash = generate_schema_hash(log)

    # Check cache first (Redis)
    cached = redis.get(f"schema:{schema_hash}")
    if cached:
        return json.loads(cached)

    # Check database
    result = db_fetchrow(db, """
        SELECT schema_id, schema_name, entity_mapping, vendor
        FROM log_schemas
        WHERE schema_hash = %s
    """, schema_hash)

    if result:
        # Update last_seen and log_count
        db_execute(db, """
            UPDATE log_schemas
            SET last_seen = NOW(), log_count = log_count + 1
            WHERE schema_hash = %s
        """, schema_hash)

        # Cache for 1 hour
        redis.setex(f"schema:{schema_hash}", 3600, json.dumps(result))

        return result

    return None  # Unknown schema - needs AI analysis
```

---

### Component 2: Log Source Registry Integration

**Purpose**: Track which log sources are being processed

**Existing Table**: `log_sources` (already in database)

**Enhancement**: Link schemas to sources

```sql
-- Add schema tracking to log sources
ALTER TABLE log_sources
ADD COLUMN IF NOT EXISTS schema_id UUID REFERENCES log_schemas(schema_id);

-- Track schema-to-source relationships (many-to-many)
CREATE TABLE IF NOT EXISTS log_source_schemas (
    id SERIAL PRIMARY KEY,
    source_id VARCHAR(255) REFERENCES log_sources(source_id),
    schema_id UUID REFERENCES log_schemas(schema_id),
    first_detected TIMESTAMP DEFAULT NOW(),
    log_count INTEGER DEFAULT 0,
    UNIQUE(source_id, schema_id)
);
```

**Log Source Detection**:
```python
def detect_log_source(log: Dict) -> str:
    """
    Identify log source using existing LogSourceIdentifier

    Returns: source_id (e.g., 'fortinet-fw-001', 'crowdstrike-001')
    """
    identifier = LogSourceIdentifier(db_connection)

    # Try pattern matching first
    source_info = identifier.identify_source(log)

    if source_info:
        return source_info['source_id']

    # Fallback: Detect from nested observer fields (Elasticsearch)
    vendor = log.get('content', {}).get('log', {}).get('data', [{}])[0].get('data', {}).get('observer', {}).get('vendor')

    if vendor:
        return f"{vendor.lower()}-001"  # Generate ID

    return 'unknown'
```

---

### Component 3: AI Mapper Generation

**Purpose**: Generate entity mappings for NEW schemas using AI consensus

**When Triggered**: Schema detector returns None (unknown schema)

**AI Consensus Models**: Gemma-3-27B (FREE) + Claude Sonnet-4 ($0.008)

**Process**:

```python
async def generate_mapping_with_ai(log: Dict, schema_hash: str) -> Dict:
    """
    Use AI consensus to generate entity mapping for new schema

    Cost: $0.008 per schema (one-time)
    Time: ~50-70s
    """
    # Step 1: Extract sample for AI analysis
    sample_log = log  # First log of new schema

    # Step 2: Send to Intelligence Engine
    request_id = str(uuid4())

    redis.publish('intelligence.generate_log_mapping', json.dumps({
        'request_id': request_id,
        'schema_hash': schema_hash,
        'sample_log': sample_log,
        'models': ['free', 'mid_quality'],  # Gemma + Sonnet
        'response_channel': f'contextualization.mapping_generated.{request_id}'
    }))

    # Step 3: Wait for AI consensus response
    mapping_result = await wait_for_response(
        f'contextualization.mapping_generated.{request_id}',
        timeout=120
    )

    # Step 4: Store mapping in database
    schema_id = db_fetchval(db, """
        INSERT INTO log_schemas (
            schema_hash, schema_name, field_structure, sample_log,
            vendor, log_type, entity_mapping, mapping_generated_by,
            mapping_confidence
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, 'ai', %s)
        RETURNING schema_id
    """,
        schema_hash,
        mapping_result['schema_name'],
        json.dumps(extract_all_field_paths(sample_log)),
        json.dumps(sample_log),
        mapping_result['vendor'],
        mapping_result['log_type'],
        json.dumps(mapping_result['entity_mapping']),
        mapping_result['confidence']
    )

    # Step 5: Cache immediately
    redis.setex(f"schema:{schema_hash}", 3600, json.dumps({
        'schema_id': schema_id,
        'entity_mapping': mapping_result['entity_mapping'],
        'vendor': mapping_result['vendor']
    }))

    logger.info(f"Generated mapping for new schema {schema_hash}: {mapping_result['schema_name']}")

    return mapping_result
```

**AI Prompt for Mapping Generation**:

```python
MAPPING_GENERATION_PROMPT = """
You are a log analysis expert. Analyze this log structure and generate entity extraction mappings.

Sample Log:
```json
{sample_log}
```

Your task:
1. Identify the vendor (CrowdStrike, Fortinet, Palo Alto, etc.)
2. Identify the log type (firewall, edr, authentication, etc.)
3. Map security entities to JSON paths

Respond in JSON format:
{{
  "schema_name": "elasticsearch_fortinet_firewall",
  "vendor": "Fortinet",
  "log_type": "firewall",
  "confidence": 0.95,
  "entity_mapping": {{
    "source_ip": "content.log.data[0].data.source.ip",
    "destination_ip": "content.log.data[0].data.destination.ip",
    "source_port": "content.log.data[0].data.source.port",
    "destination_port": "content.log.data[0].data.destination.port",
    "action": "content.log.data[0].data.event.action",
    "firewall_name": "content.log.data[0].data.observer.name",
    "protocol": "content.log.data[0].data.network.protocol",
    "application": "content.log.data[0].data.network.application"
  }}
}}

IMPORTANT:
- Use exact JSON paths with array notation [0] where needed
- Include ALL security-relevant entities you can find
- Be precise with nested field paths
- Confidence should reflect certainty (0.0-1.0)
"""
```

---

### Component 4: Deterministic Entity Extraction

**Purpose**: Extract entities using stored mappings (NO AI, FREE, FAST)

**Process**:

```python
def extract_entities_with_mapping(log: Dict, entity_mapping: Dict) -> List[Dict]:
    """
    Extract entities using stored mapping (deterministic, FREE, fast)

    Cost: $0
    Time: Milliseconds
    """
    entities = []

    for entity_type, json_path in entity_mapping.items():
        # Extract value using JSON path
        value = extract_value_by_path(log, json_path)

        if value:
            entities.append({
                'type': entity_type,
                'value': value,
                'extracted_from': json_path
            })

    return entities


def extract_value_by_path(obj: Dict, path: str) -> Optional[Any]:
    """
    Extract value from nested object using JSON path

    Examples:
    - "source.ip" → obj['source']['ip']
    - "data[0].ip" → obj['data'][0]['ip']
    - "content.log.data[0].data.source.ip" → nested extraction
    """
    keys = path.replace('[', '.').replace(']', '').split('.')
    current = obj

    for key in keys:
        if key.isdigit():
            # Array index
            try:
                current = current[int(key)]
            except (IndexError, TypeError, KeyError):
                return None
        else:
            # Dictionary key
            try:
                current = current[key] if isinstance(current, dict) else None
            except (KeyError, TypeError):
                return None

        if current is None:
            return None

    return current
```

---

## Complete Production Flow

```
┌─────────────────────────────────────────────────────────────────┐
│              NEW LOG ARRIVES (from warm_storage)                │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│ STEP 1: GENERATE SCHEMA HASH                                    │
│ - Extract all field paths from log                             │
│ - Sort alphabetically                                           │
│ - SHA-256 hash                                                  │
│ Result: "a3f2e9..." (64-char hash)                             │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│ STEP 2: CHECK IF KNOWN SCHEMA                                   │
│ - Query Redis cache: schema:a3f2e9...                          │
│ - If miss, query database: log_schemas table                   │
└────────────────────────┬────────────────────────────────────────┘
                         │
         ┌───────────────┴───────────────┐
         │                               │
    KNOWN SCHEMA                    UNKNOWN SCHEMA
         │                               │
         ▼                               ▼
┌──────────────────────┐      ┌──────────────────────────────────┐
│ STEP 3A: LOAD MAPPING│      │ STEP 3B: GENERATE MAPPING WITH AI│
│ - Get entity_mapping │      │ - Send to Intelligence Engine    │
│   from cache/db      │      │ - AI Consensus (Gemma + Sonnet)  │
│ - Cost: $0           │      │ - Store in log_schemas table     │
│ - Time: <1ms         │      │ - Cache in Redis                 │
│                      │      │ - Cost: $0.008 (one-time!)       │
└──────────┬───────────┘      │ - Time: 50-70s (one-time!)       │
           │                  └──────────┬───────────────────────┘
           │                             │
           └─────────────┬───────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│ STEP 4: DETERMINISTIC ENTITY EXTRACTION                         │
│ - Use stored mapping (JSON paths)                              │
│ - Extract entities: source_ip, dest_ip, hostname, etc.         │
│ - Cost: $0                                                      │
│ - Time: Milliseconds                                            │
└────────────────────────┬────────────────────────────────────────┘
                         │
                         ▼
┌─────────────────────────────────────────────────────────────────┐
│ STEP 5: STORE ENTITIES                                          │
│ - Insert into entities table                                    │
│ - Create relationships                                          │
│ - Update log_schemas.log_count                                 │
│ - Update log_source_schemas linkage                            │
└─────────────────────────────────────────────────────────────────┘
```

---

## Cost Analysis for 56,119 Logs

### Scenario 1: All Unknown Schemas (Worst Case)

If every log is a unique schema (impossible but theoretical):
- **AI calls**: 56,119 × $0.008 = **$449**

### Scenario 2: Realistic (3 Vendors in Your Data)

Based on your warm_storage:
- **Fortinet**: 4,490 logs
- **Unknown/Other**: 51,629 logs (likely 2-3 more vendors)

Estimated unique schemas: **3-5**

**AI calls**: 5 × $0.008 = **$0.04** (4 cents!)
**Deterministic extraction**: 56,114 × $0 = **$0**
**Total**: **$0.04**

### Scenario 3: Ongoing Production

- **Month 1**: 10 unique schemas discovered = $0.08
- **Month 2**: 2 new schemas = $0.016 (most logs match existing)
- **Month 3**: 1 new schema = $0.008
- **Steady state**: ~$0.10/month for new schema discovery

---

## Performance Metrics

### Schema Detection
- **Cache hit**: <1ms
- **Database hit**: 5-10ms
- **Unknown (cache miss)**: 5-10ms + AI generation

### AI Mapping Generation (One-Time)
- **Gemma complexity assessment**: 5-10s (FREE)
- **Gemma mapping attempt**: 27s (FREE)
- **Sonnet validation**: 46s ($0.008)
- **Total**: ~70s, $0.008

### Deterministic Extraction
- **Per log**: <1ms
- **1,000 logs**: <1s
- **56,119 logs**: ~56s

### End-to-End (First Time)
- Log 1 (Fortinet): 70s (AI generation) + 1ms (extraction) = 70s
- Log 2-4,490 (Fortinet): 1ms each (cached mapping) = 4.5s
- **Total for all Fortinet**: 74.5s

---

## Implementation Files

### File 1: `engines/contextualization/log_schema_detector.py`
- `generate_schema_hash()`
- `detect_log_schema()`
- `extract_all_field_paths()`

### File 2: `engines/contextualization/deterministic_extractor.py`
- `extract_entities_with_mapping()`
- `extract_value_by_path()`

### File 3: `engines/intelligence/mapping_generator.py`
- `generate_mapping_with_ai()`
- AI prompt templates
- Consensus validation

### File 4: Database Migration
- `database/log_schemas_table.sql`
- `database/log_source_schemas_table.sql`

---

## Success Criteria

✅ **Schema Detection**: >99% cache hit rate after first pass
✅ **Cost**: <$0.10 for 56,119 logs
✅ **Speed**: <2 minutes for all logs (after mappings cached)
✅ **Accuracy**: >95% entity extraction success rate
✅ **Scalability**: Handles new vendors automatically
✅ **Monitoring**: Full tracking of schemas and sources

---

## Next Steps

1. **Create database tables** (`log_schemas`, `log_source_schemas`)
2. **Implement schema detector** (hash generation + lookup)
3. **Implement deterministic extractor** (JSON path extraction)
4. **Integrate with Intelligence Engine** (AI mapping generation)
5. **Test with 10 sample logs** (verify mappings)
6. **Run on all 56,119 logs** (production test)
7. **Monitor and optimize** (track success rates)

---

**This is the production system.** It learns from AI once, then operates for free forever. Pattern crystallization at its finest.
