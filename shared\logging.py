"""
Universal Logging Layer for SIEMLess v2.0
Every decision, pattern, and interaction is logged for training the next generation
"""
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
import os
from pathlib import Path


class UniversalLogger:
    """
    Comprehensive logging system that captures everything for training
    """

    def __init__(self, engine_name: str = "system"):
        self.engine_name = engine_name
        self.session_id = self._generate_session_id()

        # Create log directory if it doesn't exist
        self.log_dir = Path("v2/logs")
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # Set up file handlers for different log types
        self._setup_handlers()

        # Track performance metrics
        self.operation_times = {}

    def _generate_session_id(self) -> str:
        """Generate unique session ID for tracking related logs"""
        return f"{self.engine_name}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{os.getpid()}"

    def _setup_handlers(self):
        """Set up different log handlers for different purposes"""
        # Main logger
        self.logger = logging.getLogger(f"siemless.v2.{self.engine_name}")
        self.logger.setLevel(logging.DEBUG)

        # Remove existing handlers to avoid duplicates
        self.logger.handlers.clear()

        # Console handler for development
        console = logging.StreamHandler()
        console.setLevel(logging.INFO)
        console.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        self.logger.addHandler(console)

        # File handler for all logs
        all_logs = logging.FileHandler(self.log_dir / f"{self.engine_name}_all.log")
        all_logs.setLevel(logging.DEBUG)
        all_logs.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        self.logger.addHandler(all_logs)

        # Training data handler (JSON format for ML)
        self.training_file = open(self.log_dir / f"{self.engine_name}_training.jsonl", 'a')

    def log(self, event_type: str, data: Any, level: str = "INFO") -> Dict:
        """
        Main logging method - logs everything with full context

        Args:
            event_type: Type of event (decision, performance, error, training)
            data: The actual data to log
            level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

        Returns:
            The complete log entry that was created
        """
        log_entry = self._create_log_entry(event_type, data)

        # Log to standard logger
        log_method = getattr(self.logger, level.lower(), self.logger.info)
        log_method(json.dumps(log_entry, default=str))

        # Log to training file if it's useful for training
        if self._is_training_worthy(event_type):
            self.training_file.write(json.dumps(log_entry, default=str) + '\n')
            self.training_file.flush()

        return log_entry

    def _create_log_entry(self, event_type: str, data: Any) -> Dict:
        """Create comprehensive log entry with all context"""
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'session_id': self.session_id,
            'engine': self.engine_name,
            'event_type': event_type,
            'data': data,
            'context': self._capture_context(),
            'lineage': self._capture_lineage(),
            'performance': self._capture_performance(),
            'training_metadata': self._generate_training_metadata(event_type, data)
        }

    def _capture_context(self) -> Dict:
        """Capture current execution context"""
        return {
            'timestamp_detailed': {
                'utc': datetime.utcnow().isoformat(),
                'unix': time.time()
            },
            'environment': {
                'python_version': os.sys.version,
                'platform': os.sys.platform,
                'cwd': os.getcwd()
            },
            'process': {
                'pid': os.getpid(),
                'memory_usage_mb': self._get_memory_usage()
            }
        }

    def _capture_lineage(self) -> Dict:
        """Capture data lineage information"""
        return {
            'source_engine': self.engine_name,
            'processing_chain': [],  # Will be populated by engines
            'data_transformations': []  # Will track transformations
        }

    def _capture_performance(self) -> Dict:
        """Capture performance metrics"""
        return {
            'operation_times': dict(self.operation_times),
            'memory_usage_mb': self._get_memory_usage(),
            'active_operations': len(self.operation_times)
        }

    def _generate_training_metadata(self, event_type: str, data: Any) -> Dict:
        """Generate metadata for training purposes"""
        return {
            'can_be_used_for_training': self._is_training_worthy(event_type),
            'event_category': self._categorize_event(event_type),
            'data_quality_score': self._assess_data_quality(data),
            'labels': self._generate_training_labels(event_type, data)
        }

    def _is_training_worthy(self, event_type: str) -> bool:
        """Determine if this log entry is useful for training"""
        training_worthy_types = [
            'decision', 'pattern_discovered', 'entity_extracted',
            'relationship_mapped', 'ai_consensus', 'human_validation',
            'crystallization', 'parse_result'
        ]
        return any(t in event_type.lower() for t in training_worthy_types)

    def _categorize_event(self, event_type: str) -> str:
        """Categorize event for training purposes"""
        categories = {
            'decision': 'decision_making',
            'error': 'error_handling',
            'performance': 'optimization',
            'pattern': 'pattern_recognition',
            'entity': 'entity_extraction',
            'relationship': 'graph_operations'
        }

        for key, category in categories.items():
            if key in event_type.lower():
                return category
        return 'general'

    def _assess_data_quality(self, data: Any) -> float:
        """Assess the quality of data for training (0.0 to 1.0)"""
        quality_score = 1.0

        # Reduce score for empty or None data
        if data is None:
            return 0.0
        if isinstance(data, (dict, list, str)) and len(data) == 0:
            return 0.1

        # Higher score for structured data
        if isinstance(data, dict):
            if all(v is not None for v in data.values()):
                quality_score = 0.9
            else:
                quality_score = 0.7

        return quality_score

    def _generate_training_labels(self, event_type: str, data: Any) -> List[str]:
        """Generate labels for supervised training"""
        labels = [event_type, self.engine_name]

        # Add data-specific labels
        if isinstance(data, dict):
            if 'pattern' in data:
                labels.append('pattern_based')
            if 'ai_model' in data:
                labels.append('ai_processed')
            if 'confidence' in data:
                confidence = data.get('confidence', 0)
                if confidence > 0.9:
                    labels.append('high_confidence')
                elif confidence > 0.7:
                    labels.append('medium_confidence')
                else:
                    labels.append('low_confidence')

        return labels

    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        try:
            import psutil
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0

    def start_operation(self, operation_name: str):
        """Start timing an operation"""
        self.operation_times[operation_name] = time.time()
        self.log('performance', {'operation': operation_name, 'status': 'started'}, 'DEBUG')

    def end_operation(self, operation_name: str) -> float:
        """End timing an operation and return duration"""
        if operation_name in self.operation_times:
            duration = time.time() - self.operation_times[operation_name]
            del self.operation_times[operation_name]
            self.log('performance', {
                'operation': operation_name,
                'status': 'completed',
                'duration_seconds': duration
            }, 'DEBUG')
            return duration
        return 0.0

    def log_decision(self, decision_type: str, input_data: Any, output: Any,
                    reasoning: Optional[str] = None, confidence: float = 1.0):
        """Log a decision for training"""
        self.log('decision', {
            'decision_type': decision_type,
            'input': input_data,
            'output': output,
            'reasoning': reasoning,
            'confidence': confidence
        })

    def log_pattern_discovery(self, pattern: str, sample: Any, confidence: float):
        """Log pattern discovery for crystallization"""
        self.log('pattern_discovered', {
            'pattern': pattern,
            'sample': sample,
            'confidence': confidence,
            'timestamp': datetime.utcnow().isoformat()
        })

    def log_error(self, error: Exception, context: Optional[Dict] = None):
        """Log errors with full context"""
        self.log('error', {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context or {},
            'traceback': self._get_traceback()
        }, 'ERROR')

    def _get_traceback(self) -> str:
        """Get current traceback as string"""
        import traceback
        return traceback.format_exc()

    def close(self):
        """Clean up resources"""
        self.training_file.close()


# Global logger instance
_global_logger = None


def get_logger(engine_name: str = "system") -> UniversalLogger:
    """Get or create global logger instance"""
    global _global_logger
    if _global_logger is None:
        _global_logger = UniversalLogger(engine_name)
    return _global_logger


async def log_everything(component: str, action: str, details: Dict[str, Any], level: str = "INFO"):
    """
    Convenience function for async logging from engines

    Args:
        component: Name of the component (engine name)
        action: Action being performed
        details: Additional details about the action
        level: Log level
    """
    logger = get_logger(component)
    return logger.log(action, details, level)