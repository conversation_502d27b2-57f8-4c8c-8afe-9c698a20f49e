"""
Universal CTI Source Plugin Base Class
Standardized interface for all threat intelligence sources
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum
import logging


class IndicatorType(Enum):
    """Standard indicator types"""
    IP = "ip"
    DOMAIN = "domain"
    URL = "url"
    FILE_HASH = "file_hash"
    EMAIL = "email"
    CVE = "cve"
    REGISTRY_KEY = "registry_key"
    MUTEX = "mutex"


class ThreatType(Enum):
    """Standard threat classifications"""
    MALWARE = "malware"
    PHISHING = "phishing"
    C2 = "c2"
    EXPLOIT = "exploit"
    RANSOMWARE = "ransomware"
    APT = "apt"
    BOTNET = "botnet"
    SPAM = "spam"
    UNKNOWN = "unknown"


class CTIIndicator:
    """Standardized CTI indicator format"""

    def __init__(self, data: Dict[str, Any]):
        self.indicator_type = data.get('indicator_type')
        self.indicator_value = data.get('indicator_value')
        self.threat_type = data.get('threat_type', ThreatType.UNKNOWN.value)
        self.confidence = data.get('confidence', 0.5)  # 0.0-1.0
        self.first_seen = data.get('first_seen')
        self.last_seen = data.get('last_seen')
        self.tags = data.get('tags', [])
        self.description = data.get('description', '')
        self.source_reference = data.get('source_reference', '')
        self.mitre_techniques = data.get('mitre_techniques', [])
        self.severity = data.get('severity', 'medium')
        self.raw_data = data.get('raw_data', {})

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'indicator_type': self.indicator_type,
            'indicator_value': self.indicator_value,
            'threat_type': self.threat_type,
            'confidence': self.confidence,
            'first_seen': self.first_seen.isoformat() if isinstance(self.first_seen, datetime) else self.first_seen,
            'last_seen': self.last_seen.isoformat() if isinstance(self.last_seen, datetime) else self.last_seen,
            'tags': self.tags,
            'description': self.description,
            'source_reference': self.source_reference,
            'mitre_techniques': self.mitre_techniques,
            'severity': self.severity
        }


class CTISourcePlugin:
    """
    Universal CTI Source Plugin Base Class

    All CTI integrations (OTX, ThreatFox, MISP, etc.) inherit from this class

    Benefits:
    - Standardized interface across all CTI sources
    - Easy to add new CTI sources (just create new plugin)
    - Enable/disable sources via configuration
    - Test sources independently
    """

    def __init__(self, config: Dict[str, Any], logger: logging.Logger = None):
        """
        Initialize CTI source plugin

        Args:
            config: Plugin configuration (API keys, URLs, etc.)
            logger: Logger instance
        """
        self.config = config
        self.enabled = config.get('enabled', True)
        self.api_key = config.get('api_key')
        self.api_url = config.get('api_url')
        self.logger = logger or logging.getLogger(self.__class__.__name__)

        # Rate limiting
        self.rate_limit = config.get('rate_limit', 60)  # requests per minute
        self.last_request_time = None

        # Caching
        self.cache_enabled = config.get('cache_enabled', True)
        self.cache_ttl = config.get('cache_ttl', 3600)  # seconds

    def get_source_name(self) -> str:
        """
        Return CTI source name

        Examples: 'otx', 'threatfox', 'misp', 'crowdstrike_intel'

        Returns:
            Source name (lowercase, underscore-separated)
        """
        raise NotImplementedError("Subclass must implement get_source_name()")

    def get_source_type(self) -> str:
        """
        Return CTI source type

        Returns:
            'community' | 'commercial' | 'internal'
        """
        return self.config.get('source_type', 'community')

    async def validate_credentials(self) -> bool:
        """
        Validate API credentials

        Returns:
            True if credentials are valid
        """
        raise NotImplementedError("Subclass must implement validate_credentials()")

    async def fetch_indicators(
        self,
        since: Optional[datetime] = None,
        indicator_types: Optional[List[IndicatorType]] = None,
        limit: int = 1000
    ) -> List[CTIIndicator]:
        """
        Fetch threat indicators from CTI source

        Args:
            since: Only fetch indicators updated after this timestamp
            indicator_types: Filter by specific indicator types
            limit: Maximum number of indicators to return

        Returns:
            List of standardized CTI indicators
        """
        raise NotImplementedError("Subclass must implement fetch_indicators()")

    async def get_indicator_context(self, indicator: str) -> Optional[Dict[str, Any]]:
        """
        Get additional context for a specific indicator

        Args:
            indicator: Indicator value (IP, domain, hash, etc.)

        Returns:
            Additional context data or None if not found
        """
        return None

    async def submit_indicator(self, indicator: CTIIndicator) -> bool:
        """
        Submit new indicator to CTI source (if supported)

        Args:
            indicator: Indicator to submit

        Returns:
            True if submission successful
        """
        return False  # Override if source supports submissions

    def get_priority(self) -> int:
        """
        Get source priority (higher = more trusted)

        Returns:
            Priority value (0-100)
        """
        return self.config.get('priority', 50)

    def is_enabled(self) -> bool:
        """Check if source is enabled"""
        return self.enabled and bool(self.api_key)

    async def health_check(self) -> Dict[str, Any]:
        """
        Check source health status

        Returns:
            Health status dictionary
        """
        try:
            is_valid = await self.validate_credentials()
            return {
                'source': self.get_source_name(),
                'healthy': is_valid,
                'enabled': self.is_enabled(),
                'priority': self.get_priority(),
                'type': self.get_source_type()
            }
        except Exception as e:
            return {
                'source': self.get_source_name(),
                'healthy': False,
                'enabled': self.is_enabled(),
                'error': str(e)
            }


class CTIPluginManager:
    """
    Manager for all CTI source plugins

    Handles:
    - Plugin registration and discovery
    - Multi-source indicator aggregation
    - Priority-based source selection
    - Health monitoring
    """

    def __init__(self, logger: logging.Logger = None):
        self.plugins: Dict[str, CTISourcePlugin] = {}
        self.logger = logger or logging.getLogger(__name__)

    def register_plugin(self, plugin: CTISourcePlugin):
        """Register a CTI source plugin"""
        source_name = plugin.get_source_name()
        self.plugins[source_name] = plugin
        self.logger.info(f"Registered CTI plugin: {source_name} (enabled={plugin.is_enabled()})")

    def get_plugin(self, source_name: str) -> Optional[CTISourcePlugin]:
        """Get plugin by source name"""
        return self.plugins.get(source_name)

    def get_enabled_plugins(self) -> List[CTISourcePlugin]:
        """Get all enabled plugins"""
        return [p for p in self.plugins.values() if p.is_enabled()]

    async def fetch_all_indicators(
        self,
        since: Optional[datetime] = None,
        indicator_types: Optional[List[IndicatorType]] = None,
        limit_per_source: int = 1000
    ) -> Dict[str, List[CTIIndicator]]:
        """
        Fetch indicators from all enabled sources

        Returns:
            Dictionary mapping source_name -> indicators
        """
        results = {}

        for source_name, plugin in self.plugins.items():
            if not plugin.is_enabled():
                continue

            try:
                self.logger.info(f"Fetching indicators from {source_name}...")
                indicators = await plugin.fetch_indicators(
                    since=since,
                    indicator_types=indicator_types,
                    limit=limit_per_source
                )
                results[source_name] = indicators
                self.logger.info(f"Fetched {len(indicators)} indicators from {source_name}")

            except Exception as e:
                self.logger.error(f"Error fetching from {source_name}: {e}")
                results[source_name] = []

        return results

    async def aggregate_indicators(
        self,
        since: Optional[datetime] = None,
        indicator_types: Optional[List[IndicatorType]] = None,
        deduplicate: bool = True
    ) -> List[CTIIndicator]:
        """
        Aggregate indicators from all sources with deduplication

        Returns:
            Combined list of indicators
        """
        all_results = await self.fetch_all_indicators(since, indicator_types)

        # Flatten results
        all_indicators = []
        for source, indicators in all_results.items():
            all_indicators.extend(indicators)

        if deduplicate:
            # Deduplicate by indicator_value
            seen = set()
            unique_indicators = []
            for indicator in all_indicators:
                if indicator.indicator_value not in seen:
                    seen.add(indicator.indicator_value)
                    unique_indicators.append(indicator)
            return unique_indicators

        return all_indicators

    async def health_check_all(self) -> Dict[str, Dict[str, Any]]:
        """Check health of all plugins"""
        results = {}
        for source_name, plugin in self.plugins.items():
            results[source_name] = await plugin.health_check()
        return results
