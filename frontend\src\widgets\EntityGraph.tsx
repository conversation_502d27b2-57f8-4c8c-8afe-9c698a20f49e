import React from 'react'
import { Network, User, Server, Shield } from 'lucide-react'

interface EntityGraphProps {
  entityId?: string
}

export const EntityGraph: React.FC<EntityGraphProps> = ({ entityId }) => {
  return (
    <div className="flex flex-col h-full bg-white p-4">
      <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
        <Network size={20} />
        Entity Relationship Graph
      </h3>

      <div className="flex-1 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
        <div className="text-center text-gray-500">
          <Network size={48} className="mx-auto mb-2 text-gray-400" />
          <p>Entity graph visualization will be here</p>
          <p className="text-sm mt-2">Using D3.js or Cytoscape.js</p>
          {entityId && <p className="text-sm mt-2">Showing: {entityId}</p>}
        </div>
      </div>

      <div className="mt-4 flex justify-around">
        <div className="text-center">
          <User className="mx-auto text-blue-500" />
          <span className="text-xs">Users: 45</span>
        </div>
        <div className="text-center">
          <Server className="mx-auto text-green-500" />
          <span className="text-xs">Systems: 23</span>
        </div>
        <div className="text-center">
          <Shield className="mx-auto text-red-500" />
          <span className="text-xs">Threats: 7</span>
        </div>
      </div>
    </div>
  )
}

export default EntityGraph