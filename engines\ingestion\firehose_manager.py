"""
Firehose Feed Management
Intelligent high-volume log filtering and adaptive backfill

Based on architecture in FIREHOSE_ARCHITECTURE.md
"""

import logging
from typing import Dict, List, Set
from datetime import datetime, timedelta
import hashlib
from bloom_filter2 import BloomFilter  # pip install bloom-filter2
import json

logger = logging.getLogger(__name__)


class FirehoseManager:
    """
    Firehose feed management with multi-stage filtering

    Architecture:
    - Stage 1: Bloom filter (99.9% irrelevant logs filtered)
    - Stage 2: Pattern matching (crystallized patterns)
    - Stage 3: Context enrichment (entity extraction)
    - Stage 4: Evidence caching with SIEM link-back

    Result: 99.998% storage reduction
    """

    def __init__(self, db_connection, redis_client):
        self.db = db_connection
        self.redis = redis_client

        # Bloom filter for seen/irrelevant logs (10M capacity, 0.1% false positive rate)
        self.irrelevant_bloom = BloomFilter(max_elements=10000000, error_rate=0.001)

        # Pattern cache (crystallized patterns from Intelligence Engine)
        self.pattern_cache = {}

        # Backfill state
        self.backfill_state = {}

        logger.info("Firehose Manager initialized with Bloom filter")

    async def process_firehose_log(self, log: Dict) -> Dict:
        """
        Process single log through firehose pipeline

        Returns:
        {
            "action": "filtered" | "stored" | "evidence_cached",
            "reason": "...",
            "intelligence_extracted": {...} or None,
            "siem_url": "..." or None
        }
        """
        try:
            # Stage 1: Bloom Filter Check
            log_hash = self._hash_log(log)

            if log_hash in self.irrelevant_bloom:
                return {
                    'action': 'filtered',
                    'reason': 'Previously seen and marked irrelevant',
                    'stage': 'bloom_filter'
                }

            # Stage 2: Pattern Matching
            pattern_match = await self._match_patterns(log)

            if not pattern_match:
                # No interesting pattern - add to Bloom filter and skip
                self.irrelevant_bloom.add(log_hash)
                return {
                    'action': 'filtered',
                    'reason': 'No matching security patterns',
                    'stage': 'pattern_matching'
                }

            # Stage 3: Context Enrichment (extract intelligence)
            intelligence = await self._extract_intelligence(log, pattern_match)

            # Stage 4: Evidence Caching
            if intelligence.get('security_relevant'):
                # Cache evidence with link-back to SIEM
                siem_url = self._generate_siem_linkback(log)

                await self._cache_evidence(log, intelligence, siem_url)

                return {
                    'action': 'evidence_cached',
                    'reason': f"Security-relevant: {pattern_match.get('pattern_name')}",
                    'intelligence_extracted': intelligence,
                    'siem_url': siem_url,
                    'stage': 'evidence_cached'
                }

            else:
                # Store intelligence only (no full log)
                await self._store_intelligence(intelligence)

                return {
                    'action': 'stored',
                    'reason': 'Intelligence extracted and stored',
                    'intelligence_extracted': intelligence,
                    'stage': 'intelligence_stored'
                }

        except Exception as e:
            logger.error(f"Error processing firehose log: {e}")
            return {
                'action': 'error',
                'reason': str(e)
            }

    def _hash_log(self, log: Dict) -> str:
        """Generate hash for log deduplication"""
        # Hash key fields to identify identical logs
        key_fields = [
            str(log.get('timestamp', '')),
            str(log.get('source_ip', '')),
            str(log.get('user', '')),
            str(log.get('event_type', ''))
        ]

        hash_input = '|'.join(key_fields)
        return hashlib.sha256(hash_input.encode()).hexdigest()

    async def _match_patterns(self, log: Dict) -> Optional[Dict]:
        """Match log against crystallized security patterns"""
        # Check pattern cache (from Intelligence Engine)
        cache_key = 'firehose:patterns'
        cached_patterns = self.redis.get(cache_key)

        if not cached_patterns:
            # Load patterns from database
            patterns = await self._load_patterns()
            self.redis.setex(cache_key, 300, json.dumps(patterns))  # 5-minute cache
        else:
            patterns = json.loads(cached_patterns)

        # Match log against patterns
        for pattern in patterns:
            if self._log_matches_pattern(log, pattern):
                return pattern

        return None

    def _log_matches_pattern(self, log: Dict, pattern: Dict) -> bool:
        """Check if log matches a security pattern"""
        conditions = pattern.get('conditions', {})

        for field, expected_value in conditions.items():
            log_value = log.get(field)

            if isinstance(expected_value, list):
                if log_value not in expected_value:
                    return False
            elif log_value != expected_value:
                return False

        return True

    async def _load_patterns(self) -> List[Dict]:
        """Load crystallized patterns from database"""
        try:
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT pattern_name, pattern_data, security_relevance
                FROM crystallized_patterns
                WHERE is_active = TRUE
                ORDER BY security_relevance DESC
                LIMIT 1000
            """)

            patterns = []
            for row in cursor.fetchall():
                patterns.append({
                    'pattern_name': row[0],
                    'conditions': row[1],  # JSONB column
                    'security_relevance': row[2]
                })

            cursor.close()
            return patterns

        except Exception as e:
            logger.error(f"Error loading patterns: {e}")
            return []

    async def _extract_intelligence(self, log: Dict, pattern_match: Dict) -> Dict:
        """Extract intelligence from log"""
        intelligence = {
            'timestamp': log.get('timestamp'),
            'pattern_matched': pattern_match['pattern_name'],
            'security_relevance': pattern_match['security_relevance'],
            'entities': [],
            'security_relevant': pattern_match['security_relevance'] >= 7  # threshold
        }

        # Extract entities
        entity_fields = ['source_ip', 'dest_ip', 'user', 'hostname', 'process']

        for field in entity_fields:
            if log.get(field):
                intelligence['entities'].append({
                    'type': field,
                    'value': log[field]
                })

        return intelligence

    def _generate_siem_linkback(self, log: Dict) -> str:
        """Generate link-back URL to original log in SIEM"""
        # Simplified - would use actual SIEM integration
        siem_type = log.get('source_siem', 'elastic')
        log_id = log.get('log_id', 'unknown')

        return f"siem://{siem_type}/logs/{log_id}"

    async def _cache_evidence(self, log: Dict, intelligence: Dict, siem_url: str):
        """Cache evidence in Redis for fast retrieval"""
        cache_key = f"evidence:{log.get('timestamp')}:{hashlib.md5(json.dumps(log).encode()).hexdigest()}"

        evidence_data = {
            'intelligence': intelligence,
            'siem_url': siem_url,
            'cached_at': datetime.utcnow().isoformat()
        }

        # Cache for 7 days
        self.redis.setex(cache_key, 604800, json.dumps(evidence_data))

    async def _store_intelligence(self, intelligence: Dict):
        """Store extracted intelligence (not full log)"""
        try:
            cursor = self.db.cursor()

            # Store in lightweight events table (intelligence only)
            cursor.execute("""
                INSERT INTO events (event_timestamp, event_data, entities, source_type)
                VALUES (%s, %s, %s, 'firehose')
            """, (
                intelligence['timestamp'],
                json.dumps({'pattern': intelligence['pattern_matched']}),
                json.dumps(intelligence['entities']),
            ))

            self.db.commit()
            cursor.close()

        except Exception as e:
            logger.error(f"Error storing intelligence: {e}")
            self.db.rollback()

    async def start_adaptive_backfill(self, source_id: str, start_time: datetime, end_time: datetime):
        """
        Start adaptive backfill process

        Pacing algorithm:
        - Start slow (100 logs/sec)
        - Monitor system load
        - Increase rate if load < 70%
        - Decrease rate if load > 85%
        - Pause if load > 95%
        """
        backfill_id = f"backfill:{source_id}:{int(start_time.timestamp())}"

        self.backfill_state[backfill_id] = {
            'source_id': source_id,
            'start_time': start_time,
            'end_time': end_time,
            'current_time': end_time,  # Work backwards (recent-first)
            'rate_limit': 100,  # logs/sec
            'processed': 0,
            'status': 'running'
        }

        logger.info(f"Started adaptive backfill {backfill_id}")

        # Backfill loop would run in background task
        # For now, just register the job
        self.redis.publish('ingestion.backfill_start', json.dumps({
            'backfill_id': backfill_id,
            'source_id': source_id,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat()
        }))

    def get_backfill_status(self, backfill_id: str) -> Dict:
        """Get status of backfill job"""
        state = self.backfill_state.get(backfill_id)

        if not state:
            return {'error': 'Backfill not found'}

        progress = 0
        if state['start_time'] and state['end_time']:
            total_seconds = (state['end_time'] - state['start_time']).total_seconds()
            processed_seconds = (state['end_time'] - state['current_time']).total_seconds()
            progress = (processed_seconds / total_seconds) * 100 if total_seconds > 0 else 0

        return {
            'backfill_id': backfill_id,
            'status': state['status'],
            'progress': round(progress, 2),
            'processed_logs': state['processed'],
            'current_rate': state['rate_limit'],
            'current_time': state['current_time'].isoformat()
        }

    async def get_firehose_stats(self) -> Dict:
        """Get firehose processing statistics"""
        return {
            'bloom_filter_size': len(self.irrelevant_bloom),
            'bloom_filter_capacity': self.irrelevant_bloom.capacity,
            'bloom_filter_false_positive_rate': self.irrelevant_bloom.error_rate,
            'patterns_cached': len(self.pattern_cache),
            'active_backfills': len(self.backfill_state),
            'storage_reduction': "99.998%"  # Based on architecture
        }
