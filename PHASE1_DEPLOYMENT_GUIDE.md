# Phase 1 Deployment Guide - Context-Aware Investigation

## What We Built (Revised Architecture)

### ✅ Using Existing Infrastructure
- **`entities.business_context`** (JSONB) - Analyst-added business context
- **`entities.behavioral_profile`** (JSONB) - Learned/documented behavior patterns
- **`entities.criticality_score`** (0-100) - Entity criticality

### ✅ New Investigation Workflow Tables
- **`investigation_verdicts`** - TP/FP decisions with context logging
- **`investigation_status_history`** - Workflow state tracking
- **`investigation_timeline`** - Detailed audit trail
- **`rule_tuning_suggestions`** - Learning from false positives
- **`query_templates`** - Deterministic query generation
- **`investigation_sla_config`** - SLA settings by severity
- **`investigation_enrichment_status`** - Enrichment tracking
- **`investigation_comments`** - Investigation notes/comments

---

## Step-by-Step Deployment

### Step 1: Apply Database Schema

```bash
# Connect to database
docker-compose exec postgres psql -U siemless -d siemless_v2

# Apply schema (copy/paste or use \i)
\i /path/to/init_db_investigation_workflow.sql

# Verify tables created
\dt investigation*
\dt rule_tuning*
\dt query_templates

# Check seed data
SELECT * FROM investigation_sla_config;
SELECT * FROM query_templates;
```

**Expected Output:**
```
- investigation_verdicts
- investigation_status_history
- investigation_timeline
- investigation_enrichment_status
- investigation_sla_config (4 rows: critical, high, medium, low)
- investigation_comments
- rule_tuning_suggestions
- query_templates (3 rows: elasticsearch, fortinet, palo_alto templates)
```

### Step 2: Integrate BusinessContextManager into Delivery Engine

**File**: `engines/delivery/delivery_engine.py`

Add import:
```python
from business_context_manager import BusinessContextManager
```

In `__init__`:
```python
class DeliveryEngine(BaseEngine):
    def __init__(self):
        super().__init__("delivery", 8005)

        # Existing code...

        # Add Business Context Manager
        self.context_manager = BusinessContextManager(self.db_connection)
        self.logger.info("Business Context Manager initialized")
```

In `async def _setup_http_routes(self, app)`:
```python
async def _setup_http_routes(self, app):
    # Existing routes...

    # Register business context routes
    self.context_manager.register_routes(app)
    self.logger.info("Business context routes registered")
```

### Step 3: Restart Delivery Engine

```bash
# Rebuild and restart
docker-compose build delivery_engine
docker-compose restart delivery_engine

# Check health
curl http://localhost:8005/health

# Check logs
docker-compose logs -f delivery_engine | grep "Business Context"
```

**Expected Log:**
```
Business Context Manager initialized
Business context routes registered
```

### Step 4: Test API Endpoints

```bash
# Test 1: Add business context to an entity
curl -X POST http://localhost:8005/api/entities/host/BACKUP-SERVER-01/context \
  -H "Content-Type: application/json" \
  -H "X-User-ID: admin" \
  -d '{
    "context_label": "Primary Backup Server",
    "context_description": "Runs weekly backups every Sunday 2AM-4AM",
    "business_unit": "IT Operations",
    "owner": "<EMAIL>",
    "criticality": "high",
    "security_zone": "internal",
    "scheduled_jobs": ["weekly_backup"],
    "normal_times": ["Sunday 02:00-04:00"],
    "expected_traffic": ["large_file_operations"],
    "tags": ["backup", "critical_infrastructure"]
  }'

# Expected response:
# {
#   "success": true,
#   "entity_id": "...",
#   "message": "Business context added successfully"
# }

# Test 2: Get business context
curl http://localhost:8005/api/entities/host/BACKUP-SERVER-01/context

# Expected response:
# {
#   "has_context": true,
#   "context": {
#     "entity_id": "...",
#     "business_context": {...},
#     "behavioral_profile": {...},
#     "criticality_score": 70
#   }
# }

# Test 3: List entities with context
curl http://localhost:8005/api/entities/with-context?limit=10

# Test 4: Record investigation verdict
curl -X POST http://localhost:8005/api/investigations/test-inv-001/verdict \
  -H "Content-Type: application/json" \
  -H "X-User-ID: analyst1" \
  -d '{
    "verdict": "false_positive",
    "verdict_reason": "Planned weekly backup",
    "device_context": "Primary backup server - known scheduled activity",
    "confirmation_notes": "Confirmed with IT team",
    "entities_involved": {"host": "BACKUP-SERVER-01"},
    "alert_id": "alert-123"
  }'

# Test 5: List rule tuning suggestions (should auto-generate from FP)
curl http://localhost:8005/api/rule-tuning/suggestions?status=pending
```

### Step 5: Verify Database Persistence

```bash
docker-compose exec postgres psql -U siemless -d siemless_v2

# Check entities table
SELECT entity_type, entity_value,
       business_context->>'context_label' as label,
       behavioral_profile->>'scheduled_jobs' as jobs,
       criticality_score
FROM entities
WHERE business_context IS NOT NULL;

# Check verdicts
SELECT investigation_id, verdict, verdict_reason
FROM investigation_verdicts;

# Check auto-generated rule tuning suggestions
SELECT suggestion_id, reason, suppression_conditions
FROM rule_tuning_suggestions
WHERE extracted_from_verdict = true;
```

---

## Frontend Integration (Next Step)

### Update API Calls in Frontend Components

**BusinessContextBadge.tsx** - Change line ~20:
```typescript
// OLD:
const response = await apiRequest('/context/entity/${entityType}/${entityValue}')

// NEW:
const response = await apiRequest(`/entities/${entityType}/${encodeURIComponent(entityValue)}/context`)
```

**BusinessContextEditor.tsx** - Change onSave function:
```typescript
const saveContext = async (context: BusinessContextFormData) => {
  await apiRequest(
    `/entities/${context.entity_type}/${encodeURIComponent(context.entity_value)}/context`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': 'current_user' // Get from auth store
      },
      body: JSON.stringify({
        context_label: context.context_label,
        context_description: context.context_description,
        business_unit: context.business_unit,
        owner: context.owner,
        criticality: context.criticality,
        security_zone: context.security_zone,
        scheduled_jobs: context.behavior_pattern?.scheduled_jobs || [],
        normal_times: context.behavior_pattern?.normal_times || [],
        expected_traffic: context.behavior_pattern?.expected_traffic || [],
        tags: context.tags || []
      })
    }
  )
}
```

---

## Testing Checklist

### Business Context Management
- [ ] Can add business context to new entity
- [ ] Can add business context to existing entity
- [ ] Can update business context
- [ ] Can retrieve business context by entity type/value
- [ ] Can list all entities with context
- [ ] Can delete business context
- [ ] Business context appears in entities table
- [ ] Behavioral profile stored correctly
- [ ] Criticality score calculated correctly

### Investigation Verdicts
- [ ] Can record true positive verdict
- [ ] Can record false positive verdict with context
- [ ] Can retrieve verdict by investigation ID
- [ ] False positive auto-generates rule tuning suggestion
- [ ] Entity context extracted into verdict

### Rule Tuning Suggestions
- [ ] Auto-generated from FP verdict
- [ ] Can list pending suggestions
- [ ] Can approve suggestion
- [ ] Can apply suggestion
- [ ] Status workflow works (pending → approved → applied)

### Query Templates
- [ ] Seed data loaded (3 templates)
- [ ] Can query templates by source_type
- [ ] Templates have correct placeholders

---

## Success Metrics

**After 1 Week:**
- [ ] At least 10 entities have business context added
- [ ] At least 5 false positives logged with context
- [ ] At least 2 rule tuning suggestions generated
- [ ] Analysts use context badge to verify known entities

**After 1 Month:**
- [ ] 50+ entities with business context
- [ ] 30%+ reduction in false positive investigation time
- [ ] 10+ rule tuning suggestions applied
- [ ] Business context used in 80%+ of FP resolutions

---

## Troubleshooting

### Issue: "Table does not exist"
```bash
# Verify schema applied
docker-compose exec postgres psql -U siemless -d siemless_v2 -c "\dt investigation*"

# If missing, reapply schema
docker-compose exec postgres psql -U siemless -d siemless_v2 < engines/init_db_investigation_workflow.sql
```

### Issue: "BusinessContextManager not found"
```bash
# Check file exists
ls engines/delivery/business_context_manager.py

# Check import in delivery_engine.py
grep "BusinessContextManager" engines/delivery/delivery_engine.py

# Rebuild container
docker-compose build delivery_engine
```

### Issue: "Entity not found" when adding context
This is expected! The entity will be created with the context. If you want to add context to an existing entity from enrichment, make sure the entity exists in the entities table first.

### Issue: "No route registered"
Check delivery_engine.py has:
```python
self.context_manager.register_routes(app)
```

---

## Next Steps - Phase 2

Once Phase 1 is deployed and tested:

1. **Deterministic Query Generator**
   - Generate queries only for sources we have logs from
   - Query `ingestion_logs` to detect available sources
   - Provide copy buttons and deep links

2. **Investigation Guide Enhancement**
   - Add "Queries to Run" section
   - Show "What to Look For" guidance
   - Display data source limitations

3. **Triage Button** (Advanced)
   - Auto-pull data from all sources (±30 min)
   - Build unified timeline
   - Display in Entity Explorer/Grafana

---

**Phase 1 Complete!** ✅

You now have:
- Business context management using existing entities table
- Investigation verdict tracking with context logging
- Auto-generated rule tuning suggestions from FP resolutions
- SLA configuration and tracking
- Query template foundation for deterministic query generation
