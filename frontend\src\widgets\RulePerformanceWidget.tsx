import React, { useEffect, useState } from 'react'
import { useRuleStore } from '../stores/ruleStore'
import { wsClient } from '../api/client'
import { RulePerformance } from '../api/types/api'
import {
  TrendingUp, TrendingDown, AlertCircle, CheckCircle,
  RefreshCw, Filter, Calendar, BarChart3, Target
} from 'lucide-react'

/**
 * Rule Performance Widget
 *
 * Purpose: Track detection rule effectiveness and optimization
 *
 * Features:
 * - Performance summary dashboard
 * - Top performers vs rules needing tuning
 * - True positive / false positive rates
 * - Precision, recall, F1 scores
 * - Time period selection (24h, 7d, 30d, 90d)
 * - Trend visualization
 * - Quick actions to optimize underperforming rules
 */

const RulePerformanceWidget: React.FC = () => {
  const {
    performanceSummary,
    rulePerformance,
    loading,
    error,
    fetchPerformanceSummary,
    selectRule
  } = useRuleStore()

  const [period, setPeriod] = useState<'24h' | '7d' | '30d' | '90d'>('7d')

  // Initial load
  useEffect(() => {
    fetchPerformanceSummary(period)
  }, [fetchPerformanceSummary, period])

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      fetchPerformanceSummary(period)
    }, 5 * 60 * 1000)

    return () => clearInterval(interval)
  }, [fetchPerformanceSummary, period])

  // WebSocket updates for real-time performance changes
  useEffect(() => {
    const handlePerformanceUpdate = (data: any) => {
      if (data.type === 'rule.performance.updated') {
        fetchPerformanceSummary(period)
      }
    }

    wsClient.on('rule.performance.updated', handlePerformanceUpdate)

    return () => {
      wsClient.off('rule.performance.updated', handlePerformanceUpdate)
    }
  }, [fetchPerformanceSummary, period])

  const getPerformanceColor = (score: number) => {
    if (score >= 85) return 'text-green-600'
    if (score >= 70) return 'text-blue-600'
    if (score >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getPerformanceBgColor = (score: number) => {
    if (score >= 85) return 'bg-green-50 border-green-200'
    if (score >= 70) return 'bg-blue-50 border-blue-200'
    if (score >= 50) return 'bg-yellow-50 border-yellow-200'
    return 'bg-red-50 border-red-200'
  }

  const formatRate = (rate: number) => `${(rate * 100).toFixed(1)}%`

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <BarChart3 className="text-blue-500" size={20} />
              Rule Performance Tracker
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              Monitor detection effectiveness and optimize false positives
            </p>
          </div>

          <div className="flex items-center gap-3">
            {/* Period Selector */}
            <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-100 rounded">
              <Calendar size={16} className="text-gray-500" />
              <select
                value={period}
                onChange={(e) => setPeriod(e.target.value as any)}
                className="bg-transparent border-none text-sm focus:ring-0"
              >
                <option value="24h">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
              </select>
            </div>

            <button
              onClick={() => fetchPerformanceSummary(period)}
              disabled={loading.performance}
              className="p-2 rounded hover:bg-gray-100 disabled:opacity-50"
            >
              <RefreshCw size={18} className={loading.performance ? 'animate-spin' : ''} />
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        {loading.performance && !performanceSummary ? (
          <div className="flex items-center justify-center h-64">
            <RefreshCw className="animate-spin text-gray-400" size={32} />
          </div>
        ) : error.performance ? (
          <div className="flex items-center justify-center h-64 text-red-600">
            <div className="text-center">
              <AlertCircle className="mx-auto mb-2" size={32} />
              <p>{error.performance}</p>
            </div>
          </div>
        ) : !performanceSummary ? (
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <Target className="mx-auto mb-2" size={32} />
              <p>No performance data available</p>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Summary Stats */}
            <div className="grid grid-cols-4 gap-4">
              <div className="bg-white rounded-lg border p-4">
                <div className="text-sm text-gray-500 mb-1">Total Rules</div>
                <div className="text-2xl font-bold">{performanceSummary.total_rules}</div>
                <div className="text-xs text-gray-400 mt-1">
                  {performanceSummary.active_rules} active
                </div>
              </div>

              <div className="bg-white rounded-lg border p-4">
                <div className="text-sm text-gray-500 mb-1">Total Alerts</div>
                <div className="text-2xl font-bold">{performanceSummary.total_alerts_24h}</div>
                <div className="text-xs text-gray-400 mt-1">Last 24 hours</div>
              </div>

              <div className="bg-white rounded-lg border p-4">
                <div className="text-sm text-gray-500 mb-1">Avg TP Rate</div>
                <div className={`text-2xl font-bold ${getPerformanceColor(performanceSummary.avg_true_positive_rate * 100)}`}>
                  {formatRate(performanceSummary.avg_true_positive_rate)}
                </div>
                <div className="flex items-center gap-1 mt-1">
                  <TrendingUp className="text-green-500" size={14} />
                  <span className="text-xs text-gray-400">Good detection</span>
                </div>
              </div>

              <div className="bg-white rounded-lg border p-4">
                <div className="text-sm text-gray-500 mb-1">Avg FP Rate</div>
                <div className={`text-2xl font-bold ${getPerformanceColor(100 - performanceSummary.avg_false_positive_rate * 100)}`}>
                  {formatRate(performanceSummary.avg_false_positive_rate)}
                </div>
                <div className="flex items-center gap-1 mt-1">
                  {performanceSummary.avg_false_positive_rate < 0.1 ? (
                    <>
                      <TrendingDown className="text-green-500" size={14} />
                      <span className="text-xs text-green-600">Excellent</span>
                    </>
                  ) : performanceSummary.avg_false_positive_rate < 0.25 ? (
                    <>
                      <TrendingDown className="text-blue-500" size={14} />
                      <span className="text-xs text-blue-600">Good</span>
                    </>
                  ) : (
                    <>
                      <TrendingUp className="text-red-500" size={14} />
                      <span className="text-xs text-red-600">Needs work</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Top Performers */}
            <div className="bg-white rounded-lg border">
              <div className="px-4 py-3 border-b">
                <h3 className="font-semibold flex items-center gap-2">
                  <CheckCircle className="text-green-500" size={18} />
                  Top Performing Rules
                </h3>
                <p className="text-xs text-gray-500 mt-1">
                  Rules with highest performance scores
                </p>
              </div>

              <div className="divide-y">
                {performanceSummary.top_performers.length === 0 ? (
                  <div className="p-6 text-center text-gray-500">
                    No performance data yet
                  </div>
                ) : (
                  performanceSummary.top_performers.map((rule, idx) => (
                    <div
                      key={rule.rule_id}
                      className="p-4 hover:bg-gray-50 cursor-pointer"
                      onClick={() => selectRule(rule.rule_id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                            idx === 0 ? 'bg-yellow-100 text-yellow-700' :
                            idx === 1 ? 'bg-gray-200 text-gray-600' :
                            idx === 2 ? 'bg-orange-100 text-orange-600' :
                            'bg-blue-50 text-blue-600'
                          }`}>
                            #{idx + 1}
                          </div>
                          <div>
                            <div className="font-medium">{rule.rule_name}</div>
                            <div className="text-xs text-gray-500">ID: {rule.rule_id.slice(0, 8)}</div>
                          </div>
                        </div>

                        <div className="flex items-center gap-4">
                          <div className="text-right">
                            <div className={`text-lg font-bold ${getPerformanceColor(rule.performance_score)}`}>
                              {rule.performance_score.toFixed(1)}
                            </div>
                            <div className="text-xs text-gray-500">Score</div>
                          </div>
                          <TrendingUp className="text-green-500" size={20} />
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Rules Needing Tuning */}
            <div className="bg-white rounded-lg border">
              <div className="px-4 py-3 border-b">
                <h3 className="font-semibold flex items-center gap-2">
                  <AlertCircle className="text-orange-500" size={18} />
                  Rules Needing Tuning
                </h3>
                <p className="text-xs text-gray-500 mt-1">
                  Rules with high false positive rates requiring optimization
                </p>
              </div>

              <div className="divide-y">
                {performanceSummary.needs_tuning.length === 0 ? (
                  <div className="p-6 text-center text-gray-500">
                    <CheckCircle className="mx-auto mb-2 text-green-500" size={24} />
                    <p>All rules performing well!</p>
                  </div>
                ) : (
                  performanceSummary.needs_tuning.map((rule) => (
                    <div
                      key={rule.rule_id}
                      className="p-4 hover:bg-gray-50"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="font-medium">{rule.rule_name}</div>
                          <div className="text-xs text-gray-500 mt-1">ID: {rule.rule_id.slice(0, 8)}</div>
                        </div>

                        <div className="flex items-center gap-4">
                          <div className="text-right">
                            <div className="text-lg font-bold text-red-600">
                              {formatRate(rule.false_positive_rate)}
                            </div>
                            <div className="text-xs text-gray-500">False Positive Rate</div>
                          </div>

                          <div className="flex flex-col gap-2">
                            <button
                              onClick={() => selectRule(rule.rule_id)}
                              className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
                            >
                              Review
                            </button>
                            <button
                              className="px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600"
                            >
                              Tune
                            </button>
                          </div>
                        </div>
                      </div>

                      {/* Suggested Actions */}
                      <div className="mt-3 p-3 bg-orange-50 rounded border border-orange-200">
                        <div className="text-xs font-medium text-orange-700 mb-1">
                          Optimization Suggestions:
                        </div>
                        <ul className="text-xs text-orange-600 space-y-0.5 ml-4">
                          {rule.false_positive_rate > 0.5 && (
                            <li>• Consider disabling or rewriting - FP rate critically high</li>
                          )}
                          {rule.false_positive_rate > 0.3 && rule.false_positive_rate <= 0.5 && (
                            <li>• Add exclusion conditions to reduce noise</li>
                          )}
                          {rule.false_positive_rate > 0.15 && rule.false_positive_rate <= 0.3 && (
                            <li>• Refine detection logic or increase threshold</li>
                          )}
                          <li>• Review recent false positives for patterns</li>
                          <li>• Consider adding allowlists for known-good activity</li>
                        </ul>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Performance Metrics Guide */}
            <div className="bg-blue-50 rounded-lg border border-blue-200 p-4">
              <h3 className="font-semibold text-blue-900 mb-3">Understanding Performance Metrics</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="font-medium text-blue-800 mb-1">True Positive Rate (TPR)</div>
                  <div className="text-blue-700">
                    Percentage of actual threats correctly detected. Higher is better.
                  </div>
                  <div className="mt-1 text-xs">
                    <span className="text-green-600 font-medium">&gt;90%:</span> Excellent &nbsp;
                    <span className="text-blue-600 font-medium">70-90%:</span> Good &nbsp;
                    <span className="text-orange-600 font-medium">&lt;70%:</span> Needs improvement
                  </div>
                </div>

                <div>
                  <div className="font-medium text-blue-800 mb-1">False Positive Rate (FPR)</div>
                  <div className="text-blue-700">
                    Percentage of benign activity incorrectly flagged. Lower is better.
                  </div>
                  <div className="mt-1 text-xs">
                    <span className="text-green-600 font-medium">&lt;10%:</span> Excellent &nbsp;
                    <span className="text-blue-600 font-medium">10-25%:</span> Acceptable &nbsp;
                    <span className="text-red-600 font-medium">&gt;25%:</span> Tune immediately
                  </div>
                </div>

                <div>
                  <div className="font-medium text-blue-800 mb-1">Precision</div>
                  <div className="text-blue-700">
                    When rule fires, how often is it a true threat? (TP / TP+FP)
                  </div>
                </div>

                <div>
                  <div className="font-medium text-blue-800 mb-1">F1 Score</div>
                  <div className="text-blue-700">
                    Harmonic mean of precision and recall. Overall effectiveness metric.
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default RulePerformanceWidget
