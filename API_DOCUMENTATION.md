# SIEMLess v2.0 - Complete API Documentation

## Table of Contents
- [Overview](#overview)
- [Authentication](#authentication)
- [Common Endpoints (All Engines)](#common-endpoints-all-engines)
- [Intelligence Engine (Port 8001)](#intelligence-engine-port-8001)
- [Backend Engine (Port 8002)](#backend-engine-port-8002)
- [Ingestion Engine (Port 8003)](#ingestion-engine-port-8003)
- [Contextualization Engine (Port 8004)](#contextualization-engine-port-8004)
- [Delivery Engine (Port 8005)](#delivery-engine-port-8005)
- [Data Models](#data-models)
- [WebSocket/Redis Channels](#websocketredis-channels)
- [Frontend Integration Patterns](#frontend-integration-patterns)
- [CTI Plugin Architecture](#cti-plugin-architecture)

---

## Overview

SIEMLess v2.0 provides a comprehensive REST API across 5 specialized engines, each responsible for specific functionality within the security intelligence platform.

### Engine Responsibilities

| Engine | Port | Primary Function |
|--------|------|-----------------|
| Intelligence | 8001 | AI consensus, pattern crystallization, cost optimization |
| Backend | 8002 | Storage, CTI processing, rule generation, log quality assessment |
| Ingestion | 8003 | Multi-source ingestion, CTI plugins, parser management |
| Contextualization | 8004 | Entity extraction, enrichment, relationship mapping |
| Delivery | 8005 | Case management, workflows, frontend services, investigations |

### Base URLs

```
Intelligence Engine:      http://localhost:8001
Backend Engine:          http://localhost:8002
Ingestion Engine:        http://localhost:8003
Contextualization Engine: http://localhost:8004
Delivery Engine:         http://localhost:8005
```

---

## Authentication

**Currently Disabled** - Authentication middleware is commented out for development.

When enabled, the system will use Keycloak for OAuth2/OpenID Connect:
- Realm: `siemless`
- Client ID: `siemless-api`
- Token endpoint: `http://keycloak:8080/auth/realms/siemless/protocol/openid-connect/token`

---

## Common Endpoints (All Engines)

Every engine exposes these standard endpoints:

### GET /health
Check engine health status.

**Response:**
```json
{
    "engine": "engine_name",
    "status": "healthy",
    "uptime": "0:05:23.159504",
    "last_heartbeat": "2025-10-03T04:10:51.621395",
    "messages_processed": 150,
    "errors": 0,
    "timestamp": "2025-10-03T04:15:09.771146",
    "database": "connected",
    "redis": "connected"
}
```

### GET /metrics
Get engine performance metrics.

**Response:**
```json
{
    "engine": "engine_name",
    "metrics": {
        "messages_processed": 150,
        "errors": 0,
        "processing_times": [0.023, 0.045, 0.012],
        "avg_processing_time": 0.027
    },
    "uptime": "0:10:15",
    "timestamp": "2025-10-03T04:15:09.771146"
}
```

---

## Intelligence Engine (Port 8001)

The Intelligence Engine handles AI operations, pattern crystallization, and consensus validation. It primarily operates through Redis pub/sub channels rather than direct HTTP endpoints.

### Redis Channels (Primary Interface)

#### intelligence.consensus
Request AI consensus validation for patterns.

**Request:**
```json
{
    "pattern_data": {
        "pattern": "regex_or_logic",
        "type": "security_event",
        "confidence_required": 0.85
    },
    "models": ["gemini-2.5-flash", "claude-opus-4-1-20250805"],
    "request_id": "uuid"
}
```

#### intelligence.crystallize
Convert expensive AI patterns to deterministic rules.

**Request:**
```json
{
    "pattern": {
        "type": "attack_pattern",
        "description": "Lateral movement detection",
        "logic": "complex_ai_logic"
    },
    "optimization_target": "cost",
    "request_id": "uuid"
}
```

#### intelligence.extract_entities_ai
AI-powered entity extraction for complex logs.

**Request:**
```json
{
    "log_data": "raw_log_content",
    "extraction_hints": ["ip", "user", "process"],
    "request_id": "uuid"
}
```

---

## Backend Engine (Port 8002)

The Backend Engine provides comprehensive log source quality assessment, detection fidelity calculations, and CTI-to-rule automation.

### Log Source Management

#### POST /api/log-sources/register
Register a new log source with automatic quality assessment.

**Request:**
```json
{
    "name": "CrowdStrike Falcon",
    "type": "endpoint",
    "product": "crowdstrike",
    "version": "6.44",
    "capabilities": [
        "kernel_visibility",
        "memory_analysis",
        "behavioral_detection",
        "ml_detection"
    ],
    "api_enabled": true,
    "real_time": true
}
```

**Response (201 Created):**
```json
{
    "source_id": "endpoint-abc123",
    "name": "CrowdStrike Falcon",
    "type": "endpoint",
    "tier": "PLATINUM",
    "quality_score": 98,
    "capabilities": ["kernel_visibility", "memory_analysis"],
    "assessment": {
        "strengths": ["Kernel-level visibility", "ML detection"],
        "weaknesses": [],
        "recommendations": []
    }
}
```

#### GET /api/log-sources/status
Get all registered log sources with quality scores.

**Response:**
```json
{
    "total_sources": 6,
    "sources": [
        {
            "source_id": "crowdstrike-001",
            "name": "CrowdStrike Falcon",
            "type": "endpoint",
            "tier": "PLATINUM",
            "quality_score": 98.0,
            "capabilities": ["kernel_visibility", "memory_analysis"],
            "configuration": {},
            "registered_at": "2025-09-30T03:58:30.665700",
            "last_updated": "2025-09-30T03:58:39.808119"
        }
    ],
    "environment_quality": {
        "average_score": 78.0,
        "overall_tier": "SILVER"
    }
}
```

#### DELETE /api/log-sources/{source_id}
Remove a registered log source.

**Response:**
```json
{
    "message": "Log source endpoint-abc123 removed"
}
```

### Detection Fidelity Assessment

#### POST /api/detection/fidelity
Calculate detection confidence for specific attack types.

**Request:**
```json
{
    "attack_types": ["ransomware", "lateral_movement", "data_exfiltration"]
}
```

**Response:**
```json
{
    "attack_fidelity": {
        "ransomware": {
            "confidence": 35.0,
            "requirements_met": false,
            "missing_sources": [
                {
                    "category": "file_integrity",
                    "required_tier": "SILVER",
                    "importance": "critical"
                }
            ]
        },
        "lateral_movement": {
            "confidence": 100,
            "requirements_met": true,
            "missing_sources": []
        }
    },
    "overall_confidence": 58.3,
    "available_sources": 6
}
```

#### GET /api/detection/coverage
Get overall detection coverage assessment.

**Response:**
```json
{
    "detection_coverage": {
        "high_confidence": [
            {"attack": "lateral_movement", "confidence": 100},
            {"attack": "privilege_escalation", "confidence": 85}
        ],
        "medium_confidence": [
            {"attack": "persistence", "confidence": 75}
        ],
        "low_confidence": [
            {"attack": "ransomware", "confidence": 35}
        ]
    },
    "mitre_coverage": {
        "overall_score": 65,
        "techniques_covered": 89,
        "techniques_total": 193,
        "coverage_percentage": 46.1
    }
}
```

#### POST /api/detection/technique-coverage
Check coverage for specific MITRE ATT&CK techniques.

**Request:**
```json
{
    "technique_ids": ["T1003", "T1055", "T1021", "T1486"]
}
```

### Correlation Capabilities

#### GET /api/correlation/capability
Assess current correlation capabilities.

**Response:**
```json
{
    "correlation_capability": {
        "score": 72,
        "level": "GOOD",
        "strengths": [
            "Strong endpoint visibility",
            "Excellent identity coverage"
        ],
        "weaknesses": [
            "No cloud source coverage"
        ]
    }
}
```

#### POST /api/correlation/requirements
Check requirements for detecting specific attacks.

#### POST /api/correlation/recommendations
Get recommendations for improving detection.

### Coverage Analysis

#### GET /api/coverage/gaps
Identify gaps in security coverage.

#### POST /api/coverage/simulate
Simulate impact of adding new log sources.

### Graph Visualization (Apache AGE)

#### GET /api/graph/explore
Explore entity relationships in graph format.

#### GET /api/graph/path
Find paths between entities.

#### GET /api/graph/stats
Get graph statistics.

#### GET /api/graph/high-risk
Identify high-risk entities.

#### GET /api/graph/centrality
Calculate entity centrality scores.

### MITRE ATT&CK Integration

Additional MITRE-specific endpoints are dynamically registered and available through the Backend Engine.

---

## Ingestion Engine (Port 8003)

The Ingestion Engine manages data sources, CTI plugins, and parser generation.

### Source Management

#### GET /sources
Get information about all data sources.

**Response:**
```json
{
    "active_sources": ["elastic-prod", "splunk-dev"],
    "source_configs": {
        "elasticsearch": {
            "enabled": true,
            "settings": {}
        }
    },
    "health_summary": {
        "healthy": 2,
        "unhealthy": 0
    }
}
```

#### POST /sources/start
Start a data source.

**Request:**
```json
{
    "source_id": "elastic-prod",
    "source_type": "elasticsearch"
}
```

#### POST /sources/stop
Stop a data source.

**Request:**
```json
{
    "source_id": "elastic-prod"
}
```

### CTI Plugin Management (NEW - Universal Plugin Architecture)

#### GET /cti/connectors
List all registered CTI plugins and their capabilities.

**Response:**
```json
{
    "plugins": ["otx", "threatfox", "crowdstrike", "opencti"],
    "count": 4,
    "source_types": {
        "otx": "community",
        "threatfox": "community",
        "crowdstrike": "commercial",
        "opencti": "internal"
    }
}
```

#### GET /cti/status
Get health status of all CTI plugins.

**Response:**
```json
{
    "plugin_count": 4,
    "plugins": ["otx", "threatfox", "crowdstrike", "opencti"],
    "health": {
        "otx": {
            "healthy": true,
            "last_check": "2025-10-03T10:00:00Z",
            "indicators_fetched": 1250
        },
        "threatfox": {
            "healthy": true,
            "last_check": "2025-10-03T10:05:00Z",
            "indicators_fetched": 890
        },
        "crowdstrike": {
            "healthy": true,
            "last_check": "2025-10-03T10:10:00Z",
            "indicators_fetched": 2500,
            "priority": 80
        },
        "opencti": {
            "healthy": false,
            "error": "Connection timeout"
        }
    }
}
```

#### POST /cti/manual_update
Manually trigger CTI data fetch from plugins.

**Request:**
```json
{
    "source": "all",  // or specific like "crowdstrike"
    "since_days": 1,
    "limit": 1000
}
```

**Response:**
```json
{
    "status": "success",
    "indicators_fetched": 4640,
    "sources": ["otx", "threatfox", "crowdstrike"]
}
```

### Parser Management

#### POST /api/parsers/generate
Generate a new parser using AI.

**Request:**
```json
{
    "log_sample": "raw_log_text",
    "parser_name": "custom_firewall",
    "extraction_fields": ["src_ip", "dst_ip", "action"]
}
```

#### GET /api/parsers
List all available parsers.

#### GET /api/parsers/{parser_id}
Get specific parser details.

#### DELETE /api/parsers/{parser_id}
Delete a parser.

### Statistics

#### GET /stats
Get comprehensive ingestion statistics.

**Response:**
```json
{
    "stats": {
        "logs_processed": 45832,
        "sources_active": 3,
        "patterns_matched": 12450
    },
    "performance_summary": {
        "avg_processing_time": 0.023,
        "throughput": 2500
    },
    "health_status": "healthy"
}
```

#### GET /tasks
Get status of background tasks.

---

## Contextualization Engine (Port 8004)

The Contextualization Engine primarily operates through Redis pub/sub channels for entity extraction and enrichment. It does not expose direct HTTP endpoints.

### Redis Channels

#### contextualization.process_log
Process a log and extract all entities.

**Request:**
```json
{
    "log": {
        "raw": "log_content",
        "parsed": {}
    },
    "log_id": "log_uuid",
    "pattern_type": "security_event",
    "entity_hints": [
        {"type": "ip", "value": "*************"}
    ]
}
```

#### contextualization.extract_entities
Extract entities using adaptive extraction.

**Request:**
```json
{
    "data": "text_to_analyze",
    "extraction_mode": "adaptive",
    "context": "security_investigation"
}
```

#### contextualization.enrich_entity
Enrich a specific entity with threat intelligence.

**Request:**
```json
{
    "entity_type": "ip",
    "entity_value": "*************",
    "enrich_level": "full"
}
```

#### contextualization.find_relationships
Discover relationships between entities.

**Request:**
```json
{
    "entity": {
        "type": "user",
        "value": "john.doe"
    },
    "relationship_types": ["accessed", "owns", "manages"]
}
```

---

## Delivery Engine (Port 8005)

The Delivery Engine provides comprehensive case management, workflow orchestration, and frontend services.

### Case Management

#### POST /api/cases
Create a new case.

**Request:**
```json
{
    "title": "Suspicious Login Activity",
    "description": "Multiple failed login attempts detected",
    "case_type": "security_incident",
    "priority": "high",
    "assigned_to": "analyst1"
}
```

**Response (201 Created):**
```json
{
    "case_id": "case_1696300000",
    "title": "Suspicious Login Activity",
    "status": "open",
    "workflow_stage": "triage",
    "created_at": "2025-10-03T10:00:00Z"
}
```

#### GET /api/cases
List all cases with filtering.

**Query Parameters:**
- `status` - Filter by status (open, closed, in_progress)
- `priority` - Filter by priority (low, medium, high, critical)
- `assigned_to` - Filter by assignee
- `limit` - Number of results (default: 50)
- `offset` - Pagination offset

#### GET /api/cases/{case_id}
Get detailed case information.

#### PUT /api/cases/{case_id}
Update case details.

#### DELETE /api/cases/{case_id}
Close a case.

#### POST /api/cases/{case_id}/evidence
Add evidence to a case.

### Dashboard

#### GET /api/dashboard/overview
Get dashboard overview data.

**Response:**
```json
{
    "total_cases": 45,
    "open_cases": 12,
    "critical_alerts": 3,
    "recent_activity": [],
    "system_health": "healthy"
}
```

#### GET /api/dashboard/cases
Get case statistics for dashboard.

#### GET /api/dashboard/stats
Get comprehensive system statistics.

### Alert Management

#### POST /api/alerts
Send a new alert.

**Request:**
```json
{
    "title": "High Risk Activity Detected",
    "severity": "critical",
    "source": "crowdstrike",
    "details": {},
    "auto_investigate": true
}
```

#### GET /api/alerts
List recent alerts.

#### GET /api/alerts/{alert_id}/context
Get investigation context for an alert.

**Response:**
```json
{
    "alert_id": "alert_123",
    "entities": [
        {"type": "ip", "value": "*************", "risk_score": 85}
    ],
    "related_events": [],
    "threat_intelligence": {},
    "recommendations": []
}
```

### Workflow Orchestration

#### POST /api/workflows/start
Start a new workflow.

**Request:**
```json
{
    "template": "incident_response",
    "parameters": {
        "severity": "high",
        "affected_systems": ["web-server-01"]
    }
}
```

#### GET /api/workflows
List active workflows.

#### GET /api/workflows/{workflow_id}
Get workflow status and details.

#### POST /api/workflows/{workflow_id}/cancel
Cancel a running workflow.

#### GET /api/workflows/templates
List available workflow templates.

**Response:**
```json
{
    "templates": [
        {
            "name": "incident_response",
            "description": "Standard incident response workflow",
            "steps": ["triage", "investigate", "contain", "remediate"]
        },
        {
            "name": "threat_hunt",
            "description": "Proactive threat hunting workflow",
            "steps": ["hypothesis", "data_collection", "analysis", "report"]
        }
    ]
}
```

### Entity Management

#### GET /api/entities
List entities in the system.

#### GET /api/entities/{entity_id}
Get entity details.

#### GET /api/entities/{entity_id}/enrichment
Get entity enrichment data.

#### GET /api/entities/{entity_id}/relationships
Get entity relationships.

#### GET /api/entities/{entity_id}/timeline
Get entity activity timeline.

#### GET /api/entities/{entity_id}/risk
Get entity risk assessment.

### Investigation

#### POST /api/investigations
Create a new investigation.

**Request:**
```json
{
    "title": "Ransomware Detection",
    "type": "auto",
    "alert_ids": ["alert_123", "alert_124"],
    "auto_enrich": true
}
```

#### GET /api/investigations
List investigations.

#### GET /api/investigations/{investigation_id}
Get investigation details.

#### POST /api/investigations/{investigation_id}/findings
Add findings to investigation.

### Pattern & Rule Management

#### GET /api/patterns
List detection patterns.

#### GET /api/patterns/{pattern_id}
Get pattern details.

#### POST /api/patterns/test
Test a pattern against sample data.

#### GET /api/rules
List detection rules.

#### GET /api/rules/{rule_id}
Get rule details.

#### POST /api/rules/test
Test a rule.

### Authentication

#### POST /api/auth/login
User login.

**Request:**
```json
{
    "username": "analyst1",
    "password": "secure_password"
}
```

#### POST /api/auth/logout
User logout.

#### GET /api/auth/sessions
List active sessions.

### System Status

#### GET /api/system/status
Get overall system status.

#### GET /api/system/engines
Get status of all engines.

---

## Data Models

### CTIIndicator
```typescript
interface CTIIndicator {
    indicator: string;           // The actual IOC value
    type: string;               // ip, domain, hash, url, etc.
    severity: string;           // low, medium, high, critical
    confidence: number;         // 0-100
    source: string;             // Plugin that provided it
    first_seen: string;         // ISO datetime
    last_seen: string;          // ISO datetime
    tags: string[];             // Associated tags
    metadata: {                 // Plugin-specific data
        pulse_id?: string;       // OTX pulse
        malware_family?: string; // ThreatFox
        actor_name?: string;     // CrowdStrike
        [key: string]: any;
    }
}
```

### Entity
```typescript
interface Entity {
    entity_id: string;
    entity_type: string;        // ip, user, host, domain, etc.
    entity_value: string;
    first_seen: string;
    last_seen: string;
    occurrence_count: number;
    risk_score: number;         // 0-100
    enrichment: {
        geolocation?: GeoData;
        threat_intel?: ThreatData;
        asset_info?: AssetData;
    };
    relationships: Relationship[];
}
```

### Relationship
```typescript
interface Relationship {
    relationship_id: string;
    source_entity_id: string;
    target_entity_id: string;
    relationship_type: string;  // connects_to, owns, accesses, etc.
    confidence: number;
    first_observed: string;
    last_observed: string;
    evidence_count: number;
}
```

### Case
```typescript
interface Case {
    case_id: string;
    title: string;
    description: string;
    type: string;               // security_incident, anomaly_detection
    priority: string;           // low, medium, high, critical
    status: string;             // open, in_progress, closed
    workflow_stage: string;
    assigned_to?: string;
    created_at: string;
    updated_at: string;
    closed_at?: string;
    evidence: Evidence[];
    timeline: TimelineEntry[];
    resolution?: string;
}
```

### Alert
```typescript
interface Alert {
    alert_id: string;
    title: string;
    severity: string;           // info, low, medium, high, critical
    source: string;
    timestamp: string;
    status: string;             // new, triaged, investigating, resolved
    details: object;
    entities: Entity[];
    case_id?: string;           // Linked case if exists
    investigation_id?: string;  // Linked investigation
}
```

### Workflow
```typescript
interface Workflow {
    workflow_id: string;
    template: string;
    status: string;             // running, completed, failed, cancelled
    current_step: number;
    total_steps: number;
    started_at: string;
    completed_at?: string;
    parameters: object;
    results: object;
    error?: string;
}
```

---

## WebSocket/Redis Channels

The system uses Redis pub/sub for real-time communication between engines and with frontend clients.

### Channel Patterns

#### Request/Response Pattern
Most operations follow a request/response pattern:
1. Publish request to `service.action` channel with `request_id`
2. Subscribe to `service.action.response.{request_id}` for response
3. Response includes original `request_id` for correlation

Example:
```javascript
// Request
redis.publish('backend.log_sources.list', JSON.stringify({
    request_id: 'uuid-123',
    filters: { tier: 'PLATINUM' }
}));

// Response channel
redis.subscribe('backend.log_sources.response.uuid-123');
```

### Key Channels

#### CTI Channels (NEW Architecture)
- `cti.rules.patterns` - CTI patterns for rule generation
- `cti.investigation.context` - CTI context for investigations
- `cti.mitre.mappings` - MITRE framework updates

#### Intelligence Channels
- `intelligence.consensus` - AI consensus requests
- `intelligence.crystallize` - Pattern crystallization
- `intelligence.validate` - Pattern validation
- `intelligence.extract_entities_ai` - AI entity extraction

#### Backend Channels
- `backend.store_intelligence` - Store extracted intelligence (lightweight)
- `backend.cti.indicators` - CTI indicator updates
- `backend.rule_generation` - Rule generation requests

#### Ingestion Channels
- `ingestion.events.parsed` - Parsed events
- `ingestion.cti.update` - CTI update triggers
- `ingestion.unknown_patterns` - Unrecognized patterns

#### Contextualization Channels
- `contextualization.process_log` - Process and extract entities
- `contextualization.enrich_entity` - Entity enrichment
- `contextualization.find_relationships` - Relationship discovery

#### Delivery Channels
- `delivery.create_case` - Case creation
- `delivery.send_alert` - Alert delivery
- `delivery.start_workflow` - Workflow initiation

---

## Frontend Integration Patterns

### 1. Dashboard Data Flow

```javascript
// Frontend polls for dashboard data
async function updateDashboard() {
    // Get overview
    const overview = await fetch('http://localhost:8005/api/dashboard/overview');

    // Get detection capabilities
    const coverage = await fetch('http://localhost:8002/api/detection/coverage');

    // Get CTI status
    const ctiStatus = await fetch('http://localhost:8003/cti/status');

    // Combine and render
    renderDashboard({ overview, coverage, ctiStatus });
}
```

### 2. CTI Plugin Monitoring

```javascript
// Monitor CTI plugin health
async function monitorCTIPlugins() {
    const response = await fetch('http://localhost:8003/cti/status');
    const data = await response.json();

    data.plugins.forEach(plugin => {
        const health = data.health[plugin];
        if (!health.healthy) {
            showAlert(`CTI Plugin ${plugin} is unhealthy: ${health.error}`);
        }
    });
}

// Trigger manual CTI update
async function updateCTI(source = 'all') {
    const response = await fetch('http://localhost:8003/cti/manual_update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            source: source,
            since_days: 1,
            limit: 1000
        })
    });

    const result = await response.json();
    showNotification(`Fetched ${result.indicators_fetched} indicators from ${result.sources.join(', ')}`);
}
```

### 3. Investigation Context Building

```javascript
// Get investigation context for an alert
async function investigateAlert(alertId) {
    // Get alert context from Delivery Engine
    const context = await fetch(`http://localhost:8005/api/alerts/${alertId}/context`);

    // For each entity, get enrichment
    const enrichedEntities = await Promise.all(
        context.entities.map(async entity => {
            const enrichment = await fetch(
                `http://localhost:8005/api/entities/${entity.entity_id}/enrichment`
            );
            return { ...entity, enrichment: await enrichment.json() };
        })
    );

    // Display investigation view
    renderInvestigation({ alert: context, entities: enrichedEntities });
}
```

### 4. Detection Coverage Assessment

```javascript
// Assess and visualize detection coverage
async function assessDetectionCoverage() {
    // Get current log sources
    const sources = await fetch('http://localhost:8002/api/log-sources/status');

    // Calculate detection fidelity for key attacks
    const fidelity = await fetch('http://localhost:8002/api/detection/fidelity', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            attack_types: ['ransomware', 'lateral_movement', 'data_exfiltration']
        })
    });

    // Get recommendations
    const recommendations = await fetch('http://localhost:8002/api/correlation/recommendations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            target_attacks: ['ransomware']
        })
    });

    // Visualize coverage gaps
    renderCoverageMap({ sources, fidelity, recommendations });
}
```

### 5. Real-time Updates via WebSocket

```javascript
// Connect to Redis pub/sub via WebSocket bridge
const ws = new WebSocket('ws://localhost:8005/ws');

ws.onmessage = (event) => {
    const message = JSON.parse(event.data);

    switch(message.channel) {
        case 'delivery.case_created':
            addCaseToList(message.data.case);
            break;
        case 'delivery.alert_sent':
            showAlertNotification(message.data.alert);
            break;
        case 'backend.log_source_registered':
            updateSourceList(message.data.source);
            break;
    }
};

// Subscribe to channels
ws.send(JSON.stringify({
    action: 'subscribe',
    channels: ['delivery.case_created', 'delivery.alert_sent']
}));
```

---

## CTI Plugin Architecture

### Overview

The CTI Plugin Manager provides a universal interface for integrating multiple threat intelligence sources. Each plugin implements a standard interface, allowing seamless addition of new sources.

### Plugin Types

| Plugin | Type | Priority | Description |
|--------|------|----------|-------------|
| OTX | Community | 50 | AlienVault Open Threat Exchange |
| ThreatFox | Community | 50 | Abuse.ch ThreatFox |
| CrowdStrike | Commercial | 80 | CrowdStrike Falcon Intelligence |
| OpenCTI | Internal | 60 | Internal threat intelligence platform |

### Plugin Interface

Every CTI plugin must implement:

```python
class CTIPlugin:
    async def fetch_indicators(self, since: datetime, limit: int) -> List[CTIIndicator]
    async def health_check(self) -> Dict[str, Any]
    def get_source_type(self) -> str  # 'community', 'commercial', 'internal'
    def get_priority(self) -> int     # 0-100, higher = more trusted
```

### Adding New CTI Plugins

1. Create plugin class implementing `CTISourcePlugin` interface
2. Register in Ingestion Engine startup:
```python
if os.getenv('YOUR_PLUGIN_KEY'):
    plugin = YourPlugin(config={...})
    cti_plugin_manager.register_plugin(plugin)
```
3. Plugin automatically appears in `/cti/connectors` endpoint

### CTI Data Flow

```
1. Plugin fetches indicators from source
2. Indicators normalized to CTIIndicator format
3. Deduplication based on indicator value
4. Priority-based conflict resolution
5. Publish to backend for rule generation
6. Frontend displays via API endpoints
```

---

## Example Use Cases

### 1. "Display All Threat Indicators"

```javascript
// Fetch from all CTI sources
const response = await fetch('http://localhost:8003/cti/manual_update', {
    method: 'POST',
    body: JSON.stringify({ source: 'all', since_days: 7 })
});

// Display in threat dashboard
const indicators = await response.json();
renderThreatDashboard(indicators);
```

### 2. "Monitor CrowdStrike Intelligence"

```javascript
// Check CrowdStrike plugin health
const status = await fetch('http://localhost:8003/cti/status');
const crowdstrikeHealth = status.health.crowdstrike;

if (crowdstrikeHealth.healthy) {
    // Fetch CrowdStrike-specific indicators
    const response = await fetch('http://localhost:8003/cti/manual_update', {
        method: 'POST',
        body: JSON.stringify({ source: 'crowdstrike', limit: 500 })
    });
}
```

### 3. "Assess Ransomware Detection Capability"

```javascript
// Check if we can detect ransomware
const fidelity = await fetch('http://localhost:8002/api/detection/fidelity', {
    method: 'POST',
    body: JSON.stringify({ attack_types: ['ransomware'] })
});

if (fidelity.attack_fidelity.ransomware.confidence < 80) {
    // Get recommendations
    const recommendations = await fetch('http://localhost:8002/api/correlation/recommendations', {
        method: 'POST',
        body: JSON.stringify({ target_attacks: ['ransomware'] })
    });

    showRecommendations(recommendations);
}
```

### 4. "Investigate Security Alert"

```javascript
async function investigateAlert(alertId) {
    // Start investigation workflow
    const workflow = await fetch('http://localhost:8005/api/workflows/start', {
        method: 'POST',
        body: JSON.stringify({
            template: 'auto_investigation',
            parameters: { alert_id: alertId }
        })
    });

    // Get investigation context
    const context = await fetch(`http://localhost:8005/api/alerts/${alertId}/context`);

    // Create investigation case
    const investigation = await fetch('http://localhost:8005/api/investigations', {
        method: 'POST',
        body: JSON.stringify({
            title: `Investigation: ${context.alert.title}`,
            alert_ids: [alertId],
            auto_enrich: true
        })
    });

    return investigation;
}
```

---

## Error Handling

All endpoints follow consistent error response format:

```json
{
    "error": "Error message",
    "type": "ErrorType",
    "details": {},
    "timestamp": "2025-10-03T10:00:00Z"
}
```

### Common HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (when auth enabled)
- `403` - Forbidden
- `404` - Not Found
- `429` - Too Many Requests
- `500` - Internal Server Error
- `503` - Service Unavailable

---

## Rate Limiting

Currently no rate limiting is implemented. When added, expect:
- Default: 1000 requests/minute per IP
- Burst: 100 requests/second
- Headers: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`

---

## Versioning

API version is included in response headers:
- `X-API-Version`: `2.0.0`
- `X-Engine-Version`: Engine-specific version

Future versions will support versioned endpoints:
- `/api/v2/...` - Current version
- `/api/v3/...` - Future version

---

## Development Tools

### Postman Collection

A Postman collection is available at `/docs/postman/siemless-v2.postman_collection.json`

### OpenAPI/Swagger

OpenAPI specifications for each engine:
- Intelligence: `http://localhost:8001/openapi.json`
- Backend: `http://localhost:8002/openapi.json`
- Ingestion: `http://localhost:8003/openapi.json`
- Contextualization: `http://localhost:8004/openapi.json`
- Delivery: `http://localhost:8005/openapi.json`

### Health Check Script

```bash
#!/bin/bash
# Check all engines
for port in 8001 8002 8003 8004 8005; do
    echo "Checking engine on port $port"
    curl -s http://localhost:$port/health | jq .status
done
```

---

## Support and Documentation

- GitHub: https://github.com/yourusername/siemless-v2
- Documentation: This file
- Issues: GitHub Issues
- Community: Discord/Slack channel

---

## Appendix: Quick Reference

### Most Used Endpoints

| Purpose | Method | Endpoint | Engine |
|---------|--------|----------|--------|
| Check CTI Status | GET | `/cti/status` | Ingestion (8003) |
| Trigger CTI Update | POST | `/cti/manual_update` | Ingestion (8003) |
| List Log Sources | GET | `/api/log-sources/status` | Backend (8002) |
| Check Detection Coverage | GET | `/api/detection/coverage` | Backend (8002) |
| Create Case | POST | `/api/cases` | Delivery (8005) |
| Get Dashboard | GET | `/api/dashboard/overview` | Delivery (8005) |
| List Alerts | GET | `/api/alerts` | Delivery (8005) |
| Start Workflow | POST | `/api/workflows/start` | Delivery (8005) |

### Environment Variables

```env
# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# PostgreSQL
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=siemless_v2
POSTGRES_USER=siemless
POSTGRES_PASSWORD=siemless123

# CTI Plugins
OTX_API_KEY=your_otx_key
THREATFOX_API_KEY=your_threatfox_key
CROWDSTRIKE_CLIENT_ID=your_cs_client_id
CROWDSTRIKE_CLIENT_SECRET=your_cs_secret
OPENCTI_URL=http://opencti:8080
OPENCTI_TOKEN=your_opencti_token

# AI Models (for Intelligence Engine)
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GEMINI_API_KEY=your_gemini_key
```

---

*Last Updated: October 3, 2025*
*SIEMLess v2.0 - Intelligence Foundation Platform*