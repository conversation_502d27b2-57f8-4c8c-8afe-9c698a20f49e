import type { Meta, StoryObj } from '@storybook/react'
import { DashboardLayout } from './DashboardLayout'
import { Model, IJsonModel } from 'flexlayout-react'

const meta: Meta<typeof DashboardLayout> = {
  title: 'Layouts/DashboardLayout',
  component: DashboardLayout,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Main dashboard layout using FlexLayout for drag-and-drop widget management with tabs, splits, and docking.'
      }
    }
  },
  decorators: [
    (Story) => (
      <div style={{ height: '100vh', width: '100vw' }}>
        <Story />
      </div>
    )
  ],
  tags: ['autodocs'],
  argTypes: {
    onModelChange: {
      action: 'model-changed',
      description: 'Callback when layout model changes'
    }
  }
}

export default meta
type Story = StoryObj<typeof meta>

// Default story with standard layout
export const Default: Story = {
  args: {}
}

// Analyst-focused layout
const analystLayout: IJsonModel = {
  global: {
    tabEnableClose: true,
    tabEnableFloat: true,
    tabEnableRename: true,
    tabSetEnableMaximize: true,
    tabSetEnableDrag: true,
    tabSetEnableClose: true,
  },
  borders: [],
  layout: {
    type: "row",
    weight: 100,
    children: [
      {
        type: "tabset",
        weight: 60,
        children: [
          {
            type: "tab",
            name: "Live Alerts",
            component: "AlertQueue",
            config: { refreshInterval: 3000 }
          },
          {
            type: "tab",
            name: "Active Cases",
            component: "CaseTimeline",
            config: {}
          }
        ]
      },
      {
        type: "column",
        weight: 40,
        children: [
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Entity Graph",
                component: "EntityGraph",
                config: {}
              }
            ]
          },
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Pattern Matches",
                component: "PatternLibrary",
                config: {}
              }
            ]
          }
        ]
      }
    ]
  }
}

export const AnalystDashboard: Story = {
  args: {
    initialModel: analystLayout,
    onModelChange: (model) => {
      console.log('Layout changed:', model.toJson())
    }
  },
  parameters: {
    docs: {
      description: {
        story: 'Layout optimized for security analysts with focus on alerts and case management'
      }
    }
  }
}

// Engineer-focused layout
const engineerLayout: IJsonModel = {
  global: {
    tabEnableClose: true,
    tabEnableFloat: true,
    tabEnableRename: true,
    tabSetEnableMaximize: true,
    tabSetEnableDrag: true,
    tabSetEnableClose: true,
  },
  borders: [],
  layout: {
    type: "row",
    weight: 100,
    children: [
      {
        type: "tabset",
        weight: 70,
        children: [
          {
            type: "tab",
            name: "Pattern Library",
            component: "PatternLibrary",
            config: {}
          },
          {
            type: "tab",
            name: "CTI Rules",
            component: "CTIFeeds",
            config: {}
          }
        ]
      },
      {
        type: "tabset",
        weight: 30,
        children: [
          {
            type: "tab",
            name: "Performance Metrics",
            component: "MetricsDashboard",
            config: {}
          }
        ]
      }
    ]
  }
}

export const EngineerDashboard: Story = {
  args: {
    initialModel: engineerLayout
  },
  parameters: {
    docs: {
      description: {
        story: 'Layout optimized for detection engineers focusing on pattern management and rule creation'
      }
    }
  }
}

// Executive overview layout
const executiveLayout: IJsonModel = {
  global: {
    tabEnableClose: false,
    tabEnableFloat: false,
    tabEnableRename: false,
    tabSetEnableMaximize: false,
    tabSetEnableDrag: false,
    tabSetEnableClose: false,
  },
  borders: [],
  layout: {
    type: "row",
    weight: 100,
    children: [
      {
        type: "tabset",
        weight: 100,
        children: [
          {
            type: "tab",
            name: "Executive Dashboard",
            component: "MetricsDashboard",
            config: { view: 'executive' }
          }
        ]
      }
    ]
  }
}

export const ExecutiveDashboard: Story = {
  args: {
    initialModel: executiveLayout
  },
  parameters: {
    docs: {
      description: {
        story: 'Simplified executive view with locked layout showing high-level metrics and KPIs'
      }
    }
  }
}