#!/usr/bin/env python3
"""
Test Schema Detection Flow
Tests: Schema detection -> AI mapping generation -> Deterministic extraction
"""

import asyncio
import json
import psycopg2
import redis
from uuid import uuid4

def get_one_fortinet_log():
    """Fetch one Fortinet log from warm_storage"""
    conn = psycopg2.connect(
        host='localhost',
        port=5433,
        database='siemless_v2',
        user='siemless',
        password='siemless123'
    )
    cursor = conn.cursor()

    # Get one log that has Fortinet-like structure
    cursor.execute("""
        SELECT storage_id, data
        FROM warm_storage
        WHERE data::text LIKE '%devid%'
           OR data::text LIKE '%fortinet%'
           OR data::text LIKE '%fortigate%'
        LIMIT 1
    """)

    result = cursor.fetchone()
    cursor.close()
    conn.close()

    if result:
        return {'log_id': str(result[0]), 'log_data': result[1]}
    return None

async def test_schema_detection():
    """Test the complete schema detection flow"""
    print("=" * 80)
    print("SCHEMA DETECTION FLOW TEST")
    print("=" * 80)

    # Step 1: Get one Fortinet log
    print("\n[1/5] Fetching Fortinet log from warm_storage...")
    log_entry = get_one_fortinet_log()

    if not log_entry:
        print("ERROR: No Fortinet logs found in warm_storage")
        return

    log_id = log_entry['log_id']
    log_data = log_entry['log_data']

    print(f"   Found log: {log_id}")
    print(f"   Log size: {len(json.dumps(log_data))} bytes")

    # Step 2: Check if schema is already known
    print("\n[2/5] Checking if schema is known...")
    from engines.contextualization.log_schema_detector import generate_schema_hash

    schema_hash = generate_schema_hash(log_data)
    print(f"   Schema hash: {schema_hash[:16]}...")

    conn = psycopg2.connect(
        host='localhost',
        port=5433,
        database='siemless_v2',
        user='siemless',
        password='siemless123'
    )
    cursor = conn.cursor()
    cursor.execute("SELECT schema_id, schema_name FROM log_schemas WHERE schema_hash = %s", (schema_hash,))
    existing = cursor.fetchone()
    cursor.close()
    conn.close()

    if existing:
        print(f"   Schema KNOWN: {existing[1]} (ID: {existing[0]})")
        print("   This log would be processed deterministically (FREE)")
        return
    else:
        print("   Schema NEW: Will require AI mapping generation ($0.008)")

    # Step 3: Send to Contextualization Engine
    print("\n[3/5] Sending log to Contextualization Engine...")
    r = redis.Redis(host='localhost', port=6380, decode_responses=True)

    message = {
        'log': log_data,
        'log_id': log_id,
        'pattern_type': 'firewall',
        'entity_hints': []
    }

    r.publish('contextualization.process_log', json.dumps(message))
    print("   Published to contextualization.process_log")

    # Step 4: Monitor for AI mapping request
    print("\n[4/5] Monitoring for AI mapping request...")
    print("   Subscribing to intelligence.generate_log_mapping...")

    pubsub = r.pubsub()
    pubsub.subscribe('intelligence.generate_log_mapping')

    mapping_request_seen = False
    timeout = 10
    start_time = asyncio.get_event_loop().time()

    for message in pubsub.listen():
        if message['type'] == 'message':
            data = json.loads(message['data'])
            print(f"   AI Mapping Request received!")
            print(f"   Request ID: {data.get('request_id')}")
            print(f"   Schema Hash: {data.get('schema_hash', '')[:16]}...")
            print(f"   Models: {data.get('models')}")
            print(f"   Response Channel: {data.get('response_channel')}")
            mapping_request_seen = True
            break

        # Timeout check
        if asyncio.get_event_loop().time() - start_time > timeout:
            print(f"   TIMEOUT: No mapping request seen after {timeout}s")
            break

    pubsub.close()

    # Step 5: Verify results
    print("\n[5/5] Test Results")
    print("=" * 80)

    if mapping_request_seen:
        print("SUCCESS: Schema detection flow working correctly!")
        print("")
        print("Expected Flow:")
        print("  1. Contextualization detects NEW schema")
        print("  2. Requests AI mapping from Intelligence Engine")
        print("  3. Intelligence generates mapping ($0.008)")
        print("  4. Mapping stored in database")
        print("  5. Future logs of same schema: FREE deterministic extraction")
        print("")
        print(f"Cost for this schema: $0.008 (one-time)")
        print(f"Future cost for same schema: $0.00 (deterministic)")
    else:
        print("FAILED: Schema detection flow did not trigger AI mapping request")
        print("Check:")
        print("  - Contextualization Engine is running")
        print("  - Intelligence Engine is running")
        print("  - Redis subscriptions are working")

    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(test_schema_detection())
