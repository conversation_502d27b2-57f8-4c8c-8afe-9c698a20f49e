import { useQuery, UseQueryResult } from '@tanstack/react-query'
import { alertAPI, AlertListParams, AlertListResponse } from '../api/alerts'

/**
 * React Query hook for fetching alerts
 * Automatically refetches every 30 seconds for real-time updates
 *
 * @example
 * const { data, isLoading, error } = useAlerts({ status: 'open', limit: 50 })
 */
export function useAlerts(params?: AlertListParams): UseQueryResult<AlertListResponse> {
  return useQuery({
    queryKey: ['alerts', params],
    queryFn: async () => {
      const response = await alertAPI.getAlerts(params)
      return response.data
    },
    refetchInterval: 30000, // Refresh every 30 seconds
    staleTime: 10000, // Consider data stale after 10 seconds
  })
}

export default useAlerts
