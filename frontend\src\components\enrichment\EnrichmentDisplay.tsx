/**
 * Enrichment Display Component
 * Displays three-layer entity enrichment in a consistent format
 */

import React from 'react'
import { MapPin, Shield, Database, Server, AlertTriangle, Globe, User } from 'lucide-react'
import type { Entity, GeolocationData, CTIMatch } from '../../types/api'

interface EnrichmentDisplayProps {
  entity: Entity
  layers?: ('layer1' | 'layer2' | 'layer3')[]
  compact?: boolean
}

export const EnrichmentDisplay: React.FC<EnrichmentDisplayProps> = ({
  entity,
  layers = ['layer1', 'layer2'],  // Default: show Layer 1 & 2
  compact = false
}) => {
  const { enrichments, threat_score, is_threat } = entity

  return (
    <div className={`space-y-4 ${compact ? 'text-sm' : ''}`}>
      {/* Threat Score Header */}
      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
        <div>
          <h3 className="font-semibold text-gray-900">Threat Assessment</h3>
          <p className="text-sm text-gray-600">
            {is_threat ? '⚠️ Known Threat' : '✓ Clean'}
          </p>
        </div>
        <ThreatScoreBadge score={threat_score} />
      </div>

      {/* Layer 1: Business & Technical Context */}
      {layers.includes('layer1') && (
        <div className="border-l-4 border-blue-500 pl-4">
          <h4 className="font-semibold mb-3 flex items-center gap-2">
            <Database size={16} className="text-blue-600" />
            Business Context
          </h4>

          {/* Geolocation */}
          {enrichments.geolocation && (
            <GeolocationCard data={enrichments.geolocation} compact={compact} />
          )}

          {/* Asset Info */}
          {enrichments.asset_info && (
            <AssetInfoCard data={enrichments.asset_info} compact={compact} />
          )}

          {/* Network Info */}
          {enrichments.network_info && (
            <NetworkInfoCard data={enrichments.network_info} compact={compact} />
          )}

          {/* User Info */}
          {enrichments.user_info && (
            <UserInfoCard data={enrichments.user_info} compact={compact} />
          )}
        </div>
      )}

      {/* Layer 2: CTI Threat Intelligence */}
      {layers.includes('layer2') && enrichments.cti_threat_intelligence && (
        <div className="border-l-4 border-red-500 pl-4">
          <h4 className="font-semibold mb-3 flex items-center gap-2">
            <Shield size={16} className="text-red-600" />
            Threat Intelligence
          </h4>
          <CTIMatchCard match={enrichments.cti_threat_intelligence} compact={compact} />
        </div>
      )}

      {/* Layer 3: Vendor Context (shown separately, loaded on-demand) */}
    </div>
  )
}

// Threat Score Badge
export const ThreatScoreBadge: React.FC<{ score: number }> = ({ score }) => {
  const getColor = (score: number) => {
    if (score >= 80) return 'bg-red-500'
    if (score >= 60) return 'bg-orange-500'
    if (score >= 40) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  return (
    <div className="text-center">
      <div className={`${getColor(score)} text-white rounded-full w-14 h-14 flex items-center justify-center text-xl font-bold`}>
        {score}
      </div>
      <p className="text-xs text-gray-600 mt-1">Threat Score</p>
    </div>
  )
}

// Geolocation Card
export const GeolocationCard: React.FC<{
  data: GeolocationData
  compact?: boolean
}> = ({ data, compact }) => (
  <div className={`bg-white rounded-lg border p-3 mb-3 ${compact ? 'text-xs' : 'text-sm'}`}>
    <h5 className="font-medium mb-2 flex items-center gap-2">
      <MapPin size={14} className="text-blue-600" />
      Location
    </h5>
    <div className="space-y-1 text-gray-700">
      <p>
        <span className="text-gray-500">Country:</span>{' '}
        <span className="font-medium">{data.country}</span> ({data.country_code})
      </p>
      {data.city && (
        <p>
          <span className="text-gray-500">City:</span> {data.city}
          {data.region && `, ${data.region}`}
        </p>
      )}
      {data.isp && (
        <p>
          <span className="text-gray-500">ISP:</span> {data.isp}
        </p>
      )}
      {data.asn && (
        <p>
          <span className="text-gray-500">ASN:</span> {data.asn}
        </p>
      )}
      <div className="flex gap-1 mt-2">
        {data.is_vpn && (
          <span className="px-2 py-0.5 bg-yellow-100 text-yellow-800 rounded text-xs">VPN</span>
        )}
        {data.is_proxy && (
          <span className="px-2 py-0.5 bg-orange-100 text-orange-800 rounded text-xs">Proxy</span>
        )}
        {data.is_tor && (
          <span className="px-2 py-0.5 bg-red-100 text-red-800 rounded text-xs">TOR</span>
        )}
        {data.is_datacenter && (
          <span className="px-2 py-0.5 bg-blue-100 text-blue-800 rounded text-xs">Datacenter</span>
        )}
      </div>
    </div>
  </div>
)

// Asset Info Card
export const AssetInfoCard: React.FC<{
  data: any
  compact?: boolean
}> = ({ data, compact }) => (
  <div className={`bg-white rounded-lg border p-3 mb-3 ${compact ? 'text-xs' : 'text-sm'}`}>
    <h5 className="font-medium mb-2 flex items-center gap-2">
      <Server size={14} className="text-blue-600" />
      Asset Information
    </h5>
    <div className="space-y-1 text-gray-700">
      <p>
        <span className="text-gray-500">Tier:</span>{' '}
        <span className={`font-medium ${
          data.asset_tier === 'critical' ? 'text-red-600' :
          data.asset_tier === 'standard' ? 'text-blue-600' : 'text-gray-600'
        }`}>
          {data.asset_tier?.toUpperCase()}
        </span>
      </p>
      <p>
        <span className="text-gray-500">Security Zone:</span>{' '}
        <span className="font-medium">{data.security_zone?.toUpperCase()}</span>
      </p>
      {data.business_unit && (
        <p>
          <span className="text-gray-500">Business Unit:</span> {data.business_unit}
        </p>
      )}
      {data.owner && (
        <p>
          <span className="text-gray-500">Owner:</span> {data.owner}
        </p>
      )}
      <p>
        <span className="text-gray-500">Criticality:</span>{' '}
        <span className="font-medium">{data.criticality_score}/100</span>
      </p>
    </div>
  </div>
)

// Network Info Card
export const NetworkInfoCard: React.FC<{
  data: any
  compact?: boolean
}> = ({ data, compact }) => (
  <div className={`bg-white rounded-lg border p-3 mb-3 ${compact ? 'text-xs' : 'text-sm'}`}>
    <h5 className="font-medium mb-2 flex items-center gap-2">
      <Globe size={14} className="text-blue-600" />
      Network Information
    </h5>
    <div className="space-y-1 text-gray-700">
      {data.whois_data && (
        <>
          {data.whois_data.registrar && (
            <p>
              <span className="text-gray-500">Registrar:</span> {data.whois_data.registrar}
            </p>
          )}
          {data.whois_data.registrant && (
            <p>
              <span className="text-gray-500">Registrant:</span> {data.whois_data.registrant}
            </p>
          )}
        </>
      )}
      {data.reverse_dns && (
        <p>
          <span className="text-gray-500">rDNS:</span> {data.reverse_dns}
        </p>
      )}
    </div>
  </div>
)

// User Info Card
export const UserInfoCard: React.FC<{
  data: any
  compact?: boolean
}> = ({ data, compact }) => (
  <div className={`bg-white rounded-lg border p-3 mb-3 ${compact ? 'text-xs' : 'text-sm'}`}>
    <h5 className="font-medium mb-2 flex items-center gap-2">
      <User size={14} className="text-blue-600" />
      User Information
    </h5>
    <div className="space-y-1 text-gray-700">
      {data.display_name && (
        <p>
          <span className="text-gray-500">Name:</span> {data.display_name}
        </p>
      )}
      {data.email && (
        <p>
          <span className="text-gray-500">Email:</span> {data.email}
        </p>
      )}
      {data.department && (
        <p>
          <span className="text-gray-500">Department:</span> {data.department}
        </p>
      )}
      {data.title && (
        <p>
          <span className="text-gray-500">Title:</span> {data.title}
        </p>
      )}
      <p>
        <span className="text-gray-500">Status:</span>{' '}
        <span className={`font-medium ${
          data.account_status === 'active' ? 'text-green-600' : 'text-red-600'
        }`}>
          {data.account_status?.toUpperCase()}
        </span>
      </p>
      <p>
        <span className="text-gray-500">Privilege:</span>{' '}
        <span className={`font-medium ${
          data.privilege_level === 'admin' ? 'text-red-600' : 'text-blue-600'
        }`}>
          {data.privilege_level?.toUpperCase()}
        </span>
      </p>
    </div>
  </div>
)

// CTI Match Card (Layer 2)
export const CTIMatchCard: React.FC<{
  match: CTIMatch
  compact?: boolean
}> = ({ match, compact }) => (
  <div className={`bg-red-50 rounded-lg border-2 border-red-200 p-3 ${compact ? 'text-xs' : 'text-sm'}`}>
    <div className="flex items-center justify-between mb-2">
      <h5 className="font-medium text-red-800 flex items-center gap-2">
        <AlertTriangle size={14} />
        CTI Match
      </h5>
      <span className="px-2 py-1 bg-red-600 text-white rounded text-xs font-medium">
        {match.source.toUpperCase()}
      </span>
    </div>
    <div className="space-y-1 text-gray-700">
      <p>
        <span className="text-gray-600">Match Type:</span>{' '}
        <span className="font-medium">{match.match_type}</span>
      </p>
      <p>
        <span className="text-gray-600">Confidence:</span>{' '}
        <span className="font-medium">{match.confidence}%</span>
      </p>
      {match.threat_actor && (
        <p>
          <span className="text-gray-600">Threat Actor:</span>{' '}
          <span className="font-medium text-red-700">{match.threat_actor}</span>
        </p>
      )}
      {match.campaign && (
        <p>
          <span className="text-gray-600">Campaign:</span>{' '}
          <span className="font-medium">{match.campaign}</span>
        </p>
      )}
      {match.malware_family && (
        <p>
          <span className="text-gray-600">Malware:</span>{' '}
          <span className="font-medium">{match.malware_family}</span>
        </p>
      )}
      <div className="flex flex-wrap gap-1 mt-2">
        {match.tags.map((tag) => (
          <span key={tag} className="px-2 py-0.5 bg-red-100 text-red-800 rounded text-xs">
            {tag}
          </span>
        ))}
      </div>
    </div>
  </div>
)
