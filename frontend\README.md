# SIEMLess v2.0 Frontend

A modern, widget-based security operations dashboard built with React, TypeScript, Storybook, FlexLayout, and AG-Grid.

## Technology Stack

- **React 18** - UI framework
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Storybook** - Component development and documentation
- **FlexLayout** - Advanced drag-and-drop layout management
- **AG-Grid** - Enterprise-grade data grids
- **Tailwind CSS** - Utility-first styling
- **Recharts** - Data visualization
- **Lucide React** - Icons

## Quick Start

### Prerequisites

- Node.js 18+ and npm 9+
- Backend services running (see main README)

### Installation

```bash
cd frontend
npm install
```

### Development

```bash
# Start development server
npm run dev
# App runs on http://localhost:3000

# Start Storybook for component development
npm run storybook
# Storybook runs on http://localhost:6006
```

### Build

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## Project Structure

```
frontend/
├── src/
│   ├── layouts/           # FlexLayout dashboard configurations
│   │   └── DashboardLayout.tsx
│   ├── widgets/           # Widget components
│   │   ├── AlertQueue.tsx      # AG-Grid alert table
│   │   ├── PatternLibrary.tsx  # AG-Grid pattern table
│   │   ├── EntityGraph.tsx     # Entity relationship viz
│   │   ├── CTIFeeds.tsx       # CTI feed status
│   │   ├── CaseTimeline.tsx   # Case management
│   │   └── MetricsDashboard.tsx # KPI metrics
│   ├── components/        # Reusable components
│   ├── hooks/            # Custom React hooks
│   ├── utils/            # Utility functions
│   ├── api/              # API client
│   ├── stores/           # State management (Zustand)
│   ├── types/            # TypeScript types
│   ├── App.tsx           # Main app component
│   ├── main.tsx          # Entry point
│   └── index.css         # Global styles
├── .storybook/           # Storybook configuration
├── public/               # Static assets
└── package.json
```

## Key Features

### 1. FlexLayout Dashboard

- **Drag & Drop**: Rearrange widgets by dragging tabs
- **Docking**: Dock widgets to any side
- **Tabsets**: Group related widgets in tabs
- **Maximize**: Full-screen any widget
- **Save Layouts**: Persist user layouts

### 2. AG-Grid Data Tables

- **Virtual Scrolling**: Handle millions of rows
- **Advanced Filtering**: Column filters and quick filter
- **Sorting**: Multi-column sorting
- **Cell Renderers**: Custom cell components
- **Row Selection**: Single and multi-select
- **Export**: CSV/Excel export capabilities

### 3. Widget System

Each widget is:
- **Lazy Loaded**: Better performance
- **Self-Contained**: Independent state
- **Configurable**: Props for customization
- **Responsive**: Adapts to container size
- **Real-Time**: WebSocket/polling updates

## Available Widgets

### Alert Queue
Real-time security alert monitoring with severity levels, confidence scores, and entity extraction.

```tsx
<AlertQueue
  refreshInterval={5000}
  onAlertSelect={(alert) => handleAlert(alert)}
/>
```

### Pattern Library
AI-crystallized patterns with usage statistics and cost savings metrics.

```tsx
<PatternLibrary
  onPatternSelect={(pattern) => viewPattern(pattern)}
/>
```

### Entity Graph
Interactive entity relationship visualization.

```tsx
<EntityGraph
  entityId="user-123"
/>
```

### CTI Feeds
Cyber threat intelligence feed monitoring.

```tsx
<CTIFeeds />
```

### Case Timeline
Case management workflow visualization.

```tsx
<CaseTimeline
  caseId="case-456"
/>
```

### Metrics Dashboard
KPI metrics and performance indicators.

```tsx
<MetricsDashboard
  view="executive"
/>
```

## Storybook Usage

Storybook provides:
- **Component Gallery**: Browse all widgets
- **Interactive Props**: Test different configurations
- **Documentation**: Auto-generated from TypeScript
- **Visual Testing**: Different states and edge cases

### View Stories

```bash
npm run storybook
```

### Build Storybook

```bash
npm run build-storybook
```

## Layout Customization

### Creating Custom Layouts

```typescript
const customLayout: IJsonModel = {
  global: {
    tabEnableClose: true,
    tabEnableFloat: true,
  },
  layout: {
    type: "row",
    children: [
      {
        type: "tabset",
        children: [
          {
            type: "tab",
            name: "My Widget",
            component: "AlertQueue",
            config: { refreshInterval: 3000 }
          }
        ]
      }
    ]
  }
}
```

### Saving User Layouts

```typescript
// Save layout
const saveLayout = () => {
  const json = model.toJson()
  localStorage.setItem('userLayout', JSON.stringify(json))
}

// Load layout
const loadLayout = () => {
  const saved = localStorage.getItem('userLayout')
  if (saved) {
    return Model.fromJson(JSON.parse(saved))
  }
  return Model.fromJson(defaultLayout)
}
```

## AG-Grid Configuration

### Custom Cell Renderers

```typescript
const SeverityRenderer: React.FC<ICellRendererParams> = ({ value }) => {
  return (
    <div className={`severity-${value}`}>
      {value}
    </div>
  )
}

const columnDefs: ColDef[] = [
  {
    field: 'severity',
    cellRenderer: SeverityRenderer
  }
]
```

### Enterprise Features

With AG-Grid Enterprise license:
- Row grouping
- Pivoting
- Excel export
- Advanced filtering
- Master/detail grids
- Server-side row model

## API Integration

### Connecting to Backend

```typescript
// src/api/client.ts
import axios from 'axios'

const apiClient = axios.create({
  baseURL: '/api',  // Proxied to http://localhost:8005
  headers: {
    'Content-Type': 'application/json'
  }
})

// Add auth token
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})
```

### Real-time Updates

```typescript
// WebSocket connection
const ws = new WebSocket('ws://localhost:8005/ws')

ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  // Update widget state
}
```

## Performance Optimization

### Code Splitting

Widgets are lazy loaded:
```typescript
const AlertQueue = lazy(() => import('./widgets/AlertQueue'))
```

### Memoization

```typescript
const MemoizedWidget = React.memo(Widget, (prev, next) => {
  return prev.id === next.id
})
```

### Virtual Scrolling

AG-Grid automatically virtualizes large datasets.

## Testing

```bash
# Run tests
npm run test

# Run tests with UI
npm run test:ui

# Coverage report
npm run test:coverage
```

## Deployment

### Docker Build

```dockerfile
FROM node:18-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
```

### Environment Variables

```env
VITE_API_URL=http://localhost:8005
VITE_WS_URL=ws://localhost:8005
VITE_AUTH_URL=http://localhost:8080
```

## Troubleshooting

### Common Issues

1. **Storybook won't start**
   ```bash
   rm -rf node_modules .storybook/cache
   npm install
   npm run storybook
   ```

2. **AG-Grid styles missing**
   Ensure imports in main component:
   ```typescript
   import 'ag-grid-community/styles/ag-grid.css'
   import 'ag-grid-community/styles/ag-theme-alpine.css'
   ```

3. **FlexLayout not rendering**
   Check container has explicit height:
   ```css
   .layout-container {
     height: 100vh;
     width: 100vw;
   }
   ```

## Next Steps

1. **Install dependencies**: `npm install`
2. **Start Storybook**: `npm run storybook`
3. **Explore components**: Browse widget stories
4. **Start development**: `npm run dev`
5. **Customize layouts**: Modify DashboardLayout.tsx

## License

Part of SIEMLess v2.0 Intelligence Foundation Platform