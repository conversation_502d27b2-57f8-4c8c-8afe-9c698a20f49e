#!/usr/bin/env python3
"""
Test script to deploy a rule to Elastic Security from outside Docker.
This bypasses Docker DNS issues by running on the host machine.
"""

import os
import sys
import json
import base64
import requests
import psycopg2
from psycopg2.extras import RealDictCursor

# Elastic configuration from environment
ELASTIC_CLOUD_ID = os.getenv('ELASTIC_CLOUD_ID', 'Dacta_Global:YXAtc291dGhlYXN0LTEuYXdzLmZvdW5kLmlvJDZiNjdmNDdhZGY4MjQ1ZGQ4NzA1ZWY3ZjNlM2UxNTRmJDQ1MTM1MDg0YzhmZjRkMGJhMWFhYWZmNmU2MmZhNDQz')
ELASTIC_API_KEY = os.getenv('ELASTIC_API_KEY', 'OTk0Y2Jaa0JfVndFYTRsdERnRnk6OEE2d2F2TnZyRXZMNUJFeXhnUXAzQQ==')

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'siemless_v2',
    'user': 'siemless',
    'password': 'siemless123'
}

def decode_cloud_id(cloud_id: str) -> str:
    """Decode Elastic Cloud ID to get Kibana URL"""
    parts = cloud_id.split(':')
    if len(parts) != 2:
        raise ValueError(f"Invalid Cloud ID format: {cloud_id}")

    encoded = parts[1]
    decoded = base64.b64decode(encoded).decode('utf-8')
    host_parts = decoded.split('$')
    kibana_host = host_parts[0]
    kibana_url = f"https://{kibana_host}:9243"

    return kibana_url

def get_rule_from_db(rule_id: str) -> dict:
    """Fetch rule from PostgreSQL database"""
    conn = psycopg2.connect(**DB_CONFIG, cursor_factory=RealDictCursor)
    cursor = conn.cursor()

    query = "SELECT rule_data FROM detection_rules WHERE rule_id = %s"
    cursor.execute(query, (rule_id,))
    row = cursor.fetchone()

    cursor.close()
    conn.close()

    if not row:
        raise ValueError(f"Rule {rule_id} not found in database")

    return row['rule_data']

def build_elastic_payload(rule_data: dict, enabled: bool = False) -> dict:
    """Build Elastic Security detection rule payload"""
    # Map severity levels
    severity_map = {
        'low': 'low',
        'medium': 'medium',
        'high': 'high',
        'critical': 'critical',
        'informational': 'low'
    }

    risk_score_map = {
        'low': 21,
        'medium': 47,
        'high': 73,
        'critical': 99,
        'informational': 21
    }

    severity = rule_data.get('level') or rule_data.get('severity', 'medium')
    ioc_value = rule_data.get('ioc_value', '')
    ioc_type = rule_data.get('ioc_type', 'unknown')

    # Build KQL query based on IOC type
    if ioc_type == 'ipv4-addr':
        query = f'source.ip: "{ioc_value}" or destination.ip: "{ioc_value}"'
    elif ioc_type in ['domain', 'domain-name']:
        query = f'dns.question.name: "{ioc_value}" or url.domain: "{ioc_value}"'
    elif ioc_type == 'url':
        query = f'url.full: "{ioc_value}"'
    elif 'hash' in ioc_type.lower():
        query = f'file.hash.*: "{ioc_value}"'
    else:
        query = f'*{ioc_value}*'

    payload = {
        'type': 'query',
        'name': rule_data.get('name') or rule_data.get('title', 'Unknown Rule'),
        'description': rule_data.get('description', f"Detection rule for {ioc_type}: {ioc_value}"),
        'query': query,
        'language': 'kuery',
        'risk_score': risk_score_map.get(severity, 47),
        'severity': severity_map.get(severity, 'medium'),
        'enabled': enabled,  # User controls this
        'interval': '5m',
        'from': 'now-6m',
        'to': 'now',
        'index': ['logs-*', 'winlogbeat-*', 'filebeat-*'],
        'tags': rule_data.get('tags', []) + [f'cti:{rule_data.get("source", "unknown")}'],
        'references': [rule_data.get('cti_reference', '').strip()] if rule_data.get('cti_reference') else [],
    }

    # Add MITRE ATT&CK if available
    if rule_data.get('mitre_techniques'):
        payload['threat'] = [{
            'framework': 'MITRE ATT&CK',
            'technique': [{
                'id': tech,
                'name': tech,
                'reference': f'https://attack.mitre.org/techniques/{tech}/'
            } for tech in rule_data.get('mitre_techniques', [])]
        }]

    return payload

def deploy_to_elastic(rule_id: str, enabled: bool = False):
    """Deploy rule to Elastic Security"""
    print(f"\\n[*] Fetching rule {rule_id} from database...")
    rule_data = get_rule_from_db(rule_id)

    print(f"[*] Rule Name: {rule_data.get('name', 'Unknown')}")
    print(f"[*] IOC Type: {rule_data.get('ioc_type', 'unknown')}")
    print(f"[*] IOC Value: {rule_data.get('ioc_value', 'N/A')}")

    print(f"\\n[*] Decoding Elastic Cloud ID...")
    kibana_url = decode_cloud_id(ELASTIC_CLOUD_ID)
    print(f"[+] Kibana URL: {kibana_url}")

    print(f"\\n[*]  Building Elastic payload (enabled={enabled})...")
    payload = build_elastic_payload(rule_data, enabled=enabled)

    print(f"\\n[*] Deploying to Elastic Security...")
    headers = {
        'Content-Type': 'application/json',
        'kbn-xsrf': 'true',
        'Authorization': f'ApiKey {ELASTIC_API_KEY}'
    }

    url = f"{kibana_url}/api/detection_engine/rules"

    response = requests.post(url, json=payload, headers=headers, timeout=30)

    if response.status_code in [200, 201]:
        result = response.json()
        print(f"\\n[+] SUCCESS! Rule deployed to Elastic Security")
        print(f"   Elastic Rule ID: {result.get('id')}")
        print(f"   Rule Name: {result.get('name')}")
        print(f"   Enabled: {result.get('enabled')}")
        print(f"   Created: {result.get('created_at')}")

        if not enabled:
            print(f"\\n[!]  Rule is DISABLED - user must manually enable in Elastic Security UI")

        return result
    else:
        print(f"\\n[-] FAILED! Status Code: {response.status_code}")
        print(f"   Response: {response.text}")
        raise Exception(f"Deployment failed: {response.text}")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python test_deploy_rule.py <rule_id> [--enable]")
        print("\\nExample:")
        print("  python test_deploy_rule.py 4352d816-4988-49d1-a06c-a68f44f3df5e")
        print("  python test_deploy_rule.py 4352d816-4988-49d1-a06c-a68f44f3df5e --enable")
        sys.exit(1)

    rule_id = sys.argv[1]
    enable = '--enable' in sys.argv

    try:
        deploy_to_elastic(rule_id, enabled=enable)
    except Exception as e:
        print(f"\\n[-] Error: {e}")
        sys.exit(1)
