/**
 * CTI Plugin Zustand Store
 * Manages CTI plugin status and statistics
 */

import { create } from 'zustand'
import { ctiService } from '../api/services'
import type { CTIPluginInfo, CTIStats } from '../types/api'

interface CTIPluginStore {
  // Data
  plugins: CTIPluginInfo[]
  stats: CTIStats | null

  // Loading states
  loading: boolean
  updating: Record<string, boolean>  // Per-plugin update tracking
  polling: boolean

  // Error states
  error: string | null

  // Actions
  fetchStatus: () => Promise<void>
  fetchStats: () => Promise<void>
  startPolling: () => void
  stopPolling: () => void
  triggerUpdate: (source: string, sinceDays?: number) => Promise<void>
  testCredentials: (source: string) => Promise<boolean>
  refreshAll: () => Promise<void>
}

let pollingInterval: NodeJS.Timeout | null = null

export const useCTIStore = create<CTIPluginStore>((set, get) => ({
  // Initial state
  plugins: [],
  stats: null,
  loading: false,
  updating: {},
  polling: false,
  error: null,

  // Fetch CTI plugin status
  fetchStatus: async () => {
    set({ loading: true, error: null })

    try {
      const plugins = await ctiService.getStatus()
      set({
        plugins,
        loading: false
      })
    } catch (error) {
      set({
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load CTI status'
      })
    }
  },

  // Fetch CTI statistics
  fetchStats: async () => {
    try {
      const stats = await ctiService.getStats()
      set({ stats })
    } catch (error) {
      console.error('Failed to load CTI stats:', error)
    }
  },

  // Start polling for updates
  startPolling: () => {
    if (pollingInterval) return

    set({ polling: true })

    // Initial fetch
    get().fetchStatus()
    get().fetchStats()

    // Poll every 30 seconds
    pollingInterval = setInterval(() => {
      get().fetchStatus()
      get().fetchStats()
    }, 30000)
  },

  // Stop polling
  stopPolling: () => {
    if (pollingInterval) {
      clearInterval(pollingInterval)
      pollingInterval = null
    }
    set({ polling: false })
  },

  // Trigger manual update for a specific source
  triggerUpdate: async (source: string, sinceDays: number = 7) => {
    set((state) => ({
      updating: { ...state.updating, [source]: true }
    }))

    try {
      await ctiService.triggerUpdate(source, sinceDays)

      // Wait a bit then refresh status
      setTimeout(() => {
        get().fetchStatus()
        get().fetchStats()
      }, 2000)

    } catch (error) {
      console.error(`Failed to trigger update for ${source}:`, error)
    } finally {
      set((state) => ({
        updating: { ...state.updating, [source]: false }
      }))
    }
  },

  // Test credentials for a specific source
  testCredentials: async (source: string): Promise<boolean> => {
    try {
      const result = await ctiService.testCredentials(source)

      // Refresh status after test
      await get().fetchStatus()

      return result.valid
    } catch (error) {
      console.error(`Failed to test credentials for ${source}:`, error)
      return false
    }
  },

  // Refresh all data
  refreshAll: async () => {
    await Promise.all([
      get().fetchStatus(),
      get().fetchStats()
    ])
  }
}))
