"""
SIEMLess v2.0 - Backend Engine
Ruleset management, CTI processing, storage optimization, and system services
Consolidates: Ruleset Engine + Storage Engine + Cost Optimization + Training Data + Detection
"""

import asyncio
from decimal import Decimal
import json
import uuid
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import sys

# Add parent directory to path for base_engine import
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from base_engine import BaseEngine
from correlation_engine import CorrelationEngine, CorrelationRule, LogSourceType
from log_source_identifier import LogSourceIdentifier
from ioc_filters import IOCFilter
from log_source_quality import LogSourceQualityEngine
from detection_fidelity_calculator import DetectionFidelityCalculator
from correlation_requirements import CorrelationRequirementsEngine
from age_graph_service import AGEGraphService
from mitre_attack_mapper import MITRE<PERSON>ttackMapper
from mitre_http_handlers import MITREHTTPHandlers
from log_retention_policy_engine import LogRetentionPolicyEngine
# NOTE: RuleDeploymentService moved to Ingestion Engine (architectural fix)
import sys
sys.path.append('../intelligence')
try:
    from mitre_ai_intelligence import MITREAIIntelligence
    from mitre_ai_http_handlers import MITREAIHTTPHandlers
    HAS_AI_INTELLIGENCE = True
except ImportError:
    HAS_AI_INTELLIGENCE = False
# from auth_middleware import KeycloakAuthMiddleware  # COMMENTED OUT - Auth disabled for now
from update_scheduler import UpdateScheduler

class BackendEngine(BaseEngine):
    """
    Backend Engine: System services, storage, and ruleset management

    Core Functions:
    - CTI-to-rule automation with test case generation
    - Rule lifecycle tracking and performance analytics
    - Storage tiering (Hot Redis, Warm PostgreSQL, Cold S3)
    - Cost monitoring and optimization
    - Training data collection for future ML
    """

    def __init__(self):
        super().__init__("backend")

        # Initialize Correlation Engine
        self.correlation_engine = CorrelationEngine()

        # Initialize IOC Filter for noise reduction
        self.ioc_filter = IOCFilter()

        # Initialize Log Source Identifier (will set DB connection later)
        self.log_source_identifier = None

        # Initialize Update Scheduler (will set DB connection later)
        self.update_scheduler = None

        # Initialize Log Source Quality and Detection Fidelity systems
        self.log_source_quality = LogSourceQualityEngine()
        self.detection_fidelity = DetectionFidelityCalculator()
        self.correlation_requirements = CorrelationRequirementsEngine()

        # Initialize MITRE ATT&CK Mapper
        self.mitre_mapper = None  # Will be initialized after Redis/DB connection
        self.mitre_http_handlers = None

        # Initialize MITRE AI Intelligence
        self.mitre_ai = None  # Will be initialized after Redis/DB connection
        self.mitre_ai_http_handlers = None

        # Initialize Apache AGE Graph Service
        self.age_service = None  # Will be initialized after DB connection

        # Authentication Middleware
        self.auth_middleware = None  # Will be initialized after Redis connection

        # NOTE: Rule Deployment Service moved to Ingestion Engine (architectural fix)
        # Backend Engine should not connect to external SIEMs - that's Ingestion's job

        # Storage Configuration
        self.storage_tiers = {
            'hot': {
                'storage': 'redis',
                'retention': timedelta(hours=24),
                'cost_per_gb': 0.50
            },
            'warm': {
                'storage': 'postgresql',
                'retention': timedelta(days=30),
                'cost_per_gb': 0.10
            },
            'cold': {
                'storage': 's3',
                'retention': timedelta(days=365),
                'cost_per_gb': 0.02
            }
        }

        # CTI Sources Configuration
        self.cti_sources = {
            'mitre_attack': {
                'url': 'https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json',
                'update_frequency': timedelta(days=7),
                'last_update': None
            },
            'opencti': {
                'enabled': os.getenv('OPENCTI_ENABLED', 'false').lower() == 'true',
                'url': os.getenv('OPENCTI_URL', ''),
                'token': os.getenv('OPENCTI_TOKEN', '')
            }
        }

        # Performance Metrics
        self.performance_metrics = {
            'rules_created': 0,
            'rules_deployed': 0,
            'test_cases_generated': 0,
            'storage_operations': 0,
            'cost_savings': 0.0
        }

        # Training Data Collection
        self.training_buffer = []
        self.training_data_path = '/tmp/claude/training_data'
        os.makedirs(self.training_data_path, exist_ok=True)

        self.logger.info("Backend Engine initialized")

    def get_subscribed_channels(self) -> List[str]:
        """Subscribe to channels for backend operations"""
        return [
            'backend.store_data',         # Data storage requests
            'backend.store_processed_log',  # Processed logs from ingestion
            'backend.store_raw_log',      # Raw logs for audit
            # Segregated CTI channels (new architecture)
            'cti.rules.patterns',         # CTI patterns for rule generation
            'cti.investigation.context',  # CTI context for investigations
            'cti.mitre.mappings',         # MITRE framework updates

            # Legacy CTI channels (to be deprecated)
            'ingestion.cti.update',       # OLD: CTI updates from Ingestion Engine
            'backend.cti.data',           # OLD: CTI data from Ingestion Engine (delegation pattern)
            'ingestion.cti.indicators',   # OLD: CTI indicators from Ingestion Engine
            'backend.rule_generation',    # Rule generation requests
            'backend.test_generation',    # Test case generation
            'backend.performance_track',  # Performance tracking
            'backend.save_parser',        # Save parser from Ingestion Engine
            'intelligence.pattern_crystallized',  # New crystallized patterns
            'delivery.case_completed',    # Completed cases for analysis

            # Correlation channels
            'ingestion.events.parsed',    # Raw events for correlation
            'contextualization.events.enriched',  # Enriched events for correlation
            'correlation.check_event',    # Correlation check requests
            'correlation.assess_fidelity',  # Fidelity assessment requests
            'correlation.get_capabilities',  # Capability queries
            'backend.correlate_alert',    # NEW: Alert correlation requests from Delivery

        ]

    def start_engine_tasks(self) -> List[asyncio.Task]:
        """Start Backend Engine specific tasks"""
        # Initialize Log Source Identifier with DB pool
        self.log_source_identifier = LogSourceIdentifier(self.db_pool)

        # Initialize Update Scheduler with DB pool and Redis connections
        self.update_scheduler = UpdateScheduler(self.db_pool, self.redis_client)
        self.logger.info("Update Scheduler initialized")

        # NOTE: Authentication middleware will be initialized in _setup_http_routes
        # after async Redis client is available

        # Initialize Apache AGE Graph Service
        self.age_service = AGEGraphService(self.db_pool, self.logger)
        self.logger.info("Apache AGE Graph Service initialized")

        # Initialize MITRE ATT&CK Mapper
        self.mitre_mapper = MITREAttackMapper(
            redis_client=self.redis_client,
            db_pool=self.db_pool,
            logger=self.logger
        )
        self.mitre_http_handlers = MITREHTTPHandlers(self.mitre_mapper, self.logger)

        # Initialize MITRE framework asynchronously
        asyncio.create_task(self.mitre_mapper.initialize())
        self.logger.info("MITRE ATT&CK Mapper initialized")

        # Initialize MITRE AI Intelligence (if available)
        if HAS_AI_INTELLIGENCE:
            self.mitre_ai = MITREAIIntelligence(
                redis_client=self.redis_client,
                db_pool=self.db_pool,
                logger=self.logger
            )
            self.mitre_ai_http_handlers = MITREAIHTTPHandlers(self.mitre_ai, self.logger)
            self.logger.info("MITRE AI Intelligence initialized")
        else:
            self.logger.warning("MITRE AI Intelligence not available (missing dependencies)")

        # Initialize Log Retention Policy Engine
        self.retention_engine = LogRetentionPolicyEngine(
            db_pool=self.db_pool,
            redis_client=self.redis_client
        )
        # Initialize retention policies
        asyncio.create_task(self.retention_engine.initialize_policies())
        self.logger.info("Log Retention Policy Engine initialized")

        tasks = [
            asyncio.create_task(self._storage_management_task()),
            asyncio.create_task(self._cti_update_task()),
            asyncio.create_task(self._rule_performance_task()),
            asyncio.create_task(self._training_data_task()),
            asyncio.create_task(self._cost_monitoring_task()),
            asyncio.create_task(self._run_update_scheduler()),  # Update scheduler task
            asyncio.create_task(self._retention_cleanup_task())  # NEW: Retention cleanup task
        ]
        return tasks

    async def process_message(self, message: Dict[str, Any]):
        """Process incoming messages"""
        try:
            channel = message['channel']
            data = json.loads(message['data'])

            self.logger.info(f"Processing message from {channel}")

            if channel == 'backend.store_data':
                await self._handle_store_data(data)
            elif channel == 'backend.store_processed_log':
                await self._handle_store_processed_log(data)
            elif channel == 'backend.store_raw_log':
                await self._handle_store_raw_log(data)
            # New segregated CTI channels
            elif channel == 'cti.rules.patterns':
                await self._handle_cti_rule_patterns(data)
            elif channel == 'cti.investigation.context':
                await self._handle_cti_investigation_context(data)
            elif channel == 'cti.mitre.mappings':
                await self._handle_cti_mitre_mappings(data)

            # Legacy CTI channels (backwards compatibility)
            elif channel == 'ingestion.cti.update':
                await self._handle_cti_update(data)
            elif channel == 'backend.cti.data':
                await self._handle_cti_data(data)
            elif channel == 'ingestion.cti.indicators':
                await self._handle_cti_indicators(data)
            elif channel == 'backend.rule_generation':
                await self._handle_rule_generation(data)
            elif channel == 'backend.test_generation':
                await self._handle_test_generation(data)
            elif channel == 'backend.performance_track':
                await self._handle_performance_tracking(data)
            elif channel == 'backend.save_parser':
                await self._handle_save_parser(data)
            elif channel == 'intelligence.pattern_crystallized':
                await self._handle_crystallized_pattern(data)
            elif channel == 'delivery.case_completed':
                await self._handle_case_completion(data)

            # Correlation handlers
            elif channel == 'ingestion.events.parsed':
                await self._handle_correlation_event(data)
            elif channel == 'contextualization.events.enriched':
                await self._handle_enriched_correlation_event(data)
            elif channel == 'correlation.check_event':
                await self._handle_correlation_check(data)
            elif channel == 'correlation.assess_fidelity':
                await self._handle_fidelity_assessment(data)
            elif channel == 'correlation.get_capabilities':
                await self._handle_capability_query(data)
            elif channel == 'backend.correlate_alert':
                await self._handle_correlate_alert(data)

            # Log Source Quality API handlers (Redis interface)
                await self._handle_redis_log_source_status(data)
                await self._handle_redis_register_source(data)
                await self._handle_redis_detection_fidelity(data)
                await self._handle_redis_detection_coverage(data)
                await self._handle_redis_technique_coverage(data)
                await self._handle_redis_correlation_capability(data)
                await self._handle_redis_correlation_requirements(data)
                await self._handle_redis_recommendations(data)
                await self._handle_redis_coverage_gaps(data)
                await self._handle_redis_simulate_coverage(data)

            # Log Source Quality API handlers (Redis interface)
                await self._handle_redis_log_source_status(data)
                await self._handle_redis_register_source(data)
                await self._handle_redis_detection_fidelity(data)
                await self._handle_redis_detection_coverage(data)
                await self._handle_redis_technique_coverage(data)
                await self._handle_redis_correlation_capability(data)
                await self._handle_redis_correlation_requirements(data)
                await self._handle_redis_recommendations(data)
                await self._handle_redis_coverage_gaps(data)
                await self._handle_redis_simulate_coverage(data)
            else:
                self.logger.warning(f"Unknown channel: {channel}")

        except Exception as e:
            self.logger.error(f"Error processing message: {e}")

    async def _handle_store_processed_log(self, data: Dict[str, Any]):
        """LIGHTWEIGHT: Store only metadata and send to contextualization for entity extraction"""
        try:
            log_data = data.get('data', data)

            # DON'T STORE FULL LOGS - SIEMLess is NOT a log storage system!
            # Only track metadata for security-relevant events

            pattern_type = log_data.get('pattern_type', 'unknown')
            security_patterns = ['malware', 'threat_detection', 'privilege_escalation',
                               'lateral_movement', 'data_exfiltration', 'ransomware',
                               'authentication', 'failed_authentication']

            # Generate log ID for reference
            log_id = str(uuid.uuid4())

            # Send EVERYTHING to contextualization for entity extraction
            await self.publish_message('contextualization.process_log', {
                'log_id': log_id,
                'log': log_data.get('log', {}),
                'pattern_type': pattern_type,
                'pattern_id': log_data.get('pattern_id'),
                'entity_hints': log_data.get('entities', []),
                'source_type': log_data.get('source_type', 'unknown')
            })

            # Only store metadata for security events
            if pattern_type in security_patterns:
                async with self.db_pool.acquire() as conn:
                    # Store MINIMAL metadata only (not full log!)
                    await conn.execute("""
                        INSERT INTO ingestion_logs
                        (log_id, source_type, log_data, processed, created_at)
                        VALUES ($1, $2, $3, $4, NOW())
                    """,
                        log_id,
                        log_data.get('source_type', 'unknown'),
                        json.dumps({
                            'pattern_type': pattern_type,
                            'pattern_id': log_data.get('pattern_id'),
                            'severity': log_data.get('severity', 'medium'),
                            'entity_count': len(log_data.get('entities', [])),
                            'metadata_only': True  # Flag that this is lightweight storage
                        }),
                        True
                    )

                self.performance_metrics['storage_operations'] += 1
                self.logger.debug(f"Stored metadata for security event: {pattern_type}")
            else:
                # For non-security logs, just count them
                self.performance_metrics['logs_processed'] = self.performance_metrics.get('logs_processed', 0) + 1
                self.logger.debug(f"Processed non-security log: {pattern_type} (not stored)")

        except Exception as e:
            self.logger.error(f"Failed to store processed log: {e}")

    async def _handle_store_raw_log(self, data: Dict[str, Any]):
        """Handle storage of raw logs for audit"""
        try:
            log_data = data.get('data', data)

            # Store raw logs in warm storage
            storage_id = str(uuid.uuid4())
            expires_at = datetime.utcnow() + self.storage_tiers['cold']['retention']
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO warm_storage
                    (storage_id, data, created_at, expires_at)
                    VALUES ($1, $2, NOW(), $3)
                """,
                    storage_id,
                    json.dumps({
                        'type': 'raw_log',
                        'content': log_data,
                        'tier': 'cold'
                    }),
                    expires_at
                )

            self.performance_metrics['storage_operations'] += 1

        except Exception as e:
            self.logger.error(f"Failed to store raw log: {e}")

    async def _handle_store_data(self, data: Dict[str, Any]):
        """Handle data storage request"""
        try:
            data_type = data.get('type')
            data_content = data.get('content')
            retention_policy = data.get('retention', 'warm')

            # Determine storage tier
            storage_tier = self.storage_tiers.get(retention_policy, self.storage_tiers['warm'])

            # Store data based on tier
            storage_id = await self._store_data_by_tier(data_content, storage_tier)

            # Track storage operation
            self.performance_metrics['storage_operations'] += 1

            # Publish storage confirmation
            self.publish_message('backend.data_stored', {
                'storage_id': storage_id,
                'tier': retention_policy,
                'size': len(str(data_content)),
                'retention_until': (datetime.utcnow() + storage_tier['retention']).isoformat()
            })

        except Exception as e:
            self.logger.error(f"Data storage error: {e}")

    async def _handle_cti_update(self, data: Dict[str, Any]):
        """Handle CTI update notification from Ingestion Engine"""
        try:
            source = data.get('source')
            cti_data = data.get('data', data.get('cti_data', {}))  # Support both formats

            # Process CTI data for rule generation
            rules_generated = await self._process_cti_for_rules(cti_data, source)

            # Store and generate test cases for new rules
            for rule in rules_generated:
                await self._store_rule(rule)
                await self._generate_test_cases_for_rule(rule)

            self.performance_metrics['rules_created'] += len(rules_generated)

            self.logger.info(f"Generated {len(rules_generated)} rules from {source} CTI update")

        except Exception as e:
            self.logger.error(f"CTI update error: {e}")

    async def _handle_cti_data(self, data: Dict[str, Any]):
        """Handle CTI data from Ingestion Engine (delegation pattern)

        Receives CTI data fetched by Ingestion Engine,
        processes for rule generation and storage.
        """
        try:
            source = data.get('source')
            timestamp = data.get('timestamp')
            total_items = data.get('total_items', 0)
            cti_data = data.get('data', {})

            self.logger.info(f"Received {total_items} CTI items from {source} (fetched at {timestamp})")

            # Process CTI data for rule generation
            rules_generated = await self._process_cti_for_rules(cti_data, source)

            # Store and generate test cases for new rules
            for rule in rules_generated:
                await self._store_rule(rule)
                await self._generate_test_cases_for_rule(rule)

            self.performance_metrics['rules_created'] += len(rules_generated)

            self.logger.info(f"Processed CTI data from {source}: {len(rules_generated)} rules generated")

        except Exception as e:
            self.logger.error(f"CTI data processing error: {e}", exc_info=True)

    async def _handle_cti_indicators(self, data: Dict[str, Any]):
        """Handle CTI indicators from Ingestion Engine"""
        try:
            # Unwrap the 'data' wrapper added by base_engine.publish_message
            if 'data' in data and isinstance(data['data'], dict):
                actual_data = data['data']
            else:
                actual_data = data

            source = actual_data.get('source', 'unknown')
            indicators = actual_data.get('indicators', [])
            total_count = actual_data.get('total_count', len(indicators))

            self.logger.info(f"Received {total_count} indicators from {source}")

            # Generate rules from indicators
            if indicators:
                cti_data = {'indicators': indicators}
                rules = await self._process_cti_for_rules(cti_data, source)

                for rule in rules:
                    await self._store_rule(rule)
                    await self._generate_test_cases_for_rule(rule)

                    # NOTE: Rules are STORED only, NOT auto-deployed
                    # Deployment requires explicit user action via /api/rules/{rule_id}/deploy endpoint

                self.performance_metrics['rules_created'] += len(rules)
                self.logger.info(f"Generated and stored {len(rules)} rules from {total_count} {source} indicators (deployment requires user action)")

        except Exception as e:
            self.logger.error(f"CTI indicator processing error: {e}", exc_info=True)

    async def _handle_cti_rule_patterns(self, data: Dict[str, Any]):
        """Handle CTI patterns for rule generation (new segregated channel)"""
        try:
            source = data.get('source')
            pattern_type = data.get('pattern_type')
            indicators = data.get('indicators', [])
            ttp = data.get('ttp')
            rule_template = data.get('rule_template')
            severity = data.get('severity', 'medium')

            self.logger.info(f"Received CTI rule pattern from {source}: {pattern_type} with {len(indicators)} indicators")

            # Generate rule from pattern
            rule = await self._generate_rule_from_pattern(
                pattern_type=pattern_type,
                indicators=indicators,
                ttp=ttp,
                template=rule_template,
                severity=severity,
                source=source
            )

            if rule:
                await self._store_rule(rule)
                await self._generate_test_cases_for_rule(rule)
                self.performance_metrics['rules_created'] += 1
                self.logger.info(f"Generated rule from CTI pattern: {rule.get('name')}")

        except Exception as e:
            self.logger.error(f"CTI rule pattern processing error: {e}", exc_info=True)

    async def _handle_cti_investigation_context(self, data: Dict[str, Any]):
        """Handle CTI investigation context (new segregated channel)"""
        try:
            source = data.get('source')
            threat_actor = data.get('threat_actor')
            campaigns = data.get('campaigns', [])
            ttps = data.get('ttps', [])

            self.logger.info(f"Received investigation context from {source}: {threat_actor or 'Unknown'}")

            # Store investigation context for case enrichment
            context_data = {
                'source': source,
                'threat_actor': threat_actor,
                'campaigns': campaigns,
                'ttps': ttps,
                'iocs': data.get('iocs', {}),
                'timeline': data.get('timeline'),
                'description': data.get('description'),
                'references': data.get('references', []),
                'timestamp': datetime.utcnow()
            }

            # Store in investigation context table (for future case enrichment)
            await self._store_investigation_context(context_data)

            self.logger.info(f"Stored investigation context for {threat_actor or campaigns}")

        except Exception as e:
            self.logger.error(f"CTI investigation context processing error: {e}", exc_info=True)

    async def _handle_cti_mitre_mappings(self, data: Dict[str, Any]):
        """Handle MITRE ATT&CK mappings (new segregated channel)"""
        try:
            source = data.get('source')
            technique_id = data.get('technique_id')

            self.logger.info(f"Received MITRE mapping from {source}: {technique_id}")

            # Update MITRE framework if this is framework data
            if source == 'mitre':
                await self.mitre_mapper.update_technique(data)
                self.logger.info(f"Updated MITRE technique: {technique_id}")

            # Otherwise, associate IOCs with MITRE techniques
            else:
                associated_iocs = data.get('associated_iocs', [])
                campaign = data.get('campaign')

                # Store MITRE-IOC associations for detection coverage
                await self._store_mitre_ioc_association(
                    technique_id=technique_id,
                    iocs=associated_iocs,
                    campaign=campaign,
                    source=source
                )

                self.logger.info(f"Associated {len(associated_iocs)} IOCs with MITRE {technique_id}")

        except Exception as e:
            self.logger.error(f"CTI MITRE mapping processing error: {e}", exc_info=True)

    async def _handle_rule_generation(self, data: Dict[str, Any]):
        """Handle rule generation request"""
        try:
            cti_input = data.get('cti_input')
            rule_format = data.get('format', 'sigma')
            tactics = data.get('tactics', [])
            techniques = data.get('techniques', [])

            # Generate detection rule
            rule = await self._generate_detection_rule(cti_input, rule_format, tactics, techniques)

            # Generate test cases
            test_cases = await self._generate_test_cases_for_rule(rule)

            # Store rule and test cases
            rule_id = await self._store_rule_with_tests(rule, test_cases)

            self.performance_metrics['rules_created'] += 1
            self.performance_metrics['test_cases_generated'] += len(test_cases)

            self.publish_message('backend.rule_generated', {
                'rule_id': rule_id,
                'rule': rule,
                'test_cases': test_cases,
                'performance_metrics': self.performance_metrics
            })

            # Publish rule approval for automatic deployment (Ingestion Engine listens)
            self.publish_message('backend.rule.approved', {
                'rule_id': str(rule_id),
                'rule_name': rule.get('name', rule.get('title', 'Unknown')),
                'severity': rule.get('level', 'medium'),
                'target_siem': 'elastic',  # Default to Elastic
                'auto_deploy': True,  # Auto-deploy CTI-generated rules
                'cti_source': cti_input.get('source', 'unknown'),
                'rule_type': rule_format
            })
            self.logger.info(f"Rule {rule_id} approved for deployment")

        except Exception as e:
            self.logger.error(f"Rule generation error: {e}")

    async def _handle_test_generation(self, data: Dict[str, Any]):
        """Handle test case generation request"""
        try:
            rule_data = data.get('rule')
            test_count = data.get('count', 5)

            # Generate synthetic test logs
            test_cases = await self._generate_synthetic_logs(rule_data, test_count)

            self.performance_metrics['test_cases_generated'] += len(test_cases)

            self.publish_message('backend.tests_generated', {
                'rule_id': data.get('rule_id'),
                'test_cases': test_cases
            })

        except Exception as e:
            self.logger.error(f"Test generation error: {e}")

    async def _handle_performance_tracking(self, data: Dict[str, Any]):
        """Handle performance tracking update"""
        try:
            rule_id = data.get('rule_id')
            performance_data = data.get('performance')

            # Update rule performance metrics
            await self._update_rule_performance(rule_id, performance_data)

            # Check if rule needs optimization
            if performance_data.get('false_positive_rate', 0) > 0.1:
                self.publish_message('backend.rule_optimization', {
                    'rule_id': rule_id,
                    'reason': 'high_false_positive_rate',
                    'performance': performance_data
                })

        except Exception as e:
            self.logger.error(f"Performance tracking error: {e}")

    async def _handle_crystallized_pattern(self, data: Dict[str, Any]):
        """Handle new crystallized pattern from Intelligence Engine"""
        try:
            pattern_id = data.get('pattern_id')
            pattern = data.get('crystallized_pattern')

            # Store pattern for future use
            await self._store_crystallized_pattern(pattern_id, pattern)

            # Calculate and track cost savings
            cost_savings = data.get('cost_savings', {})
            self.performance_metrics['cost_savings'] += cost_savings.get('estimated_savings', 0)

            self.logger.info(f"Stored crystallized pattern {pattern_id}")

        except Exception as e:
            self.logger.error(f"Crystallized pattern error: {e}")

    async def _handle_save_parser(self, data: Dict[str, Any]):
        """
        Handle parser save request from Ingestion Engine
        Maps parser to SIEM schema and stores in database
        """
        try:
            # Extract actual data from message envelope
            message_data = data.get('data', data)

            parser_id = message_data.get('parser_id')
            parser = message_data.get('parser', {})
            validation = message_data.get('validation', {})
            target_siem = message_data.get('target_siem', 'generic')
            log_source = message_data.get('log_source', 'unknown')
            vendor = message_data.get('vendor', 'unknown')
            response_channel = message_data.get('response_channel')

            if not parser or not response_channel:
                self.logger.error(f"Missing parser or response_channel in save request")
                return

            self.logger.info(f"Saving parser {parser_id} for {log_source} ({vendor}) -> {target_siem}")

            # Load SIEM schema loader
            from siem_schema_loader import SIEMSchemaLoader
            schema_loader = SIEMSchemaLoader(logger=self.logger)

            # Map parser fields to SIEM schema
            field_mappings = parser.get('field_mappings', {})
            siem_mapped_fields = schema_loader.map_fields_to_siem(field_mappings, target_siem)

            # Get SIEM platform info
            platform_info = schema_loader.get_platform_info(target_siem)

            # Store parser in database
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO parsers (
                        parser_id,
                        log_source,
                        vendor,
                        target_siem,
                        format_type,
                        field_mappings,
                        siem_mapped_fields,
                        entity_types,
                        regex_patterns,
                        grok_pattern,
                        validation_metrics,
                        platform_info,
                        status,
                        created_at,
                        updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, NOW(), NOW())
                    ON CONFLICT (parser_id) DO UPDATE SET
                        field_mappings = EXCLUDED.field_mappings,
                        siem_mapped_fields = EXCLUDED.siem_mapped_fields,
                        validation_metrics = EXCLUDED.validation_metrics,
                        updated_at = NOW()
                """,
                    parser_id,
                    log_source,
                    vendor,
                    target_siem,
                    parser.get('format_type', 'unknown'),
                    json.dumps(field_mappings),
                    json.dumps(siem_mapped_fields),
                    json.dumps(parser.get('entity_types', [])),
                    json.dumps(parser.get('regex_patterns', {})),
                    parser.get('grok_pattern'),
                    json.dumps(validation),
                    json.dumps(platform_info),
                    'active'
                )

            self.logger.info(f"Parser {parser_id} saved successfully")
            self.logger.info(f"Mapped {len(siem_mapped_fields)} fields to {target_siem} schema")

            # Build response
            response_data = {
                'parser_id': parser_id,
                'status': 'saved',
                'siem': target_siem,
                'platform': platform_info.get('display_name', target_siem),
                'field_count': len(siem_mapped_fields),
                'validation': validation
            }

            # Publish response
            self.publish_message(response_channel, response_data)

        except Exception as e:
            self.logger.error(f"Parser save error: {e}")
            # Send error response
            if response_channel:
                self.publish_message(response_channel, {
                    'parser_id': data.get('parser_id'),
                    'error': str(e),
                    'status': 'save_failed'
                })

    async def _handle_case_completion(self, data: Dict[str, Any]):
        """Handle completed case for training data collection"""
        try:
            case_data = data.get('case')
            investigation_results = data.get('results')

            # Collect training data
            training_sample = {
                'case_id': case_data.get('case_id'),
                'entities': case_data.get('entities', []),
                'relationships': case_data.get('relationships', []),
                'analyst_decisions': investigation_results.get('decisions', []),
                'outcome': investigation_results.get('outcome'),
                'timestamp': datetime.utcnow().isoformat()
            }

            # Add to training buffer
            self.training_buffer.append(training_sample)

            # If buffer is full, save to file
            if len(self.training_buffer) >= 100:
                await self._save_training_data()

        except Exception as e:
            self.logger.error(f"Case completion error: {e}")

    async def _store_data_by_tier(self, data: Any, storage_tier: Dict[str, Any]) -> str:
        """Store data in appropriate tier"""
        storage_id = str(uuid.uuid4())

        if storage_tier['storage'] == 'redis':
            # Store in Redis with TTL
            ttl_seconds = int(storage_tier['retention'].total_seconds())
            self.redis_client.setex(f"hot:{storage_id}", ttl_seconds, json.dumps(data))

        elif storage_tier['storage'] == 'postgresql':
            # Store in PostgreSQL
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO warm_storage (storage_id, data, created_at, expires_at)
                    VALUES ($1, $2, $3, $4)
                """,
                    storage_id, json.dumps(data), datetime.utcnow(),
                    datetime.utcnow() + storage_tier['retention']
                )

        elif storage_tier['storage'] == 's3':
            # Store in S3 (mock implementation)
            # In real implementation, this would use boto3
            cold_path = f"{self.training_data_path}/cold/{storage_id}.json"
            os.makedirs(os.path.dirname(cold_path), exist_ok=True)
            with open(cold_path, 'w') as f:
                json.dump(data, f)

        return storage_id

    async def _process_cti_for_rules(self, cti_data: Dict[str, Any], source: str) -> List[Dict[str, Any]]:
        """Process CTI data to generate detection rules"""
        rules = []

        # Extract IOCs and TTPs from CTI data
        iocs = cti_data.get('indicators', [])
        ttps = cti_data.get('tactics_techniques', [])

        # Filter out noise and scanners
        filtered_iocs = self.ioc_filter.filter_iocs(iocs)
        self.logger.info(f"Filtered {len(iocs) - len(filtered_iocs)} noisy IOCs from {source}")

        # Generate rules for filtered IOCs
        self.logger.info(f"Attempting to create rules for {len(filtered_iocs)} filtered IOCs")
        for ioc in filtered_iocs:
            self.logger.info(f"Creating rule for IOC: {ioc.get('indicator_value', ioc.get('ioc_value', 'unknown'))[:50]}")
            rule = await self._create_ioc_rule(ioc, source)

            if rule is None:
                self.logger.info(f"  → _create_ioc_rule returned None")
            else:
                self.logger.info(f"  → Rule created with quality_score: {rule.get('quality_score', 0)}")

            # Apply quality gate (production threshold)
            if rule and rule.get('quality_score', 0) >= 0.4:
                rules.append(rule)
                self.logger.info(f"  ✓ Rule PASSED quality gate: {rule.get('name', 'Unknown')[:50]} (score: {rule.get('quality_score', 0):.2f})")
            elif rule:
                self.logger.info(f"  ✗ Rule FAILED quality gate: {rule.get('name', 'Unknown')[:50]} (score: {rule.get('quality_score', 0):.2f})")

        # Generate rules for TTPs (these are usually higher quality)
        for ttp in ttps:
            rule = await self._create_ttp_rule(ttp, source)
            if rule:
                rules.append(rule)

        self.logger.info(f"Generated {len(rules)} quality rules from {len(iocs)} IOCs for {source}")
        return rules

    async def _generate_detection_rule(self, cti_input: str, rule_format: str, tactics: List[str], techniques: List[str]) -> Dict[str, Any]:
        """Generate detection rule from CTI input"""
        rule = {
            'id': str(uuid.uuid4()),
            'title': f"Detection rule from CTI: {cti_input[:50]}...",
            'format': rule_format,
            'tactics': tactics,
            'techniques': techniques,
            'created_at': datetime.utcnow().isoformat(),
            'source': 'cti_automated'
        }

        if rule_format == 'sigma':
            rule['rule_content'] = self._generate_sigma_rule(cti_input, tactics, techniques)
        elif rule_format == 'yara':
            rule['rule_content'] = self._generate_yara_rule(cti_input)
        else:
            rule['rule_content'] = self._generate_generic_rule(cti_input)

        return rule

    def _generate_sigma_rule(self, cti_input: str, tactics: List[str], techniques: List[str]) -> str:
        """Generate Sigma rule (mock implementation)"""
        return f"""
title: Automated Detection Rule
id: {uuid.uuid4()}
status: experimental
description: Generated from CTI input
author: SIEMLess Backend Engine
date: {datetime.utcnow().strftime('%Y/%m/%d')}
tags:
    - attack.{tactics[0] if tactics else 'unknown'}
    - attack.{techniques[0] if techniques else 'unknown'}
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        - CommandLine|contains: '{cti_input}'
    condition: selection
falsepositives:
    - Unknown
level: medium
"""

    def _generate_yara_rule(self, cti_input: str) -> str:
        """Generate YARA rule (mock implementation)"""
        return f"""
rule AutoGenerated_Rule_{uuid.uuid4().hex[:8]}
{{
    meta:
        description = "Generated from CTI input"
        author = "SIEMLess Backend Engine"
        date = "{datetime.utcnow().strftime('%Y-%m-%d')}"

    strings:
        $s1 = "{cti_input}" ascii wide

    condition:
        $s1
}}
"""

    def _generate_generic_rule(self, cti_input: str) -> str:
        """Generate generic rule"""
        return f"ALERT when pattern matches: {cti_input}"

    async def _generate_test_cases_for_rule(self, rule: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate test cases for a detection rule"""
        test_cases = []

        # Generate positive test cases (should trigger the rule)
        for i in range(3):
            test_case = {
                'type': 'positive',
                'test_id': str(uuid.uuid4()),
                'log_sample': self._generate_positive_log_sample(rule),
                'expected_result': 'match',
                'created_at': datetime.utcnow().isoformat()
            }
            test_cases.append(test_case)

        # Generate negative test cases (should not trigger the rule)
        for i in range(2):
            test_case = {
                'type': 'negative',
                'test_id': str(uuid.uuid4()),
                'log_sample': self._generate_negative_log_sample(rule),
                'expected_result': 'no_match',
                'created_at': datetime.utcnow().isoformat()
            }
            test_cases.append(test_case)

        return test_cases

    def _generate_positive_log_sample(self, rule: Dict[str, Any]) -> str:
        """Generate log sample that should trigger the rule"""
        # Mock implementation - would be more sophisticated in reality
        return f"Process executed: {rule.get('title', 'test_process')} with suspicious activity"

    def _generate_negative_log_sample(self, rule: Dict[str, Any]) -> str:
        """Generate log sample that should not trigger the rule"""
        return "Normal system process executed: notepad.exe"

    async def _generate_synthetic_logs(self, rule_data: Dict[str, Any], count: int) -> List[Dict[str, Any]]:
        """Generate synthetic logs for testing"""
        synthetic_logs = []

        for i in range(count):
            log = {
                'log_id': str(uuid.uuid4()),
                'timestamp': datetime.utcnow().isoformat(),
                'source': 'synthetic_generator',
                'content': self._generate_positive_log_sample(rule_data),
                'test_purpose': 'rule_validation'
            }
            synthetic_logs.append(log)

        return synthetic_logs

    async def _store_rule_with_tests(self, rule: Dict[str, Any], test_cases: List[Dict[str, Any]]) -> str:
        """Store rule and associated test cases"""
        try:
            async with self.db_pool.acquire() as conn:
                # Store rule
                await conn.execute("""
                    INSERT INTO detection_rules (rule_id, rule_data, created_at)
                    VALUES ($1, $2, $3)
                """, rule['id'], json.dumps(rule), datetime.utcnow())

                # Store test cases
                for test_case in test_cases:
                    await conn.execute("""
                        INSERT INTO rule_test_cases (rule_id, test_case_data, created_at)
                        VALUES ($1, $2, $3)
                    """, rule['id'], json.dumps(test_case), datetime.utcnow())

            return rule['id']

        except Exception as e:
            self.logger.error(f"Failed to store rule with tests: {e}")
            return None

    async def _update_rule_performance(self, rule_id: str, performance_data: Dict[str, Any]):
        """Update rule performance metrics"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO rule_performance (rule_id, performance_data, recorded_at)
                    VALUES ($1, $2, $3)
                """, rule_id, json.dumps(performance_data), datetime.utcnow())

        except Exception as e:
            self.logger.error(f"Failed to update rule performance: {e}")

    async def _store_crystallized_pattern(self, pattern_id: str, pattern: Dict[str, Any]):
        """Store crystallized pattern"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO crystallized_patterns (pattern_id, pattern_data, created_at)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (pattern_id) DO UPDATE SET pattern_data = $4, updated_at = $5
                """,
                    pattern_id, json.dumps(pattern), datetime.utcnow(),
                    json.dumps(pattern), datetime.utcnow()
                )

        except Exception as e:
            self.logger.error(f"Failed to store crystallized pattern: {e}")

    async def _save_training_data(self):
        """Save training data buffer to file"""
        try:
            timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
            filename = f"{self.training_data_path}/training_batch_{timestamp}.jsonl"

            with open(filename, 'w') as f:
                for sample in self.training_buffer:
                    f.write(json.dumps(sample) + '\n')

            self.logger.info(f"Saved {len(self.training_buffer)} training samples to {filename}")
            self.training_buffer.clear()

        except Exception as e:
            self.logger.error(f"Failed to save training data: {e}")

    async def _create_ioc_rule(self, ioc: Dict[str, Any], source: str) -> Optional[Dict[str, Any]]:
        """Create detection rule for IOC"""
        # Handle both CTI format (indicator_value) and STIX patterns (pattern)
        pattern = ioc.get('pattern') or ioc.get('raw_data', {}).get('pattern')
        value = (ioc.get('indicator_value') or  # CTI format
                ioc.get('value') or              # Legacy format
                ioc.get('indicator') or          # Alternative format
                ioc.get('ioc_value'))            # Another alternative

        if not pattern and not value:
            self.logger.debug(f"IOC missing both pattern and value: {list(ioc.keys())}")
            return None

        # Extract value from STIX pattern if present
        if pattern and not value:
            # Simple extraction for common patterns
            if 'SHA-256' in pattern or 'MD5' in pattern:
                import re
                match = re.search(r"=\s*'([^']+)'", pattern)
                if match:
                    value = match.group(1)
            elif 'domain-name:value' in pattern:
                import re
                match = re.search(r"=\s*'([^']+)'", pattern)
                if match:
                    value = match.group(1)

        rule_id = str(uuid.uuid4())

        # Generate Sigma rule for the IOC
        sigma_rule = self._generate_sigma_from_ioc(ioc, value)

        # Get indicator type (CTI format uses indicator_type, legacy uses type/pattern_type)
        ioc_type = (ioc.get('indicator_type') or
                   ioc.get('pattern_type') or
                   ioc.get('type', 'unknown'))

        # Get a proper title from various possible fields
        desc_preview = (ioc.get('description') or '')[:50] if ioc.get('description') else ''
        title = (ioc.get('name') or
                ioc.get('title') or
                ioc.get('pulse_name') or
                desc_preview or
                f"{source.upper()} {ioc_type}: {str(value)[:30] if value else 'Detection'}")

        # Get description
        description = (ioc.get('description') or
                      f"Detection rule for {source} indicator: {ioc_type}")

        # Create the base rule
        rule = {
            'id': rule_id,
            'name': title,  # Use 'name' for consistency with database lookups
            'title': title,  # Keep 'title' for backward compatibility
            'description': description,
            'ioc_value': value or pattern,
            'ioc_type': ioc_type,
            'pattern': pattern,
            'confidence': ioc.get('confidence', 0.5) if isinstance(ioc.get('confidence'), float) else ioc.get('confidence', 50) / 100.0,  # Handle both 0-1 and 0-100 scales
            'source': source,
            'labels': ioc.get('labels') or ioc.get('tags', []),  # CTI format uses 'tags', legacy uses 'labels'
            'severity': ioc.get('severity', 'medium'),  # CTI format includes severity
            'threat_type': ioc.get('threat_type'),  # CTI format includes threat_type
            'mitre_techniques': ioc.get('mitre_techniques', []),  # CTI format includes MITRE
            'sigma_rule': sigma_rule,
            'created_at': datetime.utcnow().isoformat()
        }

        # Add quality score and validation
        validation = await self._validate_rule_effectiveness(rule)
        rule['quality_score'] = validation['quality_score']
        rule['validation'] = validation

        return rule

    def _generate_sigma_from_ioc(self, ioc: Dict[str, Any], value: str) -> str:
        """Generate Sigma rule from IOC"""
        pattern_type = ioc.get('pattern_type', 'unknown')
        labels = ioc.get('labels', [])

        if 'SHA-256' in str(ioc.get('pattern', '')) or 'hash' in pattern_type.lower():
            return f"""
title: {ioc.get('name') or ioc.get('title') or ioc.get('pulse_name') or 'Hash IOC Detection'}
description: Detection rule for file hash IOC
status: experimental
author: Backend Engine
date: {datetime.utcnow().strftime('%Y/%m/%d')}
tags:
{chr(10).join(f'    - {label}' for label in labels)}
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        - Hashes|contains: '{value}'
        - sha256: '{value}'
        - MD5: '{value}'
    condition: selection
falsepositives:
    - Unknown
level: high
"""
        else:
            # Generic rule for other IOC types
            return f"""
title: {ioc.get('name') or ioc.get('title') or ioc.get('pulse_name') or 'IOC Detection'}
description: Detection rule for {pattern_type} IOC
status: experimental
author: Backend Engine
date: {datetime.utcnow().strftime('%Y/%m/%d')}
tags:
{chr(10).join(f'    - {label}' for label in labels)}
logsource:
    product: windows
detection:
    selection:
        - CommandLine|contains: '{value}'
        - Image|contains: '{value}'
    condition: selection
falsepositives:
    - Unknown
level: medium
"""

    async def _store_rule(self, rule: Dict[str, Any]) -> str:
        """Store detection rule in database with duplicate detection"""
        try:
            self.logger.info(f"Attempting to store rule {rule.get('id')}")

            async with self.db_pool.acquire() as conn:
                # Check for duplicates based on IOC value and source
                ioc_value = rule.get('ioc_value', '')
                source = rule.get('source', '')

                # Look for existing rule with same IOC value and source
                existing = await conn.fetchrow("""
                    SELECT rule_id, rule_data
                    FROM detection_rules
                    WHERE rule_data->>'ioc_value' = $1
                      AND rule_data->>'source' = $2
                    LIMIT 1
                """, ioc_value, source)

                if existing:
                    existing_id = existing['rule_id']
                    existing_data = existing['rule_data']

                    # Check if this is a meaningful update
                    if self._is_meaningful_update(existing_data, rule):
                        # Update existing rule with new information
                        await conn.execute("""
                            UPDATE detection_rules
                            SET rule_data = $1, updated_at = CURRENT_TIMESTAMP
                            WHERE rule_id = $2
                        """, json.dumps(rule), existing_id)
                        self.logger.info(f"Updated existing rule {existing_id} with new data")
                        return existing_id
                    else:
                        self.logger.debug(f"Skipping duplicate rule for IOC: {ioc_value[:50]}")
                        return existing_id

                else:
                    # No duplicate found, insert new rule
                    await conn.execute("""
                        INSERT INTO detection_rules (
                            rule_id, rule_data, created_at
                        ) VALUES ($1, $2, $3)
                        ON CONFLICT (rule_id) DO UPDATE SET
                            rule_data = EXCLUDED.rule_data,
                            updated_at = CURRENT_TIMESTAMP
                    """,
                        rule.get('id'),
                        json.dumps(rule),
                        datetime.utcnow()
                    )

            # Just use the rule ID we inserted
            rule_id = rule.get('id')
            self.logger.info(f"Stored rule {rule_id} in database")
            return rule_id

        except Exception as e:
            self.logger.error(f"Failed to store rule {rule.get('id', 'unknown')}: {str(e)}", exc_info=True)
            return None

    def _is_meaningful_update(self, existing_rule: Dict[str, Any], new_rule: Dict[str, Any]) -> bool:
        """Check if new rule has meaningful updates over existing one"""
        # Check if new rule has more labels/tags
        if len(new_rule.get('labels', [])) > len(existing_rule.get('labels', [])):
            return True

        # Check if confidence increased significantly
        if new_rule.get('confidence', 0) > existing_rule.get('confidence', 0) + 0.2:
            return True

        # Check if description is more detailed
        if len(new_rule.get('description', '')) > len(existing_rule.get('description', '')) + 50:
            return True

        return False

    def _calculate_rule_quality_score(self, rule: Dict[str, Any]) -> float:
        """Calculate quality score for a detection rule (0-1)"""
        score = 0.0

        # Base score from confidence
        score += rule.get('confidence', 0.5) * 0.3

        # Has meaningful name (not just IOC value)
        if rule.get('name') and rule.get('name') != rule.get('ioc_value'):
            score += 0.1

        # Has description
        if rule.get('description') and len(rule.get('description', '')) > 20:
            score += 0.1

        # Has labels/tags for context
        labels = rule.get('labels', [])
        if labels:
            score += min(len(labels) * 0.05, 0.2)  # Up to 0.2 for 4+ labels

        # Has MITRE ATT&CK mapping
        if any('attack.' in str(label).lower() or label.startswith('T') for label in labels):
            score += 0.15

        # Source reputation (adjust based on your trust levels)
        source_scores = {
            'opencti': 0.15,  # Your own instance
            'otx': 0.10,      # Community feed
            'threatfox': 0.12  # Specialized malware feed
        }
        score += source_scores.get(rule.get('source', '').lower(), 0.05)

        return min(score, 1.0)  # Cap at 1.0

    async def _validate_rule_effectiveness(self, rule: Dict[str, Any]) -> Dict[str, Any]:
        """Validate rule effectiveness and potential false positives"""
        validation = {
            'quality_score': self._calculate_rule_quality_score(rule),
            'potential_fps': [],
            'recommendations': []
        }

        ioc_value = rule.get('ioc_value', '')
        ioc_type = rule.get('ioc_type', '')

        # Check for potential false positives
        if ioc_type in ['ip', 'ipv4', 'ipv6']:
            # Check if it's a private IP
            import ipaddress
            try:
                ip = ipaddress.ip_address(ioc_value)
                if ip.is_private:
                    validation['potential_fps'].append('Private IP address')
                    validation['quality_score'] *= 0.5
                if ip.is_loopback:
                    validation['potential_fps'].append('Loopback address')
                    validation['quality_score'] *= 0.1
            except:
                pass

        elif ioc_type == 'domain':
            # Check for common legitimate domains
            legitimate_patterns = ['google.', 'microsoft.', 'amazon.', 'cloudflare.']
            if any(pattern in ioc_value.lower() for pattern in legitimate_patterns):
                validation['potential_fps'].append('Possibly legitimate domain')
                validation['recommendations'].append('Review domain reputation')

        elif ioc_type == 'hash':
            # Check if hash format is valid
            if len(ioc_value) not in [32, 40, 64]:  # MD5, SHA1, SHA256
                validation['potential_fps'].append('Invalid hash length')
                validation['quality_score'] *= 0.3

        # Add recommendations based on quality
        if validation['quality_score'] < 0.3:
            validation['recommendations'].append('Low quality - consider additional validation')
        elif validation['quality_score'] < 0.5:
            validation['recommendations'].append('Medium quality - monitor for false positives')
        else:
            validation['recommendations'].append('Good quality - ready for deployment')

        return validation

    async def _create_ttp_rule(self, ttp: Dict[str, Any], source: str) -> Optional[Dict[str, Any]]:
        """Create detection rule for TTP"""
        if not ttp.get('technique_id'):
            return None

        return {
            'id': str(uuid.uuid4()),
            'title': f"TTP Detection: {ttp.get('technique_id')}",
            'technique_id': ttp['technique_id'],
            'tactic': ttp.get('tactic'),
            'source': source,
            'created_at': datetime.utcnow().isoformat()
        }

    async def _storage_management_task(self):
        """Manage storage tiers and cleanup"""
        while self.is_running:
            try:
                # Clean up expired data from warm storage
                async with self.db_pool.acquire() as conn:
                    result = await conn.execute("""
                        DELETE FROM warm_storage WHERE expires_at < $1
                    """, datetime.utcnow())

                    # Extract deleted count from result string like "DELETE 5"
                    deleted_count = int(result.split()[-1]) if result and result.split()[-1].isdigit() else 0
                    if deleted_count > 0:
                        self.logger.info(f"Cleaned up {deleted_count} expired records from warm storage")

                    # Archive old data to cold storage
                    rows = await conn.fetch("""
                        SELECT storage_id, data FROM warm_storage
                        WHERE created_at < $1
                    """, datetime.utcnow() - timedelta(days=7))

                    for row in rows:
                        # Move to cold storage
                        await self._store_data_by_tier(row['data'], self.storage_tiers['cold'])
                        # Remove from warm storage
                        await conn.execute("DELETE FROM warm_storage WHERE storage_id = $1", row['storage_id'])

            except Exception as e:
                self.logger.error(f"Storage management error: {e}")

            # Run every 6 hours
            await asyncio.sleep(21600)

    async def _cti_update_task(self):
        """Monitor CTI updates from Ingestion Engine"""
        while self.is_running:
            try:
                # Log CTI processing statistics
                self.logger.info(f"CTI Rules Generated: {self.performance_metrics.get('rules_created', 0)}")

                # Original MITRE update logic as fallback
                for source_name, source_config in self.cti_sources.items():
                    if source_name == 'mitre_attack':
                        # Check if update is needed
                        if (not source_config['last_update'] or
                            datetime.utcnow() - source_config['last_update'] > source_config['update_frequency']):

                            # Simulate CTI update (would fetch from actual source)
                            cti_data = {
                                'indicators': [
                                    {'type': 'hash', 'value': 'mock_hash_123'},
                                    {'type': 'domain', 'value': 'malicious.example.com'}
                                ],
                                'tactics_techniques': [
                                    {'technique_id': 'T1055', 'tactic': 'defense_evasion'}
                                ]
                            }

                            self.publish_message('backend.cti_update', {
                                'source': source_name,
                                'cti_data': cti_data
                            })

                            source_config['last_update'] = datetime.utcnow()

            except Exception as e:
                self.logger.error(f"CTI update error: {e}")

            # Run every hour
            await asyncio.sleep(3600)

    async def _rule_performance_task(self):
        """Monitor rule performance"""
        while self.is_running:
            try:
                # Analyze rule performance from last 24 hours
                async with self.db_pool.acquire() as conn:
                    rows = await conn.fetch("""
                        SELECT rule_id, AVG((performance_data->>'accuracy')::float) as avg_accuracy,
                               AVG((performance_data->>'false_positive_rate')::float) as avg_fp_rate
                        FROM rule_performance
                        WHERE recorded_at > $1
                        GROUP BY rule_id
                    """, datetime.utcnow() - timedelta(hours=24))

                    for row in rows:
                        rule_id = row['rule_id']
                        avg_accuracy = row['avg_accuracy'] or 0
                        avg_fp_rate = row['avg_fp_rate'] or 0

                        # Check for underperforming rules
                        if avg_accuracy < 0.8 or avg_fp_rate > 0.1:
                            self.publish_message('backend.rule_optimization', {
                                'rule_id': rule_id,
                                'avg_accuracy': avg_accuracy,
                                'avg_fp_rate': avg_fp_rate,
                                'recommendation': 'review_and_tune'
                            })

            except Exception as e:
                self.logger.error(f"Rule performance task error: {e}")

            # Run every 4 hours
            await asyncio.sleep(14400)

    async def _training_data_task(self):
        """Periodic training data management"""
        while self.is_running:
            try:
                # Save any pending training data
                if self.training_buffer:
                    await self._save_training_data()

                # Compress old training files
                training_files = os.listdir(self.training_data_path)
                for filename in training_files:
                    if filename.endswith('.jsonl'):
                        file_path = os.path.join(self.training_data_path, filename)
                        file_age = datetime.utcnow() - datetime.fromtimestamp(os.path.getctime(file_path))

                        if file_age > timedelta(days=1):
                            # Compress old files (mock implementation)
                            compressed_path = file_path + '.gz'
                            # In real implementation, would use gzip
                            self.logger.info(f"Would compress {file_path} to {compressed_path}")

            except Exception as e:
                self.logger.error(f"Training data task error: {e}")

            # Run every 2 hours
            await asyncio.sleep(7200)

    async def _cost_monitoring_task(self):
        """Monitor and report costs"""
        while self.is_running:
            try:
                # Calculate storage costs
                storage_cost = 0.0
                for tier_name, tier_config in self.storage_tiers.items():
                    # Mock calculation - would query actual storage usage
                    estimated_gb = 10  # Mock value
                    tier_cost = estimated_gb * tier_config['cost_per_gb']
                    storage_cost += tier_cost

                # Log cost metrics
                total_cost = storage_cost
                savings = self.performance_metrics['cost_savings']

                self.logger.info(f"Cost metrics - Total: ${total_cost:.2f}, Savings: ${savings:.2f}")

                # Publish cost metrics
                self.publish_message('backend.cost_metrics', {
                    'storage_cost': storage_cost,
                    'total_cost': total_cost,
                    'cost_savings': savings,
                    'performance_metrics': self.performance_metrics
                })

            except Exception as e:
                self.logger.error(f"Cost monitoring error: {e}")

            # Run every hour
            await asyncio.sleep(3600)

    async def _run_update_scheduler(self):
        """Run the update scheduler for source definitions"""
        while self.is_running:
            try:
                # Load previous state
                await self.update_scheduler.load_schedule_state()

                # Start the scheduler
                self.logger.info("Starting source update scheduler...")
                await self.update_scheduler.start()

            except Exception as e:
                self.logger.error(f"Update scheduler error: {e}")
                # Restart after error
                await asyncio.sleep(60)

    # ========================================
    # CORRELATION HANDLERS
    # ========================================

    async def _handle_correlation_event(self, data: Dict[str, Any]):
        """Handle parsed events for correlation checking"""
        try:
            event = data.get('event', data)

            # Identify and enrich with source information
            event = await self.log_source_identifier.enrich_with_source_info(event)

            # Add source quality metadata from identification
            source_metadata = event.get('source_metadata', {})
            event['source_quality'] = {
                'score': source_metadata.get('quality_score', 5),
                'tier': source_metadata.get('quality_tier', 'minimal'),
                'vendor': source_metadata.get('vendor', 'unknown'),
                'product': source_metadata.get('product', 'unknown')
            }

            # Process through correlation engine
            correlation_results = await self.correlation_engine.process_event(event)

            if correlation_results:
                # Publish correlation alerts
                for result in correlation_results:
                    await self._publish_correlation_alert(result)

        except Exception as e:
            self.logger.error(f"Correlation event error: {e}")

    async def _handle_enriched_correlation_event(self, data: Dict[str, Any]):
        """Handle enriched events for better correlation"""
        try:
            event = data.get('event', data)

            # Enriched events get confidence boost
            event['enriched'] = True
            event['confidence_boost'] = 0.1

            # Process through correlation engine
            correlation_results = await self.correlation_engine.process_event(event)

            if correlation_results:
                for result in correlation_results:
                    # Boost confidence for enriched correlations
                    result['confidence'] = min(1.0, result.get('confidence', 0.5) + 0.1)
                    await self._publish_correlation_alert(result)

        except Exception as e:
            self.logger.error(f"Enriched correlation error: {e}")

    async def _handle_correlation_check(self, request: Dict[str, Any]):
        """Handle explicit correlation check requests"""
        try:
            events = request.get('events', [])
            time_window = request.get('time_window', 300)

            # Batch correlation check
            correlations = []
            for event in events:
                results = await self.correlation_engine.process_event(event)
                if results:
                    correlations.extend(results)

            # Publish results
            self.publish_message('correlation.check_result', {
                'request_id': request.get('request_id'),
                'correlations': correlations,
                'events_checked': len(events)
            })

        except Exception as e:
            self.logger.error(f"Correlation check error: {e}")

    async def _handle_fidelity_assessment(self, request: Dict[str, Any]):
        """Handle detection fidelity assessment requests"""
        try:
            attack_type = request.get('attack_type')
            available_sources = request.get('available_sources', [])

            # Assess detection capability
            assessment = await self._assess_detection_fidelity(attack_type, available_sources)

            # Publish assessment results
            self.publish_message('correlation.fidelity_result', {
                'request_id': request.get('request_id'),
                'attack_type': attack_type,
                'confidence_score': assessment['confidence'],
                'fidelity_level': assessment['fidelity'],
                'gaps': assessment['gaps'],
                'recommendations': assessment['recommendations']
            })

        except Exception as e:
            self.logger.error(f"Fidelity assessment error: {e}")

    async def _handle_capability_query(self, request: Dict[str, Any]):
        """Handle capability queries from other engines"""
        try:
            # Get current correlation capabilities
            capabilities = self.correlation_engine.get_rule_statistics()

            # Get active correlations
            active_contexts = len(self.correlation_engine.active_contexts)

            # Determine detectable attacks
            detectable = []
            for rule in self.correlation_engine.rules.values():
                if rule.use_case not in detectable:
                    detectable.append(rule.use_case)

            # Publish capability report
            self.publish_message('correlation.capabilities', {
                'request_id': request.get('request_id'),
                'total_rules': capabilities['total_rules'],
                'active_correlations': active_contexts,
                'detectable_attacks': detectable,
                'coverage_by_type': capabilities['coverage']['by_type'],
                'required_sources': capabilities['coverage']['sources_required']
            })

        except Exception as e:
            self.logger.error(f"Capability query error: {e}")

    async def _handle_correlate_alert(self, data: Dict[str, Any]):
        """
        NEW: Correlate alert with related events to find attack chains

        Searches for related events in database within time window to identify:
        - Related events from same entities (IP, user, host)
        - MITRE ATT&CK chain reconstruction
        - Multi-stage attack patterns
        - Behavioral correlation

        Published to: backend.correlation.complete.{request_id}
        """
        try:
            alert_id = data.get('alert_id')
            entities = data.get('entities', {})
            timestamp = data.get('timestamp')
            mitre_techniques = data.get('mitre_techniques', [])
            request_id = data.get('request_id', f"corr_{alert_id}")

            self.logger.info(f"Correlating alert {alert_id} with {len(entities)} entity types")

            # Parse timestamp for correlation window
            if isinstance(timestamp, str):
                alert_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                alert_time = datetime.utcnow()

            # Define correlation time window (30 minutes before and after)
            time_window_start = alert_time - timedelta(minutes=30)
            time_window_end = alert_time + timedelta(minutes=30)

            # Find related events for each entity type
            related_events = []
            correlation_summary = {
                'total_related_events': 0,
                'events_by_entity_type': {},
                'mitre_chain': [],
                'correlation_score': 0.0,
                'attack_stages_detected': []
            }

            # Database helper functions
            async def db_fetch(pool, query: str, *params):
                """Async helper to execute fetch queries with asyncpg"""
                async with pool.acquire() as conn:
                    return await conn.fetch(query, *params)

            # Query for related events by entity
            for entity_type, entity_values in entities.items():
                if not isinstance(entity_values, list):
                    entity_values = [entity_values]

                entity_events = []

                for entity_value in entity_values:
                    # Query ingestion_logs for events mentioning this entity
                    # Note: This is simplified - real implementation would query indexed entity tables
                    events = await db_fetch(
                        self.db_pool,
                        """
                        SELECT log_id, source_type, log_data, created_at
                        FROM ingestion_logs
                        WHERE created_at >= $1
                          AND created_at <= $2
                          AND log_data::text ILIKE $3
                        ORDER BY created_at ASC
                        LIMIT 50
                        """,
                        time_window_start,
                        time_window_end,
                        f'%{entity_value}%'
                    )

                    for event in events:
                        entity_events.append({
                            'log_id': event['log_id'],
                            'source_type': event['source_type'],
                            'log_data': event['log_data'],
                            'timestamp': event['created_at'].isoformat() if event['created_at'] else None,
                            'entity_type': entity_type,
                            'entity_value': entity_value
                        })

                if entity_events:
                    correlation_summary['events_by_entity_type'][entity_type] = len(entity_events)
                    related_events.extend(entity_events)

            correlation_summary['total_related_events'] = len(related_events)

            # Build MITRE ATT&CK chain from related events
            if mitre_techniques and related_events:
                # Group events by MITRE technique if available
                technique_timeline = []

                for event in related_events:
                    log_data = event.get('log_data', {})
                    if isinstance(log_data, str):
                        import json
                        try:
                            log_data = json.loads(log_data)
                        except:
                            log_data = {}

                    event_techniques = log_data.get('mitre_techniques', [])
                    if event_techniques:
                        for tech in event_techniques:
                            technique_timeline.append({
                                'technique': tech,
                                'timestamp': event.get('timestamp'),
                                'event_id': event.get('log_id')
                            })

                # Sort by timestamp to get attack progression
                technique_timeline.sort(key=lambda x: x.get('timestamp', ''))

                correlation_summary['mitre_chain'] = technique_timeline[:10]  # First 10 techniques

                # Identify attack stages based on MITRE tactics
                attack_stages = set()
                for tech_entry in technique_timeline:
                    tech_id = tech_entry.get('technique', '')
                    # Map techniques to tactics (simplified)
                    if tech_id.startswith('T1'):
                        # Would use MITRE mapper here, simplified for now
                        if any(t in tech_id for t in ['T1078', 'T1136']):
                            attack_stages.add('Initial Access')
                        elif any(t in tech_id for t in ['T1059', 'T1053']):
                            attack_stages.add('Execution')
                        elif any(t in tech_id for t in ['T1547', 'T1543']):
                            attack_stages.add('Persistence')
                        elif any(t in tech_id for t in ['T1548', 'T1134']):
                            attack_stages.add('Privilege Escalation')
                        elif any(t in tech_id for t in ['T1027', 'T1070']):
                            attack_stages.add('Defense Evasion')
                        elif any(t in tech_id for t in ['T1021', 'T1080']):
                            attack_stages.add('Lateral Movement')
                        elif any(t in tech_id for t in ['T1048', 'T1567']):
                            attack_stages.add('Exfiltration')

                correlation_summary['attack_stages_detected'] = sorted(list(attack_stages))

            # Calculate correlation score
            # Higher score = more related events + more attack stages + MITRE chain
            base_score = min(len(related_events) / 10.0, 1.0)  # 10+ events = 1.0
            stage_bonus = len(correlation_summary['attack_stages_detected']) * 0.1
            chain_bonus = 0.2 if len(correlation_summary['mitre_chain']) > 3 else 0

            correlation_summary['correlation_score'] = min(base_score + stage_bonus + chain_bonus, 1.0)

            # Publish correlation results
            await self.publish_message(f'backend.correlation.complete.{request_id}', {
                'alert_id': alert_id,
                'related_events': related_events[:20],  # First 20 for size
                'correlation_summary': correlation_summary,
                'time_window': {
                    'start': time_window_start.isoformat(),
                    'end': time_window_end.isoformat(),
                    'duration_minutes': 60
                },
                'timestamp': datetime.utcnow().isoformat()
            })

            self.logger.info(
                f"Alert {alert_id} correlation complete: "
                f"{correlation_summary['total_related_events']} related events, "
                f"{len(correlation_summary['attack_stages_detected'])} attack stages, "
                f"score: {correlation_summary['correlation_score']:.2f}"
            )

        except Exception as e:
            self.logger.error(f"Error correlating alert {alert_id}: {e}", exc_info=True)
            await self.publish_message(f'backend.correlation.complete.{request_id}', {
                'alert_id': alert_id,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            })

    async def _publish_correlation_alert(self, alert: Dict[str, Any]):
        """Publish correlation alert for consumption by other engines"""
        # Add metadata for downstream processing
        alert['correlation_metadata'] = {
            'engine': 'backend.correlation',
            'timestamp': datetime.utcnow().isoformat(),
            'correlation_id': alert.get('alert_id'),
            'fidelity_score': await self._calculate_fidelity_score(alert)
        }

        # Publish to multiple channels based on severity
        channels_to_publish = ['correlation.alert']

        if alert.get('severity') == 'critical':
            channels_to_publish.append('delivery.high_priority_alert')

        if 'ransomware' in alert.get('use_case', ''):
            channels_to_publish.append('delivery.ransomware_alert')

        for channel in channels_to_publish:
            self.publish_message(channel, alert)
            self.logger.warning(f"Published correlation alert to {channel}: {alert.get('rule_name')}")

    async def _get_source_quality(self, source_type: str) -> Dict[str, Any]:
        """Get quality metrics for a log source"""
        # Query from Redis cache or database
        quality_key = f"source_quality:{source_type}"
        quality_data = self.redis_client.get(quality_key)

        if quality_data:
            return json.loads(quality_data)

        # Default quality scores
        default_qualities = {
            'crowdstrike': {'score': 10, 'tier': 'premium'},
            'sentinel_one': {'score': 9, 'tier': 'premium'},
            'defender': {'score': 8, 'tier': 'good'},
            'elastic': {'score': 7, 'tier': 'good'},
            'wazuh': {'score': 6, 'tier': 'basic'},
            'sysmon': {'score': 7, 'tier': 'basic'},
            'firewall': {'score': 6, 'tier': 'basic'},
            'proxy': {'score': 6, 'tier': 'basic'}
        }

        return default_qualities.get(source_type, {'score': 5, 'tier': 'minimal'})

    async def _calculate_fidelity_score(self, alert: Dict[str, Any]) -> float:
        """Calculate detection fidelity score for an alert"""
        base_score = alert.get('confidence', 0.5)

        # Adjust based on source quality
        sources = alert.get('evidence', {}).get('sources', [])
        quality_scores = []

        for source in sources:
            quality = await self._get_source_quality(source)
            quality_scores.append(quality.get('score', 5) / 10)

        if quality_scores:
            avg_quality = sum(quality_scores) / len(quality_scores)
            fidelity_score = base_score * avg_quality
        else:
            fidelity_score = base_score * 0.5

        return min(1.0, fidelity_score)

    async def _assess_detection_fidelity(self, attack_type: str, sources: List[str]) -> Dict[str, Any]:
        """Assess detection fidelity for specific attack with given sources"""
        # Fidelity mapping for different attacks
        fidelity_map = {
            'lateral_movement': {
                'required': ['edr', 'auth', 'network'],
                'confidence': {
                    'all': 0.95,
                    'partial': 0.60,
                    'minimal': 0.30
                }
            },
            'ransomware': {
                'required': ['edr'],
                'confidence': {
                    'all': 0.98,
                    'partial': 0.70,
                    'minimal': 0.40
                }
            },
            'data_exfiltration': {
                'required': ['proxy', 'dlp', 'edr'],
                'confidence': {
                    'all': 0.85,
                    'partial': 0.55,
                    'minimal': 0.35
                }
            }
        }

        attack_reqs = fidelity_map.get(attack_type, {})
        required = attack_reqs.get('required', [])

        # Calculate coverage
        coverage = len(set(sources) & set(required)) / len(required) if required else 0

        if coverage >= 0.8:
            level = 'all'
            fidelity = 'high'
        elif coverage >= 0.5:
            level = 'partial'
            fidelity = 'moderate'
        else:
            level = 'minimal'
            fidelity = 'low'

        confidence = attack_reqs.get('confidence', {}).get(level, 0.5)

        # Identify gaps
        gaps = list(set(required) - set(sources))

        # Generate recommendations
        recommendations = []
        if gaps:
            recommendations.append(f"Add {', '.join(gaps)} for better {attack_type} detection")

        return {
            'confidence': confidence,
            'fidelity': fidelity,
            'gaps': gaps,
            'recommendations': recommendations
        }

    async def _store_opencti_rule(self, rule: Dict[str, Any]):
        """Store rule generated from OpenCTI"""
        try:
            async with self.db_pool.acquire() as conn:
                # Store in detection_rules table
                await conn.execute("""
                    INSERT INTO detection_rules (rule_id, rule_data, created_at)
                    VALUES ($1, $2, $3)
                    ON CONFLICT (rule_id) DO UPDATE
                    SET rule_data = EXCLUDED.rule_data,
                        updated_at = NOW()
                """,
                    rule['rule_id'],
                    json.dumps(rule),
                    datetime.utcnow()
                )

                # Store Sigma rule if available
                if rule.get('sigma_rule'):
                    # Store in pattern_library for reuse
                    pattern_id = f"opencti_{rule['rule_id']}"
                    await conn.execute("""
                        INSERT INTO pattern_library (pattern_id, pattern_data, pattern_type, created_at)
                        VALUES ($1, $2, $3, $4)
                        ON CONFLICT (pattern_id) DO UPDATE
                        SET pattern_data = EXCLUDED.pattern_data,
                            updated_at = NOW()
                    """,
                        pattern_id,
                        json.dumps({
                            'sigma': rule['sigma_rule'],
                            'metadata': rule
                        }),
                        'sigma',
                        datetime.utcnow()
                    )

            self.logger.info(f"Stored OpenCTI rule: {rule['name']}")

        except Exception as e:
            self.logger.error(f"Failed to store OpenCTI rule: {e}")

    async def _process_attack_patterns(self, patterns: List[Dict[str, Any]]):
        """Process MITRE ATT&CK patterns from OpenCTI"""
        try:
            for pattern in patterns:
                mitre_id = pattern.get('x_mitre_id')
                if not mitre_id:
                    continue

                # Create or update correlation rules based on attack patterns
                kill_chain_phases = pattern.get('killChainPhases', {}).get('edges', [])
                phases = []
                for edge in kill_chain_phases:
                    node = edge.get('node', {})
                    phases.append(node.get('phase_name'))

                # Generate correlation rule for this technique
                if phases:
                    await self._create_attack_pattern_correlation(mitre_id, pattern['name'], phases)

            self.logger.info(f"Processed {len(patterns)} attack patterns from OpenCTI")

        except Exception as e:
            self.logger.error(f"Failed to process attack patterns: {e}")

    async def _create_attack_pattern_correlation(self, mitre_id: str, name: str, phases: List[str]):
        """Create correlation rule for MITRE attack pattern"""
        try:
            # Determine required sources based on kill chain phases
            required_sources = []
            if 'initial-access' in phases or 'execution' in phases:
                required_sources.append('edr')
            if 'lateral-movement' in phases:
                required_sources.extend(['auth', 'network'])
            if 'exfiltration' in phases:
                required_sources.extend(['proxy', 'firewall'])

            if not required_sources:
                required_sources = ['edr']  # Default to EDR

            # Create correlation rule
            rule_name = f"OpenCTI_{mitre_id}_{name.replace(' ', '_')[:30]}"

            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO correlation_rules (
                        rule_name, rule_type, use_case, description,
                        required_sources, mitre_techniques, confidence, enabled
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    ON CONFLICT (rule_name) DO UPDATE
                    SET mitre_techniques = array_append(
                        correlation_rules.mitre_techniques, $9
                    ),
                    updated_at = NOW()
                """,
                    rule_name,
                    'correlation',
                    'opencti_attack_pattern',
                    f"Detection for {name} ({mitre_id})",
                    required_sources,
                    [mitre_id],
                    0.7,  # Default confidence
                    True,
                    mitre_id
                )

            self.logger.debug(f"Created correlation rule for {mitre_id}")

        except Exception as e:
            self.logger.error(f"Failed to create attack pattern correlation: {e}")

    def _map_technique_to_attacks(self, technique: str) -> List[str]:
        """Map MITRE technique to attack types"""
        technique_mapping = {
            'T1055': ['privilege_escalation', 'defense_evasion'],
            'T1003': ['credential_access', 'lateral_movement'],
            'T1021': ['lateral_movement'],
            'T1486': ['ransomware'],
            'T1048': ['data_exfiltration'],
            'T1078': ['insider_threat', 'persistence'],
            'T1190': ['initial_access'],
            'T1059': ['command_and_control'],
            'T1071': ['command_and_control'],
            'T1566': ['phishing']
        }
        return technique_mapping.get(technique, [])

    def _extract_row_data(self, row, indices_map):
        """Helper to extract data from cursor row (handles both tuple and dict)"""
        if isinstance(row, (list, tuple)):
            return {key: row[idx] for key, idx in indices_map.items()}
        else:
            return row

    async def _setup_http_routes(self, app):
        """Setup engine-specific HTTP routes for log source quality and detection fidelity"""

        # ============================================
        # AUTHENTICATION MIDDLEWARE (COMMENTED OUT)
        # ============================================
        # Uncomment the following lines to enable authentication
        # if self.auth_middleware is None:
        #     import redis.asyncio as redis_async
        #     async_redis = await redis_async.Redis(
        #         host=os.getenv('REDIS_HOST', 'localhost'),
        #         port=int(os.getenv('REDIS_PORT', 6379)),
        #         decode_responses=True
        #     )
        #
        #     from auth_middleware import KeycloakAuthMiddleware
        #     self.auth_middleware = KeycloakAuthMiddleware(
        #         keycloak_url=os.getenv('KEYCLOAK_URL', 'http://keycloak:8080'),
        #         realm='siemless',
        #         client_id='siemless-api',
        #         redis_client=async_redis
        #     )
        #     self.logger.info("Authentication middleware initialized")
        #
        # # Add middleware to app
        # app.middlewares.append(self.auth_middleware.auth_middleware)
        # self.logger.info("Authentication middleware added to HTTP app")
        # ============================================

        # Log Source Management Routes
        app.router.add_post('/api/log-sources/register', self._handle_register_log_source)
        app.router.add_get('/api/log-sources/status', self._handle_get_log_sources_status)
        app.router.add_delete('/api/log-sources/{source_id}', self._handle_remove_log_source)

        # Detection Fidelity Routes
        app.router.add_post('/api/detection/fidelity', self._handle_calculate_fidelity)
        app.router.add_get('/api/detection/coverage', self._handle_get_coverage)
        app.router.add_post('/api/detection/technique-coverage', self._handle_technique_coverage)

        # Correlation Capability Routes
        app.router.add_get('/api/correlation/capability', self._handle_assess_correlation_capability)
        app.router.add_post('/api/correlation/requirements', self._handle_check_requirements)
        app.router.add_post('/api/correlation/recommendations', self._handle_get_recommendations)

        # Coverage Gap Analysis Routes
        app.router.add_get('/api/coverage/gaps', self._handle_analyze_gaps)
        app.router.add_post('/api/coverage/simulate', self._handle_simulate_coverage)

        # Apache AGE Graph Visualization Routes
        app.router.add_get('/api/graph/explore', self._handle_graph_explore)
        app.router.add_get('/api/graph/path', self._handle_graph_path)
        app.router.add_get('/api/graph/stats', self._handle_graph_stats)
        app.router.add_get('/api/graph/high-risk', self._handle_graph_high_risk)
        app.router.add_get('/api/graph/centrality', self._handle_graph_centrality)

        # MITRE ATT&CK Integration Routes
        if self.mitre_http_handlers:
            for route in self.mitre_http_handlers.get_routes():
                app.router.add_route(route.method, route.path, route.handler)
            self.logger.info("MITRE ATT&CK API routes configured")

        # Add MITRE AI routes if available
        if self.mitre_ai_http_handlers:
            for route in self.mitre_ai_http_handlers.get_routes():
                app.router.add_route(route.method, route.path, route.handler)
            self.logger.info("MITRE AI Intelligence API routes configured")

        # Rule Deployment Routes
        app.router.add_post('/api/rules/{rule_id}/deploy/elastic', self._handle_deploy_to_elastic)
        app.router.add_post('/api/rules/{rule_id}/deploy/{target}', self._handle_deploy_to_siem)
        app.router.add_post('/api/rules/deploy/bulk', self._handle_bulk_deploy)
        app.router.add_get('/api/rules/{rule_id}/deployment/status', self._handle_deployment_status)
        app.router.add_put('/api/rules/{rule_id}/deployment/elastic/{elastic_rule_id}', self._handle_update_elastic_rule)
        app.router.add_delete('/api/rules/deployment/elastic/{elastic_rule_id}', self._handle_delete_elastic_rule)
        self.logger.info("Rule deployment API routes configured")

        self.logger.info("Log source quality API routes configured")
        self.logger.info("Apache AGE graph API routes configured")

    async def _handle_register_log_source(self, request):
        """Register a new log source with quality assessment"""
        try:
            from aiohttp import web
            import json
            import uuid

            data = await request.json()

            # Generate unique source_id
            source_id = f"{data.get('type', 'unknown')}-{str(uuid.uuid4())[:8]}"

            # Register the log source with quality engine
            source_config = {
                'type': data.get('type', 'endpoint'),
                'product': data.get('product', ''),
                'capabilities': data.get('capabilities', [])
            }

            # Register with quality engine
            result = await self.log_source_quality.register_source(
                source_name=data.get('name'),
                source_config=source_config
            )

            # Extract values safely
            tier = result.get('tier', 'BRONZE') if result else 'BRONZE'
            score = result.get('score', 50) if result else 50

            # Store in database with ON CONFLICT to handle duplicates
            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow("""
                    INSERT INTO log_sources (source_id, source_name, source_type, quality_tier, quality_score)
                    VALUES ($1, $2, $3, $4, $5)
                    ON CONFLICT (source_name) DO UPDATE SET
                        source_type = EXCLUDED.source_type,
                        quality_tier = EXCLUDED.quality_tier,
                        quality_score = EXCLUDED.quality_score,
                        updated_at = CURRENT_TIMESTAMP
                    RETURNING source_id
                """,
                    source_id,
                    data.get('name'),
                    data.get('type', 'endpoint'),
                    tier,
                    score
                )

                # Get the actual source_id (might be existing one if conflict)
                if row:
                    source_id = row['source_id']

            # Build response
            response = {
                'source_id': source_id,
                'status': 'registered',
                'name': data.get('name'),
                'type': data.get('type', 'endpoint'),
                'tier': tier,
                'score': score
            }

            # Publish update
            if self.redis_client:
                update_msg = {
                    'event': 'log_source_registered',
                    'source_id': source_id,
                    'source': data.get('name'),
                    'tier': tier
                }
                self.redis_client.publish('backend.log_sources', json.dumps(update_msg))

            return web.json_response(response, status=201)

        except Exception as e:
            self.logger.error(f"Failed to register log source: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return web.json_response({'error': str(e)}, status=500)
    async def _handle_get_log_sources_status(self, request):
        """Get status of all registered log sources"""
        try:
            from aiohttp import web
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT source_id, source_name, source_type, quality_tier,
                           quality_score, capabilities, configuration,
                           created_at, updated_at
                    FROM log_sources
                    ORDER BY quality_score DESC
                """)

            sources = []
            if rows:
                for row in rows:
                    # Handle both tuple and dict-like cursor results
                    if isinstance(row, (list, tuple)):
                        sources.append({
                            'source_id': self._extract_row_data(row, {'name': 0, 'category': 1, 'tier': 2})['name'],
                            'name': self._extract_row_data(row, {'name': 0, 'category': 1, 'tier': 2})['category'],
                            'type': self._extract_row_data(row, {'name': 0, 'category': 1, 'tier': 2})['tier'],
                            'tier': row[3],
                            'quality_score': float(row[4]) if row[4] else 0,
                            'capabilities': json.loads(row[5]) if row[5] and isinstance(row[5], str) else (row[5] if row[5] else []),
                            'configuration': json.loads(row[6]) if row[6] and isinstance(row[6], str) else (row[6] if row[6] else {}),
                            'registered_at': row[7].isoformat() if row[7] else None,
                            'last_updated': row[8].isoformat() if row[8] else None
                        })
                    else:
                        # If it's a dict-like object
                        sources.append({
                            'source_id': row['source_id'],
                            'name': row['source_name'],
                            'type': row['source_type'],
                            'tier': row['quality_tier'],
                            'quality_score': float(row['quality_score']) if row['quality_score'] else 0,
                            'capabilities': json.loads(row['capabilities']) if row['capabilities'] and isinstance(row['capabilities'], str) else (row['capabilities'] if row['capabilities'] else []),
                            'configuration': json.loads(row['configuration']) if row['configuration'] and isinstance(row['configuration'], str) else (row['configuration'] if row['configuration'] else {}),
                            'registered_at': row['created_at'].isoformat() if row['created_at'] else None,
                            'last_updated': row['updated_at'].isoformat() if row['updated_at'] else None
                        })

            # Calculate overall environment quality
            if sources:
                avg_score = sum(s['quality_score'] for s in sources) / len(sources)
                overall_tier = self.log_source_quality._get_tier_from_score(avg_score)
            else:
                avg_score = 0
                overall_tier = 'NONE'

            response = {
                'total_sources': len(sources),
                'sources': sources,
                'environment_quality': {
                    'average_score': round(avg_score, 1),
                    'overall_tier': overall_tier
                }
            }

            return web.json_response(response)

        except Exception as e:
            import traceback
            self.logger.error(f"Failed to get log sources status: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return web.json_response({'error': str(e), 'type': type(e).__name__}, status=500)

    async def _handle_remove_log_source(self, request):
        """Remove a registered log source"""
        try:
            from aiohttp import web
            source_id = request.match_info['source_id']

            async with self.db_pool.acquire() as conn:
                result = await conn.fetchrow("""
                    DELETE FROM log_sources WHERE source_id = $1
                    RETURNING source_name
                """, source_id)

            if result:
                await self.publish_message('backend.log_source_removed', {
                    'source_id': source_id,
                    'source_name': result['source_name']
                })
                return web.json_response({'message': f'Log source {source_id} removed'})
            else:
                return web.json_response({'error': 'Log source not found'}, status=404)

        except Exception as e:
            self.logger.error(f"Failed to remove log source: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _handle_calculate_fidelity(self, request):
        """Calculate detection fidelity for specific attacks"""
        try:
            self.logger.debug(f"Handling _handle_calculate_fidelity request")
            from aiohttp import web
            request_data = await request.json()

            # Get current log sources from database
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT source_name, source_type, quality_tier
                    FROM log_sources
                """)

            available_sources = []
            for row in rows:
                available_sources.append({
                    'name': row['source_name'],
                    'category': row['source_type'],
                    'tier': row['quality_tier']
                })

            # Calculate fidelity for requested attacks
            attack_types = request_data.get('attack_types', [])

            if not attack_types:
                # If no specific attacks requested, check all available
                attack_types = ['lateral_movement', 'ransomware', 'data_exfiltration']
            results = {}

            for attack_type in attack_types:
                fidelity = self.correlation_requirements.check_requirements_met(
                    attack_type, available_sources
                )
                results[attack_type] = {
                    'confidence': fidelity['confidence'],
                    'requirements_met': fidelity['requirements_met'],
                    'missing_sources': fidelity.get('missing_sources', [])
                }

            # Calculate overall environment fidelity
            if results:
                avg_confidence = sum(r['confidence'] for r in results.values()) / len(results)
            else:
                avg_confidence = 0

            return web.json_response({
                'attack_fidelity': results,
                'overall_confidence': round(avg_confidence, 1),
                'available_sources': len(available_sources)
            })

        except Exception as e:
            import traceback
            self.logger.error(f"Failed to calculate fidelity: {e}")
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return web.json_response({'error': str(e), 'type': type(e).__name__}, status=500)

    async def _handle_get_coverage(self, request):
        """Get overall detection coverage based on current log sources"""
        try:
            from aiohttp import web

            # Get current log sources
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT source_name, source_type, quality_tier
                    FROM log_sources
                """)

            available_sources = []
            if rows and len(rows) > 0:
                for row in rows:
                    if isinstance(row, (list, tuple)):
                        available_sources.append({
                            'name': row[0],
                            'category': row[1],
                            'tier': row[2]
                        })
                    else:
                        available_sources.append({
                            'name': row.get('source_name', ''),
                            'category': row.get('source_type', ''),
                            'tier': row.get('quality_tier', '')
                        })

            # Calculate coverage for all known attack types
            all_attacks = self.correlation_requirements.attack_requirements.keys()
            coverage_report = {
                'high_confidence': [],
                'medium_confidence': [],
                'low_confidence': [],
                'not_detectable': []
            }

            for attack_type in all_attacks:
                assessment = self.correlation_requirements.check_requirements_met(
                    attack_type, available_sources
                )

                if assessment['confidence'] >= 80:
                    coverage_report['high_confidence'].append({
                        'attack': attack_type,
                        'confidence': assessment['confidence']
                    })
                elif assessment['confidence'] >= 60:
                    coverage_report['medium_confidence'].append({
                        'attack': attack_type,
                        'confidence': assessment['confidence']
                    })
                elif assessment['confidence'] >= 30:
                    coverage_report['low_confidence'].append({
                        'attack': attack_type,
                        'confidence': assessment['confidence']
                    })
                else:
                    coverage_report['not_detectable'].append({
                        'attack': attack_type,
                        'confidence': assessment['confidence']
                    })

            # Calculate overall statistics
            total_attacks = len(list(all_attacks))
            detectable = len(coverage_report['high_confidence']) + len(coverage_report['medium_confidence'])

            coverage_report['statistics'] = {
                'total_attack_types': total_attacks,
                'detectable_attacks': detectable,
                'coverage_percentage': (detectable / total_attacks * 100) if total_attacks > 0 else 0,
                'source_count': len(available_sources)
            }

            return web.json_response(coverage_report)

        except Exception as e:
            self.logger.error(f"Failed to get coverage: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _handle_technique_coverage(self, request):
        """Calculate coverage for specific MITRE techniques"""
        try:
            from aiohttp import web
            data = await request.json()

            # Accept both 'techniques' and 'technique_ids' for compatibility
            techniques = data.get('techniques', data.get('technique_ids', []))

            if not techniques:
                return web.json_response({'error': 'techniques list is required'}, status=400)

            # Get current log sources - just names
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT source_name
                    FROM log_sources
                """)

            available_sources = []
            if rows:
                for row in rows:
                    if isinstance(row, (tuple, list)):
                        source_name = row[0] if row else ''
                    else:
                        source_name = row.get('source_name', '') if row else ''

                    if source_name:
                        available_sources.append(source_name)

            # Calculate coverage for each technique
            results = {}
            for technique in techniques:
                if hasattr(self.detection_fidelity, 'calculate_technique_coverage'):
                    coverage = self.detection_fidelity.calculate_technique_coverage(
                        technique, available_sources
                    )
                else:
                    # Fallback to mapping technique to attacks
                    attack_types = self._map_technique_to_attacks(technique)
                    if attack_types and available_sources:
                        fidelity = self.detection_fidelity.calculate_detection_fidelity(
                            attack_types, available_sources
                        )
                        confidence = max([
                            fidelity.get('attack_fidelity', {}).get(at, {}).get('confidence', 0)
                            for at in attack_types
                        ], default=0)
                        coverage = {
                            'covered': confidence > 70,
                            'confidence': confidence,
                            'attack_types': attack_types
                        }
                    else:
                        coverage = {
                            'covered': False,
                            'confidence': 0,
                            'attack_types': attack_types if attack_types else []
                        }

                results[technique] = coverage

            return web.json_response(results)

        except Exception as e:
            self.logger.error(f"Failed to calculate technique coverage: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return web.json_response({'error': str(e)}, status=500)

    async def _handle_assess_correlation_capability(self, request):
        """Assess current correlation capabilities"""
        try:
            from aiohttp import web

            # Get all log sources with their details
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT source_name, source_type, quality_tier, quality_score
                    FROM log_sources
                """)

            available_sources = []

            # Ensure all sources are registered with quality engine
            if rows:
                for row in rows:
                    if row:  # Check row is not None
                        if isinstance(row, (tuple, list)) and len(row) >= 4:
                            source_name = row[0]
                            source_type = row[1]
                            tier = row[2]
                            score = row[3]
                        elif isinstance(row, dict):
                            source_name = row.get('source_name', '')
                            source_type = row.get('source_type', 'unknown')
                            tier = row.get('quality_tier', 'BRONZE')
                            score = row.get('quality_score', 50)
                        else:
                            continue

                        if source_name:
                            # Ensure source is in active_sources
                            if source_name not in self.log_source_quality.active_sources:
                                # Register it with proper format
                                self.log_source_quality.active_sources[source_name] = {
                                    'config': {
                                        'type': source_type,
                                        'tier': tier,
                                        'base_score': score
                                    },
                                    'tier': tier,  # Ensure tier is accessible
                                    'quality_score': score,
                                    'last_seen': None,
                                    'event_count': 0
                                }

                            available_sources.append(source_name)

            # Now assess correlation capability with properly registered sources
            capability = {}
            if available_sources:
                try:
                    capability = self.log_source_quality.assess_correlation_capability(available_sources)
                except Exception as e:
                    self.logger.warning(f"Error assessing capability: {e}")
                    # Provide basic capability assessment
                    capability = {
                        'composite_score': len(available_sources) * 10,
                        'source_count': len(available_sources),
                        'overall_assessment': 'OPERATIONAL'
                    }

            return web.json_response({
                'status': 'success',
                'source_count': len(available_sources),
                'sources': available_sources,
                'capability': capability
            })

        except Exception as e:
            self.logger.error(f"Failed to assess correlation capability: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return web.json_response({'error': str(e)}, status=500)
    async def _handle_check_requirements(self, request):
        """Check if correlation requirements are met for an attack type"""
        try:
            from aiohttp import web
            data = await request.json()
            attack_type = data.get('attack_type')

            if not attack_type:
                return web.json_response({'error': 'attack_type is required'}, status=400)

            # Get current log sources
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT source_name, source_type, quality_tier
                    FROM log_sources
                """)

            current_sources = []
            if rows:
                for row in rows:
                    if isinstance(row, (tuple, list)):
                        current_sources.append({
                            'name': row[0],
                            'category': row[1],
                            'tier': row[2]
                        })
                    else:
                        current_sources.append({
                            'name': row.get('source_name', ''),
                            'category': row.get('source_type', ''),
                            'tier': row.get('quality_tier', '')
                        })

            # Check requirements
            result = self.correlation_requirements.check_requirements_met(
                attack_type, current_sources
            )

            return web.json_response(result)

        except Exception as e:
            self.logger.error(f"Failed to check correlation requirements: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _handle_get_recommendations(self, request):
        """Get recommendations for improving detection capability"""
        try:
            from aiohttp import web
            data = await request.json()
            target_attacks = data.get('target_attacks', [])

            if not target_attacks:
                # Use all attack types if none specified
                target_attacks = list(self.correlation_requirements.attack_requirements.keys())

            # Get current log sources
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT source_name, source_type, quality_tier
                    FROM log_sources
                """)

            current_sources = []
            if rows:
                for row in rows:
                    if isinstance(row, (tuple, list)):
                        current_sources.append({
                            'name': row[0],
                            'category': row[1],
                            'tier': row[2]
                        })
                    else:
                        current_sources.append({
                            'name': row.get('source_name', ''),
                            'category': row.get('source_type', ''),
                            'tier': row.get('quality_tier', '')
                        })

            # Get recommendations
            recommendations = self.correlation_requirements.recommend_sources(
                current_sources, target_attacks
            )

            return web.json_response(recommendations)

        except Exception as e:
            self.logger.error(f"Failed to get recommendations: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _handle_analyze_gaps(self, request):
        """Analyze coverage gaps in current configuration"""
        try:
            from aiohttp import web

            # Get current log sources
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT source_type, quality_tier, COUNT(*)
                    FROM log_sources
                    GROUP BY source_type, quality_tier
                """)

            current_coverage = {}
            if rows:
                for row in rows:
                    if isinstance(row, (tuple, list)):
                        source_type = row[0]
                        tier = row[1]
                        count = row[2]
                    else:
                        source_type = row.get('source_type', '')
                        tier = row.get('quality_tier', '')
                        count = row.get('count', 0)

                    if source_type not in current_coverage:
                        current_coverage[source_type] = {'tiers': {}, 'total': 0}
                    current_coverage[source_type]['tiers'][tier] = count
                    current_coverage[source_type]['total'] += count

            # Identify gaps
            gaps = {
                'missing_categories': [],
                'low_quality_categories': [],
                'single_source_categories': [],
                'recommendations': []
            }

            # Check for missing critical categories
            critical_categories = ['endpoint', 'network', 'identity']
            for category in critical_categories:
                if category not in current_coverage:
                    gaps['missing_categories'].append(category)
                    gaps['recommendations'].append({
                        'priority': 'critical',
                        'action': f'Add {category} log source',
                        'impact': 'Major improvement in detection capability'
                    })
                elif current_coverage[category]['total'] == 1:
                    gaps['single_source_categories'].append(category)
                    gaps['recommendations'].append({
                        'priority': 'high',
                        'action': f'Add redundant {category} log source',
                        'impact': 'Improved reliability and coverage'
                    })

            # Check for low quality categories
            for category, data in current_coverage.items():
                tiers = data['tiers']
                # If only BRONZE or no GOLD/PLATINUM sources
                if ('PLATINUM' not in tiers and 'GOLD' not in tiers):
                    gaps['low_quality_categories'].append(category)
                    gaps['recommendations'].append({
                        'priority': 'medium',
                        'action': f'Upgrade {category} to higher quality source',
                        'impact': 'Better detection accuracy'
                    })

            return web.json_response(gaps)

        except Exception as e:
            self.logger.error(f"Failed to analyze gaps: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _handle_simulate_coverage(self, request):
        """Simulate coverage improvements"""
        try:
            from aiohttp import web
            data = await request.json()
            add_sources = data.get('add_sources', [])

            # Get current log sources
            async with self.db_pool.acquire() as conn:
                rows = await conn.fetch("""
                    SELECT source_name, source_type, quality_tier
                    FROM log_sources
                """)

            current_sources = []
            if rows:
                for row in rows:
                    if isinstance(row, (tuple, list)):
                        current_sources.append({
                            'name': row[0],
                            'category': row[1],
                            'tier': row[2]
                        })
                    else:
                        current_sources.append({
                            'name': row.get('source_name', ''),
                            'category': row.get('source_type', ''),
                            'tier': row.get('quality_tier', '')
                        })

            # Simulate adding new sources
            simulated_sources = current_sources.copy()
            simulated_sources.extend(add_sources)

            # Extract source names for fidelity calculation
            current_names = [s['name'] for s in current_sources]
            simulated_names = [s['name'] for s in simulated_sources]

            # Calculate improvements
            current_fidelity = self.detection_fidelity.calculate_detection_fidelity(
                ['ransomware', 'lateral_movement', 'data_exfiltration'],
                current_names
            )

            improved_fidelity = self.detection_fidelity.calculate_detection_fidelity(
                ['ransomware', 'lateral_movement', 'data_exfiltration'],
                simulated_names
            )

            # Calculate improvement metrics
            improvements = {
                'added_sources': add_sources,
                'current_overall': current_fidelity['overall_confidence'],
                'improved_overall': improved_fidelity['overall_confidence'],
                'improvement_percentage': improved_fidelity['overall_confidence'] - current_fidelity['overall_confidence'],
                'attack_improvements': {}
            }

            for attack_type in ['ransomware', 'lateral_movement', 'data_exfiltration']:
                current = current_fidelity['attack_fidelity'].get(attack_type, {}).get('confidence', 0)
                improved = improved_fidelity['attack_fidelity'].get(attack_type, {}).get('confidence', 0)
                improvements['attack_improvements'][attack_type] = {
                    'current': current,
                    'improved': improved,
                    'gain': improved - current
                }

            return web.json_response(improvements)

        except Exception as e:
            self.logger.error(f"Failed to simulate coverage: {e}")
            return web.json_response({'error': str(e)}, status=500)

    # ============================================================================
    # LOG SOURCE QUALITY REDIS INTERFACE - All 11 endpoints via pub/sub
    # ============================================================================

    
    
    
    
    
    
    
    
    
    
    
    # ============================================
    # Apache AGE Graph API Handlers
    # ============================================

    async def _handle_graph_explore(self, request):
        """API: Explore entity relationships for visualization"""
        try:
            from aiohttp import web

            entity_id = request.query.get('entity_id')
            depth = int(request.query.get('depth', 2))

            if not entity_id:
                return web.json_response({'error': 'entity_id required'}, status=400)

            if not self.age_service:
                return web.json_response({'error': 'Graph service not initialized'}, status=503)

            graph_data = await self.age_service.explore_entity(entity_id, depth)
            return web.json_response(graph_data)

        except Exception as e:
            self.logger.error(f"Graph explore error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _handle_graph_path(self, request):
        """API: Find shortest path between two entities"""
        try:
            from aiohttp import web

            source_id = request.query.get('source')
            target_id = request.query.get('target')
            max_hops = int(request.query.get('max_hops', 10))

            if not source_id or not target_id:
                return web.json_response({'error': 'source and target required'}, status=400)

            if not self.age_service:
                return web.json_response({'error': 'Graph service not initialized'}, status=503)

            path_data = await self.age_service.find_path(source_id, target_id, max_hops)
            return web.json_response(path_data)

        except Exception as e:
            self.logger.error(f"Graph path error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _handle_graph_stats(self, request):
        """API: Get graph statistics"""
        try:
            from aiohttp import web

            if not self.age_service:
                return web.json_response({'error': 'Graph service not initialized'}, status=503)

            stats = await self.age_service.get_graph_stats()
            return web.json_response(stats)

        except Exception as e:
            self.logger.error(f"Graph stats error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _handle_graph_high_risk(self, request):
        """API: Find high-risk connections for an entity"""
        try:
            from aiohttp import web

            entity_id = request.query.get('entity_id')
            risk_threshold = int(request.query.get('risk_threshold', 7))

            if not entity_id:
                return web.json_response({'error': 'entity_id required'}, status=400)

            if not self.age_service:
                return web.json_response({'error': 'Graph service not initialized'}, status=503)

            high_risk = await self.age_service.find_high_risk_connections(entity_id, risk_threshold)
            return web.json_response({'high_risk_entities': high_risk})

        except Exception as e:
            self.logger.error(f"Graph high-risk error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _handle_graph_centrality(self, request):
        """API: Get most connected entities (centrality analysis)"""
        try:
            from aiohttp import web

            limit = int(request.query.get('limit', 20))

            if not self.age_service:
                return web.json_response({'error': 'Graph service not initialized'}, status=503)

            central_entities = await self.age_service.get_entity_centrality(limit)
            return web.json_response({'central_entities': central_entities})

        except Exception as e:
            self.logger.error(f"Graph centrality error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _retention_cleanup_task(self):
        """
        Periodic task to clean up expired logs based on retention policies

        Runs every 24 hours and:
        1. Identifies expired logs based on retention decisions
        2. Moves logs through storage tiers (hot -> warm -> cold -> archive)
        3. Permanently deletes logs past archive retention
        4. Logs cleanup statistics
        """
        while self.is_running:
            try:
                self.logger.info("Starting retention cleanup cycle...")

                # Run cleanup
                stats = await self.retention_engine.cleanup_expired_logs()

                # Log results
                self.logger.info(
                    f"Retention cleanup complete: "
                    f"checked={stats['total_checked']}, "
                    f"expired={stats['expired']}, "
                    f"moved_to_cold={stats['moved_to_cold']}, "
                    f"moved_to_archive={stats['moved_to_archive']}, "
                    f"deleted={stats['deleted']}"
                )

                # Get retention statistics
                retention_stats = await self.retention_engine.get_retention_stats()
                self.logger.info(f"Current retention stats: {retention_stats}")

                # Publish stats to Redis for dashboard
                self.publish_message('backend.retention_stats', {
                    'cleanup_stats': stats,
                    'retention_stats': retention_stats,
                    'timestamp': datetime.utcnow().isoformat()
                })

            except Exception as e:
                self.logger.error(f"Retention cleanup error: {e}", exc_info=True)

            # Run every 24 hours
            await asyncio.sleep(86400)

    # ========================================================================
    # Rule Deployment HTTP Handlers
    # ========================================================================

    async def _handle_deploy_to_elastic(self, request):
        """DEPRECATED: Deployment moved to Ingestion Engine"""
        from aiohttp import web
        return web.json_response({
            'error': 'Deployment functionality moved to Ingestion Engine',
            'message': 'Use POST http://localhost:8003/api/rules/{rule_id}/deploy/elastic instead',
            'reason': 'Architectural fix: Backend should not connect to external SIEMs'
        }, status=410)

    async def _handle_deploy_to_siem(self, request):
        """DEPRECATED: Deployment moved to Ingestion Engine"""
        from aiohttp import web
        target = request.match_info.get('target', 'unknown')
        return web.json_response({
            'error': 'Deployment functionality moved to Ingestion Engine',
            'message': f'Use POST http://localhost:8003/api/rules/{{rule_id}}/deploy/{target} instead',
            'reason': 'Architectural fix: Backend should not connect to external SIEMs'
        }, status=410)

    async def _handle_bulk_deploy(self, request):
        """DEPRECATED: Deployment moved to Ingestion Engine"""
        from aiohttp import web
        return web.json_response({
            'error': 'Deployment functionality moved to Ingestion Engine',
            'message': 'Use POST http://localhost:8003/api/rules/deploy/bulk instead',
            'reason': 'Architectural fix: Backend should not connect to external SIEMs'
        }, status=410)

    async def _handle_deployment_status(self, request):
        """Get deployment status for a rule"""
        from aiohttp import web
        try:
            rule_id = request.match_info['rule_id']

            # Fetch deployment status from database
            query = """
                SELECT
                    deployed_to_elastic,
                    elastic_rule_id,
                    elastic_version,
                    deployed_to_splunk,
                    splunk_rule_id,
                    deployed_to_sentinel,
                    sentinel_rule_id,
                    deployed_to_qradar,
                    qradar_rule_id,
                    last_deployed_at
                FROM detection_rules
                WHERE rule_id = $1
            """

            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow(query, rule_id)

            if not row:
                return web.json_response({'error': 'Rule not found'}, status=404)

            return web.json_response({
                'rule_id': rule_id,
                'elastic': {
                    'deployed': row[0] if row[0] is not None else False,
                    'rule_id': row[1],
                    'version': row[2]
                },
                'splunk': {
                    'deployed': row[3] if row[3] is not None else False,
                    'rule_id': row[4]
                },
                'sentinel': {
                    'deployed': row[5] if row[5] is not None else False,
                    'rule_id': row[6]
                },
                'qradar': {
                    'deployed': row[7] if row[7] is not None else False,
                    'rule_id': row[8]
                },
                'last_deployed_at': row[9].isoformat() if row[9] else None
            })

        except Exception as e:
            self.logger.error(f"Deployment status error: {e}", exc_info=True)
            return web.json_response({'error': str(e)}, status=500)

    async def _handle_update_elastic_rule(self, request):
        """DEPRECATED: Deployment moved to Ingestion Engine"""
        from aiohttp import web
        return web.json_response({
            'error': 'Deployment functionality moved to Ingestion Engine',
            'message': 'Use PUT http://localhost:8003/api/rules/{rule_id}/deployment/elastic/{elastic_rule_id} instead',
            'reason': 'Architectural fix: Backend should not connect to external SIEMs'
        }, status=410)

    async def _handle_delete_elastic_rule(self, request):
        """DEPRECATED: Deployment moved to Ingestion Engine"""
        from aiohttp import web
        return web.json_response({
            'error': 'Deployment functionality moved to Ingestion Engine',
            'message': 'Use DELETE http://localhost:8003/api/rules/deployment/elastic/{elastic_rule_id} instead',
            'reason': 'Architectural fix: Backend should not connect to external SIEMs'
        }, status=410)

    async def _get_rule_from_db(self, rule_id: str) -> Optional[Dict[str, Any]]:
        """Fetch rule from database"""
        try:
            query = """
                SELECT
                    rule_id, rule_name, rule_type, rule_content, description,
                    severity, mitre_tactics, mitre_techniques, enabled, tags,
                    cti_source, cti_indicator_id, created_by, created_at
                FROM detection_rules
                WHERE rule_id = $1
            """

            async with self.db_pool.acquire() as conn:
                row = await conn.fetchrow(query, rule_id)

            if not row:
                return None

            return {
                'rule_id': row[0],
                'rule_name': row[1],
                'rule_type': row[2],
                'rule_content': row[3],
                'description': row[4],
                'severity': row[5],
                'mitre_tactics': row[6] if row[6] else [],
                'mitre_techniques': row[7] if row[7] else [],
                'enabled': row[8] if row[8] is not None else True,
                'tags': row[9] if row[9] else [],
                'cti_source': row[10],
                'cti_indicator_id': row[11],
                'created_by': row[12],
                'created_at': row[13]
            }

        except Exception as e:
            self.logger.error(f"Database query error: {e}", exc_info=True)
            return None

    async def _update_rule_deployment_status(
        self,
        rule_id: str,
        target: str,
        result: Dict[str, Any]
    ):
        """Update rule deployment status in database"""
        try:
            # Build update query based on target
            if target == 'elastic':
                query = """
                    UPDATE detection_rules
                    SET
                        deployed_to_elastic = true,
                        elastic_rule_id = $1,
                        elastic_version = $2,
                        last_deployed_at = NOW()
                    WHERE rule_id = $3
                """
                params = (
                    result.get('elastic_rule_id'),
                    result.get('elastic_version', 1),
                    rule_id
                )
            elif target == 'splunk':
                query = """
                    UPDATE detection_rules
                    SET
                        deployed_to_splunk = true,
                        splunk_rule_id = $1,
                        last_deployed_at = NOW()
                    WHERE rule_id = $2
                """
                params = (result.get('splunk_rule_id'), rule_id)
            elif target == 'sentinel':
                query = """
                    UPDATE detection_rules
                    SET
                        deployed_to_sentinel = true,
                        sentinel_rule_id = $1,
                        last_deployed_at = NOW()
                    WHERE rule_id = $2
                """
                params = (result.get('sentinel_rule_id'), rule_id)
            elif target == 'qradar':
                query = """
                    UPDATE detection_rules
                    SET
                        deployed_to_qradar = true,
                        qradar_rule_id = $1,
                        last_deployed_at = NOW()
                    WHERE rule_id = $2
                """
                params = (result.get('qradar_rule_id'), rule_id)
            else:
                return

            async with self.db_pool.acquire() as conn:
                await conn.execute(query, *params)

            self.logger.info(f"✅ Updated deployment status for rule {rule_id} ({target})")

        except Exception as e:
            self.logger.error(f"Deployment status update error: {e}", exc_info=True)


if __name__ == "__main__":
    import asyncio

    async def main():
        engine = BackendEngine()
        await engine.start()

    asyncio.run(main())