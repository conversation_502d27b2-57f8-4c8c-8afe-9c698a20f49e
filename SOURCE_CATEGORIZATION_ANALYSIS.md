# Source Categorization Analysis - CRITICAL

**Date**: October 4, 2025
**Priority**: CRITICAL - Must be resolved before Phase 2.4 UI Integration
**Status**: Identified, Analysis Pending

---

## Problem Statement

**User Observation**:
> "We need to analyse log source analysis and ensure that it's in an understandable format. There are nested log sources because we pull from normalised data sources like Elastic - the question is whether we understand it and know how to categorise it properly to be able to offer it here as well."

**Core Issue**: The query generator doesn't distinguish between:
- **Collection Platform** (WHERE logs are stored): Elastic, Splunk, Sentinel
- **Actual Log Source** (WHAT data we have): Windows Events, Sysmon, Firewall, etc.

---

## Current State (BROKEN)

### Database Schema
```sql
-- ingestion_logs table
source_type VARCHAR(50)  -- Only stores platform: 'elastic_security', 'fortinet', etc.
```

### Query Template Example (Current)
```sql
-- elastic_security template
"Search all Elastic Security logs for this host"
```

**Problem**: Analyst doesn't know if they're searching:
- Windows Security Events via Elastic
- Sysmon data via Elastic
- Firewall logs via Elastic
- Network telemetry via Elastic

All appear as generic "elastic_security" queries.

---

## Required State (CORRECT)

### Two-Level Hierarchy

```
Platform (Collection/Aggregation Layer)
├── Elastic Security
│   ├── Windows Security Events
│   ├── Sysmon
│   ├── Firewall Logs (various vendors)
│   └── EDR Telemetry
├── Splunk
│   ├── Windows Security Events
│   ├── Sysmon
│   └── Application Logs
└── Fortinet (Direct Source)
    └── Firewall Logs
```

### Database Schema Options

**Option 1: Separate Columns**
```sql
ALTER TABLE ingestion_logs
ADD COLUMN platform VARCHAR(50),        -- 'elastic', 'splunk', 'sentinel', 'fortinet'
ADD COLUMN actual_source VARCHAR(100),  -- 'windows_security_events', 'sysmon', etc.
ADD COLUMN source_vendor VARCHAR(50);   -- 'microsoft', 'fortinet', 'paloalto'
```

**Option 2: JSONB Metadata (More Flexible)**
```sql
log_data JSONB
{
  "source_metadata": {
    "platform": "elastic_security",
    "actual_source": "windows_security_events",
    "vendor": "microsoft",
    "dataset": "event.dataset:windows.security",
    "integration": "winlogbeat"
  },
  ...
}
```

---

## Detection Logic

### How to Identify Actual Source

**From Elastic Logs** (ECS format):
```json
{
  "event.dataset": "windows.security",     // → windows_security_events
  "agent.type": "winlogbeat",              // → windows logs
  "log.source": "Microsoft-Windows-Security-Auditing"
}

{
  "event.dataset": "sysmon",               // → sysmon
  "log.source": "Microsoft-Windows-Sysmon"
}

{
  "event.dataset": "fortinet.firewall",    // → fortinet_firewall (via Elastic)
  "observer.vendor": "Fortinet"
}
```

**From Splunk Logs**:
```json
{
  "sourcetype": "WinEventLog:Security",    // → windows_security_events
  "source": "WinEventLog:Security"
}

{
  "sourcetype": "XmlWinEventLog:Microsoft-Windows-Sysmon/Operational"  // → sysmon
}
```

**From Direct Sources** (Fortinet, Palo Alto, etc.):
```json
{
  "platform": "fortinet",
  "actual_source": "fortigate_firewall"    // Same as platform for direct sources
}
```

---

## Query Template Evolution

### Current Template (Generic)
```sql
-- Template for: elastic_security
source_type: "elastic_security"
query: "host.name:{entity_value}"
description: "Search Elastic Security for this host"
```

**Problem**: Too vague - what data type am I searching?

### Proposed Template (Specific)
```sql
-- Template for: windows_security_events (via any platform)
actual_source: "windows_security_events"
platform: "any"  -- Works on Elastic, Splunk, Sentinel
query_elastic: "event.dataset:windows.security AND host.name:{entity_value}"
query_splunk: "sourcetype=WinEventLog:Security host={entity_value}"
query_sentinel: "SecurityEvent | where Computer == '{entity_value}'"
description: "Search Windows Security Events for authentication, privilege escalation"
what_to_look_for: ["Failed logons", "Privilege changes", "Account creation"]
```

**Benefit**:
- Analyst knows WHAT data they're searching
- Template adapts query syntax to platform
- Same logical search works across multiple platforms

---

## Implementation Plan

### Phase 1: Analysis (1 hour)
1. **Audit Current Data**:
   ```sql
   -- Sample ingestion_logs to see actual structure
   SELECT
     source_type,
     log_data->'event'->'dataset' as dataset,
     log_data->'agent'->'type' as agent,
     log_data->'log'->'source' as log_source,
     COUNT(*)
   FROM ingestion_logs
   GROUP BY 1, 2, 3, 4
   LIMIT 50;
   ```

2. **Check Existing Categorization**:
   - Review [log_source_identifier.py](engines/backend/log_source_identifier.py:1)
   - Check if source detection already exists
   - Identify which fields are most reliable

3. **Map Platform → Source Patterns**:
   - Create mapping rules for each platform
   - Document detection logic

### Phase 2: Schema Design (30 min)
1. Decide: Columns vs JSONB metadata
2. Design migration path (don't break existing code)
3. Update warm_storage schema if needed

### Phase 3: Implementation (2-3 hours)
1. **Update Ingestion**:
   - Modify ingestion pipeline to detect actual_source
   - Add source categorization logic
   - Store in new columns or JSONB

2. **Update Query Templates**:
   - Rewrite 15 templates to use actual_source
   - Add platform-specific query variants
   - Update seed_query_templates.sql

3. **Update Query Generator**:
   - Query by actual_source, not just platform
   - Return platform info for context
   - Handle platform-specific query syntax

### Phase 4: Testing (30 min)
1. Verify source detection accuracy
2. Test query generation with categorized sources
3. Validate template matching logic

---

## Expected Outcomes

### Before (Current State)
```
Analyst investigates host "SERVER-01"
Query Generator says:
  "Search Elastic Security" ← What does this mean?
```

### After (Correct State)
```
Analyst investigates host "SERVER-01"
Query Generator says:
  ✅ "Search Windows Security Events (via Elastic Security)"
     → Shows: 4,523 logs | Last 7 days
     → Look for: Failed logons, privilege escalation

  ✅ "Search Sysmon Process Creation (via Elastic Security)"
     → Shows: 12,847 logs | Last 7 days
     → Look for: Suspicious process chains, injection

  ⚠️ "Search Firewall Logs (via Fortinet)"
     → Shows: 0 logs | Not available
     → Would show: Network connections, blocked traffic
```

**Analyst Now Knows**:
- WHAT types of data are available
- HOW MUCH data exists for each type
- WHAT to look for in each data type
- WHICH sources are missing

---

## Files to Review

### Ingestion Layer
- `engines/ingestion/ingestion_engine.py` - How logs are ingested
- `engines/backend/log_source_identifier.py` - Existing source detection (if any)

### Contextualization Layer
- `engines/contextualization/adaptive_entity_extractor.py` - May already detect source
- `engines/contextualization/log_schema_detector.py` - Schema detection logic

### Database
- Check `ingestion_logs` for existing metadata
- Check `warm_storage` for entity source info
- Review `log_schemas` table structure

### Query Generator
- `engines/delivery/query_generator.py` - Needs update
- `seed_query_templates.sql` - Needs rewrite

---

## Success Criteria

✅ **Source Detection**:
- 95%+ accuracy in identifying actual source from log structure
- Handles all major platforms (Elastic, Splunk, Sentinel, direct sources)

✅ **Query Templates**:
- Templates organized by actual source (Windows Events, Sysmon, etc.)
- Platform-specific query syntax handled automatically
- Clear descriptions of what each source contains

✅ **Analyst Experience**:
- Sees actual data types, not just platforms
- Knows what to look for in each source
- Can identify data gaps immediately

---

## Risk Mitigation

**Risk**: Breaking existing ingestion pipeline
**Mitigation**:
- Add new columns/metadata without removing old ones
- Backwards compatibility: if actual_source = NULL, use source_type
- Gradual migration: new logs get categorized, old logs work as-is

**Risk**: Incorrect source detection
**Mitigation**:
- Conservative detection: only categorize when confident
- Log detection failures for review
- Manual override capability in database

**Risk**: Template complexity explosion
**Mitigation**:
- Universal source types (windows_security_events works everywhere)
- Platform-specific query syntax in template, not separate templates
- Start with 5-10 most common sources, expand later

---

## Next Steps

1. **IMMEDIATE**: Run analysis queries on ingestion_logs
2. **Session 1**: Design categorization schema and migration
3. **Session 2**: Implement detection logic and update templates
4. **Session 3**: Test and validate, then proceed with Phase 2.4 UI

**DO NOT proceed with UI integration until this is resolved.**

The UI will surface these queries to analysts - they must be meaningful and accurate.
