/**
 * Query Generator Tab - Generates queries for investigating entities across all log sources
 */

import React, { useState, useEffect } from 'react';
import { Alert } from '../../types/investigation';
import { investigationService, GeneratedQuery, TimeWindow } from '../../api/services/investigationService';

interface QueryGeneratorTabProps {
  alert: Alert;
}

export const QueryGeneratorTab: React.FC<QueryGeneratorTabProps> = ({ alert }) => {
  const [queries, setQueries] = useState<GeneratedQuery[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedEntity, setSelectedEntity] = useState<{ type: string; value: string } | null>(null);
  const [copiedQuery, setCopiedQuery] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | 'custom'>('24h');

  // Auto-generate queries for first entity on load
  useEffect(() => {
    if (alert.entities && alert.entities.length > 0) {
      const firstEntity = alert.entities[0];
      generateQueriesForEntity(firstEntity.type, firstEntity.value);
    }
  }, [alert.alert_id]);

  const generateQueriesForEntity = async (entityType: string, entityValue: string) => {
    setLoading(true);
    setError(null);
    setSelectedEntity({ type: entityType, value: entityValue });

    try {
      const timeWindow = getTimeWindow();
      const result = await investigationService.generateQueries({
        entity_type: entityType,
        entity_value: entityValue,
        time_window: timeWindow
      });

      setQueries(result.queries);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate queries');
      setQueries([]);
    } finally {
      setLoading(false);
    }
  };

  const getTimeWindow = (): TimeWindow => {
    const end = new Date();
    const start = new Date();

    switch (timeRange) {
      case '1h':
        start.setHours(start.getHours() - 1);
        break;
      case '24h':
        start.setHours(start.getHours() - 24);
        break;
      case '7d':
        start.setDate(start.getDate() - 7);
        break;
      default:
        start.setHours(start.getHours() - 24);
    }

    return {
      start: start.toISOString(),
      end: end.toISOString()
    };
  };

  const copyToClipboard = async (query: string, sourceType: string) => {
    try {
      await navigator.clipboard.writeText(query);
      setCopiedQuery(sourceType);
      setTimeout(() => setCopiedQuery(null), 2000);
    } catch (err) {
      console.error('Failed to copy query:', err);
    }
  };

  const getEntityIcon = (entityType: string): string => {
    switch (entityType.toLowerCase()) {
      case 'ip': return '🌐';
      case 'user': return '👤';
      case 'host': return '💻';
      case 'process': return '⚙️';
      case 'hash': return '#️⃣';
      case 'domain': return '🔗';
      case 'email': return '📧';
      case 'file': return '📄';
      default: return '📌';
    }
  };

  const availableQueries = queries.filter(q => q.available);
  const unavailableQueries = queries.filter(q => !q.available);

  return (
    <div className="query-generator-tab">
      {/* Entity Selection Bar */}
      <div className="entity-selector-bar">
        <div className="entity-selector-label">Generate queries for:</div>
        <div className="entity-chips">
          {alert.entities && alert.entities.map((entity, idx) => (
            <button
              key={idx}
              className={`entity-chip ${selectedEntity?.value === entity.value ? 'active' : ''}`}
              onClick={() => generateQueriesForEntity(entity.type, entity.value)}
            >
              {getEntityIcon(entity.type)} {entity.type}: {entity.value}
            </button>
          ))}
        </div>

        {/* Time Range Selector */}
        <div className="time-range-selector">
          <label>Time Range:</label>
          <select
            value={timeRange}
            onChange={(e) => {
              setTimeRange(e.target.value as typeof timeRange);
              if (selectedEntity) {
                generateQueriesForEntity(selectedEntity.type, selectedEntity.value);
              }
            }}
          >
            <option value="1h">Last 1 hour</option>
            <option value="24h">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
          </select>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="query-loading">
          <div className="spinner">⏳</div>
          <p>Generating queries...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="query-error">
          <p>Error: {error}</p>
        </div>
      )}

      {/* Generated Queries */}
      {!loading && !error && queries.length > 0 && (
        <div className="queries-container">
          {/* Summary Header */}
          <div className="queries-summary">
            <div className="summary-stat">
              <span className="stat-value">{queries.length}</span>
              <span className="stat-label">Total Sources</span>
            </div>
            <div className="summary-stat available">
              <span className="stat-value">{availableQueries.length}</span>
              <span className="stat-label">Available Now</span>
            </div>
            <div className="summary-stat unavailable">
              <span className="stat-value">{unavailableQueries.length}</span>
              <span className="stat-label">No Data</span>
            </div>
          </div>

          {/* Available Queries */}
          {availableQueries.length > 0 && (
            <div className="queries-section">
              <h3 className="section-title">✅ Available Sources ({availableQueries.length})</h3>
              <div className="queries-grid">
                {availableQueries.map((query, idx) => (
                  <QueryCard
                    key={idx}
                    query={query}
                    onCopy={copyToClipboard}
                    isCopied={copiedQuery === query.source_type}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Unavailable Queries */}
          {unavailableQueries.length > 0 && (
            <div className="queries-section">
              <h3 className="section-title">⚠️ No Recent Data ({unavailableQueries.length})</h3>
              <div className="queries-grid">
                {unavailableQueries.map((query, idx) => (
                  <QueryCard
                    key={idx}
                    query={query}
                    onCopy={copyToClipboard}
                    isCopied={copiedQuery === query.source_type}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* No Queries State */}
      {!loading && !error && queries.length === 0 && (
        <div className="no-queries">
          <p>Select an entity to generate investigation queries</p>
        </div>
      )}
    </div>
  );
};

interface QueryCardProps {
  query: GeneratedQuery;
  onCopy: (query: string, sourceType: string) => void;
  isCopied: boolean;
}

const QueryCard: React.FC<QueryCardProps> = ({ query, onCopy, isCopied }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className={`query-card ${query.available ? 'available' : 'unavailable'}`}>
      {/* Card Header */}
      <div className="query-card-header">
        <div className="source-info">
          <div className="source-name">
            {query.available ? '✅' : '⚠️'} {query.source_name}
          </div>
          <div className="source-language">{query.query_language}</div>
        </div>

        {query.source_details && (
          <div className="source-details">
            <span className="detail-badge">
              {query.source_details.vendor} {query.source_details.product}
            </span>
            {query.available && (
              <span className="log-count">
                {query.source_details.log_count.toLocaleString()} logs (7d)
              </span>
            )}
          </div>
        )}
      </div>

      {/* Query Display */}
      <div className="query-display">
        <pre className="query-text">{query.query}</pre>
        <div className="query-actions">
          <button
            className="copy-button"
            onClick={() => onCopy(query.query, query.source_type)}
            disabled={!query.available}
          >
            {isCopied ? '✓ Copied!' : '📋 Copy Query'}
          </button>
          {query.deep_link && query.available && (
            <a
              href={query.deep_link}
              target="_blank"
              rel="noopener noreferrer"
              className="deep-link-button"
            >
              🔗 Open in Console
            </a>
          )}
        </div>
      </div>

      {/* Expandable Guidance Section */}
      <div className="query-guidance">
        <button
          className="guidance-toggle"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? '▼' : '▶'} Investigation Guidance
        </button>

        {isExpanded && (
          <div className="guidance-content">
            {/* What We Have */}
            {query.what_we_have.length > 0 && (
              <div className="guidance-section">
                <h4>📊 What We Have:</h4>
                <ul>
                  {query.what_we_have.map((item, idx) => (
                    <li key={idx}>{item}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* What to Look For */}
            {query.what_to_look_for.length > 0 && (
              <div className="guidance-section">
                <h4>🔍 What to Look For:</h4>
                <ul>
                  {query.what_to_look_for.map((item, idx) => (
                    <li key={idx}>{item}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Limitations */}
            {query.limitations.length > 0 && (
              <div className="guidance-section">
                <h4>⚠️ Limitations:</h4>
                <ul>
                  {query.limitations.map((item, idx) => (
                    <li key={idx}>{item}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default QueryGeneratorTab;
