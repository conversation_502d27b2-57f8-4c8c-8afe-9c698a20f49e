"""
Use Case Context Engine - Contextualizes logs based on security use cases

This engine bridges the gap between raw entity extraction and business logic.
It understands WHAT the logs mean in a security context.
"""
import asyncio
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from pathlib import Path
import hashlib

# Add v2 to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.base import BaseEngine


class UseCaseContextEngine(BaseEngine):
    """
    Use Case Context Engine: Apply security context to extracted entities
    
    This is the missing layer between pattern extraction and detection:
    - Parser: Extracts fields from logs
    - Entity Extractor: Identifies entities
    - Use Case Context: Understands security implications <-- THIS ENGINE
    - Detection: Identifies threats
    """
    
    def __init__(self):
        super().__init__('use_case_context', '2.0.0')
        
        # Use case definitions (from v1 learnings)
        self.use_cases = {
            'credential_access': {
                'name': 'Credential Access Detection',
                'indicators': ['lsass.exe', 'mimikatz', 'credential', 'password', 'hash'],
                'entity_context': {
                    'process': ['lsass.exe', 'rundll32.exe', 'powershell.exe'],
                    'technique': ['T1003', 'T1558', 'T1110'],
                    'tactic': ['credential-access']
                },
                'severity_multiplier': 2.0
            },
            'lateral_movement': {
                'name': 'Lateral Movement Detection',
                'indicators': ['psexec', 'wmic', 'smb', 'rdp', 'ssh'],
                'entity_context': {
                    'source_ip': 'internal',
                    'destination_ip': 'internal',
                    'port': [445, 3389, 22],
                    'technique': ['T1021', 'T1570']
                },
                'severity_multiplier': 1.8
            },
            'persistence': {
                'name': 'Persistence Mechanism Detection',
                'indicators': ['scheduled task', 'registry', 'startup', 'service'],
                'entity_context': {
                    'technique': ['T1053', 'T1543', 'T1547'],
                    'tactic': ['persistence']
                },
                'severity_multiplier': 1.5
            },
            'data_exfiltration': {
                'name': 'Data Exfiltration Detection',
                'indicators': ['upload', 'transfer', 'exfil', 'large bytes'],
                'entity_context': {
                    'destination_ip': 'external',
                    'bytes_sent': 'large',  # >100MB
                    'technique': ['T1041', 'T1048', 'T1567']
                },
                'severity_multiplier': 2.5
            },
            'network_scanning': {
                'name': 'Network Reconnaissance',
                'indicators': ['nmap', 'scan', 'port sweep', 'ping sweep'],
                'entity_context': {
                    'source_ip': 'single',
                    'destination_ip': 'multiple',
                    'destination_port': 'multiple',
                    'technique': ['T1046', 'T1595']
                },
                'severity_multiplier': 1.2
            },
            'firewall_violation': {
                'name': 'Firewall Policy Violation',
                'indicators': ['deny', 'block', 'drop', 'reject'],
                'entity_context': {
                    'action': ['blocked', 'denied', 'dropped'],
                    'policy_id': 'exists'
                },
                'severity_multiplier': 1.0
            },
            'malware_activity': {
                'name': 'Malware Behavior Detection',
                'indicators': ['virus', 'trojan', 'backdoor', 'c2', 'beacon'],
                'entity_context': {
                    'threat_name': 'exists',
                    'file_hash': 'exists',
                    'technique': ['T1055', 'T1105', 'T1071']
                },
                'severity_multiplier': 2.2
            },
            'privileged_access': {
                'name': 'Privileged Account Activity',
                'indicators': ['admin', 'root', 'sudo', 'elevation'],
                'entity_context': {
                    'user': ['admin', 'administrator', 'root', 'system'],
                    'technique': ['T1078', 'T1068']
                },
                'severity_multiplier': 1.6
            }
        }
        
        # Vendor-specific context mappings
        self.vendor_contexts = {
            'crowdstrike': {
                'detection_focus': ['endpoint', 'process', 'file'],
                'key_fields': ['detection_id', 'behaviors', 'technique'],
                'severity_field': 'max_severity'
            },
            'paloalto': {
                'detection_focus': ['network', 'firewall', 'threat'],
                'key_fields': ['rule_name', 'application', 'threat_name'],
                'severity_field': 'severity'
            },
            'fortinet': {
                'detection_focus': ['network', 'firewall', 'utm'],
                'key_fields': ['policy_id', 'attack_name', 'virus_name'],
                'severity_field': 'threat_level'
            },
            'tippingpoint': {
                'detection_focus': ['ips', 'intrusion', 'attack'],
                'key_fields': ['attack_id', 'signature_name', 'zone'],
                'severity_field': 'severity'
            }
        }
        
        # Context enrichment statistics
        self.context_stats = {
            'total_processed': 0,
            'use_cases_matched': 0,
            'context_enriched': 0,
            'severity_adjusted': 0
        }
    
    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process entities and add security context based on use cases
        """
        entities = message.get('data', {}).get('entities', [])
        relationships = message.get('data', {}).get('relationships', [])
        original_log = message.get('data', {}).get('original_log', {})
        
        self.logger.start_operation(f"contextualize_{message.get('id', 'unknown')}")
        
        try:
            # Identify vendor context
            vendor_context = self._identify_vendor_context(original_log)
            
            # Match use cases
            matched_use_cases = self._match_use_cases(entities, relationships, original_log)
            
            # Apply contextual enrichment
            enriched_context = self._apply_context_enrichment(
                entities, relationships, matched_use_cases, vendor_context
            )
            
            # Calculate contextual risk score
            risk_score = self._calculate_risk_score(
                enriched_context, matched_use_cases, vendor_context
            )
            
            # Generate security narrative
            narrative = self._generate_narrative(
                enriched_context, matched_use_cases, risk_score
            )
            
            # Update statistics
            self.context_stats['total_processed'] += 1
            self.context_stats['use_cases_matched'] += len(matched_use_cases)
            self.context_stats['context_enriched'] += 1 if enriched_context else 0
            
            # Log contextualization
            self.logger.log_decision(
                'use_case_context',
                {'entities': entities, 'relationships': relationships},
                {
                    'matched_use_cases': matched_use_cases,
                    'enriched_context': enriched_context,
                    'risk_score': risk_score,
                    'narrative': narrative
                },
                reasoning=f"Matched {len(matched_use_cases)} use cases with risk score {risk_score:.2f}",
                confidence=0.85
            )
            
            result = {
                'success': True,
                'use_cases': matched_use_cases,
                'context': enriched_context,
                'risk_score': risk_score,
                'narrative': narrative,
                'vendor_context': vendor_context,
                'next_engine': 'detection_engine',
                'data': {
                    'entities': entities,
                    'relationships': relationships,
                    'use_case_context': enriched_context,
                    'risk_score': risk_score,
                    'narrative': narrative,
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
            
            return result
        
        except Exception as e:
            self.logger.log_error(e, {'message': message})
            return {
                'success': False,
                'error': str(e)
            }
        finally:
            self.logger.end_operation(f"contextualize_{message.get('id', 'unknown')}")
    
    def _identify_vendor_context(self, log: Dict[str, Any]) -> Dict[str, Any]:
        """
        Identify vendor-specific context from log
        """
        # Check for vendor indicators
        log_str = json.dumps(log).lower()
        
        for vendor, context in self.vendor_contexts.items():
            if vendor in log_str:
                return {
                    'vendor': vendor,
                    'focus': context['detection_focus'],
                    'key_fields': context['key_fields'],
                    'severity_field': context['severity_field']
                }
        
        # Default context
        return {
            'vendor': 'unknown',
            'focus': ['general'],
            'key_fields': [],
            'severity_field': 'severity'
        }
    
    def _match_use_cases(self, entities: List[Dict], relationships: List[Dict], 
                        log: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Match log against defined use cases
        """
        matched = []
        log_str = json.dumps(log).lower()
        entity_types = {e.get('type'): e.get('value') for e in entities}
        
        for use_case_id, use_case in self.use_cases.items():
            score = 0.0
            matches = []
            
            # Check indicators in log
            for indicator in use_case['indicators']:
                if indicator.lower() in log_str:
                    score += 0.3
                    matches.append(f"indicator:{indicator}")
            
            # Check entity context
            for entity_type, expected_values in use_case['entity_context'].items():
                if entity_type in entity_types:
                    if isinstance(expected_values, list):
                        if any(str(v).lower() in str(entity_types[entity_type]).lower() 
                              for v in expected_values):
                            score += 0.5
                            matches.append(f"entity:{entity_type}")
                    elif expected_values == 'exists':
                        score += 0.4
                        matches.append(f"entity_exists:{entity_type}")
            
            if score >= 0.5:  # Threshold for match
                matched.append({
                    'use_case_id': use_case_id,
                    'name': use_case['name'],
                    'confidence': min(score, 1.0),
                    'matches': matches,
                    'severity_multiplier': use_case['severity_multiplier']
                })
        
        return matched
    
    def _apply_context_enrichment(self, entities: List[Dict], relationships: List[Dict],
                                  use_cases: List[Dict], vendor_context: Dict) -> Dict[str, Any]:
        """
        Apply contextual enrichment based on use cases and vendor
        """
        enrichment = {
            'security_context': [],
            'threat_indicators': [],
            'recommended_actions': [],
            'related_techniques': set(),
            'affected_assets': []
        }
        
        # Add use case contexts
        for use_case in use_cases:
            enrichment['security_context'].append({
                'context': use_case['name'],
                'confidence': use_case['confidence'],
                'severity_impact': use_case['severity_multiplier']
            })
        
        # Extract threat indicators
        for entity in entities:
            if entity.get('type') in ['technique', 'tactic', 'threat_name', 'attack_id']:
                enrichment['threat_indicators'].append({
                    'type': entity['type'],
                    'value': entity['value'],
                    'confidence': entity.get('confidence', 0.8)
                })
            
            if entity.get('type') in ['hostname', 'device_id', 'user']:
                enrichment['affected_assets'].append({
                    'type': entity['type'],
                    'value': entity['value']
                })
        
        # Add vendor-specific context
        if vendor_context['vendor'] != 'unknown':
            enrichment['vendor_focus'] = vendor_context['focus']
            enrichment['key_indicators'] = vendor_context['key_fields']
        
        # Generate recommendations
        if any(uc['use_case_id'] == 'credential_access' for uc in use_cases):
            enrichment['recommended_actions'].append('Reset affected user passwords')
            enrichment['recommended_actions'].append('Review authentication logs')
        
        if any(uc['use_case_id'] == 'lateral_movement' for uc in use_cases):
            enrichment['recommended_actions'].append('Isolate affected systems')
            enrichment['recommended_actions'].append('Review network connections')
        
        if any(uc['use_case_id'] == 'data_exfiltration' for uc in use_cases):
            enrichment['recommended_actions'].append('Block external communication')
            enrichment['recommended_actions'].append('Investigate data transfers')
        
        return enrichment
    
    def _calculate_risk_score(self, context: Dict, use_cases: List[Dict], 
                             vendor_context: Dict) -> float:
        """
        Calculate contextual risk score (0-100)
        """
        base_score = 0.0
        
        # Use case severity contribution
        if use_cases:
            max_severity = max(uc['severity_multiplier'] for uc in use_cases)
            max_confidence = max(uc['confidence'] for uc in use_cases)
            base_score = (max_severity * max_confidence * 20)  # Max 50 from use cases
        
        # Threat indicator contribution
        threat_count = len(context.get('threat_indicators', []))
        base_score += min(threat_count * 5, 25)  # Max 25 from threats
        
        # Affected assets contribution
        asset_count = len(context.get('affected_assets', []))
        base_score += min(asset_count * 3, 15)  # Max 15 from assets
        
        # Multiple use case bonus
        if len(use_cases) > 1:
            base_score += min(len(use_cases) * 5, 10)  # Max 10 from multiple matches
        
        return min(base_score, 100.0)
    
    def _generate_narrative(self, context: Dict, use_cases: List[Dict], 
                          risk_score: float) -> str:
        """
        Generate human-readable security narrative
        """
        if not use_cases:
            return "No specific security use cases matched for this event."
        
        severity = "Critical" if risk_score >= 80 else "High" if risk_score >= 60 else "Medium" if risk_score >= 40 else "Low"
        
        primary_use_case = max(use_cases, key=lambda x: x['confidence'])
        
        narrative = f"{severity} risk event detected: {primary_use_case['name']}. "
        
        if len(use_cases) > 1:
            other_contexts = [uc['name'] for uc in use_cases if uc != primary_use_case]
            narrative += f"Additional contexts: {', '.join(other_contexts)}. "
        
        if context.get('threat_indicators'):
            techniques = [ti['value'] for ti in context['threat_indicators'] if ti['type'] == 'technique']
            if techniques:
                narrative += f"MITRE techniques observed: {', '.join(techniques[:3])}. "
        
        if context.get('affected_assets'):
            narrative += f"Affected assets: {len(context['affected_assets'])}. "
        
        if context.get('recommended_actions'):
            narrative += f"Recommended action: {context['recommended_actions'][0]}."
        
        return narrative
    
    def get_capabilities(self) -> Dict[str, Any]:
        """
        Return Use Case Context Engine capabilities
        """
        return {
            'engine': 'use_case_context',
            'version': self.version,
            'capabilities': [
                'use_case_matching',
                'context_enrichment',
                'risk_scoring',
                'narrative_generation',
                'vendor_contextualization'
            ],
            'supported_use_cases': list(self.use_cases.keys()),
            'supported_vendors': list(self.vendor_contexts.keys()),
            'statistics': self.context_stats
        }
    
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """
        Validate Use Case Context configuration
        """
        # No specific configuration required
        return True


async def main():
    """
    Main entry point for Use Case Context Engine
    """
    engine = UseCaseContextEngine()
    
    # Log capabilities
    capabilities = engine.get_capabilities()
    engine.logger.log('engine_capabilities', capabilities)
    
    # Example: Process a test message
    test_message = {
        'id': 'test_001',
        'data': {
            'entities': [
                {'type': 'hostname', 'value': 'WIN-SERVER01'},
                {'type': 'user', 'value': 'admin'},
                {'type': 'process', 'value': 'lsass.exe'},
                {'type': 'technique', 'value': 'T1003'}
            ],
            'relationships': [
                {'source': 'admin', 'target': 'WIN-SERVER01', 'type': 'logged_into'}
            ],
            'original_log': {
                'detection_id': '12345',
                'behaviors': [{'technique': 'T1003', 'tactic': 'credential-access'}],
                'max_severity': 80
            }
        }
    }
    
    result = await engine.process_message(test_message)
    print(json.dumps(result, indent=2))
    
    # Start processing from queue
    await engine.start()


if __name__ == '__main__':
    asyncio.run(main())