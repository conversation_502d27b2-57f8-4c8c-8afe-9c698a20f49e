"""
AI Consensus Engine - Multi-model validation for pattern discovery
Uses learnings from v1's unified mapper approach with multiple providers
This is where the EXPENSIVE operations happen before crystallization
"""
import asyncio
import json
import os
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from pathlib import Path
from enum import Enum
import hashlib

# Add v2 to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.base import BaseEngine


class AIProvider(Enum):
    """Supported AI providers"""
    GEMINI = "gemini"
    CLAUDE = "claude"
    GPT4 = "gpt4"
    GEMMA = "gemma"  # Free/local option
    OLLAMA = "ollama"  # Local models


class OperationType(Enum):
    """Types of AI operations"""
    PATTERN_VALIDATION = "pattern_validation"
    FIELD_MAPPING = "field_mapping"
    ENTITY_EXTRACTION = "entity_extraction"
    RELATIONSHIP_DISCOVERY = "relationship_discovery"
    THREAT_ANALYSIS = "threat_analysis"
    NORMALIZATION = "normalization"


class AIConsensusEngine(BaseEngine):
    """
    AI Consensus Engine: Multi-model validation for pattern discovery

    Philosophy:
    - Use multiple AI models to validate patterns (expensive but thorough)
    - Achieve consensus before crystallization
    - Track costs for each operation
    - Provide confidence scores based on agreement
    """

    def __init__(self):
        super().__init__('ai_consensus', '2.0.0')

        # Provider configurations (from v1 experience)
        self.providers = {
            AIProvider.GEMINI: {
                'models': ['gemini-pro', 'gemini-flash'],
                'cost_per_1k': 0.002,
                'quality_score': 0.85,
                'speed': 'fast'
            },
            AIProvider.CLAUDE: {
                'models': ['claude-opus-4', 'claude-sonnet-4'],
                'cost_per_1k': 0.015,
                'quality_score': 0.95,
                'speed': 'medium'
            },
            AIProvider.GPT4: {
                'models': ['gpt-4-turbo'],
                'cost_per_1k': 0.020,
                'quality_score': 0.90,
                'speed': 'medium'
            },
            AIProvider.GEMMA: {
                'models': ['gemma-27b', 'gemma-12b'],
                'cost_per_1k': 0.0,  # Free/local
                'quality_score': 0.80,
                'speed': 'fast'
            }
        }

        # Operation strategies (what models to use for what)
        self.operation_strategies = {
            OperationType.PATTERN_VALIDATION: [
                AIProvider.CLAUDE,  # Best for validation
                AIProvider.GEMINI,  # Good balance
                AIProvider.GPT4     # Third opinion
            ],
            OperationType.FIELD_MAPPING: [
                AIProvider.GEMMA,   # Free for simple mapping
                AIProvider.GEMINI   # Backup if needed
            ],
            OperationType.ENTITY_EXTRACTION: [
                AIProvider.GEMINI,  # Fast and good
                AIProvider.CLAUDE   # High quality validation
            ],
            OperationType.THREAT_ANALYSIS: [
                AIProvider.CLAUDE,  # Best for security
                AIProvider.GPT4,    # Good analysis
                AIProvider.GEMINI   # Additional perspective
            ]
        }

        # Cost tracking
        self.cost_tracker = {
            'total_cost': 0.0,
            'by_provider': {},
            'by_operation': {},
            'tokens_processed': 0
        }

        # Consensus thresholds
        self.consensus_config = {
            'minimum_validators': 2,
            'agreement_threshold': 0.75,  # 75% agreement needed
            'confidence_weights': {
                AIProvider.CLAUDE: 1.2,  # Higher weight for better models
                AIProvider.GPT4: 1.1,
                AIProvider.GEMINI: 1.0,
                AIProvider.GEMMA: 0.8
            }
        }

        # Pattern validation cache
        self.validation_cache = {}

        self.logger.info(f"AI Consensus Engine initialized with {len(self.providers)} providers")

    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process AI validation requests
        """
        request_type = message.get('type')
        data = message.get('data', {})

        self.logger.start_operation(f"ai_consensus_{message.get('id', 'unknown')}")

        try:
            result = None

            if request_type == 'pattern_validation':
                result = await self._validate_pattern(data)

            elif request_type == 'pattern_discovery':
                result = await self._discover_patterns(data)

            elif request_type == 'field_mapping':
                result = await self._generate_field_mapping(data)

            elif request_type == 'entity_extraction':
                result = await self._extract_entities_with_ai(data)

            elif request_type == 'threat_analysis':
                result = await self._analyze_threat(data)

            else:
                self.logger.log('unknown_request_type', {
                    'type': request_type
                }, 'WARNING')
                result = {'success': False, 'error': f'Unknown request type: {request_type}'}

            # Track costs
            if result and 'cost' in result:
                self._track_cost(result['cost'], request_type)

            return result

        except Exception as e:
            self.logger.log_error(e, {'request': message})
            return {
                'success': False,
                'error': str(e)
            }
        finally:
            self.logger.end_operation(f"ai_consensus_{message.get('id', 'unknown')}")

    async def _validate_pattern(self, pattern_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a pattern using multiple AI models
        """
        pattern = pattern_data.get('pattern', {})
        pattern_id = pattern_data.get('pattern_id', self._generate_pattern_id(pattern))

        # Check cache
        if pattern_id in self.validation_cache:
            self.logger.log('pattern_validation_cached', {
                'pattern_id': pattern_id
            }, 'DEBUG')
            return self.validation_cache[pattern_id]

        # Get validators for this operation
        validators = self.operation_strategies[OperationType.PATTERN_VALIDATION]

        # Collect validation results from each model
        validations = []
        total_cost = 0.0

        for provider in validators:
            validation = await self._validate_with_provider(provider, pattern)
            validations.append(validation)
            total_cost += validation.get('cost', 0)

        # Calculate consensus
        consensus_result = self._calculate_consensus(validations)

        # Build result
        result = {
            'success': True,
            'pattern_id': pattern_id,
            'valid': consensus_result['consensus_reached'],
            'confidence': consensus_result['confidence'],
            'agreement_rate': consensus_result['agreement_rate'],
            'validations': validations,
            'cost': total_cost,
            'recommendation': self._get_recommendation(consensus_result)
        }

        # Cache result
        self.validation_cache[pattern_id] = result

        # Log validation
        self.logger.log_decision(
            'pattern_validated',
            pattern,
            result,
            reasoning=f"Validated by {len(validations)} models with {consensus_result['agreement_rate']:.1%} agreement",
            confidence=consensus_result['confidence']
        )

        return result

    async def _validate_with_provider(self, provider: AIProvider, pattern: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate pattern with specific AI provider
        """
        # This would call the actual AI API in production
        # For now, simulate validation based on pattern characteristics

        provider_config = self.providers[provider]

        # Simulate API call delay
        await asyncio.sleep(0.1)

        # Analyze pattern quality (simplified)
        pattern_score = self._analyze_pattern_quality(pattern)

        # Provider-specific adjustments
        adjusted_score = pattern_score * provider_config['quality_score']

        # Calculate cost (based on pattern size)
        pattern_str = json.dumps(pattern)
        tokens = len(pattern_str) / 4  # Rough token estimate
        cost = (tokens / 1000) * provider_config['cost_per_1k']

        validation = {
            'provider': provider.value,
            'model': provider_config['models'][0],
            'valid': adjusted_score > 0.7,
            'confidence': adjusted_score,
            'reasoning': self._generate_validation_reasoning(pattern, adjusted_score),
            'improvements': self._suggest_improvements(pattern, adjusted_score),
            'cost': cost,
            'timestamp': datetime.utcnow().isoformat()
        }

        return validation

    def _analyze_pattern_quality(self, pattern: Dict[str, Any]) -> float:
        """
        Analyze the quality of a pattern
        """
        score = 0.0
        max_score = 0.0

        # Check for required fields
        required_fields = ['pattern_type', 'pattern_name', 'pattern_definition']
        for field in required_fields:
            max_score += 1.0
            if field in pattern:
                score += 1.0

        # Check pattern definition quality
        if 'pattern_definition' in pattern:
            definition = pattern['pattern_definition']

            # Has regex
            if 'regex' in definition:
                max_score += 2.0
                if self._is_valid_regex(definition['regex']):
                    score += 2.0

            # Has field mappings
            if 'fields' in definition:
                max_score += 1.0
                score += min(1.0, len(definition['fields']) / 5)  # Expect at least 5 fields

            # Has entity mappings
            if 'entity_mappings' in definition:
                max_score += 1.5
                score += min(1.5, len(definition['entity_mappings']) / 3)

            # Has relationship mappings
            if 'relationship_mappings' in definition:
                max_score += 1.5
                score += 1.5

        # Has examples
        if 'examples' in pattern and len(pattern['examples']) > 0:
            max_score += 1.0
            score += min(1.0, len(pattern['examples']) / 3)

        # Calculate final score
        return score / max_score if max_score > 0 else 0.0

    def _is_valid_regex(self, regex_str: str) -> bool:
        """
        Check if regex is valid
        """
        try:
            import re
            re.compile(regex_str)
            return True
        except:
            return False

    def _calculate_consensus(self, validations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate consensus from multiple validations
        """
        if not validations:
            return {
                'consensus_reached': False,
                'confidence': 0.0,
                'agreement_rate': 0.0
            }

        # Count votes
        valid_votes = sum(1 for v in validations if v['valid'])
        total_votes = len(validations)

        # Calculate weighted confidence
        weighted_confidence = 0.0
        total_weight = 0.0

        for validation in validations:
            provider = AIProvider(validation['provider'])
            weight = self.consensus_config['confidence_weights'].get(provider, 1.0)
            weighted_confidence += validation['confidence'] * weight
            total_weight += weight

        avg_confidence = weighted_confidence / total_weight if total_weight > 0 else 0.0

        # Calculate agreement rate
        agreement_rate = valid_votes / total_votes if total_votes > 0 else 0.0

        # Determine if consensus reached
        consensus_reached = (
            agreement_rate >= self.consensus_config['agreement_threshold'] and
            total_votes >= self.consensus_config['minimum_validators']
        )

        return {
            'consensus_reached': consensus_reached,
            'confidence': avg_confidence,
            'agreement_rate': agreement_rate,
            'valid_votes': valid_votes,
            'total_votes': total_votes
        }

    def _generate_validation_reasoning(self, pattern: Dict[str, Any], score: float) -> str:
        """
        Generate reasoning for validation result
        """
        if score > 0.9:
            return "Excellent pattern with comprehensive field mappings and clear structure"
        elif score > 0.7:
            return "Good pattern with solid foundation, minor improvements possible"
        elif score > 0.5:
            return "Acceptable pattern but needs enhancement in field mappings or examples"
        else:
            return "Pattern needs significant improvement in structure and completeness"

    def _suggest_improvements(self, pattern: Dict[str, Any], score: float) -> List[str]:
        """
        Suggest improvements for pattern
        """
        improvements = []

        if 'examples' not in pattern or len(pattern.get('examples', [])) < 3:
            improvements.append("Add more examples (at least 3) to validate pattern")

        if 'pattern_definition' in pattern:
            definition = pattern['pattern_definition']

            if 'entity_mappings' not in definition:
                improvements.append("Add entity_mappings to identify key entities")

            if 'relationship_mappings' not in definition:
                improvements.append("Add relationship_mappings to capture entity relationships")

            if 'confidence_score' not in definition:
                improvements.append("Add confidence_score to indicate pattern reliability")

        if score < 0.7:
            improvements.append("Consider adding field type definitions for better parsing")

        return improvements

    def _get_recommendation(self, consensus_result: Dict[str, Any]) -> str:
        """
        Get recommendation based on consensus
        """
        if consensus_result['consensus_reached'] and consensus_result['confidence'] > 0.8:
            return "CRYSTALLIZE: Pattern ready for production use"
        elif consensus_result['consensus_reached'] and consensus_result['confidence'] > 0.6:
            return "REVIEW: Pattern acceptable but could be improved"
        elif consensus_result['agreement_rate'] > 0.5:
            return "REFINE: Pattern needs refinement before crystallization"
        else:
            return "REJECT: Pattern does not meet quality standards"

    async def _discover_patterns(self, log_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Discover patterns from unknown logs using AI
        """
        logs = log_data.get('logs', [])

        if not logs:
            return {'success': False, 'error': 'No logs provided'}

        # Use multiple models to discover patterns
        discoveries = []
        total_cost = 0.0

        # Use a subset of providers for discovery
        discovery_providers = [AIProvider.GEMINI, AIProvider.CLAUDE]

        for provider in discovery_providers:
            discovery = await self._discover_with_provider(provider, logs)
            discoveries.append(discovery)
            total_cost += discovery.get('cost', 0)

        # Merge discoveries
        merged_patterns = self._merge_pattern_discoveries(discoveries)

        # Validate each discovered pattern
        validated_patterns = []
        for pattern in merged_patterns:
            validation = await self._validate_pattern({'pattern': pattern})
            if validation['valid']:
                validated_patterns.append({
                    'pattern': pattern,
                    'validation': validation
                })

        result = {
            'success': True,
            'patterns_discovered': len(merged_patterns),
            'patterns_validated': len(validated_patterns),
            'patterns': validated_patterns,
            'cost': total_cost,
            'next_engine': 'librarian' if validated_patterns else None
        }

        self.logger.log('patterns_discovered', {
            'log_count': len(logs),
            'patterns_found': len(validated_patterns),
            'cost': total_cost
        })

        return result

    async def _discover_with_provider(self, provider: AIProvider, logs: List[Any]) -> Dict[str, Any]:
        """
        Discover patterns with specific provider
        """
        # Simulate pattern discovery
        await asyncio.sleep(0.2)

        # In production, this would call the actual AI API
        # For now, generate sample patterns based on log structure

        patterns = []

        # Analyze first log for structure
        if logs and isinstance(logs[0], dict):
            sample_log = logs[0]

            # Create a basic pattern
            pattern = {
                'pattern_type': 'log_structure',
                'pattern_name': f'discovered_{provider.value}',
                'pattern_definition': {
                    'fields': list(sample_log.keys()),
                    'entity_mappings': self._infer_entity_mappings(sample_log),
                    'sample_count': len(logs)
                },
                'confidence': 0.75,
                'examples': logs[:3]  # First 3 as examples
            }
            patterns.append(pattern)

        # Calculate cost
        total_chars = sum(len(str(log)) for log in logs)
        tokens = total_chars / 4
        cost = (tokens / 1000) * self.providers[provider]['cost_per_1k']

        return {
            'provider': provider.value,
            'patterns': patterns,
            'cost': cost
        }

    def _infer_entity_mappings(self, log: Dict[str, Any]) -> Dict[str, str]:
        """
        Infer entity mappings from log fields
        """
        mappings = {}

        # Common field patterns (from v1 experience)
        field_patterns = {
            'hostname': ['hostname', 'host', 'computer', 'device', 'ComputerName'],
            'username': ['username', 'user', 'userid', 'UserName', 'account'],
            'source_ip': ['src_ip', 'source_ip', 'client_ip', 'SourceIP'],
            'destination_ip': ['dst_ip', 'dest_ip', 'destination_ip', 'server_ip']
        }

        for field_name in log.keys():
            field_lower = field_name.lower()
            for entity_type, patterns in field_patterns.items():
                for pattern in patterns:
                    if pattern.lower() in field_lower:
                        mappings[entity_type] = field_name
                        break

        return mappings

    def _merge_pattern_discoveries(self, discoveries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Merge pattern discoveries from multiple providers
        """
        merged = []
        seen_patterns = set()

        for discovery in discoveries:
            for pattern in discovery.get('patterns', []):
                # Create pattern signature
                pattern_sig = self._generate_pattern_id(pattern)

                if pattern_sig not in seen_patterns:
                    seen_patterns.add(pattern_sig)
                    merged.append(pattern)

        return merged

    def _generate_pattern_id(self, pattern: Dict[str, Any]) -> str:
        """
        Generate unique ID for pattern
        """
        pattern_str = json.dumps(pattern, sort_keys=True)
        return hashlib.sha256(pattern_str.encode()).hexdigest()[:16]

    def _track_cost(self, cost: float, operation_type: str):
        """
        Track costs for operations
        """
        self.cost_tracker['total_cost'] += cost

        if operation_type not in self.cost_tracker['by_operation']:
            self.cost_tracker['by_operation'][operation_type] = 0.0
        self.cost_tracker['by_operation'][operation_type] += cost

        self.logger.log('cost_tracked', {
            'operation': operation_type,
            'cost': cost,
            'total_cost': self.cost_tracker['total_cost']
        }, 'DEBUG')

    async def _generate_field_mapping(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate field mapping using AI (simpler operation, can use cheaper models)
        """
        # Use GEMMA (free) for simple field mapping
        provider = AIProvider.GEMMA

        # Simulate field mapping generation
        await asyncio.sleep(0.05)

        log_sample = data.get('log_sample', {})

        mapping = {
            'success': True,
            'field_mapping': self._infer_entity_mappings(log_sample),
            'confidence': 0.85,
            'provider': provider.value,
            'cost': 0.0  # Free with GEMMA
        }

        return mapping

    async def _extract_entities_with_ai(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract entities using AI when patterns don't match
        """
        logs = data.get('logs', [])

        # Use GEMINI for entity extraction (good balance)
        provider = AIProvider.GEMINI

        await asyncio.sleep(0.1)

        entities = []
        for log in logs[:10]:  # Process first 10 logs
            # Simulate entity extraction
            extracted = {
                'entities': self._infer_entity_mappings(log) if isinstance(log, dict) else {},
                'confidence': 0.8
            }
            entities.append(extracted)

        # Calculate cost
        total_chars = sum(len(str(log)) for log in logs[:10])
        tokens = total_chars / 4
        cost = (tokens / 1000) * self.providers[provider]['cost_per_1k']

        return {
            'success': True,
            'entities': entities,
            'logs_processed': min(10, len(logs)),
            'provider': provider.value,
            'cost': cost
        }

    async def _analyze_threat(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform threat analysis using multiple AI models
        """
        # Use all high-quality models for threat analysis
        providers = self.operation_strategies[OperationType.THREAT_ANALYSIS]

        analyses = []
        total_cost = 0.0

        for provider in providers:
            await asyncio.sleep(0.15)

            # Simulate threat analysis
            analysis = {
                'provider': provider.value,
                'threat_level': 'medium',  # Would be determined by AI
                'indicators': ['suspicious pattern detected'],
                'confidence': 0.75,
                'cost': 0.01
            }
            analyses.append(analysis)
            total_cost += analysis['cost']

        # Aggregate threat analyses
        consensus = self._calculate_consensus([
            {'valid': a['threat_level'] != 'low', 'confidence': a['confidence'], 'provider': a['provider']}
            for a in analyses
        ])

        return {
            'success': True,
            'threat_detected': consensus['consensus_reached'],
            'threat_level': 'high' if consensus['confidence'] > 0.8 else 'medium',
            'analyses': analyses,
            'consensus': consensus,
            'cost': total_cost,
            'next_engine': 'detection' if consensus['consensus_reached'] else None
        }

    def get_capabilities(self) -> Dict[str, Any]:
        """
        Return AI Consensus Engine capabilities
        """
        return {
            'engine': 'ai_consensus',
            'version': self.version,
            'capabilities': [
                'pattern_validation',
                'pattern_discovery',
                'field_mapping',
                'entity_extraction',
                'threat_analysis',
                'multi_model_consensus'
            ],
            'providers': [p.value for p in AIProvider],
            'operation_types': [o.value for o in OperationType],
            'cost_tracker': self.cost_tracker,
            'consensus_config': self.consensus_config,
            'cache_size': len(self.validation_cache)
        }

    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """
        Validate AI Consensus configuration
        """
        # Check for API keys (in production)
        # For now, always return True
        return True

    def get_cost_estimate(self, operation: str, data_size: int) -> Dict[str, Any]:
        """
        Estimate cost for an operation
        """
        # Estimate tokens
        tokens = data_size / 4  # Rough estimate

        # Get operation type
        try:
            op_type = OperationType(operation)
        except:
            op_type = OperationType.PATTERN_VALIDATION

        # Get providers for operation
        providers = self.operation_strategies.get(op_type, [AIProvider.GEMINI])

        # Calculate cost
        total_cost = 0.0
        for provider in providers:
            cost_per_1k = self.providers[provider]['cost_per_1k']
            total_cost += (tokens / 1000) * cost_per_1k

        return {
            'operation': operation,
            'estimated_tokens': tokens,
            'estimated_cost': total_cost,
            'providers_used': [p.value for p in providers],
            'note': 'Use GEMMA for 0 cost on simple operations'
        }


async def main():
    """
    Main entry point for AI Consensus Engine
    """
    engine = AIConsensusEngine()

    # Log capabilities
    capabilities = engine.get_capabilities()
    engine.logger.log('engine_capabilities', capabilities)

    # Show cost estimates
    estimates = [
        engine.get_cost_estimate('pattern_validation', 10000),
        engine.get_cost_estimate('field_mapping', 5000),
        engine.get_cost_estimate('threat_analysis', 20000)
    ]

    for estimate in estimates:
        engine.logger.log('cost_estimate', estimate)

    # Start processing
    await engine.start()


if __name__ == '__main__':
    asyncio.run(main())