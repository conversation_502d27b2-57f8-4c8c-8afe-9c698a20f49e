# SIEMLess v2.0 - Widget Catalog

## Purpose
Systematic specifications for all frontend widgets following the documentation-first strategy. Each widget is designed as a reusable component that can be composed into dashboards.

**Target Audiences:**
- **Primary Users**: Security Analysts & Detection Engineers
- **Primary Buyers**: Executive Leadership (CISO, VP Security)

---

## Widget Priority Matrix

| Priority | Widget | User Type | Build Week | Complexity |
|----------|--------|-----------|------------|------------|
| P0 | Dashboard Overview | Both | 1 | Low |
| P0 | Alert Queue | Analyst | 1 | Medium |
| P0 | Entity Explorer | Analyst | 1-2 | Medium |
| P0 | CTI Plugin Status | Analyst | 2 | Low |
| P1 | Detection Fidelity Calculator | Executive | 3 | High |
| P1 | Log Source Quality Matrix | Executive | 3 | Medium |
| P1 | Coverage Simulation Tool | Executive | 4 | High |
| P1 | Entity Details Panel | Analyst | 4 | High |
| P2 | Investigation Dashboard | Analyst | 5 | High |
| P2 | MITRE ATT&CK Heatmap | Both | 5 | Medium |
| P2 | Relationship Graph | Analyst | 6 | High |
| P2 | Timeline Analysis | Analyst | 6 | Medium |
| P3 | Pattern Library | Engineer | 7 | Medium |
| P3 | Rule Performance | Engineer | 7 | Low |
| P3 | Cost Analytics | Executive | 8 | Medium |

---

## P0 Widgets (Week 1-2)

### 1. Dashboard Overview Widget

**Purpose**: "As an executive, I need a single-pane-of-glass view of security posture so I can quickly assess organizational risk."

**User Story**: CISO logs in Monday morning, sees detection confidence at 88.6%, 12 critical alerts pending, and 3 coverage gaps.

**APIs**:
```typescript
// Primary data
GET /api/dashboard/stats → DashboardStats
GET /api/alerts?severity=critical&status=open → Alert[]
GET /api/detection/fidelity → DetectionFidelity

// Supporting data
GET /api/log-sources/quality → LogSourceQuality[]
GET /api/cti/status → CTIPluginStatus[]
```

**Data Flow**:
1. Component mounts → Fetch dashboard stats
2. Parallel fetch: Critical alerts + Detection fidelity + CTI status
3. Aggregate data → Calculate KPIs
4. Subscribe to WebSocket: `dashboard.*` channel
5. Real-time updates → Animate changes

**Enrichment Display**:
- **Layer 1**: Log source quality scores
- **Layer 2**: Active CTI threat indicators count
- **Layer 3**: N/A (summary level)

**TypeScript Interface**:
```typescript
interface DashboardStats {
  total_alerts: number
  critical_alerts: number
  open_cases: number
  detection_confidence: number  // 0-100
  coverage_score: number        // 0-100
  entities_tracked: number
  cti_indicators: number
  last_updated: string
}

interface DashboardWidget {
  stats: DashboardStats
  recentAlerts: Alert[]
  detectionFidelity: DetectionFidelity
  ctiStatus: CTIPluginStatus[]
  loading: boolean
  error: string | null
}
```

**Components**:
- `<StatCard />` - Reusable KPI display
- `<TrendIndicator />` - Up/down arrows with percentage
- `<MiniAlertList />` - Compact alert preview
- `<HealthBadge />` - System health indicator

**State Management** (Zustand):
```typescript
interface DashboardStore {
  stats: DashboardStats | null
  recentAlerts: Alert[]
  loading: boolean

  fetchDashboard: () => Promise<void>
  subscribeToUpdates: () => void
  unsubscribe: () => void
}
```

**Real-time Updates**:
- WebSocket channel: `dashboard.stats`
- Update frequency: Every 30 seconds
- Animation: Smooth number transitions on change

**Acceptance Criteria**:
- [ ] Displays all 8 KPIs correctly
- [ ] Updates in real-time without flicker
- [ ] Loads in <2 seconds
- [ ] Responsive on mobile
- [ ] Handles missing data gracefully

---

### 2. Alert Queue Widget

**Purpose**: "As an analyst, I need to triage incoming alerts efficiently so I can focus on real threats."

**User Story**: Analyst opens alert queue, sees 47 alerts with intelligent prioritization, clicks one, sees full context in side panel.

**APIs**:
```typescript
// Primary data
GET /api/alerts?status=open&sort=priority → Alert[]
GET /api/alerts/{id} → AlertDetail

// Enrichment
GET /api/entities/{entityId} → Entity  // For each entity in alert
GET /api/cases/{caseId} → Case  // If alert assigned to case
```

**Data Flow**:
1. Fetch alerts with filters (status, severity, time range)
2. Display in AG-Grid with virtual scrolling
3. User clicks alert → Fetch detailed alert data
4. Load related entities (IPs, users, hosts)
5. Display enrichment panel with all 3 layers
6. Subscribe to `alerts.new` for real-time additions

**Enrichment Display**:
- **Layer 1**: Entity geolocation, asset tier, security zone
- **Layer 2**: CTI threat matches (if any)
- **Layer 3**: Related events from CrowdStrike/Elastic (6.95B event corpus)

**TypeScript Interface**:
```typescript
interface Alert {
  alert_id: string
  title: string
  description: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  status: 'open' | 'in_progress' | 'closed' | 'false_positive'
  source: string
  mitre_tactics: string[]
  mitre_techniques: string[]
  entities: EntityReference[]
  created_at: string
  updated_at: string
  assigned_to?: string
  priority_score: number  // AI-calculated priority
}

interface AlertDetail extends Alert {
  enriched_entities: Entity[]
  related_alerts: Alert[]
  investigation_guide?: InvestigationGuide
  raw_data: Record<string, any>
}

interface AlertQueueState {
  alerts: Alert[]
  selectedAlert: AlertDetail | null
  filters: AlertFilters
  loading: boolean

  fetchAlerts: (filters: AlertFilters) => Promise<void>
  selectAlert: (alertId: string) => Promise<void>
  updateAlertStatus: (alertId: string, status: string) => Promise<void>
  assignToCase: (alertId: string, caseId: string) => Promise<void>
}
```

**Components**:
- `<AlertGrid />` - AG-Grid with custom cell renderers
- `<AlertDetailPanel />` - Slide-out side panel
- `<EntityEnrichmentCard />` - Displays all 3 enrichment layers
- `<MITREBadge />` - Tactic/technique tags
- `<PriorityIndicator />` - Visual priority score
- `<AlertActions />` - Quick action buttons

**State Management** (Zustand):
```typescript
interface AlertQueueStore {
  alerts: Alert[]
  selectedAlert: AlertDetail | null
  filters: AlertFilters
  pagination: { page: number, pageSize: number, total: number }

  // Actions
  fetchAlerts: (filters?: AlertFilters) => Promise<void>
  selectAlert: (alertId: string) => Promise<void>
  updateStatus: (alertId: string, status: AlertStatus) => Promise<void>
  createCase: (alertIds: string[]) => Promise<Case>

  // Real-time
  subscribeToAlerts: () => void
  handleNewAlert: (alert: Alert) => void
}
```

**Real-time Updates**:
- WebSocket channel: `alerts.new`
- Animation: Toast notification + top-of-queue insertion
- Sound alert for critical severity (optional)

**Advanced Features**:
- Bulk selection for case creation
- Custom filters (MITRE tactic, entity type, severity)
- Save filter presets
- Export to CSV
- Alert similarity clustering

**Acceptance Criteria**:
- [ ] Displays alerts with <1s load time
- [ ] Side panel shows all enrichment layers
- [ ] Real-time alerts appear without refresh
- [ ] Filters persist across sessions
- [ ] Can create case from multiple alerts
- [ ] Works with 10,000+ alerts (virtual scrolling)

---

### 3. Entity Explorer Widget

**Purpose**: "As an analyst, I need to investigate entities across all my security tools so I can understand the full picture."

**User Story**: Analyst searches IP "************", sees enrichment from geolocation, threat intel, and 847 related events from CrowdStrike and Elastic.

**APIs**:
```typescript
// Primary data
GET /api/entities/search?query={query}&type={type} → Entity[]
GET /api/entities/{entityId} → EntityDetail

// Enrichment layers
GET /api/entities/{entityId}/enrichment → EnrichmentData  // Layer 1
GET /api/investigation/context?entity={value}&vendor={vendor} → ContextData  // Layer 3

// Relationships
GET /api/entities/{entityId}/relationships → Relationship[]
GET /api/entities/{entityId}/timeline → TimelineEvent[]
```

**Data Flow**:
1. User enters search query → Call search API
2. Display matching entities in grid
3. User selects entity → Fetch full details
4. Parallel fetch: Enrichment + Relationships + Timeline
5. Display three-tab interface: Details / Relationships / Timeline
6. Load Layer 3 context on-demand (vendor-specific)

**Enrichment Display**:
- **Layer 1**:
  - Geolocation: Country, city, ISP, coordinates
  - Network: WHOIS, ASN, domain registration
  - Asset tier: Critical/Standard/Low
  - Security zone: DMZ/Internal/External

- **Layer 2**:
  - CTI matches from OTX, ThreatFox, CrowdStrike, OpenCTI
  - Threat score: 0-100
  - Associated campaigns, threat actors
  - IOC tags and categories

- **Layer 3**:
  - CrowdStrike context: Host info, detections, containment status
  - Elastic context: Related logs, aggregations, anomalies
  - Event count: X events found across Y days

**TypeScript Interface**:
```typescript
interface Entity {
  entity_id: string
  entity_type: 'ip' | 'user' | 'host' | 'process' | 'file_hash' | 'domain' | 'email'
  entity_value: string
  first_seen: string
  last_seen: string
  occurrence_count: number
  risk_score: number  // 0-100

  enrichments: {
    geolocation?: GeolocationData
    threat_intel?: ThreatIntelData
    network_info?: NetworkInfo
    asset_info?: AssetInfo
    cti_threat_intelligence?: CTIMatch  // Layer 2
  }

  threat_score: number  // From Layer 2
  is_threat: boolean
}

interface EntityDetail extends Entity {
  relationships: Relationship[]
  timeline: TimelineEvent[]
  vendor_context: {
    crowdstrike?: CrowdStrikeContext
    elastic?: ElasticContext
  }
  related_alerts: Alert[]
  related_cases: Case[]
}

interface Relationship {
  source_entity_id: string
  target_entity_id: string
  relationship_type: string  // 'connected_to', 'executed', 'created', 'accessed'
  weight: number
  first_seen: string
  last_seen: string
  event_count: number
}

interface TimelineEvent {
  timestamp: string
  event_type: string
  description: string
  source: string
  severity: 'info' | 'low' | 'medium' | 'high' | 'critical'
  entities: EntityReference[]
}

interface EntityExplorerState {
  searchResults: Entity[]
  selectedEntity: EntityDetail | null
  loading: {
    search: boolean
    details: boolean
    enrichment: boolean
    context: boolean
  }

  searchEntities: (query: string, type?: string) => Promise<void>
  selectEntity: (entityId: string) => Promise<void>
  loadVendorContext: (entityId: string, vendor: string) => Promise<void>
}
```

**Components**:
- `<EntitySearchBar />` - Search with type filter dropdown
- `<EntityResultsGrid />` - AG-Grid with entity list
- `<EntityDetailTabs />` - Three tabs: Details / Relationships / Timeline
- `<EnrichmentLayerCard />` - Displays one enrichment layer
- `<ThreatScoreBadge />` - Visual threat score (0-100)
- `<RelationshipGraph />` - D3.js force-directed graph
- `<EntityTimeline />` - Vertical timeline component
- `<VendorContextCard />` - On-demand vendor context loading

**State Management** (Zustand):
```typescript
interface EntityExplorerStore {
  searchResults: Entity[]
  selectedEntity: EntityDetail | null
  searchQuery: string
  filters: EntityFilters

  // Actions
  searchEntities: (query: string, filters?: EntityFilters) => Promise<void>
  selectEntity: (entityId: string) => Promise<void>
  loadEnrichment: (entityId: string) => Promise<void>
  loadRelationships: (entityId: string) => Promise<void>
  loadTimeline: (entityId: string, timeRange?: TimeRange) => Promise<void>
  loadVendorContext: (entityId: string, vendor: 'crowdstrike' | 'elastic') => Promise<void>

  // Utility
  clearSearch: () => void
  exportEntity: (entityId: string, format: 'json' | 'csv') => Promise<void>
}
```

**Real-time Updates**:
- WebSocket channel: `entities.updated`
- Update selected entity if new events arrive
- Show notification: "3 new events for this entity"

**Advanced Features**:
- Pivot to related entities (click relationship → load target entity)
- Bulk entity actions (tag, watchlist, export)
- Entity comparison (compare 2-3 entities side-by-side)
- Historical risk score graph
- Integration with alert creation (create alert from entity)

**Acceptance Criteria**:
- [ ] Search returns results in <1s
- [ ] All 3 enrichment layers display correctly
- [ ] Relationship graph renders 100+ nodes smoothly
- [ ] Timeline scrolls infinitely (lazy load)
- [ ] Vendor context loads on-demand without blocking UI
- [ ] Export works for JSON and CSV
- [ ] Mobile-responsive layout

---

### 4. CTI Plugin Status Widget

**Purpose**: "As an analyst, I need to monitor threat intelligence sources so I know if we're missing critical threat data."

**User Story**: Analyst checks CTI dashboard, sees OTX updated 5 minutes ago with 12 new indicators, ThreatFox failing for 2 hours.

**APIs**:
```typescript
// Primary data
GET /api/cti/status → CTIPluginStatus[]
GET /api/cti/stats → CTIStats

// Actions
POST /api/cti/manual_update → { task_id: string }
POST /api/cti/test_credentials → { valid: boolean }
```

**Data Flow**:
1. Fetch CTI plugin status for all 4 sources
2. Display health, last update, indicator counts
3. Poll every 30 seconds for status updates
4. User clicks "Update Now" → Trigger manual update
5. Show progress indicator during update
6. WebSocket updates on completion

**Enrichment Display**:
- **Layer 2 Only**: CTI plugin status is itself Layer 2 data
- Shows which threat intelligence sources are feeding Layer 2 enrichment

**TypeScript Interface**:
```typescript
interface CTIPluginStatus {
  source: 'otx' | 'threatfox' | 'crowdstrike' | 'opencti'
  status: 'active' | 'error' | 'updating' | 'disabled'
  last_update: string
  next_update: string
  total_indicators: number
  new_indicators_today: number
  error_message?: string
  credentials_valid: boolean
}

interface CTIStats {
  total_indicators: number
  indicators_by_type: Record<string, number>
  indicators_by_source: Record<string, number>
  avg_threat_score: number
  high_confidence_indicators: number
}

interface CTIPluginWidget {
  plugins: CTIPluginStatus[]
  stats: CTIStats
  loading: boolean
  updating: Record<string, boolean>  // Track per-plugin updates

  fetchStatus: () => Promise<void>
  triggerUpdate: (source: string) => Promise<void>
  testCredentials: (source: string) => Promise<boolean>
}
```

**Components**:
- `<CTIPluginCard />` - Individual plugin status card
- `<HealthIndicator />` - Green/yellow/red status dot
- `<LastUpdateBadge />` - Relative time display
- `<IndicatorCountChart />` - Small bar chart of indicator types
- `<UpdateButton />` - Manual update trigger
- `<CredentialTestButton />` - Test API connectivity

**State Management** (Zustand):
```typescript
interface CTIPluginStore {
  plugins: CTIPluginStatus[]
  stats: CTIStats
  loading: boolean
  polling: boolean

  // Actions
  fetchStatus: () => Promise<void>
  startPolling: () => void
  stopPolling: () => void
  triggerUpdate: (source: string, sinceDays?: number) => Promise<void>
  testCredentials: (source: string) => Promise<boolean>

  // WebSocket
  subscribeToUpdates: () => void
  handleUpdateComplete: (data: any) => void
}
```

**Real-time Updates**:
- WebSocket channel: `cti.update.complete`
- Poll every 30 seconds as backup
- Progress indicator during long-running updates

**Advanced Features**:
- Configure update frequency per plugin
- View indicator samples from each source
- Disable/enable plugins
- Historical update success rate graph

**Acceptance Criteria**:
- [ ] Shows real-time status for all 4 plugins
- [ ] Manual update triggers correctly
- [ ] Credential test validates connectivity
- [ ] Error messages display clearly
- [ ] Updates complete notification appears
- [ ] Polling doesn't impact UI performance

---

## P1 Widgets (Week 3-4)

### 5. Detection Fidelity Calculator

**Purpose**: "As a CISO, I need to quantify my organization's attack detection capability so I can justify security investments."

**User Story**: CISO selects their log sources, sees "You can detect 88.6% of common attacks" with coverage gaps highlighted.

**APIs**:
```typescript
// Primary data
GET /api/detection/fidelity → DetectionFidelity
GET /api/detection/techniques → TechniqueCoverage[]
GET /api/log-sources/quality → LogSourceQuality[]

// Simulation
POST /api/detection/simulate → SimulationResult
```

**Data Flow**:
1. Fetch current log sources and their quality tiers
2. Calculate detection fidelity across MITRE ATT&CK framework
3. User adjusts log sources (add/remove/upgrade)
4. Real-time recalculation of detection confidence
5. Display coverage heatmap by tactic/technique
6. Generate purchase recommendation report

**Enrichment Display**:
- **Layer 1**: Log source quality scores feed into calculation
- **Layer 2**: N/A
- **Layer 3**: N/A

**TypeScript Interface**:
```typescript
interface DetectionFidelity {
  overall_confidence: number  // 0-100
  confidence_by_tactic: Record<string, number>
  confidence_by_technique: Record<string, number>
  covered_techniques: number
  total_techniques: number
  coverage_percentage: number

  strengths: string[]  // High-confidence tactics
  weaknesses: string[]  // Low-confidence tactics
  critical_gaps: TechniqueGap[]
}

interface TechniqueGap {
  technique_id: string
  technique_name: string
  current_confidence: number
  required_sources: string[]  // What log sources would improve this
  impact: 'critical' | 'high' | 'medium' | 'low'
}

interface SimulationResult {
  current_fidelity: DetectionFidelity
  simulated_fidelity: DetectionFidelity
  improvement: number  // Percentage points gained
  cost_estimate: number
  recommended_sources: LogSource[]
}

interface DetectionFidelityState {
  fidelity: DetectionFidelity
  logSources: LogSourceQuality[]
  simulation: SimulationResult | null
  loading: boolean

  fetchFidelity: () => Promise<void>
  simulateChanges: (changes: LogSourceChange[]) => Promise<void>
  exportReport: () => Promise<void>
}
```

**Components**:
- `<FidelityScore />` - Large percentage display with trend
- `<MITREHeatmap />` - Technique coverage visualization
- `<GapAnalysisTable />` - Critical gaps with recommendations
- `<LogSourceSimulator />` - Interactive source selector
- `<ImprovementChart />` - Before/after comparison
- `<PurchaseReport />` - Executive summary for buying

**State Management** (Zustand):
```typescript
interface DetectionFidelityStore {
  fidelity: DetectionFidelity
  logSources: LogSourceQuality[]
  simulation: SimulationResult | null

  fetchFidelity: () => Promise<void>
  simulateAddSource: (source: string, tier: QualityTier) => Promise<void>
  simulateRemoveSource: (sourceId: string) => Promise<void>
  simulateUpgradeSource: (sourceId: string, newTier: QualityTier) => Promise<void>
  resetSimulation: () => void
  exportReport: (format: 'pdf' | 'excel') => Promise<void>
}
```

**Real-time Updates**:
- Recalculates on log source changes
- Debounced to avoid excessive calculations

**Advanced Features**:
- Budget-aware recommendations (best ROI sources)
- Threat-specific fidelity (ransomware, insider threat, APT)
- Compliance mapping (show which controls are met)
- Multi-environment comparison (compare prod vs dev fidelity)

**Acceptance Criteria**:
- [ ] Calculates fidelity accurately based on log sources
- [ ] Simulation updates in <500ms
- [ ] Heatmap displays 200+ techniques clearly
- [ ] Purchase report exports to PDF
- [ ] Recommendations are actionable and specific
- [ ] Works with 50+ log sources

---

### 6. Log Source Quality Matrix

**Purpose**: "As a security engineer, I need to understand the quality of my log sources so I can prioritize improvements."

**User Story**: Engineer sees CrowdStrike logs are PLATINUM tier (quality 9.5/10), but firewall logs are BRONZE tier (quality 4.2/10).

**APIs**:
```typescript
// Primary data
GET /api/log-sources/quality → LogSourceQuality[]
GET /api/log-sources/{sourceId}/details → LogSourceDetail

// Analysis
GET /api/log-sources/{sourceId}/analysis → QualityAnalysis
POST /api/log-sources/upload_sample → { analysis_id: string }
```

**Data Flow**:
1. Fetch all log sources with quality scores
2. Display in sortable/filterable matrix
3. User clicks source → Show detailed analysis
4. Display parsing success rate, field extraction, value
5. Show recommendations for improvement

**Enrichment Display**:
- **Layer 1**: Log source quality directly impacts Layer 1 enrichment success
- Shows which sources provide best entity extraction

**TypeScript Interface**:
```typescript
interface LogSourceQuality {
  source_id: string
  source_name: string
  source_type: string
  quality_tier: 'PLATINUM' | 'GOLD' | 'SILVER' | 'BRONZE'
  quality_score: number  // 0-10

  metrics: {
    parsing_success_rate: number
    field_extraction_score: number
    entity_extraction_score: number
    timestamp_accuracy: number
    data_completeness: number
  }

  volume: {
    events_per_day: number
    size_per_day_mb: number
    retention_days: number
  }

  value_score: number  // Security value vs cost
  recommendations: string[]
}

interface QualityAnalysis {
  source_id: string
  sample_size: number
  analysis_date: string

  field_coverage: Record<string, number>  // Field -> extraction %
  entity_types_found: string[]
  common_errors: ErrorPattern[]
  improvement_potential: number  // How much score could improve
  specific_recommendations: Recommendation[]
}

interface Recommendation {
  category: 'parser' | 'configuration' | 'upgrade' | 'replacement'
  priority: 'critical' | 'high' | 'medium' | 'low'
  description: string
  expected_improvement: number
  effort_required: 'low' | 'medium' | 'high'
}
```

**Components**:
- `<QualityMatrix />` - AG-Grid with custom cell renderers
- `<TierBadge />` - Visual tier indicator (PLATINUM/GOLD/etc)
- `<QualityScoreGauge />` - Radial gauge 0-10
- `<MetricsBreakdown />` - Detailed metrics display
- `<RecommendationCard />` - Improvement suggestions
- `<VolumeChart />` - Events per day line chart

**State Management** (Zustand):
```typescript
interface LogSourceQualityStore {
  sources: LogSourceQuality[]
  selectedSource: LogSourceDetail | null
  analysis: QualityAnalysis | null
  filters: QualityFilters

  fetchSources: () => Promise<void>
  selectSource: (sourceId: string) => Promise<void>
  analyzeSource: (sourceId: string) => Promise<void>
  uploadSample: (file: File) => Promise<void>
  exportMatrix: () => Promise<void>
}
```

**Real-time Updates**:
- Poll every 5 minutes for metric updates
- WebSocket updates on new source addition

**Advanced Features**:
- Upload log samples for instant analysis
- Compare sources side-by-side
- Historical quality trends
- Cost vs value analysis

**Acceptance Criteria**:
- [ ] Displays all log sources with correct quality scores
- [ ] Matrix is sortable and filterable
- [ ] Detailed analysis shows actionable recommendations
- [ ] Upload and analyze works for any log format
- [ ] Export works for reporting

---

### 7. Coverage Simulation Tool

**Purpose**: "As a CISO, I need to simulate security tool purchases to understand detection improvement before buying."

**User Story**: CISO adds CrowdStrike Falcon to simulation, sees detection confidence increase from 65% → 88%, with ROI calculation.

**APIs**:
```typescript
// Primary data
GET /api/detection/fidelity → DetectionFidelity
GET /api/log-sources/catalog → AvailableLogSource[]

// Simulation
POST /api/detection/simulate → SimulationResult
POST /api/detection/compare_scenarios → ComparisonResult
```

**Data Flow**:
1. Load current detection fidelity baseline
2. Display catalog of available log sources
3. User adds/removes sources in simulation
4. Real-time calculation of new fidelity
5. Show cost, improvement, ROI
6. Generate executive summary

**Enrichment Display**:
- **Layer 1**: Log source quality drives fidelity calculation
- Shows which sources improve which enrichment layers

**TypeScript Interface**:
```typescript
interface AvailableLogSource {
  source_name: string
  vendor: string
  category: string
  quality_tier: QualityTier
  estimated_cost_annual: number
  typical_event_volume: string
  techniques_improved: string[]
}

interface SimulationScenario {
  name: string
  added_sources: AvailableLogSource[]
  removed_sources: string[]
  total_cost: number
  fidelity_improvement: number
  roi_score: number
}

interface SimulationResult {
  baseline: DetectionFidelity
  simulated: DetectionFidelity
  improvement: {
    overall_confidence: number
    techniques_added: string[]
    gaps_closed: TechniqueGap[]
  }
  cost: {
    annual_cost: number
    cost_per_confidence_point: number
    roi_score: number
  }
}

interface CoverageSimulationState {
  baseline: DetectionFidelity
  availableSources: AvailableLogSource[]
  scenarios: SimulationScenario[]
  activeScenario: SimulationScenario | null

  createScenario: (name: string) => void
  addSourceToScenario: (source: AvailableLogSource) => Promise<void>
  removeSourceFromScenario: (sourceName: string) => Promise<void>
  simulateScenario: () => Promise<SimulationResult>
  compareScenarios: (scenarioIds: string[]) => Promise<ComparisonResult>
  exportReport: () => Promise<void>
}
```

**Components**:
- `<SimulationWorkspace />` - Drag-and-drop interface
- `<SourceCatalog />` - Browse available sources
- `<ScenarioBuilder />` - Build simulation scenario
- `<ImprovementVisualizer />` - Before/after heatmap
- `<ROICalculator />` - Cost vs improvement display
- `<ExecutiveSummary />` - Purchase justification report

**State Management** (Zustand):
```typescript
interface CoverageSimulationStore {
  baseline: DetectionFidelity
  availableSources: AvailableLogSource[]
  scenarios: SimulationScenario[]
  activeScenario: SimulationScenario | null
  simulationResult: SimulationResult | null

  // Scenario management
  createScenario: (name: string) => void
  loadScenario: (scenarioId: string) => void
  deleteScenario: (scenarioId: string) => void

  // Simulation
  addSource: (source: AvailableLogSource) => Promise<void>
  removeSource: (sourceName: string) => Promise<void>
  simulate: () => Promise<SimulationResult>
  compare: (scenarioIds: string[]) => Promise<ComparisonResult>

  // Export
  exportPDF: () => Promise<void>
  exportExcel: () => Promise<void>
}
```

**Real-time Updates**:
- Recalculates on every source change
- Debounced to 500ms

**Advanced Features**:
- Budget-constrained optimization (best sources for $X)
- Threat-specific scenarios (optimize for ransomware detection)
- Multi-year cost projections
- Competitive analysis (compare vendor offerings)

**Acceptance Criteria**:
- [ ] Simulation runs in <2s
- [ ] Supports 50+ available sources
- [ ] ROI calculation is accurate
- [ ] Executive report generates professional PDF
- [ ] Can save and load scenarios
- [ ] Mobile-responsive drag-and-drop

---

### 8. Entity Details Panel

**Purpose**: "As an analyst, I need comprehensive entity details to make accurate triage decisions."

**User Story**: Analyst clicks IP address in alert, sees geolocation, threat intel matches, 124 related events, and relationship graph all in one panel.

**APIs**:
```typescript
// Primary data
GET /api/entities/{entityId} → EntityDetail

// Enrichment (all 3 layers)
GET /api/entities/{entityId}/enrichment → EnrichmentData
GET /api/investigation/context?entity={value}&vendor={vendor} → VendorContext

// Related data
GET /api/entities/{entityId}/relationships → Relationship[]
GET /api/entities/{entityId}/timeline?days=7 → TimelineEvent[]
GET /api/entities/{entityId}/alerts → Alert[]
```

**Data Flow**:
1. Receive entityId from parent component
2. Fetch entity details + enrichment in parallel
3. Display loading skeleton during fetch
4. Render three tabs: Overview / Relationships / Timeline
5. Load vendor context on-demand (Layer 3)
6. Subscribe to entity updates

**Enrichment Display**:
- **Layer 1**: Complete display
  - Geolocation: Map, ISP, city, country
  - Network: WHOIS, ASN, registration
  - Asset: Tier, security zone, owner

- **Layer 2**: Complete display
  - CTI matches: All 4 sources
  - Threat score: 0-100 with explanation
  - Campaign/actor associations
  - IOC tags and confidence

- **Layer 3**: On-demand display
  - CrowdStrike: Host info, detections
  - Elastic: Event aggregations, anomalies
  - Event counts and timeframes

**TypeScript Interface**:
```typescript
// Already defined in Entity Explorer, reuse same interfaces
interface EntityDetailPanelProps {
  entityId: string
  onClose: () => void
  onPivot?: (entityId: string) => void
}

interface EntityDetailPanelState {
  entity: EntityDetail | null
  enrichment: EnrichmentData | null
  vendorContext: Record<string, VendorContext>
  loading: {
    entity: boolean
    enrichment: boolean
    relationships: boolean
    timeline: boolean
  }

  loadEntity: (entityId: string) => Promise<void>
  loadVendorContext: (vendor: string) => Promise<void>
  refreshData: () => Promise<void>
}
```

**Components**:
- `<EntityHeader />` - Entity value, type, first/last seen
- `<ThreatScoreDisplay />` - Large threat score with color coding
- `<EnrichmentTabs />` - Three tabs for enrichment layers
- `<GeolocationMap />` - Interactive map with marker
- `<CTIMatchList />` - All CTI matches from Layer 2
- `<VendorContextCard />` - Collapsible vendor context
- `<RelationshipMiniGraph />` - Small force-directed graph
- `<TimelineViewer />` - Scrollable timeline

**State Management** (Zustand):
```typescript
// Reuse EntityExplorerStore, add panel-specific actions
interface EntityDetailPanelStore extends EntityExplorerStore {
  panelOpen: boolean
  panelEntityId: string | null

  openPanel: (entityId: string) => Promise<void>
  closePanel: () => void
  pivotToEntity: (entityId: string) => Promise<void>
}
```

**Real-time Updates**:
- WebSocket channel: `entities.{entityId}.updated`
- Show badge: "New data available"
- Auto-refresh option

**Advanced Features**:
- Pin entities for comparison
- Add to watchlist
- Create alert from entity
- Export full entity report
- Historical threat score graph

**Acceptance Criteria**:
- [ ] Loads all data in <2s
- [ ] All 3 enrichment layers display correctly
- [ ] Vendor context loads on-demand
- [ ] Relationship graph is interactive
- [ ] Timeline scrolls smoothly
- [ ] Export generates complete report

---

## P2 Widgets (Week 5-6)

### 9. Investigation Dashboard

**Purpose**: "As an analyst, I need a unified investigation workspace to analyze complex incidents efficiently."

**User Story**: Analyst investigating ransomware, sees patient zero, lateral movement path, affected hosts, and AI-generated investigation guide in one view.

**APIs**:
```typescript
// Case data
GET /api/cases/{caseId} → CaseDetail
GET /api/cases/{caseId}/investigation-guide → InvestigationGuide

// Investigation data
GET /api/investigation/context?case_id={caseId} → InvestigationContext
GET /api/entities/{entityId} → Entity
GET /api/entities/{entityId}/relationships → Relationship[]

// Actions
POST /api/cases/{caseId}/evidence → { evidence_id: string }
PATCH /api/investigation/guide/{caseId}/step/{stepId} → InvestigationGuide
```

**Data Flow**:
1. Load case details + investigation guide
2. Fetch all entities in case
3. Build relationship graph
4. Display investigation guide steps
5. User completes step → Mark complete → Next step
6. Add evidence to case as investigation progresses

**Enrichment Display**:
- **All 3 Layers**: Complete integration
- Shows enrichment for every entity in investigation
- Highlights threat indicators from Layer 2
- Pulls vendor context from Layer 3

**TypeScript Interface**:
```typescript
interface InvestigationGuide {
  case_id: string
  steps: InvestigationStep[]
  generated_at: string
  ai_model: string
  completed_percentage: number
}

interface InvestigationStep {
  id: string
  title: string
  description: string
  queries: Record<string, string>  // SIEM -> query mapping
  evidence_required: string[]
  completed: boolean
  findings?: string
}

interface InvestigationContext {
  case_id: string
  entities: Entity[]
  relationships: Relationship[]
  timeline: TimelineEvent[]
  threat_summary: string
  affected_systems: string[]
  patient_zero?: Entity
}

interface InvestigationDashboardState {
  case: CaseDetail | null
  guide: InvestigationGuide | null
  context: InvestigationContext | null
  selectedEntity: Entity | null
  loading: boolean

  loadInvestigation: (caseId: string) => Promise<void>
  selectEntity: (entityId: string) => void
  completeStep: (stepId: string, findings: string) => Promise<void>
  addEvidence: (evidence: Evidence) => Promise<void>
}
```

**Components**:
- `<InvestigationHeader />` - Case info, status, assignee
- `<InvestigationGuide />` - Step-by-step checklist
- `<AttackPathGraph />` - Relationship graph focused on attack path
- `<EntityGrid />` - All entities in investigation
- `<EvidencePanel />` - Evidence collection interface
- `<QueryRunner />` - Run SIEM queries from guide
- `<ThreatSummary />` - AI-generated threat summary

**State Management** (Zustand):
```typescript
interface InvestigationDashboardStore {
  case: CaseDetail | null
  guide: InvestigationGuide | null
  context: InvestigationContext | null
  entities: Map<string, Entity>
  relationships: Relationship[]
  evidence: Evidence[]

  loadInvestigation: (caseId: string) => Promise<void>
  completeStep: (stepId: string, findings: string) => Promise<void>
  addEvidence: (evidence: Evidence) => Promise<void>
  updateCase: (updates: Partial<CaseDetail>) => Promise<void>
  exportReport: () => Promise<void>
}
```

**Real-time Updates**:
- WebSocket channel: `cases.{caseId}.*`
- Collaborative investigation (multiple analysts)
- Live updates on step completion

**Advanced Features**:
- Collaborative investigation (multi-user)
- Query templates for common searches
- Evidence chain of custody
- Report generation (timeline, findings, recommendations)

**Acceptance Criteria**:
- [ ] Loads complete investigation context in <3s
- [ ] Graph displays attack path clearly
- [ ] Investigation guide updates in real-time
- [ ] Query runner works with multiple SIEMs
- [ ] Evidence collection preserves chain of custody
- [ ] Export generates comprehensive report

---

### 10. MITRE ATT&CK Heatmap

**Purpose**: "As a security leader, I need to visualize my attack surface coverage across the MITRE ATT&CK framework."

**User Story**: Security director sees heatmap showing strong detection in Initial Access (95%), but weak in Defense Evasion (45%).

**APIs**:
```typescript
// Primary data
GET /api/mitre/heatmap?time_range={range} → MITREHeatmap
GET /api/mitre/techniques → TechniqueDetail[]
GET /api/mitre/techniques/{techniqueId} → TechniqueDetail

// Coverage
GET /api/detection/techniques → TechniqueCoverage[]
```

**Data Flow**:
1. Fetch MITRE heatmap data for time range
2. Render heatmap with color-coded cells
3. User hovers technique → Show tooltip with details
4. User clicks technique → Show detailed coverage
5. Filter by tactic, status, confidence level

**Enrichment Display**:
- **Layer 1**: Log source quality determines technique coverage
- Shows which sources detect which techniques

**TypeScript Interface**:
```typescript
interface MITREHeatmap {
  tactics: Tactic[]
  time_range: string
  generated_at: string
}

interface Tactic {
  tactic_id: string
  tactic_name: string
  techniques: TechniqueCell[]
  average_confidence: number
}

interface TechniqueCell {
  technique_id: string
  technique_name: string
  confidence: number  // 0-100
  detections_30d: number
  status: 'covered' | 'partial' | 'gap'
  detection_sources: string[]
}

interface TechniqueDetail {
  technique_id: string
  technique_name: string
  tactic: string
  description: string
  detection_confidence: number
  detection_sources: LogSource[]
  recent_alerts: Alert[]
  recommended_improvements: string[]
}

interface MITREHeatmapState {
  heatmap: MITREHeatmap | null
  selectedTechnique: TechniqueDetail | null
  filters: HeatmapFilters
  loading: boolean

  fetchHeatmap: (timeRange: string) => Promise<void>
  selectTechnique: (techniqueId: string) => Promise<void>
  applyFilters: (filters: HeatmapFilters) => void
}
```

**Components**:
- `<HeatmapGrid />` - Matrix of tactics x techniques
- `<TechniqueCell />` - Individual cell with color coding
- `<TechniqueTooltip />` - Hover tooltip with quick stats
- `<TechniqueDetailPanel />` - Detailed side panel
- `<TacticSummary />` - Tactic-level statistics
- `<LegendCard />` - Color legend and filters

**State Management** (Zustand):
```typescript
interface MITREHeatmapStore {
  heatmap: MITREHeatmap | null
  techniques: Map<string, TechniqueDetail>
  selectedTechnique: TechniqueDetail | null
  filters: HeatmapFilters
  timeRange: string

  fetchHeatmap: (timeRange: string) => Promise<void>
  selectTechnique: (techniqueId: string) => Promise<void>
  filterByTactic: (tacticId: string) => void
  filterByConfidence: (minConfidence: number) => void
  exportHeatmap: (format: 'svg' | 'png' | 'pdf') => Promise<void>
}
```

**Real-time Updates**:
- Update detection counts every minute
- WebSocket channel: `mitre.heatmap.updated`

**Advanced Features**:
- Time-based animation (watch coverage evolve over time)
- Compare time periods (this month vs last month)
- Export as SVG/PNG for presentations
- ATT&CK Navigator integration

**Acceptance Criteria**:
- [ ] Renders all 200+ techniques clearly
- [ ] Hover tooltips are instant
- [ ] Color coding is intuitive
- [ ] Detail panel shows actionable improvements
- [ ] Exports work for presentations
- [ ] Mobile-responsive (stacked tactics)

---

## Component Reusability Matrix

| Component | Used In | Reusable? |
|-----------|---------|-----------|
| `<StatCard />` | Dashboard, Multiple | ✅ |
| `<ThreatScoreBadge />` | All entity displays | ✅ |
| `<EntityEnrichmentCard />` | Alert Queue, Entity Explorer, Investigation | ✅ |
| `<MITREBadge />` | Alert Queue, Investigation, Heatmap | ✅ |
| `<LoadingSkeleton />` | All widgets | ✅ |
| `<ErrorBoundary />` | All widgets | ✅ |
| `<ExportButton />` | Multiple widgets | ✅ |
| `<FilterPanel />` | Alert Queue, Entity Explorer, Quality Matrix | ✅ |
| `<TimeRangePicker />` | Dashboard, Heatmap, Timeline | ✅ |

---

## State Management Strategy

### Global Stores (Zustand)
- `authStore` - User authentication
- `websocketStore` - WebSocket connections
- `notificationStore` - Toast notifications
- `themeStore` - Dark/light mode

### Widget-Specific Stores
Each widget gets its own store for isolation and performance.

### Shared Utilities
- `apiClient` - Axios instance with interceptors
- `formatters` - Date, number, entity formatting
- `validators` - Input validation
- `constants` - Enum types, configs

---

## Testing Strategy

### Unit Tests (Jest + React Testing Library)
- Component rendering
- User interactions
- State management
- API mocking

### Integration Tests
- Multi-component workflows
- API integration
- WebSocket connections
- Error scenarios

### E2E Tests (Playwright)
- Critical user flows
- Alert triage workflow
- Investigation workflow
- Executive dashboard review

---

## Accessibility Requirements

- WCAG 2.1 AA compliance
- Keyboard navigation for all widgets
- Screen reader support
- High contrast mode
- Focus indicators
- ARIA labels

---

## Performance Targets

| Metric | Target |
|--------|--------|
| Initial Load | <3s |
| Widget Render | <500ms |
| API Response | <1s |
| Real-time Update | <100ms |
| Bundle Size | <2MB |
| Lighthouse Score | >90 |

---

## Next Steps

1. ✅ Complete API Reference (Done)
2. ✅ Widget Catalog (This Document)
3. ⏳ Generate TypeScript Types (Next)
4. ⏳ Create Integration Patterns (Next)
5. ⏳ Build Reusable Components (Week 1)
6. ⏳ Implement P0 Widgets (Week 1-2)

---

**Document Status**: Complete
**Last Updated**: October 3, 2025
**Next Review**: After TypeScript types generation
