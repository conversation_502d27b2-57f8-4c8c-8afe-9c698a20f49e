import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { AgGridReact } from 'ag-grid-react'
import { ColDef, GridApi, GridReadyEvent, ICellRendererParams } from 'ag-grid-community'
import 'ag-grid-community/styles/ag-grid.css'
import 'ag-grid-community/styles/ag-theme-alpine.css'
import { Database, TrendingUp, DollarSign, CheckCircle, XCircle } from 'lucide-react'

interface Pattern {
  id: string
  name: string
  category: 'security' | 'entity' | 'behavior' | 'cti'
  created: string
  lastUsed: string
  useCount: number
  costSaved: number
  accuracy: number
  status: 'active' | 'inactive' | 'deprecated'
  aiModel: string
  description: string
}

// Status renderer
const StatusBadge: React.FC<ICellRendererParams> = ({ value }) => {
  const config = {
    active: { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-100' },
    inactive: { icon: XCircle, color: 'text-gray-600', bg: 'bg-gray-100' },
    deprecated: { icon: XCircle, color: 'text-red-600', bg: 'bg-red-100' }
  }

  const { icon: Icon, color, bg } = config[value as keyof typeof config]

  return (
    <div className={`flex items-center gap-1 px-2 py-1 rounded ${bg} ${color}`}>
      <Icon size={14} />
      <span className="text-xs font-medium capitalize">{value}</span>
    </div>
  )
}

// Cost saved renderer
const CostRenderer: React.FC<ICellRendererParams> = ({ value }) => {
  return (
    <div className="flex items-center gap-1 text-green-600">
      <DollarSign size={14} />
      <span className="font-medium">${value.toFixed(2)}</span>
    </div>
  )
}

// Use count renderer with trend
const UseCountRenderer: React.FC<ICellRendererParams> = ({ value }) => {
  const isHighUsage = value > 1000

  return (
    <div className="flex items-center gap-2">
      <span className={`font-medium ${isHighUsage ? 'text-green-600' : ''}`}>
        {value.toLocaleString()}
      </span>
      {isHighUsage && <TrendingUp size={14} className="text-green-600" />}
    </div>
  )
}

// Accuracy renderer
const AccuracyRenderer: React.FC<ICellRendererParams> = ({ value }) => {
  const percentage = Math.round(value * 100)
  const color = percentage > 90 ? 'text-green-600' : percentage > 70 ? 'text-yellow-600' : 'text-red-600'

  return (
    <div className="flex items-center gap-2">
      <div className="flex-1 bg-gray-200 rounded-full h-1.5">
        <div
          className={`${color.replace('text', 'bg')} h-1.5 rounded-full`}
          style={{ width: `${percentage}%` }}
        />
      </div>
      <span className={`text-xs font-medium ${color}`}>{percentage}%</span>
    </div>
  )
}

interface PatternLibraryProps {
  onPatternSelect?: (pattern: Pattern) => void
}

export const PatternLibrary: React.FC<PatternLibraryProps> = ({ onPatternSelect }) => {
  const [rowData, setRowData] = useState<Pattern[]>([])
  const [gridApi, setGridApi] = useState<GridApi | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  // Column definitions
  const columnDefs: ColDef[] = useMemo(() => [
    {
      field: 'name',
      headerName: 'Pattern Name',
      flex: 1,
      minWidth: 200,
      pinned: 'left',
      cellClass: 'font-medium'
    },
    {
      field: 'category',
      headerName: 'Category',
      width: 120,
      filter: true,
      cellRenderer: (params: ICellRendererParams) => {
        const categoryColors = {
          security: 'bg-red-100 text-red-800',
          entity: 'bg-blue-100 text-blue-800',
          behavior: 'bg-purple-100 text-purple-800',
          cti: 'bg-yellow-100 text-yellow-800'
        }
        return (
          <span className={`px-2 py-1 rounded text-xs font-medium ${categoryColors[params.value as keyof typeof categoryColors]}`}>
            {params.value}
          </span>
        )
      }
    },
    {
      field: 'useCount',
      headerName: 'Usage',
      width: 120,
      sortable: true,
      sort: 'desc',
      cellRenderer: UseCountRenderer
    },
    {
      field: 'costSaved',
      headerName: 'Cost Saved',
      width: 120,
      cellRenderer: CostRenderer,
      sortable: true
    },
    {
      field: 'accuracy',
      headerName: 'Accuracy',
      width: 150,
      cellRenderer: AccuracyRenderer,
      sortable: true
    },
    {
      field: 'aiModel',
      headerName: 'AI Model',
      width: 120
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 110,
      cellRenderer: StatusBadge,
      filter: true
    },
    {
      field: 'lastUsed',
      headerName: 'Last Used',
      width: 140,
      valueFormatter: (params) => {
        const date = new Date(params.value)
        const now = new Date()
        const diffMs = now.getTime() - date.getTime()
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60))

        if (diffHours < 1) return 'Just now'
        if (diffHours < 24) return `${diffHours}h ago`
        if (diffHours < 168) return `${Math.floor(diffHours / 24)}d ago`
        return date.toLocaleDateString()
      }
    }
  ], [])

  // Default column properties
  const defaultColDef = useMemo(() => ({
    resizable: true,
    sortable: true,
    filter: false
  }), [])

  // Generate mock data
  const generateMockData = (): Pattern[] => {
    const categories: Pattern['category'][] = ['security', 'entity', 'behavior', 'cti']
    const models = ['GPT-4', 'Claude', 'Gemini', 'Local']
    const statuses: Pattern['status'][] = ['active', 'inactive', 'deprecated']

    return Array.from({ length: 50 }, (_, i) => ({
      id: `pattern-${i}`,
      name: `Pattern ${i + 1}: ${['Failed Login Detection', 'Suspicious Network Activity', 'Data Exfiltration', 'Privilege Escalation'][i % 4]}`,
      category: categories[Math.floor(Math.random() * categories.length)],
      created: new Date(Date.now() - Math.random() * 30 * 86400000).toISOString(),
      lastUsed: new Date(Date.now() - Math.random() * 7 * 86400000).toISOString(),
      useCount: Math.floor(Math.random() * 5000),
      costSaved: Math.random() * 1000,
      accuracy: 0.7 + Math.random() * 0.3,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      aiModel: models[Math.floor(Math.random() * models.length)],
      description: 'AI-validated pattern for detecting suspicious activities'
    }))
  }

  // Load data
  useEffect(() => {
    setRowData(generateMockData())
  }, [])

  // Grid ready handler
  const onGridReady = useCallback((params: GridReadyEvent) => {
    setGridApi(params.api)
  }, [])

  // Row selection handler
  const onSelectionChanged = useCallback(() => {
    if (gridApi) {
      const selected = gridApi.getSelectedRows()
      if (selected.length > 0 && onPatternSelect) {
        onPatternSelect(selected[0])
      }
    }
  }, [gridApi, onPatternSelect])

  // Calculate statistics
  const stats = useMemo(() => {
    const active = rowData.filter(p => p.status === 'active').length
    const totalSaved = rowData.reduce((sum, p) => sum + p.costSaved, 0)
    const totalUses = rowData.reduce((sum, p) => sum + p.useCount, 0)
    const avgAccuracy = rowData.reduce((sum, p) => sum + p.accuracy, 0) / rowData.length

    return { active, totalSaved, totalUses, avgAccuracy }
  }, [rowData])

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header with stats */}
      <div className="p-4 border-b">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <Database size={20} />
              Pattern Library
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              AI-crystallized patterns for instant recognition
            </p>
          </div>
          <div className="flex gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">${stats.totalSaved.toFixed(0)}</p>
              <p className="text-xs text-gray-600">Total Saved</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{stats.totalUses.toLocaleString()}</p>
              <p className="text-xs text-gray-600">Total Uses</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{(stats.avgAccuracy * 100).toFixed(0)}%</p>
              <p className="text-xs text-gray-600">Avg Accuracy</p>
            </div>
          </div>
        </div>

        {/* Category filters */}
        <div className="flex gap-2 mt-4">
          <button
            className={`px-3 py-1 rounded text-sm ${selectedCategory === 'all' ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
            onClick={() => setSelectedCategory('all')}
          >
            All ({rowData.length})
          </button>
          {['security', 'entity', 'behavior', 'cti'].map(cat => (
            <button
              key={cat}
              className={`px-3 py-1 rounded text-sm capitalize ${selectedCategory === cat ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
              onClick={() => setSelectedCategory(cat)}
            >
              {cat} ({rowData.filter(p => p.category === cat).length})
            </button>
          ))}
        </div>
      </div>

      {/* AG-Grid */}
      <div className="flex-1 ag-theme-alpine">
        <AgGridReact
          rowData={selectedCategory === 'all' ? rowData : rowData.filter(p => p.category === selectedCategory)}
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          onGridReady={onGridReady}
          onSelectionChanged={onSelectionChanged}
          rowSelection="single"
          animateRows={true}
          pagination={true}
          paginationPageSize={15}
          rowHeight={35}
        />
      </div>
    </div>
  )
}

export default PatternLibrary