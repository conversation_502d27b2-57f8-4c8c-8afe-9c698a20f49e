"""
Log Schema Detector
Detects log schemas using field structure hashing for exact matching
Part of the "learn once, extract forever" pattern
"""

import hashlib
import json
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


def extract_all_field_paths(obj: Any, prefix: str = '', seen_paths: Optional[Set[str]] = None) -> List[str]:
    """
    Extract all field paths from nested object

    Examples:
    - {"source": {"ip": "*******"}} → ['source', 'source.ip']
    - {"data": [{"ip": "*******"}]} → ['data[]', 'data[].ip']
    - {"items": []} → ['items[]']

    Args:
        obj: Object to analyze
        prefix: Current path prefix
        seen_paths: Set to track seen paths (for deduplication)

    Returns:
        List of field paths
    """
    if seen_paths is None:
        seen_paths = set()

    paths = []

    if isinstance(obj, dict):
        for key, value in obj.items():
            current_path = f"{prefix}.{key}" if prefix else key

            if current_path not in seen_paths:
                paths.append(current_path)
                seen_paths.add(current_path)

            # Recursively process nested structures
            paths.extend(extract_all_field_paths(value, current_path, seen_paths))

    elif isinstance(obj, list):
        # Mark as array
        array_path = f"{prefix}[]"
        if array_path not in seen_paths:
            paths.append(array_path)
            seen_paths.add(array_path)

        # Analyze first element if array is not empty
        if len(obj) > 0:
            paths.extend(extract_all_field_paths(obj[0], f"{prefix}[]", seen_paths))

    return paths


def generate_schema_hash(log: Dict[str, Any]) -> str:
    """
    Generate deterministic hash from log field structure

    Process:
    1. Extract all field paths
    2. Sort alphabetically (for consistency)
    3. Hash with SHA-256

    Same structure = Same hash = Known schema

    Args:
        log: Log dictionary to analyze

    Returns:
        64-character hex string (SHA-256)
    """
    try:
        # Extract all field paths
        field_paths = extract_all_field_paths(log)

        # Sort for consistency
        sorted_paths = sorted(set(field_paths))

        # Create deterministic string
        schema_string = '|'.join(sorted_paths)

        # Hash with SHA-256
        schema_hash = hashlib.sha256(schema_string.encode('utf-8')).hexdigest()

        logger.debug(f"Generated schema hash {schema_hash[:16]}... from {len(sorted_paths)} fields")

        return schema_hash

    except Exception as e:
        logger.error(f"Failed to generate schema hash: {e}")
        # Return a fallback hash
        return hashlib.sha256(json.dumps(log, sort_keys=True).encode('utf-8')).hexdigest()


def detect_vendor_from_log(log: Dict[str, Any]) -> Dict[str, str]:
    """
    Detect vendor and product from log structure

    Checks common vendor identification fields:
    - observer.vendor / observer.product (ECS)
    - event_simpleName (CrowdStrike)
    - devid / devname (Fortinet)
    - CEF header (Various)

    Args:
        log: Log dictionary

    Returns:
        Dict with 'vendor', 'product', 'log_type' keys
    """
    vendor_info = {
        'vendor': 'Unknown',
        'product': 'Unknown',
        'log_type': 'generic'
    }

    try:
        # Check ECS observer fields (most common in Elasticsearch)
        if 'observer' in log:
            vendor_info['vendor'] = log['observer'].get('vendor', 'Unknown')
            vendor_info['product'] = log['observer'].get('product', 'Unknown')
            vendor_info['log_type'] = log['observer'].get('type', 'generic')
            return vendor_info

        # Nested Elasticsearch format
        if 'content' in log and 'log' in log.get('content', {}):
            data_array = log['content']['log'].get('data', [])
            if data_array and len(data_array) > 0:
                first_entry = data_array[0].get('data', {})
                if 'observer' in first_entry:
                    vendor_info['vendor'] = first_entry['observer'].get('vendor', 'Unknown')
                    vendor_info['product'] = first_entry['observer'].get('product', 'Unknown')
                    vendor_info['log_type'] = first_entry['observer'].get('type', 'generic')
                    return vendor_info

        # CrowdStrike detection
        if 'event_simpleName' in log or 'aid' in log:
            vendor_info['vendor'] = 'CrowdStrike'
            vendor_info['product'] = 'Falcon'
            vendor_info['log_type'] = 'edr'
            return vendor_info

        # Fortinet detection
        if 'devid' in log or 'devname' in log:
            vendor_info['vendor'] = 'Fortinet'
            vendor_info['product'] = 'FortiGate'
            vendor_info['log_type'] = 'firewall'
            return vendor_info

        # Palo Alto detection
        if 'PAN-OS' in str(log) or ('device_name' in log and 'serial' in log):
            vendor_info['vendor'] = 'Palo Alto'
            vendor_info['product'] = 'PAN-OS'
            vendor_info['log_type'] = 'firewall'
            return vendor_info

    except Exception as e:
        logger.error(f"Error detecting vendor: {e}")

    return vendor_info


class LogSchemaDetector:
    """
    Detects if log schemas are known or new
    Uses Redis cache + PostgreSQL database
    """

    def __init__(self, redis_client, db_connection, logger=None):
        self.redis = redis_client
        self.db = db_connection
        self.logger = logger or logging.getLogger(__name__)

        # Cache TTL (1 hour)
        self.cache_ttl = 3600

    def detect_schema(self, log: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Check if log matches a known schema

        Process:
        1. Generate schema hash
        2. Check Redis cache
        3. Check database
        4. Return schema info if found, None if new

        Args:
            log: Log dictionary to check

        Returns:
            Schema info dict if known, None if new
        """
        try:
            # Generate hash
            schema_hash = generate_schema_hash(log)

            # Check Redis cache first
            cache_key = f"schema:{schema_hash}"
            cached = self.redis.get(cache_key)

            if cached:
                self.logger.debug(f"Schema cache HIT: {schema_hash[:16]}...")
                return json.loads(cached)

            # Check database
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT
                    schema_id,
                    schema_name,
                    entity_mapping,
                    vendor,
                    product,
                    log_type,
                    mapping_confidence
                FROM log_schemas
                WHERE schema_hash = %s
            """, (schema_hash,))

            result = cursor.fetchone()
            cursor.close()

            if result:
                self.logger.info(f"Schema database HIT: {result[1]} ({schema_hash[:16]}...)")

                # Build schema info
                schema_info = {
                    'schema_id': str(result[0]),
                    'schema_name': result[1],
                    'entity_mapping': result[2] if isinstance(result[2], dict) else json.loads(result[2]),
                    'vendor': result[3],
                    'product': result[4],
                    'log_type': result[5],
                    'confidence': float(result[6]) if result[6] else 0.0,
                    'schema_hash': schema_hash
                }

                # Update last_seen and log_count
                cursor = self.db.cursor()
                cursor.execute("""
                    UPDATE log_schemas
                    SET last_seen = NOW(), log_count = log_count + 1
                    WHERE schema_hash = %s
                """, (schema_hash,))
                self.db.commit()
                cursor.close()

                # Cache for 1 hour
                self.redis.setex(cache_key, self.cache_ttl, json.dumps(schema_info))

                return schema_info

            # Not found - this is a NEW schema
            self.logger.info(f"NEW schema detected: {schema_hash[:16]}...")
            return None

        except Exception as e:
            self.logger.error(f"Error detecting schema: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None

    def register_new_schema(self, log: Dict[str, Any], entity_mapping: Dict[str, str],
                           mapping_confidence: float, generated_by: str = 'ai',
                           mapping_model: str = None) -> str:
        """
        Register a new schema in the database

        Args:
            log: Sample log
            entity_mapping: Generated entity extraction mappings
            mapping_confidence: Confidence score (0.0-1.0)
            generated_by: 'ai', 'manual', or 'hybrid'
            mapping_model: AI model used (if ai-generated)

        Returns:
            schema_id (UUID)
        """
        try:
            # Generate hash and field structure
            schema_hash = generate_schema_hash(log)
            field_paths = sorted(set(extract_all_field_paths(log)))

            # Detect vendor
            vendor_info = detect_vendor_from_log(log)

            # Generate schema name
            schema_name = f"{vendor_info['log_type']}_{vendor_info['vendor'].lower().replace(' ', '_')}"

            # Insert into database
            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO log_schemas (
                    schema_hash, schema_name, field_structure, sample_log,
                    vendor, product, log_type,
                    entity_mapping, mapping_generated_by, mapping_confidence, mapping_model
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING schema_id
            """, (
                schema_hash,
                schema_name,
                json.dumps(field_paths),
                json.dumps(log),
                vendor_info['vendor'],
                vendor_info['product'],
                vendor_info['log_type'],
                json.dumps(entity_mapping),
                generated_by,
                mapping_confidence,
                mapping_model
            ))

            schema_id = cursor.fetchone()[0]
            self.db.commit()
            cursor.close()

            self.logger.info(f"Registered new schema: {schema_name} ({schema_hash[:16]}...)")

            # Cache immediately
            cache_key = f"schema:{schema_hash}"
            schema_info = {
                'schema_id': str(schema_id),
                'schema_name': schema_name,
                'entity_mapping': entity_mapping,
                'vendor': vendor_info['vendor'],
                'product': vendor_info['product'],
                'log_type': vendor_info['log_type'],
                'confidence': mapping_confidence,
                'schema_hash': schema_hash
            }
            self.redis.setex(cache_key, self.cache_ttl, json.dumps(schema_info))

            return str(schema_id)

        except Exception as e:
            self.logger.error(f"Failed to register new schema: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            raise
