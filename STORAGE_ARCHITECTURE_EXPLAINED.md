# SIEMLess Storage Architecture - Database vs Elastic

**Question**: Are the 6.95 BILLION vendor logs stored in SIEMLess database?

**Answer**: No - and that's by design! Here's the complete architecture.

---

## Two-Tier Storage Architecture

### Tier 1: Elastic Cloud (External) - RAW LOG ARCHIVE
**Purpose**: Complete historical log archive

| Metric | Value |
|--------|-------|
| **Total Events** | 6,950,000,000+ (6.95 BILLION) |
| **Vendors** | 7 security vendors |
| **Storage Type** | Raw logs (ECS-normalized JSON) |
| **Retention** | Months to years |
| **Query Method** | Elasticsearch API (on-demand) |

**Vendors in Elastic**:
1. Fortinet FortiGate: 5.7B events
2. Palo Alto PAN-OS: 1B+ events
3. TippingPoint IPS: 578M events
4. CrowdStrike Falcon: 42M events
5. Elastic Endpoint: 3.1M events
6. ThreatLocker: 211K events
7. Microsoft Defender: 2K events

### Tier 2: PostgreSQL (Local) - INTELLIGENCE LAYER
**Purpose**: Extracted intelligence for fast correlation

| Table | Records | Purpose |
|-------|---------|---------|
| **ingestion_logs** | 45,832 | Sample logs from initial testing |
| **warm_storage** | 56,119 | Temporary query results |
| **entities** | 34 | Extracted entities (IPs, hosts, users) |
| **relationships** | 12 | Entity-to-entity mappings |
| **detection_rules** | 0 | (Ready for rules) |
| **cases** | 0 | (Ready for investigations) |

**Logs in Database (from testing)**:
```
source_type  | count
-------------+-------
database     | 16,100  (test data)
crowdstrike  | 10,300  (from CrowdStrike API tests)
fortinet     |  9,991  (from Elastic query tests)
elasticsearch|  6,741  (from Elastic query tests)
palo_alto    |  2,700  (from Elastic query tests)
TOTAL        | 45,832  (0.0007% of Elastic data)
```

---

## Why This Architecture?

### The Lightweight Approach

**Problem with Traditional SIEMs**:
```
Store 6.95B raw logs → 10+ TB of storage → Slow queries → High cost
```

**SIEMLess Approach**:
```
1. Leave logs in Elastic (already there, already indexed)
2. Query on-demand (Elastic Plugin)
3. Extract intelligence (Contextualization)
4. Store only relationships (PostgreSQL)
Result: 99%+ storage savings, faster queries
```

### Example: IP Investigation

**Traditional SIEM**:
1. Store 6.95B logs in database (10 TB)
2. Query: "Find all events for *************"
3. Scan 6.95B records → 5+ minutes
4. Store: 10 TB × $0.10/GB = $1,000/month

**SIEMLess**:
1. Logs stay in Elastic (already there)
2. Query Elastic: "Find IP *************" → 2 seconds
3. Extract: 50 entities, 100 relationships
4. Store: 150 rows × 1KB = 150 KB in PostgreSQL
5. Future queries: Check relationships (instant)
6. Storage cost: ~$1/month for PostgreSQL

**Savings**: 99.9% cost reduction, 99%+ faster repeat queries

---

## Data Flow Architecture

```
┌──────────────────────────────────────────────────────────────┐
│  ELASTIC CLOUD (External)                                     │
│  ┌──────────────────────────────────────────────────────┐   │
│  │ TippingPoint:   578M events (IPS signatures)          │   │
│  │ ThreatLocker:   211K events (app control)             │   │
│  │ Fortinet:       5.7B events (firewall)                │   │
│  │ Palo Alto:      1B+ events (next-gen FW)              │   │
│  │ CrowdStrike:    42M events (EDR)                      │   │
│  │ Others:         3M+ events                            │   │
│  │                                                        │   │
│  │ TOTAL: 6.95 BILLION EVENTS                            │   │
│  └──────────────────────────────────────────────────────┘   │
└───────────────────────────┬──────────────────────────────────┘
                            │
                            │ Query on-demand
                            │ (Elastic Plugin)
                            ▼
┌──────────────────────────────────────────────────────────────┐
│  SIEMLESS INGESTION ENGINE                                    │
│  ┌─────────────────────────────────────┐                     │
│  │ Context Plugins:                     │                     │
│  │  - Elastic Plugin (queries all 7)    │                     │
│  │  - CrowdStrike Plugin (direct API)   │                     │
│  │  - TippingPoint Plugin (via Elastic) │                     │
│  │  - ThreatLocker Plugin (via Elastic) │                     │
│  └─────────────────────────────────────┘                     │
└───────────────────────────┬──────────────────────────────────┘
                            │
                            │ Send logs
                            ▼
┌──────────────────────────────────────────────────────────────┐
│  CONTEXTUALIZATION ENGINE                                     │
│  ┌─────────────────────────────────────┐                     │
│  │ Extract Intelligence:                │                     │
│  │  - IPs: *************, ***********  │                     │
│  │  - Hosts: WS-01, SERVER-02          │                     │
│  │  - Users: admin, john.doe            │                     │
│  │  - Processes: powershell.exe         │                     │
│  │                                      │                     │
│  │ Create Relationships:                │                     │
│  │  - admin --[logged_into]--> WS-01   │                     │
│  │  - WS-01 --[connected_to]--> *******│                     │
│  │  - powershell --[spawned_by]--> ... │                     │
│  └─────────────────────────────────────┘                     │
└───────────────────────────┬──────────────────────────────────┘
                            │
                            │ Store intelligence only
                            ▼
┌──────────────────────────────────────────────────────────────┐
│  POSTGRESQL DATABASE (Local)                                  │
│  ┌────────────────────────────────────────────────────────┐ │
│  │ entities: 34 rows (IPs, hosts, users, processes)       │ │
│  │  - entity_type: ip_address                             │ │
│  │  - entity_value: *************                         │ │
│  │  - confidence: 0.95                                    │ │
│  │  - properties: {source: "elastic", vendor: "fortinet"} │ │
│  │                                                         │ │
│  │ relationships: 12 rows (connections between entities)  │ │
│  │  - source: admin (user)                                │ │
│  │  - target: WS-01 (host)                                │ │
│  │  - type: logged_into                                   │ │
│  │  - confidence: 0.90                                    │ │
│  │                                                         │ │
│  │ warm_storage: 56K rows (recent query results cache)   │ │
│  │ ingestion_logs: 45K rows (test samples)               │ │
│  └────────────────────────────────────────────────────────┘ │
└──────────────────────────────────────────────────────────────┘
```

---

## What's Actually in the Database?

### ingestion_logs (45,832 rows)

**Purpose**: Testing and initial ingestion samples

**Source Breakdown**:
- `database`: 16,100 logs (test data)
- `crowdstrike`: 10,300 logs (CrowdStrike API test)
- `fortinet`: 9,991 logs (Elastic query test)
- `elasticsearch`: 6,741 logs (Elastic query test)
- `palo_alto`: 2,700 logs (Elastic query test)

**Date Range**: Sept 27-28, 2025 (2 days of testing)

**Important**: These are **sample logs from testing**, not production log storage

### warm_storage (56,119 rows)

**Purpose**: Temporary cache for recent investigation queries

**Schema**:
```sql
storage_id | uuid
data       | jsonb (query results)
created_at | timestamp
expires_at | timestamp (auto-cleanup)
```

**Use Case**:
- Analyst queries Elastic for IP *************
- Results cached here for 24 hours
- Next query for same IP = instant (from cache)
- After 24 hours = expires, query Elastic again

### entities (34 rows)

**Purpose**: Intelligence layer - what we know

**Current Data**:
```
ip_address:  18 (unique IPs seen)
hostname:     7 (unique hosts)
username:     4 (unique users)
process:      2 (unique processes)
ip:           1 (legacy format)
mac:          1 (MAC address)
os:           1 (OS version)
```

**Example Entity**:
```json
{
  "entity_id": "uuid",
  "entity_type": "hostname",
  "entity_value": "010117039050LN1",
  "properties": {
    "source": "crowdstrike",
    "ip": "*************",
    "mac": "58-8a-5a-13-4d-13",
    "os": "Windows 10"
  },
  "confidence": 1.0,
  "first_seen": "2025-10-02 13:16:43",
  "last_seen": "2025-10-02 13:16:43"
}
```

### relationships (12 rows)

**Purpose**: How entities connect

**Examples**:
```
user: admin --[logged_into]--> host: WS-01
host: WS-01 --[has_ip]--> ip: *************
process: powershell.exe --[spawned_by]--> user: admin
```

---

## TippingPoint & ThreatLocker in Database?

**Short Answer**: NO - and here's why

### Current State:

| Vendor | In Elastic? | In Database? | Why? |
|--------|-------------|--------------|------|
| TippingPoint | ✅ 578M events | ❌ Not yet | Haven't queried yet |
| ThreatLocker | ✅ 211K events | ❌ Not yet | Haven't queried yet |
| Fortinet | ✅ 5.7B events | ✅ 9,991 samples | From test queries |
| CrowdStrike | ✅ 42M events | ✅ 10,300 samples | From API + Elastic tests |
| Palo Alto | ✅ 1B+ events | ✅ 2,700 samples | From test queries |

### To Get TippingPoint/ThreatLocker Data INTO Database:

```python
# Step 1: Query Elastic for TippingPoint data
elastic_plugin.query({
    'index': 'logs-tippingpoint-*',
    'query_type': 'ip',
    'query_value': '*************'
})

# Step 2: Contextualization extracts entities
# - Source IP
# - Destination IP
# - IPS signature name
# - Threat severity

# Step 3: Entities stored in PostgreSQL
# - entity: ************* (ip_address)
# - properties: {vendor: "tippingpoint", rule: "SQL_Injection_Attempt"}

# Step 4: Relationships created
# - ************* --[triggered_ips_rule]--> SQL_Injection_Attempt
```

**After this flow**:
- Elastic still has 578M TippingPoint events (unchanged)
- PostgreSQL now has 2-3 entities per query
- Relationships map attack patterns

---

## Storage Efficiency Comparison

### Scenario: Store 1 Week of Data

#### Traditional Approach:
```
Fortinet logs (1 week):  ~1.2 billion events
Size per event:          ~2 KB average
Total storage:           2.4 TB
Database cost:           $240/month
Query time (find IP):    3-5 minutes
```

#### SIEMLess Approach:
```
Elastic (already there):  1.2 billion events
Queries performed:        100 investigations
Entities extracted:       5,000 unique
Relationships created:    10,000 mappings
PostgreSQL storage:       15 MB
Database cost:            $1/month
Query time (find IP):
  - First query:          2 seconds (Elastic)
  - Repeat query:         <100ms (PostgreSQL relationships)
```

**Efficiency Gains**:
- Storage: 99.99% reduction (2.4 TB → 15 MB)
- Cost: 99.6% reduction ($240 → $1)
- Speed: 99.9% faster on repeat queries (5 min → 100ms)

---

## Best Practices

### When to Use Each Layer

#### Use Elastic Cloud For:
- ✅ Historical log searches
- ✅ First-time investigations
- ✅ Compliance/audit log retention
- ✅ Full-text search across logs
- ✅ Vendor-specific field queries

#### Use PostgreSQL For:
- ✅ Entity relationship mapping
- ✅ Repeat investigations (cached)
- ✅ Cross-vendor correlation
- ✅ Investigation case tracking
- ✅ Real-time graph queries

---

## Summary

**Are logs in the database?**
- ❌ **NO**: 6.95B vendor logs stay in Elastic Cloud
- ✅ **YES**: 45K sample logs from testing
- ✅ **YES**: 34 entities + 12 relationships (intelligence layer)
- ✅ **YES**: 56K warm cache entries (temporary)

**Why this architecture?**
- 99.99% storage savings
- Faster queries (cache + relationships)
- Lower costs
- Scalable to trillions of events

**How to get vendor data into intelligence layer?**
1. Query Elastic via context plugins
2. Extract entities via contextualization
3. Create relationships
4. Store in PostgreSQL (intelligence only)

**The vendors are ALL in Elastic** - TippingPoint (578M), ThreatLocker (211K), Fortinet (5.7B), Palo Alto (1B+), CrowdStrike (42M), etc.

**The intelligence is in PostgreSQL** - Entities, relationships, and investigation context extracted from those logs.

This is the "learn expensive once, operate free forever" architecture - query the massive log archive once, extract intelligence, then work with lightweight intelligence layer for fast investigations!

