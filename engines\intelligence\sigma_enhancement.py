"""
Sigma Rule Enhancement Module
Uses AI to enhance harvested detection rules with platform-specific optimizations,
evasion variants, false positive filters, and missing context
"""

import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import sys
import os

# Import directly (container has files in same directory)
from ai_models import AIModelManager


class SigmaRuleEnhancer:
    """
    Enhances Sigma detection rules using AI analysis

    Supports tiered AI models for cost optimization:
    - Gemma (free): Basic enhancement
    - Gemini Flash (low cost): Good enhancement
    - Claude <PERSON> (mid quality): Best enhancement
    """

    def __init__(self, ai_model_manager: AIModelManager, logger: Optional[logging.Logger] = None):
        self.ai_manager = ai_model_manager
        self.logger = logger or logging.getLogger(__name__)

    async def enhance_sigma_rule(
        self,
        sigma_rule: Dict[str, Any],
        source_platform: str,
        target_platforms: List[str],
        ai_tier: str = 'free',  # Start with free tier
        enhancement_types: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Enhance a Sigma rule using AI analysis

        Args:
            sigma_rule: Sigma rule in dictionary format
            source_platform: Source SIEM (e.g., 'elastic')
            target_platforms: Target SIEMs (e.g., ['wazuh', 'splunk'])
            ai_tier: 'free' (gemma), 'low_cost' (gemini-flash), 'high_quality' (sonnet)
            enhancement_types: Types of enhancements to apply

        Returns:
            Enhanced rule with AI suggestions
        """
        if enhancement_types is None:
            enhancement_types = [
                'evasion_variants',
                'false_positive_filters',
                'platform_optimizations',
                'missing_context'
            ]

        self.logger.info(f"Enhancing Sigma rule '{sigma_rule.get('title')}' with AI tier: {ai_tier}")

        # Build enhancement prompt
        prompt = self._build_enhancement_prompt(
            sigma_rule,
            source_platform,
            target_platforms,
            enhancement_types
        )

        # Call AI model
        ai_response = await self.ai_manager.call_ai_model(
            model_tier=ai_tier,
            data={'prompt': prompt, 'task': 'sigma_enhancement'}
        )

        # Parse AI response into structured enhancements
        enhancements = self._parse_enhancement_response(ai_response)

        # Add metadata
        enhancements['metadata'] = {
            'enhanced_at': datetime.utcnow().isoformat(),
            'ai_tier': ai_tier,
            'ai_model': self.ai_manager.get_model_info(ai_tier)['name'],
            'source_platform': source_platform,
            'target_platforms': target_platforms
        }

        return enhancements

    def _build_enhancement_prompt(
        self,
        sigma_rule: Dict[str, Any],
        source_platform: str,
        target_platforms: List[str],
        enhancement_types: List[str]
    ) -> str:
        """Build detailed prompt for AI enhancement"""

        rule_title = sigma_rule.get('title', 'Unnamed Rule')
        rule_description = sigma_rule.get('description', '')
        detection = sigma_rule.get('detection', {})
        tags = sigma_rule.get('tags', [])
        level = sigma_rule.get('level', 'medium')

        prompt = f"""# Security Detection Rule Enhancement Task

## Rule to Enhance
**Title**: {rule_title}
**Description**: {rule_description}
**Severity**: {level}
**Tags**: {', '.join(tags)}

**Detection Logic**:
```yaml
{json.dumps(detection, indent=2)}
```

## Source Platform
Harvested from: **{source_platform}**

## Target Platforms
Will be deployed to: **{', '.join(target_platforms)}**

## Enhancement Requirements

Please analyze this detection rule and provide enhancements in the following areas:

"""

        if 'evasion_variants' in enhancement_types:
            prompt += """
### 1. Evasion Variants
Identify known techniques attackers use to evade this detection:
- Alternative file names/paths
- Obfuscation techniques
- Living-off-the-land binaries (LOLBins)
- Process injection variants
- Encoding/encryption bypasses

Provide additional detection logic to catch these variants.
"""

        if 'false_positive_filters' in enhancement_types:
            prompt += """
### 2. False Positive Filters
Identify legitimate activities that might trigger this rule:
- Common administrative tools
- Software installers/updaters
- Developer tools
- Automated scripts
- Enterprise applications

Provide filters to exclude these false positives.
"""

        if 'platform_optimizations' in enhancement_types:
            prompt += f"""
### 3. Platform-Specific Optimizations
For each target platform ({', '.join(target_platforms)}), suggest:
- Field name optimizations
- Index/sourcetype filtering
- Query performance improvements
- Platform-specific features to leverage

Consider each platform's strengths and query syntax.
"""

        if 'missing_context' in enhancement_types:
            prompt += """
### 4. Missing Context
Identify additional indicators that would strengthen this detection:
- Related process activity
- Network connections
- File modifications
- Registry changes
- User context

Suggest additional fields/conditions to capture.
"""

        prompt += """

## Response Format

Provide your response in the following JSON structure:

```json
{
  "evasion_variants": [
    {
      "technique": "Name of evasion technique",
      "description": "How attackers use this",
      "detection_addition": "Additional Sigma detection logic",
      "confidence": 0.85
    }
  ],
  "false_positive_filters": [
    {
      "source": "Legitimate source of FP",
      "filter_logic": "Sigma filter logic to exclude",
      "reason": "Why this is a false positive"
    }
  ],
  "platform_optimizations": {
    "wazuh": [
      {
        "optimization": "Description",
        "field_changes": {"old_field": "new_field"},
        "query_improvement": "Optimized query syntax"
      }
    ],
    "splunk": [...]
  },
  "missing_context": [
    {
      "indicator": "Additional indicator to monitor",
      "field_name": "Field to add",
      "reasoning": "Why this adds value"
    }
  ],
  "overall_assessment": {
    "original_quality": 0.75,
    "enhanced_quality": 0.90,
    "improvement_summary": "Brief summary of improvements"
  }
}
```

Focus on **practical, deployable** improvements. Avoid theoretical suggestions that can't be implemented.
"""

        return prompt

    def _parse_enhancement_response(self, ai_response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse AI response into structured enhancements"""

        try:
            # Extract response text (ai_models.py returns 'result' not 'response')
            response_text = ai_response.get('result', ai_response.get('response', ''))

            # Try to parse JSON from response
            # AI might wrap in markdown code blocks
            if '```json' in response_text:
                json_start = response_text.find('```json') + 7
                json_end = response_text.find('```', json_start)
                response_text = response_text[json_start:json_end].strip()
            elif '```' in response_text:
                json_start = response_text.find('```') + 3
                json_end = response_text.find('```', json_start)
                response_text = response_text[json_start:json_end].strip()

            enhancements = json.loads(response_text)

            # Validate structure
            if not isinstance(enhancements, dict):
                raise ValueError("Response is not a dictionary")

            return enhancements

        except Exception as e:
            self.logger.error(f"Failed to parse AI enhancement response: {e}")
            self.logger.error(f"Raw AI response (first 500 chars): {response_text[:500] if response_text else 'empty'}")

            # Return minimal structure on parse failure
            return {
                'evasion_variants': [],
                'false_positive_filters': [],
                'platform_optimizations': {},
                'missing_context': [],
                'overall_assessment': {
                    'original_quality': 0.5,
                    'enhanced_quality': 0.5,
                    'improvement_summary': 'AI enhancement parsing failed'
                },
                'parse_error': str(e),
                'raw_response': ai_response.get('result', ai_response.get('response', ''))[:500]
            }

    async def compare_ai_tiers(
        self,
        sigma_rule: Dict[str, Any],
        source_platform: str,
        target_platforms: List[str]
    ) -> Dict[str, Any]:
        """
        Test all three AI tiers and compare results

        Used to determine minimum viable AI tier for production
        """
        self.logger.info(f"Comparing AI tiers for rule '{sigma_rule.get('title')}'")

        tiers = ['free', 'low_cost', 'high_quality']
        results = {}

        for tier in tiers:
            try:
                self.logger.info(f"Testing tier: {tier}")

                enhancement = await self.enhance_sigma_rule(
                    sigma_rule=sigma_rule,
                    source_platform=source_platform,
                    target_platforms=target_platforms,
                    ai_tier=tier
                )

                results[tier] = {
                    'success': True,
                    'enhancement': enhancement,
                    'model': self.ai_manager.get_model_info(tier)['name'],
                    'cost': self.ai_manager.get_model_info(tier)['cost_per_request']
                }

            except Exception as e:
                self.logger.error(f"Tier {tier} failed: {e}")
                results[tier] = {
                    'success': False,
                    'error': str(e),
                    'model': self.ai_manager.get_model_info(tier)['name']
                }

        # Generate comparison report
        comparison = self._generate_comparison_report(results)

        return comparison

    def _generate_comparison_report(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comparison report between AI tiers"""

        comparison = {
            'timestamp': datetime.utcnow().isoformat(),
            'tiers_tested': list(results.keys()),
            'results': results,
            'recommendations': []
        }

        # Analyze success rates
        successful_tiers = [tier for tier, data in results.items() if data.get('success')]

        if not successful_tiers:
            comparison['recommendations'].append({
                'tier': 'none',
                'reason': 'All AI tiers failed - check API keys and connectivity'
            })
            return comparison

        # Compare enhancement counts
        for tier in successful_tiers:
            enhancement = results[tier]['enhancement']

            evasion_count = len(enhancement.get('evasion_variants', []))
            fp_count = len(enhancement.get('false_positive_filters', []))
            context_count = len(enhancement.get('missing_context', []))

            quality = enhancement.get('overall_assessment', {}).get('enhanced_quality', 0)
            cost = results[tier]['cost']

            results[tier]['metrics'] = {
                'evasion_variants': evasion_count,
                'false_positive_filters': fp_count,
                'missing_context': context_count,
                'quality_score': quality,
                'cost_per_request': cost,
                'value_score': (quality / (cost + 0.001))  # Avoid division by zero
            }

        # Determine best tier
        best_value = max(
            successful_tiers,
            key=lambda t: results[t]['metrics']['value_score']
        )

        best_quality = max(
            successful_tiers,
            key=lambda t: results[t]['metrics']['quality_score']
        )

        comparison['recommendations'].append({
            'tier': best_value,
            'reason': f"Best value: {results[best_value]['metrics']['value_score']:.2f} quality/cost",
            'model': results[best_value]['model']
        })

        if best_quality != best_value:
            comparison['recommendations'].append({
                'tier': best_quality,
                'reason': f"Highest quality: {results[best_quality]['metrics']['quality_score']:.2f}",
                'model': results[best_quality]['model'],
                'note': 'Use for critical detections only'
            })

        return comparison


# Testing function
async def test_sigma_enhancement():
    """Test Sigma enhancement with sample rule"""
    import asyncio

    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # Sample Sigma rule (PowerShell execution)
    sample_rule = {
        'title': 'Suspicious PowerShell Execution',
        'description': 'Detects suspicious PowerShell command execution with encoded commands',
        'tags': ['attack.t1059.001', 'attack.execution'],
        'level': 'high',
        'logsource': {
            'product': 'windows',
            'service': 'powershell'
        },
        'detection': {
            'selection': {
                'Image': 'powershell.exe',
                'CommandLine|contains': '-enc'
            },
            'condition': 'selection'
        }
    }

    # Initialize
    ai_manager = AIModelManager(logger)
    enhancer = SigmaRuleEnhancer(ai_manager, logger)

    print("\n" + "="*80)
    print("SIGMA RULE ENHANCEMENT - AI TIER COMPARISON")
    print("="*80)

    # Compare all AI tiers
    comparison = await enhancer.compare_ai_tiers(
        sigma_rule=sample_rule,
        source_platform='elastic',
        target_platforms=['wazuh', 'splunk', 'sentinel']
    )

    # Print results
    print(f"\n📊 Comparison Results:")
    print(json.dumps(comparison, indent=2))

    print("\n" + "="*80)
    print("RECOMMENDATIONS")
    print("="*80)

    for rec in comparison['recommendations']:
        print(f"\n✅ {rec['tier'].upper()}: {rec['reason']}")
        print(f"   Model: {rec['model']}")
        if 'note' in rec:
            print(f"   Note: {rec['note']}")


if __name__ == "__main__":
    asyncio.run(test_sigma_enhancement())
