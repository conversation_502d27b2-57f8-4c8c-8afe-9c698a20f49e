import React, { useState, useMemo, useCallback, useEffect } from 'react'
import { AgGridReact } from 'ag-grid-react'
import { ColDef, GridApi, GridReadyEvent, ICellRendererParams } from 'ag-grid-community'
import 'ag-grid-community/styles/ag-grid.css'
import 'ag-grid-community/styles/ag-theme-alpine.css'
import { Shield, AlertTriangle, Info, AlertCircle } from 'lucide-react'
import { useAlerts } from '../hooks/useAlerts'
import { Alert as AlertType } from '../api/alerts'

interface Alert {
  id: string
  timestamp: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  source: string
  description: string
  entities: string[]
  status: 'new' | 'investigating' | 'resolved'
  assignee?: string
  confidence: number
}

// Severity renderer component
const SeverityRenderer: React.FC<ICellRendererParams> = ({ value }) => {
  const severityConfig = {
    critical: { icon: AlertCircle, color: 'text-red-600', bg: 'bg-red-100' },
    high: { icon: Alert<PERSON>riangle, color: 'text-orange-600', bg: 'bg-orange-100' },
    medium: { icon: Shield, color: 'text-yellow-600', bg: 'bg-yellow-100' },
    low: { icon: Info, color: 'text-blue-600', bg: 'bg-blue-100' }
  }

  const config = severityConfig[value as keyof typeof severityConfig]
  const Icon = config.icon

  return (
    <div className={`flex items-center gap-2 px-2 py-1 rounded ${config.bg} ${config.color}`}>
      <Icon size={16} />
      <span className="capitalize font-medium">{value}</span>
    </div>
  )
}

// Status renderer component
const StatusRenderer: React.FC<ICellRendererParams> = ({ value }) => {
  const statusConfig = {
    new: 'bg-blue-100 text-blue-800',
    investigating: 'bg-yellow-100 text-yellow-800',
    resolved: 'bg-green-100 text-green-800'
  }

  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusConfig[value as keyof typeof statusConfig]}`}>
      {value}
    </span>
  )
}

// Confidence renderer component
const ConfidenceRenderer: React.FC<ICellRendererParams> = ({ value }) => {
  const percentage = Math.round(value * 100)
  const color = percentage > 80 ? 'bg-green-500' : percentage > 50 ? 'bg-yellow-500' : 'bg-red-500'

  return (
    <div className="flex items-center gap-2">
      <div className="flex-1 bg-gray-200 rounded-full h-2">
        <div
          className={`${color} h-2 rounded-full transition-all duration-300`}
          style={{ width: `${percentage}%` }}
        />
      </div>
      <span className="text-xs font-medium">{percentage}%</span>
    </div>
  )
}

interface AlertQueueProps {
  refreshInterval?: number
  onAlertSelect?: (alert: Alert) => void
}

export const AlertQueue: React.FC<AlertQueueProps> = ({
  refreshInterval = 5000,
  onAlertSelect
}) => {
  // Fetch alerts using React Query hook
  const { data: alertsData, isLoading, error } = useAlerts({ status: 'open', limit: 50 })

  const [rowData, setRowData] = useState<Alert[]>([])
  const [gridApi, setGridApi] = useState<GridApi | null>(null)
  const [selectedRows, setSelectedRows] = useState<Alert[]>([])

  // Column definitions
  const columnDefs: ColDef[] = useMemo(() => [
    {
      field: 'severity',
      headerName: 'Severity',
      width: 120,
      cellRenderer: SeverityRenderer,
      sortable: true,
      filter: true
    },
    {
      field: 'timestamp',
      headerName: 'Time',
      width: 180,
      sortable: true,
      sort: 'desc',
      valueFormatter: (params) => {
        return new Date(params.value).toLocaleString()
      }
    },
    {
      field: 'source',
      headerName: 'Source',
      width: 150,
      sortable: true,
      filter: true
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 1,
      minWidth: 300,
      tooltipField: 'description'
    },
    {
      field: 'entities',
      headerName: 'Entities',
      width: 200,
      valueFormatter: (params) => {
        return params.value?.join(', ') || ''
      },
      tooltipValueGetter: (params) => params.value?.join('\n') || ''
    },
    {
      field: 'confidence',
      headerName: 'Confidence',
      width: 150,
      cellRenderer: ConfidenceRenderer
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      cellRenderer: StatusRenderer,
      filter: true
    },
    {
      field: 'assignee',
      headerName: 'Assignee',
      width: 120,
      filter: true
    }
  ], [])

  // Default column properties
  const defaultColDef = useMemo(() => ({
    resizable: true,
    sortable: true,
    filter: false,
    tooltipComponent: 'agTooltipComponent'
  }), [])

  // Mock data generator
  const generateMockData = (): Alert[] => {
    const severities: Alert['severity'][] = ['critical', 'high', 'medium', 'low']
    const sources = ['CrowdStrike', 'PaloAlto', 'Fortinet', 'Windows', 'Linux']
    const statuses: Alert['status'][] = ['new', 'investigating', 'resolved']
    const assignees = ['John Doe', 'Jane Smith', 'Bob Johnson', undefined]

    return Array.from({ length: 20 }, (_, i) => ({
      id: `alert-${Date.now()}-${i}`,
      timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString(),
      severity: severities[Math.floor(Math.random() * severities.length)],
      source: sources[Math.floor(Math.random() * sources.length)],
      description: `Suspicious activity detected: ${Math.random() > 0.5 ? 'Multiple failed login attempts' : 'Unusual network traffic pattern'}`,
      entities: [`IP:192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
                 `User:user${Math.floor(Math.random() * 100)}`],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      assignee: assignees[Math.floor(Math.random() * assignees.length)],
      confidence: Math.random()
    }))
  }

  // Update row data when alerts are fetched
  useEffect(() => {
    if (alertsData?.alerts && alertsData.alerts.length > 0) {
      // Map API alerts to grid format
      const mappedAlerts: Alert[] = alertsData.alerts.map((alert: any) => ({
        id: alert.alert_id,
        timestamp: alert.timestamp,
        severity: alert.severity || 'medium',
        source: alert.type || 'Unknown',
        description: alert.message || alert.title || 'No description',
        entities: [
          ...(alert.entities?.ips || []),
          ...(alert.entities?.users || []),
          ...(alert.entities?.hosts || [])
        ],
        status: alert.status === 'open' ? 'new' : alert.status,
        assignee: undefined,
        confidence: 0.8 // Default confidence since API doesn't provide it yet
      }))
      setRowData(mappedAlerts)
    } else {
      // Show empty state when no alerts
      setRowData([])
    }
  }, [alertsData, isLoading])

  // Grid ready handler
  const onGridReady = useCallback((params: GridReadyEvent) => {
    setGridApi(params.api)
    params.api.sizeColumnsToFit()
  }, [])

  // Selection handler
  const onSelectionChanged = useCallback(() => {
    if (gridApi) {
      const selected = gridApi.getSelectedRows()
      setSelectedRows(selected)
      if (selected.length > 0 && onAlertSelect) {
        onAlertSelect(selected[0])
      }
    }
  }, [gridApi, onAlertSelect])

  // Quick filter
  const [quickFilter, setQuickFilter] = useState('')

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="flex justify-between items-center p-4 border-b">
        <h2 className="text-lg font-semibold">
          Alert Queue
          {isLoading && <span className="ml-2 text-sm text-gray-500">(Loading...)</span>}
          {error && <span className="ml-2 text-sm text-red-500">(Error loading alerts)</span>}
        </h2>
        <div className="flex items-center gap-4">
          <input
            type="text"
            placeholder="Quick filter..."
            className="px-3 py-1 border rounded-md text-sm"
            value={quickFilter}
            onChange={(e) => setQuickFilter(e.target.value)}
          />
          <span className="text-sm text-gray-600">
            {rowData.length} alerts | {selectedRows.length} selected
          </span>
        </div>
      </div>

      {/* AG-Grid */}
      <div className="flex-1 ag-theme-alpine">
        <AgGridReact
          rowData={rowData}
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          onGridReady={onGridReady}
          onSelectionChanged={onSelectionChanged}
          rowSelection="multiple"
          animateRows={true}
          quickFilterText={quickFilter}
          pagination={true}
          paginationPageSize={20}
          enableCellTextSelection={true}
          suppressRowClickSelection={false}
          rowHeight={40}
        />
      </div>
    </div>
  )
}

export default AlertQueue