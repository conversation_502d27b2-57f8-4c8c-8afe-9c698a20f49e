@echo off
REM SIEMLess Keycloak Setup Script for Windows
REM Sets up local authentication with Keycloak

echo ======================================
echo SIEMLess Keycloak Setup
echo ======================================

REM Check if Docker is running
docker info >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

REM Create .env file if it doesn't exist
if not exist .env (
    echo Creating .env file...
    (
        echo # Database
        echo POSTGRES_HOST=localhost
        echo POSTGRES_PORT=5433
        echo POSTGRES_DB=siemless_v2
        echo POSTGRES_USER=siemless
        echo POSTGRES_PASSWORD=siemless123
        echo.
        echo # Redis
        echo REDIS_HOST=localhost
        echo REDIS_PORT=6380
        echo REDIS_PASSWORD=redis123
        echo REDIS_SESSION_PASSWORD=session123
        echo.
        echo # Keycloak
        echo KEYCLOAK_ADMIN=admin
        echo KEYCLOAK_ADMIN_PASSWORD=admin123
        echo KEYCLOAK_HOSTNAME=localhost
        echo KEYCLOAK_PORT=8080
        echo.
        echo # Development
        echo ENABLE_DEV_API_KEYS=true
    ) > .env
    echo Created .env file
)

REM Step 1: Start PostgreSQL if not running
echo.
echo Step 1: Checking PostgreSQL...
docker ps | findstr siemless-postgres >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Starting PostgreSQL...
    cd engines
    docker-compose up -d postgres
    cd ..
    timeout /t 10 /nobreak >nul
)

REM Step 2: Create Keycloak database
echo.
echo Step 2: Creating Keycloak database...
cd engines
echo CREATE DATABASE keycloak; | docker-compose exec -T postgres psql -U siemless -d postgres
cd ..
echo Keycloak database created or already exists

REM Step 3: Start Keycloak
echo.
echo Step 3: Starting Keycloak...
cd engines
docker-compose -f docker-compose.keycloak.yml up -d keycloak
cd ..
echo Waiting for Keycloak to start (60 seconds)...
timeout /t 60 /nobreak >nul

REM Step 4: Check Keycloak health
echo.
echo Step 4: Checking Keycloak health...
for /L %%i in (1,1,10) do (
    curl -s http://localhost:8080/health/ready | findstr "UP" >nul 2>&1
    if !ERRORLEVEL! EQU 0 (
        echo Keycloak is ready!
        goto :keycloak_ready
    ) else (
        echo Waiting for Keycloak... (attempt %%i/10)
        timeout /t 5 /nobreak >nul
    )
)

:keycloak_ready

REM Step 5: Start Redis session store
echo.
echo Step 5: Starting Redis session store...
cd engines
docker-compose -f docker-compose.keycloak.yml up -d redis-sessions
cd ..
echo Redis session store started

REM Step 6: Display access information
echo.
echo ======================================
echo Keycloak Setup Complete!
echo ======================================
echo.
echo Access URLs:
echo Keycloak Admin Console: http://localhost:8080/admin
echo Username: admin
echo Password: admin123
echo.
echo Test Users (Local Authentication):
echo -----------------------------------
echo Admin User:
echo   Username: admin
echo   Password: admin123
echo   Role: siemless-admin (full access)
echo.
echo Analyst User:
echo   Username: analyst1
echo   Password: analyst123
echo   Role: siemless-analyst (investigation access)
echo.
echo Engineer User:
echo   Username: engineer1
echo   Password: engineer123
echo   Role: siemless-engineer (pattern management)
echo.
echo Viewer User:
echo   Username: viewer1
echo   Password: viewer123
echo   Role: siemless-viewer (read-only)
echo.
echo Development API Keys:
echo -------------------
echo Admin:    dev-admin-key
echo Analyst:  dev-analyst-key
echo Engineer: dev-engineer-key
echo Viewer:   dev-viewer-key
echo.
echo Next Steps:
echo 1. Access Keycloak admin console
echo 2. Review the 'siemless' realm configuration
echo 3. Test authentication with: test_auth.bat
echo 4. When ready, add Azure AD and Google SSO providers
echo.
pause