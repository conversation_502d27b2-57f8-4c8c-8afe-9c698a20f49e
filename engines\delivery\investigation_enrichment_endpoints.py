"""
Auto-Enrichment Endpoint Methods for Investigation Dashboard
These methods extend InvestigationHTTPHandlers with auto-enrichment capabilities
"""

async def enrich_investigation(self, request):
    """
    POST /api/v1/investigations/{investigation_id}/enrich

    Auto-enrich investigation with historical context

    Returns:
    {
        "investigation_id": "uuid",
        "enrichment_added": {
            "entities": 45,
            "relationships": 123,
            "timeline_events": 89
        },
        "updated_at": "2025-10-02T..."
    }
    """
    try:
        investigation_id = request.match_info['investigation_id']

        if not self.context_manager:
            return self.engine.response({'error': 'Context manager not available'}, status=503)

        # Get investigation details
        investigation = await self.engine.get_investigation_by_id(investigation_id)
        if not investigation:
            return self.engine.response({'error': 'Investigation not found'}, status=404)

        # Extract entities from investigation
        entities = investigation.get('entities', {})
        entity_list = []

        for entity_type, values in entities.items():
            if isinstance(values, list):
                for value in values:
                    entity_list.append({'type': entity_type.rstrip('s'), 'value': value})

        if not entity_list:
            return self.engine.response({
                'investigation_id': investigation_id,
                'enrichment_added': {'entities': 0, 'relationships': 0, 'timeline_events': 0},
                'message': 'No entities to enrich'
            })

        # Get multi-entity historical context
        context = await self.context_manager.get_multi_entity_context(entity_list, hours_back=168)  # 7 days

        # Update investigation with enriched data
        enrichment_data = {
            'historical_context': context,
            'enriched_at': context.get('generated_at')
        }

        await self.engine.update_investigation(investigation_id, enrichment_data)

        return self.engine.response({
            'investigation_id': investigation_id,
            'enrichment_added': {
                'entities': len(context.get('common_entities', [])),
                'relationships': len(context.get('intersection_relationships', [])),
                'timeline_events': len(context.get('temporal_overlap', []))
            },
            'updated_at': enrichment_data['enriched_at']
        })

    except Exception as e:
        self.logger.error(f"Error enriching investigation: {e}")
        return self.engine.response({'error': str(e)}, status=500)


async def get_timeline(self, request):
    """
    GET /api/v1/investigations/{investigation_id}/timeline?hours_back=24

    Get investigation timeline (most recent first)

    Returns:
    {
        "investigation_id": "uuid",
        "timeline": [
            {
                "timestamp": "2025-10-02T10:30:00",
                "event_type": "authentication_failure",
                "entity": {"type": "user", "value": "admin"},
                "description": "Failed login attempt from *************",
                "risk_score": 75
            },
            ...
        ],
        "count": 89,
        "time_range": {"start": "...", "end": "..."}
    }
    """
    try:
        investigation_id = request.match_info['investigation_id']
        hours_back = int(request.query.get('hours_back', 24))

        if not self.context_manager:
            return self.engine.response({'error': 'Context manager not available'}, status=503)

        # Get investigation
        investigation = await self.engine.get_investigation_by_id(investigation_id)
        if not investigation:
            return self.engine.response({'error': 'Investigation not found'}, status=404)

        # Extract entities
        entities = investigation.get('entities', {})
        entity_list = []

        for entity_type, values in entities.items():
            if isinstance(values, list):
                for value in values:
                    entity_list.append({'type': entity_type.rstrip('s'), 'value': value})

        if not entity_list:
            return self.engine.response({
                'investigation_id': investigation_id,
                'timeline': [],
                'count': 0
            })

        # Get timeline for all entities
        timeline_items = []

        for entity in entity_list:
            history = await self.context_manager.get_entity_history(
                entity['type'],
                entity['value'],
                hours_back=hours_back
            )

            # Extract timeline events
            for event in history.timeline:
                timeline_items.append({
                    'timestamp': event['timestamp'],
                    'event_type': event.get('event_type', 'unknown'),
                    'entity': entity,
                    'description': event.get('description', ''),
                    'risk_score': event.get('risk_score', 0),
                    'source': event.get('source', 'unknown')
                })

        # Sort by timestamp DESC (most recent first)
        timeline_items.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

        return self.engine.response({
            'investigation_id': investigation_id,
            'timeline': timeline_items,
            'count': len(timeline_items),
            'time_range': {
                'start': timeline_items[-1]['timestamp'] if timeline_items else None,
                'end': timeline_items[0]['timestamp'] if timeline_items else None
            }
        })

    except Exception as e:
        self.logger.error(f"Error getting timeline: {e}")
        return self.engine.response({'error': str(e)}, status=500)


async def collect_evidence(self, request):
    """
    POST /api/v1/investigations/{investigation_id}/collect_evidence

    Collect evidence from SIEM

    Body:
    {
        "siem_type": "elastic",
        "filters": {
            "user.name": "admin",
            "event.action": "login"
        },
        "time_range_hours": 24,
        "max_results": 1000
    }

    Returns:
    {
        "investigation_id": "uuid",
        "query_id": "uuid",
        "evidence_collected": 45,
        "avg_relevance": 0.78,
        "siem_urls": ["https://..."]
    }
    """
    try:
        investigation_id = request.match_info['investigation_id']
        data = await request.json()

        if not self.evidence_logger:
            return self.engine.response({'error': 'Evidence logger not available'}, status=503)

        # Validate investigation exists
        investigation = await self.engine.get_investigation_by_id(investigation_id)
        if not investigation:
            return self.engine.response({'error': 'Investigation not found'}, status=404)

        # Prepare query parameters
        from datetime import datetime, timedelta

        hours_back = data.get('time_range_hours', 24)
        time_end = datetime.utcnow()
        time_start = time_end - timedelta(hours=hours_back)

        query_params = {
            'filters': data.get('filters', {}),
            'time_range_start': time_start.isoformat(),
            'time_range_end': time_end.isoformat()
        }

        # Create evidence query
        evidence_query = await self.evidence_logger.create_evidence_query(
            investigation_id=investigation_id,
            siem_type=data.get('siem_type', 'elastic'),
            query_params=query_params,
            user=data.get('user', 'system')
        )

        # Collect evidence
        evidence_items = await self.evidence_logger.collect_evidence(
            evidence_query,
            max_results=data.get('max_results', 1000)
        )

        # Calculate statistics
        avg_relevance = sum(e.relevance_score for e in evidence_items) / len(evidence_items) if evidence_items else 0
        siem_urls = list(set(e.siem_url for e in evidence_items[:10]))  # First 10 unique URLs

        return self.engine.response({
            'investigation_id': investigation_id,
            'query_id': evidence_query.query_id,
            'evidence_collected': len(evidence_items),
            'avg_relevance': round(avg_relevance, 2),
            'siem_urls': siem_urls
        }, status=201)

    except Exception as e:
        self.logger.error(f"Error collecting evidence: {e}")
        return self.engine.response({'error': str(e)}, status=500)
