"""
CrowdStrike Context Source Plugin using FalconPy SDK
Implements all CrowdStrike API scopes for investigation context
"""

from typing import Dict, Any, List
from falconpy import Hosts, Detects, Incidents
from context_source_plugin import (
    ContextSourcePlugin, ContextQuery, ContextResult, ContextCategory
)


class CrowdStrikeContextPlugin(ContextSourcePlugin):
    """
    CrowdStrike plugin using official FalconPy SDK
    Supports HOSTS_READ, DETECTIONS_READ, INCIDENTS_READ scopes
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.client_id = config.get('client_id')
        self.client_secret = config.get('client_secret')
        self.base_url = config.get('base_url', 'https://api.crowdstrike.com')

        # Initialize FalconPy clients
        self.hosts_client = None
        self.detects_client = None
        self.incidents_client = None

    def get_source_name(self) -> str:
        return "crowdstrike"

    def get_supported_categories(self) -> List[ContextCategory]:
        return [
            ContextCategory.ASSET,      # HOSTS_READ
            ContextCategory.DETECTION,  # DETECTIONS_READ
            ContextCategory.INCIDENT,   # INCIDENTS_READ
        ]

    def get_supported_query_types(self) -> List[str]:
        return ['ip', 'hostname', 'device_id', 'user', 'file_hash']

    async def validate_credentials(self) -> bool:
        """Validate API credentials"""
        if not self.client_id or not self.client_secret:
            self.logger.warning("CrowdStrike credentials not configured")
            return False

        try:
            # Initialize Hosts client to test auth
            self.hosts_client = Hosts(
                client_id=self.client_id,
                client_secret=self.client_secret,
                base_url=self.base_url
            )

            # Test authentication with a simple query
            response = self.hosts_client.query_devices_by_filter(limit=1)

            if response['status_code'] == 200:
                self.logger.info("CrowdStrike authentication successful")

                # Initialize other clients
                self.detects_client = Detects(
                    client_id=self.client_id,
                    client_secret=self.client_secret,
                    base_url=self.base_url
                )
                self.incidents_client = Incidents(
                    client_id=self.client_id,
                    client_secret=self.client_secret,
                    base_url=self.base_url
                )

                return True
            else:
                self.logger.error(f"CrowdStrike auth failed: {response['body']['errors']}")
                return False

        except Exception as e:
            self.logger.error(f"CrowdStrike authentication error: {e}")
            return False

    async def query_context(self, query: ContextQuery) -> List[ContextResult]:
        """Query CrowdStrike for context data"""
        results = []

        # Route to appropriate query method based on category
        for category in query.categories:
            if category == ContextCategory.ASSET:
                asset_results = await self._query_hosts(query)
                results.extend(asset_results)

            elif category == ContextCategory.DETECTION:
                detection_results = await self._query_detections(query)
                results.extend(detection_results)

            elif category == ContextCategory.INCIDENT:
                incident_results = await self._query_incidents(query)
                results.extend(incident_results)

        return results

    async def _query_hosts(self, query: ContextQuery) -> List[ContextResult]:
        """Query HOSTS_READ scope for asset information"""
        results = []

        try:
            # Build FQL filter based on query type
            if query.query_type == 'hostname':
                fql_filter = f"hostname:'{query.query_value}'"
            elif query.query_type == 'ip':
                fql_filter = f"local_ip:'{query.query_value}'"
            elif query.query_type == 'device_id':
                # Direct device ID lookup
                response = self.hosts_client.get_device_details(ids=[query.query_value])
                if response['status_code'] == 200 and response['body']['resources']:
                    for host in response['body']['resources']:
                        results.append(self._format_host_result(host))
                return results
            else:
                self.logger.debug(f"Query type {query.query_type} not supported for host queries")
                return results

            # Query devices with filter
            query_response = self.hosts_client.query_devices_by_filter(filter=fql_filter, limit=50)

            if query_response['status_code'] != 200:
                self.logger.error(f"Host query failed: {query_response['body'].get('errors')}")
                return results

            device_ids = query_response['body'].get('resources', [])

            if not device_ids:
                self.logger.debug(f"No hosts found for {query.query_type}={query.query_value}")
                return results

            # Get detailed host information
            details_response = self.hosts_client.get_device_details(ids=device_ids)

            if details_response['status_code'] == 200:
                for host in details_response['body'].get('resources', []):
                    results.append(self._format_host_result(host))

        except Exception as e:
            self.logger.error(f"Error querying CrowdStrike hosts: {e}", exc_info=True)

        return results

    async def _query_detections(self, query: ContextQuery) -> List[ContextResult]:
        """Query DETECTIONS_READ scope"""
        results = []

        try:
            # Build FQL filter
            if query.query_type == 'hostname':
                fql_filter = f"host_name:'{query.query_value}'"
            elif query.query_type == 'ip':
                fql_filter = f"host_ip:'{query.query_value}'"
            elif query.query_type == 'user':
                fql_filter = f"user_name:'{query.query_value}'"
            elif query.query_type == 'file_hash':
                fql_filter = f"sha256:'{query.query_value}'"
            else:
                return results

            # Query detections
            query_response = self.detects_client.query_detects(filter=fql_filter, limit=50)

            if query_response['status_code'] != 200:
                self.logger.error(f"Detection query failed: {query_response['body'].get('errors')}")
                return results

            detection_ids = query_response['body'].get('resources', [])

            if not detection_ids:
                return results

            # Get detection details
            details_response = self.detects_client.get_detect_summaries(ids=detection_ids)

            if details_response['status_code'] == 200:
                for detection in details_response['body'].get('resources', []):
                    results.append(self._format_detection_result(detection))

        except Exception as e:
            self.logger.error(f"Error querying CrowdStrike detections: {e}", exc_info=True)

        return results

    async def _query_incidents(self, query: ContextQuery) -> List[ContextResult]:
        """Query INCIDENTS_READ scope"""
        results = []

        try:
            # Build FQL filter
            if query.query_type == 'hostname':
                fql_filter = f"host_name:'{query.query_value}'"
            elif query.query_type == 'user':
                fql_filter = f"user_name:'{query.query_value}'"
            else:
                return results

            # Query incidents
            query_response = self.incidents_client.query_incidents(filter=fql_filter, limit=50)

            if query_response['status_code'] != 200:
                self.logger.error(f"Incident query failed: {query_response['body'].get('errors')}")
                return results

            incident_ids = query_response['body'].get('resources', [])

            if not incident_ids:
                return results

            # Get incident details
            details_response = self.incidents_client.get_incidents(ids=incident_ids)

            if details_response['status_code'] == 200:
                for incident in details_response['body'].get('resources', []):
                    results.append(self._format_incident_result(incident))

        except Exception as e:
            self.logger.error(f"Error querying CrowdStrike incidents: {e}", exc_info=True)

        return results

    def _format_host_result(self, host: Dict[str, Any]) -> ContextResult:
        """Format host data into ContextResult"""
        return ContextResult(
            source_name=self.get_source_name(),
            category=ContextCategory.ASSET,
            confidence=0.95,
            data={
                'device_id': host.get('device_id'),
                'hostname': host.get('hostname'),
                'local_ip': host.get('local_ip'),
                'external_ip': host.get('external_ip'),
                'mac_address': host.get('mac_address'),
                'os_version': host.get('os_version'),
                'platform_name': host.get('platform_name'),
                'first_seen': host.get('first_seen'),
                'last_seen': host.get('last_seen'),
                'status': host.get('status'),
                'agent_version': host.get('agent_version'),
                'tags': host.get('tags', []),
                'groups': host.get('groups', [])
            },
            timestamp=host.get('last_seen'),
            metadata={'raw': host}
        )

    def _format_detection_result(self, detection: Dict[str, Any]) -> ContextResult:
        """Format detection data into ContextResult"""
        return ContextResult(
            source_name=self.get_source_name(),
            category=ContextCategory.DETECTION,
            confidence=0.90,
            data={
                'detection_id': detection.get('detection_id'),
                'severity': detection.get('severity'),
                'tactic': detection.get('tactic'),
                'technique': detection.get('technique'),
                'hostname': detection.get('device', {}).get('hostname'),
                'user_name': detection.get('user_name'),
                'file_name': detection.get('file_name'),
                'file_path': detection.get('file_path'),
                'cmdline': detection.get('cmdline'),
                'sha256': detection.get('sha256'),
                'status': detection.get('status'),
                'description': detection.get('description')
            },
            timestamp=detection.get('created_timestamp'),
            metadata={'raw': detection}
        )

    def _format_incident_result(self, incident: Dict[str, Any]) -> ContextResult:
        """Format incident data into ContextResult"""
        return ContextResult(
            source_name=self.get_source_name(),
            category=ContextCategory.INCIDENT,
            confidence=0.92,
            data={
                'incident_id': incident.get('incident_id'),
                'name': incident.get('name'),
                'description': incident.get('description'),
                'status': incident.get('status'),
                'severity': incident.get('severity'),
                'hosts': incident.get('hosts', []),
                'users': incident.get('users', []),
                'tactics': incident.get('tactics', []),
                'techniques': incident.get('techniques', []),
                'assigned_to': incident.get('assigned_to'),
                'created': incident.get('created'),
                'modified': incident.get('modified')
            },
            timestamp=incident.get('modified'),
            metadata={'raw': incident}
        )
