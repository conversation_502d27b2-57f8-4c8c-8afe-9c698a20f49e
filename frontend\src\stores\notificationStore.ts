import { create } from 'zustand'

export interface Notification {
  id: string
  type: 'alert' | 'case' | 'system' | 'workflow'
  title: string
  message: string
  timestamp: number
  read: boolean
  priority: 'low' | 'medium' | 'high' | 'critical'
  metadata?: Record<string, any>
  actionUrl?: string
}

interface NotificationStore {
  notifications: Notification[]
  unreadCount: number

  // Actions
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  removeNotification: (id: string) => void
  clearAll: () => void
  snoozeNotification: (id: string, duration: number) => void
}

export const useNotificationStore = create<NotificationStore>((set, get) => ({
  notifications: [],
  unreadCount: 0,

  addNotification: (notification) => {
    const newNotification: Notification = {
      ...notification,
      id: `notif-${Date.now()}`,
      timestamp: Date.now(),
      read: false
    }

    set((state) => ({
      notifications: [newNotification, ...state.notifications],
      unreadCount: state.unreadCount + 1
    }))

    // Play sound for high/critical notifications
    if (notification.priority === 'high' || notification.priority === 'critical') {
      // Could add sound notification here
    }
  },

  markAsRead: (id) => {
    set((state) => {
      const notifications = state.notifications.map(n =>
        n.id === id ? { ...n, read: true } : n
      )
      const unreadCount = notifications.filter(n => !n.read).length
      return { notifications, unreadCount }
    })
  },

  markAllAsRead: () => {
    set((state) => ({
      notifications: state.notifications.map(n => ({ ...n, read: true })),
      unreadCount: 0
    }))
  },

  removeNotification: (id) => {
    set((state) => {
      const notifications = state.notifications.filter(n => n.id !== id)
      const unreadCount = notifications.filter(n => !n.read).length
      return { notifications, unreadCount }
    })
  },

  clearAll: () => {
    set({ notifications: [], unreadCount: 0 })
  },

  snoozeNotification: (id, duration) => {
    const notification = get().notifications.find(n => n.id === id)
    if (!notification) return

    // Remove notification temporarily
    get().removeNotification(id)

    // Re-add after duration
    setTimeout(() => {
      get().addNotification({
        ...notification,
        title: `[Snoozed] ${notification.title}`
      })
    }, duration)
  }
}))

// Initialize with some mock notifications
setTimeout(() => {
  const store = useNotificationStore.getState()

  store.addNotification({
    type: 'alert',
    title: 'High Severity Alert',
    message: 'Suspicious login activity detected from unknown location',
    priority: 'high',
    actionUrl: '/alerts/123'
  })

  store.addNotification({
    type: 'case',
    title: 'Case Updated',
    message: 'CASE-456 has been assigned to you',
    priority: 'medium',
    actionUrl: '/cases/456'
  })

  store.addNotification({
    type: 'system',
    title: 'Pattern Crystallization Complete',
    message: '8 new patterns have been validated and added to the library',
    priority: 'low',
    actionUrl: '/engineering/patterns'
  })
}, 2000)