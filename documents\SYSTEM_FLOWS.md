# SIEMLess v2.0 - System Flows & Architecture Diagrams

## Table of Contents
1. [High-Level Architecture](#high-level-architecture)
2. [Data Flow Diagrams](#data-flow-diagrams)
3. [Workflow Orchestration](#workflow-orchestration)
4. [Engine Communication](#engine-communication)
5. [Pattern Lifecycle](#pattern-lifecycle)
6. [Incident Response Flow](#incident-response-flow)

---

## High-Level Architecture

```mermaid
graph TB
    subgraph "Data Sources"
        DS1[Syslog]
        DS2[APIs]
        DS3[Cloud Logs]
        DS4[SIEM Exports]
        DS5[CTI Feeds]
    end

    subgraph "SIEMLess v2.0 Platform"
        subgraph "Ingestion Layer"
            IE[Ingestion Engine<br/>Port 8003]
            GH[GitHub Sync]
            HR[Hot Reload]
        end

        subgraph "Intelligence Layer"
            INT[Intelligence Engine<br/>Port 8001]
            AI[AI Consensus]
            PC[Pattern Crystallization]
        end

        subgraph "Context Layer"
            CE[Contextualization Engine<br/>Port 8004]
            EE[Entity Extraction]
            RM[Relationship Mapping]
        end

        subgraph "Storage Layer"
            BE[Backend Engine<br/>Port 8002]
            CTI[CTI Processing]
            ST[Storage Tiers]
        end

        subgraph "Delivery Layer"
            DE[Delivery Engine<br/>Port 8005]
            WO[Workflow Orchestrator]
            API[REST APIs]
        end

        subgraph "Infrastructure"
            RD[(Redis<br/>Port 6380)]
            PG[(PostgreSQL<br/>Port 5433)]
            S3[(S3 Archive)]
        end
    end

    subgraph "Outputs"
        O1[Dashboards]
        O2[Alerts]
        O3[SIEM Rules]
        O4[Reports]
        O5[Cases]
    end

    DS1 & DS2 & DS3 & DS4 & DS5 --> IE
    IE <--> RD
    IE --> INT
    INT --> PC
    PC --> BE
    IE --> CE
    CE --> EE & RM
    INT <--> AI
    BE --> CTI & ST
    BE <--> PG
    BE --> S3
    DE --> WO
    WO --> API
    DE --> O1 & O2 & O3 & O4 & O5

    RD <--> IE & INT & CE & BE & DE
    PG <--> BE & DE

    GH --> IE
    HR --> IE

    style IE fill:#e1f5fe
    style INT fill:#fff3e0
    style CE fill:#f3e5f5
    style BE fill:#e8f5e9
    style DE fill:#fce4ec
    style RD fill:#ffebee
    style PG fill:#e3f2fd
```

---

## Data Flow Diagrams

### Standard Log Processing Flow (Deterministic - FREE)

```mermaid
sequenceDiagram
    participant L as Log Source
    participant I as Ingestion Engine
    participant P as Pattern Library
    participant C as Context Engine
    participant D as Delivery Engine
    participant S as Storage

    L->>I: Raw Log
    I->>P: Check Pattern Match
    P-->>I: Pattern Found ✓
    I->>C: Extract Entities
    C->>C: Enrich Context
    C->>D: Enriched Event
    D->>D: Generate Alert/Case
    D->>S: Store Event
    Note over I,S: Cost: $0.0001/log (CPU only)
```

### Unknown Pattern Discovery Flow (AI Learning - EXPENSIVE)

```mermaid
sequenceDiagram
    participant L as Log Source
    participant I as Ingestion Engine
    participant P as Pattern Library
    participant AI as Intelligence Engine
    participant B as Backend Engine

    L->>I: Unknown Log
    I->>P: Check Pattern Match
    P-->>I: No Match ✗
    I->>AI: Send for Analysis

    rect rgb(255, 240, 240)
        Note over AI: Expensive AI Operation
        AI->>AI: Multi-Model Analysis
        AI->>AI: Consensus Validation (80%)
        AI->>AI: Pattern Generation
    end

    AI->>B: Crystallize Pattern
    B->>P: Store New Pattern
    B-->>I: Pattern Deployed
    Note over I,B: Future matches: FREE
    Note over AI: Cost: $0.02/pattern (one-time)
```

---

## Workflow Orchestration

### Workflow Execution Architecture

```mermaid
graph LR
    subgraph "Workflow Orchestrator"
        WT[Workflow<br/>Templates]
        WE[Workflow<br/>Executor]
        TM[Transaction<br/>Manager]
        SP[State<br/>Persistence]
    end

    subgraph "Redis Channels"
        CH1[ingestion.workflow.*]
        CH2[intelligence.workflow.*]
        CH3[context.workflow.*]
        CH4[backend.workflow.*]
        CH5[delivery.workflow.*]
        RESP[workflow.response.*]
    end

    subgraph "Engines"
        E1[Ingestion]
        E2[Intelligence]
        E3[Context]
        E4[Backend]
        E5[Delivery]
    end

    API[REST API] --> WT
    WT --> WE
    WE --> TM
    TM --> SP

    WE --> CH1 & CH2 & CH3 & CH4 & CH5
    CH1 --> E1
    CH2 --> E2
    CH3 --> E3
    CH4 --> E4
    CH5 --> E5

    E1 & E2 & E3 & E4 & E5 --> RESP
    RESP --> WE

    SP -.-> DB[(PostgreSQL)]
```

### Full Incident Investigation Workflow

```mermaid
stateDiagram-v2
    [*] --> Start: Incident Triggered

    Start --> CollectLogs: Step 1
    CollectLogs --> AnalyzeThreat: Step 2
    AnalyzeThreat --> EnrichContext: Step 3
    EnrichContext --> GenerateRules: Step 4
    GenerateRules --> CreateCase: Step 5
    CreateCase --> SendAlerts: Step 6

    SendAlerts --> Success: All Steps Complete
    Success --> [*]

    CollectLogs --> Rollback: Error
    AnalyzeThreat --> Rollback: Error
    EnrichContext --> Rollback: Error
    GenerateRules --> Rollback: Error
    CreateCase --> Rollback: Error
    SendAlerts --> Rollback: Error

    Rollback --> Failed: Compensation Complete
    Failed --> [*]

    note right of CollectLogs: Ingestion Engine\n10 actions
    note right of AnalyzeThreat: Intelligence Engine\n14 actions
    note right of EnrichContext: Context Engine\n12 actions
    note right of GenerateRules: Backend Engine\n12 actions
    note right of CreateCase: Delivery Engine\n9 actions
```

---

## Engine Communication

### Inter-Engine Message Flow via Redis Pub/Sub

```mermaid
graph TB
    subgraph "Message Channels"
        direction LR

        subgraph "Ingestion Channels"
            IC1[ingestion.log_received]
            IC2[ingestion.unknown_pattern]
            IC3[ingestion.pattern_matched]
        end

        subgraph "Intelligence Channels"
            IN1[intelligence.analyze_pattern]
            IN2[intelligence.consensus_result]
            IN3[intelligence.pattern_crystallized]
        end

        subgraph "Context Channels"
            CC1[context.entities_extracted]
            CC2[context.enrichment_complete]
            CC3[context.relationships_mapped]
        end

        subgraph "Backend Channels"
            BC1[backend.store_pattern]
            BC2[backend.cti_updated]
            BC3[backend.rules_generated]
        end

        subgraph "Delivery Channels"
            DC1[delivery.case_created]
            DC2[delivery.alert_sent]
            DC3[delivery.workflow_complete]
        end
    end

    IE[Ingestion<br/>Engine] --> IC1 & IC2 & IC3
    INT[Intelligence<br/>Engine] --> IN1 & IN2 & IN3
    CE[Context<br/>Engine] --> CC1 & CC2 & CC3
    BE[Backend<br/>Engine] --> BC1 & BC2 & BC3
    DE[Delivery<br/>Engine] --> DC1 & DC2 & DC3

    IC2 -.-> IN1
    IN3 -.-> BC1
    IC3 -.-> CC1
    CC2 -.-> DC1
    BC3 -.-> DC2
```

---

## Pattern Lifecycle

### Complete Pattern Evolution Flow

```mermaid
graph TD
    Start([Unknown Log Detected]) --> Collect[Collect Similar Logs]
    Collect --> Analyze[AI Analysis]

    Analyze --> Consensus{Consensus<br/>≥80%?}
    Consensus -->|No| Refine[Refine Pattern]
    Refine --> Analyze

    Consensus -->|Yes| Crystallize[Crystallize Pattern]
    Crystallize --> Store[(Pattern Library)]

    Store --> Deploy[Hot Deploy]
    Deploy --> Monitor[Production Monitoring]

    Monitor --> Performance{Performance<br/>Good?}
    Performance -->|No| Optimize[Optimize Pattern]
    Optimize --> Version[New Version]
    Version --> Store

    Performance -->|Yes| Track[Track Metrics]
    Track --> Report[Cost Savings Report]

    style Start fill:#e1f5fe
    style Crystallize fill:#fff3e0
    style Deploy fill:#e8f5e9
    style Report fill:#c8e6c9
```

### Pattern Crystallization Details

```mermaid
sequenceDiagram
    participant U as Unknown Log
    participant I as Intelligence Engine
    participant G as Gemma (FREE)
    participant F as Gemini Flash
    participant C as Claude
    participant B as Backend Engine
    participant P as Pattern Library

    U->>I: Analyze Request

    par Parallel AI Analysis
        I->>G: Analyze (FREE)
        I->>F: Analyze ($0.002)
        I->>C: Analyze ($0.015)
    end

    G-->>I: Pattern Suggestion
    F-->>I: Pattern Suggestion
    C-->>I: Pattern Suggestion

    I->>I: Calculate Consensus

    alt Consensus ≥ 80%
        I->>B: Crystallize Pattern
        B->>P: Store Pattern
        B-->>I: Pattern ID
        Note over P: Future: FREE
    else Consensus < 80%
        I->>I: Request Higher Model
        I->>C: Re-analyze with Context
    end
```

---

## Incident Response Flow

### Complete Incident Lifecycle

```mermaid
stateDiagram-v2
    [*] --> Detection

    state Detection {
        [*] --> LogIngestion
        LogIngestion --> PatternMatch
        PatternMatch --> ThreatIdentified
    }

    Detection --> Triage

    state Triage {
        [*] --> CollectEvidence
        CollectEvidence --> AnalyzeThreat
        AnalyzeThreat --> AssessImpact
        AssessImpact --> PriorityAssignment
    }

    Triage --> Investigation

    state Investigation {
        [*] --> EntityExtraction
        EntityExtraction --> RelationshipMapping
        RelationshipMapping --> TimelineBuilding
        TimelineBuilding --> RootCauseAnalysis
    }

    Investigation --> Response

    state Response {
        [*] --> GenerateRules
        GenerateRules --> DeployRules
        DeployRules --> ContainmentActions
        ContainmentActions --> VerifyContainment
    }

    Response --> Recovery

    state Recovery {
        [*] --> RemoveThreat
        RemoveThreat --> RestoreServices
        RestoreServices --> ValidateRecovery
    }

    Recovery --> LessonsLearned

    state LessonsLearned {
        [*] --> DocumentIncident
        DocumentIncident --> UpdatePatterns
        UpdatePatterns --> ImproveDetection
    }

    LessonsLearned --> [*]
```

---

## GitHub Pattern Sync Flow

```mermaid
sequenceDiagram
    participant GH as GitHub Repo
    participant GS as GitHub Sync
    participant V as Validator
    participant HR as Hot Reload
    participant PM as Pattern Matcher
    participant PL as Pattern Library

    loop Every Hour
        GS->>GH: Check for Updates
        GH-->>GS: New Patterns Available

        GS->>V: Validate Patterns
        V->>V: Syntax Check
        V->>V: Security Check

        alt Validation Success
            V->>HR: Deploy Patterns
            HR->>PM: Update Cache
            HR->>PL: Store Versions
            HR-->>GS: Deployment Success
            Note over PM: <100ms deployment
        else Validation Failure
            V-->>GS: Reject Patterns
            GS->>GS: Log Error
        end
    end
```

---

## CTI Integration Flow

```mermaid
graph LR
    subgraph "CTI Sources"
        OTX[OTX Feed]
        MISP[MISP]
        TF[ThreatFox]
        Custom[Custom Feeds]
    end

    subgraph "Processing Pipeline"
        Ingest[CTI Ingestion]
        Dedup[Deduplication]
        Validate[Validation]
        Enrich[Enrichment]
    end

    subgraph "Rule Generation"
        Gen[Rule Generator]
        Test[Test Cases]
        Deploy[Deployment]
    end

    subgraph "Target SIEMs"
        Splunk[Splunk]
        Elastic[Elastic]
        Sentinel[Sentinel]
        QRadar[QRadar]
    end

    OTX & MISP & TF & Custom --> Ingest
    Ingest --> Dedup
    Dedup --> Validate
    Validate --> Enrich
    Enrich --> Gen
    Gen --> Test
    Test --> Deploy
    Deploy --> Splunk & Elastic & Sentinel & QRadar

    style OTX fill:#e3f2fd
    style Gen fill:#fff3e0
    style Deploy fill:#e8f5e9
```

---

## Alert Delivery Flow

```mermaid
graph TD
    A[Alert Generated] --> Priority{Priority?}

    Priority -->|Critical| Critical[Critical Path]
    Priority -->|High| High[High Path]
    Priority -->|Medium| Medium[Medium Path]
    Priority -->|Low| Low[Low Path]

    Critical --> PD[PagerDuty]
    Critical --> SMS[SMS]
    Critical --> Phone[Phone Call]

    High --> Slack[Slack]
    High --> Email[Email]
    High --> Teams[Teams]

    Medium --> Dashboard[Dashboard]
    Medium --> Email2[Email Digest]

    Low --> Log[Log File]
    Low --> Archive[Archive]

    PD & SMS & Phone & Slack & Email & Teams & Dashboard & Email2 & Log & Archive --> Track[Delivery Tracking]
    Track --> DB[(Database)]

    style Critical fill:#ffebee
    style High fill:#fff3e0
    style Medium fill:#e3f2fd
    style Low fill:#e8f5e9
```

---

## Query Translation Flow

```mermaid
graph LR
    NL[Natural Language Query] --> Parser[Query Parser]
    Parser --> AST[Abstract Syntax Tree]

    AST --> Splunk[Splunk SPL]
    AST --> Elastic[Elastic KQL]
    AST --> Sentinel[Sentinel KQL]
    AST --> QRadar[QRadar AQL]

    subgraph "Example Translation"
        Ex1["Find failed logins"] -.-> S1["index=auth action=failed"]
        Ex1 -.-> E1["event.action:'failed'"]
        Ex1 -.-> SE1["SecurityEvent | where EventID == 4625"]
        Ex1 -.-> Q1["SELECT * FROM events WHERE action='failed'"]
    end

    style NL fill:#e1f5fe
    style AST fill:#fff3e0
```

---

## Cost Optimization Flow

```mermaid
graph TD
    Task[AI Task Required] --> Complexity{Task<br/>Complexity?}

    Complexity -->|Simple| Free[Gemma 27B<br/>FREE]
    Complexity -->|Medium| Low[Gemini Flash<br/>$0.002]
    Complexity -->|Complex| Med[Claude Haiku<br/>$0.008]
    Complexity -->|Critical| High[GPT-4/Opus<br/>$0.020]

    Free & Low & Med & High --> Execute[Execute Task]
    Execute --> Cache{Cacheable?}

    Cache -->|Yes| Pattern[Create Pattern]
    Cache -->|No| Result[Return Result]

    Pattern --> Library[(Pattern Library)]
    Library --> Future[Future: FREE]

    style Free fill:#c8e6c9
    style Low fill:#fff3e0
    style Med fill:#ffecb3
    style High fill:#ffcdd2
    style Future fill:#a5d6a7
```

---

## Storage Tiering Flow

```mermaid
graph TD
    Data[Incoming Data] --> Hot{Age?}

    Hot -->|< 24h| Redis[(Redis Cache<br/>Hot Tier)]
    Hot -->|1-30d| PG[(PostgreSQL<br/>Warm Tier)]
    Hot -->|> 30d| S3[(S3 Archive<br/>Cold Tier)]

    Redis --> Expire1{Expired?}
    Expire1 -->|Yes| MovePG[Move to PostgreSQL]
    MovePG --> PG

    PG --> Expire2{> 30 days?}
    Expire2 -->|Yes| MoveS3[Archive to S3]
    MoveS3 --> S3

    Query[Query Request] --> Check{Data Location?}
    Check -->|Hot| Redis
    Check -->|Warm| PG
    Check -->|Cold| S3

    S3 -.->|Restore| PG
    PG -.->|Cache| Redis

    style Redis fill:#ffebee
    style PG fill:#e3f2fd
    style S3 fill:#f5f5f5
```

---

## Pattern Performance Monitoring

```mermaid
sequenceDiagram
    participant P as Production Pattern
    participant M as Monitor
    participant A as Analytics
    participant O as Optimizer
    participant L as Pattern Library

    loop Every Hour
        P->>M: Collect Metrics
        Note over M: Hit Rate<br/>False Positives<br/>Processing Time

        M->>A: Analyze Performance

        alt Performance Degraded
            A->>O: Trigger Optimization
            O->>O: Adjust Pattern
            O->>O: A/B Test
            O->>L: Deploy New Version
            L-->>P: Updated Pattern
        else Performance Good
            A->>A: Log Metrics
        end
    end

    A->>A: Generate Report
    Note over A: 99.97% Cost Savings<br/>95% Hit Rate
```

---

## Workflow Rollback Flow

```mermaid
stateDiagram-v2
    [*] --> Running: Workflow Executing
    Running --> Error: Step Failed

    Error --> StartRollback: Initiate Rollback

    StartRollback --> ReverseActions: Reverse Completed Steps

    state ReverseActions {
        [*] --> CheckStep
        CheckStep --> HasCompensation: Step Has Compensation?
        HasCompensation --> ExecuteCompensation: Yes
        HasCompensation --> SkipStep: No
        ExecuteCompensation --> NextStep
        SkipStep --> NextStep
        NextStep --> CheckStep: More Steps?
        NextStep --> [*]: No More Steps
    }

    ReverseActions --> RestoreState: Restore Initial State
    RestoreState --> NotifyUser: Send Notification
    NotifyUser --> LogRollback: Log Audit Trail
    LogRollback --> [*]: Rollback Complete
```

---

*These Mermaid diagrams can be rendered using mermaidtools.com or any Markdown viewer that supports Mermaid syntax.*