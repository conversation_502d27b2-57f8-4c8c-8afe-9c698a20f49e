# Delivery Engine - Context Integration Guide

## Overview
This document provides the complete implementation for integrating the context plugin system into the Delivery Engine, enabling the frontend to pull investigation context.

---

## Architecture

```
Frontend (React)
    ↓ HTTP GET /api/alerts/{alert_id}/context
Delivery Engine
    ↓ Generate request_id
    ↓ Store in pending_contexts cache
    ↓ Redis PUBLISH ingestion.pull_context
Ingestion Engine (Plugin System)
    ↓ Query all applicable plugins
    ↓ Redis PUBLISH contextualization.extract_from_context
Contextualization Engine
    ↓ Extract entities + relationships
    ↓ Redis PUBLISH delivery.context.{request_id}.complete
Delivery Engine
    ↓ Match request_id
    ↓ Retrieve from cache
    ↓ HTTP Response to Frontend
Frontend displays unified context
```

---

## Implementation Steps

### Step 1: Add Context Request Cache

Add to `DeliveryEngine.__init__()`:

```python
# In __init__ method after self.context_generator initialization:

# Context request tracking
self.pending_contexts = {}  # {request_id: {'alert_id': ..., 'timestamp': ..., 'future': asyncio.Future()}}
self.context_cache = {}  # {request_id: context_result}
```

### Step 2: Add Message Handler for Context Responses

Add to `DeliveryEngine.process_message()`:

```python
# In process_message method, add these elif conditions:

elif channel.startswith('delivery.context.') and channel.endswith('.complete'):
    # Extract request_id from channel name
    request_id = channel.replace('delivery.context.', '').replace('.complete', '')
    await self._handle_context_complete(request_id, message_data)

elif channel.startswith('delivery.context.') and channel.endswith('.error'):
    # Extract request_id from channel name
    request_id = channel.replace('delivery.context.', '').replace('.error', '')
    await self._handle_context_error(request_id, message_data)
```

### Step 3: Implement Context Response Handlers

Add these methods to `DeliveryEngine` class:

```python
async def _handle_context_complete(self, request_id: str, data: Dict[str, Any]):
    """Handle completed context from Contextualization Engine"""
    try:
        self.logger.info(f"[{request_id}] Context complete: {data.get('total_entities', 0)} entities")

        # Store in cache
        self.context_cache[request_id] = {
            'status': 'complete',
            'data': data,
            'timestamp': datetime.utcnow().isoformat()
        }

        # Resolve pending future if exists
        if request_id in self.pending_contexts:
            pending = self.pending_contexts[request_id]
            if 'future' in pending and not pending['future'].done():
                pending['future'].set_result(data)

            # Clean up after resolution
            del self.pending_contexts[request_id]

    except Exception as e:
        self.logger.error(f"Error handling context complete: {e}", exc_info=True)


async def _handle_context_error(self, request_id: str, data: Dict[str, Any]):
    """Handle context error"""
    try:
        error = data.get('error', 'Unknown error')
        self.logger.error(f"[{request_id}] Context error: {error}")

        # Store error in cache
        self.context_cache[request_id] = {
            'status': 'error',
            'error': error,
            'timestamp': datetime.utcnow().isoformat()
        }

        # Resolve pending future with error
        if request_id in self.pending_contexts:
            pending = self.pending_contexts[request_id]
            if 'future' in pending and not pending['future'].done():
                pending['future'].set_exception(Exception(error))

            del self.pending_contexts[request_id]

    except Exception as e:
        self.logger.error(f"Error handling context error: {e}", exc_info=True)
```

### Step 4: Add HTTP Endpoint for Context Requests

Add to `_setup_http_routes()` method:

```python
# Add this route
app.router.add_get('/api/alerts/{alert_id}/context', self._api_get_alert_context_new)
```

### Step 5: Implement Context Request Method

Add this method to `DeliveryEngine` class:

```python
async def _api_get_alert_context_new(self, request: web.Request):
    """
    Get investigation context for an alert using plugin system

    GET /api/alerts/{alert_id}/context?categories=asset,detection&timeout=30

    Query params:
    - categories: Comma-separated list (default: asset,detection)
    - timeout: Timeout in seconds (default: 30)
    """
    try:
        alert_id = request.match_info['alert_id']

        # Parse query parameters
        categories = request.query.get('categories', 'asset,detection').split(',')
        timeout = int(request.query.get('timeout', 30))

        self.logger.info(f"Context request for alert {alert_id}, categories: {categories}")

        # Get alert data first
        alert = await self._get_alert_by_id(alert_id)
        if not alert:
            return web.json_response({'error': 'Alert not found'}, status=404)

        # Extract IP/hostname from alert for context query
        query_value = self._extract_query_value_from_alert(alert)
        query_type = self._determine_query_type(query_value)

        if not query_value:
            return web.json_response({
                'error': 'Could not extract IP/hostname from alert'
            }, status=400)

        # Generate request ID
        import uuid
        request_id = f"ctx-{uuid.uuid4().hex[:8]}"

        # Create future for waiting
        future = asyncio.Future()

        # Track pending request
        self.pending_contexts[request_id] = {
            'alert_id': alert_id,
            'query_value': query_value,
            'timestamp': datetime.utcnow().isoformat(),
            'future': future
        }

        # Subscribe to response channel
        pubsub = self.redis_client.pubsub()
        pubsub.subscribe(f'delivery.context.{request_id}.complete')
        pubsub.subscribe(f'delivery.context.{request_id}.error')

        # Publish context request to Ingestion Engine
        self.publish_message('ingestion.pull_context', {
            'request_id': request_id,
            'alert_id': alert_id,
            'query_type': query_type,
            'query_value': query_value,
            'categories': categories,
            'time_range': {
                'start': 'now-1h',
                'end': 'now'
            }
        })

        self.logger.info(f"[{request_id}] Published context request for {query_type}={query_value}")

        # Wait for result with timeout
        try:
            result = await asyncio.wait_for(future, timeout=timeout)

            # Add alert info to result
            result['alert'] = {
                'alert_id': alert_id,
                'title': alert.get('message', alert.get('title', 'Unknown')),
                'severity': alert.get('severity', 'medium'),
                'timestamp': alert.get('timestamp')
            }

            return web.json_response(result)

        except asyncio.TimeoutError:
            self.logger.warning(f"[{request_id}] Context request timed out")

            # Clean up
            if request_id in self.pending_contexts:
                del self.pending_contexts[request_id]

            pubsub.unsubscribe(f'delivery.context.{request_id}.complete')
            pubsub.unsubscribe(f'delivery.context.{request_id}.error')

            return web.json_response({
                'error': 'Context request timed out',
                'request_id': request_id
            }, status=504)

    except Exception as e:
        self.logger.error(f"Error getting alert context: {e}", exc_info=True)
        return web.json_response({'error': str(e)}, status=500)


async def _get_alert_by_id(self, alert_id: str) -> Optional[Dict[str, Any]]:
    """Get alert data by ID from Elastic"""
    try:
        # Fetch from Elastic
        alerts = await self._fetch_elastic_alerts(status_filter='all', limit=1000)

        for alert in alerts:
            if alert.get('alert_id') == alert_id:
                return alert

        return None

    except Exception as e:
        self.logger.error(f"Error fetching alert: {e}")
        return None


def _extract_query_value_from_alert(self, alert: Dict[str, Any]) -> Optional[str]:
    """Extract IP or hostname from alert for context query"""

    # Try to extract from entities
    entities = alert.get('entities', {})

    # Prefer IP addresses
    ips = entities.get('ips', [])
    if ips:
        return ips[0]

    # Fall back to hosts
    hosts = entities.get('hosts', [])
    if hosts:
        return hosts[0]

    # Try direct fields
    for field in ['source.ip', 'destination.ip', 'host.ip', 'agent.ip']:
        if '.' in field:
            parts = field.split('.')
            value = alert
            for part in parts:
                value = value.get(part, {})
                if not isinstance(value, dict):
                    break
            if value and isinstance(value, str):
                return value

    return None


def _determine_query_type(self, value: str) -> str:
    """Determine if value is IP, hostname, user, etc."""
    import re

    # IP address pattern
    ip_pattern = r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$'
    if re.match(ip_pattern, value):
        return 'ip'

    # Email pattern
    if '@' in value:
        return 'email'

    # Domain pattern (contains dots but not all numeric)
    if '.' in value and not re.match(ip_pattern, value):
        return 'domain'

    # Hash pattern (64 hex chars = SHA256, 40 = SHA1, 32 = MD5)
    if re.match(r'^[a-fA-F0-9]{32,64}$', value):
        return 'file_hash'

    # Default to hostname
    return 'hostname'
```

### Step 6: Add Channel Pattern Matching

Since Redis subscriptions with `*` don't work directly in Python redis library, we need to use psubscribe (pattern subscribe).

Update `get_subscribed_channels()` to return patterns:

```python
def get_subscribed_channels(self) -> List[str]:
    """Return list of Redis channels and patterns this engine subscribes to"""
    channels = [
        'delivery.create_case',
        'delivery.update_case',
        # ... other channels ...
    ]

    # Add patterns
    patterns = [
        'delivery.context.*.complete',
        'delivery.context.*.error'
    ]

    return {'channels': channels, 'patterns': patterns}
```

Then update `base_engine.py` to handle patterns in the subscribe loop.

**OR** simpler approach: In the message processing loop, check if channel starts with `delivery.context.` and route accordingly (already done in Step 2).

---

## Frontend Integration

### React Component: AlertContext.tsx

```typescript
// frontend/src/components/AlertContext.tsx

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';

interface AlertContextProps {
  alertId: string;
}

export const AlertContext: React.FC<AlertContextProps> = ({ alertId }) => {
  const [categories, setCategories] = useState(['asset', 'detection']);

  const { data, isLoading, error } = useQuery({
    queryKey: ['alert-context', alertId, categories],
    queryFn: async () => {
      const response = await axios.get(
        `http://localhost:8005/api/alerts/${alertId}/context`,
        {
          params: {
            categories: categories.join(','),
            timeout: 30
          }
        }
      );
      return response.data;
    },
    enabled: !!alertId,
    staleTime: 60000, // Cache for 1 minute
  });

  if (isLoading) {
    return (
      <div className="alert-context-loading">
        <div className="spinner" />
        <p>Pulling context from all sources...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="alert-context-error">
        <p>Failed to load context: {error.message}</p>
      </div>
    );
  }

  if (!data) return null;

  return (
    <div className="alert-context">
      <h2>Investigation Context</h2>

      {/* Alert Info */}
      <div className="context-alert">
        <h3>{data.alert.title}</h3>
        <span className={`severity-${data.alert.severity}`}>
          {data.alert.severity}
        </span>
      </div>

      {/* Entity Summary */}
      <div className="context-entities">
        <h3>Entities Found</h3>
        <p>Total: {data.total_entities} entities from {Object.keys(data.source_summaries || {}).length} sources</p>

        {data.entities && data.entities.map((entity, idx) => (
          <div key={idx} className="entity-card">
            <span className="entity-type">{entity.type}</span>
            <span className="entity-value">{entity.value}</span>
            <span className="entity-source">{entity.source}</span>
            <span className="entity-confidence">{(entity.confidence * 100).toFixed(0)}%</span>
          </div>
        ))}
      </div>

      {/* Relationships */}
      {data.relationships && data.relationships.length > 0 && (
        <div className="context-relationships">
          <h3>Relationships ({data.total_relationships})</h3>
          {data.relationships.slice(0, 10).map((rel, idx) => (
            <div key={idx} className="relationship">
              {rel.type}
            </div>
          ))}
        </div>
      )}

      {/* Source Summaries */}
      <div className="context-sources">
        <h3>Data Sources</h3>
        {data.source_summaries && Object.entries(data.source_summaries).map(([source, summary]) => (
          <div key={source} className="source-card">
            <h4>{source}</h4>
            <p>{summary.entity_count} entities</p>
            <p>Categories: {summary.categories.join(', ')}</p>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Usage in Alert Dashboard

```typescript
// frontend/src/pages/AlertDetails.tsx

import { AlertContext } from '../components/AlertContext';

export const AlertDetails = () => {
  const { alertId } = useParams();

  return (
    <div className="alert-details-page">
      <div className="alert-info">
        {/* Alert details here */}
      </div>

      {/* Context Panel */}
      <div className="alert-context-panel">
        <AlertContext alertId={alertId} />
      </div>
    </div>
  );
};
```

---

## Testing

### Test via curl:

```bash
# Get alert context
curl "http://localhost:8005/api/alerts/alert-12345/context?categories=asset,detection&timeout=30"
```

### Test via Redis:

```bash
# Subscribe to response channel
docker-compose exec redis redis-cli
SUBSCRIBE delivery.context.test-123.complete

# In another terminal, publish request
docker-compose exec redis redis-cli
PUBLISH ingestion.pull_context '{"request_id":"test-123","query_type":"ip","query_value":"*************","categories":["asset","detection"]}'
```

### Expected Response:

```json
{
  "request_id": "ctx-a1b2c3d4",
  "query_type": "ip",
  "query_value": "*************",
  "alert": {
    "alert_id": "alert-12345",
    "title": "Port Scan Activity",
    "severity": "medium",
    "timestamp": "2025-10-02T12:34:56Z"
  },
  "entities": [
    {
      "type": "hostname",
      "value": "WORKSTATION-42",
      "source": "crowdstrike",
      "confidence": 1.0,
      "entity_id": "ent-123"
    },
    {
      "type": "ip",
      "value": "*************",
      "source": "crowdstrike",
      "confidence": 1.0,
      "entity_id": "ent-124"
    }
  ],
  "relationships": [
    {
      "source_id": "ent-124",
      "target_id": "ent-123",
      "type": "ip_to_hostname"
    }
  ],
  "source_summaries": {
    "crowdstrike": {
      "entity_count": 6,
      "categories": ["asset", "detection"]
    }
  },
  "total_entities": 6,
  "total_relationships": 4,
  "timestamp": "2025-10-02T12:35:00Z"
}
```

---

## Complete Integration Checklist

✅ **Ingestion Engine**: Plugin system integrated
✅ **Contextualization Engine**: Entity extraction implemented
✅ **Delivery Engine**: (Implement steps above)
  - [ ] Add pending_contexts cache
  - [ ] Add message handlers for context responses
  - [ ] Add HTTP endpoint `/api/alerts/{alert_id}/context`
  - [ ] Implement context request method
  - [ ] Test with curl
✅ **Frontend**: (Create React component)
  - [ ] Create AlertContext component
  - [ ] Integrate into AlertDetails page
  - [ ] Test end-to-end

---

## Summary

This integration allows analysts to:
1. Click alert in dashboard
2. Frontend calls `/api/alerts/{alert_id}/context`
3. Delivery Engine triggers plugin system via Redis
4. Ingestion queries all applicable plugins (CrowdStrike, etc.)
5. Contextualization extracts entities
6. Delivery receives enriched context
7. Frontend displays unified context from all sources

**All in 2-5 seconds automatically!**
