import React, { useEffect, useState } from 'react'
import { useInvestigationStore } from '../stores/investigationStore'
import {
  Globe, User, Server, Hash, FileText, Shield,
  AlertTriangle, Clock, TrendingUp, MapPin, Activity,
  ChevronDown, ChevronRight, Loader
} from 'lucide-react'

interface EntityExplorerProps {
  entityId?: string
  onRelatedEntityClick?: (entityId: string) => void
}

export const EntityExplorer: React.FC<EntityExplorerProps> = ({
  entityId,
  onRelatedEntityClick
}) => {
  const {
    selectedEntity,
    enrichments,
    loadingEntity,
    loadingEnrichment,
    selectEntity,
    loadEntityDetails
  } = useInvestigationStore()

  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['overview', 'enrichment', 'risk'])
  )

  useEffect(() => {
    if (entityId) {
      selectEntity(entityId)
    }
  }, [entityId, selectEntity])

  const toggleSection = (section: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(section)) {
        newSet.delete(section)
      } else {
        newSet.add(section)
      }
      return newSet
    })
  }

  const getEntityIcon = (type: string) => {
    const icons = {
      ip: <Globe className="text-blue-500" size={20} />,
      user: <User className="text-green-500" size={20} />,
      domain: <Server className="text-purple-500" size={20} />,
      hash: <Hash className="text-orange-500" size={20} />,
      file: <FileText className="text-gray-500" size={20} />
    }
    return icons[type as keyof typeof icons] || <Shield size={20} />
  }

  const getRiskColor = (score: number) => {
    if (score >= 80) return 'text-red-600 bg-red-100'
    if (score >= 60) return 'text-orange-600 bg-orange-100'
    if (score >= 40) return 'text-yellow-600 bg-yellow-100'
    return 'text-green-600 bg-green-100'
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString()
  }

  if (loadingEntity) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader className="animate-spin text-blue-500" size={48} />
        <span className="ml-4 text-gray-600">Loading entity details...</span>
      </div>
    )
  }

  if (!selectedEntity) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        <div className="text-center">
          <Shield size={48} className="mx-auto mb-4 text-gray-400" />
          <p>No entity selected</p>
          <p className="text-sm mt-2">Select an entity from the case to explore</p>
        </div>
      </div>
    )
  }

  const entityEnrichments = enrichments.get(selectedEntity.id) || []

  return (
    <div className="flex flex-col h-full bg-white overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {getEntityIcon(selectedEntity.type)}
            <div>
              <h2 className="text-xl font-bold">{selectedEntity.value}</h2>
              <p className="text-blue-100 text-sm">
                Type: {selectedEntity.type.toUpperCase()} | ID: {selectedEntity.id}
              </p>
            </div>
          </div>
          <div className={`px-4 py-2 rounded-lg font-bold ${getRiskColor(selectedEntity.risk_score || 0)}`}>
            Risk: {selectedEntity.risk_score || 0}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">

        {/* Overview Section */}
        <div className="border rounded-lg">
          <button
            onClick={() => toggleSection('overview')}
            className="w-full flex items-center justify-between p-3 hover:bg-gray-50"
          >
            <div className="flex items-center gap-2 font-medium">
              <Activity size={18} />
              Overview
            </div>
            {expandedSections.has('overview') ? <ChevronDown size={18} /> : <ChevronRight size={18} />}
          </button>

          {expandedSections.has('overview') && (
            <div className="p-4 border-t">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-xs text-gray-500">First Seen</label>
                  <p className="font-medium flex items-center gap-1">
                    <Clock size={14} />
                    {selectedEntity.first_seen ? formatTimestamp(selectedEntity.first_seen) : 'Unknown'}
                  </p>
                </div>
                <div>
                  <label className="text-xs text-gray-500">Last Seen</label>
                  <p className="font-medium flex items-center gap-1">
                    <Clock size={14} />
                    {selectedEntity.last_seen ? formatTimestamp(selectedEntity.last_seen) : 'Unknown'}
                  </p>
                </div>
                <div>
                  <label className="text-xs text-gray-500">Occurrences</label>
                  <p className="font-medium flex items-center gap-1">
                    <TrendingUp size={14} />
                    {selectedEntity.occurrence_count || 0}
                  </p>
                </div>
                <div>
                  <label className="text-xs text-gray-500">Tags</label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {(selectedEntity.tags || []).map((tag, idx) => (
                      <span key={idx} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Enrichment Section */}
        <div className="border rounded-lg">
          <button
            onClick={() => toggleSection('enrichment')}
            className="w-full flex items-center justify-between p-3 hover:bg-gray-50"
          >
            <div className="flex items-center gap-2 font-medium">
              <Globe size={18} />
              Enrichment Data
              {loadingEnrichment && <Loader className="animate-spin" size={14} />}
            </div>
            {expandedSections.has('enrichment') ? <ChevronDown size={18} /> : <ChevronRight size={18} />}
          </button>

          {expandedSections.has('enrichment') && (
            <div className="p-4 border-t space-y-3">
              {entityEnrichments.length === 0 ? (
                <p className="text-gray-500 text-sm">No enrichment data available</p>
              ) : (
                entityEnrichments.map((enrichment, idx) => (
                  <div key={idx} className="bg-gray-50 rounded p-3">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium text-sm">{enrichment.source}</h4>
                      <span className="text-xs text-gray-500">
                        {formatTimestamp(enrichment.timestamp)}
                      </span>
                    </div>
                    {enrichment.source === 'geoip' && (
                      <div className="space-y-1 text-sm">
                        <p className="flex items-center gap-2">
                          <MapPin size={14} />
                          {enrichment.data.city}, {enrichment.data.country}
                        </p>
                        <p>ISP: {enrichment.data.isp}</p>
                        <p>ASN: {enrichment.data.asn}</p>
                      </div>
                    )}
                    {enrichment.source === 'threat_intel' && (
                      <div className="space-y-1 text-sm">
                        <p className="flex items-center gap-2">
                          <AlertTriangle size={14} className="text-red-500" />
                          Reputation: {enrichment.data.reputation}
                        </p>
                        <p>Malware Family: {enrichment.data.malware_family || 'None'}</p>
                        <p>Threat Score: {enrichment.data.threat_score}/100</p>
                      </div>
                    )}
                    {enrichment.source === 'asset_inventory' && (
                      <div className="space-y-1 text-sm">
                        <p>Hostname: {enrichment.data.hostname}</p>
                        <p>Department: {enrichment.data.department}</p>
                        <p>Owner: {enrichment.data.owner}</p>
                        <p>Criticality: {enrichment.data.criticality}</p>
                      </div>
                    )}
                    {/* Generic display for other sources */}
                    {!['geoip', 'threat_intel', 'asset_inventory'].includes(enrichment.source) && (
                      <pre className="text-xs bg-white p-2 rounded overflow-x-auto">
                        {JSON.stringify(enrichment.data, null, 2)}
                      </pre>
                    )}
                  </div>
                ))
              )}
            </div>
          )}
        </div>

        {/* Risk Analysis Section */}
        <div className="border rounded-lg">
          <button
            onClick={() => toggleSection('risk')}
            className="w-full flex items-center justify-between p-3 hover:bg-gray-50"
          >
            <div className="flex items-center gap-2 font-medium">
              <AlertTriangle size={18} />
              Risk Analysis
            </div>
            {expandedSections.has('risk') ? <ChevronDown size={18} /> : <ChevronRight size={18} />}
          </button>

          {expandedSections.has('risk') && (
            <div className="p-4 border-t">
              <div className="space-y-3">
                {/* Risk Score Breakdown */}
                <div className="bg-gray-50 rounded p-3">
                  <h4 className="font-medium text-sm mb-2">Risk Score Breakdown</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Threat Intelligence</span>
                      <div className="flex items-center gap-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div className="bg-red-500 h-2 rounded-full" style={{ width: '75%' }}></div>
                        </div>
                        <span className="text-sm font-medium">75</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Behavioral Anomaly</span>
                      <div className="flex items-center gap-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div className="bg-orange-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                        </div>
                        <span className="text-sm font-medium">60</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Asset Criticality</span>
                      <div className="flex items-center gap-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '40%' }}></div>
                        </div>
                        <span className="text-sm font-medium">40</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Risk Indicators */}
                <div className="bg-gray-50 rounded p-3">
                  <h4 className="font-medium text-sm mb-2">Risk Indicators</h4>
                  <div className="space-y-1 text-sm">
                    <p className="flex items-center gap-2">
                      <AlertTriangle size={14} className="text-red-500" />
                      Found in 3 threat feeds
                    </p>
                    <p className="flex items-center gap-2">
                      <AlertTriangle size={14} className="text-orange-500" />
                      Unusual geographic location
                    </p>
                    <p className="flex items-center gap-2">
                      <AlertTriangle size={14} className="text-yellow-500" />
                      First time connection to critical asset
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Historical Baseline Section */}
        <div className="border rounded-lg">
          <button
            onClick={() => toggleSection('baseline')}
            className="w-full flex items-center justify-between p-3 hover:bg-gray-50"
          >
            <div className="flex items-center gap-2 font-medium">
              <TrendingUp size={18} />
              Historical Baseline
            </div>
            {expandedSections.has('baseline') ? <ChevronDown size={18} /> : <ChevronRight size={18} />}
          </button>

          {expandedSections.has('baseline') && (
            <div className="p-4 border-t">
              <div className="space-y-3 text-sm">
                <div>
                  <label className="text-xs text-gray-500">Normal Activity Hours</label>
                  <p className="font-medium">Mon-Fri: 8:00 AM - 6:00 PM EST</p>
                </div>
                <div>
                  <label className="text-xs text-gray-500">Average Daily Events</label>
                  <p className="font-medium">247 events/day</p>
                </div>
                <div>
                  <label className="text-xs text-gray-500">Common Destinations</label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    <span className="px-2 py-1 bg-gray-100 text-xs rounded">office365.com</span>
                    <span className="px-2 py-1 bg-gray-100 text-xs rounded">github.com</span>
                    <span className="px-2 py-1 bg-gray-100 text-xs rounded">slack.com</span>
                  </div>
                </div>
                <div>
                  <label className="text-xs text-gray-500">Deviation from Baseline</label>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-orange-500 h-2 rounded-full" style={{ width: '65%' }}></div>
                    </div>
                    <span className="font-medium text-orange-600">65% anomaly</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Action Footer */}
      <div className="border-t p-3 flex gap-2">
        <button className="flex-1 px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm font-medium">
          Investigate Further
        </button>
        <button className="px-3 py-2 border rounded hover:bg-gray-50 text-sm font-medium">
          Add to Watchlist
        </button>
        <button className="px-3 py-2 border rounded hover:bg-gray-50 text-sm font-medium">
          Export Report
        </button>
      </div>
    </div>
  )
}

export default EntityExplorer