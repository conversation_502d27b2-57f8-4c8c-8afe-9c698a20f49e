# SIEMLess v2.0 - Master TODO Tracker

## Last Updated: October 3, 2025

---

## 🔥 CURRENT SESSION - Architectural Fix & CTI Formats

### ✅ Completed
- [x] Document all structured CTI formats (10 formats identified)
- [x] Move rule_deployment_service.py from Backend to Ingestion Engine
- [x] Integrate deployment service into Ingestion Engine
- [x] Add HTTP endpoints and message handlers to Ingestion
- [x] Document architectural fix and new workflow

### 🚧 In Progress
- [ ] **Test architectural fix: CTI → Rule → Deploy to Elastic** (CURRENT TASK)

### 📋 Next Up (Priority Order)
1. [ ] Remove old deployment endpoints from Backend Engine (cleanup)
2. [ ] Update Backend Engine to publish `backend.rule.approved` when rules created
3. [ ] Add database schema migrations (deployment tracking columns)
4. [ ] Create STIX pattern parser (P0 - blocks OpenCTI enterprise)
5. [ ] Build AI enhancement layer in Intelligence Engine (10% edge cases)

---

## 🎨 FRONTEND WIDGETS BACKLOG

### P0 Widgets (Week 1-2) - Completed: 3/4

#### ✅ Completed
- [x] Dashboard Overview Widget ([frontend/src/widgets/DashboardOverview.tsx](frontend/src/widgets/DashboardOverview.tsx:1))
  - Shows: Detection confidence, critical alerts, coverage score
  - APIs: `/api/dashboard/stats`, `/api/alerts`, `/api/detection/fidelity`
  - Status: **OPERATIONAL**

- [x] Alert Queue Widget ([frontend/src/widgets/AlertQueue.tsx](frontend/src/widgets/AlertQueue.tsx:1))
  - Shows: Real-time alert queue with three-layer enrichment
  - APIs: `/api/alerts`, `/api/entities/{id}/enrich`
  - Status: **OPERATIONAL**

- [x] CTI Plugin Status Widget ([frontend/src/widgets/CTIPluginStatus.tsx](frontend/src/widgets/CTIPluginStatus.tsx:1))
  - Shows: OTX, ThreatFox, CrowdStrike, OpenCTI status
  - APIs: `/api/cti/status`, `/api/cti/plugins/{source}/update`
  - Status: **OPERATIONAL**

#### 🚧 Pending
- [ ] **Entity Explorer Widget** (P0 - Medium complexity)
  - User Story: "As an analyst, I need to search for any entity (IP, user, host) and see its complete context"
  - APIs Needed:
    - `GET /api/entities/search?q={query}&type={ip|user|host|hash}`
    - `GET /api/entities/{id}/relationships`
    - `GET /api/entities/{id}/timeline`
  - Components:
    - `<EntitySearchBar />` - Autocomplete search
    - `<EntityCard />` - Entity summary with enrichment layers
    - `<RelationshipMini />` - Connected entities preview
  - Build Time: 2-3 days

### P1 Widgets (Week 3-4) - Not Started: 0/4

- [ ] **Detection Fidelity Calculator** (P1 - High complexity)
  - User Story: "As a CISO, I need to see detection fidelity scores for each MITRE technique based on my log sources"
  - APIs Needed:
    - `GET /api/detection/fidelity` (EXISTS)
    - `GET /api/detection/fidelity/technique/{technique_id}` (NEW)
    - `GET /api/log-sources/coverage` (NEW)
  - Components:
    - `<FidelityHeatmap />` - MITRE matrix with color-coded scores
    - `<TechniqueDetail />` - Detailed breakdown per technique
    - `<LogSourceImpact />` - What-if analysis
  - Build Time: 4-5 days

- [ ] **Log Source Quality Matrix** (P1 - Medium complexity)
  - User Story: "As an executive, I need to see which log sources provide the best detection value"
  - APIs: Already exist in Backend Engine
  - Build Time: 2-3 days

- [ ] **Coverage Simulation Tool** (P1 - High complexity)
  - User Story: "As a detection engineer, I need to simulate coverage if I add/remove log sources"
  - Build Time: 4-5 days

- [ ] **Entity Details Panel** (P1 - High complexity)
  - User Story: "As an analyst investigating an alert, I need a sidebar with full entity context"
  - Build Time: 3-4 days

### P2 Widgets (Week 5-6) - Not Started: 0/5

- [ ] Investigation Dashboard
- [ ] MITRE ATT&CK Heatmap
- [ ] Relationship Graph
- [ ] Timeline Analysis
- [ ] Pending Rules Widget Enhancement (add deployment buttons)

### P3 Widgets (Week 7-8) - Not Started: 0/3

- [ ] Pattern Library
- [ ] Rule Performance
- [ ] Cost Analytics

---

## 🏗️ BACKEND COMPLETENESS

### CTI Integration

#### ✅ Operational Sources (4)
- [x] OTX (AlienVault) - JSON format
- [x] ThreatFox (abuse.ch) - JSON format
- [x] CrowdStrike Intel - JSON format
- [x] Sigma Rules (Community Engine) - YAML format

#### ⚠️ Partial Implementation (2)
- [ ] **OpenCTI / STIX 2.1** - Plugin exists, **MISSING: STIX pattern parser**
  - Blocks: Enterprise CTI platform integration
  - Priority: **P0**
  - Effort: 2-3 days
  - Files to Create:
    - `engines/ingestion/stix_pattern_parser.py`
    - `engines/backend/stix_to_sigma_converter.py`
    - `engines/backend/stix_to_kql_converter.py`

- [ ] **CVE/NVD** - Used as indicator type, not as feed source
  - Priority: P2
  - Effort: 2 days
  - Files to Create:
    - `engines/ingestion/nvd_plugin.py`

#### ❌ Not Implemented (4)
- [ ] MISP (Malware Information Sharing Platform) - Priority: P1
- [ ] TAXII 2.1 (STIX transport) - Priority: P1
- [ ] Snort/Suricata Rules - Priority: P2
- [ ] YARA Rules (consumption, not just generation) - Priority: P3

### Rule Deployment

#### ✅ Completed
- [x] Elastic Security deployment (full implementation)
- [x] Moved to Ingestion Engine (architectural fix)
- [x] HTTP API endpoints
- [x] Redis pub/sub integration
- [x] Database status tracking

#### 🚧 Needs Testing
- [ ] End-to-end flow: CTI → Rule → Deploy to Elastic
- [ ] Bulk deployment
- [ ] Rule updates in Elastic
- [ ] Rule deletion from Elastic

#### ❌ Stubbed (Future)
- [ ] Splunk deployment
- [ ] Microsoft Sentinel deployment
- [ ] IBM QRadar deployment

### AI Enhancement Layer

#### ⚠️ Designed But Not Implemented
- [ ] **AI Rule Enhancer** (10% edge cases)
  - Handles: Obfuscation, context tuning, novel patterns
  - Integration: Intelligence Engine
  - Priority: P1
  - Effort: 2-3 days
  - File to Create: `engines/intelligence/ai_rule_enhancer.py`

---

## 🗄️ DATABASE SCHEMA UPDATES NEEDED

### Detection Rules Table - Deployment Tracking

```sql
-- Run these migrations:
ALTER TABLE detection_rules ADD COLUMN IF NOT EXISTS deployed_to_elastic BOOLEAN DEFAULT false;
ALTER TABLE detection_rules ADD COLUMN IF NOT EXISTS elastic_rule_id TEXT;
ALTER TABLE detection_rules ADD COLUMN IF NOT EXISTS elastic_version INTEGER DEFAULT 1;
ALTER TABLE detection_rules ADD COLUMN IF NOT EXISTS deployed_to_splunk BOOLEAN DEFAULT false;
ALTER TABLE detection_rules ADD COLUMN IF NOT EXISTS splunk_rule_id TEXT;
ALTER TABLE detection_rules ADD COLUMN IF NOT EXISTS deployed_to_sentinel BOOLEAN DEFAULT false;
ALTER TABLE detection_rules ADD COLUMN IF NOT EXISTS sentinel_rule_id TEXT;
ALTER TABLE detection_rules ADD COLUMN IF NOT EXISTS deployed_to_qradar BOOLEAN DEFAULT false;
ALTER TABLE detection_rules ADD COLUMN IF NOT EXISTS qradar_rule_id TEXT;
ALTER TABLE detection_rules ADD COLUMN IF NOT EXISTS last_deployed_at TIMESTAMP;

-- Indexes
CREATE INDEX IF NOT EXISTS idx_detection_rules_deployed_elastic ON detection_rules(deployed_to_elastic);
CREATE INDEX IF NOT EXISTS idx_detection_rules_deployed_splunk ON detection_rules(deployed_to_splunk);
CREATE INDEX IF NOT EXISTS idx_detection_rules_deployed_sentinel ON detection_rules(deployed_to_sentinel);
CREATE INDEX IF NOT EXISTS idx_detection_rules_deployed_qradar ON detection_rules(deployed_to_qradar);
```

**Status**: ❌ Not applied yet

---

## 🧪 TESTING REQUIREMENTS

### Integration Tests Needed

- [ ] **CTI → Rule → Deploy Workflow**
  1. Trigger CTI update (OTX)
  2. Verify rule created in Backend
  3. Verify `backend.rule.approved` published
  4. Verify Ingestion deploys to Elastic
  5. Verify deployment status updated in database
  6. Verify `ingestion.rule.deployed` published

- [ ] **HTTP API Endpoints**
  - [ ] `POST /api/rules/{id}/deploy/elastic`
  - [ ] `POST /api/rules/{id}/deploy/{target}`
  - [ ] `POST /api/rules/deploy/bulk`
  - [ ] `PUT /api/rules/{id}/deployment/elastic/{elastic_rule_id}`
  - [ ] `DELETE /api/rules/deployment/elastic/{elastic_rule_id}`

- [ ] **Widget Integration Tests**
  - [ ] Dashboard Overview Widget data refresh
  - [ ] Alert Queue Widget real-time updates
  - [ ] CTI Plugin Status Widget manual update trigger
  - [ ] Entity Explorer Widget search and context

---

## 📚 DOCUMENTATION PRIORITIES

### ✅ Complete
- [x] CTI_FORMATS_AND_DETERMINISTIC_CONVERSION.md
- [x] ARCHITECTURAL_FIX_RULE_DEPLOYMENT.md
- [x] WIDGET_CATALOG.md (widget specs)
- [x] COMPLETE_API_REFERENCE.md (backend APIs)
- [x] INTEGRATION_PATTERNS.md (frontend patterns)

### 🚧 Needs Updates
- [ ] Update CLAUDE.md with architectural fix
- [ ] Update API_DOCUMENTATION.md (remove Backend deployment endpoints, add Ingestion)
- [ ] Create STIX_PATTERN_PARSER_SPEC.md (when implementing P0 task)

---

## 🎯 8-WEEK BUILD PLAN TRACKER

### Week 1-2: Foundation (P0 Widgets)
- ✅ Dashboard Overview Widget
- ✅ Alert Queue Widget
- ✅ CTI Plugin Status Widget
- 🚧 Entity Explorer Widget (50% - spec complete, implementation pending)

### Week 3-4: Executive Visibility (P1 Widgets)
- ⏳ Detection Fidelity Calculator (0%)
- ⏳ Log Source Quality Matrix (0%)
- ⏳ Coverage Simulation Tool (0%)
- ⏳ Entity Details Panel (0%)

### Week 5-6: Analyst Tools (P2 Widgets)
- ⏳ Investigation Dashboard (0%)
- ⏳ MITRE ATT&CK Heatmap (0%)
- ⏳ Relationship Graph (0%)
- ⏳ Timeline Analysis (0%)

### Week 7-8: Engineer Tools (P3 Widgets)
- ⏳ Pattern Library (0%)
- ⏳ Rule Performance (0%)
- ⏳ Cost Analytics (0%)

**Current Status**: Week 2 of 8 (25% complete on widgets)

---

## 🚨 BLOCKERS & DEPENDENCIES

### High Priority Blockers

1. **STIX Pattern Parser** (P0)
   - Blocks: Enterprise CTI integration (OpenCTI)
   - Affects: 90% deterministic conversion target
   - Dependencies: None
   - **Status**: Ready to implement

2. **Database Schema Migrations** (P0)
   - Blocks: Rule deployment status tracking
   - Affects: Deployment workflow testing
   - Dependencies: None
   - **Status**: SQL ready, needs execution

3. **Backend.rule.approved Publishing** (P0)
   - Blocks: Automatic deployment workflow
   - Affects: End-to-end CTI → Deploy testing
   - Dependencies: None
   - **Status**: Need to add to Backend Engine

### Medium Priority Dependencies

4. **Entity Search API** (P1)
   - Blocks: Entity Explorer Widget (P0 widget)
   - Endpoint: `GET /api/entities/search`
   - **Status**: Backend API missing

5. **AI Rule Enhancer** (P1)
   - Blocks: 10% edge case handling
   - Affects: Novel threat detection
   - Dependencies: Intelligence Engine integration
   - **Status**: Architecture designed, needs implementation

---

## 📋 SESSION HANDOFF NOTES

### What We Just Completed (Oct 3, 2025)
1. ✅ Documented all 10 CTI formats with deterministic conversion strategy
2. ✅ Fixed critical architectural flaw (rule deployment moved to Ingestion Engine)
3. ✅ Implemented complete Elastic Security deployment service
4. ✅ Added Redis pub/sub workflow for rule approval → deployment
5. ✅ Created comprehensive architectural documentation

### What to Do Next Session
1. **Test the architectural fix** (PRIORITY 1)
   - Run end-to-end: CTI update → Rule generation → Elastic deployment
   - Verify all 5 HTTP endpoints work
   - Confirm database status updates

2. **Apply database migrations** (PRIORITY 2)
   - Run SQL from this document
   - Verify columns added correctly

3. **Implement STIX parser** (PRIORITY 3)
   - Creates immediate enterprise value
   - Unlocks OpenCTI integration

4. **Build Entity Explorer Widget** (PRIORITY 4)
   - Last P0 widget to complete Week 1-2 milestone
   - Need to create Entity Search API first

### Quick Start Commands
```bash
# Test CTI update
curl -X POST http://localhost:8003/cti/manual_update \
  -H "Content-Type: application/json" \
  -d '{"source": "threatfox", "since_days": 1}'

# Check rules created
curl http://localhost:8002/api/rules | jq

# Test manual deployment
curl -X POST http://localhost:8003/api/rules/RULE_ID/deploy/elastic

# Check Elastic Security
curl -u elastic:password \
  https://your-kibana:5601/api/detection_engine/rules/_find
```

---

## 📊 METRICS & SUCCESS CRITERIA

### CTI Integration
- ✅ 4/10 sources operational (40%)
- ⚠️ STIX parser missing (blocks enterprise adoption)
- 🎯 Target: 8/10 sources operational (80%)

### Deterministic Conversion
- ✅ Simple IOCs: 100% deterministic
- ⚠️ STIX patterns: 0% (parser missing)
- 🎯 Target: 90% deterministic, 10% AI enhancement

### Frontend Widgets
- ✅ P0 Widgets: 3/4 complete (75%)
- ⏳ P1 Widgets: 0/4 complete (0%)
- 🎯 Target: All P0 + P1 by Week 4

### Rule Deployment
- ✅ Architecture: Fixed (100%)
- ✅ Elastic: Implemented (100%)
- ⏳ Testing: Not started (0%)
- 🎯 Target: Tested + operational

---

**END OF TRACKER - Restore this list when resuming work**
