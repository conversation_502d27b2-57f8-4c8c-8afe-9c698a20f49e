import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { AgGridReact } from 'ag-grid-react'
import { ColDef, GridApi, GridReadyEvent, ICellRendererParams } from 'ag-grid-community'
import 'ag-grid-community/styles/ag-grid.css'
import 'ag-grid-community/styles/ag-theme-alpine.css'
import {
  Sparkles, CheckCircle, XCircle, AlertTriangle,
  Eye, Edit3, TestTube, ThumbsUp, ThumbsDown,
  Code, Brain, Loader
} from 'lucide-react'
import { patternAPI } from '../api/client'

interface Pattern {
  id: string
  source_log: string
  pattern_regex: string
  confidence_score: number
  ai_models_consensus: string[]
  category: string
  entity_mappings: Record<string, string>
  created_at: string
  status: 'pending' | 'approved' | 'rejected' | 'testing'
  test_results?: {
    matches: number
    false_positives: number
    performance_ms: number
  }
}

// Confidence score renderer
const ConfidenceRenderer: React.FC<ICellRendererParams> = ({ value }) => {
  const percentage = Math.round(value * 100)
  const color = percentage > 90 ? 'bg-green-500' : percentage > 70 ? 'bg-yellow-500' : 'bg-red-500'

  return (
    <div className="flex items-center gap-2">
      <div className="flex-1 bg-gray-200 rounded-full h-2">
        <div
          className={`${color} h-2 rounded-full transition-all duration-300`}
          style={{ width: `${percentage}%` }}
        />
      </div>
      <span className="text-xs font-medium">{percentage}%</span>
    </div>
  )
}

// AI consensus renderer
const ConsensusRenderer: React.FC<ICellRendererParams> = ({ value }) => {
  const models = value as string[]
  const consensusLevel = models.length

  return (
    <div className="flex items-center gap-2">
      <Brain size={14} className={consensusLevel >= 3 ? 'text-green-500' : 'text-yellow-500'} />
      <span className="text-sm">
        {consensusLevel}/5 models agree
      </span>
      <div className="flex gap-1">
        {models.map((model, idx) => (
          <span key={idx} className="px-1 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">
            {model}
          </span>
        ))}
      </div>
    </div>
  )
}

// Status renderer
const StatusRenderer: React.FC<ICellRendererParams> = ({ value }) => {
  const statusConfig = {
    pending: { icon: AlertTriangle, color: 'text-yellow-600 bg-yellow-100' },
    approved: { icon: CheckCircle, color: 'text-green-600 bg-green-100' },
    rejected: { icon: XCircle, color: 'text-red-600 bg-red-100' },
    testing: { icon: TestTube, color: 'text-blue-600 bg-blue-100' }
  }

  const config = statusConfig[value as keyof typeof statusConfig]
  const Icon = config.icon

  return (
    <div className={`flex items-center gap-1 px-2 py-1 rounded ${config.color}`}>
      <Icon size={14} />
      <span className="text-xs font-medium capitalize">{value}</span>
    </div>
  )
}

// Action buttons renderer
const ActionRenderer: React.FC<ICellRendererParams> = ({ data, context }) => {
  return (
    <div className="flex gap-1">
      <button
        onClick={() => context.onView(data)}
        className="p-1 hover:bg-gray-100 rounded"
        title="View Details"
      >
        <Eye size={16} />
      </button>
      <button
        onClick={() => context.onEdit(data)}
        className="p-1 hover:bg-gray-100 rounded"
        title="Edit Pattern"
      >
        <Edit3 size={16} />
      </button>
      <button
        onClick={() => context.onTest(data)}
        className="p-1 hover:bg-gray-100 rounded"
        title="Test Pattern"
      >
        <TestTube size={16} />
      </button>
      <button
        onClick={() => context.onApprove(data)}
        className="p-1 hover:bg-green-100 rounded text-green-600"
        title="Approve"
      >
        <ThumbsUp size={16} />
      </button>
      <button
        onClick={() => context.onReject(data)}
        className="p-1 hover:bg-red-100 rounded text-red-600"
        title="Reject"
      >
        <ThumbsDown size={16} />
      </button>
    </div>
  )
}

export const CrystallizationQueue: React.FC = () => {
  const [rowData, setRowData] = useState<Pattern[]>([])
  const [gridApi, setGridApi] = useState<GridApi | null>(null)
  const [selectedPattern, setSelectedPattern] = useState<Pattern | null>(null)
  const [showTestModal, setShowTestModal] = useState(false)
  const [testLog, setTestLog] = useState('')
  const [testResults, setTestResults] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  // Column definitions
  const columnDefs: ColDef[] = useMemo(() => [
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      cellRenderer: StatusRenderer,
      filter: true
    },
    {
      field: 'confidence_score',
      headerName: 'Confidence',
      width: 150,
      cellRenderer: ConfidenceRenderer,
      sortable: true,
      sort: 'desc'
    },
    {
      field: 'category',
      headerName: 'Category',
      width: 120,
      filter: true
    },
    {
      field: 'source_log',
      headerName: 'Source Log Sample',
      flex: 1,
      minWidth: 300,
      cellRenderer: (params: ICellRendererParams) => (
        <div className="font-mono text-xs p-1 bg-gray-50 rounded overflow-hidden text-ellipsis">
          {params.value}
        </div>
      )
    },
    {
      field: 'ai_models_consensus',
      headerName: 'AI Consensus',
      width: 250,
      cellRenderer: ConsensusRenderer
    },
    {
      field: 'created_at',
      headerName: 'Discovered',
      width: 150,
      valueFormatter: (params) => {
        return new Date(params.value).toLocaleString()
      },
      sortable: true
    },
    {
      headerName: 'Actions',
      width: 180,
      cellRenderer: ActionRenderer,
      cellRendererParams: {
        onView: handleView,
        onEdit: handleEdit,
        onTest: handleTest,
        onApprove: handleApprove,
        onReject: handleReject
      }
    }
  ], [])

  // Default column properties
  const defaultColDef = useMemo(() => ({
    resizable: true,
    sortable: true,
    filter: false
  }), [])

  // Context for action handlers
  const gridContext = useMemo(() => ({
    onView: handleView,
    onEdit: handleEdit,
    onTest: handleTest,
    onApprove: handleApprove,
    onReject: handleReject
  }), [])

  // Load patterns from API
  useEffect(() => {
    const fetchPatterns = async () => {
      try {
        // For now, patterns are not exposed via API, so use mock data
        // TODO: Add /api/patterns endpoint to delivery engine
        const mockResponse = { data: { patterns: [] } }
        if (mockResponse.data && mockResponse.data.patterns && mockResponse.data.patterns.length > 0) {
          setRowData(mockResponse.data.patterns)
        } else {
          // If no real data, generate mock data for demo
          const mockPatterns: Pattern[] = Array.from({ length: 20 }, (_, i) => ({
            id: `pattern-${i}`,
            source_log: `2024-01-${String(i + 1).padStart(2, '0')} 10:30:45 ERROR: Authentication failed for user admin from IP 192.168.1.${100 + i}`,
            pattern_regex: `(?P<timestamp>\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}) (?P<level>\\w+): (?P<message>.+) from IP (?P<ip>\\d+\\.\\d+\\.\\d+\\.\\d+)`,
            confidence_score: 0.6 + Math.random() * 0.4,
            ai_models_consensus: ['GPT-4', 'Claude', Math.random() > 0.5 ? 'Gemini' : ''].filter(Boolean),
            category: ['security', 'authentication', 'network', 'system'][Math.floor(Math.random() * 4)],
            entity_mappings: {
              ip: 'source_ip',
              user: 'username',
              timestamp: 'event_time'
            },
            created_at: new Date(Date.now() - Math.random() * 7 * 86400000).toISOString(),
            status: ['pending', 'testing', 'approved', 'rejected'][Math.floor(Math.random() * 4)] as Pattern['status']
          }))
          setRowData(mockPatterns)
        }
      } catch (error) {
        console.error('Failed to fetch patterns:', error)
        // Fall back to mock data
        const mockPatterns: Pattern[] = Array.from({ length: 20 }, (_, i) => ({
          id: `pattern-${i}`,
          source_log: `2024-01-${String(i + 1).padStart(2, '0')} 10:30:45 ERROR: Authentication failed for user admin from IP 192.168.1.${100 + i}`,
          pattern_regex: `(?P<timestamp>\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}) (?P<level>\\w+): (?P<message>.+) from IP (?P<ip>\\d+\\.\\d+\\.\\d+\\.\\d+)`,
          confidence_score: 0.6 + Math.random() * 0.4,
          ai_models_consensus: ['GPT-4', 'Claude', Math.random() > 0.5 ? 'Gemini' : ''].filter(Boolean),
          category: ['security', 'authentication', 'network', 'system'][Math.floor(Math.random() * 4)],
          entity_mappings: {
            ip: 'source_ip',
            user: 'username',
            timestamp: 'event_time'
          },
          created_at: new Date(Date.now() - Math.random() * 7 * 86400000).toISOString(),
          status: ['pending', 'testing', 'approved', 'rejected'][Math.floor(Math.random() * 4)] as Pattern['status']
        }))
        setRowData(mockPatterns)
      }
    }

    fetchPatterns()
  }, [])

  // Action handlers
  function handleView(pattern: Pattern) {
    setSelectedPattern(pattern)
  }

  function handleEdit(pattern: Pattern) {
    // Open edit modal
    console.log('Edit pattern:', pattern)
  }

  function handleTest(pattern: Pattern) {
    setSelectedPattern(pattern)
    setShowTestModal(true)
  }

  async function handleApprove(pattern: Pattern) {
    // Update pattern status
    const updatedData = rowData.map(p =>
      p.id === pattern.id ? { ...p, status: 'approved' as const } : p
    )
    setRowData(updatedData)
  }

  function handleReject(pattern: Pattern) {
    // Update pattern status
    const updatedData = rowData.map(p =>
      p.id === pattern.id ? { ...p, status: 'rejected' as const } : p
    )
    setRowData(updatedData)
  }

  // Test pattern
  const runPatternTest = async () => {
    if (!selectedPattern || !testLog) return

    setLoading(true)
    try {
      const response = await patternAPI.testPattern(selectedPattern.pattern_regex, testLog)
      setTestResults(response.data)
    } catch (error) {
      console.error('Test failed:', error)
    } finally {
      setLoading(false)
    }
  }

  // Grid ready handler
  const onGridReady = useCallback((params: GridReadyEvent) => {
    setGridApi(params.api)
  }, [])

  // Calculate statistics
  const stats = useMemo(() => {
    const pending = rowData.filter(p => p.status === 'pending').length
    const avgConfidence = rowData.reduce((sum, p) => sum + p.confidence_score, 0) / rowData.length
    const highConfidence = rowData.filter(p => p.confidence_score > 0.9).length

    return { pending, avgConfidence, highConfidence }
  }, [rowData])

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-lg font-semibold flex items-center gap-2">
              <Sparkles size={20} className="text-yellow-500" />
              Pattern Crystallization Queue
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Review and approve AI-discovered patterns for the library
            </p>
          </div>
          <div className="flex gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
              <p className="text-xs text-gray-600">Pending Review</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold">{(stats.avgConfidence * 100).toFixed(0)}%</p>
              <p className="text-xs text-gray-600">Avg Confidence</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{stats.highConfidence}</p>
              <p className="text-xs text-gray-600">High Confidence</p>
            </div>
          </div>
        </div>
      </div>

      {/* AG-Grid */}
      <div className="flex-1 ag-theme-alpine">
        <AgGridReact
          rowData={rowData}
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          context={gridContext}
          onGridReady={onGridReady}
          rowSelection="single"
          animateRows={true}
          pagination={true}
          paginationPageSize={10}
          rowHeight={45}
        />
      </div>

      {/* Test Modal */}
      {showTestModal && selectedPattern && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-3/4 max-w-4xl max-h-[80vh] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">Test Pattern</h3>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">Pattern Regex:</label>
              <code className="block p-2 bg-gray-100 rounded text-xs">
                {selectedPattern.pattern_regex}
              </code>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">Test Log:</label>
              <textarea
                className="w-full p-2 border rounded h-32 font-mono text-sm"
                value={testLog}
                onChange={(e) => setTestLog(e.target.value)}
                placeholder="Paste a log line to test the pattern..."
              />
            </div>

            <button
              onClick={runPatternTest}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mb-4"
              disabled={loading}
            >
              {loading ? <Loader className="animate-spin" size={16} /> : 'Run Test'}
            </button>

            {testResults && (
              <div className="mb-4 p-4 bg-gray-50 rounded">
                <h4 className="font-medium mb-2">Test Results:</h4>
                <pre className="text-xs overflow-x-auto">
                  {JSON.stringify(testResults, null, 2)}
                </pre>
              </div>
            )}

            <div className="flex justify-end gap-2">
              <button
                onClick={() => {
                  setShowTestModal(false)
                  setTestLog('')
                  setTestResults(null)
                }}
                className="px-4 py-2 border rounded hover:bg-gray-50"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CrystallizationQueue