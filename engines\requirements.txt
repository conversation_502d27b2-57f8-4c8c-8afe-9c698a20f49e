# SIEMLess v2.0 - Base Engine Requirements
# Common dependencies for all 5 engines

# Core Python packages
asyncio-mqtt==0.11.1
python-dotenv==1.0.0

# Database
asyncpg==0.29.0

# Redis
redis==4.6.0
hiredis==2.2.3

# Web & API
aiohttp==3.8.5
fastapi==0.103.1
uvicorn==0.23.2
pydantic==2.3.0

# Data processing
pandas==2.1.0
numpy==1.24.3
jsonschema==4.19.0

# Logging & Monitoring
structlog==23.1.0
prometheus-client==0.17.1

# Security
cryptography==41.0.4
bcrypt==4.0.1

# Utilities
uuid==1.30
requests==2.31.0
python-dateutil==2.8.2

# Testing
pytest==7.4.2
pytest-asyncio==0.21.1
pytest-mock==3.11.1

# Development
black==23.7.0
flake8==6.0.0
mypy==1.5.1