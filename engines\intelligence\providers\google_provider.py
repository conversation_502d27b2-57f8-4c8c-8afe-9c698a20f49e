"""
Google AI Provider - Gemini and Gemma models
Implements BaseAIProvider for Google's AI models
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, Any
import logging

from .base_provider import BaseAIProvider, AIResponse


class GoogleProvider(BaseAIProvider):
    """
    Provider for Google AI models (<PERSON>, Gemma)

    Supports:
    - gemma-3-27b-it (FREE)
    - gemini-2.5-flash
    - gemini-2.5-pro
    """

    def _get_provider_name(self) -> str:
        """Return provider name"""
        return 'google'

    async def call(
        self,
        model: str,
        prompt: str,
        temperature: float = 0.1,
        max_tokens: int = 8192,
        **kwargs
    ) -> AIResponse:
        """
        Call Google AI model

        Args:
            model: Model name (gemma-3-27b, gemini-2.5-flash, gemini-2.5-pro)
            prompt: The prompt text
            temperature: Temperature (0.0-1.0)
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters

        Returns:
            AIResponse

        Raises:
            Exception: If call fails
        """
        from google import genai
        from google.genai import types

        start_time = time.time()

        try:
            # Get API key
            api_key = self.get_credential()
            if not api_key:
                raise Exception(f"No API key found for {self.provider_name}")

            # Map model names to actual API names
            model_mapping = {
                "gemma-27b": "gemma-3-27b-it",
                "gemma-3-27b": "gemma-3-27b-it",
                "gemini-flash": "gemini-2.5-flash",
                "gemini-2.5-flash": "gemini-2.5-flash",
                "gemini-pro": "gemini-2.5-pro",
                "gemini-2.5-pro": "gemini-2.5-pro"
            }
            actual_model = model_mapping.get(model, model)

            # Run sync client in executor (google-genai is sync only)
            def _sync_call():
                client = genai.Client(api_key=api_key)

                contents = [
                    types.Content(
                        role="user",
                        parts=[types.Part.from_text(text=prompt)]
                    )
                ]

                config = types.GenerateContentConfig(
                    temperature=temperature,
                    max_output_tokens=max_tokens
                )

                response = client.models.generate_content(
                    model=actual_model,
                    contents=contents,
                    config=config
                )

                return response

            loop = asyncio.get_event_loop()
            raw_response = await loop.run_in_executor(None, _sync_call)

            # Extract content
            content = raw_response.text if raw_response and raw_response.text else ""

            # Handle safety filter
            if not content:
                content = "Response filtered by safety systems"
                self.logger.warning(f"Google safety filter triggered for model {model}")

            # Calculate latency
            latency_ms = (time.time() - start_time) * 1000

            # Parse response into standardized format
            return AIResponse(
                model=model,
                content=content,
                confidence=0.85,
                reasoning=f"Google {actual_model} analysis",
                input_tokens=0,  # google-genai doesn't provide token counts easily
                output_tokens=0,
                total_tokens=0,
                provider=self.provider_name,
                request_id="",
                created_at=datetime.now(),
                latency_ms=latency_ms,
                raw_response={
                    'model': actual_model,
                    'text': content
                },
                error=None,
                success=True
            )

        except Exception as e:
            # Mark key as failed if it's a credential issue
            error_str = str(e).lower()
            if 'api_key' in error_str or 'unauthorized' in error_str or 'forbidden' in error_str:
                api_key = self.get_credential()
                if api_key:
                    self.credential_manager.mark_key_failed(self.provider_name, api_key)

            latency_ms = (time.time() - start_time) * 1000

            self.logger.error(f"Google API call failed: {e}")

            return AIResponse(
                model=model,
                content="",
                confidence=0.0,
                reasoning="",
                provider=self.provider_name,
                created_at=datetime.now(),
                latency_ms=latency_ms,
                error=str(e),
                success=False
            )

    def _is_rate_limit_error(self, error: Exception) -> bool:
        """Check if error is a rate limit error"""
        error_str = str(error).lower()
        return any(phrase in error_str for phrase in [
            'rate limit',
            'quota exceeded',
            'too many requests',
            '429'
        ])

    def parse_response(self, raw_response: Any) -> AIResponse:
        """
        Parse Google API response

        Args:
            raw_response: Raw response from Google API

        Returns:
            Standardized AIResponse
        """
        # Not typically used since we parse in call() directly
        # But included for interface compliance
        content = raw_response.text if hasattr(raw_response, 'text') else str(raw_response)

        return AIResponse(
            model="unknown",
            content=content,
            confidence=0.85,
            reasoning="Google AI analysis",
            provider=self.provider_name,
            created_at=datetime.now(),
            success=True
        )
