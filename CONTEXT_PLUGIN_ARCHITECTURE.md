# Context Plugin Architecture

## Problem We Solved
Instead of hardcoding every data source in ingestion_engine.py (patching-based), we built a **plugin system** where adding new sources requires:
1. Create one plugin class (100 lines)
2. Register it in config (2 lines)
3. Done - system automatically discovers and uses it

---

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                   Investigation Context System                   │
└─────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                    ContextSourceManager                          │
│  - Manages all plugins                                           │
│  - Routes queries to appropriate sources                         │
│  - Aggregates results                                            │
└─────────────────────────────────────────────────────────────────┘
                                  │
           ┌──────────────────────┼──────────────────────┐
           ▼                      ▼                      ▼
┌─────────────────────┐  ┌────────────────────┐  ┌────────────────────┐
│ CrowdStrike Plugin  │  │ SentinelOne Plugin │  │ ElasticLogs Plugin │
│ - HOSTS_READ        │  │ - Assets           │  │ - Logs             │
│ - ALERTS_READ       │  │ - Detections       │  │ - Events           │
│ - DETECTIONS_READ   │  │ - Incidents        │  │ - Metrics          │
│ - INCIDENTS_READ    │  │                    │  │                    │
└─────────────────────┘  └────────────────────┘  └────────────────────┘
```

---

## Core Concepts

### 1. **ContextCategory** (8 Standard Categories)
All sources map to these standardized categories:

| Category | Purpose | Examples |
|----------|---------|----------|
| `CTI` | Threat Intelligence | IOCs, threat reports |
| `ASSET` | Device/Host Info | IP → hostname, OS version |
| `DETECTION` | Security Detections | EDR alerts, SIEM alerts |
| `INCIDENT` | Security Incidents | Grouped detections, cases |
| `LOG` | Raw Log Events | Firewall logs, endpoint logs |
| `IDENTITY` | User/Identity Info | AD, Okta, user profiles |
| `NETWORK` | Network Telemetry | NetFlow, firewall, proxy |
| `VULNERABILITY` | Vuln Scan Results | Tenable, Qualys, Rapid7 |

### 2. **ContextQuery** (Standardized Query Format)
All sources understand this query format:

```python
ContextQuery(
    query_type='ip',              # 'ip', 'hostname', 'user', 'file_hash', 'domain'
    query_value='*************',  # The actual value to search
    categories=[                   # Which categories to pull
        ContextCategory.ASSET,
        ContextCategory.DETECTION
    ],
    time_range={                   # Optional time window
        'start': 'now-1h',
        'end': 'now'
    },
    max_results=100
)
```

### 3. **ContextResult** (Standardized Response Format)
All sources return results in this format:

```python
ContextResult(
    source_name='crowdstrike',     # Which source provided this
    category=ContextCategory.ASSET,
    data={                          # The actual data (source-specific)
        'hostname': 'WORKSTATION-42',
        'os_version': 'Windows 10 21H2',
        ...
    },
    confidence=1.0,                # 0.0-1.0 (how confident are we)
    timestamp='2025-10-02T12:34:56Z'
)
```

---

## How to Add a New Source (e.g., SentinelOne)

### Step 1: Create Plugin Class (100 lines)

```python
# engines/ingestion/sentinelone_context_plugin.py

from context_source_plugin import (
    ContextSourcePlugin, ContextQuery, ContextResult, ContextCategory
)

class SentinelOneContextPlugin(ContextSourcePlugin):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_token = config.get('api_token')
        self.console_url = config.get('console_url')

    def get_source_name(self) -> str:
        return "sentinelone"

    def get_supported_categories(self) -> List[ContextCategory]:
        return [
            ContextCategory.ASSET,      # Endpoint inventory
            ContextCategory.DETECTION,  # Threats
            ContextCategory.INCIDENT    # Storylines (incidents)
        ]

    def get_supported_query_types(self) -> List[str]:
        return ['ip', 'hostname', 'user', 'file_hash']

    async def validate_credentials(self) -> bool:
        # Test API connection
        headers = {'Authorization': f'ApiToken {self.api_token}'}
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f'{self.console_url}/web/api/v2.1/system/status',
                headers=headers
            ) as resp:
                return resp.status == 200

    async def query_context(self, query: ContextQuery) -> List[ContextResult]:
        results = []

        for category in query.categories:
            if category == ContextCategory.ASSET:
                asset_results = await self._query_agents(query)
                results.extend(asset_results)

            elif category == ContextCategory.DETECTION:
                threat_results = await self._query_threats(query)
                results.extend(threat_results)

        return results

    async def _query_agents(self, query: ContextQuery) -> List[ContextResult]:
        # Query SentinelOne agents API
        # Return list of ContextResult objects
        pass

    async def _query_threats(self, query: ContextQuery) -> List[ContextResult]:
        # Query SentinelOne threats API
        # Return list of ContextResult objects
        pass
```

### Step 2: Register Plugin in Ingestion Engine (2 lines)

```python
# engines/ingestion/ingestion_engine.py

from sentinelone_context_plugin import SentinelOneContextPlugin

# In __init__ or setup method:
self.context_manager = ContextSourceManager()

# Register CrowdStrike plugin
crowdstrike_plugin = CrowdStrikeContextPlugin({
    'enabled': bool(os.getenv('CROWDSTRIKE_CLIENT_ID')),
    'client_id': os.getenv('CROWDSTRIKE_CLIENT_ID'),
    'client_secret': os.getenv('CROWDSTRIKE_CLIENT_SECRET')
})
self.context_manager.register_plugin(crowdstrike_plugin)

# Register SentinelOne plugin (NEW - just 3 lines!)
sentinelone_plugin = SentinelOneContextPlugin({
    'enabled': bool(os.getenv('SENTINELONE_API_TOKEN')),
    'api_token': os.getenv('SENTINELONE_API_TOKEN'),
    'console_url': os.getenv('SENTINELONE_CONSOLE_URL')
})
self.context_manager.register_plugin(sentinelone_plugin)

# Initialize all plugins
await self.context_manager.initialize_all()
```

### Step 3: Add Environment Variables

```bash
# .env
SENTINELONE_API_TOKEN=your_api_token
SENTINELONE_CONSOLE_URL=https://your-console.sentinelone.net
```

**That's it!** The system automatically:
- Discovers the new source
- Routes appropriate queries to it
- Aggregates results with other sources
- Displays in investigation UI

---

## Query Flow Example

### User Action:
Analyst clicks alert for IP `*************`

### System Flow:

```python
# 1. Delivery Engine creates query
query = create_context_query(
    query_type='ip',
    query_value='*************',
    categories=['asset', 'detection']
)

# 2. Publish to Redis
redis.publish('ingestion.pull_context', {
    'query': query.to_dict()
})

# 3. Ingestion Engine receives and routes
results = await context_manager.query_context(query)

# 4. System automatically queries ALL applicable sources:
# - CrowdStrike: Checks HOSTS_READ (assets) + ALERTS_READ (detections)
# - SentinelOne: Checks agents API (assets) + threats API (detections)
# - Elastic: Checks logs for that IP (logs)

# 5. Results aggregated:
{
    'crowdstrike': [
        ContextResult(category=ASSET, data={'hostname': 'WKS-42', ...}),
        ContextResult(category=DETECTION, data={'alert_id': '123', ...})
    ],
    'sentinelone': [
        ContextResult(category=ASSET, data={'hostname': 'WKS-42', ...}),
        ContextResult(category=DETECTION, data={'threat_id': '456', ...})
    ],
    'elastic': [
        ContextResult(category=LOG, data={'event': 'firewall_allow', ...})
    ]
}

# 6. Contextualization Engine extracts entities from ALL results

# 7. Backend stores enriched intelligence

# 8. Delivery displays unified view with data from all sources
```

---

## Benefits of Plugin Architecture

### 1. **Easy to Add Sources**
- 100 lines per source vs 500+ lines of hardcoded logic
- No need to modify core engine code
- Clear separation of concerns

### 2. **Automatic Discovery**
- System finds available sources at runtime
- Disables sources with missing credentials automatically
- No manual routing logic needed

### 3. **Standardized Interface**
- All sources speak same "language" (ContextQuery/ContextResult)
- Frontend doesn't need to know about specific sources
- Easy to swap sources (CrowdStrike → SentinelOne)

### 4. **Parallel Queries**
- Manager can query multiple sources concurrently
- Faster results (don't wait for slow sources)
- Resilient (one source failure doesn't block others)

### 5. **Clear Category Mapping**
```
Source          | CTI | ASSET | DETECTION | INCIDENT | LOG | IDENTITY | NETWORK | VULN |
----------------|-----|-------|-----------|----------|-----|----------|---------|------|
CrowdStrike     |  ✓  |   ✓   |     ✓     |    ✓     |  ✓  |          |         |      |
SentinelOne     |     |   ✓   |     ✓     |    ✓     |     |          |         |      |
Elastic         |     |       |     ✓     |          |  ✓  |          |    ✓    |      |
Active Directory|     |       |           |          |     |    ✓     |         |      |
Tenable         |     |   ✓   |           |          |     |          |         |  ✓   |
```

---

## Future Sources (Easy to Add)

### Security Tools:
- **SentinelOne**: EDR detections + asset inventory
- **Microsoft Defender**: Alerts + devices
- **Carbon Black**: Endpoint detections
- **Palo Alto Cortex XDR**: XDR detections

### Identity Sources:
- **Active Directory**: User profiles, group memberships
- **Okta**: SSO events, user context
- **Azure AD**: Cloud identity

### Network Sources:
- **Cisco Firepower**: Network flows
- **Palo Alto Panorama**: Firewall logs
- **Zscaler**: Proxy logs

### Vulnerability Sources:
- **Tenable**: Vulnerability scans
- **Qualys**: Asset vulnerabilities
- **Rapid7 InsightVM**: Vuln data

### Log Sources:
- **Splunk**: Query Splunk for specific events
- **Sumo Logic**: Pull logs
- **Datadog**: Metrics + logs

Each source: **~100 lines of code** to fully integrate!

---

## Implementation Status

### ✅ Complete:
- Plugin base architecture (`context_source_plugin.py`)
- CrowdStrike plugin with all 7 scopes (`crowdstrike_context_plugin.py`)
- Category standardization (8 categories)
- Query/Result standardization

### 🔄 In Progress:
- Integration with Ingestion Engine
- Integration with Contextualization Engine (entity extraction)
- Integration with Delivery Engine (display)

### ⏳ Next:
- Elastic logs plugin (query logs by IP/hostname)
- Active Directory plugin (user context)
- SentinelOne plugin (EDR alternative)

---

## How to Use in Code

### Simple Query:
```python
from context_source_plugin import create_context_query

# Query for IP across all sources
query = create_context_query('ip', '*************', ['asset', 'detection'])
results = await context_manager.query_context(query)

# Results automatically come from ALL applicable sources
for source_name, source_results in results.items():
    print(f"{source_name}: {len(source_results)} results")
```

### Check Plugin Status:
```python
status = context_manager.get_plugin_status()
print(status)
# {
#     'crowdstrike': {
#         'enabled': True,
#         'credentials_valid': True,
#         'supported_categories': ['asset', 'detection', 'incident'],
#         'supported_query_types': ['ip', 'hostname', 'device_id', 'user', 'file_hash']
#     },
#     'sentinelone': {
#         'enabled': False,  # Not configured
#         ...
#     }
# }
```

---

## This Architecture Scales!

Adding 10 more sources = 10 files of ~100 lines each
VS
Hardcoding 10 sources = 1 file of 5000+ lines with spaghetti logic

**Clear winner for maintenance and extensibility.**
