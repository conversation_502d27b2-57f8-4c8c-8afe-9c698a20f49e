"""
OTX (AlienVault Open Threat Exchange) Integration
Free threat intelligence feed that works without complex setup
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import hashlib

logger = logging.getLogger(__name__)


class OTXConnector:
    """
    Connects to AlienVault OTX for free threat intelligence
    Works with or without API key (limited without key)
    """

    def __init__(self, api_key: str = None):
        self.base_url = "https://otx.alienvault.com/api/v1"
        self.api_key = api_key or ""
        self.headers = {
            'X-OTX-API-KEY': self.api_key,
            'Content-Type': 'application/json'
        }

    async def test_connection(self) -> bool:
        """Test connection to OTX"""
        try:
            async with aiohttp.ClientSession() as session:
                # Test with a simple endpoint
                url = f"{self.base_url}/pulses/subscribed"
                async with session.get(url, headers=self.headers, params={'limit': 1}) as response:
                    if response.status == 200:
                        logger.info("Successfully connected to OTX")
                        return True
                    else:
                        logger.warning(f"OTX connection returned status {response.status}")
                        return False
        except Exception as e:
            logger.error(f"OTX connection error: {e}")
            return False

    async def get_recent_indicators(self, limit: int = 50) -> List[Dict]:
        """
        Fetch recent indicators from OTX public feed
        """
        indicators = []

        try:
            async with aiohttp.ClientSession() as session:
                # Get recent pulses (threat intelligence reports)
                url = f"{self.base_url}/pulses/subscribed"
                params = {
                    'limit': min(limit, 10),  # Get recent pulses
                    'page': 1
                }

                async with session.get(url, headers=self.headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        pulses = data.get('results', [])

                        for pulse in pulses:
                            pulse_indicators = pulse.get('indicators', [])
                            for ind in pulse_indicators[:limit]:
                                indicator = {
                                    'id': ind.get('id', ''),
                                    'type': ind.get('type', ''),
                                    'indicator': ind.get('indicator', ''),
                                    'title': pulse.get('name', ''),
                                    'description': ind.get('description', ''),
                                    'created': ind.get('created', ''),
                                    'tags': pulse.get('tags', []),
                                    'references': pulse.get('references', []),
                                    'pulse_name': pulse.get('name', ''),
                                    'adversary': pulse.get('adversary', ''),
                                    'malware_families': pulse.get('malware_families', []),
                                    'attack_ids': pulse.get('attack_ids', [])
                                }
                                indicators.append(indicator)

                        logger.info(f"Retrieved {len(indicators)} indicators from OTX")
                    else:
                        # If authenticated endpoint fails, try public endpoint
                        logger.info("Trying public OTX feed...")
                        indicators = await self.get_public_indicators(limit)

        except Exception as e:
            logger.error(f"Error fetching OTX indicators: {e}")
            # Fallback to public feed
            indicators = await self.get_public_indicators(limit)

        return indicators

    async def get_public_indicators(self, limit: int = 50) -> List[Dict]:
        """
        Fetch indicators from OTX public feed (requires valid API key)
        """
        indicators = []

        try:
            async with aiohttp.ClientSession() as session:
                # Get public pulses
                url = f"{self.base_url}/pulses/activity"
                params = {
                    'limit': min(limit, 50),
                    'page': 1
                }

                async with session.get(url, headers=self.headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        pulses = data.get('results', [])

                        for pulse in pulses:
                            pulse_indicators = pulse.get('indicators', [])
                            for ind in pulse_indicators[:limit]:
                                ind['pulse_name'] = pulse.get('name', 'Unknown Pulse')
                                ind['adversary'] = pulse.get('adversary', 'Unknown')
                                ind['malware_families'] = pulse.get('malware_families', [])
                                ind['references'] = pulse.get('references', [])
                                ind['tags'] = pulse.get('tags', [])
                                ind['attack_ids'] = [tag for tag in pulse.get('tags', []) if tag.startswith('T')]
                                indicators.append(ind)
                    else:
                        logger.warning(f"OTX public feed returned status {response.status}")

        except Exception as e:
            logger.error(f"Error fetching OTX public indicators: {e}")

        return indicators

    def convert_to_sigma(self, indicator: Dict) -> Optional[str]:
        """
        Convert OTX indicator to Sigma rule
        """
        ind_type = indicator.get('type', '').lower()
        ind_value = indicator.get('indicator', '')

        if not ind_value:
            return None

        # Generate rule ID
        rule_id = f"OTX_{hashlib.md5(ind_value.encode()).hexdigest()[:8]}"

        # Base Sigma rule structure
        sigma_rule = f"""title: {indicator.get('title', 'OTX Indicator')}
id: {rule_id}
description: {indicator.get('description', 'Indicator from OTX threat intelligence')}
author: OTX Integration
date: {datetime.now().strftime('%Y/%m/%d')}
references:
    - OTX Pulse: {indicator.get('pulse_name', 'Unknown')}
tags:"""

        # Add tags
        for tag in indicator.get('tags', []):
            sigma_rule += f"\n    - {tag}"

        # Add MITRE ATT&CK IDs
        for attack_id in indicator.get('attack_ids', []):
            sigma_rule += f"\n    - attack.{attack_id.lower()}"

        # Create detection logic based on indicator type
        if ind_type in ['filehash-sha256', 'filehash-sha1', 'filehash-md5']:
            sigma_rule += f"""
logsource:
    product: windows
    service: sysmon
detection:
    selection:
        EventID:
            - 1  # Process creation
            - 15 # File stream creation
        Hashes|contains: '{ind_value}'
    condition: selection
falsepositives:
    - Legitimate software with hash collision
level: high"""

        elif ind_type == 'domain':
            sigma_rule += f"""
logsource:
    category: dns
detection:
    selection:
        query: '{ind_value}'
    condition: selection
falsepositives:
    - Legitimate access to domain
level: medium"""

        elif ind_type in ['ipv4', 'ipv6']:
            sigma_rule += f"""
logsource:
    category: firewall
detection:
    selection:
        dst_ip: '{ind_value}'
    condition: selection
falsepositives:
    - Legitimate connection to IP
level: medium"""

        elif ind_type == 'url':
            sigma_rule += f"""
logsource:
    category: proxy
detection:
    selection:
        cs-uri-query|contains: '{ind_value}'
    condition: selection
falsepositives:
    - Legitimate access to URL
level: high"""

        elif ind_type == 'email':
            sigma_rule += f"""
logsource:
    product: windows
    service: exchange
detection:
    selection:
        sender: '{ind_value}'
    condition: selection
falsepositives:
    - Legitimate email from sender
level: high"""

        else:
            # Generic rule for unknown types
            sigma_rule += f"""
logsource:
    product: windows
detection:
    selection:
        - '{ind_value}'
    condition: selection
falsepositives:
    - Unknown
level: medium"""

        return sigma_rule


class OTXRuleGenerator:
    """
    Generates detection rules from OTX indicators
    """

    def __init__(self, otx_connector: OTXConnector):
        self.otx = otx_connector

    async def generate_rules(self, limit: int = 20) -> List[Dict]:
        """
        Generate detection rules from OTX indicators
        """
        # Fetch indicators
        indicators = await self.otx.get_recent_indicators(limit=limit)

        rules = []
        for indicator in indicators:
            sigma_rule = self.otx.convert_to_sigma(indicator)
            if sigma_rule:
                rule = {
                    'rule_id': f"OTX_{hashlib.md5(indicator.get('indicator', '').encode()).hexdigest()[:8]}",
                    'name': indicator.get('title', 'OTX Detection'),
                    'description': indicator.get('description', ''),
                    'source': 'OTX',
                    'indicator': indicator.get('indicator', ''),
                    'indicator_type': indicator.get('type', ''),
                    'tags': indicator.get('tags', []),
                    'attack_ids': indicator.get('attack_ids', []),
                    'sigma_rule': sigma_rule,
                    'confidence': 0.7,  # Default confidence for OTX
                    'created_at': datetime.now().isoformat()
                }
                rules.append(rule)

        logger.info(f"Generated {len(rules)} detection rules from OTX")
        return rules


async def test_otx_integration():
    """Test OTX integration"""

    print("Testing OTX Integration...")
    print("-" * 50)

    # Create connector
    connector = OTXConnector()

    # Test connection
    connected = await connector.test_connection()
    print(f"Connection Test: {'Success' if connected else 'Failed - Check API Key'}")

    # Get indicators
    print("\n[Fetching Indicators]")
    indicators = await connector.get_recent_indicators(limit=5)
    for ind in indicators:
        print(f"  - {ind.get('type', 'Unknown')}: {ind.get('indicator', '')[:50]}")

    # Generate rules
    print("\n[Generating Rules]")
    generator = OTXRuleGenerator(connector)
    rules = await generator.generate_rules(limit=5)

    for rule in rules:
        print(f"  - {rule['name']}: {rule['indicator_type']} - Confidence {rule['confidence']}")

    # Show sample Sigma rule
    if rules:
        print("\n[Sample Sigma Rule]")
        print(rules[0]['sigma_rule'])

    return rules


if __name__ == "__main__":
    asyncio.run(test_otx_integration())