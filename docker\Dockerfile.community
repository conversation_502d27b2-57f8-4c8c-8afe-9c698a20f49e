# Community Knowledge Engine Dockerfile
FROM siemless-v2-base:latest

# Copy Community Knowledge Engine specific code
COPY engines/community/ /app/engines/community/

# Create community data directory
RUN mkdir -p /app/community_data

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD redis-cli -h $REDIS_HOST ping || exit 1

# Expose Community API port
EXPOSE 5010

# Set the command to run Community Knowledge Engine with both services
CMD ["python", "engines/community/community_knowledge.py"]