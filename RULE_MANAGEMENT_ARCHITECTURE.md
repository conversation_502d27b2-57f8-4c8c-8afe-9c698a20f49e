# Rule Management System Architecture

**Date**: October 3, 2025
**Purpose**: Complete architecture for CTI → Rules → Contextualization + Correlation pipeline

---

## Overview

The Rule Management System is a **critical differentiator** that transforms threat intelligence into actionable detection rules automatically, then feeds them into our contextualization and correlation engines.

### The Complete Flow

```
CTI Plugins (OTX, ThreatFox, CrowdStrike, OpenCTI)
    ↓
CTI Aggregator (normalize indicators)
    ↓
Rule Generator (AI-powered, multi-SIEM)
    ↓
Rule Management UI (approve/edit/organize)
    ↓
Contextualization Engine (enrich with rules)
    ↓
Correlation Engine (multi-source detection)
    ↓
Alert Queue (enriched alerts with context)
```

---

## System Components

### 1. CTI-to-Rule Generation Pipeline

**Input**: CTI indicators from 4 sources
**Output**: Detection rules in multiple formats

#### Supported Rule Formats
- **Sigma** (universal)
- **Splunk SPL** (Splunk)
- **KQL/DSL** (Elastic)
- **KQL** (Microsoft Sentinel)
- **AQL** (IBM QRadar)

#### Rule Generation Process
1. **CTI Indicator Received**
   - Source: OTX, ThreatFox, CrowdStrike, OpenCTI
   - Type: IP, domain, hash, URL, email
   - Metadata: Threat actor, campaign, malware family

2. **AI Analysis** (Intelligence Engine)
   - Analyze indicator context
   - Determine detection logic
   - Generate rule conditions
   - Create test cases

3. **Multi-SIEM Translation**
   - Base: Sigma rule format
   - Translate to target SIEM syntax
   - Validate syntax
   - Quality scoring (0-100)

4. **Rule Staging**
   - Store in `pending_rules` table
   - Await human approval
   - Track generation metadata

---

### 2. Rule Management UI

#### 2.1 CTI-to-Rule Generation View

**Purpose**: Review and approve auto-generated rules from CTI

**Features**:
- **Rule Queue**: List of pending rules from CTI
- **Rule Preview**: Side-by-side (Sigma + Target SIEM)
- **CTI Context**: Show original indicator + metadata
- **Quality Score**: AI-calculated rule quality (0-100)
- **Test Cases**: Auto-generated test scenarios
- **Bulk Actions**: Approve/reject multiple rules

**UI Sections**:
```
┌─────────────────────────────────────────────────────────┐
│ CTI-Generated Rules (Pending Approval)                  │
├─────────────────────────────────────────────────────────┤
│ Filter: [All Sources ▼] [All Types ▼] [Quality: >70]   │
├─────────────────────────────────────────────────────────┤
│                                                          │
│  ┌─────────────────────────────────────────────────┐   │
│  │ 🛡️ ThreatFox → Emotet C2 Detection              │   │
│  │ Quality: 92/100 | Type: Network | Source: IP    │   │
│  │                                                  │   │
│  │ Original Indicator:                             │   │
│  │ • IP: **************                           │   │
│  │ • Threat: Emotet C2 Server                     │   │
│  │ • Campaign: TA542                              │   │
│  │ • Confidence: 95%                              │   │
│  │                                                  │   │
│  │ Generated Rule (Sigma):                         │   │
│  │ [Rule preview...]                               │   │
│  │                                                  │   │
│  │ Target Format: [Splunk SPL ▼]                   │   │
│  │ [Translated rule...]                            │   │
│  │                                                  │   │
│  │ Test Cases: 3 generated                         │   │
│  │ [✓] Connection to ************** triggers       │   │
│  │                                                  │   │
│  │ [Approve] [Edit] [Reject]                       │   │
│  └─────────────────────────────────────────────────┘   │
│                                                          │
│  [More rules...]                                        │
│                                                          │
└─────────────────────────────────────────────────────────┘
```

#### 2.2 Existing Ruleset Management

**Purpose**: Organize, track, and optimize existing detection rules

**Features**:
- **Import Rules**: Upload Sigma, Splunk, Elastic, Sentinel rules
- **MITRE Mapping**: Automatic tactic/technique tagging
- **Performance Tracking**: Alert count, false positives, effectiveness
- **Rule Organization**: Folders, tags, categories
- **Version Control**: Track rule changes over time
- **Rule Testing**: Test against historical data

**UI Sections**:
```
┌─────────────────────────────────────────────────────────┐
│ Detection Rules Library                                 │
├─────────────────────────────────────────────────────────┤
│                                                          │
│ 📁 Folders                    📊 Statistics             │
│ ├─ Network Detection (47)     Total Rules: 243          │
│ ├─ Endpoint Detection (89)    Active: 198               │
│ ├─ Email Security (31)        Draft: 45                 │
│ ├─ Cloud Security (28)        High Performers: 34       │
│ └─ User Behavior (48)         Need Tuning: 12           │
│                                                          │
├─────────────────────────────────────────────────────────┤
│ [Search rules...] [+ Import] [+ Create New]             │
│                                                          │
│ Filter: [MITRE: All ▼] [Status: Active ▼] [Format: All]│
│                                                          │
│ ┌─────────────────────────────────────────────────┐    │
│ │ Rule: Suspicious PowerShell Encoded Commands    │    │
│ │ Format: Sigma | MITRE: T1059.001 (PowerShell)  │    │
│ │ Status: Active | Performance: ★★★★☆ (82/100)    │    │
│ │                                                  │    │
│ │ Alerts (30d): 47 | True Pos: 12 | False Pos: 3  │    │
│ │ Last Triggered: 2 hours ago                     │    │
│ │                                                  │    │
│ │ [View] [Edit] [Test] [Disable]                  │    │
│ └─────────────────────────────────────────────────┘    │
│                                                          │
└─────────────────────────────────────────────────────────┘
```

#### 2.3 Rule Performance Dashboard

**Purpose**: Track rule effectiveness and optimize detection

**Metrics**:
- **Alert Volume**: Alerts triggered per rule
- **True Positive Rate**: % of alerts that are real threats
- **False Positive Rate**: % of alerts that are noise
- **MTTR**: Mean time to resolve alerts from rule
- **Coverage**: Which MITRE techniques covered
- **Confidence**: Detection confidence score

**Visualizations**:
- Rule effectiveness heatmap
- Alert volume trends
- False positive analysis
- MITRE coverage matrix

---

### 3. Database Schema

#### Tables

**`detection_rules`** - All detection rules
```sql
CREATE TABLE detection_rules (
    rule_id UUID PRIMARY KEY,
    rule_name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50), -- sigma, splunk, elastic, sentinel, qradar
    rule_content TEXT NOT NULL,
    description TEXT,
    severity VARCHAR(20), -- critical, high, medium, low

    -- MITRE mapping
    mitre_tactics TEXT[],
    mitre_techniques TEXT[],

    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by VARCHAR(100),
    source VARCHAR(100), -- cti_generated, imported, manual

    -- CTI linkage
    cti_indicator_id UUID,
    cti_source VARCHAR(50), -- otx, threatfox, crowdstrike, opencti

    -- Status
    status VARCHAR(20), -- pending, approved, active, disabled, archived
    enabled BOOLEAN DEFAULT false,

    -- Quality
    quality_score INTEGER, -- 0-100
    test_cases JSONB,

    -- Organization
    folder VARCHAR(100),
    tags TEXT[]
);
```

**`rule_performance`** - Rule effectiveness tracking
```sql
CREATE TABLE rule_performance (
    performance_id UUID PRIMARY KEY,
    rule_id UUID REFERENCES detection_rules(rule_id),

    -- Metrics
    total_alerts INTEGER DEFAULT 0,
    true_positives INTEGER DEFAULT 0,
    false_positives INTEGER DEFAULT 0,
    false_negatives INTEGER DEFAULT 0,

    -- Rates
    true_positive_rate DECIMAL,
    false_positive_rate DECIMAL,
    precision DECIMAL,
    recall DECIMAL,
    f1_score DECIMAL,

    -- Timing
    avg_triage_time_seconds INTEGER,
    last_triggered TIMESTAMP,

    -- Score
    performance_score INTEGER, -- 0-100

    -- Period
    measurement_period VARCHAR(20), -- 24h, 7d, 30d, 90d
    measured_at TIMESTAMP DEFAULT NOW()
);
```

**`pending_rules`** - CTI-generated rules awaiting approval
```sql
CREATE TABLE pending_rules (
    pending_id UUID PRIMARY KEY,

    -- CTI source
    cti_indicator JSONB, -- Full indicator data
    cti_source VARCHAR(50),

    -- Generated rules
    sigma_rule TEXT,
    translated_rules JSONB, -- {splunk: "", elastic: "", ...}

    -- Quality
    quality_score INTEGER,
    generation_method VARCHAR(50), -- ai_generated, template_based
    ai_model VARCHAR(50),

    -- Test cases
    test_cases JSONB,

    -- Review
    status VARCHAR(20), -- pending, approved, rejected, needs_revision
    reviewed_by VARCHAR(100),
    reviewed_at TIMESTAMP,
    review_notes TEXT,

    generated_at TIMESTAMP DEFAULT NOW()
);
```

**`rule_test_results`** - Rule testing results
```sql
CREATE TABLE rule_test_results (
    test_id UUID PRIMARY KEY,
    rule_id UUID REFERENCES detection_rules(rule_id),

    test_data TEXT,
    expected_result VARCHAR(20), -- match, no_match
    actual_result VARCHAR(20),
    passed BOOLEAN,

    execution_time_ms INTEGER,
    executed_at TIMESTAMP DEFAULT NOW()
);
```

---

### 4. Backend API Endpoints

#### Rule Management APIs

**CTI-Generated Rules**:
```typescript
GET  /api/rules/pending              // List pending CTI-generated rules
POST /api/rules/pending/:id/approve  // Approve pending rule
POST /api/rules/pending/:id/reject   // Reject pending rule
PUT  /api/rules/pending/:id          // Edit pending rule
GET  /api/rules/pending/:id/preview  // Preview in target format
```

**Rule CRUD**:
```typescript
GET    /api/rules                    // List all rules (with filters)
GET    /api/rules/:id                // Get rule details
POST   /api/rules                    // Create new rule
PUT    /api/rules/:id                // Update rule
DELETE /api/rules/:id                // Delete rule
POST   /api/rules/import             // Import rules (batch)
```

**Rule Testing**:
```typescript
POST /api/rules/:id/test             // Test rule against data
GET  /api/rules/:id/test-results     // Get test results
POST /api/rules/:id/validate         // Validate syntax
```

**Rule Performance**:
```typescript
GET  /api/rules/:id/performance      // Get performance metrics
GET  /api/rules/:id/alerts           // Get alerts from rule
GET  /api/rules/performance/summary  // Performance dashboard data
```

**Rule Organization**:
```typescript
GET  /api/rules/folders              // List folders
POST /api/rules/folders              // Create folder
GET  /api/rules/tags                 // List all tags
POST /api/rules/:id/tags             // Add tags to rule
```

---

### 5. Rule Generation Logic

#### AI-Powered Rule Generation

**Input**: CTI Indicator
```json
{
  "indicator_type": "ip",
  "value": "**************",
  "threat_score": 95,
  "source": "threatfox",
  "metadata": {
    "threat_actor": "TA542",
    "malware_family": "Emotet",
    "campaign": "Emotet C2 Infrastructure",
    "first_seen": "2025-09-15",
    "tags": ["c2", "botnet", "emotet"]
  }
}
```

**AI Prompt**:
```
You are a security detection engineer. Generate a Sigma detection rule for:

Indicator: IP **************
Type: Command & Control Server
Threat: Emotet C2 (TA542 campaign)
Confidence: 95%

Requirements:
1. Detect connections to this IP
2. Include network traffic in both directions
3. Tag with MITRE ATT&CK techniques
4. Generate 3 test cases
5. Rate rule quality (0-100)

Output format:
{
  "sigma_rule": "...",
  "mitre_tactics": [...],
  "mitre_techniques": [...],
  "quality_score": 92,
  "test_cases": [...]
}
```

**Output**: Sigma Rule
```yaml
title: Emotet C2 Communication Detected
id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
status: experimental
description: Detects network communication with known Emotet C2 server (**************)
references:
    - https://threatfox.abuse.ch/browse/
author: SIEMLess AI Rule Generator
date: 2025-10-03
tags:
    - attack.command_and_control
    - attack.t1071.001
logsource:
    category: firewall
    product: any
detection:
    selection:
        destination_ip: '**************'
    condition: selection
falsepositives:
    - Legitimate traffic to this IP (unlikely)
level: high
```

#### Multi-SIEM Translation

**Splunk SPL**:
```spl
index=firewall dest_ip="**************"
| stats count by src_ip, dest_port
| where count > 0
```

**Elastic KQL**:
```kql
destination.ip: "**************" AND event.category: "network"
```

**Sentinel KQL**:
```kql
CommonSecurityLog
| where DestinationIP == "**************"
| where DeviceAction != "Deny"
```

---

### 6. Integration with Contextualization & Correlation

#### How Rules Feed Enrichment

**Step 1: Rule Active**
- Detection rule enabled in system
- Monitors logs in real-time

**Step 2: Log Matches Rule**
- Contextualization engine receives log
- Checks against active rules
- Match found → Add enrichment

**Step 3: Enhanced Context**
```json
{
  "entity_type": "ip",
  "entity_value": "**************",
  "enrichments": {
    "layer_1": { /* business context */ },
    "layer_2": {
      "cti_match": {
        "source": "threatfox",
        "threat_score": 95,
        "campaign": "Emotet C2"
      },
      "rule_match": {
        "rule_id": "a1b2c3d4...",
        "rule_name": "Emotet C2 Communication Detected",
        "severity": "high",
        "mitre_techniques": ["T1071.001"]
      }
    },
    "layer_3": { /* vendor context */ }
  }
}
```

**Step 4: Correlation**
- Multiple rules match same entity
- Correlation engine aggregates
- Higher confidence alert generated

---

### 7. User Workflows

#### Workflow 1: Approve CTI-Generated Rule

1. Analyst opens "Rule Management" → "Pending Approvals"
2. Sees new rule from ThreatFox (Emotet C2)
3. Reviews:
   - Original CTI indicator
   - Generated Sigma rule
   - Translated Splunk rule
   - Quality score: 92/100
   - 3 test cases included
4. Tests rule against last 7 days of logs
5. No false positives found
6. Clicks "Approve"
7. Rule deployed to Splunk
8. Starts generating enriched alerts

#### Workflow 2: Manage Existing Ruleset

1. Engineer imports 500 Sigma rules from GitHub
2. System auto-maps to MITRE ATT&CK
3. Organizes into folders by tactic
4. Enables 50 high-priority rules
5. Monitors performance dashboard
6. Sees "PowerShell Encoded Commands" has 80% FP rate
7. Clicks "Tune" → Adjusts conditions
8. Re-tests → FP rate drops to 10%
9. Updates rule → Saves

#### Workflow 3: Rule Performance Optimization

1. Security lead opens "Rule Performance Dashboard"
2. Sees heatmap of rule effectiveness
3. Identifies 12 rules with >50% false positive rate
4. Clicks "Analyze" on worst performer
5. Reviews alert history
6. Identifies common false positive pattern
7. Updates rule exclusions
8. Monitors for 7 days
9. Confirms improvement
10. Documents changes

---

## Implementation Priority

### Phase 1: Foundation (Current)
✅ CTI Plugin Status Widget
✅ Alert Queue with Enrichment
⏳ Rule Management Backend APIs

### Phase 2: Rule Generation UI (This Phase)
1. Pending Rules View (CTI-generated)
2. Rule Preview Component
3. Approval Workflow
4. Multi-SIEM Translation Display

### Phase 3: Ruleset Management
1. Rule Library UI
2. Import/Export
3. MITRE Mapping
4. Folder Organization

### Phase 4: Performance Tracking
1. Performance Dashboard
2. Alert Analysis
3. Auto-tuning Suggestions

---

## Success Metrics

**Rule Generation**:
- CTI indicators → Rules: <2 minutes
- Rule quality score: >85 average
- Approval rate: >70%

**Rule Management**:
- Time to deploy rule: <5 minutes
- Rules organized: 100%
- Performance tracked: Real-time

**Detection Effectiveness**:
- False positive rate: <10%
- True positive rate: >80%
- Coverage: 200+ MITRE techniques

---

## Next Steps

1. ✅ Architecture defined (this document)
2. ⏳ Create rule API service
3. ⏳ Build pending rules UI
4. ⏳ Build rule library UI
5. ⏳ Build performance dashboard

**Ready to start building!** 🚀
