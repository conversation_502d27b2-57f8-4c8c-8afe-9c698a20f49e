"""
ThreatFox (abuse.ch) CTI Plugin
Universal plugin implementation for ThreatFox malware IOC feed
"""

import aiohttp
import ssl
from typing import List, Optional
from datetime import datetime
from cti_source_plugin import (
    CTISourcePlugin, CTIIndicator, IndicatorType, ThreatType
)


class ThreatFoxPlugin(CTISourcePlugin):
    """
    ThreatFox CTI Source Plugin

    Fetches malware IOCs from abuse.ch ThreatFox
    Free threat intelligence feed focused on malware indicators
    """

    def __init__(self, config: dict, logger=None):
        super().__init__(config, logger)
        self.base_url = "https://threatfox-api.abuse.ch/api/v1/"
        self.api_url = self.base_url  # Override base class default

        # ThreatFox uses Auth-Key header (not X-API-KEY)
        self.auth_header_name = 'Auth-Key'

        # SSL context (ThreatFox sometimes has SSL issues)
        self.ssl_context = ssl.create_default_context()
        if config.get('verify_ssl', True) is False:
            self.ssl_context.check_hostname = False
            self.ssl_context.verify_mode = ssl.CERT_NONE

    def get_source_name(self) -> str:
        return "threatfox"

    def get_source_type(self) -> str:
        return "community"

    async def validate_credentials(self) -> bool:
        """Test connection to ThreatFox API"""
        try:
            async with aiohttp.ClientSession(
                connector=aiohttp.TCPConnector(ssl=self.ssl_context)
            ) as session:
                data = {
                    "query": "get_iocs",
                    "days": 1
                }

                headers = {}
                if self.api_key:
                    headers[self.auth_header_name] = self.api_key

                async with session.post(
                    self.base_url,
                    json=data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get('query_status') == 'ok':
                            self.logger.info("ThreatFox credentials validated successfully")
                            return True

                    self.logger.warning(f"ThreatFox validation returned status {response.status}")
                    return False

        except Exception as e:
            self.logger.error(f"ThreatFox credential validation error: {e}")
            return False

    async def fetch_indicators(
        self,
        since: Optional[datetime] = None,
        indicator_types: Optional[List[IndicatorType]] = None,
        limit: int = 1000
    ) -> List[CTIIndicator]:
        """
        Fetch indicators from ThreatFox

        Args:
            since: Only fetch indicators updated after this timestamp
            indicator_types: Filter by specific indicator types
            limit: Maximum number of indicators to return

        Returns:
            List of standardized CTI indicators
        """
        indicators = []

        try:
            # Calculate days since timestamp
            days = 7  # Default to 7 days (ThreatFox free API limit)
            if since:
                days_diff = (datetime.utcnow() - since).days
                days = min(days_diff, 7)  # Cap at 7 for free API

            # Fetch recent IOCs
            iocs = await self._fetch_recent_iocs(days, limit)

            for ioc in iocs:
                standardized = self._standardize_indicator(ioc)
                if standardized:
                    # Filter by type if specified
                    if indicator_types:
                        type_values = [t.value for t in indicator_types]
                        if standardized.indicator_type in type_values:
                            indicators.append(standardized)
                    else:
                        indicators.append(standardized)

            self.logger.info(f"ThreatFox: Fetched {len(indicators)} indicators")

        except Exception as e:
            self.logger.error(f"ThreatFox fetch error: {e}")

        return indicators[:limit]

    async def _fetch_recent_iocs(self, days: int, limit: int) -> List[dict]:
        """Fetch recent IOCs from ThreatFox"""
        try:
            async with aiohttp.ClientSession(
                connector=aiohttp.TCPConnector(ssl=self.ssl_context)
            ) as session:
                data = {
                    "query": "get_iocs",
                    "days": min(days, 7)  # Free API limits to 7 days
                }

                headers = {}
                if self.api_key:
                    headers[self.auth_header_name] = self.api_key

                async with session.post(
                    self.base_url,
                    json=data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()

                        if result.get('query_status') == 'ok':
                            iocs = result.get('data', [])
                            self.logger.info(f"Retrieved {len(iocs)} raw IOCs from ThreatFox")
                            return iocs[:limit]
                        else:
                            self.logger.warning(f"ThreatFox query failed: {result.get('query_status')}")
                            return []
                    else:
                        self.logger.error(f"ThreatFox API error: HTTP {response.status}")
                        return []

        except Exception as e:
            self.logger.error(f"Error fetching ThreatFox IOCs: {e}")
            return []

    def _standardize_indicator(self, ioc: dict) -> Optional[CTIIndicator]:
        """
        Convert ThreatFox IOC to standardized CTI format

        ThreatFox types: md5_hash, sha256_hash, sha1_hash, domain, ip:port, url
        """
        try:
            ioc_value = ioc.get('ioc', '')
            ioc_type = ioc.get('ioc_type', '').lower()

            if not ioc_value:
                return None

            # Map ThreatFox types to standard types
            type_mapping = {
                'md5_hash': IndicatorType.FILE_HASH.value,
                'sha256_hash': IndicatorType.FILE_HASH.value,
                'sha1_hash': IndicatorType.FILE_HASH.value,
                'domain': IndicatorType.DOMAIN.value,
                'ip:port': IndicatorType.IP.value,
                'url': IndicatorType.URL.value
            }

            indicator_type = type_mapping.get(ioc_type)
            if not indicator_type:
                return None  # Skip unsupported types

            # Extract threat type
            threat_type_str = ioc.get('threat_type', '').lower()
            threat_type = ThreatType.MALWARE.value  # Default for ThreatFox

            if 'botnet' in threat_type_str:
                threat_type = ThreatType.BOTNET.value
            elif 'c2' in threat_type_str or 'c&c' in threat_type_str:
                threat_type = ThreatType.C2.value

            # Parse timestamps
            first_seen = ioc.get('first_seen_utc')
            last_seen = ioc.get('last_seen_utc')

            if first_seen:
                try:
                    first_seen = datetime.fromisoformat(first_seen.replace('Z', '+00:00'))
                except:
                    first_seen = None

            if last_seen:
                try:
                    last_seen = datetime.fromisoformat(last_seen.replace('Z', '+00:00'))
                except:
                    last_seen = None

            # Build tags
            tags = []
            if ioc.get('malware'):
                tags.append(ioc['malware'])
            if ioc.get('malware_printable'):
                tags.append(ioc['malware_printable'])
            if ioc.get('tags'):
                tags.extend(ioc['tags'])

            # Calculate confidence
            confidence = self._calculate_confidence(ioc)

            # Build standardized indicator
            return CTIIndicator({
                'indicator_type': indicator_type,
                'indicator_value': ioc_value,
                'threat_type': threat_type,
                'confidence': confidence / 100.0,  # Convert 0-100 to 0.0-1.0
                'first_seen': first_seen,
                'last_seen': last_seen,
                'tags': tags,
                'description': f"Malware IOC: {ioc.get('malware_printable', 'Unknown')}",
                'source_reference': str(ioc.get('id', '')),
                'mitre_techniques': [],  # ThreatFox doesn't provide MITRE mappings
                'severity': self._calculate_severity(ioc),
                'raw_data': {
                    'malware_family': ioc.get('malware_printable', ''),
                    'threat_type': ioc.get('threat_type', ''),
                    'reporter': ioc.get('reporter', ''),
                    'reference': ioc.get('reference', ''),
                    'confidence_level': ioc.get('confidence_level', '')
                }
            })

        except Exception as e:
            self.logger.error(f"Error standardizing ThreatFox indicator: {e}")
            return None

    def _calculate_confidence(self, ioc: dict) -> int:
        """
        Calculate confidence score (0-100)

        Factors:
        - Confidence level from ThreatFox
        - Reporter verification
        - Recency
        - Known malware families
        """
        confidence = 50  # Base confidence

        # Confidence level from source
        if ioc.get('confidence_level'):
            conf_map = {'high': 90, 'medium': 70, 'low': 30}
            confidence = conf_map.get(ioc['confidence_level'], 50)

        # Boost for verified reporters
        if ioc.get('reporter') and '@' in ioc.get('reporter', ''):
            confidence += 10

        # Boost for recent IOCs
        if ioc.get('first_seen_utc'):
            try:
                first_seen = datetime.fromisoformat(ioc['first_seen_utc'].replace('Z', '+00:00'))
                days_old = (datetime.utcnow() - first_seen.replace(tzinfo=None)).days
                if days_old < 7:
                    confidence += 10
                elif days_old < 30:
                    confidence += 5
            except:
                pass

        # Boost for known malware families
        known_families = ['cobalt strike', 'emotet', 'trickbot', 'qakbot', 'lockbit', 'ryuk', 'conti']
        if any(family in ioc.get('malware_printable', '').lower() for family in known_families):
            confidence += 10

        return min(confidence, 100)

    def _calculate_severity(self, ioc: dict) -> str:
        """Calculate severity from IOC metadata"""
        malware = ioc.get('malware_printable', '').lower()

        # High severity malware families
        high_severity = ['ransomware', 'lockbit', 'conti', 'ryuk', 'revil', 'blackmatter']
        if any(mal in malware for mal in high_severity):
            return 'high'

        # Medium severity families
        medium_severity = ['cobalt strike', 'emotet', 'trickbot', 'qakbot', 'trojan']
        if any(mal in malware for mal in medium_severity):
            return 'medium'

        return 'low'

    async def get_indicator_context(self, indicator: str) -> Optional[dict]:
        """Search for specific IOC in ThreatFox database"""
        try:
            async with aiohttp.ClientSession(
                connector=aiohttp.TCPConnector(ssl=self.ssl_context)
            ) as session:
                data = {
                    "query": "search_ioc",
                    "search_term": indicator
                }

                headers = {}
                if self.api_key:
                    headers[self.auth_header_name] = self.api_key

                async with session.post(
                    self.base_url,
                    json=data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        result = await response.json()

                        if result.get('query_status') == 'ok' and result.get('data'):
                            ioc_data = result['data'][0] if isinstance(result['data'], list) else result['data']
                            return {
                                'malware_family': ioc_data.get('malware_printable'),
                                'threat_type': ioc_data.get('threat_type'),
                                'first_seen': ioc_data.get('first_seen_utc'),
                                'last_seen': ioc_data.get('last_seen_utc'),
                                'confidence_level': ioc_data.get('confidence_level'),
                                'reference': ioc_data.get('reference'),
                                'reporter': ioc_data.get('reporter')
                            }

        except Exception as e:
            self.logger.error(f"ThreatFox context fetch error: {e}")

        return None
