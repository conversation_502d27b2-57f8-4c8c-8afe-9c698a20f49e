"""
SIEMLess v2.0 - Data Source Manager
Manages active data sources and their lifecycle for the ingestion engine
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import logging


class DataSourceManager:
    """Manages active data sources and their configurations"""

    def __init__(self, config_manager, logger: logging.Logger):
        self.config_manager = config_manager
        self.logger = logger
        self.active_sources = {}
        self.source_monitors = {}

    def start_source(self, source_id: str, source_type: str = 'elasticsearch') -> bool:
        """Start a new data source"""
        try:
            if source_id in self.active_sources:
                self.logger.warning(f"Source {source_id} is already active")
                return False

            # Validate source type
            if source_type not in self.config_manager.get_all_source_configs():
                self.logger.error(f"Unknown source type: {source_type}")
                return False

            # Create source entry
            self.active_sources[source_id] = {
                'type': source_type,
                'status': 'active',
                'started_at': datetime.utcnow(),
                'logs_processed': 0,
                'last_check': None,
                'errors': 0,
                'config': self.config_manager.get_source_config(source_type)
            }

            self.logger.info(f"Started data source: {source_id} ({source_type})")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start source {source_id}: {e}")
            return False

    def stop_source(self, source_id: str) -> bool:
        """Stop an active data source"""
        try:
            if source_id not in self.active_sources:
                self.logger.warning(f"Source {source_id} is not active")
                return False

            del self.active_sources[source_id]

            # Clean up any source monitor
            if source_id in self.source_monitors:
                del self.source_monitors[source_id]

            self.logger.info(f"Stopped data source: {source_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to stop source {source_id}: {e}")
            return False

    def configure_source(self, source_type: str, config: Dict[str, Any]) -> bool:
        """Update configuration for a source type"""
        return self.config_manager.update_source_config(source_type, config)

    def get_source_config(self, source_type: str) -> Dict[str, Any]:
        """Get configuration for a source type"""
        return self.config_manager.get_source_config(source_type)

    def get_active_sources(self) -> Dict[str, Dict[str, Any]]:
        """Get all active sources"""
        return self.active_sources.copy()

    def get_source_info(self, source_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific source"""
        return self.active_sources.get(source_id)

    def update_source_stats(self, source_id: str, logs_processed: int = 0, errors: int = 0):
        """Update statistics for a source"""
        if source_id in self.active_sources:
            source_info = self.active_sources[source_id]
            source_info['logs_processed'] += logs_processed
            source_info['errors'] += errors
            source_info['last_check'] = datetime.utcnow().isoformat()

    def get_sources_by_type(self, source_type: str) -> List[str]:
        """Get all active sources of a specific type"""
        return [
            source_id for source_id, info in self.active_sources.items()
            if info['type'] == source_type
        ]

    def is_source_healthy(self, source_id: str) -> bool:
        """Check if a source is healthy based on recent activity"""
        if source_id not in self.active_sources:
            return False

        source_info = self.active_sources[source_id]

        # Check if source has been checked recently
        last_check = source_info.get('last_check')
        if not last_check:
            return True  # New source, assume healthy

        try:
            last_check_dt = datetime.fromisoformat(last_check.replace('Z', '+00:00').replace('+00:00', ''))
            time_since_check = datetime.utcnow() - last_check_dt

            # Consider unhealthy if not checked in 5 minutes
            return time_since_check < timedelta(minutes=5)
        except Exception:
            return True  # If we can't parse, assume healthy

    async def fetch_data_from_sources(self) -> List[Dict[str, Any]]:
        """Fetch data from all active sources"""
        logs = []
        max_logs_per_cycle = self.config_manager.get_engine_setting('max_logs_per_cycle', 500)

        for source_id, source_info in self.active_sources.items():
            try:
                source_logs = await self._fetch_from_single_source(source_id, source_info)
                logs.extend(source_logs)

                # Update source statistics
                self.update_source_stats(source_id, len(source_logs))

                # Respect cycle limits
                if len(logs) >= max_logs_per_cycle:
                    self.logger.info(f"Reached cycle limit of {max_logs_per_cycle} logs")
                    break

            except Exception as e:
                self.logger.error(f"Failed to fetch from source {source_id}: {e}")
                self.update_source_stats(source_id, 0, 1)

        return logs

    async def _fetch_from_single_source(self, source_id: str, source_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch data from a single source"""
        source_type = source_info['type']
        config = source_info['config']
        logs = []

        try:
            if source_type == 'elasticsearch':
                logs = await self._fetch_elasticsearch_data(source_id, config)
            elif source_type == 'database':
                logs = await self._fetch_database_data(source_id, config)
            elif source_type == 'crowdstrike':
                logs = await self._fetch_crowdstrike_data(source_id, config)
            else:
                # Fallback to simulated data
                logs = await self._fetch_simulated_data(source_id, source_type, config)

        except Exception as e:
            self.logger.warning(f"Real data fetch failed for {source_type}: {e}, using fallback")
            logs = await self._fetch_simulated_data(source_id, source_type, config)

        return logs

    async def _fetch_elasticsearch_data(self, source_id: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch data from Elasticsearch"""
        logs = []

        try:
            # Import and use the v0.2 Elasticsearch implementation
            from data_sources_v02 import UniversalElasticIngestor

            es_config = {
                'source_name': source_id,
                'index_pattern': config.get('index_pattern', 'logs-*'),
                'timestamp_field': config.get('timestamp_field', '@timestamp')
            }

            ingestor = UniversalElasticIngestor(es_config, self.logger)
            batch_size = config.get('batch_size', 100)

            count = 0
            for log_data in ingestor.fetch_logs():
                log = {
                    'source_id': source_id,
                    'source_type': 'elasticsearch',
                    'timestamp': datetime.utcnow().isoformat(),
                    'data': log_data,
                    'log_id': f"{source_id}_{int(time.time())}_{count}"
                }
                logs.append(log)
                count += 1

                # Yield control periodically
                if count % 10 == 0:
                    await asyncio.sleep(0)

                if count >= batch_size:
                    break

        except ImportError:
            self.logger.warning("Elasticsearch data source not available, using fallback")

        return logs

    async def _fetch_database_data(self, source_id: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch data from database"""
        logs = []

        try:
            # This requires a database connection from the main engine
            # For now, return empty - this will be handled by the main engine
            pass

        except Exception as e:
            self.logger.error(f"Database fetch error: {e}")

        return logs

    async def _fetch_crowdstrike_data(self, source_id: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Fetch data from CrowdStrike"""
        logs = []

        try:
            # Import and use the v0.2 CrowdStrike implementation
            from data_sources_v02 import CrowdStrikeApiIngestor

            cs_config = {
                'source_type': config.get('source_type', 'detections'),
                'source_name': source_id
            }

            ingestor = CrowdStrikeApiIngestor(cs_config, self.logger)
            limit = config.get('limit', 1000)

            # Fetch recent detections
            detections = ingestor.fetch_detections(limit=limit)
            for idx, detection in enumerate(detections):
                log = {
                    'source_id': source_id,
                    'source_type': 'crowdstrike',
                    'timestamp': datetime.utcnow().isoformat(),
                    'data': detection,
                    'log_id': f"{source_id}_{int(time.time())}_{idx}"
                }
                logs.append(log)

        except ImportError:
            self.logger.warning("CrowdStrike data source not available, using fallback")

        return logs

    async def _fetch_simulated_data(self, source_id: str, source_type: str, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate simulated data for testing"""
        logs = []
        batch_size = config.get('batch_size', 20)

        # Try to load real samples first
        try:
            import os
            sample_file = os.path.join(os.path.dirname(__file__), 'sample_logs.json')

            if os.path.exists(sample_file):
                with open(sample_file, 'r') as f:
                    real_logs = json.load(f)

                if real_logs:
                    for i in range(min(batch_size, len(real_logs))):
                        sample_index = (int(time.time()) + i) % len(real_logs)
                        sample_log = real_logs[sample_index]

                        # Extract the _source field if it exists
                        log_data = sample_log.get('_source', sample_log)

                        log = {
                            'source_id': source_id,
                            'source_type': source_type,
                            'timestamp': datetime.utcnow().isoformat(),
                            'data': log_data,
                            'log_id': f"{source_id}_{int(time.time())}_{i}"
                        }
                        logs.append(log)

                    return logs

        except Exception as e:
            self.logger.debug(f"Could not load real samples: {e}")

        # Generate realistic fallback data
        base_timestamp = datetime.utcnow().isoformat()

        for i in range(batch_size):
            if source_type == 'crowdstrike':
                log_data = {
                    'observer': {'vendor': 'CrowdStrike', 'product': 'Falcon'},
                    'event': {'action': 'detection', 'category': ['malware']},
                    'host': {'name': f'workstation-{int(time.time()) % 100}'},
                    'user': {'name': f'user{int(time.time()) % 50}'},
                    'process': {'name': 'powershell.exe'},
                    '@timestamp': base_timestamp
                }
            elif source_type == 'palo_alto':
                log_data = {
                    'observer': {'vendor': 'Palo Alto', 'product': 'Firewall'},
                    'event': {'action': 'allow', 'category': ['network']},
                    'source': {'ip': f"10.0.{int(time.time()) % 255}.{int(time.time()) % 255}"},
                    'destination': {'ip': f"192.168.{int(time.time()) % 255}.{int(time.time()) % 255}", 'port': 443},
                    'network': {'protocol': 'tcp'},
                    '@timestamp': base_timestamp
                }
            else:  # fortinet/elasticsearch or default
                log_data = {
                    'observer': {'vendor': 'Fortinet', 'product': 'Fortigate', 'name': 'firewall'},
                    'fortinet': {'firewall': {'type': 'traffic', 'action': 'allow'}},
                    'source': {'ip': f"192.168.{int(time.time()) % 255}.{int(time.time()) % 255}"},
                    'destination': {'ip': f"10.57.{int(time.time()) % 255}.{int(time.time()) % 255}"},
                    'event': {'action': 'allow', 'category': ['network']},
                    'network': {'protocol': 'tcp'},
                    '@timestamp': base_timestamp
                }

            log = {
                'source_id': source_id,
                'source_type': source_type,
                'timestamp': datetime.utcnow().isoformat(),
                'data': log_data,
                'log_id': f"{source_id}_{int(time.time())}_{i}"
            }
            logs.append(log)

        return logs

    def get_source_health_summary(self) -> Dict[str, Any]:
        """Get health summary for all sources"""
        total_sources = len(self.active_sources)
        healthy_sources = sum(1 for source_id in self.active_sources if self.is_source_healthy(source_id))

        return {
            'total_sources': total_sources,
            'healthy_sources': healthy_sources,
            'unhealthy_sources': total_sources - healthy_sources,
            'source_types': {
                source_type: len(self.get_sources_by_type(source_type))
                for source_type in self.config_manager.get_enabled_sources()
            }
        }