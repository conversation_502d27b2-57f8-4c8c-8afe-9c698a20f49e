"""
Rule Monitoring and Quality Assessment Script
Monitors detection rules for duplicates and quality
"""

import asyncpg
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RuleMonitor:
    def __init__(self, db_pool: asyncpg.Pool):
        self.db_pool = db_pool

    async def check_duplicates(self) -> List[Tuple]:
        """Find duplicate rules based on IOC value"""
        async with self.db_pool.acquire() as conn:
            duplicates = await conn.fetch("""
                SELECT
                    rule_data->>'ioc_value' as ioc_value,
                    rule_data->>'source' as source,
                    COUNT(*) as duplicate_count,
                    ARRAY_AGG(rule_id) as rule_ids
                FROM detection_rules
                WHERE rule_data->>'ioc_value' IS NOT NULL
                GROUP BY rule_data->>'ioc_value', rule_data->>'source'
                HAVING COUNT(*) > 1
                ORDER BY duplicate_count DESC
            """)

            return [tuple(row) for row in duplicates]

    async def assess_rule_quality(self, limit: int = 100) -> Dict[str, any]:
        """Assess quality of recent rules"""
        async with self.db_pool.acquire() as conn:
            rules = await conn.fetch("""
                SELECT rule_id, rule_data
                FROM detection_rules
                ORDER BY created_at DESC
                LIMIT $1
            """, limit)

        quality_stats = {
            'total': 0,
            'high_quality': 0,
            'medium_quality': 0,
            'low_quality': 0,
            'missing_critical_fields': 0,
            'by_source': {},
            'issues': []
        }

        for rule_id, rule_data in rules:
            quality_stats['total'] += 1

            # Check for quality score
            quality_score = rule_data.get('quality_score', 0)
            if quality_score >= 0.7:
                quality_stats['high_quality'] += 1
            elif quality_score >= 0.4:
                quality_stats['medium_quality'] += 1
            else:
                quality_stats['low_quality'] += 1

            # Track by source
            source = rule_data.get('source', 'unknown')
            if source not in quality_stats['by_source']:
                quality_stats['by_source'][source] = {'total': 0, 'avg_quality': 0}
            quality_stats['by_source'][source]['total'] += 1
            quality_stats['by_source'][source]['avg_quality'] += quality_score

            # Check for missing critical fields
            critical_fields = ['name', 'ioc_value', 'ioc_type', 'source']
            missing = [f for f in critical_fields if not rule_data.get(f)]
            if missing:
                quality_stats['missing_critical_fields'] += 1
                quality_stats['issues'].append({
                    'rule_id': rule_id,
                    'missing_fields': missing
                })

        # Calculate averages
        for source in quality_stats['by_source']:
            count = quality_stats['by_source'][source]['total']
            if count > 0:
                quality_stats['by_source'][source]['avg_quality'] /= count

        return quality_stats

    async def check_false_positive_candidates(self) -> List[Dict]:
        """Identify rules that might generate false positives"""
        async with self.db_pool.acquire() as conn:
            results = await conn.fetch("""
                SELECT rule_id, rule_data
                FROM detection_rules
                WHERE rule_data->'validation'->'potential_fps' IS NOT NULL
                    AND jsonb_array_length(rule_data->'validation'->'potential_fps') > 0
                ORDER BY created_at DESC
                LIMIT 50
            """)

            fp_candidates = []
            for row in results:
                rule_id = row['rule_id']
                rule_data = row['rule_data']
                validation = rule_data.get('validation', {})
                fp_candidates.append({
                    'rule_id': rule_id,
                    'name': rule_data.get('name', 'Unknown'),
                    'ioc_value': rule_data.get('ioc_value', ''),
                    'source': rule_data.get('source', ''),
                    'potential_fps': validation.get('potential_fps', []),
                    'quality_score': rule_data.get('quality_score', 0)
                })

            return fp_candidates

    async def get_effectiveness_metrics(self) -> Dict[str, any]:
        """Get overall effectiveness metrics for rules"""
        async with self.db_pool.acquire() as conn:
            # Count rules by quality tier
            tier_results = await conn.fetch("""
                SELECT
                    CASE
                        WHEN (rule_data->>'quality_score')::float >= 0.7 THEN 'high'
                        WHEN (rule_data->>'quality_score')::float >= 0.4 THEN 'medium'
                        ELSE 'low'
                    END as quality_tier,
                    COUNT(*) as count,
                    AVG((rule_data->>'quality_score')::float) as avg_score
                FROM detection_rules
                WHERE rule_data->>'quality_score' IS NOT NULL
                GROUP BY quality_tier
            """)

            quality_tiers = {row[0]: {'count': row[1], 'avg_score': float(row[2])}
                            for row in tier_results}

            # Count rules with MITRE ATT&CK mapping
            mitre_row = await conn.fetchrow("""
                SELECT COUNT(*)
                FROM detection_rules
                WHERE rule_data->'labels' @> '["attack"]'::jsonb
                    OR EXISTS (
                        SELECT 1 FROM jsonb_array_elements_text(rule_data->'labels') AS label
                        WHERE label LIKE 'T%' OR label LIKE 'attack.%'
                    )
            """)
            mitre_mapped = mitre_row[0]

            # Get total rules
            total_row = await conn.fetchrow("SELECT COUNT(*) FROM detection_rules")
            total_rules = total_row[0]

            return {
                'total_rules': total_rules,
                'quality_tiers': quality_tiers,
                'mitre_mapped_count': mitre_mapped,
                'mitre_coverage_percent': (mitre_mapped / total_rules * 100) if total_rules > 0 else 0
            }

    async def cleanup_duplicates(self, dry_run: bool = True) -> int:
        """Remove duplicate rules, keeping the highest quality one"""
        # Find duplicates
        duplicates = await self.check_duplicates()
        total_deleted = 0

        async with self.db_pool.acquire() as conn:
            async with conn.transaction():
                for ioc_value, source, count, rule_ids in duplicates:
                    # Get all duplicate rules with their quality scores
                    rules = await conn.fetch("""
                        SELECT rule_id,
                               (rule_data->>'quality_score')::float as quality,
                               created_at
                        FROM detection_rules
                        WHERE rule_id = ANY($1::uuid[])
                        ORDER BY
                            (rule_data->>'quality_score')::float DESC NULLS LAST,
                            created_at ASC
                    """, rule_ids)

                    if len(rules) > 1:
                        # Keep the first one (highest quality or oldest)
                        keep_id = rules[0]['rule_id']
                        delete_ids = [r['rule_id'] for r in rules[1:]]

                        if not dry_run:
                            await conn.execute("""
                                DELETE FROM detection_rules
                                WHERE rule_id = ANY($1::uuid[])
                            """, delete_ids)
                            total_deleted += len(delete_ids)
                            logger.info(f"Deleted {len(delete_ids)} duplicates for IOC: {ioc_value[:50]}")
                        else:
                            logger.info(f"Would delete {len(delete_ids)} duplicates for IOC: {ioc_value[:50]}")
                            total_deleted += len(delete_ids)

        return total_deleted

    async def generate_report(self):
        """Generate comprehensive rule quality report"""
        print("=" * 60)
        print("DETECTION RULE QUALITY REPORT")
        print("=" * 60)
        print(f"Generated: {datetime.now().isoformat()}")
        print()

        # Check duplicates
        duplicates = await self.check_duplicates()
        print(f"## DUPLICATE ANALYSIS")
        print(f"Found {len(duplicates)} sets of duplicate rules")
        if duplicates[:5]:
            print("\nTop 5 duplicated IOCs:")
            for ioc, source, count, _ in duplicates[:5]:
                print(f"  - {ioc[:50]:50} ({source}): {count} copies")
        print()

        # Quality assessment
        quality_stats = await self.assess_rule_quality()
        print(f"## QUALITY ASSESSMENT (Last 100 Rules)")
        print(f"High Quality (>0.7):   {quality_stats['high_quality']:4} ({quality_stats['high_quality']/quality_stats['total']*100:.1f}%)")
        print(f"Medium Quality (0.4-0.7): {quality_stats['medium_quality']:4} ({quality_stats['medium_quality']/quality_stats['total']*100:.1f}%)")
        print(f"Low Quality (<0.4):    {quality_stats['low_quality']:4} ({quality_stats['low_quality']/quality_stats['total']*100:.1f}%)")
        print(f"Missing Critical Fields: {quality_stats['missing_critical_fields']}")
        print()

        print("Quality by Source:")
        for source, stats in quality_stats['by_source'].items():
            print(f"  {source:15} Avg Score: {stats['avg_quality']:.2f} (n={stats['total']})")
        print()

        # False positive candidates
        fp_candidates = await self.check_false_positive_candidates()
        if fp_candidates:
            print(f"## POTENTIAL FALSE POSITIVES")
            print(f"Found {len(fp_candidates)} rules with FP warnings:")
            for rule in fp_candidates[:5]:
                print(f"  - {rule['name'][:40]:40} ({rule['source']})")
                for fp in rule['potential_fps']:
                    print(f"    Warning: {fp}")
        print()

        # Effectiveness metrics
        metrics = await self.get_effectiveness_metrics()
        print(f"## EFFECTIVENESS METRICS")
        print(f"Total Rules: {metrics['total_rules']}")
        print(f"MITRE ATT&CK Mapped: {metrics['mitre_mapped_count']} ({metrics['mitre_coverage_percent']:.1f}%)")
        print()

        print("Quality Distribution:")
        for tier, data in metrics['quality_tiers'].items():
            print(f"  {tier.capitalize():10} {data['count']:5} rules (avg: {data['avg_score']:.2f})")
        print()

        print("=" * 60)


async def main():
    # Database configuration
    db_pool = await asyncpg.create_pool(
        host='localhost',
        port=5433,
        database='siemless_v2',
        user='siemless',
        password='siemless123'
    )

    try:
        monitor = RuleMonitor(db_pool)

        # Generate report
        await monitor.generate_report()

        # Optional: Clean duplicates
        print("\n## DUPLICATE CLEANUP")
        deleted = await monitor.cleanup_duplicates(dry_run=True)
        print(f"Would delete {deleted} duplicate rules (dry run)")

        # Uncomment to actually delete:
        # deleted = await monitor.cleanup_duplicates(dry_run=False)
        # print(f"Deleted {deleted} duplicate rules")

    finally:
        await db_pool.close()


if __name__ == "__main__":
    asyncio.run(main())