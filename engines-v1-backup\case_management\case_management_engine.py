#!/usr/bin/env python3
"""
SIEMLess v2.0 Case Management Engine

Comprehensive incident case management, investigation tracking, and response coordination.
Integrates with all SIEMLess engines for complete incident lifecycle management.

Author: SIEMLess Development Team
Version: 2.0.0
"""

import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
import logging

from shared.base_engine import BaseEngine
from shared.queue.queue_manager import QueueManager
from shared.logging.siemless_logger import SIEMLessLogger

class CaseStatus(Enum):
    """Case status enumeration"""
    NEW = "new"
    ASSIGNED = "assigned"
    IN_PROGRESS = "in_progress"
    PENDING_REVIEW = "pending_review"
    RESOLVED = "resolved"
    CLOSED = "closed"
    ESCALATED = "escalated"

class CasePriority(Enum):
    """Case priority levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"

class CaseType(Enum):
    """Types of security cases"""
    INCIDENT = "incident"
    INVESTIGATION = "investigation"
    THREAT_HUNT = "threat_hunt"
    COMPLIANCE = "compliance"
    VULNERABILITY = "vulnerability"
    MALWARE_ANALYSIS = "malware_analysis"
    PHISHING = "phishing"
    DATA_BREACH = "data_breach"
    INSIDER_THREAT = "insider_threat"

class EvidenceType(Enum):
    """Types of digital evidence"""
    LOG_FILE = "log_file"
    NETWORK_CAPTURE = "network_capture"
    MEMORY_DUMP = "memory_dump"
    DISK_IMAGE = "disk_image"
    FILE_SAMPLE = "file_sample"
    SCREENSHOT = "screenshot"
    EMAIL = "email"
    DOCUMENT = "document"
    TIMELINE = "timeline"
    IOCS = "iocs"

@dataclass
class CaseEvidence:
    """Digital evidence attached to a case"""
    evidence_id: str
    evidence_type: EvidenceType
    source_engine: str
    file_path: Optional[str]
    file_hash: Optional[str]
    description: str
    collected_at: datetime
    collected_by: str
    chain_of_custody: List[Dict[str, Any]]
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['evidence_type'] = self.evidence_type.value
        data['collected_at'] = self.collected_at.isoformat()
        return data

@dataclass
class CaseActivity:
    """Activity log entry for a case"""
    activity_id: str
    case_id: str
    activity_type: str
    description: str
    performed_by: str
    performed_at: datetime
    details: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['performed_at'] = self.performed_at.isoformat()
        return data

@dataclass
class SecurityCase:
    """Core case management entity"""
    case_id: str
    title: str
    description: str
    case_type: CaseType
    status: CaseStatus
    priority: CasePriority
    assigned_to: Optional[str]
    created_by: str
    created_at: datetime
    updated_at: datetime
    due_date: Optional[datetime]

    # Related entities
    related_entities: List[str]  # Entity IDs from entity engine
    related_detections: List[str]  # Detection IDs
    related_investigations: List[str]  # Investigation IDs

    # Evidence and artifacts
    evidence: List[CaseEvidence]
    activities: List[CaseActivity]

    # MITRE ATT&CK mapping
    mitre_techniques: List[str]
    mitre_tactics: List[str]

    # Metrics and SLAs
    response_time: Optional[timedelta]
    resolution_time: Optional[timedelta]
    sla_breach: bool

    # Tags and categorization
    tags: List[str]
    severity_score: float
    impact_score: float

    # Integration data
    external_case_ids: Dict[str, str]  # External system case IDs
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['case_type'] = self.case_type.value
        data['status'] = self.status.value
        data['priority'] = self.priority.value
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        if self.due_date:
            data['due_date'] = self.due_date.isoformat()
        data['evidence'] = [ev.to_dict() for ev in self.evidence]
        data['activities'] = [act.to_dict() for act in self.activities]
        return data

class CaseManagementEngine(BaseEngine):
    """
    Case Management Engine for SIEMLess v2.0

    Provides comprehensive incident case management, investigation tracking,
    and response coordination capabilities.
    """

    def __init__(self):
        super().__init__("case_management", "2.0.0")
        self.cases: Dict[str, SecurityCase] = {}
        self.case_templates: Dict[str, Dict[str, Any]] = {}
        self.sla_policies: Dict[str, Dict[str, Any]] = {}
        self.escalation_rules: List[Dict[str, Any]] = []

        # Initialize case templates
        self._initialize_case_templates()
        self._initialize_sla_policies()
        self._initialize_escalation_rules()

    async def initialize(self) -> bool:
        """Initialize the Case Management Engine"""
        try:
            self.logger.info("Initializing Case Management Engine v2.0")

            # Register message handlers
            await self.queue_manager.register_handler(
                "case.create", self._handle_create_case
            )
            await self.queue_manager.register_handler(
                "case.update", self._handle_update_case
            )
            await self.queue_manager.register_handler(
                "case.assign", self._handle_assign_case
            )
            await self.queue_manager.register_handler(
                "case.add_evidence", self._handle_add_evidence
            )
            await self.queue_manager.register_handler(
                "case.escalate", self._handle_escalate_case
            )
            await self.queue_manager.register_handler(
                "detection.alert", self._handle_detection_alert
            )

            # Load existing cases from storage
            await self._load_existing_cases()

            # Start background tasks
            asyncio.create_task(self._sla_monitor())
            asyncio.create_task(self._escalation_monitor())

            self.logger.info("Case Management Engine initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Case Management Engine: {e}")
            return False

    def _initialize_case_templates(self):
        """Initialize standard case templates"""
        self.case_templates = {
            "malware_incident": {
                "title_template": "Malware Detection: {malware_family}",
                "description_template": "Malware detected on {affected_systems}. Family: {malware_family}",
                "case_type": CaseType.INCIDENT,
                "priority": CasePriority.HIGH,
                "required_evidence": [EvidenceType.FILE_SAMPLE, EvidenceType.LOG_FILE],
                "mitre_tactics": ["initial-access", "execution", "persistence"],
                "sla_hours": 4
            },
            "phishing_incident": {
                "title_template": "Phishing Campaign: {email_subject}",
                "description_template": "Phishing email detected. Subject: {email_subject}",
                "case_type": CaseType.PHISHING,
                "priority": CasePriority.MEDIUM,
                "required_evidence": [EvidenceType.EMAIL, EvidenceType.SCREENSHOT],
                "mitre_tactics": ["initial-access"],
                "sla_hours": 8
            },
            "data_breach": {
                "title_template": "Data Breach: {affected_systems}",
                "description_template": "Potential data breach detected on {affected_systems}",
                "case_type": CaseType.DATA_BREACH,
                "priority": CasePriority.CRITICAL,
                "required_evidence": [EvidenceType.LOG_FILE, EvidenceType.NETWORK_CAPTURE],
                "mitre_tactics": ["exfiltration"],
                "sla_hours": 1
            },
            "insider_threat": {
                "title_template": "Insider Threat: {user_account}",
                "description_template": "Suspicious activity by {user_account}",
                "case_type": CaseType.INSIDER_THREAT,
                "priority": CasePriority.HIGH,
                "required_evidence": [EvidenceType.LOG_FILE, EvidenceType.TIMELINE],
                "mitre_tactics": ["collection", "exfiltration"],
                "sla_hours": 6
            }
        }

    def _initialize_sla_policies(self):
        """Initialize SLA policies by priority and type"""
        self.sla_policies = {
            "critical": {"response_hours": 1, "resolution_hours": 4},
            "high": {"response_hours": 2, "resolution_hours": 8},
            "medium": {"response_hours": 4, "resolution_hours": 24},
            "low": {"response_hours": 8, "resolution_hours": 72},
            "info": {"response_hours": 24, "resolution_hours": 168}
        }

    def _initialize_escalation_rules(self):
        """Initialize automatic escalation rules"""
        self.escalation_rules = [
            {
                "name": "critical_overdue",
                "condition": {"priority": "critical", "overdue_hours": 2},
                "action": {"escalate_to": "security_manager", "notify": True}
            },
            {
                "name": "high_overdue",
                "condition": {"priority": "high", "overdue_hours": 4},
                "action": {"escalate_to": "senior_analyst", "notify": True}
            },
            {
                "name": "unassigned_critical",
                "condition": {"priority": "critical", "unassigned_hours": 0.5},
                "action": {"auto_assign": True, "notify": True}
            }
        ]

    async def create_case(self, case_data: Dict[str, Any]) -> SecurityCase:
        """Create a new security case"""
        try:
            case_id = str(uuid.uuid4())
            current_time = datetime.utcnow()

            # Apply template if specified
            template_name = case_data.get('template')
            if template_name and template_name in self.case_templates:
                template = self.case_templates[template_name]
                case_data = {**template, **case_data}  # Template as defaults

            # Calculate due date based on SLA
            priority = case_data.get('priority', CasePriority.MEDIUM.value)
            sla_policy = self.sla_policies.get(priority, self.sla_policies['medium'])
            due_date = current_time + timedelta(hours=sla_policy['resolution_hours'])

            # Create the case
            case = SecurityCase(
                case_id=case_id,
                title=case_data['title'],
                description=case_data['description'],
                case_type=CaseType(case_data.get('case_type', CaseType.INCIDENT.value)),
                status=CaseStatus.NEW,
                priority=CasePriority(priority),
                assigned_to=case_data.get('assigned_to'),
                created_by=case_data['created_by'],
                created_at=current_time,
                updated_at=current_time,
                due_date=due_date,
                related_entities=case_data.get('related_entities', []),
                related_detections=case_data.get('related_detections', []),
                related_investigations=case_data.get('related_investigations', []),
                evidence=[],
                activities=[],
                mitre_techniques=case_data.get('mitre_techniques', []),
                mitre_tactics=case_data.get('mitre_tactics', []),
                response_time=None,
                resolution_time=None,
                sla_breach=False,
                tags=case_data.get('tags', []),
                severity_score=case_data.get('severity_score', 5.0),
                impact_score=case_data.get('impact_score', 5.0),
                external_case_ids=case_data.get('external_case_ids', {}),
                metadata=case_data.get('metadata', {})
            )

            # Add initial activity
            await self._add_activity(case, "case_created", "Case created", case_data['created_by'])

            # Store the case
            self.cases[case_id] = case

            # Broadcast case creation
            await self.queue_manager.publish("case.created", {
                "case_id": case_id,
                "case_data": case.to_dict()
            })

            self.logger.info(f"Created case {case_id}: {case.title}")
            return case

        except Exception as e:
            self.logger.error(f"Failed to create case: {e}")
            raise

    async def update_case(self, case_id: str, updates: Dict[str, Any], updated_by: str) -> Optional[SecurityCase]:
        """Update an existing case"""
        try:
            if case_id not in self.cases:
                self.logger.warning(f"Case {case_id} not found for update")
                return None

            case = self.cases[case_id]
            old_status = case.status

            # Apply updates
            for key, value in updates.items():
                if hasattr(case, key):
                    setattr(case, key, value)

            case.updated_at = datetime.utcnow()

            # Log status changes
            if 'status' in updates and old_status != case.status:
                await self._add_activity(
                    case, "status_changed",
                    f"Status changed from {old_status.value} to {case.status.value}",
                    updated_by
                )

                # Calculate response/resolution times
                if case.status == CaseStatus.ASSIGNED and not case.response_time:
                    case.response_time = datetime.utcnow() - case.created_at
                elif case.status in [CaseStatus.RESOLVED, CaseStatus.CLOSED] and not case.resolution_time:
                    case.resolution_time = datetime.utcnow() - case.created_at

            # Broadcast case update
            await self.queue_manager.publish("case.updated", {
                "case_id": case_id,
                "updates": updates,
                "updated_by": updated_by
            })

            self.logger.info(f"Updated case {case_id}")
            return case

        except Exception as e:
            self.logger.error(f"Failed to update case {case_id}: {e}")
            return None

    async def add_evidence(self, case_id: str, evidence_data: Dict[str, Any]) -> Optional[CaseEvidence]:
        """Add evidence to a case"""
        try:
            if case_id not in self.cases:
                self.logger.warning(f"Case {case_id} not found for evidence addition")
                return None

            case = self.cases[case_id]
            evidence_id = str(uuid.uuid4())

            evidence = CaseEvidence(
                evidence_id=evidence_id,
                evidence_type=EvidenceType(evidence_data['evidence_type']),
                source_engine=evidence_data['source_engine'],
                file_path=evidence_data.get('file_path'),
                file_hash=evidence_data.get('file_hash'),
                description=evidence_data['description'],
                collected_at=datetime.utcnow(),
                collected_by=evidence_data['collected_by'],
                chain_of_custody=[{
                    "action": "collected",
                    "by": evidence_data['collected_by'],
                    "at": datetime.utcnow().isoformat(),
                    "details": evidence_data.get('collection_details', {})
                }],
                metadata=evidence_data.get('metadata', {})
            )

            case.evidence.append(evidence)
            case.updated_at = datetime.utcnow()

            await self._add_activity(
                case, "evidence_added",
                f"Evidence added: {evidence.description}",
                evidence_data['collected_by']
            )

            # Broadcast evidence addition
            await self.queue_manager.publish("case.evidence_added", {
                "case_id": case_id,
                "evidence_id": evidence_id,
                "evidence_data": evidence.to_dict()
            })

            self.logger.info(f"Added evidence {evidence_id} to case {case_id}")
            return evidence

        except Exception as e:
            self.logger.error(f"Failed to add evidence to case {case_id}: {e}")
            return None

    async def _add_activity(self, case: SecurityCase, activity_type: str, description: str, performed_by: str, details: Dict[str, Any] = None):
        """Add an activity log entry to a case"""
        activity = CaseActivity(
            activity_id=str(uuid.uuid4()),
            case_id=case.case_id,
            activity_type=activity_type,
            description=description,
            performed_by=performed_by,
            performed_at=datetime.utcnow(),
            details=details or {}
        )

        case.activities.append(activity)

    async def _handle_create_case(self, message: Dict[str, Any]):
        """Handle case creation requests"""
        try:
            case_data = message.get('case_data', {})
            case = await self.create_case(case_data)

            # Send response if reply_to is specified
            if 'reply_to' in message:
                await self.queue_manager.publish(message['reply_to'], {
                    "success": True,
                    "case_id": case.case_id,
                    "case_data": case.to_dict()
                })

        except Exception as e:
            self.logger.error(f"Error handling case creation: {e}")
            if 'reply_to' in message:
                await self.queue_manager.publish(message['reply_to'], {
                    "success": False,
                    "error": str(e)
                })

    async def _handle_update_case(self, message: Dict[str, Any]):
        """Handle case update requests"""
        try:
            case_id = message.get('case_id')
            updates = message.get('updates', {})
            updated_by = message.get('updated_by', 'system')

            case = await self.update_case(case_id, updates, updated_by)

            if 'reply_to' in message:
                await self.queue_manager.publish(message['reply_to'], {
                    "success": case is not None,
                    "case_data": case.to_dict() if case else None
                })

        except Exception as e:
            self.logger.error(f"Error handling case update: {e}")

    async def _handle_assign_case(self, message: Dict[str, Any]):
        """Handle case assignment requests"""
        try:
            case_id = message.get('case_id')
            assigned_to = message.get('assigned_to')
            assigned_by = message.get('assigned_by', 'system')

            case = await self.update_case(
                case_id,
                {'assigned_to': assigned_to, 'status': CaseStatus.ASSIGNED.value},
                assigned_by
            )

            if case:
                await self._add_activity(
                    case, "case_assigned",
                    f"Case assigned to {assigned_to}",
                    assigned_by
                )

        except Exception as e:
            self.logger.error(f"Error handling case assignment: {e}")

    async def _handle_add_evidence(self, message: Dict[str, Any]):
        """Handle evidence addition requests"""
        try:
            case_id = message.get('case_id')
            evidence_data = message.get('evidence_data', {})

            evidence = await self.add_evidence(case_id, evidence_data)

            if 'reply_to' in message:
                await self.queue_manager.publish(message['reply_to'], {
                    "success": evidence is not None,
                    "evidence_data": evidence.to_dict() if evidence else None
                })

        except Exception as e:
            self.logger.error(f"Error handling evidence addition: {e}")

    async def _handle_escalate_case(self, message: Dict[str, Any]):
        """Handle case escalation requests"""
        try:
            case_id = message.get('case_id')
            escalated_by = message.get('escalated_by', 'system')
            reason = message.get('reason', 'Manual escalation')

            case = await self.update_case(
                case_id,
                {'status': CaseStatus.ESCALATED.value},
                escalated_by
            )

            if case:
                await self._add_activity(
                    case, "case_escalated",
                    f"Case escalated: {reason}",
                    escalated_by
                )

        except Exception as e:
            self.logger.error(f"Error handling case escalation: {e}")

    async def _handle_detection_alert(self, message: Dict[str, Any]):
        """Handle detection alerts and auto-create cases"""
        try:
            detection_data = message.get('detection_data', {})

            # Check if case should be auto-created
            if detection_data.get('severity') in ['high', 'critical']:
                case_data = {
                    'title': f"Detection Alert: {detection_data.get('rule_name', 'Unknown')}",
                    'description': detection_data.get('description', 'Automated case from detection alert'),
                    'case_type': CaseType.INCIDENT.value,
                    'priority': CasePriority.HIGH.value if detection_data.get('severity') == 'high' else CasePriority.CRITICAL.value,
                    'created_by': 'detection_engine',
                    'related_detections': [detection_data.get('detection_id')],
                    'mitre_techniques': detection_data.get('mitre_techniques', []),
                    'mitre_tactics': detection_data.get('mitre_tactics', []),
                    'tags': ['auto_created', 'detection_alert'],
                    'metadata': {'source_detection': detection_data}
                }

                case = await self.create_case(case_data)
                self.logger.info(f"Auto-created case {case.case_id} from detection alert")

        except Exception as e:
            self.logger.error(f"Error handling detection alert: {e}")

    async def _sla_monitor(self):
        """Monitor SLA compliance and update breach status"""
        while True:
            try:
                current_time = datetime.utcnow()

                for case_id, case in self.cases.items():
                    if case.status not in [CaseStatus.RESOLVED, CaseStatus.CLOSED]:
                        # Check if case is overdue
                        if case.due_date and current_time > case.due_date:
                            if not case.sla_breach:
                                case.sla_breach = True
                                await self._add_activity(
                                    case, "sla_breach",
                                    "SLA deadline exceeded",
                                    "system"
                                )

                                # Broadcast SLA breach
                                await self.queue_manager.publish("case.sla_breach", {
                                    "case_id": case_id,
                                    "breach_time": current_time.isoformat()
                                })

                # Sleep for 5 minutes before next check
                await asyncio.sleep(300)

            except Exception as e:
                self.logger.error(f"Error in SLA monitor: {e}")
                await asyncio.sleep(60)

    async def _escalation_monitor(self):
        """Monitor escalation rules and auto-escalate cases"""
        while True:
            try:
                current_time = datetime.utcnow()

                for case_id, case in self.cases.items():
                    if case.status in [CaseStatus.NEW, CaseStatus.ASSIGNED, CaseStatus.IN_PROGRESS]:
                        # Check escalation rules
                        for rule in self.escalation_rules:
                            if self._should_escalate(case, rule, current_time):
                                await self._auto_escalate(case, rule)

                # Sleep for 10 minutes before next check
                await asyncio.sleep(600)

            except Exception as e:
                self.logger.error(f"Error in escalation monitor: {e}")
                await asyncio.sleep(60)

    def _should_escalate(self, case: SecurityCase, rule: Dict[str, Any], current_time: datetime) -> bool:
        """Check if a case should be escalated based on rule"""
        condition = rule['condition']

        # Check priority condition
        if 'priority' in condition and case.priority.value != condition['priority']:
            return False

        # Check overdue condition
        if 'overdue_hours' in condition:
            if case.due_date and current_time > case.due_date + timedelta(hours=condition['overdue_hours']):
                return True

        # Check unassigned condition
        if 'unassigned_hours' in condition and not case.assigned_to:
            hours_since_created = (current_time - case.created_at).total_seconds() / 3600
            if hours_since_created >= condition['unassigned_hours']:
                return True

        return False

    async def _auto_escalate(self, case: SecurityCase, rule: Dict[str, Any]):
        """Auto-escalate a case based on rule"""
        action = rule['action']

        if 'escalate_to' in action:
            await self.update_case(
                case.case_id,
                {'assigned_to': action['escalate_to'], 'status': CaseStatus.ESCALATED.value},
                'system'
            )

            await self._add_activity(
                case, "auto_escalated",
                f"Auto-escalated due to rule: {rule['name']}",
                "system"
            )

        if action.get('notify'):
            await self.queue_manager.publish("notification.send", {
                "type": "case_escalation",
                "case_id": case.case_id,
                "rule": rule['name']
            })

    async def _load_existing_cases(self):
        """Load existing cases from persistent storage"""
        # Implementation would load from database/storage
        # For now, start with empty case store
        self.logger.info("Case storage initialized (in-memory)")

    async def get_case(self, case_id: str) -> Optional[SecurityCase]:
        """Get a case by ID"""
        return self.cases.get(case_id)

    async def list_cases(self, filters: Dict[str, Any] = None) -> List[SecurityCase]:
        """List cases with optional filters"""
        cases = list(self.cases.values())

        if filters:
            # Apply filters
            if 'status' in filters:
                cases = [c for c in cases if c.status.value == filters['status']]
            if 'priority' in filters:
                cases = [c for c in cases if c.priority.value == filters['priority']]
            if 'assigned_to' in filters:
                cases = [c for c in cases if c.assigned_to == filters['assigned_to']]
            if 'case_type' in filters:
                cases = [c for c in cases if c.case_type.value == filters['case_type']]

        # Sort by priority and creation date
        priority_order = {CasePriority.CRITICAL: 0, CasePriority.HIGH: 1, CasePriority.MEDIUM: 2, CasePriority.LOW: 3, CasePriority.INFO: 4}
        cases.sort(key=lambda c: (priority_order[c.priority], c.created_at), reverse=True)

        return cases

    async def get_case_metrics(self) -> Dict[str, Any]:
        """Get case management metrics"""
        total_cases = len(self.cases)
        if total_cases == 0:
            return {"total_cases": 0}

        # Status distribution
        status_dist = {}
        for case in self.cases.values():
            status = case.status.value
            status_dist[status] = status_dist.get(status, 0) + 1

        # Priority distribution
        priority_dist = {}
        for case in self.cases.values():
            priority = case.priority.value
            priority_dist[priority] = priority_dist.get(priority, 0) + 1

        # SLA metrics
        sla_breaches = sum(1 for case in self.cases.values() if case.sla_breach)

        # Response time metrics
        response_times = [case.response_time.total_seconds() / 3600 for case in self.cases.values() if case.response_time]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0

        # Resolution time metrics
        resolution_times = [case.resolution_time.total_seconds() / 3600 for case in self.cases.values() if case.resolution_time]
        avg_resolution_time = sum(resolution_times) / len(resolution_times) if resolution_times else 0

        return {
            "total_cases": total_cases,
            "status_distribution": status_dist,
            "priority_distribution": priority_dist,
            "sla_breaches": sla_breaches,
            "sla_compliance_rate": ((total_cases - sla_breaches) / total_cases) * 100,
            "avg_response_time_hours": round(avg_response_time, 2),
            "avg_resolution_time_hours": round(avg_resolution_time, 2),
            "open_cases": sum(1 for case in self.cases.values() if case.status not in [CaseStatus.RESOLVED, CaseStatus.CLOSED])
        }

async def main():
    """Main entry point for Case Management Engine"""
    engine = CaseManagementEngine()

    if await engine.initialize():
        print("Case Management Engine v2.0 started successfully")
        try:
            # Keep the engine running
            await asyncio.sleep(float('inf'))
        except KeyboardInterrupt:
            print("Shutting down Case Management Engine...")
    else:
        print("Failed to start Case Management Engine")
        return 1

    return 0

if __name__ == "__main__":
    asyncio.run(main())