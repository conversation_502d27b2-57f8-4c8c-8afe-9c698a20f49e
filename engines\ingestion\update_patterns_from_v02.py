#!/usr/bin/env python3
"""
Update pattern library with comprehensive entity extractors based on v0.2 log analysis
"""

import json
import psycopg2
from datetime import datetime

# Database connection
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'siemless_v2',
    'user': 'siemless',
    'password': 'password123'
}

# Comprehensive pattern configurations based on v0.2 analysis
COMPREHENSIVE_PATTERNS = [
    {
        'pattern_id': 'fortinet_traffic',
        'pattern_type': 'traffic',
        'pattern_data': {
            'regex': r'fortinet|fortigate|firewall.*traffic|vlan.*vip',
            'keywords': ['fortinet', 'fortigate', 'traffic', 'firewall'],
            'entity_extractors': {
                'ip_fields': [
                    'source.ip',
                    'destination.ip',
                    '_source.source.ip',
                    '_source.destination.ip',
                    'fortinet.firewall.srcip',
                    'fortinet.firewall.dstip'
                ],
                'host_fields': [
                    'observer.name',
                    '_source.observer.name',
                    'fortinet.firewall.devname',
                    'host.name'
                ],
                'user_fields': [
                    'user.name',
                    '_source.user.name',
                    'fortinet.firewall.user'
                ],
                'mac_fields': [
                    'source.mac',
                    'destination.mac',
                    '_source.source.mac',
                    '_source.destination.mac'
                ],
                'port_fields': [
                    'source.port',
                    'destination.port',
                    '_source.source.port',
                    '_source.destination.port'
                ]
            }
        },
        'source': 'v0.2_analysis',
        'is_active': True,
        'usage_count': 0
    },
    {
        'pattern_id': 'crowdstrike_detection',
        'pattern_type': 'detection',
        'pattern_data': {
            'regex': r'crowdstrike|falcon|detection|malicious.*tool|post-exploit',
            'keywords': ['crowdstrike', 'falcon', 'detection', 'malicious'],
            'entity_extractors': {
                'host_fields': [
                    'device.hostname',
                    'hostinfo.hostname',
                    'device.device_name'
                ],
                'user_fields': [
                    'device.user',
                    'user.name',
                    'hostinfo.local_username'
                ],
                'file_fields': [
                    'behaviors.filename',
                    'behaviors.filepath',
                    'quarantined_files.filename'
                ],
                'process_fields': [
                    'behaviors.cmdline',
                    'behaviors.filename',
                    'process.name'
                ],
                'ip_fields': [
                    'device.local_ip',
                    'device.external_ip',
                    'hostinfo.local_ip'
                ],
                'hash_fields': [
                    'behaviors.sha256',
                    'behaviors.md5',
                    'quarantined_files.sha256'
                ]
            }
        },
        'source': 'v0.2_analysis',
        'is_active': True,
        'usage_count': 0
    },
    {
        'pattern_id': 'authentication_events',
        'pattern_type': 'authentication',
        'pattern_data': {
            'regex': r'login|logon|authentication|auth.*failed|password',
            'keywords': ['login', 'authentication', 'password', 'logon'],
            'entity_extractors': {
                'user_fields': [
                    'user.name',
                    'winlog.event_data.TargetUserName',
                    'event.username'
                ],
                'ip_fields': [
                    'source.ip',
                    'client.ip',
                    'winlog.event_data.IpAddress'
                ],
                'host_fields': [
                    'host.name',
                    'computer_name',
                    'winlog.computer_name'
                ]
            }
        },
        'source': 'v0.2_analysis',
        'is_active': True,
        'usage_count': 0
    },
    {
        'pattern_id': 'network_connection',
        'pattern_type': 'network',
        'pattern_data': {
            'regex': r'connection|tcp|udp|network|port.*\\d+',
            'keywords': ['connection', 'network', 'tcp', 'udp'],
            'entity_extractors': {
                'ip_fields': [
                    'source.ip',
                    'destination.ip',
                    'client.ip',
                    'server.ip',
                    'network.source.ip',
                    'network.destination.ip'
                ],
                'port_fields': [
                    'source.port',
                    'destination.port',
                    'server.port',
                    'client.port',
                    'network.source.port',
                    'network.destination.port'
                ],
                'protocol_fields': [
                    'network.protocol',
                    'network.transport',
                    'network.application'
                ]
            }
        },
        'source': 'v0.2_analysis',
        'is_active': True,
        'usage_count': 0
    },
    {
        'pattern_id': 'malware_detection',
        'pattern_type': 'malware',
        'pattern_data': {
            'regex': r'malware|virus|trojan|malicious|suspicious|threat',
            'keywords': ['malware', 'virus', 'trojan', 'threat'],
            'entity_extractors': {
                'hash_fields': [
                    'file.hash.sha256',
                    'file.hash.md5',
                    'threat.indicator.file.hash.sha256'
                ],
                'file_fields': [
                    'file.name',
                    'file.path',
                    'process.executable',
                    'threat.indicator.file.name'
                ],
                'host_fields': [
                    'host.name',
                    'device.hostname'
                ],
                'user_fields': [
                    'user.name',
                    'process.user.name'
                ]
            }
        },
        'source': 'v0.2_analysis',
        'is_active': True,
        'usage_count': 0
    }
]

def update_patterns():
    """Update pattern library with comprehensive extractors"""
    try:
        # Connect to database
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()

        # First, check existing patterns
        cursor.execute("SELECT pattern_id FROM pattern_library")
        existing = {row[0] for row in cursor.fetchall()}
        print(f"Found {len(existing)} existing patterns")

        # Update or insert patterns
        for pattern in COMPREHENSIVE_PATTERNS:
            pattern_id = pattern['pattern_id']

            if pattern_id in existing:
                # Update existing pattern
                cursor.execute("""
                    UPDATE pattern_library
                    SET pattern_data = %s,
                        updated_at = NOW()
                    WHERE pattern_id = %s
                """, (json.dumps(pattern['pattern_data']), pattern_id))
                print(f"Updated pattern: {pattern_id}")
            else:
                # Insert new pattern
                cursor.execute("""
                    INSERT INTO pattern_library
                    (pattern_id, pattern_type, pattern_data, source, is_active, usage_count, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, NOW())
                """, (
                    pattern_id,
                    pattern['pattern_type'],
                    json.dumps(pattern['pattern_data']),
                    pattern['source'],
                    pattern['is_active'],
                    pattern['usage_count']
                ))
                print(f"Added new pattern: {pattern_id}")

        conn.commit()
        print(f"\nSuccessfully updated {len(COMPREHENSIVE_PATTERNS)} patterns")

        # Verify the updates
        cursor.execute("""
            SELECT pattern_id, pattern_type,
                   jsonb_array_length(COALESCE(pattern_data->'entity_extractors'->'ip_fields', '[]'::jsonb)) as ip_extractors,
                   jsonb_array_length(COALESCE(pattern_data->'entity_extractors'->'host_fields', '[]'::jsonb)) as host_extractors,
                   jsonb_array_length(COALESCE(pattern_data->'entity_extractors'->'user_fields', '[]'::jsonb)) as user_extractors
            FROM pattern_library
            WHERE source = 'v0.2_analysis'
        """)

        print("\nPattern Entity Extractors Summary:")
        print("-" * 70)
        for row in cursor.fetchall():
            print(f"{row[0]:25} | Type: {row[1]:15} | IPs: {row[2]:2} | Hosts: {row[3]:2} | Users: {row[4]:2}")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"Error updating patterns: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    update_patterns()