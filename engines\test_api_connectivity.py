#!/usr/bin/env python3
"""
Quick API Connectivity Test - Diagnose timeout issues
"""

import requests
import os
import time
import json

def test_api_endpoints():
    print("=== Quick API Connectivity Test ===")

    # Test CrowdStrike
    print("\n1. CrowdStrike API Test:")
    cs_base = os.getenv('CROWDSTRIKE_BASE_URL', 'https://api.us-2.crowdstrike.com')
    cs_client_id = os.getenv('CROWDSTRIKE_CLIENT_ID')

    if cs_client_id:
        print(f"   Base URL: {cs_base}")
        print(f"   Client ID: {cs_client_id[:8]}...")
        try:
            start = time.time()
            response = requests.get(f"{cs_base}/oauth2/token", timeout=5)
            elapsed = time.time() - start
            print(f"   Response: {response.status_code} in {elapsed:.2f}s")
        except Exception as e:
            print(f"   Error: {e}")
    else:
        print("   No credentials found")

    # Test OpenCTI
    print("\n2. OpenCTI API Test:")
    opencti_url = os.getenv('OPENCTI_URL', 'http://10.102.0.51:8080/')
    opencti_token = os.getenv('OPENCTI_TOKEN')

    if opencti_token:
        print(f"   URL: {opencti_url}")
        print(f"   Token: {opencti_token[:8]}...")
        try:
            start = time.time()
            response = requests.get(f"{opencti_url}/health", timeout=5)
            elapsed = time.time() - start
            print(f"   Response: {response.status_code} in {elapsed:.2f}s")
        except Exception as e:
            print(f"   Error: {e}")
    else:
        print("   No credentials found")

    # Test ElasticSearch
    print("\n3. ElasticSearch API Test:")
    elastic_cloud_id = os.getenv('ELASTIC_CLOUD_ID')
    elastic_api_key = os.getenv('ELASTIC_API_KEY')

    if elastic_cloud_id and elastic_api_key:
        print(f"   Cloud ID: {elastic_cloud_id[:20]}...")
        print(f"   API Key: {elastic_api_key[:8]}...")

        # Try to construct endpoint
        try:
            import base64
            cloud_parts = elastic_cloud_id.split(':')
            if len(cloud_parts) >= 2:
                encoded_endpoint = cloud_parts[1]
                decoded = base64.b64decode(encoded_endpoint + '==').decode('utf-8')
                es_endpoint = f"https://{decoded.split('$')[0]}"
                print(f"   Endpoint: {es_endpoint}")

                headers = {'Authorization': f'ApiKey {elastic_api_key}'}
                start = time.time()
                response = requests.get(f"{es_endpoint}/", headers=headers, timeout=5)
                elapsed = time.time() - start
                print(f"   Response: {response.status_code} in {elapsed:.2f}s")
            else:
                print("   Invalid cloud_id format")
        except Exception as e:
            print(f"   Error: {e}")
    else:
        print("   No credentials found")

    # Test Redis
    print("\n4. Redis Connectivity Test:")
    try:
        import redis
        redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)
        start = time.time()
        result = redis_client.ping()
        elapsed = time.time() - start
        print(f"   Redis ping: {result} in {elapsed:.3f}s")
    except Exception as e:
        print(f"   Redis error: {e}")

if __name__ == "__main__":
    test_api_endpoints()