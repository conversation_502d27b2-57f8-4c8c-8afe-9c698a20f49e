# Keycloak SSO Integration Guide

## Current Setup: Local Authentication ✅

You now have Keycloak running with local users and groups. This provides:
- User authentication with username/password
- Role-based access control (RBAC)
- Session management with Redis
- API key fallback for development

## Adding Azure AD Integration

### Prerequisites
- Azure AD tenant access
- Application Administrator role in Azure AD
- Azure AD Premium P1/P2 for group claims

### Step 1: Azure AD App Registration

```bash
# In Azure Portal (portal.azure.com)
1. Azure Active Directory → App registrations → New registration
2. Name: SIEMLess Keycloak
3. Redirect URI: http://localhost:8080/realms/siemless/broker/azure/endpoint
```

### Step 2: Configure in Keycloak Admin Console

1. Login to Keycloak: http://localhost:8080/admin
2. Select "siemless" realm
3. Identity Providers → Add provider → OpenID Connect v1.0

```json
{
  "alias": "azure",
  "displayName": "Azure AD",
  "enabled": true,
  "storeToken": true,
  "addReadTokenRoleOnCreate": true,
  "trustEmail": true,
  "firstBrokerLoginFlowAlias": "first broker login",

  "config": {
    "clientId": "YOUR_AZURE_CLIENT_ID",
    "clientSecret": "YOUR_AZURE_CLIENT_SECRET",
    "authorizationUrl": "https://login.microsoftonline.com/YOUR_TENANT_ID/oauth2/v2.0/authorize",
    "tokenUrl": "https://login.microsoftonline.com/YOUR_TENANT_ID/oauth2/v2.0/token",
    "userInfoUrl": "https://graph.microsoft.com/oidc/userinfo",
    "issuer": "https://login.microsoftonline.com/YOUR_TENANT_ID/v2.0",
    "defaultScope": "openid email profile"
  }
}
```

### Step 3: Map Azure AD Groups to Keycloak Roles

1. Identity Providers → azure → Mappers → Create
2. Name: azure-groups-mapper
3. Mapper Type: Attribute Importer
4. Claim: groups
5. User Attribute Name: azure_groups

### Step 4: Create Group-to-Role Mapper

```javascript
// Custom mapper script (if needed)
var groups = user.getAttribute("azure_groups");
if (groups.contains("AZURE_GROUP_ID_FOR_ADMINS")) {
    user.addRealmRole("siemless-admin");
}
if (groups.contains("AZURE_GROUP_ID_FOR_ANALYSTS")) {
    user.addRealmRole("siemless-analyst");
}
```

## Adding Google Workspace Integration

### Prerequisites
- Google Workspace admin access
- Google Cloud Project
- OAuth 2.0 credentials

### Step 1: Google Cloud Setup

```bash
# In Google Cloud Console (console.cloud.google.com)
1. APIs & Services → Credentials → Create Credentials → OAuth client ID
2. Application type: Web application
3. Authorized redirect URIs:
   http://localhost:8080/realms/siemless/broker/google/endpoint
```

### Step 2: Configure in Keycloak

1. Identity Providers → Add provider → Google

```json
{
  "alias": "google",
  "displayName": "Google Workspace",
  "enabled": true,

  "config": {
    "clientId": "YOUR_GOOGLE_CLIENT_ID.apps.googleusercontent.com",
    "clientSecret": "YOUR_GOOGLE_CLIENT_SECRET",
    "hostedDomain": "yourcompany.com",
    "defaultScope": "openid email profile"
  }
}
```

### Step 3: Map Google Groups (Requires Admin SDK)

For group mapping, you'll need to:
1. Enable Admin SDK API in Google Cloud
2. Create a service account with domain-wide delegation
3. Use a custom Keycloak extension or mapper

## Testing SSO Integration

### Test Azure AD Login

```bash
# Direct Azure AD login URL
http://localhost:8080/realms/siemless/protocol/openid-connect/auth?
  client_id=siemless-web&
  redirect_uri=http://localhost:3000&
  response_type=code&
  scope=openid&
  kc_idp_hint=azure
```

### Test Google Login

```bash
# Direct Google login URL
http://localhost:8080/realms/siemless/protocol/openid-connect/auth?
  client_id=siemless-web&
  redirect_uri=http://localhost:3000&
  response_type=code&
  scope=openid&
  kc_idp_hint=google
```

## Python Client Example

```python
from keycloak import KeycloakOpenID

# Configure Keycloak client
keycloak_openid = KeycloakOpenID(
    server_url="http://localhost:8080/",
    client_id="siemless-web",
    realm_name="siemless"
)

# Get token with username/password (local users)
token = keycloak_openid.token("admin", "admin123")

# Get token with Azure AD (user selects Azure on login page)
auth_url = keycloak_openid.auth_url(
    redirect_uri="http://localhost:3000/callback",
    scope="openid",
    state="your-state",
    idp_hint="azure"  # Direct to Azure AD
)

# Validate token
userinfo = keycloak_openid.userinfo(token['access_token'])
print(f"User: {userinfo['preferred_username']}")
print(f"Roles: {userinfo['realm_access']['roles']}")
```

## JavaScript/React Client Example

```javascript
import Keycloak from 'keycloak-js';

// Initialize Keycloak
const keycloak = new Keycloak({
  url: 'http://localhost:8080/',
  realm: 'siemless',
  clientId: 'siemless-web'
});

// Initialize and login
keycloak.init({
  onLoad: 'check-sso',
  silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html'
}).then((authenticated) => {
  if (authenticated) {
    console.log('User is authenticated');
    console.log('Token:', keycloak.token);
    console.log('User:', keycloak.tokenParsed.preferred_username);
    console.log('Roles:', keycloak.tokenParsed.realm_access.roles);
  } else {
    // Redirect to login with Azure hint
    keycloak.login({ idpHint: 'azure' });
  }
});

// Logout
keycloak.logout();

// Check role
if (keycloak.hasRealmRole('siemless-admin')) {
  // Show admin features
}
```

## Monitoring & Troubleshooting

### Check Keycloak Logs

```bash
docker-compose -f docker-compose.keycloak.yml logs -f keycloak
```

### Common Issues

1. **Azure AD groups not syncing**
   - Ensure group claims are configured in Azure AD
   - Check token configuration includes groups
   - Verify mapper is correctly configured

2. **Google login fails**
   - Check redirect URI matches exactly
   - Ensure hostedDomain is set for Google Workspace
   - Verify OAuth consent screen is configured

3. **Roles not assigned**
   - Check group-to-role mappers
   - Verify groups are in token claims
   - Look at user attributes in Keycloak admin

### Debug Token Contents

```python
import jwt
import json

# Decode token (without verification for debugging)
decoded = jwt.decode(token, options={"verify_signature": False})
print(json.dumps(decoded, indent=2))

# Check for groups/roles
print("Groups:", decoded.get('groups', []))
print("Roles:", decoded.get('realm_access', {}).get('roles', []))
```

## Security Considerations

1. **Production Setup**
   - Use HTTPS for all endpoints
   - Enable PKCE for public clients
   - Set secure cookie flags
   - Configure CORS properly

2. **Token Security**
   - Short access token lifetime (5-15 minutes)
   - Use refresh tokens appropriately
   - Store tokens securely (httpOnly cookies)

3. **Session Management**
   - Configure idle timeout
   - Implement proper logout
   - Clear sessions on security events

## Migration Path

### Phase 1: Local Users (Current) ✅
- Local authentication working
- RBAC configured
- Session management active

### Phase 2: Add Azure AD
- Configure Azure AD as identity provider
- Map Azure groups to roles
- Test with pilot users

### Phase 3: Add Google Workspace
- Configure Google as identity provider
- Map Google groups to roles
- Enable for specific domain

### Phase 4: Production
- Remove local users (except emergency admin)
- Enforce SSO only
- Monitor and audit access

---

*Remember: You can run with local users as long as needed. Adding SSO is just configuration - no code changes required!*