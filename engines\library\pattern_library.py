"""
SIEMLess v2.0 - Pattern Library System
Core library for pattern storage, retrieval, and management
"""

import json
import uuid
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
import re
from collections import defaultdict

class PatternType(Enum):
    """Pattern type enumeration"""
    DETECTION = "detection"
    EXTRACTION = "extraction"
    ENRICHMENT = "enrichment"
    RESPONSE = "response"
    COMPOSITE = "composite"

class PatternStatus(Enum):
    """Pattern lifecycle status"""
    DRAFT = "draft"
    CANDIDATE = "candidate"
    ACTIVE = "active"
    OPTIMIZING = "optimizing"
    DEPRECATED = "deprecated"
    ARCHIVED = "archived"

class PatternVisibility(Enum):
    """Pattern sharing visibility"""
    PRIVATE = "private"
    INTERNAL = "internal"
    COMMUNITY = "community"
    PUBLIC = "public"

class PatternLibrary:
    """Main pattern library management system"""

    def __init__(self, db_connection=None, redis_client=None, logger=None):
        self.db_connection = db_connection
        self.redis_client = redis_client
        self.logger = logger

        # In-memory pattern cache
        self.pattern_cache = {}
        self.cache_ttl = 3600  # 1 hour

        # Pattern statistics
        self.stats = defaultdict(int)

    # ==================== Pattern CRUD Operations ====================

    def create_pattern(self, pattern_data: Dict[str, Any]) -> str:
        """Create a new pattern in the library"""
        # Generate pattern ID
        pattern_id = str(uuid.uuid4())

        # Build pattern structure
        pattern = {
            'pattern_id': pattern_id,
            'pattern_name': pattern_data.get('name', 'Unnamed Pattern'),
            'pattern_version': '1.0.0',
            'pattern_type': pattern_data.get('type', PatternType.DETECTION.value),
            'pattern_category': pattern_data.get('category', 'general'),

            'metadata': {
                'author': pattern_data.get('author', 'system'),
                'created_at': datetime.utcnow().isoformat(),
                'updated_at': datetime.utcnow().isoformat(),
                'tags': pattern_data.get('tags', []),
                'mitre_attack': pattern_data.get('mitre_attack', []),
                'confidence': pattern_data.get('confidence', 0.80),
                'false_positive_rate': pattern_data.get('false_positive_rate', 0.05),
                'description': pattern_data.get('description', ''),
                'status': PatternStatus.DRAFT.value
            },

            'pattern_data': pattern_data.get('pattern_data', {}),

            'validation': {
                'test_cases': pattern_data.get('test_cases', []),
                'validation_results': {},
                'ai_consensus': pattern_data.get('ai_consensus', {}),
                'community_votes': 0
            },

            'performance': {
                'usage_count': 0,
                'success_rate': 0.0,
                'avg_execution_time_ms': 0,
                'cost_savings': 0.0,
                'last_used': None
            },

            'distribution': {
                'visibility': pattern_data.get('visibility', PatternVisibility.PRIVATE.value),
                'license': pattern_data.get('license', 'proprietary'),
                'sharing_enabled': pattern_data.get('sharing_enabled', False),
                'shared_count': 0
            }
        }

        # Store in database
        self._store_pattern(pattern)

        # Cache the pattern
        self.pattern_cache[pattern_id] = pattern

        # Track statistics
        self.stats['patterns_created'] += 1

        if self.logger:
            self.logger.info(f"Created pattern {pattern_id}: {pattern['pattern_name']}")

        return pattern_id

    def get_pattern(self, pattern_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve a pattern by ID"""
        # Check cache first
        if pattern_id in self.pattern_cache:
            self.stats['cache_hits'] += 1
            return self.pattern_cache[pattern_id]

        # Load from database
        pattern = self._load_pattern(pattern_id)

        if pattern:
            # Update cache
            self.pattern_cache[pattern_id] = pattern

            # Update usage statistics
            self._update_usage_stats(pattern_id)

        return pattern

    def update_pattern(self, pattern_id: str, updates: Dict[str, Any]) -> bool:
        """Update an existing pattern"""
        pattern = self.get_pattern(pattern_id)

        if not pattern:
            return False

        # Update version
        old_version = pattern['pattern_version']
        pattern['pattern_version'] = self._increment_version(old_version, updates.get('version_change', 'patch'))

        # Update metadata
        pattern['metadata']['updated_at'] = datetime.utcnow().isoformat()

        # Apply updates
        for key, value in updates.items():
            if key in pattern:
                if isinstance(value, dict) and isinstance(pattern[key], dict):
                    pattern[key].update(value)
                else:
                    pattern[key] = value

        # Store updated pattern
        self._store_pattern(pattern)

        # Update cache
        self.pattern_cache[pattern_id] = pattern

        # Track version history
        self._save_version_history(pattern_id, old_version, pattern)

        return True

    def delete_pattern(self, pattern_id: str, archive: bool = True) -> bool:
        """Delete or archive a pattern"""
        pattern = self.get_pattern(pattern_id)

        if not pattern:
            return False

        if archive:
            # Archive instead of delete
            pattern['metadata']['status'] = PatternStatus.ARCHIVED.value
            pattern['metadata']['archived_at'] = datetime.utcnow().isoformat()
            self._store_pattern(pattern)
        else:
            # Permanent deletion
            self._delete_pattern(pattern_id)

        # Remove from cache
        if pattern_id in self.pattern_cache:
            del self.pattern_cache[pattern_id]

        return True

    # ==================== Pattern Search & Discovery ====================

    def search_patterns(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search patterns based on criteria"""
        results = []

        # Build search query
        patterns = self._search_database(criteria)

        # Apply filters
        for pattern in patterns:
            if self._matches_criteria(pattern, criteria):
                results.append(pattern)

        # Sort results
        sort_by = criteria.get('sort_by', 'usage_count')
        reverse = criteria.get('sort_order', 'desc') == 'desc'

        if sort_by == 'usage_count':
            results.sort(key=lambda x: x['performance']['usage_count'], reverse=reverse)
        elif sort_by == 'success_rate':
            results.sort(key=lambda x: x['performance']['success_rate'], reverse=reverse)
        elif sort_by == 'created_at':
            results.sort(key=lambda x: x['metadata']['created_at'], reverse=reverse)

        # Apply pagination
        offset = criteria.get('offset', 0)
        limit = criteria.get('limit', 100)

        return results[offset:offset + limit]

    def find_similar_patterns(self, pattern_id: str, threshold: float = 0.7) -> List[Tuple[str, float]]:
        """Find patterns similar to the given pattern"""
        base_pattern = self.get_pattern(pattern_id)

        if not base_pattern:
            return []

        similar = []

        # Compare with all active patterns
        for pid, pattern in self._get_active_patterns().items():
            if pid == pattern_id:
                continue

            similarity = self._calculate_similarity(base_pattern, pattern)

            if similarity >= threshold:
                similar.append((pid, similarity))

        # Sort by similarity
        similar.sort(key=lambda x: x[1], reverse=True)

        return similar

    def recommend_patterns(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Recommend patterns based on context"""
        recommendations = []

        # Recommend based on log sources
        if 'log_sources' in context:
            for source in context['log_sources']:
                patterns = self.search_patterns({
                    'log_source': source,
                    'status': PatternStatus.ACTIVE.value,
                    'sort_by': 'success_rate',
                    'limit': 10
                })
                recommendations.extend(patterns)

        # Recommend based on threat landscape
        if 'threats' in context:
            for threat in context['threats']:
                patterns = self.search_patterns({
                    'tags': [threat],
                    'status': PatternStatus.ACTIVE.value,
                    'sort_by': 'confidence',
                    'limit': 10
                })
                recommendations.extend(patterns)

        # Recommend based on MITRE ATT&CK
        if 'mitre_techniques' in context:
            for technique in context['mitre_techniques']:
                patterns = self.search_patterns({
                    'mitre_attack': technique,
                    'status': PatternStatus.ACTIVE.value,
                    'limit': 5
                })
                recommendations.extend(patterns)

        # Remove duplicates and rank
        seen = set()
        unique_recommendations = []

        for pattern in recommendations:
            if pattern['pattern_id'] not in seen:
                seen.add(pattern['pattern_id'])
                unique_recommendations.append(pattern)

        # Calculate recommendation score
        for pattern in unique_recommendations:
            pattern['recommendation_score'] = self._calculate_recommendation_score(pattern, context)

        # Sort by recommendation score
        unique_recommendations.sort(key=lambda x: x['recommendation_score'], reverse=True)

        return unique_recommendations[:50]  # Top 50 recommendations

    # ==================== Pattern Validation & Testing ====================

    def validate_pattern(self, pattern_id: str) -> Dict[str, Any]:
        """Validate a pattern with test cases"""
        pattern = self.get_pattern(pattern_id)

        if not pattern:
            return {'valid': False, 'error': 'Pattern not found'}

        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'test_results': [],
            'performance_metrics': {}
        }

        # Syntax validation
        syntax_result = self._validate_syntax(pattern)
        if not syntax_result['valid']:
            validation_results['valid'] = False
            validation_results['errors'].extend(syntax_result['errors'])

        # Logic validation
        logic_result = self._validate_logic(pattern)
        if not logic_result['valid']:
            validation_results['valid'] = False
            validation_results['errors'].extend(logic_result['errors'])
        validation_results['warnings'].extend(logic_result.get('warnings', []))

        # Run test cases
        test_cases = pattern['validation']['test_cases']
        for test_case in test_cases:
            test_result = self._run_test_case(pattern, test_case)
            validation_results['test_results'].append(test_result)

            if not test_result['passed']:
                validation_results['valid'] = False

        # Performance testing
        perf_results = self._test_performance(pattern)
        validation_results['performance_metrics'] = perf_results

        # Update pattern validation results
        pattern['validation']['validation_results'] = validation_results
        pattern['validation']['last_validated'] = datetime.utcnow().isoformat()

        # Update pattern status based on validation
        if validation_results['valid']:
            if pattern['metadata']['status'] == PatternStatus.DRAFT.value:
                pattern['metadata']['status'] = PatternStatus.CANDIDATE.value

        self.update_pattern(pattern_id, {'validation': pattern['validation']})

        return validation_results

    def test_pattern_with_data(self, pattern_id: str, test_data: Any) -> Dict[str, Any]:
        """Test a pattern with real data"""
        pattern = self.get_pattern(pattern_id)

        if not pattern:
            return {'success': False, 'error': 'Pattern not found'}

        start_time = datetime.utcnow()

        try:
            # Execute pattern logic
            result = self._execute_pattern(pattern, test_data)

            execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000

            return {
                'success': True,
                'result': result,
                'execution_time_ms': execution_time,
                'pattern_id': pattern_id,
                'pattern_name': pattern['pattern_name']
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'pattern_id': pattern_id
            }

    # ==================== Pattern Evolution & Optimization ====================

    def evolve_pattern(self, pattern_id: str, feedback: Dict[str, Any]) -> bool:
        """Evolve pattern based on feedback"""
        pattern = self.get_pattern(pattern_id)

        if not pattern:
            return False

        # Update performance metrics
        if 'success' in feedback:
            self._update_success_rate(pattern, feedback['success'])

        if 'execution_time' in feedback:
            self._update_execution_time(pattern, feedback['execution_time'])

        if 'false_positive' in feedback:
            self._update_false_positive_rate(pattern, feedback['false_positive'])

        # Auto-optimize if needed
        if pattern['performance']['usage_count'] > 1000:
            if pattern['performance']['success_rate'] < 0.80:
                # Pattern needs optimization
                pattern['metadata']['status'] = PatternStatus.OPTIMIZING.value
                self._trigger_optimization(pattern_id)

        # Check for deprecation
        if pattern['performance']['success_rate'] < 0.50 and pattern['performance']['usage_count'] > 100:
            # Pattern should be deprecated
            pattern['metadata']['status'] = PatternStatus.DEPRECATED.value
            self._suggest_alternatives(pattern_id)

        self.update_pattern(pattern_id, {'performance': pattern['performance']})

        return True

    def optimize_pattern(self, pattern_id: str) -> Dict[str, Any]:
        """Optimize a pattern for better performance"""
        pattern = self.get_pattern(pattern_id)

        if not pattern:
            return {'success': False, 'error': 'Pattern not found'}

        optimization_results = {
            'original_pattern': pattern.copy(),
            'optimized_pattern': None,
            'improvements': [],
            'metrics': {}
        }

        # Optimization strategies
        optimized = pattern.copy()

        # 1. Simplify regex patterns
        if 'regex' in str(optimized['pattern_data']):
            optimized = self._optimize_regex(optimized)
            optimization_results['improvements'].append('Optimized regex patterns')

        # 2. Reduce complexity
        complexity_before = self._calculate_complexity(pattern)
        optimized = self._reduce_complexity(optimized)
        complexity_after = self._calculate_complexity(optimized)

        if complexity_after < complexity_before:
            optimization_results['improvements'].append(f'Reduced complexity from {complexity_before} to {complexity_after}')

        # 3. Improve caching
        optimized = self._add_caching_hints(optimized)
        optimization_results['improvements'].append('Added caching hints')

        # Test optimized pattern
        test_results = self.validate_pattern(pattern_id)

        if test_results['valid']:
            # Create new version with optimizations
            optimized['pattern_version'] = self._increment_version(pattern['pattern_version'], 'minor')
            optimized['metadata']['updated_at'] = datetime.utcnow().isoformat()
            optimized['metadata']['status'] = PatternStatus.ACTIVE.value

            self.update_pattern(pattern_id, optimized)
            optimization_results['optimized_pattern'] = optimized
            optimization_results['success'] = True
        else:
            optimization_results['success'] = False
            optimization_results['error'] = 'Optimization failed validation'

        return optimization_results

    # ==================== Pattern Sharing & Distribution ====================

    def share_pattern(self, pattern_id: str, visibility: PatternVisibility = PatternVisibility.COMMUNITY) -> Dict[str, Any]:
        """Share a pattern with the community"""
        pattern = self.get_pattern(pattern_id)

        if not pattern:
            return {'success': False, 'error': 'Pattern not found'}

        # Sanitize sensitive information
        shared_pattern = self._sanitize_pattern(pattern)

        # Update distribution settings
        shared_pattern['distribution']['visibility'] = visibility.value
        shared_pattern['distribution']['sharing_enabled'] = True
        shared_pattern['distribution']['shared_at'] = datetime.utcnow().isoformat()
        shared_pattern['distribution']['shared_count'] += 1

        # Generate sharing token
        sharing_token = self._generate_sharing_token(pattern_id)

        # Publish to community (if applicable)
        if visibility in [PatternVisibility.COMMUNITY, PatternVisibility.PUBLIC]:
            self._publish_to_community(shared_pattern)

        self.update_pattern(pattern_id, {'distribution': shared_pattern['distribution']})

        return {
            'success': True,
            'pattern_id': pattern_id,
            'sharing_token': sharing_token,
            'visibility': visibility.value
        }

    def import_pattern(self, pattern_data: Dict[str, Any], validate: bool = True) -> str:
        """Import a pattern from external source"""
        # Validate pattern structure
        if validate:
            validation = self._validate_import(pattern_data)
            if not validation['valid']:
                raise ValueError(f"Invalid pattern: {validation['errors']}")

        # Adapt to local environment
        adapted_pattern = self._adapt_pattern(pattern_data)

        # Create new pattern with imported data
        pattern_id = self.create_pattern(adapted_pattern)

        # Mark as imported
        pattern = self.get_pattern(pattern_id)
        pattern['metadata']['imported'] = True
        pattern['metadata']['imported_from'] = pattern_data.get('shared_by', 'unknown')
        pattern['metadata']['imported_at'] = datetime.utcnow().isoformat()

        self.update_pattern(pattern_id, {'metadata': pattern['metadata']})

        return pattern_id

    # ==================== Pattern Analytics ====================

    def get_library_statistics(self) -> Dict[str, Any]:
        """Get comprehensive library statistics"""
        stats = {
            'total_patterns': self._count_patterns(),
            'patterns_by_type': self._count_by_type(),
            'patterns_by_status': self._count_by_status(),
            'patterns_by_category': self._count_by_category(),

            'performance': {
                'avg_success_rate': self._calculate_avg_success_rate(),
                'avg_execution_time': self._calculate_avg_execution_time(),
                'total_usage': self._calculate_total_usage(),
                'cost_savings': self._calculate_total_cost_savings()
            },

            'community': {
                'patterns_shared': self._count_shared_patterns(),
                'patterns_imported': self._count_imported_patterns(),
                'community_contributors': self._count_contributors()
            },

            'trends': {
                'patterns_created_today': self._count_created_today(),
                'patterns_created_this_week': self._count_created_this_week(),
                'patterns_created_this_month': self._count_created_this_month(),
                'most_used_patterns': self._get_most_used_patterns(10),
                'trending_patterns': self._get_trending_patterns(10)
            }
        }

        return stats

    def get_pattern_metrics(self, pattern_id: str) -> Dict[str, Any]:
        """Get detailed metrics for a specific pattern"""
        pattern = self.get_pattern(pattern_id)

        if not pattern:
            return {}

        metrics = {
            'pattern_id': pattern_id,
            'pattern_name': pattern['pattern_name'],
            'performance': pattern['performance'],
            'validation': pattern['validation'],

            'usage_trend': self._get_usage_trend(pattern_id),
            'success_trend': self._get_success_trend(pattern_id),
            'similar_patterns': self.find_similar_patterns(pattern_id, 0.8),

            'cost_analysis': {
                'ai_calls_saved': pattern['performance']['usage_count'],
                'estimated_savings': pattern['performance']['usage_count'] * 0.02  # $0.02 per AI call
            }
        }

        return metrics

    # ==================== Helper Methods ====================

    def _increment_version(self, version: str, change_type: str = 'patch') -> str:
        """Increment semantic version"""
        parts = version.split('.')
        major, minor, patch = int(parts[0]), int(parts[1]), int(parts[2])

        if change_type == 'major':
            return f"{major + 1}.0.0"
        elif change_type == 'minor':
            return f"{major}.{minor + 1}.0"
        else:  # patch
            return f"{major}.{minor}.{patch + 1}"

    def _calculate_similarity(self, pattern1: Dict, pattern2: Dict) -> float:
        """Calculate similarity between two patterns"""
        score = 0.0

        # Type similarity
        if pattern1['pattern_type'] == pattern2['pattern_type']:
            score += 0.3

        # Category similarity
        if pattern1['pattern_category'] == pattern2['pattern_category']:
            score += 0.2

        # Tag overlap
        tags1 = set(pattern1['metadata']['tags'])
        tags2 = set(pattern2['metadata']['tags'])
        if tags1 and tags2:
            overlap = len(tags1.intersection(tags2)) / len(tags1.union(tags2))
            score += overlap * 0.3

        # MITRE technique overlap
        mitre1 = set(pattern1['metadata']['mitre_attack'])
        mitre2 = set(pattern2['metadata']['mitre_attack'])
        if mitre1 and mitre2:
            overlap = len(mitre1.intersection(mitre2)) / len(mitre1.union(mitre2))
            score += overlap * 0.2

        return min(1.0, score)

    def _calculate_recommendation_score(self, pattern: Dict, context: Dict) -> float:
        """Calculate recommendation score for a pattern"""
        score = 0.0

        # Success rate weight
        score += pattern['performance']['success_rate'] * 0.3

        # Usage count weight (popularity)
        usage_score = min(1.0, pattern['performance']['usage_count'] / 10000)
        score += usage_score * 0.2

        # Confidence weight
        score += pattern['metadata']['confidence'] * 0.2

        # Low false positive weight
        score += (1.0 - pattern['metadata']['false_positive_rate']) * 0.2

        # Recency weight
        days_old = (datetime.utcnow() - datetime.fromisoformat(pattern['metadata']['updated_at'])).days
        recency_score = max(0, 1.0 - (days_old / 365))
        score += recency_score * 0.1

        return min(1.0, score)

    def _generate_sharing_token(self, pattern_id: str) -> str:
        """Generate a sharing token for a pattern"""
        data = f"{pattern_id}{datetime.utcnow().isoformat()}"
        return hashlib.sha256(data.encode()).hexdigest()[:16]

    def _sanitize_pattern(self, pattern: Dict) -> Dict:
        """Remove sensitive information from pattern before sharing"""
        sanitized = pattern.copy()

        # Remove sensitive fields
        sensitive_fields = ['internal_notes', 'customer_data', 'api_keys']
        for field in sensitive_fields:
            if field in sanitized:
                del sanitized[field]

        # Anonymize author if needed
        if 'author' in sanitized['metadata']:
            sanitized['metadata']['author'] = 'Anonymous'

        return sanitized

    # ==================== Database Methods (Stubs) ====================

    def _store_pattern(self, pattern: Dict):
        """Store pattern in database"""
        if self.db_connection:
            # Store in PostgreSQL
            pass

        if self.redis_client:
            # Cache in Redis
            self.redis_client.setex(
                f"pattern:{pattern['pattern_id']}",
                self.cache_ttl,
                json.dumps(pattern)
            )

    def _load_pattern(self, pattern_id: str) -> Optional[Dict]:
        """Load pattern from database"""
        # Check Redis cache first
        if self.redis_client:
            cached = self.redis_client.get(f"pattern:{pattern_id}")
            if cached:
                return json.loads(cached)

        # Load from PostgreSQL
        if self.db_connection:
            # Query database
            pass

        return None

    def _delete_pattern(self, pattern_id: str):
        """Delete pattern from database"""
        if self.db_connection:
            # Delete from PostgreSQL
            pass

        if self.redis_client:
            # Remove from cache
            self.redis_client.delete(f"pattern:{pattern_id}")

    def _search_database(self, criteria: Dict) -> List[Dict]:
        """Search patterns in database"""
        # This would implement actual database search
        return []

    def _get_active_patterns(self) -> Dict[str, Dict]:
        """Get all active patterns"""
        # This would query database for active patterns
        return {}

    def _matches_criteria(self, pattern: Dict, criteria: Dict) -> bool:
        """Check if pattern matches search criteria"""
        # Implement matching logic
        return True