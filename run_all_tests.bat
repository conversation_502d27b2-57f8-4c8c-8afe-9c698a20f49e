@echo off
REM SIEMLess v2.0 - Comprehensive Test Runner (Windows)
REM ===================================================
REM Runs all test suites and generates a report

echo ==========================================
echo SIEMLess v2.0 - Test Suite Runner
echo ==========================================
echo Date: %date% %time%
echo ==========================================
echo.

REM Test results tracking
set TOTAL_TESTS=0
set PASSED_TESTS=0
set FAILED_TESTS=0

REM Check Python availability
echo Checking Python environment...
python --version 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo Error: Python not found
    exit /b 1
)

REM Check if we're in the right directory
if not exist test_comprehensive_suite.py (
    echo Error: Test files not found. Please run from siemless_v2 directory
    exit /b 1
)

REM Check Docker services
echo.
echo Checking Docker services...
docker-compose ps 2>NUL || echo Warning: Docker services may not be running

REM Run infrastructure tests first
echo.
echo ==========================================
echo PHASE 1: Infrastructure Tests
echo ==========================================

echo Running: Comprehensive System Test
python test_comprehensive_suite.py > test_comprehensive_suite.log 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [PASSED] Comprehensive System Test
    set /a PASSED_TESTS+=1
) else (
    echo [FAILED] Comprehensive System Test
    echo    See test_comprehensive_suite.log for details
    set /a FAILED_TESTS+=1
)
set /a TOTAL_TESTS+=1

REM Run individual engine tests
echo.
echo ==========================================
echo PHASE 2: Individual Engine Tests
echo ==========================================

echo Running: All Engine Tests
python test_individual_engines.py > test_individual_engines.log 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [PASSED] All Engine Tests
    set /a PASSED_TESTS+=1
) else (
    echo [FAILED] All Engine Tests
    echo    See test_individual_engines.log for details
    set /a FAILED_TESTS+=1
)
set /a TOTAL_TESTS+=1

REM Test each engine separately
for %%E in (intelligence backend ingestion contextualization delivery) do (
    echo Running: %%E Engine Test
    python test_individual_engines.py %%E > test_%%E_engine.log 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo [PASSED] %%E Engine Test
        set /a PASSED_TESTS+=1
    ) else (
        echo [FAILED] %%E Engine Test
        echo    See test_%%E_engine.log for details
        set /a FAILED_TESTS+=1
    )
    set /a TOTAL_TESTS+=1
)

REM Run async pattern tests
echo.
echo ==========================================
echo PHASE 3: Async Pattern Tests
echo ==========================================

echo Running: Async Pattern Tests
python test_async_patterns.py > test_async_patterns.log 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [PASSED] Async Pattern Tests
    set /a PASSED_TESTS+=1
) else (
    echo [FAILED] Async Pattern Tests
    echo    See test_async_patterns.log for details
    set /a FAILED_TESTS+=1
)
set /a TOTAL_TESTS+=1

REM Generate summary report
echo.
echo ==========================================
echo TEST SUMMARY REPORT
echo ==========================================
echo Total Test Suites Run: %TOTAL_TESTS%
echo Passed: %PASSED_TESTS%
echo Failed: %FAILED_TESTS%
echo.

if %FAILED_TESTS% EQU 0 (
    echo ALL TESTS PASSED!
    echo SIEMLess v2.0 is fully operational
) else (
    echo SOME TESTS FAILED
    echo Please review the log files for details
)

echo ==========================================
echo.

REM Generate detailed report file
(
    echo SIEMLess v2.0 Test Report
    echo Generated: %date% %time%
    echo ==========================================
    echo.
    echo Test Summary:
    echo - Total Test Suites: %TOTAL_TESTS%
    echo - Passed: %PASSED_TESTS%
    echo - Failed: %FAILED_TESTS%
    echo.
    echo Test Logs Available:
    dir /b test_*.log 2>NUL
) > test_report.txt

echo Detailed report saved to: test_report.txt
echo Individual test logs: test_*.log

REM Exit with appropriate code
if %FAILED_TESTS% EQU 0 (
    exit /b 0
) else (
    exit /b 1
)