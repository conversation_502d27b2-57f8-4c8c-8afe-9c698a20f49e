"""
Investigation Evidence Log System
Provides query-based evidence collection with link-back to original SIEMs
"""

import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import asyncpg
from uuid import uuid4

logger = logging.getLogger(__name__)


@dataclass
class EvidenceQuery:
    """Query specification for evidence collection"""
    query_id: str
    investigation_id: str
    siem_type: str  # elastic, splunk, sentinel, qradar, chronicle
    query_string: str
    time_range_start: datetime
    time_range_end: datetime
    filters: Dict[str, Any]
    created_at: datetime
    created_by: str


@dataclass
class EvidenceItem:
    """Individual piece of evidence"""
    evidence_id: str
    investigation_id: str
    query_id: str
    siem_type: str
    siem_url: str  # Link back to original log in SIEM
    timestamp: datetime
    event_data: Dict[str, Any]
    entities_extracted: List[Dict]
    relevance_score: float
    retention_days: int
    collected_at: datetime


class InvestigationEvidenceLogger:
    """
    Evidence collection and retention system

    Features:
    - Query-based evidence collection from SIEMs
    - URL generation for link-back to original logs
    - Intelligent retention based on relevance
    - Evidence tagging and categorization
    """

    def __init__(self, db_pool: asyncpg.Pool, redis_client, siem_configs: Dict[str, Dict]):
        self.db_pool = db_pool
        self.redis = redis_client
        self.siem_configs = siem_configs

        # Default retention policies (days)
        self.retention_policies = {
            'critical': 365,  # 1 year
            'high': 180,      # 6 months
            'medium': 90,     # 3 months
            'low': 30,        # 1 month
            'irrelevant': 7   # 1 week
        }

    async def create_evidence_query(self, investigation_id: str, siem_type: str,
                                    query_params: Dict[str, Any], user: str) -> EvidenceQuery:
        """
        Create evidence collection query

        Args:
            investigation_id: Investigation to collect evidence for
            siem_type: SIEM to query (elastic, splunk, sentinel, etc.)
            query_params: Query parameters (filters, time range, etc.)
            user: User creating query

        Returns:
            EvidenceQuery object
        """
        query_id = str(uuid4())

        # Build query string based on SIEM type
        query_string = self._build_query_string(siem_type, query_params)

        # Parse time range
        time_range_start = datetime.fromisoformat(query_params.get('time_range_start',
                                                  (datetime.utcnow() - timedelta(hours=24)).isoformat()))
        time_range_end = datetime.fromisoformat(query_params.get('time_range_end',
                                               datetime.utcnow().isoformat()))

        evidence_query = EvidenceQuery(
            query_id=query_id,
            investigation_id=investigation_id,
            siem_type=siem_type,
            query_string=query_string,
            time_range_start=time_range_start,
            time_range_end=time_range_end,
            filters=query_params.get('filters', {}),
            created_at=datetime.utcnow(),
            created_by=user
        )

        # Store query in database
        await self._store_evidence_query(evidence_query)

        logger.info(f"Created evidence query {query_id} for investigation {investigation_id}")

        return evidence_query

    def _build_query_string(self, siem_type: str, query_params: Dict) -> str:
        """Build SIEM-specific query string"""
        filters = query_params.get('filters', {})

        if siem_type == 'elastic':
            # Build Elasticsearch query DSL
            must_clauses = []

            for field, value in filters.items():
                if isinstance(value, list):
                    must_clauses.append({"terms": {field: value}})
                else:
                    must_clauses.append({"term": {field: value}})

            query = {
                "query": {
                    "bool": {
                        "must": must_clauses
                    }
                }
            }
            return json.dumps(query)

        elif siem_type == 'splunk':
            # Build SPL query
            search_terms = []
            for field, value in filters.items():
                if isinstance(value, list):
                    values_str = ' OR '.join([f'"{v}"' for v in value])
                    search_terms.append(f"{field} IN ({values_str})")
                else:
                    search_terms.append(f'{field}="{value}"')

            base_query = query_params.get('index', 'main')
            return f'search index={base_query} {" ".join(search_terms)}'

        elif siem_type == 'sentinel':
            # Build KQL query
            where_clauses = []
            for field, value in filters.items():
                if isinstance(value, list):
                    values_str = ', '.join([f'"{v}"' for v in value])
                    where_clauses.append(f"{field} in ({values_str})")
                else:
                    where_clauses.append(f'{field} == "{value}"')

            table = query_params.get('table', 'SecurityEvent')
            return f'{table} | where {" and ".join(where_clauses)}'

        elif siem_type == 'qradar':
            # Build AQL query
            where_clauses = []
            for field, value in filters.items():
                if isinstance(value, list):
                    values_str = ', '.join([f"'{v}'" for v in value])
                    where_clauses.append(f"{field} IN ({values_str})")
                else:
                    where_clauses.append(f"{field} = '{value}'")

            return f"SELECT * FROM events WHERE {' AND '.join(where_clauses)}"

        else:
            # Generic query
            return json.dumps(filters)

    async def collect_evidence(self, evidence_query: EvidenceQuery,
                               max_results: int = 1000) -> List[EvidenceItem]:
        """
        Execute evidence query and collect results

        Args:
            evidence_query: Query to execute
            max_results: Maximum number of results to collect

        Returns:
            List of EvidenceItem objects
        """
        logger.info(f"Collecting evidence for query {evidence_query.query_id}")

        evidence_items = []

        # In production, this would query the actual SIEM
        # For now, we'll simulate by querying our local events table

        try:
            # Query events table for matching evidence
            async with self.db_pool.acquire() as conn:
                events = await conn.fetch("""
                    SELECT
                        event_id,
                        event_timestamp,
                        event_data,
                        entities,
                        source_type,
                        source_id
                    FROM events
                    WHERE event_timestamp >= $1
                    AND event_timestamp <= $2
                    ORDER BY event_timestamp DESC
                    LIMIT $3
                """,
                    evidence_query.time_range_start,
                    evidence_query.time_range_end,
                    max_results
                )

            # Convert events to evidence items
            for event in events:
                # Generate link-back URL
                siem_url = self._generate_siem_url(
                    evidence_query.siem_type,
                    event['event_id'],
                    event['event_timestamp']
                )

                # Calculate relevance score
                relevance_score = self._calculate_relevance(
                    event,
                    evidence_query.filters
                )

                # Determine retention period based on relevance
                retention_days = self._determine_retention(relevance_score)

                evidence_item = EvidenceItem(
                    evidence_id=str(uuid4()),
                    investigation_id=evidence_query.investigation_id,
                    query_id=evidence_query.query_id,
                    siem_type=evidence_query.siem_type,
                    siem_url=siem_url,
                    timestamp=event['event_timestamp'],
                    event_data=event['event_data'] or {},
                    entities_extracted=event['entities'] or [],
                    relevance_score=relevance_score,
                    retention_days=retention_days,
                    collected_at=datetime.utcnow()
                )

                evidence_items.append(evidence_item)

                # Store evidence
                await self._store_evidence_item(evidence_item)

            logger.info(f"Collected {len(evidence_items)} evidence items for query {evidence_query.query_id}")

        except Exception as e:
            logger.error(f"Error collecting evidence: {e}", exc_info=True)

        return evidence_items

    def _generate_siem_url(self, siem_type: str, event_id: str, timestamp: datetime) -> str:
        """Generate link-back URL to original log in SIEM"""
        siem_config = self.siem_configs.get(siem_type, {})
        base_url = siem_config.get('url', '')

        if not base_url:
            return f"#no-url-configured-for-{siem_type}"

        if siem_type == 'elastic':
            # Kibana discover URL
            time_from = int((timestamp - timedelta(minutes=5)).timestamp() * 1000)
            time_to = int((timestamp + timedelta(minutes=5)).timestamp() * 1000)
            return f"{base_url}/app/discover#/?_g=(time:(from:'{time_from}',to:'{time_to}'))&_a=(query:(query_string:(query:'_id:\"{event_id}\"')))"

        elif siem_type == 'splunk':
            # Splunk event URL
            earliest = int((timestamp - timedelta(minutes=5)).timestamp())
            latest = int((timestamp + timedelta(minutes=5)).timestamp())
            return f"{base_url}/app/search/search?q=search _raw=\"{event_id}\"&earliest={earliest}&latest={latest}"

        elif siem_type == 'sentinel':
            # Sentinel log analytics URL
            return f"{base_url}#blade/Microsoft_Azure_Security_Insights/IncidentFullDetailsBlade/IncidentId/{event_id}"

        elif siem_type == 'qradar':
            # QRadar event URL
            return f"{base_url}/console/do/sem/eventviewer?appName=EventViewer&pageId=EventViewer&search={event_id}"

        elif siem_type == 'chronicle':
            # Chronicle event URL
            return f"{base_url}/events/{event_id}"

        else:
            return f"{base_url}/event/{event_id}"

    def _calculate_relevance(self, event: Dict, filters: Dict) -> float:
        """Calculate relevance score for evidence (0.0 - 1.0)"""
        score = 0.5  # Base score

        event_data = event.get('event_data', {})

        # Check if event matches filters
        matches = 0
        total_filters = len(filters)

        if total_filters > 0:
            for field, value in filters.items():
                if field in event_data:
                    if isinstance(value, list):
                        if event_data[field] in value:
                            matches += 1
                    elif event_data[field] == value:
                        matches += 1

            score = matches / total_filters

        # Boost score for events with entities
        entities = event.get('entities', [])
        if entities:
            score = min(1.0, score + (len(entities) * 0.05))

        return score

    def _determine_retention(self, relevance_score: float) -> int:
        """Determine retention period based on relevance"""
        if relevance_score >= 0.8:
            return self.retention_policies['critical']
        elif relevance_score >= 0.6:
            return self.retention_policies['high']
        elif relevance_score >= 0.4:
            return self.retention_policies['medium']
        elif relevance_score >= 0.2:
            return self.retention_policies['low']
        else:
            return self.retention_policies['irrelevant']

    async def _store_evidence_query(self, evidence_query: EvidenceQuery):
        """Store evidence query in database"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO investigation_evidence_queries (
                        query_id,
                        investigation_id,
                        siem_type,
                        query_string,
                        time_range_start,
                        time_range_end,
                        filters,
                        created_at,
                        created_by
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                """,
                    evidence_query.query_id,
                    evidence_query.investigation_id,
                    evidence_query.siem_type,
                    evidence_query.query_string,
                    evidence_query.time_range_start,
                    evidence_query.time_range_end,
                    json.dumps(evidence_query.filters),
                    evidence_query.created_at,
                    evidence_query.created_by
                )
        except Exception as e:
            logger.error(f"Error storing evidence query: {e}")

    async def _store_evidence_item(self, evidence_item: EvidenceItem):
        """Store evidence item in database"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO investigation_evidence (
                        evidence_id,
                        investigation_id,
                        query_id,
                        siem_type,
                        siem_url,
                        timestamp,
                        event_data,
                        entities_extracted,
                        relevance_score,
                        retention_days,
                        collected_at,
                        expires_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                """,
                    evidence_item.evidence_id,
                    evidence_item.investigation_id,
                    evidence_item.query_id,
                    evidence_item.siem_type,
                    evidence_item.siem_url,
                    evidence_item.timestamp,
                    json.dumps(evidence_item.event_data),
                    json.dumps(evidence_item.entities_extracted),
                    evidence_item.relevance_score,
                    evidence_item.retention_days,
                    evidence_item.collected_at,
                    evidence_item.collected_at + timedelta(days=evidence_item.retention_days)
                )
        except Exception as e:
            logger.error(f"Error storing evidence item: {e}")

    async def get_investigation_evidence(self, investigation_id: str,
                                        min_relevance: float = 0.0) -> List[Dict]:
        """
        Get all evidence for an investigation

        Args:
            investigation_id: Investigation ID
            min_relevance: Minimum relevance score filter

        Returns:
            List of evidence items
        """
        try:
            async with self.db_pool.acquire() as conn:
                evidence = await conn.fetch("""
                    SELECT
                        evidence_id,
                        query_id,
                        siem_type,
                        siem_url,
                        timestamp,
                        event_data,
                        entities_extracted,
                        relevance_score,
                        retention_days,
                        collected_at
                    FROM investigation_evidence
                    WHERE investigation_id = $1
                    AND relevance_score >= $2
                    AND expires_at > NOW()
                    ORDER BY timestamp DESC, relevance_score DESC
                """, investigation_id, min_relevance)

            return [dict(e) for e in evidence]

        except Exception as e:
            logger.error(f"Error fetching investigation evidence: {e}")
            return []

    async def cleanup_expired_evidence(self) -> int:
        """Clean up expired evidence (called by retention policy engine)"""
        try:
            async with self.db_pool.acquire() as conn:
                deleted = await conn.fetch("""
                    DELETE FROM investigation_evidence
                    WHERE expires_at < NOW()
                    RETURNING evidence_id
                """)

            count = len(deleted)
            logger.info(f"Cleaned up {count} expired evidence items")
            return count

        except Exception as e:
            logger.error(f"Error cleaning up expired evidence: {e}")
            return 0
