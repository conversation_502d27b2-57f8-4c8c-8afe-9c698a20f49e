#!/usr/bin/env python3
"""
Complete Data Flow Test for SIEMLess v2.0
Tests: API connections → Ingestion → Intelligence → Contextualization → Database Storage
"""

import os
import sys
import json
import redis
import psycopg2
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Add ingestion directory to path for imports
sys.path.append('./ingestion')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("DataFlowTest")


class CompleteDataFlowTest:
    """Test complete data flow from ingestion to storage"""

    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)
        self.test_results = {}
        self.captured_data = {
            'ingested': [],
            'analyzed': [],
            'contextualized': [],
            'stored': []
        }

    def connect_database(self):
        """Connect to PostgreSQL database"""
        try:
            conn = psycopg2.connect(
                host='localhost',
                port=5433,
                database='siemless_v2',
                user='siemless',
                password='siemless123'
            )
            return conn
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return None

    def test_api_connections(self):
        """Test 1: Verify API connections for Elasticsearch and CrowdStrike"""
        print("\n" + "="*60)
        print("TEST 1: API CONNECTIONS")
        print("="*60)

        test_passed = True

        # Test Elasticsearch
        print("\n[ELASTICSEARCH TEST]")
        try:
            from data_sources_v02 import UniversalElasticIngestor

            es_config = {
                'source_name': 'test_elasticsearch',
                'timestamp_field': '@timestamp'
            }

            es_source = UniversalElasticIngestor(es_config, logger)

            if es_source.test_connection():
                print("[PASS] Elasticsearch connection successful")
                self.test_results['elasticsearch_api'] = 'PASSED'
            else:
                print("[FAIL] Elasticsearch connection failed")
                self.test_results['elasticsearch_api'] = 'FAILED'
                test_passed = False

        except Exception as e:
            print(f"[FAIL] Elasticsearch test error: {e}")
            print("   Check ELASTIC_CLOUD_ID and ELASTIC_API_KEY in .env")
            self.test_results['elasticsearch_api'] = 'ERROR'
            test_passed = False

        # Test CrowdStrike
        print("\n[CROWDSTRIKE TEST]")
        try:
            from data_sources_v02 import CrowdStrikeApiIngestor

            cs_config = {
                'source_type': 'detections',
                'source_name': 'test_crowdstrike'
            }

            cs_source = CrowdStrikeApiIngestor(cs_config, logger)

            if cs_source.test_connection():
                print("[PASS] CrowdStrike connection successful")

                # Show configured scopes
                configured_scopes = os.environ.get('CROWDSTRIKE_SCOPES', 'detections:read').split(',')
                print(f"   Configured scopes: {configured_scopes}")

                self.test_results['crowdstrike_api'] = 'PASSED'
            else:
                print("[FAIL] CrowdStrike connection failed")
                self.test_results['crowdstrike_api'] = 'FAILED'
                test_passed = False

        except ImportError as e:
            print(f"[FAIL] CrowdStrike library not installed: {e}")
            print("   Run: pip install crowdstrike-falconpy")
            self.test_results['crowdstrike_api'] = 'NOT_INSTALLED'
            test_passed = False
        except ValueError as e:
            print(f"[FAIL] CrowdStrike credentials missing: {e}")
            print("   Check CROWDSTRIKE_CLIENT_ID and CROWDSTRIKE_CLIENT_SECRET in .env")
            self.test_results['crowdstrike_api'] = 'NO_CREDENTIALS'
            test_passed = False
        except Exception as e:
            print(f"[FAIL] CrowdStrike test error: {e}")
            self.test_results['crowdstrike_api'] = 'ERROR'
            test_passed = False

        return test_passed

    def test_data_ingestion(self):
        """Test 2: Ingest data from APIs and send to processing engines"""
        print("\n" + "="*60)
        print("TEST 2: DATA INGESTION & ROUTING")
        print("="*60)

        # Start a test source in Ingestion Engine
        source_id = f'test_api_source_{int(datetime.now().timestamp())}'

        request = {
            'data': {
                'source_id': source_id,
                'source_type': 'elasticsearch'
            }
        }

        print(f"[START] Starting ingestion source: {source_id}")
        self.redis_client.publish('ingestion.start_source', json.dumps(request))

        # Monitor for routed messages
        print("[MONITOR] Watching for data routing (10 seconds)...")

        pubsub = self.redis_client.pubsub()
        pubsub.subscribe([
            'intelligence.analyze_log',
            'contextualization.enrich_log',
            'ingestion.source_started'
        ])

        start_time = datetime.now()
        timeout = 10  # seconds

        messages_received = {
            'ingestion_started': False,
            'intelligence_routed': 0,
            'contextualization_routed': 0
        }

        while (datetime.now() - start_time).seconds < timeout:
            message = pubsub.get_message(timeout=1)

            if message and message['type'] == 'message':
                channel = message['channel']

                if channel == 'ingestion.source_started':
                    messages_received['ingestion_started'] = True
                    print("[PASS] Ingestion source started")

                elif channel == 'intelligence.analyze_log':
                    messages_received['intelligence_routed'] += 1
                    data = json.loads(message['data'])
                    self.captured_data['ingested'].append(data)

                elif channel == 'contextualization.enrich_log':
                    messages_received['contextualization_routed'] += 1

        print(f"\n[RESULTS]")
        print(f"  Source started: {messages_received['ingestion_started']}")
        print(f"  Logs -> Intelligence: {messages_received['intelligence_routed']}")
        print(f"  Logs -> Contextualization: {messages_received['contextualization_routed']}")

        # Stop the test source
        self.redis_client.publish('ingestion.stop_source', json.dumps({
            'data': {'source_id': source_id}
        }))

        if messages_received['intelligence_routed'] > 0:
            print("[PASS] Data ingestion and routing successful")
            self.test_results['data_ingestion'] = 'PASSED'
            return True
        else:
            print("[WARN] No data routed - sources may need real data")
            self.test_results['data_ingestion'] = 'NO_DATA'
            return False

    def test_intelligence_processing(self):
        """Test 3: Verify Intelligence Engine processes data"""
        print("\n" + "="*60)
        print("TEST 3: INTELLIGENCE ENGINE PROCESSING")
        print("="*60)

        # Send test data directly to Intelligence Engine
        test_log = {
            'request_id': f'test_{int(datetime.now().timestamp())}',
            'pattern_data': {
                'event_type': 'authentication',
                'source_ip': '*************',
                'username': 'admin',
                'result': 'failed',
                'failure_count': 5
            },
            'complexity': 'medium'
        }

        print(f"[SEND] Consensus request to Intelligence Engine")
        self.redis_client.publish('intelligence.consensus', json.dumps(test_log))

        # Wait for consensus result
        pubsub = self.redis_client.pubsub()
        pubsub.subscribe('intelligence.consensus_result')

        print("[WAIT] Waiting for consensus result...")

        for _ in range(30):  # 30 second timeout
            message = pubsub.get_message(timeout=1)

            if message and message['type'] == 'message':
                result = json.loads(message['data'])
                self.captured_data['analyzed'].append(result)

                consensus = result.get('data', {}).get('consensus', {})
                print(f"[PASS] Consensus achieved")
                print(f"   Confidence: {consensus.get('confidence', 0):.2%}")
                print(f"   Models used: {consensus.get('num_models', 0)}")

                self.test_results['intelligence_processing'] = 'PASSED'
                return True

        print("[WARN] No consensus result received")
        self.test_results['intelligence_processing'] = 'TIMEOUT'
        return False

    def test_contextualization(self):
        """Test 4: Verify Contextualization Engine enriches data"""
        print("\n" + "="*60)
        print("TEST 4: CONTEXTUALIZATION ENGINE")
        print("="*60)

        # Check if Contextualization Engine is running
        try:
            # Send test log for enrichment
            test_log = {
                'log': {
                    'source': 'test',
                    'timestamp': datetime.now().isoformat(),
                    'data': {
                        'source_ip': '********',
                        'destination_ip': '*************',
                        'username': 'john.doe',
                        'hostname': 'workstation-01',
                        'process': 'powershell.exe'
                    }
                },
                'enrich_level': 'standard'
            }

            print("[SEND] Enrichment request to Contextualization Engine")
            self.redis_client.publish('contextualization.enrich_log', json.dumps(test_log))

            # Monitor for entity extraction
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe([
                'contextualization.entities_extracted',
                'backend.store_entities'
            ])

            print("[WAIT] Waiting for entity extraction...")

            for _ in range(10):
                message = pubsub.get_message(timeout=1)

                if message and message['type'] == 'message':
                    result = json.loads(message['data'])
                    self.captured_data['contextualized'].append(result)

                    print("[PASS] Entities extracted")
                    self.test_results['contextualization'] = 'PASSED'
                    return True

            print("[WARN] Contextualization Engine may not be running")
            self.test_results['contextualization'] = 'NOT_RUNNING'
            return False

        except Exception as e:
            print(f"[FAIL] Contextualization test error: {e}")
            self.test_results['contextualization'] = 'ERROR'
            return False

    def test_database_storage(self):
        """Test 5: Verify data is stored in database"""
        print("\n" + "="*60)
        print("TEST 5: DATABASE STORAGE")
        print("="*60)

        conn = self.connect_database()
        if not conn:
            print("[FAIL] Cannot connect to database")
            self.test_results['database_storage'] = 'NO_CONNECTION'
            return False

        try:
            cur = conn.cursor()

            # Check various tables for data
            tables_to_check = [
                ('ingestion_logs', 'Ingestion logs'),
                ('entities', 'Extracted entities'),
                ('relationships', 'Entity relationships'),
                ('pattern_library', 'Pattern storage'),
                ('consensus_results', 'Consensus results')
            ]

            data_found = False

            for table_name, description in tables_to_check:
                try:
                    cur.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cur.fetchone()[0]
                    print(f"  {description:20} : {count} records")

                    if count > 0:
                        data_found = True
                        self.captured_data['stored'].append({
                            'table': table_name,
                            'count': count
                        })
                except psycopg2.Error:
                    print(f"  {description:20} : Table not found")

            if data_found:
                print("[PASS] Data is being stored in database")
                self.test_results['database_storage'] = 'PASSED'
                return True
            else:
                print("[WARN] No data found in database (may need more processing time)")
                self.test_results['database_storage'] = 'NO_DATA'
                return False

        except Exception as e:
            print(f"[FAIL] Database query error: {e}")
            self.test_results['database_storage'] = 'ERROR'
            return False
        finally:
            conn.close()

    def test_complete_handoff(self):
        """Test 6: Verify complete handoff flow between engines"""
        print("\n" + "="*60)
        print("TEST 6: COMPLETE HANDOFF FLOW")
        print("="*60)

        print("\n[HANDOFF SEQUENCE]")
        print("1. Ingestion -> Intelligence & Contextualization")
        print("2. Intelligence -> Pattern Storage -> Backend")
        print("3. Contextualization -> Entity Storage -> Backend")
        print("4. Backend -> Delivery (when implemented)")

        # Check which engines are running
        running_engines = []

        try:
            conn = self.connect_database()
            if conn:
                cur = conn.cursor()
                cur.execute("""
                    SELECT engine_name, status, last_heartbeat
                    FROM engine_coordination
                    WHERE last_heartbeat > NOW() - INTERVAL '5 minutes'
                """)

                engines = cur.fetchall()
                for engine in engines:
                    running_engines.append(engine[0])
                    print(f"  [OK] {engine[0]:20} : {engine[1]}")

                conn.close()
        except:
            pass

        # Determine handoff status
        if len(running_engines) >= 2:
            print(f"\n[PASS] {len(running_engines)} engines running - handoff possible")
            self.test_results['handoff_flow'] = 'READY'
            return True
        else:
            print(f"\n[WARN] Only {len(running_engines)} engines running")
            print("   Start more engines for complete handoff flow")
            self.test_results['handoff_flow'] = 'PARTIAL'
            return False

    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "#"*60)
        print("# COMPLETE DATA FLOW TEST REPORT")
        print("#"*60)
        print(f"# Timestamp: {datetime.now().isoformat()}")
        print("#"*60)

        print("\n[TEST RESULTS]")
        for test_name, result in self.test_results.items():
            icon = "[PASS]" if result == "PASSED" else "[FAIL]" if result in ["FAILED", "ERROR"] else "[WARN]"
            print(f"{icon} {test_name:25} : {result}")

        print("\n[DATA CAPTURED]")
        print(f"  Ingested logs      : {len(self.captured_data['ingested'])}")
        print(f"  Analyzed patterns  : {len(self.captured_data['analyzed'])}")
        print(f"  Contextualized     : {len(self.captured_data['contextualized'])}")
        print(f"  Database records   : {sum(d['count'] for d in self.captured_data['stored'])}")

        print("\n[NEXT STEPS]")

        # Provide specific recommendations based on results
        if self.test_results.get('elasticsearch_api') == 'NO_CREDENTIALS':
            print("1. Configure Elasticsearch credentials in .env:")
            print("   ELASTIC_CLOUD_ID=your_cloud_id")
            print("   ELASTIC_API_KEY=your_api_key")

        if self.test_results.get('crowdstrike_api') == 'NO_CREDENTIALS':
            print("2. Configure CrowdStrike credentials in .env:")
            print("   CROWDSTRIKE_CLIENT_ID=your_client_id")
            print("   CROWDSTRIKE_CLIENT_SECRET=your_secret")
            print("   CROWDSTRIKE_SCOPES=detections:read,hosts:read,incidents:read")

        if self.test_results.get('crowdstrike_api') == 'NOT_INSTALLED':
            print("3. Install CrowdStrike SDK:")
            print("   pip install crowdstrike-falconpy")

        if self.test_results.get('contextualization') == 'NOT_RUNNING':
            print("4. Start Contextualization Engine:")
            print("   docker-compose up -d contextualization_engine")

        if self.test_results.get('database_storage') == 'NO_DATA':
            print("5. Allow more time for data processing and check again")

        print("\n" + "#"*60)

    def run_all_tests(self):
        """Run complete test suite"""
        print("\n" + "#"*60)
        print("# SIEMLESS V2.0 COMPLETE DATA FLOW TEST")
        print("#"*60)
        print("# Testing: APIs -> Ingestion -> Processing -> Storage")
        print("#"*60)

        # Test 1: API Connections
        api_ok = self.test_api_connections()

        # Test 2: Data Ingestion
        if api_ok:
            self.test_data_ingestion()
        else:
            print("\n[SKIP] Skipping ingestion test - APIs not connected")
            self.test_results['data_ingestion'] = 'SKIPPED'

        # Test 3: Intelligence Processing
        self.test_intelligence_processing()

        # Test 4: Contextualization
        self.test_contextualization()

        # Test 5: Database Storage
        self.test_database_storage()

        # Test 6: Complete Handoff
        self.test_complete_handoff()

        # Generate final report
        self.generate_report()


if __name__ == "__main__":
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("\n[WARN] WARNING: .env file not found!")
        print("Copy .env.example to .env and configure your credentials:")
        print("  cp .env.example .env")
        print("  nano .env  # Add your API keys\n")

    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()

    # Run tests
    tester = CompleteDataFlowTest()
    tester.run_all_tests()