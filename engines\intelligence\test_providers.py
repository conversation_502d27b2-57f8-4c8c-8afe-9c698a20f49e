"""
Test all AI providers with real API calls
"""

import sys
import os
import logging
import asyncio

# Add paths
sys.path.append(os.path.dirname(__file__))

from core.model_registry import ModelRegistry
from core.credential_manager import CredentialManager
from providers.base_provider import ProviderFactory

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_provider(provider_name: str, model: str, factory: ProviderFactory):
    """Test a single provider"""
    print(f"\n{'='*80}")
    print(f"Testing {provider_name.upper()} Provider - Model: {model}")
    print(f"{'='*80}")

    try:
        # Get provider instance
        provider = factory.create_provider(provider_name)
        print(f"[OK] Provider created: {provider}")

        # Simple test prompt
        test_prompt = "What is 2+2? Answer in one word."

        # Call API
        print(f"[OK] Calling {model} with test prompt...")
        response = await provider.call(model, test_prompt, max_tokens=100)

        if response.success:
            print(f"[OK] Success!")
            print(f"    Content: {response.content[:100]}...")
            print(f"    Latency: {response.latency_ms:.2f}ms")
            print(f"    Tokens: {response.input_tokens} in, {response.output_tokens} out")
            print(f"    Confidence: {response.confidence}")
            return True
        else:
            print(f"[FAIL] API call failed: {response.error}")
            return False

    except Exception as e:
        print(f"[FAIL] Exception: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all provider tests"""
    print("\n" + "=" * 80)
    print("TESTING ALL AI PROVIDERS WITH REAL API CALLS")
    print("=" * 80)

    # Initialize components
    config_path = os.path.join(os.path.dirname(__file__), 'config', 'ai_models.yaml')
    registry = ModelRegistry(config_path, logger)
    cred_mgr = CredentialManager(logger=logger)
    factory = ProviderFactory(cred_mgr, logger=logger)

    print(f"\n[OK] Components initialized")
    print(f"[OK] Available providers: {cred_mgr.list_providers()}")

    # Test cases: (provider, model)
    test_cases = []

    # Google models
    if cred_mgr.has_credentials('google'):
        test_cases.extend([
            ('google', 'gemma-27b'),
            ('google', 'gemini-2.5-flash'),
            ('google', 'gemini-2.5-pro')
        ])

    # Anthropic models
    if cred_mgr.has_credentials('anthropic'):
        test_cases.extend([
            ('anthropic', 'claude-sonnet-4'),
            # ('anthropic', 'claude-opus-4')  # Uncomment if you want to test Opus
        ])

    # OpenAI models
    if cred_mgr.has_credentials('openai'):
        test_cases.extend([
            ('openai', 'gpt-4-turbo')
        ])

    # Ollama (no credentials needed, but need to check if running)
    ollama_provider = factory.create_provider('ollama')
    if ollama_provider.validate_connection():
        test_cases.append(('ollama', 'llama3:70b'))

    if not test_cases:
        print("\n[WARNING] No credentials found! Skipping all tests.")
        print("Set environment variables:")
        print("  - GOOGLE_API_KEY for Google models")
        print("  - ANTHROPIC_API_KEY for Claude models")
        print("  - OPENAI_API_KEY for GPT models")
        return

    # Run tests
    results = []
    for provider_name, model in test_cases:
        success = await test_provider(provider_name, model, factory)
        results.append((f"{provider_name}/{model}", success))

        # Small delay between tests to avoid rate limits
        await asyncio.sleep(2)

    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)

    for test_name, success in results:
        status = "[PASS]" if success else "[FAIL]"
        print(f"{test_name:<40} {status}")

    total = len(results)
    passed = sum(1 for _, s in results if s)
    print(f"\nTotal: {passed}/{total} tests passed")

    if passed == total:
        print("\n[SUCCESS] ALL PROVIDER TESTS PASSED!")
        return 0
    else:
        print(f"\n[WARNING] {total - passed} test(s) failed")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
