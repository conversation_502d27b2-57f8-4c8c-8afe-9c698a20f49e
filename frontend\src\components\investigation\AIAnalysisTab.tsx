import React, { useState } from 'react';
import { Alert, EnrichmentData, CorrelationData } from '../../types/investigation';
import '../../styles/AIAnalysisTab.css';

interface AIAnalysisTabProps {
  alert: Alert;
  enrichment: EnrichmentData | null;
  correlation: CorrelationData | null;
}

interface AIInsight {
  category: 'threat' | 'behavior' | 'context' | 'recommendation';
  confidence: number;
  title: string;
  description: string;
  evidence: string[];
}

export const AIAnalysisTab: React.FC<AIAnalysisTabProps> = ({ alert, enrichment, correlation }) => {
  const [selectedModel, setSelectedModel] = useState<'gemini' | 'gpt4' | 'claude'>('gemini');
  const [showEvidence, setShowEvidence] = useState<Record<number, boolean>>({});

  // Generate AI insights based on available data
  const generateInsights = (): AIInsight[] => {
    const insights: AIInsight[] = [];

    // Threat assessment from enrichment
    if (enrichment?.enrichment?.summary.threat_indicators_found > 0) {
      insights.push({
        category: 'threat',
        confidence: 0.92,
        title: 'Malicious Indicators Detected',
        description: `${enrichment.enrichment.summary.threat_indicators_found} threat intelligence sources have flagged entities in this alert as malicious.`,
        evidence: [
          'Multiple CTI sources confirm threat status',
          'Historical attack patterns match current behavior',
          'Known malicious infrastructure identified'
        ]
      });
    }

    // Behavioral analysis from correlation
    if (correlation?.correlation && correlation.correlation.summary.correlation_score > 0.5) {
      insights.push({
        category: 'behavior',
        confidence: 0.85,
        title: 'Suspicious Behavioral Pattern',
        description: 'Event sequence suggests coordinated attack activity with clear progression through multiple stages.',
        evidence: [
          `${correlation.correlation.summary.total_related_events} related events detected`,
          `Activity spans ${correlation.correlation.summary.unique_sources} different sources`,
          'Timeline shows rapid progression typical of automated attacks'
        ]
      });
    }

    // Context analysis
    if (enrichment?.enrichment?.entities && enrichment.enrichment.entities.length > 0) {
      const enrichedEntities = enrichment.enrichment.entities.filter(e => e.threat_score > 0.3);
      if (enrichedEntities.length > 0) {
        insights.push({
          category: 'context',
          confidence: 0.78,
          title: 'Environmental Context Analysis',
          description: 'Multiple high-value assets involved with anomalous access patterns.',
          evidence: [
            `${enrichedEntities.length} entities flagged with elevated threat scores`,
            'Access patterns deviate from established baselines',
            'Geographic anomalies detected in connection sources'
          ]
        });
      }
    }

    // Recommendations
    insights.push({
      category: 'recommendation',
      confidence: 0.88,
      title: 'Recommended Response Actions',
      description: 'Immediate containment recommended based on threat severity and behavioral indicators.',
      evidence: [
        'Isolate affected systems to prevent lateral movement',
        'Reset credentials for compromised accounts',
        'Collect forensic evidence before remediation',
        'Update detection rules based on observed TTPs'
      ]
    });

    return insights;
  };

  const insights = generateInsights();

  const toggleEvidence = (index: number) => {
    setShowEvidence(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const getCategoryIcon = (category: AIInsight['category']) => {
    const icons = {
      threat: '⚠️',
      behavior: '🔍',
      context: '🌐',
      recommendation: '💡'
    };
    return icons[category];
  };

  const getCategoryColor = (category: AIInsight['category']) => {
    const colors = {
      threat: '#dc2626',
      behavior: '#ea580c',
      context: '#3b82f6',
      recommendation: '#10b981'
    };
    return colors[category];
  };

  const getConfidenceLevel = (confidence: number) => {
    if (confidence >= 0.8) return { level: 'High', color: '#10b981' };
    if (confidence >= 0.6) return { level: 'Medium', color: '#f59e0b' };
    return { level: 'Low', color: '#6b7280' };
  };

  const getModelInfo = (model: typeof selectedModel) => {
    const models = {
      gemini: { name: 'Google Gemini 2.0', cost: 'Free', speed: 'Fast' },
      gpt4: { name: 'GPT-4 Turbo', cost: '$0.02', speed: 'Medium' },
      claude: { name: 'Claude Opus 4', cost: '$0.05', speed: 'Slow' }
    };
    return models[model];
  };

  if (!enrichment && !correlation) {
    return (
      <div className="no-ai-analysis">
        <div className="no-ai-icon">🤖</div>
        <h3>AI Analysis Unavailable</h3>
        <p>AI analysis requires enrichment or correlation data.</p>
        <p className="hint">Trigger enrichment and correlation to enable AI-powered insights.</p>
      </div>
    );
  }

  const modelInfo = getModelInfo(selectedModel);

  return (
    <div className="ai-analysis-tab">
      {/* AI Model Selection */}
      <div className="ai-header">
        <div className="ai-title">
          <h3>🤖 AI-Powered Analysis</h3>
          <p>Multi-model consensus analysis using crystallized patterns</p>
        </div>
        <div className="model-selector">
          <label>Analysis Model:</label>
          <select value={selectedModel} onChange={(e) => setSelectedModel(e.target.value as any)}>
            <option value="gemini">Google Gemini 2.0 (Free)</option>
            <option value="gpt4">GPT-4 Turbo ($0.02)</option>
            <option value="claude">Claude Opus 4 ($0.05)</option>
          </select>
          <div className="model-info">
            <span className="info-badge">💰 {modelInfo.cost}</span>
            <span className="info-badge">⚡ {modelInfo.speed}</span>
          </div>
        </div>
      </div>

      {/* AI Summary Card */}
      <div className="ai-summary-card">
        <div className="summary-header">
          <h4>🎯 Executive Summary</h4>
          <div className="summary-meta">
            <span className="meta-item">📊 {insights.length} insights</span>
            <span className="meta-item">
              🎯 Avg Confidence: {((insights.reduce((sum, i) => sum + i.confidence, 0) / insights.length) * 100).toFixed(0)}%
            </span>
          </div>
        </div>
        <div className="summary-content">
          <p>
            Based on multi-dimensional analysis of {enrichment?.enrichment?.entities?.length || 0} enriched entities
            and {correlation?.correlation?.summary.total_related_events || 0} correlated events, this alert demonstrates
            characteristics consistent with {alert.severity === 'critical' || alert.severity === 'high' ? 'malicious' : 'suspicious'} activity.
            The AI consensus model recommends {alert.severity === 'critical' ? 'immediate' : 'prompt'} investigation
            with {insights.filter(i => i.category === 'recommendation').length} specific action items.
          </p>
        </div>
      </div>

      {/* Insights Grid */}
      <div className="insights-grid">
        {insights.map((insight, index) => {
          const confidenceInfo = getConfidenceLevel(insight.confidence);
          return (
            <div
              key={index}
              className="insight-card"
              style={{ borderLeftColor: getCategoryColor(insight.category) }}
            >
              <div className="insight-header">
                <div className="insight-title-row">
                  <span className="insight-icon">{getCategoryIcon(insight.category)}</span>
                  <h5 className="insight-title">{insight.title}</h5>
                </div>
                <div className="insight-meta">
                  <span
                    className="confidence-badge"
                    style={{ background: confidenceInfo.color }}
                  >
                    {confidenceInfo.level} Confidence ({(insight.confidence * 100).toFixed(0)}%)
                  </span>
                  <span className="category-badge" style={{ color: getCategoryColor(insight.category) }}>
                    {insight.category.toUpperCase()}
                  </span>
                </div>
              </div>

              <div className="insight-description">
                {insight.description}
              </div>

              <div className="insight-footer">
                <button
                  className="evidence-toggle"
                  onClick={() => toggleEvidence(index)}
                >
                  {showEvidence[index] ? '▼' : '▶'} {insight.evidence.length} Evidence Items
                </button>
              </div>

              {showEvidence[index] && (
                <div className="evidence-list">
                  {insight.evidence.map((item, eIndex) => (
                    <div key={eIndex} className="evidence-item">
                      <span className="evidence-bullet">•</span>
                      <span className="evidence-text">{item}</span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Pattern Crystallization Status */}
      <div className="crystallization-section">
        <h4>💎 Pattern Crystallization</h4>
        <div className="crystallization-grid">
          <div className="crystallization-card">
            <div className="crystal-icon">🔄</div>
            <div className="crystal-label">Pattern Status</div>
            <div className="crystal-value">Learning</div>
            <div className="crystal-desc">First occurrence - AI analysis required</div>
          </div>
          <div className="crystallization-card">
            <div className="crystal-icon">💰</div>
            <div className="crystal-label">Analysis Cost</div>
            <div className="crystal-value">{modelInfo.cost}</div>
            <div className="crystal-desc">Next occurrence: $0.00 (crystallized)</div>
          </div>
          <div className="crystallization-card">
            <div className="crystal-icon">📈</div>
            <div className="crystal-label">Reuse Potential</div>
            <div className="crystal-value">High</div>
            <div className="crystal-desc">Similar patterns: 15 in last 30 days</div>
          </div>
        </div>
      </div>

      {/* AI Reasoning Chain */}
      <div className="reasoning-section">
        <h4>🧠 AI Reasoning Chain</h4>
        <div className="reasoning-steps">
          <div className="reasoning-step">
            <div className="step-number">1</div>
            <div className="step-content">
              <div className="step-title">Data Collection</div>
              <div className="step-desc">
                Gathered {enrichment?.enrichment?.entities?.length || 0} enriched entities,
                {' '}{correlation?.correlation?.summary.total_related_events || 0} correlated events
              </div>
            </div>
          </div>

          <div className="reasoning-step">
            <div className="step-number">2</div>
            <div className="step-content">
              <div className="step-title">Threat Intelligence Correlation</div>
              <div className="step-desc">
                Cross-referenced with {enrichment?.enrichment?.summary.threat_indicators_found || 0} CTI sources
              </div>
            </div>
          </div>

          <div className="reasoning-step">
            <div className="step-number">3</div>
            <div className="step-content">
              <div className="step-title">Behavioral Analysis</div>
              <div className="step-desc">
                Analyzed event patterns and temporal relationships
              </div>
            </div>
          </div>

          <div className="reasoning-step">
            <div className="step-number">4</div>
            <div className="step-content">
              <div className="step-title">Context Enrichment</div>
              <div className="step-desc">
                Applied environmental context and baseline deviations
              </div>
            </div>
          </div>

          <div className="reasoning-step">
            <div className="step-number">5</div>
            <div className="step-content">
              <div className="step-title">Consensus Verdict</div>
              <div className="step-desc">
                Generated {insights.length} actionable insights with confidence scoring
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="ai-actions">
        <button className="ai-action-btn primary">
          🔄 Re-run Analysis (Different Model)
        </button>
        <button className="ai-action-btn">
          💾 Save Pattern to Library
        </button>
        <button className="ai-action-btn">
          📤 Export Analysis Report
        </button>
      </div>
    </div>
  );
};
