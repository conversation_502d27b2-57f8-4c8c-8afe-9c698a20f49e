"""
Test Rule Management Phase 2 Endpoints
Tests DELETE, PATCH, bulk-delete, and filtered list operations
"""

import requests
import json

BASE_URL = "http://localhost:8005/api"

def test_list_rules_filtered():
    """Test GET /api/rules with quality filter"""
    print("\n=== Test 1: List rules with quality filter ===")

    # Test low quality rules
    response = requests.get(f"{BASE_URL}/rules", params={'quality_label': 'low', 'limit': 5})
    data = response.json()
    print(f"Low quality rules: {data['total']} found")
    if data['rules']:
        print(f"Sample: {data['rules'][0]['rule_name']}")

    # Test medium quality rules
    response = requests.get(f"{BASE_URL}/rules", params={'quality_label': 'medium', 'limit': 5})
    data = response.json()
    print(f"Medium quality rules: {data['total']} found")

    # Test high quality rules
    response = requests.get(f"{BASE_URL}/rules", params={'quality_label': 'high', 'limit': 5})
    data = response.json()
    print(f"High quality rules: {data['total']} found")

    print("[OK] List with filters working")
    return True


def test_update_rule():
    """Test PATCH /api/rules/{rule_id}"""
    print("\n=== Test 2: Update rule metadata ===")

    # Get a rule to update
    response = requests.get(f"{BASE_URL}/rules", params={'quality_label': 'low', 'limit': 1})
    rules = response.json()['rules']

    if not rules:
        print("[WARN] No low quality rules to test update")
        return False

    rule_id = rules[0]['rule_id']
    print(f"Testing update on rule: {rule_id}")

    # Update the rule
    update_data = {
        'quality_label': 'high',  # Override quality label
        'custom_tags': ['reviewed', 'false-positive'],
        'notes': 'Manually reviewed - confirmed false positive',
        'reviewed_by': 'test_script'
    }

    response = requests.patch(f"{BASE_URL}/rules/{rule_id}", json=update_data)

    if response.status_code == 200:
        result = response.json()
        print(f"[OK] Updated successfully")
        print(f"  Quality label: {result['quality_label']}")
        print(f"  Custom tags: {result['custom_tags']}")
        print(f"  Reviewed by: {result['reviewed_by']}")
        return True
    else:
        print(f"[FAIL] Update failed: {response.status_code}")
        print(response.json())
        return False


def test_bulk_delete_dry_run():
    """Test POST /api/rules/bulk-delete with dry_run=true"""
    print("\n=== Test 3: Bulk delete (dry-run) ===")

    # Dry-run bulk delete low quality rules
    delete_request = {
        'quality_label': 'low',
        'deployed': False,  # Only undeployed rules
        'dry_run': True  # Preview only, don't actually delete
    }

    response = requests.post(f"{BASE_URL}/rules/bulk-delete", json=delete_request)

    if response.status_code == 200:
        result = response.json()
        print(f"[OK] Dry-run successful")
        print(f"  Would delete: {result['total_matches']} rules")
        if result.get('sample_rules'):
            print(f"  Sample rule: {result['sample_rules'][0]['rule_name']}")
        return True
    else:
        print(f"[FAIL] Dry-run failed: {response.status_code}")
        print(response.json())
        return False


def test_delete_single_rule():
    """Test DELETE /api/rules/{rule_id}"""
    print("\n=== Test 4: Delete single rule ===")

    # Get a rule to delete
    response = requests.get(f"{BASE_URL}/rules", params={'quality_label': 'low', 'limit': 1})
    rules = response.json()['rules']

    if not rules:
        print("[WARN] No low quality rules to test delete")
        return False

    rule_id = rules[0]['rule_id']
    rule_name = rules[0]['rule_name']
    print(f"Deleting rule: {rule_name} ({rule_id})")

    response = requests.delete(f"{BASE_URL}/rules/{rule_id}")

    if response.status_code == 200:
        result = response.json()
        print(f"[OK] Deleted successfully")
        print(f"  Deleted from {len(result['deleted_from_siems'])} SIEM(s)")

        # Verify deletion
        verify = requests.get(f"{BASE_URL}/rules/{rule_id}")
        if verify.status_code == 404:
            print(f"[OK] Verified deletion - rule no longer exists")
        return True
    else:
        print(f"[FAIL] Delete failed: {response.status_code}")
        print(response.json())
        return False


def run_all_tests():
    """Run all tests"""
    print("=" * 60)
    print("Rule Management Phase 2 - API Test Suite")
    print("=" * 60)

    results = []

    # Test 1: List with filters
    try:
        results.append(("List Rules with Filters", test_list_rules_filtered()))
    except Exception as e:
        print(f"[FAIL] Test failed with exception: {e}")
        results.append(("List Rules with Filters", False))

    # Test 2: Update rule metadata
    try:
        results.append(("Update Rule Metadata", test_update_rule()))
    except Exception as e:
        print(f"[FAIL] Test failed with exception: {e}")
        results.append(("Update Rule Metadata", False))

    # Test 3: Bulk delete dry-run
    try:
        results.append(("Bulk Delete (Dry-Run)", test_bulk_delete_dry_run()))
    except Exception as e:
        print(f"[FAIL] Test failed with exception: {e}")
        results.append(("Bulk Delete (Dry-Run)", False))

    # Test 4: Delete single rule
    try:
        results.append(("Delete Single Rule", test_delete_single_rule()))
    except Exception as e:
        print(f"[FAIL] Test failed with exception: {e}")
        results.append(("Delete Single Rule", False))

    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)

    for test_name, passed in results:
        status = "[OK] PASS" if passed else "[FAIL] FAIL"
        print(f"{status:8} {test_name}")

    passed_count = sum(1 for _, passed in results if passed)
    total_count = len(results)

    print("=" * 60)
    print(f"Results: {passed_count}/{total_count} tests passed")
    print("=" * 60)

    return passed_count == total_count


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
