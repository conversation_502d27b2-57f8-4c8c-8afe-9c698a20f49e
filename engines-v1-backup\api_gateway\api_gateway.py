"""
API Gateway - REST/WebSocket interface for SIEMLess v2.0

This gateway provides:
- REST endpoints for all engine operations
- WebSocket for real-time updates
- Authentication and authorization
- Rate limiting and request validation
- Load balancing to engine instances
"""
import asyncio
import json
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from dataclasses import dataclass
import time
from collections import defaultdict
import websockets
from aiohttp import web, WSConnection
import aiohttp_cors
from aiohttp.web import middleware
import jwt

# Add v2 to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.base import BaseEngine


@dataclass
class APIRequest:
    """API request wrapper"""
    request_id: str
    user_id: str
    endpoint: str
    method: str
    data: Dict[str, Any]
    timestamp: datetime
    response_time: Optional[float] = None


class APIGateway(BaseEngine):
    """
    API Gateway: Central interface for all v2 engines

    Provides REST and WebSocket APIs, handles authentication,
    rate limiting, and routes requests to appropriate engines.
    """

    def __init__(self):
        super().__init__('api_gateway', '2.0.0')

        # Server configuration
        self.config = {
            'host': '0.0.0.0',
            'port': 8000,
            'websocket_port': 8001,
            'rate_limit_per_minute': 1000,
            'max_request_size': 10 * 1024 * 1024,  # 10MB
            'jwt_secret': 'siemless-v2-secret-key-2024',
            'jwt_algorithm': 'HS256'
        }

        # Rate limiting
        self.rate_limiter = defaultdict(lambda: {
            'requests': [],
            'blocked_until': None
        })

        # Active WebSocket connections
        self.websocket_connections: Dict[str, WSConnection] = {}

        # Request tracking
        self.active_requests: Dict[str, APIRequest] = {}
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0,
            'requests_by_endpoint': defaultdict(int),
            'requests_by_user': defaultdict(int)
        }

        # Engine routing map
        self.engine_routes = {
            # Ingestion routes
            '/api/v2/ingestion/ingest': 'ingestion',
            '/api/v2/ingestion/stats': 'ingestion',
            '/api/v2/ingestion/status': 'ingestion',

            # Parser routes
            '/api/v2/parser/parse': 'parser',
            '/api/v2/parser/patterns': 'parser',
            '/api/v2/parser/test': 'parser',

            # Entity extraction routes
            '/api/v2/entities/extract': 'entity_extractor',
            '/api/v2/entities/normalize': 'entity_extractor',
            '/api/v2/entities/list': 'entity_extractor',

            # Use case context routes
            '/api/v2/context/analyze': 'use_case_context',
            '/api/v2/context/use-cases': 'use_case_context',
            '/api/v2/context/risk-score': 'use_case_context',

            # Graph builder routes
            '/api/v2/graph/build': 'graph_builder',
            '/api/v2/graph/analyze': 'graph_builder',
            '/api/v2/graph/visualize': 'graph_builder',

            # Detection routes
            '/api/v2/detection/analyze': 'detection',
            '/api/v2/detection/rules': 'detection',
            '/api/v2/detection/alerts': 'detection',
            '/api/v2/detection/stats': 'detection',

            # Pattern management routes
            '/api/v2/patterns/create': 'pattern_manager',
            '/api/v2/patterns/update': 'pattern_manager',
            '/api/v2/patterns/delete': 'pattern_manager',
            '/api/v2/patterns/list': 'pattern_manager',
            '/api/v2/patterns/get': 'pattern_manager',
            '/api/v2/patterns/test': 'pattern_manager',
            '/api/v2/patterns/rollback': 'pattern_manager',
            '/api/v2/patterns/sync': 'pattern_manager',

            # AI consensus routes
            '/api/v2/ai/consensus': 'ai_consensus',
            '/api/v2/ai/validate': 'ai_consensus',
            '/api/v2/ai/models': 'ai_consensus',

            # Librarian routes
            '/api/v2/librarian/submit': 'librarian',
            '/api/v2/librarian/patterns': 'librarian',
            '/api/v2/librarian/stats': 'librarian',

            # Ruleset engine routes
            '/api/v2/ruleset/generate': 'ruleset',
            '/api/v2/ruleset/library': 'ruleset',
            '/api/v2/ruleset/sigma': 'ruleset',
            '/api/v2/ruleset/yara': 'ruleset',
            '/api/v2/ruleset/kql': 'ruleset',
            '/api/v2/ruleset/spl': 'ruleset',
            '/api/v2/ruleset/crystallize': 'ruleset',
            '/api/v2/ruleset/mitre': 'ruleset',

            # Query engine routes
            '/api/v2/query/translate': 'query',
            '/api/v2/query/deploy': 'query',
            '/api/v2/query/siems': 'query',
            '/api/v2/query/test': 'query',
            '/api/v2/query/stats': 'query',
            '/api/v2/query/mappings': 'query',
            '/api/v2/query/enrichment': 'query',
            '/api/v2/query/context': 'query',

            # Community Knowledge Engine routes
            '/api/v2/community/sources': 'community',
            '/api/v2/community/sources/add': 'community',
            '/api/v2/community/sources/remove': 'community',
            '/api/v2/community/sources/update': 'community',
            '/api/v2/community/knowledge': 'community',
            '/api/v2/community/knowledge/search': 'community',
            '/api/v2/community/mitre/update': 'community',
            '/api/v2/community/mitre/techniques': 'community',
            '/api/v2/community/mitre/tactics': 'community',
            '/api/v2/community/github/repositories': 'community',
            '/api/v2/community/github/harvest': 'community',
            '/api/v2/community/stats': 'community',
            '/api/v2/community/validate': 'community',
            '/api/v2/community/patterns/sigma': 'community',
            '/api/v2/community/patterns/yara': 'community',
            '/api/v2/community/patterns/elastic': 'community',

            # SIEM Intelligence Harvesting routes
            '/api/v2/siem/harvest/all': 'ingestion',
            '/api/v2/siem/harvest/platform': 'ingestion',
            '/api/v2/siem/artifacts/rules': 'ingestion',
            '/api/v2/siem/artifacts/dashboards': 'ingestion',
            '/api/v2/siem/artifacts/playbooks': 'ingestion',
            '/api/v2/siem/artifacts/queries': 'ingestion',
            '/api/v2/siem/artifacts/macros': 'ingestion',
            '/api/v2/siem/artifacts/workflows': 'ingestion',
            '/api/v2/siem/platforms/supported': 'ingestion',
            '/api/v2/siem/platforms/status': 'ingestion',
            '/api/v2/siem/statistics/harvest': 'ingestion',
            '/api/v2/siem/export/crystallization': 'ingestion',
            '/api/v2/siem/export/automation': 'ingestion',

            # SOC Playbook Extraction routes
            '/api/v2/soc/playbooks/extract': 'ingestion',
            '/api/v2/soc/playbooks/list': 'ingestion',
            '/api/v2/soc/playbooks/types': 'ingestion',
            '/api/v2/soc/playbooks/sources': 'ingestion',
            '/api/v2/soc/playbooks/search': 'ingestion',
            '/api/v2/soc/procedures/incident-response': 'ingestion',
            '/api/v2/soc/procedures/investigation': 'ingestion',
            '/api/v2/soc/procedures/escalation': 'ingestion',
            '/api/v2/soc/workflows/automation': 'ingestion',
            '/api/v2/soc/statistics/playbooks': 'ingestion',

            # Case Management Engine routes (Port 5011)
            '/api/v2/cases': 'case_management',
            '/api/v2/cases/create': 'case_management',
            '/api/v2/cases/list': 'case_management',
            '/api/v2/cases/search': 'case_management',
            '/api/v2/cases/export': 'case_management',
            '/api/v2/cases/metrics': 'case_management',
            '/api/v2/cases/dashboard': 'case_management',
            '/api/v2/cases/templates': 'case_management',
            '/api/v2/cases/sla-policies': 'case_management',
            '/api/v2/cases/bulk/assign': 'case_management',
            '/api/v2/cases/bulk/status': 'case_management',
            '/api/v2/cases/{case_id}': 'case_management',
            '/api/v2/cases/{case_id}/assign': 'case_management',
            '/api/v2/cases/{case_id}/evidence': 'case_management',
            '/api/v2/cases/{case_id}/escalate': 'case_management',
            '/api/v2/cases/{case_id}/close': 'case_management',
            '/api/v2/cases/health': 'case_management',
            '/api/v2/cases/info': 'case_management',

            # Investigation Engine routes (Port 5012)
            '/api/v2/investigations': 'investigation',
            '/api/v2/investigations/create': 'investigation',
            '/api/v2/investigations/list': 'investigation',
            '/api/v2/investigations/search': 'investigation',
            '/api/v2/investigations/templates': 'investigation',
            '/api/v2/investigations/metrics': 'investigation',
            '/api/v2/investigations/{investigation_id}': 'investigation',
            '/api/v2/investigations/{investigation_id}/evidence': 'investigation',
            '/api/v2/investigations/{investigation_id}/hypotheses': 'investigation',
            '/api/v2/investigations/{investigation_id}/timeline': 'investigation',
            '/api/v2/investigations/{investigation_id}/hunt': 'investigation',
            '/api/v2/investigations/{investigation_id}/analyze': 'investigation',
            '/api/v2/investigations/{investigation_id}/indicators': 'investigation',
            '/api/v2/investigations/{investigation_id}/patterns': 'investigation',
            '/api/v2/investigations/{investigation_id}/attribution': 'investigation',
            '/api/v2/investigations/{investigation_id}/export': 'investigation',
            '/api/v2/investigations/health': 'investigation',
            '/api/v2/investigations/info': 'investigation',

            # Malware Analysis Engine routes (Port 5013)
            '/api/v2/malware': 'malware_analysis',
            '/api/v2/malware/submit': 'malware_analysis',
            '/api/v2/malware/analyze': 'malware_analysis',
            '/api/v2/malware/results': 'malware_analysis',
            '/api/v2/malware/list': 'malware_analysis',
            '/api/v2/malware/search': 'malware_analysis',
            '/api/v2/malware/metrics': 'malware_analysis',
            '/api/v2/malware/signatures': 'malware_analysis',
            '/api/v2/malware/patterns': 'malware_analysis',
            '/api/v2/malware/threat-intel': 'malware_analysis',
            '/api/v2/malware/{analysis_id}': 'malware_analysis',
            '/api/v2/malware/{analysis_id}/static': 'malware_analysis',
            '/api/v2/malware/{analysis_id}/dynamic': 'malware_analysis',
            '/api/v2/malware/{analysis_id}/behavioral': 'malware_analysis',
            '/api/v2/malware/{analysis_id}/iocs': 'malware_analysis',
            '/api/v2/malware/{analysis_id}/attribution': 'malware_analysis',
            '/api/v2/malware/{analysis_id}/recommendations': 'malware_analysis',
            '/api/v2/malware/health': 'malware_analysis',
            '/api/v2/malware/info': 'malware_analysis',

            # Enhanced Ruleset Engine routes (includes CTI & lifecycle management)
            '/api/v2/rules': 'ruleset',
            '/api/v2/rules/generate': 'ruleset',
            '/api/v2/rules/list': 'ruleset',
            '/api/v2/rules/search': 'ruleset',
            '/api/v2/rules/batch/analyze': 'ruleset',
            '/api/v2/rules/{rule_id}': 'ruleset',
            '/api/v2/rules/{rule_id}/performance': 'ruleset',
            '/api/v2/rules/{rule_id}/suggestions': 'ruleset',
            '/api/v2/rules/{rule_id}/suggestions/generate': 'ruleset',
            '/api/v2/rules/{rule_id}/tests': 'ruleset',
            '/api/v2/rules/{rule_id}/tests/run': 'ruleset',
            '/api/v2/rules/{rule_id}/deploy': 'ruleset',
            '/api/v2/rules/{rule_id}/subscribe': 'ruleset',
            '/api/v2/cti/ingest': 'ruleset',
            '/api/v2/cti/batch-ingest': 'ruleset',
            '/api/v2/analytics/performance-summary': 'ruleset',
            '/api/v2/ruleset/health': 'ruleset',
            '/api/v2/ruleset/status': 'ruleset'
        }

        # Engine service URLs
        self.engine_urls = {
            'librarian': 'http://localhost:5001',
            'parser': 'http://localhost:5002',
            'ingestion': 'http://localhost:5003',
            'entity_extractor': 'http://localhost:5004',
            'use_case_context': 'http://localhost:5005',
            'graph_builder': 'http://localhost:5006',
            'detection': 'http://localhost:5007',
            'pattern_manager': 'http://localhost:5008',
            'ai_consensus': 'http://localhost:5009',
            'community': 'http://localhost:5010',
            'case_management': 'http://localhost:5011',
            'investigation': 'http://localhost:5012',
            'malware_analysis': 'http://localhost:5013',
            'ruleset': 'http://localhost:5014'  # Enhanced Ruleset Engine with CTI & lifecycle management
        }

        # WebSocket event types
        self.websocket_events = {
            'pattern_updated',
            'detection_alert',
            'ingestion_stats',
            'system_status',
            'real_time_log',
            'graph_update',
            'ai_consensus_result',
            'community_source_added',
            'community_knowledge_harvested',
            'mitre_update_completed',
            'github_harvest_started',
            'community_pattern_discovered',
            'siem_harvest_started',
            'siem_artifacts_discovered',
            'siem_platform_connected',
            'siem_harvest_completed',
            'soc_playbook_extracted',
            'soc_procedure_discovered',
            'automation_workflow_found',
            'siem_intelligence_ready',
            'rule_generated',
            'rule_crystallized',
            # Case Management events
            'case_created',
            'case_updated',
            'case_assigned',
            'case_escalated',
            'case_closed',
            'case_evidence_added',
            'case_sla_breach',
            'case_metrics_updated',
            # Investigation events
            'investigation_created',
            'investigation_evidence_added',
            'investigation_hypothesis_added',
            'investigation_patterns_found',
            'investigation_timeline_updated',
            'investigation_threat_hunt_completed',
            'investigation_analysis_completed',
            'investigation_attribution_updated',
            # Malware Analysis events
            'malware_sample_submitted',
            'malware_analysis_started',
            'malware_analysis_completed',
            'malware_static_analysis_done',
            'malware_dynamic_analysis_done',
            'malware_behavioral_patterns_detected',
            'malware_iocs_extracted',
            'malware_threat_attribution_completed',
            'malware_signatures_updated',
            'query_translated',
            'siem_deployed',
            'enrichment_updated',
            'context_updated',
            # Rule Lifecycle Management events
            'rule_created',
            'rule_updated',
            'rule_performance_updated',
            'rule_suggestion_generated',
            'rule_test_completed',
            'rule_deployed',
            'rule_needs_attention',
            'cti_ingested',
            'cti_rules_generated',
            'rule_analysis_completed',
            'rule_improvement_suggested',
            'rule_lifecycle_alert'
        }

    async def start(self):
        """Start API Gateway with both REST and WebSocket servers"""
        self.logger.log('api_gateway_starting', {
            'rest_port': self.config['port'],
            'websocket_port': self.config['websocket_port']
        })

        # Start REST server
        rest_task = asyncio.create_task(self._start_rest_server())

        # Start WebSocket server
        websocket_task = asyncio.create_task(self._start_websocket_server())

        # Start message queue processor
        queue_task = asyncio.create_task(super().start())

        # Start statistics reporter
        stats_task = asyncio.create_task(self._start_stats_reporter())

        # Wait for all servers
        await asyncio.gather(rest_task, websocket_task, queue_task, stats_task)

    async def _start_rest_server(self):
        """Start aiohttp REST server"""
        app = web.Application(middlewares=[
            self._auth_middleware,
            self._rate_limit_middleware,
            self._logging_middleware
        ])

        # Configure CORS
        cors = aiohttp_cors.setup(app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })

        # Add routes
        self._setup_routes(app, cors)

        # Start server
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, self.config['host'], self.config['port'])

        self.logger.log('rest_server_started', {
            'host': self.config['host'],
            'port': self.config['port']
        })

        await site.start()

        # Keep server running
        while True:
            await asyncio.sleep(3600)

    async def _start_websocket_server(self):
        """Start WebSocket server for real-time updates"""
        async def websocket_handler(websocket, path):
            connection_id = str(uuid.uuid4())
            self.websocket_connections[connection_id] = websocket

            try:
                self.logger.log('websocket_connected', {
                    'connection_id': connection_id,
                    'remote_address': websocket.remote_address
                })

                # Send welcome message
                await websocket.send(json.dumps({
                    'type': 'welcome',
                    'connection_id': connection_id,
                    'timestamp': datetime.utcnow().isoformat()
                }))

                # Keep connection alive and handle messages
                async for message in websocket:
                    await self._handle_websocket_message(connection_id, message)

            except websockets.exceptions.ConnectionClosed:
                pass
            except Exception as e:
                self.logger.log_error(e, {'connection_id': connection_id})
            finally:
                self.websocket_connections.pop(connection_id, None)
                self.logger.log('websocket_disconnected', {'connection_id': connection_id})

        # Start WebSocket server
        server = await websockets.serve(
            websocket_handler,
            self.config['host'],
            self.config['websocket_port']
        )

        self.logger.log('websocket_server_started', {
            'host': self.config['host'],
            'port': self.config['websocket_port']
        })

        await server.wait_closed()

    def _setup_routes(self, app, cors):
        """Setup REST API routes"""

        # Health check
        app.router.add_get('/health', self._handle_health)
        app.router.add_get('/api/v2/health', self._handle_health)

        # System status
        app.router.add_get('/api/v2/status', self._handle_system_status)

        # Engine proxy routes
        for route, engine in self.engine_routes.items():
            app.router.add_route('*', route, self._create_engine_handler(engine))

        # Pipeline execution (orchestrated)
        app.router.add_post('/api/v2/pipeline/execute', self._handle_pipeline_execution)

        # Real-time log processing
        app.router.add_post('/api/v2/realtime/process', self._handle_realtime_processing)

        # Statistics and monitoring
        app.router.add_get('/api/v2/stats/overall', self._handle_overall_stats)
        app.router.add_get('/api/v2/stats/engines', self._handle_engine_stats)

        # Add CORS to all routes
        for route in list(app.router.routes()):
            cors.add(route)

    @middleware
    async def _auth_middleware(self, request, handler):
        """Authentication middleware"""
        # Skip auth for health checks
        if request.path in ['/health', '/api/v2/health']:
            return await handler(request)

        # Extract JWT token
        auth_header = request.headers.get('Authorization', '')
        if not auth_header.startswith('Bearer '):
            return web.json_response(
                {'error': 'Missing or invalid authorization header'},
                status=401
            )

        token = auth_header[7:]  # Remove 'Bearer '

        try:
            # Verify JWT token
            payload = jwt.decode(
                token,
                self.config['jwt_secret'],
                algorithms=[self.config['jwt_algorithm']]
            )

            # Add user info to request
            request['user_id'] = payload.get('user_id', 'unknown')
            request['user_roles'] = payload.get('roles', [])

            return await handler(request)

        except jwt.InvalidTokenError as e:
            return web.json_response(
                {'error': f'Invalid token: {str(e)}'},
                status=401
            )

    @middleware
    async def _rate_limit_middleware(self, request, handler):
        """Rate limiting middleware"""
        user_id = getattr(request, 'user_id', request.remote)
        current_time = time.time()

        # Clean old requests
        user_limit = self.rate_limiter[user_id]
        user_limit['requests'] = [
            req_time for req_time in user_limit['requests']
            if current_time - req_time < 60  # 1 minute window
        ]

        # Check if blocked
        if user_limit['blocked_until'] and current_time < user_limit['blocked_until']:
            return web.json_response(
                {'error': 'Rate limit exceeded, blocked temporarily'},
                status=429
            )

        # Check rate limit
        if len(user_limit['requests']) >= self.config['rate_limit_per_minute']:
            user_limit['blocked_until'] = current_time + 60  # Block for 1 minute
            return web.json_response(
                {'error': 'Rate limit exceeded'},
                status=429
            )

        # Record request
        user_limit['requests'].append(current_time)

        return await handler(request)

    @middleware
    async def _logging_middleware(self, request, handler):
        """Request logging middleware"""
        request_id = str(uuid.uuid4())
        start_time = time.time()

        # Create request object
        api_request = APIRequest(
            request_id=request_id,
            user_id=getattr(request, 'user_id', 'anonymous'),
            endpoint=request.path,
            method=request.method,
            data={},
            timestamp=datetime.utcnow()
        )

        self.active_requests[request_id] = api_request

        try:
            response = await handler(request)
            api_request.response_time = time.time() - start_time

            # Update statistics
            self.request_stats['total_requests'] += 1
            self.request_stats['successful_requests'] += 1
            self.request_stats['requests_by_endpoint'][request.path] += 1
            self.request_stats['requests_by_user'][api_request.user_id] += 1

            # Log successful request
            self.logger.log('api_request', {
                'request_id': request_id,
                'user_id': api_request.user_id,
                'endpoint': request.path,
                'method': request.method,
                'response_time': api_request.response_time,
                'status': response.status
            })

            return response

        except Exception as e:
            api_request.response_time = time.time() - start_time
            self.request_stats['failed_requests'] += 1

            self.logger.log_error(e, {
                'request_id': request_id,
                'endpoint': request.path,
                'method': request.method
            })

            return web.json_response(
                {'error': 'Internal server error', 'request_id': request_id},
                status=500
            )
        finally:
            self.active_requests.pop(request_id, None)

    async def _handle_health(self, request):
        """Health check endpoint"""
        return web.json_response({
            'status': 'healthy',
            'service': 'api_gateway',
            'version': self.version,
            'timestamp': datetime.utcnow().isoformat()
        })

    async def _handle_system_status(self, request):
        """System status endpoint"""
        # Check engine health via message queue
        engine_status = {}

        for engine_name in set(self.engine_routes.values()):
            try:
                # Send health check to engine
                message = {
                    'id': str(uuid.uuid4()),
                    'command': 'health_check',
                    'timestamp': datetime.utcnow().isoformat()
                }

                # In a real implementation, we'd wait for response
                # For now, assume healthy
                engine_status[engine_name] = 'healthy'

            except Exception as e:
                engine_status[engine_name] = f'error: {str(e)}'

        return web.json_response({
            'status': 'operational',
            'engines': engine_status,
            'api_gateway': {
                'active_connections': len(self.websocket_connections),
                'active_requests': len(self.active_requests),
                'total_requests': self.request_stats['total_requests']
            },
            'timestamp': datetime.utcnow().isoformat()
        })

    def _create_engine_handler(self, engine_name: str):
        """Create handler for engine-specific routes"""
        async def handler(request):
            try:
                # Parse request data
                if request.method in ['POST', 'PUT', 'PATCH']:
                    data = await request.json()
                else:
                    data = dict(request.query)

                # Create message for engine
                message = {
                    'id': str(uuid.uuid4()),
                    'source_engine': 'api_gateway',
                    'target_engine': engine_name,
                    'command': self._extract_command_from_path(request.path),
                    'data': data,
                    'user_id': getattr(request, 'user_id', 'anonymous'),
                    'timestamp': datetime.utcnow().isoformat()
                }

                # Send to engine via message queue
                result = await self._send_to_engine(engine_name, message)

                return web.json_response(result)

            except Exception as e:
                return web.json_response(
                    {'error': str(e)},
                    status=500
                )

        return handler

    async def _handle_pipeline_execution(self, request):
        """Handle full pipeline execution"""
        try:
            data = await request.json()
            log_data = data.get('log_data')
            pipeline_config = data.get('config', {})

            if not log_data:
                return web.json_response(
                    {'error': 'log_data is required'},
                    status=400
                )

            # Execute full pipeline: Ingestion → Parser → Entity → Context → Graph → Detection
            pipeline_id = str(uuid.uuid4())

            # Track pipeline execution
            pipeline_result = {
                'pipeline_id': pipeline_id,
                'start_time': datetime.utcnow().isoformat(),
                'stages': []
            }

            # Stage 1: Ingestion
            ingestion_message = {
                'id': f"{pipeline_id}_ingestion",
                'command': 'ingest_log',
                'data': {'log_data': log_data},
                'pipeline_id': pipeline_id
            }

            ingestion_result = await self._send_to_engine('ingestion', ingestion_message)
            pipeline_result['stages'].append({
                'stage': 'ingestion',
                'result': ingestion_result,
                'timestamp': datetime.utcnow().isoformat()
            })

            if not ingestion_result.get('success'):
                return web.json_response(pipeline_result, status=400)

            # Continue with other stages...
            # (Implementation would continue through all pipeline stages)

            pipeline_result['end_time'] = datetime.utcnow().isoformat()
            pipeline_result['success'] = True

            # Broadcast pipeline completion via WebSocket
            await self._broadcast_websocket_event('pipeline_complete', pipeline_result)

            return web.json_response(pipeline_result)

        except Exception as e:
            return web.json_response(
                {'error': f'Pipeline execution failed: {str(e)}'},
                status=500
            )

    async def _handle_realtime_processing(self, request):
        """Handle real-time log processing"""
        try:
            data = await request.json()
            logs = data.get('logs', [])

            # Process logs in real-time
            results = []

            for log in logs:
                # Quick processing through key engines
                result = await self._quick_process_log(log)
                results.append(result)

                # Send real-time update via WebSocket
                await self._broadcast_websocket_event('real_time_log', {
                    'log_id': result.get('log_id'),
                    'result': result,
                    'timestamp': datetime.utcnow().isoformat()
                })

            return web.json_response({
                'success': True,
                'processed': len(results),
                'results': results
            })

        except Exception as e:
            return web.json_response(
                {'error': f'Real-time processing failed: {str(e)}'},
                status=500
            )

    async def _handle_overall_stats(self, request):
        """Handle overall statistics request"""
        return web.json_response({
            'api_gateway': self.request_stats,
            'websocket_connections': len(self.websocket_connections),
            'active_requests': len(self.active_requests)
        })

    async def _handle_engine_stats(self, request):
        """Handle engine statistics request"""
        engine_stats = {}

        for engine_name in set(self.engine_routes.values()):
            try:
                message = {
                    'id': str(uuid.uuid4()),
                    'command': 'get_stats',
                    'timestamp': datetime.utcnow().isoformat()
                }

                result = await self._send_to_engine(engine_name, message)
                engine_stats[engine_name] = result

            except Exception as e:
                engine_stats[engine_name] = {'error': str(e)}

        return web.json_response(engine_stats)

    async def _handle_websocket_message(self, connection_id: str, message: str):
        """Handle incoming WebSocket message"""
        try:
            data = json.loads(message)
            message_type = data.get('type')

            if message_type == 'subscribe':
                # Subscribe to specific events
                events = data.get('events', [])
                # Store subscription preferences for this connection
                # (Implementation would track subscriptions per connection)

            elif message_type == 'ping':
                # Respond to ping
                websocket = self.websocket_connections.get(connection_id)
                if websocket:
                    await websocket.send(json.dumps({
                        'type': 'pong',
                        'timestamp': datetime.utcnow().isoformat()
                    }))

        except Exception as e:
            self.logger.log_error(e, {
                'connection_id': connection_id,
                'message': message
            })

    async def _broadcast_websocket_event(self, event_type: str, data: Dict[str, Any]):
        """Broadcast event to all WebSocket connections"""
        if event_type not in self.websocket_events:
            return

        message = {
            'type': event_type,
            'data': data,
            'timestamp': datetime.utcnow().isoformat()
        }

        message_json = json.dumps(message)

        # Send to all connected clients
        disconnected = []
        for connection_id, websocket in self.websocket_connections.items():
            try:
                await websocket.send(message_json)
            except Exception:
                disconnected.append(connection_id)

        # Clean up disconnected clients
        for connection_id in disconnected:
            self.websocket_connections.pop(connection_id, None)

    async def _send_to_engine(self, engine_name: str, message: Dict[str, Any]) -> Dict[str, Any]:
        """Send message to engine via message queue"""
        try:
            await self.message_queue.publish(engine_name, message)

            # In a real implementation, we'd wait for response
            # For now, return success
            return {
                'success': True,
                'message': f'Message sent to {engine_name}',
                'message_id': message['id']
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    async def _quick_process_log(self, log: Dict[str, Any]) -> Dict[str, Any]:
        """Quick processing for real-time logs"""
        # Simplified processing for real-time response
        return {
            'log_id': str(uuid.uuid4()),
            'processed': True,
            'timestamp': datetime.utcnow().isoformat(),
            'summary': 'Log processed successfully'
        }

    def _extract_command_from_path(self, path: str) -> str:
        """Extract command from API path"""
        # Convert REST path to command
        parts = path.split('/')
        if len(parts) >= 4:
            return parts[-1]  # Last part as command
        return 'unknown'

    async def _start_stats_reporter(self):
        """Report statistics periodically"""
        while True:
            await asyncio.sleep(60)  # Report every minute

            # Calculate average response time
            if self.request_stats['successful_requests'] > 0:
                # This would be calculated from actual response times
                self.request_stats['avg_response_time'] = 0.1  # Placeholder

            self.logger.log('api_gateway_stats', self.request_stats)

            # Broadcast stats via WebSocket
            await self._broadcast_websocket_event('system_status', {
                'api_gateway_stats': self.request_stats,
                'active_connections': len(self.websocket_connections)
            })

    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Process messages from other engines"""
        command = message.get('command')

        if command == 'broadcast_event':
            # Broadcast event to WebSocket clients
            event_type = message.get('event_type')
            event_data = message.get('event_data', {})
            await self._broadcast_websocket_event(event_type, event_data)

            return {'success': True, 'broadcasted': True}

        return {'success': False, 'error': f'Unknown command: {command}'}

    def get_capabilities(self) -> Dict[str, Any]:
        """Return API Gateway capabilities"""
        return {
            'engine': 'api_gateway',
            'version': self.version,
            'capabilities': [
                'rest_api',
                'websocket_api',
                'authentication',
                'rate_limiting',
                'request_routing',
                'pipeline_orchestration',
                'real_time_processing',
                'event_broadcasting'
            ],
            'endpoints': list(self.engine_routes.keys()),
            'websocket_events': list(self.websocket_events),
            'configuration': self.config,
            'statistics': self.request_stats
        }

    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate API Gateway configuration"""
        required_fields = ['host', 'port', 'jwt_secret']
        return all(field in config for field in required_fields)


async def main():
    """Main entry point for API Gateway"""
    gateway = APIGateway()

    # Log capabilities
    capabilities = gateway.get_capabilities()
    gateway.logger.log('engine_capabilities', capabilities)

    print(f"Starting API Gateway...")
    print(f"REST API: http://{gateway.config['host']}:{gateway.config['port']}")
    print(f"WebSocket: ws://{gateway.config['host']}:{gateway.config['websocket_port']}")

    # Start the gateway
    await gateway.start()


if __name__ == '__main__':
    asyncio.run(main())