@echo off
echo ===============================================
echo        SIEMLess v2.0 - Starting Platform       
echo ===============================================
echo.
echo IMPORTANT: Using consolidated Grafana setup
echo Location: ./engines/grafana/docker-compose.grafana.yml
echo.

REM Check Docker
docker info >nul 2>&1
if errorlevel 1 (
    echo Error: Docker is not running!
    echo Please start Docker Desktop and try again.
    pause
    exit /b 1
)

cd engines

REM Parse arguments
set MODE=core
if "%1"=="--with-grafana" set MODE=grafana
if "%1"=="--full" set MODE=full

echo Stopping existing containers...
docker-compose down

REM Start based on mode
if "%MODE%"=="grafana" (
    echo Starting with Grafana monitoring...
    docker-compose -f docker-compose.yml -f grafana/docker-compose.grafana.yml up -d --build
) else if "%MODE%"=="full" (
    echo Starting ALL services...
    docker-compose -f docker-compose.yml -f grafana/docker-compose.grafana.yml -f docker-compose.keycloak.yml up -d --build
) else (
    echo Starting core services only...
    docker-compose up -d --build
)

timeout /t 10 /nobreak >nul

docker-compose ps

echo.
echo ===============================================
echo        Access Points          
echo ===============================================
echo Frontend: http://localhost:3000
echo APIs: http://localhost:8001-8005/docs

if "%MODE%"=="grafana" (
    echo Grafana: http://localhost:3001 (admin/admin123)
)
if "%MODE%"=="full" (
    echo Grafana: http://localhost:3001 (admin/admin123)
    echo Keycloak: http://localhost:8080 (admin/admin)
)

echo ===============================================
pause
