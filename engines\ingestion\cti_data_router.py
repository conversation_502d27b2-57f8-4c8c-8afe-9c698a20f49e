"""
CTI Data Router
Routes CTI data to different channels based on purpose
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class CTIDataRouter:
    """
    Routes CTI data to segregated Redis channels based on purpose:
    1. Enrichment IOCs - for real-time firehose enrichment
    2. Rule Patterns - for detection rule generation
    3. Investigation Context - for investigation enrichment
    4. MITRE Mappings - for framework updates
    """

    def __init__(self, redis_client):
        self.redis = redis_client

        # Channel definitions
        self.channels = {
            'enrichment': 'cti.enrichment.iocs',
            'rules': 'cti.rules.patterns',
            'investigation': 'cti.investigation.context',
            'mitre': 'cti.mitre.mappings'
        }

    async def route_cti_data(self, cti_data: Dict[str, Any]):
        """
        Route CTI data to appropriate channels based on content

        Single CTI fetch serves multiple purposes:
        - IOCs → enrichment + rules
        - TTPs → rules + investigation + MITRE
        - Campaigns → investigation
        - Framework updates → MITRE
        """
        source = cti_data.get('source')
        logger.info(f"Routing CTI data from {source} to segregated channels")

        # Extract different data types from CTI
        enrichment_data = self._extract_enrichment_iocs(cti_data)
        rule_patterns = self._extract_rule_patterns(cti_data)
        investigation_context = self._extract_investigation_context(cti_data)
        mitre_mappings = self._extract_mitre_mappings(cti_data)

        # Publish to each channel
        published_count = 0

        if enrichment_data:
            await self._publish_enrichment_iocs(enrichment_data)
            published_count += len(enrichment_data)
            logger.info(f"Published {len(enrichment_data)} IOCs to enrichment channel")

        if rule_patterns:
            await self._publish_rule_patterns(rule_patterns)
            published_count += len(rule_patterns)
            logger.info(f"Published {len(rule_patterns)} patterns to rules channel")

        if investigation_context:
            await self._publish_investigation_context(investigation_context)
            published_count += 1
            logger.info(f"Published investigation context to context channel")

        if mitre_mappings:
            await self._publish_mitre_mappings(mitre_mappings)
            published_count += len(mitre_mappings)
            logger.info(f"Published {len(mitre_mappings)} MITRE mappings")

        logger.info(f"Total routing complete: {published_count} items to {len(self.channels)} channels")

    def _extract_enrichment_iocs(self, cti_data: Dict) -> List[Dict]:
        """
        Extract IOCs for real-time enrichment
        Format optimized for fast Redis lookups
        """
        iocs = []
        indicators = cti_data.get('indicators', [])

        for indicator in indicators:
            ioc = {
                'source': cti_data.get('source'),
                'ioc_type': indicator.get('type'),
                'value': indicator.get('value'),
                'threat_score': self._calculate_threat_score(indicator),
                'tags': indicator.get('tags', []),
                'campaign': cti_data.get('campaign'),
                'threat_actor': cti_data.get('threat_actor'),
                'first_seen': indicator.get('first_seen', datetime.utcnow().isoformat()),
                'ttl': 86400  # 24 hours for enrichment cache
            }
            iocs.append(ioc)

        return iocs

    def _extract_rule_patterns(self, cti_data: Dict) -> List[Dict]:
        """
        Extract patterns suitable for rule generation
        Focus on actionable indicators and TTPs
        """
        patterns = []
        indicators = cti_data.get('indicators', [])

        # Group indicators by type for pattern creation
        grouped = self._group_indicators_by_type(indicators)

        for indicator_type, indicator_list in grouped.items():
            if len(indicator_list) >= 1:  # Create patterns even for single IOCs
                pattern = {
                    'source': cti_data.get('source'),
                    'pattern_type': self._map_to_pattern_type(indicator_type),
                    'indicators': [ind['value'] for ind in indicator_list],
                    'ttp': cti_data.get('mitre_technique'),
                    'rule_template': self._select_rule_template(indicator_type),
                    'severity': self._calculate_severity(cti_data),
                    'campaign': cti_data.get('campaign'),
                    'confidence': cti_data.get('confidence', 'medium')
                }
                patterns.append(pattern)

        return patterns

    def _extract_investigation_context(self, cti_data: Dict) -> Optional[Dict]:
        """
        Extract context for investigation enrichment
        Focus on threat actor, campaign, and TTPs
        """
        # Only create context if we have campaign or threat actor info
        if not (cti_data.get('campaign') or cti_data.get('threat_actor')):
            return None

        context = {
            'source': cti_data.get('source'),
            'threat_actor': cti_data.get('threat_actor'),
            'campaigns': [cti_data.get('campaign')] if cti_data.get('campaign') else [],
            'ttps': cti_data.get('mitre_techniques', []),
            'iocs': self._group_iocs_for_investigation(cti_data.get('indicators', [])),
            'timeline': cti_data.get('timeline', f"{datetime.utcnow().year}-Q{(datetime.utcnow().month-1)//3 + 1}"),
            'description': cti_data.get('description', ''),
            'references': cti_data.get('references', [])
        }

        return context

    def _extract_mitre_mappings(self, cti_data: Dict) -> List[Dict]:
        """
        Extract MITRE ATT&CK mappings
        Focus on technique updates and data source mappings
        """
        mappings = []

        # Check if this is MITRE framework data
        if cti_data.get('source') == 'mitre' or cti_data.get('type') == 'mitre_framework':
            techniques = cti_data.get('techniques', [])

            for technique in techniques:
                mapping = {
                    'source': 'mitre',
                    'technique_id': technique.get('id'),
                    'technique_name': technique.get('name'),
                    'tactic': technique.get('tactic'),
                    'data_sources': technique.get('data_sources', []),
                    'detection_methods': technique.get('detection', []),
                    'platforms': technique.get('platforms', []),
                    'updated': datetime.utcnow().isoformat()
                }
                mappings.append(mapping)

        # Also extract MITRE technique tags from IOCs
        elif cti_data.get('mitre_techniques'):
            for technique_id in cti_data.get('mitre_techniques', []):
                mapping = {
                    'source': cti_data.get('source'),
                    'technique_id': technique_id,
                    'associated_iocs': [ind.get('value') for ind in cti_data.get('indicators', [])[:5]],  # Limit to 5
                    'campaign': cti_data.get('campaign'),
                    'updated': datetime.utcnow().isoformat()
                }
                mappings.append(mapping)

        return mappings

    async def _publish_enrichment_iocs(self, iocs: List[Dict]):
        """Publish IOCs to enrichment channel"""
        for ioc in iocs:
            self.redis.publish(
                self.channels['enrichment'],
                json.dumps(ioc)
            )

    async def _publish_rule_patterns(self, patterns: List[Dict]):
        """Publish patterns to rules channel"""
        for pattern in patterns:
            self.redis.publish(
                self.channels['rules'],
                json.dumps(pattern)
            )

    async def _publish_investigation_context(self, context: Dict):
        """Publish context to investigation channel"""
        self.redis.publish(
            self.channels['investigation'],
            json.dumps(context)
        )

    async def _publish_mitre_mappings(self, mappings: List[Dict]):
        """Publish MITRE mappings to MITRE channel"""
        for mapping in mappings:
            self.redis.publish(
                self.channels['mitre'],
                json.dumps(mapping)
            )

    # Helper methods

    def _calculate_threat_score(self, indicator: Dict) -> int:
        """Calculate threat score (0-100) based on indicator attributes"""
        score = 50  # Base score

        # Confidence boost
        confidence = indicator.get('confidence', 'medium')
        if confidence == 'high':
            score += 30
        elif confidence == 'medium':
            score += 15

        # Tag-based adjustments
        tags = indicator.get('tags', [])
        if 'malware' in tags:
            score += 10
        if 'c2' in tags or 'command-and-control' in tags:
            score += 15
        if 'apt' in tags:
            score += 20

        # Severity
        severity = indicator.get('severity', 'medium')
        if severity == 'critical':
            score += 20
        elif severity == 'high':
            score += 10

        return min(score, 100)

    def _group_indicators_by_type(self, indicators: List[Dict]) -> Dict[str, List]:
        """Group indicators by type for pattern matching"""
        grouped = {}
        for ind in indicators:
            ind_type = ind.get('type', 'unknown')
            if ind_type not in grouped:
                grouped[ind_type] = []
            grouped[ind_type].append(ind)
        return grouped

    def _map_to_pattern_type(self, indicator_type: str) -> str:
        """Map indicator type to pattern type"""
        mapping = {
            'ip': 'network',
            'domain': 'network',
            'url': 'network',
            'email': 'network',
            'hash': 'file',
            'md5': 'file',
            'sha1': 'file',
            'sha256': 'file',
            'registry': 'registry',
            'mutex': 'process'
        }
        return mapping.get(indicator_type.lower(), 'generic')

    def _select_rule_template(self, indicator_type: str) -> str:
        """Select appropriate rule template based on indicator type"""
        templates = {
            'ip': 'network_connection',
            'domain': 'dns_query',
            'url': 'web_request',
            'hash': 'file_hash',
            'registry': 'registry_modification',
            'mutex': 'mutex_creation'
        }
        return templates.get(indicator_type.lower(), 'generic_ioc')

    def _calculate_severity(self, cti_data: Dict) -> str:
        """Calculate severity for rule generation"""
        # Base on threat actor and campaign
        if cti_data.get('threat_actor'):
            return 'high'
        if cti_data.get('campaign'):
            return 'medium'

        # Base on indicator confidence
        confidence = cti_data.get('confidence', 'medium')
        if confidence == 'high':
            return 'high'
        elif confidence == 'medium':
            return 'medium'
        else:
            return 'low'

    def _group_iocs_for_investigation(self, indicators: List[Dict]) -> Dict[str, List]:
        """Group IOCs by type for investigation display"""
        iocs = {
            'ips': [],
            'domains': [],
            'urls': [],
            'hashes': [],
            'emails': []
        }

        for ind in indicators:
            ind_type = ind.get('type', '').lower()
            value = ind.get('value')

            if ind_type == 'ip':
                iocs['ips'].append(value)
            elif ind_type == 'domain':
                iocs['domains'].append(value)
            elif ind_type == 'url':
                iocs['urls'].append(value)
            elif ind_type in ['hash', 'md5', 'sha1', 'sha256']:
                iocs['hashes'].append(value)
            elif ind_type == 'email':
                iocs['emails'].append(value)

        # Remove empty categories
        return {k: v for k, v in iocs.items() if v}
