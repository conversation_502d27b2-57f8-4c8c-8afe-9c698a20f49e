#!/usr/bin/env python3
"""
Test Complete CrowdStrike CTI Plugin
Tests all endpoints: Indicators, Threat Actors, Malware Families, Vulnerabilities
"""
import asyncio
import os
import sys

# Add engines to path
sys.path.insert(0, 'engines/ingestion')

from crowdstrike_plugin import CrowdStrikePlugin
import logging

logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_crowdstrike_full():
    """Test all CrowdStrike CTI capabilities"""

    # Initialize plugin
    config = {
        'client_id': os.getenv('CROWDSTRIKE_CLIENT_ID'),
        'client_secret': os.getenv('CROWDSTRIKE_CLIENT_SECRET'),
        'base_url': os.getenv('CROWDSTRIKE_BASE_URL', 'https://api.crowdstrike.com'),
        'priority': 80
    }

    if not config['client_id'] or not config['client_secret']:
        print("ERROR: CrowdStrike credentials not found")
        print("Set CROWDSTRIKE_CLIENT_ID and CROWDSTRIKE_CLIENT_SECRET")
        return

    plugin = CrowdStrikePlugin(config, logger)

    print("=" * 70)
    print("CrowdStrike CTI Plugin - Full Test")
    print("=" * 70)

    # Test 1: Validate Credentials
    print("\n[1] Testing credential validation...")
    is_valid = await plugin.validate_credentials()
    print(f"   Credentials: {'VALID' if is_valid else 'INVALID'}")

    if not is_valid:
        print("   Stopping tests - credentials invalid")
        return

    # Test 2: Fetch Threat Intelligence Indicators
    print("\n[2] Fetching threat indicators...")
    indicators = await plugin.fetch_indicators(limit=10)
    print(f"   Indicators fetched: {len(indicators)}")

    if indicators:
        sample = indicators[0]
        print(f"   Sample indicator:")
        print(f"     Type: {sample.indicator_type}")
        print(f"     Value: {sample.indicator_value[:50]}...")
        print(f"     Threat: {sample.threat_type}")
        print(f"     Confidence: {sample.confidence}")
        print(f"     Tags: {', '.join(sample.tags[:3])}...")

    # Test 3: Fetch Threat Actors
    print("\n[3] Fetching threat actors...")
    actors = await plugin.get_threat_actors(limit=5)
    print(f"   Threat actors fetched: {len(actors)}")

    if actors:
        sample_actor = actors[0]
        print(f"   Sample actor:")
        print(f"     Name: {sample_actor['name']}")
        print(f"     Description: {sample_actor['description'][:80]}...")
        print(f"     Aliases: {', '.join(map(str, sample_actor.get('aliases', [])[:3]))}")

        countries = sample_actor.get('target_countries', [])
        if countries and isinstance(countries[0], dict):
            countries = [c.get('value', str(c)) for c in countries]
        print(f"     Target Countries: {', '.join(map(str, countries[:5]))}")

        industries = sample_actor.get('target_industries', [])
        if industries and isinstance(industries[0], dict):
            industries = [i.get('value', str(i)) for i in industries]
        print(f"     Target Industries: {', '.join(map(str, industries[:5]))}")

    # Test 4: Fetch Malware Families
    print("\n[4] Fetching malware families...")
    malware = await plugin.get_malware_families(limit=10)
    print(f"   Malware families fetched: {len(malware)}")

    if malware:
        # Show top 5 by indicator count
        top_malware = sorted(malware, key=lambda x: x['indicator_count'], reverse=True)[:5]
        print(f"   Top malware families by indicators:")
        for i, m in enumerate(top_malware, 1):
            print(f"     {i}. {m['name']}: {m['indicator_count']} indicators")
            if m['associated_actors']:
                print(f"        Actors: {', '.join(list(m['associated_actors'])[:3])}")

    # Test 5: Fetch Vulnerabilities (Spotlight)
    print("\n[5] Fetching vulnerabilities (Spotlight - may require additional scope)...")
    try:
        vulns = await plugin.get_vulnerabilities(limit=5)
        print(f"   Vulnerabilities fetched: {len(vulns)}")

        if vulns:
            sample_vuln = vulns[0]
            print(f"   Sample vulnerability:")
            print(f"     CVE: {sample_vuln['cve_id']}")
            print(f"     Severity: {sample_vuln['severity']}")
            print(f"     CVSS Score: {sample_vuln['cvss_score']}")
            print(f"     Description: {sample_vuln['cve_description'][:80]}...")
    except Exception as e:
        print(f"   Vulnerabilities: Not accessible ({str(e)})")

    # Summary
    print("\n" + "=" * 70)
    print("Test Summary")
    print("=" * 70)
    print(f"Indicators:       {len(indicators)}")
    print(f"Threat Actors:    {len(actors)}")
    print(f"Malware Families: {len(malware)}")
    print(f"Vulnerabilities:  {len(vulns) if 'vulns' in locals() else 'N/A'}")
    print("\nCrowdStrike CTI Plugin - All tests complete!")


if __name__ == "__main__":
    asyncio.run(test_crowdstrike_full())
