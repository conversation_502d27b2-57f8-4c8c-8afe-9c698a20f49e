/**
 * Rule Management Dashboard - Central Command Center Styles
 */

.rule-management-dashboard {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f9fafb;
  overflow: hidden;
}

/* ===== Dashboard Header ===== */
.dashboard-header {
  background: #ffffff;
  border-bottom: 2px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
}

.header-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.header-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0 0 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #ffffff;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.refresh-btn {
  padding: 8px;
  width: 36px;
}

.filter-btn {
  padding: 8px;
  width: 36px;
}

.filter-btn.active {
  background: #3b82f6;
  color: #ffffff;
  border-color: #3b82f6;
}

.settings-btn {
  padding: 8px;
  width: 36px;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ===== Quick Stats Bar ===== */
.quick-stats-bar {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16px;
  padding: 16px 24px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s;
}

.stat-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  flex-shrink: 0;
}

.stat-icon.total {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-icon.quality {
  background: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%);
}

.stat-icon.tp {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-icon.fp {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  line-height: 1.2;
}

.stat-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  margin-top: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ===== Global Filters ===== */
.global-filters {
  padding: 16px 24px;
  background: #ffffff;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-bar {
  flex: 1;
  max-width: 400px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  transition: all 0.2s;
}

.search-bar:focus-within {
  background: #ffffff;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-bar svg {
  color: #9ca3af;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  font-size: 14px;
  color: #111827;
}

.search-input::placeholder {
  color: #9ca3af;
}

.filter-quick-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-chip {
  padding: 6px 12px;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-chip:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.filter-chip.active {
  background: #3b82f6;
  color: #ffffff;
  border-color: #3b82f6;
}

/* ===== Tab Navigation ===== */
.tab-navigation {
  display: flex;
  background: #ffffff;
  border-bottom: 2px solid #e5e7eb;
  padding: 0 24px;
  gap: 4px;
}

.tab {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px 20px;
  background: transparent;
  color: #6b7280;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.tab:hover {
  color: #111827;
  background: #f9fafb;
}

.tab.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: #eff6ff;
}

.tab-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-label {
  font-weight: 600;
}

.tab-badge {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 700;
  color: #ffffff;
  line-height: 1.2;
}

.tab-badge.pending {
  background: #f59e0b;
}

.tab-badge.library {
  background: #3b82f6;
}

.tab-badge.alert {
  background: #ef4444;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* ===== Tab Content Area ===== */
.tab-content-area {
  flex: 1;
  overflow: hidden;
  background: #f9fafb;
}

/* ===== Workflow Hint Panel ===== */
.workflow-hint {
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  padding: 12px 24px;
}

.workflow-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.workflow-step {
  padding: 6px 12px;
  background: #ffffff;
  color: #6b7280;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.workflow-step.active {
  background: #3b82f6;
  color: #ffffff;
  border-color: #3b82f6;
  font-weight: 600;
}

.workflow-arrow {
  color: #9ca3af;
  font-size: 14px;
  font-weight: 700;
}

/* ===== Responsive Design ===== */
@media (max-width: 1400px) {
  .quick-stats-bar {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .quick-stats-bar {
    grid-template-columns: repeat(2, 1fr);
  }

  .global-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .search-bar {
    max-width: none;
  }

  .tab-navigation {
    overflow-x: auto;
    padding: 0 16px;
  }

  .workflow-hint {
    display: none;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 16px;
  }

  .header-title {
    font-size: 20px;
  }

  .header-subtitle {
    font-size: 13px;
  }

  .quick-stats-bar {
    grid-template-columns: 1fr;
    padding: 12px 16px;
    gap: 8px;
  }

  .stat-card {
    padding: 10px 12px;
  }

  .stat-icon {
    width: 36px;
    height: 36px;
  }

  .stat-value {
    font-size: 18px;
  }

  .action-btn span {
    display: none;
  }

  .tab {
    padding: 12px 16px;
    font-size: 14px;
  }

  .tab-label {
    font-size: 13px;
  }

  .filter-quick-actions {
    overflow-x: auto;
    flex-wrap: nowrap;
  }
}

/* ===== Dark Mode Support (Optional) ===== */
@media (prefers-color-scheme: dark) {
  .rule-management-dashboard {
    background: #111827;
  }

  .dashboard-header {
    background: #1f2937;
    border-bottom-color: #374151;
  }

  .header-title {
    color: #f9fafb;
  }

  .header-subtitle {
    color: #9ca3af;
  }

  .action-btn {
    background: #374151;
    color: #f9fafb;
    border-color: #4b5563;
  }

  .action-btn:hover {
    background: #4b5563;
  }

  .quick-stats-bar {
    background: #1f2937;
    border-top-color: #374151;
  }

  .stat-card {
    background: #374151;
    border-color: #4b5563;
  }

  .stat-value {
    color: #f9fafb;
  }

  .stat-label {
    color: #9ca3af;
  }

  .global-filters {
    background: #1f2937;
    border-top-color: #374151;
  }

  .search-bar {
    background: #374151;
    border-color: #4b5563;
  }

  .search-input {
    color: #f9fafb;
  }

  .tab-navigation {
    background: #1f2937;
    border-bottom-color: #374151;
  }

  .tab {
    color: #9ca3af;
  }

  .tab:hover {
    color: #f9fafb;
    background: #374151;
  }

  .tab.active {
    color: #60a5fa;
    background: #1e3a8a;
  }

  .tab-content-area {
    background: #111827;
  }

  .workflow-hint {
    background: #1f2937;
    border-top-color: #374151;
  }

  .workflow-step {
    background: #374151;
    color: #9ca3af;
    border-color: #4b5563;
  }

  .workflow-step.active {
    background: #3b82f6;
    color: #ffffff;
  }
}

/* ===== Print Styles ===== */
@media print {
  .header-actions,
  .global-filters,
  .workflow-hint {
    display: none;
  }

  .dashboard-header {
    border-bottom: 2px solid #000;
  }

  .quick-stats-bar {
    page-break-inside: avoid;
  }

  .tab-navigation {
    display: none;
  }
}
