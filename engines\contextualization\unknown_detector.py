"""
SIEMLess v2.0 - Unknown Detection System
Detects threats outside the range of known patterns
"""

import numpy as np
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from scipy import stats
from collections import defaultdict
import math

class UnknownDetector:
    """Multi-layer unknown threat detection system"""

    def __init__(self, db_connection=None, logger=None):
        self.db_connection = db_connection
        self.logger = logger
        self.baselines = {}
        self.sensitivity_levels = {
            'high': {'z_score': 2.5, 'percentile': 95},
            'medium': {'z_score': 3.0, 'percentile': 97},
            'low': {'z_score': 3.5, 'percentile': 99}
        }

    def detect_unknowns(self, entity: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Main entry point for unknown detection"""
        detections = {
            'unknown_detected': False,
            'anomalies': [],
            'risk_score': 0,
            'confidence': 0,
            'recommended_actions': []
        }

        # Layer 1: Statistical Anomaly Detection
        statistical = self.detect_statistical_anomaly(entity, context)
        if statistical['anomaly_detected']:
            detections['anomalies'].append(statistical)
            detections['unknown_detected'] = True

        # Layer 2: Behavioral Anomaly Detection
        behavioral = self.detect_behavioral_anomaly(entity, context)
        if behavioral['anomaly_detected']:
            detections['anomalies'].append(behavioral)
            detections['unknown_detected'] = True

        # Layer 3: Temporal Anomaly Detection
        temporal = self.detect_temporal_anomaly(entity, context)
        if temporal['anomaly_detected']:
            detections['anomalies'].append(temporal)
            detections['unknown_detected'] = True

        # Layer 4: Entropy-based Detection
        entropy = self.detect_entropy_anomaly(entity, context)
        if entropy['anomaly_detected']:
            detections['anomalies'].append(entropy)
            detections['unknown_detected'] = True

        # Layer 5: Graph/Relationship Anomalies
        graph = self.detect_graph_anomaly(entity, context)
        if graph['anomaly_detected']:
            detections['anomalies'].append(graph)
            detections['unknown_detected'] = True

        # Calculate overall risk and confidence
        if detections['unknown_detected']:
            detections['risk_score'] = self.calculate_unknown_risk(detections['anomalies'])
            detections['confidence'] = self.calculate_confidence(detections['anomalies'])
            detections['recommended_actions'] = self.recommend_actions(detections)

        return detections

    def detect_statistical_anomaly(self, entity: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Detect statistical anomalies using z-score and percentiles"""
        entity_id = f"{entity.get('type')}:{entity.get('value')}"
        baseline = self.get_or_create_baseline(entity_id, context)

        if not baseline or baseline['sample_size'] < 10:
            return {'anomaly_detected': False, 'reason': 'Insufficient baseline data'}

        # Extract current metrics
        current_value = self.extract_metric_value(entity, context)

        # Calculate z-score
        z_score = abs((current_value - baseline['mean']) / (baseline['std_dev'] + 0.0001))

        # Check percentiles
        percentile_rank = stats.percentileofscore(baseline['historical_values'], current_value)

        # Determine if anomaly
        sensitivity = context.get('sensitivity', 'medium')
        thresholds = self.sensitivity_levels[sensitivity]

        if z_score > thresholds['z_score'] or percentile_rank > thresholds['percentile']:
            return {
                'anomaly_detected': True,
                'type': 'statistical',
                'z_score': z_score,
                'percentile': percentile_rank,
                'baseline_mean': baseline['mean'],
                'baseline_std': baseline['std_dev'],
                'current_value': current_value,
                'severity': self.calculate_severity(z_score),
                'description': f"Value {z_score:.2f} standard deviations from normal"
            }

        return {'anomaly_detected': False}

    def detect_behavioral_anomaly(self, entity: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Detect breaks in behavioral patterns"""
        entity_id = f"{entity.get('type')}:{entity.get('value')}"

        # Get historical behavior patterns
        patterns = self.get_behavior_patterns(entity_id)
        current_behavior = self.extract_behavior(entity, context)

        anomalies = []

        # Check sequence anomaly
        if not self.matches_known_pattern(current_behavior, patterns):
            anomalies.append({
                'type': 'sequence_break',
                'description': 'Action sequence never seen before',
                'severity': 'high'
            })

        # Check access pattern anomaly
        new_resources = self.find_new_resource_access(entity_id, current_behavior)
        if new_resources:
            anomalies.append({
                'type': 'new_resource_access',
                'resources': new_resources,
                'description': 'Accessing previously unseen resources',
                'severity': 'medium'
            })

        # Check privilege escalation pattern
        if self.detect_privilege_escalation(entity_id, current_behavior):
            anomalies.append({
                'type': 'privilege_escalation',
                'description': 'Unusual privilege elevation detected',
                'severity': 'critical'
            })

        if anomalies:
            return {
                'anomaly_detected': True,
                'type': 'behavioral',
                'anomalies': anomalies,
                'confidence': len(anomalies) / 3.0  # More anomalies = higher confidence
            }

        return {'anomaly_detected': False}

    def detect_temporal_anomaly(self, entity: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Detect time-based anomalies"""
        timestamp = context.get('timestamp', datetime.utcnow())
        entity_id = f"{entity.get('type')}:{entity.get('value')}"

        # Check time-of-day anomaly
        hour = timestamp.hour
        day_of_week = timestamp.weekday()

        normal_hours = self.get_normal_activity_hours(entity_id)
        if hour not in normal_hours['hours'] or day_of_week not in normal_hours['days']:
            return {
                'anomaly_detected': True,
                'type': 'temporal',
                'subtype': 'unusual_time',
                'hour': hour,
                'day': day_of_week,
                'normal_hours': normal_hours['hours'],
                'normal_days': normal_hours['days'],
                'description': 'Activity outside normal time window',
                'severity': 'medium'
            }

        # Check for beaconing/periodicity
        intervals = self.get_activity_intervals(entity_id)
        if self.detect_beaconing(intervals):
            return {
                'anomaly_detected': True,
                'type': 'temporal',
                'subtype': 'beaconing',
                'interval': np.mean(intervals),
                'description': 'Periodic behavior suggesting C2 communication',
                'severity': 'critical'
            }

        # Check for velocity anomaly (too fast/too slow)
        velocity = self.calculate_activity_velocity(entity_id, timestamp)
        if velocity['anomaly']:
            return {
                'anomaly_detected': True,
                'type': 'temporal',
                'subtype': 'velocity',
                'current_velocity': velocity['current'],
                'normal_velocity': velocity['normal'],
                'description': velocity['description'],
                'severity': velocity['severity']
            }

        return {'anomaly_detected': False}

    def detect_entropy_anomaly(self, entity: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Detect anomalies based on entropy (randomness)"""
        data = context.get('data', '')
        if not data:
            return {'anomaly_detected': False}

        # Calculate Shannon entropy
        entropy = self.calculate_shannon_entropy(str(data))

        # Check for encryption/obfuscation (high entropy)
        if entropy > 7.5:
            return {
                'anomaly_detected': True,
                'type': 'entropy',
                'subtype': 'high_entropy',
                'entropy_value': entropy,
                'description': 'Possible encrypted/obfuscated content',
                'possible_threats': ['encrypted_c2', 'ransomware', 'data_exfiltration'],
                'severity': 'high'
            }

        # Check for repetitive patterns (low entropy)
        if entropy < 2.0 and len(str(data)) > 100:
            return {
                'anomaly_detected': True,
                'type': 'entropy',
                'subtype': 'low_entropy',
                'entropy_value': entropy,
                'description': 'Repetitive pattern detected',
                'possible_threats': ['dos_attack', 'fuzzing', 'scanning'],
                'severity': 'medium'
            }

        # Check for entropy distribution anomaly
        if self.has_entropy_distribution_anomaly(data):
            return {
                'anomaly_detected': True,
                'type': 'entropy',
                'subtype': 'distribution_anomaly',
                'description': 'Unusual entropy distribution pattern',
                'possible_threats': ['steganography', 'covert_channel'],
                'severity': 'medium'
            }

        return {'anomaly_detected': False}

    def detect_graph_anomaly(self, entity: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Detect anomalies in entity relationships"""
        entity_id = f"{entity.get('type')}:{entity.get('value')}"
        relationships = context.get('relationships', [])

        anomalies = []

        # Check for unusual connection count
        conn_count = len(relationships)
        normal_conn_count = self.get_normal_connection_count(entity_id)
        if conn_count > normal_conn_count['p95']:
            anomalies.append({
                'type': 'excessive_connections',
                'count': conn_count,
                'normal_p95': normal_conn_count['p95'],
                'description': 'Abnormally high number of connections'
            })

        # Check for new relationship types
        new_rel_types = self.find_new_relationship_types(entity_id, relationships)
        if new_rel_types:
            anomalies.append({
                'type': 'new_relationship_types',
                'new_types': new_rel_types,
                'description': 'Previously unseen relationship patterns'
            })

        # Check for lateral movement patterns
        if self.detect_lateral_movement_pattern(relationships):
            anomalies.append({
                'type': 'lateral_movement',
                'description': 'Pattern suggests lateral movement',
                'severity': 'critical'
            })

        # Check for data staging patterns
        if self.detect_data_staging_pattern(entity_id, relationships):
            anomalies.append({
                'type': 'data_staging',
                'description': 'Pattern suggests data collection/staging',
                'severity': 'high'
            })

        if anomalies:
            return {
                'anomaly_detected': True,
                'type': 'graph',
                'anomalies': anomalies,
                'relationship_count': conn_count,
                'confidence': min(0.95, len(anomalies) * 0.3)
            }

        return {'anomaly_detected': False}

    # Helper methods

    def calculate_shannon_entropy(self, data: str) -> float:
        """Calculate Shannon entropy of data"""
        if not data:
            return 0.0

        # Calculate frequency of each byte
        freq = defaultdict(int)
        for byte in data:
            freq[byte] += 1

        # Calculate entropy
        entropy = 0.0
        data_len = len(data)
        for count in freq.values():
            if count > 0:
                probability = count / data_len
                entropy -= probability * math.log2(probability)

        return entropy

    def detect_beaconing(self, intervals: List[float]) -> bool:
        """Detect periodic beaconing behavior"""
        if len(intervals) < 5:
            return False

        # Check for regular intervals (with jitter tolerance)
        mean_interval = np.mean(intervals)
        std_interval = np.std(intervals)

        # Low standard deviation relative to mean indicates periodicity
        if std_interval / mean_interval < 0.2:  # 20% jitter tolerance
            return True

        # Check for pattern in intervals (e.g., 60, 61, 59, 60, 62)
        rounded_intervals = [round(i, -1) for i in intervals]  # Round to nearest 10
        if len(set(rounded_intervals)) == 1:
            return True

        return False

    def calculate_severity(self, z_score: float) -> str:
        """Calculate severity based on z-score"""
        if z_score > 4:
            return 'critical'
        elif z_score > 3:
            return 'high'
        elif z_score > 2:
            return 'medium'
        else:
            return 'low'

    def calculate_unknown_risk(self, anomalies: List[Dict]) -> int:
        """Calculate overall risk score for unknown threats"""
        if not anomalies:
            return 0

        severity_scores = {
            'critical': 25,
            'high': 20,
            'medium': 10,
            'low': 5
        }

        total_score = 0
        for anomaly in anomalies:
            severity = anomaly.get('severity', 'medium')
            total_score += severity_scores.get(severity, 10)

        # Add bonus for multiple anomaly types
        if len(anomalies) > 3:
            total_score += 20

        return min(100, total_score)

    def calculate_confidence(self, anomalies: List[Dict]) -> float:
        """Calculate confidence in unknown detection"""
        if not anomalies:
            return 0.0

        # Base confidence on number and types of anomalies
        base_confidence = min(0.5 + (len(anomalies) * 0.15), 0.95)

        # Adjust for anomaly types
        high_confidence_types = ['behavioral', 'graph', 'temporal']
        for anomaly in anomalies:
            if anomaly.get('type') in high_confidence_types:
                base_confidence = min(base_confidence + 0.1, 0.99)

        return base_confidence

    def recommend_actions(self, detections: Dict) -> List[str]:
        """Recommend actions based on unknown detections"""
        actions = []
        risk = detections['risk_score']

        if risk > 80:
            actions.extend([
                'Immediate isolation of affected entity',
                'Trigger incident response',
                'Capture forensic data',
                'Alert SOC team'
            ])
        elif risk > 60:
            actions.extend([
                'Enhanced monitoring of entity',
                'Review recent activity logs',
                'Check for similar patterns',
                'Prepare containment plan'
            ])
        elif risk > 40:
            actions.extend([
                'Flag for analyst review',
                'Collect additional context',
                'Monitor for escalation'
            ])
        else:
            actions.extend([
                'Log for correlation',
                'Update baseline if false positive'
            ])

        # Add specific actions for anomaly types
        for anomaly in detections.get('anomalies', []):
            if anomaly.get('type') == 'behavioral':
                actions.append('Review user/entity behavior analytics')
            elif anomaly.get('type') == 'temporal':
                actions.append('Check for automated/scripted activity')
            elif anomaly.get('type') == 'graph':
                actions.append('Analyze relationship patterns for lateral movement')

        return list(set(actions))  # Remove duplicates

    # Stub methods - would connect to database in production

    def get_or_create_baseline(self, entity_id: str, context: Dict) -> Dict:
        """Get or create baseline for entity"""
        # In production, this would query historical data from database
        return {
            'mean': 50,
            'std_dev': 10,
            'historical_values': list(range(30, 70)),
            'sample_size': 100,
            'last_updated': datetime.utcnow()
        }

    def extract_metric_value(self, entity: Dict, context: Dict) -> float:
        """Extract metric value from entity and context"""
        # This would extract the relevant metric based on entity type
        return context.get('metric_value', 50)

    def get_behavior_patterns(self, entity_id: str) -> List[Dict]:
        """Get historical behavior patterns"""
        # Would query from database
        return []

    def extract_behavior(self, entity: Dict, context: Dict) -> Dict:
        """Extract current behavior from context"""
        return context.get('behavior', {})

    def matches_known_pattern(self, behavior: Dict, patterns: List) -> bool:
        """Check if behavior matches known patterns"""
        # Pattern matching logic
        return False

    def find_new_resource_access(self, entity_id: str, behavior: Dict) -> List[str]:
        """Find newly accessed resources"""
        return []

    def detect_privilege_escalation(self, entity_id: str, behavior: Dict) -> bool:
        """Detect privilege escalation attempts"""
        return False

    def get_normal_activity_hours(self, entity_id: str) -> Dict:
        """Get normal activity hours for entity"""
        return {
            'hours': list(range(9, 18)),  # 9 AM to 6 PM
            'days': list(range(0, 5))     # Monday to Friday
        }

    def get_activity_intervals(self, entity_id: str) -> List[float]:
        """Get time intervals between activities"""
        return []

    def calculate_activity_velocity(self, entity_id: str, timestamp: datetime) -> Dict:
        """Calculate activity velocity"""
        return {'anomaly': False, 'current': 0, 'normal': 0}

    def has_entropy_distribution_anomaly(self, data: str) -> bool:
        """Check for entropy distribution anomalies"""
        return False

    def get_normal_connection_count(self, entity_id: str) -> Dict:
        """Get normal connection count statistics"""
        return {'mean': 10, 'p95': 25, 'p99': 40}

    def find_new_relationship_types(self, entity_id: str, relationships: List) -> List[str]:
        """Find new relationship types"""
        return []

    def detect_lateral_movement_pattern(self, relationships: List) -> bool:
        """Detect lateral movement patterns"""
        # Look for fan-out pattern or unusual peer connections
        return len(relationships) > 20

    def detect_data_staging_pattern(self, entity_id: str, relationships: List) -> bool:
        """Detect data staging/collection patterns"""
        return False