"""
Model Registry - Hot-reloadable AI model configuration
Handles model selection, aliasing, and task-based routing
"""

import yaml
import os
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
import threading


@dataclass
class ModelConfig:
    """Configuration for a single AI model"""
    name: str
    provider: str
    model_name: str
    tier: str
    cost_per_1k_tokens: float
    cost_per_request: float
    quality_score: int
    speed_score: int
    max_tokens: int
    capabilities: List[str] = field(default_factory=list)
    recommended_for: List[str] = field(default_factory=list)
    test_results: Dict[str, Any] = field(default_factory=dict)
    warnings: List[str] = field(default_factory=list)
    notes: str = ""


class ModelRegistry:
    """
    Manages AI model configurations with hot-reload support

    Features:
    - Load models from YAML config
    - Hot-reload on config changes
    - Intelligent model selection based on task/cost/quality
    - Model aliasing for easy switching
    - Validation before applying changes
    """

    def __init__(self, config_path: str, logger: logging.Logger = None):
        """
        Initialize Model Registry

        Args:
            config_path: Path to ai_models.yaml
            logger: Logger instance
        """
        self.config_path = config_path
        self.logger = logger or logging.getLogger(__name__)

        self.models: Dict[str, ModelConfig] = {}
        self.aliases: Dict[str, str] = {}
        self.task_requirements: Dict[str, Dict] = {}
        self.hot_reload_config: Dict[str, Any] = {}

        self._last_reload = None
        self._reload_lock = threading.Lock()

        # Initial load
        self.reload_config()

    def reload_config(self) -> bool:
        """
        Hot-reload model configurations from YAML

        Returns:
            True if successful, False otherwise
        """
        with self._reload_lock:
            try:
                self.logger.info(f"Reloading model config from {self.config_path}")

                # Check if file exists
                if not os.path.exists(self.config_path):
                    self.logger.error(f"Config file not found: {self.config_path}")
                    return False

                # Load YAML
                with open(self.config_path, 'r') as f:
                    config = yaml.safe_load(f)

                # Validate config structure
                if not self._validate_config(config):
                    self.logger.error("Config validation failed")
                    return False

                # Parse models
                new_models = {}
                for model_id, model_data in config.get('models', {}).items():
                    try:
                        new_models[model_id] = ModelConfig(
                            name=model_id,
                            provider=model_data['provider'],
                            model_name=model_data['model_name'],
                            tier=model_data['tier'],
                            cost_per_1k_tokens=model_data['cost_per_1k_tokens'],
                            cost_per_request=model_data['cost_per_request'],
                            quality_score=model_data['quality_score'],
                            speed_score=model_data['speed_score'],
                            max_tokens=model_data['max_tokens'],
                            capabilities=model_data.get('capabilities', []),
                            recommended_for=model_data.get('recommended_for', []),
                            test_results=model_data.get('test_results', {}),
                            warnings=model_data.get('warnings', []),
                            notes=model_data.get('notes', '')
                        )
                    except Exception as e:
                        self.logger.error(f"Failed to parse model {model_id}: {e}")
                        continue

                # Update registry
                self.models = new_models
                self.aliases = config.get('aliases', {})
                self.task_requirements = config.get('task_requirements', {})
                self.hot_reload_config = config.get('hot_reload', {})

                self._last_reload = datetime.now()

                self.logger.info(f"Successfully loaded {len(self.models)} models, "
                               f"{len(self.aliases)} aliases")
                return True

            except Exception as e:
                self.logger.error(f"Failed to reload config: {e}")
                return False

    def _validate_config(self, config: Dict) -> bool:
        """Validate config structure before applying"""
        required_keys = ['models']
        for key in required_keys:
            if key not in config:
                self.logger.error(f"Missing required key: {key}")
                return False

        # Validate each model has required fields
        for model_id, model_data in config.get('models', {}).items():
            required_model_fields = [
                'provider', 'model_name', 'tier', 'cost_per_1k_tokens',
                'cost_per_request', 'quality_score', 'speed_score', 'max_tokens'
            ]
            for field in required_model_fields:
                if field not in model_data:
                    self.logger.error(f"Model {model_id} missing field: {field}")
                    return False

        return True

    def get_model(self, model_id: str) -> Optional[ModelConfig]:
        """
        Get model configuration by ID or alias

        Args:
            model_id: Model ID or alias

        Returns:
            ModelConfig if found, None otherwise
        """
        # Check if it's an alias
        if model_id in self.aliases:
            model_id = self.aliases[model_id]

        return self.models.get(model_id)

    def get_model_for_task(
        self,
        task_type: str,
        max_cost: Optional[float] = None,
        min_quality: Optional[int] = None
    ) -> Optional[ModelConfig]:
        """
        Get best model for a specific task based on requirements

        Args:
            task_type: Type of task (e.g., 'sigma_enhancement')
            max_cost: Maximum cost per request (optional override)
            min_quality: Minimum quality score (optional override)

        Returns:
            Best matching ModelConfig or None
        """
        # Get task requirements
        task_req = self.task_requirements.get(task_type, {})

        # Use overrides or defaults from config
        max_cost = max_cost or task_req.get('max_cost_per_request', float('inf'))
        min_quality = min_quality or task_req.get('min_quality_score', 0)
        required_caps = task_req.get('required_capabilities', [])
        preferred_models = task_req.get('preferred_models', [])
        fallback_models = task_req.get('fallback_models', [])

        # Try preferred models first
        for model_id in preferred_models:
            model = self.get_model(model_id)
            if model and self._model_meets_requirements(
                model, max_cost, min_quality, required_caps
            ):
                self.logger.info(f"Selected preferred model for {task_type}: {model.name}")
                return model

        # Try fallback models
        for model_id in fallback_models:
            model = self.get_model(model_id)
            if model and self._model_meets_requirements(
                model, max_cost, min_quality, required_caps
            ):
                self.logger.info(f"Selected fallback model for {task_type}: {model.name}")
                return model

        # No preferred/fallback worked, find best match
        candidates = []
        for model in self.models.values():
            if self._model_meets_requirements(model, max_cost, min_quality, required_caps):
                candidates.append(model)

        if not candidates:
            self.logger.warning(f"No models meet requirements for {task_type}")
            return None

        # Sort by quality score (descending)
        candidates.sort(key=lambda m: m.quality_score, reverse=True)
        best = candidates[0]

        self.logger.info(f"Selected best available model for {task_type}: {best.name}")
        return best

    def _model_meets_requirements(
        self,
        model: ModelConfig,
        max_cost: float,
        min_quality: int,
        required_capabilities: List[str]
    ) -> bool:
        """Check if model meets specified requirements"""
        # Cost check
        if model.cost_per_request > max_cost:
            return False

        # Quality check
        if model.quality_score < min_quality:
            return False

        # Capabilities check
        for cap in required_capabilities:
            if cap not in model.capabilities:
                return False

        return True

    def list_available_models(
        self,
        tier: Optional[str] = None,
        provider: Optional[str] = None,
        min_quality: Optional[int] = None
    ) -> List[ModelConfig]:
        """
        List available models with optional filtering

        Args:
            tier: Filter by tier (free, production, premium, etc.)
            provider: Filter by provider (google, anthropic, openai)
            min_quality: Minimum quality score

        Returns:
            List of matching ModelConfigs
        """
        models = list(self.models.values())

        if tier:
            models = [m for m in models if m.tier == tier]

        if provider:
            models = [m for m in models if m.provider == provider]

        if min_quality is not None:
            models = [m for m in models if m.quality_score >= min_quality]

        return models

    def get_model_stats(self) -> Dict[str, Any]:
        """Get registry statistics"""
        return {
            'total_models': len(self.models),
            'total_aliases': len(self.aliases),
            'providers': len(set(m.provider for m in self.models.values())),
            'last_reload': self._last_reload.isoformat() if self._last_reload else None,
            'hot_reload_enabled': self.hot_reload_config.get('enabled', False)
        }

    def validate_model_id(self, model_id: str) -> bool:
        """Check if model ID or alias exists"""
        return model_id in self.models or model_id in self.aliases

    def get_alias_target(self, alias: str) -> Optional[str]:
        """Get the actual model ID for an alias"""
        return self.aliases.get(alias)

    def __repr__(self) -> str:
        return f"ModelRegistry({len(self.models)} models, {len(self.aliases)} aliases)"
