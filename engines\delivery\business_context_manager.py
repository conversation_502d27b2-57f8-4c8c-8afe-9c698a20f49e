"""
Business Context Manager (Revised)
Uses existing entities table for business context storage
Integrates with investigation workflow
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
from uuid import uuid4

from aiohttp import web
import asyncpg


logger = logging.getLogger(__name__)


class BusinessContextManager:
    """Manages business context using entities.business_context field"""

    def __init__(self, db_pool: asyncpg.Pool):
        self.db_pool = db_pool
        self.logger = logger

    def register_routes(self, app: web.Application):
        """Register HTTP routes"""
        # Business context on entities
        app.router.add_get('/api/entities/{entity_type}/{entity_value}/context', self.get_business_context)
        app.router.add_post('/api/entities/{entity_type}/{entity_value}/context', self.add_business_context)
        app.router.add_put('/api/entities/{entity_type}/{entity_value}/context', self.update_business_context)
        app.router.add_delete('/api/entities/{entity_type}/{entity_value}/context', self.remove_business_context)

        # Batch operations
        app.router.add_get('/api/entities/with-context', self.list_entities_with_context)

        # Context checking during investigation
        app.router.add_post('/api/entities/check-context', self.check_multiple_entities_context)

        # Investigation verdicts
        app.router.add_post('/api/investigations/{investigation_id}/verdict', self.record_verdict)
        app.router.add_get('/api/investigations/{investigation_id}/verdict', self.get_verdict)

        # Rule tuning suggestions
        app.router.add_get('/api/rule-tuning/suggestions', self.list_rule_tuning_suggestions)
        app.router.add_post('/api/rule-tuning/suggestions/{suggestion_id}/approve', self.approve_rule_tuning)
        app.router.add_post('/api/rule-tuning/suggestions/{suggestion_id}/apply', self.apply_rule_tuning)

        # Query Generator (Phase 2)
        app.router.add_post('/api/investigation/generate-queries', self.generate_queries)
        app.router.add_get('/api/investigation/sources', self.get_available_sources)
        app.router.add_get('/api/investigation/guidance/{entity_type}/{entity_value}', self.get_query_guidance)

    # =============================
    # BUSINESS CONTEXT OPERATIONS
    # =============================

    async def get_business_context(self, request: web.Request) -> web.Response:
        """
        Get business context for entity
        GET /api/entities/{entity_type}/{entity_value}/context

        Returns:
        {
            "has_context": true/false,
            "context": {
                "entity_id": "...",
                "business_context": {...},
                "behavioral_profile": {...},
                "criticality_score": 70,
                "tags": []
            }
        }
        """
        try:
            entity_type = request.match_info['entity_type']
            entity_value = request.match_info['entity_value']

            async with self.db_pool.acquire() as conn:
                entity = await conn.fetchrow("""
                    SELECT entity_id, entity_type, entity_value,
                           business_context, behavioral_profile,
                           criticality_score, tags,
                           enrichment_updated_by, enrichment_updated_at
                    FROM entities
                    WHERE entity_type = $1 AND entity_value = $2
                """, entity_type, entity_value)

            if not entity or not entity['business_context']:
                return web.json_response({
                    'has_context': False,
                    'message': f'No business context found for {entity_type}: {entity_value}'
                })

            return web.json_response({
                'has_context': True,
                'context': {
                    'entity_id': str(entity['entity_id']),
                    'entity_type': entity['entity_type'],
                    'entity_value': entity['entity_value'],
                    'business_context': entity['business_context'],
                    'behavioral_profile': entity['behavioral_profile'],
                    'criticality_score': entity['criticality_score'],
                    'tags': entity['tags'],
                    'updated_by': entity['enrichment_updated_by'],
                    'updated_at': entity['enrichment_updated_at'].isoformat() if entity['enrichment_updated_at'] else None
                }
            })

        except Exception as e:
            self.logger.error(f"Error getting business context: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def add_business_context(self, request: web.Request) -> web.Response:
        """
        Add business context to entity
        POST /api/entities/{entity_type}/{entity_value}/context

        Body:
        {
            "context_label": "Primary Backup Server",
            "context_description": "Runs weekly backups every Sunday 2AM-4AM",
            "business_unit": "IT Operations",
            "owner": "<EMAIL>",
            "criticality": "high",
            "security_zone": "internal",
            "scheduled_jobs": ["weekly_backup", "monthly_maintenance"],
            "normal_times": ["Sunday 02:00-04:00", "First Monday 01:00-02:00"],
            "expected_traffic": ["large_file_operations", "database_backups"],
            "tags": ["backup", "critical_infrastructure"]
        }
        """
        try:
            entity_type = request.match_info['entity_type']
            entity_value = request.match_info['entity_value']
            data = await request.json()
            user = request.headers.get('X-User-ID', 'anonymous')

            # Build business_context JSONB
            business_context = {
                'context_label': data.get('context_label'),
                'context_description': data.get('context_description'),
                'business_unit': data.get('business_unit'),
                'owner': data.get('owner'),
                'security_zone': data.get('security_zone'),
                'added_by': user,
                'added_at': datetime.now().isoformat(),
                'last_updated': datetime.now().isoformat()
            }

            # Build behavioral_profile JSONB
            behavioral_profile = {
                'scheduled_jobs': data.get('scheduled_jobs', []),
                'normal_times': data.get('normal_times', []),
                'expected_traffic': data.get('expected_traffic', []),
                'learned': False,  # User-provided, not ML-learned
                'confidence': 100  # 100% confidence for user-provided
            }

            # Convert criticality to score
            criticality_score = self._criticality_to_score(data.get('criticality', 'medium'))

            # Check if entity exists and update/create
            async with self.db_pool.acquire() as conn:
                existing = await conn.fetchrow("""
                    SELECT entity_id FROM entities
                    WHERE entity_type = $1 AND entity_value = $2
                """, entity_type, entity_value)

                if existing:
                    # Update existing entity
                    entity_id = existing['entity_id']
                    await conn.execute("""
                        UPDATE entities
                        SET business_context = $1,
                            behavioral_profile = $2,
                            criticality_score = $3,
                            tags = $4,
                            enrichment_updated_by = $5,
                            enrichment_updated_at = NOW(),
                            updated_at = NOW()
                        WHERE entity_id = $6
                    """,
                        json.dumps(business_context),
                        json.dumps(behavioral_profile),
                        criticality_score,
                        data.get('tags', []),
                        user,
                        entity_id
                    )
                    self.logger.info(f"Updated business context for entity {entity_id}")
                else:
                    # Create new entity with business context
                    entity_id = str(uuid4())
                    await conn.execute("""
                        INSERT INTO entities (
                            entity_id, entity_type, entity_value,
                            business_context, behavioral_profile,
                            criticality_score, tags,
                            enrichment_updated_by, enrichment_updated_at
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
                    """,
                        entity_id,
                        entity_type,
                        entity_value,
                        json.dumps(business_context),
                        json.dumps(behavioral_profile),
                        criticality_score,
                        data.get('tags', []),
                        user
                    )
                    self.logger.info(f"Created new entity {entity_id} with business context")

            return web.json_response({
                'success': True,
                'entity_id': entity_id,
                'message': 'Business context added successfully'
            }, status=201)

        except Exception as e:
            self.logger.error(f"Error adding business context: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def update_business_context(self, request: web.Request) -> web.Response:
        """Update existing business context"""
        try:
            entity_type = request.match_info['entity_type']
            entity_value = request.match_info['entity_value']
            data = await request.json()
            user = request.headers.get('X-User-ID', 'anonymous')

            # Get existing context and update
            async with self.db_pool.acquire() as conn:
                existing = await conn.fetchrow("""
                    SELECT business_context, behavioral_profile
                    FROM entities
                    WHERE entity_type = $1 AND entity_value = $2
                """, entity_type, entity_value)

                if not existing:
                    return web.json_response({
                        'error': 'Entity not found'
                    }, status=404)

                # Merge with existing context
                business_context = existing['business_context'] or {}
                behavioral_profile = existing['behavioral_profile'] or {}

                # Update fields
                if 'context_label' in data:
                    business_context['context_label'] = data['context_label']
                if 'context_description' in data:
                    business_context['context_description'] = data['context_description']
                if 'business_unit' in data:
                    business_context['business_unit'] = data['business_unit']
                if 'owner' in data:
                    business_context['owner'] = data['owner']

                business_context['last_updated'] = datetime.now().isoformat()
                business_context['updated_by'] = user

                if 'scheduled_jobs' in data:
                    behavioral_profile['scheduled_jobs'] = data['scheduled_jobs']
                if 'normal_times' in data:
                    behavioral_profile['normal_times'] = data['normal_times']
                if 'expected_traffic' in data:
                    behavioral_profile['expected_traffic'] = data['expected_traffic']

                # Build update query dynamically
                update_fields = []
                params = []
                param_idx = 1

                update_fields.append(f"business_context = ${param_idx}")
                params.append(json.dumps(business_context))
                param_idx += 1

                update_fields.append(f"behavioral_profile = ${param_idx}")
                params.append(json.dumps(behavioral_profile))
                param_idx += 1

                if 'criticality' in data:
                    update_fields.append(f"criticality_score = ${param_idx}")
                    params.append(self._criticality_to_score(data['criticality']))
                    param_idx += 1

                if 'tags' in data:
                    update_fields.append(f"tags = ${param_idx}")
                    params.append(data['tags'])
                    param_idx += 1

                update_fields.append(f"enrichment_updated_by = ${param_idx}")
                params.append(user)
                param_idx += 1

                update_fields.append("enrichment_updated_at = NOW()")
                update_fields.append("updated_at = NOW()")

                params.extend([entity_type, entity_value])

                query = f"""
                    UPDATE entities
                    SET {', '.join(update_fields)}
                    WHERE entity_type = ${param_idx} AND entity_value = ${param_idx + 1}
                """

                await conn.execute(query, *params)

            return web.json_response({
                'success': True,
                'message': 'Business context updated successfully'
            })

        except Exception as e:
            self.logger.error(f"Error updating business context: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def remove_business_context(self, request: web.Request) -> web.Response:
        """Remove business context from entity"""
        try:
            entity_type = request.match_info['entity_type']
            entity_value = request.match_info['entity_value']
            user = request.headers.get('X-User-ID', 'anonymous')

            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE entities
                    SET business_context = NULL,
                        behavioral_profile = NULL,
                        enrichment_updated_by = $1,
                        enrichment_updated_at = NOW()
                    WHERE entity_type = $2 AND entity_value = $3
                """, user, entity_type, entity_value)

            return web.json_response({
                'success': True,
                'message': 'Business context removed'
            })

        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def list_entities_with_context(self, request: web.Request) -> web.Response:
        """List all entities that have business context"""
        try:
            limit = int(request.query.get('limit', 50))
            offset = int(request.query.get('offset', 0))
            entity_type = request.query.get('entity_type')

            query = """
                SELECT entity_id, entity_type, entity_value,
                       business_context, criticality_score, tags
                FROM entities
                WHERE business_context IS NOT NULL
            """
            params = []
            param_idx = 1

            if entity_type:
                query += f" AND entity_type = ${param_idx}"
                params.append(entity_type)
                param_idx += 1

            query += f" ORDER BY criticality_score DESC, entity_value LIMIT ${param_idx} OFFSET ${param_idx + 1}"
            params.extend([limit, offset])

            async with self.db_pool.acquire() as conn:
                entities = await conn.fetch(query, *params)

            return web.json_response({
                'entities': [dict(e) for e in entities],
                'count': len(entities),
                'limit': limit,
                'offset': offset
            })

        except Exception as e:
            self.logger.error(f"Error listing entities with context: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def check_multiple_entities_context(self, request: web.Request) -> web.Response:
        """
        Check context for multiple entities at once
        POST /api/entities/check-context
        Body: {
            "entities": [
                {"type": "host", "value": "BACKUP-SERVER-01"},
                {"type": "user", "value": "svc_backup"},
                {"type": "ip", "value": "*************"}
            ]
        }
        """
        try:
            data = await request.json()
            entities = data.get('entities', [])

            if not entities:
                return web.json_response({'results': []})

            results = []

            async with self.db_pool.acquire() as conn:
                for entity in entities:
                    entity_type = entity.get('type')
                    entity_value = entity.get('value')

                    result = await conn.fetchrow("""
                        SELECT entity_id, business_context, behavioral_profile, criticality_score
                        FROM entities
                        WHERE entity_type = $1 AND entity_value = $2
                    """, entity_type, entity_value)

                    results.append({
                        'entity_type': entity_type,
                        'entity_value': entity_value,
                        'has_context': bool(result and result['business_context']),
                        'context': dict(result) if result else None
                    })

            return web.json_response({'results': results})

        except Exception as e:
            self.logger.error(f"Error checking entity contexts: {e}")
            return web.json_response({'error': str(e)}, status=500)

    # =============================
    # INVESTIGATION VERDICTS
    # =============================

    async def record_verdict(self, request: web.Request) -> web.Response:
        """
        Record investigation verdict
        POST /api/investigations/{investigation_id}/verdict
        """
        try:
            investigation_id = request.match_info['investigation_id']
            data = await request.json()
            user = request.headers.get('X-User-ID', 'anonymous')

            verdict_id = str(uuid4())

            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO investigation_verdicts (
                        verdict_id, investigation_id, alert_id, verdict,
                        verdict_reason, device_context, user_context, time_context,
                        confirmation_notes, business_justification,
                        entities_involved, our_prediction, our_confidence,
                        decided_by, time_to_verdict_minutes
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
                """,
                    verdict_id,
                    investigation_id,
                    data.get('alert_id'),
                    data['verdict'],
                    data.get('verdict_reason'),
                    data.get('device_context'),
                    data.get('user_context'),
                    data.get('time_context'),
                    data.get('confirmation_notes'),
                    data.get('business_justification'),
                    json.dumps(data.get('entities_involved', {})),
                    data.get('our_prediction'),
                    data.get('our_confidence'),
                    user,
                    data.get('time_to_verdict_minutes')
                )

            # If false positive with context, auto-generate rule tuning suggestion
            if data['verdict'] == 'false_positive' and data.get('verdict_reason'):
                await self._auto_generate_rule_tuning(
                    investigation_id,
                    data.get('alert_id'),
                    data,
                    user
                )

            return web.json_response({
                'success': True,
                'verdict_id': verdict_id
            }, status=201)

        except Exception as e:
            self.logger.error(f"Error recording verdict: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def _auto_generate_rule_tuning(self, investigation_id, alert_id, verdict_data, user):
        """Auto-generate rule tuning suggestion from FP verdict"""
        try:
            # Extract conditions from context
            conditions = {}

            if verdict_data.get('entities_involved'):
                conditions['entities'] = verdict_data['entities_involved']

            if verdict_data.get('time_context'):
                conditions['time_pattern'] = verdict_data['time_context']

            suggestion_id = str(uuid4())
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO rule_tuning_suggestions (
                        suggestion_id, investigation_id, alert_id,
                        suggestion_type, suppression_conditions, reason,
                        analyst_context, extracted_from_verdict, created_by
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, true, $8)
                """,
                    suggestion_id,
                    investigation_id,
                    alert_id,
                    'suppress',
                    json.dumps(conditions),
                    verdict_data['verdict_reason'],
                    f"{verdict_data.get('device_context', '')} | {verdict_data.get('confirmation_notes', '')}",
                    user
                )

            self.logger.info(f"Auto-generated rule tuning suggestion {suggestion_id}")

        except Exception as e:
            self.logger.error(f"Error auto-generating rule tuning: {e}")

    async def get_verdict(self, request: web.Request) -> web.Response:
        """Get verdict for investigation"""
        try:
            investigation_id = request.match_info['investigation_id']

            async with self.db_pool.acquire() as conn:
                verdict = await conn.fetchrow("""
                    SELECT * FROM investigation_verdicts
                    WHERE investigation_id = $1
                """, investigation_id)

            if not verdict:
                return web.json_response({
                    'has_verdict': False
                })

            return web.json_response({
                'has_verdict': True,
                'verdict': dict(verdict)
            })

        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    # =============================
    # RULE TUNING SUGGESTIONS
    # =============================

    async def list_rule_tuning_suggestions(self, request: web.Request) -> web.Response:
        """List rule tuning suggestions"""
        try:
            status = request.query.get('status', 'pending')
            limit = int(request.query.get('limit', 50))

            async with self.db_pool.acquire() as conn:
                suggestions = await conn.fetch("""
                    SELECT * FROM rule_tuning_suggestions
                    WHERE status = $1
                    ORDER BY created_at DESC
                    LIMIT $2
                """, status, limit)

            return web.json_response({
                'suggestions': [dict(s) for s in suggestions],
                'count': len(suggestions)
            })

        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def approve_rule_tuning(self, request: web.Request) -> web.Response:
        """Approve a rule tuning suggestion"""
        try:
            suggestion_id = request.match_info['suggestion_id']
            user = request.headers.get('X-User-ID', 'anonymous')

            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE rule_tuning_suggestions
                    SET status = 'approved', approved_by = $1, approved_at = NOW()
                    WHERE suggestion_id = $2
                """, user, suggestion_id)

            return web.json_response({'success': True})

        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def apply_rule_tuning(self, request: web.Request) -> web.Response:
        """Mark rule tuning as applied"""
        try:
            suggestion_id = request.match_info['suggestion_id']
            data = await request.json()
            user = request.headers.get('X-User-ID', 'anonymous')

            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE rule_tuning_suggestions
                    SET status = 'applied', applied = true,
                        applied_by = $1, applied_at = NOW(),
                        application_notes = $2
                    WHERE suggestion_id = $3
                """, user, data.get('notes'), suggestion_id)

            return web.json_response({'success': True})

        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    # =============================
    # UTILITY METHODS
    # =============================

    def _criticality_to_score(self, criticality: str) -> int:
        """Convert criticality string to 0-100 score"""
        mapping = {
            'critical': 90,
            'high': 70,
            'medium': 50,
            'low': 30
        }
        return mapping.get(criticality.lower(), 50)

    # =============================
    # QUERY GENERATOR ENDPOINTS (Phase 2)
    # =============================

    async def generate_queries(self, request: web.Request):
        """
        Generate investigation queries for an entity

        POST /api/investigation/generate-queries
        Body: {
            "entity_type": "host",
            "entity_value": "SERVER-01",
            "time_window": {
                "start": "2025-10-04T10:00:00",
                "end": "2025-10-04T11:00:00"
            }
        }
        """
        from query_generator import QueryGeneratorService

        try:
            data = await request.json()
            entity_type = data.get('entity_type')
            entity_value = data.get('entity_value')
            time_window = data.get('time_window')

            if not entity_type or not entity_value:
                return web.json_response(
                    {'error': 'entity_type and entity_value required'},
                    status=400
                )

            # Parse time window if provided
            if time_window:
                from datetime import datetime
                time_window = {
                    'start': datetime.fromisoformat(time_window['start']),
                    'end': datetime.fromisoformat(time_window['end'])
                }

            # Generate queries
            service = QueryGeneratorService(self.db_pool, self.logger)
            queries = await service.generate_queries_for_entity(
                entity_type, entity_value, time_window
            )

            return web.json_response({
                'entity_type': entity_type,
                'entity_value': entity_value,
                'total_queries': len(queries),
                'available_sources': sum(1 for q in queries if q['available']),
                'queries': queries
            })

        except Exception as e:
            self.logger.error(f"Query generation error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def get_available_sources(self, request: web.Request):
        """
        Get list of sources we have logs from

        GET /api/investigation/sources
        """
        from query_generator import QueryGeneratorService

        try:
            service = QueryGeneratorService(self.db_pool, self.logger)
            sources = await service.get_available_sources()

            return web.json_response({
                'total_sources': len(sources),
                'sources': sources
            })

        except Exception as e:
            self.logger.error(f"Get sources error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def get_query_guidance(self, request: web.Request):
        """
        Get investigation guidance for an entity type

        GET /api/investigation/guidance/{entity_type}/{entity_value}
        """
        from query_generator import QueryGeneratorService

        try:
            entity_type = request.match_info['entity_type']
            entity_value = request.match_info['entity_value']

            service = QueryGeneratorService(self.db_pool, self.logger)
            guidance = await service.get_query_guidance(entity_type, entity_value)

            return web.json_response(guidance)

        except Exception as e:
            self.logger.error(f"Get guidance error: {e}")
            return web.json_response({'error': str(e)}, status=500)
