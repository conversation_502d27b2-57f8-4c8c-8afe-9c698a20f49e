#!/usr/bin/env python3
"""
Test ONLY Gemma API calls to save money
"""

import json
import redis
import time

def test_gemma_only():
    print("=== Testing ONLY Gemma API Call ===")

    redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)

    # Simple test request that should work with <PERSON>
    test_event = {
        'request_id': f'gemma_test_{int(time.time())}',
        'pattern_data': {
            'event_type': 'test_security_analysis',
            'description': 'Test security event for <PERSON>'
        },
        'model_override': 'gemma',  # Force only Gemma to respond
        'task': 'consensus'
    }

    print(f"[SEND] Gemma test request: {test_event['request_id']}")
    redis_client.publish('intelligence.consensus', json.dumps(test_event))

    # Monitor for response
    pubsub = redis_client.pubsub()
    pubsub.subscribe('intelligence.consensus_result')  # Fixed channel name

    print("[MONITOR] Waiting for Gemma response...")
    start_time = time.time()

    for message in pubsub.listen():
        if time.time() - start_time > 30:
            print("[TIMEOUT] No response received in 30 seconds")
            break

        if message['type'] == 'message':
            response = json.loads(message['data'])
            print(f"\n[RESPONSE] Gemma Test Result:")
            print(json.dumps(response, indent=2))
            break

if __name__ == "__main__":
    test_gemma_only()