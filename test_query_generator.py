#!/usr/bin/env python3
"""
Test Query Generator Service
Verifies Phase 2 implementation
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8005"

def test_get_available_sources():
    """Test GET /api/investigation/sources"""
    print("\n" + "="*60)
    print("TEST 1: Get Available Sources")
    print("="*60)

    url = f"{BASE_URL}/api/investigation/sources"
    response = requests.get(url)

    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Total Sources: {data.get('total_sources', 0)}")
        print("\nSources:")
        for source in data.get('sources', []):
            print(f"  - {source['source_type']}: {source['log_count_7d']} logs in last 7 days")
    else:
        print(f"Error: {response.text}")

    return response.status_code == 200


def test_get_query_guidance():
    """Test GET /api/investigation/guidance/{type}/{value}"""
    print("\n" + "="*60)
    print("TEST 2: Get Query Guidance (host)")
    print("="*60)

    entity_type = "host"
    entity_value = "SERVER-01"
    url = f"{BASE_URL}/api/investigation/guidance/{entity_type}/{entity_value}"

    response = requests.get(url)

    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Entity: {data['entity_type']}={data['entity_value']}")
        print(f"Total Templates: {data['total_templates']}")
        print(f"Available Sources: {data['available_sources']}")
        print(f"Missing Sources: {data['missing_sources']}")
        print("\nSources:")
        for source in data.get('sources', [])[:5]:  # Show first 5
            available = "✓" if source['available'] else "✗"
            print(f"  [{available}] {source['source_name']}")
            print(f"      {source['what_we_have']}")
    else:
        print(f"Error: {response.text}")

    return response.status_code == 200


def test_generate_queries():
    """Test POST /api/investigation/generate-queries"""
    print("\n" + "="*60)
    print("TEST 3: Generate Queries for Entity")
    print("="*60)

    url = f"{BASE_URL}/api/investigation/generate-queries"

    # Test with default time window (±30 min from now)
    payload = {
        "entity_type": "host",
        "entity_value": "SERVER-01"
    }

    response = requests.post(url, json=payload)

    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Entity: {data['entity_type']}={data['entity_value']}")
        print(f"Total Queries: {data['total_queries']}")
        print(f"Available Sources: {data['available_sources']}")

        print("\nGenerated Queries:")
        for query in data.get('queries', []):
            available = "✓ AVAILABLE" if query['available'] else "✗ NO LOGS"
            print(f"\n  [{available}] {query['source_name']}")
            print(f"  Language: {query['query_language']}")
            print(f"  Query: {query['query'][:100]}...")
            if query.get('deep_link'):
                print(f"  Link: {query['deep_link'][:80]}...")
            print(f"  What we have: {query['what_we_have'][:60]}...")
            print(f"  Limitations: {query['limitations'][:60]}...")

    else:
        print(f"Error: {response.text}")

    return response.status_code == 200


def test_generate_queries_with_time_window():
    """Test query generation with custom time window"""
    print("\n" + "="*60)
    print("TEST 4: Generate Queries with Time Window")
    print("="*60)

    url = f"{BASE_URL}/api/investigation/generate-queries"

    # Test with custom time window
    now = datetime.now()
    payload = {
        "entity_type": "user",
        "entity_value": "jdoe",
        "time_window": {
            "start": (now - timedelta(hours=2)).isoformat(),
            "end": now.isoformat()
        }
    }

    response = requests.post(url, json=payload)

    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Entity: {data['entity_type']}={data['entity_value']}")
        print(f"Total Queries: {data['total_queries']}")
        print(f"Available Sources: {data['available_sources']}")

        # Show just Elastic and CrowdStrike queries
        for query in data.get('queries', []):
            if 'elastic' in query['source_type'].lower() or 'crowdstrike' in query['source_type'].lower():
                available = "✓" if query['available'] else "✗"
                print(f"\n  [{available}] {query['source_name']}")
                print(f"       Query: {query['query']}")
                print(f"       Time Range: {query['time_range']['start']} to {query['time_range']['end']}")

    else:
        print(f"Error: {response.text}")

    return response.status_code == 200


def test_all_entity_types():
    """Test query generation for all entity types"""
    print("\n" + "="*60)
    print("TEST 5: All Entity Types")
    print("="*60)

    url = f"{BASE_URL}/api/investigation/generate-queries"

    test_cases = [
        ("host", "SERVER-01"),
        ("user", "jdoe"),
        ("ip", "*************"),
        ("process", "powershell.exe"),
        ("hash", "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855")
    ]

    for entity_type, entity_value in test_cases:
        payload = {
            "entity_type": entity_type,
            "entity_value": entity_value
        }

        response = requests.post(url, json=payload)

        if response.status_code == 200:
            data = response.json()
            print(f"  {entity_type:10s} → {data['total_queries']} queries, {data['available_sources']} available")
        else:
            print(f"  {entity_type:10s} → ERROR: {response.status_code}")

    return True


def main():
    """Run all tests"""
    print("\n" + "="*60)
    print("QUERY GENERATOR TEST SUITE")
    print("Phase 2: Deterministic Query Generator")
    print("="*60)

    tests = [
        ("Get Available Sources", test_get_available_sources),
        ("Get Query Guidance", test_get_query_guidance),
        ("Generate Queries (Default Time)", test_generate_queries),
        ("Generate Queries (Custom Time)", test_generate_queries_with_time_window),
        ("All Entity Types", test_all_entity_types)
    ]

    results = []

    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"\nERROR in {test_name}: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)

    passed = sum(1 for _, success in results if success)
    total = len(results)

    for test_name, success in results:
        status = "PASS" if success else "FAIL"
        symbol = "✓" if success else "✗"
        print(f"  [{symbol}] {test_name}: {status}")

    print(f"\nTotal: {passed}/{total} tests passed")

    if passed == total:
        print("\n✓ All tests PASSED! Query Generator is operational.")
    else:
        print(f"\n✗ {total - passed} test(s) FAILED. Check Delivery Engine logs.")


if __name__ == "__main__":
    main()
