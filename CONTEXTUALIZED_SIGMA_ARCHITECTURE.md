# Contextualized Sigma Architecture

## The Refined Pipeline

```
CTI → Generic Sigma → [CONTEXTUALIZATION] → Enriched Sigma → [CORRELATION] → Multi-Source Sigma → [pySigma] → SIEM Query
                            ↑                                        ↑
                     Your Environment                        Your Relationships
                     Your Log Sources                        Your Entity Context
                     Your Quality Scores                     Your Correlation Needs
```

## 1. The Contextualization Layer (SIEMLess Value-Add)

### Generic Sigma Rule (Before)
```yaml
title: Suspicious PowerShell Execution
detection:
    selection:
        CommandLine|contains:
            - 'powershell'
            - '-encoded'
    condition: selection
```

### Contextualized Sigma Rule (After SIEMLess)
```yaml
title: Suspicious PowerShell Execution - Contextualized for ACME Corp
metadata:
    siemless_version: 2.0
    environment_context:
        available_sources: ['crowdstrike', 'windows_security', 'paloalto']
        source_quality_scores:
            crowdstrike: 0.95
            windows_security: 0.60
            paloalto: 0.80
        detection_confidence: 0.87  # Based on YOUR sources

detection:
    # Original selection enhanced with source-specific fields
    selection_crowdstrike:
        event_simpleName: ProcessRollup2
        CommandLine|contains:
            - 'powershell'
            - '-encoded'
        # Context from YOUR environment
        |context:
            - UserName|not_in: ['svc_automation', 'svc_backup']  # Your service accounts
            - ComputerName|not_in: ['DC01', 'DC02']  # Your DCs that legitimately run PS

    selection_windows:
        EventID: 4688
        NewProcessName|endswith: '\powershell.exe'
        CommandLine|contains: '-encoded'
        # Adjusted for YOUR baseline
        |context:
            - SubjectUserName|not_in: ['ACME\svc_*']

    condition: selection_crowdstrike OR selection_windows

correlation:
    # Added by SIEMLess based on YOUR correlation capabilities
    enhance_with:
        - network_check:
            source: paloalto
            within: 60s
            check: "outbound connection from same host"
        - auth_check:
            source: windows_security
            within: -300s  # 5 min before
            check: "unusual authentication"

detection_quality:
    fidelity: high  # Because you have CrowdStrike
    gaps: ['no_memory_analysis', 'no_script_block_logging']
    recommendations:
        - Enable PowerShell Script Block Logging for 95% confidence
        - Add Sysmon for enhanced process genealogy
```

## 2. The Correlation Layer (Multi-Source Enhancement)

### Before Correlation (Single Source)
```yaml
detection:
    selection:
        EventID: 4688
        CommandLine|contains: 'mimikatz'
    condition: selection
```

### After Correlation (Multi-Source with Context)
```yaml
detection:
    # Primary detection
    selection_primary:
        EventID: 4688
        CommandLine|contains: 'mimikatz'

    # Correlation requirements based on YOUR sources
    correlation_auth:
        source: windows_auth
        EventID: 4624
        LogonType: 10  # RDP
        timeframe: -600s to 0s  # Within 10 min before
        same_field: [TargetUserName, SubjectUserName]

    correlation_network:
        source: firewall
        action: allowed
        dst_port: 445
        timeframe: 0s to +300s  # Within 5 min after
        same_field: [src_ip, Workstation]

    # Enrichment context from YOUR entity store
    enrichment:
        entity_context:
            user_risk_score: '>70'
            user_department: '!IT'  # Not in IT
            host_criticality: 'high'
            host_contains_pii: true

    condition: selection_primary AND (correlation_auth OR correlation_network)

confidence_calculation:
    base: 0.60  # Mimikatz string match
    modifiers:
        - correlation_auth: +0.20
        - correlation_network: +0.15
        - entity_risk_high: +0.10
        - host_critical: +0.05
    final: 0.90  # With all correlations
```

## 3. Investigation Query Generation (Beyond Detection)

This is where SIEMLess provides MASSIVE value beyond pySigma:

### Detection vs Investigation Queries

```python
class SIEMLessQueryGenerator:
    def generate_detection_rule(self, threat):
        """Standard detection - could use pySigma"""
        return contextualized_sigma_rule

    def generate_investigation_queries(self, alert):
        """THIS is unique value - investigation workflows"""

        # Based on the alert, generate investigation queries
        investigation_flow = {
            "1_initial_scope": self.scope_incident(alert),
            "2_timeline_reconstruction": self.build_timeline_queries(alert),
            "3_lateral_movement_check": self.check_lateral_movement(alert),
            "4_persistence_check": self.check_persistence_mechanisms(alert),
            "5_data_staging": self.check_data_staging(alert),
            "6_exfiltration": self.check_exfiltration_paths(alert),
            "7_similar_patterns": self.find_similar_behavior(alert)
        }

        return investigation_flow
```

### Example: Alert Triggered → Investigation Queries Generated

**Alert**: PowerShell Empire detected on WORKSTATION01 by user JSMITH

**SIEMLess Generates Investigation Query Set**:

```python
# 1. Scope - What else did this user do?
investigation_queries['user_scope'] = """
    index=* (user="JSMITH" OR SubjectUserName="JSMITH" OR UserName="JSMITH")
    earliest=-24h latest=now
    | eval data_source=index
    | stats values(process) values(CommandLine) values(action) by _time, host, data_source
    | context: user_baseline, peer_comparison
"""

# 2. Timeline - Reconstruct the attack
investigation_queries['attack_timeline'] = """
    index=* host="WORKSTATION01"
    earliest=-1h@h latest=+30m@m
    | eval phase=case(
        EventCode=4624, "1_initial_access",
        EventCode=4672, "2_privilege_assigned",
        process="*powershell*", "3_execution",
        EventCode=5140, "4_network_share",
        EventCode=4663, "5_file_access",
        true(), "other"
    )
    | sort _time
    | transaction host maxspan=1h
    | context: attack_chain_enrichment
"""

# 3. Lateral Movement - Where else did they go?
investigation_queries['lateral_check'] = """
    # YOUR environment-specific query
    index=crowdstrike event_simpleName=NetworkConnect
    src_ip="{host_ip}" OR RemoteAddressIP4="{host_ip}"
    | join type=left [
        search index=firewall src="{host_ip}" action=allowed
    ]
    | eval lateral_risk=if(dest_port IN (445,3389,5985), "high", "low")
    | context: critical_assets_touched
"""

# 4. Data Staging - What data was accessed?
investigation_queries['data_check'] = """
    index=* host="WORKSTATION01"
    (EventCode=4663 OR event_simpleName=FileOpenInfo)
    | where file_path IN [
        "*\\Documents\\*",
        "*\\Downloads\\*",
        "C:\\Sensitive\\*"  # YOUR sensitive paths
    ]
    | eval pii_risk=lookup(pii_locations, file_path)
    | stats sum(file_size) as total_staged by file_extension
"""

# 5. Persistence - Did they establish persistence?
investigation_queries['persistence'] = """
    index=* host="WORKSTATION01"
    (EventCode=4698 OR  # Scheduled task
     EventCode=7045 OR  # Service install
     event_simpleName=RegKeyValueCreate)  # Registry
    | eval persistence_type=case(
        EventCode=4698, "scheduled_task",
        EventCode=7045, "service",
        RegistryPath="*\\Run*", "registry_run",
        true(), "other"
    )
    | context: persistence_baseline_comparison
"""

# 6. Context-Aware Peer Analysis
investigation_queries['peer_analysis'] = """
    # Compare to peers in same department
    index=* user_department="{user_dept}"
    | eval is_anomaly=ml_detect_anomaly(
        CommandLine,
        baseline=department_baseline,
        sensitivity=YOUR_CONFIGURED_SENSITIVITY
    )
    | where is_anomaly=true
"""
```

## 4. Why This is Higher Value Than pySigma Alone

### pySigma (Translation Only)
```python
# Input: Sigma rule
# Output: SIEM query
splunk_query = pysigma.convert(sigma_rule)
# That's it - no context, no correlation, no investigation
```

### SIEMLess (Intelligence Layer)
```python
# Input: Threat + YOUR environment
# Output: Complete investigation workflow

result = {
    # 1. Contextualized detection rule
    "detection": contextualized_sigma_with_correlation,

    # 2. Investigation query chain
    "investigation": {
        "scope": 5 queries,
        "timeline": 3 queries,
        "lateral": 4 queries,
        "persistence": 6 queries,
        "exfiltration": 3 queries
    },

    # 3. Remediation queries
    "remediation": {
        "find_all_affected": query,
        "check_backup_compromise": query,
        "verify_cleanup": query
    },

    # 4. Threat hunt queries
    "hunt": {
        "find_similar_patterns": query,
        "historical_check": query,
        "variant_detection": query
    },

    # 5. Context enrichment
    "context": {
        "entity_relationships": query,
        "baseline_comparison": query,
        "peer_analysis": query
    }
}
```

## 5. The Architecture Value Stack

```
Level 4: Investigation Automation (SIEMLess unique)
         ↓
Level 3: Correlation Logic (SIEMLess adds)
         ↓
Level 2: Contextualization (SIEMLess adds)
         ↓
Level 1: Translation (pySigma handles)
         ↓
Level 0: SIEM Execution
```

## 6. Practical Example: Ransomware Detection

### Generic Sigma (Starting Point)
```yaml
title: Ransomware Behavior
detection:
    selection:
        CommandLine|contains: 'vssadmin delete shadows'
```

### After SIEMLess Contextualization
```yaml
title: Ransomware Behavior - ACME Corp Context
detection:
    selection_primary:
        CommandLine|contains: 'vssadmin delete shadows'
        |context:
            - User|not_in: ACME_backup_admins  # Your legit backup admins
            - Host|not_in: ACME_backup_servers  # Your backup servers
            - TimeOfDay|not_in: backup_window   # Your backup window

    correlation_required:
        - file_encryption:
            source: crowdstrike
            event: MassFileModification
            threshold: 100 files
            timeframe: +300s

        - network_spike:
            source: firewall
            metric: outbound_bytes
            threshold: baseline * 10
            timeframe: +600s

    confidence: 0.95  # With YOUR sources and correlation

    investigation_triggers:
        - generate_timeline: true
        - check_lateral_movement: true
        - identify_patient_zero: true
        - backup_integrity_check: critical
```

### Generated Investigation Queries
```python
# SIEMLess generates 20+ investigation queries:
- "What was patient zero?"
- "Which files were encrypted?"
- "Did they exfiltrate before encrypting?"
- "What persistence was established?"
- "Which backups were touched?"
- "What C2 channels exist?"
# ... etc, all customized for YOUR environment
```

## Summary

The key insight is that **contextualization and correlation happen BEFORE translation**:

1. **CTI** → Generic threat intelligence
2. **Generic Sigma** → Standard detection logic
3. **Contextualization** → Add YOUR environment context
4. **Correlation** → Add YOUR multi-source logic
5. **Investigation Generation** → Create investigation workflows
6. **Translation** (pySigma) → Convert to SIEM syntax
7. **SIEM Query** → Deploy and detect

**SIEMLess owns steps 3-5**, which is where the real value lies. Even if you regularly update pySigma, it only handles step 6. The intelligence about YOUR environment, YOUR correlation capabilities, and YOUR investigation needs is the irreplaceable value that SIEMLess provides.