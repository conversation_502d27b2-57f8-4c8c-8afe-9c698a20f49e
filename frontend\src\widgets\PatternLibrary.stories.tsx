import type { Meta, StoryObj } from '@storybook/react'
import { PatternLibrary } from './PatternLibrary'

const meta: Meta<typeof PatternLibrary> = {
  title: 'Widgets/PatternLibrary',
  component: PatternLibrary,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Pattern library showcasing AI-crystallized patterns with usage stats, cost savings, and accuracy metrics.'
      }
    }
  },
  decorators: [
    (Story) => (
      <div style={{ height: '600px', width: '100%' }}>
        <Story />
      </div>
    )
  ],
  tags: ['autodocs'],
  argTypes: {
    onPatternSelect: {
      action: 'pattern-selected',
      description: 'Callback when a pattern is selected'
    }
  }
}

export default meta
type Story = StoryObj<typeof meta>

// Default story
export const Default: Story = {
  args: {}
}

// Story with selection callback
export const WithSelectionCallback: Story = {
  args: {
    onPatternSelect: (pattern) => {
      console.log('Selected pattern:', pattern)
      alert(`Selected: ${pattern.name}\nCategory: ${pattern.category}\nAccuracy: ${(pattern.accuracy * 100).toFixed(0)}%`)
    }
  },
  parameters: {
    docs: {
      description: {
        story: 'Pattern library with interactive selection that shows pattern details'
      }
    }
  }
}

// Story showcasing cost savings
export const CostSavingsHighlight: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: 'Emphasizes the cost savings achieved through pattern crystallization - learn expensive once, operate free forever'
      }
    }
  }
}

// Story showcasing different categories
export const CategoryFiltering: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates filtering patterns by category: Security, Entity, Behavior, and CTI'
      }
    }
  }
}