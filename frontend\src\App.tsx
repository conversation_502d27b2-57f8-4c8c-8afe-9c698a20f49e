import React, { useEffect } from 'react'
import { RouterProvider } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'react-hot-toast'
import { router } from './router'
import { useAuthStore } from './stores/authStore'
import './index.css'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 30000,
    },
  },
})

function App() {
  const { initialize } = useAuthStore()

  useEffect(() => {
    // Initialize auth on app load
    initialize()

    // Set up keyboard shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      // Global search: Ctrl+K or Cmd+K
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault()
        document.querySelector<HTMLButtonElement>('[data-search-trigger]')?.click()
      }

      // Quick actions: Ctrl+Q
      if (e.ctrlKey && e.key === 'q') {
        e.preventDefault()
        document.querySelector<HTMLButtonElement>('[data-quick-actions]')?.click()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [initialize])

  return (
    <QueryClientProvider client={queryClient}>
      <div className="h-screen overflow-hidden">
        <RouterProvider router={router} />
        <Toaster
          position="bottom-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
          }}
        />
      </div>
    </QueryClientProvider>
  )
}

export default App