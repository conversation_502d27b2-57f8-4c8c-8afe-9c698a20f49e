# SIEMLess v2.0 - Intelligence Engine Docker Image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy base engine
COPY base_engine.py ./base_engine.py

# Copy intelligence engine and all modules
COPY intelligence_engine.py .
COPY ai_models.py .
COPY pattern_manager.py .
COPY consensus_engine.py .
COPY cost_tracker.py .
COPY message_handlers.py .
COPY mapping_generator.py .

# Copy new modular architecture
COPY core/ ./core/
COPY providers/ ./providers/
COPY config/ ./config/
COPY prompts/ ./prompts/
COPY migrations/ ./migrations/

# Create directories for data and logs
RUN mkdir -p /tmp/claude/intelligence /var/log/siemless

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import asyncio; from intelligence_engine import IntelligenceEngine; engine = IntelligenceEngine(); print('Health check passed')" || exit 1

# Run the intelligence engine
CMD ["python", "intelligence_engine.py"]