#!/usr/bin/env python3
"""
Complete Intelligence Engine Stack Testing
Tests all functions: consensus, crystallization, validation, unknown patterns, entities
"""

import json
import redis
import time
import asyncio
from datetime import datetime

class IntelligenceEngineTester:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)
        self.test_results = {}

    def wait_for_response(self, channel, timeout=15):
        """Wait for response on a channel"""
        pubsub = self.redis_client.pubsub()
        pubsub.subscribe(channel)

        start_time = time.time()
        for message in pubsub.listen():
            if time.time() - start_time > timeout:
                return None

            if message['type'] == 'message':
                return json.loads(message['data'])

        return None

    def test_consensus(self):
        """Test 1: AI Consensus Request"""
        print("\n" + "="*60)
        print("TEST 1: AI CONSENSUS REQUEST")
        print("="*60)

        # Create consensus request
        request = {
            'request_id': f'consensus_test_{int(time.time())}',
            'pattern_data': {
                'event_type': 'failed_login',
                'source_ip': '*************',
                'target_user': 'admin',
                'failure_count': 5,
                'time_window': '5_minutes',
                'description': 'Multiple failed login attempts detected'
            },
            'complexity': 'medium'
        }

        print(f"[SEND] Consensus request: {request['request_id']}")
        self.redis_client.publish('intelligence.consensus', json.dumps(request))

        # Wait for response
        print("[WAIT] Waiting for consensus result...")
        response = self.wait_for_response('intelligence.consensus_result', timeout=20)

        if response:
            print("[SUCCESS] Consensus achieved!")
            print(f"  - Confidence: {response.get('data', {}).get('consensus', {}).get('confidence', 0):.2%}")
            print(f"  - Models Used: {response.get('data', {}).get('consensus', {}).get('num_models', 0)}")
            print(f"  - Consensus: {response.get('data', {}).get('consensus', {}).get('consensus', False)}")
            self.test_results['consensus'] = 'PASSED'
        else:
            print("[FAILED] No consensus response received")
            self.test_results['consensus'] = 'FAILED'

        return response

    def test_crystallization(self, consensus_result=None):
        """Test 2: Pattern Crystallization"""
        print("\n" + "="*60)
        print("TEST 2: PATTERN CRYSTALLIZATION")
        print("="*60)

        # Use consensus result or create new insights
        if consensus_result:
            ai_insights = consensus_result.get('data', {}).get('consensus', {})
        else:
            ai_insights = {
                'pattern': 'Failed login attempts',
                'indicators': [
                    'EventID=4625',
                    'Result=Failed',
                    'Count>3'
                ],
                'confidence': 0.85,
                'description': 'Brute force login attempt pattern'
            }

        request = {
            'pattern_id': f'pattern_{int(time.time())}',
            'ai_insights': ai_insights,
            'source_data': {
                'original_request': 'consensus_test'
            }
        }

        print(f"[SEND] Crystallization request: {request['pattern_id']}")
        self.redis_client.publish('intelligence.crystallize', json.dumps(request))

        # Wait for response
        print("[WAIT] Waiting for crystallization result...")
        response = self.wait_for_response('intelligence.pattern_crystallized', timeout=10)

        if response:
            print("[SUCCESS] Pattern crystallized!")
            print(f"  - Pattern ID: {response.get('data', {}).get('pattern_id', 'N/A')}")
            print(f"  - Cost Savings: {response.get('data', {}).get('cost_savings', 0)}")
            self.test_results['crystallization'] = 'PASSED'
        else:
            print("[FAILED] No crystallization response received")
            self.test_results['crystallization'] = 'FAILED'

        return response

    def test_validation(self):
        """Test 3: Pattern Validation"""
        print("\n" + "="*60)
        print("TEST 3: PATTERN VALIDATION")
        print("="*60)

        request = {
            'pattern_id': 'test_pattern_validation',
            'pattern': {
                'name': 'Brute Force Detection',
                'conditions': [
                    'EventID=4625',
                    'Count>5',
                    'TimeWindow=5min'
                ]
            },
            'type': 'security_rule'
        }

        print(f"[SEND] Validation request: {request['pattern_id']}")
        self.redis_client.publish('intelligence.validate', json.dumps(request))

        # Wait for response
        print("[WAIT] Waiting for validation result...")
        response = self.wait_for_response('intelligence.pattern_validated', timeout=10)

        if response:
            print("[SUCCESS] Pattern validated!")
            print(f"  - Pattern ID: {response.get('data', {}).get('pattern_id', 'N/A')}")
            print(f"  - Validation Confidence: {response.get('data', {}).get('confidence', 0):.2%}")
            self.test_results['validation'] = 'PASSED'
        else:
            print("[NOTE] Validation may not be fully implemented")
            self.test_results['validation'] = 'PENDING'

        return response

    def test_unknown_pattern(self):
        """Test 4: Unknown Pattern Analysis"""
        print("\n" + "="*60)
        print("TEST 4: UNKNOWN PATTERN ANALYSIS")
        print("="*60)

        request = {
            'log_sample': '2024-01-15 14:23:45 [CUSTOM] User:john.doe Action:PRIVILEGE_ESCALATION Target:root Result:SUCCESS',
            'source_type': 'custom_security_app',
            'timestamp': datetime.now().isoformat()
        }

        print(f"[SEND] Unknown pattern: {request['source_type']}")
        self.redis_client.publish('ingestion.unknown_patterns', json.dumps(request))

        # This might trigger crystallization if confident
        print("[WAIT] Waiting for analysis...")

        # Check for crystallization request
        response = self.wait_for_response('intelligence.crystallize', timeout=10)

        if response:
            print("[SUCCESS] Unknown pattern analyzed and queued for crystallization!")
            self.test_results['unknown_pattern'] = 'PASSED'
        else:
            print("[NOTE] Pattern analysis may need higher confidence or not be implemented")
            self.test_results['unknown_pattern'] = 'PENDING'

        return response

    def test_entity_pattern(self):
        """Test 5: Entity Pattern Learning"""
        print("\n" + "="*60)
        print("TEST 5: ENTITY PATTERN LEARNING")
        print("="*60)

        request = {
            'entity_pattern': {
                'type': 'user_behavior',
                'entity': 'admin_user',
                'pattern': 'After hours access',
                'indicators': [
                    'TimeOfDay>20:00',
                    'UserRole=Admin',
                    'Location=Remote'
                ]
            },
            'confidence': 0.85,
            'source': 'contextualization_engine'
        }

        print(f"[SEND] Entity pattern: {request['entity_pattern']['type']}")
        self.redis_client.publish('contextualization.new_entities', json.dumps(request))

        print("[WAIT] Pattern should be stored if confidence > threshold")
        time.sleep(2)  # Give it time to process

        # Check logs for processing
        print("[NOTE] Check engine logs for entity pattern processing")
        self.test_results['entity_pattern'] = 'MANUAL_CHECK'

        return None

    def test_cost_metrics(self):
        """Test 6: Cost Metrics Reporting"""
        print("\n" + "="*60)
        print("TEST 6: COST METRICS")
        print("="*60)

        # The engine logs cost metrics periodically
        print("[INFO] Cost metrics are logged every consensus request")
        print("[CHECK] Review logs for cost tracking:")
        print("  - Total requests")
        print("  - Total cost")
        print("  - Free vs paid requests")
        print("  - Crystallized patterns count")

        self.test_results['cost_metrics'] = 'MANUAL_CHECK'

    def run_all_tests(self):
        """Run complete test suite"""
        print("\n" + "#"*60)
        print("# INTELLIGENCE ENGINE COMPLETE STACK TEST")
        print("#"*60)
        print(f"# Started: {datetime.now().isoformat()}")
        print("#"*60)

        # Test 1: Consensus (this works!)
        consensus_result = self.test_consensus()
        time.sleep(2)

        # Test 2: Crystallization (uses consensus result)
        crystallization_result = self.test_crystallization(consensus_result)
        time.sleep(2)

        # Test 3: Validation
        validation_result = self.test_validation()
        time.sleep(2)

        # Test 4: Unknown patterns
        unknown_result = self.test_unknown_pattern()
        time.sleep(2)

        # Test 5: Entity patterns
        entity_result = self.test_entity_pattern()
        time.sleep(2)

        # Test 6: Cost metrics
        self.test_cost_metrics()

        # Summary
        print("\n" + "#"*60)
        print("# TEST SUMMARY")
        print("#"*60)

        for test_name, result in self.test_results.items():
            status_icon = "✅" if result == "PASSED" else "❌" if result == "FAILED" else "⚠️"
            print(f"{status_icon} {test_name:20} : {result}")

        print("#"*60)
        print(f"# Completed: {datetime.now().isoformat()}")
        print("#"*60)

        # Check engine health
        print("\n[BONUS] Checking engine health...")
        try:
            import requests
            health = requests.get('http://localhost:8001/health', timeout=2)
            if health.status_code == 200:
                print("✅ Intelligence Engine API is healthy")
            else:
                print("⚠️ Intelligence Engine API returned:", health.status_code)
        except:
            print("⚠️ Could not reach Intelligence Engine API endpoint")

if __name__ == "__main__":
    tester = IntelligenceEngineTester()
    tester.run_all_tests()