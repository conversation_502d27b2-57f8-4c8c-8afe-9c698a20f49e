#!/usr/bin/env python3
"""
Community Knowledge Engine API

Provides REST API endpoints for managing community knowledge sources,
viewing harvested intelligence, and configuring automated updates.
"""

from flask import Flask, request, jsonify
from datetime import datetime
import asyncio
import json
from typing import Dict, Any

app = Flask(__name__)

# Community source templates for easy user configuration
COMMUNITY_SOURCE_TEMPLATES = {
    "github_sigma_rules": {
        "source_type": "github",
        "url": "https://api.github.com/repos/SigmaHQ/sigma/contents/rules",
        "description": "Community Sigma detection rules",
        "update_frequency": 12,
        "extraction_config": {
            "file_extensions": [".yml", ".yaml"],
            "extract_patterns": True,
            "validate_sigma": True
        }
    },

    "github_yara_rules": {
        "source_type": "github",
        "url": "https://api.github.com/repos/Yara-Rules/rules/contents",
        "description": "Community YARA malware detection rules",
        "update_frequency": 24,
        "extraction_config": {
            "file_extensions": [".yar", ".yara"],
            "extract_patterns": True,
            "rule_type": "yara"
        }
    },

    "mitre_attack": {
        "source_type": "mitre",
        "url": "https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json",
        "description": "Official MITRE ATT&CK Enterprise Framework",
        "update_frequency": 24,
        "extraction_config": {
            "extract_techniques": True,
            "extract_tactics": True,
            "extract_groups": True
        }
    },

    "custom_log_parsers": {
        "source_type": "github",
        "url": "https://api.github.com/repos/your-org/log-parsers/contents/parsers",
        "description": "Custom log parsing patterns",
        "update_frequency": 6,
        "extraction_config": {
            "file_extensions": [".json", ".yml"],
            "extract_patterns": True,
            "pattern_type": "log_parser"
        }
    }
}

@app.route('/api/community/sources', methods=['GET'])
def list_community_sources():
    """List all configured community sources"""
    # This would interface with the Community Knowledge Engine
    # For now, return template information

    return jsonify({
        "status": "success",
        "sources": COMMUNITY_SOURCE_TEMPLATES,
        "message": "Available community source templates"
    })

@app.route('/api/community/sources', methods=['POST'])
def add_community_source():
    """Add a new community knowledge source"""

    try:
        source_config = request.get_json()

        # Validate required fields
        required_fields = ['source_id', 'source_type', 'url', 'description']
        for field in required_fields:
            if field not in source_config:
                return jsonify({
                    "status": "error",
                    "message": f"Missing required field: {field}"
                }), 400

        # Add default values
        source_config.setdefault('update_frequency', 24)
        source_config.setdefault('enabled', True)
        source_config.setdefault('extraction_config', {})

        # Here you would send this to the Community Knowledge Engine
        # message = {
        #     'type': 'add_source',
        #     'source_config': source_config
        # }
        # result = await send_message_to_engine('community_knowledge', message)

        return jsonify({
            "status": "success",
            "message": f"Community source '{source_config['source_id']}' added successfully",
            "source_id": source_config['source_id']
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Failed to add community source: {str(e)}"
        }), 500

@app.route('/api/community/sources/<source_id>', methods=['DELETE'])
def remove_community_source(source_id):
    """Remove a community knowledge source"""

    try:
        # Here you would send this to the Community Knowledge Engine
        # message = {
        #     'type': 'remove_source',
        #     'source_id': source_id
        # }
        # result = await send_message_to_engine('community_knowledge', message)

        return jsonify({
            "status": "success",
            "message": f"Community source '{source_id}' removed successfully"
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Failed to remove community source: {str(e)}"
        }), 500

@app.route('/api/community/sources/<source_id>/update', methods=['POST'])
def force_update_source(source_id):
    """Force update of a specific community source"""

    try:
        # Here you would send this to the Community Knowledge Engine
        # message = {
        #     'type': 'force_update',
        #     'source_id': source_id
        # }
        # result = await send_message_to_engine('community_knowledge', message)

        return jsonify({
            "status": "success",
            "message": f"Forced update of community source '{source_id}'"
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Failed to update community source: {str(e)}"
        }), 500

@app.route('/api/community/knowledge', methods=['GET'])
def get_community_knowledge():
    """Get harvested community knowledge with optional filtering"""

    try:
        # Get query parameters for filtering
        knowledge_type = request.args.get('type')  # 'detection_pattern', 'mitre_technique', etc.
        source_id = request.args.get('source')
        limit = int(request.args.get('limit', 100))
        offset = int(request.args.get('offset', 0))

        # Here you would query the Community Knowledge Engine
        # message = {
        #     'type': 'get_knowledge',
        #     'knowledge_type': knowledge_type,
        #     'source_id': source_id,
        #     'limit': limit,
        #     'offset': offset
        # }
        # result = await send_message_to_engine('community_knowledge', message)

        # Mock response for now
        mock_knowledge = [
            {
                "item_id": "sigma_001",
                "source_id": "sigma_rules",
                "knowledge_type": "detection_pattern",
                "title": "Suspicious PowerShell Activity",
                "confidence": 0.9,
                "extracted_at": "2024-09-27T12:00:00Z",
                "mitre_techniques": ["T1059.001"],
                "tags": ["powershell", "execution"]
            },
            {
                "item_id": "mitre_T1059",
                "source_id": "mitre_attack",
                "knowledge_type": "mitre_technique",
                "title": "T1059: Command and Scripting Interpreter",
                "confidence": 1.0,
                "extracted_at": "2024-09-27T06:00:00Z",
                "mitre_techniques": ["T1059"],
                "tags": ["execution", "scripting"]
            }
        ]

        return jsonify({
            "status": "success",
            "knowledge_items": mock_knowledge,
            "total_count": len(mock_knowledge),
            "filters_applied": {
                "knowledge_type": knowledge_type,
                "source_id": source_id,
                "limit": limit,
                "offset": offset
            }
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Failed to retrieve community knowledge: {str(e)}"
        }), 500

@app.route('/api/community/mitre/update', methods=['POST'])
def update_mitre_framework():
    """Force update of MITRE ATT&CK framework"""

    try:
        # Here you would trigger MITRE update
        # message = {
        #     'type': 'force_update',
        #     'source_id': 'mitre_attack'
        # }
        # result = await send_message_to_engine('community_knowledge', message)

        return jsonify({
            "status": "success",
            "message": "MITRE ATT&CK framework update initiated",
            "estimated_completion": "5-10 minutes"
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Failed to update MITRE framework: {str(e)}"
        }), 500

@app.route('/api/community/mitre/techniques', methods=['GET'])
def get_mitre_techniques():
    """Get MITRE ATT&CK techniques with optional filtering"""

    try:
        tactic = request.args.get('tactic')  # Filter by tactic
        platform = request.args.get('platform')  # Filter by platform
        search = request.args.get('search')  # Search in title/description

        # Mock MITRE techniques for now
        mock_techniques = [
            {
                "technique_id": "T1059.001",
                "name": "PowerShell",
                "description": "Adversaries may abuse PowerShell commands and scripts for execution.",
                "tactics": ["execution"],
                "platforms": ["Windows"],
                "data_sources": ["Process", "Command"]
            },
            {
                "technique_id": "T1055",
                "name": "Process Injection",
                "description": "Adversaries may inject code into processes in order to evade detection.",
                "tactics": ["defense-evasion", "privilege-escalation"],
                "platforms": ["Windows", "macOS", "Linux"],
                "data_sources": ["Process", "Module"]
            }
        ]

        return jsonify({
            "status": "success",
            "techniques": mock_techniques,
            "total_count": len(mock_techniques),
            "filters_applied": {
                "tactic": tactic,
                "platform": platform,
                "search": search
            }
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Failed to retrieve MITRE techniques: {str(e)}"
        }), 500

@app.route('/api/community/stats', methods=['GET'])
def get_community_stats():
    """Get statistics about community knowledge harvesting"""

    try:
        # Mock statistics for now
        stats = {
            "total_sources": 6,
            "active_sources": 5,
            "total_knowledge_items": 1247,
            "knowledge_by_type": {
                "detection_pattern": 856,
                "mitre_technique": 234,
                "log_parser": 89,
                "use_case": 45,
                "ioc_feed": 23
            },
            "knowledge_by_source": {
                "sigma_rules": 425,
                "mitre_attack": 234,
                "elastic_rules": 198,
                "yara_rules": 156,
                "splunk_security": 134,
                "ossec_rules": 100
            },
            "last_update": "2024-09-27T12:15:00Z",
            "next_scheduled_update": "2024-09-27T18:00:00Z"
        }

        return jsonify({
            "status": "success",
            "statistics": stats
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Failed to retrieve community statistics: {str(e)}"
        }), 500

@app.route('/api/community/validate', methods=['POST'])
def validate_community_source():
    """Validate that a community source is accessible and returns expected content"""

    try:
        source_config = request.get_json()

        if 'url' not in source_config:
            return jsonify({
                "status": "error",
                "message": "URL is required for validation"
            }), 400

        # Here you would actually validate the source
        # For now, just check basic URL format
        url = source_config['url']

        if not url.startswith(('http://', 'https://')):
            return jsonify({
                "status": "error",
                "message": "Invalid URL format"
            })

        return jsonify({
            "status": "success",
            "message": "Community source validation passed",
            "validation_details": {
                "url_accessible": True,
                "content_type": "application/json",
                "estimated_items": 150
            }
        })

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"Validation failed: {str(e)}"
        }), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5010, debug=True)