"""
Business Context Management API
Allows analysts to add, edit, and manage organizational context for entities
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
from uuid import uuid4

from aiohttp import web
import asyncpg


logger = logging.getLogger(__name__)


class BusinessContextAPI:
    """Handles business context CRUD operations"""

    def __init__(self, db_pool: asyncpg.Pool):
        self.db_pool = db_pool
        self.logger = logger

    def register_routes(self, app: web.Application):
        """Register HTTP routes"""
        app.router.add_get('/api/context', self.list_contexts)
        app.router.add_get('/api/context/{context_id}', self.get_context)
        app.router.add_post('/api/context', self.create_context)
        app.router.add_put('/api/context/{context_id}', self.update_context)
        app.router.add_delete('/api/context/{context_id}', self.delete_context)

        # Entity-specific context lookup
        app.router.add_get('/api/context/entity/{entity_type}/{entity_value}', self.get_context_for_entity)

        # Bulk operations
        app.router.add_post('/api/context/bulk', self.create_bulk_contexts)

        # Rule tuning suggestions
        app.router.add_get('/api/rule-tuning/suggestions', self.list_rule_tuning_suggestions)
        app.router.add_post('/api/rule-tuning/suggestions', self.create_rule_tuning_suggestion)
        app.router.add_put('/api/rule-tuning/suggestions/{suggestion_id}/approve', self.approve_rule_tuning)
        app.router.add_put('/api/rule-tuning/suggestions/{suggestion_id}/apply', self.apply_rule_tuning)

        # Query Generator (Phase 2)
        app.router.add_post('/api/investigation/generate-queries', self.generate_queries)
        app.router.add_get('/api/investigation/sources', self.get_available_sources)
        app.router.add_get('/api/investigation/guidance/{entity_type}/{entity_value}', self.get_query_guidance)

    # =============================
    # CONTEXT CRUD OPERATIONS
    # =============================

    async def list_contexts(self, request: web.Request) -> web.Response:
        """
        List all organizational contexts with filtering
        GET /api/context?entity_type=host&status=active&limit=50
        """
        try:
            # Query parameters
            entity_type = request.query.get('entity_type')
            status = request.query.get('status', 'active')
            search = request.query.get('search', '')
            limit = int(request.query.get('limit', 50))
            offset = int(request.query.get('offset', 0))

            # Build query
            query = """
                SELECT context_id, entity_type, entity_value, context_label,
                       context_description, business_unit, owner, criticality,
                       security_zone, confidence_score, created_by, created_at,
                       status
                FROM organizational_context
                WHERE 1=1
            """
            params = []
            param_idx = 1

            if entity_type:
                query += f" AND entity_type = ${param_idx}"
                params.append(entity_type)
                param_idx += 1

            if status:
                query += f" AND status = ${param_idx}"
                params.append(status)
                param_idx += 1

            if search:
                search_pattern = f"%{search}%"
                query += f" AND (entity_value ILIKE ${param_idx} OR context_label ILIKE ${param_idx+1} OR context_description ILIKE ${param_idx+2})"
                params.extend([search_pattern, search_pattern, search_pattern])
                param_idx += 3

            query += f" ORDER BY created_at DESC LIMIT ${param_idx} OFFSET ${param_idx+1}"
            params.extend([limit, offset])

            # Execute
            async with self.db_pool.acquire() as conn:
                contexts = await conn.fetch(query, *params)

                # Get total count
                count_query = "SELECT COUNT(*) FROM organizational_context WHERE status = $1" if status else "SELECT COUNT(*) FROM organizational_context"
                if status:
                    total = await conn.fetchval(count_query, status)
                else:
                    total = await conn.fetchval(count_query)

            return web.json_response({
                'contexts': [dict(c) for c in contexts],
                'total': total,
                'limit': limit,
                'offset': offset
            })

        except Exception as e:
            self.logger.error(f"Error listing contexts: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def get_context(self, request: web.Request) -> web.Response:
        """
        Get single context by ID
        GET /api/context/{context_id}
        """
        try:
            context_id = request.match_info['context_id']

            async with self.db_pool.acquire() as conn:
                context = await conn.fetchrow("""
                    SELECT * FROM organizational_context
                    WHERE context_id = $1
                """, context_id)

            if not context:
                return web.json_response({'error': 'Context not found'}, status=404)

            return web.json_response(dict(context))

        except Exception as e:
            self.logger.error(f"Error getting context: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def get_context_for_entity(self, request: web.Request) -> web.Response:
        """
        Get context for specific entity
        GET /api/context/entity/host/BACKUP-SERVER-01
        """
        try:
            entity_type = request.match_info['entity_type']
            entity_value = request.match_info['entity_value']

            async with self.db_pool.acquire() as conn:
                context = await conn.fetchrow("""
                    SELECT * FROM organizational_context
                    WHERE entity_type = $1 AND entity_value = $2 AND status = 'active'
                """, entity_type, entity_value)

            if not context:
                return web.json_response({
                    'has_context': False,
                    'message': f'No context found for {entity_type}: {entity_value}'
                })

            return web.json_response({
                'has_context': True,
                'context': dict(context)
            })

        except Exception as e:
            self.logger.error(f"Error getting entity context: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def create_context(self, request: web.Request) -> web.Response:
        """
        Create new organizational context
        POST /api/context
        {
            "entity_type": "host",
            "entity_value": "BACKUP-SERVER-01",
            "context_label": "Primary Backup Server",
            "context_description": "Runs weekly backups every Sunday 2AM-4AM",
            "business_unit": "IT Operations",
            "owner": "<EMAIL>",
            "criticality": "high",
            "security_zone": "internal",
            "behavior_pattern": {
                "scheduled_jobs": ["weekly_backup"],
                "normal_times": ["Sunday 02:00-04:00"],
                "expected_traffic": ["large_file_operations"]
            }
        }
        """
        try:
            data = await request.json()
            user = request.headers.get('X-User-ID', 'anonymous')

            # Validation
            required_fields = ['entity_type', 'entity_value', 'context_label']
            for field in required_fields:
                if field not in data:
                    return web.json_response({
                        'error': f'Missing required field: {field}'
                    }, status=400)

            # Insert
            context_id = str(uuid4())

            try:
                async with self.db_pool.acquire() as conn:
                    await conn.execute("""
                        INSERT INTO organizational_context (
                            context_id, entity_type, entity_value, context_label,
                            context_description, business_unit, owner, criticality,
                            security_zone, behavior_pattern, created_by, status
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                    """,
                        context_id,
                        data['entity_type'],
                        data['entity_value'],
                        data['context_label'],
                        data.get('context_description'),
                        data.get('business_unit'),
                        data.get('owner'),
                        data.get('criticality', 'medium'),
                        data.get('security_zone'),
                        json.dumps(data.get('behavior_pattern', {})),
                        user,
                        'active'  # Auto-approve for now; can add approval workflow later
                    )

                self.logger.info(f"Created context {context_id} for {data['entity_type']}: {data['entity_value']}")

                return web.json_response({
                    'success': True,
                    'context_id': context_id,
                    'message': 'Context created successfully'
                }, status=201)

            except asyncpg.UniqueViolationError as e:
                return web.json_response({
                    'error': 'Context already exists for this entity'
                }, status=409)

        except Exception as e:
            self.logger.error(f"Error creating context: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def update_context(self, request: web.Request) -> web.Response:
        """
        Update existing context
        PUT /api/context/{context_id}
        """
        try:
            context_id = request.match_info['context_id']
            data = await request.json()
            user = request.headers.get('X-User-ID', 'anonymous')

            # Build update query dynamically
            update_fields = []
            params = []
            param_idx = 1

            updatable_fields = [
                'context_label', 'context_description', 'business_unit',
                'owner', 'criticality', 'security_zone', 'behavior_pattern', 'status'
            ]

            for field in updatable_fields:
                if field in data:
                    update_fields.append(f"{field} = ${param_idx}")
                    if field == 'behavior_pattern':
                        params.append(json.dumps(data[field]))
                    else:
                        params.append(data[field])
                    param_idx += 1

            if not update_fields:
                return web.json_response({'error': 'No fields to update'}, status=400)

            update_fields.append(f"updated_by = ${param_idx}")
            update_fields.append("updated_at = NOW()")
            params.append(user)
            param_idx += 1
            params.append(context_id)

            query = f"""
                UPDATE organizational_context
                SET {', '.join(update_fields)}
                WHERE context_id = ${param_idx}
            """

            async with self.db_pool.acquire() as conn:
                await conn.execute(query, *params)

            self.logger.info(f"Updated context {context_id}")

            return web.json_response({
                'success': True,
                'message': 'Context updated successfully'
            })

        except Exception as e:
            self.logger.error(f"Error updating context: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def delete_context(self, request: web.Request) -> web.Response:
        """
        Delete (archive) context
        DELETE /api/context/{context_id}
        """
        try:
            context_id = request.match_info['context_id']
            user = request.headers.get('X-User-ID', 'anonymous')

            # Soft delete by setting status to 'archived'
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE organizational_context
                    SET status = 'archived', updated_by = $1, updated_at = NOW()
                    WHERE context_id = $2
                """, user, context_id)

            self.logger.info(f"Archived context {context_id}")

            return web.json_response({
                'success': True,
                'message': 'Context archived successfully'
            })

        except Exception as e:
            self.logger.error(f"Error deleting context: {e}")
            return web.json_response({'error': str(e)}, status=500)

    # =============================
    # RULE TUNING SUGGESTIONS
    # =============================

    async def list_rule_tuning_suggestions(self, request: web.Request) -> web.Response:
        """
        List rule tuning suggestions from false positive resolutions
        GET /api/rule-tuning/suggestions?status=pending
        """
        try:
            status = request.query.get('status', 'pending')
            limit = int(request.query.get('limit', 50))

            async with self.db_pool.acquire() as conn:
                suggestions = await conn.fetch("""
                    SELECT * FROM rule_tuning_suggestions
                    WHERE status = $1
                    ORDER BY created_at DESC
                    LIMIT $2
                """, status, limit)

            return web.json_response({
                'suggestions': [dict(s) for s in suggestions],
                'count': len(suggestions)
            })

        except Exception as e:
            self.logger.error(f"Error listing rule tuning suggestions: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def create_rule_tuning_suggestion(self, request: web.Request) -> web.Response:
        """
        Create rule tuning suggestion (usually from FP verdict)
        POST /api/rule-tuning/suggestions
        """
        try:
            data = await request.json()
            user = request.headers.get('X-User-ID', 'anonymous')

            suggestion_id = str(uuid4())

            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO rule_tuning_suggestions (
                        suggestion_id, rule_id, rule_name, investigation_id,
                        suggestion_type, suppression_conditions, reason,
                        analyst_context, created_by
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                """,
                    suggestion_id,
                    data.get('rule_id'),
                    data.get('rule_name'),
                    data.get('investigation_id'),
                    data['suggestion_type'],
                    json.dumps(data.get('suppression_conditions', {})),
                    data['reason'],
                    data.get('analyst_context'),
                    user
                )

            return web.json_response({
                'success': True,
                'suggestion_id': suggestion_id
            }, status=201)

        except Exception as e:
            self.logger.error(f"Error creating rule tuning suggestion: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def approve_rule_tuning(self, request: web.Request) -> web.Response:
        """Approve a rule tuning suggestion"""
        try:
            suggestion_id = request.match_info['suggestion_id']
            user = request.headers.get('X-User-ID', 'anonymous')

            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE rule_tuning_suggestions
                    SET status = 'approved', approved_by = $1, approved_at = NOW()
                    WHERE suggestion_id = $2
                """, user, suggestion_id)

            return web.json_response({'success': True})

        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def apply_rule_tuning(self, request: web.Request) -> web.Response:
        """Mark rule tuning as applied"""
        try:
            suggestion_id = request.match_info['suggestion_id']
            user = request.headers.get('X-User-ID', 'anonymous')

            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE rule_tuning_suggestions
                    SET status = 'applied', applied = true, applied_by = $1, applied_at = NOW()
                    WHERE suggestion_id = $2
                """, user, suggestion_id)

            return web.json_response({'success': True})

        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    # =============================
    # QUERY GENERATOR ENDPOINTS (Phase 2)
    # =============================

    async def generate_queries(self, request: web.Request):
        """
        Generate investigation queries for an entity

        POST /api/investigation/generate-queries
        Body: {
            "entity_type": "host",
            "entity_value": "SERVER-01",
            "time_window": {
                "start": "2025-10-04T10:00:00",
                "end": "2025-10-04T11:00:00"
            }
        }
        """
        from query_generator import QueryGeneratorService

        try:
            data = await request.json()
            entity_type = data.get('entity_type')
            entity_value = data.get('entity_value')
            time_window = data.get('time_window')

            if not entity_type or not entity_value:
                return web.json_response(
                    {'error': 'entity_type and entity_value required'},
                    status=400
                )

            # Parse time window if provided
            if time_window:
                from datetime import datetime
                time_window = {
                    'start': datetime.fromisoformat(time_window['start']),
                    'end': datetime.fromisoformat(time_window['end'])
                }

            # Generate queries
            service = QueryGeneratorService(self.db_pool, self.logger)
            queries = await service.generate_queries_for_entity(
                entity_type, entity_value, time_window
            )

            return web.json_response({
                'entity_type': entity_type,
                'entity_value': entity_value,
                'total_queries': len(queries),
                'available_sources': sum(1 for q in queries if q['available']),
                'queries': queries
            })

        except Exception as e:
            self.logger.error(f"Query generation error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def get_available_sources(self, request: web.Request):
        """
        Get list of sources we have logs from

        GET /api/investigation/sources
        """
        from query_generator import QueryGeneratorService

        try:
            service = QueryGeneratorService(self.db_pool, self.logger)
            sources = await service.get_available_sources()

            return web.json_response({
                'total_sources': len(sources),
                'sources': sources
            })

        except Exception as e:
            self.logger.error(f"Get sources error: {e}")
            return web.json_response({'error': str(e)}, status=500)

    async def get_query_guidance(self, request: web.Request):
        """
        Get investigation guidance for an entity type

        GET /api/investigation/guidance/{entity_type}/{entity_value}
        """
        from query_generator import QueryGeneratorService

        try:
            entity_type = request.match_info['entity_type']
            entity_value = request.match_info['entity_value']

            service = QueryGeneratorService(self.db_pool, self.logger)
            guidance = await service.get_query_guidance(entity_type, entity_value)

            return web.json_response(guidance)

        except Exception as e:
            self.logger.error(f"Get guidance error: {e}")
            return web.json_response({'error': str(e)}, status=500)
