"""
SIEMLess v2.0 - Ingestion Engine Configuration Management
Centralized configuration handling for data sources and engine settings
"""

from typing import Dict, Any, List, Optional
import json
import logging


class ConfigManager:
    """Manages configuration for data sources and ingestion settings"""

    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.source_configs = self._initialize_default_configs()
        self.engine_settings = self._initialize_engine_settings()

    def _initialize_default_configs(self) -> Dict[str, Dict[str, Any]]:
        """Initialize default source configurations"""
        return {
            'elasticsearch': {
                'enabled': True,
                'batch_size': 1000,
                'poll_interval': 30,
                'index_pattern': 'logs-*',
                'timestamp_field': '@timestamp'
            },
            'crowdstrike': {
                'enabled': True,
                'batch_size': 500,
                'poll_interval': 60,
                'source_type': 'detections',
                'limit': 1000
            },
            'palo_alto': {
                'enabled': True,
                'batch_size': 100,
                'poll_interval': 120
            },
            'database': {
                'enabled': True,
                'batch_size': 100,
                'poll_interval': 60
            },
            'github': {
                'enabled': True,
                'repositories': [],
                'sync_interval': 3600,
                'github_token': None
            }
        }

    def _initialize_engine_settings(self) -> Dict[str, Any]:
        """Initialize engine-level settings"""
        return {
            'max_logs_per_cycle': 500,
            'processing_yield_interval': 10,
            'stats_reporting_interval': 60,
            'github_sync_check_interval': 3600,
            'api_doc_update_interval': 3600,
            'source_monitor_interval': 30,
            'ingestion_loop_interval': 10,
            'unknown_pattern_threshold': 10
        }

    def get_source_config(self, source_type: str) -> Dict[str, Any]:
        """Get configuration for a specific source type"""
        return self.source_configs.get(source_type, {})

    def update_source_config(self, source_type: str, config: Dict[str, Any]) -> bool:
        """Update configuration for a source type"""
        if source_type in self.source_configs:
            self.source_configs[source_type].update(config)
            self.logger.info(f"Updated configuration for {source_type}")
            return True
        else:
            self.logger.warning(f"Unknown source type: {source_type}")
            return False

    def get_all_source_configs(self) -> Dict[str, Dict[str, Any]]:
        """Get all source configurations"""
        return self.source_configs.copy()

    def get_enabled_sources(self) -> List[str]:
        """Get list of enabled source types"""
        return [
            source_type for source_type, config in self.source_configs.items()
            if config.get('enabled', False)
        ]

    def get_engine_setting(self, setting_name: str, default=None) -> Any:
        """Get an engine setting with optional default"""
        return self.engine_settings.get(setting_name, default)

    def update_engine_setting(self, setting_name: str, value: Any) -> None:
        """Update an engine setting"""
        self.engine_settings[setting_name] = value
        self.logger.info(f"Updated engine setting {setting_name} = {value}")

    def add_github_repository(self, repo_url: str, branch: str = 'main',
                             sync_interval: int = 3600) -> bool:
        """Add a GitHub repository to the configuration"""
        try:
            if 'repositories' not in self.source_configs['github']:
                self.source_configs['github']['repositories'] = []

            repo_config = {
                'url': repo_url,
                'branch': branch,
                'sync_interval': sync_interval
            }

            # Check if repository already exists
            existing_repos = self.source_configs['github']['repositories']
            for repo in existing_repos:
                if repo.get('url') == repo_url:
                    self.logger.warning(f"Repository {repo_url} already exists")
                    return False

            existing_repos.append(repo_config)
            self.logger.info(f"Added GitHub repository: {repo_url}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to add GitHub repository: {e}")
            return False

    def remove_github_repository(self, repo_url: str) -> bool:
        """Remove a GitHub repository from the configuration"""
        try:
            repositories = self.source_configs['github'].get('repositories', [])
            original_count = len(repositories)

            self.source_configs['github']['repositories'] = [
                repo for repo in repositories if repo.get('url') != repo_url
            ]

            removed = len(self.source_configs['github']['repositories']) < original_count
            if removed:
                self.logger.info(f"Removed GitHub repository: {repo_url}")
            else:
                self.logger.warning(f"Repository {repo_url} not found")

            return removed

        except Exception as e:
            self.logger.error(f"Failed to remove GitHub repository: {e}")
            return False

    def validate_source_config(self, source_type: str, config: Dict[str, Any]) -> bool:
        """Validate a source configuration"""
        if source_type not in self.source_configs:
            return False

        required_fields = {
            'elasticsearch': ['batch_size', 'poll_interval'],
            'crowdstrike': ['batch_size', 'poll_interval'],
            'palo_alto': ['batch_size', 'poll_interval'],
            'database': ['batch_size', 'poll_interval'],
            'github': ['sync_interval']
        }

        source_required = required_fields.get(source_type, [])
        for field in source_required:
            if field not in config:
                self.logger.error(f"Missing required field '{field}' for {source_type}")
                return False

        return True

    def export_config(self) -> Dict[str, Any]:
        """Export current configuration as dictionary"""
        return {
            'source_configs': self.source_configs,
            'engine_settings': self.engine_settings
        }

    def import_config(self, config_data: Dict[str, Any]) -> bool:
        """Import configuration from dictionary"""
        try:
            if 'source_configs' in config_data:
                self.source_configs.update(config_data['source_configs'])

            if 'engine_settings' in config_data:
                self.engine_settings.update(config_data['engine_settings'])

            self.logger.info("Configuration imported successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to import configuration: {e}")
            return False