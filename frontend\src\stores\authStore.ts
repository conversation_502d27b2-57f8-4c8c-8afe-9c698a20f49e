import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface User {
  id: string
  name: string
  email: string
  role: string
  department?: string
  avatar?: string
  sessionStartTime?: string
  sessionExpiry?: string
  accessLevel?: string
  enabledFeatures?: string[]
}

interface AuthStore {
  user: User | null
  token: string | null
  isAuthenticated: boolean

  login: (user: User, token: string) => void
  logout: () => void
  initialize: () => void
  updateUser: (user: Partial<User>) => void
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set) => ({
      user: null,
      token: null,
      isAuthenticated: false,

      login: (user, token) => {
        set({
          user: {
            ...user,
            sessionStartTime: new Date().toISOString(),
            sessionExpiry: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString() // 8 hours
          },
          token,
          isAuthenticated: true
        })
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false
        })
      },

      initialize: () => {
        // Mock user for development
        if (!useAuthStore.getState().isAuthenticated) {
          set({
            user: {
              id: '1',
              name: 'Security Analyst',
              email: '<EMAIL>',
              role: 'analyst',
              department: 'SOC',
              accessLevel: 'operator',
              enabledFeatures: ['investigation', 'patterns', 'alerts'],
              sessionStartTime: new Date().toISOString()
            },
            isAuthenticated: true,
            token: 'mock-token'
          })
        }
      },

      updateUser: (updates) => {
        set((state) => ({
          user: state.user ? { ...state.user, ...updates } : null
        }))
      }
    }),
    {
      name: 'auth-storage'
    }
  )
)