"""
Complete Investigation Context Flow Test

This demonstrates:
1. Query Elastic for general logs
2. Send to Contextualization engine for entity extraction
3. Map relationships
4. Check database for updates
"""

import asyncio
import json
import psycopg2
from redis import Redis
from uuid import uuid4
from datetime import datetime
from elasticsearch import Elasticsearch
import os
from dotenv import load_dotenv

load_dotenv()

# Configuration
REDIS_HOST = 'localhost'
REDIS_PORT = 6380
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'siemless_v2',
    'user': 'siemless',
    'password': 'siemless123'
}

# Elastic credentials
ELASTIC_CLOUD_ID = os.getenv('ELASTIC_CLOUD_ID')
ELASTIC_API_KEY = os.getenv('ELASTIC_API_KEY')


class InvestigationFlowTest:
    """Test complete investigation context flow"""

    def __init__(self):
        self.redis_client = Redis(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)
        self.db_conn = None
        self.elastic_client = None

    def connect_db(self):
        """Connect to PostgreSQL"""
        print("\n[1] Connecting to PostgreSQL...")
        self.db_conn = psycopg2.connect(**DB_CONFIG)
        print("[OK] Database connected")

    def connect_elastic(self):
        """Connect to Elastic"""
        print("\n[2] Connecting to Elastic Cloud...")
        self.elastic_client = Elasticsearch(
            cloud_id=ELASTIC_CLOUD_ID,
            api_key=ELASTIC_API_KEY,
            request_timeout=30
        )
        if self.elastic_client.ping():
            info = self.elastic_client.info()
            print(f"[OK] Connected to Elasticsearch {info['version']['number']}")
        else:
            raise Exception("Elastic connection failed")

    def get_baseline_stats(self):
        """Get current database stats before test"""
        print("\n[3] Getting baseline statistics...")
        cursor = self.db_conn.cursor()

        # Count entities
        cursor.execute("SELECT COUNT(*) FROM entities")
        entity_count = cursor.fetchone()[0]

        # Count relationships
        cursor.execute("SELECT COUNT(*) FROM relationships")
        relationship_count = cursor.fetchone()[0]

        print(f"[OK] Baseline - Entities: {entity_count}, Relationships: {relationship_count}")
        cursor.close()

        return {
            'entities': entity_count,
            'relationships': relationship_count
        }

    def query_elastic_logs(self, limit=10):
        """Query Elastic for recent general logs"""
        print(f"\n[4] Querying Elastic for {limit} recent logs...")

        # Simple query to get recent logs
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"range": {"@timestamp": {"gte": "now-24h"}}}
                    ]
                }
            },
            "size": limit,
            "sort": [{"@timestamp": {"order": "desc"}}]
        }

        try:
            response = self.elastic_client.search(
                index=['logs-*', 'filebeat-*', 'winlogbeat-*'],
                body=query
            )

            hits = response['hits']['hits']
            print(f"[OK] Retrieved {len(hits)} logs from Elastic")

            # Show sample
            if hits:
                sample = hits[0]['_source']
                print(f"\n[Sample Log]")
                print(f"  Timestamp: {sample.get('@timestamp', 'N/A')}")
                if 'host' in sample:
                    print(f"  Host: {sample['host'].get('name', 'N/A')}")
                if 'event' in sample:
                    print(f"  Event: {sample['event'].get('action', 'N/A')}")
                print(f"  Available fields: {list(sample.keys())[:10]}...")

            return hits

        except Exception as e:
            print(f"[FAIL] Elastic query error: {e}")
            return []

    def send_to_contextualization(self, logs):
        """Send logs to contextualization engine for processing"""
        print(f"\n[5] Sending {len(logs)} logs to Contextualization Engine...")

        request_id = str(uuid4())

        # Prepare logs for contextualization
        log_data = {
            'request_id': request_id,
            'source': 'elastic',
            'logs': []
        }

        for hit in logs:
            log_data['logs'].append({
                'timestamp': hit['_source'].get('@timestamp'),
                'raw': hit['_source'],
                'index': hit['_index'],
                'id': hit['_id']
            })

        # Publish to contextualization engine
        self.redis_client.publish(
            'contextualization.extract_entities',
            json.dumps(log_data)
        )

        print(f"[OK] Published {len(logs)} logs to contextualization.extract_entities")
        print(f"[OK] Request ID: {request_id}")

        return request_id

    def wait_for_contextualization(self, request_id, timeout=30):
        """Wait for contextualization results"""
        print(f"\n[6] Waiting for entity extraction results...")

        pubsub = self.redis_client.pubsub()
        response_channel = f'contextualization.entities_extracted.{request_id}'
        pubsub.subscribe(response_channel)

        start_time = datetime.now()
        results = None

        for message in pubsub.listen():
            if (datetime.now() - start_time).total_seconds() > timeout:
                print("[FAIL] Timeout waiting for contextualization")
                break

            if message['type'] == 'message':
                data = json.loads(message['data'])
                print(f"[OK] Received extraction results")

                if data.get('status') == 'success':
                    results = data
                    print(f"\n[Extraction Summary]")
                    print(f"  Entities extracted: {data.get('entity_count', 0)}")
                    print(f"  Relationships created: {data.get('relationship_count', 0)}")

                    # Show sample entities
                    entities = data.get('entities', [])
                    if entities:
                        print(f"\n[Sample Entities] (first 5)")
                        for i, entity in enumerate(entities[:5], 1):
                            print(f"  {i}. {entity.get('type')}: {entity.get('value')}")

                break

        pubsub.close()
        return results

    def check_database_updates(self, baseline):
        """Check database for new entities and relationships"""
        print(f"\n[7] Checking database for updates...")

        cursor = self.db_conn.cursor()

        # Count new entities
        cursor.execute("SELECT COUNT(*) FROM entities")
        new_entity_count = cursor.fetchone()[0]

        # Count new relationships
        cursor.execute("SELECT COUNT(*) FROM relationships")
        new_relationship_count = cursor.fetchone()[0]

        entities_added = new_entity_count - baseline['entities']
        relationships_added = new_relationship_count - baseline['relationships']

        print(f"\n[Database Updates]")
        print(f"  Entities before: {baseline['entities']}")
        print(f"  Entities after: {new_entity_count}")
        print(f"  NEW ENTITIES: +{entities_added}")
        print(f"")
        print(f"  Relationships before: {baseline['relationships']}")
        print(f"  Relationships after: {new_relationship_count}")
        print(f"  NEW RELATIONSHIPS: +{relationships_added}")

        # Get sample of new entities
        if entities_added > 0:
            print(f"\n[Sample New Entities]")
            cursor.execute("""
                SELECT entity_type, value, confidence, first_seen
                FROM entities
                ORDER BY first_seen DESC
                LIMIT 5
            """)

            for row in cursor.fetchall():
                entity_type, value, confidence, first_seen = row
                print(f"  - {entity_type}: {value} (confidence: {confidence:.2f})")

        # Get sample of new relationships
        if relationships_added > 0:
            print(f"\n[Sample New Relationships]")
            cursor.execute("""
                SELECT r.relationship_type, e1.value as source, e2.value as target, r.confidence
                FROM relationships r
                JOIN entities e1 ON r.source_entity_id = e1.id
                JOIN entities e2 ON r.target_entity_id = e2.id
                ORDER BY r.created_at DESC
                LIMIT 5
            """)

            for row in cursor.fetchall():
                rel_type, source, target, confidence = row
                print(f"  - {source} --[{rel_type}]--> {target} (confidence: {confidence:.2f})")

        cursor.close()

        return {
            'entities_added': entities_added,
            'relationships_added': relationships_added
        }

    def run_complete_test(self):
        """Run complete investigation flow test"""
        print("="*80)
        print("COMPLETE INVESTIGATION CONTEXT FLOW TEST")
        print("="*80)

        try:
            # Setup
            self.connect_db()
            self.connect_elastic()

            # Get baseline
            baseline = self.get_baseline_stats()

            # Query Elastic
            logs = self.query_elastic_logs(limit=10)

            if not logs:
                print("\n[WARN] No logs retrieved from Elastic - cannot test contextualization")
                return

            # Send to contextualization
            request_id = self.send_to_contextualization(logs)

            # Wait for results
            results = self.wait_for_contextualization(request_id)

            if not results:
                print("\n[WARN] No contextualization results - checking database anyway")

            # Check database
            updates = self.check_database_updates(baseline)

            # Summary
            print("\n" + "="*80)
            print("TEST COMPLETE")
            print("="*80)

            if updates['entities_added'] > 0 or updates['relationships_added'] > 0:
                print(f"\n[SUCCESS] Investigation context flow working!")
                print(f"  - Retrieved {len(logs)} logs from Elastic")
                print(f"  - Extracted {updates['entities_added']} new entities")
                print(f"  - Created {updates['relationships_added']} new relationships")
                print(f"\nThe system successfully:")
                print(f"  1. Queried Elastic for logs")
                print(f"  2. Extracted entities via Contextualization engine")
                print(f"  3. Mapped relationships")
                print(f"  4. Persisted to database")
            else:
                print(f"\n[INFO] No new data added (entities/relationships may already exist)")

        except Exception as e:
            print(f"\n[ERROR] Test failed: {e}")
            import traceback
            traceback.print_exc()

        finally:
            if self.db_conn:
                self.db_conn.close()


def main():
    """Run the test"""
    test = InvestigationFlowTest()
    test.run_complete_test()


if __name__ == '__main__':
    main()
