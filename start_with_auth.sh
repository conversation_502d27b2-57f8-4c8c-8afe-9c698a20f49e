#!/bin/bash

# Start SIEMLess v2.0 with Full Authentication
# This script starts both the main stack and authentication services

set -e

echo "=========================================="
echo "SIEMLess v2.0 - Starting with Authentication"
echo "=========================================="

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Check Docker
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Error: Docker is not running${NC}"
    exit 1
fi

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
    echo -e "${GREEN}Loaded environment variables${NC}"
fi

# Step 1: Start infrastructure
echo -e "\n${YELLOW}Starting infrastructure services...${NC}"
cd engines
docker-compose up -d postgres redis
sleep 10

# Step 2: Start authentication stack
echo -e "\n${YELLOW}Starting authentication services...${NC}"
docker-compose -f docker-compose.keycloak.yml up -d
cd ..

# Wait for Keycloak
echo -e "\n${YELLOW}Waiting for Keycloak to be ready...${NC}"
for i in {1..30}; do
    if curl -s http://localhost:8080/health/ready | grep -q "UP"; then
        echo -e "${GREEN}Keycloak is ready!${NC}"
        break
    else
        echo -n "."
        sleep 2
    fi
done

# Step 3: Start application engines
echo -e "\n${YELLOW}Starting application engines...${NC}"
cd engines
docker-compose up -d
cd ..

# Step 4: Wait for all services to be healthy
echo -e "\n${YELLOW}Checking service health...${NC}"
sleep 10

# Check each service
services=(
    "postgres:5433"
    "redis:6380"
    "keycloak:8080"
    "delivery_engine:8005"
)

all_healthy=true
for service in "${services[@]}"; do
    name="${service%%:*}"
    port="${service##*:}"

    if curl -s "http://localhost:$port/health" 2>/dev/null | grep -q "healthy\|UP\|ok" ||
       nc -z localhost $port 2>/dev/null; then
        echo -e "${GREEN}✓${NC} $name is healthy"
    else
        echo -e "${RED}✗${NC} $name is not responding"
        all_healthy=false
    fi
done

# Display summary
echo -e "\n=========================================="
if [ "$all_healthy" = true ]; then
    echo -e "${GREEN}All services started successfully!${NC}"
else
    echo -e "${YELLOW}Some services may need more time to start${NC}"
fi
echo "=========================================="

echo -e "\nAccess Points:"
echo -e "${GREEN}Keycloak Admin:${NC} http://localhost:8080/admin (admin/admin123)"
echo -e "${GREEN}SIEMLess API:${NC} http://localhost:8005/api/"
echo -e "${GREEN}Protected API:${NC} http://localhost/api/ (via Nginx)"

echo -e "\nTest Credentials:"
echo "  Admin:    username=admin password=admin123"
echo "  Analyst:  username=analyst1 password=analyst123"
echo "  API Key:  dev-admin-key"

echo -e "\n${GREEN}To test authentication:${NC} python test_auth.py"
echo -e "${GREEN}To view logs:${NC} cd engines && docker-compose logs -f"
echo -e "${GREEN}To stop all:${NC} cd engines && docker-compose down && docker-compose -f docker-compose.keycloak.yml down"