apiVersion: 1

datasources:
  # Prometheus for system metrics
  - name: Prometheus
    type: prometheus
    uid: prometheus-uid
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "30s"
      queryTimeout: "60s"
      httpMethod: POST

  # PostgreSQL for business data
  - name: PostgreSQL
    type: postgres
    uid: postgres-uid
    access: proxy
    url: postgres:5432
    user: siemless
    database: siemless_v2
    jsonData:
      sslmode: disable
      postgresVersion: 1500
      timescaledb: false
    secureJsonData:
      password: siemless123
    editable: true

  # Redis for real-time data
  - name: Redis
    type: redis-datasource
    uid: redis-uid
    access: proxy
    url: redis://redis:6379
    jsonData:
      client: standalone
    editable: true

  # Intelligence Engine API
  - name: Intelligence Engine
    type: marcusolsson-json-datasource
    uid: intelligence-api
    access: proxy
    url: http://intelligence_engine:8001
    jsonData:
      httpHeaderName1: "Content-Type"
    secureJsonData:
      httpHeaderValue1: "application/json"
    editable: true

  # Backend Engine API
  - name: Backend Engine
    type: marcusolsson-json-datasource
    uid: backend-api
    access: proxy
    url: http://backend_engine:8002
    jsonData:
      httpHeaderName1: "Content-Type"
    secureJsonData:
      httpHeaderValue1: "application/json"
    editable: true

  # Ingestion Engine API
  - name: Ingestion Engine
    type: marcusolsson-json-datasource
    uid: ingestion-api
    access: proxy
    url: http://ingestion_engine:8003
    jsonData:
      httpHeaderName1: "Content-Type"
    secureJsonData:
      httpHeaderValue1: "application/json"
    editable: true

  # Contextualization Engine API
  - name: Contextualization Engine
    type: marcusolsson-json-datasource
    uid: context-api
    access: proxy
    url: http://contextualization_engine:8004
    jsonData:
      httpHeaderName1: "Content-Type"
    secureJsonData:
      httpHeaderValue1: "application/json"
    editable: true

  # Delivery Engine API (main REST API)
  - name: Delivery Engine
    type: marcusolsson-json-datasource
    uid: delivery-api
    access: proxy
    url: http://delivery_engine:8005
    jsonData:
      httpHeaderName1: "Content-Type"
    secureJsonData:
      httpHeaderValue1: "application/json"
    editable: true