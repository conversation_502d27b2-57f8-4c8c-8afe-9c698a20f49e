# Parser Generation Workflow - Engine Interactions

## Question: Would Contextualization Engine Get Involved?

**Answer: YES - Contextualization Engine is CRITICAL to parser generation!**

---

## Current Architecture (Lightweight Mode)

### Existing Data Flow:
```
Ingestion Engine → Contextualization Engine → Backend Engine
      ↓                      ↓                        ↓
  Raw Logs           Entity Extraction         Intelligence Storage
                     (IP, user, host)           (30 bytes per entity)
```

**Key Insight from CLAUDE.md:**
> "NEW: Ingestion → Contextualization (extract) → Enrichment → Backend (store intelligence)"

**Proven Results:**
- 40x more entities extracted (30 → 1,206 entities)
- 243,103 relationships created
- 18.6 entities per log average

---

## Parser Generation Workflow (Proposed)

### Two-Phase Architecture:

## Phase 1: Initial Parser Creation (One-Time, AI-Powered)

```
┌─────────────────────────────────────────────────────────────────┐
│                    PARSER GENERATION PHASE                      │
└─────────────────────────────────────────────────────────────────┘

User Sample Logs
       │
       ▼
┌──────────────────┐
│ Ingestion Engine │  POST /api/parsers/generate
│  (Port 8003)     │  - Receives sample logs (3-10 samples)
└────────┬─────────┘  - Validates log format
         │            - Assigns task to Intelligence
         │
         ▼
┌──────────────────────┐
│ Intelligence Engine  │  AI Analysis (46s with <PERSON>-4)
│   (Port 8001)        │
└────────┬─────────────┘  Tasks:
         │                 1. Identify log format (JSON, CEF, Syslog, etc.)
         │                 2. Extract field patterns
         │                 3. Detect entity types (IP, user, process, etc.)
         │                 4. Generate regex/grok patterns
         │                 5. Map to generic field names
         │
         ▼
┌────────────────────────┐
│ Contextualization      │  Entity Validation & Normalization
│ Engine (Port 8004)     │
└────────┬───────────────┘  Tasks:
         │                   1. Test parser against sample logs
         │                   2. Extract entities using generated parser
         │                   3. Validate entity types (is IP valid? is user format correct?)
         │                   4. Calculate extraction coverage (% of fields captured)
         │                   5. Normalize entity values (lowercase, trim, etc.)
         │
         ▼
┌──────────────────┐
│ Backend Engine   │  SIEM Mapping & Storage
│  (Port 8002)     │
└────────┬─────────┘  Tasks:
         │            1. Load target SIEM schema (siem_definitions/elastic.yaml)
         │            2. Map generic fields → SIEM-specific fields
         │            3. Save parser to pattern_library table
         │            4. Version and activate parser
         │
         ▼
┌──────────────────┐
│  Pattern Library │  Parser Storage
│  (PostgreSQL)    │  - parser_id, regex, field_mappings
└──────────────────┘  - test_coverage, version, status
```

---

## Phase 2: Production Log Processing (Real-Time, FREE)

```
┌─────────────────────────────────────────────────────────────────┐
│                   PRODUCTION LOG PROCESSING                     │
└─────────────────────────────────────────────────────────────────┘

Incoming Log (Real-Time)
       │
       ▼
┌──────────────────┐
│ Ingestion Engine │  Pattern Matching (1ms)
│  (Port 8003)     │
└────────┬─────────┘  Tasks:
         │            1. Load parser from pattern_library (cached in Redis)
         │            2. Match log against regex pattern
         │            3. Extract fields using parser
         │            4. NO AI COST (crystallized pattern)
         │
         ▼
┌────────────────────────┐
│ Contextualization      │  Entity Extraction (5ms)
│ Engine (Port 8004)     │
└────────┬───────────────┘  Tasks:
         │                   1. Extract entities from parsed fields
         │                   2. Validate and normalize entities
         │                   3. Create relationships (user→host, IP→port)
         │                   4. Enrich with threat intel, geo data
         │                   5. Map to SIEM-specific schema
         │
         ▼
┌──────────────────┐
│ Backend Engine   │  Intelligence Storage (2ms)
│  (Port 8002)     │
└────────┬─────────┘  Tasks:
         │            1. Store entities (30 bytes each)
         │            2. Store relationships (50 bytes each)
         │            3. Store events (100 bytes each)
         │            4. NO RAW LOGS (lightweight mode)
         │
         ▼
┌──────────────────┐
│    PostgreSQL    │  Intelligence Storage
│   Entities       │  - 1,206 entities from 10,000 logs
│  Relationships   │  - 243,103 relationships
└──────────────────┘  - 98.4% storage reduction
```

---

## Contextualization Engine Role in Parser Generation

### ✅ Why Contextualization IS Involved:

**1. Entity Validation**
```python
# After Intelligence Engine generates parser, test it:
entities = contextualization_engine.extract_entities(sample_log)

# Validate extraction worked:
- Are IPs valid? (regex: \d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})
- Are usernames reasonable? (no special chars, length limits)
- Are timestamps parseable? (datetime validation)
```

**2. Coverage Calculation**
```python
# Calculate how much of the log was captured:
total_fields = len(sample_log.keys())
extracted_fields = len(entities)
coverage = (extracted_fields / total_fields) * 100

# If coverage < 80%, refine parser with AI
```

**3. Entity Normalization**
```python
# Normalize extracted values for SIEM ingestion:
- IPs: Remove leading zeros (*************** → *******)
- Users: Lowercase (ADMIN → admin)
- Domains: Lowercase (EXAMPLE.COM → example.com)
- Timestamps: Convert to ISO 8601 (multiple formats → 2025-10-02T12:00:00Z)
```

**4. Relationship Discovery**
```python
# From entities, create relationships:
user="admin" + host="DC01" → relationship: "admin logged into DC01"
src_ip="***********" + dst_port=445 → relationship: "*********** connected to port 445"
```

**5. Field Type Detection**
```python
# Contextualization knows entity types from experience:
contextualization_engine.ENTITY_FIELD_MAPPINGS = {
    'ip_address': ['source.ip', 'src_ip', 'srcip', ...],  # 27+ patterns
    'username': ['user.name', 'username', 'account.name', ...],  # 15+ patterns
    'hostname': ['host.name', 'hostname', 'computer_name', ...],  # 14+ patterns
    # ... 20+ entity types total
}

# Uses this knowledge to classify unknown fields
```

---

## Detailed Example: Generate Parser for Firewall Logs

### Sample Log Provided by User:
```json
{
  "timestamp": "2025-10-02 14:23:45",
  "src": "*************",
  "dst": "********",
  "srcport": 54321,
  "dstport": 443,
  "action": "allow",
  "user": "john.doe",
  "bytes": 1024
}
```

### Step-by-Step Flow:

**Step 1: Intelligence Engine Analyzes (46s)**
```python
# Prompt to AI:
"""
Analyze this firewall log and extract parsing patterns:
{sample_log}

Target SIEM: Elastic ECS
"""

# AI Response:
{
  "log_format": {
    "type": "json",
    "timestamp_format": "%Y-%m-%d %H:%M:%S"
  },
  "entities": {
    "ips": [
      {"field": "src", "type": "source", "value": "*************"},
      {"field": "dst", "type": "destination", "value": "********"}
    ],
    "ports": [
      {"field": "srcport", "type": "source", "value": 54321},
      {"field": "dstport", "type": "destination", "value": 443}
    ],
    "users": [
      {"field": "user", "type": "username", "value": "john.doe"}
    ],
    "actions": [
      {"field": "action", "type": "event_action", "value": "allow"}
    ]
  },
  "parsing_pattern": {
    "field_mappings": {
      "src": "source_ip",
      "dst": "destination_ip",
      "srcport": "source_port",
      "dstport": "destination_port",
      "user": "username",
      "action": "event_action"
    }
  }
}
```

**Step 2: Contextualization Engine Validates (5ms)**
```python
# Test extraction
extractor = EntityExtractor()
entities = extractor.extract_entities(sample_log)

# Validate results
assert len(entities['ips']) == 2  # Found both source and dest
assert ipaddress.ip_address('*************')  # Valid IP
assert ipaddress.ip_address('********')  # Valid IP

# Normalize
normalized_entities = {
    'source_ip': '*************',  # Already normalized
    'destination_ip': '********',
    'source_port': 54321,
    'destination_port': 443,
    'username': 'john.doe',  # Normalized to lowercase
    'event_action': 'allow'
}

# Calculate coverage
coverage = 7/8 * 100 = 87.5%  # (missed 'bytes' field, but acceptable)

# Create relationships
relationships = [
    {
        'from': {'type': 'user', 'value': 'john.doe'},
        'to': {'type': 'host', 'value': '********'},
        'relationship': 'connected_to'
    },
    {
        'from': {'type': 'ip', 'value': '*************'},
        'to': {'type': 'port', 'value': 443},
        'relationship': 'connected_to_port'
    }
]
```

**Step 3: Backend Engine Maps to SIEM (2ms)**
```python
# Load Elastic ECS schema
siem_schema = load_yaml('siem_definitions/elastic.yaml')

# Map generic → Elastic-specific
elastic_mapping = {
    'source_ip': 'source.ip',              # Generic → ECS
    'destination_ip': 'destination.ip',
    'source_port': 'source.port',
    'destination_port': 'destination.port',
    'username': 'user.name',
    'event_action': 'event.action'
}

# Final parser
parser = {
    'parser_id': 'firewall-json-001',
    'log_format': 'json',
    'field_mappings': elastic_mapping,
    'entity_types': ['ip_address', 'port', 'username', 'event_action'],
    'coverage': 87.5,
    'validated': True,
    'status': 'active'
}

# Save to pattern_library
save_to_database(parser)
```

**Step 4: Production Use (Real Logs)**
```python
# New firewall log arrives
incoming_log = {...}

# Ingestion: Match against parser (1ms)
parser = pattern_matcher.match(incoming_log)  # Finds 'firewall-json-001'

# Contextualization: Extract entities (5ms)
entities = entity_extractor.extract_entities(incoming_log, parser)
relationships = relationship_mapper.create_relationships(entities)

# Backend: Store intelligence (2ms)
store_entities(entities)  # 30 bytes each
store_relationships(relationships)  # 50 bytes each

# Total: 8ms, $0.00 (FREE - no AI cost!)
```

---

## Why Contextualization is Critical

### 1. **It Already Knows How to Extract Entities**
- 20+ entity types predefined
- 100+ field name patterns mapped
- Proven: 18.6 entities per log extraction rate

### 2. **It Validates Parser Quality**
- Tests parser against sample logs
- Calculates coverage percentage
- Identifies missing or incorrect extractions

### 3. **It Normalizes Data**
- Converts values to standard formats
- Validates data types (IP, email, timestamp)
- Ensures SIEM compatibility

### 4. **It Creates Relationships**
- Understands semantic connections
- Builds entity graphs automatically
- Enables threat hunting and investigation

### 5. **It's Already Integrated**
- Subscribes to `contextualization.process_log` channel
- Connected to Backend for storage
- Proven to work in production

---

## Complete Workflow Diagram

```
┌─────────────────────────────────────────────────────────────────────────┐
│                         PARSER GENERATION                               │
└─────────────────────────────────────────────────────────────────────────┘

 User → Ingestion → Intelligence → Contextualization → Backend → Storage
        (API)       (AI Analysis)  (Validation)        (SIEM Map)  (DB)
         │              │               │                  │          │
    Sample Logs    Parse Pattern   Test Extract      Map Fields   Save Parser
    (3-10 logs)    Generate Regex  Validate Types    Elastic ECS  pattern_library
                   Cost: $0.008    Coverage: 87%     Field Map    Version: 1.0


┌─────────────────────────────────────────────────────────────────────────┐
│                      PRODUCTION LOG PROCESSING                          │
└─────────────────────────────────────────────────────────────────────────┘

Log → Ingestion → Contextualization → Backend → Storage
       (Match)     (Extract + Enrich)   (Store)   (DB)
         │              │                  │         │
    Find Parser    Extract Entities   Store Only   Entities (30B)
    (1ms, FREE)    Create Relations   Intelligence Relationships (50B)
    From Cache     Normalize Values   (Lightweight) Events (100B)
                   Cost: $0.00
```

---

## Performance Comparison

### Without Contextualization (Naive Approach):
```
Intelligence → Backend
      │            │
AI extracts    Stores raw
everything     extraction
      │            │
Cost: $0.008   No validation
Time: 46s      No normalization
Quality: 75%   No relationships
```

### With Contextualization (SIEMLess Approach):
```
Intelligence → Contextualization → Backend
      │              │                 │
AI generates    Validates +         Stores
parser          normalizes        intelligence
      │              │                 │
Cost: $0.008    Quality: 95%      Relationships
Time: 46s       Coverage: 87%     Graph-ready
Once only       Enrichment        Lightweight
```

**Result:** 40x more entities, 98.4% storage reduction

---

## Answer to Your Question

**"Would Contextualization Engine get involved in parser generation?"**

### ✅ YES - Absolutely Critical!

**Role in Parser Generation:**
1. **Validation:** Test parser against sample logs
2. **Quality Control:** Ensure 80%+ coverage
3. **Normalization:** Clean and standardize values
4. **Relationship Mapping:** Create entity connections
5. **Type Detection:** Leverage existing entity type knowledge

**Role in Production (After Parser Created):**
1. **Entity Extraction:** Use parser to extract entities (5ms)
2. **Enrichment:** Add threat intel, geo data, asset info
3. **Relationship Creation:** Build investigation graphs
4. **SIEM Mapping:** Convert to target SIEM schema
5. **Lightweight Storage:** Store only intelligence (30-100 bytes vs 9KB)

**Without Contextualization:**
- ❌ No entity validation
- ❌ No relationship mapping
- ❌ No data normalization
- ❌ No enrichment
- ❌ Store raw logs (9KB each)

**With Contextualization:**
- ✅ Validated entities
- ✅ 243K relationships created
- ✅ Normalized data
- ✅ Enriched with context
- ✅ 98.4% storage reduction

---

## Recommended Implementation

### Parser Generation API Endpoint:
```python
# In Ingestion Engine
@app.route('/api/parsers/generate', methods=['POST'])
async def generate_parser(request):
    data = await request.json()

    # Step 1: Send to Intelligence for AI analysis
    analysis = await intelligence_engine.analyze_logs(
        samples=data['log_samples'],
        target_siem=data['target_siem']
    )

    # Step 2: Send to Contextualization for validation
    validation = await contextualization_engine.validate_parser(
        parser=analysis['parser'],
        samples=data['log_samples']
    )

    # Step 3: Send to Backend for SIEM mapping and storage
    parser = await backend_engine.save_parser(
        parser=analysis['parser'],
        validation=validation,
        siem=data['target_siem']
    )

    return {
        'parser_id': parser['parser_id'],
        'coverage': validation['coverage'],
        'status': 'active'
    }
```

**Bottom Line:** Contextualization Engine is the bridge between AI-generated parsers and production-ready intelligence extraction. It's essential!
