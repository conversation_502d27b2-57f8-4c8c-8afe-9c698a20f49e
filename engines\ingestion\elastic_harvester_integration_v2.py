"""
Elastic Rule Harvester Integration v2 - Proper Architecture Flow
Treats harvested rules as intelligence artifacts, not logs
Routes through proper engine pipeline for analysis and enrichment
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from elastic_rule_harvester import ElasticRuleHarvester, ElasticRule, KibanaSavedSearch


class ElasticRuleAnalyzer:
    """
    Analyzes harvested Elastic rules as intelligence artifacts
    Routes through proper SIEMLess v2.0 engine pipeline
    """

    def __init__(self, redis_client, logger: Optional[logging.Logger] = None):
        self.redis_client = redis_client
        self.logger = logger or logging.getLogger(__name__)
        self.harvester = ElasticRuleHarvester(self.logger)

    async def harvest_and_analyze(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Harvest Elastic rules and route through proper analysis pipeline

        Flow:
            Elastic → Harvest → Contextualization → Intelligence → Backend

        Args:
            config: Elastic connection configuration

        Returns:
            Analysis results and statistics
        """
        try:
            # Step 1: Harvest from Elastic
            if not await self.harvester.configure(config):
                return {'success': False, 'error': 'Failed to configure Elastic connection'}

            results = await self.harvester.harvest_all()

            if not results['success']:
                return {'success': False, 'error': 'Harvest failed'}

            self.logger.info(f"Harvested {len(results['detection_rules'])} rules from Elastic")

            # Step 2: Send each rule through analysis pipeline
            analysis_stats = await self._analyze_harvested_rules(results)

            return {
                'success': True,
                'harvest_stats': {
                    'detection_rules': len(results['detection_rules']),
                    'saved_searches': len(results['saved_searches']),
                    'watcher_alerts': len(results['watcher_alerts'])
                },
                'analysis_stats': analysis_stats,
                'harvest_time': results['harvest_time']
            }

        except Exception as e:
            self.logger.error(f"Harvest and analysis failed: {e}")
            return {'success': False, 'error': str(e)}
        finally:
            await self.harvester.close()

    async def _analyze_harvested_rules(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Route harvested rules through analysis pipeline

        Pipeline:
            1. Contextualization: Break down rule structure, extract components
            2. Intelligence: Compare against existing patterns, evaluate quality
            3. Backend: Store crystallized pattern with enhancements
        """
        stats = {
            'rules_sent_for_analysis': 0,
            'searches_sent_for_analysis': 0,
            'contextualization_requests': 0,
            'intelligence_requests': 0,
            'errors': 0
        }

        # Process detection rules
        for rule in results.get('detection_rules', []):
            try:
                await self._analyze_single_rule(rule)
                stats['rules_sent_for_analysis'] += 1
            except Exception as e:
                self.logger.error(f"Error analyzing rule {rule.name}: {e}")
                stats['errors'] += 1

        # Process saved searches
        for search in results.get('saved_searches', []):
            try:
                await self._analyze_saved_search(search)
                stats['searches_sent_for_analysis'] += 1
            except Exception as e:
                self.logger.error(f"Error analyzing search {search.title}: {e}")
                stats['errors'] += 1

        return stats

    async def _analyze_single_rule(self, rule: ElasticRule):
        """
        Analyze a single Elastic rule through the engine pipeline

        Step 1: Contextualization Engine - Break down and analyze rule structure
        """
        # Prepare rule for contextualization analysis
        rule_artifact = {
            'artifact_type': 'detection_rule',
            'source_platform': 'elastic_security',
            'rule_id': rule.rule_id,
            'name': rule.name,
            'description': rule.description,

            # Detection logic to analyze
            'detection': {
                'query': rule.query,
                'language': rule.language,
                'type': rule.rule_type,
                'filters': rule.filters,
                'index_patterns': rule.index_patterns
            },

            # Metadata for context
            'metadata': {
                'severity': rule.severity,
                'risk_score': rule.risk_score,
                'enabled': rule.enabled,
                'mitre_techniques': rule.mitre_techniques,
                'tags': rule.tags,
                'false_positives': rule.false_positives,
                'interval': rule.interval,
                'created_by': rule.created_by,
                'version': rule.version
            },

            # Analysis instructions for engines
            'analysis_required': {
                'extract_entities': True,          # What entities does this rule detect?
                'extract_iocs': True,              # Any hardcoded IOCs?
                'map_relationships': True,         # What relationships does it monitor?
                'identify_use_case': True,         # What attack does it detect?
                'compare_existing': True,          # Do we have similar rules?
                'quality_assessment': True         # Is this a good rule?
            },

            'timestamp': datetime.utcnow().isoformat()
        }

        # Send to Contextualization Engine for initial analysis
        await self._send_to_contextualization(rule_artifact)

        # Contextualization will then forward to Intelligence for deeper analysis
        # Intelligence will compare against pattern library and enhance
        # Backend will store the final crystallized pattern

    async def _send_to_contextualization(self, artifact: Dict[str, Any]):
        """
        Send artifact to Contextualization Engine for analysis

        Channel: contextualization.analyze_artifact

        What Contextualization should do:
            1. Parse detection query into components
            2. Extract entities mentioned (IPs, processes, files, etc.)
            3. Map MITRE techniques to actual detection logic
            4. Identify detection use case/pattern
            5. Compare query structure against known patterns
            6. Forward to Intelligence Engine with analysis
        """
        message = {
            'type': 'analyze_detection_artifact',
            'artifact': artifact,
            'request_id': f"elastic_harvest_{artifact['rule_id']}",
            'next_engine': 'intelligence',  # After contextualization, go to intelligence
            'source': 'elastic_harvester'
        }

        await self.redis_client.publish(
            'contextualization.analyze_artifact',
            json.dumps(message)
        )

        self.logger.debug(f"Sent rule '{artifact['name']}' to Contextualization for analysis")

    async def _analyze_saved_search(self, search: KibanaSavedSearch):
        """
        Analyze a Kibana saved search as a potential detection pattern
        """
        search_artifact = {
            'artifact_type': 'saved_search',
            'source_platform': 'kibana',
            'search_id': search.id,
            'title': search.title,
            'description': search.description,

            'search_definition': {
                'query': search.query,
                'filters': search.filters,
                'columns': search.columns,
                'sort': search.sort,
                'index_pattern': search.index_pattern
            },

            'analysis_required': {
                'potential_detection': True,     # Could this be a detection rule?
                'extract_entities': True,        # What are they searching for?
                'identify_pattern': True,        # What pattern is this?
                'suggest_rule': True             # Should we make this a rule?
            },

            'timestamp': datetime.utcnow().isoformat()
        }

        await self._send_to_contextualization(search_artifact)

    async def compare_with_existing_patterns(self, rule: ElasticRule) -> Dict[str, Any]:
        """
        Request comparison of harvested rule against existing pattern library

        This is sent to Intelligence Engine which will:
            1. Query pattern library for similar rules
            2. Calculate similarity scores
            3. Identify duplicates or overlaps
            4. Suggest consolidation opportunities
        """
        comparison_request = {
            'type': 'compare_rule_to_library',
            'rule': {
                'id': rule.rule_id,
                'name': rule.name,
                'query': rule.query,
                'language': rule.language,
                'mitre_techniques': rule.mitre_techniques,
                'severity': rule.severity
            },
            'comparison_criteria': [
                'query_similarity',        # Similar detection logic
                'mitre_overlap',          # Same MITRE techniques
                'entity_overlap',         # Detecting same entities
                'use_case_match'          # Same attack scenario
            ],
            'request_id': f"compare_{rule.rule_id}",
            'response_channel': f"elastic_harvester.comparison.{rule.rule_id}"
        }

        await self.redis_client.publish(
            'intelligence.compare_pattern',
            json.dumps(comparison_request)
        )

        # Wait for response
        pubsub = self.redis_client.pubsub()
        await pubsub.subscribe(comparison_request['response_channel'])

        try:
            async for message in pubsub.listen():
                if message['type'] == 'message':
                    result = json.loads(message['data'])
                    await pubsub.unsubscribe(comparison_request['response_channel'])
                    return result
        except asyncio.TimeoutError:
            self.logger.warning(f"Comparison timeout for rule {rule.name}")
            return {'error': 'timeout'}

    async def request_rule_enhancement(self, rule: ElasticRule) -> Dict[str, Any]:
        """
        Request AI enhancement of harvested rule

        Sends to Intelligence Engine for:
            1. Quality assessment
            2. False positive prediction
            3. Performance optimization suggestions
            4. Multi-SIEM conversion recommendations
            5. Detection gap analysis
        """
        enhancement_request = {
            'type': 'enhance_detection_rule',
            'rule': {
                'id': rule.rule_id,
                'name': rule.name,
                'description': rule.description,
                'query': rule.query,
                'language': rule.language,
                'severity': rule.severity,
                'risk_score': rule.risk_score,
                'mitre_techniques': rule.mitre_techniques,
                'false_positives': rule.false_positives
            },
            'enhancement_tasks': [
                'quality_score',           # Rate the rule 0-100
                'false_positive_risk',     # Predict FP rate
                'optimize_query',          # Suggest query improvements
                'expand_coverage',         # Suggest variants for better coverage
                'multi_siem_translation'   # Generate Splunk/Sentinel versions
            ],
            'request_id': f"enhance_{rule.rule_id}",
            'response_channel': f"elastic_harvester.enhancement.{rule.rule_id}"
        }

        await self.redis_client.publish(
            'intelligence.enhance_pattern',
            json.dumps(enhancement_request)
        )

        self.logger.info(f"Requested enhancement for rule: {rule.name}")

    async def get_harvest_analysis_status(self) -> Dict[str, Any]:
        """
        Get status of rules being analyzed through the pipeline

        Queries each engine for current processing status
        """
        status = {
            'contextualization_queue': 0,
            'intelligence_queue': 0,
            'backend_queue': 0,
            'completed': 0,
            'errors': 0
        }

        # Query each engine's Redis queue
        # This would be implemented based on your queue naming conventions

        return status


class ElasticRuleComparator:
    """
    Compares harvested Elastic rules against existing SIEMLess pattern library
    Identifies duplicates, overlaps, and enhancement opportunities
    """

    def __init__(self, db_connection, logger: Optional[logging.Logger] = None):
        self.db_connection = db_connection
        self.logger = logger or logging.getLogger(__name__)

    def compare_rule_to_library(self, rule: ElasticRule) -> Dict[str, Any]:
        """
        Compare harvested rule against existing patterns

        Returns:
            {
                'exact_match': bool,
                'similar_patterns': List[Dict],
                'similarity_scores': List[float],
                'recommendation': str  # 'keep', 'merge', 'discard', 'enhance'
            }
        """
        cursor = self.db_connection.cursor()

        try:
            # Query for patterns with same MITRE techniques
            cursor.execute("""
                SELECT pattern_id, pattern_name, pattern_data, source_type
                FROM pattern_library
                WHERE pattern_data->>'mitre_techniques' ?| %s
                AND is_active = TRUE
            """, (rule.mitre_techniques,))

            similar_patterns = []
            for row in cursor.fetchall():
                pattern_data = row[2]

                similarity = self._calculate_similarity(rule, pattern_data)

                similar_patterns.append({
                    'pattern_id': row[0],
                    'pattern_name': row[1],
                    'source_type': row[3],
                    'similarity_score': similarity
                })

            # Sort by similarity
            similar_patterns.sort(key=lambda x: x['similarity_score'], reverse=True)

            # Determine recommendation
            if similar_patterns and similar_patterns[0]['similarity_score'] > 0.95:
                recommendation = 'discard'  # Near duplicate
            elif similar_patterns and similar_patterns[0]['similarity_score'] > 0.7:
                recommendation = 'merge'    # Similar, could consolidate
            elif similar_patterns and similar_patterns[0]['similarity_score'] > 0.4:
                recommendation = 'enhance'  # Related, learn from both
            else:
                recommendation = 'keep'     # Unique rule

            return {
                'exact_match': similar_patterns[0]['similarity_score'] > 0.95 if similar_patterns else False,
                'similar_patterns': similar_patterns[:5],  # Top 5
                'recommendation': recommendation,
                'analysis': self._generate_comparison_analysis(rule, similar_patterns)
            }

        finally:
            cursor.close()

    def _calculate_similarity(self, rule: ElasticRule, existing_pattern: Dict) -> float:
        """
        Calculate similarity score between harvested rule and existing pattern

        Factors:
            - MITRE technique overlap (40%)
            - Query structure similarity (30%)
            - Entity overlap (20%)
            - Use case match (10%)
        """
        score = 0.0

        # MITRE technique overlap
        existing_mitre = set(existing_pattern.get('mitre_techniques', []))
        rule_mitre = set(rule.mitre_techniques)

        if existing_mitre and rule_mitre:
            overlap = len(existing_mitre & rule_mitre)
            total = len(existing_mitre | rule_mitre)
            score += (overlap / total) * 0.4

        # Query similarity (simplified - would use NLP in production)
        existing_query = existing_pattern.get('query', {}).get('text', '')
        if existing_query and rule.query:
            # Simple word overlap - would use better similarity in production
            existing_words = set(existing_query.lower().split())
            rule_words = set(rule.query.lower().split())
            if existing_words and rule_words:
                overlap = len(existing_words & rule_words)
                total = len(existing_words | rule_words)
                score += (overlap / total) * 0.3

        # Severity match
        existing_severity = existing_pattern.get('severity', '')
        if existing_severity == rule.severity:
            score += 0.1

        # Source type boost (if from same platform)
        if existing_pattern.get('source', '') == 'elastic_security':
            score += 0.2

        return min(score, 1.0)

    def _generate_comparison_analysis(self, rule: ElasticRule, similar_patterns: List[Dict]) -> str:
        """Generate human-readable analysis of comparison results"""
        if not similar_patterns:
            return f"No similar patterns found. '{rule.name}' appears to be a unique detection rule."

        top_match = similar_patterns[0]
        similarity = top_match['similarity_score']

        if similarity > 0.95:
            return f"Near duplicate found: '{top_match['pattern_name']}' (similarity: {similarity:.0%}). Consider discarding or merging."
        elif similarity > 0.7:
            return f"Similar pattern exists: '{top_match['pattern_name']}' (similarity: {similarity:.0%}). Recommend consolidation."
        elif similarity > 0.4:
            return f"Related pattern found: '{top_match['pattern_name']}' (similarity: {similarity:.0%}). Both rules provide value."
        else:
            return f"Low similarity to existing patterns. '{rule.name}' adds unique detection coverage."


# Example usage
async def test_proper_flow():
    """
    Test the proper architecture flow for rule harvesting
    """
    import redis.asyncio as redis
    import psycopg2

    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # Setup connections
    redis_client = await redis.Redis(host='localhost', port=6380, decode_responses=True)
    db_conn = psycopg2.connect(
        dbname='siemless_v2',
        user='siemless',
        password='siemless123',
        host='localhost',
        port=5433
    )

    # Configuration
    config = {
        'url': 'https://your-elastic:9200',
        'api_key': 'your_api_key',
        'verify_ssl': False
    }

    # Create analyzer
    analyzer = ElasticRuleAnalyzer(redis_client, logger)

    # Harvest and analyze (proper flow)
    result = await analyzer.harvest_and_analyze(config)

    print("\nHarvest and Analysis Results:")
    print(json.dumps(result, indent=2))

    # Cleanup
    await redis_client.close()
    db_conn.close()


if __name__ == "__main__":
    asyncio.run(test_proper_flow())
