"""
SIEMLess v2.0 - Comprehensive Entity Extractor
Extracts 20+ entity types from security logs with pattern discovery
"""

import re
import json
import ipaddress
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
from fnmatch import fnmatch

class EntityExtractor:
    """Comprehensive entity extraction from security logs"""

    def __init__(self):
        # Field mappings for each entity type
        self.ENTITY_FIELD_MAPPINGS = {
            'ip_address': [
                'source.ip', 'destination.ip', 'client.ip', 'server.ip',
                'src_ip', 'dst_ip', 'srcip', 'dstip', 'sourceip', 'destip',
                'device.local_ip', 'device.external_ip', 'device.ip',
                'network.source.ip', 'network.destination.ip',
                'nat.ip', 'translated.ip', 'original.ip',
                'fortinet.firewall.srcip', 'fortinet.firewall.dstip',
                '_source.source.ip', '_source.destination.ip'
            ],
            'port': [
                'source.port', 'destination.port', 'src_port', 'dst_port',
                'srcport', 'dstport', 'sourceport', 'destport',
                'server.port', 'client.port', 'service.port',
                'network.source.port', 'network.destination.port',
                'fortinet.firewall.srcport', 'fortinet.firewall.dstport'
            ],
            'hostname': [
                'host.name', 'hostname', 'device.hostname', 'computer_name',
                'observer.name', 'agent.hostname', 'server.name', 'client.name',
                'winlog.computer_name', 'agent.name', 'host.hostname',
                'device.device_name', 'device.name', 'hostinfo.hostname'
            ],
            'process': [
                'process.name', 'process.executable', 'process.command_line',
                'parent.process.name', 'child.process.name', 'process.title',
                'winlog.event_data.ProcessName', 'event.process',
                'behaviors[].filename', 'behaviors[].cmdline', 'behaviors[].process_name'
            ],
            'service': [
                'service.name', 'service.type', 'windows.service.name',
                'application.name', 'app.name', 'software.name',
                'network.application', 'fortinet.firewall.service',
                'panw.panos.app', 'rule.app'
            ],
            'username': [
                'user.name', 'username', 'account.name', 'user.email',
                'winlog.event_data.TargetUserName', 'event.username',
                'user.id', 'account.id', 'device.user', 'user_name',
                'hostinfo.local_username', 'behaviors[].user_name'
            ],
            'domain': [
                'domain', 'dns.query.name', 'url.domain', 'http.host',
                'tls.server.name', 'destination.domain', 'dns.question.name',
                'network.domain', 'user.domain', 'device.machine_domain'
            ],
            'hash': [
                'file.hash.sha256', 'file.hash.md5', 'file.hash.sha1',
                'hash.sha256', 'hash.md5', 'hash.sha1',
                'behaviors[].sha256', 'behaviors[].md5',
                'quarantined_files[].sha256', 'ioc_value'
            ],
            'file_path': [
                'file.path', 'file.name', 'process.executable',
                'file.path.text', 'file.directory', 'behaviors[].filepath',
                'winlog.event_data.TargetFilename', 'event.file'
            ],
            'rule': [
                'rule.name', 'rule.id', 'policy.id', 'policy.name',
                'signature.id', 'signature.name', 'detection.name',
                'fortinet.firewall.policyid', 'panw.panos.ruleset',
                'rule.uuid', 'detection_id'
            ],
            'mac_address': [
                'source.mac', 'destination.mac', 'device.mac', 'host.mac',
                'src_mac', 'dst_mac', 'network.source.mac', 'network.destination.mac'
            ],
            'protocol': [
                'network.protocol', 'network.transport', 'application.protocol',
                'network.type', 'fortinet.firewall.proto', 'protocol'
            ],
            'email': [
                'user.email', 'email', 'mail.from', 'mail.to',
                'email.address', 'sender', 'recipient'
            ],
            'cve': [
                'vulnerability.id', 'cve.id', 'vulnerability.cve',
                'threat.indicator.cve', 'ioc.cve'
            ],
            'url': [
                'url.full', 'url.original', 'url.path', 'http.url',
                'request.url', 'network.url'
            ],
            'detection_id': [
                'detection_id', 'alert.id', 'event.id', 'incident.id',
                'detection.id', 'crowdstrike.detection_id'
            ],
            'severity': [
                'severity', 'event.severity', 'alert.severity',
                'log.level', 'priority', 'criticality'
            ],
            'action': [
                'action', 'event.action', 'fortinet.firewall.action',
                'panw.panos.action', 'rule.action', 'disposition'
            ],
            'group': [
                'user.group', 'group.name', 'ad.group', 'groups[]',
                'user.groups[]', 'winlog.event_data.TargetGroupName'
            ],
            'registry': [
                'registry.key', 'registry.path', 'registry.value',
                'winlog.event_data.TargetObject', 'behaviors[].registry_key'
            ]
        }

        # Regex patterns for pattern-based discovery
        self.ENTITY_REGEX_PATTERNS = {
            'ip_address': re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'),
            'ipv6': re.compile(r'\b(?:[0-9a-fA-F]{0,4}:){7}[0-9a-fA-F]{0,4}\b'),
            'mac_address': re.compile(r'\b(?:[0-9a-fA-F]{2}[:-]){5}[0-9a-fA-F]{2}\b'),
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'domain': re.compile(r'\b(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}\b'),
            'url': re.compile(r'https?://[^\s<>"{}|\\^`\[\]]+'),
            'sha256': re.compile(r'\b[a-f0-9]{64}\b'),
            'sha1': re.compile(r'\b[a-f0-9]{40}\b'),
            'md5': re.compile(r'\b[a-f0-9]{32}\b'),
            'cve': re.compile(r'\bCVE-\d{4}-\d{4,}\b'),
            'file_path_windows': re.compile(r'[A-Z]:\\(?:[^\\/:*?"<>|\r\n]+\\)*[^\\/:*?"<>|\r\n]+'),
            'file_path_unix': re.compile(r'/(?:[^/\0]+/)*[^/\0]+'),
            'port': re.compile(r'\b(?:port[:\s]+)?([1-9][0-9]{0,4})\b', re.IGNORECASE)
        }

    def extract_entities(self, log_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract all entities from a log"""
        entities = []

        # Field-based extraction
        for entity_type, field_paths in self.ENTITY_FIELD_MAPPINGS.items():
            for field_path in field_paths:
                # Handle array notation
                if '[]' in field_path:
                    extracted = self._extract_from_array(log_data, field_path, entity_type)
                    entities.extend(extracted)
                else:
                    value = self._get_nested_value(log_data, field_path)
                    if value:
                        entities.append({
                            'type': entity_type,
                            'value': self._normalize_value(value, entity_type),
                            'field': field_path,
                            'confidence': 1.0,
                            'extraction_method': 'field',
                            'context': self._get_field_context(log_data, field_path)
                        })

        # Pattern-based discovery
        entities.extend(self._discover_entities_by_pattern(log_data))

        # Deduplicate and enrich
        entities = self._deduplicate_entities(entities)

        # Extract relationships from context
        self._add_entity_relationships(entities, log_data)

        return entities

    def _get_nested_value(self, data: Dict, path: str) -> Optional[Any]:
        """Get value from nested dictionary using dot notation"""
        if not path or not data:
            return None

        keys = path.split('.')
        value = data

        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            elif isinstance(value, dict) and '_source' in value and key in value['_source']:
                value = value['_source'][key]
            else:
                return None

        return value

    def _extract_from_array(self, data: Dict, field_path: str, entity_type: str) -> List[Dict]:
        """Extract entities from array fields"""
        entities = []

        # Parse array notation (e.g., "behaviors[].filename")
        parts = field_path.split('[]')
        if len(parts) != 2:
            return entities

        array_path = parts[0]
        item_field = parts[1].lstrip('.')

        # Get the array
        array = self._get_nested_value(data, array_path)
        if not isinstance(array, list):
            return entities

        # Extract from each array item
        for item in array:
            if isinstance(item, dict):
                value = item.get(item_field)
                if value:
                    entities.append({
                        'type': entity_type,
                        'value': self._normalize_value(value, entity_type),
                        'field': field_path,
                        'confidence': 1.0,
                        'extraction_method': 'array',
                        'array_index': array.index(item)
                    })

        return entities

    def _discover_entities_by_pattern(self, log_data: Dict) -> List[Dict]:
        """Discover entities using regex patterns"""
        entities = []

        # Convert log to string for pattern matching
        log_str = json.dumps(log_data, default=str).lower()

        for entity_type, pattern in self.ENTITY_REGEX_PATTERNS.items():
            matches = pattern.findall(log_str)
            for match in matches:
                # Skip common false positives
                if entity_type == 'domain' and self._is_false_domain(match):
                    continue
                if entity_type == 'ip_address' and not self._is_valid_ip(match):
                    continue

                entities.append({
                    'type': self._map_regex_type(entity_type),
                    'value': match,
                    'field': 'regex_discovery',
                    'confidence': 0.8,
                    'extraction_method': 'pattern',
                    'pattern': entity_type
                })

        return entities

    def _normalize_value(self, value: Any, entity_type: str) -> str:
        """Normalize entity values for consistency"""
        if not value:
            return str(value)

        value_str = str(value)

        # Type-specific normalization
        if entity_type == 'hostname':
            # Remove domain suffixes for consistency
            value_str = value_str.lower().split('.')[0]
        elif entity_type == 'username':
            # Remove domain prefix (DOMAIN\user -> user)
            if '\\' in value_str:
                value_str = value_str.split('\\')[-1]
            # Remove email domain (user@domain -> user)
            if '@' in value_str:
                value_str = value_str.split('@')[0]
            value_str = value_str.lower()
        elif entity_type == 'ip_address':
            # Validate and normalize IP
            try:
                ip = ipaddress.ip_address(value_str)
                value_str = str(ip)
            except:
                pass
        elif entity_type in ['hash', 'sha256', 'md5', 'sha1']:
            value_str = value_str.lower()
        elif entity_type == 'mac_address':
            # Normalize MAC format to XX:XX:XX:XX:XX:XX
            value_str = value_str.upper().replace('-', ':')
        elif entity_type == 'port':
            # Ensure port is integer string
            try:
                port = int(value_str)
                if 0 < port < 65536:
                    value_str = str(port)
            except:
                pass

        return value_str

    def _get_field_context(self, data: Dict, field_path: str) -> Dict:
        """Get contextual information around a field"""
        context = {}

        # Get parent path
        parent_path = '.'.join(field_path.split('.')[:-1])
        if parent_path:
            parent = self._get_nested_value(data, parent_path)
            if isinstance(parent, dict):
                # Get sibling fields for relationship context
                for key in parent.keys():
                    if key in ['port', 'protocol', 'action', 'severity']:
                        context[f'sibling_{key}'] = parent[key]

        # Add timestamp if available
        for ts_field in ['@timestamp', 'timestamp', 'event.timestamp']:
            timestamp = self._get_nested_value(data, ts_field)
            if timestamp:
                context['timestamp'] = timestamp
                break

        return context

    def _deduplicate_entities(self, entities: List[Dict]) -> List[Dict]:
        """Remove duplicate entities, keeping the most confident"""
        seen = {}

        for entity in entities:
            key = f"{entity['type']}:{entity['value']}"

            if key not in seen:
                seen[key] = entity
            else:
                # Keep the one with higher confidence
                if entity.get('confidence', 0) > seen[key].get('confidence', 0):
                    seen[key] = entity
                # Or if same confidence, prefer field extraction over pattern
                elif (entity.get('confidence', 0) == seen[key].get('confidence', 0) and
                      entity.get('extraction_method') == 'field'):
                    seen[key] = entity

        return list(seen.values())

    def _add_entity_relationships(self, entities: List[Dict], log_data: Dict) -> None:
        """Add relationship hints between entities"""
        # Group entities by their extraction context
        context_groups = {}

        for entity in entities:
            # Get the parent field path
            field = entity.get('field', 'unknown')
            if field and field != 'regex_discovery':
                context = field.rsplit('.', 1)[0] if '.' in field else 'root'
            else:
                context = 'global'

            if context not in context_groups:
                context_groups[context] = []
            context_groups[context].append(entity)

        # Add relationship hints
        for context, group in context_groups.items():
            # Find IPs and ports in same context
            ips = [e for e in group if e['type'] == 'ip_address']
            ports = [e for e in group if e['type'] == 'port']

            # Link IPs with their ports
            for ip_entity in ips:
                for port_entity in ports:
                    if 'relationships' not in ip_entity:
                        ip_entity['relationships'] = []
                    ip_entity['relationships'].append({
                        'target_type': 'port',
                        'target_value': port_entity['value'],
                        'relationship': 'connects_to'
                    })

    def _is_false_domain(self, domain: str) -> bool:
        """Check if a domain match is a false positive"""
        false_patterns = [
            'example.com', 'test.com', 'localhost',
            '127.0.0.1', '0.0.0.0', '192.168.',
            'python.', 'java.', 'system.'
        ]
        return any(pattern in domain for pattern in false_patterns)

    def _is_valid_ip(self, ip_str: str) -> bool:
        """Validate IP address"""
        try:
            ipaddress.ip_address(ip_str)
            return True
        except:
            return False

    def _map_regex_type(self, pattern_type: str) -> str:
        """Map regex pattern types to entity types"""
        mapping = {
            'ipv6': 'ip_address',
            'sha256': 'hash',
            'sha1': 'hash',
            'md5': 'hash',
            'file_path_windows': 'file_path',
            'file_path_unix': 'file_path'
        }
        return mapping.get(pattern_type, pattern_type)