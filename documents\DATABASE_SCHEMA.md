# SIEMLess v2.0 - Database Schema Documentation

## Database Overview

**Database**: PostgreSQL (Port 5433)
**Schema Version**: v2.1 (Updated: September 2025)
**Total Tables**: 22+ core tables (Added log_sources, log_source_metrics)

---

## Complete Entity Relationship Diagram

```mermaid
erDiagram
    %% Engine Coordination
    engine_coordination {
        int id PK
        varchar engine_name UK
        varchar status
        varchar version
        timestamp last_heartbeat
        jsonb metadata
        timestamp created_at
        timestamp updated_at
    }

    %% Workflow Management
    workflow_instances {
        varchar workflow_id PK
        varchar workflow_type
        varchar name
        varchar status
        jsonb context
        varchar initiator
        timestamp started_at
        timestamp completed_at
        jsonb steps
        varchar current_step
    }

    workflow_history {
        int id PK
        varchar workflow_id FK
        varchar action
        jsonb details
        timestamp timestamp
    }

    workflow_templates {
        int id PK
        varchar template_id UK
        varchar name
        varchar category
        jsonb definition
        varchar version
        boolean is_active
        timestamp created_at
    }

    %% Pattern Management
    pattern_library {
        int id PK
        varchar pattern_id UK
        varchar pattern_name
        varchar pattern_type
        text pattern_regex
        jsonb pattern_data
        int match_count
        float confidence_score
        boolean is_active
        timestamp created_at
        timestamp last_used
    }

    crystallized_patterns {
        int id PK
        varchar pattern_id FK
        jsonb ai_analysis
        float consensus_score
        jsonb model_results
        varchar created_by
        timestamp crystallized_at
        float cost_saved
    }

    pattern_versions {
        int id PK
        varchar pattern_id FK
        int version_number
        text pattern_regex
        jsonb changes
        varchar modified_by
        timestamp created_at
    }

    pattern_usage {
        int id PK
        varchar pattern_id FK
        timestamp used_at
        varchar source_type
        boolean matched
        float processing_time_ms
        jsonb metadata
    }

    %% Intelligence & AI
    intelligence_consensus_results {
        int id PK
        varchar consensus_id UK
        varchar pattern_id FK
        jsonb model_inputs
        jsonb model_outputs
        float consensus_score
        varchar selected_pattern
        float total_cost
        timestamp created_at
    }

    ai_model_performance {
        int id PK
        varchar model_name
        int requests_count
        float total_cost
        float avg_response_time
        float accuracy_score
        date date
    }

    %% Detection Rules
    detection_rules {
        int id PK
        varchar rule_id UK
        varchar rule_name
        varchar platform
        text rule_content
        varchar source_type
        jsonb metadata
        boolean is_active
        timestamp created_at
        timestamp last_modified
    }

    rule_test_cases {
        int id PK
        varchar rule_id FK
        varchar test_name
        text test_log
        boolean expected_result
        boolean actual_result
        timestamp tested_at
    }

    rule_performance {
        int id PK
        varchar rule_id FK
        int true_positives
        int false_positives
        int true_negatives
        int false_negatives
        float accuracy
        date date
    }

    %% Data Processing
    ingestion_logs {
        bigint id PK
        varchar source_id
        text raw_log
        jsonb parsed_data
        varchar pattern_id FK
        boolean processed
        timestamp ingested_at
        timestamp processed_at
    }

    entities {
        int id PK
        varchar entity_id UK
        varchar entity_type
        varchar entity_value
        jsonb attributes
        jsonb enrichment_data
        timestamp first_seen
        timestamp last_seen
        int occurrence_count
    }

    relationships {
        int id PK
        varchar source_entity_id FK
        varchar target_entity_id FK
        varchar relationship_type
        float confidence_score
        jsonb metadata
        timestamp created_at
    }

    %% Storage Tiers
    warm_storage {
        bigint id PK
        varchar data_type
        jsonb data
        timestamp created_at
        timestamp expires_at
        boolean is_archived
    }

    cold_storage_index {
        int id PK
        varchar object_key
        varchar s3_bucket
        varchar data_type
        date date_partition
        bigint size_bytes
        timestamp archived_at
    }

    %% Cases & Alerts
    cases {
        int id PK
        varchar case_id UK
        varchar title
        text description
        varchar status
        varchar priority
        varchar assigned_to
        jsonb evidence
        jsonb timeline
        timestamp created_at
        timestamp updated_at
        timestamp closed_at
    }

    case_comments {
        int id PK
        varchar case_id FK
        varchar user_id
        text comment
        timestamp created_at
    }

    alerts {
        int id PK
        varchar alert_id UK
        varchar alert_type
        varchar severity
        jsonb content
        varchar delivery_channel
        varchar recipient
        varchar status
        timestamp created_at
        timestamp delivered_at
    }

    %% Log Source Quality (NEW)
    log_sources {
        int id PK
        varchar source_id UK
        varchar source_name UK
        varchar source_type
        varchar quality_tier
        decimal quality_score
        jsonb capabilities
        jsonb metadata
        timestamp created_at
        timestamp updated_at
    }

    log_source_metrics {
        int id PK
        varchar source_id FK
        bigint event_count
        float avg_latency
        float availability_percent
        timestamp measured_at
    }

    %% CTI Integration
    cti_indicators {
        int id PK
        varchar indicator_id UK
        varchar indicator_type
        varchar indicator_value
        varchar source
        float confidence
        jsonb metadata
        timestamp first_seen
        timestamp last_seen
        boolean is_active
    }

    cti_feeds {
        int id PK
        varchar feed_id UK
        varchar feed_name
        varchar feed_type
        varchar url
        jsonb credentials
        int sync_interval
        timestamp last_sync
        boolean is_active
    }

    %% User Management
    users {
        int id PK
        varchar user_id UK
        varchar username
        varchar email
        varchar role
        jsonb permissions
        timestamp created_at
        timestamp last_login
        boolean is_active
    }

    user_sessions {
        int id PK
        varchar session_id UK
        varchar user_id FK
        varchar ip_address
        varchar user_agent
        timestamp created_at
        timestamp expires_at
    }

    %% Audit & Compliance
    audit_log {
        bigint id PK
        varchar user_id FK
        varchar action
        varchar resource_type
        varchar resource_id
        jsonb changes
        varchar ip_address
        timestamp created_at
    }

    %% Relationships
    workflow_instances ||--o{ workflow_history : has
    pattern_library ||--o{ crystallized_patterns : crystallized
    pattern_library ||--o{ pattern_versions : versioned
    pattern_library ||--o{ pattern_usage : tracked
    pattern_library ||--o{ intelligence_consensus_results : validated
    detection_rules ||--o{ rule_test_cases : tested
    detection_rules ||--o{ rule_performance : measured
    ingestion_logs }o--|| pattern_library : matches
    entities ||--o{ relationships : source
    entities ||--o{ relationships : target
    cases ||--o{ case_comments : has
    users ||--o{ user_sessions : has
    users ||--o{ audit_log : performs
    workflow_instances }o--|| users : initiated_by
    cases }o--|| users : assigned_to
```

---

## Table Descriptions

### Engine Coordination Tables

#### engine_coordination
**Purpose**: Tracks the health and status of all 5 engines
```sql
CREATE TABLE engine_coordination (
    id SERIAL PRIMARY KEY,
    engine_name VARCHAR(50) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'initializing',
    version VARCHAR(20),
    last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Workflow Management Tables

#### workflow_instances
**Purpose**: Stores active and completed workflow executions
```sql
CREATE TABLE workflow_instances (
    workflow_id VARCHAR(100) PRIMARY KEY,
    workflow_type VARCHAR(50) NOT NULL,
    name VARCHAR(200),
    status VARCHAR(20) DEFAULT 'pending',
    context JSONB DEFAULT '{}',
    initiator VARCHAR(100),
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    steps JSONB DEFAULT '{}',
    current_step VARCHAR(100)
);
```

#### workflow_history
**Purpose**: Audit trail for workflow executions
```sql
CREATE TABLE workflow_history (
    id SERIAL PRIMARY KEY,
    workflow_id VARCHAR(100) REFERENCES workflow_instances(workflow_id),
    action VARCHAR(100),
    details JSONB DEFAULT '{}',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Pattern Library Tables

#### pattern_library
**Purpose**: Core pattern storage with performance metrics
```sql
CREATE TABLE pattern_library (
    id SERIAL PRIMARY KEY,
    pattern_id VARCHAR(100) UNIQUE NOT NULL,
    pattern_name VARCHAR(200) NOT NULL,
    pattern_type VARCHAR(50),
    pattern_regex TEXT,
    pattern_data JSONB DEFAULT '{}',
    match_count INTEGER DEFAULT 0,
    confidence_score FLOAT DEFAULT 0.0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP
);

-- Performance indexes
CREATE INDEX idx_pattern_type ON pattern_library(pattern_type);
CREATE INDEX idx_pattern_active ON pattern_library(is_active);
CREATE INDEX idx_pattern_data ON pattern_library USING gin(pattern_data);
```

#### crystallized_patterns
**Purpose**: AI-validated patterns with cost savings tracking
```sql
CREATE TABLE crystallized_patterns (
    id SERIAL PRIMARY KEY,
    pattern_id VARCHAR(100) REFERENCES pattern_library(pattern_id),
    ai_analysis JSONB,
    consensus_score FLOAT,
    model_results JSONB,
    created_by VARCHAR(100),
    crystallized_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    cost_saved FLOAT DEFAULT 0.0
);
```

### Intelligence Tables

#### intelligence_consensus_results
**Purpose**: Multi-AI consensus validation results
```sql
CREATE TABLE intelligence_consensus_results (
    id SERIAL PRIMARY KEY,
    consensus_id VARCHAR(100) UNIQUE NOT NULL,
    pattern_id VARCHAR(100),
    model_inputs JSONB,
    model_outputs JSONB,
    consensus_score FLOAT,
    selected_pattern TEXT,
    total_cost FLOAT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Detection Rules Tables

#### detection_rules
**Purpose**: Multi-SIEM detection rules
```sql
CREATE TABLE detection_rules (
    id SERIAL PRIMARY KEY,
    rule_id VARCHAR(100) UNIQUE NOT NULL,
    rule_name VARCHAR(200),
    platform VARCHAR(50), -- splunk, elastic, sentinel, qradar
    rule_content TEXT,
    source_type VARCHAR(50), -- cti, pattern, manual
    metadata JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Entity & Relationship Tables

#### entities
**Purpose**: Extracted and enriched entities
```sql
CREATE TABLE entities (
    id SERIAL PRIMARY KEY,
    entity_id VARCHAR(100) UNIQUE NOT NULL,
    entity_type VARCHAR(50), -- user, device, process, file, network
    entity_value VARCHAR(500),
    attributes JSONB DEFAULT '{}',
    enrichment_data JSONB DEFAULT '{}',
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    occurrence_count INTEGER DEFAULT 1
);

-- Performance indexes
CREATE INDEX idx_entity_type ON entities(entity_type);
CREATE INDEX idx_entity_value ON entities(entity_value);
```

#### relationships
**Purpose**: Entity relationship graph
```sql
CREATE TABLE relationships (
    id SERIAL PRIMARY KEY,
    source_entity_id VARCHAR(100) REFERENCES entities(entity_id),
    target_entity_id VARCHAR(100) REFERENCES entities(entity_id),
    relationship_type VARCHAR(50),
    confidence_score FLOAT DEFAULT 1.0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Graph query optimization
CREATE INDEX idx_rel_source ON relationships(source_entity_id);
CREATE INDEX idx_rel_target ON relationships(target_entity_id);
CREATE INDEX idx_rel_type ON relationships(relationship_type);
```

### Case Management Tables

#### cases
**Purpose**: Security incident case management
```sql
CREATE TABLE cases (
    id SERIAL PRIMARY KEY,
    case_id VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(500),
    description TEXT,
    status VARCHAR(20) DEFAULT 'open',
    priority VARCHAR(20) DEFAULT 'medium',
    assigned_to VARCHAR(100),
    evidence JSONB DEFAULT '[]',
    timeline JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    closed_at TIMESTAMP
);
```

---

## Database Indexes

### Performance Optimization Indexes

```sql
-- Pattern matching performance
CREATE INDEX idx_pattern_regex_gin ON pattern_library USING gin(pattern_regex gin_trgm_ops);

-- JSON search optimization
CREATE INDEX idx_ingestion_parsed ON ingestion_logs USING gin(parsed_data);
CREATE INDEX idx_entity_attrs ON entities USING gin(attributes);
CREATE INDEX idx_entity_enrich ON entities USING gin(enrichment_data);

-- Time-based queries
CREATE INDEX idx_ingestion_time ON ingestion_logs(ingested_at DESC);
CREATE INDEX idx_cases_created ON cases(created_at DESC);
CREATE INDEX idx_alerts_created ON alerts(created_at DESC);

-- Workflow performance
CREATE INDEX idx_workflow_status ON workflow_instances(status);
CREATE INDEX idx_workflow_type ON workflow_instances(workflow_type);

-- CTI lookups
CREATE INDEX idx_cti_value ON cti_indicators(indicator_value);
CREATE INDEX idx_cti_type ON cti_indicators(indicator_type);
```

---

## Data Retention Policies

### Storage Tier Transitions

```sql
-- Move to warm storage after 24 hours
INSERT INTO warm_storage (data_type, data, created_at, expires_at)
SELECT 'ingestion_log',
       jsonb_build_object('id', id, 'data', parsed_data),
       ingested_at,
       ingested_at + INTERVAL '30 days'
FROM ingestion_logs
WHERE ingested_at < NOW() - INTERVAL '24 hours'
  AND processed = true;

-- Archive to cold storage after 30 days
INSERT INTO cold_storage_index (object_key, s3_bucket, data_type, date_partition, size_bytes, archived_at)
SELECT
    'logs/' || DATE(created_at) || '/' || id || '.json',
    'siemless-archive',
    data_type,
    DATE(created_at),
    pg_column_size(data),
    NOW()
FROM warm_storage
WHERE expires_at < NOW();
```

---

## Database Views

### Useful Analytical Views

```sql
-- Pattern effectiveness view
CREATE VIEW pattern_effectiveness AS
SELECT
    pl.pattern_name,
    pl.pattern_type,
    pl.match_count,
    pl.confidence_score,
    cp.consensus_score,
    cp.cost_saved,
    COUNT(pu.id) as usage_count,
    AVG(pu.processing_time_ms) as avg_processing_time
FROM pattern_library pl
LEFT JOIN crystallized_patterns cp ON pl.pattern_id = cp.pattern_id
LEFT JOIN pattern_usage pu ON pl.pattern_id = pu.pattern_id
WHERE pl.is_active = true
GROUP BY pl.pattern_id, pl.pattern_name, pl.pattern_type,
         pl.match_count, pl.confidence_score, cp.consensus_score, cp.cost_saved;

-- Active cases dashboard view
CREATE VIEW active_cases_summary AS
SELECT
    c.case_id,
    c.title,
    c.status,
    c.priority,
    c.assigned_to,
    u.username as assigned_username,
    c.created_at,
    NOW() - c.created_at as age,
    COUNT(cc.id) as comment_count,
    jsonb_array_length(c.evidence) as evidence_count
FROM cases c
LEFT JOIN users u ON c.assigned_to = u.user_id
LEFT JOIN case_comments cc ON c.case_id = cc.case_id
WHERE c.status != 'closed'
GROUP BY c.case_id, c.title, c.status, c.priority,
         c.assigned_to, u.username, c.created_at, c.evidence;

-- Entity relationship graph view
CREATE VIEW entity_graph AS
SELECT
    e1.entity_id as source_id,
    e1.entity_type as source_type,
    e1.entity_value as source_value,
    r.relationship_type,
    e2.entity_id as target_id,
    e2.entity_type as target_type,
    e2.entity_value as target_value,
    r.confidence_score
FROM relationships r
JOIN entities e1 ON r.source_entity_id = e1.entity_id
JOIN entities e2 ON r.target_entity_id = e2.entity_id;
```

---

## Database Functions

### Useful Stored Procedures

```sql
-- Update pattern usage statistics
CREATE OR REPLACE FUNCTION update_pattern_stats()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE pattern_library
    SET match_count = match_count + 1,
        last_used = NOW()
    WHERE pattern_id = NEW.pattern_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER pattern_usage_stats
AFTER INSERT ON pattern_usage
FOR EACH ROW
EXECUTE FUNCTION update_pattern_stats();

-- Calculate cost savings
CREATE OR REPLACE FUNCTION calculate_cost_savings(
    pattern_id VARCHAR(100)
) RETURNS NUMERIC AS $$
DECLARE
    usage_count INTEGER;
    ai_cost NUMERIC := 0.02; -- Average AI cost per analysis
    pattern_cost NUMERIC := 0.0001; -- CPU cost per pattern match
BEGIN
    SELECT COUNT(*) INTO usage_count
    FROM pattern_usage
    WHERE pattern_id = $1;

    -- First use is expensive (AI), rest are cheap (pattern)
    RETURN (usage_count - 1) * (ai_cost - pattern_cost);
END;
$$ LANGUAGE plpgsql;
```

---

## Migration Scripts

### Initial Schema Creation

```sql
-- Run this to create all tables
\i sql/init.sql

-- Verify all tables created
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
ORDER BY table_name;
```

### Sample Data Loading

```sql
-- Insert sample engines
INSERT INTO engine_coordination (engine_name, status, version) VALUES
('intelligence', 'running', 'v2.0'),
('backend', 'running', 'v2.0'),
('ingestion', 'running', 'v2.0'),
('contextualization', 'running', 'v2.0'),
('delivery', 'running', 'v2.0');

-- Insert sample patterns
INSERT INTO pattern_library (pattern_id, pattern_name, pattern_type, pattern_regex) VALUES
('pat_001', 'SSH Authentication', 'security', 'sshd\[.*\]: (?P<action>Accepted|Failed) .* for (?P<user>\S+)'),
('pat_002', 'Windows Logon', 'security', 'EventID=(?P<event_id>4624|4625).*User=(?P<user>[^,]+)'),
('pat_003', 'HTTP Request', 'network', '(?P<method>GET|POST|PUT|DELETE) (?P<path>\S+) HTTP');
```

---

## Database Maintenance

### Regular Maintenance Tasks

```sql
-- Vacuum and analyze tables daily
VACUUM ANALYZE pattern_library;
VACUUM ANALYZE ingestion_logs;
VACUUM ANALYZE entities;

-- Update table statistics
ANALYZE;

-- Check table sizes
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Monitor slow queries
SELECT
    query,
    calls,
    mean_exec_time,
    max_exec_time,
    stddev_exec_time
FROM pg_stat_statements
ORDER BY mean_exec_time DESC
LIMIT 10;
```

---

## Connection Configuration

### Application Connection String

```python
# Python connection example
import psycopg2

conn = psycopg2.connect(
    host="localhost",
    port=5433,
    database="siemless_v2",
    user="siemless",
    password="siemless123"
)

# Connection pool for production
from psycopg2 import pool

db_pool = psycopg2.pool.ThreadedConnectionPool(
    minconn=1,
    maxconn=20,
    host="localhost",
    port=5433,
    database="siemless_v2",
    user="siemless",
    password="siemless123"
)
```

---

*This schema supports the complete SIEMLess v2.0 Intelligence Foundation Platform with pattern crystallization, workflow orchestration, and multi-engine coordination.*