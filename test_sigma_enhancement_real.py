"""
Test Sigma Rule Enhancement with REAL Elastic rules
Tests 3 AI tiers: <PERSON> (free), <PERSON> Flash (low cost), <PERSON> (mid quality)
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add paths
sys.path.append(os.path.join(os.path.dirname(__file__), 'engines'))

# Import directly (works both locally and in container)
from ai_models import AIModelManager
from sigma_enhancement import SigmaRuleEnhancer
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# Real Sigma rules converted from Elastic Security
REAL_SIGMA_RULES = [
    {
        'title': 'Suspicious PowerShell Encoded Command',
        'id': 'f4bbd493-b796-416e-bbf2-121235348529',
        'description': 'Detects PowerShell execution with encoded commands (-enc, -e, -EncodedCommand)',
        'tags': ['attack.t1059.001', 'attack.execution', 'attack.defense_evasion'],
        'level': 'high',
        'logsource': {
            'product': 'windows',
            'service': 'powershell',
            'category': 'process_creation'
        },
        'detection': {
            'selection': {
                'Image|endswith': 'powershell.exe',
                'CommandLine|contains': ['-enc', '-e ', '-EncodedCommand']
            },
            'condition': 'selection'
        },
        'falsepositives': [
            'Legitimate administrative scripts',
            'Software installers'
        ]
    },
    {
        'title': 'Mimikatz Command Line Detection',
        'id': 'a642964e-bead-4bed-8910-1bb4d63e3b4d',
        'description': 'Detects Mimikatz credential dumping tool usage via command line',
        'tags': ['attack.t1003.001', 'attack.credential_access'],
        'level': 'critical',
        'logsource': {
            'product': 'windows',
            'category': 'process_creation'
        },
        'detection': {
            'selection': {
                'CommandLine|contains': [
                    'sekurlsa::logonpasswords',
                    'privilege::debug',
                    'lsadump::sam',
                    'sekurlsa::tickets',
                    'kerberos::golden'
                ]
            },
            'condition': 'selection'
        },
        'falsepositives': [
            'Penetration testing (authorized)',
            'Security research'
        ]
    },
    {
        'title': 'Suspicious Network Connection from Office Application',
        'id': 'd240b5f9-e6dd-4c53-a93d-bc3e7f3ad6b4',
        'description': 'Detects Office applications making suspicious network connections',
        'tags': ['attack.t1566.001', 'attack.initial_access', 'attack.execution'],
        'level': 'medium',
        'logsource': {
            'product': 'windows',
            'category': 'network_connection'
        },
        'detection': {
            'selection_office': {
                'Image|endswith': [
                    'WINWORD.EXE',
                    'EXCEL.EXE',
                    'POWERPNT.EXE',
                    'OUTLOOK.EXE'
                ]
            },
            'selection_suspicious': {
                'DestinationPort': [443, 80, 8080, 4444, 5555]
            },
            'filter': {
                'DestinationIp|startswith': [
                    '10.',
                    '192.168.',
                    '172.16.'
                ]
            },
            'condition': 'selection_office and selection_suspicious and not filter'
        },
        'falsepositives': [
            'Office365 cloud connections',
            'SharePoint connections',
            'OneDrive sync'
        ]
    }
]


async def test_single_rule_all_tiers(rule: dict):
    """Test a single rule with all 3 AI tiers"""

    print("\n" + "="*100)
    print(f"TESTING RULE: {rule['title']}")
    print(f"Severity: {rule['level'].upper()} | Tags: {', '.join(rule['tags'][:3])}")
    print("="*100)

    # Initialize
    ai_manager = AIModelManager(logger)
    enhancer = SigmaRuleEnhancer(ai_manager, logger)

    # Test all 4 AI models for comparison
    tiers = [
        ('free', 'gemma-3-27b', '$0.00'),              # FREE tier
        ('low_cost', 'gemini-2.5-flash', '$0.002'),    # Low cost tier
        ('high_quality', 'gemini-2.5-pro', '$0.015'),  # High quality Google
        ('mid_quality', 'claude-sonnet-4', '$0.008'),  # Mid quality Anthropic
    ]

    results = {}

    for tier_key, model_name, cost in tiers:
        print(f"\n{'-'*100}")
        print(f"Testing {tier_key.upper()} tier: {model_name} (Cost: {cost})")
        print(f"{'-'*100}")

        try:
            start_time = datetime.now()

            enhancement = await enhancer.enhance_sigma_rule(
                sigma_rule=rule,
                source_platform='elastic',
                target_platforms=['wazuh', 'splunk', 'sentinel'],
                ai_tier=tier_key,
                enhancement_types=[
                    'evasion_variants',
                    'false_positive_filters',
                    'platform_optimizations',
                    'missing_context'
                ]
            )

            duration = (datetime.now() - start_time).total_seconds()

            # Count enhancements
            evasion_count = len(enhancement.get('evasion_variants', []))
            fp_count = len(enhancement.get('false_positive_filters', []))
            context_count = len(enhancement.get('missing_context', []))

            assessment = enhancement.get('overall_assessment', {})
            quality_improvement = assessment.get('enhanced_quality', 0.5) - assessment.get('original_quality', 0.5)

            results[tier_key] = {
                'success': True,
                'duration': duration,
                'evasion_variants': evasion_count,
                'fp_filters': fp_count,
                'missing_context': context_count,
                'quality_improvement': quality_improvement,
                'enhancement': enhancement
            }

            print(f"\nSUCCESS in {duration:.2f}s")
            print(f"   Enhancements:")
            print(f"      - Evasion Variants: {evasion_count}")
            print(f"      - False Positive Filters: {fp_count}")
            print(f"      - Missing Context: {context_count}")
            print(f"      - Quality Improvement: {quality_improvement:+.2f}")

            # Show sample enhancements
            if evasion_count > 0:
                print(f"\n   Sample Evasion Variant:")
                sample = enhancement['evasion_variants'][0]
                print(f"      Technique: {sample.get('technique', 'N/A')}")
                print(f"      Confidence: {sample.get('confidence', 0):.0%}")

            if fp_count > 0:
                print(f"\n   Sample FP Filter:")
                sample = enhancement['false_positive_filters'][0]
                print(f"      Source: {sample.get('source', 'N/A')}")
                print(f"      Reason: {sample.get('reason', 'N/A')[:80]}")

        except Exception as e:
            results[tier_key] = {
                'success': False,
                'error': str(e)
            }
            print(f"\nFAILED: {e}")

    return results


async def compare_all_tiers():
    """Compare all AI tiers across multiple real rules"""

    print("\n" + "="*100)
    print("SIGMA RULE ENHANCEMENT - FULL AI TIER COMPARISON")
    print("Testing: Gemma (FREE) vs Gemini Flash ($0.002) vs Gemini Pro ($0.015) vs Claude Sonnet ($0.008)")
    print("="*100)

    all_results = {}

    for i, rule in enumerate(REAL_SIGMA_RULES, 1):
        rule_results = await test_single_rule_all_tiers(rule)
        all_results[rule['title']] = rule_results

    # Generate comparison report
    print("\n" + "="*100)
    print("FINAL COMPARISON REPORT")
    print("="*100)

    tiers = ['free', 'low_cost', 'high_quality', 'mid_quality']
    tier_names = ['Gemma (FREE)', 'Gemini Flash ($0.002)', 'Gemini Pro ($0.015)', 'Claude Sonnet ($0.008)']

    # Success rates
    print("\nSuccess Rates:")
    for tier, name in zip(tiers, tier_names):
        successful = sum(1 for results in all_results.values() if results.get(tier, {}).get('success', False))
        total = len(all_results)
        print(f"   {name:30} {successful}/{total} ({successful/total*100:.0f}%)")

    # Average enhancements
    print("\nAverage Enhancements per Rule:")
    for tier, name in zip(tiers, tier_names):
        successful_results = [
            results[tier] for results in all_results.values()
            if results.get(tier, {}).get('success', False)
        ]

        if successful_results:
            avg_evasion = sum(r['evasion_variants'] for r in successful_results) / len(successful_results)
            avg_fp = sum(r['fp_filters'] for r in successful_results) / len(successful_results)
            avg_context = sum(r['missing_context'] for r in successful_results) / len(successful_results)
            avg_quality = sum(r['quality_improvement'] for r in successful_results) / len(successful_results)
            avg_time = sum(r['duration'] for r in successful_results) / len(successful_results)

            print(f"\n   {name}:")
            print(f"      Evasion Variants: {avg_evasion:.1f}")
            print(f"      FP Filters: {avg_fp:.1f}")
            print(f"      Missing Context: {avg_context:.1f}")
            print(f"      Quality Improvement: {avg_quality:+.2f}")
            print(f"      Avg Response Time: {avg_time:.2f}s")

    # Cost analysis
    print("\nCost Analysis (for 127 rules like Elastic harvest):")
    costs = {
        'free': 0.0,
        'low_cost': 0.002,
        'high_quality': 0.015,
        'mid_quality': 0.008
    }

    for tier, name in zip(tiers, tier_names):
        cost_per_rule = costs[tier]
        total_cost = cost_per_rule * 127
        print(f"   {name:30} ${total_cost:.2f}")

    # Recommendation
    print("\n" + "="*100)
    print("RECOMMENDATION")
    print("="*100)

    # Find best value tier
    best_tier = 'low_cost'  # Default recommendation

    print(f"\nRECOMMENDED: Gemini Flash (low_cost tier)")
    print(f"   Reason: Best balance of quality, speed, and cost")
    print(f"   Cost: $0.25 for 127 rules (Elastic harvest)")
    print(f"   Use Gemma (free) for: Testing and development")
    print(f"   Use Claude Sonnet (mid) for: Critical detection rules only")

    return all_results


if __name__ == "__main__":
    asyncio.run(compare_all_tiers())
