"""
Database Cleanup Script

Audit and clean up unused PostgreSQL databases in SIEMLess v2.0
"""

import psycopg2
import sys


class DatabaseCleanup:
    """Clean up unused databases"""

    def __init__(self):
        self.conn = psycopg2.connect(
            host='localhost',
            port=5433,
            user='siemless',
            password='siemless123',
            database='postgres'  # Connect to postgres DB to manage other DBs
        )
        self.conn.autocommit = True

    def audit_databases(self):
        """Audit all databases and identify which are in use"""
        cursor = self.conn.cursor()

        # Get all non-system databases
        cursor.execute("""
            SELECT datname
            FROM pg_database
            WHERE datname NOT IN ('postgres', 'template0', 'template1')
            ORDER BY datname
        """)

        databases = [row[0] for row in cursor.fetchall()]

        print("="*80)
        print("DATABASE AUDIT")
        print("="*80)

        audit_results = {}

        for db_name in databases:
            print(f"\n{db_name}:")
            print("-" * 40)

            # Connect to each database to check tables
            try:
                db_conn = psycopg2.connect(
                    host='localhost',
                    port=5433,
                    user='siemless',
                    password='siemless123',
                    database=db_name
                )
                db_cursor = db_conn.cursor()

                # Count tables
                db_cursor.execute("""
                    SELECT COUNT(*)
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_type = 'BASE TABLE'
                """)
                table_count = db_cursor.fetchone()[0]

                # Get table names
                db_cursor.execute("""
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_type = 'BASE TABLE'
                    ORDER BY table_name
                    LIMIT 10
                """)
                tables = [row[0] for row in db_cursor.fetchall()]

                # Check if any data exists
                has_data = False
                data_counts = {}
                if tables:
                    for table in tables[:5]:  # Check first 5 tables
                        try:
                            db_cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = db_cursor.fetchone()[0]
                            data_counts[table] = count
                            if count > 0:
                                has_data = True
                        except:
                            pass

                audit_results[db_name] = {
                    'table_count': table_count,
                    'tables': tables,
                    'has_data': has_data,
                    'data_counts': data_counts
                }

                print(f"  Tables: {table_count}")
                if tables:
                    print(f"  Sample tables: {', '.join(tables[:5])}")
                    if data_counts:
                        print(f"  Data counts:")
                        for table, count in data_counts.items():
                            print(f"    {table}: {count:,} rows")
                else:
                    print(f"  No tables found")

                print(f"  Status: {'IN USE (has data)' if has_data else 'EMPTY (no data)'}")

                db_cursor.close()
                db_conn.close()

            except Exception as e:
                print(f"  ERROR: {e}")
                audit_results[db_name] = {
                    'error': str(e)
                }

        cursor.close()

        return audit_results

    def get_recommendations(self, audit_results):
        """Generate cleanup recommendations"""

        print("\n" + "="*80)
        print("CLEANUP RECOMMENDATIONS")
        print("="*80)

        active_db = None
        empty_dbs = []
        keycloak_db = None

        for db_name, info in audit_results.items():
            if 'error' in info:
                continue

            # Check for Keycloak tables
            if any('client' in t or 'realm' in t for t in info.get('tables', [])):
                keycloak_db = db_name

            # Check if empty
            if info['table_count'] == 0 or not info['has_data']:
                empty_dbs.append(db_name)
            else:
                # Check if this looks like the main SIEMLess database
                siemless_tables = ['entities', 'relationships', 'warm_storage', 'cases']
                if any(t in info.get('tables', []) for t in siemless_tables):
                    active_db = db_name

        print(f"\nACTIVE DATABASE (SIEMLess v2.0 main DB):")
        print(f"  {active_db if active_db else 'NOT IDENTIFIED'}")

        if keycloak_db:
            print(f"\nKEYCLOAK DATABASE (Authentication):")
            print(f"  {keycloak_db} - KEEP (used for authentication)")

        if empty_dbs:
            print(f"\nEMPTY DATABASES (safe to remove):")
            for db in empty_dbs:
                print(f"  - {db}")

        print("\n" + "="*80)
        print("RECOMMENDED ACTIONS:")
        print("="*80)

        if active_db:
            print(f"1. KEEP: {active_db} (active SIEMLess v2.0 database)")

        if keycloak_db and keycloak_db != active_db:
            print(f"2. KEEP: {keycloak_db} (Keycloak authentication)")

        for db in empty_dbs:
            if db != keycloak_db:
                print(f"3. REMOVE: {db} (empty, no data)")

        return {
            'active_db': active_db,
            'keycloak_db': keycloak_db,
            'empty_dbs': empty_dbs
        }

    def drop_database(self, db_name: str, force: bool = False):
        """Drop a database"""

        if not force:
            print(f"\nWARNING: About to drop database '{db_name}'")
            response = input("Type database name to confirm: ")
            if response != db_name:
                print("Cancelled")
                return False

        try:
            cursor = self.conn.cursor()

            # Terminate connections to the database
            cursor.execute(f"""
                SELECT pg_terminate_backend(pg_stat_activity.pid)
                FROM pg_stat_activity
                WHERE pg_stat_activity.datname = '{db_name}'
                AND pid <> pg_backend_pid()
            """)

            # Drop the database
            cursor.execute(f"DROP DATABASE {db_name}")
            cursor.close()

            print(f"Successfully dropped database: {db_name}")
            return True

        except Exception as e:
            print(f"ERROR dropping database {db_name}: {e}")
            return False

    def close(self):
        """Close connection"""
        self.conn.close()


def main():
    import argparse

    parser = argparse.ArgumentParser(description='Audit and cleanup PostgreSQL databases')
    parser.add_argument('--drop', nargs='+', help='Drop specified databases')
    parser.add_argument('--drop-empty', action='store_true', help='Drop all empty databases')
    parser.add_argument('--force', action='store_true', help='Skip confirmation prompts')

    args = parser.parse_args()

    cleanup = DatabaseCleanup()

    try:
        # Always audit first
        audit_results = cleanup.audit_databases()
        recommendations = cleanup.get_recommendations(audit_results)

        # Drop databases if requested
        if args.drop:
            print("\n" + "="*80)
            print("DROPPING DATABASES")
            print("="*80)
            for db_name in args.drop:
                cleanup.drop_database(db_name, force=args.force)

        elif args.drop_empty:
            print("\n" + "="*80)
            print("DROPPING EMPTY DATABASES")
            print("="*80)
            for db_name in recommendations['empty_dbs']:
                if db_name != recommendations['keycloak_db']:
                    cleanup.drop_database(db_name, force=args.force)

        else:
            print("\n" + "="*80)
            print("NEXT STEPS")
            print("="*80)
            print("\nTo drop empty databases:")
            print("  python cleanup_databases.py --drop-empty")
            print("\nTo drop specific database:")
            print("  python cleanup_databases.py --drop siemless")
            print("\nAdd --force to skip confirmations")

    finally:
        cleanup.close()


if __name__ == "__main__":
    main()
