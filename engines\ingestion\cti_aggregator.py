"""
CTI Aggregator
Merges CTI data from multiple sources, deduplicates, and stores in entities table as single source of truth
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import psycopg2.extras
from uuid import uuid4

logger = logging.getLogger(__name__)


class CTIAggregator:
    """
    Single aggregator for ALL CTI sources
    Merges duplicates BEFORE routing to prevent loops and duplication

    Flow:
    1. Receive CTI from any source (OTX, OpenCTI, ThreatFox, CrowdStrike INTEL/IOCS)
    2. Check if entity exists in entities table
    3. Merge new CTI data with existing (deduplicate)
    4. Store merged entity (single source of truth)
    5. Update derived caches ONLY (Redis for enrichment)
    """

    def __init__(self, db_connection, redis_client, cti_router):
        self.db = db_connection
        self.redis = redis_client
        self.cti_router = cti_router

        # Track deduplication stats
        self.stats = {
            'total_ingested': 0,
            'duplicates_merged': 0,
            'new_entities': 0,
            'cache_updates': 0
        }

    async def ingest_cti_data(self, cti_data: Dict[str, Any], source: str) -> Dict[str, Any]:
        """
        Main ingestion point for ALL CTI data

        Args:
            cti_data: CTI data from any source
            source: Source identifier (otx, opencti, threatfox, crowdstrike_intel, crowdstrike_ioc)

        Returns:
            Statistics on ingestion
        """
        result = {
            'source': source,
            'processed': 0,
            'merged': 0,
            'new': 0,
            'cached': 0
        }

        try:
            indicators = cti_data.get('indicators', [])
            logger.info(f"Aggregating {len(indicators)} indicators from {source}")

            for indicator in indicators:
                # Extract entity details
                entity_type = indicator.get('type', 'unknown')
                entity_value = indicator.get('value')

                if not entity_value:
                    continue

                # Check if entity exists
                existing_entity = await self._get_existing_entity(entity_type, entity_value)

                if existing_entity:
                    # Merge with existing
                    merged_entity = await self._merge_entity(existing_entity, indicator, source)
                    await self._update_entity(merged_entity)
                    result['merged'] += 1
                    self.stats['duplicates_merged'] += 1
                else:
                    # Create new entity
                    new_entity = await self._create_entity_from_indicator(indicator, source)
                    await self._insert_entity(new_entity)
                    result['new'] += 1
                    self.stats['new_entities'] += 1

                # Update Redis cache if high-risk
                threat_score = indicator.get('threat_score', 50)
                if threat_score > 50:
                    await self._update_redis_cache(entity_type, entity_value, indicator, source)
                    result['cached'] += 1
                    self.stats['cache_updates'] += 1

                result['processed'] += 1
                self.stats['total_ingested'] += 1

            logger.info(f"CTI aggregation complete: {result}")
            return result

        except Exception as e:
            logger.error(f"CTI aggregation failed for {source}: {e}", exc_info=True)
            return result

    async def _get_existing_entity(self, entity_type: str, entity_value: str) -> Optional[Dict]:
        """Check if entity exists in entities table"""
        try:
            query = """
                SELECT
                    entity_id,
                    entity_type,
                    entity_value,
                    enrichment_metadata,
                    tags,
                    risk_score,
                    properties
                FROM entities
                WHERE entity_type = %s
                AND entity_value = %s
                LIMIT 1
            """

            cursor = self.db.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            cursor.execute(query, (entity_type, entity_value))
            result = cursor.fetchone()
            cursor.close()

            return dict(result) if result else None

        except Exception as e:
            logger.error(f"Error fetching entity {entity_type}:{entity_value}: {e}")
            return None

    async def _merge_entity(self, existing: Dict, new_indicator: Dict, source: str) -> Dict:
        """
        Merge new CTI data with existing entity

        Merge strategy:
        - Threat score: Take maximum
        - Tags: Union (combine all unique tags)
        - Sources: Track all sources
        - Enrichment metadata: Merge objects
        """
        merged = existing.copy()

        # Get existing metadata
        enrichment = existing.get('enrichment_metadata') or {}
        tags = set(existing.get('tags') or [])
        current_risk_score = existing.get('risk_score', 0)

        # Merge threat score (take maximum)
        new_threat_score = new_indicator.get('threat_score', 50)
        merged['risk_score'] = max(current_risk_score, new_threat_score)

        # Merge tags (union)
        new_tags = new_indicator.get('tags', [])
        tags.update(new_tags)
        merged['tags'] = list(tags)

        # Track all sources
        sources = enrichment.get('cti_sources', [])
        if source not in sources:
            sources.append(source)

        # Merge source-specific metadata
        source_key = f"{source}_data"
        if source_key not in enrichment:
            enrichment[source_key] = {}

        enrichment[source_key] = {
            'threat_score': new_threat_score,
            'confidence': new_indicator.get('confidence', 'medium'),
            'first_seen': new_indicator.get('first_seen'),
            'last_updated': datetime.utcnow().isoformat(),
            'indicators': new_indicator.get('indicators', []),
            'campaign': new_indicator.get('campaign'),
            'threat_actor': new_indicator.get('threat_actor'),
            'malware_family': new_indicator.get('malware_family')
        }

        # Update metadata
        enrichment['cti_sources'] = sources
        enrichment['last_cti_update'] = datetime.utcnow().isoformat()
        enrichment['combined_threat_score'] = merged['risk_score']

        # Take best campaign/threat actor info (prefer higher confidence sources)
        if not enrichment.get('primary_campaign') and new_indicator.get('campaign'):
            enrichment['primary_campaign'] = new_indicator['campaign']

        if not enrichment.get('primary_threat_actor') and new_indicator.get('threat_actor'):
            enrichment['primary_threat_actor'] = new_indicator['threat_actor']

        merged['enrichment_metadata'] = enrichment
        merged['enrichment_updated_at'] = datetime.utcnow()
        merged['enrichment_updated_by'] = f'cti_aggregator_{source}'

        logger.debug(f"Merged entity {existing['entity_type']}:{existing['entity_value']} - sources: {sources}, score: {merged['risk_score']}")

        return merged

    async def _create_entity_from_indicator(self, indicator: Dict, source: str) -> Dict:
        """Create new entity from CTI indicator"""
        entity_type = indicator.get('type', 'unknown')
        entity_value = indicator.get('value')

        # Build enrichment metadata
        enrichment = {
            'cti_sources': [source],
            'first_cti_source': source,
            'last_cti_update': datetime.utcnow().isoformat(),
            'combined_threat_score': indicator.get('threat_score', 50),
            f"{source}_data": {
                'threat_score': indicator.get('threat_score', 50),
                'confidence': indicator.get('confidence', 'medium'),
                'first_seen': indicator.get('first_seen'),
                'campaign': indicator.get('campaign'),
                'threat_actor': indicator.get('threat_actor'),
                'malware_family': indicator.get('malware_family')
            }
        }

        if indicator.get('campaign'):
            enrichment['primary_campaign'] = indicator['campaign']
        if indicator.get('threat_actor'):
            enrichment['primary_threat_actor'] = indicator['threat_actor']

        entity = {
            'entity_id': str(uuid4()),
            'entity_type': entity_type,
            'entity_value': entity_value,
            'enrichment_metadata': enrichment,
            'tags': indicator.get('tags', []),
            'risk_score': indicator.get('threat_score', 50),
            'enrichment_updated_at': datetime.utcnow(),
            'enrichment_updated_by': f'cti_aggregator_{source}',
            'first_seen': datetime.utcnow(),
            'last_seen': datetime.utcnow()
        }

        logger.debug(f"Created new entity from {source}: {entity_type}:{entity_value}")

        return entity

    async def _insert_entity(self, entity: Dict):
        """Insert new entity into entities table"""
        try:
            query = """
                INSERT INTO entities (
                    entity_id,
                    entity_type,
                    entity_value,
                    enrichment_metadata,
                    tags,
                    risk_score,
                    enrichment_updated_at,
                    enrichment_updated_by,
                    first_seen,
                    last_seen
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            cursor = self.db.cursor()
            cursor.execute(query, (
                entity['entity_id'],
                entity['entity_type'],
                entity['entity_value'],
                json.dumps(entity['enrichment_metadata']),
                entity['tags'],
                entity['risk_score'],
                entity['enrichment_updated_at'],
                entity['enrichment_updated_by'],
                entity['first_seen'],
                entity['last_seen']
            ))
            self.db.commit()
            cursor.close()

            logger.debug(f"Inserted entity: {entity['entity_type']}:{entity['entity_value']}")

        except Exception as e:
            logger.error(f"Error inserting entity: {e}")
            self.db.rollback()

    async def _update_entity(self, entity: Dict):
        """Update existing entity in entities table"""
        try:
            query = """
                UPDATE entities
                SET enrichment_metadata = %s,
                    tags = %s,
                    risk_score = %s,
                    enrichment_updated_at = %s,
                    enrichment_updated_by = %s,
                    last_seen = %s
                WHERE entity_id = %s
            """

            cursor = self.db.cursor()
            cursor.execute(query, (
                json.dumps(entity['enrichment_metadata']),
                entity['tags'],
                entity['risk_score'],
                entity['enrichment_updated_at'],
                entity['enrichment_updated_by'],
                datetime.utcnow(),
                entity['entity_id']
            ))
            self.db.commit()
            cursor.close()

            logger.debug(f"Updated entity: {entity['entity_type']}:{entity['entity_value']}")

        except Exception as e:
            logger.error(f"Error updating entity: {e}")
            self.db.rollback()

    async def _update_redis_cache(self, entity_type: str, entity_value: str, indicator: Dict, source: str):
        """
        Update Redis cache for real-time enrichment

        NOTE: This is the ONLY place that writes to Redis IOC cache
        One-way: entities table → Redis (NO loop back)
        """
        try:
            cache_key = f"cti:ioc:{entity_type}:{entity_value}"

            # Build cache data
            cache_data = {
                'ioc_type': entity_type,
                'value': entity_value,
                'threat_score': indicator.get('threat_score', 50),
                'tags': indicator.get('tags', []),
                'source': source,
                'campaign': indicator.get('campaign'),
                'threat_actor': indicator.get('threat_actor'),
                'first_seen': indicator.get('first_seen'),
                'cached_at': datetime.utcnow().isoformat()
            }

            # Cache with 24-hour TTL
            self.redis.setex(
                cache_key,
                86400,  # 24 hours
                json.dumps(cache_data)
            )

            # Add to type index
            index_key = f"cti:index:{entity_type}"
            self.redis.sadd(index_key, entity_value)
            self.redis.expire(index_key, 86400)

            logger.debug(f"Cached IOC in Redis: {entity_type}:{entity_value} from {source}")

            # ✨ NEW: Publish IOC update to contextualization engine for real-time enrichment
            self.redis.publish('cti.enrichment.iocs', json.dumps(cache_data))

        except Exception as e:
            logger.error(f"Error updating Redis cache: {e}")

    def get_stats(self) -> Dict[str, int]:
        """Get aggregation statistics"""
        return self.stats.copy()

    async def refresh_cache_from_entities(self):
        """
        Periodic cache refresh: Rebuild Redis from entities table
        One-way: entities → Redis (NO loop back to entities!)
        """
        try:
            logger.info("Starting periodic Redis cache refresh from entities table")

            # Clear existing cache
            ioc_keys = self.redis.keys("cti:ioc:*")
            if ioc_keys:
                self.redis.delete(*ioc_keys)

            index_keys = self.redis.keys("cti:index:*")
            if index_keys:
                self.redis.delete(*index_keys)

            # Fetch high-risk entities
            query = """
                SELECT
                    entity_type,
                    entity_value,
                    enrichment_metadata,
                    tags,
                    risk_score
                FROM entities
                WHERE risk_score > 50
                AND entity_type IN ('ip', 'domain', 'hash', 'md5', 'sha1', 'sha256', 'url', 'email')
                ORDER BY risk_score DESC
                LIMIT 10000
            """

            cursor = self.db.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            cursor.execute(query)
            entities = cursor.fetchall()
            cursor.close()

            # Rebuild cache
            cached_count = 0
            for entity in entities:
                enrichment = entity.get('enrichment_metadata') or {}

                indicator = {
                    'threat_score': entity['risk_score'],
                    'tags': entity.get('tags', []),
                    'campaign': enrichment.get('primary_campaign'),
                    'threat_actor': enrichment.get('primary_threat_actor'),
                    'first_seen': enrichment.get('last_cti_update')
                }

                source = enrichment.get('first_cti_source', 'unknown')

                await self._update_redis_cache(
                    entity['entity_type'],
                    entity['entity_value'],
                    indicator,
                    source
                )
                cached_count += 1

            logger.info(f"Redis cache refresh complete: {cached_count} IOCs cached")

        except Exception as e:
            logger.error(f"Cache refresh failed: {e}", exc_info=True)
