import { create } from 'zustand'
import { entityAPI, caseAPI } from '../api/client'

interface Entity {
  id: string
  type: 'ip' | 'user' | 'hash' | 'domain' | 'file'
  value: string
  first_seen?: string
  last_seen?: string
  risk_score?: number
  tags?: string[]
}

interface Relationship {
  source: string
  target: string
  type: string
  weight: number
  first_seen?: string
  last_seen?: string
}

interface Enrichment {
  entity_id: string
  source: string
  data: Record<string, any>
  timestamp: string
}

interface InvestigationGuide {
  case_id: string
  steps: Array<{
    id: string
    title: string
    description: string
    queries: Record<string, string> // SIEM -> query mapping
    evidence_required: string[]
    completed: boolean
  }>
  generated_at: string
  ai_model: string
}

interface InvestigationState {
  // Current investigation context
  currentCase: any | null
  selectedEntity: Entity | null
  entities: Map<string, Entity>
  relationships: Relationship[]
  enrichments: Map<string, Enrichment[]>
  investigationGuide: InvestigationGuide | null

  // Loading states
  loadingEntity: boolean
  loadingRelationships: boolean
  loadingEnrichment: boolean
  loadingGuide: boolean

  // Actions
  selectEntity: (entityId: string) => Promise<void>
  loadEntityDetails: (entityId: string) => Promise<void>
  loadRelationships: (entityId: string) => Promise<void>
  loadEnrichment: (entityId: string) => Promise<void>
  loadInvestigationGuide: (caseId: string) => Promise<void>
  updateGuideStep: (stepId: string, completed: boolean) => void
  clearInvestigation: () => void
}

export const useInvestigationStore = create<InvestigationState>((set, get) => ({
  // Initial state
  currentCase: null,
  selectedEntity: null,
  entities: new Map(),
  relationships: [],
  enrichments: new Map(),
  investigationGuide: null,
  loadingEntity: false,
  loadingRelationships: false,
  loadingEnrichment: false,
  loadingGuide: false,

  // Select and load entity
  selectEntity: async (entityId: string) => {
    const state = get()

    // Check if entity is already loaded
    const existingEntity = state.entities.get(entityId)
    if (existingEntity) {
      set({ selectedEntity: existingEntity })
      return
    }

    // Load entity details
    await state.loadEntityDetails(entityId)
  },

  // Load entity basic details
  loadEntityDetails: async (entityId: string) => {
    set({ loadingEntity: true })
    try {
      const response = await entityAPI.getEntity(entityId)
      const entity = response.data

      set(state => ({
        selectedEntity: entity,
        entities: new Map(state.entities).set(entityId, entity),
        loadingEntity: false
      }))

      // Load relationships and enrichment in parallel
      const state = get()
      Promise.all([
        state.loadRelationships(entityId),
        state.loadEnrichment(entityId)
      ])
    } catch (error) {
      console.error('Failed to load entity:', error)
      set({ loadingEntity: false })
    }
  },

  // Load entity relationships
  loadRelationships: async (entityId: string) => {
    set({ loadingRelationships: true })
    try {
      const response = await entityAPI.getRelationships(entityId)
      const relationships = response.data.relationships
      const relatedEntities = response.data.entities

      // Add related entities to the store
      set(state => {
        const newEntities = new Map(state.entities)
        relatedEntities.forEach((entity: Entity) => {
          newEntities.set(entity.id, entity)
        })

        return {
          relationships,
          entities: newEntities,
          loadingRelationships: false
        }
      })
    } catch (error) {
      console.error('Failed to load relationships:', error)
      set({ loadingRelationships: false })
    }
  },

  // Load entity enrichment data
  loadEnrichment: async (entityId: string) => {
    set({ loadingEnrichment: true })
    try {
      const response = await entityAPI.getEnrichment(entityId)
      const enrichmentData = response.data

      set(state => ({
        enrichments: new Map(state.enrichments).set(entityId, enrichmentData),
        loadingEnrichment: false
      }))
    } catch (error) {
      console.error('Failed to load enrichment:', error)
      set({ loadingEnrichment: false })
    }
  },

  // Load AI-generated investigation guide
  loadInvestigationGuide: async (caseId: string) => {
    set({ loadingGuide: true })
    try {
      const response = await caseAPI.getInvestigationGuide(caseId)
      set({
        investigationGuide: response.data,
        loadingGuide: false
      })
    } catch (error) {
      console.error('Failed to load investigation guide:', error)
      set({ loadingGuide: false })
    }
  },

  // Update guide step completion
  updateGuideStep: (stepId: string, completed: boolean) => {
    set(state => {
      if (!state.investigationGuide) return state

      const updatedGuide = {
        ...state.investigationGuide,
        steps: state.investigationGuide.steps.map(step =>
          step.id === stepId ? { ...step, completed } : step
        )
      }

      return { investigationGuide: updatedGuide }
    })
  },

  // Clear investigation state
  clearInvestigation: () => {
    set({
      currentCase: null,
      selectedEntity: null,
      entities: new Map(),
      relationships: [],
      enrichments: new Map(),
      investigationGuide: null
    })
  }
}))