@echo off
REM SIEMLess v2.0 - Sync Development to Production Repository
REM Windows version

echo =========================================
echo   SIEMLess - Sync to Production Repo
echo =========================================
echo.

REM Configuration
set DEV_REPO=C:\Users\<USER>\Documents\siemless_v2
set PROD_REPO=C:\Users\<USER>\Documents\siemless-production

REM Check if production repo exists
if not exist "%PROD_REPO%" (
    echo Error: Production repository not found at %PROD_REPO%
    echo Please clone it first:
    echo   <NAME_EMAIL>:crazyguy106/siemless-production.git
    pause
    exit /b 1
)

REM Navigate to dev repo
cd /d "%DEV_REPO%"

REM Check for uncommitted changes
git status --porcelain > temp_status.txt
set /p STATUS=<temp_status.txt
del temp_status.txt

if not "%STATUS%"=="" (
    echo Warning: You have uncommitted changes in development repo
    set /p CONTINUE=Continue anyway? (y/n):
    if /i not "%CONTINUE%"=="y" exit /b 1
)

echo Starting sync from development to production...
echo.

REM Navigate to production repo
cd /d "%PROD_REPO%"

REM Pull latest
echo Pulling latest from production repository...
git pull origin master

REM Create list of files to exclude
echo Creating exclusion list...
(
echo .git
echo documents\archive
echo CLAUDE.md
echo PROJECT_INDEX.md
echo sync_to_production.sh
echo sync_to_production.bat
echo test_*.py
echo test_*.js
echo tests\
echo *_STATUS.md
echo *_REPORT.md
echo *_COMPLETE.md
echo HANDOFF_*.md
echo IMMEDIATE_*.md
echo *.backup
echo __pycache__
echo *.pyc
echo .env
echo .env.local
echo *.log
) > exclude.txt

REM Clear production (except .git and special files)
echo Clearing production directory...
for /d %%i in (*) do (
    if /i not "%%i"==".git" rd /s /q "%%i"
)
for %%i in (*) do (
    if /i not "%%i"=="LICENSE" if /i not "%%i"=="README_COLLABORATORS.md" del /q "%%i"
)

REM Copy files (using robocopy for exclusions)
echo Updating production files...
robocopy "%DEV_REPO%" "%PROD_REPO%" /E /XD .git documents\archive tests __pycache__ /XF CLAUDE.md PROJECT_INDEX.md sync_to_production.* test_*.py test_*.js *_STATUS.md *_REPORT.md *_COMPLETE.md *.backup *.pyc .env .env.local *.log

REM Check if LICENSE exists
if not exist "LICENSE" (
    echo WARNING: LICENSE file missing - please add it
)

if not exist "README_COLLABORATORS.md" (
    echo WARNING: README_COLLABORATORS.md missing - please add it
)

REM Clean up
del exclude.txt 2>nul

REM Show changes
echo.
echo Changes to be committed:
git status --short

REM Commit changes
git add -A

REM Get timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "TIMESTAMP=%dt:~0,4%-%dt:~4,2%-%dt:~6,2% %dt:~8,2%:%dt:~10,2%"

git commit -m "Sync from development: %TIMESTAMP%" -m "Synced from development repository" -m "Excludes: test files, dev documentation, archive"

REM Ask to push
echo.
echo Changes committed locally.
set /p PUSH=Push to remote production repository? (y/n):

if /i "%PUSH%"=="y" (
    git push origin master
    echo.
    echo Successfully synced to production!
) else (
    echo.
    echo Changes committed but not pushed.
    echo To push later: cd %PROD_REPO% ^&^& git push origin master
)

echo.
echo =========================================
echo   Sync Complete
echo =========================================
echo.
echo Production repo: https://github.com/crazyguy106/siemless-production
echo Development continues in: %DEV_REPO%
echo.
pause