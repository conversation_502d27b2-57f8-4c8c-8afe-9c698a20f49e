/**
 * Alert API Service
 * Handles all alert-related API calls
 */

import apiClient from '../client'
import type {
  <PERSON>ert,
  AlertDetail,
  AlertFilters,
  PaginatedResponse,
  APIResponse,
  CreateAlertRequest,
  UpdateAlertRequest
} from '../../types/api'

export const alertService = {
  /**
   * Get paginated list of alerts
   */
  async getAlerts(
    filters?: AlertFilters,
    page: number = 1,
    pageSize: number = 50
  ): Promise<PaginatedResponse<Alert>> {
    const response = await apiClient.get<PaginatedResponse<Alert>>(
      '/alerts',
      {
        params: {
          ...filters,
          page,
          page_size: pageSize
        }
      }
    )
    return response.data
  },

  /**
   * Get detailed alert information
   */
  async getAlertDetail(alertId: string): Promise<AlertDetail> {
    const response = await apiClient.get<APIResponse<AlertDetail>>(
      `/api/alerts/${alertId}`
    )
    return response.data.data
  },

  /**
   * Update alert status or fields
   */
  async updateAlert(
    alertId: string,
    updates: UpdateAlertRequest
  ): Promise<Alert> {
    const response = await apiClient.patch<APIResponse<Alert>>(
      `/api/alerts/${alertId}`,
      updates
    )
    return response.data.data
  },

  /**
   * Create new alert
   */
  async createAlert(alert: CreateAlertRequest): Promise<Alert> {
    const response = await apiClient.post<APIResponse<Alert>>(
      '/alerts',
      alert
    )
    return response.data.data
  },

  /**
   * Assign alert to case
   */
  async assignToCase(alertId: string, caseId: string): Promise<Alert> {
    const response = await apiClient.post<APIResponse<Alert>>(
      `/api/alerts/${alertId}/assign`,
      { case_id: caseId }
    )
    return response.data.data
  },

  /**
   * Get alert history
   */
  async getAlertHistory(params?: any): Promise<Alert[]> {
    const response = await apiClient.get<APIResponse<Alert[]>>(
      '/alerts/history',
      { params }
    )
    return response.data.data
  }
}
