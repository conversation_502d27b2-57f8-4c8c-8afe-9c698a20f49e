"""
SIEMLess v2.0 - Ingestion Engine Message Handler
Coordinates message processing and routing for the ingestion engine
"""

import json
import asyncio
from typing import Dict, Any, Callable, List
from datetime import datetime
import logging


class MessageHandler:
    """Coordinates message processing for ingestion engine"""

    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.handlers = {}
        self.setup_handlers()

    def setup_handlers(self):
        """Initialize message handler registry"""
        # This will be populated by the main engine during initialization
        self.handlers = {}

    def register_handler(self, channel: str, handler: Callable):
        """Register a handler for a specific channel"""
        self.handlers[channel] = handler
        self.logger.debug(f"Registered handler for channel: {channel}")

    def get_subscribed_channels(self) -> List[str]:
        """Return list of Redis channels this engine handles"""
        return [
            'ingestion.start_source',
            'ingestion.stop_source',
            'ingestion.configure_source',
            'ingestion.get_stats',
            'ingestion.sync_github',
            'ingestion.add_github_repo',
            'ingestion.reload_parsers',
            'ingestion.generate_api_docs',
            'ingestion.cti.update',  # CTI update triggers from Backend Scheduler
            'ingestion.pull_context',  # Context queries from Delivery Engine (hosts, detections, etc.)
            'backend.rule.approved'  # Rule deployment triggers from Backend Engine
        ]

    async def process_message(self, message: Dict[str, Any]):
        """Process incoming message from message queue"""
        try:
            data = json.loads(message['data'])
            channel = message['channel']

            self.logger.info(f"Processing message from {channel}")

            # Route to appropriate handler
            if channel in self.handlers:
                handler = self.handlers[channel]
                await handler(data)
            else:
                self.logger.warning(f"No handler registered for channel: {channel}")

        except Exception as e:
            self.logger.error(f"Error processing message: {e}")


class SourceMessageHandler:
    """Handles source management messages"""

    def __init__(self, source_manager, publisher, logger: logging.Logger):
        self.source_manager = source_manager
        self.publisher = publisher
        self.logger = logger

    async def handle_start_source(self, data: Dict[str, Any]):
        """Handle source start request"""
        source_id = data.get('source_id')
        source_type = data.get('source_type', 'elasticsearch')

        if source_id:
            success = self.source_manager.start_source(source_id, source_type)

            if success:
                # Publish confirmation
                self.publisher('ingestion.source_started', {
                    'source_id': source_id,
                    'status': 'started',
                    'timestamp': datetime.utcnow().isoformat()
                })
                self.logger.info(f"Started ingestion source: {source_id}")
            else:
                self.logger.error(f"Failed to start source: {source_id}")

    async def handle_stop_source(self, data: Dict[str, Any]):
        """Handle source stop request"""
        source_id = data.get('source_id')

        if source_id:
            success = self.source_manager.stop_source(source_id)

            if success:
                # Publish confirmation
                self.publisher('ingestion.source_stopped', {
                    'source_id': source_id,
                    'status': 'stopped',
                    'timestamp': datetime.utcnow().isoformat()
                })
                self.logger.info(f"Stopped ingestion source: {source_id}")

    async def handle_configure_source(self, data: Dict[str, Any]):
        """Handle source configuration update"""
        source_type = data.get('source_type')
        config = data.get('config', {})

        if source_type:
            success = self.source_manager.configure_source(source_type, config)

            if success:
                # Publish confirmation
                self.publisher('ingestion.source_configured', {
                    'source_type': source_type,
                    'config': self.source_manager.get_source_config(source_type),
                    'timestamp': datetime.utcnow().isoformat()
                })
                self.logger.info(f"Configured source type: {source_type}")


class GitHubMessageHandler:
    """Handles GitHub-related messages"""

    def __init__(self, github_sync, config_manager, publisher, logger: logging.Logger):
        self.github_sync = github_sync
        self.config_manager = config_manager
        self.publisher = publisher
        self.logger = logger

    async def handle_sync_github(self, data: Dict[str, Any]):
        """Handle manual GitHub sync request"""
        try:
            repository = data.get('repository')

            if repository:
                # Sync specific repository
                result = await self.github_sync.sync_repository(repository)
            else:
                # Sync all repositories
                results = await self.github_sync.sync_all_repositories()
                result = {'repositories': len(results), 'status': 'completed'}

            # Publish result
            self.publisher('ingestion.sync_github_result', result)

        except Exception as e:
            self.logger.error(f"GitHub sync handler error: {e}")

    async def handle_add_github_repo(self, data: Dict[str, Any]):
        """Handle adding a new GitHub repository"""
        try:
            repo_url = data.get('repo_url')
            branch = data.get('branch', 'main')
            sync_interval = data.get('sync_interval', 3600)

            # Add to GitHub sync service
            success = await self.github_sync.add_repository(repo_url, branch, sync_interval)

            if success:
                # Update configuration
                self.config_manager.add_github_repository(repo_url, branch, sync_interval)
                self.logger.info(f"Added GitHub repository: {repo_url}")

                # Publish success
                self.publisher('ingestion.github_repo_added', {
                    'repo_url': repo_url,
                    'status': 'success'
                })
            else:
                # Publish failure
                self.publisher('ingestion.github_repo_added', {
                    'repo_url': repo_url,
                    'status': 'failed'
                })

        except Exception as e:
            self.logger.error(f"Add GitHub repo error: {e}")


class ParserMessageHandler:
    """Handles parser-related messages"""

    def __init__(self, parser_hot_reload, stats_manager, publisher, db_connection, logger: logging.Logger):
        self.parser_hot_reload = parser_hot_reload
        self.stats_manager = stats_manager
        self.publisher = publisher
        self.db_connection = db_connection
        self.logger = logger

    async def handle_reload_parsers(self, data: Dict[str, Any]):
        """Handle parser reload request"""
        try:
            parser_id = data.get('parser_id')

            if parser_id:
                # Reload specific parser
                cursor = self.db_connection.cursor()
                cursor.execute("""
                    SELECT pattern_type, pattern_data
                    FROM pattern_library
                    WHERE pattern_id = %s AND is_active = TRUE
                """, (parser_id,))

                row = cursor.fetchone()
                if row:
                    success = await self.parser_hot_reload.reload_parser(parser_id, row[0], row[1])
                    status = 'success' if success else 'failed'
                else:
                    status = 'not_found'
                cursor.close()
            else:
                # Reload all parsers
                await self.parser_hot_reload.initialize()
                status = 'success'

            # Get current parser status
            parser_status = await self.parser_hot_reload.get_parser_status()
            self.stats_manager.update_stat('patterns_loaded', parser_status['total_parsers'])

            # Publish result
            self.publisher('ingestion.parsers_reloaded', {
                'status': status,
                'total_parsers': parser_status['total_parsers']
            })

        except Exception as e:
            self.logger.error(f"Parser reload error: {e}")


class APIDocMessageHandler:
    """Handles API documentation messages"""

    def __init__(self, api_doc_generator, publisher, logger: logging.Logger):
        self.api_doc_generator = api_doc_generator
        self.publisher = publisher
        self.logger = logger

    async def handle_generate_api_docs(self, data: Dict[str, Any]):
        """Handle API documentation generation request"""
        try:
            format_type = data.get('format', 'json')
            output_path = data.get('output_path')

            # Generate documentation
            spec = await self.api_doc_generator.generate_documentation()

            # Export if path provided
            if output_path:
                await self.api_doc_generator.export_documentation(format_type, output_path)

            # Generate SDKs if requested
            if data.get('generate_sdks'):
                python_sdk = await self.api_doc_generator.generate_client_sdk('python')
                js_sdk = await self.api_doc_generator.generate_client_sdk('javascript')

                # Save SDKs
                with open('siemless_client.py', 'w') as f:
                    f.write(python_sdk)
                with open('siemless_client.js', 'w') as f:
                    f.write(js_sdk)

            # Publish result
            self.publisher('ingestion.api_docs_generated', {
                'status': 'success',
                'endpoints': len(spec.get('paths', {})),
                'schemas': len(spec.get('components', {}).get('schemas', {}))
            })

        except Exception as e:
            self.logger.error(f"API doc generation error: {e}")


class StatsMessageHandler:
    """Handles statistics-related messages"""

    def __init__(self, stats_manager, source_manager, config_manager, publisher, logger: logging.Logger):
        self.stats_manager = stats_manager
        self.source_manager = source_manager
        self.config_manager = config_manager
        self.publisher = publisher
        self.logger = logger

    async def handle_get_stats(self, data: Dict[str, Any]):
        """Handle statistics request"""
        stats = {
            'engine': 'ingestion',
            'stats': self.stats_manager.get_all_stats(),
            'active_sources': self.source_manager.get_active_sources(),
            'source_configs': self.config_manager.get_all_source_configs(),
            'timestamp': datetime.utcnow().isoformat()
        }

        # Publish statistics
        self.publisher('ingestion.stats_response', stats)