# CTI Data Flow Analysis - Finding Duplication & Inefficiencies

## Current State Analysis

### Data Sources We Have:

**1. External CTI Feeds** (Ingestion Engine)
- OTX (AlienVault)
- OpenCTI
- ThreatFox
- **Status**: Being fetched and routed through CTI Data Router ✅

**2. CrowdStrike CTI Scopes** (INTEL_READ, IOCS_READ)
- Threat intel indicators
- Malware families
- Threat actors
- **Status**: ❓ Need to verify current flow

**3. Entities Table CTI** (Database)
- Enriched entities from all sources
- CrowdStrike intel metadata
- Risk scores, tags
- **Status**: ❓ Potential duplication

## The Critical Questions:

### Question 1: Is existing CTI able to enrich our data?

Let me trace the flow:

#### Current Flow (What We Just Built):
```
External CTI (OTX/OpenCTI/ThreatFox)
  → Ingestion (CTI Manager)
    → CTI Data Router
      → cti.enrichment.iocs channel
        → Contextualization (CTI Enrichment Pipeline)
          → Cache in Redis
            → Enrich incoming logs ✅
```

**Answer**: YES, external CTI can enrich through the new pipeline.

#### CrowdStrike CTI Flow (Unclear):
```
CrowdStrike INTEL scope
  → ❓ Where does this go?
    → entities table?
    → Or direct enrichment?
```

**Need to check**: Are CrowdStrike CTI scopes currently active and enriching?

### Question 2: Does it cause duplication?

**Potential Duplication Scenarios**:

#### Scenario A: Same IOC, Multiple Sources
```
OTX says: ******* is malicious (threat_score: 80)
CrowdStrike INTEL says: ******* is malicious (threat_score: 90)
ThreatFox says: ******* is malicious (threat_score: 85)

Result: Same IOC stored 3 times in Redis cache ❌
```

#### Scenario B: Entity Table → CTI Router Loop
```
CrowdStrike CTI → entities table (enrichment_metadata)
  → Entity CTI Extractor reads entities
    → Routes back through CTI Router
      → Goes to enrichment cache
        → Used to enrich logs
          → Creates entities ❌ LOOP!
```

#### Scenario C: Database vs Redis Duplication
```
CTI stored in:
1. Redis cache (cti:ioc:*) - for fast enrichment
2. entities table (enrichment_metadata) - for persistence
3. Backend rules table - for detection

Same data, 3 locations! ❌
```

### Question 3: How would it work better?

## Proposed Optimal Architecture

### Design Principle: **Single Source of Truth with Derived Caches**

```
┌─────────────────────────────────────────────────────────────┐
│                   SINGLE SOURCE OF TRUTH                     │
│                  entities table (PostgreSQL)                 │
│                                                              │
│  All CTI data stored here with:                             │
│  - entity_type (ip, domain, hash, etc.)                     │
│  - enrichment_metadata (CTI details)                        │
│  - Multiple sources merged (OTX + CrowdStrike + ThreatFox)  │
│  - Highest threat score wins                                │
│  - Combined tags from all sources                           │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                    DERIVED CACHES                            │
│                                                              │
│  1. Redis IOC Cache (fast enrichment)                       │
│     - TTL: 24 hours                                         │
│     - Rebuilt from entities table                           │
│     - Purpose: Real-time log enrichment                     │
│                                                              │
│  2. Rule Patterns (PostgreSQL detection_rules)              │
│     - Generated from high-risk entities                     │
│     - Purpose: Detection                                    │
│                                                              │
│  3. Investigation Context (PostgreSQL + Redis)              │
│     - Threat actor/campaign mappings                        │
│     - Purpose: Case enrichment                              │
└─────────────────────────────────────────────────────────────┘
```

### Data Flow (No Duplication):

#### Step 1: CTI Ingestion (All Sources → entities table)
```
┌─────────────────┐
│  External CTI   │
│  - OTX          │
│  - OpenCTI      │
│  - ThreatFox    │
└─────────────────┘
         ↓
┌─────────────────┐
│ CrowdStrike CTI │
│  - INTEL scope  │
│  - IOCS scope   │
└─────────────────┐
         ↓
    ┌────────────────────────────┐
    │  CTI Aggregator/Merger     │
    │                            │
    │  Merges duplicate IOCs:    │
    │  - Same IP from OTX + CS   │
    │  - Takes highest score     │
    │  - Combines all tags       │
    │  - Merges all sources      │
    └────────────────────────────┘
                ↓
    ┌────────────────────────────┐
    │    entities table          │
    │                            │
    │  UPSERT (no duplicates):   │
    │  - entity_value = '*******'│
    │  - threat_score = MAX(all) │
    │  - tags = UNION(all)       │
    │  - sources = [otx, cs, tf] │
    └────────────────────────────┘
```

#### Step 2: Cache Building (entities → Redis, NO router loop)
```
entities table
  ↓
  Query: SELECT * FROM entities WHERE risk_score > 50
  ↓
  Build Redis cache:
    - cti:ioc:ip:*******
    - cti:ioc:domain:evil.com
  ↓
  Contextualization uses cache for enrichment
  (NO feedback loop to entities table!)
```

#### Step 3: Rule Generation (entities → rules, one-way)
```
entities table (high-risk entities)
  ↓
  Backend: Generate rules from entities WHERE risk_score > 70
  ↓
  detection_rules table
  (NO feedback loop!)
```

## The Problems with Current Architecture:

### Problem 1: CTI Router Creates Duplicates
```
CURRENT (BAD):
OTX → CTI Router → 4 channels → All write to different places
CrowdStrike → CTI Router → 4 channels → DUPLICATES OTX data!
Entity Extractor → CTI Router → 4 channels → DUPLICATES EVERYTHING!

RESULT: Same IOC in Redis 3+ times with different keys!
```

### Problem 2: No Deduplication Logic
```
CURRENT (BAD):
OTX: ******* (score: 80) → Redis: cti:ioc:ip:******* = {score: 80, source: otx}
CS:  ******* (score: 90) → Redis: cti:ioc:ip:******* = {score: 90, source: cs} ← OVERWRITES!

RESULT: Lost OTX data, only CS data remains
```

### Problem 3: Feedback Loops
```
CURRENT (BAD):
CTI → entities table
  → Entity Extractor reads entities
    → Sends through CTI Router AGAIN
      → Goes to enrichment
        → Creates more entities
          → Entity Extractor reads them AGAIN ← INFINITE LOOP!
```

## Better Architecture Design:

### Flow 1: CTI Ingestion (Merge First, Route Once)

```python
class CTIAggregator:
    """
    Single aggregator for ALL CTI sources
    Merges duplicates BEFORE routing
    """

    async def ingest_cti(self, cti_data: Dict, source: str):
        # 1. Merge with existing entity data (deduplication)
        entity = await self._merge_with_existing_entity(cti_data, source)

        # 2. Store merged entity (single source of truth)
        await self._store_entity(entity)

        # 3. Update derived caches ONLY if needed
        if entity['risk_score'] > 50:
            await self._update_redis_cache(entity)

        # 4. Generate rules ONLY if high risk AND new
        if entity['risk_score'] > 70 and entity['is_new']:
            await self._generate_detection_rule(entity)
```

### Flow 2: Enrichment (Read-Only from Cache)

```python
class CTIEnrichment:
    """
    Enrichment reads from Redis cache ONLY
    NEVER writes back to entities table
    """

    async def enrich_log(self, log_data: Dict):
        # Read from cache
        matches = await self._check_redis_cache(log_data)

        # Enrich log in-memory
        enriched = log_data.copy()
        enriched['cti_matches'] = matches

        # Return enriched log
        # DO NOT create new entities!
        return enriched
```

### Flow 3: Cache Refresh (Scheduled, One-Way)

```python
class CacheRefresher:
    """
    Periodically rebuild cache from entities table
    One-way: entities → Redis (no loop back)
    """

    async def refresh_cache(self):
        # Clear old cache
        await self._clear_redis_ioc_cache()

        # Rebuild from entities
        entities = await db.fetch("""
            SELECT * FROM entities
            WHERE risk_score > 50
            AND entity_type IN ('ip', 'domain', 'hash')
        """)

        for entity in entities:
            await self._cache_entity_ioc(entity)
```

## Recommendations:

### 1. Stop Entity CTI Extractor from Routing
**Current**: Entity Extractor → CTI Router → Channels
**Better**: Entity Extractor → Direct cache update (no routing)

### 2. Add CTI Deduplication Layer
**Create**: `CTIAggregator` class
**Purpose**: Merge CTI from all sources before storage
**Location**: Before entities table insert

### 3. Separate CrowdStrike CTI from EDR Logs
**INTEL/IOCS scopes**: Treat as CTI feeds (like OTX)
**Event Streams scope**: Treat as log ingestion

### 4. Make Enrichment Read-Only
**Enrichment**: ONLY reads from Redis cache
**Enrichment**: NEVER creates entities (no loop)

### 5. Scheduled Cache Rebuild
**Every 15 minutes**: Rebuild Redis cache from entities table
**One-way**: entities → Redis (no feedback)

## Next Steps:

1. ✅ Keep CTI Router for external feeds (OTX, OpenCTI, ThreatFox)
2. ❌ Remove Entity CTI Extractor routing (causes duplication)
3. ✅ Add CTI Aggregator for deduplication
4. ✅ Make CrowdStrike INTEL/IOCS a CTI connector
5. ✅ Make enrichment read-only (no entity creation)

Would you like me to implement the CTI Aggregator and fix these issues?
