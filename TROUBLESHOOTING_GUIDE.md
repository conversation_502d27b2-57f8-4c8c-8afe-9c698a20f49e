# SIEMLess v2.0 - Troubleshooting Guide

## Quick Diagnostics

### System Health Check
```bash
# Run comprehensive health check
cd engines
./health_check.sh

# Quick container status
docker-compose ps

# Check engine connectivity
for port in ************** 8004 8005; do
    echo -n "Port $port: "
    curl -s http://localhost:$port/health && echo "✅ OK" || echo "❌ FAIL"
done
```

### Common Issues and Solutions

## 1. HTTP Health Endpoints Not Responding

### Symptom
- `curl http://localhost:8001/health` returns connection refused
- Containers show as "unhealthy" in `docker-compose ps`
- Health check endpoints timeout or fail

### Root Cause
**Synchronous blocking operations in async context preventing HTTP server startup**

### Quick Fix
```bash
# The issue is in base_engine.py - synchronous Redis blocking the event loop
# Fix has been applied - ensure you have the latest base_engine.py

# Verify the fix is in place
docker-compose exec intelligence_engine grep -n "redis.asyncio" base_engine.py

# If missing, rebuild with fixed version
cd engines
for dir in intelligence ingestion contextualization delivery backend; do
    cp base_engine.py $dir/base_engine.py
done
docker-compose up --build -d
```

### Detailed Solution
The `_message_loop()` was using synchronous Redis operations that blocked the event loop:
- **Problem**: `message = pubsub.get_message(timeout=1)` blocks for 1 second
- **Solution**: Use `redis.asyncio` for async operations
- **See**: `ASYNC_FIX_DOCUMENTATION.md` for complete details

### Verification
```bash
# Test all health endpoints
for port in ************** 8004 8005; do
    echo -n "Port $port: "
    curl -s http://localhost:$port/health | jq -r '.status' || echo "FAILED"
done
```

## 2. Pattern Matching Issues

### Symptom: "0 matched (0.0%), X unknown"
```bash
# Check current logs
docker-compose logs ingestion_engine | grep "Processed"
```

**Root Cause**: Pattern library not loading or patterns not matching log structure

**Solution**:
```bash
# 1. Check pattern library
docker-compose exec postgres psql -U siemless -d siemless_v2 -c "
SELECT pattern_id, is_active, pattern_data->'regex' as regex
FROM pattern_library
WHERE is_active = true;"

# 2. Verify pattern compilation
docker-compose exec ingestion_engine python -c "
import sys
sys.path.append('/app')
from pattern_matcher import PatternMatcher
import psycopg2
import logging

logger = logging.getLogger('test')
conn = psycopg2.connect(
    host='postgres', port=5432,
    database='siemless_v2', user='siemless', password='siemless123'
)

class DummyRedis: pass
matcher = PatternMatcher(conn, DummyRedis(), logger)
print(f'Loaded {len(matcher.patterns)} patterns')
for pid in matcher.patterns:
    print(f'  - {pid}')
"

# 3. Restart ingestion engine if needed
docker-compose restart ingestion_engine
```

### Symptom: "Failed to compile pattern"
**Root Cause**: Invalid regex in pattern_data

**Solution**:
```sql
-- Check for invalid patterns
docker-compose exec postgres psql -U siemless -d siemless_v2 -c "
SELECT pattern_id, pattern_data
FROM pattern_library
WHERE is_active = true;"

-- Fix invalid regex patterns
UPDATE pattern_library
SET pattern_data = '{"regex": "simple_regex", "entity_extractors": {}}'
WHERE pattern_id = 'problematic_pattern';
```

## 2. Database Connection Issues

### Symptom: "Database connection failed"
```bash
# Check database logs
docker-compose logs postgres | tail -20
```

**Common Causes**:

1. **Wrong credentials**:
```bash
# Check environment variables
docker-compose exec ingestion_engine env | grep POSTGRES
```

2. **Database doesn't exist**:
```bash
# List databases
docker-compose exec postgres psql -U siemless -c "\l"

# Create database if missing
docker-compose exec postgres createdb -U siemless siemless_v2
```

3. **Connection timeout**:
```bash
# Test connectivity
docker-compose exec ingestion_engine ping postgres
```

### Symptom: "column does not exist"
**Root Cause**: Schema mismatch between application and database

**Solution**:
```bash
# Check table schema
docker-compose exec postgres psql -U siemless -d siemless_v2 -c "
\d pattern_library
\d warm_storage
\d ingestion_logs"

# Compare with expected schema in SCHEMA_PATTERN_MERMAID.md
```

## 3. Redis Communication Issues

### Symptom: "Redis connection failed"
```bash
# Test Redis connectivity
docker-compose exec redis redis-cli ping

# Check Redis logs
docker-compose logs redis | tail -10
```

**Solution**:
```bash
# Restart Redis if needed
docker-compose restart redis

# Check Redis configuration
docker-compose exec redis redis-cli CONFIG GET "*"
```

### Symptom: Messages not being processed
```bash
# Monitor Redis channels
docker-compose exec redis redis-cli MONITOR

# Check message publication
docker-compose exec redis redis-cli PUBLISH test_channel "test_message"
```

## 4. API Integration Issues

### Symptom: "No credentials found"
```bash
# Check environment variables in container
docker-compose exec ingestion_engine env | grep -E "CROWDSTRIKE|ELASTIC|OPENCTI"
```

**Solution**:
```bash
# Update .env file
cd engines
nano .env

# Add missing credentials
CROWDSTRIKE_CLIENT_ID=your_client_id
CROWDSTRIKE_CLIENT_SECRET=your_client_secret
ELASTIC_URL=your_elastic_url
ELASTIC_API_KEY=your_api_key

# Restart affected engines
docker-compose restart ingestion_engine
```

### Symptom: "API authentication failed"
```bash
# Test API connectivity manually
docker-compose exec ingestion_engine python test_api_connectivity.py
```

**Solution**:
```bash
# Verify credentials are correct
# Check API endpoint URLs
# Ensure network access to API endpoints
```

### Symptom: "falconpy library not installed"
```bash
# Check if library is missing
docker-compose exec ingestion_engine pip list | grep falcon
```

**Solution**:
```bash
# Add to requirements.txt
echo "crowdstrike-falconpy>=1.3.0" >> engines/ingestion/requirements.txt

# Rebuild container
docker-compose build ingestion_engine
docker-compose up ingestion_engine -d
```

## 5. Storage and Performance Issues

### Symptom: High memory usage
```bash
# Check container memory usage
docker stats --no-stream

# Check PostgreSQL connections
docker-compose exec postgres psql -U siemless -d siemless_v2 -c "
SELECT count(*) as active_connections
FROM pg_stat_activity
WHERE state = 'active';"
```

**Solution**:
```bash
# Limit container memory in docker-compose.yml
services:
  intelligence_engine:
    deploy:
      resources:
        limits:
          memory: 4G

# Tune PostgreSQL settings
docker-compose exec postgres psql -U siemless -d siemless_v2 -c "
ALTER SYSTEM SET shared_buffers = '2GB';
ALTER SYSTEM SET effective_cache_size = '6GB';
SELECT pg_reload_conf();"
```

### Symptom: Slow database queries
```sql
-- Check slow queries
docker-compose exec postgres psql -U siemless -d siemless_v2 -c "
SELECT query, mean_exec_time, calls
FROM pg_stat_statements
ORDER BY mean_exec_time DESC
LIMIT 10;"

-- Check table sizes
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## 6. Docker and Container Issues

### Symptom: "Container keeps restarting"
```bash
# Check container logs
docker-compose logs [service_name] | tail -50

# Check exit codes
docker-compose ps
```

**Common Solutions**:

1. **Memory limits exceeded**:
```yaml
# Increase memory limits in docker-compose.yml
deploy:
  resources:
    limits:
      memory: 8G
```

2. **Health check failures**:
```bash
# Disable health checks temporarily
# Comment out healthcheck in docker-compose.yml
# healthcheck:
#   test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
```

3. **Port conflicts**:
```bash
# Check port usage
netstat -tulpn | grep -E "8001|8002|8003|8004|8005|5433|6380"

# Change ports in docker-compose.yml if conflicts exist
```

### Symptom: "Build failures"
```bash
# Clean build
docker-compose down
docker system prune -a
docker-compose build --no-cache
```

## 7. AI Model Integration Issues

### Symptom: "API key invalid" or "Rate limit exceeded"
```bash
# Check API key format
docker-compose exec intelligence_engine env | grep -E "OPENAI|ANTHROPIC|GOOGLE"
```

**Solution**:
```bash
# Verify API keys are valid and have sufficient credits
# Implement rate limiting and retry logic
# Use multiple API keys for load distribution
```

### Symptom: "Model not available"
```bash
# Check available models
docker-compose exec intelligence_engine python -c "
import openai
client = openai.OpenAI()
models = client.models.list()
for model in models.data:
    print(model.id)
"
```

## 8. Performance Monitoring

### Real-time Monitoring Commands
```bash
# Monitor log processing rate
watch 'docker-compose exec postgres psql -U siemless -d siemless_v2 -c "
SELECT
    COUNT(*) as total_logs,
    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '1 hour') as last_hour
FROM ingestion_logs;"'

# Monitor Redis activity
docker-compose exec redis redis-cli MONITOR

# Monitor container resources
watch docker stats --no-stream
```

### Database Performance Monitoring
```sql
-- Active connections
SELECT count(*) as connections, state
FROM pg_stat_activity
GROUP BY state;

-- Lock monitoring
SELECT
    l.pid,
    l.mode,
    l.granted,
    a.query
FROM pg_locks l
JOIN pg_stat_activity a ON l.pid = a.pid
WHERE NOT l.granted;

-- Cache hit ratio
SELECT
    schemaname,
    tablename,
    heap_blks_read,
    heap_blks_hit,
    round(heap_blks_hit*100.0/(heap_blks_hit+heap_blks_read),2) as cache_hit_ratio
FROM pg_statio_user_tables
WHERE heap_blks_read > 0;
```

## 9. Emergency Procedures

### Complete System Restart
```bash
# Graceful restart
docker-compose down
docker-compose up -d

# Force restart (if containers are stuck)
docker-compose kill
docker-compose rm -f
docker-compose up -d
```

### Database Recovery
```bash
# Restore from backup
docker-compose exec postgres psql -U siemless -c "DROP DATABASE IF EXISTS siemless_v2;"
docker-compose exec postgres psql -U siemless -c "CREATE DATABASE siemless_v2;"
docker-compose exec postgres psql -U siemless -d siemless_v2 < /backups/latest_backup.sql
```

### Configuration Reset
```bash
# Reset to default configuration
git checkout HEAD -- engines/.env
git checkout HEAD -- engines/docker-compose.yml

# Rebuild with clean configuration
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

## 10. Debug Mode

### Enable Debug Logging
```bash
# Add to .env file
LOG_LEVEL=DEBUG

# Restart engines
docker-compose restart
```

### Detailed Debugging
```python
# Test individual components
docker-compose exec ingestion_engine python -c "
import sys
sys.path.append('/app')
from ingestion_engine import IngestionEngine
import logging

logging.basicConfig(level=logging.DEBUG)
engine = IngestionEngine()

# Test pattern matching
sample_log = engine._generate_sample_log('fortinet')
result = engine.pattern_matcher.match(sample_log)
print('Pattern match result:', result)
"
```

## Contact Information

### Support Channels
- **GitHub Issues**: https://github.com/crazyguy106/siemless_v2/issues
- **Documentation**: See CLAUDE.md and README.md
- **Emergency Contact**: Check repository contacts

### Useful Commands Reference
```bash
# Quick status check
docker-compose ps && docker-compose logs --tail=5 | grep ERROR

# Resource usage
docker stats --no-stream

# Database quick check
docker-compose exec postgres psql -U siemless -d siemless_v2 -c "
SELECT 'ingestion_logs' as table, count(*) as records FROM ingestion_logs
UNION ALL
SELECT 'pattern_library', count(*) FROM pattern_library WHERE is_active = true
UNION ALL
SELECT 'warm_storage', count(*) FROM warm_storage;"

# Redis health
docker-compose exec redis redis-cli ping && echo "Redis OK"
```

---

**SIEMLess v2.0 Troubleshooting Guide**
**Version**: 1.0
**Date**: September 27, 2025
**Status**: Production Ready ✅