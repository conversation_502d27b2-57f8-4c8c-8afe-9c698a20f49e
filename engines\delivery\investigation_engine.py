"""
Auto-Investigation Dashboard Engine
Creates and manages automated security investigations

Workflow:
1. <PERSON><PERSON> received → Create investigation
2. Auto-enrich with threat intel + MITRE
3. Extract entities and relationships
4. Build timeline
5. Score risk
6. Present in dashboard
"""

import asyncio
import json
import uuid
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import logging
import redis.asyncio as redis_async


@dataclass
class Investigation:
    """Investigation structure"""
    id: str
    title: str
    severity: str
    status: str  # 'open', 'investigating', 'closed'
    created_at: datetime
    updated_at: datetime
    alert_ids: List[str]
    entities: Dict  # IPs, users, hosts, etc.
    mitre_techniques: List[str]
    threat_intel: List[Dict]
    timeline: List[Dict]
    risk_score: int
    assignee: Optional[str] = None
    tags: List[str] = None


@dataclass
class TimelineEvent:
    """Timeline event for investigation"""
    timestamp: datetime
    event_type: str
    source: str
    description: str
    entities: Dict
    raw_data: Dict


class InvestigationEngine:
    """
    Auto-Investigation Engine
    - Creates investigations from alerts
    - Auto-enriches with context
    - Builds timelines
    - Scores risk
    - Provides dashboard API
    """

    def __init__(self, redis_client, db_connection, logger: Optional[logging.Logger] = None):
        self.redis = redis_client
        self.db = db_connection
        self.logger = logger or logging.getLogger(__name__)

        # Active investigations cache
        self.active_investigations = {}

    async def start(self):
        """Start investigation engine"""
        # Subscribe to investigation triggers
        pubsub = self.redis.pubsub()
        await pubsub.subscribe('investigation.create', 'ingestion.alerts.received')

        self.logger.info("Investigation Engine started")

        # Process messages
        async for message in pubsub.listen():
            if message['type'] == 'message':
                channel = message['channel'].decode('utf-8')
                data = json.loads(message['data'])

                if channel == 'investigation.create':
                    await self.create_investigation_from_trigger(data)
                elif channel == 'ingestion.alerts.received':
                    # Auto-create for high/critical
                    if data.get('severity') in ['high', 'critical']:
                        await self.create_investigation_from_alert(data)

    async def create_investigation_from_alert(self, alert_data: Dict) -> Investigation:
        """Create investigation from SIEM alert"""
        try:
            investigation_id = str(uuid.uuid4())

            investigation = Investigation(
                id=investigation_id,
                title=f"Investigation: {alert_data.get('title', 'Unknown')}",
                severity=alert_data.get('severity', 'medium'),
                status='open',
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                alert_ids=[alert_data.get('alert_id')],
                entities=alert_data.get('entities', {}),
                mitre_techniques=alert_data.get('mitre_techniques', []),
                threat_intel=[],
                timeline=[],
                risk_score=0,
                tags=[]
            )

            # Auto-enrich investigation
            await self._enrich_investigation(investigation, alert_data)

            # Store in Redis (active investigations)
            await self.redis.setex(
                f'investigation:{investigation_id}',
                86400,  # 24 hours
                json.dumps(asdict(investigation), default=str)
            )

            # Store in PostgreSQL (permanent)
            await self._store_investigation(investigation)

            # Publish investigation created event
            await self.redis.publish('investigation.created', json.dumps({
                'investigation_id': investigation_id,
                'title': investigation.title,
                'severity': investigation.severity
            }))

            self.logger.info(f"Investigation created: {investigation_id} - {investigation.title}")

            return investigation

        except Exception as e:
            self.logger.error(f"Failed to create investigation: {e}")
            raise

    async def create_investigation_from_trigger(self, trigger_data: Dict) -> Investigation:
        """Create investigation from manual trigger"""
        return await self.create_investigation_from_alert(trigger_data)

    async def _enrich_investigation(self, investigation: Investigation, alert_data: Dict):
        """Auto-enrich investigation with context"""
        try:
            # 1. Enrich with threat intelligence
            await self._enrich_threat_intel(investigation)

            # 2. Enrich with MITRE context
            await self._enrich_mitre_context(investigation)

            # 3. Build timeline
            await self._build_timeline(investigation, alert_data)

            # 4. Calculate risk score
            investigation.risk_score = self._calculate_risk_score(investigation)

            # 5. Extract additional entities from graph
            await self._enrich_entities_from_graph(investigation)

            investigation.updated_at = datetime.utcnow()

        except Exception as e:
            self.logger.error(f"Enrichment failed: {e}")

    async def _enrich_threat_intel(self, investigation: Investigation):
        """Enrich investigation with threat intelligence"""
        try:
            # Basic threat intel enrichment (simplified for now)
            # Future: Query actual CTI systems
            investigation.threat_intel = []

        except Exception as e:
            self.logger.error(f"Threat intel enrichment failed: {e}")

    async def _enrich_mitre_context(self, investigation: Investigation):
        """Enrich with MITRE ATT&CK context"""
        try:
            # Simplified enrichment - just log for now
            # Future: Query MITRE mapper for full technique details
            if investigation.mitre_techniques:
                self.logger.debug(f"MITRE techniques present: {len(investigation.mitre_techniques)}")

        except Exception as e:
            self.logger.error(f"MITRE enrichment failed: {e}")

    async def _build_timeline(self, investigation: Investigation, alert_data: Dict):
        """Build investigation timeline"""
        timeline = []

        # Add alert as first event
        timeline.append({
            'timestamp': alert_data.get('timestamp', datetime.utcnow().isoformat()),
            'event_type': 'alert',
            'source': alert_data.get('source_siem', 'unknown'),
            'description': alert_data.get('title', 'Alert received'),
            'severity': alert_data.get('severity'),
            'entities': alert_data.get('entities', {}),
            'raw_data': alert_data.get('raw_alert', {})
        })

        # Query for related events (from contextualization engine)
        # This would query PostgreSQL for events involving same entities

        investigation.timeline = timeline

    def _calculate_risk_score(self, investigation: Investigation) -> int:
        """Calculate risk score (0-100)"""
        score = 0

        # Base severity score
        severity_scores = {
            'low': 20,
            'medium': 40,
            'high': 70,
            'critical': 90
        }
        score += severity_scores.get(investigation.severity, 40)

        # Threat intel matches (+10 each, max +30)
        score += min(len(investigation.threat_intel) * 10, 30)

        # MITRE technique count (+2 each, max +20)
        score += min(len(investigation.mitre_techniques) * 2, 20)

        # Entity count (more entities = potentially wider impact)
        total_entities = sum(len(v) for v in investigation.entities.values())
        score += min(total_entities, 10)

        # Cap at 100
        return min(score, 100)

    async def _enrich_entities_from_graph(self, investigation: Investigation):
        """Enrich entities using graph database"""
        try:
            # Query Apache AGE for related entities
            # This would use the graph DB to find connected entities
            pass
        except Exception as e:
            self.logger.error(f"Graph enrichment failed: {e}")

    async def _store_investigation(self, investigation: Investigation):
        """Store investigation in PostgreSQL"""
        if not self.db:
            return

        try:
            # Use synchronous DB operations (delivery engine passes sync connection)
            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO investigations
                (id, title, severity, status, created_at, updated_at,
                 alert_ids, entities, mitre_techniques, threat_intel,
                 timeline, risk_score, assignee, tags)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (id) DO UPDATE SET
                    updated_at = EXCLUDED.updated_at,
                    status = EXCLUDED.status,
                    risk_score = EXCLUDED.risk_score,
                    threat_intel = EXCLUDED.threat_intel,
                    timeline = EXCLUDED.timeline
            """, (
                investigation.id,
                investigation.title,
                investigation.severity,
                investigation.status,
                investigation.created_at,
                investigation.updated_at,
                json.dumps(investigation.alert_ids),
                json.dumps(investigation.entities),
                json.dumps(investigation.mitre_techniques),
                json.dumps(investigation.threat_intel, default=str),
                json.dumps(investigation.timeline, default=str),
                investigation.risk_score,
                investigation.assignee,
                json.dumps(investigation.tags) if investigation.tags else None
            ))
            self.db.commit()
            cursor.close()

        except Exception as e:
            self.logger.error(f"Failed to store investigation: {e}")

    # Dashboard API Methods
    async def get_investigation(self, investigation_id: str) -> Optional[Investigation]:
        """Get investigation by ID"""
        try:
            # Try Redis first (active investigations)
            data = await self.redis.get(f'investigation:{investigation_id}')
            if data:
                inv_dict = json.loads(data)
                return Investigation(**inv_dict)

            # Fallback to PostgreSQL
            if self.db:
                cursor = self.db.cursor()
                cursor.execute("""
                    SELECT id, title, severity, status, created_at, updated_at,
                           alert_ids, entities, mitre_techniques, threat_intel,
                           timeline, risk_score, assignee, tags
                    FROM investigations
                    WHERE id = %s
                """, (investigation_id,))

                row = cursor.fetchone()
                if row:
                    return Investigation(
                        id=row[0],
                        title=row[1],
                        severity=row[2],
                        status=row[3],
                        created_at=row[4],
                        updated_at=row[5],
                        alert_ids=json.loads(row[6]) if row[6] else [],
                        entities=json.loads(row[7]) if row[7] else {},
                        mitre_techniques=json.loads(row[8]) if row[8] else [],
                        threat_intel=json.loads(row[9]) if row[9] else [],
                        timeline=json.loads(row[10]) if row[10] else [],
                        risk_score=row[11],
                        assignee=row[12],
                        tags=json.loads(row[13]) if row[13] else []
                    )

            return None

        except Exception as e:
            self.logger.error(f"Failed to get investigation: {e}")
            return None

    async def list_investigations(self, status: Optional[str] = None, limit: int = 50) -> List[Investigation]:
        """List investigations"""
        investigations = []

        try:
            if self.db:
                cursor = self.db.cursor()

                if status:
                    cursor.execute("""
                        SELECT id, title, severity, status, created_at, updated_at,
                               risk_score
                        FROM investigations
                        WHERE status = %s
                        ORDER BY created_at DESC
                        LIMIT %s
                    """, (status, limit))
                else:
                    cursor.execute("""
                        SELECT id, title, severity, status, created_at, updated_at,
                               risk_score
                        FROM investigations
                        ORDER BY created_at DESC
                        LIMIT %s
                    """, (limit,))

                for row in cursor.fetchall():
                    # Return partial Investigation for list view
                    investigations.append({
                        'id': row[0],
                        'title': row[1],
                        'severity': row[2],
                        'status': row[3],
                        'created_at': row[4].isoformat() if row[4] else None,
                        'updated_at': row[5].isoformat() if row[5] else None,
                        'risk_score': row[6]
                    })

        except Exception as e:
            self.logger.error(f"Failed to list investigations: {e}")

        return investigations

    async def update_investigation(self, investigation_id: str, updates: Dict):
        """Update investigation"""
        try:
            investigation = await self.get_investigation(investigation_id)
            if not investigation:
                return False

            # Apply updates
            for key, value in updates.items():
                if hasattr(investigation, key):
                    setattr(investigation, key, value)

            investigation.updated_at = datetime.utcnow()

            # Update Redis
            await self.redis.setex(
                f'investigation:{investigation_id}',
                86400,
                json.dumps(asdict(investigation), default=str)
            )

            # Update PostgreSQL
            await self._store_investigation(investigation)

            return True

        except Exception as e:
            self.logger.error(f"Failed to update investigation: {e}")
            return False

    async def close_investigation(self, investigation_id: str, resolution: str):
        """Close investigation"""
        return await self.update_investigation(investigation_id, {
            'status': 'closed',
            'tags': ['resolution:' + resolution]
        })

    async def assign_investigation(self, investigation_id: str, assignee: str):
        """Assign investigation to analyst"""
        return await self.update_investigation(investigation_id, {
            'assignee': assignee,
            'status': 'investigating'
        })
