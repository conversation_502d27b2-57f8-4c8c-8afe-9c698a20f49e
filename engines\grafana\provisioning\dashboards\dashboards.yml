apiVersion: 1

providers:
  # Backend Monitoring Dashboards
  - name: 'Backend Monitoring'
    orgId: 1
    folder: 'Backend Monitoring'
    folderUid: backend-monitoring
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/backend

  # Security Operations Dashboards
  - name: 'Security Operations'
    orgId: 1
    folder: 'Security Operations'
    folderUid: security-ops
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/security

  # Investigation Dashboards
  - name: 'Investigation & Triage'
    orgId: 1
    folder: 'Investigation & Triage'
    folderUid: investigation
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/investigation

  # Engineering Dashboards
  - name: 'Detection Engineering'
    orgId: 1
    folder: 'Detection Engineering'
    folderUid: engineering
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/engineering

  # ROI & Performance Dashboards
  - name: 'Performance & ROI'
    orgId: 1
    folder: 'Performance & ROI'
    folderUid: performance
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards/performance