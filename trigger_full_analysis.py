"""
Manual Trigger Script: Analyze All Stored Data

This script triggers complete analysis of all data in storage:
1. Extract trapped entities from warm_storage
2. Enrich all entities
3. Run correlation analysis on all events
4. Generate insights and statistics

Usage:
    python trigger_full_analysis.py [--batch-size 1000] [--dry-run]
"""

import asyncio
import json
import sys
import psycopg2
import redis
from datetime import datetime
from typing import Dict, List, Optional
import argparse


class FullAnalysisTrigger:
    """Trigger complete analysis of all stored data"""

    def __init__(self, dry_run: bool = False, batch_size: int = 1000):
        self.dry_run = dry_run
        self.batch_size = batch_size
        self.stats = {
            'logs_analyzed': 0,
            'entities_extracted': 0,
            'entities_enriched': 0,
            'correlations_checked': 0,
            'alerts_generated': 0
        }

        # Database connection
        self.db = psycopg2.connect(
            host='localhost',
            port=5433,
            user='siemless',
            password='siemless123',
            database='siemless_v2'
        )

        # Redis connection
        self.redis = redis.Redis(
            host='localhost',
            port=6380,
            decode_responses=True
        )

        print(f"Initialized FullAnalysisTrigger (dry_run={dry_run}, batch_size={batch_size})")

    def get_warm_storage_stats(self) -> Dict:
        """Get statistics about warm_storage data"""
        cursor = self.db.cursor()

        # Total logs
        cursor.execute("SELECT COUNT(*) FROM warm_storage")
        total_logs = cursor.fetchone()[0]

        # Processed logs
        cursor.execute("SELECT COUNT(*) FROM warm_storage WHERE processed = true")
        processed_logs = cursor.fetchone()[0]

        # Logs with entities extracted
        cursor.execute("""
            SELECT COUNT(*)
            FROM warm_storage
            WHERE processed = true
            AND enriched_data->>'entity_count' IS NOT NULL
            AND (enriched_data->>'entity_count')::int > 0
        """)
        logs_with_entities = cursor.fetchone()[0]

        # Total entities in warm_storage
        cursor.execute("""
            SELECT SUM((enriched_data->>'entity_count')::int)
            FROM warm_storage
            WHERE enriched_data->>'entity_count' IS NOT NULL
        """)
        result = cursor.fetchone()
        entities_in_storage = result[0] if result[0] else 0

        cursor.close()

        return {
            'total_logs': total_logs,
            'processed_logs': processed_logs,
            'logs_with_entities': logs_with_entities,
            'entities_in_storage': entities_in_storage
        }

    def get_entity_table_stats(self) -> Dict:
        """Get statistics about entities table"""
        cursor = self.db.cursor()

        # Total entities
        cursor.execute("SELECT COUNT(*) FROM entities")
        total_entities = cursor.fetchone()[0]

        # Entities with enrichment
        cursor.execute("""
            SELECT COUNT(*)
            FROM entities
            WHERE properties IS NOT NULL
            AND properties->>'enrichments' IS NOT NULL
        """)
        enriched_entities = cursor.fetchone()[0]

        # Entities with business context
        cursor.execute("""
            SELECT COUNT(*)
            FROM entities
            WHERE business_context IS NOT NULL
        """)
        entities_with_context = cursor.fetchone()[0]

        # Entity types breakdown
        cursor.execute("""
            SELECT entity_type, COUNT(*) as count
            FROM entities
            GROUP BY entity_type
            ORDER BY count DESC
        """)
        entity_types = {row[0]: row[1] for row in cursor.fetchall()}

        cursor.close()

        return {
            'total_entities': total_entities,
            'enriched_entities': enriched_entities,
            'entities_with_context': entities_with_context,
            'entity_types': entity_types
        }

    def extract_entities_from_warm_storage(self, limit: Optional[int] = None):
        """
        Extract trapped entities from warm_storage and trigger proper processing

        This sends logs to contextualization engine for entity extraction and enrichment
        """
        cursor = self.db.cursor()

        query = """
            SELECT log_id, log_data, enriched_data
            FROM warm_storage
            WHERE processed = true
            AND enriched_data->>'entity_count' IS NOT NULL
            AND (enriched_data->>'entity_count')::int > 0
        """

        if limit:
            query += f" LIMIT {limit}"

        cursor.execute(query)

        logs_to_process = cursor.fetchall()
        cursor.close()

        print(f"\n{'[DRY RUN] ' if self.dry_run else ''}Found {len(logs_to_process)} logs with entities to re-process")

        if self.dry_run:
            print(f"Would send {len(logs_to_process)} logs to contextualization.process_log")
            return len(logs_to_process)

        # Process in batches
        processed = 0
        for i in range(0, len(logs_to_process), self.batch_size):
            batch = logs_to_process[i:i+self.batch_size]

            for log_id, log_data, enriched_data in batch:
                try:
                    # Parse log data
                    if isinstance(log_data, str):
                        log_data = json.loads(log_data)

                    # Send to contextualization engine
                    message = {
                        'log': log_data,
                        'pattern_type': 'reprocessing',
                        'entity_hints': [],
                        'log_id': log_id
                    }

                    self.redis.publish('contextualization.process_log', json.dumps(message))
                    processed += 1

                    if processed % 100 == 0:
                        print(f"  Triggered processing for {processed}/{len(logs_to_process)} logs...")

                except Exception as e:
                    print(f"  ERROR processing log {log_id}: {e}")

            # Small delay between batches
            if i + self.batch_size < len(logs_to_process):
                print(f"  Batch {i//self.batch_size + 1} complete. Sleeping 2 seconds...")
                asyncio.sleep(2)

        print(f"Triggered entity extraction for {processed} logs")
        return processed

    def trigger_entity_enrichment(self, entity_types: Optional[List[str]] = None):
        """
        Trigger enrichment for all entities (or specific types)

        This forces re-enrichment of existing entities with latest context
        """
        cursor = self.db.cursor()

        query = "SELECT entity_id, entity_type, entity_value FROM entities"

        if entity_types:
            placeholders = ','.join(['%s'] * len(entity_types))
            query += f" WHERE entity_type IN ({placeholders})"
            cursor.execute(query, entity_types)
        else:
            cursor.execute(query)

        entities = cursor.fetchall()
        cursor.close()

        print(f"\n{'[DRY RUN] ' if self.dry_run else ''}Found {len(entities)} entities to enrich")

        if self.dry_run:
            print(f"Would send {len(entities)} enrichment requests to contextualization.enrich_entity")
            return len(entities)

        # Process in batches
        enriched = 0
        for i in range(0, len(entities), self.batch_size):
            batch = entities[i:i+self.batch_size]

            for entity_id, entity_type, entity_value in batch:
                try:
                    message = {
                        'entity_type': entity_type,
                        'entity_value': entity_value,
                        'enrich_level': 'deep'
                    }

                    self.redis.publish('contextualization.enrich_entity', json.dumps(message))
                    enriched += 1

                    if enriched % 100 == 0:
                        print(f"  Triggered enrichment for {enriched}/{len(entities)} entities...")

                except Exception as e:
                    print(f"  ERROR enriching entity {entity_id}: {e}")

            # Small delay between batches
            if i + self.batch_size < len(entities):
                print(f"  Batch {i//self.batch_size + 1} complete. Sleeping 1 second...")
                asyncio.sleep(1)

        print(f"Triggered enrichment for {enriched} entities")
        return enriched

    def trigger_correlation_analysis(self):
        """
        Trigger correlation analysis on all stored events

        This re-runs correlation rules against historical data
        """
        cursor = self.db.cursor()

        # Get all processed logs with entities
        cursor.execute("""
            SELECT log_id, log_data, enriched_data
            FROM warm_storage
            WHERE processed = true
            ORDER BY timestamp ASC
        """)

        events = cursor.fetchall()
        cursor.close()

        print(f"\n{'[DRY RUN] ' if self.dry_run else ''}Found {len(events)} events for correlation analysis")

        if self.dry_run:
            print(f"Would send {len(events)} events to backend.correlation_check")
            return len(events)

        # Send to backend for correlation
        checked = 0
        for log_id, log_data, enriched_data in events:
            try:
                if isinstance(log_data, str):
                    log_data = json.loads(log_data)

                if isinstance(enriched_data, str):
                    enriched_data = json.loads(enriched_data)

                message = {
                    'event_id': log_id,
                    'log_data': log_data,
                    'enriched_data': enriched_data,
                    'historical_analysis': True
                }

                self.redis.publish('backend.correlation_check', json.dumps(message))
                checked += 1

                if checked % 100 == 0:
                    print(f"  Triggered correlation check for {checked}/{len(events)} events...")

            except Exception as e:
                print(f"  ERROR checking correlation for {log_id}: {e}")

        print(f"Triggered correlation analysis for {checked} events")
        return checked

    def generate_analysis_report(self) -> str:
        """Generate comprehensive analysis report"""

        warm_stats = self.get_warm_storage_stats()
        entity_stats = self.get_entity_table_stats()

        report = f"""
================================================================================
                    FULL STORAGE ANALYSIS REPORT
================================================================================
Generated: {datetime.now().isoformat()}

WARM STORAGE STATUS:
--------------------
Total Logs:              {warm_stats['total_logs']:,}
Processed Logs:          {warm_stats['processed_logs']:,}
Logs with Entities:      {warm_stats['logs_with_entities']:,}
Entities in Storage:     {warm_stats['entities_in_storage']:,}

ENTITY TABLE STATUS:
--------------------
Total Entities:          {entity_stats['total_entities']:,}
Enriched Entities:       {entity_stats['enriched_entities']:,}
With Business Context:   {entity_stats['entities_with_context']:,}

ENTITY TYPES BREAKDOWN:
-----------------------
"""

        for entity_type, count in entity_stats['entity_types'].items():
            report += f"{entity_type:20} {count:,}\n"

        # Calculate gaps
        trapped_entities = warm_stats['entities_in_storage'] - entity_stats['total_entities']
        unenriched_entities = entity_stats['total_entities'] - entity_stats['enriched_entities']

        report += f"""
IDENTIFIED GAPS:
----------------
Trapped Entities:        {trapped_entities:,} (in warm_storage, not in entities table)
Unenriched Entities:     {unenriched_entities:,} (in entities table, missing enrichment)

RECOMMENDATIONS:
----------------
"""

        if trapped_entities > 0:
            report += f"1. Extract {trapped_entities:,} trapped entities from warm_storage\n"

        if unenriched_entities > 0:
            report += f"2. Enrich {unenriched_entities:,} entities with missing context\n"

        if warm_stats['processed_logs'] > 0:
            report += f"3. Run correlation analysis on {warm_stats['processed_logs']:,} processed logs\n"

        report += """
================================================================================
"""

        return report

    def run_full_analysis(self,
                         extract_entities: bool = True,
                         enrich_entities: bool = True,
                         run_correlation: bool = True,
                         entity_types: Optional[List[str]] = None,
                         limit: Optional[int] = None):
        """
        Run complete analysis pipeline

        Args:
            extract_entities: Extract trapped entities from warm_storage
            enrich_entities: Re-enrich all entities
            run_correlation: Run correlation analysis
            entity_types: Specific entity types to process (None = all)
            limit: Limit number of items to process (None = all)
        """
        print("="*80)
        print("FULL STORAGE ANALYSIS TRIGGER")
        print("="*80)

        # Generate initial report
        print(self.generate_analysis_report())

        if self.dry_run:
            print("\n*** DRY RUN MODE - No actual processing will occur ***\n")

        # Phase 1: Extract trapped entities
        if extract_entities:
            print("\n" + "="*80)
            print("PHASE 1: EXTRACT TRAPPED ENTITIES FROM WARM_STORAGE")
            print("="*80)
            self.stats['entities_extracted'] = self.extract_entities_from_warm_storage(limit)

        # Phase 2: Enrich entities
        if enrich_entities:
            print("\n" + "="*80)
            print("PHASE 2: TRIGGER ENTITY ENRICHMENT")
            print("="*80)
            self.stats['entities_enriched'] = self.trigger_entity_enrichment(entity_types)

        # Phase 3: Correlation analysis
        if run_correlation:
            print("\n" + "="*80)
            print("PHASE 3: TRIGGER CORRELATION ANALYSIS")
            print("="*80)
            self.stats['correlations_checked'] = self.trigger_correlation_analysis()

        # Summary
        print("\n" + "="*80)
        print("ANALYSIS COMPLETE - SUMMARY")
        print("="*80)
        print(f"Entities Extracted:      {self.stats['entities_extracted']:,}")
        print(f"Entities Enriched:       {self.stats['entities_enriched']:,}")
        print(f"Correlations Checked:    {self.stats['correlations_checked']:,}")
        print("="*80)

        if self.dry_run:
            print("\n*** DRY RUN COMPLETE - Run without --dry-run to execute ***")
        else:
            print("\n*** ANALYSIS TRIGGERED - Check engine logs for progress ***")
            print("Monitor with: docker-compose logs -f contextualization_engine backend_engine")


def main():
    parser = argparse.ArgumentParser(
        description='Trigger full analysis of all stored data in SIEMLess v2.0'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be done without actually doing it'
    )

    parser.add_argument(
        '--batch-size',
        type=int,
        default=1000,
        help='Number of items to process per batch (default: 1000)'
    )

    parser.add_argument(
        '--skip-extraction',
        action='store_true',
        help='Skip entity extraction phase'
    )

    parser.add_argument(
        '--skip-enrichment',
        action='store_true',
        help='Skip entity enrichment phase'
    )

    parser.add_argument(
        '--skip-correlation',
        action='store_true',
        help='Skip correlation analysis phase'
    )

    parser.add_argument(
        '--entity-types',
        nargs='+',
        help='Only process specific entity types (e.g., ip_address hostname)'
    )

    parser.add_argument(
        '--limit',
        type=int,
        help='Limit number of items to process (for testing)'
    )

    parser.add_argument(
        '--report-only',
        action='store_true',
        help='Only generate analysis report, do not trigger processing'
    )

    args = parser.parse_args()

    try:
        analyzer = FullAnalysisTrigger(
            dry_run=args.dry_run,
            batch_size=args.batch_size
        )

        if args.report_only:
            print(analyzer.generate_analysis_report())
        else:
            analyzer.run_full_analysis(
                extract_entities=not args.skip_extraction,
                enrich_entities=not args.skip_enrichment,
                run_correlation=not args.skip_correlation,
                entity_types=args.entity_types,
                limit=args.limit
            )

    except KeyboardInterrupt:
        print("\n\nInterrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nERROR: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
