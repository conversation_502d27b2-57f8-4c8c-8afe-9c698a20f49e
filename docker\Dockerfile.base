# Base Dockerfile for all SIEMLess v2.0 engines
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    postgresql-client \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Create non-root user
RUN groupadd -r siemless && useradd -r -g siemless siemless

# Copy shared modules
COPY shared/ /app/shared/
COPY patterns/ /app/patterns/

# Set Python path
ENV PYTHONPATH=/app:$PYTHONPATH

# Switch to non-root user
USER siemless

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1