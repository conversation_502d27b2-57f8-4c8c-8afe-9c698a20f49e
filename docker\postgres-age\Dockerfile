# PostgreSQL with Apache AGE Extension
# Provides graph database capabilities on top of PostgreSQL

FROM postgres:15

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    postgresql-server-dev-15 \
    libreadline-dev \
    zlib1g-dev \
    flex \
    bison \
    && rm -rf /var/lib/apt/lists/*

# Build and install Apache AGE from source
# Using release tag PG15/v1.5.0-rc0 (only available release for PG15)
ENV AGE_VERSION=1.5.0-rc0
RUN git clone --branch PG15/v${AGE_VERSION} https://github.com/apache/age.git /tmp/age && \
    cd /tmp/age && \
    make && \
    make install && \
    cd / && \
    rm -rf /tmp/age

# Add AGE initialization script
COPY ./init-age.sql /docker-entrypoint-initdb.d/01-init-age.sql

# Expose PostgreSQL port
EXPOSE 5432

# Use the default postgres entrypoint
ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["postgres"]
