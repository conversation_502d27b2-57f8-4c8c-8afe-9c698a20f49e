"""
Anthropic Provider - Claude models
Implements BaseAIProvider for Anthropic's Claude models
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, Any
import logging

from .base_provider import BaseAIProvider, AIResponse


class AnthropicProvider(BaseAIProvider):
    """
    Provider for Anthropic Claude models

    Supports:
    - claude-sonnet-4
    - claude-opus-4
    - claude-haiku-3.5
    """

    def _get_provider_name(self) -> str:
        """Return provider name"""
        return 'anthropic'

    async def call(
        self,
        model: str,
        prompt: str,
        temperature: float = 0.1,
        max_tokens: int = 4096,
        **kwargs
    ) -> AIResponse:
        """
        Call Anthropic Claude model

        Args:
            model: Model name (claude-sonnet-4, claude-opus-4, etc.)
            prompt: The prompt text
            temperature: Temperature (0.0-1.0)
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters

        Returns:
            AIResponse

        Raises:
            Exception: If call fails
        """
        import anthropic

        start_time = time.time()

        try:
            # Get API key
            api_key = self.get_credential()
            if not api_key:
                raise Exception(f"No API key found for {self.provider_name}")

            # Map model names to actual API names
            model_mapping = {
                "claude-sonnet-4": "claude-sonnet-4-20250514",
                "claude-opus-4": "claude-opus-4-20250514",
                "claude-opus-4-1": "claude-opus-4-1-20250805",
                "claude-haiku-3.5": "claude-3-5-haiku-20241022"
            }
            actual_model = model_mapping.get(model, model)

            # Run sync client in executor
            def _sync_call():
                client = anthropic.Anthropic(api_key=api_key)
                message = client.messages.create(
                    model=actual_model,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    messages=[
                        {"role": "user", "content": prompt}
                    ]
                )
                return message

            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, _sync_call)

            # Extract content
            content = result.content[0].text if result.content else ""

            # Calculate latency
            latency_ms = (time.time() - start_time) * 1000

            # Extract token usage
            input_tokens = result.usage.input_tokens if hasattr(result.usage, 'input_tokens') else 0
            output_tokens = result.usage.output_tokens if hasattr(result.usage, 'output_tokens') else 0
            total_tokens = input_tokens + output_tokens

            # Track usage if cost tracker available
            if self.cost_tracker:
                self._track_usage(model, input_tokens, output_tokens, "general")

            # Parse response into standardized format
            return AIResponse(
                model=model,
                content=content,
                confidence=0.90,  # Claude typically high confidence
                reasoning=f"Claude {model} analysis",
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                total_tokens=total_tokens,
                provider=self.provider_name,
                request_id=result.id if hasattr(result, 'id') else "",
                created_at=datetime.now(),
                latency_ms=latency_ms,
                raw_response={
                    'id': result.id if hasattr(result, 'id') else "",
                    'model': result.model if hasattr(result, 'model') else actual_model,
                    'usage': {
                        'input_tokens': input_tokens,
                        'output_tokens': output_tokens
                    }
                },
                error=None,
                success=True
            )

        except Exception as e:
            # Mark key as failed if it's a credential issue
            error_str = str(e).lower()
            if 'api_key' in error_str or 'unauthorized' in error_str or 'authentication' in error_str:
                api_key = self.get_credential()
                if api_key:
                    self.credential_manager.mark_key_failed(self.provider_name, api_key)

            latency_ms = (time.time() - start_time) * 1000

            self.logger.error(f"Anthropic API call failed: {e}")

            return AIResponse(
                model=model,
                content="",
                confidence=0.0,
                reasoning="",
                provider=self.provider_name,
                created_at=datetime.now(),
                latency_ms=latency_ms,
                error=str(e),
                success=False
            )

    def _is_rate_limit_error(self, error: Exception) -> bool:
        """Check if error is a rate limit error"""
        error_str = str(error).lower()
        return any(phrase in error_str for phrase in [
            'rate_limit_error',
            'rate limit',
            'too many requests',
            '429'
        ])

    def parse_response(self, raw_response: Any) -> AIResponse:
        """
        Parse Anthropic API response

        Args:
            raw_response: Raw response from Anthropic API

        Returns:
            Standardized AIResponse
        """
        # Not typically used since we parse in call() directly
        # But included for interface compliance
        content = raw_response.content[0].text if hasattr(raw_response, 'content') and raw_response.content else str(raw_response)

        input_tokens = raw_response.usage.input_tokens if hasattr(raw_response, 'usage') else 0
        output_tokens = raw_response.usage.output_tokens if hasattr(raw_response, 'usage') else 0

        return AIResponse(
            model=raw_response.model if hasattr(raw_response, 'model') else "unknown",
            content=content,
            confidence=0.90,
            reasoning="Claude analysis",
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=input_tokens + output_tokens,
            provider=self.provider_name,
            created_at=datetime.now(),
            success=True
        )
