"""
CTI Enrichment Pipeline
Real-time IOC enrichment for firehose log processing
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import redis

logger = logging.getLogger(__name__)


class CTIEnrichmentPipeline:
    """
    Real-time CTI enrichment for incoming logs
    Uses Redis-cached IOCs for sub-millisecond lookups
    """

    def __init__(self, redis_client):
        self.redis = redis_client

        # IOC cache keys
        self.ioc_cache_prefix = "cti:ioc:"
        self.ioc_index_prefix = "cti:index:"

        # Statistics
        self.enrichment_stats = {
            'total_checks': 0,
            'matches_found': 0,
            'cache_hits': 0
        }

    async def enrich_log(self, log_data: Dict) -> Dict:
        """
        Enrich log with CTI data
        Check extracted entities against IOC cache
        """
        enriched = log_data.copy()
        enriched['cti_matches'] = []
        enriched['threat_score'] = 0

        # Extract entities from log
        entities = self._extract_entities_from_log(log_data)

        # Check each entity against IOC cache
        for entity in entities:
            self.enrichment_stats['total_checks'] += 1

            match = await self._check_ioc_cache(entity)
            if match:
                self.enrichment_stats['matches_found'] += 1
                enriched['cti_matches'].append(match)

                # Update threat score
                enriched['threat_score'] = max(
                    enriched['threat_score'],
                    match.get('threat_score', 0)
                )

        # Add enrichment metadata
        if enriched['cti_matches']:
            enriched['cti_enriched'] = True
            enriched['cti_sources'] = list(set([m['source'] for m in enriched['cti_matches']]))
            enriched['threat_level'] = self._calculate_threat_level(enriched['threat_score'])

            logger.info(f"Log enriched with {len(enriched['cti_matches'])} CTI matches (threat score: {enriched['threat_score']})")
        else:
            enriched['cti_enriched'] = False

        return enriched

    async def _check_ioc_cache(self, entity: Dict) -> Optional[Dict]:
        """
        Check if entity matches any cached IOC
        Uses Redis for fast lookup
        """
        entity_type = entity.get('type')
        entity_value = entity.get('value')

        # Build cache key
        cache_key = f"{self.ioc_cache_prefix}{entity_type}:{entity_value}"

        try:
            # Check Redis cache
            cached_ioc = self.redis.get(cache_key)

            if cached_ioc:
                self.enrichment_stats['cache_hits'] += 1
                ioc_data = json.loads(cached_ioc)

                return {
                    'entity': entity_value,
                    'entity_type': entity_type,
                    'source': ioc_data.get('source'),
                    'threat_score': ioc_data.get('threat_score', 50),
                    'tags': ioc_data.get('tags', []),
                    'campaign': ioc_data.get('campaign'),
                    'threat_actor': ioc_data.get('threat_actor'),
                    'first_seen': ioc_data.get('first_seen'),
                    'match_type': 'exact'
                }

        except Exception as e:
            logger.error(f"Error checking IOC cache for {entity_value}: {e}")

        return None

    async def cache_ioc(self, ioc_data: Dict):
        """
        Cache IOC in Redis for fast lookup
        Called when new IOC data arrives from CTI feeds
        """
        try:
            entity_type = ioc_data.get('ioc_type')
            entity_value = ioc_data.get('value')
            ttl = ioc_data.get('ttl', 86400)  # Default 24 hours

            # Build cache key
            cache_key = f"{self.ioc_cache_prefix}{entity_type}:{entity_value}"

            # Cache IOC data
            self.redis.setex(
                cache_key,
                ttl,
                json.dumps(ioc_data)
            )

            # Add to type index for batch lookups
            index_key = f"{self.ioc_index_prefix}{entity_type}"
            self.redis.sadd(index_key, entity_value)
            self.redis.expire(index_key, ttl)

            logger.debug(f"Cached IOC: {entity_type}:{entity_value} (TTL: {ttl}s)")

        except Exception as e:
            logger.error(f"Error caching IOC: {e}")

    async def handle_cti_ioc_update(self, ioc_message: Dict):
        """
        Handle incoming IOC from 'cti.enrichment.iocs' channel
        Cache for real-time enrichment
        """
        try:
            # Cache the IOC
            await self.cache_ioc(ioc_message)

            logger.debug(f"Processed CTI IOC update from {ioc_message.get('source')}")

        except Exception as e:
            logger.error(f"Error handling CTI IOC update: {e}")

    def _extract_entities_from_log(self, log_data: Dict) -> List[Dict]:
        """
        Extract entities from log for CTI checking
        """
        entities = []

        # Extract from structured fields
        fields_to_check = {
            'source_ip': 'ip',
            'dest_ip': 'ip',
            'ip': 'ip',
            'domain': 'domain',
            'url': 'url',
            'hash': 'hash',
            'md5': 'md5',
            'sha1': 'sha1',
            'sha256': 'sha256',
            'email': 'email'
        }

        for field, entity_type in fields_to_check.items():
            if field in log_data:
                entities.append({
                    'type': entity_type,
                    'value': log_data[field],
                    'field': field
                })

        # Extract from parsed entities (if already extracted)
        if 'entities' in log_data:
            for entity in log_data['entities']:
                if entity.get('type') in ['ip', 'domain', 'hash', 'email', 'url']:
                    entities.append(entity)

        return entities

    def _calculate_threat_level(self, threat_score: int) -> str:
        """
        Convert threat score to threat level
        """
        if threat_score >= 80:
            return 'critical'
        elif threat_score >= 60:
            return 'high'
        elif threat_score >= 40:
            return 'medium'
        else:
            return 'low'

    async def get_enrichment_stats(self) -> Dict:
        """Get enrichment statistics"""
        stats = self.enrichment_stats.copy()

        # Calculate hit rate
        if stats['total_checks'] > 0:
            stats['match_rate'] = stats['matches_found'] / stats['total_checks']
            stats['cache_hit_rate'] = stats['cache_hits'] / stats['total_checks']
        else:
            stats['match_rate'] = 0
            stats['cache_hit_rate'] = 0

        # Get cache size
        try:
            ioc_keys = self.redis.keys(f"{self.ioc_cache_prefix}*")
            stats['cached_iocs'] = len(ioc_keys) if ioc_keys else 0
        except:
            stats['cached_iocs'] = 0

        return stats

    async def clear_cache(self):
        """Clear all cached IOCs"""
        try:
            # Delete all IOC cache keys
            ioc_keys = self.redis.keys(f"{self.ioc_cache_prefix}*")
            if ioc_keys:
                self.redis.delete(*ioc_keys)

            # Delete all index keys
            index_keys = self.redis.keys(f"{self.ioc_index_prefix}*")
            if index_keys:
                self.redis.delete(*index_keys)

            logger.info("Cleared CTI enrichment cache")

        except Exception as e:
            logger.error(f"Error clearing CTI cache: {e}")


class BulkIOCMatcher:
    """
    Optimized bulk IOC matching for high-volume scenarios
    """

    def __init__(self, redis_client):
        self.redis = redis_client
        self.ioc_index_prefix = "cti:index:"

    async def check_batch(self, entities: List[Dict]) -> Dict[str, Dict]:
        """
        Check multiple entities against IOC cache efficiently
        Returns dict mapping entity_value -> match_data
        """
        matches = {}

        # Group entities by type
        by_type = {}
        for entity in entities:
            entity_type = entity.get('type')
            if entity_type not in by_type:
                by_type[entity_type] = []
            by_type[entity_type].append(entity)

        # Batch check each type
        for entity_type, entity_list in by_type.items():
            index_key = f"{self.ioc_index_prefix}{entity_type}"

            # Get all IOCs of this type from index
            try:
                cached_iocs = self.redis.smembers(index_key)
                if not cached_iocs:
                    continue

                # Check which entities match
                for entity in entity_list:
                    if entity['value'] in cached_iocs:
                        # Fetch full IOC data
                        cache_key = f"cti:ioc:{entity_type}:{entity['value']}"
                        ioc_data = self.redis.get(cache_key)

                        if ioc_data:
                            matches[entity['value']] = json.loads(ioc_data)

            except Exception as e:
                logger.error(f"Error in bulk IOC matching for type {entity_type}: {e}")

        return matches
