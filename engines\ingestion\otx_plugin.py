"""
OTX (AlienVault Open Threat Exchange) CTI Plugin
Universal plugin implementation for OTX threat intelligence feed
"""

import aiohttp
from typing import List, Optional
from datetime import datetime
from cti_source_plugin import (
    CTISourcePlugin, CTIIndicator, IndicatorType, ThreatType
)


class OTXPlugin(CTISourcePlugin):
    """
    OTX CTI Source Plugin

    Fetches threat intelligence from AlienVault Open Threat Exchange
    Works with or without API key (limited without key)
    """

    def __init__(self, config: dict, logger=None):
        super().__init__(config, logger)
        self.base_url = "https://otx.alienvault.com/api/v1"
        self.api_url = self.base_url  # Override base class default

    def get_source_name(self) -> str:
        return "otx"

    def get_source_type(self) -> str:
        return "community"

    async def validate_credentials(self) -> bool:
        """Test connection to OTX"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/pulses/subscribed"
                headers = {
                    'X-OTX-API-KEY': self.api_key or '',
                    'Content-Type': 'application/json'
                }
                async with session.get(url, headers=headers, params={'limit': 1}) as response:
                    if response.status == 200:
                        self.logger.info("OTX credentials validated successfully")
                        return True
                    else:
                        self.logger.warning(f"OTX validation returned status {response.status}")
                        return False
        except Exception as e:
            self.logger.error(f"OTX credential validation error: {e}")
            return False

    async def fetch_indicators(
        self,
        since: Optional[datetime] = None,
        indicator_types: Optional[List[IndicatorType]] = None,
        limit: int = 1000
    ) -> List[CTIIndicator]:
        """
        Fetch indicators from OTX

        Returns standardized CTI indicators
        """
        indicators = []

        try:
            # Fetch from subscribed pulses (requires API key)
            subscribed_indicators = await self._fetch_subscribed_pulses(limit)
            indicators.extend(subscribed_indicators)

            # If we didn't get enough, try public feed
            if len(indicators) < limit:
                public_indicators = await self._fetch_public_pulses(limit - len(indicators))
                indicators.extend(public_indicators)

            self.logger.info(f"OTX: Fetched {len(indicators)} indicators")

        except Exception as e:
            self.logger.error(f"OTX fetch error: {e}")

        return indicators

    async def _fetch_subscribed_pulses(self, limit: int) -> List[CTIIndicator]:
        """Fetch indicators from subscribed pulses"""
        indicators = []

        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/pulses/subscribed"
                headers = {
                    'X-OTX-API-KEY': self.api_key or '',
                    'Content-Type': 'application/json'
                }
                params = {
                    'limit': min(limit // 10, 10),  # Get 10 pulses
                    'page': 1
                }

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        pulses = data.get('results', [])

                        for pulse in pulses:
                            pulse_indicators = pulse.get('indicators', [])
                            for ind in pulse_indicators[:limit]:
                                standardized = self._standardize_indicator(ind, pulse)
                                if standardized:
                                    indicators.append(standardized)

        except Exception as e:
            self.logger.error(f"OTX subscribed pulses error: {e}")

        return indicators

    async def _fetch_public_pulses(self, limit: int) -> List[CTIIndicator]:
        """Fetch indicators from public pulses"""
        indicators = []

        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/pulses/activity"
                headers = {
                    'X-OTX-API-KEY': self.api_key or '',
                    'Content-Type': 'application/json'
                }
                params = {
                    'limit': min(limit // 10, 50),
                    'page': 1
                }

                async with session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        pulses = data.get('results', [])

                        for pulse in pulses:
                            pulse_indicators = pulse.get('indicators', [])
                            for ind in pulse_indicators[:limit]:
                                standardized = self._standardize_indicator(ind, pulse)
                                if standardized:
                                    indicators.append(standardized)

        except Exception as e:
            self.logger.error(f"OTX public pulses error: {e}")

        return indicators

    def _standardize_indicator(self, ind: dict, pulse: dict) -> Optional[CTIIndicator]:
        """
        Convert OTX indicator to standardized CTI format

        OTX types: IPv4, IPv6, domain, hostname, URL, FileHash-MD5, FileHash-SHA1, FileHash-SHA256, email
        """
        try:
            otx_type = ind.get('type', '').lower()
            indicator_value = ind.get('indicator', '')

            if not indicator_value:
                return None

            # Map OTX types to standard types
            type_mapping = {
                'ipv4': IndicatorType.IP.value,
                'ipv6': IndicatorType.IP.value,
                'domain': IndicatorType.DOMAIN.value,
                'hostname': IndicatorType.DOMAIN.value,
                'url': IndicatorType.URL.value,
                'filehash-md5': IndicatorType.FILE_HASH.value,
                'filehash-sha1': IndicatorType.FILE_HASH.value,
                'filehash-sha256': IndicatorType.FILE_HASH.value,
                'email': IndicatorType.EMAIL.value,
            }

            indicator_type = type_mapping.get(otx_type.replace('-', '').replace('_', ''))
            if not indicator_type:
                return None  # Skip unsupported types

            # Determine threat type from tags
            tags = pulse.get('tags', [])
            threat_type = ThreatType.UNKNOWN.value

            tag_lower = [t.lower() for t in tags]
            if any(x in tag_lower for x in ['malware', 'trojan', 'rat', 'backdoor']):
                threat_type = ThreatType.MALWARE.value
            elif any(x in tag_lower for x in ['phishing', 'phish']):
                threat_type = ThreatType.PHISHING.value
            elif any(x in tag_lower for x in ['c2', 'c&c', 'command']):
                threat_type = ThreatType.C2.value
            elif any(x in tag_lower for x in ['ransomware', 'ransom']):
                threat_type = ThreatType.RANSOMWARE.value
            elif any(x in tag_lower for x in ['apt']):
                threat_type = ThreatType.APT.value
            elif any(x in tag_lower for x in ['botnet', 'bot']):
                threat_type = ThreatType.BOTNET.value

            # Extract MITRE techniques from tags
            mitre_techniques = [tag for tag in tags if tag.startswith('T') and len(tag) >= 5]

            # Parse timestamps
            created = ind.get('created')
            if created:
                try:
                    created = datetime.fromisoformat(created.replace('Z', '+00:00'))
                except:
                    created = None

            # Build standardized indicator
            return CTIIndicator({
                'indicator_type': indicator_type,
                'indicator_value': indicator_value,
                'threat_type': threat_type,
                'confidence': 0.7,  # OTX community feed = medium confidence
                'first_seen': created,
                'last_seen': created,
                'tags': tags,
                'description': pulse.get('description', ''),
                'source_reference': pulse.get('id', ''),
                'mitre_techniques': mitre_techniques,
                'severity': self._calculate_severity(pulse),
                'raw_data': {
                    'pulse_name': pulse.get('name', ''),
                    'adversary': pulse.get('adversary', ''),
                    'malware_families': pulse.get('malware_families', []),
                    'references': pulse.get('references', []),
                    'pulse_id': pulse.get('id', '')
                }
            })

        except Exception as e:
            self.logger.error(f"Error standardizing OTX indicator: {e}")
            return None

    def _calculate_severity(self, pulse: dict) -> str:
        """Calculate severity from pulse metadata"""
        tags = [t.lower() for t in pulse.get('tags', [])]

        # High severity indicators
        if any(x in tags for x in ['ransomware', 'apt', 'critical', 'zero-day']):
            return 'high'

        # Medium severity indicators
        if any(x in tags for x in ['malware', 'trojan', 'c2', 'exploit']):
            return 'medium'

        # Default to low
        return 'low'

    async def get_indicator_context(self, indicator: str) -> Optional[dict]:
        """Get additional context for an indicator from OTX"""
        try:
            async with aiohttp.ClientSession() as session:
                # Determine indicator type for URL construction
                if '.' in indicator and len(indicator) <= 15 and all(c.isdigit() or c == '.' for c in indicator):
                    # IP address
                    url = f"{self.base_url}/indicators/IPv4/{indicator}/general"
                elif '.' in indicator:
                    # Domain
                    url = f"{self.base_url}/indicators/domain/{indicator}/general"
                else:
                    # Hash
                    url = f"{self.base_url}/indicators/file/{indicator}/general"

                headers = {
                    'X-OTX-API-KEY': self.api_key or '',
                    'Content-Type': 'application/json'
                }

                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'pulse_count': data.get('pulse_info', {}).get('count', 0),
                            'pulses': data.get('pulse_info', {}).get('pulses', [])[:5],
                            'reputation': data.get('reputation', 0)
                        }

        except Exception as e:
            self.logger.error(f"OTX context fetch error: {e}")

        return None
