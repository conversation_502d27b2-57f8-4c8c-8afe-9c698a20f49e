"""
Historical Context Manager
Retrieves and manages historical log context starting from most recent events
Essential for investigation and pattern recognition
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import asyncpg

logger = logging.getLogger(__name__)


@dataclass
class HistoricalQuery:
    """Historical context query configuration"""
    entity_type: str
    entity_value: str
    time_range_hours: int = 24
    max_results: int = 1000
    include_relationships: bool = True
    include_events: bool = True
    include_sessions: bool = True
    priority_order: str = 'recent'  # 'recent', 'relevance', 'risk'


@dataclass
class HistoricalContext:
    """Historical context result"""
    entity: Dict
    recent_events: List[Dict]
    relationships: List[Dict]
    sessions: List[Dict]
    timeline: List[Dict]
    risk_evolution: List[Dict]
    summary: Dict


class HistoricalContextManager:
    """
    Manages historical context retrieval with emphasis on recent events

    Key Features:
    1. Most-recent-first ordering (critical for investigations)
    2. Time-windowed context (last N hours)
    3. Entity-centric history
    4. Relationship timeline
    5. Risk score evolution
    6. Session grouping
    """

    def __init__(self, db_pool: asyncpg.Pool, redis_client=None):
        self.db_pool = db_pool
        self.redis = redis_client

        # Caching configuration
        self.cache_ttl = {
            'recent_events': 300,      # 5 minutes for recent events
            'relationships': 600,      # 10 minutes for relationships
            'risk_evolution': 1800     # 30 minutes for risk data
        }

    async def get_entity_history(self,
                                  entity_type: str,
                                  entity_value: str,
                                  hours_back: int = 24,
                                  max_results: int = 1000) -> HistoricalContext:
        """
        Get complete historical context for an entity

        Args:
            entity_type: Type of entity (ip, user, host, etc.)
            entity_value: Value of entity
            hours_back: How many hours to look back
            max_results: Maximum results to return

        Returns:
            Complete historical context starting from most recent
        """
        try:
            # Try cache first
            cache_key = f"history:{entity_type}:{entity_value}:{hours_back}"
            if self.redis:
                cached = self.redis.get(cache_key)
                if cached:
                    logger.debug(f"Cache hit for {entity_type}:{entity_value}")
                    return HistoricalContext(**json.loads(cached))

            # Get entity details
            entity = await self._get_entity_details(entity_type, entity_value)
            if not entity:
                logger.warning(f"Entity not found: {entity_type}:{entity_value}")
                return None

            # Get recent events (MOST RECENT FIRST)
            recent_events = await self._get_recent_events(
                entity_type,
                entity_value,
                hours_back,
                max_results
            )

            # Get relationships (ordered by last interaction)
            relationships = await self._get_entity_relationships(
                entity_type,
                entity_value,
                hours_back
            )

            # Get sessions (grouped activity)
            sessions = await self._get_entity_sessions(
                entity_type,
                entity_value,
                hours_back
            )

            # Build timeline (chronological view of all activity)
            timeline = await self._build_timeline(
                entity_type,
                entity_value,
                hours_back
            )

            # Get risk evolution (how risk score changed over time)
            risk_evolution = await self._get_risk_evolution(
                entity_type,
                entity_value,
                hours_back
            )

            # Generate summary
            summary = await self._generate_summary(
                entity,
                recent_events,
                relationships,
                sessions,
                risk_evolution
            )

            context = HistoricalContext(
                entity=entity,
                recent_events=recent_events,
                relationships=relationships,
                sessions=sessions,
                timeline=timeline,
                risk_evolution=risk_evolution,
                summary=summary
            )

            # Cache result
            if self.redis:
                self.redis.setex(
                    cache_key,
                    self.cache_ttl['recent_events'],
                    json.dumps(asdict(context))
                )

            return context

        except Exception as e:
            logger.error(f"Error getting entity history: {e}", exc_info=True)
            return None

    async def _get_entity_details(self, entity_type: str, entity_value: str) -> Optional[Dict]:
        """Get entity details from entities table"""
        try:
            query = """
                SELECT
                    entity_id,
                    entity_type,
                    entity_value,
                    enrichment_metadata,
                    tags,
                    risk_score,
                    first_seen,
                    last_seen,
                    enrichment_updated_at,
                    properties
                FROM entities
                WHERE entity_type = $1
                AND entity_value = $2
                LIMIT 1
            """

            async with self.db_pool.acquire() as conn:
                result = await conn.fetchrow(query, entity_type, entity_value)
                return dict(result) if result else None

        except Exception as e:
            logger.error(f"Error fetching entity details: {e}")
            return None

    async def _get_recent_events(self,
                                  entity_type: str,
                                  entity_value: str,
                                  hours_back: int,
                                  max_results: int) -> List[Dict]:
        """
        Get recent events involving this entity
        ORDERED BY MOST RECENT FIRST
        """
        try:
            time_cutoff = datetime.utcnow() - timedelta(hours=hours_back)

            query = """
                SELECT
                    e.event_id,
                    e.event_type,
                    e.event_timestamp,
                    e.source_system,
                    e.severity,
                    e.entities_involved,
                    e.event_data,
                    e.risk_score,
                    e.mitre_techniques,
                    e.created_at
                FROM events e
                WHERE e.event_timestamp >= $1
                AND (
                    e.entities_involved @> $2::jsonb
                    OR e.event_data @> $3::jsonb
                )
                ORDER BY e.event_timestamp DESC
                LIMIT $4
            """

            entity_json = json.dumps({entity_type: entity_value})

            async with self.db_pool.acquire() as conn:
                results = await conn.fetch(query, time_cutoff, entity_json, entity_json, max_results)
                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error fetching recent events: {e}")
            return []

    async def _get_entity_relationships(self,
                                        entity_type: str,
                                        entity_value: str,
                                        hours_back: int) -> List[Dict]:
        """
        Get relationships for entity
        ORDERED BY MOST RECENT INTERACTION
        """
        try:
            time_cutoff = datetime.utcnow() - timedelta(hours=hours_back)

            query = """
                SELECT
                    r.relationship_id,
                    r.from_entity_type,
                    r.from_entity_value,
                    r.relationship_type,
                    r.to_entity_type,
                    r.to_entity_value,
                    r.relationship_metadata,
                    r.confidence_score,
                    r.first_seen,
                    r.last_seen,
                    r.occurrence_count
                FROM relationships r
                WHERE r.last_seen >= $1
                AND (
                    (r.from_entity_type = $2 AND r.from_entity_value = $3)
                    OR (r.to_entity_type = $4 AND r.to_entity_value = $5)
                )
                ORDER BY r.last_seen DESC
                LIMIT 500
            """

            async with self.db_pool.acquire() as conn:
                results = await conn.fetch(query,
                    time_cutoff,
                    entity_type, entity_value,
                    entity_type, entity_value
                )
                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error fetching relationships: {e}")
            return []

    async def _get_entity_sessions(self,
                                   entity_type: str,
                                   entity_value: str,
                                   hours_back: int) -> List[Dict]:
        """
        Get sessions involving entity
        ORDERED BY MOST RECENT SESSION
        """
        try:
            time_cutoff = datetime.utcnow() - timedelta(hours=hours_back)

            query = """
                SELECT
                    s.session_id,
                    s.session_type,
                    s.start_time,
                    s.end_time,
                    s.duration_seconds,
                    s.entities_involved,
                    s.event_count,
                    s.risk_level,
                    s.session_summary,
                    s.created_at
                FROM sessions s
                WHERE s.start_time >= $1
                AND s.entities_involved @> $2::jsonb
                ORDER BY s.start_time DESC
                LIMIT 100
            """

            entity_json = json.dumps({entity_type: entity_value})

            async with self.db_pool.acquire() as conn:
                results = await conn.fetch(query, time_cutoff, entity_json)
                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error fetching sessions: {e}")
            return []

    async def _build_timeline(self,
                             entity_type: str,
                             entity_value: str,
                             hours_back: int) -> List[Dict]:
        """
        Build chronological timeline of ALL activity
        ORDERED CHRONOLOGICALLY (most recent first)
        """
        try:
            time_cutoff = datetime.utcnow() - timedelta(hours=hours_back)

            # Union events, relationships, and sessions into timeline
            query = """
                WITH timeline AS (
                    -- Events
                    SELECT
                        'event' as item_type,
                        e.event_id as item_id,
                        e.event_timestamp as timestamp,
                        e.event_type as type_detail,
                        e.severity,
                        e.risk_score,
                        e.event_data as data
                    FROM events e
                    WHERE e.event_timestamp >= %s
                    AND (
                        e.entities_involved @> %s::jsonb
                        OR e.event_data @> %s::jsonb
                    )

                    UNION ALL

                    -- Relationships (use last_seen as timestamp)
                    SELECT
                        'relationship' as item_type,
                        r.relationship_id as item_id,
                        r.last_seen as timestamp,
                        r.relationship_type as type_detail,
                        NULL as severity,
                        NULL as risk_score,
                        jsonb_build_object(
                            'from', r.from_entity_type || ':' || r.from_entity_value,
                            'to', r.to_entity_type || ':' || r.to_entity_value,
                            'count', r.occurrence_count
                        ) as data
                    FROM relationships r
                    WHERE r.last_seen >= %s
                    AND (
                        (r.from_entity_type = %s AND r.from_entity_value = %s)
                        OR (r.to_entity_type = %s AND r.to_entity_value = %s)
                    )

                    UNION ALL

                    -- Sessions (use end_time as timestamp)
                    SELECT
                        'session' as item_type,
                        s.session_id as item_id,
                        s.end_time as timestamp,
                        s.session_type as type_detail,
                        NULL as severity,
                        NULL as risk_score,
                        jsonb_build_object(
                            'duration', s.duration_seconds,
                            'event_count', s.event_count,
                            'risk_level', s.risk_level
                        ) as data
                    FROM sessions s
                    WHERE s.end_time >= %s
                    AND s.entities_involved @> %s::jsonb
                )
                SELECT * FROM timeline
                ORDER BY timestamp DESC
                LIMIT 1000
            """

            entity_json = json.dumps({entity_type: entity_value})

            async with self.db_pool.acquire() as conn:
                results = await conn.fetch(query,
                    time_cutoff, entity_json, entity_json,  # events
                    time_cutoff, entity_type, entity_value, entity_type, entity_value,  # relationships
                    time_cutoff, entity_json  # sessions
                )
                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error building timeline: {e}")
            return []

    async def _get_risk_evolution(self,
                                  entity_type: str,
                                  entity_value: str,
                                  hours_back: int) -> List[Dict]:
        """
        Get risk score evolution over time
        ORDERED BY MOST RECENT
        """
        try:
            time_cutoff = datetime.utcnow() - timedelta(hours=hours_back)

            # Query risk score changes from entity updates
            query = """
                SELECT
                    timestamp,
                    risk_score,
                    reason,
                    contributing_factors
                FROM entity_risk_history
                WHERE entity_type = $1
                AND entity_value = $2
                AND timestamp >= $3
                ORDER BY timestamp DESC
                LIMIT 100
            """

            async with self.db_pool.acquire() as conn:
                results = await conn.fetch(query, entity_type, entity_value, time_cutoff)

                if results:
                    return [dict(row) for row in results]

                # Fallback: If no risk history table, estimate from events
                event_risk_query = """
                    SELECT
                        DATE_TRUNC('hour', event_timestamp) as timestamp,
                        MAX(risk_score) as risk_score,
                        'estimated_from_events' as reason,
                        jsonb_build_object(
                            'event_count', COUNT(*),
                            'max_severity', MAX(severity)
                        ) as contributing_factors
                    FROM events
                    WHERE event_timestamp >= $1
                    AND (
                        entities_involved @> $2::jsonb
                        OR event_data @> $3::jsonb
                    )
                    GROUP BY DATE_TRUNC('hour', event_timestamp)
                    ORDER BY timestamp DESC
                    LIMIT 100
                """

                entity_json = json.dumps({entity_type: entity_value})
                results = await conn.fetch(event_risk_query, time_cutoff, entity_json, entity_json)
                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error fetching risk evolution: {e}")
            return []

    async def _generate_summary(self,
                               entity: Dict,
                               recent_events: List[Dict],
                               relationships: List[Dict],
                               sessions: List[Dict],
                               risk_evolution: List[Dict]) -> Dict:
        """Generate summary of historical context"""
        try:
            summary = {
                'entity_summary': {
                    'type': entity.get('entity_type'),
                    'value': entity.get('entity_value'),
                    'current_risk_score': entity.get('risk_score', 0),
                    'tags': entity.get('tags', []),
                    'first_seen': entity.get('first_seen').isoformat() if entity.get('first_seen') else None,
                    'last_seen': entity.get('last_seen').isoformat() if entity.get('last_seen') else None
                },
                'activity_summary': {
                    'total_events': len(recent_events),
                    'total_relationships': len(relationships),
                    'total_sessions': len(sessions),
                    'most_recent_event': recent_events[0].get('event_timestamp').isoformat() if recent_events else None,
                    'event_types': list(set([e.get('event_type') for e in recent_events if e.get('event_type')])),
                    'relationship_types': list(set([r.get('relationship_type') for r in relationships if r.get('relationship_type')])),
                    'session_types': list(set([s.get('session_type') for s in sessions if s.get('session_type')]))
                },
                'risk_summary': {
                    'current_risk': entity.get('risk_score', 0),
                    'peak_risk': max([r.get('risk_score', 0) for r in risk_evolution]) if risk_evolution else 0,
                    'risk_trend': self._calculate_risk_trend(risk_evolution),
                    'high_risk_events': len([e for e in recent_events if e.get('risk_score', 0) > 70])
                },
                'temporal_patterns': {
                    'most_active_hour': await self._get_most_active_hour(recent_events),
                    'activity_distribution': await self._get_activity_distribution(recent_events),
                    'unusual_time_activity': await self._detect_unusual_timing(recent_events)
                }
            }

            return summary

        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return {}

    def _calculate_risk_trend(self, risk_evolution: List[Dict]) -> str:
        """Calculate risk trend (increasing, decreasing, stable)"""
        if not risk_evolution or len(risk_evolution) < 2:
            return 'unknown'

        # Compare most recent vs older scores
        recent_scores = [r.get('risk_score', 0) for r in risk_evolution[:5]]
        older_scores = [r.get('risk_score', 0) for r in risk_evolution[-5:]]

        recent_avg = sum(recent_scores) / len(recent_scores) if recent_scores else 0
        older_avg = sum(older_scores) / len(older_scores) if older_scores else 0

        if recent_avg > older_avg * 1.2:
            return 'increasing'
        elif recent_avg < older_avg * 0.8:
            return 'decreasing'
        else:
            return 'stable'

    async def _get_most_active_hour(self, events: List[Dict]) -> int:
        """Get hour of day with most activity"""
        if not events:
            return None

        hour_counts = {}
        for event in events:
            timestamp = event.get('event_timestamp')
            if timestamp:
                hour = timestamp.hour
                hour_counts[hour] = hour_counts.get(hour, 0) + 1

        return max(hour_counts, key=hour_counts.get) if hour_counts else None

    async def _get_activity_distribution(self, events: List[Dict]) -> Dict:
        """Get hourly activity distribution"""
        distribution = {i: 0 for i in range(24)}

        for event in events:
            timestamp = event.get('event_timestamp')
            if timestamp:
                distribution[timestamp.hour] += 1

        return distribution

    async def _detect_unusual_timing(self, events: List[Dict]) -> List[Dict]:
        """Detect activity at unusual times"""
        unusual = []

        for event in events:
            timestamp = event.get('event_timestamp')
            if timestamp:
                # Flag activity between 12am-6am as potentially unusual
                if 0 <= timestamp.hour < 6:
                    unusual.append({
                        'event_id': event.get('event_id'),
                        'event_type': event.get('event_type'),
                        'timestamp': timestamp.isoformat(),
                        'reason': 'off_hours_activity'
                    })

        return unusual

    async def get_multi_entity_context(self, entities: List[Dict], hours_back: int = 24) -> Dict:
        """
        Get historical context for multiple entities
        Useful for investigation of related entities
        """
        try:
            results = {}

            # Get context for each entity
            for entity in entities:
                context = await self.get_entity_history(
                    entity['type'],
                    entity['value'],
                    hours_back
                )
                if context:
                    results[f"{entity['type']}:{entity['value']}"] = context

            # Find intersection (events involving multiple entities)
            intersection = await self._find_entity_intersections(entities, hours_back)

            return {
                'individual_contexts': results,
                'intersections': intersection,
                'summary': {
                    'total_entities': len(entities),
                    'entities_with_context': len(results),
                    'shared_events': len(intersection.get('shared_events', [])),
                    'shared_sessions': len(intersection.get('shared_sessions', []))
                }
            }

        except Exception as e:
            logger.error(f"Error getting multi-entity context: {e}")
            return {}

    async def _find_entity_intersections(self, entities: List[Dict], hours_back: int) -> Dict:
        """Find events/sessions involving multiple entities"""
        try:
            time_cutoff = datetime.utcnow() - timedelta(hours=hours_back)

            # Build query to find events with ALL entities
            entity_conditions = []
            for entity in entities:
                entity_json = json.dumps({entity['type']: entity['value']})
                entity_conditions.append(f"entities_involved @> '{entity_json}'::jsonb")

            query = f"""
                SELECT
                    event_id,
                    event_type,
                    event_timestamp,
                    entities_involved,
                    risk_score
                FROM events
                WHERE event_timestamp >= $1
                AND ({' AND '.join(entity_conditions)})
                ORDER BY event_timestamp DESC
                LIMIT 100
            """

            # Same for sessions
            session_query = f"""
                SELECT
                    session_id,
                    session_type,
                    start_time,
                    end_time,
                    entities_involved,
                    risk_level
                FROM sessions
                WHERE start_time >= $1
                AND ({' AND '.join(entity_conditions)})
                ORDER BY start_time DESC
                LIMIT 100
            """

            async with self.db_pool.acquire() as conn:
                shared_events = await conn.fetch(query, time_cutoff)
                shared_sessions = await conn.fetch(session_query, time_cutoff)

                return {
                    'shared_events': [dict(row) for row in shared_events],
                    'shared_sessions': [dict(row) for row in shared_sessions]
                }

        except Exception as e:
            logger.error(f"Error finding intersections: {e}")
            return {}


# Helper functions for API integration
async def get_recent_context(db_pool: asyncpg.Pool, entity_type: str, entity_value: str, hours: int = 24):
    """Quick helper to get recent context"""
    manager = HistoricalContextManager(db_pool)
    return await manager.get_entity_history(entity_type, entity_value, hours)


async def get_investigation_context(db_pool: asyncpg.Pool, entities: List[Dict], hours: int = 48):
    """Get context for investigation involving multiple entities"""
    manager = HistoricalContextManager(db_pool)
    return await manager.get_multi_entity_context(entities, hours)
