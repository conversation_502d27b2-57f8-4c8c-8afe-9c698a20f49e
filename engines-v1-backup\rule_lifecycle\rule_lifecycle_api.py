"""
SIEMLess v2.0 - Rule Lifecycle Management API
REST API endpoints for comprehensive rule lifecycle management and CTI integration.

Key Features:
- CTI ingestion and rule generation endpoints
- Rule performance tracking and analytics
- AI-powered improvement suggestions
- Rule testing and validation
- SIEM platform deployment integration
- Real-time rule health monitoring

Port: 5014 (as per SIEMLess v2.0 architecture)
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import asyncio
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging

# Import engine components
from rule_lifecycle_engine import (
    RuleLifecycleEngine, DetectionRule, ThreatIntelligence, RuleTestCase,
    RuleFormat, RuleStatus, ThreatContext, UpdateTrigger
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Initialize Rule Lifecycle Engine
rule_engine = RuleLifecycleEngine()

# === Health and Status Endpoints ===

@app.route('/api/health', methods=['GET'])
def health_check():
    """Engine health check"""
    return jsonify({
        'status': 'healthy',
        'service': 'rule_lifecycle_engine',
        'version': '2.0.0',
        'timestamp': datetime.now().isoformat(),
        'capabilities': [
            'cti_ingestion',
            'rule_generation',
            'performance_tracking',
            'improvement_suggestions',
            'rule_testing',
            'platform_deployment'
        ]
    })

@app.route('/api/status', methods=['GET'])
def detailed_status():
    """Detailed engine status and metrics"""
    try:
        total_rules = len(rule_engine.rules)
        active_rules = len([r for r in rule_engine.rules.values() if r.status == RuleStatus.ACTIVE])
        total_suggestions = len(rule_engine.improvement_suggestions)
        pending_suggestions = len([s for s in rule_engine.improvement_suggestions.values() if s.status == 'pending'])

        # Calculate average performance metrics
        if total_rules > 0:
            avg_precision = sum(r.performance_metrics.precision for r in rule_engine.rules.values()) / total_rules
            avg_recall = sum(r.performance_metrics.recall for r in rule_engine.rules.values()) / total_rules
            avg_f1 = sum(r.performance_metrics.f1_score for r in rule_engine.rules.values()) / total_rules
        else:
            avg_precision = avg_recall = avg_f1 = 0.0

        return jsonify({
            'status': 'operational',
            'metrics': {
                'total_rules': total_rules,
                'active_rules': active_rules,
                'testing_rules': len([r for r in rule_engine.rules.values() if r.status == RuleStatus.TESTING]),
                'deprecated_rules': len([r for r in rule_engine.rules.values() if r.status == RuleStatus.DEPRECATED]),
                'total_suggestions': total_suggestions,
                'pending_suggestions': pending_suggestions,
                'avg_precision': round(avg_precision, 3),
                'avg_recall': round(avg_recall, 3),
                'avg_f1_score': round(avg_f1, 3)
            },
            'queue_status': {
                'update_queue_length': rule_engine.redis_client.llen('rule_update_queue'),
                'improvement_queue_length': rule_engine.redis_client.llen('rule_improvement_queue')
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# === CTI Ingestion and Intelligence Endpoints ===

@app.route('/api/cti/ingest', methods=['POST'])
def ingest_cti():
    """Ingest threat intelligence data"""
    try:
        data = request.get_json()

        # Create ThreatIntelligence object
        cti = ThreatIntelligence(
            source=data['source'],
            ioc_type=data['ioc_type'],
            ioc_value=data['ioc_value'],
            threat_context=ThreatContext(data['threat_context']),
            confidence_score=float(data['confidence_score']),
            first_seen=datetime.fromisoformat(data['first_seen']),
            last_seen=datetime.fromisoformat(data['last_seen']),
            mitre_tactics=data.get('mitre_tactics', []),
            mitre_techniques=data.get('mitre_techniques', []),
            kill_chain_phase=data.get('kill_chain_phase'),
            threat_actor=data.get('threat_actor'),
            malware_family=data.get('malware_family'),
            campaign_name=data.get('campaign_name'),
            raw_intelligence=data.get('raw_intelligence', {})
        )

        # Ingest CTI asynchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        success = loop.run_until_complete(rule_engine.ingest_threat_intelligence(cti))
        loop.close()

        if success:
            return jsonify({
                'status': 'success',
                'message': 'CTI ingested successfully',
                'cti_id': f"{cti.source}:{cti.ioc_value}",
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({'error': 'Failed to ingest CTI'}), 500

    except Exception as e:
        logger.error(f"CTI ingestion error: {str(e)}")
        return jsonify({'error': str(e)}), 400

@app.route('/api/cti/batch-ingest', methods=['POST'])
def batch_ingest_cti():
    """Batch ingest multiple CTI records"""
    try:
        data = request.get_json()
        cti_batch = data.get('cti_records', [])

        if not cti_batch:
            return jsonify({'error': 'No CTI records provided'}), 400

        ingested_count = 0
        failed_count = 0

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        for cti_data in cti_batch:
            try:
                cti = ThreatIntelligence(
                    source=cti_data['source'],
                    ioc_type=cti_data['ioc_type'],
                    ioc_value=cti_data['ioc_value'],
                    threat_context=ThreatContext(cti_data['threat_context']),
                    confidence_score=float(cti_data['confidence_score']),
                    first_seen=datetime.fromisoformat(cti_data['first_seen']),
                    last_seen=datetime.fromisoformat(cti_data['last_seen']),
                    mitre_tactics=cti_data.get('mitre_tactics', []),
                    mitre_techniques=cti_data.get('mitre_techniques', [])
                )

                success = loop.run_until_complete(rule_engine.ingest_threat_intelligence(cti))
                if success:
                    ingested_count += 1
                else:
                    failed_count += 1

            except Exception as e:
                logger.error(f"Failed to ingest CTI record: {str(e)}")
                failed_count += 1

        loop.close()

        return jsonify({
            'status': 'completed',
            'ingested_count': ingested_count,
            'failed_count': failed_count,
            'total_processed': len(cti_batch),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 400

# === Rule Generation and Management Endpoints ===

@app.route('/api/rules/generate', methods=['POST'])
def generate_rules_from_cti():
    """Generate detection rules from CTI data"""
    try:
        data = request.get_json()
        cti_data_list = data.get('cti_data', [])
        rule_format = RuleFormat(data.get('rule_format', 'sigma'))

        if not cti_data_list:
            return jsonify({'error': 'No CTI data provided'}), 400

        # Convert to ThreatIntelligence objects
        cti_objects = []
        for cti_data in cti_data_list:
            cti = ThreatIntelligence(
                source=cti_data['source'],
                ioc_type=cti_data['ioc_type'],
                ioc_value=cti_data['ioc_value'],
                threat_context=ThreatContext(cti_data['threat_context']),
                confidence_score=float(cti_data['confidence_score']),
                first_seen=datetime.fromisoformat(cti_data['first_seen']),
                last_seen=datetime.fromisoformat(cti_data['last_seen']),
                mitre_tactics=cti_data.get('mitre_tactics', []),
                mitre_techniques=cti_data.get('mitre_techniques', [])
            )
            cti_objects.append(cti)

        # Generate rules
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        generated_rules = loop.run_until_complete(
            rule_engine.generate_rules_from_cti(cti_objects, rule_format)
        )
        loop.close()

        # Store generated rules
        rule_summaries = []
        for rule in generated_rules:
            rule_engine.rules[rule.rule_id] = rule
            rule_summaries.append({
                'rule_id': rule.rule_id,
                'name': rule.name,
                'format': rule.rule_format.value,
                'status': rule.status.value,
                'threat_context': rule.threat_context.value,
                'created_date': rule.created_date.isoformat()
            })

        return jsonify({
            'status': 'success',
            'generated_count': len(generated_rules),
            'rules': rule_summaries,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/rules', methods=['GET'])
def list_rules():
    """List all detection rules with optional filtering"""
    try:
        # Get query parameters
        status_filter = request.args.get('status')
        format_filter = request.args.get('format')
        limit = int(request.args.get('limit', 100))
        offset = int(request.args.get('offset', 0))

        # Filter rules
        filtered_rules = list(rule_engine.rules.values())

        if status_filter:
            filtered_rules = [r for r in filtered_rules if r.status.value == status_filter]

        if format_filter:
            filtered_rules = [r for r in filtered_rules if r.rule_format.value == format_filter]

        # Apply pagination
        total_count = len(filtered_rules)
        paginated_rules = filtered_rules[offset:offset + limit]

        # Format response
        rules_data = []
        for rule in paginated_rules:
            rules_data.append({
                'rule_id': rule.rule_id,
                'name': rule.name,
                'description': rule.description,
                'format': rule.rule_format.value,
                'status': rule.status.value,
                'version': rule.version,
                'created_date': rule.created_date.isoformat(),
                'last_updated': rule.last_updated.isoformat(),
                'performance': {
                    'precision': rule.performance_metrics.precision,
                    'recall': rule.performance_metrics.recall,
                    'f1_score': rule.performance_metrics.f1_score,
                    'total_alerts': rule.performance_metrics.total_alerts
                },
                'deployed_platforms': rule.deployed_platforms,
                'threat_context': rule.threat_context.value,
                'mitre_tactics': rule.mitre_tactics,
                'mitre_techniques': rule.mitre_techniques
            })

        return jsonify({
            'rules': rules_data,
            'pagination': {
                'total_count': total_count,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total_count
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/rules/<rule_id>', methods=['GET'])
def get_rule_details(rule_id):
    """Get detailed information about a specific rule"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        rule_status = loop.run_until_complete(rule_engine.get_rule_status(rule_id))
        loop.close()

        if not rule_status:
            return jsonify({'error': 'Rule not found'}), 404

        # Get full rule data
        rule = rule_engine.rules[rule_id]

        return jsonify({
            'rule_id': rule_id,
            'name': rule.name,
            'description': rule.description,
            'rule_content': rule.rule_content,
            'format': rule.rule_format.value,
            'status': rule.status.value,
            'version': rule.version,
            'created_date': rule.created_date.isoformat(),
            'last_updated': rule.last_updated.isoformat(),
            'next_review_date': rule.next_review_date.isoformat(),
            'performance_metrics': {
                'precision': rule.performance_metrics.precision,
                'recall': rule.performance_metrics.recall,
                'f1_score': rule.performance_metrics.f1_score,
                'true_positives': rule.performance_metrics.true_positives,
                'false_positives': rule.performance_metrics.false_positives,
                'false_negatives': rule.performance_metrics.false_negatives,
                'total_alerts': rule.performance_metrics.total_alerts,
                'last_triggered': rule.performance_metrics.last_triggered.isoformat() if rule.performance_metrics.last_triggered else None
            },
            'threat_context': {
                'context': rule.threat_context.value,
                'mitre_tactics': rule.mitre_tactics,
                'mitre_techniques': rule.mitre_techniques,
                'threat_actors': rule.threat_actors,
                'malware_families': rule.malware_families
            },
            'deployment': {
                'platforms': rule.deployed_platforms,
                'configs': rule.deployment_config
            },
            'source_cti': [{
                'source': cti.source,
                'ioc_type': cti.ioc_type,
                'ioc_value': cti.ioc_value,
                'confidence_score': cti.confidence_score
            } for cti in rule.source_cti],
            'update_history_count': len(rule.update_history),
            'quality_score': rule.quality_score,
            'tags': rule.tags
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# === Rule Performance and Analytics Endpoints ===

@app.route('/api/rules/<rule_id>/performance', methods=['POST'])
def update_rule_performance(rule_id):
    """Update rule performance metrics with new alert data"""
    try:
        data = request.get_json()

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        success = loop.run_until_complete(rule_engine.update_rule_performance(rule_id, data))
        loop.close()

        if success:
            return jsonify({
                'status': 'success',
                'message': 'Performance metrics updated',
                'rule_id': rule_id,
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({'error': 'Failed to update performance metrics'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/rules/<rule_id>/performance', methods=['GET'])
def get_rule_performance(rule_id):
    """Get detailed performance metrics for a rule"""
    try:
        if rule_id not in rule_engine.rules:
            return jsonify({'error': 'Rule not found'}), 404

        rule = rule_engine.rules[rule_id]
        metrics = rule.performance_metrics

        return jsonify({
            'rule_id': rule_id,
            'performance_metrics': {
                'precision': metrics.precision,
                'recall': metrics.recall,
                'f1_score': metrics.f1_score,
                'true_positives': metrics.true_positives,
                'false_positives': metrics.false_positives,
                'false_negatives': metrics.false_negatives,
                'total_alerts': metrics.total_alerts,
                'avg_response_time': metrics.avg_response_time,
                'performance_trend': metrics.performance_trend,
                'analyst_feedback_score': metrics.analyst_feedback_score,
                'coverage_effectiveness': metrics.coverage_effectiveness,
                'last_triggered': metrics.last_triggered.isoformat() if metrics.last_triggered else None
            },
            'health_status': 'healthy' if metrics.precision >= 0.8 and metrics.recall >= 0.7 else 'needs_attention',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analytics/performance-summary', methods=['GET'])
def get_performance_summary():
    """Get overall performance analytics across all rules"""
    try:
        if not rule_engine.rules:
            return jsonify({
                'total_rules': 0,
                'performance_summary': {},
                'timestamp': datetime.now().isoformat()
            })

        # Calculate aggregate metrics
        total_rules = len(rule_engine.rules)
        healthy_rules = 0
        needs_attention = 0
        total_alerts = 0
        total_tp = total_fp = total_fn = 0

        precision_scores = []
        recall_scores = []
        f1_scores = []

        for rule in rule_engine.rules.values():
            metrics = rule.performance_metrics

            if metrics.precision >= 0.8 and metrics.recall >= 0.7:
                healthy_rules += 1
            else:
                needs_attention += 1

            total_alerts += metrics.total_alerts
            total_tp += metrics.true_positives
            total_fp += metrics.false_positives
            total_fn += metrics.false_negatives

            if metrics.precision > 0:
                precision_scores.append(metrics.precision)
            if metrics.recall > 0:
                recall_scores.append(metrics.recall)
            if metrics.f1_score > 0:
                f1_scores.append(metrics.f1_score)

        # Calculate averages
        avg_precision = sum(precision_scores) / len(precision_scores) if precision_scores else 0
        avg_recall = sum(recall_scores) / len(recall_scores) if recall_scores else 0
        avg_f1 = sum(f1_scores) / len(f1_scores) if f1_scores else 0

        # Calculate overall effectiveness
        overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0

        return jsonify({
            'total_rules': total_rules,
            'rule_health': {
                'healthy_rules': healthy_rules,
                'needs_attention': needs_attention,
                'health_percentage': (healthy_rules / total_rules * 100) if total_rules > 0 else 0
            },
            'performance_summary': {
                'total_alerts': total_alerts,
                'true_positives': total_tp,
                'false_positives': total_fp,
                'false_negatives': total_fn,
                'overall_precision': round(overall_precision, 3),
                'overall_recall': round(overall_recall, 3),
                'avg_precision': round(avg_precision, 3),
                'avg_recall': round(avg_recall, 3),
                'avg_f1_score': round(avg_f1, 3)
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# === Rule Improvement and Suggestions Endpoints ===

@app.route('/api/rules/<rule_id>/suggestions', methods=['GET'])
def get_improvement_suggestions(rule_id):
    """Get AI-powered improvement suggestions for a rule"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        suggestions = loop.run_until_complete(rule_engine.get_improvement_suggestions(rule_id))
        loop.close()

        return jsonify({
            'rule_id': rule_id,
            'suggestions': suggestions,
            'count': len(suggestions),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/rules/<rule_id>/suggestions/generate', methods=['POST'])
def generate_improvement_suggestions(rule_id):
    """Generate new improvement suggestions for a rule"""
    try:
        if rule_id not in rule_engine.rules:
            return jsonify({'error': 'Rule not found'}), 404

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        suggestions = loop.run_until_complete(rule_engine.generate_improvement_suggestions(rule_id))
        loop.close()

        suggestion_data = [{
            'suggestion_id': s.suggestion_id,
            'type': s.suggestion_type,
            'description': s.description,
            'proposed_changes': s.proposed_changes,
            'confidence_score': s.confidence_score,
            'expected_impact': s.expected_impact,
            'priority': s.implementation_priority,
            'status': s.status
        } for s in suggestions]

        return jsonify({
            'status': 'success',
            'rule_id': rule_id,
            'suggestions': suggestion_data,
            'count': len(suggestions),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# === Rule Testing and Validation Endpoints ===

@app.route('/api/rules/<rule_id>/tests', methods=['GET'])
def get_rule_tests(rule_id):
    """Get all test cases for a rule"""
    try:
        if rule_id not in rule_engine.test_cases:
            return jsonify({
                'rule_id': rule_id,
                'test_cases': [],
                'count': 0,
                'timestamp': datetime.now().isoformat()
            })

        test_cases = rule_engine.test_cases[rule_id]
        test_data = [{
            'test_id': tc.test_id,
            'test_name': tc.test_name,
            'test_type': tc.test_type,
            'expected_result': tc.expected_result,
            'actual_result': tc.actual_result,
            'test_status': tc.test_status,
            'execution_time': tc.execution_time,
            'last_run': tc.last_run.isoformat() if tc.last_run else None,
            'error_message': tc.error_message
        } for tc in test_cases]

        return jsonify({
            'rule_id': rule_id,
            'test_cases': test_data,
            'count': len(test_cases),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/rules/<rule_id>/tests', methods=['POST'])
def create_rule_test(rule_id):
    """Create a new test case for a rule"""
    try:
        data = request.get_json()

        test_case = RuleTestCase(
            rule_id=rule_id,
            test_name=data['test_name'],
            test_type=data['test_type'],
            test_data=data['test_data'],
            expected_result=data['expected_result']
        )

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        success = loop.run_until_complete(rule_engine.create_test_case(rule_id, test_case))
        loop.close()

        if success:
            return jsonify({
                'status': 'success',
                'test_id': test_case.test_id,
                'message': 'Test case created successfully',
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({'error': 'Failed to create test case'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/rules/<rule_id>/tests/run', methods=['POST'])
def run_rule_tests(rule_id):
    """Run all test cases for a rule"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        test_results = loop.run_until_complete(rule_engine.run_rule_tests(rule_id))
        loop.close()

        return jsonify({
            'rule_id': rule_id,
            'test_results': test_results,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# === Rule Deployment Endpoints ===

@app.route('/api/rules/<rule_id>/deploy', methods=['POST'])
def deploy_rule(rule_id):
    """Deploy rule to specified SIEM platforms"""
    try:
        data = request.get_json()
        platforms = data.get('platforms', [])
        config = data.get('config', {})

        if not platforms:
            return jsonify({'error': 'No platforms specified'}), 400

        deployment_results = []

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        for platform in platforms:
            success = loop.run_until_complete(
                rule_engine.deploy_rule_to_platform(rule_id, platform, config.get(platform, {}))
            )
            deployment_results.append({
                'platform': platform,
                'success': success,
                'timestamp': datetime.now().isoformat()
            })

        loop.close()

        successful_deployments = [r for r in deployment_results if r['success']]

        return jsonify({
            'status': 'completed',
            'rule_id': rule_id,
            'deployments': deployment_results,
            'successful_count': len(successful_deployments),
            'total_platforms': len(platforms),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 400

# === WebSocket Events for Real-time Updates ===

@app.route('/api/rules/<rule_id>/subscribe', methods=['POST'])
def subscribe_to_rule_updates(rule_id):
    """Subscribe to real-time rule updates (WebSocket placeholder)"""
    # This would implement WebSocket subscription in a real deployment
    return jsonify({
        'status': 'subscribed',
        'rule_id': rule_id,
        'events': ['performance_update', 'status_change', 'new_suggestion', 'test_completion'],
        'message': 'WebSocket subscription placeholder - would provide real-time updates',
        'timestamp': datetime.now().isoformat()
    })

# === Batch Operations Endpoints ===

@app.route('/api/rules/batch/analyze', methods=['POST'])
def batch_analyze_rules():
    """Batch analyze multiple rules for improvements"""
    try:
        data = request.get_json()
        rule_ids = data.get('rule_ids', [])

        if not rule_ids:
            return jsonify({'error': 'No rule IDs provided'}), 400

        analysis_results = []

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        for rule_id in rule_ids:
            if rule_id in rule_engine.rules:
                suggestions = loop.run_until_complete(rule_engine.generate_improvement_suggestions(rule_id))
                analysis_results.append({
                    'rule_id': rule_id,
                    'suggestions_count': len(suggestions),
                    'status': 'analyzed'
                })
            else:
                analysis_results.append({
                    'rule_id': rule_id,
                    'status': 'not_found'
                })

        loop.close()

        return jsonify({
            'status': 'completed',
            'analyzed_rules': len([r for r in analysis_results if r['status'] == 'analyzed']),
            'results': analysis_results,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    logger.info("Starting Rule Lifecycle Management API on port 5014")
    app.run(host='0.0.0.0', port=5014, debug=True)