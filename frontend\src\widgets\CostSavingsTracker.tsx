import React from 'react'
import { TrendingUp, DollarSign, Zap, Brain } from 'lucide-react'

interface SavingMetric {
  title: string
  value: string
  subtitle: string
  trend: string
  icon: React.ReactNode
  color: string
}

export const CostSavingsTracker: React.FC = () => {
  const metrics: SavingMetric[] = [
    {
      title: 'Total Cost Saved',
      value: '$124,567',
      subtitle: 'YTD 2025',
      trend: '+34% vs 2024',
      icon: <DollarSign className="text-white" size={20} />,
      color: 'bg-green-500'
    },
    {
      title: 'Patterns Crystallized',
      value: '8,432',
      subtitle: '99.97% cost reduction',
      trend: '+156 this week',
      icon: <Brain className="text-white" size={20} />,
      color: 'bg-purple-500'
    },
    {
      title: 'AI Calls Saved',
      value: '45.6K',
      subtitle: 'Via pattern matching',
      trend: '95% cache hit rate',
      icon: <Zap className="text-white" size={20} />,
      color: 'bg-blue-500'
    }
  ]

  return (
    <div className="p-6 bg-gray-50 h-full overflow-auto">
      <h2 className="text-xl font-bold mb-4">Cost Optimization Metrics</h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {metrics.map((metric, idx) => (
          <div key={idx} className="bg-white p-4 rounded-lg shadow-sm border">
            <div className="flex justify-between items-start mb-2">
              <div className="flex-1">
                <p className="text-sm text-gray-600">{metric.title}</p>
                <p className="text-2xl font-bold mt-1">{metric.value}</p>
                <p className="text-xs text-gray-500 mt-1">{metric.subtitle}</p>
              </div>
              <div className={`p-2 rounded-lg ${metric.color}`}>
                {metric.icon}
              </div>
            </div>
            <div className="flex items-center gap-1 mt-2">
              <TrendingUp size={14} className="text-green-500" />
              <span className="text-sm text-green-600">{metric.trend}</span>
            </div>
          </div>
        ))}
      </div>

      {/* AI Model Usage Breakdown */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="font-semibold mb-4">AI Model Usage (Today)</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm">Gemini Flash (Free)</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">12,345 calls</span>
              <span className="text-xs text-gray-500">$0.00</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-sm">Pattern Cache Hits</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">8,901 calls</span>
              <span className="text-xs text-gray-500">$0.00</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
              <span className="text-sm">GPT-4 Turbo</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">234 calls</span>
              <span className="text-xs text-gray-500">$23.40</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
              <span className="text-sm">Claude Opus</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">56 calls</span>
              <span className="text-xs text-gray-500">$16.80</span>
            </div>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t">
          <div className="flex justify-between items-center">
            <span className="font-semibold">Total Cost Today</span>
            <span className="font-bold text-lg">$40.20</span>
          </div>
          <div className="flex justify-between items-center text-sm text-gray-600 mt-1">
            <span>vs Traditional SIEM</span>
            <span className="text-red-600 font-medium">~$1,200</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CostSavingsTracker