# Microsoft Sentinel SIEM Configuration
# Query Language: KQL (Kusto Query Language)

platform:
  name: sentinel
  display_name: Microsoft Sentinel
  query_language: kql
  description: Microsoft's Kusto Query Language for Azure Sentinel
  vendor: Microsoft Corporation
  version: "1.0"
  active: true

# Field mappings: generic_field -> Sentinel field
field_mappings:
  source_ip: SourceIP
  destination_ip: DestinationIP
  username: Account
  process_name: ProcessName
  file_hash: FileHash
  event_id: EventID
  hostname: Computer
  port: DestinationPort
  source_port: SourcePort
  destination_port: DestinationPort
  domain: Domain
  url: Url
  file_name: FileName
  file_path: FilePath
  registry_path: RegistryKey
  command_line: CommandLine
  parent_process: ParentProcessName
  network_protocol: Protocol
  http_method: HttpMethod
  user_agent: UserAgent
  email_sender: SenderAddress
  email_recipient: RecipientAddress
  dns_query: QueryName
  service_name: ServiceName
  account_name: AccountName
  process_id: ProcessId
  parent_process_id: ParentProcessId
  user_domain: UserDomain
  host_os: OSName
  event_action: Activity
  threat_category: ThreatCategory
  severity: AlertSeverity

# Operator mappings: generic_operator -> Sentinel operator
operator_mappings:
  equals: "=="
  not_equals: "!="
  contains: contains
  not_contains: "!contains"
  regex: matches regex
  greater_than: ">"
  less_than: "<"
  greater_equal: ">="
  less_equal: "<="
  in_list: in
  not_in_list: "!in"
  has: has
  has_any: has_any
  starts_with: startswith
  ends_with: endswith

# Time field for temporal queries
time_field: TimeGenerated

# Query syntax specifics
syntax:
  comment: "//"
  string_quote: "\""
  escape_char: "\\"
  wildcard: "*"
  field_separator: " "
  logical_and: and
  logical_or: or
  logical_not: not
  table_operator: "|"
  where_operator: "where"
  summarize_operator: "summarize"
  join_operator: "join"
  extend_operator: "extend"
  project_operator: "project"
  case_sensitive: false

# Platform-specific features
metadata:
  supports_regex: true
  supports_wildcards: false
  supports_joins: true
  supports_functions: true
  supports_let_statements: true
  max_query_results: 500000
  default_time_range: "24h"
  table_prefix: ""

  # Common tables
  common_tables:
    - SecurityEvent
    - Syslog
    - CommonSecurityLog
    - DeviceEvents
    - DeviceProcessEvents
    - DeviceNetworkEvents
    - DeviceFileEvents
    - DeviceRegistryEvents
    - DeviceLogonEvents
    - SigninLogs
    - AuditLogs
    - OfficeActivity
    - AzureActivity
    - ThreatIntelligenceIndicator

  # Detection rule template
  rule_template: |
    SecurityEvent
    | where TimeGenerated > ago({time_range})
    | where {conditions}
    | summarize count() by {group_fields}
    | where count_ > {threshold}

  # Analytic rule types
  rule_types:
    - Scheduled  # Time-based query rules
    - Fusion  # ML correlation
    - Microsoft Security  # Microsoft product alerts
    - ML Behavior Analytics  # UEBA rules
    - Threat Intelligence  # IOC matching

  # Data connector types
  data_connectors:
    - Azure Active Directory
    - Office 365
    - Microsoft Defender for Endpoint
    - Microsoft Defender for Cloud
    - Microsoft Defender for Identity
    - Azure Firewall
    - DNS
    - Windows Firewall
    - Syslog
    - CEF
    - Custom logs

  # MITRE ATT&CK integration
  supports_mitre: true

  # Incident management
  supports_incidents: true
  supports_automation: true  # Logic Apps / Playbooks
