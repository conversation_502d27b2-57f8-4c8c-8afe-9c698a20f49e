"""
Log Retention Policy Engine
Intelligent log retention based on value, vulnerability scores (EPSS), and cost optimization
"""

import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import asyncpg
from uuid import uuid4
import aiohttp

logger = logging.getLogger(__name__)


@dataclass
class RetentionPolicy:
    """Retention policy definition"""
    policy_id: str
    name: str
    description: str
    priority: int  # Higher = more important
    retention_days: int
    storage_tier: str  # hot, warm, cold, archive
    conditions: Dict[str, Any]
    created_at: datetime
    updated_at: datetime


@dataclass
class LogRetentionDecision:
    """Retention decision for a log or event"""
    log_id: str
    policy_applied: str
    retention_days: int
    storage_tier: str
    reasoning: str
    expires_at: datetime
    value_score: float  # 0.0 - 1.0
    cost_estimate: float  # USD per month


class LogRetentionPolicyEngine:
    """
    Intelligent log retention engine

    Features:
    - Value-based retention (security value, investigation value)
    - EPSS score integration for vulnerability-related logs
    - Cost optimization (tiered storage)
    - Compliance-aware retention
    - Automatic policy application
    """

    def __init__(self, db_pool: asyncpg.Pool, redis_client):
        self.db_pool = db_pool
        self.redis = redis_client

        # Storage tier costs (USD per GB per month)
        self.storage_costs = {
            'hot': 0.023,     # High-performance (7 days)
            'warm': 0.01,     # Standard (30 days)
            'cold': 0.004,    # Archive (90 days)
            'archive': 0.0012 # Deep archive (365+ days)
        }

        # Base retention policies
        self.base_policies = [
            # Critical security events
            RetentionPolicy(
                policy_id='critical_security',
                name='Critical Security Events',
                description='High-value security events (incidents, breaches, attacks)',
                priority=1000,
                retention_days=365,
                storage_tier='warm',
                conditions={
                    'severity': ['critical', 'high'],
                    'event_type': ['incident', 'attack', 'breach', 'compromise']
                },
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),

            # Compliance requirements
            RetentionPolicy(
                policy_id='compliance_required',
                name='Compliance-Required Logs',
                description='Logs required by regulatory compliance (HIPAA, PCI-DSS, SOX)',
                priority=900,
                retention_days=2555,  # 7 years
                storage_tier='cold',
                conditions={
                    'compliance_tags': ['hipaa', 'pci-dss', 'sox', 'gdpr']
                },
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),

            # Investigation evidence
            RetentionPolicy(
                policy_id='investigation_evidence',
                name='Investigation Evidence',
                description='Logs marked as evidence in active investigations',
                priority=950,
                retention_days=180,
                storage_tier='warm',
                conditions={
                    'is_evidence': True
                },
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),

            # High EPSS vulnerabilities
            RetentionPolicy(
                policy_id='high_epss_vulns',
                name='High EPSS Vulnerability Logs',
                description='Logs related to high EPSS score vulnerabilities',
                priority=800,
                retention_days=180,
                storage_tier='warm',
                conditions={
                    'epss_score': {'gte': 0.7}
                },
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),

            # Successful authentication
            RetentionPolicy(
                policy_id='successful_auth',
                name='Successful Authentication',
                description='Successful authentication events (lower value)',
                priority=200,
                retention_days=30,
                storage_tier='warm',
                conditions={
                    'event_type': ['authentication'],
                    'outcome': ['success']
                },
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),

            # Failed authentication
            RetentionPolicy(
                policy_id='failed_auth',
                name='Failed Authentication',
                description='Failed authentication events (security relevant)',
                priority=500,
                retention_days=90,
                storage_tier='warm',
                conditions={
                    'event_type': ['authentication'],
                    'outcome': ['failure', 'failed']
                },
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),

            # Normal traffic
            RetentionPolicy(
                policy_id='normal_traffic',
                name='Normal Network Traffic',
                description='Routine network traffic without anomalies',
                priority=100,
                retention_days=7,
                storage_tier='hot',
                conditions={
                    'event_type': ['network'],
                    'is_anomaly': False
                },
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),

            # Default policy
            RetentionPolicy(
                policy_id='default',
                name='Default Retention',
                description='Default policy for unclassified logs',
                priority=50,
                retention_days=30,
                storage_tier='warm',
                conditions={},
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
        ]

    async def initialize_policies(self):
        """Initialize retention policies in database"""
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.transaction():
                    for policy in self.base_policies:
                        await conn.execute("""
                            INSERT INTO log_retention_policies (
                                policy_id,
                                name,
                                description,
                                priority,
                                retention_days,
                                storage_tier,
                                conditions,
                                created_at,
                                updated_at
                            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                            ON CONFLICT (policy_id) DO UPDATE SET
                                name = EXCLUDED.name,
                                description = EXCLUDED.description,
                                priority = EXCLUDED.priority,
                                retention_days = EXCLUDED.retention_days,
                                storage_tier = EXCLUDED.storage_tier,
                                conditions = EXCLUDED.conditions,
                                updated_at = EXCLUDED.updated_at
                        """,
                            policy.policy_id,
                            policy.name,
                            policy.description,
                            policy.priority,
                            policy.retention_days,
                            policy.storage_tier,
                            json.dumps(policy.conditions),
                            policy.created_at,
                            policy.updated_at
                        )

                logger.info(f"Initialized {len(self.base_policies)} retention policies")

        except Exception as e:
            logger.error(f"Error initializing policies: {e}")

    async def apply_retention_policy(self, log: Dict[str, Any]) -> LogRetentionDecision:
        """
        Apply retention policy to a log

        Args:
            log: Log event with metadata

        Returns:
            LogRetentionDecision with policy applied
        """
        log_id = log.get('event_id') or log.get('log_id')

        # Get EPSS score if vulnerability-related
        epss_score = await self._get_epss_score(log)
        if epss_score:
            log['epss_score'] = epss_score

        # Calculate value score
        value_score = self._calculate_value_score(log)

        # Find matching policy (highest priority)
        matched_policy = await self._match_policy(log)

        # Calculate cost
        log_size_kb = len(json.dumps(log)) / 1024
        log_size_gb = log_size_kb / (1024 * 1024)
        cost_per_month = log_size_gb * self.storage_costs[matched_policy.storage_tier]

        decision = LogRetentionDecision(
            log_id=log_id,
            policy_applied=matched_policy.name,
            retention_days=matched_policy.retention_days,
            storage_tier=matched_policy.storage_tier,
            reasoning=self._generate_reasoning(matched_policy, log, value_score),
            expires_at=datetime.utcnow() + timedelta(days=matched_policy.retention_days),
            value_score=value_score,
            cost_estimate=cost_per_month
        )

        # Store decision
        await self._store_retention_decision(decision)

        return decision

    async def _get_epss_score(self, log: Dict) -> Optional[float]:
        """
        Get EPSS score for vulnerability-related logs

        EPSS (Exploit Prediction Scoring System) predicts likelihood of exploitation
        """
        # Check if log is vulnerability-related
        cve_id = log.get('cve_id')
        if not cve_id:
            return None

        try:
            # Try Redis cache first
            cache_key = f"epss:score:{cve_id}"
            cached_score = self.redis.get(cache_key)
            if cached_score:
                return float(cached_score)

            # Fetch from FIRST.org EPSS API
            url = f"https://api.first.org/data/v1/epss?cve={cve_id}"
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('data'):
                            epss_score = float(data['data'][0].get('epss', 0.0))
                            # Cache for 24 hours
                            self.redis.setex(cache_key, 86400, str(epss_score))
                            return epss_score

        except Exception as e:
            logger.error(f"Error fetching EPSS score for {cve_id}: {e}")

        return None

    def _calculate_value_score(self, log: Dict) -> float:
        """
        Calculate overall value score for a log (0.0 - 1.0)

        Factors:
        - Severity
        - Security relevance
        - Investigation value
        - EPSS score (if applicable)
        - Entity richness
        """
        score = 0.0

        # Severity scoring
        severity = log.get('severity', '').lower()
        severity_scores = {
            'critical': 1.0,
            'high': 0.8,
            'medium': 0.5,
            'low': 0.3,
            'info': 0.1
        }
        score += severity_scores.get(severity, 0.3)

        # Security event type scoring
        event_type = log.get('event_type', '').lower()
        if event_type in ['incident', 'attack', 'breach', 'compromise']:
            score += 0.3
        elif event_type in ['authentication', 'access', 'privilege_escalation']:
            score += 0.2

        # Investigation evidence
        if log.get('is_evidence'):
            score += 0.3

        # EPSS score (if applicable)
        epss_score = log.get('epss_score')
        if epss_score:
            score += epss_score * 0.5  # Weight EPSS at 50%

        # Entity richness
        entities = log.get('entities', [])
        if entities:
            score += min(0.2, len(entities) * 0.02)

        # Compliance requirement
        if log.get('compliance_tags'):
            score += 0.4

        # Normalize to 0.0 - 1.0
        return min(1.0, score)

    async def _match_policy(self, log: Dict) -> RetentionPolicy:
        """Match log to highest priority policy"""
        # Sort policies by priority (descending)
        sorted_policies = sorted(self.base_policies, key=lambda p: p.priority, reverse=True)

        for policy in sorted_policies:
            if self._matches_conditions(log, policy.conditions):
                return policy

        # Return default policy if no match
        return next(p for p in sorted_policies if p.policy_id == 'default')

    def _matches_conditions(self, log: Dict, conditions: Dict) -> bool:
        """Check if log matches policy conditions"""
        if not conditions:
            return True  # Empty conditions = default policy

        for field, condition in conditions.items():
            log_value = log.get(field)

            if log_value is None:
                return False

            # Handle different condition types
            if isinstance(condition, list):
                # List = OR condition
                if log_value not in condition:
                    return False
            elif isinstance(condition, dict):
                # Dict = comparison operators
                if 'gte' in condition:
                    if not (log_value >= condition['gte']):
                        return False
                if 'lte' in condition:
                    if not (log_value <= condition['lte']):
                        return False
                if 'eq' in condition:
                    if not (log_value == condition['eq']):
                        return False
            elif isinstance(condition, bool):
                # Boolean exact match
                if log_value != condition:
                    return False
            else:
                # Direct value comparison
                if log_value != condition:
                    return False

        return True

    def _generate_reasoning(self, policy: RetentionPolicy, log: Dict, value_score: float) -> str:
        """Generate human-readable reasoning for retention decision"""
        reasons = []

        reasons.append(f"Policy: {policy.name}")
        reasons.append(f"Value Score: {value_score:.2f}/1.0")

        if log.get('severity'):
            reasons.append(f"Severity: {log['severity']}")

        if log.get('is_evidence'):
            reasons.append("Marked as investigation evidence")

        if log.get('epss_score'):
            reasons.append(f"EPSS Score: {log['epss_score']:.3f}")

        if log.get('compliance_tags'):
            reasons.append(f"Compliance: {', '.join(log['compliance_tags'])}")

        reasons.append(f"Retention: {policy.retention_days} days ({policy.storage_tier} tier)")

        return " | ".join(reasons)

    async def _store_retention_decision(self, decision: LogRetentionDecision):
        """Store retention decision in database"""
        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO log_retention_decisions (
                        log_id,
                        policy_applied,
                        retention_days,
                        storage_tier,
                        reasoning,
                        expires_at,
                        value_score,
                        cost_estimate,
                        decided_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                    ON CONFLICT (log_id) DO UPDATE SET
                        policy_applied = EXCLUDED.policy_applied,
                        retention_days = EXCLUDED.retention_days,
                        storage_tier = EXCLUDED.storage_tier,
                        reasoning = EXCLUDED.reasoning,
                        expires_at = EXCLUDED.expires_at,
                        value_score = EXCLUDED.value_score,
                        cost_estimate = EXCLUDED.cost_estimate,
                        decided_at = EXCLUDED.decided_at
                """,
                    decision.log_id,
                    decision.policy_applied,
                    decision.retention_days,
                    decision.storage_tier,
                    decision.reasoning,
                    decision.expires_at,
                    decision.value_score,
                    decision.cost_estimate,
                    datetime.utcnow()
                )
        except Exception as e:
            logger.error(f"Error storing retention decision: {e}")

    async def cleanup_expired_logs(self) -> Dict[str, int]:
        """
        Clean up expired logs based on retention decisions

        Returns:
            Statistics on cleanup
        """
        stats = {
            'total_checked': 0,
            'expired': 0,
            'moved_to_cold': 0,
            'moved_to_archive': 0,
            'deleted': 0
        }

        try:
            async with self.db_pool.acquire() as conn:
                # Find expired logs
                expired_logs = await conn.fetch("""
                    SELECT
                        d.log_id,
                        d.storage_tier,
                        d.expires_at,
                        e.event_id
                    FROM log_retention_decisions d
                    LEFT JOIN events e ON d.log_id = e.event_id
                    WHERE d.expires_at <= NOW()
                    ORDER BY d.expires_at ASC
                    LIMIT 10000
                """)

                stats['total_checked'] = len(expired_logs)
                stats['expired'] = len(expired_logs)

                for log in expired_logs:
                    storage_tier = log['storage_tier']

                    if storage_tier == 'hot':
                        # Move to warm
                        await self._move_to_tier(log['log_id'], 'warm')
                        stats['moved_to_cold'] += 1
                    elif storage_tier == 'warm':
                        # Move to cold
                        await self._move_to_tier(log['log_id'], 'cold')
                        stats['moved_to_cold'] += 1
                    elif storage_tier == 'cold':
                        # Move to archive
                        await self._move_to_tier(log['log_id'], 'archive')
                        stats['moved_to_archive'] += 1
                    else:
                        # Delete from archive
                        await self._delete_log(log['log_id'])
                        stats['deleted'] += 1

                logger.info(f"Cleanup complete: {stats}")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

        return stats

    async def _move_to_tier(self, log_id: str, tier: str):
        """Move log to different storage tier (placeholder)"""
        # In production, this would move data to different storage backend
        logger.info(f"Moving log {log_id} to {tier} tier")

    async def _delete_log(self, log_id: str):
        """Delete log permanently"""
        try:
            async with self.db_pool.acquire() as conn:
                async with conn.transaction():
                    await conn.execute("DELETE FROM events WHERE event_id = $1", log_id)
                    await conn.execute("DELETE FROM log_retention_decisions WHERE log_id = $1", log_id)
                    logger.info(f"Permanently deleted log {log_id}")
        except Exception as e:
            logger.error(f"Error deleting log {log_id}: {e}")

    async def get_retention_stats(self) -> Dict[str, Any]:
        """Get retention statistics"""
        try:
            async with self.db_pool.acquire() as conn:
                # Count by storage tier
                tier_stats = await conn.fetch("""
                    SELECT
                        storage_tier,
                        COUNT(*) as count,
                        AVG(value_score) as avg_value_score,
                        SUM(cost_estimate) as total_cost
                    FROM log_retention_decisions
                    GROUP BY storage_tier
                """)

                # Total logs
                total_row = await conn.fetchrow("SELECT COUNT(*) as total FROM log_retention_decisions")
                total = total_row['total']

                # Expiring soon
                expiring_row = await conn.fetchrow("""
                    SELECT COUNT(*) as expiring_soon
                    FROM log_retention_decisions
                    WHERE expires_at <= NOW() + INTERVAL '7 days'
                """)
                expiring_soon = expiring_row['expiring_soon']

                return {
                    'total_logs': total,
                    'expiring_soon': expiring_soon,
                    'by_tier': [dict(s) for s in tier_stats],
                    'generated_at': datetime.utcnow().isoformat()
                }

        except Exception as e:
            logger.error(f"Error getting retention stats: {e}")
            return {}
