#!/usr/bin/env python3
"""
Batch migrate all remaining cursor blocks in backend_engine.py

This reads the file, finds all cursor blocks, and converts them to asyncpg.
"""

import re
import sys

def convert_sql_placeholders(sql, num_params):
    """Convert %s to $1, $2, $3..."""
    result = sql
    for i in range(1, num_params + 1):
        result = result.replace('%s', f'${i}', 1)
    return result

def find_cursor_blocks(content):
    """Find all cursor blocks in the file"""
    lines = content.split('\n')
    blocks = []

    for i, line in enumerate(lines):
        if 'cursor = self.db_connection.cursor()' in line:
            blocks.append(i)

    return blocks

def extract_block(lines, start_line):
    """Extract a cursor block from start to cursor.close() or implicit end"""
    indent = len(lines[start_line]) - len(lines[start_line].lstrip())
    block_lines = [start_line]

    i = start_line + 1
    found_close = False

    while i < len(lines):
        line = lines[i]
        current_indent = len(line) - len(line.lstrip())

        # Check for cursor.close()
        if 'cursor.close()' in line:
            block_lines.append(i)
            found_close = True
            break

        # Check for self.db_connection.commit()
        if 'self.db_connection.commit()' in line:
            block_lines.append(i)
            i += 1
            continue

        # Check for end of block (dedent or except/finally)
        if line.strip() and current_indent <= indent:
            if line.strip().startswith(('except ', 'finally:', 'else:', 'elif ')):
                break
            # Might be end of function
            if not line.strip().startswith(('self.', 'return', '#')):
                break

        block_lines.append(i)
        i += 1

    return block_lines, found_close

def convert_block(lines, block_line_numbers):
    """Convert a cursor block to asyncpg"""
    start_line = block_line_numbers[0]
    base_indent = len(lines[start_line]) - len(lines[start_line].lstrip())

    # Find the cursor.execute() statement
    execute_start = None
    execute_end = None
    fetch_type = None
    fetch_var = None

    for i, line_num in enumerate(block_line_numbers):
        line = lines[line_num]

        if 'cursor.execute(' in line:
            execute_start = i
            # Find end of execute (matching parentheses)
            paren_depth = line.count('(') - line.count(')')
            execute_end = i
            j = i + 1
            while j < len(block_line_numbers) and paren_depth > 0:
                next_line = lines[block_line_numbers[j]]
                paren_depth += next_line.count('(') - next_line.count(')')
                execute_end = j
                j += 1

        elif 'cursor.fetchone()' in line:
            fetch_type = 'fetchrow'
            match = re.search(r'(\w+)\s*=\s*cursor\.fetchone\(\)', line)
            if match:
                fetch_var = match.group(1)

        elif 'cursor.fetchall()' in line:
            if 'for row in cursor.fetchall()' in line:
                fetch_type = 'fetch_iter'
            else:
                fetch_type = 'fetch'
                match = re.search(r'(\w+)\s*=\s*cursor\.fetchall\(\)', line)
                if match:
                    fetch_var = match.group(1)

    if execute_start is None:
        # No execute found, can't convert
        return None

    # Extract the SQL and parameters from execute
    execute_lines = [lines[block_line_numbers[i]] for i in range(execute_start, execute_end + 1)]
    execute_text = '\n'.join(execute_lines)

    # Parse the execute statement
    # Pattern: cursor.execute("""SQL""", (params))
    match = re.search(r'cursor\.execute\(\s*"""(.+?)"""\s*,\s*\((.+?)\)\s*\)', execute_text, re.DOTALL)
    if not match:
        # Try single quotes
        match = re.search(r'cursor\.execute\(\s*"""(.+?)"""\s*\)', execute_text, re.DOTALL)
        if match:
            sql = match.group(1)
            params = None
        else:
            return None
    else:
        sql = match.group(1)
        params = match.group(2)

    # Count parameters
    if params:
        # Count commas at depth 0
        depth = 0
        param_count = 1
        for char in params:
            if char in '([{':
                depth += 1
            elif char in ')]}':
                depth -= 1
            elif char == ',' and depth == 0:
                param_count += 1
        sql_converted = convert_sql_placeholders(sql, param_count)
        # Clean up params (remove newlines, extra spaces)
        params_clean = re.sub(r'\s+', ' ', params).strip()
    else:
        sql_converted = sql
        params_clean = None

    # Generate asyncpg code
    new_lines = []
    new_lines.append(' ' * base_indent + 'async with self.db_pool.acquire() as conn:')

    if fetch_type == 'fetchrow':
        new_lines.append(' ' * (base_indent + 4) + f'{fetch_var} = await conn.fetchrow("""')
        new_lines.append(' ' * (base_indent + 8) + sql_converted.strip())
        if params_clean:
            new_lines.append(' ' * (base_indent + 4) + f'""", {params_clean})')
        else:
            new_lines.append(' ' * (base_indent + 4) + '""")')

    elif fetch_type == 'fetch' or fetch_type == 'fetch_iter':
        var_name = fetch_var if fetch_var else 'rows'
        new_lines.append(' ' * (base_indent + 4) + f'{var_name} = await conn.fetch("""')
        new_lines.append(' ' * (base_indent + 8) + sql_converted.strip())
        if params_clean:
            new_lines.append(' ' * (base_indent + 4) + f'""", {params_clean})')
        else:
            new_lines.append(' ' * (base_indent + 4) + '""")')

    else:
        # Just execute (INSERT/UPDATE/DELETE)
        new_lines.append(' ' * (base_indent + 4) + 'await conn.execute("""')
        new_lines.append(' ' * (base_indent + 8) + sql_converted.strip())
        if params_clean:
            new_lines.append(' ' * (base_indent + 4) + f'""", {params_clean})')
        else:
            new_lines.append(' ' * (base_indent + 4) + '""")')

    # Add remaining non-cursor lines
    for i, line_num in enumerate(block_line_numbers):
        line = lines[line_num]
        if i == 0:  # Skip cursor = line
            continue
        if 'cursor.execute(' in line or (execute_start <= i <= execute_end):
            continue
        if 'cursor.fetchone()' in line or 'cursor.fetchall()' in line:
            if fetch_type == 'fetch_iter' and 'for row in' in line:
                # Update the for loop
                new_lines.append(' ' * (base_indent + 4) + line.strip().replace('cursor.fetchall()', 'rows'))
            continue
        if 'cursor.close()' in line or 'self.db_connection.commit()' in line:
            continue

        # Keep other lines
        if line.strip():
            new_lines.append(' ' * (base_indent + 4) + line.strip())

    return new_lines

def main():
    input_file = r'c:\Users\<USER>\Documents\siemless_v2\engines\backend\backend_engine.py'

    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()

    lines = content.split('\n')

    # Find all cursor blocks
    cursor_lines = find_cursor_blocks(content)
    print(f"Found {len(cursor_lines)} cursor blocks to convert")

    # Process each block
    conversions = []
    for cursor_line in cursor_lines:
        block_lines, has_close = extract_block(lines, cursor_line)
        print(f"\nBlock at line {cursor_line + 1}:")
        print(f"  Lines: {block_lines[0] + 1} to {block_lines[-1] + 1}")
        print(f"  Has close: {has_close}")

        # Convert the block
        new_lines = convert_block(lines, block_lines)
        if new_lines:
            conversions.append((block_lines, new_lines))
            print(f"  Converted successfully ({len(new_lines)} new lines)")
        else:
            print(f"  Failed to convert (complex pattern)")

    # Apply conversions (in reverse order to preserve line numbers)
    for block_lines, new_lines in reversed(conversions):
        # Replace the block
        start = block_lines[0]
        end = block_lines[-1]
        lines[start:end + 1] = new_lines

    # Write output
    output = '\n'.join(lines)
    with open(input_file + '.migrated', 'w', encoding='utf-8') as f:
        f.write(output)

    print(f"\n\nConverted {len(conversions)} blocks")
    print(f"Output written to: {input_file}.migrated")
    print("\nReview the file before replacing the original!")

if __name__ == '__main__':
    main()
