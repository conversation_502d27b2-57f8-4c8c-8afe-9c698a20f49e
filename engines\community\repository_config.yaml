# Community Detection Rule Repositories Configuration
#
# Add your open-source GitHub repositories here for automatic rule syncing
# Each repository will be scanned for detection rules and imported into SIEMLess

repositories:
  # Sigma HQ - Universal SIEM rule format
  - repo_url: https://github.com/SigmaHQ/sigma
    name: Sigma HQ Rules
    enabled: true
    rule_paths:
      - rules/
    rule_formats:
      - sigma
    sync_frequency: daily  # hourly, daily, weekly
    priority: 90  # 0-100, higher = more trusted
    tags:
      - sigma
      - community
      - verified

  # Elastic Detection Rules
  - repo_url: https://github.com/elastic/detection-rules
    name: Elastic Detection Rules
    enabled: true
    rule_paths:
      - rules/
    rule_formats:
      - elastic
      - kql
    sync_frequency: daily
    priority: 85
    tags:
      - elastic
      - edr
      - siem

  # Splunk Security Content (ES Content Update)
  - repo_url: https://github.com/splunk/security_content
    name: Splunk Security Content
    enabled: true
    rule_paths:
      - detections/
    rule_formats:
      - splunk
      - spl
    sync_frequency: daily
    priority: 85
    tags:
      - splunk
      - escu

  # Microsoft Sentinel
  - repo_url: https://github.com/Azure/Azure-Sentinel
    name: Microsoft Sentinel Rules
    enabled: true
    rule_paths:
      - Detections/
    rule_formats:
      - sentinel
      - kql
    sync_frequency: daily
    priority: 80
    tags:
      - sentinel
      - microsoft
      - cloud

  # Panther Labs Detection Rules
  - repo_url: https://github.com/panther-labs/panther-analysis
    name: Panther Analysis
    enabled: false  # Disabled by default - enable as needed
    rule_paths:
      - rules/
    rule_formats:
      - yaml
      - python
    sync_frequency: weekly
    priority: 75
    tags:
      - panther
      - cloud-siem

  # Chronicle Detection Rules
  - repo_url: https://github.com/chronicle/detection-rules
    name: Chronicle Detection Rules
    enabled: false  # Disabled by default
    rule_paths:
      - rules/
    rule_formats:
      - yara-l
    sync_frequency: weekly
    priority: 75
    tags:
      - chronicle
      - google

  # Falco Rules (Runtime Security)
  - repo_url: https://github.com/falcosecurity/falco
    name: Falco Security Rules
    enabled: false
    rule_paths:
      - rules/
    rule_formats:
      - yaml
    sync_frequency: weekly
    priority: 70
    tags:
      - falco
      - kubernetes
      - runtime

  # SOC Prime Sigma Rules
  - repo_url: https://github.com/socprime/SigmaUI
    name: SOC Prime Sigma UI
    enabled: false
    rule_paths:
      - rules/
    rule_formats:
      - sigma
    sync_frequency: weekly
    priority: 80
    tags:
      - sigma
      - socprime

  # Additional Sigma Community Rules (350+ rules mapped to MITRE)
  - repo_url: https://github.com/mdecrevoisier/SIGMA-detection-rules
    name: SIGMA Detection Rules (MITRE Mapped)
    enabled: true
    rule_paths:
      - rules/
    rule_formats:
      - sigma
    sync_frequency: daily
    priority: 85
    tags:
      - sigma
      - mitre
      - community
      - verified

  # Loginsoft Threat Detection Rules (Emerging Threats)
  - repo_url: https://github.com/Loginsoft-Research/detection-rules
    name: Loginsoft Detection Rules
    enabled: true
    rule_paths:
      - rules/
    rule_formats:
      - sigma
    sync_frequency: weekly
    priority: 75
    tags:
      - sigma
      - emerging-threats
      - opensource

  # LogHub - Log Datasets and Parsing (Research/Training Data)
  - repo_url: https://github.com/logpai/loghub
    name: LogHub - Log Datasets
    enabled: true
    rule_paths:
      - /
    rule_formats:
      - logs
      - datasets
    sync_frequency: weekly
    priority: 60
    tags:
      - logs
      - datasets
      - research
      - parsing
      - training-data

  # Firewall IDS Log Analysis (Network Security)
  - repo_url: https://github.com/parsavares/firewall-ids-log-analysis
    name: Firewall IDS Log Analysis
    enabled: true
    rule_paths:
      - /
    rule_formats:
      - python
      - analysis
    sync_frequency: weekly
    priority: 65
    tags:
      - firewall
      - ids
      - analysis
      - network

  # Wazuh Rules (Open-source XDR/SIEM)
  - repo_url: https://github.com/wazuh/wazuh
    name: Wazuh XDR/SIEM Platform
    enabled: false  # Large repo, enable if needed
    rule_paths:
      - ruleset/rules/
    rule_formats:
      - xml
    sync_frequency: weekly
    priority: 80
    tags:
      - wazuh
      - xdr
      - siem
      - opensource

  # SOCFortress Advanced Wazuh Rules
  - repo_url: https://github.com/socfortress/Wazuh-Rules
    name: SOCFortress Wazuh Rules
    enabled: false
    rule_paths:
      - rules/
    rule_formats:
      - xml
    sync_frequency: weekly
    priority: 75
    tags:
      - wazuh
      - socfortress
      - advanced

# Global Settings
settings:
  # Automatically sync repositories on schedule
  auto_sync: true

  # Deduplicate rules before importing
  deduplicate: true

  # Minimum quality score to import (0-100)
  quality_threshold: 70

  # Maximum rules to import per repository (prevents overwhelming system)
  max_rules_per_repo: 10000

  # GitHub API token for higher rate limits (optional)
  # Set via environment variable: GITHUB_TOKEN
  use_github_token: true

  # Import rules as "pending" (requires approval) or "active" (auto-approved)
  import_status: pending

  # Notify on new rules imported
  notify_on_import: true

# Custom Repository Template
# Copy and modify this to add your own repositories:
#
# - repo_url: https://github.com/YOUR_ORG/YOUR_REPO
#   name: Your Repository Name
#   enabled: true
#   rule_paths:
#     - path/to/rules/
#   rule_formats:
#     - sigma  # or splunk, elastic, sentinel, kql, etc.
#   sync_frequency: daily
#   priority: 75
#   tags:
#     - custom
#     - your-tag
