#!/usr/bin/env python3
"""
SIEMLess v2.0 - Deployment Package Creator
Creates a complete deployment package for client environments
"""

import os
import shutil
import zipfile
import json
import argparse
from datetime import datetime
from pathlib import Path

class DeploymentPackageBuilder:
    """Build deployment packages for client environments"""

    def __init__(self, source_dir: str, output_dir: str, client_name: str = "default"):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.client_name = client_name
        self.package_name = f"siemless-v2-{client_name}-{datetime.now().strftime('%Y%m%d')}"
        self.package_dir = self.output_dir / self.package_name

    def create_package(self):
        """Create complete deployment package"""
        print(f"🚀 Creating deployment package for {self.client_name}")

        # Create package directory structure
        self._create_directories()

        # Copy core files
        self._copy_engines()
        self._copy_database_schemas()
        self._copy_patterns()
        self._copy_docker_files()

        # Generate configuration files
        self._generate_env_template()
        self._generate_docker_compose()
        self._generate_deployment_scripts()

        # Create documentation
        self._copy_documentation()

        # Create ZIP archive
        self._create_archive()

        print(f"✅ Package created: {self.package_name}.zip")

    def _create_directories(self):
        """Create package directory structure"""
        dirs = [
            'engines/ingestion',
            'engines/contextualization',
            'engines/shared',
            'postgres/init',
            'patterns',
            'scripts',
            'docs',
            'config'
        ]

        for dir_path in dirs:
            (self.package_dir / dir_path).mkdir(parents=True, exist_ok=True)

    def _copy_engines(self):
        """Copy engine files"""
        print("📦 Copying engine files...")

        # Ingestion Engine
        ingestion_files = [
            'ingestion_engine.py',
            'pattern_matcher.py',
            'requirements.txt',
            'Dockerfile'
        ]
        for file in ingestion_files:
            src = self.source_dir / 'engines' / 'ingestion' / file
            if src.exists():
                shutil.copy2(src, self.package_dir / 'engines' / 'ingestion' / file)

        # Contextualization Engine
        context_files = [
            'contextualization_engine.py',
            'entity_extractor.py',
            'enrichment_service.py',
            'requirements.txt',
            'Dockerfile'
        ]
        for file in context_files:
            src = self.source_dir / 'engines' / 'contextualization' / file
            if src.exists():
                shutil.copy2(src, self.package_dir / 'engines' / 'contextualization' / file)

        # Shared files
        shared_files = ['base_engine.py']
        for file in shared_files:
            # Try multiple locations
            for location in ['engines/ingestion', 'engines/contextualization', 'engines']:
                src = self.source_dir / location / file
                if src.exists():
                    shutil.copy2(src, self.package_dir / 'engines' / 'shared' / file)
                    break

    def _copy_database_schemas(self):
        """Copy database initialization scripts"""
        print("🗄️ Copying database schemas...")

        schema_files = [
            '01_base_schema.sql',
            '02_pattern_library_schema.sql',
            '10_enhanced_contextualization_schema.sql',
            '11_restore_v07_relationship_model.sql'
        ]

        for file in schema_files:
            src = self.source_dir / 'engines' / 'postgres' / 'init' / file
            if src.exists():
                shutil.copy2(src, self.package_dir / 'postgres' / 'init' / file)

    def _copy_patterns(self):
        """Copy and generate pattern configurations"""
        print("🎯 Creating pattern configurations...")

        # Default patterns
        default_patterns = {
            "fortinet_traffic": {
                "pattern_id": "fortinet_001",
                "pattern_type": "traffic",
                "entity_extractors": {
                    "ip_fields": ["source.ip", "destination.ip"],
                    "port_fields": ["source.port", "destination.port"],
                    "protocol_fields": ["network.protocol"]
                }
            },
            "crowdstrike_detection": {
                "pattern_id": "crowdstrike_001",
                "pattern_type": "detection",
                "entity_extractors": {
                    "host_fields": ["device.hostname"],
                    "user_fields": ["device.user"],
                    "process_fields": ["behaviors[].filename"],
                    "hash_fields": ["behaviors[].sha256"]
                }
            }
        }

        with open(self.package_dir / 'patterns' / 'default_patterns.json', 'w') as f:
            json.dump(default_patterns, f, indent=2)

    def _generate_env_template(self):
        """Generate environment variable template"""
        print("🔧 Generating environment template...")

        env_template = """# SIEMLess v2.0 Environment Configuration
# Client: {client_name}
# Generated: {timestamp}

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=siemless_v2
POSTGRES_USER=siemless
POSTGRES_PASSWORD=CHANGE_THIS_PASSWORD

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6380
REDIS_PASSWORD=OPTIONAL_REDIS_PASSWORD

# Client Settings
CLIENT_NAME={client_name}
ENVIRONMENT=production
LOG_LEVEL=INFO

# Data Sources
ENABLE_FORTINET=true
ENABLE_CROWDSTRIKE=true
ENABLE_PALO_ALTO=false
ENABLE_SPLUNK=false

# Enrichment Services
ENABLE_GEOIP=true
ENABLE_THREAT_INTEL=true
ENABLE_ASSET_INVENTORY=true
ENABLE_ZONE_DETECTION=true

# Performance Tuning
MAX_WORKERS=10
BATCH_SIZE=100
CACHE_TTL=3600

# AI Services (Optional)
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
GOOGLE_API_KEY=
""".format(
            client_name=self.client_name,
            timestamp=datetime.now().isoformat()
        )

        with open(self.package_dir / '.env.template', 'w') as f:
            f.write(env_template)

    def _generate_docker_compose(self):
        """Generate Docker Compose file"""
        print("🐳 Generating Docker Compose configuration...")

        docker_compose = """version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: siemless_postgres_v2
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - ./postgres/init:/docker-entrypoint-initdb.d
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: siemless_redis_v2
    command: redis-server --requirepass ${REDIS_PASSWORD:-}
    ports:
      - "${REDIS_PORT}:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  ingestion:
    build: ./engines/ingestion
    container_name: siemless_ingestion_v2
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=5432
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    volumes:
      - ./logs:/var/log/siemless
    restart: unless-stopped

  contextualization:
    build: ./engines/contextualization
    container_name: siemless_contextualization_v2
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=5432
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - MAX_WORKERS=${MAX_WORKERS}
      - BATCH_SIZE=${BATCH_SIZE}
    volumes:
      - ./logs:/var/log/siemless
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: siemless_network
"""

        with open(self.package_dir / 'docker-compose.yml', 'w') as f:
            f.write(docker_compose)

    def _generate_deployment_scripts(self):
        """Generate deployment and management scripts"""
        print("📝 Generating deployment scripts...")

        # Deploy script
        deploy_script = """#!/bin/bash
set -e

echo "🚀 SIEMLess v2.0 Deployment Script"
echo "=================================="

# Load environment
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
else
    echo "❌ .env file not found. Copy .env.template to .env and configure."
    exit 1
fi

# Check Docker
command -v docker >/dev/null 2>&1 || {{ echo "❌ Docker required"; exit 1; }}

# Create directories
mkdir -p data/postgres data/redis logs

# Build images
echo "📦 Building Docker images..."
docker-compose build

# Start services
echo "🚀 Starting services..."
docker-compose up -d

# Wait for services
echo "⏳ Waiting for services to be healthy..."
sleep 30

# Verify deployment
echo "✅ Verifying deployment..."
docker-compose ps

echo "🎉 Deployment complete!"
"""

        with open(self.package_dir / 'scripts' / 'deploy.sh', 'w') as f:
            f.write(deploy_script)
        os.chmod(self.package_dir / 'scripts' / 'deploy.sh', 0o755)

        # Health check script
        health_check = """#!/usr/bin/env python3
import psycopg2
import redis
import sys

def check_postgres():
    try:
        conn = psycopg2.connect(
            host='localhost',
            port=5433,
            database='siemless_v2',
            user='siemless',
            password='CHANGE_THIS'
        )
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM entities")
        count = cursor.fetchone()[0]
        print(f"✅ PostgreSQL: Connected ({count} entities)")
        return True
    except Exception as e:
        print(f"❌ PostgreSQL: {e}")
        return False

def check_redis():
    try:
        r = redis.Redis(host='localhost', port=6380)
        r.ping()
        print("✅ Redis: Connected")
        return True
    except Exception as e:
        print(f"❌ Redis: {e}")
        return False

if __name__ == "__main__":
    pg_ok = check_postgres()
    redis_ok = check_redis()

    if pg_ok and redis_ok:
        print("\\n🎉 All systems operational!")
        sys.exit(0)
    else:
        print("\\n⚠️ Some systems need attention")
        sys.exit(1)
"""

        with open(self.package_dir / 'scripts' / 'health_check.py', 'w') as f:
            f.write(health_check)

    def _copy_documentation(self):
        """Copy relevant documentation"""
        print("📚 Copying documentation...")

        docs = [
            'CLIENT_DEPLOYMENT_GUIDE.md',
            'CONTEXTUALIZATION_V2_COMPLETE.md',
            'ARCHITECTURE.md',
            'README.md'
        ]

        for doc in docs:
            src = self.source_dir / doc
            if src.exists():
                shutil.copy2(src, self.package_dir / 'docs' / doc)

    def _create_archive(self):
        """Create ZIP archive of the package"""
        print("📦 Creating ZIP archive...")

        zip_path = self.output_dir / f"{self.package_name}.zip"

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(self.package_dir):
                for file in files:
                    file_path = Path(root) / file
                    arcname = file_path.relative_to(self.package_dir.parent)
                    zipf.write(file_path, arcname)

        # Calculate package size
        size_mb = zip_path.stat().st_size / (1024 * 1024)
        print(f"📏 Package size: {size_mb:.2f} MB")

        return zip_path


def main():
    parser = argparse.ArgumentParser(description='Create SIEMLess v2.0 deployment package')
    parser.add_argument('--source', default='.', help='Source directory (default: current)')
    parser.add_argument('--output', default='./packages', help='Output directory')
    parser.add_argument('--client', default='default', help='Client name')

    args = parser.parse_args()

    # Create output directory
    Path(args.output).mkdir(exist_ok=True)

    # Build package
    builder = DeploymentPackageBuilder(args.source, args.output, args.client)
    builder.create_package()

    print(f"""
    ✅ Deployment package ready!

    Next steps for client:
    1. Extract {builder.package_name}.zip
    2. Configure .env file with client settings
    3. Run ./scripts/deploy.sh
    4. Verify with ./scripts/health_check.py

    Package location: {args.output}/{builder.package_name}.zip
    """)


if __name__ == "__main__":
    main()