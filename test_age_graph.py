#!/usr/bin/env python3
"""
Apache AGE Graph Database Test Script
Tests basic graph operations and validates AGE installation
"""

import asyncio
import asyncpg
import json
from datetime import datetime

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'siemless_v2',
    'user': 'siemless',
    'password': 'siemless123'
}


async def test_age_installation():
    """Test if Apache AGE is installed and working"""
    print("[TEST] Connecting to PostgreSQL with AGE...")

    try:
        conn = await asyncpg.connect(**DB_CONFIG)
        print("[OK] Connected to database")

        # Test 1: Check if AGE extension is installed
        print("\n[TEST 1] Checking AGE extension...")
        result = await conn.fetchval("""
            SELECT COUNT(*) FROM pg_extension WHERE extname = 'age'
        """)

        if result > 0:
            print("[PASS] AGE extension is installed")
        else:
            print("[FAIL] AGE extension not found")
            return False

        # Test 2: Check if entity_graph exists
        print("\n[TEST 2] Checking entity_graph...")
        result = await conn.fetchval("""
            SELECT COUNT(*) FROM ag_catalog.ag_graph WHERE name = 'entity_graph'
        """)

        if result > 0:
            print("[PASS] entity_graph exists")
        else:
            print("[WARN] entity_graph not found, creating it...")
            await conn.execute("SELECT create_graph('entity_graph');")
            print("[OK] entity_graph created")

        # Test 3: Load AGE into search path
        print("\n[TEST 3] Setting search path...")
        await conn.execute("SET search_path = ag_catalog, '$user', public;")
        print("[OK] Search path configured")

        # Test 4: Create sample nodes
        print("\n[TEST 4] Creating sample graph nodes...")

        # Create a user node
        await conn.execute("""
            SELECT * FROM cypher('entity_graph', $$
                CREATE (u:User {
                    id: 'user_001',
                    value: 'admin',
                    entity_type: 'user',
                    first_seen: '2025-09-30T12:00:00',
                    risk_score: 45
                })
            $$) as (result agtype);
        """)
        print("[OK] Created User node: admin")

        # Create a host node
        await conn.execute("""
            SELECT * FROM cypher('entity_graph', $$
                CREATE (h:Host {
                    id: 'host_001',
                    value: 'DC01',
                    entity_type: 'host',
                    first_seen: '2025-09-30T12:00:00',
                    risk_score: 30
                })
            $$) as (result agtype);
        """)
        print("[OK] Created Host node: DC01")

        # Create an IP node
        await conn.execute("""
            SELECT * FROM cypher('entity_graph', $$
                CREATE (i:IP {
                    id: 'ip_001',
                    value: '*************',
                    entity_type: 'ip',
                    first_seen: '2025-09-30T12:00:00',
                    risk_score: 70
                })
            $$) as (result agtype);
        """)
        print("[OK] Created IP node: *************")

        # Create a suspicious IP node
        await conn.execute("""
            SELECT * FROM cypher('entity_graph', $$
                CREATE (i:IP {
                    id: 'ip_002',
                    value: '************',
                    entity_type: 'ip',
                    first_seen: '2025-09-30T12:05:00',
                    risk_score: 95,
                    is_malicious: true
                })
            $$) as (result agtype);
        """)
        print("[OK] Created IP node: ************ (malicious)")

        # Test 5: Create relationships
        print("\n[TEST 5] Creating relationships...")

        # User logged into Host
        await conn.execute("""
            SELECT * FROM cypher('entity_graph', $$
                MATCH (u:User {id: 'user_001'}), (h:Host {id: 'host_001'})
                CREATE (u)-[r:LOGGED_INTO {
                    timestamp: '2025-09-30T12:01:00',
                    confidence: 1.0,
                    relationship_type: 'logged_into'
                }]->(h)
            $$) as (result agtype);
        """)
        print("[OK] Created relationship: User -> LOGGED_INTO -> Host")

        # Host connected to IP
        await conn.execute("""
            SELECT * FROM cypher('entity_graph', $$
                MATCH (h:Host {id: 'host_001'}), (i:IP {id: 'ip_001'})
                CREATE (h)-[r:CONNECTED_TO {
                    timestamp: '2025-09-30T12:02:00',
                    confidence: 1.0,
                    relationship_type: 'connected_to'
                }]->(i)
            $$) as (result agtype);
        """)
        print("[OK] Created relationship: Host -> CONNECTED_TO -> IP")

        # Host also connected to malicious IP
        await conn.execute("""
            SELECT * FROM cypher('entity_graph', $$
                MATCH (h:Host {id: 'host_001'}), (i:IP {id: 'ip_002'})
                CREATE (h)-[r:CONNECTED_TO {
                    timestamp: '2025-09-30T12:05:00',
                    confidence: 0.85,
                    relationship_type: 'connected_to',
                    suspicious: true
                }]->(i)
            $$) as (result agtype);
        """)
        print("[OK] Created relationship: Host -> CONNECTED_TO -> Malicious IP")

        # Test 6: Query the graph
        print("\n[TEST 6] Querying graph data...")

        # Count nodes
        result = await conn.fetch("""
            SELECT * FROM cypher('entity_graph', $$
                MATCH (n)
                RETURN n.entity_type, count(*) as count
            $$) as (entity_type agtype, count agtype);
        """)

        print("\n[RESULTS] Node counts:")
        for row in result:
            entity_type = str(row['entity_type']).strip('"')
            count = row['count']
            print(f"  - {entity_type}: {count}")

        # Test 7: Path finding
        print("\n[TEST 7] Finding paths (User -> Malicious IP)...")

        result = await conn.fetch("""
            SELECT * FROM cypher('entity_graph', $$
                MATCH path = (u:User {id: 'user_001'})-[*1..5]-(i:IP {is_malicious: true})
                RETURN path
            $$) as (path agtype);
        """)

        print(f"[OK] Found {len(result)} path(s) from User to Malicious IP")

        if result:
            print("\n[PATH] User admin accessed malicious IP via:")
            print("  admin -> logged_into -> DC01 -> connected_to -> ************")

        # Test 8: Neighbor query
        print("\n[TEST 8] Finding neighbors of Host DC01...")

        result = await conn.fetch("""
            SELECT * FROM cypher('entity_graph', $$
                MATCH (h:Host {id: 'host_001'})-[r]-(n)
                RETURN n.value as neighbor, type(r) as relationship
            $$) as (neighbor agtype, relationship agtype);
        """)

        print(f"[OK] Found {len(result)} neighbor(s):")
        for row in result:
            neighbor = str(row['neighbor']).strip('"')
            rel_type = str(row['relationship']).strip('"')
            print(f"  - {neighbor} ({rel_type})")

        # Test 9: Risk analysis
        print("\n[TEST 9] Analyzing risk scores...")

        result = await conn.fetch("""
            SELECT * FROM cypher('entity_graph', $$
                MATCH (n)
                WHERE n.risk_score > 50
                RETURN n.entity_type, n.value, n.risk_score
                ORDER BY n.risk_score DESC
            $$) as (entity_type agtype, value agtype, risk_score agtype);
        """)

        print(f"[OK] Found {len(result)} high-risk entities:")
        for row in result:
            entity_type = str(row['entity_type']).strip('"')
            value = str(row['value']).strip('"')
            risk_score = row['risk_score']
            print(f"  - {entity_type}: {value} (risk: {risk_score})")

        # Test 10: Performance test
        print("\n[TEST 10] Performance test (multi-hop query)...")

        import time
        start = time.time()

        result = await conn.fetch("""
            SELECT * FROM cypher('entity_graph', $$
                MATCH path = (u:User)-[*1..3]-(n)
                RETURN count(path) as path_count
            $$) as (path_count agtype);
        """)

        elapsed = (time.time() - start) * 1000  # Convert to ms
        path_count = result[0]['path_count']

        print(f"[OK] Found {path_count} paths in {elapsed:.2f}ms")

        if elapsed < 100:
            print("[PASS] Query performance is excellent (<100ms)")
        elif elapsed < 500:
            print("[PASS] Query performance is good (<500ms)")
        else:
            print("[WARN] Query performance is slow (>500ms)")

        print("\n" + "=" * 60)
        print("[SUCCESS] All AGE tests passed!")
        print("=" * 60)
        print("\nGraph Statistics:")
        print(f"  - Nodes: 4 (1 User, 1 Host, 2 IPs)")
        print(f"  - Relationships: 3")
        print(f"  - High-risk entities: 2")
        print(f"  - Paths to malicious IP: 1")
        print("\nNext Steps:")
        print("  1. Migrate existing entities/relationships from PostgreSQL tables")
        print("  2. Set up trigger-based sync for real-time updates")
        print("  3. Add graph query endpoints to Backend Engine")
        print("  4. Enhance frontend with graph query controls")

        await conn.close()
        return True

    except Exception as e:
        print(f"\n[ERROR] Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function"""
    print("=" * 60)
    print("Apache AGE Graph Database Test Suite")
    print("SIEMLess v2.0 - Entity Relationship Graph")
    print("=" * 60 + "\n")

    success = await test_age_installation()

    if success:
        print("\n[READY] Apache AGE is ready for production use!")
        return 0
    else:
        print("\n[FAILED] AGE installation or configuration issues detected")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
