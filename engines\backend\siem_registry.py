"""
SIEM Registry - Configuration-Based SIEM Platform Management
Enables adding new SIEM platforms via YAML configuration without code changes

This module provides:
- Hot-reloadable SIEM definitions
- Community-contributed SIEM configs
- Version-controlled platform mappings
- Easy addition of new platforms (Wazuh, OpenSearch, etc.)
"""

import yaml
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field, asdict
from enum import Enum
from datetime import datetime
import re


@dataclass
class SIEMDefinition:
    """Complete SIEM platform definition"""
    name: str
    display_name: str
    query_language: str
    description: str = ""
    vendor: str = ""
    version: str = "1.0"

    # Field mappings: generic_field -> platform_field
    field_mappings: Dict[str, str] = field(default_factory=dict)

    # Operator mappings: generic_op -> platform_op
    operator_mappings: Dict[str, str] = field(default_factory=dict)

    # Time field name
    time_field: str = "timestamp"

    # Query syntax specifics
    syntax: Dict[str, Any] = field(default_factory=dict)

    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)

    # Status
    active: bool = True
    validated: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)


class SIEMRegistry:
    """
    Central registry for SIEM platform definitions

    Loads SIEM configurations from YAML files and provides
    unified access to platform-specific mappings
    """

    def __init__(self, definitions_path: Optional[Path] = None, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)

        # Set definitions path
        if definitions_path:
            self.definitions_path = Path(definitions_path)
        else:
            # Default: siem_definitions/ in project root
            self.definitions_path = Path(__file__).parent.parent.parent / 'siem_definitions'

        # Registry storage
        self.siems: Dict[str, SIEMDefinition] = {}

        # Load definitions
        self._load_all_definitions()

    def _load_all_definitions(self):
        """Load all SIEM definitions from YAML files"""
        if not self.definitions_path.exists():
            self.logger.warning(f"SIEM definitions path not found: {self.definitions_path}")
            self.logger.info("Creating definitions directory...")
            self.definitions_path.mkdir(parents=True, exist_ok=True)
            return

        # Find all YAML files
        yaml_files = list(self.definitions_path.glob('*.yaml')) + list(self.definitions_path.glob('*.yml'))

        loaded_count = 0
        for yaml_file in yaml_files:
            try:
                definition = self._load_definition(yaml_file)
                if definition:
                    self.siems[definition.name] = definition
                    loaded_count += 1
                    self.logger.debug(f"Loaded SIEM definition: {definition.name}")
            except Exception as e:
                self.logger.error(f"Failed to load {yaml_file.name}: {e}")

        self.logger.info(f"Loaded {loaded_count} SIEM definitions from {self.definitions_path}")

    def _load_definition(self, yaml_file: Path) -> Optional[SIEMDefinition]:
        """Load a single SIEM definition from YAML file"""
        try:
            with open(yaml_file, 'r') as f:
                data = yaml.safe_load(f)

            # Validate required fields
            if not data or 'platform' not in data:
                self.logger.warning(f"Invalid SIEM definition in {yaml_file.name}: missing 'platform' section")
                return None

            platform = data['platform']

            # Create definition
            definition = SIEMDefinition(
                name=platform.get('name', yaml_file.stem),
                display_name=platform.get('display_name', platform.get('name', yaml_file.stem)),
                query_language=platform.get('query_language', 'unknown'),
                description=platform.get('description', ''),
                vendor=platform.get('vendor', ''),
                version=platform.get('version', '1.0'),
                field_mappings=data.get('field_mappings', {}),
                operator_mappings=data.get('operator_mappings', {}),
                time_field=data.get('time_field', 'timestamp'),
                syntax=data.get('syntax', {}),
                metadata=data.get('metadata', {}),
                active=platform.get('active', True)
            )

            # Validate definition
            if self._validate_definition(definition):
                definition.validated = True
                return definition
            else:
                self.logger.warning(f"SIEM definition {definition.name} failed validation")
                return definition  # Return anyway but mark as not validated

        except Exception as e:
            self.logger.error(f"Error loading SIEM definition from {yaml_file}: {e}")
            return None

    def _validate_definition(self, definition: SIEMDefinition) -> bool:
        """Validate a SIEM definition"""
        try:
            # Check required fields
            if not definition.name:
                self.logger.error("SIEM definition missing name")
                return False

            if not definition.query_language:
                self.logger.error(f"SIEM {definition.name} missing query_language")
                return False

            # Validate field mappings
            if not definition.field_mappings:
                self.logger.warning(f"SIEM {definition.name} has no field mappings")

            # Validate name format (alphanumeric, underscore, hyphen only)
            if not re.match(r'^[a-zA-Z0-9_-]+$', definition.name):
                self.logger.error(f"Invalid SIEM name format: {definition.name}")
                return False

            return True

        except Exception as e:
            self.logger.error(f"Validation error for {definition.name}: {e}")
            return False

    def get_siem(self, name: str) -> Optional[SIEMDefinition]:
        """Get SIEM definition by name"""
        return self.siems.get(name.lower())

    def list_siems(self, active_only: bool = True) -> List[SIEMDefinition]:
        """List all registered SIEMs"""
        if active_only:
            return [siem for siem in self.siems.values() if siem.active]
        return list(self.siems.values())

    def get_field_mapping(self, siem_name: str, generic_field: str) -> Optional[str]:
        """Get platform-specific field name for a generic field"""
        siem = self.get_siem(siem_name)
        if not siem:
            return None
        return siem.field_mappings.get(generic_field)

    def get_operator_mapping(self, siem_name: str, generic_operator: str) -> Optional[str]:
        """Get platform-specific operator for a generic operator"""
        siem = self.get_siem(siem_name)
        if not siem:
            return None
        return siem.operator_mappings.get(generic_operator)

    def get_time_field(self, siem_name: str) -> Optional[str]:
        """Get platform-specific time field name"""
        siem = self.get_siem(siem_name)
        if not siem:
            return None
        return siem.time_field

    def get_all_field_mappings(self, siem_name: str) -> Dict[str, str]:
        """Get all field mappings for a SIEM"""
        siem = self.get_siem(siem_name)
        if not siem:
            return {}
        return siem.field_mappings

    def get_all_operator_mappings(self, siem_name: str) -> Dict[str, str]:
        """Get all operator mappings for a SIEM"""
        siem = self.get_siem(siem_name)
        if not siem:
            return {}
        return siem.operator_mappings

    def register_siem(self, definition: SIEMDefinition) -> bool:
        """Programmatically register a new SIEM"""
        try:
            if self._validate_definition(definition):
                definition.validated = True
                self.siems[definition.name] = definition
                self.logger.info(f"Registered SIEM: {definition.name}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to register SIEM {definition.name}: {e}")
            return False

    def unregister_siem(self, name: str) -> bool:
        """Unregister a SIEM"""
        if name in self.siems:
            del self.siems[name]
            self.logger.info(f"Unregistered SIEM: {name}")
            return True
        return False

    def reload(self):
        """Reload all SIEM definitions from disk"""
        self.logger.info("Reloading SIEM definitions...")
        self.siems.clear()
        self._load_all_definitions()

    def export_definition(self, name: str) -> Optional[str]:
        """Export SIEM definition as YAML string"""
        siem = self.get_siem(name)
        if not siem:
            return None

        data = {
            'platform': {
                'name': siem.name,
                'display_name': siem.display_name,
                'query_language': siem.query_language,
                'description': siem.description,
                'vendor': siem.vendor,
                'version': siem.version,
                'active': siem.active
            },
            'field_mappings': siem.field_mappings,
            'operator_mappings': siem.operator_mappings,
            'time_field': siem.time_field,
            'syntax': siem.syntax,
            'metadata': siem.metadata
        }

        return yaml.dump(data, default_flow_style=False, sort_keys=False)

    def save_definition(self, name: str, overwrite: bool = False) -> bool:
        """Save SIEM definition to YAML file"""
        siem = self.get_siem(name)
        if not siem:
            self.logger.error(f"SIEM not found: {name}")
            return False

        file_path = self.definitions_path / f"{name}.yaml"

        if file_path.exists() and not overwrite:
            self.logger.error(f"SIEM definition file already exists: {file_path}")
            return False

        try:
            yaml_content = self.export_definition(name)
            if yaml_content:
                with open(file_path, 'w') as f:
                    f.write(yaml_content)
                self.logger.info(f"Saved SIEM definition to {file_path}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to save SIEM definition: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """Get registry statistics"""
        total = len(self.siems)
        active = len([s for s in self.siems.values() if s.active])
        validated = len([s for s in self.siems.values() if s.validated])

        return {
            'total_siems': total,
            'active_siems': active,
            'validated_siems': validated,
            'definitions_path': str(self.definitions_path),
            'platforms': [siem.name for siem in self.siems.values()]
        }

    def search_field_mappings(self, generic_field: str) -> Dict[str, str]:
        """Search field mappings across all SIEMs"""
        results = {}
        for siem in self.siems.values():
            if generic_field in siem.field_mappings:
                results[siem.name] = siem.field_mappings[generic_field]
        return results

    def get_common_fields(self) -> List[str]:
        """Get list of common fields defined across all SIEMs"""
        all_fields = set()
        for siem in self.siems.values():
            all_fields.update(siem.field_mappings.keys())
        return sorted(list(all_fields))

    def validate_field_coverage(self) -> Dict[str, List[str]]:
        """Check which SIEMs have mappings for each common field"""
        common_fields = self.get_common_fields()
        coverage = {}

        for field in common_fields:
            coverage[field] = [
                siem.name for siem in self.siems.values()
                if field in siem.field_mappings
            ]

        return coverage


# Convenience functions for backward compatibility
_registry_instance: Optional[SIEMRegistry] = None

def get_registry() -> SIEMRegistry:
    """Get singleton registry instance"""
    global _registry_instance
    if _registry_instance is None:
        _registry_instance = SIEMRegistry()
    return _registry_instance

def reload_registry():
    """Reload the global registry"""
    global _registry_instance
    if _registry_instance:
        _registry_instance.reload()
    else:
        _registry_instance = SIEMRegistry()


# Example usage and testing
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)

    # Create registry
    registry = SIEMRegistry()

    print("\n" + "="*60)
    print("SIEM REGISTRY STATISTICS")
    print("="*60)
    stats = registry.get_statistics()
    print(f"Total SIEMs: {stats['total_siems']}")
    print(f"Active SIEMs: {stats['active_siems']}")
    print(f"Validated: {stats['validated_siems']}")
    print(f"Definitions Path: {stats['definitions_path']}")
    print(f"Platforms: {', '.join(stats['platforms'])}")

    # List all SIEMs
    print("\n" + "="*60)
    print("REGISTERED SIEMS")
    print("="*60)
    for siem in registry.list_siems():
        print(f"\n{siem.display_name} ({siem.name})")
        print(f"  Query Language: {siem.query_language}")
        print(f"  Vendor: {siem.vendor}")
        print(f"  Fields: {len(siem.field_mappings)}")
        print(f"  Operators: {len(siem.operator_mappings)}")
        print(f"  Validated: {'YES' if siem.validated else 'NO'}")

    # Field coverage analysis
    print("\n" + "="*60)
    print("FIELD COVERAGE ANALYSIS")
    print("="*60)
    coverage = registry.validate_field_coverage()
    for field, siems in sorted(coverage.items()):
        print(f"{field}: {len(siems)}/{stats['total_siems']} SIEMs")
