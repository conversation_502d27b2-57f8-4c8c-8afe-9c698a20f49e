"""
Query Language Generator for Multi-SIEM Support
Translates investigation queries across Splunk, Elastic, Sentinel, QRadar, and Chronicle
"""

import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from enum import Enum

class SIEMType(Enum):
    SPLUNK = "splunk"
    ELASTIC = "elastic"
    SENTINEL = "sentinel"
    QRADAR = "qradar"
    CHRONICLE = "chronicle"

class QueryLanguageGenerator:
    """Generate SIEM-specific queries from universal query intent"""

    def __init__(self, redis_client, logger):
        self.redis = redis_client
        self.logger = logger

        # Query templates for common operations
        self.templates = {
            'search': self._search_template,
            'aggregate': self._aggregate_template,
            'timeline': self._timeline_template,
            'correlation': self._correlation_template,
            'statistical': self._statistical_template,
            'threat_hunt': self._threat_hunt_template
        }

        # SIEM-specific syntax mappings
        self.syntax_map = self._init_syntax_map()

    def _init_syntax_map(self) -> Dict:
        """Initialize SIEM-specific syntax mappings"""
        return {
            SIEMType.SPLUNK: {
                'wildcard': '*',
                'and': 'AND',
                'or': 'OR',
                'not': 'NOT',
                'equals': '=',
                'not_equals': '!=',
                'greater': '>',
                'less': '<',
                'contains': '*{value}*',
                'regex': 'rex',
                'pipe': '|',
                'time_field': '_time'
            },
            SIEMType.ELASTIC: {
                'wildcard': '*',
                'and': 'AND',
                'or': 'OR',
                'not': 'NOT',
                'equals': ':',
                'not_equals': 'NOT',
                'greater': '>',
                'less': '<',
                'contains': '*{value}*',
                'regex': '/',
                'pipe': '',
                'time_field': '@timestamp'
            },
            SIEMType.SENTINEL: {
                'wildcard': '*',
                'and': 'and',
                'or': 'or',
                'not': 'not',
                'equals': '==',
                'not_equals': '!=',
                'greater': '>',
                'less': '<',
                'contains': 'contains',
                'regex': 'matches regex',
                'pipe': '|',
                'time_field': 'TimeGenerated'
            },
            SIEMType.QRADAR: {
                'wildcard': '%',
                'and': 'AND',
                'or': 'OR',
                'not': 'NOT',
                'equals': '=',
                'not_equals': '!=',
                'greater': '>',
                'less': '<',
                'contains': 'LIKE',
                'regex': 'MATCHES',
                'pipe': '',
                'time_field': 'starttime'
            },
            SIEMType.CHRONICLE: {
                'wildcard': '*',
                'and': 'AND',
                'or': 'OR',
                'not': 'NOT',
                'equals': '=',
                'not_equals': '!=',
                'greater': '>',
                'less': '<',
                'contains': '~',
                'regex': '=~',
                'pipe': '|',
                'time_field': 'metadata.event_timestamp'
            }
        }

    async def generate_queries(self, query_intent: Dict) -> Dict[str, str]:
        """Generate queries for all supported SIEMs from universal intent"""
        try:
            # Parse the query intent
            query_type = query_intent.get('type', 'search')
            filters = query_intent.get('filters', {})
            aggregations = query_intent.get('aggregations', [])
            time_range = query_intent.get('time_range', '-24h')

            # Generate for each SIEM
            queries = {}
            for siem_type in SIEMType:
                query = await self._generate_single_query(
                    siem_type,
                    query_type,
                    filters,
                    aggregations,
                    time_range
                )
                queries[siem_type.value] = query

            # Cache the generated queries
            cache_key = f"queries:{json.dumps(query_intent, sort_keys=True)}"
            await self.redis.setex(cache_key, 3600, json.dumps(queries))

            self.logger.info(f"Generated queries for {len(queries)} SIEMs")
            return queries

        except Exception as e:
            self.logger.error(f"Failed to generate queries: {e}")
            raise

    async def _generate_single_query(
        self,
        siem_type: SIEMType,
        query_type: str,
        filters: Dict,
        aggregations: List,
        time_range: str
    ) -> str:
        """Generate query for a specific SIEM"""

        # Get the template function
        template_func = self.templates.get(query_type, self._search_template)

        # Generate the query
        return template_func(siem_type, filters, aggregations, time_range)

    def _search_template(
        self,
        siem_type: SIEMType,
        filters: Dict,
        aggregations: List,
        time_range: str
    ) -> str:
        """Generate basic search query"""

        if siem_type == SIEMType.SPLUNK:
            return self._splunk_search(filters, time_range)
        elif siem_type == SIEMType.ELASTIC:
            return self._elastic_search(filters, time_range)
        elif siem_type == SIEMType.SENTINEL:
            return self._sentinel_search(filters, time_range)
        elif siem_type == SIEMType.QRADAR:
            return self._qradar_search(filters, time_range)
        elif siem_type == SIEMType.CHRONICLE:
            return self._chronicle_search(filters, time_range)

    def _splunk_search(self, filters: Dict, time_range: str) -> str:
        """Generate Splunk SPL query"""
        query_parts = []

        # Add index if specified
        if 'index' in filters:
            query_parts.append(f"index={filters['index']}")
        else:
            query_parts.append("index=*")

        # Add time range
        query_parts.append(f"earliest={time_range}")

        # Add filters
        for field, value in filters.items():
            if field == 'index':
                continue
            if isinstance(value, list):
                or_conditions = [f'{field}="{v}"' for v in value]
                query_parts.append(f"({' OR '.join(or_conditions)})")
            else:
                query_parts.append(f'{field}="{value}"')

        return ' '.join(query_parts)

    def _elastic_search(self, filters: Dict, time_range: str) -> str:
        """Generate Elasticsearch KQL query"""
        query_parts = []

        # Add filters
        for field, value in filters.items():
            if isinstance(value, list):
                or_conditions = [f'{field}:"{v}"' for v in value]
                query_parts.append(f"({' OR '.join(or_conditions)})")
            else:
                query_parts.append(f'{field}:"{value}"')

        # Add time range
        query_parts.append(f'@timestamp:[now{time_range} TO now]')

        return ' AND '.join(query_parts) if query_parts else '*'

    def _sentinel_search(self, filters: Dict, time_range: str) -> str:
        """Generate Azure Sentinel KQL query"""
        query_lines = []

        # Start with table
        table = filters.get('table', 'SecurityEvent')
        query_lines.append(table)

        # Add time filter
        query_lines.append(f"| where TimeGenerated > ago({time_range[1:]})")

        # Add filters
        where_conditions = []
        for field, value in filters.items():
            if field == 'table':
                continue
            if isinstance(value, list):
                or_conditions = [f'{field} == "{v}"' for v in value]
                where_conditions.append(f"({' or '.join(or_conditions)})")
            else:
                where_conditions.append(f'{field} == "{value}"')

        if where_conditions:
            query_lines.append(f"| where {' and '.join(where_conditions)}")

        return '\n'.join(query_lines)

    def _qradar_search(self, filters: Dict, time_range: str) -> str:
        """Generate QRadar AQL query"""
        query_parts = ['SELECT * FROM events']

        # Add WHERE clause
        where_conditions = []

        # Add time filter
        time_ms = self._parse_time_range_to_ms(time_range)
        where_conditions.append(f"starttime >= {time_ms}")

        # Add filters
        for field, value in filters.items():
            if isinstance(value, list):
                or_conditions = [f"{field} = '{v}'" for v in value]
                where_conditions.append(f"({' OR '.join(or_conditions)})")
            else:
                where_conditions.append(f"{field} = '{value}'")

        if where_conditions:
            query_parts.append(f"WHERE {' AND '.join(where_conditions)}")

        return ' '.join(query_parts)

    def _chronicle_search(self, filters: Dict, time_range: str) -> str:
        """Generate Chronicle UDM query"""
        query_parts = []

        # Add filters
        for field, value in filters.items():
            if isinstance(value, list):
                or_conditions = [f'{field} = "{v}"' for v in value]
                query_parts.append(f"({' OR '.join(or_conditions)})")
            else:
                query_parts.append(f'{field} = "{value}"')

        # Add time filter
        query_parts.append(f'metadata.event_timestamp.seconds > {self._parse_time_range_to_seconds(time_range)}')

        return ' AND '.join(query_parts)

    def _aggregate_template(
        self,
        siem_type: SIEMType,
        filters: Dict,
        aggregations: List,
        time_range: str
    ) -> str:
        """Generate aggregation query"""

        base_query = self._search_template(siem_type, filters, [], time_range)

        if siem_type == SIEMType.SPLUNK:
            agg_parts = []
            for agg in aggregations:
                if agg['type'] == 'count':
                    agg_parts.append(f"stats count by {agg.get('field', '_time')}")
                elif agg['type'] == 'sum':
                    agg_parts.append(f"stats sum({agg['field']}) by {agg.get('group_by', '_time')}")
                elif agg['type'] == 'avg':
                    agg_parts.append(f"stats avg({agg['field']}) by {agg.get('group_by', '_time')}")
            return f"{base_query} | {' | '.join(agg_parts)}"

        elif siem_type == SIEMType.SENTINEL:
            agg_parts = []
            for agg in aggregations:
                if agg['type'] == 'count':
                    agg_parts.append(f"summarize count() by {agg.get('field', 'TimeGenerated')}")
                elif agg['type'] == 'sum':
                    agg_parts.append(f"summarize sum({agg['field']}) by {agg.get('group_by', 'TimeGenerated')}")
            return f"{base_query}\n| {' | '.join(agg_parts)}"

        # Add other SIEM-specific aggregation logic
        return base_query

    def _timeline_template(
        self,
        siem_type: SIEMType,
        filters: Dict,
        aggregations: List,
        time_range: str
    ) -> str:
        """Generate timeline query"""

        if siem_type == SIEMType.SPLUNK:
            base = self._splunk_search(filters, time_range)
            return f"{base} | timechart span=1h count"

        elif siem_type == SIEMType.SENTINEL:
            base = self._sentinel_search(filters, time_range)
            return f"{base}\n| summarize count() by bin(TimeGenerated, 1h)"

        elif siem_type == SIEMType.ELASTIC:
            # Return aggregation query for Kibana
            return json.dumps({
                "aggs": {
                    "timeline": {
                        "date_histogram": {
                            "field": "@timestamp",
                            "interval": "1h"
                        }
                    }
                }
            })

        return self._search_template(siem_type, filters, [], time_range)

    def _correlation_template(
        self,
        siem_type: SIEMType,
        filters: Dict,
        aggregations: List,
        time_range: str
    ) -> str:
        """Generate correlation query"""

        if siem_type == SIEMType.SPLUNK:
            # Example correlation between users and IPs
            return f"""
                index=* earliest={time_range}
                | stats values(src_ip) as source_ips by user
                | where mvcount(source_ips) > 5
                | mvexpand source_ips
            """

        elif siem_type == SIEMType.SENTINEL:
            return f"""
                SecurityEvent
                | where TimeGenerated > ago({time_range[1:]})
                | summarize SourceIPs = make_set(SourceIP) by Account
                | where array_length(SourceIPs) > 5
            """

        return self._search_template(siem_type, filters, [], time_range)

    def _threat_hunt_template(
        self,
        siem_type: SIEMType,
        filters: Dict,
        aggregations: List,
        time_range: str
    ) -> str:
        """Generate threat hunting query"""

        # Extract threat indicators
        iocs = filters.get('iocs', [])
        techniques = filters.get('mitre_techniques', [])

        if siem_type == SIEMType.SPLUNK:
            query_parts = [f"index=* earliest={time_range}"]

            # Add IOC searches
            if iocs:
                ioc_conditions = []
                for ioc in iocs:
                    if ioc['type'] == 'ip':
                        ioc_conditions.append(f'(src_ip="{ioc["value"]}" OR dest_ip="{ioc["value"]}")')
                    elif ioc['type'] == 'hash':
                        ioc_conditions.append(f'file_hash="{ioc["value"]}"')
                    elif ioc['type'] == 'domain':
                        ioc_conditions.append(f'domain="{ioc["value"]}"')

                if ioc_conditions:
                    query_parts.append(f"({' OR '.join(ioc_conditions)})")

            # Add MITRE technique searches
            if techniques:
                tech_conditions = [f'mitre_technique="*{t}*"' for t in techniques]
                query_parts.append(f"({' OR '.join(tech_conditions)})")

            return ' '.join(query_parts) + " | table _time, user, src_ip, dest_ip, action"

        elif siem_type == SIEMType.SENTINEL:
            query_lines = ["SecurityEvent"]
            query_lines.append(f"| where TimeGenerated > ago({time_range[1:]})")

            where_conditions = []

            # Add IOC searches
            for ioc in iocs:
                if ioc['type'] == 'ip':
                    where_conditions.append(f'(SourceIP == "{ioc["value"]}" or DestinationIP == "{ioc["value"]}")')
                elif ioc['type'] == 'hash':
                    where_conditions.append(f'FileHash == "{ioc["value"]}"')

            if where_conditions:
                query_lines.append(f"| where {' or '.join(where_conditions)}")

            query_lines.append("| project TimeGenerated, Account, SourceIP, DestinationIP, Activity")

            return '\n'.join(query_lines)

        return self._search_template(siem_type, filters, [], time_range)

    def _statistical_template(
        self,
        siem_type: SIEMType,
        filters: Dict,
        aggregations: List,
        time_range: str
    ) -> str:
        """Generate statistical analysis query"""

        if siem_type == SIEMType.SPLUNK:
            base = self._splunk_search(filters, time_range)
            return f"""
                {base}
                | stats count, dc(user) as unique_users, dc(src_ip) as unique_ips
                | eval avg_events_per_user = count / unique_users
                | eval avg_events_per_ip = count / unique_ips
            """

        elif siem_type == SIEMType.SENTINEL:
            base = self._sentinel_search(filters, time_range)
            return f"""
                {base}
                | summarize
                    TotalEvents = count(),
                    UniqueUsers = dcount(Account),
                    UniqueIPs = dcount(SourceIP)
                | extend AvgEventsPerUser = TotalEvents / UniqueUsers
                | extend AvgEventsPerIP = TotalEvents / UniqueIPs
            """

        return self._search_template(siem_type, filters, [], time_range)

    def _parse_time_range_to_ms(self, time_range: str) -> int:
        """Convert time range string to milliseconds for QRadar"""
        # Parse formats like "-24h", "-7d", "-30m"
        import re
        match = re.match(r'-(\d+)([hdm])', time_range)
        if match:
            value = int(match.group(1))
            unit = match.group(2)

            if unit == 'h':
                delta = timedelta(hours=value)
            elif unit == 'd':
                delta = timedelta(days=value)
            elif unit == 'm':
                delta = timedelta(minutes=value)
            else:
                delta = timedelta(hours=24)  # Default to 24 hours

            return int((datetime.utcnow() - delta).timestamp() * 1000)

        # Default to 24 hours ago
        return int((datetime.utcnow() - timedelta(hours=24)).timestamp() * 1000)

    def _parse_time_range_to_seconds(self, time_range: str) -> int:
        """Convert time range string to seconds for Chronicle"""
        ms = self._parse_time_range_to_ms(time_range)
        return ms // 1000

    async def translate_natural_language(self, natural_query: str) -> Dict:
        """Translate natural language to query intent"""
        # This would use NLP to parse the query
        # For now, use pattern matching

        query_intent = {
            'type': 'search',
            'filters': {},
            'aggregations': [],
            'time_range': '-24h'
        }

        # Extract time range
        time_patterns = {
            r'last (\d+) hours?': lambda m: f'-{m.group(1)}h',
            r'last (\d+) days?': lambda m: f'-{m.group(1)}d',
            r'last (\d+) minutes?': lambda m: f'-{m.group(1)}m',
            r'past (\d+) hours?': lambda m: f'-{m.group(1)}h',
            r'yesterday': lambda m: '-1d',
            r'today': lambda m: '-24h',
            r'this week': lambda m: '-7d',
            r'last week': lambda m: '-7d',
            r'last month': lambda m: '-30d'
        }

        for pattern, formatter in time_patterns.items():
            match = re.search(pattern, natural_query.lower())
            if match:
                query_intent['time_range'] = formatter(match)
                break

        # Extract entities
        if 'user' in natural_query.lower():
            # Extract user pattern
            user_match = re.search(r'user[:\s]+(\S+)', natural_query, re.IGNORECASE)
            if user_match:
                query_intent['filters']['user'] = user_match.group(1)

        if 'ip' in natural_query.lower() or 'address' in natural_query.lower():
            # Extract IP pattern
            ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
            ip_matches = re.findall(ip_pattern, natural_query)
            if ip_matches:
                query_intent['filters']['ip'] = ip_matches

        # Detect query type
        if any(word in natural_query.lower() for word in ['count', 'how many', 'total']):
            query_intent['type'] = 'aggregate'
            query_intent['aggregations'].append({'type': 'count'})
        elif any(word in natural_query.lower() for word in ['timeline', 'over time', 'trend']):
            query_intent['type'] = 'timeline'
        elif any(word in natural_query.lower() for word in ['correlate', 'relationship', 'between']):
            query_intent['type'] = 'correlation'
        elif any(word in natural_query.lower() for word in ['hunt', 'threat', 'malicious', 'suspicious']):
            query_intent['type'] = 'threat_hunt'
        elif any(word in natural_query.lower() for word in ['stats', 'statistics', 'average']):
            query_intent['type'] = 'statistical'

        return query_intent

    async def optimize_query(self, siem_type: SIEMType, query: str) -> str:
        """Optimize query for better performance"""

        if siem_type == SIEMType.SPLUNK:
            # Add index specification if missing
            if 'index=' not in query:
                query = f"index=* {query}"

            # Add time range if missing
            if 'earliest=' not in query:
                query = f"{query} earliest=-24h"

            # Suggest field extraction optimization
            if '| rex' in query:
                self.logger.info("Consider using field extractions at index time for better performance")

        elif siem_type == SIEMType.ELASTIC:
            # Suggest using filters instead of queries for better caching
            if '"query":' in query and '"filter":' not in query:
                self.logger.info("Consider using filters for better query caching")

        elif siem_type == SIEMType.SENTINEL:
            # Suggest using materialized views for repeated queries
            if query.count('|') > 5:
                self.logger.info("Complex query detected. Consider creating a function for reuse")

        return query

    async def validate_query(self, siem_type: SIEMType, query: str) -> Dict:
        """Validate query syntax"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }

        if siem_type == SIEMType.SPLUNK:
            # Check for common SPL errors
            if '| stats' in query and 'by' not in query:
                validation_result['warnings'].append("Stats command without 'by' clause may aggregate all events")

            if query.count('|') > 10:
                validation_result['warnings'].append("Complex query with many pipes may be slow")

            if 'index=*' in query:
                validation_result['suggestions'].append("Specify index names for better performance")

        elif siem_type == SIEMType.SENTINEL:
            # Check for KQL syntax
            if '==' not in query and '=' in query:
                validation_result['errors'].append("Use '==' for equality in KQL, not '='")
                validation_result['valid'] = False

            if not query.strip().split('\n')[0].replace(' ', '').isalpha():
                validation_result['errors'].append("KQL queries should start with a table name")
                validation_result['valid'] = False

        return validation_result