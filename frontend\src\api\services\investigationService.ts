/**
 * Investigation API Service
 * Handles investigation-related API calls including query generation
 */

import apiClient from '../client'
import type { APIResponse } from '../../types/api'

/**
 * Time window for queries
 */
export interface TimeWindow {
  start: string
  end: string
}

/**
 * Source details from available log sources
 */
export interface SourceDetails {
  platform: string | null
  vendor: string | null
  product: string | null
  log_count: number
}

/**
 * Generated query for a specific source
 */
export interface GeneratedQuery {
  source_type: string
  source_name: string
  query_language: string
  query: string
  available: boolean
  deep_link: string | null
  what_we_have: string[]
  what_to_look_for: string[]
  limitations: string[]
  source_details: SourceDetails | null
}

/**
 * Request to generate queries
 */
export interface GenerateQueriesRequest {
  entity_type: string
  entity_value: string
  time_window?: TimeWindow
}

/**
 * Response from query generation
 */
export interface GenerateQueriesResponse {
  total_queries: number
  available_sources: number
  queries: GeneratedQuery[]
}

/**
 * Available log source
 */
export interface AvailableSource {
  platform: string
  vendor: string
  product: string
  oldest_log: string | null
  newest_log: string | null
  log_count_7d: number
}

/**
 * Available sources response
 */
export interface AvailableSourcesResponse {
  total_sources: number
  sources: AvailableSource[]
}

/**
 * Investigation guidance response
 */
export interface InvestigationGuidanceResponse {
  total_templates: number
  templates: {
    source_name: string
    query_language: string
    what_to_look_for: string[]
    what_we_have: string[]
    limitations: string[]
  }[]
}

export const investigationService = {
  /**
   * Generate queries for an entity across all available sources
   */
  async generateQueries(
    request: GenerateQueriesRequest
  ): Promise<GenerateQueriesResponse> {
    const response = await apiClient.post<GenerateQueriesResponse>(
      '/investigation/generate-queries',
      request
    )
    return response.data
  },

  /**
   * Get investigation guidance for an entity type
   */
  async getGuidance(
    entityType: string,
    entityValue: string
  ): Promise<InvestigationGuidanceResponse> {
    const response = await apiClient.get<InvestigationGuidanceResponse>(
      `/investigation/guidance/${entityType}/${entityValue}`
    )
    return response.data
  },

  /**
   * Get list of available log sources
   */
  async getAvailableSources(): Promise<AvailableSourcesResponse> {
    const response = await apiClient.get<AvailableSourcesResponse>(
      '/investigation/sources'
    )
    return response.data
  }
}
