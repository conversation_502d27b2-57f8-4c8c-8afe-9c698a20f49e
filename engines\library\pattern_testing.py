"""
Pattern Testing Framework for SIEMLess v2.0
Validates patterns through comprehensive testing before deployment
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from uuid import uuid4
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class TestResult(Enum):
    """Test result types"""
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"

@dataclass
class TestCase:
    """Individual test case for a pattern"""
    test_id: str
    test_name: str
    test_type: str  # unit, integration, performance, false_positive
    input_data: Dict
    expected_output: Any
    timeout_seconds: int = 30
    required_confidence: float = 0.8


class PatternTester:
    """Comprehensive pattern testing framework"""

    def __init__(self, pattern_library, intelligence_engine=None):
        self.pattern_library = pattern_library
        self.intelligence_engine = intelligence_engine
        self.test_sandbox = PatternSandbox()

    async def test_pattern(self, pattern: Dict) -> Dict[str, Any]:
        """Run complete test suite on a pattern"""
        test_report = {
            'pattern_id': pattern['pattern_id'],
            'pattern_name': pattern['pattern_name'],
            'test_time': datetime.utcnow().isoformat(),
            'tests': {},
            'summary': {}
        }

        # Run different test types
        test_results = []

        # 1. Syntax validation
        syntax_result = await self._test_syntax(pattern)
        test_results.append(('syntax', syntax_result))

        # 2. Logic validation
        logic_result = await self._test_logic(pattern)
        test_results.append(('logic', logic_result))

        # 3. Performance testing
        perf_result = await self._test_performance(pattern)
        test_results.append(('performance', perf_result))

        # 4. False positive testing
        fp_result = await self._test_false_positives(pattern)
        test_results.append(('false_positives', fp_result))

        # 5. Integration testing
        integration_result = await self._test_integration(pattern)
        test_results.append(('integration', integration_result))

        # 6. Security testing
        security_result = await self._test_security(pattern)
        test_results.append(('security', security_result))

        # Compile results
        for test_type, result in test_results:
            test_report['tests'][test_type] = result

        # Generate summary
        test_report['summary'] = self._generate_test_summary(test_results)

        return test_report

    async def _test_syntax(self, pattern: Dict) -> Dict:
        """Validate pattern syntax"""
        result = {
            'status': TestResult.PASSED,
            'errors': [],
            'warnings': []
        }

        try:
            # Check required fields
            required_fields = ['pattern_id', 'pattern_name', 'pattern_type', 'pattern_data']
            for field in required_fields:
                if field not in pattern:
                    result['errors'].append(f"Missing required field: {field}")
                    result['status'] = TestResult.FAILED

            # Validate pattern data structure
            pattern_data = pattern.get('pattern_data', {})
            pattern_type = pattern.get('pattern_type')

            if pattern_type == 'detection':
                if 'detection_logic' not in pattern_data:
                    result['errors'].append("Detection pattern missing detection_logic")
                    result['status'] = TestResult.FAILED

            elif pattern_type == 'extraction':
                if 'extraction_rules' not in pattern_data:
                    result['errors'].append("Extraction pattern missing extraction_rules")
                    result['status'] = TestResult.FAILED

            elif pattern_type == 'enrichment':
                if 'enrichment_config' not in pattern_data:
                    result['errors'].append("Enrichment pattern missing enrichment_config")
                    result['status'] = TestResult.FAILED

            # Validate JSON serialization
            try:
                json.dumps(pattern)
            except Exception as e:
                result['errors'].append(f"Pattern not JSON serializable: {e}")
                result['status'] = TestResult.FAILED

        except Exception as e:
            result['status'] = TestResult.ERROR
            result['errors'].append(f"Syntax validation error: {e}")

        return result

    async def _test_logic(self, pattern: Dict) -> Dict:
        """Test pattern logic with sample data"""
        result = {
            'status': TestResult.PASSED,
            'test_cases': [],
            'success_rate': 0.0
        }

        try:
            # Get or generate test cases
            test_cases = await self._get_test_cases(pattern)

            if not test_cases:
                result['status'] = TestResult.SKIPPED
                result['message'] = "No test cases available"
                return result

            # Run each test case in sandbox
            passed = 0
            for test_case in test_cases:
                case_result = await self.test_sandbox.run_test_case(pattern, test_case)

                result['test_cases'].append({
                    'test_name': test_case.test_name,
                    'passed': case_result['passed'],
                    'execution_time': case_result['execution_time'],
                    'output': case_result.get('output'),
                    'error': case_result.get('error')
                })

                if case_result['passed']:
                    passed += 1

            # Calculate success rate
            result['success_rate'] = passed / len(test_cases) if test_cases else 0

            # Determine overall status
            if result['success_rate'] < 0.8:  # Less than 80% pass rate
                result['status'] = TestResult.FAILED
            elif result['success_rate'] < 1.0:
                result['status'] = TestResult.PASSED
                result['warnings'] = [f"Only {result['success_rate']*100:.1f}% test cases passed"]

        except Exception as e:
            result['status'] = TestResult.ERROR
            result['error'] = str(e)

        return result

    async def _test_performance(self, pattern: Dict) -> Dict:
        """Test pattern performance characteristics"""
        result = {
            'status': TestResult.PASSED,
            'metrics': {},
            'warnings': []
        }

        try:
            # Generate performance test data
            test_data = self._generate_performance_test_data(pattern['pattern_type'])

            # Measure execution time
            execution_times = []
            memory_usage = []

            for _ in range(100):  # Run 100 iterations
                start_time = time.perf_counter()
                start_memory = self._get_memory_usage()

                await self.test_sandbox.execute_pattern(pattern, test_data)

                execution_times.append((time.perf_counter() - start_time) * 1000)  # ms
                memory_usage.append(self._get_memory_usage() - start_memory)

            # Calculate metrics
            result['metrics'] = {
                'avg_execution_time_ms': sum(execution_times) / len(execution_times),
                'min_execution_time_ms': min(execution_times),
                'max_execution_time_ms': max(execution_times),
                'p95_execution_time_ms': self._percentile(execution_times, 95),
                'p99_execution_time_ms': self._percentile(execution_times, 99),
                'avg_memory_kb': sum(memory_usage) / len(memory_usage) / 1024
            }

            # Check performance thresholds
            if result['metrics']['avg_execution_time_ms'] > 100:
                result['warnings'].append("Average execution time exceeds 100ms")
            if result['metrics']['p99_execution_time_ms'] > 500:
                result['warnings'].append("P99 execution time exceeds 500ms")
                result['status'] = TestResult.FAILED

        except Exception as e:
            result['status'] = TestResult.ERROR
            result['error'] = str(e)

        return result

    async def _test_false_positives(self, pattern: Dict) -> Dict:
        """Test pattern for false positives"""
        result = {
            'status': TestResult.PASSED,
            'false_positive_rate': 0.0,
            'test_samples': 0
        }

        try:
            # Get benign test data
            benign_samples = await self._get_benign_samples(pattern['pattern_type'])

            if not benign_samples:
                result['status'] = TestResult.SKIPPED
                result['message'] = "No benign samples available"
                return result

            # Test against benign data
            false_positives = 0
            for sample in benign_samples:
                match_result = await self.test_sandbox.execute_pattern(pattern, sample)
                if match_result.get('matched', False):
                    false_positives += 1

            result['test_samples'] = len(benign_samples)
            result['false_positive_rate'] = false_positives / len(benign_samples)

            # Check threshold
            if result['false_positive_rate'] > 0.05:  # More than 5% false positives
                result['status'] = TestResult.FAILED
                result['error'] = f"False positive rate {result['false_positive_rate']*100:.1f}% exceeds 5% threshold"

        except Exception as e:
            result['status'] = TestResult.ERROR
            result['error'] = str(e)

        return result

    async def _test_integration(self, pattern: Dict) -> Dict:
        """Test pattern integration with other patterns"""
        result = {
            'status': TestResult.PASSED,
            'compatible_patterns': [],
            'conflicts': []
        }

        try:
            # Find related patterns
            related_patterns = await self.pattern_library.find_related_patterns(
                pattern['pattern_id']
            )

            for related in related_patterns:
                # Test compatibility
                compat_result = await self._test_pattern_compatibility(
                    pattern, related
                )

                if compat_result['compatible']:
                    result['compatible_patterns'].append({
                        'pattern_id': related['pattern_id'],
                        'pattern_name': related['pattern_name']
                    })
                else:
                    result['conflicts'].append({
                        'pattern_id': related['pattern_id'],
                        'pattern_name': related['pattern_name'],
                        'reason': compat_result.get('reason')
                    })

            # Check for conflicts
            if result['conflicts']:
                result['status'] = TestResult.FAILED
                result['error'] = f"Pattern conflicts with {len(result['conflicts'])} existing patterns"

        except Exception as e:
            result['status'] = TestResult.ERROR
            result['error'] = str(e)

        return result

    async def _test_security(self, pattern: Dict) -> Dict:
        """Test pattern for security vulnerabilities"""
        result = {
            'status': TestResult.PASSED,
            'vulnerabilities': [],
            'warnings': []
        }

        try:
            pattern_data = pattern.get('pattern_data', {})

            # Check for potential injection attacks
            if self._contains_injection_risk(pattern_data):
                result['vulnerabilities'].append("Potential injection vulnerability")
                result['status'] = TestResult.FAILED

            # Check for resource exhaustion
            if self._contains_resource_exhaustion_risk(pattern_data):
                result['vulnerabilities'].append("Potential resource exhaustion")
                result['status'] = TestResult.FAILED

            # Check for information disclosure
            if self._contains_info_disclosure_risk(pattern_data):
                result['warnings'].append("Potential information disclosure")

            # Check for logic bombs
            if self._contains_logic_bomb(pattern_data):
                result['vulnerabilities'].append("Potential logic bomb detected")
                result['status'] = TestResult.FAILED

        except Exception as e:
            result['status'] = TestResult.ERROR
            result['error'] = str(e)

        return result

    async def _get_test_cases(self, pattern: Dict) -> List[TestCase]:
        """Get or generate test cases for pattern"""
        test_cases = []

        # Check for provided test cases
        validation = pattern.get('validation', {})
        if 'test_cases' in validation:
            for tc in validation['test_cases']:
                test_cases.append(TestCase(
                    test_id=str(uuid4()),
                    test_name=tc.get('name', 'Test'),
                    test_type=tc.get('type', 'unit'),
                    input_data=tc.get('input'),
                    expected_output=tc.get('expected')
                ))

        # Generate additional test cases if needed
        if len(test_cases) < 5 and self.intelligence_engine:
            generated = await self._generate_test_cases(pattern)
            test_cases.extend(generated)

        return test_cases

    async def _generate_test_cases(self, pattern: Dict) -> List[TestCase]:
        """Use AI to generate test cases"""
        if not self.intelligence_engine:
            return []

        try:
            # Ask AI to generate test cases
            response = await self.intelligence_engine.generate_test_cases(pattern)

            test_cases = []
            for tc in response.get('test_cases', []):
                test_cases.append(TestCase(
                    test_id=str(uuid4()),
                    test_name=tc['name'],
                    test_type='generated',
                    input_data=tc['input'],
                    expected_output=tc['expected']
                ))

            return test_cases

        except Exception as e:
            logger.error(f"Failed to generate test cases: {e}")
            return []

    def _generate_performance_test_data(self, pattern_type: str) -> Dict:
        """Generate data for performance testing"""
        if pattern_type == 'detection':
            return {
                'log': 'Sample log data ' * 100,  # Large log entry
                'metadata': {'source': 'test', 'timestamp': datetime.utcnow().isoformat()}
            }
        elif pattern_type == 'extraction':
            return {
                'text': 'IP: ***********, User: admin, Process: cmd.exe ' * 50
            }
        elif pattern_type == 'enrichment':
            return {
                'entity': {'type': 'ip_address', 'value': '***********'}
            }
        else:
            return {'test_data': 'generic'}

    async def _get_benign_samples(self, pattern_type: str) -> List[Dict]:
        """Get benign samples for false positive testing"""
        # TODO: Load from database or file
        # For now, return synthetic benign data
        samples = []

        for i in range(100):
            if pattern_type == 'detection':
                samples.append({
                    'log': f'Normal user activity log entry {i}',
                    'source': 'windows',
                    'level': 'info'
                })
            elif pattern_type == 'extraction':
                samples.append({
                    'text': f'Normal business communication {i}'
                })

        return samples

    async def _test_pattern_compatibility(self, pattern1: Dict, pattern2: Dict) -> Dict:
        """Test if two patterns are compatible"""
        result = {'compatible': True}

        try:
            # Check for conflicting types
            if pattern1['pattern_type'] == pattern2['pattern_type']:
                # Same type patterns might conflict
                if self._patterns_overlap(pattern1, pattern2):
                    result['compatible'] = False
                    result['reason'] = "Overlapping detection logic"

        except Exception as e:
            result['compatible'] = False
            result['reason'] = str(e)

        return result

    def _patterns_overlap(self, pattern1: Dict, pattern2: Dict) -> bool:
        """Check if patterns have overlapping logic"""
        # Simplified check - would be more complex in production
        data1 = pattern1.get('pattern_data', {})
        data2 = pattern2.get('pattern_data', {})

        # Check for common fields
        fields1 = set(data1.get('fields', []))
        fields2 = set(data2.get('fields', []))

        return len(fields1.intersection(fields2)) > 0

    def _contains_injection_risk(self, pattern_data: Dict) -> bool:
        """Check for injection vulnerabilities"""
        risk_patterns = ['eval(', 'exec(', '__import__', 'subprocess', 'os.system']
        data_str = json.dumps(pattern_data)
        return any(risk in data_str for risk in risk_patterns)

    def _contains_resource_exhaustion_risk(self, pattern_data: Dict) -> bool:
        """Check for resource exhaustion risks"""
        # Check for unbounded loops or recursion
        data_str = json.dumps(pattern_data)
        return 'while True' in data_str or 'recursion' in data_str

    def _contains_info_disclosure_risk(self, pattern_data: Dict) -> bool:
        """Check for information disclosure risks"""
        sensitive_fields = ['password', 'token', 'secret', 'key', 'credential']
        data_str = json.dumps(pattern_data).lower()
        return any(field in data_str for field in sensitive_fields)

    def _contains_logic_bomb(self, pattern_data: Dict) -> bool:
        """Check for potential logic bombs"""
        # Check for time-based triggers
        data_str = json.dumps(pattern_data)
        return 'sleep(' in data_str or 'time.sleep' in data_str

    def _percentile(self, values: List[float], percentile: float) -> float:
        """Calculate percentile value"""
        sorted_values = sorted(values)
        index = int(len(sorted_values) * percentile / 100)
        return sorted_values[min(index, len(sorted_values) - 1)]

    def _get_memory_usage(self) -> int:
        """Get current memory usage in bytes"""
        # Simplified - would use psutil in production
        return 0

    def _generate_test_summary(self, test_results: List[Tuple[str, Dict]]) -> Dict:
        """Generate test summary"""
        summary = {
            'total_tests': len(test_results),
            'passed': 0,
            'failed': 0,
            'skipped': 0,
            'errors': 0,
            'overall_status': TestResult.PASSED
        }

        for test_type, result in test_results:
            status = result.get('status', TestResult.ERROR)

            if status == TestResult.PASSED:
                summary['passed'] += 1
            elif status == TestResult.FAILED:
                summary['failed'] += 1
                summary['overall_status'] = TestResult.FAILED
            elif status == TestResult.SKIPPED:
                summary['skipped'] += 1
            else:
                summary['errors'] += 1
                if summary['overall_status'] != TestResult.FAILED:
                    summary['overall_status'] = TestResult.ERROR

        summary['success_rate'] = summary['passed'] / summary['total_tests'] if summary['total_tests'] > 0 else 0

        return summary


class PatternSandbox:
    """Isolated sandbox for pattern execution"""

    def __init__(self):
        self.execution_timeout = 30  # seconds

    async def run_test_case(self, pattern: Dict, test_case: TestCase) -> Dict:
        """Run test case in sandbox"""
        result = {
            'passed': False,
            'execution_time': 0
        }

        try:
            start_time = time.perf_counter()

            # Execute pattern with timeout
            output = await asyncio.wait_for(
                self.execute_pattern(pattern, test_case.input_data),
                timeout=test_case.timeout_seconds
            )

            result['execution_time'] = time.perf_counter() - start_time
            result['output'] = output

            # Check expected output
            if test_case.expected_output is not None:
                result['passed'] = self._compare_output(
                    output, test_case.expected_output
                )
            else:
                result['passed'] = True  # No expected output means just check for no errors

        except asyncio.TimeoutError:
            result['error'] = f"Test case timed out after {test_case.timeout_seconds}s"
        except Exception as e:
            result['error'] = str(e)

        return result

    async def execute_pattern(self, pattern: Dict, input_data: Dict) -> Any:
        """Execute pattern in sandbox (simplified)"""
        # This is a simplified implementation
        # In production, this would run in an actual sandbox environment

        pattern_type = pattern.get('pattern_type')
        pattern_data = pattern.get('pattern_data', {})

        if pattern_type == 'detection':
            return await self._execute_detection(pattern_data, input_data)
        elif pattern_type == 'extraction':
            return await self._execute_extraction(pattern_data, input_data)
        elif pattern_type == 'enrichment':
            return await self._execute_enrichment(pattern_data, input_data)
        else:
            return {'executed': True}

    async def _execute_detection(self, pattern_data: Dict, input_data: Dict) -> Dict:
        """Execute detection pattern"""
        # Simplified detection logic
        detection_logic = pattern_data.get('detection_logic', {})

        # Check if input matches detection criteria
        matched = False
        for field, value in detection_logic.get('match', {}).items():
            if field in input_data and value in str(input_data[field]):
                matched = True
                break

        return {'matched': matched, 'confidence': 0.9 if matched else 0.1}

    async def _execute_extraction(self, pattern_data: Dict, input_data: Dict) -> Dict:
        """Execute extraction pattern"""
        # Simplified extraction
        extracted = {}
        rules = pattern_data.get('extraction_rules', {})

        for entity_type, patterns in rules.items():
            extracted[entity_type] = []
            # Would use regex or other extraction logic here

        return {'extracted': extracted}

    async def _execute_enrichment(self, pattern_data: Dict, input_data: Dict) -> Dict:
        """Execute enrichment pattern"""
        # Simplified enrichment
        enriched = input_data.copy()
        config = pattern_data.get('enrichment_config', {})

        # Add enrichment data
        enriched['enrichment'] = {
            'timestamp': datetime.utcnow().isoformat(),
            'source': config.get('source', 'test')
        }

        return enriched

    def _compare_output(self, actual: Any, expected: Any) -> bool:
        """Compare actual output with expected"""
        if isinstance(expected, dict) and isinstance(actual, dict):
            # For dicts, check if expected keys/values are present
            for key, value in expected.items():
                if key not in actual:
                    return False
                if not self._compare_output(actual[key], value):
                    return False
            return True
        elif isinstance(expected, list) and isinstance(actual, list):
            # For lists, check length and elements
            if len(expected) != len(actual):
                return False
            return all(self._compare_output(a, e) for a, e in zip(actual, expected))
        else:
            # Direct comparison
            return actual == expected