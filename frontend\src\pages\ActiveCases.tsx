import React from 'react'
import { useParams } from 'react-router-dom'

const ActiveCases: React.FC = () => {
  const { caseId } = useParams()

  return (
    <div className="h-full p-4">
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-4">Active Cases</h1>
        {caseId ? (
          <div>
            <p className="text-gray-600">Viewing Case: {caseId}</p>
          </div>
        ) : (
          <div className="space-y-4">
            <p className="text-gray-600">Cases list will appear here</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default ActiveCases