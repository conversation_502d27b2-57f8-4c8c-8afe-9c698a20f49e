# Log Source Library - Comprehensive Catalog

## Table of Contents
1. [Log Source Quality Tiers](#log-source-quality-tiers)
2. [Endpoint Detection & Response (EDR)](#endpoint-detection--response-edr)
3. [Network Security](#network-security)
4. [Identity & Access Management](#identity--access-management)
5. [Cloud Platforms](#cloud-platforms)
6. [Applications & Databases](#applications--databases)
7. [Operating Systems](#operating-systems)
8. [Correlation Requirements Matrix](#correlation-requirements-matrix)
9. [Detection Coverage Mapping](#detection-coverage-mapping)

---

## Log Source Quality Tiers

### Tier System Overview

```python
LOG_SOURCE_TIERS = {
    'PLATINUM': {
        'quality_score': 95,
        'characteristics': [
            'Kernel-level visibility',
            'Process genealogy',
            'Memory analysis',
            'Behavioral detection',
            'Real-time streaming',
            'Structured data'
        ],
        'examples': ['CrowdStrike', 'SentinelOne', 'CarbonBlack']
    },
    'GOLD': {
        'quality_score': 80,
        'characteristics': [
            'System-level visibility',
            'Process monitoring',
            'Network connections',
            'File integrity',
            'Near real-time',
            'Semi-structured data'
        ],
        'examples': ['Microsoft Defender', 'Sysmon', 'osquery']
    },
    'SILVER': {
        'quality_score': 65,
        'characteristics': [
            'Application-level logs',
            'Authentication events',
            'Network flows',
            'Periodic collection',
            'Structured data'
        ],
        'examples': ['Windows Event Logs', 'Firewall logs', 'VPN logs']
    },
    'BRONZE': {
        'quality_score': 45,
        'characteristics': [
            'Basic logging',
            'Limited context',
            'Delayed collection',
            'Unstructured data'
        ],
        'examples': ['Syslog', 'Application logs', 'Custom scripts']
    }
}
```

---

## Endpoint Detection & Response (EDR)

### CrowdStrike Falcon - PLATINUM TIER
**Quality Score**: 98/100

```python
CROWDSTRIKE_CAPABILITIES = {
    'visibility': {
        'process_creation': True,
        'process_injection': True,
        'file_operations': True,
        'registry_changes': True,
        'network_connections': True,
        'dns_queries': True,
        'memory_analysis': True,
        'kernel_events': True
    },
    'detection_coverage': {
        'mitre_coverage': '94%',
        'techniques_detected': 180,
        'behavioral_detection': True,
        'ml_detection': True,
        'ioc_matching': True
    },
    'data_quality': {
        'structure': 'JSON',
        'enrichment': 'Built-in',
        'latency': '<1 second',
        'retention': '30 days',
        'volume': '500-1000 events/endpoint/hour'
    },
    'key_fields': [
        'aid',           # Agent ID
        'CommandLine',   # Full command line
        'FileName',      # File name
        'SHA256HashData', # File hash
        'RemoteAddressIP4', # Network connection
        'UserName',      # User context
        'ParentProcessId', # Process tree
        'InjectedDll'    # DLL injection
    ],
    'correlation_value': {
        'attack_chain': 'EXCELLENT',
        'lateral_movement': 'EXCELLENT',
        'persistence': 'EXCELLENT',
        'data_theft': 'EXCELLENT'
    }
}

# Example Event Structure
CROWDSTRIKE_EVENT_EXAMPLE = {
    "event_simpleName": "ProcessRollup2",
    "name": "ProcessRollup2V19",
    "aid": "6f3e2c3d4b5a1234567890abcdef",
    "CommandLine": "powershell.exe -EncodedCommand SGVsbG8gV29ybGQ=",
    "FileName": "\\Device\\HarddiskVolume2\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe",
    "SHA256HashData": "908b64b1971a979c7e3e8ce4621945cba84854cb98d76367b791a6e22b5f6d53",
    "UserName": "DOMAIN\\john.doe",
    "ParentProcessId": "12345678",
    "RemoteAddressIP4": "********",
    "RemotePort": "443",
    "ProcessStartTime": "1635360000.123"
}
```

### SentinelOne - PLATINUM TIER
**Quality Score**: 96/100

```python
SENTINELONE_CAPABILITIES = {
    'visibility': {
        'process_behavior': True,
        'file_tracking': True,
        'network_activity': True,
        'cross_process_injection': True,
        'script_execution': True,
        'registry_monitoring': True
    },
    'unique_features': [
        'Storyline technology',
        'Automated response',
        'Rollback capability',
        'Deep visibility mode'
    ],
    'correlation_value': {
        'ransomware': 'EXCELLENT',
        'fileless_attacks': 'EXCELLENT',
        'supply_chain': 'GOOD'
    }
}
```

### Microsoft Defender for Endpoint - GOLD TIER
**Quality Score**: 82/100

```python
DEFENDER_CAPABILITIES = {
    'visibility': {
        'process_creation': True,
        'file_creation': True,
        'registry_events': True,
        'network_connections': True,
        'wmi_events': True,
        'powershell_logging': True
    },
    'integration': {
        'azure_ad': 'Native',
        'office365': 'Native',
        'azure_sentinel': 'Native'
    },
    'key_tables': [
        'DeviceProcessEvents',
        'DeviceFileEvents',
        'DeviceRegistryEvents',
        'DeviceNetworkEvents',
        'DeviceLogonEvents'
    ]
}
```

### Sysmon - GOLD TIER
**Quality Score**: 78/100

```python
SYSMON_CAPABILITIES = {
    'event_ids': {
        1: 'Process creation',
        2: 'File creation time changed',
        3: 'Network connection',
        5: 'Process terminated',
        6: 'Driver loaded',
        7: 'Image loaded',
        8: 'CreateRemoteThread',
        10: 'ProcessAccess',
        11: 'FileCreate',
        12: 'RegistryEvent (Object create/delete)',
        13: 'RegistryEvent (Value set)',
        14: 'RegistryEvent (Key/Value rename)',
        15: 'FileCreateStreamHash',
        17: 'PipeEvent (Pipe created)',
        18: 'PipeEvent (Pipe connected)',
        19: 'WmiEvent',
        20: 'WmiEvent',
        21: 'WmiEvent',
        22: 'DNSEvent',
        23: 'FileDelete',
        24: 'ClipboardChange',
        25: 'ProcessTampering',
        26: 'FileDeleteDetected'
    },
    'configuration': {
        'xml_based': True,
        'hash_algorithms': ['MD5', 'SHA256', 'IMPHASH'],
        'network_connection': True
    },
    'correlation_value': {
        'process_injection': 'GOOD',
        'persistence': 'EXCELLENT',
        'lateral_movement': 'GOOD'
    }
}
```

---

## Network Security

### Palo Alto Networks Firewall - GOLD TIER
**Quality Score**: 85/100

```python
PALOALTO_CAPABILITIES = {
    'log_types': {
        'traffic': {
            'volume': 'HIGH',
            'fields': ['src', 'dst', 'sport', 'dport', 'app', 'rule', 'bytes'],
            'correlation_value': 'MEDIUM'
        },
        'threat': {
            'volume': 'MEDIUM',
            'fields': ['threatid', 'category', 'severity', 'direction'],
            'correlation_value': 'HIGH'
        },
        'url': {
            'volume': 'HIGH',
            'fields': ['url', 'category', 'action', 'user'],
            'correlation_value': 'MEDIUM'
        },
        'wildfire': {
            'volume': 'LOW',
            'fields': ['verdict', 'filetype', 'sha256'],
            'correlation_value': 'VERY_HIGH'
        }
    },
    'app_id': {
        'applications_identified': 5000,
        'ssl_decryption': True,
        'user_identification': True
    },
    'integration': {
        'cortex_xdr': True,
        'prisma_cloud': True,
        'autofocus': True
    }
}
```

### Fortinet FortiGate - GOLD TIER
**Quality Score**: 83/100

```python
FORTIGATE_CAPABILITIES = {
    'log_types': [
        'traffic',      # Connection logs
        'utm',          # Unified Threat Management
        'anomaly',      # IPS/IDS
        'virus',        # Antivirus
        'webfilter',    # Web filtering
        'application',  # Application control
        'dlp'          # Data Loss Prevention
    ],
    'key_features': {
        'session_tracking': True,
        'user_identification': True,
        'application_control': True,
        'ssl_inspection': True
    },
    'log_format': {
        'type': 'CEF/Syslog',
        'structured': True,
        'volume': '1000-5000 logs/second'
    }
}
```

### Cisco ASA/Firepower - SILVER TIER
**Quality Score**: 72/100

```python
CISCO_CAPABILITIES = {
    'asa_logs': {
        'connection_events': True,
        'acl_hits': True,
        'vpn_events': True,
        'nat_translations': True
    },
    'firepower_logs': {
        'intrusion_events': True,
        'file_events': True,
        'malware_events': True,
        'security_intelligence': True
    },
    'syslog_ids': {
        '106023': 'ACL Deny',
        '302013': 'TCP Connection Built',
        '302014': 'TCP Connection Teardown',
        '113019': 'SSL Session',
        '716002': 'VPN User Authentication'
    }
}
```

### Zeek (formerly Bro) - GOLD TIER
**Quality Score**: 88/100

```python
ZEEK_CAPABILITIES = {
    'protocol_logs': [
        'conn.log',     # Connection summary
        'dns.log',      # DNS queries/responses
        'http.log',     # HTTP requests/responses
        'ssl.log',      # SSL/TLS handshakes
        'files.log',    # File transfers
        'smtp.log',     # Email metadata
        'ssh.log',      # SSH connections
        'rdp.log',      # RDP sessions
        'smb.log'       # SMB/CIFS activity
    ],
    'metadata_extraction': {
        'file_hashes': True,
        'certificates': True,
        'ja3_fingerprints': True,
        'http_headers': True,
        'dns_answers': True
    },
    'correlation_value': {
        'network_recon': 'EXCELLENT',
        'lateral_movement': 'EXCELLENT',
        'data_exfiltration': 'EXCELLENT',
        'c2_detection': 'EXCELLENT'
    }
}
```

---

## Identity & Access Management

### Active Directory - PLATINUM TIER
**Quality Score**: 92/100

```python
ACTIVE_DIRECTORY_CAPABILITIES = {
    'domain_controller_logs': {
        '4624': 'Successful logon',
        '4625': 'Failed logon',
        '4634': 'Logoff',
        '4648': 'Explicit credential logon',
        '4672': 'Special privileges assigned',
        '4720': 'User account created',
        '4726': 'User account deleted',
        '4728': 'Member added to security group',
        '4732': 'Member added to local group',
        '4740': 'Account locked out',
        '4768': 'Kerberos TGT requested',
        '4769': 'Kerberos service ticket requested',
        '4776': 'NTLM authentication'
    },
    'advanced_auditing': {
        'credential_validation': True,
        'kerberos_events': True,
        'directory_service_access': True,
        'directory_service_changes': True
    },
    'correlation_value': {
        'credential_compromise': 'EXCELLENT',
        'privilege_escalation': 'EXCELLENT',
        'lateral_movement': 'EXCELLENT',
        'golden_ticket': 'EXCELLENT'
    }
}
```

### Okta - GOLD TIER
**Quality Score**: 86/100

```python
OKTA_CAPABILITIES = {
    'event_types': {
        'user.session.start': 'Login',
        'user.session.end': 'Logout',
        'user.authentication.auth_via_mfa': 'MFA challenge',
        'application.provision.user.deactivate': 'App access revoked',
        'user.account.lock': 'Account locked',
        'policy.evaluate_sign_on': 'Policy evaluation'
    },
    'rich_context': {
        'geo_location': True,
        'device_fingerprint': True,
        'risk_scoring': True,
        'threat_insights': True
    },
    'integration': {
        'saml_apps': 7000,
        'oidc_apps': True,
        'radius': True,
        'ldap': True
    }
}
```

### Azure AD - GOLD TIER
**Quality Score**: 84/100

```python
AZURE_AD_CAPABILITIES = {
    'sign_in_logs': {
        'interactive': True,
        'non_interactive': True,
        'service_principal': True,
        'managed_identity': True
    },
    'audit_logs': {
        'user_management': True,
        'group_management': True,
        'application_management': True,
        'directory_management': True
    },
    'risk_detection': {
        'risky_users': True,
        'risky_sign_ins': True,
        'risk_events': True
    },
    'conditional_access': {
        'policy_evaluation': True,
        'mfa_events': True,
        'device_compliance': True
    }
}
```

---

## Cloud Platforms

### AWS CloudTrail - GOLD TIER
**Quality Score': 87/100

```python
AWS_CLOUDTRAIL_CAPABILITIES = {
    'event_categories': {
        'management_events': {
            'api_calls': True,
            'console_sign_in': True,
            'service_events': True
        },
        'data_events': {
            's3_object_access': True,
            'lambda_invocations': True,
            'dynamodb_operations': True
        },
        'insights_events': {
            'unusual_api_activity': True,
            'error_rate_insights': True
        }
    },
    'key_fields': [
        'eventName',
        'userIdentity',
        'sourceIPAddress',
        'userAgent',
        'requestParameters',
        'responseElements',
        'errorCode'
    ],
    'correlation_value': {
        'privilege_escalation': 'EXCELLENT',
        'data_exfiltration': 'GOOD',
        'resource_hijacking': 'EXCELLENT'
    }
}
```

### Azure Activity Logs - GOLD TIER
**Quality Score**: 85/100

```python
AZURE_ACTIVITY_CAPABILITIES = {
    'log_categories': [
        'Administrative',
        'Security',
        'ServiceHealth',
        'Alert',
        'Recommendation',
        'Policy',
        'Autoscale',
        'ResourceHealth'
    ],
    'operations_tracked': {
        'write_operations': True,
        'delete_operations': True,
        'action_operations': True
    },
    'resource_providers': 150,
    'correlation_id': True
}
```

### Google Cloud Audit Logs - GOLD TIER
**Quality Score**: 86/100

```python
GCP_AUDIT_CAPABILITIES = {
    'log_types': {
        'admin_activity': {
            'api_calls': True,
            'configuration_changes': True
        },
        'data_access': {
            'read_operations': True,
            'write_operations': True
        },
        'system_event': {
            'service_actions': True
        },
        'policy_denied': {
            'authorization_failures': True
        }
    },
    'coverage': {
        'services': 100,
        'automatic': True,
        'retention': '400 days'
    }
}
```

---

## Applications & Databases

### Web Server Logs - SILVER TIER

```python
WEB_SERVER_CAPABILITIES = {
    'apache': {
        'access_log': {
            'fields': ['ip', 'timestamp', 'method', 'uri', 'status', 'size', 'referer', 'user_agent'],
            'correlation_value': 'MEDIUM'
        },
        'error_log': {
            'fields': ['timestamp', 'level', 'client', 'message'],
            'correlation_value': 'LOW'
        }
    },
    'nginx': {
        'formats': ['combined', 'json', 'custom'],
        'real_time': True,
        'buffering': True
    },
    'iis': {
        'w3c_format': True,
        'fields_customizable': True,
        'substatus_codes': True
    },
    'detection_use_cases': [
        'Web attacks (SQLi, XSS)',
        'Brute force',
        'Web shells',
        'Data exfiltration via HTTP'
    ]
}
```

### Database Audit Logs - GOLD TIER

```python
DATABASE_AUDIT_CAPABILITIES = {
    'oracle': {
        'unified_audit': True,
        'fine_grained_audit': True,
        'vault_audit': True,
        'events': ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DDL', 'DCL']
    },
    'sql_server': {
        'audit_specifications': True,
        'extended_events': True,
        'c2_audit': True,
        'events': ['LOGIN', 'LOGOUT', 'DDL', 'DML', 'PERMISSION_CHANGE']
    },
    'postgresql': {
        'pgaudit': True,
        'log_statement': ['none', 'ddl', 'mod', 'all'],
        'log_connections': True
    },
    'correlation_value': {
        'data_theft': 'EXCELLENT',
        'privilege_abuse': 'EXCELLENT',
        'sql_injection': 'GOOD'
    }
}
```

---

## Operating Systems

### Windows Event Logs - SILVER TIER
**Quality Score**: 70/100

```python
WINDOWS_EVENT_CAPABILITIES = {
    'channels': {
        'Security': {
            'volume': 'HIGH',
            'value': 'EXCELLENT',
            'key_events': ['4624', '4625', '4648', '4672', '4688']
        },
        'System': {
            'volume': 'MEDIUM',
            'value': 'MEDIUM',
            'key_events': ['7034', '7035', '7036', '7040']
        },
        'Application': {
            'volume': 'HIGH',
            'value': 'LOW',
            'key_events': ['Application specific']
        },
        'PowerShell/Operational': {
            'volume': 'MEDIUM',
            'value': 'EXCELLENT',
            'key_events': ['4103', '4104']
        },
        'Microsoft-Windows-Sysmon/Operational': {
            'volume': 'HIGH',
            'value': 'EXCELLENT'
        }
    },
    'limitations': [
        'Event ID reuse across sources',
        'Limited command line logging (without Sysmon)',
        'Rotation/loss of events',
        'Requires configuration'
    ]
}
```

### Linux Auditd - GOLD TIER
**Quality Score**: 76/100

```python
LINUX_AUDITD_CAPABILITIES = {
    'syscall_monitoring': {
        'execve': 'Process execution',
        'open': 'File access',
        'connect': 'Network connections',
        'setuid': 'Privilege changes'
    },
    'file_integrity_monitoring': {
        'watches': True,
        'attributes': ['permissions', 'ownership', 'timestamps', 'content']
    },
    'user_activity': {
        'authentication': True,
        'authorization': True,
        'user_commands': True
    },
    'correlation_value': {
        'privilege_escalation': 'EXCELLENT',
        'persistence': 'GOOD',
        'data_access': 'EXCELLENT'
    }
}
```

### macOS Unified Logs - SILVER TIER
**Quality Score**: 68/100

```python
MACOS_UNIFIED_CAPABILITIES = {
    'subsystems': [
        'com.apple.securityd',
        'com.apple.sudo',
        'com.apple.system.logger',
        'com.apple.xpc.activity'
    ],
    'predicates': {
        'process': True,
        'sender': True,
        'eventMessage': True
    },
    'limitations': [
        'Short retention',
        'Requires specific predicates',
        'Less documented'
    ]
}
```

---

## Correlation Requirements Matrix

### Multi-Source Correlation Requirements

```python
CORRELATION_REQUIREMENTS = {
    'ransomware_detection': {
        'required_sources': [
            {'type': 'EDR', 'tier': 'GOLD', 'reason': 'Process and file activity'},
            {'type': 'FILE_INTEGRITY', 'tier': 'SILVER', 'reason': 'Mass file changes'},
            {'type': 'NETWORK', 'tier': 'SILVER', 'reason': 'C2 communication'}
        ],
        'optional_sources': [
            {'type': 'BACKUP', 'reason': 'Deletion of backups'},
            {'type': 'IDENTITY', 'reason': 'Privilege escalation'}
        ],
        'confidence_by_sources': {
            1: 30,  # Single source
            2: 65,  # Two sources
            3: 85,  # Three sources
            4: 95   # Four+ sources
        }
    },

    'lateral_movement': {
        'required_sources': [
            {'type': 'IDENTITY', 'tier': 'GOLD', 'reason': 'Authentication events'},
            {'type': 'NETWORK', 'tier': 'SILVER', 'reason': 'RDP/SSH connections'},
            {'type': 'EDR', 'tier': 'GOLD', 'reason': 'Remote execution'}
        ],
        'correlation_window': '4 hours',
        'minimum_events': 3
    },

    'data_exfiltration': {
        'required_sources': [
            {'type': 'NETWORK', 'tier': 'GOLD', 'reason': 'Large transfers'},
            {'type': 'DLP', 'tier': 'SILVER', 'reason': 'Sensitive data movement'},
            {'type': 'CLOUD', 'tier': 'SILVER', 'reason': 'Cloud uploads'}
        ],
        'thresholds': {
            'data_volume': '100MB',
            'time_window': '1 hour',
            'destination_reputation': 'CHECK'
        }
    },

    'insider_threat': {
        'required_sources': [
            {'type': 'IDENTITY', 'tier': 'GOLD', 'reason': 'Unusual access patterns'},
            {'type': 'DATA_ACCESS', 'tier': 'GOLD', 'reason': 'Database queries'},
            {'type': 'FILE_ACCESS', 'tier': 'SILVER', 'reason': 'File downloads'},
            {'type': 'EMAIL', 'tier': 'SILVER', 'reason': 'Data sending'}
        ],
        'behavioral_baseline': 'REQUIRED',
        'minimum_anomaly_score': 75
    },

    'account_compromise': {
        'required_sources': [
            {'type': 'IDENTITY', 'tier': 'PLATINUM', 'reason': 'Authentication anomalies'},
            {'type': 'ENDPOINT', 'tier': 'GOLD', 'reason': 'Credential theft tools'}
        ],
        'indicators': [
            'Impossible travel',
            'Unusual MFA patterns',
            'New device/location',
            'Privilege escalation'
        ]
    }
}
```

---

## Detection Coverage Mapping

### MITRE ATT&CK Coverage by Log Source

```python
MITRE_COVERAGE_MATRIX = {
    'Initial Access': {
        'T1566': {  # Phishing
            'email_gateway': 90,
            'endpoint_edr': 70,
            'network_proxy': 60
        },
        'T1078': {  # Valid Accounts
            'identity_logs': 95,
            'vpn_logs': 85,
            'cloud_logs': 80
        }
    },

    'Execution': {
        'T1059': {  # Command and Scripting Interpreter
            'endpoint_edr': 95,
            'sysmon': 90,
            'powershell_logs': 95,
            'bash_history': 60
        }
    },

    'Persistence': {
        'T1547': {  # Boot or Logon Autostart Execution
            'endpoint_edr': 90,
            'sysmon': 85,
            'windows_events': 60
        }
    },

    'Privilege Escalation': {
        'T1055': {  # Process Injection
            'endpoint_edr': 95,
            'sysmon': 80,
            'windows_events': 30
        }
    },

    'Defense Evasion': {
        'T1070': {  # Indicator Removal on Host
            'endpoint_edr': 85,
            'file_integrity': 70,
            'windows_events': 50
        }
    },

    'Credential Access': {
        'T1003': {  # OS Credential Dumping
            'endpoint_edr': 95,
            'sysmon': 75,
            'windows_events': 60
        }
    },

    'Discovery': {
        'T1082': {  # System Information Discovery
            'endpoint_edr': 80,
            'windows_events': 40,
            'auditd': 70
        }
    },

    'Lateral Movement': {
        'T1021': {  # Remote Services
            'network_logs': 85,
            'identity_logs': 90,
            'endpoint_edr': 85,
            'windows_events': 70
        }
    },

    'Collection': {
        'T1074': {  # Data Staged
            'endpoint_edr': 75,
            'file_integrity': 60,
            'dlp': 80
        }
    },

    'Command and Control': {
        'T1071': {  # Application Layer Protocol
            'network_ids': 90,
            'proxy_logs': 85,
            'dns_logs': 75,
            'netflow': 60
        }
    },

    'Exfiltration': {
        'T1041': {  # Exfiltration Over C2 Channel
            'network_ids': 85,
            'dlp': 90,
            'proxy_logs': 80,
            'netflow': 65
        }
    }
}
```

### Log Source Combination Effectiveness

```python
def calculate_detection_confidence(available_sources, attack_technique):
    """
    Calculate detection confidence based on available log sources
    """
    base_confidence = 0
    multiplier = 1.0

    for source in available_sources:
        # Get source contribution
        source_confidence = get_source_confidence(source, attack_technique)

        # Apply tier multiplier
        tier_multiplier = {
            'PLATINUM': 1.0,
            'GOLD': 0.85,
            'SILVER': 0.65,
            'BRONZE': 0.40
        }

        adjusted_confidence = source_confidence * tier_multiplier[source['tier']]

        # Add to base (with diminishing returns)
        if base_confidence == 0:
            base_confidence = adjusted_confidence
        else:
            base_confidence += adjusted_confidence * (1 - base_confidence/100) * 0.5

    return min(base_confidence, 98)  # Cap at 98%

# Example Usage
available_sources = [
    {'name': 'crowdstrike', 'tier': 'PLATINUM'},
    {'name': 'paloalto', 'tier': 'GOLD'},
    {'name': 'windows_events', 'tier': 'SILVER'}
]

detection_confidence = calculate_detection_confidence(
    available_sources,
    'T1055'  # Process Injection
)
# Result: 96% confidence
```

---

## Log Volume & Retention Planning

```python
LOG_VOLUME_ESTIMATES = {
    'endpoints': {
        'edr_premium': {
            'events_per_day': 50000,
            'size_per_day_mb': 100,
            'retention_days': 90,
            'value': 'CRITICAL'
        },
        'windows_events': {
            'events_per_day': 10000,
            'size_per_day_mb': 20,
            'retention_days': 30,
            'value': 'HIGH'
        }
    },
    'network': {
        'firewall': {
            'events_per_day': 1000000,
            'size_per_day_mb': 2000,
            'retention_days': 30,
            'value': 'MEDIUM'
        },
        'ids_ips': {
            'events_per_day': 50000,
            'size_per_day_mb': 100,
            'retention_days': 60,
            'value': 'HIGH'
        }
    },
    'identity': {
        'active_directory': {
            'events_per_day': 100000,
            'size_per_day_mb': 50,
            'retention_days': 180,
            'value': 'CRITICAL'
        }
    },
    'cloud': {
        'cloudtrail': {
            'events_per_day': 200000,
            'size_per_day_mb': 150,
            'retention_days': 90,
            'value': 'HIGH'
        }
    }
}
```

---

## Implementation Recommendations

### Minimum Viable Log Sources

```python
MINIMUM_VIABLE_SOURCES = {
    'small_organization': [
        'Windows Events + Sysmon',
        'Active Directory',
        'Firewall logs',
        'O365/Google Workspace'
    ],

    'medium_organization': [
        'EDR (Gold tier minimum)',
        'Active Directory + Azure AD',
        'Firewall + IDS/IPS',
        'Cloud logs (AWS/Azure/GCP)',
        'Email gateway',
        'Web proxy'
    ],

    'large_enterprise': [
        'EDR (Platinum tier)',
        'Full identity stack (AD, AAD, Okta)',
        'Complete network visibility (FW, IDS, Netflow, Zeek)',
        'Multi-cloud logging',
        'Application logs',
        'Database audit logs',
        'DLP solution'
    ]
}
```

### Log Source Quality Improvement Path

```python
IMPROVEMENT_PATH = [
    {
        'stage': 1,
        'name': 'Foundation',
        'sources': ['Basic OS logs', 'Firewall', 'AD authentication'],
        'detection_capability': 30,
        'cost': '$'
    },
    {
        'stage': 2,
        'name': 'Enhanced',
        'sources': ['Add Sysmon', 'Enable PowerShell logging', 'Add proxy logs'],
        'detection_capability': 50,
        'cost': '$'
    },
    {
        'stage': 3,
        'name': 'Advanced',
        'sources': ['Deploy EDR', 'Add network visibility', 'Cloud logging'],
        'detection_capability': 75,
        'cost': '$$'
    },
    {
        'stage': 4,
        'name': 'Comprehensive',
        'sources': ['Platinum EDR', 'Full stack visibility', 'Behavior analytics'],
        'detection_capability': 90,
        'cost': '$$$'
    }
]
```

---

## Conclusion

Effective correlation requires:
1. **Multiple log sources** - No single source sees everything
2. **Quality over quantity** - Better to have 5 good sources than 50 poor ones
3. **Complementary coverage** - Different sources see different parts of attacks
4. **Proper configuration** - Even good sources need proper setup
5. **Retention strategy** - Keep high-value logs longer

The log source library should be continuously updated as new sources become available and existing sources improve their capabilities.