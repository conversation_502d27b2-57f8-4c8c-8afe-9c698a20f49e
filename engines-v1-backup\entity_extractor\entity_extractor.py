"""
Entity Extraction Engine - Extract and normalize entities from parsed logs
Uses patterns from v1's successful entity_mapping approach
"""
import asyncio
import json
import re
from typing import Dict, Any, Optional, List, Set
from datetime import datetime
from pathlib import Path
from collections import defaultdict

# Add v2 to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.base import BaseEngine


class EntityExtractorEngine(BaseEngine):
    """
    Entity Extraction Engine: Extract entities from parsed logs

    Uses proven patterns from v1:
    - entity_mapping configurations
    - Field normalization
    - Entity deduplication
    - Relationship inference
    """

    def __init__(self):
        super().__init__('entity_extractor', '2.0.0')

        # Entity type definitions (from v1 success)
        self.entity_types = {
            'user': ['username', 'user', 'userid', 'user_name', 'account', 'UserName'],
            'hostname': ['hostname', 'host', 'computer', 'device', 'endpoint', 'ComputerName'],
            'ip': ['ip', 'source_ip', 'dest_ip', 'src_ip', 'dst_ip', 'client_ip', 'server_ip', 'RemoteAddressIP4'],
            'process': ['process', 'proc_name', 'process_name', 'command', 'FileName', 'ImageFileName'],
            'file': ['file', 'filename', 'file_name', 'path', 'filepath'],
            'port': ['port', 'source_port', 'dest_port', 'src_port', 'dst_port', 'RemotePort'],
            'domain': ['domain', 'fqdn', 'dns_name', 'website'],
            'hash': ['hash', 'md5', 'sha1', 'sha256', 'file_hash'],
            'email': ['email', 'email_address', 'mail'],
            'url': ['url', 'uri', 'link', 'website_url']
        }

        # Entity patterns for validation
        self.validation_patterns = {
            'ip': re.compile(r'^(\d{1,3}\.){3}\d{1,3}$'),
            'email': re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
            'domain': re.compile(r'^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$'),
            'md5': re.compile(r'^[a-fA-F0-9]{32}$'),
            'sha1': re.compile(r'^[a-fA-F0-9]{40}$'),
            'sha256': re.compile(r'^[a-fA-F0-9]{64}$'),
            'mac': re.compile(r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$')
        }

        # Entity normalization rules (from v1 experience)
        self.normalization_rules = {
            'hostname': lambda x: x.lower().strip(),
            'username': lambda x: self._normalize_username(x),
            'domain': lambda x: x.lower().strip(),
            'email': lambda x: x.lower().strip(),
            'ip': lambda x: x.strip()
        }

        # Statistics
        self.extraction_stats = {
            'total_processed': 0,
            'entities_extracted': 0,
            'entities_normalized': 0,
            'entities_deduplicated': 0,
            'relationships_inferred': 0
        }

        # Entity cache for deduplication
        self.entity_cache = defaultdict(set)

    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process parsed log to extract entities
        """
        parsed_data = message.get('data', {})

        self.logger.start_operation(f"extract_entities_{message.get('id', 'unknown')}")

        try:
            # Extract entities based on parsed fields
            entities = await self._extract_entities(parsed_data)

            # Normalize entities
            normalized = self._normalize_entities(entities)

            # Deduplicate entities
            deduplicated = self._deduplicate_entities(normalized)

            # Infer relationships
            relationships = self._infer_relationships(deduplicated, parsed_data)

            # Update statistics
            self.extraction_stats['total_processed'] += 1
            self.extraction_stats['entities_extracted'] += len(entities)
            self.extraction_stats['entities_normalized'] += len(normalized)
            self.extraction_stats['entities_deduplicated'] += len(deduplicated)
            self.extraction_stats['relationships_inferred'] += len(relationships)

            # Log extraction
            self.logger.log_decision(
                'entities_extracted',
                parsed_data,
                {
                    'entities': deduplicated,
                    'relationships': relationships
                },
                reasoning=f"Extracted {len(deduplicated)} entities and {len(relationships)} relationships",
                confidence=0.95
            )

            result = {
                'success': True,
                'entities': deduplicated,
                'relationships': relationships,
                'stats': {
                    'extracted': len(entities),
                    'normalized': len(normalized),
                    'deduplicated': len(deduplicated)
                },
                'next_engine': 'graph_builder',
                'data': {
                    'entities': deduplicated,
                    'relationships': relationships,
                    'original_log': parsed_data.get('original_log'),
                    'timestamp': parsed_data.get('timestamp', datetime.utcnow().isoformat())
                }
            }

            return result

        except Exception as e:
            self.logger.log_error(e, {'parsed_data': parsed_data})
            return {
                'success': False,
                'error': str(e)
            }
        finally:
            self.logger.end_operation(f"extract_entities_{message.get('id', 'unknown')}")

    async def _extract_entities(self, parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract entities from parsed log fields
        """
        entities = []

        # Check if there's an entity_mappings field (from parser)
        if 'entity_mappings' in parsed_data:
            mappings = parsed_data['entity_mappings']
            fields = parsed_data.get('fields', {})

            for entity_type, field_path in mappings.items():
                value = self._extract_field_value(fields, field_path)
                if value:
                    entities.append({
                        'type': self._determine_entity_type(entity_type),
                        'value': value,
                        'source_field': field_path,
                        'confidence': 1.0  # High confidence from mapping
                    })

        # Also scan all fields for entity patterns
        fields = parsed_data.get('fields', {})
        for field_name, field_value in fields.items():
            if field_value:
                entity_type = self._identify_entity_type(field_name, field_value)
                if entity_type:
                    entities.append({
                        'type': entity_type,
                        'value': str(field_value),
                        'source_field': field_name,
                        'confidence': 0.8  # Medium confidence from pattern
                    })

        return entities

    def _extract_field_value(self, fields: Dict[str, Any], field_path: str) -> Optional[str]:
        """
        Extract value from nested field path (supports dot notation)
        """
        parts = field_path.split('.')
        current = fields

        for part in parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return None

        return str(current) if current else None

    def _identify_entity_type(self, field_name: str, field_value: str) -> Optional[str]:
        """
        Identify entity type based on field name and value patterns
        """
        field_lower = field_name.lower()

        # Check field name patterns
        for entity_type, patterns in self.entity_types.items():
            for pattern in patterns:
                if pattern.lower() in field_lower:
                    # Validate with regex if available
                    if entity_type in self.validation_patterns:
                        if self.validation_patterns[entity_type].match(str(field_value)):
                            return entity_type
                    else:
                        return entity_type

        # Check value patterns
        for entity_type, pattern in self.validation_patterns.items():
            if pattern.match(str(field_value)):
                return entity_type

        return None

    def _determine_entity_type(self, mapped_type: str) -> str:
        """
        Determine normalized entity type from mapping
        """
        type_lower = mapped_type.lower()

        # Standard mappings from v1
        type_mappings = {
            'hostname': 'hostname',
            'host': 'hostname',
            'computer': 'hostname',
            'computername': 'hostname',
            'user': 'user',
            'username': 'user',
            'userid': 'user',
            'source_ip': 'ip',
            'destination_ip': 'ip',
            'src_ip': 'ip',
            'dst_ip': 'ip',
            'ip': 'ip',
            'process': 'process',
            'filename': 'file',
            'file': 'file',
            'port': 'port',
            'source_port': 'port',
            'destination_port': 'port'
        }

        return type_mappings.get(type_lower, mapped_type)

    def _normalize_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Normalize entity values for consistency
        """
        normalized = []

        for entity in entities:
            entity_type = entity['type']
            value = entity['value']

            # Apply normalization rules
            if entity_type in self.normalization_rules:
                value = self.normalization_rules[entity_type](value)

            normalized.append({
                **entity,
                'value': value,
                'normalized': True
            })

        return normalized

    def _normalize_username(self, username: str) -> str:
        """
        Normalize username (handle DOMAIN\\user and user@domain formats)
        """
        username = username.strip()

        # Handle DOMAIN\\user format
        if '\\' in username:
            parts = username.split('\\')
            if len(parts) == 2:
                return parts[1].lower()

        # Handle user@domain format
        if '@' in username:
            parts = username.split('@')
            if len(parts) >= 1:
                return parts[0].lower()

        return username.lower()

    def _deduplicate_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Deduplicate entities while preserving highest confidence
        """
        unique_entities = {}

        for entity in entities:
            key = f"{entity['type']}:{entity['value']}"

            if key not in unique_entities:
                unique_entities[key] = entity
            else:
                # Keep the one with higher confidence
                if entity.get('confidence', 0) > unique_entities[key].get('confidence', 0):
                    unique_entities[key] = entity

        return list(unique_entities.values())

    def _infer_relationships(self, entities: List[Dict[str, Any]],
                            parsed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Infer relationships between entities based on context
        """
        relationships = []

        # Get relationship mappings if present
        if 'relationship_mappings' in parsed_data:
            for mapping in parsed_data['relationship_mappings']:
                source_type = mapping.get('source')
                target_type = mapping.get('target')
                rel_type = mapping.get('type', 'related_to')

                # Find matching entities
                source_entities = [e for e in entities if e['type'] == source_type]
                target_entities = [e for e in entities if e['type'] == target_type]

                for source in source_entities:
                    for target in target_entities:
                        relationships.append({
                            'source': source,
                            'target': target,
                            'type': rel_type,
                            'confidence': 0.9,
                            'timestamp': parsed_data.get('timestamp', datetime.utcnow().isoformat())
                        })

        # Infer common relationships
        relationships.extend(self._infer_common_relationships(entities, parsed_data))

        return relationships

    def _infer_common_relationships(self, entities: List[Dict[str, Any]],
                                   context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Infer common relationships based on entity types present
        """
        relationships = []

        # Group entities by type
        entities_by_type = defaultdict(list)
        for entity in entities:
            entities_by_type[entity['type']].append(entity)

        # User -> Hostname (logged into)
        if 'user' in entities_by_type and 'hostname' in entities_by_type:
            for user in entities_by_type['user']:
                for host in entities_by_type['hostname']:
                    relationships.append({
                        'source': user,
                        'target': host,
                        'type': 'logged_into',
                        'confidence': 0.7,
                        'inferred': True
                    })

        # Process -> File (accessed)
        if 'process' in entities_by_type and 'file' in entities_by_type:
            for process in entities_by_type['process']:
                for file in entities_by_type['file']:
                    relationships.append({
                        'source': process,
                        'target': file,
                        'type': 'accessed',
                        'confidence': 0.6,
                        'inferred': True
                    })

        # Hostname -> IP (has_ip)
        if 'hostname' in entities_by_type and 'ip' in entities_by_type:
            for host in entities_by_type['hostname']:
                for ip in entities_by_type['ip']:
                    relationships.append({
                        'source': host,
                        'target': ip,
                        'type': 'has_ip',
                        'confidence': 0.8,
                        'inferred': True
                    })

        return relationships

    def get_capabilities(self) -> Dict[str, Any]:
        """
        Return Entity Extractor capabilities
        """
        return {
            'engine': 'entity_extractor',
            'version': self.version,
            'capabilities': [
                'entity_extraction',
                'entity_normalization',
                'entity_deduplication',
                'relationship_inference',
                'type_validation'
            ],
            'supported_entity_types': list(self.entity_types.keys()),
            'statistics': self.extraction_stats,
            'cache_size': sum(len(entities) for entities in self.entity_cache.values())
        }

    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """
        Validate Entity Extractor configuration
        """
        # No required configuration for now
        return True


async def main():
    """
    Main entry point for Entity Extractor Engine
    """
    engine = EntityExtractorEngine()

    # Log capabilities
    capabilities = engine.get_capabilities()
    engine.logger.log('engine_capabilities', capabilities)

    # Start processing
    await engine.start()


if __name__ == '__main__':
    asyncio.run(main())