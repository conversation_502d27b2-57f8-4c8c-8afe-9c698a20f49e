# Elastic Rule Harvester - Quick Start

## 🚀 5-Minute Setup

### 1. Get Your Elastic API Key

In Kibana:
```
Stack Management → Security → API Keys → Create API Key
```

Name: `SIEMLess Harvester`

Privileges needed:
- `read` on `.siem-signals-*`
- `monitor_watcher`
- `read_security`

Copy the **base64-encoded key** (not the ID).

### 2. Edit Test Script

Open `test_elastic_harvester.py` and update:

```python
config = {
    'url': 'https://YOUR-ELASTIC-HOST:9200',
    'api_key': 'YOUR_BASE64_API_KEY',
    'verify_ssl': False  # True in production
}
```

### 3. Run Test

```bash
cd siemless_v2
python test_elastic_harvester.py
```

### 4. Check Results

- Terminal: Shows connection status and sample rules
- File: `elastic_harvest_results.json` has complete export

## 📋 Quick Reference

### Connection Options

**Option 1: API Key (Best)**
```python
config = {
    'url': 'https://elastic:9200',
    'api_key': 'base64_key'
}
```

**Option 2: Username/Password**
```python
config = {
    'url': 'https://elastic:9200',
    'username': 'elastic',
    'password': 'your_password'
}
```

**Option 3: Elastic Cloud**
```python
config = {
    'cloud_id': 'deployment:base64string',
    'api_key': 'base64_key'
}
```

### What Gets Harvested

| Type | API Endpoint | Count |
|------|--------------|-------|
| Detection Rules | `/api/detection_engine/rules/_find` | All enabled/disabled |
| Saved Searches | `/api/saved_objects/_find?type=search` | All user searches |
| Watcher Alerts | `/_watcher/watch` | All watches |

### Common Issues

**Can't Connect**
```bash
# Test directly
curl -k https://your-elastic:9200
```

**403 Forbidden**
- Need Security Admin or Kibana Admin role
- API key needs read permissions

**No Rules Found**
- Elastic Security must be installed
- Check rules exist in UI: Security → Rules

## 🔗 Integration

### Add HTTP Endpoint to Ingestion Engine

```python
# In ingestion_engine.py
from elastic_harvester_integration import ElasticHarvesterIntegration

async def _setup_http_routes(self, app):
    app.router.add_post('/harvest/elastic', self._harvest_elastic)

async def _harvest_elastic(self, request):
    config = await request.json()
    result = await self.elastic_harvester.start_harvest(config)
    return web.json_response(result)
```

### Trigger via API

```bash
curl -X POST http://localhost:8003/harvest/elastic \
  -H "Content-Type: application/json" \
  -d '{"url":"https://elastic:9200","api_key":"..."}'
```

## 📊 Output Format

```json
{
  "harvest_time": "2025-01-15T10:30:45Z",
  "summary": {
    "detection_rules_count": 127,
    "saved_searches_count": 43,
    "total_artifacts": 170
  },
  "detection_rules": [
    {
      "rule_id": "abc-123",
      "name": "Suspicious PowerShell",
      "query": "process.name: powershell.exe AND ...",
      "severity": "high",
      "mitre_techniques": ["T1059.001"]
    }
  ]
}
```

## 🎯 Next Steps

1. **Test**: Run `python test_elastic_harvester.py`
2. **Review**: Check `elastic_harvest_results.json`
3. **Integrate**: Add to ingestion engine
4. **Enhance**: Use Intelligence Engine to improve rules
5. **Deploy**: Push to other SIEMs via Delivery Engine

## 📚 Full Docs

- `ELASTIC_RULE_HARVESTING_GUIDE.md` - Complete guide
- `ELASTIC_HARVESTER_IMPLEMENTATION_SUMMARY.md` - Technical details
- `engines/ingestion/elastic_rule_harvester.py` - Source code

---

**Ready to harvest your Elastic rules!** 🎉
