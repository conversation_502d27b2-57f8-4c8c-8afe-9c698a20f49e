"""
Apache AGE Graph Service for SIEMLess v2.0
Provides graph query capabilities for entity/relationship visualization
"""

import asyncpg
from typing import Dict, List, Any, Optional
import json
import logging


class AGEGraphService:
    """
    Service layer for Apache AGE graph queries

    This wraps complex Cypher queries for common investigation patterns:
    - Entity exploration (find all connected entities)
    - Path finding (shortest path between entities)
    - Pattern detection (attack chains, lateral movement)
    - Community detection (clusters of related entities)
    """

    def __init__(self, db_pool: asyncpg.Pool, logger: logging.Logger):
        self.db_pool = db_pool
        self.logger = logger

    async def _execute_cypher(self, query: str, params: Dict[str, Any] = None) -> List[Dict]:
        """Execute Cypher query and return results as list of dicts"""
        async with self.db_pool.acquire() as conn:
            try:
                # Load AGE extension
                await conn.execute("LOAD 'age';")
                await conn.execute("SET search_path = ag_catalog, '$user', public;")

                # Execute Cypher query
                results = await conn.fetch(query)

                # Convert to list of dicts
                result_list = []

                for row in results:
                    result_dict = {}
                    for col in row.keys():
                        # Parse AGE agtype format to Python objects
                        value = row[col]
                        if value:
                            # Try to parse as JSON if it's agtype
                            try:
                                result_dict[col] = json.loads(str(value))
                            except:
                                result_dict[col] = str(value)
                        else:
                            result_dict[col] = None
                    result_list.append(result_dict)

                return result_list

            except Exception as e:
                self.logger.error(f"Cypher query error: {e}")
                raise

    async def explore_entity(self, entity_id: str, depth: int = 2) -> Dict[str, Any]:
        """
        Explore all entities connected to a given entity within N hops

        Args:
            entity_id: UUID of the entity to explore
            depth: Maximum number of hops (default: 2)

        Returns:
            Dict with nodes and edges for graph visualization
        """
        query = f"""
        SELECT * FROM cypher('entity_graph', $$
            MATCH path = (start {{id: '{entity_id}'}})-[*1..{depth}]-(connected)
            RETURN DISTINCT
                nodes(path) as nodes,
                relationships(path) as relationships
        $$) as (nodes agtype, relationships agtype);
        """

        results = await self._execute_cypher(query)

        # Convert to D3.js format
        nodes_set = set()
        edges_list = []

        for result in results:
            # Parse nodes
            if result.get('nodes'):
                for node in result['nodes']:
                    if isinstance(node, dict):
                        nodes_set.add(json.dumps(node, sort_keys=True))

            # Parse relationships
            if result.get('relationships'):
                for rel in result['relationships']:
                    if isinstance(rel, dict):
                        edges_list.append(rel)

        # Convert nodes set to list of dicts
        nodes_list = [json.loads(n) for n in nodes_set]

        return {
            'nodes': nodes_list,
            'edges': edges_list,
            'center_entity': entity_id,
            'depth': depth
        }

    async def find_path(self, source_id: str, target_id: str, max_hops: int = 10) -> Dict[str, Any]:
        """
        Find shortest path between two entities

        Args:
            source_id: Source entity UUID
            target_id: Target entity UUID
            max_hops: Maximum path length (default: 10)

        Returns:
            Dict with path information
        """
        query = f"""
        SELECT * FROM cypher('entity_graph', $$
            MATCH path = shortestPath(
                (source {{id: '{source_id}'}})-[*1..{max_hops}]-(target {{id: '{target_id}'}})
            )
            RETURN
                length(path) as hops,
                nodes(path) as path_nodes,
                relationships(path) as path_edges
        $$) as (hops agtype, path_nodes agtype, path_edges agtype);
        """

        results = await self._execute_cypher(query)

        if results:
            return {
                'found': True,
                'hops': results[0].get('hops'),
                'nodes': results[0].get('path_nodes'),
                'edges': results[0].get('path_edges')
            }
        else:
            return {
                'found': False,
                'message': f'No path found between {source_id} and {target_id}'
            }

    async def find_high_risk_connections(self, entity_id: str, risk_threshold: int = 7) -> List[Dict]:
        """
        Find high-risk entities connected to given entity

        Args:
            entity_id: Entity UUID to investigate
            risk_threshold: Minimum risk score (0-10, default: 7)

        Returns:
            List of high-risk connected entities
        """
        query = f"""
        SELECT * FROM cypher('entity_graph', $$
            MATCH (start {{id: '{entity_id}'}})-[*1..3]-(risky)
            WHERE risky.risk_score >= {risk_threshold}
            RETURN DISTINCT
                risky.id as entity_id,
                risky.value as entity_value,
                risky.type as entity_type,
                risky.risk_score as risk_score
            ORDER BY risky.risk_score DESC
            LIMIT 20
        $$) as (entity_id agtype, entity_value agtype, entity_type agtype, risk_score agtype);
        """

        return await self._execute_cypher(query)

    async def find_attack_paths(self, external_ip: str, sensitive_hosts: List[str]) -> List[Dict]:
        """
        Find potential attack paths from external IP to sensitive hosts

        Args:
            external_ip: External IP address
            sensitive_hosts: List of sensitive hostname patterns

        Returns:
            List of attack paths
        """
        # Build host pattern matching
        host_patterns = "', '".join(sensitive_hosts)

        query = f"""
        SELECT * FROM cypher('entity_graph', $$
            MATCH path = (external:Ip {{value: '{external_ip}'}})-[*1..8]-(sensitive:Host)
            WHERE sensitive.value IN ['{host_patterns}']
            RETURN
                sensitive.value as target_host,
                length(path) as hops,
                nodes(path) as attack_chain
            ORDER BY length(path)
            LIMIT 10
        $$) as (target_host agtype, hops agtype, attack_chain agtype);
        """

        return await self._execute_cypher(query)

    async def find_lateral_movement(self, user_id: str, time_window_minutes: int = 60) -> List[Dict]:
        """
        Detect lateral movement: user accessing multiple hosts in short time

        Args:
            user_id: User entity UUID
            time_window_minutes: Time window for detection (default: 60 min)

        Returns:
            List of accessed hosts
        """
        query = f"""
        SELECT * FROM cypher('entity_graph', $$
            MATCH (user:User {{id: '{user_id}'}})-[access:ACCESSED]->(host:Host)
            RETURN
                host.value as hostname,
                count(access) as access_count,
                max(access.last_seen) as last_access
            ORDER BY access_count DESC
        $$) as (hostname agtype, access_count agtype, last_access agtype);
        """

        return await self._execute_cypher(query)

    async def find_communication_clusters(self, min_cluster_size: int = 5) -> List[Dict]:
        """
        Find clusters of entities that communicate frequently (community detection)

        Args:
            min_cluster_size: Minimum number of entities in cluster

        Returns:
            List of entity clusters
        """
        query = f"""
        SELECT * FROM cypher('entity_graph', $$
            MATCH (a)-[r]->(b)
            WITH a, b, count(r) as connection_strength
            WHERE connection_strength >= {min_cluster_size}
            RETURN
                a.value as entity_a,
                b.value as entity_b,
                connection_strength
            ORDER BY connection_strength DESC
            LIMIT 50
        $$) as (entity_a agtype, entity_b agtype, connection_strength agtype);
        """

        return await self._execute_cypher(query)

    async def get_entity_centrality(self, limit: int = 20) -> List[Dict]:
        """
        Find most connected entities (highest degree centrality)
        Useful for identifying critical infrastructure or compromised pivot points

        Args:
            limit: Maximum number of results

        Returns:
            List of entities sorted by connection count
        """
        query = f"""
        SELECT * FROM cypher('entity_graph', $$
            MATCH (entity)-[r]-()
            RETURN
                entity.id as entity_id,
                entity.value as entity_value,
                entity.type as entity_type,
                count(r) as connection_count
            ORDER BY connection_count DESC
            LIMIT {limit}
        $$) as (entity_id agtype, entity_value agtype, entity_type agtype, connection_count agtype);
        """

        return await self._execute_cypher(query)

    async def get_graph_stats(self) -> Dict[str, int]:
        """Get overall graph statistics"""
        query = """
        SELECT * FROM cypher('entity_graph', $$
            MATCH (n)
            RETURN count(n) as node_count
        $$) as (node_count agtype);
        """

        node_result = await self._execute_cypher(query)

        query = """
        SELECT * FROM cypher('entity_graph', $$
            MATCH ()-[r]->()
            RETURN count(r) as edge_count
        $$) as (edge_count agtype);
        """

        edge_result = await self._execute_cypher(query)

        return {
            'nodes': node_result[0]['node_count'] if node_result else 0,
            'edges': edge_result[0]['edge_count'] if edge_result else 0
        }


# ============================================
# EXAMPLE USAGE IN BACKEND ENGINE
# ============================================

async def example_integration(db_pool: asyncpg.Pool):
    """
    Example of how to use AGEGraphService in backend_engine.py
    """
    logger = logging.getLogger('age_graph')
    graph_service = AGEGraphService(db_pool, logger)

    # Example 1: Explore entity from alert
    alert_ip_id = 'uuid-of-suspicious-ip'
    graph_data = await graph_service.explore_entity(alert_ip_id, depth=3)
    # Returns: {'nodes': [...], 'edges': [...], 'center_entity': '...'}

    # Example 2: Find attack path
    external_ip = '************'
    dc_host = 'DC01.corp.local'
    attack_path = await graph_service.find_attack_paths(external_ip, [dc_host])

    # Example 3: Detect lateral movement
    suspicious_user_id = 'uuid-of-user'
    lateral_movement = await graph_service.find_lateral_movement(suspicious_user_id)

    # Example 4: Get most connected entities
    critical_entities = await graph_service.get_entity_centrality(limit=10)

    return {
        'graph_visualization': graph_data,
        'attack_paths': attack_path,
        'lateral_movement': lateral_movement,
        'critical_entities': critical_entities
    }
