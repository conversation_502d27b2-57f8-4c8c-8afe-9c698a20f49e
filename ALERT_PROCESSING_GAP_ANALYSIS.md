# Alert Processing Gap Analysis

## Date: October 3, 2025

## Problem Statement
Rules are created from CTI and deployed to SIEMs, but when alerts fire, they are NOT being processed through Contextualization and Correlation engines.

---

## Current Flow (As Implemented)

### Phase 1: Rule Creation
```
CTI Indicators (OTX/ThreatFox/CrowdStrike)
  ↓
Backend Engine: Generate Sigma Rules
  ↓
Store in detection_rules table (DISABLED)
  ↓
Delivery Engine: User manages rules (list/update/delete)
  ↓
Ingestion Engine: Deploy to SIEM (user-triggered)
  ↓
Rule stored in SIEM (DISABLED by default)
```

### Phase 2: Alert Generation (When Rule Fires)
```
SIEM: Rule matches event
  ↓
SIEM: Generate alert
  ↓
SIEM: Send webhook to port 9000
  ↓
SIEMAlertListener: Receive webhook
  ↓
SIEMAlertListener: Normalize alert
  ↓
Publish to 'ingestion.alerts.received'
  ↓
Delivery Engine: _handle_alert_for_investigation()
  ↓
Investigation Engine: create_investigation_from_alert()
  ↓
**GAP: <PERSON><PERSON> stops here**
```

### What's Missing
```
❌ Alert → Contextualization Engine (entity extraction + enrichment)
❌ Alert → Correlation Engine (check related events)
❌ Contextualization → Investigation (add enriched context)
❌ Correlation → Investigation (add correlated events)
❌ Investigation → Rules (link back to detection rule metadata)
```

---

## What SHOULD Happen

### Complete Alert Processing Flow

```
1. Alert Received (SIEM → Ingestion)
   ↓
   SIEMAlertListener receives webhook
   Normalizes to standard Alert format
   Publishes to 'ingestion.alerts.received'

2. Alert → Contextualization (NEW - MISSING)
   ↓
   Delivery subscribes to 'ingestion.alerts.received'
   Publishes to 'contextualization.enrich_alert'
   Contextualization Engine:
     - Extracts entities from alert
     - Enriches entities (GeoIP, CTI, Asset info)
     - Creates relationships
     - Publishes to 'contextualization.alert.enriched'

3. Alert → Correlation (NEW - MISSING)
   ↓
   Backend subscribes to 'ingestion.alerts.received'
   Publishes to 'backend.correlate_alert'
   Correlation Engine:
     - Searches for related events (±30 minutes)
     - Links events by entities (IP, user, host)
     - Maps to MITRE ATT&CK chain
     - Publishes to 'backend.correlation.complete'

4. Investigation Creation
   ↓
   Delivery Engine:
     - Waits for enrichment + correlation
     - Creates investigation with ALL context
     - Links to original rule in detection_rules table
     - Includes:
       * Enriched entities
       * Correlated events
       * MITRE techniques
       * Rule metadata (IoC source, quality score)
       * Historical context

5. Investigation Display
   ↓
   Frontend shows:
     - Alert details
     - ALL enriched entities
     - Related events timeline
     - MITRE ATT&CK mapping
     - Original detection rule info
     - Recommended response actions
```

---

## Current Code Analysis

### Files Involved

1. **Alert Reception**: `engines/ingestion/siem_alert_listener.py`
   - ✅ Webhook receivers working
   - ✅ Alert normalization working
   - ✅ Publishing to 'ingestion.alerts.received'
   - ❌ NOT sending to Contextualization
   - ❌ NOT sending to Correlation

2. **Alert Handling**: `engines/delivery/delivery_engine.py`
   - ✅ Subscribes to 'ingestion.alerts.received'
   - ✅ Creates investigation for high/critical
   - ❌ Does NOT trigger enrichment
   - ❌ Does NOT trigger correlation

3. **Investigation Engine**: `engines/delivery/investigation_engine.py`
   - ✅ Creates investigation object
   - ❌ Does NOT call Contextualization
   - ❌ Does NOT call Correlation
   - ❌ Does NOT link to detection_rules table

4. **Contextualization Engine**: `engines/contextualization/contextualization_engine.py`
   - ✅ Has entity extraction capability
   - ✅ Has enrichment capability
   - ❌ NOT subscribed to alert channel
   - ❌ No alert processing handler

5. **Correlation Engine**: `engines/backend/correlation_engine.py`
   - ✅ Has correlation logic
   - ❌ NOT subscribed to alert channel
   - ❌ No alert processing handler

---

## Required Changes

### Priority 1: Connect Alerts to Contextualization

**File**: `engines/delivery/delivery_engine.py`

**Current** (line 877):
```python
async def _handle_alert_for_investigation(self, data: Dict[str, Any]):
    """Handle alerts from ingestion for auto-investigation"""
    severity = data.get('severity', '').lower()
    if severity in ['high', 'critical']:
        investigation = await self.investigation_engine.create_investigation_from_alert(data)
```

**Should Be**:
```python
async def _handle_alert_for_investigation(self, data: Dict[str, Any]):
    """Handle alerts from ingestion for auto-investigation"""
    # Step 1: Send to Contextualization for entity enrichment
    self.publish_message('contextualization.enrich_alert', {
        'alert_id': data.get('alert_id'),
        'entities': data.get('entities', {}),
        'request_id': f"alert_enrich_{data.get('alert_id')}"
    })

    # Step 2: Send to Correlation for related events
    self.publish_message('backend.correlate_alert', {
        'alert_id': data.get('alert_id'),
        'entities': data.get('entities', {}),
        'timestamp': data.get('timestamp'),
        'mitre_techniques': data.get('mitre_techniques', [])
    })

    # Step 3: Create investigation (will be enriched async)
    severity = data.get('severity', '').lower()
    if severity in ['high', 'critical']:
        investigation = await self.investigation_engine.create_investigation_from_alert(data)
```

### Priority 2: Add Alert Handler to Contextualization Engine

**File**: `engines/contextualization/contextualization_engine.py`

**Add to subscription channels** (line ~40):
```python
subscription_channels = [
    'contextualization.process_log',
    'contextualization.extract_entities',
    'contextualization.enrich_log',
    'contextualization.enrich_entity',
    'contextualization.find_relationships',
    'contextualization.get_context',
    'contextualization.validate_parser',
    'contextualization.extract_from_context',
    'cti.enrichment.iocs',
    'contextualization.enrich_alert'  # NEW
]
```

**Add handler** (new method):
```python
async def _handle_enrich_alert(self, data: Dict[str, Any]):
    """Enrich alert entities with contextual information"""
    try:
        alert_id = data.get('alert_id')
        entities = data.get('entities', {})
        request_id = data.get('request_id')

        enriched_entities = {}

        # Enrich each entity
        for entity_type, entity_values in entities.items():
            if not isinstance(entity_values, list):
                entity_values = [entity_values]

            for entity_value in entity_values:
                # Extract and enrich
                enrichment = await self.enrichment_service.enrich_entity(
                    entity_type, entity_value
                )

                if entity_type not in enriched_entities:
                    enriched_entities[entity_type] = []
                enriched_entities[entity_type].append({
                    'value': entity_value,
                    'enrichment': enrichment
                })

        # Publish enriched results
        self.publish_message(f'contextualization.alert.enriched.{request_id}', {
            'alert_id': alert_id,
            'enriched_entities': enriched_entities,
            'timestamp': datetime.utcnow().isoformat()
        })

    except Exception as e:
        self.logger.error(f"Alert enrichment failed: {e}")
```

### Priority 3: Add Alert Handler to Correlation Engine

**File**: `engines/backend/correlation_engine.py`

**Add subscription**:
```python
# Subscribe to alert correlation requests
await pubsub.subscribe('backend.correlate_alert')
```

**Add handler**:
```python
async def _handle_correlate_alert(self, data: Dict[str, Any]):
    """Find related events for an alert"""
    try:
        alert_id = data.get('alert_id')
        entities = data.get('entities', {})
        alert_time = datetime.fromisoformat(data.get('timestamp'))

        # Search window: ±30 minutes
        time_window = timedelta(minutes=30)
        start_time = alert_time - time_window
        end_time = alert_time + time_window

        related_events = []

        # Search by each entity
        for entity_type, entity_values in entities.items():
            # Query database for related events
            events = self._query_related_events(
                entity_type, entity_values,
                start_time, end_time
            )
            related_events.extend(events)

        # Build correlation graph
        correlation_chain = self._build_mitre_chain(related_events)

        # Publish results
        self.publish_message('backend.correlation.complete', {
            'alert_id': alert_id,
            'related_events': related_events,
            'mitre_chain': correlation_chain,
            'correlation_score': self._calculate_correlation_score(related_events)
        })

    except Exception as e:
        self.logger.error(f"Alert correlation failed: {e}")
```

### Priority 4: Link Investigation to Detection Rule

**File**: `engines/delivery/investigation_engine.py`

**Add to investigation creation**:
```python
async def create_investigation_from_alert(self, alert_data: Dict) -> Investigation:
    """Create investigation with rule context"""
    investigation_id = str(uuid.uuid4())

    # NEW: Fetch rule metadata from detection_rules table
    rule_metadata = await self._fetch_rule_metadata(alert_data.get('rule_id'))

    investigation = Investigation(
        id=investigation_id,
        title=f"Investigation: {alert_data.get('title')}",
        severity=alert_data.get('severity'),
        created_at=datetime.utcnow(),
        alert_id=alert_data.get('alert_id'),
        entities=alert_data.get('entities', {}),
        mitre_techniques=alert_data.get('mitre_techniques', []),
        rule_metadata=rule_metadata  # NEW: Include rule context
    )

    # Wait for enrichment and correlation (async)
    enrichment_request_id = f"alert_enrich_{alert_data.get('alert_id')}"

    # Subscribe to results
    await self._subscribe_to_enrichment(investigation_id, enrichment_request_id)
    await self._subscribe_to_correlation(investigation_id, alert_data.get('alert_id'))

    return investigation

async def _fetch_rule_metadata(self, rule_id: str) -> Dict:
    """Fetch rule details from detection_rules table"""
    if not rule_id:
        return {}

    cursor = self.db_connection.cursor()
    cursor.execute("""
        SELECT rule_data FROM detection_rules WHERE rule_id = %s
    """, (rule_id,))
    row = cursor.fetchone()
    cursor.close()

    if row:
        return row[0]  # JSONB rule_data
    return {}
```

---

## Implementation Plan

### Phase 1: Basic Integration (High Priority)
1. ✅ Identify gaps (this document)
2. ⏳ Add `contextualization.enrich_alert` handler to Contextualization Engine
3. ⏳ Add `backend.correlate_alert` handler to Correlation Engine
4. ⏳ Update Delivery Engine to publish to both engines
5. ⏳ Test with sample alert

### Phase 2: Investigation Enhancement
1. ⏳ Link investigations to detection_rules table
2. ⏳ Subscribe to enrichment/correlation results
3. ⏳ Update investigation with enriched data
4. ⏳ Display complete context in frontend

### Phase 3: Testing
1. ⏳ Deploy test rule to Elastic
2. ⏳ Trigger alert (manually inject event)
3. ⏳ Verify enrichment flow
4. ⏳ Verify correlation flow
5. ⏳ Verify investigation includes all context

---

## Expected Outcome

After implementation, when a rule fires:

1. **Alert received** from SIEM
2. **Entities extracted** and enriched (GeoIP, CTI, asset info)
3. **Related events** found via correlation
4. **MITRE chain** constructed
5. **Investigation created** with:
   - Original alert
   - Enriched entities
   - Related events timeline
   - MITRE ATT&CK mapping
   - Detection rule context (IoC source, quality score)
   - Recommended actions
6. **Analyst sees complete picture** in one view

---

## Benefits

1. **Complete Context**: Every alert has full enrichment
2. **Correlation Visibility**: See related events automatically
3. **MITRE Mapping**: Understand attack chain
4. **Rule Traceability**: Know which CTI source triggered detection
5. **Faster Triage**: All information in one place
6. **Quality Feedback**: Link alert outcomes back to rule quality

---

## Files to Modify

1. `engines/delivery/delivery_engine.py` - Publish to enrichment/correlation
2. `engines/contextualization/contextualization_engine.py` - Add alert handler
3. `engines/backend/correlation_engine.py` - Add alert handler
4. `engines/delivery/investigation_engine.py` - Link to rules, subscribe to results

---

## Next Steps

**Decision needed**: Should we implement this now or document for future phase?

**Recommendation**: Implement Phase 1 (basic integration) now because:
- Rules are already being deployed
- Alerts are being received
- But they're not getting the full SIEMLess intelligence treatment
- This is a core value proposition

**Estimated effort**: 2-4 hours for Phase 1
