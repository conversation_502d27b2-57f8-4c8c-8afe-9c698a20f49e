import apiClient from '../client'
import {
  DetectionRule,
  PendingRule,
  RulePerformance,
  RuleTestResult,
  PaginatedResponse,
  CreateRuleRequest,
  UpdateRuleRequest,
  RuleTestRequest,
  RuleImportRequest,
  RuleFolder,
  RulePerformanceSummary
} from '../types/api'

/**
 * Rule Service
 *
 * Handles all rule management operations:
 * - CTI-generated rules (pending approval)
 * - Rule CRUD operations
 * - Rule testing and validation
 * - Performance tracking
 * - Organization (folders, tags)
 */

// ============================================================================
// CTI-Generated Rules (Pending Approval)
// ============================================================================

/**
 * Get all pending CTI-generated rules awaiting approval
 */
export const getPendingRules = async (filters?: {
  cti_source?: string
  quality_min?: number
  indicator_type?: string
}): Promise<PaginatedResponse<PendingRule>> => {
  const response = await apiClient.get('/rules/pending', { params: filters })
  return response.data
}

/**
 * Get a specific pending rule by ID
 */
export const getPendingRule = async (pendingId: string): Promise<PendingRule> => {
  const response = await apiClient.get(`/api/rules/pending/${pendingId}`)
  return response.data
}

/**
 * Approve a pending CTI-generated rule (moves to active rules)
 */
export const approvePendingRule = async (
  pendingId: string,
  edits?: {
    rule_name?: string
    description?: string
    severity?: string
    folder?: string
    tags?: string[]
  }
): Promise<DetectionRule> => {
  const response = await apiClient.post(`/api/rules/pending/${pendingId}/approve`, edits)
  return response.data
}

/**
 * Reject a pending CTI-generated rule
 */
export const rejectPendingRule = async (
  pendingId: string,
  reason?: string
): Promise<{ success: boolean }> => {
  const response = await apiClient.post(`/api/rules/pending/${pendingId}/reject`, { reason })
  return response.data
}

/**
 * Edit a pending rule before approval
 */
export const updatePendingRule = async (
  pendingId: string,
  updates: {
    sigma_rule?: string
    description?: string
    severity?: string
    test_cases?: any[]
  }
): Promise<PendingRule> => {
  const response = await apiClient.put(`/api/rules/pending/${pendingId}`, updates)
  return response.data
}

/**
 * Preview pending rule in target SIEM format
 */
export const previewRuleFormat = async (
  pendingId: string,
  targetFormat: 'splunk' | 'elastic' | 'sentinel' | 'qradar'
): Promise<{ translated_rule: string; syntax_valid: boolean }> => {
  const response = await apiClient.get(`/api/rules/pending/${pendingId}/preview`, {
    params: { format: targetFormat }
  })
  return response.data
}

// ============================================================================
// Rule CRUD Operations
// ============================================================================

/**
 * Get all detection rules with optional filters
 */
export const getRules = async (filters?: {
  status?: string
  rule_type?: string
  folder?: string
  tags?: string[]
  severity?: string
  mitre_tactic?: string
  mitre_technique?: string
  search?: string
  page?: number
  page_size?: number
}): Promise<PaginatedResponse<DetectionRule>> => {
  const response = await apiClient.get('/rules', { params: filters })
  return response.data
}

/**
 * Get a specific rule by ID
 */
export const getRule = async (ruleId: string): Promise<DetectionRule> => {
  const response = await apiClient.get(`/api/rules/${ruleId}`)
  return response.data
}

/**
 * Create a new detection rule manually
 */
export const createRule = async (rule: CreateRuleRequest): Promise<DetectionRule> => {
  const response = await apiClient.post('/rules', rule)
  return response.data
}

/**
 * Update an existing detection rule
 */
export const updateRule = async (
  ruleId: string,
  updates: UpdateRuleRequest
): Promise<DetectionRule> => {
  const response = await apiClient.put(`/api/rules/${ruleId}`, updates)
  return response.data
}

/**
 * Delete a detection rule
 */
export const deleteRule = async (ruleId: string): Promise<{ success: boolean }> => {
  const response = await apiClient.delete(`/api/rules/${ruleId}`)
  return response.data
}

/**
 * Import rules in batch (from file upload or external source)
 */
export const importRules = async (
  importRequest: RuleImportRequest
): Promise<{ imported: number; failed: number; rules: DetectionRule[] }> => {
  const response = await apiClient.post('/rules/import', importRequest)
  return response.data
}

/**
 * Export rules (download as Sigma YAML, JSON, etc.)
 */
export const exportRules = async (
  ruleIds: string[],
  format: 'sigma' | 'json' | 'yaml'
): Promise<Blob> => {
  const response = await apiClient.post(
    '/rules/export',
    { rule_ids: ruleIds, format },
    { responseType: 'blob' }
  )
  return response.data
}

// ============================================================================
// Rule Testing & Validation
// ============================================================================

/**
 * Test a rule against sample data
 */
export const testRule = async (
  ruleId: string,
  testRequest: RuleTestRequest
): Promise<RuleTestResult> => {
  const response = await apiClient.post(`/api/rules/${ruleId}/test`, testRequest)
  return response.data
}

/**
 * Get historical test results for a rule
 */
export const getRuleTestResults = async (
  ruleId: string,
  limit?: number
): Promise<RuleTestResult[]> => {
  const response = await apiClient.get(`/api/rules/${ruleId}/test-results`, {
    params: { limit }
  })
  return response.data
}

/**
 * Validate rule syntax without executing
 */
export const validateRule = async (
  ruleId: string
): Promise<{ valid: boolean; errors?: string[] }> => {
  const response = await apiClient.post(`/api/rules/${ruleId}/validate`)
  return response.data
}

/**
 * Validate rule content directly (not yet saved)
 */
export const validateRuleContent = async (
  ruleContent: string,
  ruleType: string
): Promise<{ valid: boolean; errors?: string[] }> => {
  const response = await apiClient.post('/rules/validate', {
    rule_content: ruleContent,
    rule_type: ruleType
  })
  return response.data
}

// ============================================================================
// Rule Performance Tracking
// ============================================================================

/**
 * Get performance metrics for a specific rule
 */
export const getRulePerformance = async (
  ruleId: string,
  period?: '24h' | '7d' | '30d' | '90d'
): Promise<RulePerformance> => {
  const response = await apiClient.get(`/api/rules/${ruleId}/performance`, {
    params: { period }
  })
  return response.data
}

/**
 * Get all alerts triggered by a specific rule
 */
export const getRuleAlerts = async (
  ruleId: string,
  filters?: {
    status?: string
    severity?: string
    start_date?: string
    end_date?: string
  }
): Promise<any[]> => {
  const response = await apiClient.get(`/api/rules/${ruleId}/alerts`, { params: filters })
  return response.data
}

/**
 * Get performance summary for all rules (dashboard data)
 */
export const getRulePerformanceSummary = async (
  period?: '24h' | '7d' | '30d' | '90d'
): Promise<RulePerformanceSummary> => {
  const response = await apiClient.get('/rules/performance/summary', {
    params: { period }
  })
  return response.data
}

/**
 * Update rule performance metrics (mark alert as TP/FP)
 */
export const updateRulePerformance = async (
  ruleId: string,
  alertId: string,
  verdict: 'true_positive' | 'false_positive' | 'false_negative'
): Promise<RulePerformance> => {
  const response = await apiClient.post(`/api/rules/${ruleId}/performance/update`, {
    alert_id: alertId,
    verdict
  })
  return response.data
}

// ============================================================================
// Rule Organization (Folders, Tags)
// ============================================================================

/**
 * Get all rule folders
 */
export const getRuleFolders = async (): Promise<RuleFolder[]> => {
  const response = await apiClient.get('/rules/folders')
  return response.data
}

/**
 * Create a new folder
 */
export const createRuleFolder = async (
  name: string,
  description?: string,
  parent_folder?: string
): Promise<RuleFolder> => {
  const response = await apiClient.post('/rules/folders', {
    name,
    description,
    parent_folder
  })
  return response.data
}

/**
 * Update folder details
 */
export const updateRuleFolder = async (
  folderId: string,
  updates: { name?: string; description?: string }
): Promise<RuleFolder> => {
  const response = await apiClient.put(`/api/rules/folders/${folderId}`, updates)
  return response.data
}

/**
 * Delete a folder (moves rules to root)
 */
export const deleteRuleFolder = async (folderId: string): Promise<{ success: boolean }> => {
  const response = await apiClient.delete(`/api/rules/folders/${folderId}`)
  return response.data
}

/**
 * Get all unique tags used across all rules
 */
export const getRuleTags = async (): Promise<string[]> => {
  const response = await apiClient.get('/rules/tags')
  return response.data
}

/**
 * Add tags to a rule
 */
export const addRuleTags = async (
  ruleId: string,
  tags: string[]
): Promise<DetectionRule> => {
  const response = await apiClient.post(`/api/rules/${ruleId}/tags`, { tags })
  return response.data
}

/**
 * Remove tags from a rule
 */
export const removeRuleTags = async (
  ruleId: string,
  tags: string[]
): Promise<DetectionRule> => {
  const response = await apiClient.delete(`/api/rules/${ruleId}/tags`, {
    data: { tags }
  })
  return response.data
}

// ============================================================================
// Bulk Operations
// ============================================================================

/**
 * Bulk approve multiple pending rules
 */
export const bulkApprovePendingRules = async (
  pendingIds: string[]
): Promise<{ approved: number; failed: number }> => {
  const response = await apiClient.post('/rules/pending/bulk-approve', {
    pending_ids: pendingIds
  })
  return response.data
}

/**
 * Bulk enable/disable rules
 */
export const bulkUpdateRuleStatus = async (
  ruleIds: string[],
  enabled: boolean
): Promise<{ updated: number }> => {
  const response = await apiClient.post('/rules/bulk-update', {
    rule_ids: ruleIds,
    enabled
  })
  return response.data
}

/**
 * Bulk move rules to folder
 */
export const bulkMoveRules = async (
  ruleIds: string[],
  folderId: string
): Promise<{ moved: number }> => {
  const response = await apiClient.post('/rules/bulk-move', {
    rule_ids: ruleIds,
    folder_id: folderId
  })
  return response.data
}

/**
 * Bulk delete rules
 */
export const bulkDeleteRules = async (
  ruleIds: string[]
): Promise<{ deleted: number }> => {
  const response = await apiClient.post('/rules/bulk-delete', {
    rule_ids: ruleIds
  })
  return response.data
}

// Export all functions as named exports
export default {
  // Pending rules
  getPendingRules,
  getPendingRule,
  approvePendingRule,
  rejectPendingRule,
  updatePendingRule,
  previewRuleFormat,

  // Rule CRUD
  getRules,
  getRule,
  createRule,
  updateRule,
  deleteRule,
  importRules,
  exportRules,

  // Testing
  testRule,
  getRuleTestResults,
  validateRule,
  validateRuleContent,

  // Performance
  getRulePerformance,
  getRuleAlerts,
  getRulePerformanceSummary,
  updateRulePerformance,

  // Organization
  getRuleFolders,
  createRuleFolder,
  updateRuleFolder,
  deleteRuleFolder,
  getRuleTags,
  addRuleTags,
  removeRuleTags,

  // Bulk operations
  bulkApprovePendingRules,
  bulkUpdateRuleStatus,
  bulkMoveRules,
  bulkDeleteRules
}
