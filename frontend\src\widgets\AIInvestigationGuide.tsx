import React, { useState, useEffect } from 'react'
import { useInvestigationStore } from '../stores/investigationStore'
import {
  Brain, CheckSquare, Square, ChevronRight, ChevronDown,
  Copy, ExternalLink, AlertTriangle, Info, Zap,
  Clock, Target, FileText, Code, Loader
} from 'lucide-react'

interface InvestigationStep {
  id: string
  title: string
  description: string
  queries: Record<string, string> // SIEM -> query mapping
  evidence_required: string[]
  risk_indicators: string[]
  escalation_criteria?: string[]
  estimated_time: number // in minutes
  completed: boolean
  notes?: string
}

interface AIInvestigationGuideProps {
  caseId: string
  onStepComplete?: (stepId: string) => void
  onEscalate?: () => void
}

export const AIInvestigationGuide: React.FC<AIInvestigationGuideProps> = ({
  caseId,
  onStepComplete,
  onEscalate
}) => {
  const {
    investigationGuide,
    loadingGuide,
    loadInvestigationGuide,
    updateGuideStep
  } = useInvestigationStore()

  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set())
  const [selectedSiem, setSelectedSiem] = useState<string>('splunk')
  const [stepNotes, setStepNotes] = useState<Record<string, string>>({})
  const [showEscalation, setShowEscalation] = useState(false)

  useEffect(() => {
    if (caseId) {
      loadInvestigationGuide(caseId)
    }
  }, [caseId, loadInvestigationGuide])

  const toggleStep = (stepId: string) => {
    setExpandedSteps(prev => {
      const newSet = new Set(prev)
      if (newSet.has(stepId)) {
        newSet.delete(stepId)
      } else {
        newSet.add(stepId)
      }
      return newSet
    })
  }

  const handleStepComplete = (stepId: string, completed: boolean) => {
    updateGuideStep(stepId, completed)
    if (completed && onStepComplete) {
      onStepComplete(stepId)
    }
  }

  const copyQuery = (query: string) => {
    navigator.clipboard.writeText(query)
    // Show toast notification
  }

  const openInSiem = (query: string) => {
    // This would open the SIEM in a new window with the query
    const siemUrls: Record<string, string> = {
      splunk: `https://splunk.company.com/search?q=${encodeURIComponent(query)}`,
      elastic: `https://elastic.company.com/_search?q=${encodeURIComponent(query)}`,
      sentinel: `https://sentinel.azure.com/query?q=${encodeURIComponent(query)}`,
      qradar: `https://qradar.company.com/console?search=${encodeURIComponent(query)}`
    }

    window.open(siemUrls[selectedSiem], '_blank')
  }

  const calculateProgress = () => {
    if (!investigationGuide?.steps) return 0
    const completed = investigationGuide.steps.filter(s => s.completed).length
    return (completed / investigationGuide.steps.length) * 100
  }

  const estimateTotalTime = () => {
    if (!investigationGuide?.steps) return 0
    return investigationGuide.steps.reduce((sum, step) => sum + (step.estimated_time || 5), 0)
  }

  const estimateRemainingTime = () => {
    if (!investigationGuide?.steps) return 0
    return investigationGuide.steps
      .filter(s => !s.completed)
      .reduce((sum, step) => sum + (step.estimated_time || 5), 0)
  }

  if (loadingGuide) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader className="animate-spin text-blue-500" size={48} />
        <span className="ml-4 text-gray-600">Generating investigation guide...</span>
      </div>
    )
  }

  if (!investigationGuide) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        <div className="text-center">
          <Brain size={48} className="mx-auto mb-4 text-gray-400" />
          <p>No investigation guide available</p>
          <p className="text-sm mt-2">Select a case to generate AI guidance</p>
        </div>
      </div>
    )
  }

  const progress = calculateProgress()

  // Mock steps for demonstration
  const steps: InvestigationStep[] = investigationGuide?.steps || [
    {
      id: '1',
      title: 'Initial Triage',
      description: 'Verify the alert is legitimate and gather initial context',
      queries: {
        splunk: 'index=security source_ip="*************" earliest=-1h',
        elastic: 'source.ip:"*************" AND @timestamp:[now-1h TO now]',
        sentinel: 'SecurityEvent | where SourceIP == "*************" | where TimeGenerated > ago(1h)',
        qradar: 'SELECT * FROM events WHERE sourceip = "*************" LAST 1 HOUR'
      },
      evidence_required: ['Source IP activity', 'User authentication logs', 'Network connections'],
      risk_indicators: ['Multiple failed logins', 'Unusual time of activity', 'New source location'],
      estimated_time: 5,
      completed: false
    },
    {
      id: '2',
      title: 'User Verification',
      description: 'Confirm if the user activity is authorized and expected',
      queries: {
        splunk: 'index=auth user="admin" | stats count by action, src_ip',
        elastic: 'event.module:auth AND user.name:"admin"',
        sentinel: 'SigninLogs | where UserPrincipalName == "<EMAIL>"',
        qradar: 'SELECT username, sourceip, eventname FROM events WHERE username = "admin"'
      },
      evidence_required: ['User login history', 'Account permissions', 'Recent password changes'],
      risk_indicators: ['Account compromise indicators', 'Privilege escalation attempts'],
      escalation_criteria: ['Confirmed unauthorized access', 'Admin account compromise'],
      estimated_time: 10,
      completed: false
    },
    {
      id: '3',
      title: 'Lateral Movement Check',
      description: 'Investigate if the attacker moved to other systems',
      queries: {
        splunk: 'index=network src_ip="*************" | table dest_ip, dest_port, bytes',
        elastic: 'source.ip:"*************" AND event.category:network',
        sentinel: 'NetworkCommunication | where SourceIP == "*************"',
        qradar: 'SELECT destinationip, destinationport FROM flows WHERE sourceip = "*************"'
      },
      evidence_required: ['Network flow logs', 'System access logs', 'File access logs'],
      risk_indicators: ['Access to critical systems', 'Data exfiltration patterns'],
      estimated_time: 15,
      completed: false
    }
  ]

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-500 to-blue-500 text-white p-4">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-xl font-bold flex items-center gap-2">
              <Brain size={24} />
              AI Investigation Guide
            </h2>
            <p className="text-purple-100 text-sm mt-1">
              Generated by {investigationGuide.ai_model || 'Multi-AI Consensus'}
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">{progress.toFixed(0)}%</div>
            <div className="text-xs text-purple-100">Complete</div>
          </div>
        </div>

        {/* Progress bar */}
        <div className="mt-4 bg-white/20 rounded-full h-2">
          <div
            className="bg-white rounded-full h-2 transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>

        {/* Time estimates */}
        <div className="flex justify-between mt-3 text-sm">
          <span className="flex items-center gap-1">
            <Clock size={14} />
            Est. Total: {estimateTotalTime()} min
          </span>
          <span className="flex items-center gap-1">
            <Zap size={14} />
            Remaining: {estimateRemainingTime()} min
          </span>
        </div>
      </div>

      {/* SIEM Selector */}
      <div className="p-3 border-b bg-gray-50">
        <div className="flex items-center gap-3">
          <span className="text-sm font-medium">Query Format:</span>
          <select
            className="px-3 py-1 border rounded text-sm"
            value={selectedSiem}
            onChange={(e) => setSelectedSiem(e.target.value)}
          >
            <option value="splunk">Splunk SPL</option>
            <option value="elastic">Elasticsearch KQL</option>
            <option value="sentinel">Azure Sentinel KQL</option>
            <option value="qradar">QRadar AQL</option>
          </select>
        </div>
      </div>

      {/* Investigation Steps */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-3">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`border rounded-lg ${step.completed ? 'bg-green-50 border-green-300' : ''}`}
            >
              {/* Step Header */}
              <div
                className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50"
                onClick={() => toggleStep(step.id)}
              >
                <div className="flex items-center gap-3">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleStepComplete(step.id, !step.completed)
                    }}
                    className="text-blue-500 hover:text-blue-700"
                  >
                    {step.completed ? <CheckSquare size={20} /> : <Square size={20} />}
                  </button>
                  <div>
                    <h3 className="font-medium">
                      Step {index + 1}: {step.title}
                    </h3>
                    <p className="text-sm text-gray-600">{step.description}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-gray-500 flex items-center gap-1">
                    <Clock size={12} />
                    {step.estimated_time} min
                  </span>
                  {expandedSteps.has(step.id) ? <ChevronDown size={18} /> : <ChevronRight size={18} />}
                </div>
              </div>

              {/* Step Details */}
              {expandedSteps.has(step.id) && (
                <div className="p-4 border-t space-y-4">
                  {/* Query Section */}
                  <div>
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <Code size={14} />
                      {selectedSiem.toUpperCase()} Query
                    </h4>
                    <div className="bg-gray-900 text-gray-100 p-3 rounded font-mono text-xs">
                      <div className="flex justify-between items-start">
                        <code className="flex-1">{step.queries[selectedSiem]}</code>
                        <div className="flex gap-1 ml-2">
                          <button
                            onClick={() => copyQuery(step.queries[selectedSiem])}
                            className="p-1 hover:bg-gray-700 rounded"
                            title="Copy Query"
                          >
                            <Copy size={14} />
                          </button>
                          <button
                            onClick={() => openInSiem(step.queries[selectedSiem])}
                            className="p-1 hover:bg-gray-700 rounded"
                            title="Open in SIEM"
                          >
                            <ExternalLink size={14} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Evidence Required */}
                  <div>
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <FileText size={14} />
                      Evidence Required
                    </h4>
                    <ul className="space-y-1">
                      {step.evidence_required.map((evidence, idx) => (
                        <li key={idx} className="flex items-start gap-2 text-sm">
                          <Target size={12} className="mt-1 text-gray-400" />
                          <span>{evidence}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Risk Indicators */}
                  <div>
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <AlertTriangle size={14} className="text-orange-500" />
                      Risk Indicators to Watch
                    </h4>
                    <ul className="space-y-1">
                      {step.risk_indicators.map((indicator, idx) => (
                        <li key={idx} className="flex items-start gap-2 text-sm">
                          <Info size={12} className="mt-1 text-orange-400" />
                          <span>{indicator}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Escalation Criteria */}
                  {step.escalation_criteria && (
                    <div className="bg-red-50 border border-red-200 rounded p-3">
                      <h4 className="font-medium text-sm mb-2 text-red-700">
                        Escalation Criteria
                      </h4>
                      <ul className="space-y-1">
                        {step.escalation_criteria.map((criteria, idx) => (
                          <li key={idx} className="text-sm text-red-600">
                            • {criteria}
                          </li>
                        ))}
                      </ul>
                      {showEscalation && (
                        <button
                          onClick={() => {
                            setShowEscalation(true)
                            onEscalate?.()
                          }}
                          className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                        >
                          Escalate Case
                        </button>
                      )}
                    </div>
                  )}

                  {/* Notes Section */}
                  <div>
                    <h4 className="font-medium text-sm mb-2">Investigation Notes</h4>
                    <textarea
                      className="w-full p-2 border rounded text-sm"
                      rows={3}
                      placeholder="Add your findings and observations..."
                      value={stepNotes[step.id] || ''}
                      onChange={(e) => setStepNotes(prev => ({ ...prev, [step.id]: e.target.value }))}
                    />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Footer Actions */}
      <div className="border-t p-3 flex justify-between items-center bg-gray-50">
        <div className="text-sm text-gray-600">
          Generated: {investigationGuide.generated_at ? new Date(investigationGuide.generated_at).toLocaleString() : 'Recently'}
        </div>
        <div className="flex gap-2">
          <button className="px-4 py-2 border rounded hover:bg-white text-sm">
            Export Guide
          </button>
          <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">
            Complete Investigation
          </button>
        </div>
      </div>
    </div>
  )
}

export default AIInvestigationGuide