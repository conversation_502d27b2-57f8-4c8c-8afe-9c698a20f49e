# Minimal Prometheus configuration for SIEMLess v2
# Optimized for low resource usage

global:
  scrape_interval: 30s  # Reduced frequency to save resources
  evaluation_interval: 60s

  # External labels for federation (if needed)
  external_labels:
    monitor: 'siemless-v2'
    environment: 'production'

# Minimal storage configuration
storage:
  tsdb:
    retention.time: 7d  # Keep only 7 days of data
    retention.size: 1GB  # Max 1GB storage

# Scrape configs - only essential metrics
scrape_configs:
  # SIEMLess Engines metrics
  - job_name: 'siemless-engines'
    scrape_interval: 60s  # Less frequent for engines
    static_configs:
      - targets:
        - 'ingestion:8003'
        - 'intelligence:8001'
        - 'contextualization:8004'
        - 'backend:8002'
        - 'delivery:8005'
    metrics_path: '/metrics'

  # Redis metrics (lightweight)
  - job_name: 'redis'
    scrape_interval: 60s
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'

  # PostgreSQL metrics (essential only)
  - job_name: 'postgres'
    scrape_interval: 120s  # Less frequent for DB
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: '/metrics'

# Alerting rules (minimal)
rule_files:
  - '/etc/prometheus/alerts.yml'

# Remote write for long-term storage (optional)
# Can use free tier of Grafana Cloud (10k series free)
# remote_write:
#   - url: https://prometheus-us-central1.grafana.net/api/prom/push