"""
Cost Tracker - Real-time AI cost tracking and budget enforcement
Tracks token usage, calculates costs, enforces budgets
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import threading


def db_fetch(connection, query: str, *params):
    """Helper to execute fetch queries with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    results = cursor.fetchall()
    cursor.close()
    return results


def db_execute(connection, query: str, *params):
    """Helper to execute non-query commands with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    cursor.close()


def db_fetchval(connection, query: str, *params):
    """Helper to fetch a single value with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    result = cursor.fetchone()
    cursor.close()
    return result[0] if result else None


def db_fetchrow(connection, query: str, *params):
    """Helper to fetch a single row with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    result = cursor.fetchone()
    cursor.close()
    return result



@dataclass
class UsageRecord:
    """Single usage record"""
    timestamp: datetime
    model: str
    provider: str
    task: str
    input_tokens: int
    output_tokens: int
    total_tokens: int
    cost: float
    request_id: str = ""


class CostTracker:
    """
    Tracks AI usage and costs in real-time

    Features:
    - Track token usage per model/task/provider
    - Calculate actual costs based on model pricing
    - Enforce budget limits (warn/throttle/block)
    - Generate usage reports
    - Store usage history in PostgreSQL
    """

    def __init__(
        self,
        db_connection=None,
        budget_config: Optional[Dict[str, Any]] = None,
        logger: logging.Logger = None
    ):
        """
        Initialize Cost Tracker

        Args:
            db_connection: PostgreSQL connection (optional)
            budget_config: Budget configuration dict
            logger: Logger instance
        """
        self.db = db_connection
        self.logger = logger or logging.getLogger(__name__)

        # In-memory usage tracking
        self._usage_records: List[UsageRecord] = []
        self._lock = threading.Lock()

        # Budget configuration
        self.budget_config = budget_config or {
            'monthly_total': 50.0,
            'per_model': {},
            'alerts': []
        }

        # Current spending
        self._current_spend = {
            'total': 0.0,
            'by_model': {},
            'by_provider': {},
            'by_task': {}
        }

        # Model pricing (cost per 1M tokens)
        self._model_pricing = {
            # Google models
            'gemma-27b': {'input': 0.0, 'output': 0.0},  # FREE
            'gemma-3-27b': {'input': 0.0, 'output': 0.0},  # FREE
            'gemini-2.5-flash': {'input': 0.075, 'output': 0.30},  # $0.075/$0.30 per 1M
            'gemini-2.5-pro': {'input': 1.25, 'output': 5.00},  # %s.25/%s.00 per 1M

            # Anthropic models
            'claude-sonnet-4': {'input': 3.00, 'output': 15.00},  # %s/$15 per 1M
            'claude-opus-4': {'input': 15.00, 'output': 75.00},  # $15/$75 per 1M
            'claude-haiku-3.5': {'input': 0.80, 'output': 4.00},  # $0.80/%s.00 per 1M

            # OpenAI models
            'gpt-4-turbo': {'input': 10.00, 'output': 30.00},  # $10/$30 per 1M
            'gpt-5': {'input': 15.00, 'output': 60.00},  # $15/$60 per 1M (estimated)
            'gpt-4o': {'input': 2.50, 'output': 10.00},  # %s.50/$10 per 1M

            # Ollama (local, free)
            'llama3:70b': {'input': 0.0, 'output': 0.0},
            'mistral:7b': {'input': 0.0, 'output': 0.0}
        }

        # Load budget config from database if available
        if self.db:
            self._load_budget_from_db()

        self.logger.info("Cost Tracker initialized")

    def record_usage(
        self,
        model: str,
        input_tokens: int,
        output_tokens: int,
        task: str = "general",
        provider: str = "",
        request_id: str = ""
    ):
        """
        Record AI usage and calculate cost

        Args:
            model: Model name
            input_tokens: Input tokens used
            output_tokens: Output tokens generated
            task: Task type (sigma_enhancement, log_parsing, etc.)
            provider: Provider name (google, anthropic, openai)
            request_id: Unique request ID
        """
        with self._lock:
            # Calculate cost
            cost = self._calculate_cost(model, input_tokens, output_tokens)

            # Create record
            record = UsageRecord(
                timestamp=datetime.now(),
                model=model,
                provider=provider,
                task=task,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                total_tokens=input_tokens + output_tokens,
                cost=cost,
                request_id=request_id
            )

            # Store in memory
            self._usage_records.append(record)

            # Update current spend
            self._current_spend['total'] += cost
            self._current_spend['by_model'][model] = self._current_spend['by_model'].get(model, 0.0) + cost
            self._current_spend['by_provider'][provider] = self._current_spend['by_provider'].get(provider, 0.0) + cost
            self._current_spend['by_task'][task] = self._current_spend['by_task'].get(task, 0.0) + cost

            # Log usage
            self.logger.info(
                f"Usage recorded: {model} | {input_tokens} in, {output_tokens} out | "
                f"${cost:.6f} | Task: {task}"
            )

            # Check budget alerts
            self._check_budget_alerts()

            # Store in database if available
            if self.db:
                self._store_in_db(record)

    def _calculate_cost(self, model: str, input_tokens: int, output_tokens: int) -> float:
        """
        Calculate cost for usage

        Args:
            model: Model name
            input_tokens: Input tokens
            output_tokens: Output tokens

        Returns:
            Cost in USD
        """
        # Get pricing for model
        pricing = self._model_pricing.get(model, {'input': 0.0, 'output': 0.0})

        # Calculate cost (pricing is per 1M tokens)
        input_cost = (input_tokens / 1_000_000) * pricing['input']
        output_cost = (output_tokens / 1_000_000) * pricing['output']

        return input_cost + output_cost

    def check_budget(self, model: str, estimated_tokens: int) -> bool:
        """
        Check if request would exceed budget

        Args:
            model: Model name
            estimated_tokens: Estimated total tokens

        Returns:
            True if within budget, False if would exceed
        """
        # Estimate cost (assume 50/50 split for estimation)
        input_tokens = estimated_tokens // 2
        output_tokens = estimated_tokens // 2
        estimated_cost = self._calculate_cost(model, input_tokens, output_tokens)

        # Check total budget
        monthly_budget = self.budget_config.get('monthly_total', float('inf'))
        if self._current_spend['total'] + estimated_cost > monthly_budget:
            self.logger.warning(
                f"Budget check failed: ${self._current_spend['total'] + estimated_cost:.2f} "
                f"would exceed ${monthly_budget:.2f}"
            )
            return False

        # Check per-model budget
        per_model_budgets = self.budget_config.get('per_model', {})
        if model in per_model_budgets:
            model_budget = per_model_budgets[model]
            current_model_spend = self._current_spend['by_model'].get(model, 0.0)
            if current_model_spend + estimated_cost > model_budget:
                self.logger.warning(
                    f"Model budget check failed: {model} "
                    f"${current_model_spend + estimated_cost:.2f} "
                    f"would exceed ${model_budget:.2f}"
                )
                return False

        return True

    def _check_budget_alerts(self):
        """Check if any budget alerts should be triggered"""
        monthly_budget = self.budget_config.get('monthly_total', float('inf'))
        current_percent = (self._current_spend['total'] / monthly_budget) * 100 if monthly_budget > 0 else 0

        alerts = self.budget_config.get('alerts', [])
        for alert in alerts:
            threshold = alert.get('threshold', 1.0)  # 0.8 = 80%
            threshold_percent = threshold * 100

            if current_percent >= threshold_percent:
                action = alert.get('action', 'log')
                self.logger.warning(
                    f"Budget alert triggered: {current_percent:.1f}% of budget used "
                    f"(threshold: {threshold_percent:.0f}%) | Action: {action}"
                )

    def get_current_spend(self, timeframe: str = 'all') -> Dict[str, Any]:
        """
        Get current spending

        Args:
            timeframe: 'all', 'month', 'week', 'day'

        Returns:
            Dictionary with spending data
        """
        if timeframe == 'all':
            return self._current_spend.copy()

        # Filter by timeframe
        now = datetime.now()
        if timeframe == 'month':
            start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        elif timeframe == 'week':
            start_date = now - timedelta(days=7)
        elif timeframe == 'day':
            start_date = now - timedelta(days=1)
        else:
            start_date = datetime.min

        # Calculate from records
        filtered_records = [r for r in self._usage_records if r.timestamp >= start_date]

        spend = {
            'total': sum(r.cost for r in filtered_records),
            'by_model': {},
            'by_provider': {},
            'by_task': {},
            'timeframe': timeframe,
            'start_date': start_date.isoformat(),
            'record_count': len(filtered_records)
        }

        for record in filtered_records:
            spend['by_model'][record.model] = spend['by_model'].get(record.model, 0.0) + record.cost
            spend['by_provider'][record.provider] = spend['by_provider'].get(record.provider, 0.0) + record.cost
            spend['by_task'][record.task] = spend['by_task'].get(record.task, 0.0) + record.cost

        return spend

    def get_usage_report(self, group_by: str = 'model', limit: int = 10) -> List[Dict[str, Any]]:
        """
        Generate usage report

        Args:
            group_by: 'model', 'provider', 'task', 'date'
            limit: Maximum number of results

        Returns:
            List of usage statistics
        """
        report = []

        if group_by == 'model':
            for model, cost in sorted(self._current_spend['by_model'].items(), key=lambda x: x[1], reverse=True)[:limit]:
                records = [r for r in self._usage_records if r.model == model]
                report.append({
                    'model': model,
                    'total_cost': cost,
                    'total_tokens': sum(r.total_tokens for r in records),
                    'request_count': len(records),
                    'avg_cost_per_request': cost / len(records) if records else 0
                })

        elif group_by == 'provider':
            for provider, cost in sorted(self._current_spend['by_provider'].items(), key=lambda x: x[1], reverse=True)[:limit]:
                records = [r for r in self._usage_records if r.provider == provider]
                report.append({
                    'provider': provider,
                    'total_cost': cost,
                    'total_tokens': sum(r.total_tokens for r in records),
                    'request_count': len(records)
                })

        elif group_by == 'task':
            for task, cost in sorted(self._current_spend['by_task'].items(), key=lambda x: x[1], reverse=True)[:limit]:
                records = [r for r in self._usage_records if r.task == task]
                report.append({
                    'task': task,
                    'total_cost': cost,
                    'total_tokens': sum(r.total_tokens for r in records),
                    'request_count': len(records)
                })

        return report

    def get_budget_status(self) -> Dict[str, Any]:
        """Get current budget status"""
        monthly_budget = self.budget_config.get('monthly_total', 0.0)
        current_spend = self._current_spend['total']
        remaining = monthly_budget - current_spend
        percent_used = (current_spend / monthly_budget * 100) if monthly_budget > 0 else 0

        return {
            'monthly_budget': monthly_budget,
            'current_spend': current_spend,
            'remaining': remaining,
            'percent_used': percent_used,
            'status': 'healthy' if percent_used < 80 else 'warning' if percent_used < 95 else 'critical'
        }

    def _store_in_db(self, record: UsageRecord):
        """Store usage record in database"""
        if not self.db:
            return

        try:
            # Call PostgreSQL function to record usage
            self.db.execute(
                """
                SELECT record_ai_usage(%s, %s, %s, %s, %s, %s, %s, %s)
                """,
                record.model,
                record.provider,
                record.task,
                record.input_tokens,
                record.output_tokens,
                record.cost,
                record.request_id or None,
                {}  # metadata placeholder
            )
            self.logger.debug(f"Stored usage record in database: {record.request_id}")
        except Exception as e:
            self.logger.error(f"Failed to store usage in database: {e}")

    def reset_monthly(self):
        """Reset monthly counters (call at start of month)"""
        with self._lock:
            self._current_spend = {
                'total': 0.0,
                'by_model': {},
                'by_provider': {},
                'by_task': {}
            }
            self.logger.info("Monthly counters reset")

    def _load_budget_from_db(self):
        """Load budget configuration from database"""
        try:
            result = self.db.fetchrow(
                "SELECT monthly_total, per_model, alerts FROM budget_configurations WHERE is_active = TRUE LIMIT 1"
            )
            if result:
                self.budget_config = {
                    'monthly_total': float(result['monthly_total']),
                    'per_model': result['per_model'] or {},
                    'alerts': result['alerts'] or []
                }
                self.logger.info(f"Loaded budget config from database: ${self.budget_config['monthly_total']:.2f}/month")
        except Exception as e:
            self.logger.warning(f"Failed to load budget from database: {e}")

    def get_db_spending_summary(self, days: int = 30) -> Dict[str, Any]:
        """Get spending summary from database"""
        if not self.db:
            return {}

        try:
            # Get spending by model from last N days
            rows = self.db.fetch(
                """
                SELECT model, provider, task,
                       COUNT(*) as request_count,
                       SUM(input_tokens) as total_input_tokens,
                       SUM(output_tokens) as total_output_tokens,
                       SUM(cost) as total_cost,
                       AVG(cost) as avg_cost
                FROM ai_usage_records
                WHERE timestamp >= NOW() - INTERVAL '%s days'
                GROUP BY model, provider, task
                ORDER BY total_cost DESC
                """,
                days
            )

            summary = {
                'total_cost': 0.0,
                'total_requests': 0,
                'by_model': [],
                'days': days
            }

            for row in rows:
                model_data = {
                    'model': row['model'],
                    'provider': row['provider'],
                    'task': row['task'],
                    'request_count': row['request_count'],
                    'total_input_tokens': row['total_input_tokens'],
                    'total_output_tokens': row['total_output_tokens'],
                    'total_cost': float(row['total_cost']),
                    'avg_cost': float(row['avg_cost'])
                }
                summary['by_model'].append(model_data)
                summary['total_cost'] += model_data['total_cost']
                summary['total_requests'] += model_data['request_count']

            return summary

        except Exception as e:
            self.logger.error(f"Failed to get spending summary from database: {e}")
            return {}

    def get_cost_projection(self) -> Dict[str, Any]:
        """Calculate projected monthly cost based on current usage"""
        if not self.db:
            return {}

        try:
            # Get current month spending
            result = self.db.fetchrow(
                """
                SELECT
                    COALESCE(SUM(cost), 0) as current_spend,
                    COUNT(*) as request_count,
                    EXTRACT(DAY FROM NOW()) as current_day
                FROM ai_usage_records
                WHERE timestamp >= DATE_TRUNC('month', NOW())
                """
            )

            if not result or result['current_day'] == 0:
                return {}

            current_spend = float(result['current_spend'])
            current_day = int(result['current_day'])
            request_count = result['request_count']

            # Project to end of month (30 days)
            daily_avg = current_spend / current_day if current_day > 0 else 0
            projected_spend = daily_avg * 30

            # Calculate confidence (higher with more days of data)
            confidence = min(current_day / 30.0, 1.0)

            return {
                'current_spend': current_spend,
                'projected_spend': projected_spend,
                'daily_average': daily_avg,
                'days_elapsed': current_day,
                'request_count': request_count,
                'confidence': confidence,
                'budget_limit': self.budget_config.get('monthly_total', 0.0),
                'projected_overage': max(0, projected_spend - self.budget_config.get('monthly_total', 0.0))
            }

        except Exception as e:
            self.logger.error(f"Failed to calculate cost projection: {e}")
            return {}

    def __repr__(self) -> str:
        return f"CostTracker(records={len(self._usage_records)}, spend=${self._current_spend['total']:.4f})"
