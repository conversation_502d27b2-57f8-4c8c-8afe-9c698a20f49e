# SIEMLess v2.0 - Contextualization Engine Docker Image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy base engine
COPY base_engine.py ./base_engine.py

# Copy contextualization engine code
COPY contextualization_engine.py .
COPY entity_extractor.py .
COPY enrichment_service.py .
COPY adaptive_entity_extractor.py .
COPY cti_enrichment_pipeline.py .
COPY log_schema_detector.py .
COPY deterministic_extractor.py .

# Create directories for data and logs
RUN mkdir -p /tmp/claude/contextualization /var/log/siemless

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import asyncio; from contextualization_engine import ContextualizationEngine; engine = ContextualizationEngine(); print('Health check passed')" || exit 1

# Run the contextualization engine
CMD ["python", "contextualization_engine.py"]