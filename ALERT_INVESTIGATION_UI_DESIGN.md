# Alert Investigation UI Design
**Frontend Visualization for Enriched and Correlated Alerts**

## Overview

This document describes how enriched and correlated alert data is presented to analysts in the SIEMLess frontend, transforming raw backend data into actionable intelligence.

---

## 1. <PERSON><PERSON> Queue Widget (Entry Point)

**Location**: Dashboard main view
**Purpose**: First view of incoming alerts

```
┌─────────────────────────────────────────────────────────────┐
│ 🚨 Alert Queue                              [Auto-refresh ✓]│
├─────────────────────────────────────────────────────────────┤
│                                                               │
│ 🔴 CRITICAL  Lateral Movement Detected                       │
│    ************ → Multiple Hosts                            │
│    ├─ 🔍 Enriched (5/5 entities)                            │
│    ├─ 🔗 Correlated (12 related events)                      │
│    ├─ ⚔️  MITRE: T1021, T1078                                │
│    └─ 📊 Correlation Score: 0.85 (HIGH)                      │
│    [View Investigation →]                    2 min ago       │
│                                                               │
│ 🟠 HIGH  Unusual Data Upload                                 │
│    user: john.doe → ***********                             │
│    ├─ 🔍 Enriching... (3/5 entities)        ⏳              │
│    ├─ 🔗 Correlating... (searching 30min window) ⏳         │
│    └─ ⚠️  Threat Score: 0.65 (MEDIUM)                        │
│    [View Investigation →]                    5 min ago       │
│                                                               │
│ 🟡 MEDIUM  Failed Authentication Spike                       │
│    user: admin → 192.168.1.100                              │
│    ├─ 🔍 Not enriched                        [Enrich Now]   │
│    ├─ 🔗 Not correlated                      [Correlate]    │
│    └─ 📊 No correlation data                                 │
│    [View Investigation →]                    12 min ago      │
│                                                               │
└─────────────────────────────────────────────────────────────┘
```

**Visual Indicators**:
- ✅ Green checkmark: Enrichment/correlation complete
- ⏳ Spinner: In progress
- ❌ Gray X: Not performed
- 📊 Bar chart icon: Correlation score indicator
- 🔍 Magnifying glass: Enrichment status
- 🔗 Chain link: Correlation status

---

## 2. Alert Investigation Screen (Full Detail)

When user clicks "View Investigation →"

### 2.1 Header Section

```
┌─────────────────────────────────────────────────────────────┐
│ ← Back to Queue                                              │
│                                                               │
│ 🔴 CRITICAL                                                  │
│ Lateral Movement Detected                                    │
│ Alert ID: alert_abc123        Created: 2025-10-03 12:00 UTC │
│                                                               │
│ Rule: Detect_SMB_Lateral_Movement_v2                         │
│ Source: Elastic Security (elastic-prod-01)                   │
│ Quality Score: 8.5/10 (Premium Detection)                    │
│                                                               │
│ [🔍 Re-enrich] [🔗 Re-correlate] [📋 Export] [🚫 Close]    │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Tab Navigation

```
┌─────────────────────────────────────────────────────────────┐
│ [📊 Overview] [🌐 Entities] [🔗 Correlation] [⚔️ MITRE]    │
│              [📜 Timeline]   [🤖 AI Analysis]                │
└─────────────────────────────────────────────────────────────┘
```

---

## 3. TAB 1: Overview

**Purpose**: Executive summary with key findings

```
┌─────────────────────────────────────────────────────────────┐
│ 📊 OVERVIEW                                                  │
├─────────────────────────────────────────────────────────────┤
│                                                               │
│ ┌─────────────────────────┐  ┌─────────────────────────┐   │
│ │ 🎯 Threat Assessment    │  │ 📈 Correlation Summary  │   │
│ │                         │  │                         │   │
│ │ Overall Threat: HIGH    │  │ Related Events: 12      │   │
│ │ Confidence: 85%         │  │ Time Span: 45 min       │   │
│ │                         │  │ Correlation Score: 0.85 │   │
│ │ ✓ 2 Malicious IPs       │  │                         │   │
│ │ ✓ 3 Attack Stages       │  │ Attack Stages:          │   │
│ │ ✓ Multi-host spread     │  │ ✓ Initial Access        │   │
│ │                         │  │ ✓ Lateral Movement      │   │
│ │ [View Details →]        │  │ ✓ Privilege Escalation  │   │
│ └─────────────────────────┘  └─────────────────────────┘   │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 🤖 AI Verdict                                         │   │
│ │                                                       │   │
│ │ Verdict: LIKELY MALICIOUS                            │   │
│ │ Confidence: 85%                                       │   │
│ │                                                       │   │
│ │ Key Findings:                                         │   │
│ │ • Source IP *********** is known C2 server (OTX)    │   │
│ │ • Lateral movement to 5 hosts in 30 minutes          │   │
│ │ • Used psexec.exe and wmic.exe (LOLBAS tools)        │   │
│ │ • Followed known APT29 playbook patterns             │   │
│ │                                                       │   │
│ │ Recommended Action: IMMEDIATE CONTAINMENT            │   │
│ │                                                       │   │
│ │ Suggested Steps:                                      │   │
│ │ 1. Isolate all 5 affected hosts from network        │   │
│ │ 2. Reset credentials for user 'admin'                │   │
│ │ 3. Block *********** at perimeter firewall          │   │
│ │ 4. Initiate EDR memory capture on all hosts          │   │
│ │ 5. Escalate to Incident Response team                │   │
│ │                                                       │   │
│ │ [🚀 Execute Playbook] [📋 Copy to Runbook]           │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
└─────────────────────────────────────────────────────────────┘
```

---

## 4. TAB 2: Entities (Enrichment Data)

**Purpose**: Show all enriched entities with threat intelligence

```
┌─────────────────────────────────────────────────────────────┐
│ 🌐 ENTITIES (5 enriched)                                     │
├─────────────────────────────────────────────────────────────┤
│                                                               │
│ Filter: [All] [IPs] [Users] [Hosts] [Malicious Only ✓]     │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 🔴 IP: ***********                    Threat: 0.95/1.0│   │
│ │────────────────────────────────────────────────────────│   │
│ │                                                        │   │
│ │ 🌍 Geolocation                                         │   │
│ │   • Country: Russia                                    │   │
│ │   • City: Moscow                                       │   │
│ │   • ASN: AS12345 (MALICIOUS-HOSTING-AS)              │   │
│ │   • IP Type: External / Public                         │   │
│ │                                                        │   │
│ │ ⚠️ Threat Intelligence (2 sources)                     │   │
│ │   • OTX: Known C2 server (APT29 campaign)             │   │
│ │     - First seen: 2025-09-15                           │   │
│ │     - Indicators: 127 related IPs                      │   │
│ │     - Tags: malware, c2, apt29, nobelium               │   │
│ │                                                        │   │
│ │   • ThreatFox: Cobalt Strike C2                        │   │
│ │     - Confidence: High                                 │   │
│ │     - Malware family: CobaltStrike                     │   │
│ │     - Reporter: abuse.ch                               │   │
│ │                                                        │   │
│ │ 🔍 WHOIS                                               │   │
│ │   • Registrar: MALICIOUS-REGISTRAR-RU                 │   │
│ │   • Created: 2025-08-01 (recent!)                      │   │
│ │   • Updated: 2025-09-20                                │   │
│ │                                                        │   │
│ │ 📊 Historical Activity                                 │   │
│ │   • First seen in your network: TODAY (NEW!)          │   │
│ │   • Previous alerts: 0                                 │   │
│ │   • Connections: 5 internal hosts                      │   │
│ │                                                        │   │
│ │ [🚫 Block IP] [🔎 Pivot Search] [📋 Copy]            │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 🟢 IP: ************              Threat: 0.00/1.0     │   │
│ │────────────────────────────────────────────────────────│   │
│ │                                                        │   │
│ │ 🌍 Geolocation                                         │   │
│ │   • IP Type: Internal / RFC1918                        │   │
│ │   • Network: Corporate LAN (VLAN 10)                   │   │
│ │                                                        │   │
│ │ 🏢 Asset Information                                   │   │
│ │   • Hostname: DC-01.corp.local                         │   │
│ │   • Asset Type: Domain Controller                      │   │
│ │   • Criticality: CRITICAL                              │   │
│ │   • Owner: IT Infrastructure                           │   │
│ │   • Last Seen: 2025-10-03 12:00 UTC                   │   │
│ │                                                        │   │
│ │ ✅ Threat Intelligence                                 │   │
│ │   • No threats found (Checked: OTX, ThreatFox)        │   │
│ │                                                        │   │
│ │ 📊 Historical Activity                                 │   │
│ │   • Baseline: 150-200 connections/day                  │   │
│ │   • Today: 245 connections (⚠️ 22% above baseline)    │   │
│ │   • Anomaly detected: Outbound connections spike       │   │
│ │                                                        │   │
│ │ [🔎 Pivot Search] [📊 View Baseline] [📋 Copy]       │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 👤 USER: admin                   Threat: 0.40/1.0     │   │
│ │────────────────────────────────────────────────────────│   │
│ │                                                        │   │
│ │ 👤 User Information                                    │   │
│ │   • Full Name: Administrator Account                   │   │
│ │   • Email: <EMAIL>                            │   │
│ │   • Department: IT Operations                          │   │
│ │   • Privilege Level: Domain Admin                      │   │
│ │   • Status: Active                                     │   │
│ │                                                        │   │
│ │ ⚠️ Risk Indicators                                     │   │
│ │   • Used from 3 different IPs (unusual!)              │   │
│ │   • Login from external IP (policy violation!)         │   │
│ │   • Lateral movement to 5 hosts                        │   │
│ │                                                        │   │
│ │ 📊 Behavioral Analysis                                 │   │
│ │   • Typical login times: 08:00-17:00 EST              │   │
│ │   • This alert: 23:45 EST (⚠️ after hours)            │   │
│ │   • Typical login location: ***********/24            │   │
│ │   • This alert: *********** (⚠️ external!)            │   │
│ │                                                        │   │
│ │ [🔐 Reset Password] [🚫 Disable Account] [📋 Copy]   │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
└─────────────────────────────────────────────────────────────┘
```

**Color Coding**:
- 🔴 Red header: Malicious (threat score > 0.7)
- 🟠 Orange header: Suspicious (threat score 0.4-0.7)
- 🟢 Green header: Benign (threat score < 0.4)

---

## 5. TAB 3: Correlation (Related Events)

**Purpose**: Show attack chain and related events

```
┌─────────────────────────────────────────────────────────────┐
│ 🔗 CORRELATION (12 related events)                           │
├─────────────────────────────────────────────────────────────┤
│                                                               │
│ Time Window: 11:30 - 12:30 UTC (60 minutes)                 │
│ Correlation Score: 0.85 (HIGH confidence)                    │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 📊 Event Distribution                                  │   │
│ │                                                        │   │
│ │ By Entity Type:                                        │   │
│ │   IP addresses:    8 events  ████████░░  80%          │   │
│ │   Users:           5 events  █████░░░░░  50%          │   │
│ │   Hosts:           2 events  ██░░░░░░░░  20%          │   │
│ │                                                        │   │
│ │ By Source:                                             │   │
│ │   Windows Events:  7 events  ███████░░░  70%          │   │
│ │   Firewall:        3 events  ███░░░░░░░  30%          │   │
│ │   EDR:             2 events  ██░░░░░░░░  20%          │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ ⏱️ Timeline View                                       │   │
│ │                                                        │   │
│ │ 11:50 ├─● Initial Access (T1078)                      │   │
│ │       │  Event: External login from ***********       │   │
│ │       │  User: admin, Host: DC-01                      │   │
│ │       │  Source: Windows Event 4624                    │   │
│ │       │  [View Event →]                                │   │
│ │       │                                                 │   │
│ │ 11:52 ├─● Execution (T1059)                            │   │
│ │       │  Event: PowerShell execution                   │   │
│ │       │  Command: Invoke-Mimikatz.ps1                  │   │
│ │       │  Source: EDR (CrowdStrike)                     │   │
│ │       │  [View Event →]                                │   │
│ │       │                                                 │   │
│ │ 11:55 ├─● Lateral Movement (T1021)                     │   │
│ │       │  Event: SMB connection to 5 hosts              │   │
│ │       │  From: DC-01 → WS-{01-05}                      │   │
│ │       │  Port: 445 (SMB)                                │   │
│ │       │  Source: Firewall logs                          │   │
│ │       │  [View Event →]                                │   │
│ │       │                                                 │   │
│ │ 12:00 ├─● THIS ALERT (T1021)                           │   │
│ │       │  Event: Lateral movement detected              │   │
│ │       │  Severity: CRITICAL                             │   │
│ │       │  [You are here]                                 │   │
│ │                                                         │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 📋 Related Events Table                                │   │
│ │────────────────────────────────────────────────────────│   │
│ │ Time   │ Source    │ Event              │ Entity      │   │
│ │────────┼───────────┼────────────────────┼─────────────│   │
│ │ 11:50  │ Windows   │ User login         │ admin       │   │
│ │ 11:50  │ Firewall  │ Allow from ext     │ ***********│   │
│ │ 11:52  │ EDR       │ PS execution       │ DC-01       │   │
│ │ 11:52  │ EDR       │ Mimikatz detected  │ DC-01       │   │
│ │ 11:55  │ Firewall  │ SMB to WS-01       │ 192.168.1.1│   │
│ │ 11:56  │ Firewall  │ SMB to WS-02       │ 192.168.1.2│   │
│ │ 11:57  │ Windows   │ User login WS-03   │ admin       │   │
│ │ 11:58  │ Windows   │ User login WS-04   │ admin       │   │
│ │ 12:00  │ EDR       │ psexec.exe exec    │ WS-05       │   │
│ │────────┴───────────┴────────────────────┴─────────────│   │
│ │ [Load More →] Showing 9 of 12 events                   │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ [📊 Export Timeline] [🔍 Pivot to SIEM] [📋 Copy All]      │
│                                                               │
└─────────────────────────────────────────────────────────────┘
```

---

## 6. TAB 4: MITRE ATT&CK

**Purpose**: Show attack chain mapped to MITRE framework

```
┌─────────────────────────────────────────────────────────────┐
│ ⚔️ MITRE ATT&CK CHAIN                                        │
├─────────────────────────────────────────────────────────────┤
│                                                               │
│ Attack Stages Detected: 3 of 14                              │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ Kill Chain Visualization                               │   │
│ │                                                        │   │
│ │ Initial    Lateral    Privilege   Defense   Command   │   │
│ │ Access  →  Movement → Escalation→ Evasion → & Control │   │
│ │   ✓           ✓          ✓          ✗         ✗       │   │
│ │                                                        │   │
│ │ [═══════▶][═══════▶][═══════▶][ ········ ][ ········ ]│   │
│ │                                                        │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 🎯 T1078: Valid Accounts                               │   │
│ │────────────────────────────────────────────────────────│   │
│ │ Tactic: Initial Access                                 │   │
│ │ Detection: 11:50 UTC                                   │   │
│ │ Confidence: HIGH (95%)                                 │   │
│ │                                                        │   │
│ │ Evidence:                                              │   │
│ │ • Login from external IP (***********)                │   │
│ │ • After-hours login (23:45 local time)                │   │
│ │ • Domain admin account used                            │   │
│ │                                                        │   │
│ │ Sub-techniques:                                        │   │
│ │ • T1078.002: Domain Accounts ✓                        │   │
│ │                                                        │   │
│ │ [View MITRE Details] [View Evidence →]                │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 🎯 T1021: Remote Services                              │   │
│ │────────────────────────────────────────────────────────│   │
│ │ Tactic: Lateral Movement                               │   │
│ │ Detection: 11:55 UTC                                   │   │
│ │ Confidence: HIGH (92%)                                 │   │
│ │                                                        │   │
│ │ Evidence:                                              │   │
│ │ • SMB connections to 5 internal hosts                  │   │
│ │ • psexec.exe execution detected                        │   │
│ │ • Sequential host access pattern                       │   │
│ │                                                        │   │
│ │ Sub-techniques:                                        │   │
│ │ • T1021.002: SMB/Windows Admin Shares ✓               │   │
│ │                                                        │   │
│ │ [View MITRE Details] [View Evidence →]                │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 🎯 T1059: Command and Scripting Interpreter            │   │
│ │────────────────────────────────────────────────────────│   │
│ │ Tactic: Execution                                      │   │
│ │ Detection: 11:52 UTC                                   │   │
│ │ Confidence: HIGH (97%)                                 │   │
│ │                                                        │   │
│ │ Evidence:                                              │   │
│ │ • PowerShell execution detected                        │   │
│ │ • Invoke-Mimikatz.ps1 script                          │   │
│ │ • Credential dumping behavior                          │   │
│ │                                                        │   │
│ │ Sub-techniques:                                        │   │
│ │ • T1059.001: PowerShell ✓                             │   │
│ │                                                        │   │
│ │ [View MITRE Details] [View Evidence →]                │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 📚 Similar Campaigns                                   │   │
│ │                                                        │   │
│ │ This attack pattern matches:                           │   │
│ │ • APT29 (Cozy Bear) - 85% similarity                  │   │
│ │ • Nobelium Campaign 2024 - 78% similarity              │   │
│ │                                                        │   │
│ │ [View Campaign Details →]                              │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
└─────────────────────────────────────────────────────────────┘
```

---

## 7. TAB 5: Timeline (Combined View)

**Purpose**: Unified timeline of alert + enrichment + correlation

```
┌─────────────────────────────────────────────────────────────┐
│ 📜 INVESTIGATION TIMELINE                                    │
├─────────────────────────────────────────────────────────────┤
│                                                               │
│ Filter: [All Events] [Alert Only] [Enrichment] [Correlation]│
│                                                               │
│ 11:50 UTC ┌──────────────────────────────────────────────┐  │
│           │ 🔵 SYSTEM: External Login Detected           │  │
│           │ Event: Windows Event 4624                    │  │
│           │ User: admin from ***********                 │  │
│           └──────────────────────────────────────────────┘  │
│                                                               │
│ 11:50 UTC ┌──────────────────────────────────────────────┐  │
│           │ 🟣 ENRICHMENT: IP Analyzed                   │  │
│           │ *********** → MALICIOUS (OTX, ThreatFox)    │  │
│           │ Threat Score: 0.95/1.0                       │  │
│           │ Tags: C2, APT29, CobaltStrike                │  │
│           └──────────────────────────────────────────────┘  │
│                                                               │
│ 11:52 UTC ┌──────────────────────────────────────────────┐  │
│           │ 🔵 CORRELATED: PowerShell Execution          │  │
│           │ Command: Invoke-Mimikatz.ps1                 │  │
│           │ Host: DC-01                                  │  │
│           │ MITRE: T1059.001 (PowerShell)                │  │
│           └──────────────────────────────────────────────┘  │
│                                                               │
│ 11:55 UTC ┌──────────────────────────────────────────────┐  │
│           │ 🔵 CORRELATED: Lateral Movement Begin        │  │
│           │ SMB connections: DC-01 → 5 hosts             │  │
│           │ MITRE: T1021.002 (SMB)                       │  │
│           └──────────────────────────────────────────────┘  │
│                                                               │
│ 12:00 UTC ┌──────────────────────────────────────────────┐  │
│           │ 🔴 ALERT: Lateral Movement Detected          │  │
│           │ Severity: CRITICAL                           │  │
│           │ Rule: Detect_SMB_Lateral_Movement_v2         │  │
│           │ Triggered by correlation engine              │  │
│           └──────────────────────────────────────────────┘  │
│                                                               │
│ 12:00 UTC ┌──────────────────────────────────────────────┐  │
│           │ 🟣 ENRICHMENT: Started                       │  │
│           │ Enriching 5 entities...                      │  │
│           └──────────────────────────────────────────────┘  │
│                                                               │
│ 12:00 UTC ┌──────────────────────────────────────────────┐  │
│           │ 🟢 CORRELATION: Started                      │  │
│           │ Searching ±30min window...                   │  │
│           └──────────────────────────────────────────────┘  │
│                                                               │
│ 12:00 UTC ┌──────────────────────────────────────────────┐  │
│           │ 🟣 ENRICHMENT: Complete                      │  │
│           │ 5/5 entities enriched                        │  │
│           │ 2 threats found                              │  │
│           └──────────────────────────────────────────────┘  │
│                                                               │
│ 12:01 UTC ┌──────────────────────────────────────────────┐  │
│           │ 🟢 CORRELATION: Complete                     │  │
│           │ 12 related events found                      │  │
│           │ 3 MITRE techniques identified                │  │
│           │ Score: 0.85 (HIGH)                           │  │
│           └──────────────────────────────────────────────┘  │
│                                                               │
│ 12:01 UTC ┌──────────────────────────────────────────────┐  │
│           │ 🤖 AI ANALYSIS: Complete                     │  │
│           │ Verdict: LIKELY MALICIOUS (85%)              │  │
│           │ Recommendation: IMMEDIATE CONTAINMENT        │  │
│           └──────────────────────────────────────────────┘  │
│                                                               │
│ 12:02 UTC ┌──────────────────────────────────────────────┐  │
│           │ 👤 ANALYST: Investigation Opened             │  │
│           │ Analyst: sarah.johnson                       │  │
│           │ Status: In Progress                          │  │
│           └──────────────────────────────────────────────┘  │
│                                                               │
└─────────────────────────────────────────────────────────────┘
```

**Legend**:
- 🔴 Red: Alert events
- 🔵 Blue: Correlated events
- 🟣 Purple: Enrichment activities
- 🟢 Green: Correlation activities
- 🤖 Robot: AI analysis
- 👤 Person: Analyst actions

---

## 8. TAB 6: AI Analysis

**Purpose**: AI-powered investigation summary and recommendations

```
┌─────────────────────────────────────────────────────────────┐
│ 🤖 AI ANALYSIS                                               │
├─────────────────────────────────────────────────────────────┤
│                                                               │
│ Analysis Model: Claude Opus 4.1                              │
│ Confidence: 85% (HIGH)                                       │
│ Generated: 2025-10-03 12:01 UTC (1 minute ago)              │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 📊 VERDICT                                             │   │
│ │                                                        │   │
│ │ ⚠️ LIKELY MALICIOUS - IMMEDIATE ACTION REQUIRED        │   │
│ │                                                        │   │
│ │ Confidence: 85%                                        │   │
│ │ Risk Level: CRITICAL                                   │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 🧠 KEY FINDINGS                                        │   │
│ │                                                        │   │
│ │ 1. Known Malicious Infrastructure                      │   │
│ │    • Source IP *********** is confirmed C2 server     │   │
│ │    • Linked to APT29 (Cozy Bear) campaign             │   │
│ │    • First seen in your network TODAY (NEW!)          │   │
│ │    • Risk: CRITICAL                                    │   │
│ │                                                        │   │
│ │ 2. Credential Compromise Indicators                    │   │
│ │    • Domain admin account accessed externally          │   │
│ │    • After-hours login (23:45 local vs 08:00 typical) │   │
│ │    • Mimikatz credential dumping detected              │   │
│ │    • Risk: HIGH                                        │   │
│ │                                                        │   │
│ │ 3. Active Lateral Movement                             │   │
│ │    • 5 hosts accessed in 10 minutes (rapid spread)    │   │
│ │    • SMB protocol used (common for LM)                 │   │
│ │    • LOLBAS tools: psexec.exe, wmic.exe               │   │
│ │    • Risk: CRITICAL                                    │   │
│ │                                                        │   │
│ │ 4. Attack Sophistication                               │   │
│ │    • Follows known APT29 playbook (85% match)         │   │
│ │    • Multi-stage attack (3 MITRE tactics)             │   │
│ │    • Targeted Domain Controller first                  │   │
│ │    • Risk: HIGH                                        │   │
│ │                                                        │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 💭 REASONING                                           │   │
│ │                                                        │   │
│ │ This appears to be a sophisticated attack rather than  │   │
│ │ legitimate administrative activity because:            │   │
│ │                                                        │   │
│ │ • External access to domain admin is NEVER normal     │   │
│ │ • The source IP has ZERO reputation (created Aug 2025)│   │
│ │ • Credential dumping has NO legitimate use case       │   │
│ │ • Rapid lateral movement indicates automation         │   │
│ │ • Attack pattern matches known APT group              │   │
│ │                                                        │   │
│ │ Alternate hypotheses considered:                       │   │
│ │ ✗ Legitimate admin activity: 5% probability           │   │
│ │   - Rejected: External access + Mimikatz use          │   │
│ │ ✗ Penetration test: 10% probability                   │   │
│ │   - Rejected: No pen-test in schedule                 │   │
│ │ ✓ APT29 attack: 85% probability                       │   │
│ │   - Supported: All indicators align                   │   │
│ │                                                        │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 🎯 RECOMMENDED ACTIONS (Priority Order)                │   │
│ │                                                        │   │
│ │ IMMEDIATE (0-15 min):                                  │   │
│ │ [1] 🚫 Block *********** at perimeter firewall        │   │
│ │     Impact: Stops ongoing C2 communication             │   │
│ │     [Execute via Firewall API →]                       │   │
│ │                                                        │   │
│ │ [2] 🔒 Isolate 5 affected hosts from network          │   │
│ │     Hosts: DC-01, WS-01, WS-02, WS-03, WS-04          │   │
│ │     Impact: Prevents further spread                    │   │
│ │     [Execute via EDR API →]                            │   │
│ │                                                        │   │
│ │ [3] 🔐 Reset credentials for 'admin' account          │   │
│ │     Impact: Invalidates compromised credentials        │   │
│ │     [Open AD Management Console →]                     │   │
│ │                                                        │   │
│ │ SHORT-TERM (15-60 min):                                │   │
│ │ [4] 💾 Capture memory dumps from all 5 hosts          │   │
│ │     Purpose: Forensic analysis, malware recovery       │   │
│ │     [Execute via EDR API →]                            │   │
│ │                                                        │   │
│ │ [5] 🔍 Hunt for additional compromised accounts       │   │
│ │     Search for: Mimikatz artifacts, suspicious logins  │   │
│ │     [Launch Threat Hunt →]                             │   │
│ │                                                        │   │
│ │ [6] 📢 Escalate to Incident Response team             │   │
│ │     Classification: Critical / APT                     │   │
│ │     [Create IR Ticket →]                               │   │
│ │                                                        │   │
│ │ LONG-TERM (1-24 hours):                                │   │
│ │ [7] 🔎 Full forensic investigation                     │   │
│ │ [8] 🛡️ Apply additional hardening to DC-01             │   │
│ │ [9] 📊 Review all admin account access                │   │
│ │                                                        │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ ┌───────────────────────────────────────────────────────┐   │
│ │ 📋 AUTOMATED PLAYBOOK AVAILABLE                        │   │
│ │                                                        │   │
│ │ Playbook: APT_Lateral_Movement_Response_v3             │   │
│ │ Steps: 12 automated, 4 require manual approval        │   │
│ │ Estimated Time: 45 minutes                             │   │
│ │                                                        │   │
│ │ [🚀 Execute Playbook] [📄 View Playbook Details]      │   │
│ └───────────────────────────────────────────────────────┘   │
│                                                               │
│ [🔄 Re-analyze] [📋 Export Analysis] [💬 Ask AI Question]  │
│                                                               │
└─────────────────────────────────────────────────────────────┘
```

---

## 9. Action Buttons (Always Visible)

**Bottom toolbar on every tab**:

```
┌─────────────────────────────────────────────────────────────┐
│ ACTIONS                                                       │
├─────────────────────────────────────────────────────────────┤
│                                                               │
│ [🚀 Execute Playbook]  Execute automated response            │
│ [🚫 Block Entities]     Block malicious IPs/domains          │
│ [🔐 Reset Credentials]  Reset compromised accounts           │
│ [🔍 Pivot to SIEM]      Open in Elastic/Splunk               │
│ [📋 Export Report]      PDF/JSON export                      │
│ [✅ Mark Resolved]      Close investigation                  │
│ [📧 Escalate]           Send to IR team                      │
│                                                               │
└─────────────────────────────────────────────────────────────┘
```

---

## 10. Real-time Updates

**WebSocket notifications** appear as toasts:

```
┌─────────────────────────────────────┐
│ 🟣 Enrichment Complete              │
│ 5/5 entities enriched               │
│ 2 malicious indicators found        │
│ [View Results →]           [Dismiss]│
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ 🟢 Correlation Complete             │
│ 12 related events found             │
│ Score: 0.85 (HIGH)                  │
│ [View Results →]           [Dismiss]│
└─────────────────────────────────────┘
```

---

## 11. Mobile View (Bonus)

Simplified view for on-call analysts:

```
┌────────────────────────┐
│ 🔴 CRITICAL ALERT      │
├────────────────────────┤
│                        │
│ Lateral Movement       │
│ ************ → 5 hosts│
│                        │
│ 🔍 Enriched ✓          │
│ 🔗 Correlated ✓        │
│                        │
│ Threat: HIGH (0.85)    │
│ 2 malicious IPs        │
│                        │
│ [View Full Details]    │
│ [Execute Playbook]     │
│ [Block IPs]            │
│                        │
└────────────────────────┘
```

---

## Summary

This UI design transforms backend enrichment and correlation data into **actionable intelligence** for analysts:

1. **Alert Queue**: Quick triage with status indicators
2. **Overview Tab**: Executive summary with AI verdict
3. **Entities Tab**: Deep dive into enriched entities
4. **Correlation Tab**: Attack chain visualization
5. **MITRE Tab**: Framework mapping
6. **Timeline Tab**: Unified chronological view
7. **AI Analysis Tab**: Recommendations and reasoning

**Key UX Principles**:
- ✅ Progressive disclosure (overview → details)
- ✅ Color-coded threat levels
- ✅ One-click actions
- ✅ Real-time updates
- ✅ Mobile-responsive
- ✅ Export capabilities
- ✅ SIEM integration links

This reduces investigation time from **30-60 minutes** to **5-10 minutes** per alert.
