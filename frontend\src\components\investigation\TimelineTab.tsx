import React, { useState, useMemo } from 'react';
import { Alert, CorrelationData, RelatedEvent } from '../../types/investigation';
import '../../styles/TimelineTab.css';

interface TimelineTabProps {
  alert: Alert;
  correlation: CorrelationData | null;
}

type TimelineViewMode = 'chronological' | 'grouped' | 'heatmap';
type TimelineFilter = 'all' | 'critical' | 'high' | 'medium' | 'low';

export const TimelineTab: React.FC<TimelineTabProps> = ({ alert, correlation }) => {
  const [viewMode, setViewMode] = useState<TimelineViewMode>('chronological');
  const [severityFilter, setSeverityFilter] = useState<TimelineFilter>('all');
  const [selectedHour, setSelectedHour] = useState<string | null>(null);

  const relatedEvents = correlation?.correlation?.related_events || [];

  if (relatedEvents.length === 0) {
    return (
      <div className="no-timeline">
        <div className="no-timeline-icon">⏱️</div>
        <h3>No Timeline Data Available</h3>
        <p>No related events found to build a timeline.</p>
        <p className="hint">Correlation analysis creates event timelines when related activity is detected.</p>
      </div>
    );
  }

  // Filter events by severity
  const filteredEvents = useMemo(() => {
    if (severityFilter === 'all') return relatedEvents;
    return relatedEvents.filter(e => e.severity === severityFilter);
  }, [relatedEvents, severityFilter]);

  // Sort events chronologically
  const sortedEvents = useMemo(() => {
    return [...filteredEvents].sort((a, b) =>
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  }, [filteredEvents]);

  // Group events by hour
  const eventsByHour = useMemo(() => {
    const grouped: Record<string, RelatedEvent[]> = {};
    sortedEvents.forEach(event => {
      const hour = new Date(event.timestamp).toISOString().slice(0, 13) + ':00:00';
      if (!grouped[hour]) grouped[hour] = [];
      grouped[hour].push(event);
    });
    return grouped;
  }, [sortedEvents]);

  // Heatmap data
  const heatmapData = useMemo(() => {
    const data: Record<string, Record<string, number>> = {};
    sortedEvents.forEach(event => {
      const date = new Date(event.timestamp).toISOString().slice(0, 10);
      const hour = new Date(event.timestamp).getHours();
      if (!data[date]) data[date] = {};
      data[date][hour] = (data[date][hour] || 0) + 1;
    });
    return data;
  }, [sortedEvents]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return '#dc2626';
      case 'high': return '#ea580c';
      case 'medium': return '#d97706';
      case 'low': return '#65a30d';
      default: return '#6b7280';
    }
  };

  const getEventTypeIcon = (eventType: string) => {
    const icons: Record<string, string> = {
      'authentication': '🔐',
      'network': '🌐',
      'file': '📁',
      'process': '⚙️',
      'registry': '📋',
      'dns': '🔍',
      'http': '🌐',
      'email': '📧',
      'endpoint': '💻',
      'cloud': '☁️'
    };
    return icons[eventType.toLowerCase()] || '📌';
  };

  const formatTimeRange = (start: string, end: string) => {
    const startDate = new Date(start);
    const endDate = new Date(end);
    const diff = endDate.getTime() - startDate.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  const getHeatmapColor = (count: number, max: number) => {
    const intensity = count / max;
    if (intensity >= 0.8) return '#dc2626';
    if (intensity >= 0.6) return '#ea580c';
    if (intensity >= 0.4) return '#f59e0b';
    if (intensity >= 0.2) return '#fbbf24';
    return '#fef3c7';
  };

  const exportTimeline = () => {
    const data = {
      alert_id: alert.alert_id,
      timeline: sortedEvents.map(e => ({
        timestamp: e.timestamp,
        event_type: e.event_type,
        severity: e.severity,
        description: e.description,
        source: e.source
      }))
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `timeline_${alert.alert_id}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="timeline-tab">
      {/* Timeline Header */}
      <div className="timeline-header">
        <div className="timeline-summary">
          <h3>⏱️ Event Timeline</h3>
          <div className="timeline-stats">
            <div className="stat">
              <span className="stat-value">{sortedEvents.length}</span>
              <span className="stat-label">Events</span>
            </div>
            <div className="stat">
              <span className="stat-value">
                {formatTimeRange(sortedEvents[0]?.timestamp, sortedEvents[sortedEvents.length - 1]?.timestamp)}
              </span>
              <span className="stat-label">Duration</span>
            </div>
            <div className="stat">
              <span className="stat-value">{Object.keys(eventsByHour).length}</span>
              <span className="stat-label">Time Slots</span>
            </div>
          </div>
        </div>

        <div className="timeline-controls">
          <div className="view-mode-toggle">
            <button
              className={`mode-btn ${viewMode === 'chronological' ? 'active' : ''}`}
              onClick={() => setViewMode('chronological')}
            >
              📅 Chronological
            </button>
            <button
              className={`mode-btn ${viewMode === 'grouped' ? 'active' : ''}`}
              onClick={() => setViewMode('grouped')}
            >
              📊 Grouped
            </button>
            <button
              className={`mode-btn ${viewMode === 'heatmap' ? 'active' : ''}`}
              onClick={() => setViewMode('heatmap')}
            >
              🔥 Heatmap
            </button>
          </div>
          <button className="export-btn" onClick={exportTimeline}>
            📥 Export
          </button>
        </div>
      </div>

      {/* Severity Filters */}
      <div className="severity-filters">
        {(['all', 'critical', 'high', 'medium', 'low'] as TimelineFilter[]).map(severity => (
          <button
            key={severity}
            className={`severity-filter ${severityFilter === severity ? 'active' : ''}`}
            onClick={() => setSeverityFilter(severity)}
            style={{
              borderColor: severity === 'all' ? '#3b82f6' : getSeverityColor(severity),
              background: severityFilter === severity
                ? (severity === 'all' ? '#3b82f6' : getSeverityColor(severity))
                : 'transparent',
              color: severityFilter === severity ? '#ffffff' : '#374151'
            }}
          >
            {severity.charAt(0).toUpperCase() + severity.slice(1)}
            {severity === 'all' ? ` (${relatedEvents.length})` :
             ` (${relatedEvents.filter(e => e.severity === severity).length})`}
          </button>
        ))}
      </div>

      {/* Chronological View */}
      {viewMode === 'chronological' && (
        <div className="chronological-view">
          <div className="timeline-axis">
            {sortedEvents.map((event, index) => {
              const isFirst = index === 0;
              const isLast = index === sortedEvents.length - 1;
              const prevTime = index > 0 ? new Date(sortedEvents[index - 1].timestamp).getTime() : 0;
              const currTime = new Date(event.timestamp).getTime();
              const timeDiff = prevTime ? currTime - prevTime : 0;

              return (
                <React.Fragment key={event.event_id}>
                  {!isFirst && timeDiff > 60000 && (
                    <div className="time-gap">
                      <div className="gap-line"></div>
                      <div className="gap-label">
                        {Math.floor(timeDiff / 60000)} minutes gap
                      </div>
                    </div>
                  )}
                  <div className="timeline-event">
                    <div className="event-marker" style={{ background: getSeverityColor(event.severity) }}>
                      <div className="marker-dot"></div>
                      {!isLast && <div className="marker-line"></div>}
                    </div>
                    <div className="event-card">
                      <div className="event-time">
                        {new Date(event.timestamp).toLocaleTimeString()}
                      </div>
                      <div className="event-content">
                        <div className="event-header-row">
                          <span className="event-icon">{getEventTypeIcon(event.event_type)}</span>
                          <span className="event-type">{event.event_type}</span>
                          <span
                            className="event-severity-badge"
                            style={{ background: getSeverityColor(event.severity) }}
                          >
                            {event.severity.toUpperCase()}
                          </span>
                        </div>
                        <div className="event-description">{event.description}</div>
                        <div className="event-source">Source: {event.source}</div>
                      </div>
                    </div>
                  </div>
                </React.Fragment>
              );
            })}
          </div>
        </div>
      )}

      {/* Grouped View */}
      {viewMode === 'grouped' && (
        <div className="grouped-view">
          {Object.entries(eventsByHour)
            .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
            .map(([hour, events]) => (
              <div key={hour} className="hour-group">
                <div className="hour-header">
                  <div className="hour-time">
                    📅 {new Date(hour).toLocaleString(undefined, {
                      month: 'short',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                  <div className="hour-count">
                    {events.length} event{events.length !== 1 ? 's' : ''}
                  </div>
                </div>
                <div className="hour-events">
                  {events.map(event => (
                    <div
                      key={event.event_id}
                      className="grouped-event"
                      style={{ borderLeftColor: getSeverityColor(event.severity) }}
                    >
                      <div className="grouped-event-header">
                        <span className="grouped-event-icon">{getEventTypeIcon(event.event_type)}</span>
                        <span className="grouped-event-type">{event.event_type}</span>
                        <span
                          className="grouped-severity-badge"
                          style={{ background: getSeverityColor(event.severity) }}
                        >
                          {event.severity.toUpperCase()}
                        </span>
                      </div>
                      <div className="grouped-event-description">{event.description}</div>
                      <div className="grouped-event-meta">
                        <span>🕐 {new Date(event.timestamp).toLocaleTimeString()}</span>
                        <span>🔌 {event.source}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
        </div>
      )}

      {/* Heatmap View */}
      {viewMode === 'heatmap' && (
        <div className="heatmap-view">
          <div className="heatmap-info">
            <h4>📊 Activity Heatmap</h4>
            <p>Click on a cell to see events for that hour</p>
          </div>
          <div className="heatmap-grid">
            <div className="heatmap-hours">
              <div className="hour-label"></div>
              {Array.from({ length: 24 }, (_, i) => (
                <div key={i} className="hour-label">{i}:00</div>
              ))}
            </div>
            {Object.entries(heatmapData).map(([date, hours]) => {
              const maxCount = Math.max(...Object.values(hours));
              return (
                <div key={date} className="heatmap-row">
                  <div className="date-label">{new Date(date).toLocaleDateString()}</div>
                  {Array.from({ length: 24 }, (_, hour) => {
                    const count = hours[hour] || 0;
                    const hourKey = `${date}T${hour.toString().padStart(2, '0')}:00:00`;
                    return (
                      <div
                        key={hour}
                        className={`heatmap-cell ${count > 0 ? 'has-events' : ''} ${selectedHour === hourKey ? 'selected' : ''}`}
                        style={{ background: count > 0 ? getHeatmapColor(count, maxCount) : '#f3f4f6' }}
                        onClick={() => count > 0 && setSelectedHour(hourKey)}
                        title={`${count} events at ${hour}:00`}
                      >
                        {count > 0 && <span className="cell-count">{count}</span>}
                      </div>
                    );
                  })}
                </div>
              );
            })}
          </div>

          {selectedHour && eventsByHour[selectedHour] && (
            <div className="selected-hour-events">
              <div className="selected-hour-header">
                <h4>Events at {new Date(selectedHour).toLocaleString()}</h4>
                <button className="close-btn" onClick={() => setSelectedHour(null)}>✕</button>
              </div>
              <div className="selected-events-list">
                {eventsByHour[selectedHour].map(event => (
                  <div
                    key={event.event_id}
                    className="selected-event"
                    style={{ borderLeftColor: getSeverityColor(event.severity) }}
                  >
                    <div className="selected-event-header">
                      <span>{getEventTypeIcon(event.event_type)} {event.event_type}</span>
                      <span
                        className="selected-severity"
                        style={{ background: getSeverityColor(event.severity) }}
                      >
                        {event.severity.toUpperCase()}
                      </span>
                    </div>
                    <div className="selected-event-desc">{event.description}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="heatmap-legend">
            <span className="legend-label">Activity Level:</span>
            <div className="legend-gradient">
              <div className="gradient-bar"></div>
              <div className="gradient-labels">
                <span>Low</span>
                <span>High</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
