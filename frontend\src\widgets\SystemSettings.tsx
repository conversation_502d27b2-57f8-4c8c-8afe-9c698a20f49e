import React, { useState } from 'react'
import {
  Settings, Database, Shield, Bell, Globe,
  Save, RefreshCw, AlertTriangle, Check,
  Server, Cloud, Key, Mail, Slack, Webhook
} from 'lucide-react'

interface SettingSection {
  id: string
  title: string
  icon: React.ReactNode
  description: string
}

const sections: SettingSection[] = [
  {
    id: 'general',
    title: 'General Settings',
    icon: <Settings size={20} />,
    description: 'System name, timezone, and basic configuration'
  },
  {
    id: 'engines',
    title: 'Engine Configuration',
    icon: <Server size={20} />,
    description: 'Configure the 5 engines and their parameters'
  },
  {
    id: 'database',
    title: 'Database',
    icon: <Database size={20} />,
    description: 'PostgreSQL and Redis connection settings'
  },
  {
    id: 'security',
    title: 'Security',
    icon: <Shield size={20} />,
    description: 'Authentication, encryption, and access control'
  },
  {
    id: 'notifications',
    title: 'Notifications',
    icon: <Bell size={20} />,
    description: 'Alert delivery channels and notification rules'
  },
  {
    id: 'integrations',
    title: 'Integrations',
    icon: <Globe size={20} />,
    description: 'External services and API connections'
  }
]

export const SystemSettings: React.FC = () => {
  const [activeSection, setActiveSection] = useState('general')
  const [hasChanges, setHasChanges] = useState(false)
  const [saving, setSaving] = useState(false)
  const [testingConnection, setTestingConnection] = useState<string | null>(null)

  const handleSave = async () => {
    setSaving(true)
    await new Promise(resolve => setTimeout(resolve, 1500))
    setSaving(false)
    setHasChanges(false)
  }

  const testConnection = async (service: string) => {
    setTestingConnection(service)
    await new Promise(resolve => setTimeout(resolve, 2000))
    setTestingConnection(null)
  }

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          System Name
        </label>
        <input
          type="text"
          defaultValue="SIEMLess v2.0"
          onChange={() => setHasChanges(true)}
          className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          System URL
        </label>
        <input
          type="text"
          defaultValue="https://siemless.local"
          onChange={() => setHasChanges(true)}
          className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Timezone
        </label>
        <select
          defaultValue="UTC"
          onChange={() => setHasChanges(true)}
          className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="UTC">UTC</option>
          <option value="America/New_York">Eastern Time</option>
          <option value="America/Chicago">Central Time</option>
          <option value="America/Denver">Mountain Time</option>
          <option value="America/Los_Angeles">Pacific Time</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Session Timeout (minutes)
        </label>
        <input
          type="number"
          defaultValue="480"
          onChange={() => setHasChanges(true)}
          className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
    </div>
  )

  const renderEngineSettings = () => (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
          <Server size={16} />
          Intelligence Engine (Port 8001)
        </h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm">Status</span>
            <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
              Running
            </span>
          </div>
          <div>
            <label className="text-sm text-gray-600">AI Model Priority</label>
            <select
              onChange={() => setHasChanges(true)}
              className="w-full mt-1 px-2 py-1 text-sm border rounded"
            >
              <option>Gemini Flash (Low Cost)</option>
              <option>GPT-4 Turbo</option>
              <option>Claude Opus</option>
            </select>
          </div>
        </div>
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h4 className="font-medium text-green-900 mb-2 flex items-center gap-2">
          <Database size={16} />
          Backend Engine (Port 8002)
        </h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm">Status</span>
            <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
              Running
            </span>
          </div>
          <div>
            <label className="text-sm text-gray-600">Storage Tier Thresholds</label>
            <input
              type="text"
              defaultValue="Hot: 7d, Warm: 30d, Cold: 90d"
              onChange={() => setHasChanges(true)}
              className="w-full mt-1 px-2 py-1 text-sm border rounded"
            />
          </div>
        </div>
      </div>

      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <h4 className="font-medium text-purple-900 mb-2 flex items-center gap-2">
          <Cloud size={16} />
          Ingestion Engine (Port 8003)
        </h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm">Status</span>
            <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
              Running
            </span>
          </div>
          <div>
            <label className="text-sm text-gray-600">Max Batch Size</label>
            <input
              type="number"
              defaultValue="1000"
              onChange={() => setHasChanges(true)}
              className="w-full mt-1 px-2 py-1 text-sm border rounded"
            />
          </div>
        </div>
      </div>
    </div>
  )

  const renderDatabaseSettings = () => (
    <div className="space-y-6">
      <div className="border rounded-lg p-4">
        <h4 className="font-medium mb-3">PostgreSQL Configuration</h4>
        <div className="space-y-3">
          <div>
            <label className="block text-sm text-gray-600 mb-1">Host</label>
            <input
              type="text"
              defaultValue="postgres"
              onChange={() => setHasChanges(true)}
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-600 mb-1">Port</label>
            <input
              type="text"
              defaultValue="5433"
              onChange={() => setHasChanges(true)}
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-600 mb-1">Database</label>
            <input
              type="text"
              defaultValue="siemless_v3"
              onChange={() => setHasChanges(true)}
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>
          <button
            onClick={() => testConnection('postgresql')}
            disabled={testingConnection === 'postgresql'}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {testingConnection === 'postgresql' ? (
              <span className="flex items-center gap-2">
                <RefreshCw size={16} className="animate-spin" />
                Testing...
              </span>
            ) : (
              'Test Connection'
            )}
          </button>
        </div>
      </div>

      <div className="border rounded-lg p-4">
        <h4 className="font-medium mb-3">Redis Configuration</h4>
        <div className="space-y-3">
          <div>
            <label className="block text-sm text-gray-600 mb-1">Host</label>
            <input
              type="text"
              defaultValue="redis"
              onChange={() => setHasChanges(true)}
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-600 mb-1">Port</label>
            <input
              type="text"
              defaultValue="6380"
              onChange={() => setHasChanges(true)}
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>
          <button
            onClick={() => testConnection('redis')}
            disabled={testingConnection === 'redis'}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {testingConnection === 'redis' ? (
              <span className="flex items-center gap-2">
                <RefreshCw size={16} className="animate-spin" />
                Testing...
              </span>
            ) : (
              'Test Connection'
            )}
          </button>
        </div>
      </div>
    </div>
  )

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start gap-2">
          <AlertTriangle className="text-yellow-600 mt-1" size={16} />
          <div>
            <p className="text-sm font-medium text-yellow-900">Security Notice</p>
            <p className="text-sm text-yellow-700 mt-1">
              Changes to security settings require admin approval and may affect user access.
            </p>
          </div>
        </div>
      </div>

      <div>
        <h4 className="font-medium mb-3">Authentication Provider</h4>
        <select
          defaultValue="keycloak"
          onChange={() => setHasChanges(true)}
          className="w-full px-3 py-2 border rounded-lg"
        >
          <option value="keycloak">Keycloak SSO</option>
          <option value="oauth">OAuth 2.0</option>
          <option value="saml">SAML 2.0</option>
          <option value="local">Local Authentication</option>
        </select>
      </div>

      <div>
        <h4 className="font-medium mb-3">Multi-Factor Authentication</h4>
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            defaultChecked
            onChange={() => setHasChanges(true)}
            className="rounded"
          />
          <span className="text-sm">Require MFA for all users</span>
        </label>
      </div>

      <div>
        <h4 className="font-medium mb-3">Session Management</h4>
        <div className="space-y-2">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              defaultChecked
              onChange={() => setHasChanges(true)}
              className="rounded"
            />
            <span className="text-sm">Single session per user</span>
          </label>
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              defaultChecked
              onChange={() => setHasChanges(true)}
              className="rounded"
            />
            <span className="text-sm">Auto-logout on idle</span>
          </label>
        </div>
      </div>
    </div>
  )

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div className="border rounded-lg p-4">
        <h4 className="font-medium mb-3 flex items-center gap-2">
          <Mail size={16} />
          Email Configuration
        </h4>
        <div className="space-y-3">
          <div>
            <label className="block text-sm text-gray-600 mb-1">SMTP Server</label>
            <input
              type="text"
              placeholder="smtp.gmail.com"
              onChange={() => setHasChanges(true)}
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-600 mb-1">Port</label>
            <input
              type="text"
              defaultValue="587"
              onChange={() => setHasChanges(true)}
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>
        </div>
      </div>

      <div className="border rounded-lg p-4">
        <h4 className="font-medium mb-3 flex items-center gap-2">
          <Slack size={16} />
          Slack Integration
        </h4>
        <div className="space-y-3">
          <div>
            <label className="block text-sm text-gray-600 mb-1">Webhook URL</label>
            <input
              type="text"
              placeholder="https://hooks.slack.com/services/..."
              onChange={() => setHasChanges(true)}
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-600 mb-1">Default Channel</label>
            <input
              type="text"
              placeholder="#security-alerts"
              onChange={() => setHasChanges(true)}
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>
        </div>
      </div>
    </div>
  )

  const renderIntegrationSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="border rounded-lg p-4">
          <h4 className="font-medium mb-3">AI Providers</h4>
          <div className="space-y-2">
            <div>
              <label className="text-sm text-gray-600">OpenAI API Key</label>
              <input
                type="password"
                placeholder="sk-..."
                onChange={() => setHasChanges(true)}
                className="w-full mt-1 px-2 py-1 text-sm border rounded"
              />
            </div>
            <div>
              <label className="text-sm text-gray-600">Anthropic API Key</label>
              <input
                type="password"
                placeholder="sk-ant-..."
                onChange={() => setHasChanges(true)}
                className="w-full mt-1 px-2 py-1 text-sm border rounded"
              />
            </div>
            <div>
              <label className="text-sm text-gray-600">Google API Key</label>
              <input
                type="password"
                placeholder="AIza..."
                onChange={() => setHasChanges(true)}
                className="w-full mt-1 px-2 py-1 text-sm border rounded"
              />
            </div>
          </div>
        </div>

        <div className="border rounded-lg p-4">
          <h4 className="font-medium mb-3">CTI Feeds</h4>
          <div className="space-y-2">
            <div>
              <label className="text-sm text-gray-600">OTX API Key</label>
              <input
                type="password"
                placeholder="Your OTX key"
                onChange={() => setHasChanges(true)}
                className="w-full mt-1 px-2 py-1 text-sm border rounded"
              />
            </div>
            <div>
              <label className="text-sm text-gray-600">VirusTotal API Key</label>
              <input
                type="password"
                placeholder="Your VT key"
                onChange={() => setHasChanges(true)}
                className="w-full mt-1 px-2 py-1 text-sm border rounded"
              />
            </div>
            <div>
              <label className="text-sm text-gray-600">AbuseIPDB Key</label>
              <input
                type="password"
                placeholder="Your AbuseIPDB key"
                onChange={() => setHasChanges(true)}
                className="w-full mt-1 px-2 py-1 text-sm border rounded"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="border rounded-lg p-4">
        <h4 className="font-medium mb-3">SIEM Integrations</h4>
        <div className="grid grid-cols-2 gap-3">
          <label className="flex items-center gap-2">
            <input type="checkbox" className="rounded" onChange={() => setHasChanges(true)} />
            <span className="text-sm">Splunk</span>
          </label>
          <label className="flex items-center gap-2">
            <input type="checkbox" className="rounded" onChange={() => setHasChanges(true)} />
            <span className="text-sm">Elastic</span>
          </label>
          <label className="flex items-center gap-2">
            <input type="checkbox" className="rounded" onChange={() => setHasChanges(true)} />
            <span className="text-sm">Azure Sentinel</span>
          </label>
          <label className="flex items-center gap-2">
            <input type="checkbox" className="rounded" onChange={() => setHasChanges(true)} />
            <span className="text-sm">QRadar</span>
          </label>
        </div>
      </div>
    </div>
  )

  const renderContent = () => {
    switch (activeSection) {
      case 'general':
        return renderGeneralSettings()
      case 'engines':
        return renderEngineSettings()
      case 'database':
        return renderDatabaseSettings()
      case 'security':
        return renderSecuritySettings()
      case 'notifications':
        return renderNotificationSettings()
      case 'integrations':
        return renderIntegrationSettings()
      default:
        return renderGeneralSettings()
    }
  }

  return (
    <div className="flex h-full bg-gray-50">
      {/* Sidebar */}
      <div className="w-64 bg-white border-r">
        <div className="p-4">
          <h2 className="text-xl font-bold mb-4">System Settings</h2>
          <nav className="space-y-1">
            {sections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`w-full text-left px-3 py-2 rounded-lg flex items-center gap-3 transition-colors ${
                  activeSection === section.id
                    ? 'bg-blue-50 text-blue-600'
                    : 'hover:bg-gray-50'
                }`}
              >
                {section.icon}
                <div>
                  <div className="font-medium text-sm">{section.title}</div>
                  <div className="text-xs text-gray-500">{section.description}</div>
                </div>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <h3 className="text-xl font-semibold">
              {sections.find(s => s.id === activeSection)?.title}
            </h3>
            <p className="text-gray-600">
              {sections.find(s => s.id === activeSection)?.description}
            </p>
          </div>

          {/* Settings Content */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            {renderContent()}
          </div>

          {/* Save Bar */}
          {hasChanges && (
            <div className="fixed bottom-0 left-64 right-0 bg-white border-t p-4">
              <div className="flex items-center justify-between max-w-4xl mx-auto">
                <p className="text-sm text-gray-600">You have unsaved changes</p>
                <div className="flex gap-2">
                  <button
                    onClick={() => setHasChanges(false)}
                    className="px-4 py-2 border rounded-lg hover:bg-gray-50"
                  >
                    Discard
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={saving}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
                  >
                    {saving ? (
                      <>
                        <RefreshCw size={16} className="animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save size={16} />
                        Save Changes
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default SystemSettings