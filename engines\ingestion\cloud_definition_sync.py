"""
Cloud Definition Sync Module
Extends GitHubPatternSync to support all definition types for cloud-based updates

This module enables automatic updates for:
1. Log source definitions (vendors, quality scores, telemetry patterns)
2. SIEM platform definitions (field mappings, query syntax)
3. Detection coverage maps (attack-to-source mappings)
4. Telemetry detection patterns (alert-only vs full telemetry)
5. Sigma rules (universal detection rules)
6. MITRE ATT&CK framework updates
7. Crystallized patterns (shared intelligence)
8. AI cost models (pricing and performance)
"""

import json
import asyncio
import aiohttp
import yaml
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import hashlib
import logging


def db_fetch(connection, query: str, *params):
    """Helper to execute fetch queries with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    results = cursor.fetchall()
    cursor.close()
    return results


def db_execute(connection, query: str, *params):
    """Helper to execute non-query commands with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    cursor.close()


def db_fetchval(connection, query: str, *params):
    """Helper to fetch a single value with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    result = cursor.fetchone()
    cursor.close()
    return result[0] if result else None


def db_fetchrow(connection, query: str, *params):
    """Helper to fetch a single row with psycopg2"""
    cursor = connection.cursor()
    cursor.execute(query, params if params else None)
    result = cursor.fetchone()
    cursor.close()
    return result


logger = logging.getLogger(__name__)


class CloudDefinitionSync:
    """
    Comprehensive cloud-based definition synchronization

    Supports multiple definition types with validation, versioning, and rollback
    """

    # Definition type configurations
    DEFINITION_TYPES = {
        'log_source': {
            'path': 'log_sources/',
            'extensions': ['.yaml', '.yml', '.json'],
            'validator': '_validate_log_source',
            'deployer': '_deploy_log_source',
            'table': 'log_source_definitions',
            'update_channel': 'backend.log_source_updated'
        },
        'telemetry_pattern': {
            'path': 'telemetry_patterns/',
            'extensions': ['.yaml', '.yml', '.json'],
            'validator': '_validate_telemetry_pattern',
            'deployer': '_deploy_telemetry_pattern',
            'table': 'telemetry_pattern_definitions',
            'update_channel': 'backend.telemetry_pattern_updated'
        },
        'detection_coverage': {
            'path': 'detection_coverage/',
            'extensions': ['.yaml', '.yml', '.json'],
            'validator': '_validate_coverage_map',
            'deployer': '_deploy_coverage_map',
            'table': 'detection_coverage_maps',
            'update_channel': 'backend.coverage_updated'
        },
        'siem_definition': {
            'path': 'siem_definitions/',
            'extensions': ['.yaml', '.yml'],
            'validator': '_validate_siem_definition',
            'deployer': '_deploy_siem_definition',
            'table': 'siem_platform_definitions',
            'update_channel': 'backend.siem_updated'
        },
        'sigma_rule': {
            'path': 'sigma_rules/',
            'extensions': ['.yaml', '.yml'],
            'validator': '_validate_sigma_rule',
            'deployer': '_deploy_sigma_rule',
            'table': 'sigma_rules',
            'update_channel': 'backend.sigma_rule_updated'
        },
        'crystallized_pattern': {
            'path': 'patterns/',
            'extensions': ['.json', '.yaml'],
            'validator': '_validate_crystallized_pattern',
            'deployer': '_deploy_crystallized_pattern',
            'table': 'crystallized_patterns',
            'update_channel': 'intelligence.pattern_updated'
        },
        'mitre_attack': {
            'path': 'mitre/',
            'extensions': ['.json'],
            'validator': '_validate_mitre_data',
            'deployer': '_deploy_mitre_data',
            'table': 'mitre_attack_framework',
            'update_channel': 'backend.mitre_updated'
        },
        'ai_cost_model': {
            'path': 'ai_models/',
            'extensions': ['.yaml', '.yml'],
            'validator': '_validate_cost_model',
            'deployer': '_deploy_cost_model',
            'table': 'ai_cost_models',
            'update_channel': 'intelligence.cost_model_updated'
        }
    }

    # Update channel configurations
    UPDATE_CHANNELS = {
        'stable': {
            'description': 'Production-ready, tested definitions',
            'frequency': timedelta(days=1),
            'auto_deploy': True
        },
        'beta': {
            'description': 'Testing channel with latest features',
            'frequency': timedelta(hours=6),
            'auto_deploy': False
        },
        'community': {
            'description': 'Community-contributed definitions',
            'frequency': timedelta(hours=12),
            'auto_deploy': False
        },
        'emergency': {
            'description': 'Critical security updates',
            'frequency': timedelta(minutes=15),
            'auto_deploy': True
        }
    }

    def __init__(self, redis_client, db_connection, logger):
        self.redis = redis_client
        self.db = db_connection
        self.logger = logger or logging.getLogger(__name__)

        # GitHub configuration
        self.github_api_base = "https://api.github.com"
        self.official_repo = "SIEMLess/cloud-definitions"
        self.headers = {
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "SIEMLess-CloudSync/2.0"
        }

        # Subscription configuration
        self.subscribed_channels = ['stable', 'emergency']
        self.subscribed_types = list(self.DEFINITION_TYPES.keys())

        # Sync state
        self.last_sync = {}
        self.sync_stats = {}

    async def initialize(self, github_token: Optional[str] = None,
                        subscribed_channels: Optional[List[str]] = None):
        """Initialize cloud sync with authentication and subscriptions"""
        if github_token:
            self.headers["Authorization"] = f"token {github_token}"
            self.logger.info("Cloud sync initialized with GitHub authentication")

        if subscribed_channels:
            self.subscribed_channels = subscribed_channels

        await self._ensure_schema()
        self.logger.info(f"Cloud sync initialized - Channels: {self.subscribed_channels}")

    async def _ensure_schema(self):
        """Ensure database schema exists for all definition types"""
        try:
            # Definition metadata table
            db_execute(self.db, """
                CREATE TABLE IF NOT EXISTS cloud_definitions (
                    id SERIAL PRIMARY KEY,
                    definition_type VARCHAR(50) NOT NULL,
                    definition_id VARCHAR(255) NOT NULL,
                    version VARCHAR(50) NOT NULL,
                    content JSONB NOT NULL,
                    source_repo VARCHAR(500),
                    source_file VARCHAR(500),
                    commit_sha VARCHAR(40),
                    channel VARCHAR(50) DEFAULT 'stable',
                    validated BOOLEAN DEFAULT FALSE,
                    validation_errors TEXT,
                    deployed BOOLEAN DEFAULT FALSE,
                    deployed_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(definition_type, definition_id, version)
                )
            """)

            # Sync history table
            db_execute(self.db, """
                CREATE TABLE IF NOT EXISTS cloud_sync_history (
                    id SERIAL PRIMARY KEY,
                    sync_type VARCHAR(50) NOT NULL,
                    channel VARCHAR(50) NOT NULL,
                    definitions_found INTEGER DEFAULT 0,
                    definitions_validated INTEGER DEFAULT 0,
                    definitions_deployed INTEGER DEFAULT 0,
                    errors JSONB,
                    sync_started TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    sync_completed TIMESTAMP,
                    status VARCHAR(50) DEFAULT 'in_progress'
                )
            """)

            # Subscription configuration table
            db_execute(self.db, """
                CREATE TABLE IF NOT EXISTS cloud_subscriptions (
                    id SERIAL PRIMARY KEY,
                    definition_type VARCHAR(50) NOT NULL,
                    channel VARCHAR(50) NOT NULL,
                    auto_deploy BOOLEAN DEFAULT FALSE,
                    last_sync TIMESTAMP,
                    enabled BOOLEAN DEFAULT TRUE,
                    UNIQUE(definition_type, channel)
                )
            """)

            self.logger.info("Cloud definition schema initialized")

        except Exception as e:
            self.logger.error(f"Failed to create cloud sync schema: {e}")
            raise

    async def sync_all_definitions(self, force: bool = False) -> Dict[str, Any]:
        """
        Sync all subscribed definition types from all channels

        Args:
            force: Force sync even if not due

        Returns:
            Dict with sync results per type and channel
        """
        sync_results = {
            'started': datetime.utcnow().isoformat(),
            'types': {},
            'total_updated': 0,
            'errors': []
        }

        # Record sync start
        sync_id = await self._record_sync_start('all', 'all')

        try:
            # Sync each definition type
            for def_type in self.subscribed_types:
                type_results = await self.sync_definition_type(def_type, force)
                sync_results['types'][def_type] = type_results
                sync_results['total_updated'] += type_results.get('deployed', 0)

            sync_results['status'] = 'completed'

        except Exception as e:
            sync_results['status'] = 'error'
            sync_results['errors'].append(str(e))
            self.logger.error(f"Sync failed: {e}")

        finally:
            await self._record_sync_complete(sync_id, sync_results)

        return sync_results

    async def sync_definition_type(self, def_type: str, force: bool = False) -> Dict[str, Any]:
        """
        Sync a specific definition type from all subscribed channels

        Args:
            def_type: Definition type to sync
            force: Force sync even if not due

        Returns:
            Sync results for this type
        """
        if def_type not in self.DEFINITION_TYPES:
            raise ValueError(f"Unknown definition type: {def_type}")

        type_config = self.DEFINITION_TYPES[def_type]
        results = {
            'type': def_type,
            'channels': {},
            'found': 0,
            'validated': 0,
            'deployed': 0,
            'errors': []
        }

        # Sync from each channel
        for channel in self.subscribed_channels:
            channel_results = await self._sync_channel(def_type, channel, force)
            results['channels'][channel] = channel_results
            results['found'] += channel_results.get('found', 0)
            results['validated'] += channel_results.get('validated', 0)
            results['deployed'] += channel_results.get('deployed', 0)
            results['errors'].extend(channel_results.get('errors', []))

        return results

    async def _sync_channel(self, def_type: str, channel: str, force: bool) -> Dict[str, Any]:
        """Sync definitions from a specific channel"""
        type_config = self.DEFINITION_TYPES[def_type]
        channel_config = self.UPDATE_CHANNELS.get(channel, {})

        results = {
            'channel': channel,
            'found': 0,
            'validated': 0,
            'deployed': 0,
            'errors': []
        }

        try:
            # Check if sync is needed
            if not force and not await self._should_sync(def_type, channel):
                results['status'] = 'skipped'
                return results

            # Fetch definition files from GitHub
            path = f"{channel}/{type_config['path']}"
            definition_files = await self._fetch_definition_files(path, type_config['extensions'])
            results['found'] = len(definition_files)

            # Process each definition file
            for file_info in definition_files:
                try:
                    # Download content
                    content = await self._fetch_file_content_from_url(file_info['download_url'])
                    if not content:
                        continue

                    # Parse definition
                    definition = self._parse_content(content, file_info['name'])
                    if not definition:
                        continue

                    # Validate definition
                    validator = getattr(self, type_config['validator'])
                    is_valid, errors = await validator(definition)

                    if is_valid:
                        results['validated'] += 1

                        # Deploy if auto-deploy enabled
                        if channel_config.get('auto_deploy', False):
                            deployer = getattr(self, type_config['deployer'])
                            deployed = await deployer(
                                definition,
                                def_type,
                                file_info['path'],
                                file_info.get('sha', 'unknown'),
                                channel
                            )
                            if deployed:
                                results['deployed'] += 1
                    else:
                        results['errors'].append({
                            'file': file_info['name'],
                            'errors': errors
                        })

                except Exception as e:
                    results['errors'].append({
                        'file': file_info.get('name', 'unknown'),
                        'error': str(e)
                    })

            # Update last sync time
            await self._update_last_sync(def_type, channel)
            results['status'] = 'completed'

        except Exception as e:
            results['status'] = 'error'
            results['errors'].append(str(e))
            self.logger.error(f"Channel sync failed for {def_type}/{channel}: {e}")

        return results

    async def _should_sync(self, def_type: str, channel: str) -> bool:
        """Check if sync is needed based on frequency"""
        try:
            result = db_fetchrow(self.db, """
                SELECT last_sync FROM cloud_subscriptions
                WHERE definition_type = %s AND channel = %s AND enabled = TRUE
            """, def_type, channel)

            if not result or not result['last_sync']:
                return True

            channel_config = self.UPDATE_CHANNELS.get(channel, {})
            frequency = channel_config.get('frequency', timedelta(days=1))

            time_since_sync = datetime.utcnow() - result['last_sync']
            return time_since_sync >= frequency

        except Exception as e:
            self.logger.error(f"Error checking sync schedule: {e}")
            return True

    async def _fetch_definition_files(self, path: str, extensions: List[str]) -> List[Dict]:
        """Fetch list of definition files from GitHub repository"""
        files = []

        try:
            url = f"{self.github_api_base}/repos/{self.official_repo}/contents/{path}"

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 200:
                        contents = await response.json()

                        for item in contents:
                            if item['type'] == 'file':
                                if any(item['name'].endswith(ext) for ext in extensions):
                                    files.append(item)
                    elif response.status == 404:
                        self.logger.warning(f"Path not found: {path}")
                    else:
                        self.logger.error(f"GitHub API error {response.status} for {path}")

        except Exception as e:
            self.logger.error(f"Failed to fetch definition files from {path}: {e}")

        return files

    async def _fetch_file_content_from_url(self, url: str) -> Optional[str]:
        """Fetch raw file content from URL"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        return await response.text()
        except Exception as e:
            self.logger.error(f"Failed to fetch file content: {e}")
        return None

    def _parse_content(self, content: str, filename: str) -> Optional[Dict]:
        """Parse YAML or JSON content"""
        try:
            if filename.endswith('.json'):
                return json.loads(content)
            elif filename.endswith(('.yaml', '.yml')):
                return yaml.safe_load(content)
        except Exception as e:
            self.logger.error(f"Failed to parse {filename}: {e}")
        return None

    # ===== VALIDATORS =====

    async def _validate_log_source(self, definition: Dict) -> Tuple[bool, List[str]]:
        """Validate log source definition"""
        errors = []

        required = ['vendor', 'product', 'type', 'quality_score']
        for field in required:
            if field not in definition:
                errors.append(f"Missing required field: {field}")

        # Validate quality score
        score = definition.get('quality_score', 0)
        if not isinstance(score, (int, float)) or score < 0 or score > 100:
            errors.append(f"Invalid quality_score: {score} (must be 0-100)")

        # Validate detection patterns if present
        if 'detection_patterns' in definition:
            for pattern in definition['detection_patterns']:
                if 'regex' in pattern:
                    try:
                        re.compile(pattern['regex'])
                    except re.error as e:
                        errors.append(f"Invalid regex in detection_patterns: {e}")

        return (len(errors) == 0, errors)

    async def _validate_telemetry_pattern(self, definition: Dict) -> Tuple[bool, List[str]]:
        """Validate telemetry detection pattern"""
        errors = []

        required = ['pattern_id', 'mode', 'indicators']
        for field in required:
            if field not in definition:
                errors.append(f"Missing required field: {field}")

        # Validate mode
        valid_modes = ['alert_only', 'full_telemetry', 'partial']
        if definition.get('mode') not in valid_modes:
            errors.append(f"Invalid mode: {definition.get('mode')}")

        return (len(errors) == 0, errors)

    async def _validate_coverage_map(self, definition: Dict) -> Tuple[bool, List[str]]:
        """Validate detection coverage map"""
        errors = []

        required = ['attack_type', 'required_sources', 'detection_confidence']
        for field in required:
            if field not in definition:
                errors.append(f"Missing required field: {field}")

        return (len(errors) == 0, errors)

    async def _validate_siem_definition(self, definition: Dict) -> Tuple[bool, List[str]]:
        """Validate SIEM platform definition"""
        errors = []

        if 'platform' not in definition:
            errors.append("Missing 'platform' section")
            return (False, errors)

        platform = definition['platform']
        required = ['name', 'query_language']
        for field in required:
            if field not in platform:
                errors.append(f"Missing platform field: {field}")

        return (len(errors) == 0, errors)

    async def _validate_sigma_rule(self, definition: Dict) -> Tuple[bool, List[str]]:
        """Validate Sigma rule format"""
        errors = []

        required = ['title', 'logsource', 'detection']
        for field in required:
            if field not in definition:
                errors.append(f"Missing required field: {field}")

        # Validate logsource
        if 'logsource' in definition:
            logsource = definition['logsource']
            if not isinstance(logsource, dict):
                errors.append("logsource must be a dictionary")

        return (len(errors) == 0, errors)

    async def _validate_crystallized_pattern(self, definition: Dict) -> Tuple[bool, List[str]]:
        """Validate crystallized pattern for sharing"""
        errors = []

        required = ['pattern_id', 'pattern_type', 'pattern_data']
        for field in required:
            if field not in definition:
                errors.append(f"Missing required field: {field}")

        return (len(errors) == 0, errors)

    async def _validate_mitre_data(self, definition: Dict) -> Tuple[bool, List[str]]:
        """Validate MITRE ATT&CK data"""
        errors = []

        if 'type' not in definition:
            errors.append("Missing 'type' field")

        valid_types = ['attack-pattern', 'intrusion-set', 'malware', 'tool']
        if definition.get('type') not in valid_types:
            errors.append(f"Invalid type: {definition.get('type')}")

        return (len(errors) == 0, errors)

    async def _validate_cost_model(self, definition: Dict) -> Tuple[bool, List[str]]:
        """Validate AI cost model definition"""
        errors = []

        required = ['model_name', 'cost_per_1k_tokens', 'tier']
        for field in required:
            if field not in definition:
                errors.append(f"Missing required field: {field}")

        return (len(errors) == 0, errors)

    # ===== DEPLOYERS =====

    async def _deploy_log_source(self, definition: Dict, def_type: str,
                                 file_path: str, commit_sha: str, channel: str) -> bool:
        """Deploy log source definition"""
        try:
            definition_id = f"{definition['vendor']}-{definition['product']}"
            version = definition.get('version', self._generate_version(definition))

            # Store in cloud_definitions
            db_execute(self.db, """
                INSERT INTO cloud_definitions
                (definition_type, definition_id, version, content, source_file, commit_sha, channel, validated, deployed)
                VALUES (%s, %s, %s, %s, %s, %s, %s, TRUE, TRUE)
                ON CONFLICT (definition_type, definition_id, version) DO UPDATE
                SET content = %s, deployed = TRUE, deployed_at = NOW()
            """, def_type, definition_id, version, json.dumps(definition), file_path, commit_sha, channel)

            # Publish update event
            await self.redis.publish(
                self.DEFINITION_TYPES[def_type]['update_channel'],
                json.dumps({
                    'definition_id': definition_id,
                    'version': version,
                    'channel': channel,
                    'content': definition
                })
            )

            self.logger.info(f"Deployed log source: {definition_id} v{version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deploy log source: {e}")
            return False

    async def _deploy_telemetry_pattern(self, definition: Dict, def_type: str,
                                       file_path: str, commit_sha: str, channel: str) -> bool:
        """Deploy telemetry detection pattern"""
        try:
            pattern_id = definition['pattern_id']

            # Store in Redis for fast lookup
            cache_key = f"telemetry_pattern:{pattern_id}"
            await self.redis.set(cache_key, json.dumps(definition), ex=86400)  # 24h TTL

            # Store in database
            db_execute(self.db, """
                INSERT INTO cloud_definitions
                (definition_type, definition_id, version, content, source_file, commit_sha, channel, validated, deployed)
                VALUES (%s, %s, %s, %s, %s, %s, %s, TRUE, TRUE)
                ON CONFLICT (definition_type, definition_id, version) DO UPDATE
                SET content = %s, deployed = TRUE, deployed_at = NOW()
            """, def_type, pattern_id, definition.get('version', '1.0'),
                json.dumps(definition), file_path, commit_sha, channel)

            self.logger.info(f"Deployed telemetry pattern: {pattern_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deploy telemetry pattern: {e}")
            return False

    async def _deploy_coverage_map(self, definition: Dict, def_type: str,
                                   file_path: str, commit_sha: str, channel: str) -> bool:
        """Deploy detection coverage map"""
        try:
            attack_type = definition['attack_type']

            db_execute(self.db, """
                INSERT INTO cloud_definitions
                (definition_type, definition_id, version, content, source_file, commit_sha, channel, validated, deployed)
                VALUES (%s, %s, %s, %s, %s, %s, %s, TRUE, TRUE)
                ON CONFLICT (definition_type, definition_id, version) DO UPDATE
                SET content = %s, deployed = TRUE, deployed_at = NOW()
            """, def_type, attack_type, definition.get('version', '1.0'),
                json.dumps(definition), file_path, commit_sha, channel)

            self.logger.info(f"Deployed coverage map: {attack_type}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deploy coverage map: {e}")
            return False

    async def _deploy_siem_definition(self, definition: Dict, def_type: str,
                                     file_path: str, commit_sha: str, channel: str) -> bool:
        """Deploy SIEM platform definition"""
        try:
            siem_name = definition['platform']['name']

            # Write to siem_definitions directory
            siem_def_path = Path(__file__).parent.parent.parent / 'siem_definitions'
            siem_def_path.mkdir(exist_ok=True)

            siem_file = siem_def_path / f"{siem_name}.yaml"
            with open(siem_file, 'w') as f:
                yaml.dump(definition, f, default_flow_style=False)

            self.logger.info(f"Deployed SIEM definition: {siem_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deploy SIEM definition: {e}")
            return False

    async def _deploy_sigma_rule(self, definition: Dict, def_type: str,
                                file_path: str, commit_sha: str, channel: str) -> bool:
        """Deploy Sigma rule"""
        try:
            rule_id = definition.get('id', hashlib.md5(json.dumps(definition).encode()).hexdigest())

            db_execute(self.db, """
                INSERT INTO cloud_definitions
                (definition_type, definition_id, version, content, source_file, commit_sha, channel, validated, deployed)
                VALUES (%s, %s, %s, %s, %s, %s, %s, TRUE, TRUE)
                ON CONFLICT (definition_type, definition_id, version) DO UPDATE
                SET content = %s, deployed = TRUE, deployed_at = NOW()
            """, def_type, rule_id, definition.get('date', '1.0'),
                json.dumps(definition), file_path, commit_sha, channel)

            self.logger.info(f"Deployed Sigma rule: {definition.get('title', rule_id)}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deploy Sigma rule: {e}")
            return False

    async def _deploy_crystallized_pattern(self, definition: Dict, def_type: str,
                                          file_path: str, commit_sha: str, channel: str) -> bool:
        """Deploy shared crystallized pattern"""
        try:
            pattern_id = definition['pattern_id']

            # Deploy directly to pattern library
            await self.redis.publish('intelligence.pattern_updated', json.dumps({
                'pattern_id': pattern_id,
                'pattern_type': definition['pattern_type'],
                'pattern_data': definition['pattern_data'],
                'source': f'cloud:{channel}'
            }))

            self.logger.info(f"Deployed crystallized pattern: {pattern_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deploy crystallized pattern: {e}")
            return False

    async def _deploy_mitre_data(self, definition: Dict, def_type: str,
                                file_path: str, commit_sha: str, channel: str) -> bool:
        """Deploy MITRE ATT&CK data"""
        try:
            # MITRE data deployment is handled by dedicated update manager
            await self.redis.publish('backend.mitre_updated', json.dumps(definition))

            self.logger.info(f"Deployed MITRE data: {definition.get('name', 'unknown')}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deploy MITRE data: {e}")
            return False

    async def _deploy_cost_model(self, definition: Dict, def_type: str,
                                file_path: str, commit_sha: str, channel: str) -> bool:
        """Deploy AI cost model"""
        try:
            model_name = definition['model_name']

            # Update cost model in Redis
            await self.redis.hset('ai_cost_models', model_name, json.dumps(definition))

            # Notify intelligence engine
            await self.redis.publish('intelligence.cost_model_updated', json.dumps({
                'model': model_name,
                'cost_per_1k': definition['cost_per_1k_tokens'],
                'tier': definition['tier']
            }))

            self.logger.info(f"Deployed cost model: {model_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deploy cost model: {e}")
            return False

    # ===== UTILITY METHODS =====

    def _generate_version(self, definition: Dict) -> str:
        """Generate version from content hash"""
        content_hash = hashlib.md5(
            json.dumps(definition, sort_keys=True).encode()
        ).hexdigest()[:8]
        timestamp = datetime.utcnow().strftime("%Y%m%d")
        return f"{timestamp}-{content_hash}"

    async def _record_sync_start(self, sync_type: str, channel: str) -> int:
        """Record sync start in history"""
        result = db_fetchrow(self.db, """
            INSERT INTO cloud_sync_history (sync_type, channel, status)
            VALUES (%s, %s, 'in_progress')
            RETURNING id
        """, sync_type, channel)
        return result['id']

    async def _record_sync_complete(self, sync_id: int, results: Dict):
        """Record sync completion"""
        db_execute(self.db, """
            UPDATE cloud_sync_history
            SET status = %s,
                definitions_found = %s,
                definitions_validated = %s,
                definitions_deployed = %s,
                errors = %s,
                sync_completed = NOW()
            WHERE id = %s
        """, sync_id, results.get('status', 'completed'),
            results.get('total_updated', 0),
            results.get('total_updated', 0),
            results.get('total_updated', 0),
            json.dumps(results.get('errors', [])))

    async def _update_last_sync(self, def_type: str, channel: str):
        """Update last sync timestamp"""
        db_execute(self.db, """
            INSERT INTO cloud_subscriptions (definition_type, channel, last_sync)
            VALUES (%s, %s, NOW())
            ON CONFLICT (definition_type, channel) DO UPDATE
            SET last_sync = NOW()
        """, def_type, channel)

    async def subscribe_to_channel(self, channel: str, auto_deploy: bool = False):
        """Subscribe to an update channel"""
        if channel not in self.UPDATE_CHANNELS:
            raise ValueError(f"Unknown channel: {channel}")

        if channel not in self.subscribed_channels:
            self.subscribed_channels.append(channel)

        # Update all definition type subscriptions
        for def_type in self.subscribed_types:
            db_execute(self.db, """
                INSERT INTO cloud_subscriptions (definition_type, channel, auto_deploy, enabled)
                VALUES (%s, %s, %s, TRUE)
                ON CONFLICT (definition_type, channel) DO UPDATE
                SET enabled = TRUE, auto_deploy = %s
            """, def_type, channel, auto_deploy)

        self.logger.info(f"Subscribed to channel: {channel} (auto_deploy={auto_deploy})")

    async def unsubscribe_from_channel(self, channel: str):
        """Unsubscribe from an update channel"""
        if channel in self.subscribed_channels:
            self.subscribed_channels.remove(channel)

        db_execute(self.db, """
            UPDATE cloud_subscriptions
            SET enabled = FALSE
            WHERE channel = %s
        """, channel)

        self.logger.info(f"Unsubscribed from channel: {channel}")

    async def get_sync_status(self) -> Dict[str, Any]:
        """Get current sync status and statistics"""
        status = {
            'subscribed_channels': self.subscribed_channels,
            'subscribed_types': self.subscribed_types,
            'last_syncs': {},
            'recent_updates': []
        }

        # Get last sync times
        rows = db_fetch(self.db, """
            SELECT definition_type, channel, last_sync
            FROM cloud_subscriptions
            WHERE enabled = TRUE
            ORDER BY last_sync DESC
        """)

        for row in rows:
            key = f"{row['definition_type']}/{row['channel']}"
            status['last_syncs'][key] = row['last_sync'].isoformat() if row['last_sync'] else None

        # Get recent sync history
        history = db_fetch(self.db, """
            SELECT sync_type, channel, status, definitions_deployed, sync_completed
            FROM cloud_sync_history
            ORDER BY sync_completed DESC
            LIMIT 10
        """)

        for row in history:
            status['recent_updates'].append({
                'type': row['sync_type'],
                'channel': row['channel'],
                'status': row['status'],
                'deployed': row['definitions_deployed'],
                'completed': row['sync_completed'].isoformat() if row['sync_completed'] else None
            })

        return status
