#!/usr/bin/env python3
"""
Script to generate Dockerfiles for all SIEMLess v2.0 engines
"""

import os
from pathlib import Path

# Engine configurations
ENGINES = {
    'parser': {
        'description': 'Parser Engine',
        'ports': [],
        'health_check': 'redis-cli -h $REDIS_HOST ping || exit 1'
    },
    'entity_extractor': {
        'description': 'Entity Extraction Engine',
        'ports': [],
        'health_check': 'redis-cli -h $REDIS_HOST ping || exit 1'
    },
    'use_case_context': {
        'description': 'Use Case Context Engine',
        'ports': [],
        'health_check': 'redis-cli -h $REDIS_HOST ping || exit 1'
    },
    'graph_builder': {
        'description': 'Graph Builder Engine',
        'ports': [],
        'health_check': 'redis-cli -h $REDIS_HOST ping || exit 1'
    },
    'detection': {
        'description': 'Detection Engine',
        'ports': [],
        'health_check': 'redis-cli -h $REDIS_HOST ping || exit 1'
    },
    'pattern_manager': {
        'description': 'Pattern Manager',
        'ports': [],
        'health_check': 'redis-cli -h $REDIS_HOST ping || exit 1'
    },
    'ai_consensus': {
        'description': 'AI Consensus Engine',
        'ports': [],
        'health_check': 'redis-cli -h $REDIS_HOST ping || exit 1'
    },
    'librarian': {
        'description': 'Librarian Engine',
        'ports': [],
        'health_check': 'redis-cli -h $REDIS_HOST ping || exit 1'
    }
}

DOCKERFILE_TEMPLATE = """# {description} Dockerfile
FROM siemless-v2-base:latest

# Copy {description} specific code
COPY engines/{engine_name}/ /app/engines/{engine_name}/

{ports_section}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD {health_check}

# Set the command to run {description}
CMD ["python", "engines/{engine_name}/{engine_name}.py"]
"""

def create_dockerfiles():
    """Create Dockerfiles for all engines"""
    docker_dir = Path('docker')
    docker_dir.mkdir(exist_ok=True)

    for engine_name, config in ENGINES.items():
        # Skip if already exists
        dockerfile_path = docker_dir / f"Dockerfile.{engine_name}"
        if dockerfile_path.exists():
            print(f"Skipping {engine_name} - Dockerfile already exists")
            continue

        # Generate ports section
        ports_section = ""
        if config['ports']:
            ports_line = " ".join(str(p) for p in config['ports'])
            ports_section = f"\n# Expose ports\nEXPOSE {ports_line}\n"

        # Generate Dockerfile content
        dockerfile_content = DOCKERFILE_TEMPLATE.format(
            description=config['description'],
            engine_name=engine_name,
            ports_section=ports_section,
            health_check=config['health_check']
        )

        # Write Dockerfile
        with open(dockerfile_path, 'w') as f:
            f.write(dockerfile_content)

        print(f"Created Dockerfile.{engine_name}")

if __name__ == '__main__':
    create_dockerfiles()
    print("All Dockerfiles created successfully!")