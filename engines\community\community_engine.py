"""
Community Engine - Configurable GitHub Rule Repository Integration

Purpose: Integrate open-source detection rules from community GitHub repositories
- Configurable repository sources (not hardcoded)
- Automatic rule syncing
- Format detection and conversion
- Quality scoring
- Deduplication against existing rules
"""

import asyncio
import json
import os
import requests
import base64
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import sys
import yaml
import hashlib

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from base_engine import BaseEngine


class GitHubRepository:
    """Configuration for a community GitHub repository"""

    def __init__(self, config: Dict[str, Any]):
        self.repo_url = config['repo_url']  # e.g., "https://github.com/SigmaHQ/sigma"
        self.name = config.get('name', self.repo_url.split('/')[-1])
        self.enabled = config.get('enabled', True)
        self.rule_paths = config.get('rule_paths', ['rules/'])  # Paths within repo containing rules
        self.rule_formats = config.get('rule_formats', ['sigma'])  # sigma, splunk, elastic, etc.
        self.sync_frequency = config.get('sync_frequency', 'daily')  # hourly, daily, weekly
        self.priority = config.get('priority', 50)  # 0-100, higher = more trusted
        self.tags = config.get('tags', [])  # Custom tags for organization
        self.last_sync = None
        self.rule_count = 0

        # Extract owner and repo from URL
        parts = self.repo_url.rstrip('/').split('/')
        self.owner = parts[-2]
        self.repo = parts[-1]

    def to_dict(self) -> Dict[str, Any]:
        return {
            'repo_url': self.repo_url,
            'name': self.name,
            'enabled': self.enabled,
            'rule_paths': self.rule_paths,
            'rule_formats': self.rule_formats,
            'sync_frequency': self.sync_frequency,
            'priority': self.priority,
            'tags': self.tags,
            'last_sync': self.last_sync.isoformat() if self.last_sync else None,
            'rule_count': self.rule_count,
            'owner': self.owner,
            'repo': self.repo
        }


class CommunityEngine(BaseEngine):
    """
    Community Engine: Open-source rule integration

    Features:
    - Configurable GitHub repository sources
    - Automatic rule syncing
    - Format detection and conversion
    - Deduplication
    - Quality scoring
    - Community rule library management
    """

    def __init__(self):
        super().__init__("community")

        # GitHub API configuration
        self.github_token = os.getenv('GITHUB_TOKEN')  # Optional, increases rate limits
        self.github_api_base = "https://api.github.com"

        # Repository configuration
        self.repositories: List[GitHubRepository] = []
        self.load_repository_config()

        # Community rule tracking
        self.community_rules = {}  # rule_id -> rule_data
        self.rule_sources = {}  # rule_id -> source_repo

        # Sync scheduler
        self.sync_tasks = []

        self.logger.info("Community Engine initialized")

    def get_subscribed_channels(self) -> List[str]:
        """Subscribe to channels for community operations"""
        return [
            'community.sync_repositories',     # Trigger repository sync
            'community.add_repository',        # Add new repository
            'community.remove_repository',     # Remove repository
            'community.get_rules',             # Get community rules
            'backend.rule_approved',           # Rule approved notification
        ]

    def load_repository_config(self):
        """Load repository configuration from file or database"""
        config_path = os.path.join(
            os.path.dirname(__file__),
            'repository_config.yaml'
        )

        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
                for repo_config in config.get('repositories', []):
                    repo = GitHubRepository(repo_config)
                    self.repositories.append(repo)
                    self.logger.info(f"Loaded repository: {repo.name}")
        else:
            # Create default configuration
            self.create_default_config(config_path)
            self.logger.info(f"Created default repository config at {config_path}")

    def create_default_config(self, config_path: str):
        """Create default repository configuration with popular community sources"""
        default_config = {
            'repositories': [
                {
                    'repo_url': 'https://github.com/SigmaHQ/sigma',
                    'name': 'Sigma HQ Rules',
                    'enabled': True,
                    'rule_paths': ['rules/'],
                    'rule_formats': ['sigma'],
                    'sync_frequency': 'daily',
                    'priority': 90,
                    'tags': ['sigma', 'community', 'verified']
                },
                {
                    'repo_url': 'https://github.com/elastic/detection-rules',
                    'name': 'Elastic Detection Rules',
                    'enabled': True,
                    'rule_paths': ['rules/'],
                    'rule_formats': ['elastic', 'kql'],
                    'sync_frequency': 'daily',
                    'priority': 85,
                    'tags': ['elastic', 'edr', 'siem']
                },
                {
                    'repo_url': 'https://github.com/splunk/security_content',
                    'name': 'Splunk Security Content',
                    'enabled': True,
                    'rule_paths': ['detections/'],
                    'rule_formats': ['splunk', 'spl'],
                    'sync_frequency': 'daily',
                    'priority': 85,
                    'tags': ['splunk', 'escu']
                },
                {
                    'repo_url': 'https://github.com/Azure/Azure-Sentinel',
                    'name': 'Microsoft Sentinel Rules',
                    'enabled': True,
                    'rule_paths': ['Detections/'],
                    'rule_formats': ['sentinel', 'kql'],
                    'sync_frequency': 'daily',
                    'priority': 80,
                    'tags': ['sentinel', 'microsoft', 'cloud']
                },
                {
                    'repo_url': 'https://github.com/chronicle/detection-rules',
                    'name': 'Chronicle Detection Rules',
                    'enabled': False,  # Disabled by default
                    'rule_paths': ['rules/'],
                    'rule_formats': ['yara-l'],
                    'sync_frequency': 'weekly',
                    'priority': 75,
                    'tags': ['chronicle', 'google']
                }
            ],
            'settings': {
                'auto_sync': True,
                'deduplicate': True,
                'quality_threshold': 70,
                'max_rules_per_repo': 10000
            }
        }

        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False)

        # Load the created config
        for repo_config in default_config['repositories']:
            repo = GitHubRepository(repo_config)
            self.repositories.append(repo)

    def start_engine_tasks(self) -> List[asyncio.Task]:
        """Start Community Engine specific tasks"""
        tasks = []

        # Start automatic repository syncing
        tasks.append(asyncio.create_task(self._auto_sync_scheduler()))

        self.logger.info(f"Community Engine started with {len(self.repositories)} repositories")
        return tasks

    async def _auto_sync_scheduler(self):
        """Automatically sync repositories based on configured frequency"""
        while True:
            try:
                for repo in self.repositories:
                    if not repo.enabled:
                        continue

                    # Check if it's time to sync
                    if repo.last_sync is None:
                        # First sync
                        await self.sync_repository(repo)
                    else:
                        # Check frequency
                        now = datetime.utcnow()
                        delta = now - repo.last_sync

                        if repo.sync_frequency == 'hourly' and delta > timedelta(hours=1):
                            await self.sync_repository(repo)
                        elif repo.sync_frequency == 'daily' and delta > timedelta(days=1):
                            await self.sync_repository(repo)
                        elif repo.sync_frequency == 'weekly' and delta > timedelta(weeks=1):
                            await self.sync_repository(repo)

                # Sleep for 1 hour before checking again
                await asyncio.sleep(3600)

            except Exception as e:
                self.logger.error(f"Auto sync error: {e}", exc_info=True)
                await asyncio.sleep(3600)

    async def sync_repository(self, repo: GitHubRepository) -> Dict[str, Any]:
        """
        Sync rules from a GitHub repository

        Returns:
            {
                'success': bool,
                'rules_found': int,
                'rules_imported': int,
                'rules_duplicated': int,
                'errors': List[str]
            }
        """
        self.logger.info(f"Syncing repository: {repo.name} ({repo.repo_url})")

        results = {
            'success': False,
            'rules_found': 0,
            'rules_imported': 0,
            'rules_duplicated': 0,
            'errors': []
        }

        try:
            # Fetch repository contents
            for rule_path in repo.rule_paths:
                rules = await self._fetch_rules_from_path(repo, rule_path)
                results['rules_found'] += len(rules)

                for rule_data in rules:
                    # Check for duplicates
                    if await self._is_duplicate_rule(rule_data):
                        results['rules_duplicated'] += 1
                        continue

                    # Import rule
                    imported = await self._import_community_rule(repo, rule_data)
                    if imported:
                        results['rules_imported'] += 1

            repo.last_sync = datetime.utcnow()
            repo.rule_count = results['rules_imported']
            results['success'] = True

            self.logger.info(
                f"✅ Synced {repo.name}: "
                f"{results['rules_imported']}/{results['rules_found']} rules imported"
            )

        except Exception as e:
            results['errors'].append(str(e))
            self.logger.error(f"Repository sync failed: {e}", exc_info=True)

        return results

    async def _fetch_rules_from_path(
        self,
        repo: GitHubRepository,
        path: str
    ) -> List[Dict[str, Any]]:
        """Fetch rule files from GitHub repository path"""
        rules = []

        try:
            # GitHub API endpoint
            url = f"{self.github_api_base}/repos/{repo.owner}/{repo.repo}/contents/{path}"

            headers = {}
            if self.github_token:
                headers['Authorization'] = f'token {self.github_token}'

            response = requests.get(url, headers=headers, timeout=30)

            if response.status_code != 200:
                self.logger.error(f"GitHub API error: {response.status_code} - {response.text}")
                return rules

            contents = response.json()

            # Process each file/directory
            for item in contents:
                if item['type'] == 'file':
                    # Check if file is a rule based on extension
                    if self._is_rule_file(item['name'], repo.rule_formats):
                        rule_data = await self._fetch_rule_file(repo, item['path'])
                        if rule_data:
                            rules.append(rule_data)

                elif item['type'] == 'dir':
                    # Recursively fetch from subdirectory
                    subrules = await self._fetch_rules_from_path(repo, item['path'])
                    rules.extend(subrules)

        except Exception as e:
            self.logger.error(f"Error fetching rules from {path}: {e}", exc_info=True)

        return rules

    def _is_rule_file(self, filename: str, formats: List[str]) -> bool:
        """Check if file is a rule based on format"""
        extensions = {
            'sigma': ['.yml', '.yaml'],
            'splunk': ['.yml', '.yaml', '.spl'],
            'elastic': ['.yml', '.yaml', '.json', '.toml'],
            'sentinel': ['.kql', '.yaml', '.json'],
            'kql': ['.kql'],
            'yara-l': ['.yaral']
        }

        filename_lower = filename.lower()

        for fmt in formats:
            if fmt in extensions:
                for ext in extensions[fmt]:
                    if filename_lower.endswith(ext):
                        return True

        return False

    async def _fetch_rule_file(
        self,
        repo: GitHubRepository,
        file_path: str
    ) -> Optional[Dict[str, Any]]:
        """Fetch and parse individual rule file from GitHub"""
        try:
            url = f"{self.github_api_base}/repos/{repo.owner}/{repo.repo}/contents/{file_path}"

            headers = {}
            if self.github_token:
                headers['Authorization'] = f'token {self.github_token}'

            response = requests.get(url, headers=headers, timeout=30)

            if response.status_code != 200:
                return None

            file_data = response.json()
            content = base64.b64decode(file_data['content']).decode('utf-8')

            # Parse based on format
            if file_path.endswith(('.yml', '.yaml')):
                parsed = yaml.safe_load(content)
            elif file_path.endswith('.json'):
                parsed = json.loads(content)
            else:
                # Plain text (e.g., .kql, .spl)
                parsed = {'query': content}

            return {
                'path': file_path,
                'content': content,
                'parsed': parsed,
                'format': self._detect_rule_format(file_path, parsed),
                'sha': file_data['sha']
            }

        except Exception as e:
            self.logger.error(f"Error fetching file {file_path}: {e}")
            return None

    def _detect_rule_format(self, file_path: str, parsed: Any) -> str:
        """Detect rule format from file path and content"""
        if 'detection' in parsed and 'condition' in parsed:
            return 'sigma'
        elif 'query' in parsed or 'kql' in file_path.lower():
            return 'kql'
        elif '.spl' in file_path or 'search' in str(parsed).lower():
            return 'splunk'
        else:
            return 'unknown'

    async def _is_duplicate_rule(self, rule_data: Dict[str, Any]) -> bool:
        """Check if rule already exists in our database"""
        try:
            # Calculate content hash
            content = rule_data.get('content', '')
            rule_hash = hashlib.sha256(content.encode()).hexdigest()

            # Check against existing rules
            query = """
                SELECT rule_id FROM detection_rules
                WHERE rule_hash = %s
            """

            cursor = self.db_connection.cursor()
            cursor.execute(query, (rule_hash,))
            exists = cursor.fetchone() is not None
            cursor.close()

            return exists

        except Exception as e:
            self.logger.error(f"Duplicate check error: {e}")
            return False

    async def _import_community_rule(
        self,
        repo: GitHubRepository,
        rule_data: Dict[str, Any]
    ) -> bool:
        """Import community rule into pending rules queue"""
        try:
            parsed = rule_data.get('parsed', {})
            content = rule_data.get('content', '')
            rule_format = rule_data.get('format', 'unknown')

            # Extract rule metadata
            rule_name = parsed.get('title', parsed.get('name', rule_data.get('path', 'Unknown')))
            description = parsed.get('description', '')
            tags = parsed.get('tags', [])

            # Add source tags
            tags.extend(repo.tags)
            tags.append('community')
            tags.append(repo.name)

            # Calculate hash for deduplication
            rule_hash = hashlib.sha256(content.encode()).hexdigest()

            # Store as pending rule
            query = """
                INSERT INTO pending_rules (
                    pending_id, rule_name, rule_type, rule_content,
                    description, tags, source, source_repo,
                    quality_score, status, generated_at, rule_hash
                )
                VALUES (
                    gen_random_uuid(), %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s
                )
                ON CONFLICT (rule_hash) DO NOTHING
                RETURNING pending_id
            """

            params = (
                rule_name,
                rule_format,
                content,
                description,
                tags,
                'community',
                repo.repo_url,
                repo.priority,  # Use repo priority as quality score
                'pending',
                rule_hash
            )

            cursor = self.db_connection.cursor()
            cursor.execute(query, params)
            result = cursor.fetchone()
            self.db_connection.commit()
            cursor.close()

            if result:
                self.logger.info(f"✅ Imported rule: {rule_name} from {repo.name}")
                return True
            else:
                self.logger.debug(f"Rule already exists (hash conflict): {rule_name}")
                return False

        except Exception as e:
            self.logger.error(f"Rule import error: {e}", exc_info=True)
            return False

    async def _handle_message(self, channel: str, message: Dict[str, Any]):
        """Handle incoming Redis messages"""
        if channel == 'community.sync_repositories':
            # Manual sync trigger
            for repo in self.repositories:
                if repo.enabled:
                    await self.sync_repository(repo)

        elif channel == 'community.add_repository':
            # Add new repository dynamically
            repo_config = message.get('repository')
            if repo_config:
                repo = GitHubRepository(repo_config)
                self.repositories.append(repo)
                self.logger.info(f"Added repository: {repo.name}")

        elif channel == 'community.remove_repository':
            # Remove repository
            repo_url = message.get('repo_url')
            self.repositories = [r for r in self.repositories if r.repo_url != repo_url]
            self.logger.info(f"Removed repository: {repo_url}")

        elif channel == 'community.get_rules':
            # Return community rules
            self.publish_message('community.rules_response', {
                'rules': list(self.community_rules.values()),
                'sources': self.rule_sources
            })

    async def _setup_http_routes(self, app):
        """Setup HTTP routes for community engine management"""

        # Repository management
        app.router.add_get('/api/community/repositories', self._handle_get_repositories)
        app.router.add_post('/api/community/repositories', self._handle_add_repository)
        app.router.add_delete('/api/community/repositories/{repo_url}', self._handle_remove_repository)
        app.router.add_post('/api/community/repositories/{repo_id}/sync', self._handle_sync_repository)

        # Rule browsing
        app.router.add_get('/api/community/rules', self._handle_get_community_rules)
        app.router.add_get('/api/community/rules/{rule_id}', self._handle_get_rule_detail)

        self.logger.info("Community Engine API routes configured")

    async def _handle_get_repositories(self, request):
        """Get all configured repositories"""
        from aiohttp import web
        return web.json_response({
            'repositories': [repo.to_dict() for repo in self.repositories]
        })

    async def _handle_add_repository(self, request):
        """Add new repository"""
        from aiohttp import web
        try:
            data = await request.json()
            repo = GitHubRepository(data)
            self.repositories.append(repo)

            # Trigger initial sync
            if repo.enabled:
                asyncio.create_task(self.sync_repository(repo))

            return web.json_response({
                'success': True,
                'repository': repo.to_dict()
            })

        except Exception as e:
            return web.json_response({'error': str(e)}, status=400)

    async def _handle_remove_repository(self, request):
        """Remove repository"""
        from aiohttp import web
        repo_url = request.match_info['repo_url']
        self.repositories = [r for r in self.repositories if r.repo_url != repo_url]
        return web.json_response({'success': True})

    async def _handle_sync_repository(self, request):
        """Manually trigger repository sync"""
        from aiohttp import web
        try:
            repo_id = request.match_info['repo_id']
            repo = next((r for r in self.repositories if r.repo == repo_id), None)

            if not repo:
                return web.json_response({'error': 'Repository not found'}, status=404)

            result = await self.sync_repository(repo)
            return web.json_response(result)

        except Exception as e:
            return web.json_response({'error': str(e)}, status=500)

    async def _handle_get_community_rules(self, request):
        """Get all community rules"""
        from aiohttp import web
        # TODO: Query from database
        return web.json_response({'rules': []})

    async def _handle_get_rule_detail(self, request):
        """Get specific community rule detail"""
        from aiohttp import web
        rule_id = request.match_info['rule_id']
        # TODO: Query from database
        return web.json_response({'rule': {}})


if __name__ == "__main__":
    async def main():
        engine = CommunityEngine()
        await engine.start()

    asyncio.run(main())
