"""
CTI Rule Generator for Backend Engine
Converts CTI indicators into detection rules and patterns
"""

import json
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import uuid4
import redis.asyncio as redis
import asyncpg
import logging
import re
import hashlib

logger = logging.getLogger(__name__)

class CTIRuleGenerator:
    """Generates detection rules from CTI indicators"""

    def __init__(self, redis_client: redis.Redis, pg_pool: asyncpg.Pool):
        self.redis_client = redis_client
        self.pg_pool = pg_pool
        self.rule_templates = self._load_rule_templates()

    async def initialize(self):
        """Initialize CTI rule generator"""
        # Subscribe to CTI indicator channel
        pubsub = self.redis_client.pubsub()
        await pubsub.subscribe('backend.cti.indicator')

        # Start processing loop
        asyncio.create_task(self._process_cti_indicators(pubsub))

        logger.info("CTI Rule Generator initialized")

    async def _process_cti_indicators(self, pubsub):
        """Process incoming CTI indicators"""
        async for message in pubsub.listen():
            if message['type'] == 'message':
                try:
                    indicator = json.loads(message['data'])
                    await self.generate_rule_from_indicator(indicator)
                except Exception as e:
                    logger.error(f"Error processing CTI indicator: {e}")

    async def generate_rule_from_indicator(self, indicator: Dict) -> Dict:
        """Generate detection rule from CTI indicator"""
        indicator_type = indicator.get('type')
        indicator_value = indicator.get('value')

        if not indicator_type or not indicator_value:
            logger.warning("Invalid indicator: missing type or value")
            return None

        # Generate rule based on indicator type
        if indicator_type == 'ip':
            rule = await self._generate_ip_rule(indicator)
        elif indicator_type == 'domain':
            rule = await self._generate_domain_rule(indicator)
        elif indicator_type == 'hash':
            rule = await self._generate_hash_rule(indicator)
        elif indicator_type == 'url':
            rule = await self._generate_url_rule(indicator)
        elif indicator_type == 'cve':
            rule = await self._generate_cve_rule(indicator)
        elif indicator_type == 'yara':
            rule = await self._generate_yara_rule(indicator)
        else:
            logger.warning(f"Unsupported indicator type: {indicator_type}")
            return None

        # Store rule in database
        if rule:
            await self._store_rule(rule)

            # Generate test cases for the rule
            test_cases = await self._generate_test_cases(rule, indicator)
            await self._store_test_cases(rule['rule_id'], test_cases)

            # Notify pattern library about new rule
            await self._notify_pattern_library(rule)

        return rule

    async def _generate_ip_rule(self, indicator: Dict) -> Dict:
        """Generate rule for IP indicator"""
        ip_value = indicator['value']
        confidence = indicator.get('confidence', 70)

        rule = {
            'rule_id': str(uuid4()),
            'rule_name': f"CTI_IP_{ip_value.replace('.', '_')}",
            'rule_type': 'detection',
            'severity': self._calculate_severity(confidence),
            'description': f"Detect communication with malicious IP {ip_value}",
            'detection_logic': {
                'type': 'network',
                'conditions': [
                    {
                        'field': 'destination.ip',
                        'operator': 'equals',
                        'value': ip_value
                    },
                    {
                        'field': 'source.ip',
                        'operator': 'equals',
                        'value': ip_value,
                        'logic': 'OR'
                    }
                ]
            },
            'metadata': {
                'source': indicator.get('source', 'unknown'),
                'confidence': confidence,
                'tags': indicator.get('tags', []),
                'mitre_attack': indicator.get('mitre_attack', []),
                'references': indicator.get('references', [])
            },
            'query_translations': {
                'splunk': f'index=* (dest_ip="{ip_value}" OR src_ip="{ip_value}")',
                'elastic': f'destination.ip:"{ip_value}" OR source.ip:"{ip_value}"',
                'sentinel': f'DeviceNetworkEvents | where RemoteIP == "{ip_value}" or LocalIP == "{ip_value}"'
            },
            'response_actions': [
                {
                    'action': 'alert',
                    'priority': 'high'
                },
                {
                    'action': 'block_ip',
                    'target': ip_value,
                    'duration': '24h'
                }
            ],
            'created_at': datetime.utcnow().isoformat(),
            'created_by': 'cti_rule_generator',
            'enabled': True
        }

        return rule

    async def _generate_domain_rule(self, indicator: Dict) -> Dict:
        """Generate rule for domain indicator"""
        domain_value = indicator['value']
        confidence = indicator.get('confidence', 70)

        rule = {
            'rule_id': str(uuid4()),
            'rule_name': f"CTI_Domain_{domain_value.replace('.', '_')}",
            'rule_type': 'detection',
            'severity': self._calculate_severity(confidence),
            'description': f"Detect communication with malicious domain {domain_value}",
            'detection_logic': {
                'type': 'dns',
                'conditions': [
                    {
                        'field': 'dns.query',
                        'operator': 'contains',
                        'value': domain_value
                    },
                    {
                        'field': 'http.host',
                        'operator': 'equals',
                        'value': domain_value,
                        'logic': 'OR'
                    },
                    {
                        'field': 'tls.sni',
                        'operator': 'equals',
                        'value': domain_value,
                        'logic': 'OR'
                    }
                ]
            },
            'metadata': {
                'source': indicator.get('source', 'unknown'),
                'confidence': confidence,
                'malware': indicator.get('malware', ''),
                'tags': indicator.get('tags', []),
                'references': indicator.get('references', [])
            },
            'query_translations': {
                'splunk': f'index=* (dns_query="*{domain_value}*" OR http_host="{domain_value}" OR ssl_sni="{domain_value}")',
                'elastic': f'dns.question.name:*{domain_value}* OR http.host:"{domain_value}" OR tls.client.server_name:"{domain_value}"',
                'sentinel': f'DnsEvents | where Name contains "{domain_value}"'
            },
            'response_actions': [
                {
                    'action': 'alert',
                    'priority': 'high'
                },
                {
                    'action': 'sinkhole_domain',
                    'target': domain_value
                }
            ],
            'created_at': datetime.utcnow().isoformat(),
            'created_by': 'cti_rule_generator',
            'enabled': True
        }

        return rule

    async def _generate_hash_rule(self, indicator: Dict) -> Dict:
        """Generate rule for file hash indicator"""
        hash_value = indicator['value']
        hash_type = indicator.get('hash_type', 'sha256')
        confidence = indicator.get('confidence', 80)

        rule = {
            'rule_id': str(uuid4()),
            'rule_name': f"CTI_Hash_{hash_value[:8]}",
            'rule_type': 'detection',
            'severity': self._calculate_severity(confidence),
            'description': f"Detect malicious file with {hash_type} hash {hash_value}",
            'detection_logic': {
                'type': 'file',
                'conditions': [
                    {
                        'field': f'file.hash.{hash_type}',
                        'operator': 'equals',
                        'value': hash_value
                    },
                    {
                        'field': f'process.hash.{hash_type}',
                        'operator': 'equals',
                        'value': hash_value,
                        'logic': 'OR'
                    }
                ]
            },
            'metadata': {
                'source': indicator.get('source', 'unknown'),
                'confidence': confidence,
                'malware_family': indicator.get('malware_family', ''),
                'tags': indicator.get('tags', []),
                'file_type': indicator.get('metadata', {}).get('file_type', '')
            },
            'query_translations': {
                'splunk': f'index=* (file_hash_{hash_type}="{hash_value}" OR process_hash_{hash_type}="{hash_value}")',
                'elastic': f'file.hash.{hash_type}:"{hash_value}" OR process.hash.{hash_type}:"{hash_value}"',
                'sentinel': f'DeviceFileEvents | where {hash_type.upper()} == "{hash_value}"'
            },
            'response_actions': [
                {
                    'action': 'alert',
                    'priority': 'critical'
                },
                {
                    'action': 'quarantine_file',
                    'target': hash_value
                },
                {
                    'action': 'kill_process',
                    'target': hash_value
                }
            ],
            'created_at': datetime.utcnow().isoformat(),
            'created_by': 'cti_rule_generator',
            'enabled': True
        }

        return rule

    async def _generate_url_rule(self, indicator: Dict) -> Dict:
        """Generate rule for URL indicator"""
        url_value = indicator['value']
        confidence = indicator.get('confidence', 70)

        # Parse URL components
        import urllib.parse
        parsed = urllib.parse.urlparse(url_value)
        domain = parsed.netloc
        path = parsed.path

        rule = {
            'rule_id': str(uuid4()),
            'rule_name': f"CTI_URL_{hashlib.md5(url_value.encode()).hexdigest()[:8]}",
            'rule_type': 'detection',
            'severity': self._calculate_severity(confidence),
            'description': f"Detect access to malicious URL",
            'detection_logic': {
                'type': 'web',
                'conditions': [
                    {
                        'field': 'http.url',
                        'operator': 'equals',
                        'value': url_value
                    },
                    {
                        'field': 'http.request.full_uri',
                        'operator': 'contains',
                        'value': url_value,
                        'logic': 'OR'
                    }
                ]
            },
            'metadata': {
                'source': indicator.get('source', 'unknown'),
                'confidence': confidence,
                'tags': indicator.get('tags', []),
                'references': indicator.get('references', [])
            },
            'query_translations': {
                'splunk': f'index=* (url="{url_value}" OR uri_path="{path}" AND http_host="{domain}")',
                'elastic': f'url.full:"{url_value}" OR (url.domain:"{domain}" AND url.path:"{path}")',
                'sentinel': f'CommonSecurityLog | where RequestURL == "{url_value}"'
            },
            'response_actions': [
                {
                    'action': 'alert',
                    'priority': 'high'
                },
                {
                    'action': 'block_url',
                    'target': url_value
                }
            ],
            'created_at': datetime.utcnow().isoformat(),
            'created_by': 'cti_rule_generator',
            'enabled': True
        }

        return rule

    async def _generate_cve_rule(self, indicator: Dict) -> Dict:
        """Generate rule for CVE indicator"""
        cve_id = indicator['value']
        confidence = indicator.get('confidence', 80)

        rule = {
            'rule_id': str(uuid4()),
            'rule_name': f"CTI_CVE_{cve_id.replace('-', '_')}",
            'rule_type': 'vulnerability',
            'severity': 'critical',
            'description': f"Detect exploitation attempts of {cve_id}",
            'detection_logic': {
                'type': 'vulnerability',
                'conditions': [
                    {
                        'field': 'vulnerability.id',
                        'operator': 'equals',
                        'value': cve_id
                    },
                    {
                        'field': 'event.category',
                        'operator': 'equals',
                        'value': 'intrusion_detection',
                        'logic': 'AND'
                    }
                ]
            },
            'metadata': {
                'source': indicator.get('source', 'unknown'),
                'confidence': confidence,
                'cvss_score': indicator.get('cvss_score'),
                'affected_products': indicator.get('affected_products', []),
                'references': indicator.get('references', [])
            },
            'query_translations': {
                'splunk': f'index=* cve="{cve_id}"',
                'elastic': f'vulnerability.id:"{cve_id}"',
                'sentinel': f'SecurityAlert | where Properties contains "{cve_id}"'
            },
            'response_actions': [
                {
                    'action': 'alert',
                    'priority': 'critical'
                },
                {
                    'action': 'patch_notification',
                    'target': cve_id
                }
            ],
            'created_at': datetime.utcnow().isoformat(),
            'created_by': 'cti_rule_generator',
            'enabled': True
        }

        return rule

    async def _generate_yara_rule(self, indicator: Dict) -> Dict:
        """Generate detection from YARA rule"""
        yara_rule = indicator['value']
        confidence = indicator.get('confidence', 85)

        rule = {
            'rule_id': str(uuid4()),
            'rule_name': f"CTI_YARA_{indicator.get('name', 'unknown')}",
            'rule_type': 'yara',
            'severity': self._calculate_severity(confidence),
            'description': f"YARA rule from CTI: {indicator.get('description', '')}",
            'detection_logic': {
                'type': 'yara',
                'yara_rule': yara_rule
            },
            'metadata': {
                'source': indicator.get('source', 'unknown'),
                'confidence': confidence,
                'tags': indicator.get('tags', []),
                'author': indicator.get('author', ''),
                'references': indicator.get('references', [])
            },
            'response_actions': [
                {
                    'action': 'alert',
                    'priority': 'high'
                },
                {
                    'action': 'scan_with_yara',
                    'rule': yara_rule
                }
            ],
            'created_at': datetime.utcnow().isoformat(),
            'created_by': 'cti_rule_generator',
            'enabled': True
        }

        return rule

    def _calculate_severity(self, confidence: int) -> str:
        """Calculate rule severity based on confidence"""
        if confidence >= 90:
            return 'critical'
        elif confidence >= 75:
            return 'high'
        elif confidence >= 50:
            return 'medium'
        else:
            return 'low'

    async def _generate_test_cases(self, rule: Dict, indicator: Dict) -> List[Dict]:
        """Generate test cases for rule validation"""
        test_cases = []

        # Positive test case (should match)
        positive_test = {
            'test_id': str(uuid4()),
            'test_name': f"Positive test for {rule['rule_name']}",
            'test_type': 'positive',
            'input_data': self._generate_positive_test_data(rule, indicator),
            'expected_result': {
                'match': True,
                'severity': rule['severity']
            }
        }
        test_cases.append(positive_test)

        # Negative test case (should not match)
        negative_test = {
            'test_id': str(uuid4()),
            'test_name': f"Negative test for {rule['rule_name']}",
            'test_type': 'negative',
            'input_data': self._generate_negative_test_data(rule),
            'expected_result': {
                'match': False
            }
        }
        test_cases.append(negative_test)

        return test_cases

    def _generate_positive_test_data(self, rule: Dict, indicator: Dict) -> Dict:
        """Generate positive test data that should trigger the rule"""
        indicator_type = indicator.get('type')
        indicator_value = indicator.get('value')

        if indicator_type == 'ip':
            return {
                'event_type': 'network',
                'source': {'ip': '*************'},
                'destination': {'ip': indicator_value},
                'action': 'allowed'
            }
        elif indicator_type == 'domain':
            return {
                'event_type': 'dns',
                'dns': {'query': indicator_value},
                'source': {'ip': '*************'}
            }
        elif indicator_type == 'hash':
            return {
                'event_type': 'file',
                'file': {
                    'hash': {
                        indicator.get('hash_type', 'sha256'): indicator_value
                    },
                    'name': 'malicious.exe'
                }
            }
        else:
            return {'test_data': 'positive'}

    def _generate_negative_test_data(self, rule: Dict) -> Dict:
        """Generate negative test data that should not trigger the rule"""
        return {
            'event_type': 'normal',
            'source': {'ip': '********'},
            'destination': {'ip': '********'},
            'action': 'allowed',
            'description': 'Normal internal traffic'
        }

    async def _store_rule(self, rule: Dict):
        """Store rule in database"""
        async with self.pg_pool.acquire() as conn:
            await conn.execute("""
                INSERT INTO detection_rules (
                    rule_id, rule_name, rule_type, severity,
                    description, detection_logic, metadata,
                    query_translations, response_actions,
                    created_at, created_by, enabled
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            """,
                rule['rule_id'], rule['rule_name'], rule['rule_type'],
                rule['severity'], rule['description'],
                json.dumps(rule['detection_logic']),
                json.dumps(rule['metadata']),
                json.dumps(rule.get('query_translations', {})),
                json.dumps(rule.get('response_actions', [])),
                datetime.fromisoformat(rule['created_at']),
                rule['created_by'], rule['enabled']
            )

        logger.info(f"Stored CTI rule: {rule['rule_name']}")

    async def _store_test_cases(self, rule_id: str, test_cases: List[Dict]):
        """Store test cases for rule"""
        async with self.pg_pool.acquire() as conn:
            for test_case in test_cases:
                await conn.execute("""
                    INSERT INTO rule_test_cases (
                        test_id, rule_id, test_name, test_type,
                        input_data, expected_result, created_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                """,
                    test_case['test_id'], rule_id,
                    test_case['test_name'], test_case['test_type'],
                    json.dumps(test_case['input_data']),
                    json.dumps(test_case['expected_result']),
                    datetime.utcnow()
                )

    async def _notify_pattern_library(self, rule: Dict):
        """Notify pattern library about new CTI-based rule"""
        notification = {
            'event': 'cti_rule_created',
            'rule_id': rule['rule_id'],
            'rule_name': rule['rule_name'],
            'source': rule['metadata'].get('source'),
            'timestamp': datetime.utcnow().isoformat()
        }

        await self.redis_client.publish(
            'library.pattern.cti_rule',
            json.dumps(notification)
        )

    def _load_rule_templates(self) -> Dict:
        """Load rule templates for different indicator types"""
        return {
            'ip': {
                'fields': ['source.ip', 'destination.ip', 'client.ip', 'server.ip'],
                'operators': ['equals', 'in_subnet'],
                'actions': ['alert', 'block_ip']
            },
            'domain': {
                'fields': ['dns.query', 'http.host', 'tls.sni'],
                'operators': ['equals', 'contains', 'matches'],
                'actions': ['alert', 'sinkhole_domain']
            },
            'hash': {
                'fields': ['file.hash.md5', 'file.hash.sha1', 'file.hash.sha256'],
                'operators': ['equals'],
                'actions': ['alert', 'quarantine_file', 'kill_process']
            },
            'url': {
                'fields': ['http.url', 'http.request.full_uri'],
                'operators': ['equals', 'contains'],
                'actions': ['alert', 'block_url']
            }
        }


class CTIRuleManager:
    """Manages CTI-generated rules lifecycle"""

    def __init__(self, pg_pool: asyncpg.Pool, redis_client: redis.Redis):
        self.pg_pool = pg_pool
        self.redis_client = redis_client

    async def get_active_cti_rules(self) -> List[Dict]:
        """Get all active CTI-generated rules"""
        async with self.pg_pool.acquire() as conn:
            rows = await conn.fetch("""
                SELECT * FROM detection_rules
                WHERE created_by = 'cti_rule_generator'
                AND enabled = true
                ORDER BY created_at DESC
            """)

            return [dict(row) for row in rows]

    async def update_rule_effectiveness(self, rule_id: str, metrics: Dict):
        """Update rule effectiveness metrics"""
        async with self.pg_pool.acquire() as conn:
            await conn.execute("""
                UPDATE detection_rules
                SET metadata = metadata || $1::jsonb,
                    updated_at = CURRENT_TIMESTAMP
                WHERE rule_id = $2
            """,
                json.dumps({'effectiveness': metrics}),
                rule_id
            )

    async def disable_ineffective_rules(self, threshold: float = 0.2):
        """Disable rules with low effectiveness"""
        async with self.pg_pool.acquire() as conn:
            # Find rules with high false positive rate
            rows = await conn.fetch("""
                SELECT rule_id, rule_name, metadata
                FROM detection_rules
                WHERE created_by = 'cti_rule_generator'
                AND enabled = true
                AND (metadata->>'effectiveness'->>'false_positive_rate')::float > $1
            """, threshold)

            for row in rows:
                await conn.execute("""
                    UPDATE detection_rules
                    SET enabled = false,
                        metadata = metadata || '{"disabled_reason": "high_false_positives"}'::jsonb
                    WHERE rule_id = $1
                """, row['rule_id'])

                logger.info(f"Disabled ineffective rule: {row['rule_name']}")

    async def export_rules_to_siem(self, siem_type: str) -> List[Dict]:
        """Export CTI rules to specific SIEM format"""
        rules = await self.get_active_cti_rules()
        exported_rules = []

        for rule in rules:
            # Get SIEM-specific query
            query_translations = json.loads(rule.get('query_translations', '{}'))
            siem_query = query_translations.get(siem_type.lower())

            if siem_query:
                exported_rules.append({
                    'name': rule['rule_name'],
                    'description': rule['description'],
                    'query': siem_query,
                    'severity': rule['severity'],
                    'tags': rule.get('metadata', {}).get('tags', [])
                })

        return exported_rules