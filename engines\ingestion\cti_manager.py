"""
CTI Feed Manager for Ingestion Engine
Handles all threat intelligence feed ingestion
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

# Import CTI connectors
from opencti_integration import OpenCTIConnector
from otx_integration import OTXConnector
from threatfox_integration import ThreatFoxConnector
from crowdstrike_cti_integration import CrowdStrikeCTIConnector
from cti_data_router import CTIDataRouter
from cti_aggregator import CTIAggregator

logger = logging.getLogger(__name__)


class CTIManager:
    """
    Manages all CTI (Cyber Threat Intelligence) feed sources
    Ingests from multiple sources and publishes normalized data

    NEW ARCHITECTURE:
    1. Fetch CTI from sources (OTX, OpenCTI, ThreatFox, CrowdStrike INTEL/IOCS)
    2. Send to CTI Aggregator (deduplicates, merges, stores in entities table)
    3. Aggregator updates Redis cache (one-way, no loops)
    4. Aggregator routes to segregated channels (rules, investigation, MITRE)
    """

    def __init__(self, redis_client, db_connection, config: Dict[str, Any]):
        self.redis_client = redis_client
        self.db_connection = db_connection
        self.config = config

        # Initialize CTI Data Router for segregated publishing
        self.cti_router = CTIDataRouter(redis_client)

        # Initialize CTI Aggregator (single source of truth)
        self.cti_aggregator = CTIAggregator(db_connection, redis_client, self.cti_router)

        # Initialize connectors
        self.connectors = {}
        self._initialize_connectors()

        # Track last update times
        self.last_updates = {}

        # Update intervals (in seconds)
        self.update_intervals = {
            'opencti': 3600,  # 1 hour
            'otx': 3600,      # 1 hour
            'threatfox': 7200, # 2 hours
            'crowdstrike_cti': 3600,  # 1 hour (INTEL + IOCS scopes)
            'misp': 3600      # 1 hour
        }

    def _initialize_connectors(self):
        """Initialize CTI source connectors based on configuration"""
        logger.info(f"Initializing CTI connectors with config: {self.config}")

        # OpenCTI
        if self.config.get('opencti', {}).get('enabled'):
            try:
                logger.info(f"Attempting to initialize OpenCTI with URL: {self.config['opencti'].get('url')}")
                self.connectors['opencti'] = OpenCTIConnector(
                    url=self.config['opencti'].get('url'),
                    api_key=self.config['opencti'].get('api_key')
                )
                logger.info("OpenCTI connector initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize OpenCTI: {e}", exc_info=True)

        # OTX (AlienVault)
        if self.config.get('otx', {}).get('enabled'):
            try:
                logger.info(f"Attempting to initialize OTX with API key: {'*' * 10 if self.config['otx'].get('api_key') else 'None'}")
                self.connectors['otx'] = OTXConnector(
                    api_key=self.config['otx'].get('api_key')
                )
                logger.info("OTX connector initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize OTX: {e}", exc_info=True)

        # ThreatFox (abuse.ch)
        if self.config.get('threatfox', {}).get('enabled'):
            try:
                logger.info(f"Attempting to initialize ThreatFox with API key: {'*' * 10 if self.config['threatfox'].get('api_key') else 'None'}")
                self.connectors['threatfox'] = ThreatFoxConnector(
                    api_key=self.config['threatfox'].get('api_key')
                )
                logger.info("ThreatFox connector initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize ThreatFox: {e}", exc_info=True)

        # CrowdStrike CTI (INTEL + IOCS scopes - separate from EDR logs)
        if self.config.get('crowdstrike_cti', {}).get('enabled'):
            try:
                logger.info("Attempting to initialize CrowdStrike CTI connector (INTEL + IOCS scopes)")
                self.connectors['crowdstrike_cti'] = CrowdStrikeCTIConnector(
                    client_id=self.config['crowdstrike_cti'].get('client_id'),
                    client_secret=self.config['crowdstrike_cti'].get('client_secret'),
                    base_url=self.config['crowdstrike_cti'].get('base_url', 'https://api.crowdstrike.com')
                )
                logger.info("CrowdStrike CTI connector initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize CrowdStrike CTI: {e}", exc_info=True)

        logger.info(f"CTI connectors initialized: {list(self.connectors.keys())}")

    async def start(self, auto_update=False):
        """Start CTI ingestion tasks

        Args:
            auto_update: If True, starts automatic update loops.
                        If False (default), updates are manual only.
        """
        tasks = []

        if auto_update:
            # Create update task for each enabled connector
            for source_name, connector in self.connectors.items():
                task = asyncio.create_task(self._update_loop(source_name, connector))
                tasks.append(task)
                logger.info(f"Started CTI update task for {source_name}")
        else:
            logger.info("CTI Manager started in manual mode - use manual_update() to fetch data")

        return tasks

    async def _update_loop(self, source_name: str, connector):
        """Continuous update loop for a CTI source"""
        while True:
            try:
                await self._fetch_and_publish(source_name, connector)
                self.last_updates[source_name] = datetime.now()

            except Exception as e:
                logger.error(f"Error updating {source_name}: {e}")

            # Wait for next update interval
            interval = self.update_intervals.get(source_name, 3600)
            await asyncio.sleep(interval)

    async def _fetch_and_publish(self, source_name: str, connector):
        """Fetch data from CTI source and publish to Redis"""

        logger.info(f"Fetching updates from {source_name}")

        if source_name == 'opencti':
            await self._process_opencti(connector)
        elif source_name == 'otx':
            await self._process_otx(connector)
        elif source_name == 'threatfox':
            await self._process_threatfox(connector)
        elif source_name == 'crowdstrike_cti':
            await self._process_crowdstrike_cti(connector)
        # Add other sources as needed

    async def _process_opencti(self, connector: OpenCTIConnector):
        """Process OpenCTI indicators"""
        try:
            # Test connection
            connected = await connector.connect()
            if not connected:
                logger.warning("OpenCTI connection failed")
                return

            # Fetch indicators
            indicators = await connector.get_indicators(limit=100)
            logger.info(f"Retrieved {len(indicators)} indicators from OpenCTI")

            # Fetch reports
            reports = await connector.get_reports(limit=10)
            logger.info(f"Retrieved {len(reports)} reports from OpenCTI")

            # Fetch attack patterns
            attack_patterns = await connector.get_attack_patterns()
            logger.info(f"Retrieved {len(attack_patterns)} attack patterns from OpenCTI")

            # Normalize and publish
            cti_data = {
                'source': 'opencti',
                'timestamp': datetime.now().isoformat(),
                'indicators': indicators,
                'reports': reports,
                'attack_patterns': attack_patterns,
                'total_items': len(indicators) + len(reports) + len(attack_patterns)
            }

            # Publish to Redis channel for Backend Engine to process
            await self._publish_cti_data(cti_data)

        except Exception as e:
            logger.error(f"Error processing OpenCTI data: {e}")

    async def _process_otx(self, connector: OTXConnector):
        """Process OTX indicators"""
        try:
            # Test connection
            connected = await connector.test_connection()
            if not connected:
                logger.warning("OTX connection failed - check API key")
                return

            # Fetch indicators
            indicators = await connector.get_recent_indicators(limit=50)
            logger.info(f"Retrieved {len(indicators)} indicators from OTX")

            # Normalize and publish
            cti_data = {
                'source': 'otx',
                'timestamp': datetime.now().isoformat(),
                'indicators': indicators,
                'reports': [],
                'attack_patterns': [],
                'total_items': len(indicators)
            }

            # Publish to Redis channel
            await self._publish_cti_data(cti_data)

        except Exception as e:
            logger.error(f"Error processing OTX data: {e}")

    async def _process_threatfox(self, connector: ThreatFoxConnector):
        """Process ThreatFox indicators"""
        try:
            # Test connection
            connected = await connector.test_connection()
            if not connected:
                logger.warning("ThreatFox connection failed - check API key")
                return

            # Fetch recent IOCs
            indicators = await connector.get_recent_iocs(days=1, limit=100)
            logger.info(f"Retrieved {len(indicators)} indicators from ThreatFox")

            # Also fetch malware-specific IOCs
            malware_iocs = await connector.get_malware_iocs('Cobalt Strike', limit=50)
            if malware_iocs:
                indicators.extend(malware_iocs)
                logger.info(f"Retrieved {len(malware_iocs)} Cobalt Strike IOCs from ThreatFox")

            # Normalize and publish
            cti_data = {
                'source': 'threatfox',
                'timestamp': datetime.now().isoformat(),
                'indicators': indicators,
                'reports': [],
                'attack_patterns': [],
                'total_items': len(indicators)
            }

            # Publish to Redis channel
            await self._publish_cti_data(cti_data)

        except Exception as e:
            logger.error(f"Error processing ThreatFox data: {e}")

    async def _process_crowdstrike_cti(self, connector: CrowdStrikeCTIConnector):
        """
        Process CrowdStrike CTI data from INTEL and IOCS scopes
        NOTE: This is CTI feed ingestion, NOT EDR log ingestion
        """
        try:
            # Test connection
            connected = await connector.test_connection()
            if not connected:
                logger.warning("CrowdStrike CTI connection failed - check API credentials")
                return

            all_indicators = []

            # Fetch INTEL indicators (threat intelligence)
            logger.info("Fetching CrowdStrike INTEL indicators...")
            intel_indicators = await connector.get_intel_indicators(limit=100)
            logger.info(f"Retrieved {len(intel_indicators)} INTEL indicators from CrowdStrike")
            all_indicators.extend(intel_indicators)

            # Fetch custom IOCs
            logger.info("Fetching CrowdStrike custom IOCs...")
            custom_iocs = await connector.get_custom_iocs(limit=100)
            logger.info(f"Retrieved {len(custom_iocs)} custom IOCs from CrowdStrike")
            all_indicators.extend(custom_iocs)

            # Fetch threat actors (optional, for context)
            threat_actors = await connector.get_threat_actors(limit=20)
            logger.info(f"Retrieved {len(threat_actors)} threat actors from CrowdStrike")

            # Convert threat actors to enrichment data
            reports = []
            for actor in threat_actors:
                report = {
                    'id': actor['id'],
                    'name': actor['name'],
                    'description': actor.get('description', ''),
                    'threat_actor': actor['name'],
                    'aliases': actor.get('aliases', []),
                    'first_seen': actor.get('first_seen'),
                    'target_countries': actor.get('target_countries', []),
                    'target_industries': actor.get('target_industries', [])
                }
                reports.append(report)

            # Normalize and publish
            cti_data = {
                'source': 'crowdstrike_cti',
                'timestamp': datetime.now().isoformat(),
                'indicators': all_indicators,
                'reports': reports,
                'attack_patterns': [],
                'total_items': len(all_indicators) + len(reports)
            }

            # Publish to CTI Aggregator
            await self._publish_cti_data(cti_data)

            logger.info(f"CrowdStrike CTI processing complete: {len(all_indicators)} indicators, {len(reports)} actor reports")

        except Exception as e:
            logger.error(f"Error processing CrowdStrike CTI data: {e}", exc_info=True)

    async def _publish_cti_data(self, cti_data: Dict[str, Any]):
        """
        Send CTI data to aggregator (NEW ARCHITECTURE)

        Flow:
        1. CTI Aggregator deduplicates and stores in entities table
        2. Aggregator updates Redis cache (one-way, no loops)
        3. Aggregator routes to segregated channels (rules, investigation, MITRE)
        """
        try:
            source = cti_data.get('source')

            # Send to CTI Aggregator (single source of truth)
            stats = await self.cti_aggregator.ingest_cti_data(cti_data, source)

            logger.info(f"CTI aggregation complete from {source}: {stats}")

            # THEN route to segregated channels (rules, investigation, MITRE)
            # Aggregator already handled enrichment cache
            await self.cti_router.route_cti_data(cti_data)

            logger.info(f"Routed {cti_data['total_items']} items from {source} to segregated channels")

            # Also publish indicators specifically for rule generation
            if cti_data.get('indicators'):
                indicator_channel = 'ingestion.cti.indicators'
                for indicator in cti_data['indicators'][:10]:  # Limit for performance
                    indicator_msg = {
                        'source': cti_data['source'],
                        'timestamp': cti_data['timestamp'],
                        'indicator': indicator
                    }
                    self.redis_client.publish(indicator_channel, json.dumps(indicator_msg))

                logger.info(f"Published indicators to {indicator_channel}")

        except Exception as e:
            logger.error(f"Error publishing CTI data: {e}")

    async def get_status(self) -> Dict[str, Any]:
        """Get status of all CTI sources"""
        status = {
            'enabled_sources': list(self.connectors.keys()),
            'last_updates': {
                source: last_update.isoformat() if last_update else 'Never'
                for source, last_update in self.last_updates.items()
            },
            'update_intervals': self.update_intervals
        }

        # Check connector health
        for source_name, connector in self.connectors.items():
            try:
                if source_name == 'opencti':
                    status[f'{source_name}_connected'] = await connector.connect()
                elif source_name == 'otx':
                    status[f'{source_name}_connected'] = await connector.test_connection()
            except:
                status[f'{source_name}_connected'] = False

        return status

    async def manual_update(self, source: str) -> bool:
        """Trigger manual update for a specific source"""
        if source in self.connectors:
            try:
                logger.info(f"Starting manual update for {source}")
                # Add timeout to prevent hanging
                await asyncio.wait_for(
                    self._fetch_and_publish(source, self.connectors[source]),
                    timeout=30.0
                )
                logger.info(f"Manual update completed for {source}")
                return True
            except asyncio.TimeoutError:
                logger.error(f"Manual update timed out for {source}")
                return False
            except Exception as e:
                logger.error(f"Manual update failed for {source}: {e}", exc_info=True)
                return False
        else:
            logger.warning(f"Source {source} not configured")
            return False

    async def handle_update_trigger(self, trigger_data: Dict):
        """Handle CTI update trigger from Backend Scheduler

        Receives trigger from 'ingestion.cti.update' channel,
        fetches data from CTI sources, publishes to 'backend.cti.data'
        """
        try:
            sources = trigger_data.get('sources', [])
            triggered_by = trigger_data.get('triggered_by', 'unknown')
            logger.info(f"CTI update triggered by {triggered_by} for sources: {sources}")

            for source in sources:
                if source in self.connectors:
                    # Fetch from source and publish to Backend
                    success = await self.manual_update(source)
                    if success:
                        logger.info(f"Successfully processed triggered update for {source}")
                    else:
                        logger.error(f"Failed to process triggered update for {source}")
                else:
                    logger.warning(f"Source {source} not configured, skipping")

        except Exception as e:
            logger.error(f"CTI trigger handler failed: {e}", exc_info=True)