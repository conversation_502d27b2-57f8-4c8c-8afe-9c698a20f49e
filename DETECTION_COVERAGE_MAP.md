# Detection Coverage Map - MITRE ATT&CK vs Log Sources

## Executive Summary

This document maps detection coverage across the MITRE ATT&CK framework based on available log sources, showing exactly which techniques can be detected with different logging configurations.

---

## Coverage Visualization by Kill Chain Stage

### Initial Access (TA0001)

```python
INITIAL_ACCESS_COVERAGE = {
    'T1566': {  # Phishing
        'technique': 'Phishing',
        'sub_techniques': ['Spearphishing Attachment', 'Spearphishing Link'],
        'detection_sources': {
            'email_gateway': {
                'quality': 90,
                'tier': 'GOLD',
                'detects': ['Suspicious attachments', 'Malicious URLs', 'Sender reputation']
            },
            'endpoint_edr': {
                'quality': 70,
                'tier': 'PLATINUM',
                'detects': ['File execution after email', 'Process spawned from Outlook']
            },
            'web_proxy': {
                'quality': 60,
                'tier': 'SILVER',
                'detects': ['Clicked phishing links', 'Payload downloads']
            }
        },
        'minimum_coverage': 'Email Gateway OR EDR',
        'optimal_coverage': 'Email Gateway + EDR + Proxy',
        'blind_spots': ['Encrypted attachments', 'Zero-day exploits']
    },

    'T1078': {  # Valid Accounts
        'technique': 'Valid Accounts',
        'sub_techniques': ['Default', 'Domain', 'Local', 'Cloud'],
        'detection_sources': {
            'identity_logs': {
                'quality': 95,
                'tier': 'PLATINUM',
                'detects': ['Unusual login times', 'Impossible travel', 'New device']
            },
            'vpn_logs': {
                'quality': 85,
                'tier': 'GOLD',
                'detects': ['Remote access patterns', 'Concurrent sessions']
            },
            'cloud_logs': {
                'quality': 80,
                'tier': 'GOLD',
                'detects': ['Cloud console access', 'API usage', 'MFA bypass']
            }
        },
        'minimum_coverage': 'Identity Management System',
        'optimal_coverage': 'AD + Azure AD + Cloud Logs + VPN',
        'blind_spots': ['Legitimate credential use', 'Insider threats']
    },

    'T1190': {  # Exploit Public-Facing Application
        'technique': 'Exploit Public-Facing Application',
        'detection_sources': {
            'waf_logs': {
                'quality': 85,
                'tier': 'GOLD',
                'detects': ['SQL injection', 'XSS attempts', 'Path traversal']
            },
            'web_server_logs': {
                'quality': 65,
                'tier': 'SILVER',
                'detects': ['404 errors', 'Large requests', 'Scanner patterns']
            },
            'network_ids': {
                'quality': 80,
                'tier': 'GOLD',
                'detects': ['Exploit signatures', 'Shellcode', 'Anomalous traffic']
            }
        }
    }
}
```

### Execution (TA0002)

```python
EXECUTION_COVERAGE = {
    'T1059': {  # Command and Scripting Interpreter
        'technique': 'Command and Scripting Interpreter',
        'sub_techniques': ['PowerShell', 'CMD', 'Bash', 'Python', 'JavaScript'],
        'detection_sources': {
            'endpoint_edr': {
                'quality': 95,
                'tier': 'PLATINUM',
                'detects': ['Full command line', 'Script blocks', 'Obfuscation']
            },
            'sysmon': {
                'quality': 90,
                'tier': 'GOLD',
                'detects': ['Process creation', 'Command line arguments']
            },
            'powershell_logs': {
                'quality': 95,
                'tier': 'GOLD',
                'detects': ['Script blocks', 'Transcript', 'Module loading']
            },
            'windows_events': {
                'quality': 60,
                'tier': 'SILVER',
                'detects': ['Basic process creation', 'Limited command line']
            }
        },
        'coverage_by_configuration': {
            'basic': 30,     # Windows Events only
            'enhanced': 70,  # + Sysmon
            'advanced': 95   # + EDR + PowerShell logging
        }
    },

    'T1047': {  # Windows Management Instrumentation
        'technique': 'Windows Management Instrumentation',
        'detection_sources': {
            'endpoint_edr': {
                'quality': 90,
                'tier': 'PLATINUM',
                'detects': ['WMI process creation', 'WMI queries', 'Remote WMI']
            },
            'sysmon': {
                'quality': 85,
                'tier': 'GOLD',
                'detects': ['WmiEventFilter', 'WmiEventConsumer', 'WmiEventConsumerToFilter']
            },
            'windows_events': {
                'quality': 40,
                'tier': 'SILVER',
                'detects': ['Basic WMI activity']
            }
        }
    }
}
```

### Persistence (TA0003)

```python
PERSISTENCE_COVERAGE = {
    'T1547': {  # Boot or Logon Autostart Execution
        'technique': 'Boot or Logon Autostart Execution',
        'sub_techniques': ['Registry Run Keys', 'Startup Folder', 'Winlogon'],
        'detection_sources': {
            'endpoint_edr': {
                'quality': 90,
                'tier': 'PLATINUM',
                'detects': ['Registry modifications', 'File creation', 'Process monitoring']
            },
            'sysmon': {
                'quality': 85,
                'tier': 'GOLD',
                'detects': ['Registry events', 'File creation', 'Process creation']
            },
            'windows_events': {
                'quality': 60,
                'tier': 'SILVER',
                'detects': ['Some registry changes', 'Startup changes']
            },
            'file_integrity': {
                'quality': 70,
                'tier': 'SILVER',
                'detects': ['Startup folder changes', 'System file modifications']
            }
        }
    },

    'T1053': {  # Scheduled Task/Job
        'technique': 'Scheduled Task/Job',
        'sub_techniques': ['At', 'Cron', 'Scheduled Task', 'Systemd'],
        'detection_sources': {
            'endpoint_edr': {
                'quality': 95,
                'tier': 'PLATINUM',
                'detects': ['Task creation', 'Task execution', 'Task modification']
            },
            'windows_events': {
                'quality': 75,
                'tier': 'SILVER',
                'detects': ['Task scheduler events (4698, 4699, 4700)']
            },
            'auditd': {
                'quality': 80,
                'tier': 'GOLD',
                'detects': ['Cron modifications', 'At commands']
            }
        }
    }
}
```

### Privilege Escalation (TA0004)

```python
PRIVILEGE_ESCALATION_COVERAGE = {
    'T1055': {  # Process Injection
        'technique': 'Process Injection',
        'sub_techniques': ['DLL Injection', 'Process Hollowing', 'Thread Hijacking'],
        'detection_sources': {
            'endpoint_edr': {
                'quality': 95,
                'tier': 'PLATINUM',
                'detects': ['Memory modifications', 'Cross-process operations', 'Behavioral anomalies']
            },
            'sysmon': {
                'quality': 80,
                'tier': 'GOLD',
                'detects': ['CreateRemoteThread', 'SetWindowsHookEx', 'Process access']
            },
            'windows_events': {
                'quality': 30,
                'tier': 'SILVER',
                'detects': ['Limited visibility']
            }
        },
        'critical_gap': 'Without EDR or Sysmon, process injection is nearly invisible'
    },

    'T1068': {  # Exploitation for Privilege Escalation
        'technique': 'Exploitation for Privilege Escalation',
        'detection_sources': {
            'endpoint_edr': {
                'quality': 85,
                'tier': 'PLATINUM',
                'detects': ['Exploit behavior', 'Privilege changes', 'Kernel callbacks']
            },
            'windows_events': {
                'quality': 40,
                'tier': 'SILVER',
                'detects': ['Special privilege assignment (4672)']
            },
            'patch_management': {
                'quality': 60,
                'tier': 'BRONZE',
                'detects': ['Unpatched vulnerabilities']
            }
        }
    }
}
```

### Defense Evasion (TA0005)

```python
DEFENSE_EVASION_COVERAGE = {
    'T1070': {  # Indicator Removal on Host
        'technique': 'Indicator Removal on Host',
        'sub_techniques': ['Clear Windows Event Logs', 'Clear Command History', 'File Deletion'],
        'detection_sources': {
            'endpoint_edr': {
                'quality': 90,
                'tier': 'PLATINUM',
                'detects': ['Log clearing attempts', 'File deletion', 'History clearing']
            },
            'sysmon': {
                'quality': 75,
                'tier': 'GOLD',
                'detects': ['Event log cleared (Event ID 23)', 'File deletion']
            },
            'windows_events': {
                'quality': 60,
                'tier': 'SILVER',
                'detects': ['Security log cleared (1102)']
            },
            'file_integrity': {
                'quality': 70,
                'tier': 'SILVER',
                'detects': ['Log file modifications', 'Critical file deletion']
            }
        }
    },

    'T1562': {  # Impair Defenses
        'technique': 'Impair Defenses',
        'sub_techniques': ['Disable Windows Event Logging', 'Disable Security Tools'],
        'detection_sources': {
            'endpoint_edr': {
                'quality': 95,
                'tier': 'PLATINUM',
                'detects': ['Service stops', 'Process termination', 'Registry changes']
            },
            'windows_events': {
                'quality': 70,
                'tier': 'SILVER',
                'detects': ['Service stop events (7034, 7035)']
            },
            'sysmon': {
                'quality': 80,
                'tier': 'GOLD',
                'detects': ['Process termination', 'Service changes']
            }
        }
    }
}
```

### Credential Access (TA0006)

```python
CREDENTIAL_ACCESS_COVERAGE = {
    'T1003': {  # OS Credential Dumping
        'technique': 'OS Credential Dumping',
        'sub_techniques': ['LSASS Memory', 'NTDS', 'SAM', 'Cached Credentials'],
        'detection_sources': {
            'endpoint_edr': {
                'quality': 95,
                'tier': 'PLATINUM',
                'detects': ['LSASS access', 'Mimikatz patterns', 'Credential dumping tools']
            },
            'sysmon': {
                'quality': 75,
                'tier': 'GOLD',
                'detects': ['Process access to LSASS (Event 10)']
            },
            'windows_events': {
                'quality': 60,
                'tier': 'SILVER',
                'detects': ['SAM access (4661)', 'Special privileges (4672)']
            }
        },
        'critical_note': 'LSASS protection and EDR are essential for detection'
    },

    'T1110': {  # Brute Force
        'technique': 'Brute Force',
        'sub_techniques': ['Password Guessing', 'Password Spraying', 'Credential Stuffing'],
        'detection_sources': {
            'identity_logs': {
                'quality': 95,
                'tier': 'PLATINUM',
                'detects': ['Failed authentications', 'Account lockouts', 'Unusual patterns']
            },
            'windows_events': {
                'quality': 85,
                'tier': 'SILVER',
                'detects': ['Failed logons (4625)', 'Account lockout (4740)']
            },
            'network_logs': {
                'quality': 70,
                'tier': 'GOLD',
                'detects': ['Multiple auth attempts', 'Source IP patterns']
            }
        }
    }
}
```

### Lateral Movement (TA0008)

```python
LATERAL_MOVEMENT_COVERAGE = {
    'T1021': {  # Remote Services
        'technique': 'Remote Services',
        'sub_techniques': ['RDP', 'SSH', 'SMB', 'WinRM', 'VNC'],
        'detection_sources': {
            'network_logs': {
                'quality': 85,
                'tier': 'GOLD',
                'detects': ['Connection patterns', 'Unusual ports', 'Traffic volume']
            },
            'identity_logs': {
                'quality': 90,
                'tier': 'PLATINUM',
                'detects': ['Remote authentication', 'Service account usage']
            },
            'endpoint_edr': {
                'quality': 95,
                'tier': 'PLATINUM',
                'detects': ['Remote process creation', 'Network connections', 'Authentication']
            },
            'windows_events': {
                'quality': 70,
                'tier': 'SILVER',
                'detects': ['RDP logons (4624 Type 10)', 'Network logons (Type 3)']
            }
        },
        'correlation_requirement': 'Best detected with network + identity + endpoint'
    },

    'T1570': {  # Lateral Tool Transfer
        'technique': 'Lateral Tool Transfer',
        'detection_sources': {
            'network_logs': {
                'quality': 75,
                'tier': 'GOLD',
                'detects': ['File transfers', 'SMB/RDP file copy']
            },
            'endpoint_edr': {
                'quality': 90,
                'tier': 'PLATINUM',
                'detects': ['File creation', 'Network file transfers']
            },
            'dlp': {
                'quality': 80,
                'tier': 'GOLD',
                'detects': ['Executable transfers', 'Tool signatures']
            }
        }
    }
}
```

### Command and Control (TA0011)

```python
COMMAND_CONTROL_COVERAGE = {
    'T1071': {  # Application Layer Protocol
        'technique': 'Application Layer Protocol',
        'sub_techniques': ['Web Protocols', 'DNS', 'Mail Protocols', 'File Transfer'],
        'detection_sources': {
            'network_ids': {
                'quality': 90,
                'tier': 'GOLD',
                'detects': ['C2 signatures', 'Beaconing', 'Protocol anomalies']
            },
            'proxy_logs': {
                'quality': 85,
                'tier': 'GOLD',
                'detects': ['Suspicious domains', 'User-agent strings', 'Traffic patterns']
            },
            'dns_logs': {
                'quality': 80,
                'tier': 'GOLD',
                'detects': ['DNS tunneling', 'DGA domains', 'Unusual queries']
            },
            'netflow': {
                'quality': 60,
                'tier': 'SILVER',
                'detects': ['Traffic patterns', 'Long connections', 'Data volumes']
            }
        }
    },

    'T1095': {  # Non-Application Layer Protocol
        'technique': 'Non-Application Layer Protocol',
        'detection_sources': {
            'network_ids': {
                'quality': 85,
                'tier': 'GOLD',
                'detects': ['Raw sockets', 'ICMP tunneling', 'Custom protocols']
            },
            'firewall_logs': {
                'quality': 70,
                'tier': 'SILVER',
                'detects': ['Unusual protocols', 'Port usage']
            },
            'netflow': {
                'quality': 65,
                'tier': 'SILVER',
                'detects': ['Protocol anomalies', 'Traffic patterns']
            }
        }
    }
}
```

### Exfiltration (TA0010)

```python
EXFILTRATION_COVERAGE = {
    'T1041': {  # Exfiltration Over C2 Channel
        'technique': 'Exfiltration Over C2 Channel',
        'detection_sources': {
            'network_ids': {
                'quality': 85,
                'tier': 'GOLD',
                'detects': ['Large data transfers', 'Encrypted channels', 'Known C2']
            },
            'dlp': {
                'quality': 90,
                'tier': 'GOLD',
                'detects': ['Sensitive data movement', 'File classification']
            },
            'proxy_logs': {
                'quality': 80,
                'tier': 'GOLD',
                'detects': ['Upload volumes', 'Suspicious destinations']
            },
            'netflow': {
                'quality': 65,
                'tier': 'SILVER',
                'detects': ['Data volume anomalies', 'Connection duration']
            }
        }
    },

    'T1048': {  # Exfiltration Over Alternative Protocol
        'technique': 'Exfiltration Over Alternative Protocol',
        'sub_techniques': ['DNS', 'ICMP', 'Email'],
        'detection_sources': {
            'dns_logs': {
                'quality': 90,
                'tier': 'GOLD',
                'detects': ['DNS tunneling', 'Large TXT records', 'Unusual queries']
            },
            'email_gateway': {
                'quality': 85,
                'tier': 'GOLD',
                'detects': ['Large attachments', 'Unusual recipients', 'Data patterns']
            },
            'network_ids': {
                'quality': 80,
                'tier': 'GOLD',
                'detects': ['Protocol abuse', 'ICMP tunneling']
            }
        }
    }
}
```

---

## Coverage Analysis by Log Source Configuration

### Minimal Configuration (Score: 30-40)

```python
MINIMAL_CONFIGURATION = {
    'sources': [
        'Windows Security Events',
        'Basic Firewall Logs',
        'Active Directory (basic)'
    ],
    'coverage': {
        'techniques_detectable': 45,  # Out of ~200
        'coverage_percentage': 22,
        'high_confidence_detection': 5,
        'medium_confidence_detection': 15,
        'low_confidence_detection': 25
    },
    'strengths': [
        'Authentication events',
        'Basic network connections',
        'Account changes'
    ],
    'critical_gaps': [
        'Process injection invisible',
        'Command line arguments missing',
        'No behavioral detection',
        'Limited lateral movement visibility',
        'No threat intelligence'
    ]
}
```

### Standard Configuration (Score: 60-70)

```python
STANDARD_CONFIGURATION = {
    'sources': [
        'Windows Security Events',
        'Sysmon',
        'Active Directory (advanced)',
        'Firewall + Proxy',
        'Office 365'
    ],
    'coverage': {
        'techniques_detectable': 120,
        'coverage_percentage': 60,
        'high_confidence_detection': 30,
        'medium_confidence_detection': 50,
        'low_confidence_detection': 40
    },
    'strengths': [
        'Good process visibility',
        'Network connections tracked',
        'Authentication monitoring',
        'Email security'
    ],
    'remaining_gaps': [
        'Limited memory analysis',
        'Some process injection missed',
        'No cloud visibility',
        'Limited threat intelligence'
    ]
}
```

### Advanced Configuration (Score: 85-95)

```python
ADVANCED_CONFIGURATION = {
    'sources': [
        'Premium EDR (CrowdStrike/SentinelOne)',
        'Network IDS (Zeek)',
        'Full Identity Stack (AD + Azure AD + Okta)',
        'Cloud Security (AWS + Azure + GCP)',
        'SIEM with Threat Intel',
        'DLP Solution'
    ],
    'coverage': {
        'techniques_detectable': 180,
        'coverage_percentage': 90,
        'high_confidence_detection': 120,
        'medium_confidence_detection': 40,
        'low_confidence_detection': 20
    },
    'strengths': [
        'Complete visibility',
        'Behavioral detection',
        'Memory analysis',
        'Threat intelligence enrichment',
        'Cloud and SaaS coverage'
    ],
    'minor_gaps': [
        'Some encrypted traffic',
        'Legitimate tool abuse',
        'Zero-day exploits',
        'Supply chain attacks'
    ]
}
```

---

## Coverage Improvement Roadmap

### Phase 1: Critical Gaps (0-3 months)
**Target: 30% → 50% coverage**

```python
PHASE_1_IMPROVEMENTS = {
    'add_sources': [
        {'source': 'Sysmon', 'cost': '$0', 'impact': '+15% coverage'},
        {'source': 'PowerShell logging', 'cost': '$0', 'impact': '+10% coverage'},
        {'source': 'Advanced AD auditing', 'cost': '$0', 'impact': '+5% coverage'}
    ],
    'expected_results': {
        'before': 45,
        'after': 90,
        'techniques_gained': [
            'Process creation with command line',
            'PowerShell attacks',
            'Registry persistence',
            'Basic process injection'
        ]
    }
}
```

### Phase 2: Visibility Enhancement (3-6 months)
**Target: 50% → 75% coverage**

```python
PHASE_2_IMPROVEMENTS = {
    'add_sources': [
        {'source': 'EDR solution', 'cost': '$50-150K', 'impact': '+20% coverage'},
        {'source': 'Network IDS', 'cost': '$30-80K', 'impact': '+10% coverage'},
        {'source': 'Cloud logging', 'cost': '$10-30K', 'impact': '+5% coverage'}
    ],
    'expected_results': {
        'techniques_gained': [
            'Advanced process injection',
            'Memory attacks',
            'Lateral movement',
            'C2 detection',
            'Cloud attacks'
        ]
    }
}
```

### Phase 3: Advanced Detection (6-12 months)
**Target: 75% → 90% coverage**

```python
PHASE_3_IMPROVEMENTS = {
    'add_sources': [
        {'source': 'Behavioral analytics', 'cost': '$100K+', 'impact': '+10% coverage'},
        {'source': 'Threat intelligence', 'cost': '$50K+', 'impact': '+5% coverage'},
        {'source': 'Deception technology', 'cost': '$50K+', 'impact': '+5% coverage'}
    ],
    'expected_results': {
        'techniques_gained': [
            'Unknown malware',
            'Zero-day exploits',
            'Advanced persistent threats',
            'Insider threats'
        ]
    }
}
```

---

## Critical Coverage Insights

### Must-Have Coverage Areas

1. **Process Creation & Command Lines**
   - Minimum: Sysmon
   - Optimal: EDR
   - Without: Blind to 50% of attacks

2. **Network Connections**
   - Minimum: Firewall logs
   - Optimal: IDS + Netflow + Proxy
   - Without: Miss lateral movement and C2

3. **Authentication Events**
   - Minimum: Windows Events
   - Optimal: AD + Cloud Identity
   - Without: Miss account compromise

4. **File System Activity**
   - Minimum: File integrity monitoring
   - Optimal: EDR with real-time monitoring
   - Without: Miss persistence and data theft

### Coverage vs Investment Analysis

```python
COVERAGE_ROI = {
    'free_improvements': {
        'coverage_gain': 20,
        'techniques': ['Sysmon', 'PowerShell logging', 'Advanced auditing'],
        'effort': 'Configuration only'
    },
    'low_cost': {
        'investment': '<$50K',
        'coverage_gain': 15,
        'techniques': ['Basic EDR', 'Cloud logging', 'SIEM'],
    },
    'medium_cost': {
        'investment': '$50-200K',
        'coverage_gain': 25,
        'techniques': ['Premium EDR', 'Network IDS', 'SOAR'],
    },
    'high_cost': {
        'investment': '>$200K',
        'coverage_gain': 15,
        'techniques': ['Full stack', 'Behavioral analytics', 'Threat hunting team'],
    }
}
```

---

## Conclusion

Detection coverage is directly proportional to:
1. **Quality of log sources** (PLATINUM > GOLD > SILVER > BRONZE)
2. **Diversity of sources** (Endpoint + Network + Identity minimum)
3. **Proper configuration** (Even good tools need proper setup)
4. **Correlation capability** (Multiple sources seeing same event)

Key findings:
- **Without EDR**: Missing 40-50% of techniques
- **Without network visibility**: Missing 30% of techniques
- **Without identity monitoring**: Missing 25% of techniques
- **With all three**: Can detect 85-90% of techniques

Organizations should prioritize based on:
1. **Highest risk** techniques for their environment
2. **Biggest coverage** gains per dollar spent
3. **Integration** with existing tools
4. **Team capabilities** to use the data