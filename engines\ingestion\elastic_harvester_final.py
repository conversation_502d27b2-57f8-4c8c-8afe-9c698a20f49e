"""
Elastic Rule Harvester - Final Implementation with Proper Flow
Version 3: Database comparison + Sigma conversion + Minimal AI

Flow:
    1. Harvest from Elastic
    2. Convert to Sigma (universal format)
    3. Compare against existing rules in DB
    4. Route based on comparison:
        - Duplicate → Skip
        - Similar → Contextualization for analysis
        - New → Full pipeline (Contextualization → Intelligence → Backend)
        - Broken/Uninterpretable → Intelligence for AI assistance
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from elastic_rule_harvester import ElasticRuleHarvester, ElasticRule
from sigma_converter import ElasticToSigmaConverter, SigmaRule, find_duplicate_rules


class ElasticRulePipeline:
    """
    Complete pipeline for harvesting, comparing, and processing Elastic rules

    Key Principles:
        1. Convert to Sigma first (universal format)
        2. Compare against existing rules (deduplication)
        3. Minimize AI use - only when structure is unclear
        4. Store everything in pattern_library for future comparison
    """

    def __init__(self, redis_client, db_connection, logger: Optional[logging.Logger] = None):
        self.redis_client = redis_client
        self.db_connection = db_connection
        self.logger = logger or logging.getLogger(__name__)

        self.harvester = ElasticRuleHarvester(self.logger)
        self.sigma_converter = ElasticToSigmaConverter()

    async def process_harvest(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main pipeline: Harvest → Convert → Compare → Route

        Args:
            config: Elastic connection configuration

        Returns:
            Processing statistics and results
        """
        stats = {
            'harvested': 0,
            'duplicates': 0,
            'similar': 0,
            'new_rules': 0,
            'sent_for_analysis': 0,
            'skipped': 0,
            'errors': 0
        }

        try:
            # Step 1: Harvest from Elastic
            self.logger.info("Step 1: Harvesting rules from Elastic...")
            if not await self.harvester.configure(config):
                return {'success': False, 'error': 'Failed to configure Elastic connection'}

            results = await self.harvester.harvest_all()
            stats['harvested'] = len(results['detection_rules'])

            # Step 2: Load existing rules from database for comparison
            self.logger.info("Step 2: Loading existing rules from database...")
            existing_rules = self._load_existing_rules_from_db()

            # Step 3: Process each harvested rule
            self.logger.info(f"Step 3: Processing {stats['harvested']} harvested rules...")
            for elastic_rule in results['detection_rules']:
                try:
                    await self._process_single_rule(elastic_rule, existing_rules, stats)
                except Exception as e:
                    self.logger.error(f"Error processing rule {elastic_rule.name}: {e}")
                    stats['errors'] += 1

            return {
                'success': True,
                'statistics': stats,
                'harvest_time': results['harvest_time']
            }

        except Exception as e:
            self.logger.error(f"Pipeline failed: {e}")
            return {'success': False, 'error': str(e)}
        finally:
            await self.harvester.close()

    async def _process_single_rule(self, elastic_rule: ElasticRule,
                                   existing_rules: List[SigmaRule],
                                   stats: Dict[str, int]):
        """
        Process a single rule through the pipeline

        Steps:
            1. Convert to Sigma format
            2. Check for duplicates in DB
            3. Route based on comparison result
        """
        # Step 1: Convert to Sigma (universal format)
        sigma_rule = self.sigma_converter.convert_elastic_rule({
            'rule_id': elastic_rule.rule_id,
            'name': elastic_rule.name,
            'description': elastic_rule.description,
            'query': elastic_rule.query,
            'language': elastic_rule.language,
            'severity': elastic_rule.severity,
            'enabled': elastic_rule.enabled,
            'mitre_techniques': elastic_rule.mitre_techniques,
            'tags': elastic_rule.tags,
            'index_patterns': elastic_rule.index_patterns,
            'false_positives': elastic_rule.false_positives
        })

        # Step 2: Compare against existing rules
        matches = find_duplicate_rules(sigma_rule, existing_rules, threshold=0.7)

        # Step 3: Route based on comparison
        if matches and matches[0]['similarity_score'] > 0.95:
            # Near duplicate - skip
            self.logger.info(f"SKIP: '{sigma_rule.title}' is duplicate of '{matches[0]['rule_title']}'")
            stats['duplicates'] += 1
            stats['skipped'] += 1
            return

        elif matches and matches[0]['similarity_score'] > 0.7:
            # Similar - send for contextualization analysis only
            self.logger.info(f"SIMILAR: '{sigma_rule.title}' similar to '{matches[0]['rule_title']}' ({matches[0]['similarity_score']:.0%})")
            await self._send_for_contextualization_only(sigma_rule, elastic_rule, matches)
            stats['similar'] += 1
            stats['sent_for_analysis'] += 1

        else:
            # New rule - full pipeline
            self.logger.info(f"NEW: '{sigma_rule.title}' - sending through full pipeline")
            await self._send_through_full_pipeline(sigma_rule, elastic_rule)
            stats['new_rules'] += 1
            stats['sent_for_analysis'] += 1

    def _load_existing_rules_from_db(self) -> List[SigmaRule]:
        """
        Load existing rules from pattern_library database

        Returns list of Sigma rules for comparison
        """
        cursor = self.db_connection.cursor()
        existing_rules = []

        try:
            # Query all detection rules in pattern library
            cursor.execute("""
                SELECT pattern_id, pattern_name, pattern_data
                FROM pattern_library
                WHERE pattern_type IN ('detection_rule', 'sigma_rule')
                AND is_active = TRUE
            """)

            for row in cursor.fetchall():
                pattern_data = row[2]

                # Convert stored pattern to Sigma format for comparison
                if 'sigma_rule' in pattern_data:
                    # Already in Sigma format
                    sigma_data = pattern_data['sigma_rule']
                    sigma_rule = SigmaRule(**sigma_data)
                    existing_rules.append(sigma_rule)

                elif 'detection' in pattern_data:
                    # Has detection logic - create minimal Sigma rule
                    sigma_rule = SigmaRule(
                        title=pattern_data.get('name', row[1]),
                        id=row[0],
                        status='stable',
                        description=pattern_data.get('description', ''),
                        author='existing',
                        date='2025-01-01',
                        tags=pattern_data.get('tags', []),
                        logsource=pattern_data.get('logsource', {}),
                        detection=pattern_data.get('detection', {}),
                        level=pattern_data.get('severity', 'medium')
                    )
                    existing_rules.append(sigma_rule)

            self.logger.info(f"Loaded {len(existing_rules)} existing rules for comparison")
            return existing_rules

        except Exception as e:
            self.logger.error(f"Error loading existing rules: {e}")
            return []
        finally:
            cursor.close()

    async def _send_for_contextualization_only(self, sigma_rule: SigmaRule,
                                                elastic_rule: ElasticRule,
                                                similar_matches: List[Dict]):
        """
        Send to Contextualization Engine for analysis only (no AI)

        For similar rules, we just want structural analysis:
            - What entities does it detect?
            - How does it differ from existing rule?
            - Should we merge or keep separate?

        NO AI NEEDED - just deterministic comparison
        """
        message = {
            'type': 'analyze_similar_rule',
            'sigma_rule': sigma_rule.to_dict(),
            'elastic_rule': {
                'rule_id': elastic_rule.rule_id,
                'name': elastic_rule.name,
                'query': elastic_rule.query,
                'language': elastic_rule.language,
                'risk_score': elastic_rule.risk_score
            },
            'similar_to': similar_matches[0],  # Most similar match
            'analysis_needed': [
                'extract_entities',        # What does this rule look for?
                'compare_coverage',        # Coverage overlap with existing?
                'merge_recommendation'     # Should we merge these rules?
            ],
            'skip_ai': True,  # NO AI - just structural analysis
            'next_action': 'store_or_merge',  # After analysis, decide to store or merge
            'timestamp': datetime.utcnow().isoformat()
        }

        await self.redis_client.publish(
            'contextualization.analyze_rule',
            json.dumps(message)
        )

        self.logger.debug(f"Sent '{sigma_rule.title}' for contextualization-only analysis")

    async def _send_through_full_pipeline(self, sigma_rule: SigmaRule, elastic_rule: ElasticRule):
        """
        Send new rule through full pipeline

        Flow:
            Contextualization → Intelligence (only if structure unclear) → Backend

        Contextualization:
            - Parse detection logic
            - Extract entities
            - Map MITRE techniques to actual detections
            - Identify use case

        Intelligence:
            - ONLY invoked if Contextualization can't understand structure
            - OR if rule quality assessment needed
            - NOT for every rule

        Backend:
            - Store in pattern_library
            - Generate multi-SIEM versions
        """
        message = {
            'type': 'new_detection_rule',
            'sigma_rule': sigma_rule.to_dict(),
            'elastic_rule': {
                'rule_id': elastic_rule.rule_id,
                'name': elastic_rule.name,
                'description': elastic_rule.description,
                'query': elastic_rule.query,
                'language': elastic_rule.language,
                'rule_type': elastic_rule.rule_type,
                'severity': elastic_rule.severity,
                'risk_score': elastic_rule.risk_score,
                'mitre_techniques': elastic_rule.mitre_techniques,
                'tags': elastic_rule.tags,
                'filters': elastic_rule.filters,
                'enabled': elastic_rule.enabled
            },
            'analysis_pipeline': [
                'contextualization',  # ALWAYS FIRST
                'intelligence_if_needed',  # ONLY IF STRUCTURE UNCLEAR
                'backend'  # ALWAYS LAST - store the result
            ],
            'ai_rules': {
                'use_ai_only_if': [
                    'detection_logic_unclear',
                    'query_parsing_failed',
                    'no_entities_extracted',
                    'quality_assessment_requested'
                ]
            },
            'timestamp': datetime.utcnow().isoformat()
        }

        await self.redis_client.publish(
            'contextualization.analyze_rule',
            json.dumps(message)
        )

        self.logger.debug(f"Sent '{sigma_rule.title}' through full pipeline")

    async def store_processed_rule(self, sigma_rule: SigmaRule, analysis_result: Dict):
        """
        Store processed rule in pattern_library

        Called by Backend Engine after all analysis is complete
        """
        cursor = self.db_connection.cursor()

        try:
            pattern_data = {
                'sigma_rule': sigma_rule.to_dict(),
                'sigma_hash': sigma_rule.get_hash(),
                'analysis': analysis_result,
                'source': 'elastic_security',
                'processed_at': datetime.utcnow().isoformat()
            }

            cursor.execute("""
                INSERT INTO pattern_library
                (pattern_id, pattern_type, pattern_name, pattern_data, source_type, is_active, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (pattern_id) DO UPDATE SET
                    pattern_data = EXCLUDED.pattern_data,
                    updated_at = CURRENT_TIMESTAMP
            """, (
                sigma_rule.id,
                'sigma_rule',
                sigma_rule.title,
                json.dumps(pattern_data),
                'elastic_security',
                True,
                datetime.utcnow()
            ))

            self.db_connection.commit()
            self.logger.info(f"Stored rule '{sigma_rule.title}' in pattern library")

        except Exception as e:
            self.db_connection.rollback()
            self.logger.error(f"Failed to store rule: {e}")
            raise
        finally:
            cursor.close()


class RuleDeduplicationService:
    """
    Service for comparing and deduplicating rules across SIEM platforms

    Uses Sigma as universal format for comparison
    """

    def __init__(self, db_connection, logger: Optional[logging.Logger] = None):
        self.db_connection = db_connection
        self.logger = logger or logging.getLogger(__name__)

    def find_duplicates_in_database(self, sigma_rule: SigmaRule) -> Dict[str, Any]:
        """
        Search database for duplicate or similar rules

        Returns:
            {
                'is_duplicate': bool,
                'exact_matches': List[Dict],
                'similar_rules': List[Dict],
                'recommendation': str
            }
        """
        cursor = self.db_connection.cursor()

        try:
            # Search by hash first (exact duplicate)
            rule_hash = sigma_rule.get_hash()

            cursor.execute("""
                SELECT pattern_id, pattern_name, pattern_data
                FROM pattern_library
                WHERE pattern_data->>'sigma_hash' = %s
                AND is_active = TRUE
            """, (rule_hash,))

            exact_match = cursor.fetchone()

            if exact_match:
                return {
                    'is_duplicate': True,
                    'exact_matches': [{
                        'pattern_id': exact_match[0],
                        'pattern_name': exact_match[1],
                        'similarity': 1.0
                    }],
                    'similar_rules': [],
                    'recommendation': 'skip'  # Already have this rule
                }

            # Search by MITRE techniques (potential similarity)
            similar_rules = []

            if sigma_rule.tags:
                # Extract MITRE tags
                mitre_tags = [t for t in sigma_rule.tags if t.startswith('attack.')]

                if mitre_tags:
                    cursor.execute("""
                        SELECT pattern_id, pattern_name, pattern_data
                        FROM pattern_library
                        WHERE pattern_data->'sigma_rule'->'tags' ?| %s
                        AND is_active = TRUE
                        LIMIT 10
                    """, (mitre_tags,))

                    for row in cursor.fetchall():
                        similar_rules.append({
                            'pattern_id': row[0],
                            'pattern_name': row[1],
                            'mitre_overlap': True
                        })

            if similar_rules:
                return {
                    'is_duplicate': False,
                    'exact_matches': [],
                    'similar_rules': similar_rules,
                    'recommendation': 'analyze'  # Need deeper analysis
                }

            # No matches found
            return {
                'is_duplicate': False,
                'exact_matches': [],
                'similar_rules': [],
                'recommendation': 'new'  # Completely new rule
            }

        finally:
            cursor.close()


# Example usage
async def test_pipeline():
    """Test the complete pipeline"""
    import redis.asyncio as redis
    import psycopg2

    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # Connect to services
    redis_client = await redis.Redis(host='localhost', port=6380, decode_responses=True)
    db_conn = psycopg2.connect(
        dbname='siemless_v2',
        user='siemless',
        password='siemless123',
        host='localhost',
        port=5433
    )

    # Configuration
    config = {
        'url': 'https://your-elastic:9200',
        'api_key': 'your_api_key',
        'verify_ssl': False
    }

    # Create pipeline
    pipeline = ElasticRulePipeline(redis_client, db_conn, logger)

    # Process harvest
    result = await pipeline.process_harvest(config)

    print("\nPipeline Results:")
    print(json.dumps(result, indent=2))

    # Cleanup
    await redis_client.close()
    db_conn.close()


if __name__ == "__main__":
    asyncio.run(test_pipeline())
