"""
SIEMLess v2.0 - Contextualization Engine
Entity enrichment, relationship mapping, and context building
"""

import asyncio
import json
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from base_engine import BaseEngine
from entity_extractor import EntityExtractor
from enrichment_service import EnrichmentService
from adaptive_entity_extractor import AdaptiveEntityExtractor
from cti_enrichment_pipeline import CTIEnrichmentPipeline
from log_schema_detector import LogSchemaDetector, generate_schema_hash
from deterministic_extractor import DeterministicExtractor

class ContextualizationEngine(BaseEngine):
    """Contextualization Engine for SIEMLess v2.0"""

    def __init__(self):
        super().__init__("contextualization")
        self.entity_cache = {}
        self.relationship_cache = {}
        self.enrichment_stats = {
            'entities_processed': 0,
            'entities_extracted': 0,
            'relationships_created': 0,
            'enrichments_applied': 0,
            'cache_hits': 0,
            'logs_processed': 0
        }
        self.enrichment_sources = {
            'threat_intel': {'enabled': True, 'priority': 1},
            'geolocation': {'enabled': True, 'priority': 2},
            'asset_inventory': {'enabled': True, 'priority': 3},
            'user_directory': {'enabled': True, 'priority': 4}
        }

        # Initialize extractors and enrichers
        self.entity_extractor = EntityExtractor()
        self.enrichment_service = EnrichmentService(self.redis_client)
        self.adaptive_extractor = None  # Will be initialized after redis is connected
        self.cti_enrichment = CTIEnrichmentPipeline(self.redis_client)  # CTI enrichment pipeline

        # Schema detection and deterministic extraction
        self.schema_detector = LogSchemaDetector(self.redis_client, self.db_pool, self.logger)
        self.deterministic_extractor = DeterministicExtractor(self.logger)

        # Pending schema mapping requests
        self.pending_mapping_requests = {}

    def start_engine_tasks(self) -> List[asyncio.Task]:
        """Start contextualization-specific background tasks"""
        tasks = []

        # Initialize adaptive extractor (needs to be async so do it here)
        tasks.append(asyncio.create_task(self._init_adaptive_extractor()))

        # Entity enrichment task
        tasks.append(asyncio.create_task(self._enrichment_loop()))

        # Cache maintenance task
        tasks.append(asyncio.create_task(self._cache_maintenance_loop()))

        # Relationship analysis task
        tasks.append(asyncio.create_task(self._relationship_analysis_loop()))

        self.logger.info("Contextualization Engine tasks started")
        return tasks

    async def _init_adaptive_extractor(self):
        """Initialize adaptive extractor with async Redis"""
        try:
            import redis.asyncio as redis_async
            import os

            async_redis = await redis_async.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                decode_responses=True
            )
            self.adaptive_extractor = AdaptiveEntityExtractor(async_redis, self.logger)
            self.logger.info("Adaptive Entity Extractor initialized successfully")

            # Keep alive loop - this task needs to stay running
            while self.is_running:
                await asyncio.sleep(60)  # Check every minute
        except Exception as e:
            self.logger.error(f"Failed to initialize Adaptive Entity Extractor: {e}")
            raise

    def get_subscribed_channels(self) -> List[str]:
        """Return list of Redis channels this engine subscribes to"""
        return [
            'contextualization.process_log',  # NEW: Process logs and extract entities
            'contextualization.extract_entities',  # NEW: For adaptive extraction from ingestion
            'contextualization.enrich_log',
            'contextualization.enrich_entity',
            'contextualization.find_relationships',
            'contextualization.get_context',
            'contextualization.validate_parser',  # Parser validation requests
            'contextualization.extract_from_context',  # Extract entities from investigation context
            'cti.enrichment.iocs',  # ✨ NEW: Real-time CTI IOC updates for enrichment cache
            'contextualization.enrich_alert',  # ✨ Alert enrichment for investigation
            'contextualization.mapping_generated.*'  # NEW: Schema mapping responses from Intelligence Engine
        ]

    async def process_message(self, message: Dict[str, Any]):
        """Process incoming message from message queue"""
        try:
            data = json.loads(message['data'])
            channel = message['channel']

            self.logger.info(f"Processing message from {channel}")

            # Handle both data['data'] and direct data formats
            message_data = data.get('data', data)

            if channel == 'contextualization.process_log':
                await self._handle_process_log(message_data)
            elif channel == 'contextualization.extract_entities':
                await self._handle_extract_entities_adaptive(message_data)
            elif channel == 'contextualization.enrich_log':
                await self._handle_enrich_log(message_data)
            elif channel == 'contextualization.enrich_entity':
                await self._handle_enrich_entity(message_data)
            elif channel == 'contextualization.find_relationships':
                await self._handle_find_relationships(message_data)
            elif channel == 'contextualization.get_context':
                await self._handle_get_context(message_data)
            elif channel == 'contextualization.validate_parser':
                await self._handle_validate_parser(message_data)
            elif channel == 'contextualization.extract_from_context':
                await self._handle_extract_from_context(message_data)
            elif channel == 'contextualization.enrich_alert':
                await self._handle_enrich_alert(message_data)
            elif channel == 'cti.enrichment.iocs':
                # ✨ NEW: Handle real-time CTI IOC updates
                await self._handle_cti_ioc_update(message_data)
            elif channel.startswith('contextualization.mapping_generated.'):
                # NEW: Handle schema mapping responses
                await self._handle_mapping_generated(message_data)

        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            self.logger.error(f"Message data: {message.get('data', 'No data')}")

    async def _handle_process_log(self, data: Dict[str, Any]):
        """
        Handle log processing with schema detection
        Flow: Detect schema -> Use mapping (deterministic) OR Request AI generation
        """
        log = data.get('log', {})
        pattern_type = data.get('pattern_type')
        entity_hints = data.get('entity_hints', [])
        log_id = data.get('log_id')

        extracted_entities = []

        # STEP 1: Try schema detection (FREE, FAST)
        schema_info = self.schema_detector.detect_schema(log)

        if schema_info:
            # KNOWN SCHEMA - Use deterministic extraction (FREE)
            self.logger.info(f"Known schema detected: {schema_info.get('schema_name')} for log {log_id}")
            self.enrichment_stats['logs_processed'] += 1

            # Extract entities using stored mapping (NO AI, FREE)
            deterministic_entities = self.deterministic_extractor.extract(
                log,
                schema_info['entity_mapping']
            )

            # Store deterministically extracted entities
            for entity in deterministic_entities:
                entity_id = await self._store_entity(
                    entity_type=entity['type'],
                    entity_value=entity['value'],
                    source_log_id=log_id,
                    extracted_from=entity.get('extracted_from', 'deterministic')
                )
                if entity_id:
                    extracted_entities.append({
                        'entity_id': entity_id,
                        'entity_type': entity['type'],
                        'entity_value': entity['value']
                    })

            # Update schema usage statistics
            from log_schema_detector import db_execute
            db_execute(
                self.db_pool,
                "SELECT increment_schema_usage(%s, %s)",
                schema_info['schema_id'],
                True
            )

        else:
            # NEW SCHEMA - Request AI mapping generation (ONE-TIME COST)
            schema_hash = generate_schema_hash(log)
            self.logger.info(f"New schema detected (hash: {schema_hash[:8]}...) - requesting AI mapping")

            request_id = str(uuid.uuid4())
            response_channel = f'contextualization.mapping_generated.{request_id}'

            # Store pending request
            self.pending_mapping_requests[request_id] = {
                'log': log,
                'log_id': log_id,
                'pattern_type': pattern_type,
                'entity_hints': entity_hints,
                'timestamp': time.time()
            }

            # Request AI mapping from Intelligence Engine
            await self.publish_message('intelligence.generate_log_mapping', {
                'request_id': request_id,
                'sample_log': log,
                'schema_hash': schema_hash,
                'models': ['free', 'mid_quality'],  # Gemma + Sonnet (~$0.008)
                'response_channel': response_channel
            })

            self.logger.info(f"Waiting for AI mapping generation (request_id: {request_id})")
            # Exit here - will continue in _handle_mapping_generated
            return

        # Use entity hints from pattern matcher (if available)
        for hint in entity_hints:
            if hint.get('type') and hint.get('value'):
                entity_id = await self._store_entity(
                    entity_type=hint['type'],
                    entity_value=hint['value'],
                    source_log_id=log_id,
                    extracted_from=hint.get('field', 'pattern_match')
                )
                if entity_id:
                    extracted_entities.append({
                        'entity_id': entity_id,
                        'entity_type': hint['type'],
                        'entity_value': hint['value']
                    })

        # Create relationships between entities from same log
        relationships = []
        if len(extracted_entities) > 1:
            relationships = await self._create_entity_relationships(extracted_entities, log_id)
            self.logger.info(f"Created {len(relationships)} relationships")

        self.logger.info(f"Extracted {len(extracted_entities)} entities from log {log_id}")

        # Enrich ALL entities
        enriched_entities = []
        for entity_info in extracted_entities:
            enriched = await self._enrich_stored_entity(
                entity_info['entity_id'],
                entity_info['entity_type'],
                entity_info['entity_value']
            )
            enriched_entities.append(enriched)

        # Send enriched intelligence to backend (NOT the full log!)
        await self.publish_message('backend.store_intelligence', {
            'log_id': log_id,
            'pattern_type': pattern_type,
            'entities': enriched_entities,
            'relationships': relationships,
            'entity_count': len(extracted_entities),
            'timestamp': datetime.utcnow().isoformat(),
            'metadata_only': True  # Signal this is lightweight storage
        })

    async def _handle_enrich_log(self, data: Dict[str, Any]):
        """Handle log enrichment request"""
        log = data.get('log', {})
        enrich_level = data.get('enrich_level', 'standard')

        # Extract entities from log
        entities = await self._extract_entities(log)

        # Enrich each entity
        enriched_entities = {}
        for entity_type, entity_value in entities.items():
            enriched_data = await self._enrich_entity(entity_type, entity_value, enrich_level)
            enriched_entities[entity_type] = enriched_data

        # Create enriched log
        enriched_log = {
            'original_log': log,
            'entities': enriched_entities,
            'enrichment_timestamp': datetime.utcnow().isoformat(),
            'enrich_level': enrich_level
        }

        # Send to Delivery Engine for storage and presentation
        self.publish_message('delivery.store_enriched_log', {
            'enriched_log': enriched_log,
            'priority': 'normal'
        })

        self.enrichment_stats['entities_processed'] += len(entities)
        self.logger.info(f"Enriched log with {len(entities)} entities")

    async def _handle_mapping_generated(self, data: Dict[str, Any]):
        """
        Handle AI-generated schema mapping response
        Continue processing log with newly generated mapping
        """
        request_id = data.get('request_id')
        schema_hash = data.get('schema_hash')
        entity_mapping = data.get('entity_mapping', {})
        schema_name = data.get('schema_name', 'unknown')
        vendor = data.get('vendor', 'Unknown')
        confidence = data.get('confidence', 0.0)

        self.logger.info(f"Received AI mapping for schema {schema_name} (confidence: {confidence})")

        # Retrieve pending request
        pending = self.pending_mapping_requests.pop(request_id, None)
        if not pending:
            self.logger.warning(f"No pending request found for {request_id}")
            return

        log = pending['log']
        log_id = pending['log_id']
        pattern_type = pending['pattern_type']
        entity_hints = pending['entity_hints']

        # Register new schema in database
        schema_id = self.schema_detector.register_new_schema(
            log=log,
            entity_mapping=entity_mapping,
            confidence=confidence,
            generated_by='ai'
        )

        self.logger.info(f"Registered new schema {schema_id} ({schema_name})")

        # Extract entities using newly generated mapping
        deterministic_entities = self.deterministic_extractor.extract(
            log,
            entity_mapping
        )

        extracted_entities = []

        # Store deterministically extracted entities
        for entity in deterministic_entities:
            entity_id = self._store_entity(
                entity_type=entity['type'],
                entity_value=entity['value'],
                source_log_id=log_id,
                extracted_from=entity.get('extracted_from', 'ai_generated_mapping')
            )
            if entity_id:
                extracted_entities.append({
                    'entity_id': entity_id,
                    'entity_type': entity['type'],
                    'entity_value': entity['value']
                })

        # Use entity hints from pattern matcher (if available)
        for hint in entity_hints:
            if hint.get('type') and hint.get('value'):
                entity_id = await self._store_entity(
                    entity_type=hint['type'],
                    entity_value=hint['value'],
                    source_log_id=log_id,
                    extracted_from=hint.get('field', 'pattern_match')
                )
                if entity_id:
                    extracted_entities.append({
                        'entity_id': entity_id,
                        'entity_type': hint['type'],
                        'entity_value': hint['value']
                    })

        # Create relationships between entities from same log
        relationships = []
        if len(extracted_entities) > 1:
            relationships = await self._create_entity_relationships(extracted_entities, log_id)
            self.logger.info(f"Created {len(relationships)} relationships")

        self.logger.info(f"Extracted {len(extracted_entities)} entities from log {log_id} using new mapping")

        # Enrich ALL entities
        enriched_entities = []
        for entity_info in extracted_entities:
            enriched = await self._enrich_stored_entity(
                entity_info['entity_id'],
                entity_info['entity_type'],
                entity_info['entity_value']
            )
            enriched_entities.append(enriched)

        # Send enriched intelligence to backend
        await self.publish_message('backend.store_intelligence', {
            'log_id': log_id,
            'pattern_type': pattern_type,
            'entities': enriched_entities,
            'relationships': relationships,
            'entity_count': len(extracted_entities),
            'timestamp': datetime.utcnow().isoformat(),
            'metadata_only': True,
            'schema_id': schema_id,
            'schema_name': schema_name
        })

    async def _handle_enrich_entity(self, data: Dict[str, Any]):
        """Handle direct entity enrichment request"""
        entity_type = data.get('entity_type')
        entity_value = data.get('entity_value')
        enrich_level = data.get('enrich_level', 'standard')

        enriched_data = await self._enrich_entity(entity_type, entity_value, enrich_level)

        # Publish enrichment result
        self.publish_message('contextualization.entity_enriched', {
            'entity_type': entity_type,
            'entity_value': entity_value,
            'enriched_data': enriched_data,
            'timestamp': datetime.utcnow().isoformat()
        })

    async def _handle_find_relationships(self, data: Dict[str, Any]):
        """Handle relationship discovery request"""
        entity = data.get('entity', {})
        relationship_types = data.get('types', ['all'])

        relationships = await self._find_entity_relationships(entity, relationship_types)

        # Publish relationship results
        self.publish_message('contextualization.relationships_found', {
            'entity': entity,
            'relationships': relationships,
            'timestamp': datetime.utcnow().isoformat()
        })

        self.enrichment_stats['relationships_created'] += len(relationships)

    async def _handle_get_context(self, data: Dict[str, Any]):
        """Handle context retrieval request"""
        entity = data.get('entity', {})
        context_depth = data.get('depth', 2)

        context = await self._build_entity_context(entity, context_depth)

        # Publish context result
        self.publish_message('contextualization.context_response', {
            'entity': entity,
            'context': context,
            'timestamp': datetime.utcnow().isoformat()
        })

    async def _extract_entities(self, log: Dict[str, Any]) -> Dict[str, str]:
        """Extract entities from log data (compatible with real ingestion data)"""
        entities = {}

        # Handle multiple nesting levels from ingestion pipeline
        log_data = log
        if 'data' in log and 'log' in log['data']:
            log_data = log['data']['log']  # From ingestion engine format
        elif 'data' in log:
            log_data = log['data']  # Alternative format
        elif 'log' in log:
            log_data = log['log']  # Direct format

        # Extract IP addresses from multiple possible fields
        ip_fields = ['source_ip', 'dest_ip', 'src_ip', 'dst_ip', 'source.ip', 'destination.ip']
        for field in ip_fields:
            if '.' in field:  # Handle nested fields like 'source.ip'
                value = self._get_nested_value(log_data, field)
            else:
                value = log_data.get(field)

            if value:
                # Convert to string if it's a complex object
                entity_value = str(value) if isinstance(value, dict) else value
                entities[f'ip_{field.replace(".", "_")}'] = entity_value

        # Extract hostnames from multiple fields
        hostname_fields = ['host', 'hostname', 'observer.name', 'host.name']
        for field in hostname_fields:
            if '.' in field:
                value = self._get_nested_value(log_data, field)
            else:
                value = log_data.get(field)

            if value:
                # Convert to string if it's a complex object
                entity_value = str(value) if isinstance(value, dict) else value
                entities[f'hostname_{field.replace(".", "_")}'] = entity_value

        # Extract users
        user_fields = ['user', 'username', 'user_name', 'user.name']
        for field in user_fields:
            if '.' in field:
                value = self._get_nested_value(log_data, field)
            else:
                value = log_data.get(field)

            if value:
                # Convert to string if it's a complex object
                entity_value = str(value) if isinstance(value, dict) else value
                entities[f'user_{field.replace(".", "_")}'] = entity_value

        # Extract processes
        process_fields = ['process', 'process_name', 'process.name']
        for field in process_fields:
            if '.' in field:
                value = self._get_nested_value(log_data, field)
            else:
                value = log_data.get(field)

            if value:
                # Convert to string if it's a complex object
                entity_value = str(value) if isinstance(value, dict) else value
                entities[f'process_{field.replace(".", "_")}'] = entity_value

        # Extract file hashes
        hash_fields = ['file_hash', 'hash', 'md5', 'sha1', 'sha256']
        for field in hash_fields:
            value = log_data.get(field)
            if value:
                # Convert to string if it's a complex object
                entity_value = str(value) if isinstance(value, dict) else value
                entities[f'hash_{field}'] = entity_value

        return entities

    def _get_nested_value(self, data: Dict[str, Any], field_path: str) -> Any:
        """Get value from nested dictionary using dot notation"""
        try:
            value = data
            for key in field_path.split('.'):
                value = value[key]
            return value
        except (KeyError, TypeError):
            return None

    async def _enrich_entity(self, entity_type: str, entity_value: str, enrich_level: str) -> Dict[str, Any]:
        """Enrich an entity with additional context"""
        # Check cache first
        cache_key = f"{entity_type}:{entity_value}"
        if cache_key in self.entity_cache:
            self.enrichment_stats['cache_hits'] += 1
            return self.entity_cache[cache_key]

        enriched_data = {
            'entity_type': entity_type,
            'entity_value': entity_value,
            'enrichments': {},
            'last_updated': datetime.utcnow().isoformat()
        }

        # Apply enrichments based on entity type
        if entity_type == 'ip':
            enriched_data['enrichments'] = await self._enrich_ip_address(entity_value, enrich_level)
        elif entity_type == 'hostname':
            enriched_data['enrichments'] = await self._enrich_hostname(entity_value, enrich_level)
        elif entity_type == 'user':
            enriched_data['enrichments'] = await self._enrich_user(entity_value, enrich_level)
        elif entity_type == 'process':
            enriched_data['enrichments'] = await self._enrich_process(entity_value, enrich_level)
        elif entity_type == 'file_hash':
            enriched_data['enrichments'] = await self._enrich_file_hash(entity_value, enrich_level)

        # ✨ CTI Enrichment - Check entity against threat intelligence cache
        cti_match = await self.cti_enrichment._check_ioc_cache({
            'type': entity_type,
            'value': entity_value
        })

        if cti_match:
            enriched_data['enrichments']['cti_threat_intelligence'] = cti_match
            enriched_data['threat_score'] = cti_match.get('threat_score', 0)
            enriched_data['is_threat'] = cti_match.get('threat_score', 0) >= 50
            self.logger.info(f"CTI MATCH: {entity_type}:{entity_value} - Threat Score: {cti_match.get('threat_score')}, Source: {cti_match.get('source')}")
        else:
            enriched_data['threat_score'] = 0
            enriched_data['is_threat'] = False

        # ✨ NEW: Business Context Enrichment - Pull organizational knowledge
        self.logger.info(f"[ENRICHMENT] About to query business context for {entity_type}:{entity_value}")
        business_context = await self._get_business_context(entity_type, entity_value)
        self.logger.info(f"[ENRICHMENT] Business context query returned: {business_context is not None}")
        if business_context:
            enriched_data['enrichments']['business_context'] = business_context
            enriched_data['has_business_context'] = True
            enriched_data['criticality_score'] = business_context.get('criticality_score', 0)
            self.logger.info(f"BUSINESS CONTEXT FOUND: {entity_type}:{entity_value} - {business_context.get('context_label', 'Unknown')}")
        else:
            enriched_data['has_business_context'] = False

        # Store entity in database
        await self._store_entity_in_database(entity_type, entity_value, enriched_data)

        # Cache the result
        self.entity_cache[cache_key] = enriched_data
        self.enrichment_stats['enrichments_applied'] += 1

        return enriched_data

    async def _enrich_ip_address(self, ip: str, enrich_level: str) -> Dict[str, Any]:
        """Enrich IP address with geolocation and threat intel"""
        enrichments = {}

        # Simulate geolocation enrichment
        enrichments['geolocation'] = {
            'country': 'US',
            'region': 'California',
            'city': 'San Francisco',
            'asn': 'AS13335',
            'organization': 'Cloudflare'
        }

        # Simulate threat intelligence check
        enrichments['threat_intel'] = {
            'is_malicious': False,
            'threat_types': [],
            'reputation_score': 85,
            'last_seen': None
        }

        if enrich_level == 'deep':
            # Additional enrichments for deep analysis
            enrichments['network_info'] = {
                'is_internal': ip.startswith('10.') or ip.startswith('192.168.'),
                'network_segment': 'dmz' if ip.startswith('10.') else 'external'
            }

        return enrichments

    async def _enrich_hostname(self, hostname: str, enrich_level: str) -> Dict[str, Any]:
        """Enrich hostname with asset inventory and DNS info"""
        enrichments = {}

        # Simulate asset inventory lookup
        enrichments['asset_info'] = {
            'asset_type': 'workstation',
            'os': 'Windows 10',
            'owner': 'IT Department',
            'location': 'Building A, Floor 2',
            'criticality': 'medium'
        }

        # Simulate DNS enrichment
        enrichments['dns_info'] = {
            'fqdn': f"{hostname}.company.local",
            'ip_addresses': ['**********'],
            'dns_history': []
        }

        return enrichments

    async def _enrich_user(self, user: str, enrich_level: str) -> Dict[str, Any]:
        """Enrich user with directory and access information"""
        enrichments = {}

        # Simulate user directory lookup
        enrichments['user_info'] = {
            'full_name': f"{user.title()} User",
            'department': 'Engineering',
            'manager': 'john.manager',
            'last_login': datetime.utcnow().isoformat(),
            'access_level': 'standard'
        }

        if enrich_level == 'deep':
            enrichments['access_patterns'] = {
                'typical_login_hours': '09:00-17:00',
                'typical_locations': ['workstation-01', 'workstation-05'],
                'privileged_access': False
            }

        return enrichments

    async def _enrich_process(self, process: str, enrich_level: str) -> Dict[str, Any]:
        """Enrich process with executable information"""
        enrichments = {}

        # Simulate process enrichment
        enrichments['process_info'] = {
            'executable_path': f"C:\\Windows\\System32\\{process}",
            'digital_signature': 'Microsoft Corporation',
            'version': '10.0.19041.1',
            'risk_score': 10 if process == 'powershell.exe' else 1
        }

        return enrichments

    async def _enrich_file_hash(self, file_hash: str, enrich_level: str) -> Dict[str, Any]:
        """Enrich file hash with malware analysis"""
        enrichments = {}

        # Simulate malware analysis
        enrichments['malware_analysis'] = {
            'is_malware': False,
            'virus_total_score': '0/70',
            'file_type': 'PE32 executable',
            'first_seen': datetime.utcnow().isoformat()
        }

        return enrichments

    async def _get_business_context(self, entity_type: str, entity_value: str) -> Optional[Dict[str, Any]]:
        """
        Query database for business context added by analysts
        This enables automatic enrichment with organizational knowledge
        """
        try:
            self.logger.info(f"[BUSINESS CONTEXT] Querying for {entity_type}:{entity_value}")

            async with self.db_pool.acquire() as conn:
                result = await conn.fetchrow("""
                    SELECT
                        business_context,
                        behavioral_profile,
                        criticality_score,
                        tags
                    FROM entities
                    WHERE entity_type = $1 AND entity_value = $2
                    AND business_context IS NOT NULL
                """, entity_type, entity_value)

            if not result:
                self.logger.info(f"[BUSINESS CONTEXT] No context found for {entity_type}:{entity_value}")
                return None

            # asyncpg.Record acts like a dict
            business_context = result['business_context']
            behavioral_profile = result['behavioral_profile']
            criticality_score = result['criticality_score']
            tags = result['tags']

            self.logger.info(f"[BUSINESS CONTEXT] Found context for {entity_type}:{entity_value} - Type: {type(business_context)}")

            # asyncpg returns JSONB as dict automatically, but handle strings for backward compatibility
            import json
            if isinstance(business_context, str):
                business_context = json.loads(business_context)
            if isinstance(behavioral_profile, str):
                behavioral_profile = json.loads(behavioral_profile)

            # Return structured business context
            return {
                'context_label': business_context.get('context_label') if business_context else None,
                'context_description': business_context.get('context_description') if business_context else None,
                'business_unit': business_context.get('business_unit') if business_context else None,
                'owner': business_context.get('owner') if business_context else None,
                'security_zone': business_context.get('security_zone') if business_context else None,
                'added_by': business_context.get('added_by') if business_context else None,
                'added_at': business_context.get('added_at') if business_context else None,
                'behavioral_profile': behavioral_profile,
                'criticality_score': criticality_score,
                'tags': tags if tags else []
            }

        except Exception as e:
            self.logger.error(f"[BUSINESS CONTEXT] Failed to query for {entity_type}:{entity_value}: {e}")
            import traceback
            self.logger.error(f"[BUSINESS CONTEXT] Traceback: {traceback.format_exc()}")
            return None

    async def _find_entity_relationships(self, entity: Dict[str, Any], types: List[str]) -> List[Dict[str, Any]]:
        """Find relationships for an entity"""
        relationships = []

        # Simulate relationship discovery
        entity_type = entity.get('type')
        entity_value = entity.get('value')

        if entity_type == 'user':
            # User-to-Host relationships
            relationships.append({
                'type': 'logs_into',
                'target_entity': {'type': 'hostname', 'value': 'workstation-01'},
                'confidence': 0.95,
                'last_seen': datetime.utcnow().isoformat()
            })

        elif entity_type == 'ip':
            # IP-to-Host relationships
            relationships.append({
                'type': 'communicates_with',
                'target_entity': {'type': 'hostname', 'value': 'server-01'},
                'confidence': 0.85,
                'last_seen': datetime.utcnow().isoformat()
            })

        return relationships

    async def _build_entity_context(self, entity: Dict[str, Any], depth: int) -> Dict[str, Any]:
        """Build comprehensive context for an entity"""
        context = {
            'entity': entity,
            'direct_relationships': [],
            'indirect_relationships': [],
            'timeline': [],
            'risk_factors': []
        }

        # Build context based on depth
        if depth >= 1:
            # Direct relationships
            context['direct_relationships'] = await self._find_entity_relationships(entity, ['all'])

        if depth >= 2:
            # Indirect relationships (relationships of related entities)
            for rel in context['direct_relationships']:
                target_entity = rel['target_entity']
                indirect_rels = await self._find_entity_relationships(target_entity, ['all'])
                context['indirect_relationships'].extend(indirect_rels)

        return context

    async def _enrichment_loop(self):
        """Main enrichment processing loop"""
        while self.is_running:
            try:
                # Process enrichment queue if we had one
                # For now, just maintain cache
                await asyncio.sleep(30)

            except Exception as e:
                self.logger.error(f"Enrichment loop error: {e}")
                await asyncio.sleep(5)

    async def _cache_maintenance_loop(self):
        """Maintain entity and relationship caches"""
        while self.is_running:
            try:
                # Clean expired cache entries
                current_time = datetime.utcnow()
                expired_keys = []

                for key, data in self.entity_cache.items():
                    last_updated = datetime.fromisoformat(data['last_updated'])
                    if current_time - last_updated > timedelta(hours=1):
                        expired_keys.append(key)

                for key in expired_keys:
                    del self.entity_cache[key]

                self.logger.info(f"Cleaned {len(expired_keys)} expired cache entries")

                await asyncio.sleep(300)  # Clean every 5 minutes

            except Exception as e:
                self.logger.error(f"Cache maintenance error: {e}")
                await asyncio.sleep(60)

    async def _relationship_analysis_loop(self):
        """Analyze and update entity relationships"""
        while self.is_running:
            try:
                # Analyze relationship patterns
                # Publish insights to Intelligence Engine
                if self.relationship_cache:
                    self.publish_message('intelligence.relationship_patterns', {
                        'pattern_count': len(self.relationship_cache),
                        'timestamp': datetime.utcnow().isoformat()
                    })

                await asyncio.sleep(120)  # Analyze every 2 minutes

            except Exception as e:
                self.logger.error(f"Relationship analysis error: {e}")
                await asyncio.sleep(30)

    async def _store_entity_in_database(self, entity_type: str, entity_value: str, enriched_data: Dict[str, Any]):
        """Store enriched entity in database"""
        try:
            # Calculate confidence score based on enrichment quality
            confidence = self._calculate_confidence_score(enriched_data)

            # Check if entity already exists using asyncpg
            async with self.db_pool.acquire() as conn:
                existing = await conn.fetchrow("""
                    SELECT entity_id FROM entities
                    WHERE entity_value = $1 AND entity_type = $2
                """, entity_value, entity_type)

                if existing:
                    # Update existing entity
                    await conn.execute("""
                        UPDATE entities
                        SET properties = $1, confidence = $2, updated_at = NOW()
                        WHERE entity_id = $3
                    """, json.dumps(enriched_data), confidence, existing['entity_id'])
                else:
                    # Insert new entity
                    entity_id = str(uuid.uuid4())
                    await conn.execute("""
                        INSERT INTO entities (entity_id, entity_type, entity_value, properties, confidence, created_at, updated_at)
                        VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
                    """, entity_id, entity_type, entity_value, json.dumps(enriched_data), confidence)

            self.logger.info(f"Successfully stored entity {entity_type}:{entity_value} with confidence {confidence}")

        except Exception as e:
            self.logger.error(f"Failed to store entity in database: {e}")

    async def _store_relationship_in_database(self, source_entity: Dict, target_entity: Dict, relationship_type: str, properties: Dict):
        """Store entity relationship in database"""
        try:
            relationship_id = str(uuid.uuid4())

            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO relationships (relationship_id, source_entity_id, target_entity_id, relationship_type, properties, created_at)
                    VALUES ($1, $2, $3, $4, $5, NOW())
                """, relationship_id, source_entity.get('entity_id'), target_entity.get('entity_id'),
                    relationship_type, json.dumps(properties))

            self.logger.debug(f"Stored relationship {relationship_type} between {source_entity.get('entity_value')} and {target_entity.get('entity_value')}")

        except Exception as e:
            self.logger.error(f"Failed to store relationship in database: {e}")

    def _calculate_confidence_score(self, enriched_data: Dict[str, Any]) -> float:
        """Calculate confidence score based on enrichment quality"""
        score = 0.5  # Base score

        enrichments = enriched_data.get('enrichments', {})

        # Increase confidence based on available enrichments
        if 'geolocation' in enrichments:
            score += 0.1
        if 'threat_intel' in enrichments:
            score += 0.2
        if 'asset_info' in enrichments:
            score += 0.1
        if 'user_info' in enrichments:
            score += 0.1

        # Cap at 1.0
        return min(score, 1.0)

    async def _store_entity(self, entity_type: str, entity_value: str, source_log_id: str = None,
                     extracted_from: str = None) -> Optional[uuid.UUID]:
        """Store entity directly in database"""
        try:
            entity_id = uuid.uuid4()

            async with self.db_pool.acquire() as conn:
                # Check if entity already exists
                existing = await conn.fetchrow("""
                    SELECT entity_id FROM entities
                    WHERE entity_type = $1 AND entity_value = $2
                """, entity_type, entity_value)

                if existing:
                    entity_id = existing['entity_id']
                    # Update existing entity
                    await conn.execute("""
                        UPDATE entities
                        SET updated_at = NOW(),
                            properties = jsonb_set(
                                COALESCE(properties, '{}'),
                                '{last_seen}',
                                to_jsonb(NOW()::text)
                            )
                        WHERE entity_id = $1
                    """, str(entity_id))
                else:
                    # Insert new entity
                    properties = {
                        'first_seen': datetime.utcnow().isoformat(),
                        'source_log': source_log_id,
                        'extracted_from': extracted_from
                    }

                    await conn.execute("""
                        INSERT INTO entities (entity_id, entity_type, entity_value, properties, confidence)
                        VALUES ($1, $2, $3, $4, $5)
                    """, str(entity_id), entity_type, entity_value, json.dumps(properties), 1.0)

            return entity_id

        except Exception as e:
            self.logger.error(f"Failed to store entity: {e}")
            return None

    def _extract_entities_from_log(self, log_data: Dict[str, Any]) -> List[Dict]:
        """Extract entities from log data structure"""
        entities = []

        # Extract IPs
        for field in ['source.ip', 'destination.ip', 'client.ip', 'server.ip']:
            value = self._get_nested_value(log_data, field)
            if value:
                entities.append({
                    'type': 'ip_address',
                    'value': value,
                    'field': field
                })

        # Extract hostnames
        for field in ['host.name', 'hostname', 'device.hostname', 'observer.name']:
            value = self._get_nested_value(log_data, field)
            if value:
                entities.append({
                    'type': 'hostname',
                    'value': value,
                    'field': field
                })

        # Extract users
        for field in ['user.name', 'username', 'device.user']:
            value = self._get_nested_value(log_data, field)
            if value:
                entities.append({
                    'type': 'username',
                    'value': value,
                    'field': field
                })

        # Extract processes
        for field in ['process.name', 'process.executable']:
            value = self._get_nested_value(log_data, field)
            if value:
                entities.append({
                    'type': 'process',
                    'value': value,
                    'field': field
                })

        # Remove duplicates
        unique_entities = []
        seen = set()
        for entity in entities:
            key = f"{entity['type']}:{entity['value']}"
            if key not in seen:
                seen.add(key)
                unique_entities.append(entity)

        return unique_entities

    def _get_nested_value(self, data: Dict, path: str) -> Optional[Any]:
        """Get value from nested dict using dot notation"""
        if not path or not data:
            return None

        keys = path.split('.')
        value = data

        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None

        return value

    async def _update_entity_enrichments(self, entity_id: str, enrichments: Dict[str, Any]):
        """Update entity with enrichment data"""
        try:
            async with self.db_pool.acquire() as conn:
                # Merge enrichments into entity properties
                await conn.execute("""
                    UPDATE entities
                    SET properties = jsonb_set(
                        COALESCE(properties, '{}'),
                        '{enrichments}',
                        $1::jsonb,
                        true
                    ),
                    updated_at = NOW()
                    WHERE entity_id = $2
                """, json.dumps(enrichments), str(entity_id))

        except Exception as e:
            self.logger.error(f"Failed to update entity enrichments: {e}")

    async def _create_entity_relationships(self, entities: List[Dict], source_log_id: str):
        """Create relationships between entities from same log"""
        try:
            async with self.db_pool.acquire() as conn:
                # Create relationships between all entity pairs
                for i in range(len(entities)):
                    for j in range(i + 1, len(entities)):
                        relationship_id = uuid.uuid4()
                        await conn.execute("""
                            INSERT INTO relationships (
                                relationship_id,
                                source_entity_id,
                                target_entity_id,
                                relationship_type,
                                properties,
                                confidence
                            ) VALUES ($1, $2, $3, $4, $5, $6)
                        """, str(relationship_id), str(entities[i]['entity_id']), str(entities[j]['entity_id']),
                            'co_occurred_in_log', json.dumps({'source_log': source_log_id}), 0.8)

        except Exception as e:
            self.logger.error(f"Failed to create relationships: {e}")

    async def _enrich_stored_entity(self, entity_id: uuid.UUID, entity_type: str, entity_value: str):
        """Enrich an already stored entity"""
        try:
            enrichments = await self._enrich_entity(entity_type, entity_value, 'standard')

            # Update entity with enrichments
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE entities
                    SET properties = jsonb_set(
                        COALESCE(properties, '{}'),
                        '{enrichments}',
                        $1
                    ),
                    updated_at = NOW()
                    WHERE entity_id = $2
                """, json.dumps(enrichments), str(entity_id))

        except Exception as e:
            self.logger.error(f"Failed to enrich entity {entity_id}: {e}")

    async def _handle_validate_parser(self, data: Dict[str, Any]):
        """
        Validate parser by applying it to log samples and extracting entities
        Tests parser coverage, entity extraction, and relationships
        """
        try:
            # Extract actual data from message envelope
            message_data = data.get('data', data)

            parser_id = message_data.get('parser_id')
            parser = message_data.get('parser', {})
            log_samples = message_data.get('log_samples', [])
            response_channel = message_data.get('response_channel')

            if not parser or not log_samples or not response_channel:
                self.logger.error(f"Missing parser, log_samples, or response_channel in validation request")
                return

            self.logger.info(f"Validating parser {parser_id} with {len(log_samples)} samples")

            # Extract parser field mappings and patterns
            field_mappings = parser.get('field_mappings', {})
            entity_types = parser.get('entity_types', [])
            format_type = parser.get('format_type', 'unknown')

            # Test parser on each log sample
            total_fields = 0
            total_entities = 0
            total_relationships = 0
            successful_parses = 0
            field_coverage = set()

            for idx, log_sample in enumerate(log_samples):
                try:
                    # Parse the log based on format type
                    parsed_fields = self._parse_log_sample(log_sample, parser)

                    if parsed_fields:
                        successful_parses += 1
                        total_fields += len(parsed_fields)
                        field_coverage.update(parsed_fields.keys())

                        # Extract entities from parsed fields
                        entities = self._extract_entities_from_parsed_fields(
                            parsed_fields, entity_types
                        )
                        total_entities += len(entities)

                        # Create relationships between entities
                        if len(entities) > 1:
                            relationships = self._create_test_relationships(entities)
                            total_relationships += len(relationships)

                        self.logger.debug(f"Sample {idx+1}: {len(parsed_fields)} fields, "
                                        f"{len(entities)} entities, "
                                        f"{len(relationships) if len(entities) > 1 else 0} relationships")

                except Exception as e:
                    self.logger.warning(f"Failed to parse sample {idx+1}: {e}")

            # Calculate coverage metrics
            expected_fields = len(field_mappings)
            coverage_percent = (len(field_coverage) / expected_fields * 100) if expected_fields > 0 else 0
            success_rate = (successful_parses / len(log_samples) * 100) if log_samples else 0

            # Build validation response
            validation_result = {
                'parser_id': parser_id,
                'coverage': round(coverage_percent, 1),
                'success_rate': round(success_rate, 1),
                'entity_count': total_entities,
                'relationship_count': total_relationships,
                'fields_extracted': len(field_coverage),
                'expected_fields': expected_fields,
                'samples_tested': len(log_samples),
                'successful_parses': successful_parses,
                'field_list': list(field_coverage)
            }

            # Publish validation response
            self.publish_message(response_channel, validation_result)

            self.logger.info(f"Parser validation complete: {parser_id}")
            self.logger.info(f"Coverage: {coverage_percent:.1f}%, Entities: {total_entities}, "
                           f"Relationships: {total_relationships}")

        except Exception as e:
            self.logger.error(f"Parser validation error: {e}")
            # Send error response
            if response_channel:
                self.publish_message(response_channel, {
                    'parser_id': data.get('parser_id'),
                    'error': str(e),
                    'status': 'validation_failed'
                })

    def _parse_log_sample(self, log_sample: str, parser: Dict[str, Any]) -> Dict[str, Any]:
        """Parse a log sample using the parser logic"""
        format_type = parser.get('format_type', 'unknown')
        field_mappings = parser.get('field_mappings', {})
        regex_patterns = parser.get('regex_patterns', {})

        parsed_fields = {}

        try:
            # Handle JSON format
            if format_type == 'json':
                import json as json_lib
                log_data = json_lib.loads(log_sample)
                # Map fields according to field_mappings
                for source_field, target_field in field_mappings.items():
                    if source_field in log_data:
                        parsed_fields[target_field] = log_data[source_field]

            # Handle key-value format
            elif format_type == 'key-value':
                import re
                # Parse key=value pairs
                kv_pattern = r'(\w+)=([^\s]+)'
                matches = re.findall(kv_pattern, log_sample)
                log_data = dict(matches)
                # Map fields
                for source_field, target_field in field_mappings.items():
                    if source_field in log_data:
                        parsed_fields[target_field] = log_data[source_field]

            # Handle custom formats with regex
            elif regex_patterns:
                import re
                for field_name, pattern in regex_patterns.items():
                    match = re.search(pattern, log_sample)
                    if match:
                        parsed_fields[field_name] = match.group(1) if match.groups() else match.group(0)

            # Fallback: try to extract using entity extractor
            else:
                parsed_fields = self.entity_extractor.extract_fields(log_sample)

        except Exception as e:
            self.logger.debug(f"Failed to parse log sample: {e}")

        return parsed_fields

    def _extract_entities_from_parsed_fields(self, fields: Dict[str, Any],
                                            entity_types: List[str]) -> List[Dict[str, Any]]:
        """Extract entities from parsed fields"""
        entities = []

        # IP address patterns
        if 'ip' in entity_types:
            import re
            ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
            for field_name, field_value in fields.items():
                if isinstance(field_value, str):
                    ips = re.findall(ip_pattern, str(field_value))
                    for ip in ips:
                        entities.append({'type': 'ip', 'value': ip, 'field': field_name})

        # User/username patterns
        if 'user' in entity_types or 'username' in entity_types:
            for field_name in ['user', 'username', 'account', 'login']:
                if field_name in fields and fields[field_name]:
                    entities.append({'type': 'user', 'value': fields[field_name], 'field': field_name})

        # Host/hostname patterns
        if 'host' in entity_types or 'hostname' in entity_types:
            for field_name in ['host', 'hostname', 'computer', 'device']:
                if field_name in fields and fields[field_name]:
                    entities.append({'type': 'host', 'value': fields[field_name], 'field': field_name})

        # Process patterns
        if 'process' in entity_types:
            for field_name in ['process', 'process_name', 'executable', 'command']:
                if field_name in fields and fields[field_name]:
                    entities.append({'type': 'process', 'value': fields[field_name], 'field': field_name})

        # Port patterns
        if 'port' in entity_types:
            for field_name in ['port', 'srcport', 'dstport', 'source_port', 'destination_port']:
                if field_name in fields and fields[field_name]:
                    try:
                        port_val = int(fields[field_name])
                        if 1 <= port_val <= 65535:
                            entities.append({'type': 'port', 'value': str(port_val), 'field': field_name})
                    except (ValueError, TypeError):
                        pass

        return entities

    def _create_test_relationships(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create relationships between entities (for testing)"""
        relationships = []

        # Create relationships between entity pairs
        for i in range(len(entities)):
            for j in range(i + 1, len(entities)):
                relationship = {
                    'source': entities[i],
                    'target': entities[j],
                    'type': f"{entities[i]['type']}_to_{entities[j]['type']}",
                    'confidence': 0.8
                }
                relationships.append(relationship)

        return relationships

    async def _handle_extract_from_context(self, data: Dict[str, Any]):
        """
        Extract entities from context plugin results

        This receives data from multiple context sources (CrowdStrike, SentinelOne, etc.)
        and extracts entities, then forwards enriched data to Backend
        """
        try:
            request_id = data.get('request_id')
            query_type = data.get('query_type')
            query_value = data.get('query_value')
            context_results = data.get('context_results', {})
            response_channel = data.get('response_channel')

            self.logger.info(f"[{request_id}] Extracting entities from context results")

            all_entities = []
            all_relationships = []
            source_summaries = {}

            # Process results from each source
            for source_name, results_list in context_results.items():
                self.logger.info(f"[{request_id}] Processing {len(results_list)} results from {source_name}")

                source_entities = []
                for result in results_list:
                    # Extract entities from result data
                    category = result.get('category')
                    data_dict = result.get('data', {})
                    confidence = result.get('confidence', 1.0)

                    # Extract based on category
                    if category == 'asset':
                        # Asset data: hostname, IP, OS, user, etc.
                        entities = self._extract_asset_entities(data_dict, source_name, confidence)
                        source_entities.extend(entities)

                    elif category == 'detection':
                        # Detection data: alerts, malware, techniques
                        entities = self._extract_detection_entities(data_dict, source_name, confidence)
                        source_entities.extend(entities)

                    elif category == 'incident':
                        # Incident data: grouped detections
                        entities = self._extract_incident_entities(data_dict, source_name, confidence)
                        source_entities.extend(entities)

                # Store entities in database
                for entity in source_entities:
                    entity_id = await self._store_entity(
                        entity_type=entity['type'],
                        entity_value=entity['value'],
                        source_log_id=request_id,
                        extracted_from=f"{source_name}:{entity.get('field', 'unknown')}"
                    )
                    if entity_id:
                        entity['entity_id'] = entity_id
                        all_entities.append(entity)

                source_summaries[source_name] = {
                    'entity_count': len(source_entities),
                    'categories': list(set(e.get('category', 'unknown') for e in source_entities))
                }

            # Create relationships between entities
            if len(all_entities) > 1:
                all_relationships = await self._create_entity_relationships(all_entities, request_id)

            # Send enriched data back to Delivery via response channel
            if response_channel:
                self.publish_message(response_channel, {
                    'request_id': request_id,
                    'query_type': query_type,
                    'query_value': query_value,
                    'entities': all_entities,
                    'relationships': all_relationships,
                    'source_summaries': source_summaries,
                    'total_entities': len(all_entities),
                    'total_relationships': len(all_relationships),
                    'timestamp': datetime.utcnow().isoformat()
                })

            self.logger.info(f"[{request_id}] Context extraction complete: "
                           f"{len(all_entities)} entities, {len(all_relationships)} relationships")

        except Exception as e:
            self.logger.error(f"Error extracting from context: {e}", exc_info=True)
            if data.get('response_channel'):
                self.publish_message(data['response_channel'], {
                    'request_id': data.get('request_id'),
                    'error': str(e)
                })

    def _extract_asset_entities(self, data: Dict[str, Any], source: str, confidence: float) -> List[Dict[str, Any]]:
        """Extract entities from asset/host data"""
        entities = []

        # Hostname
        if data.get('hostname'):
            entities.append({
                'type': 'hostname',
                'value': data['hostname'],
                'source': source,
                'confidence': confidence,
                'category': 'asset',
                'field': 'hostname'
            })

        # IP address
        if data.get('local_ip'):
            entities.append({
                'type': 'ip',
                'value': data['local_ip'],
                'source': source,
                'confidence': confidence,
                'category': 'asset',
                'field': 'local_ip'
            })

        # User
        if data.get('last_login_user'):
            entities.append({
                'type': 'user',
                'value': data['last_login_user'],
                'source': source,
                'confidence': confidence,
                'category': 'asset',
                'field': 'last_login_user'
            })

        # OS
        if data.get('os_version'):
            entities.append({
                'type': 'os',
                'value': data['os_version'],
                'source': source,
                'confidence': confidence,
                'category': 'asset',
                'field': 'os_version'
            })

        # MAC address
        if data.get('mac_address'):
            entities.append({
                'type': 'mac',
                'value': data['mac_address'],
                'source': source,
                'confidence': confidence,
                'category': 'asset',
                'field': 'mac_address'
            })

        return entities

    def _extract_detection_entities(self, data: Dict[str, Any], source: str, confidence: float) -> List[Dict[str, Any]]:
        """Extract entities from detection/alert data"""
        entities = []

        # Detection/Alert ID
        detection_id = data.get('detection_id') or data.get('alert_id')
        if detection_id:
            entities.append({
                'type': 'detection_id',
                'value': detection_id,
                'source': source,
                'confidence': confidence,
                'category': 'detection',
                'field': 'detection_id'
            })

        # Hostname from detection
        if data.get('device_hostname'):
            entities.append({
                'type': 'hostname',
                'value': data['device_hostname'],
                'source': source,
                'confidence': confidence,
                'category': 'detection',
                'field': 'device_hostname'
            })

        # IP from detection
        if data.get('device_ip'):
            entities.append({
                'type': 'ip',
                'value': data['device_ip'],
                'source': source,
                'confidence': confidence,
                'category': 'detection',
                'field': 'device_ip'
            })

        # User from detection
        if data.get('user_name'):
            entities.append({
                'type': 'user',
                'value': data['user_name'],
                'source': source,
                'confidence': confidence,
                'category': 'detection',
                'field': 'user_name'
            })

        # MITRE techniques
        if data.get('mitre_techniques'):
            for technique in data['mitre_techniques']:
                if technique:
                    entities.append({
                        'type': 'mitre_technique',
                        'value': technique,
                        'source': source,
                        'confidence': confidence,
                        'category': 'detection',
                        'field': 'mitre_techniques'
                    })

        return entities

    def _extract_incident_entities(self, data: Dict[str, Any], source: str, confidence: float) -> List[Dict[str, Any]]:
        """Extract entities from incident data"""
        entities = []

        # Incident ID
        if data.get('incident_id'):
            entities.append({
                'type': 'incident_id',
                'value': data['incident_id'],
                'source': source,
                'confidence': confidence,
                'category': 'incident',
                'field': 'incident_id'
            })

        # Tactics
        if data.get('tactics'):
            for tactic in data['tactics']:
                if tactic:
                    entities.append({
                        'type': 'mitre_tactic',
                        'value': tactic,
                        'source': source,
                        'confidence': confidence,
                        'category': 'incident',
                        'field': 'tactics'
                    })

        # Techniques
        if data.get('techniques'):
            for technique in data['techniques']:
                if technique:
                    entities.append({
                        'type': 'mitre_technique',
                        'value': technique,
                        'source': source,
                        'confidence': confidence,
                        'category': 'incident',
                        'field': 'techniques'
                    })

        # Hosts involved
        if data.get('hosts'):
            for host in data['hosts']:
                if isinstance(host, dict) and host.get('hostname'):
                    entities.append({
                        'type': 'hostname',
                        'value': host['hostname'],
                        'source': source,
                        'confidence': confidence,
                        'category': 'incident',
                        'field': 'hosts'
                    })

        return entities

    async def _handle_extract_entities_adaptive(self, data: Dict[str, Any]):
        """
        NEW: Handle entity extraction using adaptive extractor

        This method receives logs from ingestion and uses the adaptive extractor
        to automatically detect vendor and extract entities, learning new patterns as needed
        """
        try:
            request_id = data.get('request_id')
            source = data.get('source', 'unknown')
            logs = data.get('logs', [])

            if not logs:
                self.logger.warning(f"[{request_id}] No logs to process")
                return

            self.logger.info(f"[{request_id}] Processing {len(logs)} logs from {source} using adaptive extraction")

            all_entities = []
            all_relationships = []
            extraction_stats = {
                'total_logs': len(logs),
                'method_counts': {'pattern': 0, 'ai': 0, 'hybrid': 0},
                'learned_vendors': []
            }

            for idx, log_entry in enumerate(logs):
                try:
                    # Get the raw log data
                    log_data = log_entry.get('raw', log_entry)

                    # Use adaptive extractor
                    result = await self.adaptive_extractor.extract_entities(log_data, vendor=source)

                    # Track extraction method
                    method = result.get('method', 'unknown')
                    extraction_stats['method_counts'][method] = extraction_stats['method_counts'].get(method, 0) + 1

                    if result.get('learned'):
                        extraction_stats['learned_vendors'].append(result.get('vendor'))

                    # Store entities in database
                    entities = result.get('entities', [])
                    for entity in entities:
                        entity_id = await self._store_entity(
                            entity_type=entity['type'],
                            entity_value=entity['value'],
                            source_log_id=request_id,
                            extracted_from=f"{source}:{entity.get('source_field', 'adaptive')}"
                        )
                        if entity_id:
                            entity['entity_id'] = entity_id
                            all_entities.append(entity)

                    # Create relationships
                    if len(entities) > 1:
                        relationships = await self._create_entity_relationships(entities, request_id)
                        all_relationships.extend(relationships or [])

                    self.logger.debug(
                        f"[{request_id}] Log {idx+1}/{len(logs)}: "
                        f"{len(entities)} entities ({method}), "
                        f"vendor={result.get('vendor')}, "
                        f"confidence={result.get('confidence', 0):.2f}"
                    )

                except Exception as e:
                    self.logger.error(f"[{request_id}] Error extracting from log {idx+1}: {e}")

            # Send results to backend for storage
            self.publish_message('backend.store_intelligence', {
                'request_id': request_id,
                'source': source,
                'entities': all_entities,
                'relationships': all_relationships,
                'entity_count': len(all_entities),
                'relationship_count': len(all_relationships),
                'extraction_stats': extraction_stats,
                'timestamp': datetime.utcnow().isoformat(),
                'metadata_only': True
            })

            self.logger.info(
                f"[{request_id}] Adaptive extraction complete: "
                f"{len(all_entities)} entities, {len(all_relationships)} relationships, "
                f"methods={extraction_stats['method_counts']}, "
                f"learned={extraction_stats['learned_vendors']}"
            )

        except Exception as e:
            self.logger.error(f"Error in adaptive extraction: {e}", exc_info=True)

    async def _handle_cti_ioc_update(self, data: Dict[str, Any]):
        """
        ✨ NEW: Handle real-time CTI IOC updates from ingestion engine
        Cache IOCs for sub-millisecond enrichment lookups
        """
        try:
            ioc_data = data.get('ioc', data)  # Handle both wrapped and direct formats

            # Cache the IOC using CTI enrichment pipeline
            await self.cti_enrichment.cache_ioc(ioc_data)

            self.logger.info(
                f"CTI IOC cached: {ioc_data.get('ioc_type')}:{ioc_data.get('value')} "
                f"from {ioc_data.get('source')} (threat_score: {ioc_data.get('threat_score', 'N/A')})"
            )

            # Update enrichment stats
            self.enrichment_stats['enrichments_applied'] += 1

            # Publish notification that new threat intel is available
            await self.publish_message('contextualization.cti_cache_updated', {
                'ioc_type': ioc_data.get('ioc_type'),
                'source': ioc_data.get('source'),
                'threat_score': ioc_data.get('threat_score', 0),
                'timestamp': datetime.utcnow().isoformat()
            })

        except Exception as e:
            self.logger.error(f"Error caching CTI IOC: {e}", exc_info=True)

    async def _handle_enrich_alert(self, data: Dict[str, Any]):
        """
        ✨ NEW: Enrich alert entities with contextual information for investigation

        Provides three-layer enrichment:
        - Layer 1: Basic (GeoIP, WHOIS, DNS)
        - Layer 2: Threat Intel (CTI feeds, reputation)
        - Layer 3: Environmental (asset info, user context, historical)
        """
        try:
            alert_id = data.get('alert_id')
            entities = data.get('entities', {})
            request_id = data.get('request_id', f"alert_enrich_{alert_id}")

            self.logger.info(f"Enriching alert {alert_id} with {len(entities)} entity types")

            enriched_entities = {}
            enrichment_summary = {
                'total_entities': 0,
                'enriched_count': 0,
                'threat_indicators_found': 0
            }

            # Enrich each entity type
            for entity_type, entity_values in entities.items():
                # Normalize to list
                if not isinstance(entity_values, list):
                    entity_values = [entity_values]

                enriched_entities[entity_type] = []

                for entity_value in entity_values:
                    if not entity_value:
                        continue

                    enrichment_summary['total_entities'] += 1

                    try:
                        # Layer 1: Basic enrichment
                        basic_enrichment = await self.enrichment_service.enrich_entity(
                            entity_type, entity_value
                        )

                        # Layer 2: CTI enrichment (check if entity is malicious)
                        cti_enrichment = await self.cti_enrichment.enrich_entity(
                            entity_type, entity_value
                        )

                        # Layer 3: Environmental context (from investigation plugins if available)
                        environmental_context = {}
                        # TODO: Add asset inventory, user directory lookups

                        # Combine all enrichment layers
                        combined_enrichment = {
                            'value': entity_value,
                            'type': entity_type,
                            'basic': basic_enrichment,
                            'cti': cti_enrichment,
                            'environmental': environmental_context,
                            'threat_score': cti_enrichment.get('threat_score', 0),
                            'is_malicious': cti_enrichment.get('is_malicious', False)
                        }

                        enriched_entities[entity_type].append(combined_enrichment)
                        enrichment_summary['enriched_count'] += 1

                        if combined_enrichment.get('is_malicious'):
                            enrichment_summary['threat_indicators_found'] += 1

                    except Exception as e:
                        self.logger.error(f"Failed to enrich {entity_type}:{entity_value}: {e}")
                        # Still add with minimal enrichment
                        enriched_entities[entity_type].append({
                            'value': entity_value,
                            'type': entity_type,
                            'error': str(e)
                        })

            # Publish enriched results
            await self.publish_message(f'contextualization.alert.enriched.{request_id}', {
                'alert_id': alert_id,
                'enriched_entities': enriched_entities,
                'enrichment_summary': enrichment_summary,
                'timestamp': datetime.utcnow().isoformat()
            })

            self.logger.info(
                f"Alert {alert_id} enrichment complete: "
                f"{enrichment_summary['enriched_count']}/{enrichment_summary['total_entities']} entities, "
                f"{enrichment_summary['threat_indicators_found']} threats found"
            )

            # Update stats
            self.enrichment_stats['enrichments_applied'] += enrichment_summary['enriched_count']

        except Exception as e:
            self.logger.error(f"Error enriching alert {alert_id}: {e}", exc_info=True)
            # Publish error so investigation doesn't hang waiting
            await self.publish_message(f'contextualization.alert.enriched.{request_id}', {
                'alert_id': alert_id,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            })

if __name__ == "__main__":
    async def main():
        engine = ContextualizationEngine()
        await engine.start()

    asyncio.run(main())