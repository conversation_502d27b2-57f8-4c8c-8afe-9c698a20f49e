"""
Test Elastic Log Ingestion with MITRE ATT&CK Mapping
Complete workflow test: Elastic logs → Ingestion → MITRE mapping → Coverage scoring
"""

import asyncio
import json
import redis
import requests
from datetime import datetime
from typing import Dict, List

# Sample Elastic Security logs (various attack types)
SAMPLE_ELASTIC_LOGS = [
    # 1. Process Injection (T1055)
    {
        "timestamp": "2024-10-02T10:15:30.000Z",
        "event": {
            "kind": "event",
            "category": ["process"],
            "type": ["start"],
            "action": "process_injection"
        },
        "process": {
            "name": "powershell.exe",
            "pid": 4892,
            "command_line": "powershell.exe -enc JABzAD0ATgBlAHcALQBPAGIAagBlAGMAdA",
            "parent": {
                "name": "explorer.exe",
                "pid": 1234
            }
        },
        "host": {
            "name": "WORKSTATION-01",
            "os": {"family": "windows"}
        },
        "user": {"name": "john.doe"},
        "rule": {
            "name": "Suspicious PowerShell Encoded Command",
            "description": "Detects execution of encoded PowerShell commands",
            "mitre": ["T1059.001", "T1027"]  # Explicit MITRE tags
        }
    },

    # 2. Credential Dumping (T1003)
    {
        "timestamp": "2024-10-02T10:20:15.000Z",
        "event": {
            "kind": "alert",
            "category": ["process"],
            "type": ["start"],
            "action": "credential_access"
        },
        "process": {
            "name": "mimikatz.exe",
            "pid": 5678,
            "command_line": "mimikatz.exe sekurlsa::logonpasswords",
            "hash": {"sha256": "abc123def456"}
        },
        "host": {"name": "SERVER-DC01"},
        "user": {"name": "admin"},
        "rule": {
            "name": "Mimikatz Credential Dumping",
            "description": "Detects Mimikatz credential theft tool",
            "mitre": ["T1003.001"]  # LSASS Memory
        }
    },

    # 3. Lateral Movement via RDP (T1021.001)
    {
        "timestamp": "2024-10-02T10:25:00.000Z",
        "event": {
            "kind": "event",
            "category": ["network"],
            "type": ["connection"],
            "action": "rdp_connection"
        },
        "source": {"ip": "*********", "port": 52341},
        "destination": {"ip": "**********", "port": 3389},
        "network": {"protocol": "rdp"},
        "host": {"name": "SERVER-02"},
        "user": {"name": "admin"},
        "rule": {
            "name": "RDP Lateral Movement",
            "description": "Detects RDP connections between internal hosts",
            "tags": ["lateral_movement", "T1021.001"]
        }
    },

    # 4. File Deletion (T1485) - No explicit MITRE tag
    {
        "timestamp": "2024-10-02T10:30:45.000Z",
        "event": {
            "kind": "event",
            "category": ["file"],
            "type": ["deletion"],
            "action": "file_deleted"
        },
        "file": {
            "path": "C:\\Users\\<USER>\\Documents\\important.docx",
            "name": "important.docx",
            "extension": "docx"
        },
        "process": {
            "name": "cmd.exe",
            "command_line": "cmd.exe /c del /F /Q C:\\Users\\<USER>\\Documents\\*.docx"
        },
        "host": {"name": "WORKSTATION-01"},
        "user": {"name": "john.doe"},
        "rule": {
            "name": "Suspicious Bulk File Deletion",
            "description": "Detects mass file deletion via command line"
            # No MITRE tag - will use data source mapping
        }
    },

    # 5. DNS Tunneling (T1071.004)
    {
        "timestamp": "2024-10-02T10:35:20.000Z",
        "event": {
            "kind": "event",
            "category": ["network"],
            "type": ["protocol"],
            "action": "dns_query"
        },
        "dns": {
            "question": {
                "name": "dGVzdGRhdGE.malicious-c2.com",
                "type": "TXT"
            },
            "response_code": "NOERROR"
        },
        "source": {"ip": "*********"},
        "destination": {"ip": "*******"},
        "host": {"name": "WORKSTATION-01"},
        "rule": {
            "name": "DNS Tunneling Detected",
            "description": "Detects suspicious DNS TXT queries indicating tunneling"
        }
    },

    # 6. Persistence via Registry (T1547.001)
    {
        "timestamp": "2024-10-02T10:40:10.000Z",
        "event": {
            "kind": "event",
            "category": ["registry"],
            "type": ["change"],
            "action": "registry_value_set"
        },
        "registry": {
            "path": "HKLM\\Software\\Microsoft\\Windows\\CurrentVersion\\Run\\Malware",
            "value": "C:\\Temp\\malware.exe",
            "key": "HKLM\\Software\\Microsoft\\Windows\\CurrentVersion\\Run"
        },
        "process": {"name": "powershell.exe", "pid": 9876},
        "host": {"name": "WORKSTATION-01"},
        "user": {"name": "john.doe"},
        "rule": {
            "name": "Registry Run Key Persistence",
            "mitre": ["T1547.001"]
        }
    }
]


class ElasticMITRETest:
    """Test Elastic log ingestion with MITRE mapping"""

    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)
        self.backend_url = "http://localhost:8002"
        self.ingestion_url = "http://localhost:8003"

    def test_complete_workflow(self):
        """Run complete test workflow"""
        print("=" * 80)
        print("ELASTIC -> MITRE ATT&CK MAPPING TEST")
        print("=" * 80)
        print()

        # Step 1: Check engine health
        print("Step 1: Checking engine health...")
        self.check_engine_health()

        # Step 2: Initialize MITRE framework
        print("\nStep 2: Initializing MITRE ATT&CK framework...")
        self.initialize_mitre()

        # Step 3: Ingest sample Elastic logs
        print("\nStep 3: Ingesting Elastic Security logs...")
        self.ingest_elastic_logs()

        # Step 4: Map logs to MITRE techniques
        print("\nStep 4: Mapping logs to MITRE techniques...")
        mappings = self.map_logs_to_mitre()

        # Step 5: Calculate coverage
        print("\nStep 5: Calculating MITRE coverage...")
        coverage = self.calculate_coverage(mappings)

        # Step 6: Identify gaps
        print("\nStep 6: Identifying detection gaps...")
        gaps = self.identify_gaps()

        # Step 7: Analyze log source value
        print("\nStep 7: Analyzing Elastic Security value...")
        source_analysis = self.analyze_log_source()

        # Generate report
        print("\n" + "=" * 80)
        print("TEST RESULTS SUMMARY")
        print("=" * 80)
        self.print_results(mappings, coverage, gaps, source_analysis)

    def check_engine_health(self):
        """Check if engines are running"""
        engines = {
            "Backend": f"{self.backend_url}/health",
            "Ingestion": f"{self.ingestion_url}/health"
        }

        for name, url in engines.items():
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"  [OK] {name} Engine: Running")
                else:
                    print(f"  [FAIL] {name} Engine: Unhealthy (HTTP {response.status_code})")
            except Exception as e:
                print(f"  [FAIL] {name} Engine: Not reachable ({e})")

    def initialize_mitre(self):
        """Initialize MITRE ATT&CK framework"""
        try:
            # Trigger framework update
            response = requests.post(
                f"{self.backend_url}/api/v1/mitre/update_framework",
                json={"force": False},
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                if result['status'] == 'success':
                    framework_data = result['result']
                    print(f"  [OK] MITRE Framework loaded: {framework_data.get('techniques', 0)} techniques")
                else:
                    print(f"  [X] Framework initialization failed: {result.get('message')}")
            else:
                print(f"  [WARN] Framework may be cached (HTTP {response.status_code})")

        except Exception as e:
            print(f"  [WARN] Framework check failed: {e}")

    def ingest_elastic_logs(self):
        """Send sample Elastic logs to ingestion engine"""
        print(f"  Ingesting {len(SAMPLE_ELASTIC_LOGS)} Elastic Security logs...")

        for idx, log in enumerate(SAMPLE_ELASTIC_LOGS, 1):
            # Add metadata
            log['log_source'] = 'elastic_security'
            log['source_type'] = 'siem'
            log['ingestion_timestamp'] = datetime.utcnow().isoformat()

            # Publish to ingestion channel
            self.redis_client.publish('ingestion.elastic.events', json.dumps(log))

            rule_name = log.get('rule', {}).get('name', 'Unknown')
            print(f"    Log {idx}: {rule_name}")

        print(f"  [OK] Sent {len(SAMPLE_ELASTIC_LOGS)} logs to ingestion")

    def map_logs_to_mitre(self) -> List[Dict]:
        """Map each log/rule to MITRE techniques"""
        mappings = []

        for log in SAMPLE_ELASTIC_LOGS:
            rule = log.get('rule', {})
            rule_name = rule.get('name', 'Unknown')

            # Build rule object for mapping
            rule_data = {
                'rule_id': f"elastic_{hash(rule_name) % 10000}",
                'name': rule_name,
                'description': rule.get('description', ''),
                'logsource': {
                    'product': 'elastic',
                    'service': 'security',
                    'category': log.get('event', {}).get('category', [])
                },
                'mitre_attack': rule.get('mitre', []),
                'tags': rule.get('tags', [])
            }

            try:
                response = requests.post(
                    f"{self.backend_url}/api/v1/mitre/map_rule",
                    json=rule_data,
                    timeout=10
                )

                if response.status_code == 200:
                    result = response.json()
                    if result['status'] == 'success':
                        mapping = result['mapping']
                        mappings.append(mapping)

                        techniques = ', '.join(mapping['techniques']) if mapping['techniques'] else 'None'
                        method = mapping['mapping_method']
                        print(f"  [OK] {rule_name[:50]:<50} → {techniques} ({method})")
                    else:
                        print(f"  [X] Mapping failed for {rule_name}: {result.get('message')}")
                else:
                    print(f"  [X] HTTP {response.status_code} for {rule_name}")

            except Exception as e:
                print(f"  [X] Error mapping {rule_name}: {e}")

        return mappings

    def calculate_coverage(self, mappings: List[Dict]) -> Dict:
        """Calculate overall MITRE coverage"""
        try:
            response = requests.get(
                f"{self.backend_url}/api/v1/mitre/coverage",
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result['status'] == 'success':
                    coverage = result['coverage']
                    print(f"  [OK] Overall Coverage: {coverage['coverage_percentage']:.1f}%")
                    print(f"    - Total Techniques: {coverage['total_techniques']}")
                    print(f"    - Covered: {coverage['covered_techniques']}")
                    print(f"    - Gaps: {coverage['gaps_count']}")
                    print(f"    - Overlaps: {coverage['overlaps_count']}")
                    return coverage

            print("  [X] Coverage calculation failed")
            return {}

        except Exception as e:
            print(f"  [X] Error: {e}")
            return {}

    def identify_gaps(self) -> List[Dict]:
        """Identify detection gaps"""
        try:
            response = requests.get(
                f"{self.backend_url}/api/v1/mitre/gaps",
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result['status'] == 'success':
                    gaps = result['gaps']
                    print(f"  [OK] Found {result['total_gaps']} detection gaps")

                    # Show top 5 gaps
                    print("    Top gaps:")
                    for gap in gaps[:5]:
                        tactics = ', '.join(gap['tactics'])
                        print(f"      - {gap['technique_id']}: {gap['name']} ({tactics})")

                    return gaps

            print("  [X] Gap analysis failed")
            return []

        except Exception as e:
            print(f"  [X] Error: {e}")
            return []

    def analyze_log_source(self) -> Dict:
        """Analyze Elastic Security as a log source"""
        try:
            response = requests.post(
                f"{self.backend_url}/api/v1/mitre/analyze_sources",
                json={
                    "sources": [
                        {
                            "name": "Elastic Security",
                            "type": "siem",
                            "quality_score": 85,
                            "vendor": "Elastic"
                        }
                    ]
                },
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result['status'] == 'success':
                    analysis = result['analysis']
                    total_cov = analysis['total_coverage']

                    print(f"  [OK] Elastic Security Analysis:")
                    print(f"    - Techniques Covered: {total_cov['techniques_covered']}")
                    print(f"    - Coverage: {total_cov['coverage_percentage']:.1f}%")

                    # Show recommendations
                    recommendations = analysis.get('recommendations', [])
                    if recommendations:
                        print(f"    - Recommendations: {len(recommendations)}")
                        for rec in recommendations[:3]:
                            print(f"      • {rec['technique_id']}: {rec['recommendation']}")

                    return analysis

            print("  [X] Source analysis failed")
            return {}

        except Exception as e:
            print(f"  [X] Error: {e}")
            return {}

    def print_results(self, mappings: List[Dict], coverage: Dict, gaps: List[Dict], source_analysis: Dict):
        """Print comprehensive results"""
        print()
        print("DETECTION COVERAGE BREAKDOWN")
        print("-" * 80)

        # By tactic
        if coverage and 'by_tactic' in coverage:
            print("\nCoverage by MITRE Tactic:")
            for tactic, stats in coverage['by_tactic'].items():
                if stats['total'] > 0:
                    bar_length = int(stats['coverage_percentage'] / 5)
                    bar = "#" * bar_length + "-" * (20 - bar_length)
                    print(f"  {tactic:25} {bar} {stats['coverage_percentage']:5.1f}% ({stats['covered']}/{stats['total']})")

        # Mapped techniques
        print(f"\nMAPPED TECHNIQUES ({len(mappings)} rules)")
        print("-" * 80)

        technique_counts = {}
        for mapping in mappings:
            for tech in mapping['techniques']:
                technique_counts[tech] = technique_counts.get(tech, 0) + 1

        for tech, count in sorted(technique_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"  {tech}: {count} rule(s)")

        # High-value gaps
        print(f"\nCRITICAL GAPS (Top 10)")
        print("-" * 80)

        high_priority_tactics = ['initial-access', 'execution', 'persistence', 'privilege-escalation']
        critical_gaps = [
            g for g in gaps
            if any(t in high_priority_tactics for t in g.get('tactics', []))
        ][:10]

        for gap in critical_gaps:
            tactics = ', '.join(gap['tactics'])
            print(f"  {gap['technique_id']:12} {gap['name']:50} ({tactics})")

        # Recommendations
        print(f"\nRECOMMENDATIONS")
        print("-" * 80)

        if source_analysis and 'recommendations' in source_analysis:
            for i, rec in enumerate(source_analysis['recommendations'][:5], 1):
                print(f"  {i}. Add coverage for {rec['technique_id']}: {rec['technique_name']}")
                print(f"     Required: {', '.join(rec['required_data_sources'][:2])}")
        else:
            print("  No specific recommendations available")

        print()
        print("=" * 80)
        print("[OK] Test completed successfully!")
        print("=" * 80)


if __name__ == "__main__":
    test = ElasticMITRETest()
    test.test_complete_workflow()
