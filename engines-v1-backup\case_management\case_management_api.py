#!/usr/bin/env python3
"""
SIEMLess v2.0 Case Management API

REST API endpoints for case management functionality.
Provides comprehensive case lifecycle management.

Author: SIEMLess Development Team
Version: 2.0.0
"""

from flask import Flask, request, jsonify
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import asyncio
import json
import uuid

from case_management_engine import CaseManagementEngine, CaseStatus, CasePriority, CaseType, EvidenceType

app = Flask(__name__)

# Global engine instance
case_engine: Optional[CaseManagementEngine] = None

async def init_engine():
    """Initialize the case management engine"""
    global case_engine
    case_engine = CaseManagementEngine()
    await case_engine.initialize()

# Case Management Endpoints

@app.route('/api/v2/cases', methods=['POST'])
def create_case():
    """Create a new security case"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['title', 'description', 'created_by']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    "success": False,
                    "error": f"Missing required field: {field}"
                }), 400

        # Create case asynchronously
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        case = loop.run_until_complete(case_engine.create_case(data))

        return jsonify({
            "success": True,
            "message": "Case created successfully",
            "case_id": case.case_id,
            "case_data": case.to_dict()
        }), 201

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to create case: {str(e)}"
        }), 500

@app.route('/api/v2/cases', methods=['GET'])
def list_cases():
    """List security cases with optional filters"""
    try:
        # Parse query parameters for filters
        filters = {}
        if request.args.get('status'):
            filters['status'] = request.args.get('status')
        if request.args.get('priority'):
            filters['priority'] = request.args.get('priority')
        if request.args.get('assigned_to'):
            filters['assigned_to'] = request.args.get('assigned_to')
        if request.args.get('case_type'):
            filters['case_type'] = request.args.get('case_type')

        # Pagination
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))
        offset = (page - 1) * limit

        # Get cases
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        all_cases = loop.run_until_complete(case_engine.list_cases(filters))
        total_cases = len(all_cases)

        # Apply pagination
        cases = all_cases[offset:offset + limit]

        return jsonify({
            "success": True,
            "cases": [case.to_dict() for case in cases],
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_cases,
                "total_pages": (total_cases + limit - 1) // limit
            }
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to list cases: {str(e)}"
        }), 500

@app.route('/api/v2/cases/<case_id>', methods=['GET'])
def get_case(case_id):
    """Get a specific case by ID"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        case = loop.run_until_complete(case_engine.get_case(case_id))

        if not case:
            return jsonify({
                "success": False,
                "error": "Case not found"
            }), 404

        return jsonify({
            "success": True,
            "case_data": case.to_dict()
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to get case: {str(e)}"
        }), 500

@app.route('/api/v2/cases/<case_id>', methods=['PUT'])
def update_case(case_id):
    """Update an existing case"""
    try:
        data = request.get_json()
        updated_by = data.get('updated_by', 'api_user')

        # Remove metadata fields from updates
        updates = {k: v for k, v in data.items() if k not in ['updated_by']}

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        case = loop.run_until_complete(case_engine.update_case(case_id, updates, updated_by))

        if not case:
            return jsonify({
                "success": False,
                "error": "Case not found"
            }), 404

        return jsonify({
            "success": True,
            "message": "Case updated successfully",
            "case_data": case.to_dict()
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to update case: {str(e)}"
        }), 500

@app.route('/api/v2/cases/<case_id>/assign', methods=['POST'])
def assign_case(case_id):
    """Assign a case to an analyst"""
    try:
        data = request.get_json()
        assigned_to = data.get('assigned_to')
        assigned_by = data.get('assigned_by', 'api_user')

        if not assigned_to:
            return jsonify({
                "success": False,
                "error": "Missing required field: assigned_to"
            }), 400

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        case = loop.run_until_complete(case_engine.update_case(
            case_id,
            {'assigned_to': assigned_to, 'status': CaseStatus.ASSIGNED.value},
            assigned_by
        ))

        if not case:
            return jsonify({
                "success": False,
                "error": "Case not found"
            }), 404

        return jsonify({
            "success": True,
            "message": f"Case assigned to {assigned_to}",
            "case_data": case.to_dict()
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to assign case: {str(e)}"
        }), 500

@app.route('/api/v2/cases/<case_id>/evidence', methods=['POST'])
def add_evidence(case_id):
    """Add evidence to a case"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['evidence_type', 'source_engine', 'description', 'collected_by']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    "success": False,
                    "error": f"Missing required field: {field}"
                }), 400

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        evidence = loop.run_until_complete(case_engine.add_evidence(case_id, data))

        if not evidence:
            return jsonify({
                "success": False,
                "error": "Case not found or failed to add evidence"
            }), 404

        return jsonify({
            "success": True,
            "message": "Evidence added successfully",
            "evidence_id": evidence.evidence_id,
            "evidence_data": evidence.to_dict()
        }), 201

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to add evidence: {str(e)}"
        }), 500

@app.route('/api/v2/cases/<case_id>/escalate', methods=['POST'])
def escalate_case(case_id):
    """Escalate a case"""
    try:
        data = request.get_json()
        escalated_by = data.get('escalated_by', 'api_user')
        reason = data.get('reason', 'Manual escalation via API')

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        case = loop.run_until_complete(case_engine.update_case(
            case_id,
            {'status': CaseStatus.ESCALATED.value},
            escalated_by
        ))

        if not case:
            return jsonify({
                "success": False,
                "error": "Case not found"
            }), 404

        return jsonify({
            "success": True,
            "message": "Case escalated successfully",
            "case_data": case.to_dict()
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to escalate case: {str(e)}"
        }), 500

@app.route('/api/v2/cases/<case_id>/close', methods=['POST'])
def close_case(case_id):
    """Close a case"""
    try:
        data = request.get_json()
        closed_by = data.get('closed_by', 'api_user')
        resolution_notes = data.get('resolution_notes', '')

        updates = {
            'status': CaseStatus.CLOSED.value,
            'metadata': {'resolution_notes': resolution_notes}
        }

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        case = loop.run_until_complete(case_engine.update_case(case_id, updates, closed_by))

        if not case:
            return jsonify({
                "success": False,
                "error": "Case not found"
            }), 404

        return jsonify({
            "success": True,
            "message": "Case closed successfully",
            "case_data": case.to_dict()
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to close case: {str(e)}"
        }), 500

# Metrics and Analytics Endpoints

@app.route('/api/v2/cases/metrics', methods=['GET'])
def get_case_metrics():
    """Get case management metrics and analytics"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        metrics = loop.run_until_complete(case_engine.get_case_metrics())

        return jsonify({
            "success": True,
            "metrics": metrics,
            "generated_at": datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to get metrics: {str(e)}"
        }), 500

@app.route('/api/v2/cases/dashboard', methods=['GET'])
def get_dashboard_data():
    """Get dashboard data for case management"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # Get recent cases
        recent_cases = loop.run_until_complete(case_engine.list_cases())[:10]

        # Get metrics
        metrics = loop.run_until_complete(case_engine.get_case_metrics())

        # Get overdue cases
        current_time = datetime.utcnow()
        all_cases = loop.run_until_complete(case_engine.list_cases())
        overdue_cases = [
            case for case in all_cases
            if case.due_date and current_time > case.due_date and
            case.status not in [CaseStatus.RESOLVED, CaseStatus.CLOSED]
        ]

        # Get high priority open cases
        high_priority_cases = [
            case for case in all_cases
            if case.priority in [CasePriority.CRITICAL, CasePriority.HIGH] and
            case.status not in [CaseStatus.RESOLVED, CaseStatus.CLOSED]
        ]

        return jsonify({
            "success": True,
            "dashboard_data": {
                "recent_cases": [case.to_dict() for case in recent_cases],
                "overdue_cases": [case.to_dict() for case in overdue_cases],
                "high_priority_cases": [case.to_dict() for case in high_priority_cases],
                "metrics": metrics
            },
            "generated_at": datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to get dashboard data: {str(e)}"
        }), 500

# Template and Configuration Endpoints

@app.route('/api/v2/cases/templates', methods=['GET'])
def get_case_templates():
    """Get available case templates"""
    try:
        return jsonify({
            "success": True,
            "templates": case_engine.case_templates
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to get templates: {str(e)}"
        }), 500

@app.route('/api/v2/cases/templates/<template_name>', methods=['GET'])
def get_case_template(template_name):
    """Get a specific case template"""
    try:
        if template_name not in case_engine.case_templates:
            return jsonify({
                "success": False,
                "error": "Template not found"
            }), 404

        return jsonify({
            "success": True,
            "template": case_engine.case_templates[template_name]
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to get template: {str(e)}"
        }), 500

@app.route('/api/v2/cases/sla-policies', methods=['GET'])
def get_sla_policies():
    """Get SLA policies"""
    try:
        return jsonify({
            "success": True,
            "sla_policies": case_engine.sla_policies
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to get SLA policies: {str(e)}"
        }), 500

# Bulk Operations

@app.route('/api/v2/cases/bulk/assign', methods=['POST'])
def bulk_assign_cases():
    """Bulk assign multiple cases"""
    try:
        data = request.get_json()
        case_ids = data.get('case_ids', [])
        assigned_to = data.get('assigned_to')
        assigned_by = data.get('assigned_by', 'api_user')

        if not case_ids or not assigned_to:
            return jsonify({
                "success": False,
                "error": "Missing required fields: case_ids and assigned_to"
            }), 400

        results = []
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        for case_id in case_ids:
            try:
                case = loop.run_until_complete(case_engine.update_case(
                    case_id,
                    {'assigned_to': assigned_to, 'status': CaseStatus.ASSIGNED.value},
                    assigned_by
                ))

                results.append({
                    "case_id": case_id,
                    "success": case is not None,
                    "error": None if case else "Case not found"
                })
            except Exception as e:
                results.append({
                    "case_id": case_id,
                    "success": False,
                    "error": str(e)
                })

        successful_assignments = sum(1 for r in results if r['success'])

        return jsonify({
            "success": True,
            "message": f"Assigned {successful_assignments} of {len(case_ids)} cases",
            "results": results
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to bulk assign cases: {str(e)}"
        }), 500

@app.route('/api/v2/cases/bulk/status', methods=['POST'])
def bulk_update_status():
    """Bulk update status of multiple cases"""
    try:
        data = request.get_json()
        case_ids = data.get('case_ids', [])
        new_status = data.get('status')
        updated_by = data.get('updated_by', 'api_user')

        if not case_ids or not new_status:
            return jsonify({
                "success": False,
                "error": "Missing required fields: case_ids and status"
            }), 400

        # Validate status
        try:
            CaseStatus(new_status)
        except ValueError:
            return jsonify({
                "success": False,
                "error": f"Invalid status: {new_status}"
            }), 400

        results = []
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        for case_id in case_ids:
            try:
                case = loop.run_until_complete(case_engine.update_case(
                    case_id,
                    {'status': new_status},
                    updated_by
                ))

                results.append({
                    "case_id": case_id,
                    "success": case is not None,
                    "error": None if case else "Case not found"
                })
            except Exception as e:
                results.append({
                    "case_id": case_id,
                    "success": False,
                    "error": str(e)
                })

        successful_updates = sum(1 for r in results if r['success'])

        return jsonify({
            "success": True,
            "message": f"Updated {successful_updates} of {len(case_ids)} cases",
            "results": results
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to bulk update status: {str(e)}"
        }), 500

# Search and Export

@app.route('/api/v2/cases/search', methods=['POST'])
def search_cases():
    """Advanced case search"""
    try:
        data = request.get_json()
        query = data.get('query', '')
        filters = data.get('filters', {})

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        all_cases = loop.run_until_complete(case_engine.list_cases(filters))

        # Simple text search in title and description
        if query:
            query_lower = query.lower()
            matching_cases = [
                case for case in all_cases
                if query_lower in case.title.lower() or query_lower in case.description.lower()
            ]
        else:
            matching_cases = all_cases

        return jsonify({
            "success": True,
            "cases": [case.to_dict() for case in matching_cases],
            "total_results": len(matching_cases),
            "query": query,
            "filters": filters
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to search cases: {str(e)}"
        }), 500

@app.route('/api/v2/cases/export', methods=['POST'])
def export_cases():
    """Export cases to various formats"""
    try:
        data = request.get_json()
        filters = data.get('filters', {})
        export_format = data.get('format', 'json')

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        cases = loop.run_until_complete(case_engine.list_cases(filters))

        if export_format == 'json':
            export_data = {
                "cases": [case.to_dict() for case in cases],
                "exported_at": datetime.utcnow().isoformat(),
                "total_cases": len(cases)
            }

            return jsonify({
                "success": True,
                "data": export_data,
                "format": "json"
            }), 200

        elif export_format == 'csv':
            # Simple CSV export
            csv_data = "case_id,title,status,priority,assigned_to,created_at\n"
            for case in cases:
                csv_data += f"{case.case_id},{case.title},{case.status.value},{case.priority.value},{case.assigned_to or ''},{case.created_at.isoformat()}\n"

            return jsonify({
                "success": True,
                "data": csv_data,
                "format": "csv"
            }), 200

        else:
            return jsonify({
                "success": False,
                "error": f"Unsupported export format: {export_format}"
            }), 400

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to export cases: {str(e)}"
        }), 500

# Health and Status

@app.route('/api/v2/cases/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        return jsonify({
            "success": True,
            "service": "case_management",
            "version": "2.0.0",
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Health check failed: {str(e)}"
        }), 500

@app.route('/api/v2/cases/info', methods=['GET'])
def get_info():
    """Get service information"""
    try:
        return jsonify({
            "success": True,
            "service_info": {
                "name": "Case Management Engine",
                "version": "2.0.0",
                "description": "Comprehensive incident case management and investigation tracking",
                "supported_case_types": [ct.value for ct in CaseType],
                "supported_statuses": [cs.value for cs in CaseStatus],
                "supported_priorities": [cp.value for cp in CasePriority],
                "supported_evidence_types": [et.value for et in EvidenceType],
                "features": [
                    "Case lifecycle management",
                    "Evidence tracking with chain of custody",
                    "SLA monitoring and compliance",
                    "Automatic escalation rules",
                    "MITRE ATT&CK mapping",
                    "Bulk operations",
                    "Advanced search and filtering",
                    "Metrics and analytics"
                ]
            }
        }), 200

    except Exception as e:
        return jsonify({
            "success": False,
            "error": f"Failed to get service info: {str(e)}"
        }), 500

if __name__ == '__main__':
    # Initialize the engine
    loop = asyncio.get_event_loop()
    loop.run_until_complete(init_engine())

    # Run the Flask app
    app.run(host='0.0.0.0', port=5011, debug=True)