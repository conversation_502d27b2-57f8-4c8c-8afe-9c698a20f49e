"""
CTI (Cyber Threat Intelligence) Collector for SIEMLess v2.0
Handles OTX and other threat intelligence feed ingestion
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from uuid import uuid4
import logging
import redis.asyncio as redis
from OTXv2 import OTXv2
import hashlib

logger = logging.getLogger(__name__)

class CTICollector:
    """Collects and processes threat intelligence from multiple sources"""

    def __init__(self, redis_client: redis.Redis, config: Dict):
        self.redis_client = redis_client
        self.config = config
        self.sources = {}
        self.collection_interval = config.get('collection_interval', 3600)  # 1 hour default
        self.initialize_sources()

    def initialize_sources(self):
        """Initialize CTI source clients"""
        # OTX Integration
        if self.config.get('otx_api_key'):
            self.sources['otx'] = OTXClient(
                api_key=self.config['otx_api_key'],
                redis_client=self.redis_client
            )
            logger.info("OTX CTI source initialized")

        # Add other CTI sources here
        if self.config.get('misp_url') and self.config.get('misp_api_key'):
            self.sources['misp'] = MISPClient(
                url=self.config['misp_url'],
                api_key=self.config['misp_api_key'],
                redis_client=self.redis_client
            )
            logger.info("MISP CTI source initialized")

        if self.config.get('threatfox_api_key'):
            self.sources['threatfox'] = ThreatFoxClient(
                api_key=self.config['threatfox_api_key'],
                redis_client=self.redis_client
            )
            logger.info("ThreatFox CTI source initialized")

    async def start_collection(self):
        """Start continuous CTI collection"""
        logger.info("Starting CTI collection service")

        while True:
            try:
                # Collect from all sources
                await self.collect_all_sources()

                # Wait for next collection interval
                await asyncio.sleep(self.collection_interval)

            except Exception as e:
                logger.error(f"CTI collection error: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retry

    async def collect_all_sources(self):
        """Collect threat intelligence from all configured sources"""
        collection_tasks = []

        for source_name, source_client in self.sources.items():
            logger.info(f"Collecting CTI from {source_name}")
            collection_tasks.append(
                self.collect_from_source(source_name, source_client)
            )

        # Collect from all sources in parallel
        results = await asyncio.gather(*collection_tasks, return_exceptions=True)

        # Process results
        total_indicators = 0
        for source_name, result in zip(self.sources.keys(), results):
            if isinstance(result, Exception):
                logger.error(f"Failed to collect from {source_name}: {result}")
            else:
                total_indicators += result
                logger.info(f"Collected {result} indicators from {source_name}")

        logger.info(f"Total CTI indicators collected: {total_indicators}")

        # Notify Backend Engine to process new CTI data
        await self.notify_backend_engine(total_indicators)

    async def collect_from_source(self, source_name: str, source_client) -> int:
        """Collect CTI from a specific source"""
        try:
            indicators = await source_client.collect()

            # Process and store indicators
            processed_count = 0
            for indicator in indicators:
                # Enrich with source metadata
                indicator['source'] = source_name
                indicator['collected_at'] = datetime.utcnow().isoformat()

                # Route to appropriate processing
                await self.route_indicator(indicator)
                processed_count += 1

            return processed_count

        except Exception as e:
            logger.error(f"Error collecting from {source_name}: {e}")
            raise

    async def route_indicator(self, indicator: Dict):
        """Route indicator to appropriate engine for processing"""
        indicator_type = indicator.get('type', 'unknown')

        # Publish to Redis channels for processing
        if indicator_type in ['ip', 'domain', 'url', 'hash']:
            # Send to Backend Engine for rule generation
            await self.redis_client.publish(
                'backend.cti.indicator',
                json.dumps(indicator)
            )

        if indicator_type in ['malware', 'campaign', 'threat-actor']:
            # Send to Intelligence Engine for pattern extraction
            await self.redis_client.publish(
                'intelligence.cti.threat',
                json.dumps(indicator)
            )

        # Store in Redis for immediate enrichment use
        await self.store_indicator(indicator)

    async def store_indicator(self, indicator: Dict):
        """Store indicator in Redis for quick lookup"""
        indicator_type = indicator.get('type')
        indicator_value = indicator.get('value')

        if not indicator_type or not indicator_value:
            return

        # Create cache key
        cache_key = f"cti:{indicator_type}:{indicator_value}"

        # Store with TTL based on indicator confidence
        ttl = self.calculate_ttl(indicator)

        await self.redis_client.setex(
            cache_key,
            ttl,
            json.dumps(indicator)
        )

    def calculate_ttl(self, indicator: Dict) -> int:
        """Calculate TTL based on indicator freshness and confidence"""
        # High confidence indicators get longer TTL
        confidence = indicator.get('confidence', 50)

        if confidence >= 80:
            return 7 * 24 * 3600  # 7 days
        elif confidence >= 50:
            return 3 * 24 * 3600  # 3 days
        else:
            return 24 * 3600  # 1 day

    async def notify_backend_engine(self, indicator_count: int):
        """Notify Backend Engine about new CTI data"""
        notification = {
            'event': 'cti_collection_complete',
            'timestamp': datetime.utcnow().isoformat(),
            'indicator_count': indicator_count,
            'sources': list(self.sources.keys())
        }

        await self.redis_client.publish(
            'backend.cti.collection_complete',
            json.dumps(notification)
        )


class OTXClient:
    """OTX (Open Threat Exchange) CTI client"""

    def __init__(self, api_key: str, redis_client: redis.Redis):
        self.api_key = api_key
        self.redis_client = redis_client
        self.otx = OTXv2(api_key)
        self.last_pulse_timestamp = None

    async def collect(self) -> List[Dict]:
        """Collect latest threat intelligence from OTX"""
        indicators = []

        try:
            # Get subscribed pulses (threat intel bundles)
            pulses = await self.get_pulses()

            for pulse in pulses:
                # Extract indicators from pulse
                pulse_indicators = self.extract_indicators(pulse)
                indicators.extend(pulse_indicators)

            # Skip malware samples - API doesn't have search_malware
            # malware_indicators = await self.get_malware_indicators()
            # indicators.extend(malware_indicators)

            # Deduplicate indicators
            indicators = self.deduplicate_indicators(indicators)

            logger.info(f"Collected {len(indicators)} indicators from OTX")

        except Exception as e:
            logger.error(f"OTX collection error: {e}")

        return indicators

    async def get_pulses(self, limit: int = 50) -> List[Dict]:
        """Get latest OTX pulses"""
        try:
            # Run OTX API call in thread pool to avoid blocking
            loop = asyncio.get_event_loop()

            # Get subscribed pulses - getall returns a generator
            pulse_generator = await loop.run_in_executor(
                None,
                self.otx.getall,
                limit
            )

            # Convert generator to list
            pulses = []
            if pulse_generator:
                # Handle both dict and generator responses
                if isinstance(pulse_generator, dict):
                    pulses = pulse_generator.get('results', [])
                else:
                    # It's a generator, collect pulses
                    for pulse in pulse_generator:
                        pulses.append(pulse)
                        if len(pulses) >= limit:
                            break

            return pulses

        except Exception as e:
            logger.error(f"Failed to get OTX pulses: {e}")
            # Fallback to search if subscribed fails
            return await self.search_pulses()

    async def search_pulses(self, query: str = 'malware', limit: int = 10) -> List[Dict]:
        """Search for pulses using OTX search API"""
        try:
            import requests

            headers = {'X-OTX-API-KEY': self.api_key}
            base_url = 'https://otx.alienvault.com/api/v1'

            # Search for recent threats
            params = {'q': query, 'limit': limit}
            response = requests.get(
                f'{base_url}/search/pulses',
                headers=headers,
                params=params,
                timeout=15
            )

            if response.status_code == 200:
                data = response.json()
                return data.get('results', [])

        except Exception as e:
            logger.error(f"Search pulses failed: {e}")

        return []

    def extract_indicators(self, pulse: Dict) -> List[Dict]:
        """Extract indicators from OTX pulse"""
        indicators = []

        pulse_id = pulse.get('id')
        pulse_name = pulse.get('name', 'Unknown')
        pulse_tags = pulse.get('tags', [])
        pulse_references = pulse.get('references', [])

        for otx_indicator in pulse.get('indicators', []):
            indicator = {
                'type': self.map_indicator_type(otx_indicator.get('type')),
                'value': otx_indicator.get('indicator'),
                'source': 'otx',
                'pulse_id': pulse_id,
                'pulse_name': pulse_name,
                'tags': pulse_tags,
                'references': pulse_references,
                'confidence': self.calculate_confidence(otx_indicator),
                'first_seen': otx_indicator.get('created'),
                'last_seen': otx_indicator.get('modified'),
                'description': otx_indicator.get('description', ''),
                'metadata': {
                    'otx_indicator_id': otx_indicator.get('id'),
                    'is_active': otx_indicator.get('is_active', True)
                }
            }

            # Add MITRE ATT&CK mapping if available
            if pulse.get('attack_ids'):
                indicator['mitre_attack'] = pulse['attack_ids']

            indicators.append(indicator)

        return indicators

    async def get_malware_indicators(self, days: int = 7) -> List[Dict]:
        """Get recent malware indicators from OTX"""
        indicators = []

        try:
            loop = asyncio.get_event_loop()

            # Calculate date range
            since = (datetime.utcnow() - timedelta(days=days)).strftime('%Y-%m-%d')

            # Get malware samples
            malware_data = await loop.run_in_executor(
                None,
                lambda: self.otx.search_malware(query='*', since=since)
            )

            for malware in malware_data.get('results', []):
                # Create hash indicators
                for hash_type in ['md5', 'sha1', 'sha256']:
                    if hash_value := malware.get(hash_type):
                        indicators.append({
                            'type': 'hash',
                            'value': hash_value,
                            'hash_type': hash_type,
                            'source': 'otx',
                            'malware_family': malware.get('family', ''),
                            'confidence': 85,
                            'tags': ['malware'] + malware.get('tags', []),
                            'first_seen': malware.get('first_seen'),
                            'metadata': {
                                'file_type': malware.get('file_type'),
                                'file_size': malware.get('file_size')
                            }
                        })

        except Exception as e:
            logger.error(f"Failed to get malware indicators: {e}")

        return indicators

    def map_indicator_type(self, otx_type: str) -> str:
        """Map OTX indicator types to standard types"""
        type_mapping = {
            'IPv4': 'ip',
            'IPv6': 'ip',
            'domain': 'domain',
            'hostname': 'domain',
            'URL': 'url',
            'URI': 'url',
            'FileHash-MD5': 'hash',
            'FileHash-SHA1': 'hash',
            'FileHash-SHA256': 'hash',
            'email': 'email',
            'CVE': 'cve',
            'YARA': 'yara',
            'JA3': 'ja3',
            'CIDR': 'cidr'
        }

        return type_mapping.get(otx_type, 'unknown')

    def calculate_confidence(self, indicator: Dict) -> int:
        """Calculate confidence score for indicator"""
        base_confidence = 70  # OTX base confidence

        # Adjust based on validation flags
        if indicator.get('is_active'):
            base_confidence += 5

        # Adjust based on role (malware, c2, etc)
        role = indicator.get('role', '')
        if role in ['malware', 'c2', 'botnet']:
            base_confidence += 10

        # Cap at 95
        return min(base_confidence, 95)

    def deduplicate_indicators(self, indicators: List[Dict]) -> List[Dict]:
        """Remove duplicate indicators"""
        seen = set()
        unique_indicators = []

        for indicator in indicators:
            # Create unique key
            key = f"{indicator['type']}:{indicator['value']}"

            if key not in seen:
                seen.add(key)
                unique_indicators.append(indicator)

        return unique_indicators


class MISPClient:
    """MISP threat intelligence platform client"""

    def __init__(self, url: str, api_key: str, redis_client: redis.Redis):
        self.url = url
        self.api_key = api_key
        self.redis_client = redis_client

    async def collect(self) -> List[Dict]:
        """Collect threat intelligence from MISP"""
        indicators = []

        try:
            # Get recent events
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Authorization': self.api_key,
                    'Accept': 'application/json'
                }

                # Get events from last 7 days
                params = {
                    'from': (datetime.utcnow() - timedelta(days=7)).strftime('%Y-%m-%d')
                }

                async with session.get(
                    f"{self.url}/events/index",
                    headers=headers,
                    params=params
                ) as response:
                    if response.status == 200:
                        events = await response.json()

                        # Extract indicators from events
                        for event in events:
                            event_indicators = self.extract_event_indicators(event)
                            indicators.extend(event_indicators)

        except Exception as e:
            logger.error(f"MISP collection error: {e}")

        return indicators

    def extract_event_indicators(self, event: Dict) -> List[Dict]:
        """Extract indicators from MISP event"""
        indicators = []

        for attribute in event.get('Attribute', []):
            indicator = {
                'type': self.map_misp_type(attribute.get('type')),
                'value': attribute.get('value'),
                'source': 'misp',
                'event_id': event.get('id'),
                'event_name': event.get('info'),
                'tags': [tag['name'] for tag in attribute.get('Tag', [])],
                'confidence': int(attribute.get('confidence', 75)),
                'category': attribute.get('category'),
                'comment': attribute.get('comment', '')
            }

            indicators.append(indicator)

        return indicators

    def map_misp_type(self, misp_type: str) -> str:
        """Map MISP types to standard types"""
        type_mapping = {
            'ip-src': 'ip',
            'ip-dst': 'ip',
            'domain': 'domain',
            'url': 'url',
            'md5': 'hash',
            'sha1': 'hash',
            'sha256': 'hash',
            'email-src': 'email',
            'email-dst': 'email'
        }

        return type_mapping.get(misp_type, 'unknown')


class ThreatFoxClient:
    """ThreatFox threat intelligence client"""

    def __init__(self, api_key: str, redis_client: redis.Redis):
        self.api_key = api_key
        self.redis_client = redis_client
        self.api_url = "https://threatfox-api.abuse.ch/api/v1/"

    async def collect(self) -> List[Dict]:
        """Collect threat intelligence from ThreatFox"""
        indicators = []

        try:
            async with aiohttp.ClientSession() as session:
                # Get recent IOCs
                payload = {
                    'query': 'get_iocs',
                    'days': 7
                }

                async with session.post(
                    self.api_url,
                    json=payload
                ) as response:
                    if response.status == 200:
                        data = await response.json()

                        if data.get('query_status') == 'ok':
                            for ioc in data.get('data', []):
                                indicator = self.parse_ioc(ioc)
                                if indicator:
                                    indicators.append(indicator)

        except Exception as e:
            logger.error(f"ThreatFox collection error: {e}")

        return indicators

    def parse_ioc(self, ioc: Dict) -> Optional[Dict]:
        """Parse ThreatFox IOC into standard format"""
        ioc_type = ioc.get('ioc_type')

        if ioc_type in ['domain', 'ip:port', 'url']:
            return {
                'type': self.map_threatfox_type(ioc_type),
                'value': ioc.get('ioc'),
                'source': 'threatfox',
                'malware': ioc.get('malware'),
                'confidence': int(ioc.get('confidence_level', 50)),
                'tags': ioc.get('tags', []),
                'first_seen': ioc.get('first_seen'),
                'last_seen': ioc.get('last_seen'),
                'reporter': ioc.get('reporter'),
                'metadata': {
                    'malware_printable': ioc.get('malware_printable'),
                    'threat_type': ioc.get('threat_type')
                }
            }

        return None

    def map_threatfox_type(self, tf_type: str) -> str:
        """Map ThreatFox types to standard types"""
        type_mapping = {
            'domain': 'domain',
            'ip:port': 'ip',
            'url': 'url'
        }

        return type_mapping.get(tf_type, 'unknown')


class CTIEnricher:
    """Enriches entities with CTI data"""

    def __init__(self, redis_client: redis.Redis):
        self.redis_client = redis_client

    async def enrich_entity(self, entity: Dict) -> Dict:
        """Enrich entity with CTI data"""
        entity_type = entity.get('type')
        entity_value = entity.get('value')

        if not entity_type or not entity_value:
            return entity

        # Look up CTI data in Redis
        cache_key = f"cti:{entity_type}:{entity_value}"
        cti_data = await self.redis_client.get(cache_key)

        if cti_data:
            cti_info = json.loads(cti_data)

            # Add CTI enrichment
            entity['cti_enrichment'] = {
                'found': True,
                'source': cti_info.get('source'),
                'confidence': cti_info.get('confidence'),
                'tags': cti_info.get('tags', []),
                'malware': cti_info.get('malware'),
                'first_seen': cti_info.get('first_seen'),
                'last_seen': cti_info.get('last_seen'),
                'references': cti_info.get('references', []),
                'mitre_attack': cti_info.get('mitre_attack', [])
            }

            # Increase risk score based on CTI match
            if 'risk_score' in entity:
                entity['risk_score'] += cti_info.get('confidence', 50) / 10
        else:
            entity['cti_enrichment'] = {'found': False}

        return entity