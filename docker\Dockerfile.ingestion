# Ingestion Engine Dockerfile
FROM siemless-v2-base:latest

# Copy Ingestion Engine specific code
COPY engines/ingestion/ /app/engines/ingestion/

# Expose ports for syslog and HTTP ingestion
EXPOSE 514/udp 8514

# Health check - no HTTP endpoint for ingestion engine
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD redis-cli -h $REDIS_HOST ping || exit 1

# Set the command to run Ingestion Engine
CMD ["python", "engines/ingestion/ingestion.py"]