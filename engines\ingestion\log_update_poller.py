"""
Log Update Poller
Regular polling of log sources with priority on most recent data
Integrates with historical context for continuous timeline building
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import aiohttp

logger = logging.getLogger(__name__)


@dataclass
class PollingConfig:
    """Configuration for log source polling"""
    source_id: str
    source_type: str  # 'siem', 'edr', 'firewall', 'cloud'
    poll_interval_seconds: int
    fetch_size: int = 1000
    time_window_minutes: int = 5  # Look back 5 minutes for new logs
    priority: str = 'recent'  # Always get most recent first
    enabled: bool = True
    last_poll: Optional[datetime] = None
    last_checkpoint: Optional[datetime] = None


@dataclass
class PollResult:
    """Result of a polling operation"""
    source_id: str
    poll_timestamp: datetime
    logs_fetched: int
    time_range_start: datetime
    time_range_end: datetime
    checkpoint: datetime
    errors: List[str]
    duration_seconds: float


class LogUpdatePoller:
    """
    Polls log sources for updates with emphasis on most recent data

    Key Features:
    1. Recent-first priority (always get newest logs first)
    2. Checkpoint-based continuation (never miss logs)
    3. Configurable polling intervals per source
    4. Automatic backfill for gaps
    5. Priority adjustment based on activity level
    """

    def __init__(self, redis_client=None, db_connection=None):
        self.redis = redis_client
        self.db = db_connection
        self.polling_configs: Dict[str, PollingConfig] = {}
        self.is_running = False

        # Default polling intervals by source type
        self.default_intervals = {
            'edr': 60,           # EDR: 1 minute (high activity)
            'siem': 300,         # SIEM: 5 minutes (aggregated)
            'firewall': 120,     # Firewall: 2 minutes (moderate)
            'cloud': 180,        # Cloud: 3 minutes
            'threat_intel': 3600 # CTI: 1 hour (low frequency)
        }

    async def start(self):
        """Start the polling system"""
        self.is_running = True
        logger.info("Log Update Poller started")

        # Load configurations
        await self._load_polling_configs()

        # Start polling tasks
        tasks = [
            asyncio.create_task(self._run_pollers()),
            asyncio.create_task(self._monitor_poll_health()),
            asyncio.create_task(self._adjust_intervals_dynamically()),
            asyncio.create_task(self._handle_backfill_requests())
        ]

        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Polling error: {e}")
        finally:
            self.is_running = False

    async def stop(self):
        """Stop the polling system"""
        self.is_running = False
        logger.info("Log Update Poller stopped")

    async def add_source(self, source_config: Dict) -> bool:
        """Add or update a polling source"""
        try:
            source_id = source_config['source_id']
            source_type = source_config.get('source_type', 'siem')

            config = PollingConfig(
                source_id=source_id,
                source_type=source_type,
                poll_interval_seconds=source_config.get(
                    'poll_interval',
                    self.default_intervals.get(source_type, 300)
                ),
                fetch_size=source_config.get('fetch_size', 1000),
                time_window_minutes=source_config.get('time_window_minutes', 5),
                priority='recent',
                enabled=source_config.get('enabled', True)
            )

            self.polling_configs[source_id] = config

            # Save to database
            if self.db:
                await self._save_polling_config(config)

            logger.info(f"Added polling source: {source_id} ({source_type})")
            return True

        except Exception as e:
            logger.error(f"Error adding source: {e}")
            return False

    async def _run_pollers(self):
        """Main polling loop"""
        while self.is_running:
            try:
                current_time = datetime.utcnow()

                for source_id, config in self.polling_configs.items():
                    if not config.enabled:
                        continue

                    # Check if it's time to poll
                    if config.last_poll is None or \
                       (current_time - config.last_poll).total_seconds() >= config.poll_interval_seconds:

                        # Poll this source
                        result = await self._poll_source(config)

                        # Update last poll time
                        config.last_poll = current_time

                        # Update checkpoint if successful
                        if result.logs_fetched > 0:
                            config.last_checkpoint = result.checkpoint

                        # Publish results
                        await self._publish_poll_result(result)

                        # Store metrics
                        await self._store_poll_metrics(result)

            except Exception as e:
                logger.error(f"Error in polling loop: {e}")

            await asyncio.sleep(10)  # Check every 10 seconds

    async def _poll_source(self, config: PollingConfig) -> PollResult:
        """
        Poll a single source for updates
        ALWAYS FETCHES MOST RECENT FIRST
        """
        start_time = datetime.utcnow()
        errors = []
        logs_fetched = 0
        checkpoint = config.last_checkpoint or (datetime.utcnow() - timedelta(minutes=config.time_window_minutes))

        try:
            # Calculate time range (from last checkpoint to now)
            time_range_start = checkpoint
            time_range_end = datetime.utcnow()

            logger.info(f"Polling {config.source_id} from {time_range_start} to {time_range_end}")

            # Fetch logs based on source type
            if config.source_type == 'edr':
                logs = await self._poll_edr(config, time_range_start, time_range_end)
            elif config.source_type == 'siem':
                logs = await self._poll_siem(config, time_range_start, time_range_end)
            elif config.source_type == 'firewall':
                logs = await self._poll_firewall(config, time_range_start, time_range_end)
            elif config.source_type == 'cloud':
                logs = await self._poll_cloud(config, time_range_start, time_range_end)
            else:
                logs = []
                errors.append(f"Unknown source type: {config.source_type}")

            logs_fetched = len(logs)

            # Send logs to ingestion pipeline
            if logs:
                await self._send_to_ingestion(config.source_id, logs)
                checkpoint = max([log.get('timestamp', time_range_start) for log in logs])

        except Exception as e:
            logger.error(f"Error polling {config.source_id}: {e}")
            errors.append(str(e))

        duration = (datetime.utcnow() - start_time).total_seconds()

        return PollResult(
            source_id=config.source_id,
            poll_timestamp=start_time,
            logs_fetched=logs_fetched,
            time_range_start=time_range_start,
            time_range_end=time_range_end,
            checkpoint=checkpoint,
            errors=errors,
            duration_seconds=duration
        )

    async def _poll_edr(self, config: PollingConfig, start_time: datetime, end_time: datetime) -> List[Dict]:
        """Poll EDR source (CrowdStrike, SentinelOne, etc.)"""
        try:
            # Example: CrowdStrike Falcon Event Stream
            # In production, use actual API

            query = {
                'filter': f"timestamp:>='{start_time.isoformat()}' AND timestamp:<='{end_time.isoformat()}'",
                'sort': 'timestamp|desc',  # MOST RECENT FIRST
                'limit': config.fetch_size
            }

            # Simulate API call (replace with actual implementation)
            logs = await self._fetch_from_api(
                config.source_id,
                'edr',
                query
            )

            return logs

        except Exception as e:
            logger.error(f"EDR poll error: {e}")
            return []

    async def _poll_siem(self, config: PollingConfig, start_time: datetime, end_time: datetime) -> List[Dict]:
        """Poll SIEM source (Splunk, Elastic, etc.)"""
        try:
            query = {
                'query': f'@timestamp:[{start_time.isoformat()} TO {end_time.isoformat()}]',
                'sort': [{'@timestamp': {'order': 'desc'}}],  # MOST RECENT FIRST
                'size': config.fetch_size
            }

            logs = await self._fetch_from_api(
                config.source_id,
                'siem',
                query
            )

            return logs

        except Exception as e:
            logger.error(f"SIEM poll error: {e}")
            return []

    async def _poll_firewall(self, config: PollingConfig, start_time: datetime, end_time: datetime) -> List[Dict]:
        """Poll firewall logs (Palo Alto, Fortinet, etc.)"""
        try:
            query = {
                'time_generated': {
                    'start': start_time.isoformat(),
                    'end': end_time.isoformat()
                },
                'sort': 'time_generated desc',  # MOST RECENT FIRST
                'limit': config.fetch_size
            }

            logs = await self._fetch_from_api(
                config.source_id,
                'firewall',
                query
            )

            return logs

        except Exception as e:
            logger.error(f"Firewall poll error: {e}")
            return []

    async def _poll_cloud(self, config: PollingConfig, start_time: datetime, end_time: datetime) -> List[Dict]:
        """Poll cloud provider logs (AWS CloudTrail, Azure Activity, GCP Audit)"""
        try:
            query = {
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'max_results': config.fetch_size,
                'order': 'desc'  # MOST RECENT FIRST
            }

            logs = await self._fetch_from_api(
                config.source_id,
                'cloud',
                query
            )

            return logs

        except Exception as e:
            logger.error(f"Cloud poll error: {e}")
            return []

    async def _fetch_from_api(self, source_id: str, source_type: str, query: Dict) -> List[Dict]:
        """
        Generic API fetch method
        Replace with actual API integration
        """
        try:
            # Get API credentials from config
            api_config = await self._get_api_config(source_id)
            if not api_config:
                return []

            # Make API call
            async with aiohttp.ClientSession() as session:
                headers = {
                    'Authorization': f"Bearer {api_config.get('api_key')}",
                    'Content-Type': 'application/json'
                }

                async with session.post(
                    api_config.get('endpoint'),
                    headers=headers,
                    json=query
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        # Extract logs from response (format varies by source)
                        return data.get('logs', data.get('events', data.get('results', [])))
                    else:
                        logger.warning(f"API call failed: {response.status}")
                        return []

        except Exception as e:
            logger.error(f"API fetch error: {e}")
            return []

    async def _send_to_ingestion(self, source_id: str, logs: List[Dict]):
        """Send fetched logs to ingestion pipeline"""
        if not self.redis:
            return

        try:
            # Publish to ingestion channel
            message = {
                'source_id': source_id,
                'timestamp': datetime.utcnow().isoformat(),
                'log_count': len(logs),
                'logs': logs
            }

            await self.redis.publish(
                'ingestion.logs.poll',
                json.dumps(message)
            )

            logger.info(f"Sent {len(logs)} logs from {source_id} to ingestion")

        except Exception as e:
            logger.error(f"Error sending to ingestion: {e}")

    async def _monitor_poll_health(self):
        """Monitor polling health and alert on issues"""
        while self.is_running:
            try:
                current_time = datetime.utcnow()

                for source_id, config in self.polling_configs.items():
                    if not config.enabled:
                        continue

                    # Check if source is stale (no poll in 2x expected interval)
                    if config.last_poll:
                        time_since_poll = (current_time - config.last_poll).total_seconds()
                        if time_since_poll > (config.poll_interval_seconds * 2):
                            # Alert: Source is stale
                            await self._alert_stale_source(source_id, time_since_poll)

            except Exception as e:
                logger.error(f"Health monitor error: {e}")

            await asyncio.sleep(60)  # Check every minute

    async def _adjust_intervals_dynamically(self):
        """Adjust polling intervals based on activity level"""
        while self.is_running:
            try:
                # Get recent poll metrics
                for source_id, config in self.polling_configs.items():
                    if not config.enabled:
                        continue

                    # Get average logs per poll
                    avg_logs = await self._get_average_logs_per_poll(source_id)

                    # Adjust interval based on activity
                    if avg_logs > 5000:
                        # High activity: Poll more frequently
                        new_interval = max(30, config.poll_interval_seconds // 2)
                    elif avg_logs < 100:
                        # Low activity: Poll less frequently
                        new_interval = min(1800, config.poll_interval_seconds * 2)
                    else:
                        # Normal activity: Keep current interval
                        new_interval = config.poll_interval_seconds

                    if new_interval != config.poll_interval_seconds:
                        logger.info(f"Adjusting {source_id} interval: {config.poll_interval_seconds}s -> {new_interval}s")
                        config.poll_interval_seconds = new_interval

            except Exception as e:
                logger.error(f"Interval adjustment error: {e}")

            await asyncio.sleep(600)  # Adjust every 10 minutes

    async def _handle_backfill_requests(self):
        """Handle requests to backfill historical data"""
        if not self.redis:
            return

        try:
            pubsub = self.redis.pubsub()
            await pubsub.subscribe('ingestion.backfill.request')

            while self.is_running:
                message = await pubsub.get_message(timeout=1.0)
                if message and message['type'] == 'message':
                    request = json.loads(message['data'])

                    source_id = request.get('source_id')
                    start_time = datetime.fromisoformat(request.get('start_time'))
                    end_time = datetime.fromisoformat(request.get('end_time'))

                    logger.info(f"Backfill request: {source_id} from {start_time} to {end_time}")

                    # Execute backfill
                    await self._execute_backfill(source_id, start_time, end_time)

                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"Backfill handler error: {e}")

    async def _execute_backfill(self, source_id: str, start_time: datetime, end_time: datetime):
        """Execute historical backfill"""
        try:
            config = self.polling_configs.get(source_id)
            if not config:
                logger.error(f"Unknown source for backfill: {source_id}")
                return

            # Split into chunks (poll in reverse chronological order)
            chunk_size = timedelta(hours=1)
            current_end = end_time

            while current_end > start_time:
                current_start = max(current_end - chunk_size, start_time)

                # Poll this chunk (most recent in chunk first)
                result = await self._poll_source_time_range(
                    config,
                    current_start,
                    current_end
                )

                logger.info(f"Backfill chunk: {source_id} {current_start} - {current_end}: {result.logs_fetched} logs")

                # Move to next chunk (going backwards in time)
                current_end = current_start

            logger.info(f"Backfill complete: {source_id}")

        except Exception as e:
            logger.error(f"Backfill execution error: {e}")

    async def _poll_source_time_range(self, config: PollingConfig, start_time: datetime, end_time: datetime) -> PollResult:
        """Poll source for specific time range"""
        # Similar to _poll_source but with explicit time range
        return await self._poll_source(config)

    async def _load_polling_configs(self):
        """Load polling configurations from database"""
        if not self.db:
            return

        try:
            query = """
                SELECT
                    source_id,
                    source_type,
                    poll_interval_seconds,
                    fetch_size,
                    time_window_minutes,
                    enabled,
                    last_checkpoint
                FROM log_polling_config
                WHERE enabled = TRUE
            """

            cursor = self.db.cursor()
            cursor.execute(query)
            results = cursor.fetchall()
            cursor.close()

            for row in results:
                config = PollingConfig(
                    source_id=row[0],
                    source_type=row[1],
                    poll_interval_seconds=row[2],
                    fetch_size=row[3],
                    time_window_minutes=row[4],
                    enabled=row[5],
                    last_checkpoint=row[6]
                )
                self.polling_configs[row[0]] = config

            logger.info(f"Loaded {len(self.polling_configs)} polling configurations")

        except Exception as e:
            logger.error(f"Error loading configs: {e}")

    async def _save_polling_config(self, config: PollingConfig):
        """Save polling configuration to database"""
        if not self.db:
            return

        try:
            query = """
                INSERT INTO log_polling_config (
                    source_id, source_type, poll_interval_seconds,
                    fetch_size, time_window_minutes, enabled, last_checkpoint
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (source_id) DO UPDATE
                SET source_type = EXCLUDED.source_type,
                    poll_interval_seconds = EXCLUDED.poll_interval_seconds,
                    fetch_size = EXCLUDED.fetch_size,
                    time_window_minutes = EXCLUDED.time_window_minutes,
                    enabled = EXCLUDED.enabled,
                    last_checkpoint = EXCLUDED.last_checkpoint
            """

            cursor = self.db.cursor()
            cursor.execute(query, (
                config.source_id,
                config.source_type,
                config.poll_interval_seconds,
                config.fetch_size,
                config.time_window_minutes,
                config.enabled,
                config.last_checkpoint
            ))
            self.db.commit()
            cursor.close()

        except Exception as e:
            logger.error(f"Error saving config: {e}")
            self.db.rollback()

    async def _publish_poll_result(self, result: PollResult):
        """Publish poll result metrics"""
        if not self.redis:
            return

        await self.redis.publish(
            'ingestion.poll.metrics',
            json.dumps(asdict(result), default=str)
        )

    async def _store_poll_metrics(self, result: PollResult):
        """Store poll metrics for monitoring"""
        if not self.db:
            return

        try:
            query = """
                INSERT INTO log_polling_metrics (
                    source_id, poll_timestamp, logs_fetched,
                    time_range_start, time_range_end, duration_seconds,
                    errors
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """

            cursor = self.db.cursor()
            cursor.execute(query, (
                result.source_id,
                result.poll_timestamp,
                result.logs_fetched,
                result.time_range_start,
                result.time_range_end,
                result.duration_seconds,
                json.dumps(result.errors)
            ))
            self.db.commit()
            cursor.close()

        except Exception as e:
            logger.error(f"Error storing metrics: {e}")
            self.db.rollback()

    async def _get_api_config(self, source_id: str) -> Optional[Dict]:
        """Get API configuration for source"""
        # In production, retrieve from secure vault
        # For now, return placeholder
        return {
            'endpoint': 'https://api.example.com/logs',
            'api_key': 'placeholder'
        }

    async def _get_average_logs_per_poll(self, source_id: str) -> float:
        """Get average logs per poll for source"""
        if not self.db:
            return 0

        try:
            query = """
                SELECT AVG(logs_fetched)
                FROM log_polling_metrics
                WHERE source_id = %s
                AND poll_timestamp > NOW() - INTERVAL '1 hour'
            """

            cursor = self.db.cursor()
            cursor.execute(query, (source_id,))
            result = cursor.fetchone()
            cursor.close()

            return float(result[0]) if result and result[0] else 0

        except Exception as e:
            logger.error(f"Error getting average: {e}")
            return 0

    async def _alert_stale_source(self, source_id: str, seconds_since_poll: float):
        """Alert about stale polling source"""
        if not self.redis:
            return

        await self.redis.publish(
            'ingestion.alert.stale_source',
            json.dumps({
                'source_id': source_id,
                'seconds_since_poll': seconds_since_poll,
                'timestamp': datetime.utcnow().isoformat()
            })
        )

        logger.warning(f"ALERT: Source {source_id} is stale ({seconds_since_poll}s since last poll)")
