# Context Plugin Quick Start Guide
**Adding New Security Vendors to SIEMLess v2.0**

## ✅ What We Just Accomplished

**CrowdStrike Integration COMPLETE:**
- ✅ Switched to FalconPy SDK (CrowdStrike's official Python library)
- ✅ Plugin successfully authenticated to US-2 CrowdStrike
- ✅ Retrieved real host data: "010117039050LN1"
- ✅ End-to-end flow working: Ingestion → Contextualization → Backend

## Why Context Plugins?

**Problem**: Different vendors have different data formats
- CrowdStrike: `device.hostname`, `local_ip`, `agent_version`
- SentinelOne: `computerName`, `networkInterfaces[].inet`, `agentVersion`
- Elastic: `host.name`, `host.ip`, `agent.version`

**Solution**: Context Plugins standardize vendor-specific formats
- Plugin handles vendor API/SDK
- Translates to standard ContextResult format
- Contextualization engine extracts entities from standardized format

## Adding a New Vendor (5 Steps)

### Step 1: Check for Official SDK

**Priority Order:**
1. ✅ Official Python SDK (best - use this!)
2. ⚠️ Community SDK (verify security, check maintenance)
3. ❌ REST API wrapper (build your own)

**SDK Availability (as of 2025):**
- **CrowdStrike**: ✅ FalconPy (`crowdstrike-falconpy`)
- **Elastic**: ✅ elasticsearch-py (`elasticsearch`)
- **Microsoft Sentinel**: ✅ Azure Monitor (`azure-monitor-query`)
- **SentinelOne**: ⚠️ Community only (security risk on PyPI)
- **Palo Alto Cortex XDR**: ❌ No official SDK (REST API)

### Step 2: Create Plugin File

**Location**: `engines/ingestion/<vendor>_context_plugin.py`

**Template**:
```python
from typing import Dict, Any, List
from <vendor_sdk> import Client
from context_source_plugin import (
    ContextSourcePlugin, ContextQuery, ContextResult, ContextCategory
)

class <Vendor>ContextPlugin(ContextSourcePlugin):
    """
    <Vendor> plugin using official SDK
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config.get('api_key')
        self.base_url = config.get('base_url')
        self.client = None

    def get_source_name(self) -> str:
        return "<vendor_lowercase>"

    def get_supported_categories(self) -> List[ContextCategory]:
        return [
            ContextCategory.ASSET,      # Device/host info
            ContextCategory.DETECTION,  # Alerts/detections
            ContextCategory.INCIDENT,   # Incidents/cases
        ]

    def get_supported_query_types(self) -> List[str]:
        return ['ip', 'hostname', 'user']

    async def validate_credentials(self) -> bool:
        """Initialize SDK client and test auth"""
        try:
            self.client = Client(
                api_key=self.api_key,
                base_url=self.base_url
            )
            # Test auth with simple query
            response = self.client.test_connection()
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Auth failed: {e}")
            return False

    async def query_context(self, query: ContextQuery) -> List[ContextResult]:
        """Main query handler"""
        results = []

        for category in query.categories:
            if category == ContextCategory.ASSET:
                results.extend(await self._query_assets(query))
            elif category == ContextCategory.DETECTION:
                results.extend(await self._query_detections(query))

        return results

    async def _query_assets(self, query: ContextQuery) -> List[ContextResult]:
        """Query for asset/host information"""
        results = []

        # Build vendor-specific query
        if query.query_type == 'hostname':
            response = self.client.get_devices(hostname=query.query_value)
        elif query.query_type == 'ip':
            response = self.client.get_devices(ip=query.query_value)
        else:
            return results

        # Format each result
        for device in response.get('devices', []):
            results.append(self._format_asset_result(device))

        return results

    def _format_asset_result(self, device: Dict) -> ContextResult:
        """Translate vendor format → standard format"""
        return ContextResult(
            source_name=self.get_source_name(),
            category=ContextCategory.ASSET,
            confidence=0.95,
            data={
                # Map vendor fields → standard fields
                'device_id': device.get('id'),
                'hostname': device.get('name') or device.get('hostname'),
                'local_ip': device.get('ip_address'),
                'mac_address': device.get('mac'),
                'os_version': device.get('os'),
                'status': device.get('status'),
            },
            timestamp=device.get('last_seen'),
            metadata={'raw': device}  # Store original for debugging
        )
```

### Step 3: Add SDK to Requirements

**File**: `engines/ingestion/requirements.txt`

```bash
# Data source connectors
elasticsearch==8.9.0
crowdstrike-falconpy==1.3.0
<your-vendor-sdk>==<version>
```

### Step 4: Register Plugin

**File**: `engines/ingestion/ingestion_engine.py`

Find `_setup_context_plugins()` method and add:

```python
def _setup_context_plugins(self):
    """Setup context source plugins"""

    # CrowdStrike plugin
    crowdstrike_plugin = CrowdStrikeContextPlugin({
        'enabled': bool(os.getenv('CROWDSTRIKE_CLIENT_ID')),
        'client_id': os.getenv('CROWDSTRIKE_CLIENT_ID'),
        'client_secret': os.getenv('CROWDSTRIKE_CLIENT_SECRET'),
        'base_url': os.getenv('CROWDSTRIKE_BASE_URL')
    })
    self.context_manager.register_plugin(crowdstrike_plugin)

    # YOUR NEW PLUGIN HERE
    your_plugin = YourVendorContextPlugin({
        'enabled': bool(os.getenv('YOUR_VENDOR_API_KEY')),
        'api_key': os.getenv('YOUR_VENDOR_API_KEY'),
        'base_url': os.getenv('YOUR_VENDOR_URL')
    })
    self.context_manager.register_plugin(your_plugin)
```

### Step 5: Add Environment Variables

**File**: `docker-compose.yml`

Under `ingestion_engine` → `environment`:

```yaml
ingestion_engine:
  environment:
    # Existing vars...
    - CROWDSTRIKE_CLIENT_ID=${CROWDSTRIKE_CLIENT_ID}
    - CROWDSTRIKE_CLIENT_SECRET=${CROWDSTRIKE_CLIENT_SECRET}

    # Your new vendor
    - YOUR_VENDOR_API_KEY=${YOUR_VENDOR_API_KEY}
    - YOUR_VENDOR_URL=${YOUR_VENDOR_URL:-https://api.yourvendor.com}
```

**File**: `.env`

```bash
# Your Vendor
YOUR_VENDOR_API_KEY=your_api_key_here
YOUR_VENDOR_URL=https://api.yourvendor.com
```

## Testing Your Plugin

### 1. Rebuild Ingestion Engine
```bash
docker-compose up -d --build --no-deps ingestion_engine
```

### 2. Check Initialization
```bash
docker-compose logs ingestion_engine | grep -i "your_vendor\|initialized"
```

Expected output:
```
✓ your_vendor plugin initialized successfully
```

### 3. Test Query via Redis
```bash
docker-compose exec -T redis redis-cli PUBLISH 'ingestion.pull_context' '{
  "request_id": "test-vendor-001",
  "query_type": "hostname",
  "query_value": "test-device",
  "categories": ["asset"]
}'
```

### 4. Check Results
```bash
docker-compose logs --tail=50 ingestion_engine contextualization_engine | grep "test-vendor"
```

Expected flow:
```
ingestion          | [test-vendor-001] Pulling context for hostname=test-device
ingestion          | [test-vendor-001] Got 1 results from your_vendor
ingestion          | [test-vendor-001] Sending 1 results to Contextualization
contextualization  | [test-vendor-001] Extracting entities from context results
contextualization  | [test-vendor-001] Processing 1 results from your_vendor
```

## Real-World Example: CrowdStrike

**What We Built:**
```python
# engines/ingestion/crowdstrike_context_plugin.py (300 lines)

from falconpy import Hosts, Detects, Incidents

class CrowdStrikeContextPlugin(ContextSourcePlugin):

    async def validate_credentials(self) -> bool:
        self.hosts_client = Hosts(
            client_id=self.client_id,
            client_secret=self.client_secret,
            base_url=self.base_url
        )
        response = self.hosts_client.query_devices_by_filter(limit=1)
        return response['status_code'] == 200

    async def _query_hosts(self, query: ContextQuery):
        # Build FQL filter
        if query.query_type == 'hostname':
            fql_filter = f"hostname:'{query.query_value}'"

        # Query CrowdStrike
        query_response = self.hosts_client.query_devices_by_filter(
            filter=fql_filter, limit=50
        )

        device_ids = query_response['body']['resources']
        details = self.hosts_client.get_device_details(ids=device_ids)

        # Format results
        for host in details['body']['resources']:
            results.append(self._format_host_result(host))

    def _format_host_result(self, host: Dict) -> ContextResult:
        return ContextResult(
            source_name='crowdstrike',
            category=ContextCategory.ASSET,
            confidence=0.95,
            data={
                'device_id': host.get('device_id'),
                'hostname': host.get('hostname'),
                'local_ip': host.get('local_ip'),
                'mac_address': host.get('mac_address'),
                'os_version': host.get('os_version'),
                'status': host.get('status')
            },
            timestamp=host.get('last_seen'),
            metadata={'raw': host}
        )
```

**What It Does:**
1. Authenticates to CrowdStrike US-2 using FalconPy SDK
2. Queries hosts by hostname, IP, or device_id
3. Retrieves detailed device information
4. Translates CrowdStrike format → standard ContextResult
5. Sends to Contextualization for entity extraction

**Test Results:**
```bash
✓ crowdstrike plugin initialized successfully
✓ Retrieved host: 010117039050LN1
✓ Sent 1 results to Contextualization
✓ Extracted entities: hostname, IP, MAC, OS
```

## Common Patterns

### Pattern 1: Query Builder
```python
def _build_query(self, query: ContextQuery) -> str:
    """Build vendor-specific query from standard ContextQuery"""
    if query.query_type == 'hostname':
        return f"hostname:'{query.query_value}'"
    elif query.query_type == 'ip':
        return f"ip_address:'{query.query_value}'"
    # Add more as needed
```

### Pattern 2: Field Mapping
```python
# Map vendor fields → standard fields
FIELD_MAPPINGS = {
    'device_id': ['id', 'device_id', 'endpoint_id'],
    'hostname': ['hostname', 'name', 'computer_name', 'device_name'],
    'local_ip': ['ip', 'ip_address', 'local_ip', 'internal_ip'],
    'os_version': ['os', 'os_version', 'operating_system']
}

def _get_field_value(self, data: Dict, standard_field: str):
    """Get value from vendor data using field mappings"""
    for vendor_field in FIELD_MAPPINGS.get(standard_field, []):
        if vendor_field in data and data[vendor_field]:
            return data[vendor_field]
    return None
```

### Pattern 3: Error Handling
```python
async def query_context(self, query: ContextQuery):
    results = []
    try:
        # Query vendor API
        response = await self._query_vendor(query)

        # Format results
        for item in response.get('items', []):
            try:
                results.append(self._format_result(item))
            except Exception as e:
                self.logger.warning(f"Failed to format result: {e}")
                continue  # Skip bad results, continue processing

    except Exception as e:
        self.logger.error(f"Query failed: {e}", exc_info=True)
        return []  # Return empty, don't crash

    return results
```

## Supported Categories

Choose which categories your plugin supports:

```python
class ContextCategory(Enum):
    CTI = "cti"                      # Threat Intel (IOCs, threat feeds)
    ASSET = "asset"                  # Device/host information
    DETECTION = "detection"          # Security alerts/detections
    INCIDENT = "incident"            # Incidents/cases
    LOG = "log"                      # Raw log events
    IDENTITY = "identity"            # User/identity (AD, Okta)
    NETWORK = "network"              # Network flows, firewall
    VULNERABILITY = "vulnerability"  # Vuln scans (Tenable, Qualys)
```

**Example Category Support:**
- **EDR (CrowdStrike, SentinelOne)**: ASSET, DETECTION, INCIDENT
- **Identity (Active Directory)**: IDENTITY, ASSET
- **Network (Firewall, IDS)**: NETWORK, DETECTION
- **Vulnerability Scanner**: VULNERABILITY, ASSET

## Query Types

Choose which query types your plugin supports:

```python
def get_supported_query_types(self) -> List[str]:
    return [
        'ip',           # IP address lookup
        'hostname',     # Hostname/device name
        'user',         # Username
        'domain',       # Domain name
        'file_hash',    # File hash (SHA256/MD5)
        'device_id',    # Vendor-specific device ID
        'process',      # Process name
        'email',        # Email address
    ]
```

## Next Vendors to Add

**Priority Order (based on SDK availability):**

1. **Elastic Security** ✅ Official SDK
   - SDK: `elasticsearch-py`
   - Categories: LOG, DETECTION, ASSET
   - Query Types: ip, hostname, user, hash

2. **Microsoft Sentinel** ✅ Official SDK
   - SDK: `azure-monitor-query`
   - Categories: LOG, DETECTION, INCIDENT
   - Query Types: ip, hostname, user

3. **Active Directory** 🔧 Build wrapper
   - Library: `ldap3`
   - Categories: IDENTITY, ASSET
   - Query Types: user, hostname, email

4. **SentinelOne** ⚠️ REST API (no safe SDK)
   - Build custom wrapper
   - Categories: ASSET, DETECTION, INCIDENT
   - Query Types: ip, hostname, hash

## Benefits of This Approach

✅ **No Core Engine Modification**
- Add new vendors without touching contextualization_engine.py
- Each plugin is self-contained

✅ **Standardized Data Flow**
- All plugins output ContextResult format
- Contextualization extracts entities consistently

✅ **Easy Testing**
- Test plugins independently
- Mock vendor APIs for unit tests

✅ **Automatic Entity Extraction**
- Plugin formats data → standardized fields
- Contextualization extracts → entities
- No manual mapping needed

✅ **Infinite Scalability**
- 1 file per vendor
- Register in 2 lines of code
- Deploy with environment variables

## Troubleshooting

### Plugin not initializing
```bash
# Check if credentials are loaded
docker-compose exec ingestion_engine env | grep YOUR_VENDOR

# Check logs
docker-compose logs ingestion_engine | grep -i "your_vendor\|error"
```

### No results returned
```bash
# Add debug logging to plugin
self.logger.info(f"Query response: {response}")

# Check if query reaches plugin
docker-compose logs ingestion_engine | grep "Querying your_vendor"
```

### JSON serialization errors
```python
# Ensure ContextResult uses standard types
data={
    'field': str(value),           # Convert to string
    'timestamp': dt.isoformat(),   # Convert datetime
    'list_field': list(items)      # Convert to list
}
```

## Summary

**You've successfully completed:**
- ✅ CrowdStrike plugin using FalconPy SDK
- ✅ End-to-end data flow working
- ✅ Plugin architecture proven

**To add a new vendor:**
1. Check SDK availability (30 min)
2. Create `<vendor>_context_plugin.py` (1-2 hours)
3. Register plugin (5 min)
4. Add environment variables (5 min)
5. Test (30 min)

**Total time per vendor: ~2-3 hours**

## Files Created/Modified

- ✅ `engines/ingestion/context_source_plugin.py` - Base plugin class
- ✅ `engines/ingestion/crowdstrike_context_plugin.py` - CrowdStrike implementation
- ✅ `engines/ingestion/ingestion_engine.py` - Plugin registration
- ✅ `engines/ingestion/message_handler.py` - Added pull_context channel
- ✅ `docker-compose.yml` - Added CrowdStrike env vars
- ✅ `.env` - CrowdStrike credentials

**Next up**: Which vendor should we add next? Elastic? SentinelOne? Active Directory?
