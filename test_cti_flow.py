"""
Test script to verify CTI flow from Ingestion to Backend
"""

import asyncio
import json
import redis.asyncio as redis_async
from dotenv import load_dotenv
import os

load_dotenv()


async def test_cti_flow():
    """Test the CTI data flow from Ingestion to Backend"""

    print("Testing CTI Flow: Ingestion Engine -> Backend Engine")
    print("=" * 60)

    # Connect to Redis (force localhost for testing)
    redis_client = await redis_async.Redis(
        host='localhost',  # Force localhost
        port=6380,  # Force port 6380
        decode_responses=True
    )

    # Create a sample CTI indicator (like what OpenCTI would provide)
    sample_indicator = {
        'id': 'test-001',
        'name': 'Test Malware Hash',
        'pattern': "[file:hashes.'SHA-256' = 'abc123def456']",
        'pattern_type': 'stix',
        'confidence': 90,
        'labels': ['malware', 'trojan'],
        'source': 'test'
    }

    # Simulate what Ingestion Engine would publish
    cti_update_message = {
        'source': 'test_cti',
        'timestamp': '2025-01-01T00:00:00',
        'total_items': 1,
        'data': {
            'indicators': [sample_indicator],
            'reports': [],
            'attack_patterns': []
        }
    }

    # Publish to the channel that Backend Engine listens to
    print("\n1. Publishing CTI update to 'ingestion.cti.update' channel...")
    await redis_client.publish(
        'ingestion.cti.update',
        json.dumps(cti_update_message)
    )
    print("   [OK] CTI update published")

    # Also publish individual indicator
    print("\n2. Publishing indicator to 'ingestion.cti.indicators' channel...")
    indicator_message = {
        'source': 'test_cti',
        'timestamp': '2025-01-01T00:00:00',
        'indicator': sample_indicator
    }

    await redis_client.publish(
        'ingestion.cti.indicators',
        json.dumps(indicator_message)
    )
    print("   [OK] Indicator published")

    print("\n3. Backend Engine should now:")
    print("   - Receive these messages")
    print("   - Generate detection rules")
    print("   - Store rules in database")
    print("   - Update performance metrics")

    print("\n4. Check Backend Engine logs to verify processing:")
    print("   docker-compose logs backend_engine --tail=20")

    print("\n5. Expected log entries:")
    print("   - 'Generated X rules from test_cti CTI update'")
    print("   - 'Generated X rules from test_cti indicator'")

    await redis_client.close()
    print("\n[OK] Test complete")


if __name__ == "__main__":
    asyncio.run(test_cti_flow())
