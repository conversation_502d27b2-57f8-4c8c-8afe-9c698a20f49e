"""
Simple Elastic Query Test
Query Elastic directly and show what data we have
"""

import os
import json
import psycopg2
from elasticsearch import Elasticsearch
from dotenv import load_dotenv
from datetime import datetime, timedelta

load_dotenv()

# Configuration
ELASTIC_CLOUD_ID = os.getenv('ELASTIC_CLOUD_ID')
ELASTIC_API_KEY = os.getenv('ELASTIC_API_KEY')

DB_CONFIG = {
    'host': 'localhost',
    'port': 5433,
    'database': 'siemless_v2',
    'user': 'siemless',
    'password': 'siemless123'
}

print("="*80)
print("ELASTIC QUERY + DATABASE CHECK")
print("="*80)

# Step 1: Connect to Elastic
print("\n[1] Connecting to Elastic Cloud...")
client = Elasticsearch(
    cloud_id=ELASTIC_CLOUD_ID,
    api_key=ELASTIC_API_KEY,
    request_timeout=30
)

if client.ping():
    info = client.info()
    print(f"[OK] Connected to Elasticsearch {info['version']['number']}")
else:
    print("[FAIL] Connection failed")
    exit(1)

# Step 2: Query for recent logs
print("\n[2] Querying for logs from last 24 hours...")

query = {
    "query": {
        "range": {
            "@timestamp": {
                "gte": "now-24h"
            }
        }
    },
    "size": 10,
    "sort": [{"@timestamp": {"order": "desc"}}]
}

try:
    response = client.search(
        index=['logs-*', 'filebeat-*', 'winlogbeat-*'],
        body=query
    )

    total = response['hits']['total']['value']
    hits = response['hits']['hits']

    print(f"[OK] Found {total} total logs in last 24 hours")
    print(f"[OK] Retrieved {len(hits)} sample logs")

    # Analyze the logs
    print("\n[3] Analyzing log content...")

    ips = set()
    hosts = set()
    users = set()
    processes = set()

    for hit in hits:
        source = hit['_source']

        # Extract IPs
        if 'source' in source and isinstance(source['source'], dict):
            if 'ip' in source['source']:
                ips.add(source['source']['ip'])
        if 'destination' in source and isinstance(source['destination'], dict):
            if 'ip' in source['destination']:
                ips.add(source['destination']['ip'])
        if 'host' in source and isinstance(source['host'], dict):
            if 'ip' in source['host']:
                if isinstance(source['host']['ip'], list):
                    ips.update(source['host']['ip'])
                else:
                    ips.add(source['host']['ip'])

        # Extract hostnames
        if 'host' in source and isinstance(source['host'], dict):
            if 'name' in source['host']:
                hosts.add(source['host']['name'])
            if 'hostname' in source['host']:
                hosts.add(source['host']['hostname'])

        # Extract users
        if 'user' in source and isinstance(source['user'], dict):
            if 'name' in source['user']:
                users.add(source['user']['name'])

        # Extract processes
        if 'process' in source and isinstance(source['process'], dict):
            if 'name' in source['process']:
                processes.add(source['process']['name'])

    print(f"\n[Entities Found in Logs]")
    print(f"  IPs: {len(ips)}")
    if ips:
        for ip in list(ips)[:5]:
            print(f"    - {ip}")
        if len(ips) > 5:
            print(f"    ... and {len(ips) - 5} more")

    print(f"\n  Hosts: {len(hosts)}")
    if hosts:
        for host in list(hosts)[:5]:
            print(f"    - {host}")
        if len(hosts) > 5:
            print(f"    ... and {len(hosts) - 5} more")

    print(f"\n  Users: {len(users)}")
    if users:
        for user in list(users)[:5]:
            print(f"    - {user}")

    print(f"\n  Processes: {len(processes)}")
    if processes:
        for proc in list(processes)[:5]:
            print(f"    - {proc}")

    # Show sample log
    if hits:
        print(f"\n[Sample Log Structure]")
        sample = hits[0]['_source']
        print(f"  Index: {hits[0]['_index']}")
        print(f"  Timestamp: {sample.get('@timestamp')}")
        print(f"  Top-level keys: {list(sample.keys())[:15]}")

except Exception as e:
    print(f"[FAIL] Query error: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Step 4: Check database
print("\n" + "="*80)
print("[4] Checking SIEMLess database...")

try:
    conn = psycopg2.connect(**DB_CONFIG)
    cursor = conn.cursor()

    # Count entities
    cursor.execute("SELECT COUNT(*) FROM entities")
    entity_count = cursor.fetchone()[0]

    # Count relationships
    cursor.execute("SELECT COUNT(*) FROM relationships")
    relationship_count = cursor.fetchone()[0]

    print(f"[OK] Database connected")
    print(f"\n[Current Database State]")
    print(f"  Total Entities: {entity_count}")
    print(f"  Total Relationships: {relationship_count}")

    # Show entity breakdown
    cursor.execute("""
        SELECT entity_type, COUNT(*) as count
        FROM entities
        GROUP BY entity_type
        ORDER BY count DESC
    """)

    print(f"\n[Entity Types]")
    for row in cursor.fetchall():
        entity_type, count = row
        print(f"  {entity_type}: {count}")

    # Show recent entities
    cursor.execute("""
        SELECT entity_type, entity_value, confidence, first_seen
        FROM entities
        ORDER BY first_seen DESC
        LIMIT 10
    """)

    print(f"\n[Most Recent Entities]")
    for row in cursor.fetchall():
        entity_type, value, confidence, first_seen = row
        print(f"  {first_seen.strftime('%Y-%m-%d %H:%M:%S')} - {entity_type}: {value} (conf: {confidence:.2f})")

    # Check if any of the Elastic IPs/hosts are in database
    print(f"\n[Checking if Elastic entities exist in database...]")

    entities_found = 0
    if ips:
        test_ip = list(ips)[0]
        cursor.execute("SELECT COUNT(*) FROM entities WHERE entity_value = %s", (test_ip,))
        if cursor.fetchone()[0] > 0:
            print(f"  [OK] Found IP {test_ip} in database")
            entities_found += 1

    if hosts:
        test_host = list(hosts)[0]
        cursor.execute("SELECT COUNT(*) FROM entities WHERE entity_value = %s", (test_host,))
        if cursor.fetchone()[0] > 0:
            print(f"  [OK] Found host {test_host} in database")
            entities_found += 1

    if entities_found == 0:
        print(f"  [INFO] No matching entities from Elastic found in database")
        print(f"  [INFO] This is expected if contextualization hasn't processed these logs yet")

    cursor.close()
    conn.close()

except Exception as e:
    print(f"[FAIL] Database error: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "="*80)
print("TEST COMPLETE")
print("="*80)
print("\n[Summary]")
print(f"  - Elastic is connected and has {total} logs from last 24h")
print(f"  - Found {len(ips)} IPs, {len(hosts)} hosts, {len(users)} users")
print(f"  - Database has {entity_count} entities and {relationship_count} relationships")
print(f"\nNext step: Send these logs to contextualization engine for entity extraction")
