#!/usr/bin/env python3
"""
Full migration of backend_engine.py from psycopg2 to asyncpg

This script handles all cursor patterns:
1. INSERT/UPDATE/DELETE with execute -> await conn.execute()
2. SELECT with fetchone() -> await conn.fetchrow()
3. SELECT with fetchall() -> await conn.fetch()
4. Converts %s to $1, $2, $3... placeholders
"""

import re

def count_params_in_tuple(text):
    """Count parameters in a tuple - handles multi-line tuples"""
    # Remove whitespace and newlines
    cleaned = re.sub(r'\s+', ' ', text)
    # Find the tuple content between outermost parentheses
    match = re.search(r'\(\s*(.+)\s*\)', cleaned)
    if not match:
        return 0
    content = match.group(1)
    # Count commas at depth 0 (not inside nested parentheses)
    depth = 0
    count = 1  # Start at 1 since "a, b, c" has 2 commas but 3 items
    for char in content:
        if char == '(':
            depth += 1
        elif char == ')':
            depth -= 1
        elif char == ',' and depth == 0:
            count += 1
    return count

def convert_placeholders(sql, num_params):
    """Convert %s placeholders to $1, $2, $3..."""
    result = sql
    for i in range(1, num_params + 1):
        result = result.replace('%s', f'${i}', 1)
    return result

def extract_indent(line):
    """Get the indentation of a line"""
    return len(line) - len(line.lstrip())

def find_cursor_block_end(lines, start_idx):
    """Find where a cursor block ends (cursor.close() or end of try block)"""
    indent = extract_indent(lines[start_idx])
    for i in range(start_idx + 1, len(lines)):
        line = lines[i].strip()
        current_indent = extract_indent(lines[i])

        # Found cursor.close()
        if 'cursor.close()' in line:
            return i, 'close'

        # Found except or finally (end of try block)
        if current_indent <= indent and line.startswith(('except ', 'finally:')):
            return i - 1, 'implicit'

        # Found another cursor = (nested, end previous)
        if 'cursor = self.db_connection.cursor()' in line:
            return i - 1, 'implicit'

        # End of function (dedent to function level)
        if current_indent < indent and line and not line.startswith('#'):
            return i - 1, 'implicit'

    return len(lines) - 1, 'eof'

def convert_cursor_block(lines, cursor_line_idx):
    """Convert a single cursor block to asyncpg"""
    base_indent = extract_indent(lines[cursor_line_idx])
    end_idx, end_type = find_cursor_block_end(lines, cursor_line_idx)

    # Collect all the lines in this cursor block
    block_lines = lines[cursor_line_idx + 1:end_idx + (0 if end_type == 'close' else 1)]

    # Find cursor.execute() calls
    execute_blocks = []
    i = 0
    while i < len(block_lines):
        line = block_lines[i]
        if 'cursor.execute(' in line:
            # Start of execute block
            exec_start = i
            # Find the complete execute statement (may span multiple lines)
            paren_depth = line.count('(') - line.count(')')
            exec_lines = [line]
            i += 1
            while i < len(block_lines) and paren_depth > 0:
                exec_lines.append(block_lines[i])
                paren_depth += block_lines[i].count('(') - block_lines[i].count(')')
                i += 1

            # Determine if there's a fetch operation after this
            fetch_type = None
            fetch_var = None
            for j in range(i, min(i + 5, len(block_lines))):
                if 'cursor.fetchone()' in block_lines[j]:
                    fetch_type = 'fetchrow'
                    # Extract variable name: "var = cursor.fetchone()"
                    match = re.search(r'(\w+)\s*=\s*cursor\.fetchone\(\)', block_lines[j])
                    if match:
                        fetch_var = match.group(1)
                    break
                elif 'cursor.fetchall()' in block_lines[j]:
                    fetch_type = 'fetch'
                    match = re.search(r'(\w+)\s*=\s*cursor\.fetchall\(\)', block_lines[j])
                    if match:
                        fetch_var = match.group(1)
                    break
                elif 'for row in cursor.fetchall()' in block_lines[j]:
                    fetch_type = 'fetch_iter'
                    break

            execute_blocks.append({
                'start': exec_start,
                'lines': exec_lines,
                'fetch_type': fetch_type,
                'fetch_var': fetch_var
            })
        else:
            i += 1

    # Now generate the asyncpg version
    new_lines = []
    new_lines.append(' ' * base_indent + 'async with self.db_pool.acquire() as conn:')

    # Process the block
    processed_lines = set()  # Track which lines we've already processed
    for i, line in enumerate(block_lines):
        if i in processed_lines:
            continue

        # Check if this line is part of an execute block
        is_execute_line = False
        current_exec = None
        for exec_block in execute_blocks:
            if exec_block['start'] <= i < exec_block['start'] + len(exec_block['lines']):
                is_execute_line = True
                current_exec = exec_block
                break

        if is_execute_line and i == current_exec['start']:
            # This is the start of an execute block - convert it
            exec_text = ''.join(current_exec['lines'])

            # Extract the SQL and parameters
            # Pattern: cursor.execute("""SQL""", (params))
            # or: cursor.execute("SQL", params)
            match = re.search(r'cursor\.execute\(\s*("""|"|\')(.+?)\1\s*,\s*(.+?)\s*\)\)', exec_text, re.DOTALL)
            if not match:
                # Try without parameters
                match = re.search(r'cursor\.execute\(\s*("""|"|\')(.+?)\1\s*\)', exec_text, re.DOTALL)
                if match:
                    sql = match.group(2)
                    params_text = None
                else:
                    # Can't parse, keep original
                    new_lines.extend([' ' * (base_indent + 4) + l for l in current_exec['lines']])
                    for j in range(len(current_exec['lines'])):
                        processed_lines.add(current_exec['start'] + j)
                    continue
            else:
                sql = match.group(2)
                params_text = match.group(3)

            # Count parameters and convert placeholders
            if params_text:
                num_params = count_params_in_tuple(params_text)
                sql_converted = convert_placeholders(sql, num_params)
                # Remove tuple parentheses from params
                params_cleaned = re.sub(r'^\(\s*|\s*\)$', '', params_text.strip())
            else:
                sql_converted = sql
                params_cleaned = ''

            # Generate asyncpg code based on fetch type
            if current_exec['fetch_type'] == 'fetchrow':
                new_lines.append(' ' * (base_indent + 4) + f'{current_exec["fetch_var"]} = await conn.fetchrow("""')
                new_lines.append(' ' * (base_indent + 8) + sql_converted)
                if params_cleaned:
                    new_lines.append(' ' * (base_indent + 4) + f'""", {params_cleaned})')
                else:
                    new_lines.append(' ' * (base_indent + 4) + '""")')
                # Mark fetch line as processed
                for j in range(i + len(current_exec['lines']), min(i + len(current_exec['lines']) + 5, len(block_lines))):
                    if 'cursor.fetchone()' in block_lines[j]:
                        processed_lines.add(j)
                        break
            elif current_exec['fetch_type'] == 'fetch':
                new_lines.append(' ' * (base_indent + 4) + f'{current_exec["fetch_var"]} = await conn.fetch("""')
                new_lines.append(' ' * (base_indent + 8) + sql_converted)
                if params_cleaned:
                    new_lines.append(' ' * (base_indent + 4) + f'""", {params_cleaned})')
                else:
                    new_lines.append(' ' * (base_indent + 4) + '""")')
                # Mark fetch line as processed
                for j in range(i + len(current_exec['lines']), min(i + len(current_exec['lines']) + 5, len(block_lines))):
                    if 'cursor.fetchall()' in block_lines[j]:
                        processed_lines.add(j)
                        break
            elif current_exec['fetch_type'] == 'fetch_iter':
                new_lines.append(' ' * (base_indent + 4) + 'rows = await conn.fetch("""')
                new_lines.append(' ' * (base_indent + 8) + sql_converted)
                if params_cleaned:
                    new_lines.append(' ' * (base_indent + 4) + f'""", {params_cleaned})')
                else:
                    new_lines.append(' ' * (base_indent + 4) + '""")')
                # Find and update the for loop
                for j in range(i + len(current_exec['lines']), min(i + len(current_exec['lines']) + 5, len(block_lines))):
                    if 'for row in cursor.fetchall()' in block_lines[j]:
                        block_lines[j] = block_lines[j].replace('cursor.fetchall()', 'rows')
                        break
            else:
                # Just execute (INSERT/UPDATE/DELETE)
                new_lines.append(' ' * (base_indent + 4) + 'await conn.execute("""')
                new_lines.append(' ' * (base_indent + 8) + sql_converted)
                if params_cleaned:
                    new_lines.append(' ' * (base_indent + 4) + f'""", {params_cleaned})')
                else:
                    new_lines.append(' ' * (base_indent + 4) + '""")')

            # Mark all execute lines as processed
            for j in range(len(current_exec['lines'])):
                processed_lines.add(current_exec['start'] + j)

        elif 'cursor.close()' not in line and 'cursor.fetchone()' not in line and 'cursor.fetchall()' not in line:
            # Keep other lines (but increase indent)
            if line.strip():
                new_lines.append(' ' * (base_indent + 4) + line.strip())
            else:
                new_lines.append('')

    return new_lines

def migrate_file(input_path, output_path):
    """Migrate entire file"""
    with open(input_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # Remove trailing newlines for processing
    lines = [line.rstrip('\n') for line in lines]

    new_lines = []
    i = 0
    conversions = 0

    while i < len(lines):
        line = lines[i]

        if 'cursor = self.db_connection.cursor()' in line:
            # Convert this cursor block
            converted = convert_cursor_block(lines, i)
            new_lines.extend(converted)

            # Skip to end of cursor block
            end_idx, _ = find_cursor_block_end(lines, i)
            i = end_idx + 1
            conversions += 1
        else:
            new_lines.append(line)
            i += 1

    # Write output
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))

    print(f"Converted {conversions} cursor blocks")
    print(f"Output written to: {output_path}")

if __name__ == '__main__':
    input_file = r'c:\Users\<USER>\Documents\siemless_v2\engines\backend\backend_engine.py.converted'
    output_file = r'c:\Users\<USER>\Documents\siemless_v2\engines\backend\backend_engine.py.migrated'

    migrate_file(input_file, output_file)
