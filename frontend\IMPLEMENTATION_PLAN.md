# Frontend Implementation Plan - Pattern Library Showcase

## Tech Stack Decision
- **Framework**: Next.js 14 (App Router)
- **Styling**: Tailwind CSS + Shadcn/ui
- **Data Viz**: Recharts + D3.js
- **State**: <PERSON>ustand (simple, lightweight)
- **API**: REST initially, GraphQL later

## MVP Components (Week 1-2)

### 1. Pattern Browser
```typescript
// Core features needed:
- Pattern search/filter
- Pattern cards with metrics
- Performance visualization
- Cost savings display
```

### 2. CTI Dashboard
```typescript
// Show OTX integration value:
- Active threat feeds
- IoC indicators
- Rule generation status
- SIEM deployment status
```

### 3. Cost Analytics
```typescript
// Demonstrate ROI:
- AI usage costs
- Pattern reuse savings
- Cost per 1000 logs
- Comparison to traditional SIEM
```

## API Requirements from Backend

### Pattern Endpoints
```
GET /api/patterns - List all patterns
GET /api/patterns/{id} - Pattern details
GET /api/patterns/{id}/performance - Performance metrics
POST /api/patterns/{id}/test - Test pattern
```

### CTI Endpoints
```
GET /api/cti/feeds - Active CTI feeds
GET /api/cti/indicators - Recent IoCs
GET /api/cti/rules - Generated rules
```

### Metrics Endpoints
```
GET /api/metrics/costs - Cost tracking
GET /api/metrics/performance - System performance
GET /api/metrics/patterns - Pattern statistics
```

## Directory Structure
```
frontend/
├── app/
│   ├── layout.tsx
│   ├── page.tsx
│   ├── patterns/
│   │   ├── page.tsx       # Pattern library
│   │   └── [id]/page.tsx  # Pattern details
│   ├── cti/
│   │   └── page.tsx       # CTI dashboard
│   └── analytics/
│       └── page.tsx       # Cost analytics
├── components/
│   ├── patterns/
│   │   ├── PatternCard.tsx
│   │   ├── PatternList.tsx
│   │   └── PatternPerformance.tsx
│   ├── cti/
│   │   ├── ThreatFeed.tsx
│   │   └── RuleGenerator.tsx
│   └── shared/
│       ├── Navigation.tsx
│       └── MetricCard.tsx
├── lib/
│   ├── api.ts            # API client
│   └── utils.ts          # Utilities
└── styles/
    └── globals.css       # Tailwind imports
```

## Quick Start Commands
```bash
# Create Next.js project
npx create-next-app@latest frontend --typescript --tailwind --app

# Install dependencies
cd frontend
npm install @radix-ui/themes recharts zustand axios
npm install -D @types/node

# Install shadcn/ui
npx shadcn-ui@latest init
npx shadcn-ui@latest add card button table
```

## Integration with Existing Stack
1. Frontend runs on port 3000 (already allocated in docker-compose)
2. Proxies API calls to backend engines
3. WebSocket connection to Redis for real-time updates
4. Grafana embeds for detailed metrics