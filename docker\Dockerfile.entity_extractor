# Entity Extraction Engine Dockerfile
FROM siemless-v2-base:latest

# Copy Entity Extraction Engine specific code
COPY engines/entity_extractor/ /app/engines/entity_extractor/



# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD redis-cli -h $REDIS_HOST ping || exit 1

# Set the command to run Entity Extraction Engine
CMD ["python", "engines/entity_extractor/entity_extractor.py"]
