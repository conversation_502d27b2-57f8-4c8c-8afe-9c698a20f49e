#!/usr/bin/env python3
"""
CTI Integration Module for SIEMLess v2.0 Ingestion Engine

Adapts v1 Detection Engine CTI capabilities for v2 architecture.
Handles ingestion from multiple threat intelligence sources.
"""

import asyncio
import json
import logging
import requests
import feedparser
import re
from typing import Dict, List, Any, Optional, Generator
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
from enum import Enum
import time
import hashlib

try:
    from pycti import OpenCTIApiClient
    PYCTI_AVAILABLE = True
except ImportError:
    PYCTI_AVAILABLE = False
    OpenCTIApiClient = None


class CTISourceType(Enum):
    """Supported CTI source types for v2."""
    OPENCTI = "opencti"
    OTX = "otx"
    MISP = "misp"
    RSS_FEED = "rss"
    STIX_TAXII = "stix"
    API = "api"
    MANUAL = "manual"


@dataclass
class CTISource:
    """CTI source configuration for v2 architecture."""
    source_id: str
    name: str
    source_type: CTISourceType
    endpoint: str
    credentials: Optional[Dict[str, str]] = None
    update_interval: int = 3600  # seconds
    is_active: bool = True
    last_sync: Optional[datetime] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class CTIEntry:
    """Raw CTI entry from source for v2 processing."""
    entry_id: str
    source_id: str
    title: str
    description: str
    published: datetime
    raw_content: str
    metadata: Dict[str, Any]
    iocs: List[Dict[str, Any]] = None
    processed: bool = False

    def __post_init__(self):
        if self.iocs is None:
            self.iocs = []


@dataclass
class CTIIntelligence:
    """Structured CTI intelligence for rule generation."""
    threat_name: str
    description: str
    tactics: List[str]
    techniques: List[str]
    iocs: List[Dict[str, Any]]
    severity: str
    confidence: float
    source: str
    published: datetime
    raw_data: Dict[str, Any]


class CTIProcessor:
    """
    CTI Processing Engine for v2 Ingestion

    Handles CTI feed ingestion and conversion to enrichment data
    for use by Context and Ruleset engines.
    """

    def __init__(self):
        self.logger = logging.getLogger('ingestion.cti')
        self.sources = []
        self.cache = {}
        self.last_sync_times = {}

    def register_source(self, source: CTISource) -> None:
        """Register a new CTI source."""
        self.logger.info(f"Registering CTI source: {source.name} ({source.source_type.value})")
        self.sources.append(source)

    async def sync_all_sources(self, hours_back: int = 24) -> Dict[str, Any]:
        """Sync all registered CTI sources."""
        results = {
            'total_entries': 0,
            'sources_synced': 0,
            'errors': [],
            'sync_time': time.time()
        }

        for source in self.sources:
            if not source.is_active:
                continue

            try:
                self.logger.info(f"Syncing CTI source: {source.name}")
                entries = await self._sync_source(source, hours_back)
                results['total_entries'] += len(entries)
                results['sources_synced'] += 1

                # Store in cache for other engines to use
                self.cache[source.source_id] = {
                    'entries': entries,
                    'last_sync': datetime.utcnow(),
                    'source': source
                }

                source.last_sync = datetime.utcnow()
                self.logger.info(f"Synced {len(entries)} entries from {source.name}")

            except Exception as e:
                error_msg = f"Failed to sync {source.name}: {str(e)}"
                self.logger.error(error_msg)
                results['errors'].append(error_msg)

        return results

    async def _sync_source(self, source: CTISource, hours_back: int) -> List[CTIEntry]:
        """Sync a specific CTI source."""
        if source.source_type == CTISourceType.OPENCTI:
            return await self._sync_opencti(source, hours_back)
        elif source.source_type == CTISourceType.OTX:
            return await self._sync_otx(source, hours_back)
        elif source.source_type == CTISourceType.RSS_FEED:
            return await self._sync_rss(source, hours_back)
        elif source.source_type == CTISourceType.API:
            return await self._sync_api(source, hours_back)
        else:
            self.logger.warning(f"Unsupported source type: {source.source_type}")
            return []

    async def _sync_opencti(self, source: CTISource, hours_back: int) -> List[CTIEntry]:
        """Sync OpenCTI source."""
        if not PYCTI_AVAILABLE:
            self.logger.warning("pycti not available, falling back to GraphQL")
            return await self._sync_opencti_graphql(source, hours_back)

        try:
            api_client = OpenCTIApiClient(
                url=source.endpoint,
                token=source.credentials.get('api_token')
            )

            # Get recent indicators
            since_date = datetime.utcnow() - timedelta(hours=hours_back)
            indicators = api_client.indicator.list(
                first=100,
                after=since_date.isoformat()
            )

            entries = []
            for indicator in indicators:
                entry = CTIEntry(
                    entry_id=f"opencti_{indicator.get('id', 'unknown')}",
                    source_id=source.source_id,
                    title=indicator.get('name', 'Unknown Indicator'),
                    description=indicator.get('description', ''),
                    published=datetime.utcnow(),
                    raw_content=json.dumps(indicator),
                    metadata={
                        'indicator_type': indicator.get('type'),
                        'confidence': indicator.get('confidence', 50),
                        'labels': indicator.get('labels', [])
                    },
                    iocs=self._extract_iocs_from_opencti(indicator)
                )
                entries.append(entry)

            return entries

        except Exception as e:
            self.logger.error(f"OpenCTI sync failed: {e}")
            return []

    async def _sync_opencti_graphql(self, source: CTISource, hours_back: int) -> List[CTIEntry]:
        """Fallback GraphQL sync for OpenCTI."""
        try:
            headers = {
                'Authorization': f"Bearer {source.credentials.get('api_token')}",
                'Content-Type': 'application/json'
            }

            query = """
            query GetIndicators($first: Int, $after: String) {
                indicators(first: $first, after: $after) {
                    edges {
                        node {
                            id
                            entity_type
                            value
                            labels
                            confidence
                            created
                            description
                        }
                    }
                }
            }
            """

            response = requests.post(
                urljoin(source.endpoint, '/graphql'),
                headers=headers,
                json={'query': query, 'variables': {'first': 100}},
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                indicators = data.get('data', {}).get('indicators', {}).get('edges', [])

                entries = []
                for edge in indicators:
                    node = edge['node']
                    entry = CTIEntry(
                        entry_id=f"opencti_gql_{node.get('id')}",
                        source_id=source.source_id,
                        title=f"OpenCTI Indicator: {node.get('value', 'Unknown')}",
                        description=node.get('description', ''),
                        published=datetime.utcnow(),
                        raw_content=json.dumps(node),
                        metadata={
                            'entity_type': node.get('entity_type'),
                            'confidence': node.get('confidence', 50),
                            'labels': node.get('labels', [])
                        },
                        iocs=[{
                            'type': node.get('entity_type', 'unknown'),
                            'value': node.get('value', ''),
                            'confidence': node.get('confidence', 50)
                        }]
                    )
                    entries.append(entry)

                return entries
            else:
                self.logger.error(f"OpenCTI GraphQL query failed: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"OpenCTI GraphQL sync failed: {e}")
            return []

    async def _sync_otx(self, source: CTISource, hours_back: int) -> List[CTIEntry]:
        """Sync AlienVault OTX source."""
        try:
            headers = {
                'X-OTX-API-KEY': source.credentials.get('api_key')
            }

            # Get recent pulses
            url = urljoin(source.endpoint, f'/api/v1/pulses/subscribed?limit=50')
            response = requests.get(url, headers=headers, timeout=30)

            if response.status_code == 200:
                data = response.json()
                pulses = data.get('results', [])

                entries = []
                for pulse in pulses:
                    entry = CTIEntry(
                        entry_id=f"otx_{pulse.get('id')}",
                        source_id=source.source_id,
                        title=pulse.get('name', 'Unknown Pulse'),
                        description=pulse.get('description', ''),
                        published=datetime.fromisoformat(pulse.get('created', '').replace('Z', '+00:00')),
                        raw_content=json.dumps(pulse),
                        metadata={
                            'author_name': pulse.get('author_name'),
                            'pulse_source': pulse.get('pulse_source'),
                            'tags': pulse.get('tags', []),
                            'references': pulse.get('references', [])
                        },
                        iocs=self._extract_iocs_from_otx(pulse)
                    )
                    entries.append(entry)

                return entries
            else:
                self.logger.error(f"OTX API request failed: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"OTX sync failed: {e}")
            return []

    async def _sync_rss(self, source: CTISource, hours_back: int) -> List[CTIEntry]:
        """Sync RSS/Atom feed source."""
        try:
            feed = feedparser.parse(source.endpoint)
            entries = []

            for item in feed.entries[:50]:  # Limit to recent 50 items
                published = datetime.utcnow()
                if hasattr(item, 'published_parsed') and item.published_parsed:
                    published = datetime(*item.published_parsed[:6])

                # Skip old entries
                if (datetime.utcnow() - published).total_seconds() > hours_back * 3600:
                    continue

                entry = CTIEntry(
                    entry_id=f"rss_{hashlib.md5(item.link.encode()).hexdigest()}",
                    source_id=source.source_id,
                    title=item.get('title', 'Unknown RSS Item'),
                    description=item.get('summary', ''),
                    published=published,
                    raw_content=item.get('description', ''),
                    metadata={
                        'link': item.get('link'),
                        'author': item.get('author'),
                        'categories': [tag.term for tag in getattr(item, 'tags', [])]
                    },
                    iocs=self._extract_iocs_from_text(item.get('description', ''))
                )
                entries.append(entry)

            return entries

        except Exception as e:
            self.logger.error(f"RSS sync failed: {e}")
            return []

    async def _sync_api(self, source: CTISource, hours_back: int) -> List[CTIEntry]:
        """Sync generic API source."""
        try:
            headers = {}
            if source.credentials:
                if 'api_key' in source.credentials:
                    headers['X-API-Key'] = source.credentials['api_key']
                elif 'authorization' in source.credentials:
                    headers['Authorization'] = source.credentials['authorization']

            response = requests.get(source.endpoint, headers=headers, timeout=30)

            if response.status_code == 200:
                data = response.json()

                # Handle different API response formats
                items = data
                if isinstance(data, dict):
                    items = data.get('data', data.get('results', data.get('items', [data])))

                entries = []
                for item in items[:50]:  # Limit to 50 items
                    entry = CTIEntry(
                        entry_id=f"api_{hashlib.md5(json.dumps(item, sort_keys=True).encode()).hexdigest()}",
                        source_id=source.source_id,
                        title=item.get('title', item.get('name', 'Unknown API Item')),
                        description=item.get('description', item.get('summary', '')),
                        published=datetime.utcnow(),
                        raw_content=json.dumps(item),
                        metadata=item,
                        iocs=self._extract_iocs_from_dict(item)
                    )
                    entries.append(entry)

                return entries
            else:
                self.logger.error(f"API request failed: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"API sync failed: {e}")
            return []

    def _extract_iocs_from_opencti(self, indicator: Dict) -> List[Dict[str, Any]]:
        """Extract IOCs from OpenCTI indicator."""
        iocs = []

        if 'value' in indicator:
            ioc_type = self._determine_ioc_type(indicator.get('value', ''))
            iocs.append({
                'type': ioc_type,
                'value': indicator['value'],
                'confidence': indicator.get('confidence', 50),
                'source': 'opencti'
            })

        return iocs

    def _extract_iocs_from_otx(self, pulse: Dict) -> List[Dict[str, Any]]:
        """Extract IOCs from OTX pulse."""
        iocs = []

        for indicator in pulse.get('indicators', []):
            iocs.append({
                'type': indicator.get('type', 'unknown'),
                'value': indicator.get('indicator', ''),
                'confidence': 75,  # Default confidence for OTX
                'source': 'otx'
            })

        return iocs

    def _extract_iocs_from_text(self, text: str) -> List[Dict[str, Any]]:
        """Extract IOCs from text using regex patterns."""
        iocs = []

        # IP addresses
        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
        for match in re.finditer(ip_pattern, text):
            iocs.append({
                'type': 'ip',
                'value': match.group(),
                'confidence': 60,
                'source': 'text_extraction'
            })

        # Domain names
        domain_pattern = r'\b[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\b'
        for match in re.finditer(domain_pattern, text):
            domain = match.group()
            if '.' in domain and len(domain) > 4:
                iocs.append({
                    'type': 'domain',
                    'value': domain,
                    'confidence': 50,
                    'source': 'text_extraction'
                })

        # SHA256 hashes
        sha256_pattern = r'\b[a-fA-F0-9]{64}\b'
        for match in re.finditer(sha256_pattern, text):
            iocs.append({
                'type': 'sha256',
                'value': match.group().lower(),
                'confidence': 80,
                'source': 'text_extraction'
            })

        # MD5 hashes
        md5_pattern = r'\b[a-fA-F0-9]{32}\b'
        for match in re.finditer(md5_pattern, text):
            iocs.append({
                'type': 'md5',
                'value': match.group().lower(),
                'confidence': 80,
                'source': 'text_extraction'
            })

        return iocs

    def _extract_iocs_from_dict(self, data: Dict) -> List[Dict[str, Any]]:
        """Extract IOCs from dictionary data."""
        iocs = []

        # Look for common IOC fields
        ioc_fields = {
            'ip': ['ip', 'ip_address', 'source_ip', 'dest_ip'],
            'domain': ['domain', 'hostname', 'fqdn'],
            'url': ['url', 'link', 'href'],
            'hash': ['hash', 'md5', 'sha1', 'sha256'],
            'email': ['email', 'email_address']
        }

        def extract_from_obj(obj, prefix=''):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    full_key = f"{prefix}.{key}" if prefix else key

                    # Check if this field matches IOC patterns
                    for ioc_type, field_names in ioc_fields.items():
                        if any(field_name in key.lower() for field_name in field_names):
                            if isinstance(value, str) and value:
                                detected_type = self._determine_ioc_type(value)
                                if detected_type != 'unknown':
                                    iocs.append({
                                        'type': detected_type,
                                        'value': value,
                                        'confidence': 70,
                                        'source': 'api_extraction',
                                        'field': full_key
                                    })

                    # Recurse into nested objects
                    if isinstance(value, (dict, list)):
                        extract_from_obj(value, full_key)

            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    extract_from_obj(item, f"{prefix}[{i}]")

        extract_from_obj(data)
        return iocs

    def _determine_ioc_type(self, value: str) -> str:
        """Determine IOC type from value."""
        value = value.strip()

        # IP address
        if re.match(r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$', value):
            return 'ip'

        # Domain
        if re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)+$', value):
            return 'domain'

        # URL
        if value.startswith(('http://', 'https://', 'ftp://')):
            return 'url'

        # Email
        if '@' in value and re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', value):
            return 'email'

        # Hashes
        if re.match(r'^[a-fA-F0-9]{32}$', value):
            return 'md5'
        elif re.match(r'^[a-fA-F0-9]{40}$', value):
            return 'sha1'
        elif re.match(r'^[a-fA-F0-9]{64}$', value):
            return 'sha256'

        return 'unknown'

    def get_cached_cti_data(self, source_id: Optional[str] = None) -> Dict[str, Any]:
        """Get cached CTI data for use by other engines."""
        if source_id:
            return self.cache.get(source_id, {})
        return self.cache

    def convert_to_enrichment_data(self, entries: List[CTIEntry]) -> Dict[str, Any]:
        """Convert CTI entries to enrichment data format for Context Engine."""
        enrichment = {
            'threat_intel': {
                'ioc_matches': 0,
                'threat_actor_associations': [],
                'malware_families': [],
                'attack_patterns': [],
                'campaigns': []
            },
            'indicators': [],
            'sources': [],
            'last_updated': datetime.utcnow().isoformat()
        }

        all_iocs = []
        for entry in entries:
            # Collect IOCs
            all_iocs.extend(entry.iocs)

            # Extract threat actor info
            if 'threat_actor' in entry.title.lower() or 'apt' in entry.title.lower():
                enrichment['threat_intel']['threat_actor_associations'].append(entry.title)

            # Extract malware family info
            if any(keyword in entry.description.lower() for keyword in ['malware', 'trojan', 'ransomware']):
                enrichment['threat_intel']['malware_families'].append(entry.title)

            # Add source info
            enrichment['sources'].append({
                'source_id': entry.source_id,
                'title': entry.title,
                'published': entry.published.isoformat()
            })

        # Process indicators
        enrichment['indicators'] = all_iocs
        enrichment['threat_intel']['ioc_matches'] = len(all_iocs)

        return enrichment

    async def setup_default_sources(self) -> None:
        """Setup default CTI sources if environment variables are available."""
        import os

        # OpenCTI
        opencti_url = os.environ.get('OPENCTI_URL')
        opencti_token = os.environ.get('OPENCTI_TOKEN')
        if opencti_url and opencti_token:
            opencti_source = CTISource(
                source_id='opencti_default',
                name='OpenCTI Default Instance',
                source_type=CTISourceType.OPENCTI,
                endpoint=opencti_url,
                credentials={'api_token': opencti_token}
            )
            self.register_source(opencti_source)

        # OTX
        otx_key = os.environ.get('OTX_API_KEY')
        if otx_key:
            otx_source = CTISource(
                source_id='otx_default',
                name='AlienVault OTX',
                source_type=CTISourceType.OTX,
                endpoint='https://otx.alienvault.com',
                credentials={'api_key': otx_key}
            )
            self.register_source(otx_source)

        self.logger.info(f"Setup {len(self.sources)} default CTI sources")