#!/usr/bin/env python3
"""
SIEMLess v2.0 - Engine Testing Script
Test functionality of the dockerized engines
"""

import asyncio
import json
import time
import redis
import psycopg2
from psycopg2.extras import RealDictCursor
import requests
import sys

class EngineTest:
    def __init__(self):
        self.redis_client = None
        self.db_connection = None

    async def setup_connections(self):
        """Setup Redis and database connections"""
        try:
            # Redis connection
            self.redis_client = redis.Redis(
                host='localhost',
                port=6380,
                decode_responses=True,
                socket_connect_timeout=5
            )
            self.redis_client.ping()
            print("[+] Redis connection successful")

            # Database connection
            self.db_connection = psycopg2.connect(
                host='localhost',
                port=5433,
                database='siemless_v3',
                user='siemless',
                password='secure_password_123',
                cursor_factory=RealDictCursor
            )
            print("[+] Database connection successful")

        except Exception as e:
            print(f"[X] Connection setup failed: {e}")
            return False

        return True

    async def test_database_schema(self):
        """Test database schema and initial data"""
        try:
            cursor = self.db_connection.cursor()

            # Check if tables exist
            tables = [
                'engine_coordination',
                'workflow_instances',
                'intelligence_consensus_results',
                'pattern_library',
                'detection_rules',
                'crystallized_patterns'
            ]

            for table in tables:
                cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.tables
                    WHERE table_name = %s
                """, (table,))
                count = cursor.fetchone()[0]
                if count > 0:
                    print(f"[+] Table '{table}' exists")
                else:
                    print(f"[X] Table '{table}' missing")

            # Check initial engine data
            cursor.execute("SELECT engine_id, status FROM engine_coordination")
            engines = cursor.fetchall()
            print(f"[INFO] Engine coordination entries: {len(engines)}")
            for engine in engines:
                print(f"   - {engine['engine_id']}: {engine['status']}")

            # Check pattern library
            cursor.execute("SELECT COUNT(*) FROM pattern_library")
            pattern_count = cursor.fetchone()[0]
            print(f"[INFO] Pattern library entries: {pattern_count}")

            return True

        except Exception as e:
            print(f"[X] Database schema test failed: {e}")
            return False

    async def test_redis_messaging(self):
        """Test Redis message queue functionality"""
        try:
            # Test basic Redis operations
            test_key = "test:connection"
            test_value = "test_value"

            self.redis_client.set(test_key, test_value, ex=10)
            retrieved = self.redis_client.get(test_key)

            if retrieved == test_value:
                print("[+] Redis read/write operations working")
            else:
                print("[X] Redis read/write test failed")
                return False

            # Test pub/sub functionality
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe('test_channel')

            # Publish a test message
            self.redis_client.publish('test_channel', json.dumps({
                'test': True,
                'timestamp': time.time()
            }))

            # Try to receive the message
            message = pubsub.get_message(timeout=2)
            if message and message['type'] == 'message':
                print("[+] Redis pub/sub working")
            else:
                print("[WARN]  Redis pub/sub test inconclusive")

            pubsub.close()
            return True

        except Exception as e:
            print(f"[X] Redis messaging test failed: {e}")
            return False

    async def test_engine_heartbeats(self):
        """Test if engines are sending heartbeats"""
        try:
            # Subscribe to heartbeat channels
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe('heartbeat.intelligence', 'heartbeat.backend')

            print("[WAIT] Waiting for engine heartbeats (30 seconds)...")
            start_time = time.time()
            heartbeats_received = []

            while time.time() - start_time < 30:
                message = pubsub.get_message(timeout=1)
                if message and message['type'] == 'message':
                    channel = message['channel']
                    try:
                        data = json.loads(message['data'])
                        engine_name = data.get('engine')
                        status = data.get('status')

                        if engine_name not in heartbeats_received:
                            heartbeats_received.append(engine_name)
                            print(f"[BEAT] Heartbeat received from {engine_name} (status: {status})")
                    except:
                        pass

            pubsub.close()

            if heartbeats_received:
                print(f"[+] Received heartbeats from: {', '.join(heartbeats_received)}")
                return True
            else:
                print("[X] No heartbeats received from engines")
                return False

        except Exception as e:
            print(f"[X] Heartbeat test failed: {e}")
            return False

    async def test_intelligence_engine(self):
        """Test Intelligence Engine functionality"""
        try:
            print("[AI] Testing Intelligence Engine...")

            # Send a consensus request
            test_message = {
                'pattern_data': {'test': 'pattern'},
                'complexity': 'simple',
                'request_id': 'test_request_123'
            }

            self.redis_client.publish('intelligence.consensus', json.dumps(test_message))
            print("[SEND] Sent consensus request to Intelligence Engine")

            # Listen for response
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe('intelligence.consensus_result')

            response_received = False
            start_time = time.time()

            while time.time() - start_time < 10 and not response_received:
                message = pubsub.get_message(timeout=1)
                if message and message['type'] == 'message':
                    try:
                        data = json.loads(message['data'])
                        if data.get('data', {}).get('source_request') == 'test_request_123':
                            print("[+] Intelligence Engine responded to consensus request")
                            response_received = True
                    except:
                        pass

            pubsub.close()

            if not response_received:
                print("[WARN]  No response from Intelligence Engine (may still be initializing)")

            return True

        except Exception as e:
            print(f"[X] Intelligence Engine test failed: {e}")
            return False

    async def test_backend_engine(self):
        """Test Backend Engine functionality"""
        try:
            print("[DATA] Testing Backend Engine...")

            # Send a data storage request
            test_message = {
                'type': 'test_data',
                'content': {'test': 'storage_data', 'timestamp': time.time()},
                'retention': 'warm'
            }

            self.redis_client.publish('backend.store_data', json.dumps(test_message))
            print("[SEND] Sent storage request to Backend Engine")

            # Listen for response
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe('backend.data_stored')

            response_received = False
            start_time = time.time()

            while time.time() - start_time < 10 and not response_received:
                message = pubsub.get_message(timeout=1)
                if message and message['type'] == 'message':
                    try:
                        data = json.loads(message['data'])
                        if data.get('data', {}).get('tier') == 'warm':
                            print("[+] Backend Engine responded to storage request")
                            response_received = True
                    except:
                        pass

            pubsub.close()

            if not response_received:
                print("[WARN]  No response from Backend Engine (may still be initializing)")

            return True

        except Exception as e:
            print(f"[X] Backend Engine test failed: {e}")
            return False

    async def test_engine_coordination(self):
        """Test engine coordination table updates"""
        try:
            cursor = self.db_connection.cursor()

            # Check if engines are updating their status
            cursor.execute("""
                SELECT engine_id, status, last_heartbeat,
                       EXTRACT(EPOCH FROM (NOW() - last_heartbeat)) as seconds_ago
                FROM engine_coordination
            """)

            engines = cursor.fetchall()
            active_engines = 0

            print("[INFO] Engine Coordination Status:")
            for engine in engines:
                seconds_ago = engine['seconds_ago'] or float('inf')
                status_indicator = "[OK]" if seconds_ago < 60 else "[SLOW]" if seconds_ago < 300 else "[FAIL]"

                print(f"   {status_indicator} {engine['engine_id']}: {engine['status']} "
                      f"(last seen: {seconds_ago:.0f}s ago)")

                if seconds_ago < 300:  # Active within 5 minutes
                    active_engines += 1

            print(f"[+] {active_engines} engines appear to be active")
            return active_engines > 0

        except Exception as e:
            print(f"[X] Engine coordination test failed: {e}")
            return False

    async def run_all_tests(self):
        """Run all tests"""
        print("=> Starting SIEMLess v2.0 Engine Tests")
        print("=" * 50)

        # Setup connections
        if not await self.setup_connections():
            print("[X] Failed to setup connections")
            return False

        tests = [
            ("Database Schema", self.test_database_schema),
            ("Redis Messaging", self.test_redis_messaging),
            ("Engine Coordination", self.test_engine_coordination),
            ("Engine Heartbeats", self.test_engine_heartbeats),
            ("Intelligence Engine", self.test_intelligence_engine),
            ("Backend Engine", self.test_backend_engine)
        ]

        results = []
        for test_name, test_func in tests:
            print(f"\n[TEST] Running: {test_name}")
            print("-" * 30)
            try:
                result = await test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"[X] Test '{test_name}' failed with exception: {e}")
                results.append((test_name, False))

        # Summary
        print("\n" + "=" * 50)
        print("[FINISH] Test Results Summary")
        print("=" * 50)

        passed = 0
        total = len(results)

        for test_name, result in results:
            status = "[+] PASS" if result else "[X] FAIL"
            print(f"{status} {test_name}")
            if result:
                passed += 1

        print(f"\n[INFO] Overall: {passed}/{total} tests passed")

        if passed == total:
            print("[SUCCESS] All tests passed! SIEMLess v2.0 engines are working correctly.")
        elif passed > total // 2:
            print("[WARN]  Most tests passed. Some engines may still be initializing.")
        else:
            print("[X] Multiple tests failed. Check engine logs for issues.")

        return passed == total

    def cleanup(self):
        """Cleanup connections"""
        if self.redis_client:
            self.redis_client.close()
        if self.db_connection:
            self.db_connection.close()

async def main():
    tester = EngineTest()
    try:
        success = await tester.run_all_tests()
        sys.exit(0 if success else 1)
    finally:
        tester.cleanup()

if __name__ == "__main__":
    asyncio.run(main())