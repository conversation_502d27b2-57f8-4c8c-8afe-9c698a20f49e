"""
GitHub Pattern Synchronization Module
Ingests patterns, parsers, and query templates from GitHub repositories
"""

import json
import asyncio
import aiohttp
import re
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import hashlib
import yaml
from pathlib import Path
import logging

class GitHubPatternSync:
    """Synchronize patterns from GitHub repositories"""

    def __init__(self, redis_client, postgres_conn, logger):
        self.redis = redis_client
        self.db = postgres_conn
        self.logger = logger

        # GitHub API configuration
        self.github_api_base = "https://api.github.com"
        self.headers = {
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "SIEMLess-Pattern-Sync/2.0"
        }

        # Pattern validation regex
        self.pattern_validators = {
            'regex': self._validate_regex_pattern,
            'entity_extractors': self._validate_entity_extractors,
            'query_template': self._validate_query_template
        }

        # Supported pattern file extensions
        self.pattern_extensions = ['.json', '.yaml', '.yml']

    async def initialize(self, github_token: Optional[str] = None):
        """Initialize GitHub sync with optional authentication"""
        if github_token:
            self.headers["Authorization"] = f"token {github_token}"
            self.logger.info("GitHub sync initialized with authentication")
        else:
            self.logger.warning("GitHub sync initialized without auth (rate limited)")

        # Create tables if not exists
        await self._ensure_schema()

    async def _ensure_schema(self):
        """Ensure required database schema exists"""
        try:
            cursor = self.db.cursor()

            # Pattern versions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS pattern_versions (
                    id SERIAL PRIMARY KEY,
                    pattern_id VARCHAR(255) NOT NULL,
                    version VARCHAR(50) NOT NULL,
                    content JSONB NOT NULL,
                    source_repo VARCHAR(500),
                    source_file VARCHAR(500),
                    commit_sha VARCHAR(40),
                    validated BOOLEAN DEFAULT FALSE,
                    validation_errors TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(pattern_id, version)
                )
            """)

            # GitHub sources table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS github_sources (
                    id SERIAL PRIMARY KEY,
                    repo_url VARCHAR(500) UNIQUE NOT NULL,
                    branch VARCHAR(100) DEFAULT 'main',
                    last_sync TIMESTAMP,
                    last_commit_sha VARCHAR(40),
                    patterns_count INTEGER DEFAULT 0,
                    active BOOLEAN DEFAULT TRUE,
                    sync_interval INTEGER DEFAULT 3600,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Pattern deployment history
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS pattern_deployments (
                    id SERIAL PRIMARY KEY,
                    pattern_id VARCHAR(255) NOT NULL,
                    version VARCHAR(50) NOT NULL,
                    deployed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    deployed_by VARCHAR(255) DEFAULT 'github_sync',
                    status VARCHAR(50) DEFAULT 'active',
                    rollback_version VARCHAR(50),
                    performance_metrics JSONB
                )
            """)

            self.db.commit()
            self.logger.info("GitHub sync schema initialized")

        except Exception as e:
            self.logger.error(f"Failed to create schema: {e}")
            self.db.rollback()
            raise

    async def add_repository(self, repo_url: str, branch: str = "main",
                            sync_interval: int = 3600) -> bool:
        """Add a GitHub repository as a pattern source"""
        try:
            # Parse repo URL
            repo_parts = self._parse_repo_url(repo_url)
            if not repo_parts:
                return False

            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO github_sources (repo_url, branch, sync_interval)
                VALUES (%s, %s, %s)
                ON CONFLICT (repo_url)
                DO UPDATE SET branch = %s, sync_interval = %s, active = TRUE
            """, (repo_url, branch, sync_interval, branch, sync_interval))

            self.db.commit()
            self.logger.info(f"Added GitHub repository: {repo_url}")

            # Trigger initial sync
            await self.sync_repository(repo_url)
            return True

        except Exception as e:
            self.logger.error(f"Failed to add repository: {e}")
            self.db.rollback()
            return False

    async def sync_repository(self, repo_url: str) -> Dict[str, Any]:
        """Sync patterns from a specific repository"""
        sync_result = {
            'repo': repo_url,
            'status': 'started',
            'patterns_found': 0,
            'patterns_validated': 0,
            'patterns_deployed': 0,
            'errors': []
        }

        try:
            # Get repo info from database
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT branch, last_commit_sha
                FROM github_sources
                WHERE repo_url = %s AND active = TRUE
            """, (repo_url,))

            row = cursor.fetchone()
            if not row:
                sync_result['status'] = 'error'
                sync_result['errors'].append('Repository not found or inactive')
                return sync_result

            branch, last_commit_sha = row

            # Parse repository URL
            repo_parts = self._parse_repo_url(repo_url)
            owner, repo = repo_parts['owner'], repo_parts['repo']

            # Get latest commit
            latest_commit = await self._get_latest_commit(owner, repo, branch)
            if not latest_commit:
                sync_result['status'] = 'error'
                sync_result['errors'].append('Failed to fetch latest commit')
                return sync_result

            # Check if update needed
            if latest_commit['sha'] == last_commit_sha:
                sync_result['status'] = 'up_to_date'
                self.logger.info(f"Repository {repo_url} is up to date")
                return sync_result

            # Fetch pattern files
            pattern_files = await self._fetch_pattern_files(owner, repo, branch)
            sync_result['patterns_found'] = len(pattern_files)

            # Process each pattern file
            for file_path in pattern_files:
                try:
                    content = await self._fetch_file_content(owner, repo, file_path, branch)
                    if content:
                        pattern = self._parse_pattern_content(content, file_path)
                        if pattern:
                            # Validate pattern
                            is_valid, errors = await self._validate_pattern(pattern)
                            if is_valid:
                                sync_result['patterns_validated'] += 1
                                # Deploy pattern
                                if await self._deploy_pattern(pattern, repo_url, file_path,
                                                             latest_commit['sha']):
                                    sync_result['patterns_deployed'] += 1
                            else:
                                sync_result['errors'].extend(errors)

                except Exception as e:
                    sync_result['errors'].append(f"Error processing {file_path}: {str(e)}")

            # Update last sync info
            cursor.execute("""
                UPDATE github_sources
                SET last_sync = NOW(),
                    last_commit_sha = %s,
                    patterns_count = %s
                WHERE repo_url = %s
            """, (latest_commit['sha'], sync_result['patterns_deployed'], repo_url))

            self.db.commit()
            sync_result['status'] = 'completed'

            # Publish sync completion event
            self.redis.publish('ingestion.github_sync_completed', json.dumps(sync_result))

        except Exception as e:
            sync_result['status'] = 'error'
            sync_result['errors'].append(str(e))
            self.logger.error(f"Repository sync failed: {e}")

        return sync_result

    async def sync_all_repositories(self) -> List[Dict[str, Any]]:
        """Sync all active repositories"""
        results = []

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT repo_url
                FROM github_sources
                WHERE active = TRUE
                AND (last_sync IS NULL OR last_sync < NOW() - INTERVAL '1 hour' * sync_interval / 3600)
            """)

            repos = cursor.fetchall()
            self.logger.info(f"Syncing {len(repos)} repositories")

            for (repo_url,) in repos:
                result = await self.sync_repository(repo_url)
                results.append(result)

        except Exception as e:
            self.logger.error(f"Failed to sync repositories: {e}")

        return results

    async def _get_latest_commit(self, owner: str, repo: str, branch: str) -> Optional[Dict]:
        """Get the latest commit SHA for a branch"""
        try:
            url = f"{self.github_api_base}/repos/{owner}/{repo}/commits/{branch}"
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            'sha': data['sha'],
                            'message': data['commit']['message'],
                            'author': data['commit']['author']['name'],
                            'date': data['commit']['author']['date']
                        }
        except Exception as e:
            self.logger.error(f"Failed to get latest commit: {e}")
        return None

    async def _fetch_pattern_files(self, owner: str, repo: str, branch: str) -> List[str]:
        """Fetch list of pattern files from repository"""
        pattern_files = []

        try:
            # Common pattern directories
            pattern_dirs = ['patterns', 'parsers', 'rules', 'queries', '.']

            for dir_path in pattern_dirs:
                url = f"{self.github_api_base}/repos/{owner}/{repo}/contents/{dir_path}"
                params = {'ref': branch}

                async with aiohttp.ClientSession() as session:
                    async with session.get(url, headers=self.headers, params=params) as response:
                        if response.status == 200:
                            contents = await response.json()
                            for item in contents:
                                if item['type'] == 'file':
                                    # Check if it's a pattern file
                                    if any(item['name'].endswith(ext) for ext in self.pattern_extensions):
                                        pattern_files.append(item['path'])
                                elif item['type'] == 'dir' and item['name'] in ['patterns', 'parsers']:
                                    # Recursively fetch from subdirectories
                                    subfiles = await self._fetch_directory_contents(
                                        owner, repo, item['path'], branch
                                    )
                                    pattern_files.extend(subfiles)

        except Exception as e:
            self.logger.error(f"Failed to fetch pattern files: {e}")

        return pattern_files

    async def _fetch_directory_contents(self, owner: str, repo: str,
                                       path: str, branch: str) -> List[str]:
        """Recursively fetch pattern files from a directory"""
        files = []

        try:
            url = f"{self.github_api_base}/repos/{owner}/{repo}/contents/{path}"
            params = {'ref': branch}

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers, params=params) as response:
                    if response.status == 200:
                        contents = await response.json()
                        for item in contents:
                            if item['type'] == 'file':
                                if any(item['name'].endswith(ext) for ext in self.pattern_extensions):
                                    files.append(item['path'])

        except Exception as e:
            self.logger.error(f"Failed to fetch directory contents: {e}")

        return files

    async def _fetch_file_content(self, owner: str, repo: str,
                                 file_path: str, branch: str) -> Optional[str]:
        """Fetch raw content of a file from GitHub"""
        try:
            url = f"https://raw.githubusercontent.com/{owner}/{repo}/{branch}/{file_path}"

            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        return await response.text()

        except Exception as e:
            self.logger.error(f"Failed to fetch file content: {e}")

        return None

    def _parse_pattern_content(self, content: str, file_path: str) -> Optional[Dict]:
        """Parse pattern content from JSON or YAML"""
        try:
            if file_path.endswith('.json'):
                return json.loads(content)
            elif file_path.endswith(('.yaml', '.yml')):
                return yaml.safe_load(content)
        except Exception as e:
            self.logger.error(f"Failed to parse pattern file {file_path}: {e}")
        return None

    async def _validate_pattern(self, pattern: Dict) -> tuple[bool, List[str]]:
        """Validate a pattern before deployment"""
        errors = []

        # Check required fields
        required_fields = ['pattern_id', 'pattern_type']
        for field in required_fields:
            if field not in pattern:
                errors.append(f"Missing required field: {field}")

        # Validate pattern type
        valid_types = ['log_parser', 'entity_extractor', 'query_template',
                      'detection_rule', 'enrichment_rule']
        if pattern.get('pattern_type') not in valid_types:
            errors.append(f"Invalid pattern type: {pattern.get('pattern_type')}")

        # Validate pattern data based on type
        pattern_type = pattern.get('pattern_type')
        pattern_data = pattern.get('pattern_data', {})

        if pattern_type == 'log_parser':
            # Validate regex
            if 'regex' in pattern_data:
                is_valid, error = self._validate_regex_pattern(pattern_data['regex'])
                if not is_valid:
                    errors.append(f"Invalid regex: {error}")

        elif pattern_type == 'entity_extractor':
            # Validate entity extractors
            if 'entity_extractors' in pattern_data:
                is_valid, error = self._validate_entity_extractors(
                    pattern_data['entity_extractors']
                )
                if not is_valid:
                    errors.append(f"Invalid entity extractors: {error}")

        elif pattern_type == 'query_template':
            # Validate query template
            is_valid, error = self._validate_query_template(pattern_data)
            if not is_valid:
                errors.append(f"Invalid query template: {error}")

        return (len(errors) == 0, errors)

    def _validate_regex_pattern(self, regex: str) -> tuple[bool, Optional[str]]:
        """Validate a regex pattern"""
        try:
            re.compile(regex)
            return (True, None)
        except re.error as e:
            return (False, str(e))

    def _validate_entity_extractors(self, extractors: Dict) -> tuple[bool, Optional[str]]:
        """Validate entity extractor configuration"""
        valid_entity_types = ['ip_fields', 'host_fields', 'user_fields',
                            'mac_fields', 'port_fields', 'hash_fields',
                            'file_fields', 'process_fields', 'url_fields']

        for entity_type, fields in extractors.items():
            if entity_type not in valid_entity_types:
                return (False, f"Invalid entity type: {entity_type}")
            if not isinstance(fields, list):
                return (False, f"Entity fields must be a list for {entity_type}")

        return (True, None)

    def _validate_query_template(self, template: Dict) -> tuple[bool, Optional[str]]:
        """Validate a query template"""
        required_fields = ['query_type', 'supported_siems']

        for field in required_fields:
            if field not in template:
                return (False, f"Missing required field: {field}")

        valid_query_types = ['search', 'aggregate', 'timeline',
                           'correlation', 'threat_hunt', 'statistical']
        if template['query_type'] not in valid_query_types:
            return (False, f"Invalid query type: {template['query_type']}")

        return (True, None)

    async def _deploy_pattern(self, pattern: Dict, repo_url: str,
                             file_path: str, commit_sha: str) -> bool:
        """Deploy a validated pattern to the pattern library"""
        try:
            pattern_id = pattern['pattern_id']
            version = pattern.get('version', self._generate_version(pattern))

            cursor = self.db.cursor()

            # Store pattern version
            cursor.execute("""
                INSERT INTO pattern_versions
                (pattern_id, version, content, source_repo, source_file, commit_sha, validated)
                VALUES (%s, %s, %s, %s, %s, %s, TRUE)
                ON CONFLICT (pattern_id, version) DO UPDATE
                SET content = %s, source_file = %s, commit_sha = %s
            """, (
                pattern_id, version, json.dumps(pattern),
                repo_url, file_path, commit_sha,
                json.dumps(pattern), file_path, commit_sha
            ))

            # Update main pattern library
            cursor.execute("""
                INSERT INTO pattern_library
                (pattern_id, pattern_type, pattern_data, source, is_active)
                VALUES (%s, %s, %s, %s, TRUE)
                ON CONFLICT (pattern_id) DO UPDATE
                SET pattern_data = %s, updated_at = NOW()
            """, (
                pattern_id, pattern['pattern_type'],
                json.dumps(pattern.get('pattern_data', {})),
                f"github:{repo_url}",
                json.dumps(pattern.get('pattern_data', {}))
            ))

            # Record deployment
            cursor.execute("""
                INSERT INTO pattern_deployments
                (pattern_id, version, deployed_by, status)
                VALUES (%s, %s, 'github_sync', 'active')
            """, (pattern_id, version))

            self.db.commit()

            # Publish pattern update event
            self.redis.publish('ingestion.pattern_updated', json.dumps({
                'pattern_id': pattern_id,
                'version': version,
                'type': pattern['pattern_type'],
                'source': repo_url
            }))

            self.logger.info(f"Deployed pattern {pattern_id} v{version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to deploy pattern: {e}")
            self.db.rollback()
            return False

    def _generate_version(self, pattern: Dict) -> str:
        """Generate a version string for a pattern"""
        # Create version from content hash
        content_hash = hashlib.md5(
            json.dumps(pattern, sort_keys=True).encode()
        ).hexdigest()[:8]

        timestamp = datetime.now().strftime("%Y%m%d")
        return f"{timestamp}-{content_hash}"

    def _parse_repo_url(self, repo_url: str) -> Optional[Dict]:
        """Parse GitHub repository URL"""
        # Support both HTTPS and SSH URLs
        patterns = [
            r'https://github.com/([^/]+)/([^/]+?)(?:\.git)?$',
            r'**************:([^/]+)/([^/]+?)(?:\.git)?$',
            r'github.com/([^/]+)/([^/]+?)(?:\.git)?$'
        ]

        for pattern in patterns:
            match = re.match(pattern, repo_url)
            if match:
                return {
                    'owner': match.group(1),
                    'repo': match.group(2)
                }

        self.logger.error(f"Invalid repository URL: {repo_url}")
        return None

    async def rollback_pattern(self, pattern_id: str, target_version: str) -> bool:
        """Rollback a pattern to a specific version"""
        try:
            cursor = self.db.cursor()

            # Get the target version content
            cursor.execute("""
                SELECT content FROM pattern_versions
                WHERE pattern_id = %s AND version = %s
            """, (pattern_id, target_version))

            row = cursor.fetchone()
            if not row:
                self.logger.error(f"Version {target_version} not found for pattern {pattern_id}")
                return False

            pattern_content = row[0]

            # Update pattern library with rollback version
            cursor.execute("""
                UPDATE pattern_library
                SET pattern_data = %s, updated_at = NOW()
                WHERE pattern_id = %s
            """, (json.dumps(pattern_content.get('pattern_data', {})), pattern_id))

            # Record rollback in deployments
            cursor.execute("""
                INSERT INTO pattern_deployments
                (pattern_id, version, deployed_by, status)
                VALUES (%s, %s, 'rollback', 'active')
            """, (pattern_id, target_version))

            self.db.commit()

            # Publish rollback event
            self.redis.publish('ingestion.pattern_rollback', json.dumps({
                'pattern_id': pattern_id,
                'version': target_version
            }))

            self.logger.info(f"Rolled back pattern {pattern_id} to version {target_version}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to rollback pattern: {e}")
            self.db.rollback()
            return False

    async def get_pattern_performance(self, pattern_id: str) -> Dict[str, Any]:
        """Get performance metrics for a pattern"""
        try:
            cursor = self.db.cursor()

            # Get deployment history
            cursor.execute("""
                SELECT version, deployed_at, status, performance_metrics
                FROM pattern_deployments
                WHERE pattern_id = %s
                ORDER BY deployed_at DESC
                LIMIT 10
            """, (pattern_id,))

            deployments = []
            for row in cursor.fetchall():
                deployments.append({
                    'version': row[0],
                    'deployed_at': row[1].isoformat() if row[1] else None,
                    'status': row[2],
                    'metrics': row[3] or {}
                })

            # Get usage stats from pattern library
            cursor.execute("""
                SELECT usage_count, success_rate, avg_processing_time
                FROM pattern_library
                WHERE pattern_id = %s
            """, (pattern_id,))

            row = cursor.fetchone()
            usage_stats = {
                'usage_count': row[0] if row else 0,
                'success_rate': row[1] if row else 0,
                'avg_processing_time': row[2] if row else 0
            }

            return {
                'pattern_id': pattern_id,
                'deployments': deployments,
                'usage_stats': usage_stats
            }

        except Exception as e:
            self.logger.error(f"Failed to get pattern performance: {e}")
            return {}