/**
 * SIEMLess v2.0 - API Type Definitions
 * Auto-generated from COMPLETE_API_REFERENCE.md and CODEBASE_INDEX.md
 *
 * IMPORTANT: These types match the actual backend API responses
 * Do NOT modify manually - regenerate from API documentation
 */

// ============================================================================
// ENUMS & CONSTANTS
// ============================================================================

export type EntityType = 'ip' | 'user' | 'host' | 'process' | 'file_hash' | 'domain' | 'email' | 'port' | 'mac_address'

export type AlertSeverity = 'critical' | 'high' | 'medium' | 'low' | 'info'

export type AlertStatus = 'open' | 'in_progress' | 'closed' | 'false_positive'

export type CaseStatus = 'open' | 'investigating' | 'resolved' | 'closed' | 'false_positive'

export type CasePriority = 'critical' | 'high' | 'medium' | 'low'

export type QualityTier = 'PLATINUM' | 'GOLD' | 'SILVER' | 'BRONZE' | 'MINIMAL'

export type LogSourceType = 'firewall' | 'edr' | 'proxy' | 'dns' | 'auth' | 'cloud' | 'email' | 'dlp'

export type CTISource = 'otx' | 'threatfox' | 'crowdstrike' | 'opencti'

export type CTIPluginStatus = 'active' | 'error' | 'updating' | 'disabled'

export type CorrelationType = 'simple' | 'correlation' | 'sequence' | 'aggregation' | 'behavioral'

export type WorkflowStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'

export type RelationshipType = 'connected_to' | 'executed' | 'created' | 'accessed' | 'communicated_with' | 'spawned' | 'modified'

// ============================================================================
// ENTITY & ENRICHMENT TYPES
// ============================================================================

export interface Entity {
  entity_id: string
  entity_type: EntityType
  entity_value: string
  first_seen: string  // ISO 8601 timestamp
  last_seen: string   // ISO 8601 timestamp
  occurrence_count: number
  risk_score: number  // 0-100

  // Three-layer enrichment
  enrichments: {
    // Layer 1: Business & Technical Context
    geolocation?: GeolocationData
    network_info?: NetworkInfo
    asset_info?: AssetInfo
    user_info?: UserInfo

    // Layer 2: CTI Threat Intelligence
    cti_threat_intelligence?: CTIMatch

    // Layer 3: Vendor-specific Context (loaded on-demand)
    vendor_context?: Record<string, VendorContext>
  }

  threat_score: number  // From Layer 2, 0-100
  is_threat: boolean
  tags?: string[]
}

// Layer 1: Geolocation Enrichment
export interface GeolocationData {
  country: string
  country_code: string
  city?: string
  region?: string
  latitude?: number
  longitude?: number
  isp?: string
  asn?: string
  organization?: string
  is_proxy?: boolean
  is_vpn?: boolean
  is_tor?: boolean
  is_datacenter?: boolean
}

// Layer 1: Network Information
export interface NetworkInfo {
  whois_data?: {
    registrar?: string
    registration_date?: string
    expiration_date?: string
    registrant?: string
  }
  dns_records?: {
    a_records?: string[]
    mx_records?: string[]
    txt_records?: string[]
  }
  reverse_dns?: string
  asn?: string
  asn_name?: string
}

// Layer 1: Asset Information
export interface AssetInfo {
  asset_tier: 'critical' | 'standard' | 'low'
  security_zone: 'dmz' | 'internal' | 'external' | 'restricted'
  business_unit?: string
  owner?: string
  criticality_score: number  // 0-100
  compliance_tags?: string[]
}

// Layer 1: User Information
export interface UserInfo {
  display_name?: string
  email?: string
  department?: string
  title?: string
  manager?: string
  account_status: 'active' | 'disabled' | 'locked'
  privilege_level: 'admin' | 'power_user' | 'standard' | 'guest'
  risk_score: number  // 0-100
}

// Layer 2: CTI Match
export interface CTIMatch {
  entity: string
  source: CTISource
  threat_score: number  // 0-100
  confidence: number    // 0-100
  match_type: 'exact' | 'fuzzy' | 'related'
  tags: string[]
  first_seen: string
  last_seen: string
  campaign?: string
  threat_actor?: string
  malware_family?: string
  attack_types?: string[]
  additional_context?: Record<string, any>
}

// Layer 3: Vendor Context (loaded on-demand)
export interface VendorContext {
  vendor: 'crowdstrike' | 'elastic' | 'splunk' | 'sentinel'
  timestamp: string
  event_count: number
  time_range_days: number
  summary: string
  details: Record<string, any>
}

// CrowdStrike-specific context
export interface CrowdStrikeContext extends VendorContext {
  vendor: 'crowdstrike'
  details: {
    host_info?: {
      hostname: string
      platform: string
      os_version: string
      agent_version: string
      last_seen: string
      status: 'normal' | 'contained' | 'suspicious'
    }
    detections?: Array<{
      detection_id: string
      severity: AlertSeverity
      tactic: string
      technique: string
      description: string
      timestamp: string
    }>
    containment_status?: {
      is_contained: boolean
      contained_at?: string
      contained_by?: string
    }
  }
}

// Elastic-specific context
export interface ElasticContext extends VendorContext {
  vendor: 'elastic'
  details: {
    event_aggregations?: Record<string, number>
    top_event_types?: Array<{ event_type: string; count: number }>
    anomalies?: Array<{
      field: string
      anomaly_score: number
      description: string
    }>
    related_indices?: string[]
  }
}

// ============================================================================
// RELATIONSHIP & TIMELINE TYPES
// ============================================================================

export interface Relationship {
  relationship_id: string
  source_entity_id: string
  target_entity_id: string
  source_entity: EntityReference
  target_entity: EntityReference
  relationship_type: RelationshipType
  weight: number  // Strength of relationship, 0-1
  first_seen: string
  last_seen: string
  event_count: number
  bidirectional: boolean
  properties?: Record<string, any>
}

export interface EntityReference {
  entity_id: string
  entity_type: EntityType
  entity_value: string
}

export interface TimelineEvent {
  event_id: string
  timestamp: string
  event_type: string
  description: string
  source: string  // Which log source
  severity: AlertSeverity
  entities: EntityReference[]
  raw_data?: Record<string, any>
  mitre_tactics?: string[]
  mitre_techniques?: string[]
}

// ============================================================================
// ALERT & CASE TYPES
// ============================================================================

export interface Alert {
  alert_id: string
  title: string
  description: string
  severity: AlertSeverity
  status: AlertStatus
  source: string
  created_at: string
  updated_at: string
  assigned_to?: string
  priority_score: number  // AI-calculated priority, 0-100

  // MITRE ATT&CK mapping
  mitre_tactics: string[]
  mitre_techniques: string[]

  // Entities involved
  entities: EntityReference[]

  // Case assignment
  case_id?: string

  // Additional context
  confidence: number  // Detection confidence, 0-100
  false_positive_likelihood?: number  // 0-100
  tags?: string[]
}

export interface AlertDetail extends Alert {
  enriched_entities: Entity[]
  related_alerts: Alert[]
  investigation_guide?: InvestigationGuide
  raw_data: Record<string, any>
  timeline?: TimelineEvent[]
}

export interface Case {
  case_id: string
  title: string
  description: string
  severity: AlertSeverity
  priority: CasePriority
  status: CaseStatus
  created_at: string
  updated_at: string
  closed_at?: string
  created_by: string
  assigned_to?: string

  // Related data
  alert_ids: string[]
  entity_ids: string[]
  evidence_count: number

  // Investigation
  investigation_status?: string
  findings?: string
  resolution?: string

  // Metadata
  tags?: string[]
  mitre_tactics?: string[]
  mitre_techniques?: string[]
}

export interface CaseDetail extends Case {
  alerts: Alert[]
  entities: Entity[]
  evidence: Evidence[]
  timeline: TimelineEvent[]
  investigation_guide?: InvestigationGuide
  activity_log: ActivityLogEntry[]
}

export interface Evidence {
  evidence_id: string
  case_id: string
  evidence_type: 'log' | 'screenshot' | 'file' | 'network_capture' | 'memory_dump' | 'note'
  description: string
  added_at: string
  added_by: string
  file_path?: string
  file_hash?: string
  metadata?: Record<string, any>
}

export interface ActivityLogEntry {
  log_id: string
  case_id: string
  timestamp: string
  user: string
  action: string
  details: string
}

// ============================================================================
// INVESTIGATION TYPES
// ============================================================================

export interface InvestigationGuide {
  case_id: string
  steps: InvestigationStep[]
  generated_at: string
  ai_model: string
  completed_percentage: number
}

export interface InvestigationStep {
  id: string
  title: string
  description: string
  queries: Record<string, string>  // SIEM name -> query string
  evidence_required: string[]
  completed: boolean
  findings?: string
  completed_at?: string
  completed_by?: string
}

export interface InvestigationContext {
  case_id: string
  entities: Entity[]
  relationships: Relationship[]
  timeline: TimelineEvent[]
  threat_summary: string
  affected_systems: string[]
  patient_zero?: Entity  // Initial compromise entity
  attack_path?: AttackPath
}

export interface AttackPath {
  start_entity: EntityReference
  end_entity: EntityReference
  steps: Array<{
    from_entity: EntityReference
    to_entity: EntityReference
    relationship_type: RelationshipType
    timestamp: string
    description: string
  }>
  total_steps: number
  duration_seconds: number
}

// ============================================================================
// CTI & THREAT INTELLIGENCE TYPES
// ============================================================================

export interface CTIPluginInfo {
  source: CTISource
  status: CTIPluginStatus
  last_update: string
  next_update: string
  total_indicators: number
  new_indicators_today: number
  error_message?: string
  credentials_valid: boolean
}

export interface CTIStats {
  total_indicators: number
  indicators_by_type: Record<string, number>
  indicators_by_source: Record<string, number>
  avg_threat_score: number
  high_confidence_indicators: number
  last_update: string
}

export interface CTIIndicator {
  indicator_id: string
  source: CTISource
  ioc_type: string  // 'ip', 'domain', 'hash', 'url', etc.
  value: string
  threat_score: number  // 0-100
  confidence: number    // 0-100
  tags: string[]
  first_seen: string
  last_seen: string
  campaign?: string
  threat_actor?: string
  malware_family?: string
  description?: string
}

// ============================================================================
// DETECTION FIDELITY TYPES
// ============================================================================

export interface DetectionFidelity {
  overall_confidence: number  // 0-100
  confidence_by_tactic: Record<string, number>
  confidence_by_technique: Record<string, number>
  covered_techniques: number
  total_techniques: number
  coverage_percentage: number

  strengths: string[]  // High-confidence tactics
  weaknesses: string[]  // Low-confidence tactics
  critical_gaps: TechniqueGap[]

  log_sources: LogSourceQuality[]
  calculation_timestamp: string
}

export interface TechniqueGap {
  technique_id: string
  technique_name: string
  tactic: string
  current_confidence: number  // 0-100
  required_sources: string[]  // What log sources would improve this
  impact: 'critical' | 'high' | 'medium' | 'low'
  recommendation: string
}

export interface TechniqueCoverage {
  technique_id: string
  technique_name: string
  tactic: string
  confidence: number  // 0-100
  detection_sources: string[]  // Which log sources detect this
  recent_detections: number
  status: 'covered' | 'partial' | 'gap'
}

// ============================================================================
// LOG SOURCE QUALITY TYPES
// ============================================================================

export interface LogSourceQuality {
  source_id: string
  source_name: string
  source_type: LogSourceType
  quality_tier: QualityTier
  quality_score: number  // 0-10

  metrics: {
    parsing_success_rate: number      // 0-1
    field_extraction_score: number    // 0-10
    entity_extraction_score: number   // 0-10
    timestamp_accuracy: number        // 0-10
    data_completeness: number         // 0-10
  }

  volume: {
    events_per_day: number
    size_per_day_mb: number
    retention_days: number
  }

  value_score: number  // Security value vs cost, 0-10
  recommendations: string[]
  last_assessment: string
}

export interface QualityAnalysis {
  source_id: string
  sample_size: number
  analysis_date: string

  field_coverage: Record<string, number>  // Field name -> extraction percentage
  entity_types_found: EntityType[]
  common_errors: ErrorPattern[]
  improvement_potential: number  // 0-10, how much score could improve
  specific_recommendations: Recommendation[]
}

export interface ErrorPattern {
  error_type: string
  occurrence_count: number
  sample_errors: string[]
  fix_recommendation: string
}

export interface Recommendation {
  category: 'parser' | 'configuration' | 'upgrade' | 'replacement'
  priority: 'critical' | 'high' | 'medium' | 'low'
  description: string
  expected_improvement: number  // Quality score points
  effort_required: 'low' | 'medium' | 'high'
  cost_estimate?: number
}

// ============================================================================
// COVERAGE SIMULATION TYPES
// ============================================================================

export interface AvailableLogSource {
  source_name: string
  vendor: string
  category: LogSourceType
  quality_tier: QualityTier
  estimated_cost_annual: number
  typical_event_volume: string  // e.g., "1-10M events/day"
  techniques_improved: string[]  // MITRE technique IDs
  description: string
}

export interface SimulationScenario {
  scenario_id: string
  name: string
  description?: string
  added_sources: AvailableLogSource[]
  removed_sources: string[]
  total_cost: number
  created_at: string
}

export interface SimulationResult {
  scenario_id: string
  baseline: DetectionFidelity
  simulated: DetectionFidelity
  improvement: {
    overall_confidence: number  // Percentage points gained
    techniques_added: string[]
    gaps_closed: TechniqueGap[]
  }
  cost: {
    annual_cost: number
    cost_per_confidence_point: number
    roi_score: number  // 0-100
  }
}

export interface ComparisonResult {
  scenarios: SimulationScenario[]
  comparison_matrix: {
    scenario_id: string
    overall_confidence: number
    techniques_covered: number
    annual_cost: number
    roi_score: number
  }[]
  recommendation: {
    best_scenario_id: string
    reason: string
  }
}

// ============================================================================
// MITRE ATT&CK TYPES
// ============================================================================

export interface MITREHeatmap {
  tactics: Tactic[]
  time_range: string
  generated_at: string
}

export interface Tactic {
  tactic_id: string
  tactic_name: string
  techniques: TechniqueCell[]
  average_confidence: number  // 0-100
}

export interface TechniqueCell {
  technique_id: string
  technique_name: string
  confidence: number  // 0-100
  detections_30d: number
  status: 'covered' | 'partial' | 'gap'
  detection_sources: string[]
}

export interface TechniqueDetail {
  technique_id: string
  technique_name: string
  tactic: string
  description: string
  detection_confidence: number  // 0-100
  detection_sources: LogSourceQuality[]
  recent_alerts: Alert[]
  recommended_improvements: string[]
  mitre_url: string
}

// ============================================================================
// PATTERN & RULE TYPES
// ============================================================================

export interface Pattern {
  pattern_id: string
  pattern_name: string
  pattern_type: 'security_event' | 'entity_relationship' | 'behavioral_baseline' | 'cti_pattern'
  pattern_data: Record<string, any>
  confidence_score: number  // 0-1
  usage_count: number
  created_at: string
  updated_at: string
  cost_savings: number  // Total cost saved by reuse
  ai_model_used?: string
}

export interface PatternPerformance {
  pattern_id: string
  total_matches: number
  true_positives: number
  false_positives: number
  false_negatives: number
  precision: number  // 0-1
  recall: number     // 0-1
  f1_score: number   // 0-1
  last_updated: string
}

export interface DetectionRule {
  rule_id: string
  rule_name: string
  rule_type: 'sigma' | 'splunk' | 'elastic' | 'sentinel' | 'qradar'
  rule_content: string
  description: string
  severity: AlertSeverity
  mitre_tactics: string[]
  mitre_techniques: string[]
  created_at: string
  updated_at: string
  enabled: boolean
  test_cases?: TestCase[]
}

export interface TestCase {
  test_id: string
  rule_id: string
  test_name: string
  test_data: string
  expected_result: 'match' | 'no_match'
  actual_result?: 'match' | 'no_match'
  passed?: boolean
  executed_at?: string
}

export interface RulePerformance {
  rule_id: string
  total_alerts: number
  true_positives: number
  false_positives: number
  false_positive_rate: number  // 0-1
  avg_triage_time_seconds: number
  last_triggered: string
  performance_score: number  // 0-100
}

// ============================================================================
// CORRELATION TYPES
// ============================================================================

export interface CorrelationRule {
  rule_id: string
  rule_name: string
  rule_type: CorrelationType
  use_case: string
  description: string
  required_sources: LogSourceType[]
  conditions: Array<{
    source: LogSourceType
    field: string
    operator: string
    value: any
  }>
  correlation_logic: Record<string, any>
  time_window: number  // Seconds
  mitre_techniques: string[]
  severity: AlertSeverity
  enabled: boolean
}

export interface CorrelationContext {
  context_id: string
  rule: CorrelationRule
  entity_id: string
  entity_type: EntityType
  events_matched: Array<{
    event_id: string
    source: string
    timestamp: string
  }>
  sources_seen: string[]
  correlation_score: number  // 0-1
  status: 'active' | 'triggered' | 'expired'
  created_at: string
  expires_at: string
}

export interface CorrelationCapability {
  overall_capability: number  // 0-100
  capabilities_by_use_case: Record<string, number>
  available_sources: LogSourceType[]
  missing_sources: LogSourceType[]
  recommendations: string[]
}

// ============================================================================
// WORKFLOW TYPES
// ============================================================================

export interface Workflow {
  workflow_id: string
  workflow_name: string
  workflow_type: string
  description: string
  status: WorkflowStatus
  created_at: string
  started_at?: string
  completed_at?: string
  created_by: string

  steps: WorkflowStep[]
  current_step?: number
  progress_percentage: number

  input_data?: Record<string, any>
  output_data?: Record<string, any>
  error?: string
}

export interface WorkflowStep {
  step_id: string
  step_name: string
  step_type: string
  status: WorkflowStatus
  started_at?: string
  completed_at?: string
  output?: Record<string, any>
  error?: string
}

export interface WorkflowTemplate {
  template_id: string
  template_name: string
  description: string
  steps: Array<{
    step_name: string
    step_type: string
    configuration: Record<string, any>
  }>
  use_case: string
}

export interface WorkflowStatistics {
  total_workflows: number
  workflows_by_status: Record<WorkflowStatus, number>
  avg_execution_time_seconds: number
  success_rate: number  // 0-1
  most_used_templates: Array<{
    template_name: string
    usage_count: number
  }>
}

// ============================================================================
// DASHBOARD & SYSTEM TYPES
// ============================================================================

export interface DashboardStats {
  total_alerts: number
  critical_alerts: number
  open_cases: number
  detection_confidence: number  // 0-100
  coverage_score: number        // 0-100
  entities_tracked: number
  cti_indicators: number
  last_updated: string
}

export interface EngineStatus {
  engine_name: string
  status: 'running' | 'stopped' | 'error'
  port: number
  last_heartbeat: string
  uptime_seconds: number
  performance_metrics: {
    cpu_usage: number     // 0-1
    memory_usage: number  // 0-1
    messages_processed: number
    avg_response_time_ms: number
  }
}

export interface SystemStatus {
  status: 'healthy' | 'degraded' | 'error'
  engines: EngineStatus[]
  redis_status: 'connected' | 'disconnected'
  postgres_status: 'connected' | 'disconnected'
  timestamp: string
}

// ============================================================================
// API RESPONSE WRAPPERS
// ============================================================================

export interface APIResponse<T> {
  success: boolean
  data: T
  message?: string
  timestamp: string
}

export interface PaginatedResponse<T> {
  success: boolean
  data: T[]
  pagination: {
    page: number
    page_size: number
    total_items: number
    total_pages: number
  }
  timestamp: string
}

export interface ErrorResponse {
  success: false
  error: string
  error_code?: string
  details?: Record<string, any>
  timestamp: string
}

// ============================================================================
// REQUEST PAYLOAD TYPES
// ============================================================================

export interface CreateCaseRequest {
  title: string
  description: string
  severity: AlertSeverity
  priority: CasePriority
  alert_ids?: string[]
  assigned_to?: string
  tags?: string[]
}

export interface UpdateCaseRequest {
  title?: string
  description?: string
  status?: CaseStatus
  assigned_to?: string
  findings?: string
  resolution?: string
  tags?: string[]
}

export interface CreateAlertRequest {
  title: string
  description: string
  severity: AlertSeverity
  source: string
  entities: EntityReference[]
  mitre_tactics?: string[]
  mitre_techniques?: string[]
  raw_data?: Record<string, any>
}

export interface UpdateAlertRequest {
  status?: AlertStatus
  assigned_to?: string
  case_id?: string
  tags?: string[]
}

export interface StartWorkflowRequest {
  workflow_type: string
  input_data?: Record<string, any>
  priority?: 'high' | 'medium' | 'low'
}

export interface EntitySearchRequest {
  query: string
  entity_type?: EntityType
  filters?: {
    risk_score_min?: number
    risk_score_max?: number
    first_seen_after?: string
    first_seen_before?: string
    tags?: string[]
  }
  limit?: number
  offset?: number
}

export interface DetectionFidelityRequest {
  log_sources: Array<{
    source_name: string
    source_type: LogSourceType
    quality_tier: QualityTier
  }>
  include_gaps?: boolean
  include_recommendations?: boolean
}

export interface CoverageSimulationRequest {
  scenario_name: string
  add_sources?: AvailableLogSource[]
  remove_sources?: string[]
}

// ============================================================================
// FILTER & QUERY TYPES
// ============================================================================

export interface AlertFilters {
  severity?: AlertSeverity[]
  status?: AlertStatus[]
  source?: string[]
  mitre_tactic?: string[]
  mitre_technique?: string[]
  date_from?: string
  date_to?: string
  assigned_to?: string
  has_case?: boolean
}

export interface CaseFilters {
  status?: CaseStatus[]
  priority?: CasePriority[]
  severity?: AlertSeverity[]
  assigned_to?: string
  created_by?: string
  date_from?: string
  date_to?: string
  tags?: string[]
}

export interface EntityFilters {
  entity_type?: EntityType[]
  risk_score_min?: number
  risk_score_max?: number
  is_threat?: boolean
  has_enrichment?: boolean
  tags?: string[]
}

export interface TimeRange {
  start: string  // ISO 8601
  end: string    // ISO 8601
}

// ============================================================================
// WEBSOCKET MESSAGE TYPES
// ============================================================================

export type WebSocketMessageType =
  | 'alert.new'
  | 'alert.updated'
  | 'case.created'
  | 'case.updated'
  | 'entity.updated'
  | 'detection_fidelity.updated'
  | 'cti.indicator.new'
  | 'workflow.status'
  | 'dashboard.stats'

export interface WebSocketMessage<T = any> {
  type: WebSocketMessageType
  data: T
  timestamp: string
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type RequireAtLeastOne<T, Keys extends keyof T = keyof T> =
  Pick<T, Exclude<keyof T, Keys>> &
  { [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>> }[Keys]

// ============================================================================
// TYPE GUARDS
// ============================================================================

export function isAPIResponse<T>(obj: any): obj is APIResponse<T> {
  return obj && typeof obj === 'object' && 'success' in obj && 'data' in obj
}

export function isPaginatedResponse<T>(obj: any): obj is PaginatedResponse<T> {
  return isAPIResponse(obj) && 'pagination' in obj
}

export function isErrorResponse(obj: any): obj is ErrorResponse {
  return obj && typeof obj === 'object' && obj.success === false && 'error' in obj
}
