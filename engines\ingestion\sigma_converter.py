"""
Sigma Rule Converter for SIEMLess v2.0
Converts detection rules from various SIEM formats to Sigma universal format
Enables true multi-SIEM portability and comparison

Sigma: https://github.com/SigmaHQ/sigma
"""

import json
import yaml
import re
from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass, asdict
import hashlib


@dataclass
class SigmaRule:
    """
    Sigma rule format representation

    Sigma is the universal rule format for SIEM systems
    Similar to YARA for malware, Snort for network - but for SIEM queries
    """
    title: str
    id: str  # UUID
    status: str  # experimental, test, stable
    description: str
    author: str
    date: str  # YYYY-MM-DD
    modified: Optional[str] = None
    tags: List[str] = None
    logsource: Dict[str, str] = None  # product, service, category
    detection: Dict[str, Any] = None  # selection, condition
    falsepositives: List[str] = None
    level: str = 'medium'  # low, medium, high, critical
    references: List[str] = None

    def to_yaml(self) -> str:
        """Convert to YAML format (standard Sigma format)"""
        data = asdict(self)
        # Remove None values
        data = {k: v for k, v in data.items() if v is not None}
        return yaml.dump(data, default_flow_style=False, sort_keys=False)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

    def get_hash(self) -> str:
        """Generate hash for deduplication"""
        # Hash based on detection logic and logsource
        hash_content = json.dumps({
            'detection': self.detection,
            'logsource': self.logsource
        }, sort_keys=True)
        return hashlib.sha256(hash_content.encode()).hexdigest()


class ElasticToSigmaConverter:
    """
    Converts Elastic Security rules to Sigma format

    This enables:
        1. Universal rule comparison (Elastic vs Splunk vs Sentinel)
        2. Deduplication across SIEM platforms
        3. Multi-SIEM deployment from single source
        4. Rule quality assessment
    """

    def __init__(self):
        # Elastic field to Sigma field mapping
        self.field_mapping = {
            # Process
            'process.name': 'Image',
            'process.executable': 'Image',
            'process.command_line': 'CommandLine',
            'process.parent.name': 'ParentImage',
            'process.pid': 'ProcessId',

            # Network
            'source.ip': 'SourceIp',
            'destination.ip': 'DestinationIp',
            'source.port': 'SourcePort',
            'destination.port': 'DestinationPort',
            'network.protocol': 'Protocol',

            # File
            'file.path': 'TargetFilename',
            'file.name': 'TargetFilename',
            'file.hash.sha256': 'Hashes',
            'file.hash.md5': 'Hashes',

            # User
            'user.name': 'User',
            'user.domain': 'Domain',

            # Host
            'host.name': 'ComputerName',
            'host.hostname': 'ComputerName',

            # Event
            'event.code': 'EventID',
            'event.action': 'EventType',

            # Registry
            'registry.path': 'TargetObject',
            'registry.key': 'TargetObject',
            'registry.value': 'Details'
        }

    def convert_elastic_rule(self, elastic_rule: Dict[str, Any]) -> SigmaRule:
        """
        Convert an Elastic Security rule to Sigma format

        Args:
            elastic_rule: Dictionary containing Elastic rule data
                Expected keys: rule_id, name, query, language, severity,
                              mitre_techniques, description, etc.

        Returns:
            SigmaRule object
        """
        # Generate Sigma rule ID from Elastic rule ID
        sigma_id = self._generate_sigma_id(elastic_rule.get('rule_id', ''))

        # Convert severity
        sigma_level = self._convert_severity(elastic_rule.get('severity', 'medium'))

        # Convert detection logic
        detection = self._convert_detection(
            elastic_rule.get('query', ''),
            elastic_rule.get('language', 'kuery'),
            elastic_rule.get('filters', [])
        )

        # Determine logsource from index patterns
        logsource = self._determine_logsource(elastic_rule.get('index_patterns', []))

        # Build Sigma rule
        sigma_rule = SigmaRule(
            title=elastic_rule.get('name', 'Unnamed Rule'),
            id=sigma_id,
            status='stable' if elastic_rule.get('enabled', False) else 'experimental',
            description=elastic_rule.get('description', ''),
            author=elastic_rule.get('created_by', 'SIEMLess Elastic Harvester'),
            date=datetime.utcnow().strftime('%Y-%m-%d'),
            tags=self._convert_tags(elastic_rule),
            logsource=logsource,
            detection=detection,
            falsepositives=elastic_rule.get('false_positives', []),
            level=sigma_level,
            references=[f"Elastic Rule ID: {elastic_rule.get('rule_id', 'unknown')}"]
        )

        return sigma_rule

    def _generate_sigma_id(self, elastic_rule_id: str) -> str:
        """Generate Sigma-compatible UUID from Elastic rule ID"""
        import uuid
        # Create deterministic UUID from rule ID
        namespace = uuid.UUID('6ba7b810-9dad-11d1-80b4-00c04fd430c8')
        return str(uuid.uuid5(namespace, f"elastic_{elastic_rule_id}"))

    def _convert_severity(self, elastic_severity: str) -> str:
        """Convert Elastic severity to Sigma level"""
        mapping = {
            'low': 'low',
            'medium': 'medium',
            'high': 'high',
            'critical': 'critical'
        }
        return mapping.get(elastic_severity.lower(), 'medium')

    def _convert_detection(self, query: str, language: str, filters: List) -> Dict[str, Any]:
        """
        Convert Elastic query to Sigma detection format

        This is the complex part - parsing KQL/EQL/Lucene to Sigma
        """
        detection = {
            'selection': {},
            'condition': 'selection'
        }

        if language == 'kuery' or language == 'lucene':
            # Parse KQL/Lucene query
            detection['selection'] = self._parse_kql_query(query)

        elif language == 'eql':
            # Parse EQL query
            detection['selection'] = self._parse_eql_query(query)

        elif language == 'esql':
            # Parse ES|QL query (newer Elasticsearch query language)
            detection['selection'] = self._parse_esql_query(query)

        # Add filters as additional conditions
        if filters:
            detection = self._add_filters_to_detection(detection, filters)

        return detection

    def _parse_kql_query(self, query: str) -> Dict[str, Any]:
        """
        Parse Kibana Query Language (KQL) to Sigma selection

        Example KQL:
            process.name: "powershell.exe" AND process.args: "-enc"

        Becomes Sigma:
            selection:
                Image: "powershell.exe"
                CommandLine|contains: "-enc"
        """
        selection = {}

        # Simple parser - production would use proper KQL parser
        # Split by AND/OR (simplified)
        conditions = re.split(r'\s+AND\s+|\s+and\s+', query, flags=re.IGNORECASE)

        for condition in conditions:
            condition = condition.strip()

            # Match: field: "value" or field: value
            match = re.match(r'([a-zA-Z._]+)\s*:\s*(.+)', condition)
            if match:
                field = match.group(1)
                value = match.group(2).strip().strip('"').strip("'")

                # Map Elastic field to Sigma field
                sigma_field = self.field_mapping.get(field, field)

                # Detect wildcards and add modifiers
                if '*' in value:
                    sigma_field += '|contains'
                    value = value.replace('*', '')

                selection[sigma_field] = value

        # If no fields parsed, add as raw search
        if not selection:
            selection['_raw'] = query

        return selection

    def _parse_eql_query(self, query: str) -> Dict[str, Any]:
        """
        Parse Event Query Language (EQL) to Sigma selection

        Example EQL:
            process where process.name == "powershell.exe"

        Becomes Sigma:
            selection:
                Image: "powershell.exe"
        """
        selection = {}

        # Extract WHERE clause
        where_match = re.search(r'where\s+(.+)', query, re.IGNORECASE)
        if where_match:
            where_clause = where_match.group(1)

            # Parse field == "value" patterns
            field_patterns = re.findall(r'([a-zA-Z._]+)\s*==\s*"([^"]+)"', where_clause)
            for field, value in field_patterns:
                sigma_field = self.field_mapping.get(field, field)
                selection[sigma_field] = value

        return selection

    def _parse_esql_query(self, query: str) -> Dict[str, Any]:
        """Parse ES|QL query to Sigma selection"""
        # ES|QL is newer - simplified parsing
        selection = {}

        # Extract WHERE conditions
        where_match = re.search(r'WHERE\s+(.+?)(?:$|\|)', query, re.IGNORECASE)
        if where_match:
            conditions = where_match.group(1)
            # Similar parsing to KQL
            selection = self._parse_kql_query(conditions)

        return selection

    def _add_filters_to_detection(self, detection: Dict, filters: List) -> Dict:
        """Add Elastic filters to Sigma detection as additional conditions"""
        if not filters:
            return detection

        # Create filter_not section for exclude filters
        filter_not = {}

        for filter_obj in filters:
            if isinstance(filter_obj, dict):
                # Parse filter structure
                if 'query' in filter_obj:
                    # Add to selection or filter_not based on meta.negate
                    pass

        if filter_not:
            detection['filter_not'] = filter_not
            detection['condition'] = 'selection and not filter_not'

        return detection

    def _determine_logsource(self, index_patterns: List[str]) -> Dict[str, str]:
        """
        Determine Sigma logsource from Elastic index patterns

        Sigma logsource has: product, service, category
        """
        logsource = {}

        if not index_patterns:
            return {'category': 'unknown'}

        # Analyze index patterns
        pattern_str = ' '.join(index_patterns).lower()

        # Determine product
        if 'windows' in pattern_str or 'winlog' in pattern_str:
            logsource['product'] = 'windows'
        elif 'linux' in pattern_str or 'syslog' in pattern_str:
            logsource['product'] = 'linux'
        elif 'macos' in pattern_str:
            logsource['product'] = 'macos'

        # Determine service
        if 'security' in pattern_str:
            logsource['service'] = 'security'
        elif 'sysmon' in pattern_str:
            logsource['service'] = 'sysmon'
        elif 'powershell' in pattern_str:
            logsource['service'] = 'powershell'
        elif 'process' in pattern_str:
            logsource['category'] = 'process_creation'
        elif 'network' in pattern_str:
            logsource['category'] = 'network_connection'
        elif 'file' in pattern_str:
            logsource['category'] = 'file_event'

        # Default if nothing matched
        if not logsource:
            logsource['category'] = 'generic'

        return logsource

    def _convert_tags(self, elastic_rule: Dict) -> List[str]:
        """
        Convert Elastic rule metadata to Sigma tags

        Sigma tags format: attack.t1234, attack.technique_name
        """
        tags = []

        # Add original tags
        if elastic_rule.get('tags'):
            tags.extend(elastic_rule['tags'])

        # Add MITRE tags in Sigma format
        for technique in elastic_rule.get('mitre_techniques', []):
            # Convert T1234 to attack.t1234
            tags.append(f"attack.{technique.lower()}")

        # Add source tag
        tags.append('source.elastic')

        return list(set(tags))  # Deduplicate


# Comparison and deduplication functions

def calculate_rule_similarity(sigma_rule1: SigmaRule, sigma_rule2: SigmaRule) -> float:
    """
    Calculate similarity between two Sigma rules

    Returns score 0.0-1.0 where 1.0 is identical
    """
    score = 0.0

    # Logsource similarity (30%)
    if sigma_rule1.logsource == sigma_rule2.logsource:
        score += 0.3

    # Detection logic similarity (50%)
    detection1 = json.dumps(sigma_rule1.detection, sort_keys=True)
    detection2 = json.dumps(sigma_rule2.detection, sort_keys=True)

    if detection1 == detection2:
        score += 0.5
    else:
        # Partial match based on selection fields
        selection1 = sigma_rule1.detection.get('selection', {})
        selection2 = sigma_rule2.detection.get('selection', {})

        if selection1 and selection2:
            common_keys = set(selection1.keys()) & set(selection2.keys())
            total_keys = set(selection1.keys()) | set(selection2.keys())
            if total_keys:
                score += (len(common_keys) / len(total_keys)) * 0.5

    # Tags similarity (20%)
    tags1 = set(sigma_rule1.tags or [])
    tags2 = set(sigma_rule2.tags or [])

    if tags1 and tags2:
        common_tags = tags1 & tags2
        total_tags = tags1 | tags2
        score += (len(common_tags) / len(total_tags)) * 0.2

    return score


def find_duplicate_rules(new_rule: SigmaRule, existing_rules: List[SigmaRule], threshold: float = 0.8) -> List[Dict]:
    """
    Find duplicate or similar rules in existing ruleset

    Args:
        new_rule: Newly harvested rule
        existing_rules: List of existing rules in database
        threshold: Similarity threshold (0.0-1.0)

    Returns:
        List of matches with similarity scores
    """
    matches = []

    for existing_rule in existing_rules:
        similarity = calculate_rule_similarity(new_rule, existing_rule)

        if similarity >= threshold:
            matches.append({
                'rule_id': existing_rule.id,
                'rule_title': existing_rule.title,
                'similarity_score': similarity,
                'recommendation': 'duplicate' if similarity > 0.95 else 'similar'
            })

    # Sort by similarity descending
    matches.sort(key=lambda x: x['similarity_score'], reverse=True)

    return matches


# Example usage
def example_conversion():
    """Example of converting Elastic rule to Sigma"""

    # Sample Elastic rule
    elastic_rule = {
        'rule_id': 'abc-123-def',
        'name': 'Suspicious PowerShell Execution',
        'description': 'Detects suspicious PowerShell command execution',
        'query': 'process.name: "powershell.exe" AND process.args: "-enc"',
        'language': 'kuery',
        'severity': 'high',
        'enabled': True,
        'mitre_techniques': ['T1059.001', 'T1086'],
        'tags': ['windows', 'powershell'],
        'index_patterns': ['logs-windows*', 'winlogbeat-*'],
        'false_positives': ['Legitimate admin scripts']
    }

    # Convert to Sigma
    converter = ElasticToSigmaConverter()
    sigma_rule = converter.convert_elastic_rule(elastic_rule)

    # Output as YAML
    print("Sigma Rule (YAML):")
    print(sigma_rule.to_yaml())

    # Get hash for deduplication
    print(f"\nRule Hash: {sigma_rule.get_hash()}")


if __name__ == "__main__":
    example_conversion()
