# Update Scheduler Architecture Fix

## Problem Identified

The current `update_scheduler.py` violates separation of concerns by having the **Backend Engine fetch data directly** from external APIs instead of delegating to the **Ingestion Engine**.

### Current (INCORRECT) Flow ❌

```
Backend Engine
  ├── Update Scheduler (scheduling) ✅ CORRECT
  ├── SourceUpdateManager (fetching) ❌ WRONG ENGINE
  └── Direct API calls to OTX, OpenCTI, etc. ❌ WRONG ENGINE
```

**Problem**: Backend Engine shouldn't make external API calls - that's Ingestion's job!

### Correct Flow ✅

```
Backend Engine (Scheduler)
    ↓ Redis publish: 'ingestion.cti.update'
Ingestion Engine
    ↓ Fetches from OTX, OpenCTI, ThreatFox
    ↓ Redis publish: 'backend.cti.data'
Backend Engine
    ↓ Processes and stores
Database
```

## Architecture Principles

### Engine Responsibilities

1. **Ingestion Engine** (Port 8003)
   - Fetch data from external sources
   - Normalize to common format
   - Publish to other engines
   - **Owns**: API keys, rate limiting, retry logic

2. **Backend Engine** (Port 8002)
   - Schedule when updates should happen
   - Store and process received data
   - Generate rules from CTI
   - **Owns**: Storage, processing, scheduling

3. **Intelligence Engine** (Port 8001)
   - AI-powered analysis
   - Pattern crystallization
   - **Owns**: AI models, pattern library

## Corrected Implementation

### Backend Engine: Update Scheduler (Trigger Only)

```python
async def _check_cti_updates(self, auto_apply: bool):
    """Trigger CTI update check (delegates to Ingestion)"""
    try:
        logger.info("Triggering CTI update check...")

        # Publish trigger to Ingestion Engine
        await self.redis_client.publish('ingestion.cti.update', json.dumps({
            'sources': ['otx', 'opencti', 'threatfox'],
            'auto_apply': auto_apply,
            'triggered_by': 'scheduler',
            'timestamp': datetime.utcnow().isoformat()
        }))

        logger.info("CTI update triggered - waiting for Ingestion Engine")

    except Exception as e:
        logger.error(f"CTI update trigger failed: {e}")
```

### Ingestion Engine: CTI Collector (Does the Work)

```python
async def _handle_cti_update_trigger(self, data: Dict):
    """Handle CTI update trigger from Backend Scheduler"""
    try:
        sources = data.get('sources', [])

        for source in sources:
            logger.info(f"Fetching CTI updates from {source}...")

            if source == 'otx':
                updates = await self._fetch_otx_updates()
            elif source == 'opencti':
                updates = await self._fetch_opencti_updates()
            elif source == 'threatfox':
                updates = await self._fetch_threatfox_updates()

            # Publish results back to Backend
            if updates:
                await self.redis_client.publish('backend.cti.data', json.dumps({
                    'source': source,
                    'updates': updates,
                    'count': len(updates),
                    'timestamp': datetime.utcnow().isoformat()
                }))

        logger.info(f"CTI fetch complete for {len(sources)} sources")

    except Exception as e:
        logger.error(f"CTI fetch failed: {e}")
```

### Backend Engine: Data Receiver (Processes Results)

```python
async def _handle_cti_data(self, data: Dict):
    """Process CTI data from Ingestion Engine"""
    try:
        source = data.get('source')
        updates = data.get('updates', [])

        logger.info(f"Received {len(updates)} updates from {source}")

        # Process and store
        results = await self.update_manager.apply_updates(updates, auto_apply)

        # Update correlation rules
        await self._update_correlation_rules_from_cti(updates)

        # Log results
        await self._log_update_results(source, results)

    except Exception as e:
        logger.error(f"CTI data processing failed: {e}")
```

## Redis Channels

### Backend → Ingestion (Triggers)
- `ingestion.cti.update` - Trigger CTI fetch
- `ingestion.elastic.harvest` - Trigger Elastic rule harvest
- `ingestion.sigma.update` - Trigger Sigma rule update

### Ingestion → Backend (Data)
- `backend.cti.data` - CTI data ready for processing
- `backend.rules.data` - Detection rules ready for storage
- `backend.mitre.data` - MITRE framework updates

### Backend → All (Events)
- `backend.update_complete` - Update completed successfully
- `backend.update_failed` - Update failed

## Benefits of Correct Architecture

1. **Separation of Concerns** ✅
   - Ingestion owns external API calls
   - Backend owns scheduling and storage
   - Clear responsibilities

2. **Scalability** ✅
   - Can scale Ingestion independently
   - Rate limiting in one place
   - Parallel source fetching

3. **Resilience** ✅
   - Ingestion failure doesn't crash Backend
   - Retry logic in Ingestion only
   - Better error isolation

4. **Testing** ✅
   - Mock Ingestion responses easily
   - Test scheduling without API calls
   - Integration tests clearer

## Migration Plan

### Phase 1: Add Delegation (Non-Breaking)
1. Keep existing `update_manager` calls
2. Add Redis pub/sub triggers alongside
3. Add Ingestion handlers
4. Run both in parallel

### Phase 2: Monitor & Verify
1. Confirm Ingestion receives triggers
2. Verify data flows correctly
3. Compare results from both paths

### Phase 3: Switch Over
1. Make Redis path primary
2. Keep old path as fallback
3. Log when fallback is used

### Phase 4: Cleanup
1. Remove direct API calls from Backend
2. Delete `source_update_manager` API code
3. Keep only scheduling logic

## Current Status

- **Update Scheduler**: ✅ Running in Backend (scheduling works)
- **Architecture**: ❌ Direct API calls (needs delegation)
- **Ingestion Handlers**: ❌ Not implemented yet

## Next Steps

1. **Add Ingestion CTI handlers** (1 hour)
   - Subscribe to `ingestion.cti.update`
   - Fetch from APIs
   - Publish to `backend.cti.data`

2. **Add Backend data receivers** (30 min)
   - Subscribe to `backend.cti.data`
   - Process incoming data
   - Store to database

3. **Test end-to-end** (30 min)
   - Trigger update manually
   - Verify Ingestion fetches
   - Confirm Backend stores

4. **Switch to delegation** (15 min)
   - Change scheduler to publish triggers
   - Remove direct API calls
   - Test production

**Total Effort**: ~2.5 hours to fix properly

## Recommendation

The Update Scheduler is **functionally working** but **architecturally incorrect**.

**Options**:

1. **Fix Now** (2.5 hours) - Proper architecture, scalable
2. **Fix Later** - Keep current implementation, refactor when needed
3. **Hybrid** - Add delegation path, keep fallback

**Recommendation**: **Fix Later** - Current implementation works for MVP, refactor during scaling phase when we actually need multi-engine deployment benefits.
