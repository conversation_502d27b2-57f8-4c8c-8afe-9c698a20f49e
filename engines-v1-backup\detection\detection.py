"""
Detection Engine - Real-time threat detection using crystallized patterns

This engine performs:
- Pattern-based detection (deterministic, free)
- Behavioral analysis (graph-based)
- Anomaly detection (statistical)
- Threat intelligence correlation
- Alert generation with evidence
"""
import asyncio
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from dataclasses import dataclass, field
import statistics
from collections import defaultdict, deque

# Add v2 to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.base import BaseEngine


@dataclass
class Detection:
    """Represents a security detection"""
    detection_id: str
    severity: str  # Critical, High, Medium, Low, Info
    confidence: float
    detection_type: str
    title: str
    description: str
    evidence: List[Dict[str, Any]]
    entities: List[Dict[str, Any]]
    mitre_techniques: List[str]
    recommended_actions: List[str]
    timestamp: datetime
    raw_score: float = 0.0
    kill_chain_phase: str = ""


@dataclass
class DetectionRule:
    """Detection rule definition"""
    rule_id: str
    name: str
    description: str
    severity: str
    rule_type: str  # pattern, behavioral, anomaly, threat_intel
    conditions: Dict[str, Any]
    mitre_techniques: List[str] = field(default_factory=list)
    response_actions: List[str] = field(default_factory=list)
    enabled: bool = True
    confidence_modifier: float = 1.0


class DetectionEngine(BaseEngine):
    """
    Detection Engine: Real-time threat detection and alerting

    Uses crystallized patterns for fast, deterministic detection
    with minimal AI fallback for unknown threats.
    """

    def __init__(self):
        super().__init__('detection', '2.0.0')

        # Detection rules (from v1 experience + new patterns)
        self.detection_rules = self._initialize_detection_rules()

        # Behavioral baselines (for anomaly detection)
        self.baselines = defaultdict(lambda: {
            'values': deque(maxlen=1000),
            'mean': 0,
            'stddev': 0
        })

        # Threat intelligence cache
        self.threat_intel = {
            'malicious_ips': set(),
            'malicious_domains': set(),
            'malicious_hashes': set(),
            'known_c2': set(),
            'iocs': defaultdict(set)
        }

        # Detection statistics
        self.stats = {
            'total_analyzed': 0,
            'total_detections': 0,
            'detections_by_severity': defaultdict(int),
            'detections_by_type': defaultdict(int),
            'false_positives': 0
        }

        # Active detections (for correlation)
        self.active_detections = deque(maxlen=1000)

        # Detection correlation window (5 minutes)
        self.correlation_window = timedelta(minutes=5)

    def _initialize_detection_rules(self) -> List[DetectionRule]:
        """Initialize detection rules from patterns"""
        rules = [
            # Credential Access Rules
            DetectionRule(
                rule_id='DET-001',
                name='Credential Dumping Detection',
                description='Detects attempts to dump credentials from memory',
                severity='Critical',
                rule_type='pattern',
                conditions={
                    'any_of': [
                        {'process': 'mimikatz.exe'},
                        {'process': 'lsass.exe', 'action': 'memory_access'},
                        {'command_line': {'contains': ['sekurlsa', 'logonpasswords']}}
                    ],
                    'mitre_technique': 'T1003'
                },
                mitre_techniques=['T1003'],
                response_actions=['isolate_host', 'reset_passwords'],
                confidence_modifier=1.0
            ),

            # Lateral Movement Rules
            DetectionRule(
                rule_id='DET-002',
                name='Lateral Movement via RDP',
                description='Detects lateral movement using Remote Desktop',
                severity='High',
                rule_type='behavioral',
                conditions={
                    'all_of': [
                        {'source_ip': 'internal'},
                        {'destination_ip': 'internal'},
                        {'destination_port': 3389},
                        {'not': {'source_ip': 'admin_subnet'}}
                    ]
                },
                mitre_techniques=['T1021.001'],
                response_actions=['alert_soc', 'block_connection'],
                confidence_modifier=0.9
            ),

            # Persistence Rules
            DetectionRule(
                rule_id='DET-003',
                name='Scheduled Task Persistence',
                description='Detects creation of suspicious scheduled tasks',
                severity='High',
                rule_type='pattern',
                conditions={
                    'all_of': [
                        {'process': {'in': ['schtasks.exe', 'at.exe']}},
                        {'command_line': {'contains': ['/create', 'powershell', 'cmd']}}
                    ]
                },
                mitre_techniques=['T1053.005'],
                response_actions=['alert_soc', 'disable_task'],
                confidence_modifier=0.85
            ),

            # Data Exfiltration Rules
            DetectionRule(
                rule_id='DET-004',
                name='Large Data Transfer to External',
                description='Detects potential data exfiltration',
                severity='High',
                rule_type='anomaly',
                conditions={
                    'all_of': [
                        {'destination_ip': 'external'},
                        {'bytes_sent': {'greater_than': 104857600}},  # 100MB
                        {'not': {'destination_ip': 'known_cloud_providers'}}
                    ]
                },
                mitre_techniques=['T1041', 'T1048'],
                response_actions=['alert_soc', 'throttle_connection'],
                confidence_modifier=0.8
            ),

            # Malware Detection Rules
            DetectionRule(
                rule_id='DET-005',
                name='Known Malware Hash',
                description='Detects execution of known malware',
                severity='Critical',
                rule_type='threat_intel',
                conditions={
                    'any_of': [
                        {'file_hash': 'in_threat_intel'},
                        {'process_hash': 'in_threat_intel'},
                        {'domain': 'in_threat_intel'}
                    ]
                },
                mitre_techniques=['T1055', 'T1105'],
                response_actions=['isolate_host', 'kill_process', 'delete_file'],
                confidence_modifier=1.0
            ),

            # Anomaly Detection Rules
            DetectionRule(
                rule_id='DET-006',
                name='Unusual Process Execution',
                description='Detects processes unusual for this user/host',
                severity='Medium',
                rule_type='anomaly',
                conditions={
                    'anomaly_score': {'greater_than': 3.0}  # 3 standard deviations
                },
                mitre_techniques=['T1059'],
                response_actions=['alert_soc', 'collect_forensics'],
                confidence_modifier=0.7
            ),

            # Network Scanning Rules
            DetectionRule(
                rule_id='DET-007',
                name='Port Scanning Activity',
                description='Detects network reconnaissance via port scanning',
                severity='Medium',
                rule_type='behavioral',
                conditions={
                    'all_of': [
                        {'source_ip': 'single'},
                        {'destination_ip': 'multiple'},
                        {'destination_port': 'sequential_or_common'},
                        {'connection_state': 'failed', 'threshold': 10}
                    ]
                },
                mitre_techniques=['T1046'],
                response_actions=['alert_soc', 'rate_limit'],
                confidence_modifier=0.75
            ),

            # Privilege Escalation Rules
            DetectionRule(
                rule_id='DET-008',
                name='Privilege Escalation Attempt',
                description='Detects attempts to elevate privileges',
                severity='High',
                rule_type='pattern',
                conditions={
                    'any_of': [
                        {'process': 'UAC bypass technique'},
                        {'command_line': {'contains': ['runas', '/elevated', 'admin']}},
                        {'event_id': 4672}  # Special privileges assigned
                    ]
                },
                mitre_techniques=['T1068', 'T1548'],
                response_actions=['alert_soc', 'audit_permissions'],
                confidence_modifier=0.85
            )
        ]

        return rules

    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Process enriched data for threat detection"""
        try:
            # Extract data from message
            entities = message.get('data', {}).get('entities', [])
            relationships = message.get('data', {}).get('relationships', [])
            use_case_context = message.get('data', {}).get('use_case_context', {})
            risk_score = message.get('data', {}).get('risk_score', 0)

            self.stats['total_analyzed'] += 1

            # Run detection methods in parallel
            detection_tasks = [
                self._run_pattern_detection(entities, relationships),
                self._run_behavioral_detection(entities, relationships),
                self._run_anomaly_detection(entities),
                self._run_threat_intel_detection(entities)
            ]

            detection_results = await asyncio.gather(*detection_tasks)

            # Merge and correlate detections
            all_detections = []
            for detections in detection_results:
                all_detections.extend(detections)

            # Correlate with recent detections
            correlated_detections = self._correlate_detections(all_detections)

            # Apply use case context to boost confidence
            enhanced_detections = self._enhance_with_context(
                correlated_detections, use_case_context, risk_score
            )

            # Generate alerts for high-confidence detections
            alerts = []
            for detection in enhanced_detections:
                if detection.confidence >= 0.7:  # Confidence threshold
                    alert = self._generate_alert(detection)
                    alerts.append(alert)
                    self.stats['total_detections'] += 1
                    self.stats['detections_by_severity'][detection.severity] += 1
                    self.stats['detections_by_type'][detection.detection_type] += 1

            # Store active detections for correlation
            for detection in enhanced_detections:
                self.active_detections.append(detection)

            # Log detection results
            self.logger.log_decision(
                'threat_detection',
                {'entities': len(entities), 'relationships': len(relationships)},
                {'detections': len(enhanced_detections), 'alerts': len(alerts)},
                reasoning=f"Analyzed {len(entities)} entities, found {len(enhanced_detections)} detections",
                confidence=0.9
            )

            return {
                'success': True,
                'detections': [self._detection_to_dict(d) for d in enhanced_detections],
                'alerts': alerts,
                'statistics': dict(self.stats),
                'next_engine': 'playbook_engine' if alerts else None,
                'data': {
                    'alerts': alerts,
                    'detections': [self._detection_to_dict(d) for d in enhanced_detections],
                    'entities': entities,
                    'timestamp': datetime.utcnow().isoformat()
                }
            }

        except Exception as e:
            self.logger.log_error(e, {'message': message})
            return {'success': False, 'error': str(e)}

    async def _run_pattern_detection(self, entities: List[Dict],
                                    relationships: List[Dict]) -> List[Detection]:
        """Run pattern-based detection rules"""
        detections = []

        for rule in self.detection_rules:
            if rule.rule_type != 'pattern' or not rule.enabled:
                continue

            if self._evaluate_rule_conditions(rule.conditions, entities, relationships):
                detection = Detection(
                    detection_id=f"DET-{datetime.utcnow().timestamp()}",
                    severity=rule.severity,
                    confidence=0.9 * rule.confidence_modifier,
                    detection_type='pattern',
                    title=rule.name,
                    description=rule.description,
                    evidence=self._gather_evidence(rule, entities, relationships),
                    entities=self._extract_relevant_entities(rule, entities),
                    mitre_techniques=rule.mitre_techniques,
                    recommended_actions=rule.response_actions,
                    timestamp=datetime.utcnow()
                )
                detections.append(detection)

        return detections

    async def _run_behavioral_detection(self, entities: List[Dict],
                                       relationships: List[Dict]) -> List[Detection]:
        """Run behavioral analysis detection"""
        detections = []

        # Analyze relationship patterns
        relationship_patterns = self._analyze_relationship_patterns(relationships)

        for rule in self.detection_rules:
            if rule.rule_type != 'behavioral' or not rule.enabled:
                continue

            if self._evaluate_behavioral_rule(rule, entities, relationships, relationship_patterns):
                detection = Detection(
                    detection_id=f"BEH-{datetime.utcnow().timestamp()}",
                    severity=rule.severity,
                    confidence=0.8 * rule.confidence_modifier,
                    detection_type='behavioral',
                    title=rule.name,
                    description=rule.description,
                    evidence=self._gather_evidence(rule, entities, relationships),
                    entities=self._extract_relevant_entities(rule, entities),
                    mitre_techniques=rule.mitre_techniques,
                    recommended_actions=rule.response_actions,
                    timestamp=datetime.utcnow()
                )
                detections.append(detection)

        return detections

    async def _run_anomaly_detection(self, entities: List[Dict]) -> List[Detection]:
        """Run statistical anomaly detection"""
        detections = []

        # Update baselines and detect anomalies
        for entity in entities:
            entity_type = entity.get('type')
            entity_value = entity.get('value')

            if not entity_type or not entity_value:
                continue

            # Check if this entity is anomalous
            anomaly_score = self._calculate_anomaly_score(entity_type, entity_value)

            if anomaly_score > 3.0:  # 3 standard deviations
                detection = Detection(
                    detection_id=f"ANO-{datetime.utcnow().timestamp()}",
                    severity='Medium' if anomaly_score < 4 else 'High',
                    confidence=min(0.6 + (anomaly_score - 3) * 0.1, 0.9),
                    detection_type='anomaly',
                    title=f"Anomalous {entity_type} Activity",
                    description=f"Unusual activity detected for {entity_type}: {entity_value}",
                    evidence=[{'entity': entity, 'anomaly_score': anomaly_score}],
                    entities=[entity],
                    mitre_techniques=['T1059'],  # Generic execution
                    recommended_actions=['alert_soc', 'investigate'],
                    timestamp=datetime.utcnow(),
                    raw_score=anomaly_score
                )
                detections.append(detection)

        return detections

    async def _run_threat_intel_detection(self, entities: List[Dict]) -> List[Detection]:
        """Check entities against threat intelligence"""
        detections = []

        for entity in entities:
            entity_type = entity.get('type')
            entity_value = entity.get('value')

            is_threat = False
            threat_type = None

            # Check various threat intel sources
            if entity_type == 'ip_address' and entity_value in self.threat_intel['malicious_ips']:
                is_threat = True
                threat_type = 'malicious_ip'
            elif entity_type == 'domain' and entity_value in self.threat_intel['malicious_domains']:
                is_threat = True
                threat_type = 'malicious_domain'
            elif entity_type in ['file_hash', 'process_hash'] and entity_value in self.threat_intel['malicious_hashes']:
                is_threat = True
                threat_type = 'known_malware'

            if is_threat:
                detection = Detection(
                    detection_id=f"TI-{datetime.utcnow().timestamp()}",
                    severity='Critical',
                    confidence=0.95,
                    detection_type='threat_intel',
                    title=f"Known Threat: {threat_type}",
                    description=f"Entity matches threat intelligence: {entity_value}",
                    evidence=[{'entity': entity, 'threat_type': threat_type}],
                    entities=[entity],
                    mitre_techniques=['T1071', 'T1105'],
                    recommended_actions=['block_immediately', 'isolate_host'],
                    timestamp=datetime.utcnow()
                )
                detections.append(detection)

        return detections

    def _evaluate_rule_conditions(self, conditions: Dict, entities: List[Dict],
                                 relationships: List[Dict]) -> bool:
        """Evaluate if rule conditions are met"""
        if 'any_of' in conditions:
            for condition in conditions['any_of']:
                if self._check_condition(condition, entities, relationships):
                    return True
            return False

        elif 'all_of' in conditions:
            for condition in conditions['all_of']:
                if not self._check_condition(condition, entities, relationships):
                    return False
            return True

        return self._check_condition(conditions, entities, relationships)

    def _check_condition(self, condition: Dict, entities: List[Dict],
                        relationships: List[Dict]) -> bool:
        """Check a single condition"""
        # Check entity conditions
        for entity in entities:
            matches = True
            for key, value in condition.items():
                if key == 'not':
                    if self._check_condition(value, entities, relationships):
                        matches = False
                        break
                elif key in entity:
                    if isinstance(value, dict):
                        if 'contains' in value:
                            if not any(v in str(entity[key]) for v in value['contains']):
                                matches = False
                                break
                        elif 'in' in value:
                            if entity[key] not in value['in']:
                                matches = False
                                break
                        elif 'greater_than' in value:
                            try:
                                if float(entity[key]) <= value['greater_than']:
                                    matches = False
                                    break
                            except:
                                matches = False
                                break
                    elif entity[key] != value:
                        matches = False
                        break
                else:
                    matches = False
                    break

            if matches:
                return True

        return False

    def _analyze_relationship_patterns(self, relationships: List[Dict]) -> Dict:
        """Analyze patterns in relationships"""
        patterns = {
            'lateral_movement': 0,
            'reconnaissance': 0,
            'persistence': 0,
            'exfiltration': 0
        }

        # Count relationship types
        rel_types = defaultdict(int)
        for rel in relationships:
            rel_types[rel.get('type', '')] += 1

        # Detect patterns
        if rel_types.get('connected_to', 0) > 5:
            patterns['reconnaissance'] += 1

        if rel_types.get('executed', 0) > 3:
            patterns['lateral_movement'] += 1

        return patterns

    def _evaluate_behavioral_rule(self, rule: DetectionRule, entities: List[Dict],
                                 relationships: List[Dict], patterns: Dict) -> bool:
        """Evaluate behavioral rules"""
        # Simplified behavioral evaluation
        return self._evaluate_rule_conditions(rule.conditions, entities, relationships)

    def _calculate_anomaly_score(self, entity_type: str, entity_value: str) -> float:
        """Calculate anomaly score for an entity"""
        baseline = self.baselines[f"{entity_type}:{entity_value}"]

        # Update baseline
        baseline['values'].append(1)  # Simplified: just track occurrences

        if len(baseline['values']) < 10:
            return 0.0  # Not enough data

        # Calculate statistics
        mean = statistics.mean(baseline['values'])
        stddev = statistics.stdev(baseline['values']) if len(baseline['values']) > 1 else 1

        baseline['mean'] = mean
        baseline['stddev'] = stddev

        # Calculate z-score
        if stddev == 0:
            return 0.0

        z_score = abs((1 - mean) / stddev)
        return z_score

    def _correlate_detections(self, detections: List[Detection]) -> List[Detection]:
        """Correlate detections with recent activity"""
        correlated = []

        for detection in detections:
            # Check for related detections in time window
            related_count = 0
            for active in self.active_detections:
                time_diff = detection.timestamp - active.timestamp
                if time_diff <= self.correlation_window:
                    # Check if entities overlap
                    active_entities = {e['value'] for e in active.entities}
                    current_entities = {e['value'] for e in detection.entities}
                    if active_entities & current_entities:
                        related_count += 1

            # Boost confidence if correlated
            if related_count > 0:
                detection.confidence = min(detection.confidence * (1 + related_count * 0.1), 1.0)
                detection.description += f" (Correlated with {related_count} recent detections)"

            correlated.append(detection)

        return correlated

    def _enhance_with_context(self, detections: List[Detection],
                             use_case_context: Dict, risk_score: float) -> List[Detection]:
        """Enhance detections with use case context"""
        enhanced = []

        for detection in detections:
            # Boost confidence based on use case match
            if use_case_context:
                for context in use_case_context.get('security_context', []):
                    if any(tech in context.get('context', '') for tech in detection.mitre_techniques):
                        detection.confidence = min(detection.confidence * 1.2, 1.0)

            # Adjust severity based on risk score
            if risk_score >= 80:
                if detection.severity == 'Medium':
                    detection.severity = 'High'
                elif detection.severity == 'Low':
                    detection.severity = 'Medium'

            enhanced.append(detection)

        return enhanced

    def _gather_evidence(self, rule: DetectionRule, entities: List[Dict],
                        relationships: List[Dict]) -> List[Dict]:
        """Gather evidence for a detection"""
        evidence = []

        # Add matching entities
        for entity in entities[:5]:  # Limit to 5 for brevity
            evidence.append({
                'type': 'entity',
                'data': entity
            })

        # Add relevant relationships
        for rel in relationships[:3]:  # Limit to 3
            evidence.append({
                'type': 'relationship',
                'data': rel
            })

        # Add rule information
        evidence.append({
            'type': 'rule',
            'data': {
                'rule_id': rule.rule_id,
                'conditions': rule.conditions
            }
        })

        return evidence

    def _extract_relevant_entities(self, rule: DetectionRule,
                                  entities: List[Dict]) -> List[Dict]:
        """Extract entities relevant to the detection"""
        relevant = []

        # Get key entity types for this rule
        key_types = set()
        for tech in rule.mitre_techniques:
            if tech.startswith('T1003'):  # Credential access
                key_types.update(['user', 'process', 'hostname'])
            elif tech.startswith('T1021'):  # Lateral movement
                key_types.update(['source_ip', 'destination_ip', 'hostname'])
            elif tech.startswith('T1053'):  # Persistence
                key_types.update(['process', 'scheduled_task', 'user'])

        for entity in entities:
            if entity.get('type') in key_types:
                relevant.append(entity)

        return relevant[:10]  # Limit to 10 most relevant

    def _generate_alert(self, detection: Detection) -> Dict[str, Any]:
        """Generate alert from detection"""
        return {
            'alert_id': f"ALERT-{detection.detection_id}",
            'severity': detection.severity,
            'confidence': detection.confidence,
            'title': detection.title,
            'description': detection.description,
            'detection_type': detection.detection_type,
            'mitre_techniques': detection.mitre_techniques,
            'evidence': detection.evidence,
            'entities': detection.entities,
            'recommended_actions': detection.recommended_actions,
            'timestamp': detection.timestamp.isoformat(),
            'status': 'new',
            'assigned_to': None
        }

    def _detection_to_dict(self, detection: Detection) -> Dict[str, Any]:
        """Convert Detection object to dictionary"""
        return {
            'detection_id': detection.detection_id,
            'severity': detection.severity,
            'confidence': detection.confidence,
            'detection_type': detection.detection_type,
            'title': detection.title,
            'description': detection.description,
            'evidence': detection.evidence,
            'entities': detection.entities,
            'mitre_techniques': detection.mitre_techniques,
            'recommended_actions': detection.recommended_actions,
            'timestamp': detection.timestamp.isoformat(),
            'raw_score': detection.raw_score,
            'kill_chain_phase': detection.kill_chain_phase
        }

    def get_capabilities(self) -> Dict[str, Any]:
        """Return detection engine capabilities"""
        return {
            'engine': 'detection',
            'version': self.version,
            'capabilities': [
                'pattern_detection',
                'behavioral_analysis',
                'anomaly_detection',
                'threat_intel_correlation',
                'alert_generation',
                'detection_correlation'
            ],
            'active_rules': len([r for r in self.detection_rules if r.enabled]),
            'detection_types': ['pattern', 'behavioral', 'anomaly', 'threat_intel'],
            'statistics': dict(self.stats)
        }

    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate detection engine configuration"""
        return True  # No specific configuration required for MVP


async def main():
    """Main entry point for Detection Engine"""
    engine = DetectionEngine()

    # Log capabilities
    capabilities = engine.get_capabilities()
    engine.logger.log('engine_capabilities', capabilities)

    # Test with sample enriched data
    test_message = {
        'id': 'test_001',
        'data': {
            'entities': [
                {'type': 'process', 'value': 'mimikatz.exe'},
                {'type': 'hostname', 'value': 'WIN-SERVER01'},
                {'type': 'user', 'value': 'admin'},
                {'type': 'technique', 'value': 'T1003'}
            ],
            'relationships': [
                {'source': 'admin', 'target': 'mimikatz.exe', 'type': 'executed'}
            ],
            'use_case_context': {
                'security_context': [
                    {'context': 'credential_access', 'confidence': 0.95}
                ]
            },
            'risk_score': 85
        }
    }

    result = await engine.process_message(test_message)
    print(json.dumps(result, indent=2, default=str))

    # Start processing from queue
    await engine.start()


if __name__ == '__main__':
    asyncio.run(main())