"""
Base Engine Class for SIEMLess v2.0
Every engine inherits from this to ensure consistent behavior
"""
import asyncio
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod
from pathlib import Path
import psycopg2
from psycopg2.extras import RealDictCursor

from .logging import UniversalLogger
from .queue import MessageQueue


class BaseEngine(ABC):
    """
    Base class for all SIEMLess engines
    Provides common functionality for all microservices
    """

    def __init__(self, engine_name: str, version: str = "2.0.0"):
        """
        Initialize base engine with standard components

        Args:
            engine_name: Name of the engine (e.g., 'librarian', 'parser')
            version: Engine version for tracking
        """
        self.engine_name = engine_name
        self.version = version
        self.start_time = datetime.utcnow()

        # Initialize universal logger
        self.logger = UniversalLogger(engine_name)

        # Initialize message queue
        self.message_queue = MessageQueue(engine_name)

        # Database connection (will be initialized when needed)
        self._db_connection = None

        # Configuration cache
        self._config_cache = {}

        # Health metrics
        self.metrics = {
            'messages_processed': 0,
            'errors_encountered': 0,
            'last_activity': datetime.utcnow(),
            'status': 'initializing'
        }

        # Log engine startup
        self.logger.log('engine_startup', {
            'engine': engine_name,
            'version': version,
            'pid': os.getpid()
        })

    @abstractmethod
    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single message - must be implemented by each engine

        Args:
            message: The message to process

        Returns:
            Processing result with metadata
        """
        pass

    @abstractmethod
    def get_capabilities(self) -> Dict[str, Any]:
        """
        Return engine capabilities for discovery

        Returns:
            Dictionary describing what this engine can do
        """
        pass

    @abstractmethod
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """
        Validate engine-specific configuration

        Args:
            config: Configuration to validate

        Returns:
            True if configuration is valid
        """
        pass

    def get_db_connection(self):
        """Get or create database connection"""
        if not self._db_connection or self._db_connection.closed:
            self._db_connection = psycopg2.connect(
                host=os.environ.get('POSTGRES_HOST', 'localhost'),
                port=os.environ.get('POSTGRES_PORT', 5432),
                database=os.environ.get('POSTGRES_DB', 'siemless_db'),
                user=os.environ.get('POSTGRES_USER', 'test'),
                password=os.environ.get('POSTGRES_PASSWORD', 'test123'),
                cursor_factory=RealDictCursor
            )
        return self._db_connection

    async def start(self):
        """
        Start the engine and begin processing messages
        """
        self.logger.log('engine_started', {
            'engine': self.engine_name,
            'status': 'running'
        })

        self.metrics['status'] = 'running'

        try:
            # Main processing loop
            while True:
                # Get next message from queue
                message = await self.message_queue.get_message()

                if message:
                    self.logger.start_operation(f"process_message_{message.get('id', 'unknown')}")

                    try:
                        # Process the message
                        result = await self.process_message(message)

                        # Log successful processing
                        self.logger.log_decision(
                            'message_processed',
                            message,
                            result,
                            reasoning=f"Processed by {self.engine_name}",
                            confidence=result.get('confidence', 1.0)
                        )

                        # Update metrics
                        self.metrics['messages_processed'] += 1
                        self.metrics['last_activity'] = datetime.utcnow()

                        # Send result to next engine if specified
                        if 'next_engine' in result:
                            await self.message_queue.send_message(
                                result['next_engine'],
                                result['data']
                            )

                    except Exception as e:
                        self.logger.log_error(e, {'message': message})
                        self.metrics['errors_encountered'] += 1

                    finally:
                        duration = self.logger.end_operation(f"process_message_{message.get('id', 'unknown')}")

                        # Log performance metrics
                        if duration > 5.0:  # Log slow operations
                            self.logger.log('performance_warning', {
                                'operation': 'process_message',
                                'duration': duration,
                                'threshold': 5.0
                            }, 'WARNING')

                else:
                    # No messages, wait briefly
                    await asyncio.sleep(0.1)

        except KeyboardInterrupt:
            self.logger.log('engine_shutdown', {
                'engine': self.engine_name,
                'reason': 'user_interrupt'
            })
        except Exception as e:
            self.logger.log_error(e, {'engine': self.engine_name})
            raise
        finally:
            await self.shutdown()

    async def shutdown(self):
        """
        Clean shutdown of the engine
        """
        self.metrics['status'] = 'shutting_down'

        # Log final metrics
        self.logger.log('engine_shutdown', {
            'engine': self.engine_name,
            'metrics': self.metrics,
            'uptime_seconds': (datetime.utcnow() - self.start_time).total_seconds()
        })

        # Close connections
        if self._db_connection and not self._db_connection.closed:
            self._db_connection.close()

        # Close logger
        self.logger.close()

        # Close message queue
        await self.message_queue.close()

        self.metrics['status'] = 'stopped'

    def get_health(self) -> Dict[str, Any]:
        """
        Get current health status of the engine

        Returns:
            Health metrics and status
        """
        uptime = (datetime.utcnow() - self.start_time).total_seconds()

        health = {
            'engine': self.engine_name,
            'version': self.version,
            'status': self.metrics['status'],
            'uptime_seconds': uptime,
            'messages_processed': self.metrics['messages_processed'],
            'errors_encountered': self.metrics['errors_encountered'],
            'last_activity': self.metrics['last_activity'].isoformat(),
            'error_rate': self.metrics['errors_encountered'] / max(1, self.metrics['messages_processed'])
        }

        # Add engine-specific health checks
        health['healthy'] = (
            self.metrics['status'] == 'running' and
            health['error_rate'] < 0.1 and  # Less than 10% error rate
            (datetime.utcnow() - self.metrics['last_activity']).total_seconds() < 300  # Active in last 5 minutes
        )

        return health

    def load_configuration(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Load engine configuration from file or database

        Args:
            config_path: Optional path to configuration file

        Returns:
            Configuration dictionary
        """
        # Check cache first
        if self._config_cache:
            return self._config_cache

        config = {}

        # Try loading from file
        if config_path and Path(config_path).exists():
            with open(config_path, 'r') as f:
                config = json.load(f)

        # Try loading from database
        elif self._db_connection:
            try:
                cursor = self._db_connection.cursor()
                cursor.execute("""
                    SELECT configuration
                    FROM engine_configurations
                    WHERE engine_name = %s AND active = true
                    ORDER BY created_at DESC
                    LIMIT 1
                """, (self.engine_name,))

                result = cursor.fetchone()
                if result:
                    config = result['configuration']
                cursor.close()
            except Exception as e:
                self.logger.log_error(e, {'context': 'load_configuration'})

        # Validate configuration
        if config and self.validate_configuration(config):
            self._config_cache = config
            self.logger.log('configuration_loaded', {
                'engine': self.engine_name,
                'source': 'file' if config_path else 'database'
            })
        else:
            self.logger.log('configuration_invalid', {
                'engine': self.engine_name,
                'config': config
            }, 'WARNING')

        return config

    def emit_pattern(self, pattern: Dict[str, Any]):
        """
        Emit a discovered pattern for crystallization

        Args:
            pattern: The pattern to emit
        """
        self.logger.log_pattern_discovery(
            pattern.get('pattern', ''),
            pattern.get('sample', {}),
            pattern.get('confidence', 0.0)
        )

        # Send to Librarian for crystallization
        asyncio.create_task(
            self.message_queue.send_message('librarian', {
                'type': 'pattern_submission',
                'source_engine': self.engine_name,
                'pattern': pattern,
                'timestamp': datetime.utcnow().isoformat()
            })
        )

    def request_pattern(self, pattern_type: str) -> Optional[Dict[str, Any]]:
        """
        Request a pattern from the Librarian

        Args:
            pattern_type: Type of pattern needed (e.g., 'cisco_asa_parser')

        Returns:
            Pattern if available, None otherwise
        """
        try:
            # Query pattern library (will be implemented with Librarian)
            conn = self.get_db_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT pattern_data, confidence, last_updated
                FROM pattern_library
                WHERE pattern_type = %s AND active = true
                ORDER BY confidence DESC, last_updated DESC
                LIMIT 1
            """, (pattern_type,))

            result = cursor.fetchone()
            cursor.close()

            if result:
                self.logger.log('pattern_retrieved', {
                    'pattern_type': pattern_type,
                    'confidence': result['confidence']
                }, 'DEBUG')
                return result['pattern_data']

        except Exception as e:
            self.logger.log_error(e, {'context': 'request_pattern', 'pattern_type': pattern_type})

        return None