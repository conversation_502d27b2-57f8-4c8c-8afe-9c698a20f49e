"""
SIEMLess v2.0 - Enrichment Services
Business context, threat intelligence, and technical enrichment
"""

import json
import ipaddress
import hashlib
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import random  # For simulated enrichment data

class EnrichmentService:
    """Master enrichment orchestrator"""

    def __init__(self, redis_client=None):
        self.redis_client = redis_client
        self.enrichers = {
            'business': BusinessContextEnricher(),
            'threat': ThreatIntelligenceEnricher(),
            'technical': TechnicalEnricher(),
            'security_zone': SecurityZoneDetector(),
            'baseline': BaselineAnalyzer(),
            'cti': CTIContextEnricher(redis_client) if redis_client else None
        }

    def enrich_entity(self, entity: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive enrichment on an entity"""
        enrichments = {}

        # Apply all enrichers
        for name, enricher in self.enrichers.items():
            try:
                result = enricher.enrich(entity)
                if result:
                    enrichments[name] = result
            except Exception as e:
                # Log but don't fail entire enrichment
                enrichments[name] = {'error': str(e)}

        # Calculate risk score based on enrichments
        enrichments['risk_score'] = self._calculate_risk_score(entity, enrichments)

        return enrichments

    def _calculate_risk_score(self, entity: Dict, enrichments: Dict) -> int:
        """Calculate risk score based on all enrichment factors"""
        score = 0
        weights = {
            'criticality': 0.30,
            'threat_reputation': 0.25,
            'anomaly': 0.20,
            'exposure': 0.10,
            'vulnerability': 0.10,
            'history': 0.05
        }

        # Business criticality
        business = enrichments.get('business', {})
        criticality = business.get('criticality', 50)
        score += criticality * weights['criticality']

        # Threat reputation
        threat = enrichments.get('threat', {})
        reputation = 100 - threat.get('reputation_score', 100)
        score += reputation * weights['threat_reputation']

        # Zone trust (inverse - lower trust = higher risk)
        zone = enrichments.get('security_zone', {})
        trust = 100 - zone.get('trust_level', 50)
        score += trust * weights['exposure']

        return min(100, int(score))


class BusinessContextEnricher:
    """Enriches entities with business context"""

    def __init__(self):
        # Simulated business data
        self.asset_inventory = {
            'PROD-WEB-01': {'owner': 'Web Team', 'criticality': 90, 'department': 'IT'},
            'PROD-DB-01': {'owner': 'Database Team', 'criticality': 100, 'department': 'IT'},
            'DEV-APP-01': {'owner': 'Dev Team', 'criticality': 30, 'department': 'Engineering'},
            '10.1.1.10': {'owner': 'Finance', 'criticality': 95, 'department': 'Finance'},
            '192.168.1.100': {'owner': 'HR', 'criticality': 70, 'department': 'HR'}
        }

        self.user_directory = {
            'admin': {'role': 'administrator', 'department': 'IT', 'criticality': 100},
            'jsmith': {'role': 'developer', 'department': 'Engineering', 'criticality': 60},
            'accounting': {'role': 'service_account', 'department': 'Finance', 'criticality': 80}
        }

    def enrich(self, entity: Dict[str, Any]) -> Dict[str, Any]:
        """Add business context to entity"""
        entity_type = entity.get('type')
        entity_value = entity.get('value')

        context = {
            'enrichment_timestamp': datetime.utcnow().isoformat()
        }

        if entity_type == 'hostname':
            # Check asset inventory
            asset_info = self.asset_inventory.get(entity_value.upper(), {})
            if asset_info:
                context.update(asset_info)
            else:
                # Guess based on naming convention
                context.update(self._guess_from_hostname(entity_value))

        elif entity_type == 'ip_address':
            # Check if IP is in inventory
            asset_info = self.asset_inventory.get(entity_value, {})
            if asset_info:
                context.update(asset_info)
            else:
                # Determine based on subnet
                context.update(self._guess_from_subnet(entity_value))

        elif entity_type == 'username':
            # Check user directory
            user_info = self.user_directory.get(entity_value.lower(), {})
            if user_info:
                context.update(user_info)
            else:
                # Guess based on username patterns
                context.update(self._guess_from_username(entity_value))

        return context

    def _guess_from_hostname(self, hostname: str) -> Dict:
        """Guess business context from hostname patterns"""
        hostname_upper = hostname.upper()

        if 'PROD' in hostname_upper or 'PRD' in hostname_upper:
            return {'environment': 'production', 'criticality': 80}
        elif 'DEV' in hostname_upper or 'TEST' in hostname_upper:
            return {'environment': 'development', 'criticality': 30}
        elif 'DB' in hostname_upper or 'SQL' in hostname_upper:
            return {'service_type': 'database', 'criticality': 90}
        elif 'WEB' in hostname_upper or 'WWW' in hostname_upper:
            return {'service_type': 'web', 'criticality': 70}
        elif 'DC' in hostname_upper or 'AD' in hostname_upper:
            return {'service_type': 'domain_controller', 'criticality': 100}
        else:
            return {'environment': 'unknown', 'criticality': 50}

    def _guess_from_subnet(self, ip: str) -> Dict:
        """Guess business context from IP subnet"""
        try:
            ip_obj = ipaddress.ip_address(ip)

            # Private IP ranges
            if ip_obj.is_private:
                if ip_obj in ipaddress.ip_network('********/16'):
                    return {'network': 'production', 'criticality': 80}
                elif ip_obj in ipaddress.ip_network('***********/16'):
                    return {'network': 'office', 'criticality': 50}
                elif ip_obj in ipaddress.ip_network('**********/12'):
                    return {'network': 'dmz', 'criticality': 60}

            # Public IP
            if ip_obj.is_global:
                return {'network': 'external', 'criticality': 40, 'exposure': 'public'}

        except:
            pass

        return {'network': 'unknown', 'criticality': 50}

    def _guess_from_username(self, username: str) -> Dict:
        """Guess user context from username patterns"""
        username_lower = username.lower()

        if 'admin' in username_lower or 'root' in username_lower:
            return {'role': 'administrator', 'criticality': 100}
        elif 'svc' in username_lower or 'service' in username_lower:
            return {'role': 'service_account', 'criticality': 80}
        elif 'test' in username_lower or 'temp' in username_lower:
            return {'role': 'test_account', 'criticality': 20}
        elif username_lower.startswith('sa-'):
            return {'role': 'service_account', 'criticality': 80}
        else:
            return {'role': 'standard_user', 'criticality': 50}


class ThreatIntelligenceEnricher:
    """Enriches entities with threat intelligence"""

    def __init__(self):
        # Simulated threat data
        self.known_bad_ips = {
            '*************': {'reputation': 10, 'category': 'tor_exit'},
            '*********': {'reputation': 0, 'category': 'test'},
            '************': {'reputation': 20, 'category': 'vpn'}
        }

        self.known_bad_hashes = {
            '44d88612fea8a8f36de82e1278abb02f': {'malware': 'WannaCry', 'severity': 'critical'},
            '098f6bcd4621d373cade4e832627b4f6': {'malware': 'TestFile', 'severity': 'info'}
        }

        self.known_bad_domains = {
            'evil.com': {'reputation': 0, 'category': 'malware'},
            'phishing.net': {'reputation': 10, 'category': 'phishing'}
        }

    def enrich(self, entity: Dict[str, Any]) -> Dict[str, Any]:
        """Add threat intelligence to entity"""
        entity_type = entity.get('type')
        entity_value = entity.get('value')

        threat_data = {
            'reputation_score': 100,  # Default to good
            'threat_feeds': [],
            'last_checked': datetime.utcnow().isoformat()
        }

        if entity_type == 'ip_address':
            # Check known bad IPs
            if entity_value in self.known_bad_ips:
                bad_ip = self.known_bad_ips[entity_value]
                threat_data['reputation_score'] = bad_ip['reputation']
                threat_data['threat_category'] = bad_ip['category']
                threat_data['threat_feeds'].append('internal_blocklist')

            # Check if Tor/VPN/Proxy
            threat_data.update(self._check_anonymizer(entity_value))

        elif entity_type == 'hash':
            # Check known malware hashes
            if entity_value.lower() in self.known_bad_hashes:
                malware = self.known_bad_hashes[entity_value.lower()]
                threat_data['reputation_score'] = 0
                threat_data['malware_family'] = malware['malware']
                threat_data['severity'] = malware['severity']
                threat_data['threat_feeds'].append('malware_database')

        elif entity_type == 'domain':
            # Check known bad domains
            if entity_value in self.known_bad_domains:
                bad_domain = self.known_bad_domains[entity_value]
                threat_data['reputation_score'] = bad_domain['reputation']
                threat_data['threat_category'] = bad_domain['category']
                threat_data['threat_feeds'].append('domain_blocklist')

        return threat_data

    def _check_anonymizer(self, ip: str) -> Dict:
        """Check if IP is associated with anonymization services"""
        # Simulated check
        try:
            ip_obj = ipaddress.ip_address(ip)

            # Tor exit nodes (simulated range)
            if ip_obj in ipaddress.ip_network('*************/24'):
                return {
                    'is_tor_exit': True,
                    'anonymizer_type': 'tor',
                    'reputation_score': 30
                }

            # VPN providers (simulated)
            if ip_obj in ipaddress.ip_network('************/24'):
                return {
                    'is_vpn': True,
                    'anonymizer_type': 'vpn',
                    'reputation_score': 50
                }

        except:
            pass

        return {}


class TechnicalEnricher:
    """Enriches entities with technical details"""

    def enrich(self, entity: Dict[str, Any]) -> Dict[str, Any]:
        """Add technical enrichment to entity"""
        entity_type = entity.get('type')
        entity_value = entity.get('value')

        technical_data = {}

        if entity_type == 'ip_address':
            technical_data.update(self._enrich_ip(entity_value))
        elif entity_type == 'domain':
            technical_data.update(self._enrich_domain(entity_value))
        elif entity_type == 'port':
            technical_data.update(self._enrich_port(entity_value))
        elif entity_type == 'hash':
            technical_data.update(self._enrich_hash(entity_value))

        return technical_data

    def _enrich_ip(self, ip: str) -> Dict:
        """Enrich IP address with technical details"""
        data = {}

        try:
            ip_obj = ipaddress.ip_address(ip)

            data['ip_version'] = 4 if isinstance(ip_obj, ipaddress.IPv4Address) else 6
            data['is_private'] = ip_obj.is_private
            data['is_global'] = ip_obj.is_global
            data['is_multicast'] = ip_obj.is_multicast
            data['is_loopback'] = ip_obj.is_loopback

            # Simulated GeoIP
            if ip_obj.is_global:
                data['geo'] = {
                    'country': 'US',
                    'city': 'New York',
                    'latitude': 40.7128,
                    'longitude': -74.0060
                }

                # Simulated ASN
                data['asn'] = {
                    'number': 'AS15169',
                    'organization': 'Google LLC'
                }

        except:
            pass

        return data

    def _enrich_domain(self, domain: str) -> Dict:
        """Enrich domain with technical details"""
        return {
            'tld': domain.split('.')[-1] if '.' in domain else None,
            'subdomain_count': domain.count('.'),
            'length': len(domain),
            'has_hyphen': '-' in domain,
            'has_numbers': any(c.isdigit() for c in domain)
        }

    def _enrich_port(self, port_str: str) -> Dict:
        """Enrich port with service information"""
        try:
            port = int(port_str)

            well_known_ports = {
                21: 'ftp', 22: 'ssh', 23: 'telnet', 25: 'smtp',
                53: 'dns', 80: 'http', 110: 'pop3', 143: 'imap',
                443: 'https', 445: 'smb', 3306: 'mysql', 3389: 'rdp',
                5432: 'postgresql', 8080: 'http-alt', 8443: 'https-alt'
            }

            data = {
                'port_number': port,
                'port_type': 'well_known' if port < 1024 else 'registered' if port < 49152 else 'dynamic'
            }

            if port in well_known_ports:
                data['service'] = well_known_ports[port]
                data['is_encrypted'] = port in [22, 443, 8443, 3389]

            return data

        except:
            return {}

    def _enrich_hash(self, hash_value: str) -> Dict:
        """Enrich hash with type information"""
        hash_len = len(hash_value)

        hash_types = {
            32: 'md5',
            40: 'sha1',
            64: 'sha256',
            128: 'sha512'
        }

        return {
            'hash_type': hash_types.get(hash_len, 'unknown'),
            'hash_length': hash_len
        }


class SecurityZoneDetector:
    """Detects and assigns security zones to entities"""

    def __init__(self):
        self.zones = {
            'production': {
                'trust_level': 80,
                'subnets': ['********/16'],
                'hostname_patterns': ['PROD-*', '*-PRD-*', 'P-*'],
                'criticality': 'high'
            },
            'dmz': {
                'trust_level': 30,
                'subnets': ['**********/24'],
                'hostname_patterns': ['DMZ-*', 'WEB-*', 'PROXY-*'],
                'criticality': 'medium'
            },
            'development': {
                'trust_level': 20,
                'subnets': ['***********/16'],
                'hostname_patterns': ['DEV-*', 'TEST-*', 'QA-*'],
                'criticality': 'low'
            },
            'management': {
                'trust_level': 90,
                'subnets': ['10.0.0.0/24'],
                'hostname_patterns': ['MGMT-*', 'DC-*', 'AD-*'],
                'criticality': 'critical'
            },
            'external': {
                'trust_level': 0,
                'subnets': [],
                'hostname_patterns': [],
                'criticality': 'unknown'
            }
        }

    def enrich(self, entity: Dict[str, Any]) -> Dict[str, Any]:
        """Detect security zone for entity"""
        entity_type = entity.get('type')
        entity_value = entity.get('value')

        zone_data = {
            'zone': 'external',
            'trust_level': 0,
            'confidence': 0.5
        }

        if entity_type == 'hostname':
            detected_zone = self._detect_by_hostname(entity_value)
            if detected_zone:
                zone_data.update(detected_zone)

        elif entity_type == 'ip_address':
            detected_zone = self._detect_by_ip(entity_value)
            if detected_zone:
                zone_data.update(detected_zone)

        return zone_data

    def _detect_by_hostname(self, hostname: str) -> Optional[Dict]:
        """Detect zone by hostname patterns"""
        hostname_upper = hostname.upper()

        for zone_name, zone_config in self.zones.items():
            for pattern in zone_config['hostname_patterns']:
                # Simple pattern matching (could use fnmatch for wildcards)
                pattern_check = pattern.replace('*', '')
                if pattern.startswith('*') and hostname_upper.endswith(pattern_check):
                    return {
                        'zone': zone_name,
                        'trust_level': zone_config['trust_level'],
                        'confidence': 0.9,
                        'detection_method': 'hostname_pattern'
                    }
                elif pattern.endswith('*') and hostname_upper.startswith(pattern_check):
                    return {
                        'zone': zone_name,
                        'trust_level': zone_config['trust_level'],
                        'confidence': 0.9,
                        'detection_method': 'hostname_pattern'
                    }

        return None

    def _detect_by_ip(self, ip: str) -> Optional[Dict]:
        """Detect zone by IP subnet"""
        try:
            ip_obj = ipaddress.ip_address(ip)

            for zone_name, zone_config in self.zones.items():
                for subnet_str in zone_config['subnets']:
                    subnet = ipaddress.ip_network(subnet_str)
                    if ip_obj in subnet:
                        return {
                            'zone': zone_name,
                            'trust_level': zone_config['trust_level'],
                            'confidence': 0.95,
                            'detection_method': 'subnet_match'
                        }

            # If public IP, it's external
            if ip_obj.is_global:
                return {
                    'zone': 'external',
                    'trust_level': 0,
                    'confidence': 1.0,
                    'detection_method': 'public_ip'
                }

        except:
            pass

        return None


class BaselineAnalyzer:
    """Analyzes entity behavior against baselines"""

    def __init__(self):
        # Simulated baseline data
        self.baselines = {}

    def enrich(self, entity: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze entity against behavioral baseline"""
        entity_id = f"{entity.get('type')}:{entity.get('value')}"

        # Simulated baseline analysis
        baseline_data = {
            'has_baseline': entity_id in self.baselines,
            'baseline_period': '30_days',
            'anomaly_score': random.uniform(0, 30)  # Simulated
        }

        if baseline_data['has_baseline']:
            baseline_data['deviation'] = random.choice(['normal', 'slight', 'moderate', 'high'])
            baseline_data['confidence'] = random.uniform(0.7, 0.95)

        return baseline_data


class CTIContextEnricher:
    """Enriches entities with CTI (Cyber Threat Intelligence) context"""

    def __init__(self, redis_client):
        self.redis_client = redis_client

    async def enrich(self, entity: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich entity with CTI data from Redis cache"""
        entity_type = entity.get('type')
        entity_value = entity.get('value')

        if not entity_type or not entity_value or not self.redis_client:
            return {'cti_found': False}

        # Map entity types to CTI types
        cti_type_mapping = {
            'ip_address': 'ip',
            'domain': 'domain',
            'url': 'url',
            'hash': 'hash',
            'email': 'email',
            'file_hash': 'hash'
        }

        cti_type = cti_type_mapping.get(entity_type)
        if not cti_type:
            return {'cti_found': False}

        # Look up CTI data in Redis
        cache_key = f"cti:{cti_type}:{entity_value}"

        try:
            cti_data = await self.redis_client.get(cache_key)

            if cti_data:
                cti_info = json.loads(cti_data)

                # Build comprehensive CTI context
                cti_context = {
                    'cti_found': True,
                    'threat_level': self._calculate_threat_level(cti_info),
                    'source': cti_info.get('source', 'unknown'),
                    'confidence': cti_info.get('confidence', 0),
                    'tags': cti_info.get('tags', []),
                    'first_seen': cti_info.get('first_seen'),
                    'last_seen': cti_info.get('last_seen'),
                    'references': cti_info.get('references', [])
                }

                # Add specific threat details
                if cti_info.get('malware'):
                    cti_context['malware'] = cti_info['malware']

                if cti_info.get('malware_family'):
                    cti_context['malware_family'] = cti_info['malware_family']

                if cti_info.get('mitre_attack'):
                    cti_context['mitre_attack'] = cti_info['mitre_attack']
                    cti_context['attack_patterns'] = self._get_attack_patterns(cti_info['mitre_attack'])

                if cti_info.get('pulse_name'):
                    cti_context['threat_campaign'] = cti_info['pulse_name']

                if cti_info.get('threat_type'):
                    cti_context['threat_type'] = cti_info['threat_type']

                # Add risk indicators
                cti_context['risk_indicators'] = self._extract_risk_indicators(cti_info)

                # Add recommended actions
                cti_context['recommended_actions'] = self._get_recommended_actions(cti_info)

                return cti_context

            else:
                # Check if entity is in allowlist
                allowlist_key = f"allowlist:{cti_type}:{entity_value}"
                is_allowlisted = await self.redis_client.get(allowlist_key)

                if is_allowlisted:
                    return {
                        'cti_found': False,
                        'allowlisted': True,
                        'threat_level': 'safe'
                    }

                return {'cti_found': False}

        except Exception as e:
            return {
                'cti_found': False,
                'error': str(e)
            }

    def _calculate_threat_level(self, cti_info: Dict) -> str:
        """Calculate threat level based on CTI data"""
        confidence = cti_info.get('confidence', 0)

        # Check for specific high-risk indicators
        if cti_info.get('malware') or cti_info.get('malware_family'):
            if confidence >= 80:
                return 'critical'
            elif confidence >= 60:
                return 'high'
            else:
                return 'medium'

        # Check for C2 or botnet
        tags = cti_info.get('tags', [])
        if any(tag in tags for tag in ['c2', 'botnet', 'ransomware']):
            return 'critical'

        # Base on confidence score
        if confidence >= 90:
            return 'critical'
        elif confidence >= 75:
            return 'high'
        elif confidence >= 50:
            return 'medium'
        else:
            return 'low'

    def _get_attack_patterns(self, mitre_ids: List[str]) -> List[Dict]:
        """Get MITRE ATT&CK pattern details"""
        patterns = []

        # Simplified MITRE mapping (in production, would query MITRE database)
        mitre_map = {
            'T1055': {'name': 'Process Injection', 'tactic': 'Defense Evasion'},
            'T1003': {'name': 'OS Credential Dumping', 'tactic': 'Credential Access'},
            'T1486': {'name': 'Data Encrypted for Impact', 'tactic': 'Impact'},
            'T1071': {'name': 'Application Layer Protocol', 'tactic': 'Command and Control'},
            'T1082': {'name': 'System Information Discovery', 'tactic': 'Discovery'}
        }

        for mitre_id in mitre_ids:
            if mitre_id in mitre_map:
                patterns.append({
                    'technique_id': mitre_id,
                    'technique_name': mitre_map[mitre_id]['name'],
                    'tactic': mitre_map[mitre_id]['tactic']
                })

        return patterns

    def _extract_risk_indicators(self, cti_info: Dict) -> List[str]:
        """Extract risk indicators from CTI data"""
        indicators = []

        # Check confidence level
        if cti_info.get('confidence', 0) >= 80:
            indicators.append('high_confidence_threat')

        # Check for malware
        if cti_info.get('malware'):
            indicators.append(f"known_malware:{cti_info['malware']}")

        # Check tags for specific threats
        tags = cti_info.get('tags', [])
        threat_tags = ['ransomware', 'apt', 'botnet', 'c2', 'phishing', 'exploit']
        for tag in tags:
            if tag.lower() in threat_tags:
                indicators.append(f"threat_type:{tag}")

        # Check recency
        if cti_info.get('last_seen'):
            try:
                last_seen = datetime.fromisoformat(cti_info['last_seen'].replace('Z', '+00:00'))
                if (datetime.utcnow() - last_seen.replace(tzinfo=None)) < timedelta(days=7):
                    indicators.append('recently_active')
            except:
                pass

        # Check if part of active campaign
        if cti_info.get('pulse_name'):
            indicators.append('active_campaign')

        return indicators

    def _get_recommended_actions(self, cti_info: Dict) -> List[str]:
        """Get recommended actions based on CTI data"""
        actions = []
        threat_level = self._calculate_threat_level(cti_info)

        # Base actions on threat level
        if threat_level == 'critical':
            actions.extend([
                'immediate_isolation',
                'forensic_investigation',
                'threat_hunt_initiation',
                'executive_notification'
            ])
        elif threat_level == 'high':
            actions.extend([
                'block_communication',
                'enhanced_monitoring',
                'incident_response_team_alert'
            ])
        elif threat_level == 'medium':
            actions.extend([
                'monitor_closely',
                'sandbox_analysis',
                'user_notification'
            ])
        else:
            actions.append('continue_monitoring')

        # Specific actions based on threat type
        if cti_info.get('malware'):
            actions.append('antivirus_scan')
            actions.append('memory_analysis')

        if any(tag in cti_info.get('tags', []) for tag in ['c2', 'botnet']):
            actions.append('network_traffic_analysis')
            actions.append('check_persistence_mechanisms')

        if 'ransomware' in cti_info.get('tags', []):
            actions.append('backup_verification')
            actions.append('disable_user_account')

        return list(set(actions))  # Remove duplicates