"""
Pattern Federation and Community Exchange System for SIEMLess v2.0
Enables pattern sharing across organizations and community collaboration
"""

import json
import asyncio
import aiohttp
import hashlib
import jwt
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from uuid import uuid4
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend
import logging

logger = logging.getLogger(__name__)

class PatternFederation:
    """Manages pattern federation and community exchange"""

    def __init__(self, storage, marketplace, config: Dict):
        self.storage = storage
        self.marketplace = marketplace
        self.config = config
        self.federation_nodes = []
        self.trusted_peers = set()
        self.signing_key = None
        self.verification_keys = {}

    async def initialize(self):
        """Initialize federation system"""
        # Generate or load signing keys
        await self._initialize_keys()

        # Connect to federation network
        await self._connect_to_federation()

        # Start federation sync task
        asyncio.create_task(self._federation_sync_loop())

    async def share_pattern(self, pattern_id: str, share_config: Dict) -> Dict:
        """Share a pattern with the community"""
        pattern = await self.storage.get_pattern(pattern_id)
        if not pattern:
            raise ValueError(f"Pattern {pattern_id} not found")

        # Sanitize pattern for sharing
        sanitized = self._sanitize_for_sharing(pattern)

        # Add sharing metadata
        sharing_package = {
            'pattern': sanitized,
            'shared_by': self.config['organization_id'],
            'shared_at': datetime.utcnow().isoformat(),
            'sharing_config': share_config,
            'signature': await self._sign_pattern(sanitized)
        }

        # Determine sharing scope
        if share_config.get('scope') == 'public':
            # Share publicly
            result = await self._share_public(sharing_package)
        elif share_config.get('scope') == 'federation':
            # Share with federation
            result = await self._share_with_federation(sharing_package)
        elif share_config.get('scope') == 'selective':
            # Share with specific organizations
            result = await self._share_selective(
                sharing_package,
                share_config.get('recipients', [])
            )
        else:
            raise ValueError(f"Invalid sharing scope: {share_config.get('scope')}")

        # Track sharing
        await self._track_sharing(pattern_id, share_config, result)

        return result

    async def import_pattern(self, source_url: str, pattern_id: str) -> Dict:
        """Import a pattern from community or federation"""
        # Fetch pattern from source
        pattern_package = await self._fetch_pattern(source_url, pattern_id)

        # Verify signature and authenticity
        if not await self._verify_pattern(pattern_package):
            raise ValueError("Pattern verification failed")

        # Check pattern safety
        safety_check = await self._check_pattern_safety(pattern_package['pattern'])
        if not safety_check['safe']:
            raise ValueError(f"Pattern safety check failed: {safety_check['reason']}")

        # Adapt pattern to local environment
        adapted = await self._adapt_pattern(pattern_package['pattern'])

        # Test pattern
        if self.config.get('test_before_import', True):
            test_result = await self._test_imported_pattern(adapted)
            if not test_result['passed']:
                raise ValueError(f"Pattern testing failed: {test_result['reason']}")

        # Store pattern locally
        local_id = await self.storage.store_pattern(adapted)

        # Track import
        await self._track_import(pattern_id, source_url, local_id)

        # Send thanks to contributor
        await self._send_thanks(pattern_package['shared_by'])

        return {
            'success': True,
            'local_pattern_id': local_id,
            'original_pattern_id': pattern_id,
            'shared_by': pattern_package['shared_by'],
            'imported_at': datetime.utcnow().isoformat()
        }

    async def discover_patterns(self, criteria: Dict) -> List[Dict]:
        """Discover patterns from community based on criteria"""
        discovered = []

        # Search in public repository
        public_patterns = await self._search_public_repository(criteria)
        discovered.extend(public_patterns)

        # Search in federation
        if self.federation_nodes:
            federation_patterns = await self._search_federation(criteria)
            discovered.extend(federation_patterns)

        # Filter and rank results
        filtered = self._filter_patterns(discovered, criteria)
        ranked = self._rank_patterns(filtered)

        return ranked

    async def sync_with_community(self):
        """Synchronize patterns with community repository"""
        # Get list of local patterns marked for sharing
        sharing_patterns = await self.storage.search_patterns({
            'distribution.sharing_enabled': True
        })

        # Get list of community patterns we might want
        community_patterns = await self.discover_patterns({
            'min_rating': 4.0,
            'min_uses': 100
        })

        # Sync operations
        sync_report = {
            'patterns_shared': 0,
            'patterns_imported': 0,
            'patterns_updated': 0
        }

        # Share new patterns
        for pattern in sharing_patterns:
            if not await self._is_already_shared(pattern['pattern_id']):
                await self.share_pattern(pattern['pattern_id'], {
                    'scope': 'public',
                    'license': pattern.get('distribution', {}).get('license', 'MIT')
                })
                sync_report['patterns_shared'] += 1

        # Import highly-rated community patterns
        for community_pattern in community_patterns[:10]:  # Limit to top 10
            if not await self._have_pattern(community_pattern['pattern_id']):
                try:
                    await self.import_pattern(
                        community_pattern['source_url'],
                        community_pattern['pattern_id']
                    )
                    sync_report['patterns_imported'] += 1
                except Exception as e:
                    logger.error(f"Failed to import pattern: {e}")

        return sync_report

    async def _initialize_keys(self):
        """Initialize cryptographic keys for federation"""
        # Generate RSA key pair for signing
        self.signing_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=default_backend()
        )

        # Get public key for sharing
        self.public_key = self.signing_key.public_key()

        # Register public key with federation
        await self._register_public_key()

    async def _connect_to_federation(self):
        """Connect to federation network"""
        federation_urls = self.config.get('federation_nodes', [])

        for url in federation_urls:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{url}/federation/join",
                        json={
                            'organization_id': self.config['organization_id'],
                            'public_key': self._serialize_public_key(),
                            'capabilities': ['share', 'import', 'sync']
                        }
                    ) as response:
                        if response.status == 200:
                            node_info = await response.json()
                            self.federation_nodes.append(node_info)
                            logger.info(f"Connected to federation node: {url}")
            except Exception as e:
                logger.error(f"Failed to connect to federation node {url}: {e}")

    async def _federation_sync_loop(self):
        """Background task for federation synchronization"""
        while True:
            try:
                await asyncio.sleep(3600)  # Sync every hour
                await self.sync_with_community()
            except Exception as e:
                logger.error(f"Federation sync error: {e}")

    def _sanitize_for_sharing(self, pattern: Dict) -> Dict:
        """Remove sensitive data before sharing"""
        sanitized = pattern.copy()

        # Remove organization-specific data
        sensitive_fields = [
            'internal_notes', 'customer_data', 'api_keys',
            'credentials', 'internal_ips', 'proprietary_logic'
        ]

        def remove_sensitive(obj):
            if isinstance(obj, dict):
                return {
                    k: remove_sensitive(v)
                    for k, v in obj.items()
                    if k not in sensitive_fields
                }
            elif isinstance(obj, list):
                return [remove_sensitive(item) for item in obj]
            else:
                return obj

        return remove_sensitive(sanitized)

    async def _sign_pattern(self, pattern: Dict) -> str:
        """Create cryptographic signature for pattern"""
        # Serialize pattern
        pattern_bytes = json.dumps(pattern, sort_keys=True).encode()

        # Create signature
        signature = self.signing_key.sign(
            pattern_bytes,
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )

        return signature.hex()

    async def _verify_pattern(self, pattern_package: Dict) -> bool:
        """Verify pattern signature and authenticity"""
        try:
            # Get public key for organization
            org_id = pattern_package['shared_by']
            public_key = await self._get_public_key(org_id)

            if not public_key:
                logger.warning(f"No public key found for {org_id}")
                return False

            # Verify signature
            pattern_bytes = json.dumps(
                pattern_package['pattern'],
                sort_keys=True
            ).encode()

            signature = bytes.fromhex(pattern_package['signature'])

            public_key.verify(
                signature,
                pattern_bytes,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )

            return True

        except Exception as e:
            logger.error(f"Pattern verification failed: {e}")
            return False

    async def _check_pattern_safety(self, pattern: Dict) -> Dict:
        """Check pattern for safety and malicious content"""
        safety_result = {
            'safe': True,
            'warnings': [],
            'reason': None
        }

        pattern_str = json.dumps(pattern)

        # Check for dangerous patterns
        dangerous_patterns = [
            'eval(', 'exec(', '__import__', 'os.system',
            'subprocess', 'compile(', 'globals()', 'locals()'
        ]

        for dangerous in dangerous_patterns:
            if dangerous in pattern_str:
                safety_result['safe'] = False
                safety_result['reason'] = f"Contains dangerous operation: {dangerous}"
                return safety_result

        # Check for resource exhaustion
        if 'while True' in pattern_str or 'recursion' in pattern_str:
            safety_result['warnings'].append("Potential resource exhaustion")

        # Check pattern complexity
        if len(pattern_str) > 1000000:  # 1MB limit
            safety_result['safe'] = False
            safety_result['reason'] = "Pattern too large"

        return safety_result

    async def _adapt_pattern(self, pattern: Dict) -> Dict:
        """Adapt imported pattern to local environment"""
        adapted = pattern.copy()

        # Update field mappings for local log format
        if 'pattern_data' in adapted:
            pattern_data = adapted['pattern_data']

            # Adapt field names
            field_mapping = self.config.get('field_mapping', {})
            if field_mapping and 'fields' in pattern_data:
                pattern_data['fields'] = [
                    field_mapping.get(field, field)
                    for field in pattern_data['fields']
                ]

        # Add local metadata
        adapted['metadata']['imported'] = True
        adapted['metadata']['import_source'] = 'community'
        adapted['metadata']['adapted_at'] = datetime.utcnow().isoformat()

        return adapted

    async def _test_imported_pattern(self, pattern: Dict) -> Dict:
        """Test imported pattern before activation"""
        # Simplified testing - would use full pattern tester in production
        test_result = {'passed': True}

        try:
            # Basic validation
            if 'pattern_data' not in pattern:
                test_result['passed'] = False
                test_result['reason'] = "Missing pattern_data"

            # Check required fields
            required = ['pattern_type', 'pattern_name']
            for field in required:
                if field not in pattern:
                    test_result['passed'] = False
                    test_result['reason'] = f"Missing required field: {field}"
                    break

        except Exception as e:
            test_result['passed'] = False
            test_result['reason'] = str(e)

        return test_result

    async def _share_public(self, sharing_package: Dict) -> Dict:
        """Share pattern publicly"""
        # Post to public repository
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.config['public_repository_url']}/patterns",
                json=sharing_package
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return {
                        'success': True,
                        'public_id': result['pattern_id'],
                        'url': result['pattern_url']
                    }
                else:
                    return {
                        'success': False,
                        'error': f"Failed to share: {response.status}"
                    }

    async def _share_with_federation(self, sharing_package: Dict) -> Dict:
        """Share pattern with federation members"""
        results = []

        for node in self.federation_nodes:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{node['url']}/patterns/federated",
                        json=sharing_package
                    ) as response:
                        if response.status == 200:
                            results.append({
                                'node': node['id'],
                                'success': True
                            })
            except Exception as e:
                results.append({
                    'node': node['id'],
                    'success': False,
                    'error': str(e)
                })

        return {
            'success': len([r for r in results if r['success']]) > 0,
            'federation_results': results
        }

    async def _share_selective(self, sharing_package: Dict,
                              recipients: List[str]) -> Dict:
        """Share pattern with specific organizations"""
        results = []

        for recipient_id in recipients:
            # Get recipient endpoint
            endpoint = await self._get_recipient_endpoint(recipient_id)

            if endpoint:
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.post(
                            f"{endpoint}/patterns/shared",
                            json=sharing_package
                        ) as response:
                            results.append({
                                'recipient': recipient_id,
                                'success': response.status == 200
                            })
                except Exception as e:
                    results.append({
                        'recipient': recipient_id,
                        'success': False,
                        'error': str(e)
                    })

        return {
            'success': len([r for r in results if r['success']]) > 0,
            'selective_results': results
        }

    async def _fetch_pattern(self, source_url: str, pattern_id: str) -> Dict:
        """Fetch pattern from remote source"""
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{source_url}/patterns/{pattern_id}"
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    raise ValueError(f"Failed to fetch pattern: {response.status}")

    async def _search_public_repository(self, criteria: Dict) -> List[Dict]:
        """Search public pattern repository"""
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.config['public_repository_url']}/patterns/search",
                json=criteria
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return []

    async def _search_federation(self, criteria: Dict) -> List[Dict]:
        """Search federation for patterns"""
        all_results = []

        for node in self.federation_nodes:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{node['url']}/patterns/search",
                        json=criteria
                    ) as response:
                        if response.status == 200:
                            results = await response.json()
                            all_results.extend(results)
            except Exception as e:
                logger.error(f"Federation search error: {e}")

        return all_results

    def _filter_patterns(self, patterns: List[Dict], criteria: Dict) -> List[Dict]:
        """Filter patterns based on criteria"""
        filtered = []

        for pattern in patterns:
            # Check minimum rating
            if 'min_rating' in criteria:
                if pattern.get('rating', 0) < criteria['min_rating']:
                    continue

            # Check minimum uses
            if 'min_uses' in criteria:
                if pattern.get('usage_count', 0) < criteria['min_uses']:
                    continue

            # Check pattern type
            if 'pattern_type' in criteria:
                if pattern.get('pattern_type') != criteria['pattern_type']:
                    continue

            filtered.append(pattern)

        return filtered

    def _rank_patterns(self, patterns: List[Dict]) -> List[Dict]:
        """Rank patterns by relevance and quality"""
        for pattern in patterns:
            # Calculate ranking score
            score = 0

            # Factor in rating
            score += pattern.get('rating', 0) * 2

            # Factor in usage
            score += min(pattern.get('usage_count', 0) / 100, 10)

            # Factor in freshness
            if 'updated_at' in pattern:
                age_days = (datetime.utcnow() -
                          datetime.fromisoformat(pattern['updated_at'])).days
                score += max(0, 10 - age_days / 30)

            # Factor in community endorsements
            score += pattern.get('endorsements', 0) * 0.5

            pattern['ranking_score'] = score

        # Sort by ranking score
        patterns.sort(key=lambda x: x['ranking_score'], reverse=True)

        return patterns

    async def _track_sharing(self, pattern_id: str, share_config: Dict, result: Dict):
        """Track pattern sharing activity"""
        # Store sharing record
        sharing_record = {
            'pattern_id': pattern_id,
            'shared_at': datetime.utcnow().isoformat(),
            'share_config': share_config,
            'result': result
        }

        # TODO: Store in database
        logger.info(f"Pattern {pattern_id} shared: {result}")

    async def _track_import(self, original_id: str, source_url: str, local_id: str):
        """Track pattern import activity"""
        import_record = {
            'original_pattern_id': original_id,
            'local_pattern_id': local_id,
            'source_url': source_url,
            'imported_at': datetime.utcnow().isoformat()
        }

        # TODO: Store in database
        logger.info(f"Pattern {original_id} imported as {local_id}")

    async def _send_thanks(self, contributor_id: str):
        """Send thanks to pattern contributor"""
        # TODO: Implement reputation/thanks system
        logger.info(f"Thanks sent to {contributor_id}")

    async def _is_already_shared(self, pattern_id: str) -> bool:
        """Check if pattern is already shared"""
        # TODO: Check sharing records
        return False

    async def _have_pattern(self, pattern_id: str) -> bool:
        """Check if we already have this pattern"""
        pattern = await self.storage.get_pattern(pattern_id)
        return pattern is not None

    async def _get_public_key(self, org_id: str):
        """Get public key for organization"""
        # TODO: Fetch from key registry
        return self.verification_keys.get(org_id)

    async def _register_public_key(self):
        """Register public key with federation"""
        # TODO: Register with key registry
        pass

    async def _get_recipient_endpoint(self, recipient_id: str) -> Optional[str]:
        """Get API endpoint for recipient organization"""
        # TODO: Look up from registry
        return None

    def _serialize_public_key(self) -> str:
        """Serialize public key for sharing"""
        return self.public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        ).decode()