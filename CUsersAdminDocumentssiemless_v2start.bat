@echo off
echo ===============================================
echo        SIEMLess v2.0 - Starting Platform       
echo ===============================================

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo Error: Docker is not running!
    echo Please start Docker Desktop and try again.
    pause
    exit /b 1
)

REM Navigate to engines directory
cd engines

REM Stop existing containers
echo Stopping existing containers...
docker-compose down

REM Start services
echo Starting all services...
docker-compose up -d --build

REM Wait for services
echo Waiting for services to be ready...
timeout /t 10 /nobreak >nul

REM Check service health
echo.
echo Checking service status...
docker-compose ps

echo.
echo ===============================================
echo        SIEMLess v2.0 - Access Points          
echo ===============================================
echo Frontend: http://localhost:3000
echo Intelligence API: http://localhost:8001/docs
echo Backend API: http://localhost:8002/docs
echo PostgreSQL: localhost:5433
echo Redis: localhost:6380
echo ===============================================
echo.
echo To view logs: docker-compose logs -f [service_name]
echo To stop: docker-compose down
echo.
pause
