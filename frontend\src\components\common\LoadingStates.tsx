/**
 * Loading States Components
 * Reusable loading indicators and skeleton loaders
 */

import React from 'react'

// Simple loading spinner (already exists, but adding more variants)
export const LoadingSpinner: React.FC<{ size?: 'sm' | 'md' | 'lg', className?: string }> = ({
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }

  return (
    <div
      className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]} ${className}`}
    />
  )
}

// Skeleton loader for cards
export const SkeletonCard: React.FC = () => (
  <div className="animate-pulse bg-white rounded-lg shadow p-6">
    <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
    <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
    <div className="h-4 bg-gray-200 rounded w-full"></div>
  </div>
)

// Skeleton loader for table rows
export const SkeletonTableRow: React.FC = () => (
  <tr className="animate-pulse">
    <td className="px-4 py-3">
      <div className="h-4 bg-gray-200 rounded"></div>
    </td>
    <td className="px-4 py-3">
      <div className="h-4 bg-gray-200 rounded"></div>
    </td>
    <td className="px-4 py-3">
      <div className="h-4 bg-gray-200 rounded"></div>
    </td>
  </tr>
)

// Full-page loader
export const PageLoader: React.FC<{ message?: string }> = ({ message }) => (
  <div className="flex items-center justify-center h-screen">
    <div className="text-center">
      <LoadingSpinner size="lg" />
      {message && <p className="mt-4 text-gray-600">{message}</p>}
    </div>
  </div>
)

// Loading overlay (for sections)
export const LoadingOverlay: React.FC<{ message?: string }> = ({ message }) => (
  <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50 rounded-lg">
    <div className="text-center">
      <LoadingSpinner size="lg" />
      {message && <p className="mt-4 text-gray-600 font-medium">{message}</p>}
    </div>
  </div>
)

// Mini alert list skeleton
export const SkeletonAlertList: React.FC = () => (
  <div className="space-y-2">
    {[1, 2, 3].map((i) => (
      <div key={i} className="animate-pulse bg-gray-50 rounded p-3">
        <div className="flex items-center justify-between mb-2">
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          <div className="h-4 bg-gray-200 rounded w-16"></div>
        </div>
        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
      </div>
    ))}
  </div>
)
