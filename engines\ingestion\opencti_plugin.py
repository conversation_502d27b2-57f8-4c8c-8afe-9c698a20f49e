"""
OpenCTI CTI Plugin
Universal plugin implementation for OpenCTI threat intelligence platform
"""

import aiohttp
from typing import List, Optional
from datetime import datetime, timedelta
from cti_source_plugin import (
    CTISourcePlugin, CTIIndicator, IndicatorType, ThreatType
)


class OpenCTIPlugin(CTISourcePlugin):
    """
    OpenCTI CTI Source Plugin

    Fetches threat intelligence from OpenCTI platform
    Enterprise/Community threat intelligence platform with STIX 2.1 support
    """

    def __init__(self, config: dict, logger=None):
        super().__init__(config, logger)
        # OpenCTI URL should include /graphql endpoint
        self.base_url = config.get('api_url', 'http://localhost:8080').rstrip('/')
        self.graphql_url = f"{self.base_url}/graphql" if not self.base_url.endswith('/graphql') else self.base_url

        # OpenCTI uses Bearer token authentication
        self.auth_token = config.get('api_key') or config.get('token')

    def get_source_name(self) -> str:
        return "opencti"

    def get_source_type(self) -> str:
        return self.config.get('source_type', 'internal')  # Can be internal or commercial

    async def validate_credentials(self) -> bool:
        """Test connection to OpenCTI"""
        try:
            async with aiohttp.ClientSession() as session:
                # Simple GraphQL query to test connectivity
                query = """
                query {
                    about {
                        version
                    }
                }
                """

                headers = {
                    'Authorization': f'Bearer {self.auth_token}',
                    'Content-Type': 'application/json'
                }

                async with session.post(
                    self.graphql_url,
                    json={'query': query},
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        if 'data' in data and 'about' in data['data']:
                            self.logger.info(f"OpenCTI credentials validated (version: {data['data']['about'].get('version', 'unknown')})")
                            return True

                    self.logger.warning(f"OpenCTI validation returned status {response.status}")
                    return False

        except Exception as e:
            self.logger.error(f"OpenCTI credential validation error: {e}")
            return False

    async def fetch_indicators(
        self,
        since: Optional[datetime] = None,
        indicator_types: Optional[List[IndicatorType]] = None,
        limit: int = 1000
    ) -> List[CTIIndicator]:
        """
        Fetch indicators from OpenCTI

        Args:
            since: Only fetch indicators updated after this timestamp
            indicator_types: Filter by specific indicator types
            limit: Maximum number of indicators to return

        Returns:
            List of standardized CTI indicators
        """
        indicators = []

        try:
            # Fetch indicators using GraphQL
            opencti_indicators = await self._fetch_opencti_indicators(since, limit)

            for indicator in opencti_indicators:
                standardized = self._standardize_indicator(indicator)
                if standardized:
                    # Filter by type if specified
                    if indicator_types:
                        type_values = [t.value for t in indicator_types]
                        if standardized.indicator_type in type_values:
                            indicators.append(standardized)
                    else:
                        indicators.append(standardized)

            self.logger.info(f"OpenCTI: Fetched {len(indicators)} indicators")

        except Exception as e:
            self.logger.error(f"OpenCTI fetch error: {e}")

        return indicators[:limit]

    async def _fetch_opencti_indicators(self, since: Optional[datetime], limit: int) -> List[dict]:
        """Fetch indicators from OpenCTI using GraphQL"""
        try:
            # Build filters
            filters = []
            if since:
                filters.append({
                    "key": "updated_at",
                    "values": [since.isoformat()],
                    "operator": "gt"
                })

            # GraphQL query for indicators (OpenCTI 6.x compatible)
            # Note: Simplified query without filters for compatibility
            query = """
            query GetIndicators($first: Int) {
                indicators(first: $first) {
                    edges {
                        node {
                            id
                            pattern
                            pattern_type
                            name
                            description
                            valid_from
                            valid_until
                            x_opencti_score
                            created_at
                            updated_at
                        }
                    }
                }
            }
            """

            variables = {
                "first": limit
            }

            headers = {
                'Authorization': f'Bearer {self.auth_token}',
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.graphql_url,
                    json={'query': query, 'variables': variables},
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        data = await response.json()

                        if 'data' in data and 'indicators' in data['data']:
                            edges = data['data']['indicators'].get('edges', [])
                            indicators = [edge['node'] for edge in edges]
                            self.logger.info(f"Retrieved {len(indicators)} raw indicators from OpenCTI")
                            return indicators
                        else:
                            error = data.get('errors', [])
                            self.logger.error(f"OpenCTI GraphQL error: {error}")
                            return []
                    else:
                        self.logger.error(f"OpenCTI API error: HTTP {response.status}")
                        return []

        except Exception as e:
            self.logger.error(f"Error fetching OpenCTI indicators: {e}")
            return []

    def _standardize_indicator(self, indicator: dict) -> Optional[CTIIndicator]:
        """
        Convert OpenCTI indicator to standardized CTI format

        OpenCTI uses STIX 2.1 patterns
        """
        try:
            pattern = indicator.get('pattern', '')
            pattern_type = indicator.get('pattern_type', 'stix')

            if not pattern or pattern_type != 'stix':
                return None

            # Extract indicator value and type from STIX pattern
            # Examples:
            # [ipv4-addr:value = '*******']
            # [domain-name:value = 'evil.com']
            # [file:hashes.'SHA-256' = 'abc123...']

            indicator_type, indicator_value = self._parse_stix_pattern(pattern)
            if not indicator_type or not indicator_value:
                return None

            # Determine threat type from indicator types
            opencti_types = indicator.get('indicator_types', [])
            threat_type = self._map_opencti_threat_type(opencti_types)

            # Parse timestamps
            valid_from = indicator.get('valid_from')
            if valid_from:
                try:
                    valid_from = datetime.fromisoformat(valid_from.replace('Z', '+00:00'))
                except:
                    valid_from = None

            valid_until = indicator.get('valid_until')
            if valid_until:
                try:
                    valid_until = datetime.fromisoformat(valid_until.replace('Z', '+00:00'))
                except:
                    valid_until = None

            # Extract labels/tags (may not be present in simplified query)
            tags = []

            # Get confidence score
            confidence = indicator.get('x_opencti_score', 50) / 100.0  # Convert 0-100 to 0.0-1.0

            # External references (not in simplified query)
            references = []

            # Build standardized indicator
            return CTIIndicator({
                'indicator_type': indicator_type,
                'indicator_value': indicator_value,
                'threat_type': threat_type,
                'confidence': min(confidence, 1.0),
                'first_seen': valid_from,
                'last_seen': valid_until,
                'tags': tags,
                'description': indicator.get('description', indicator.get('name', '')),
                'source_reference': indicator.get('id', ''),
                'mitre_techniques': [],  # Would need additional query to fetch
                'severity': self._calculate_severity_from_score(indicator.get('x_opencti_score', 50)),
                'raw_data': {
                    'pattern': pattern,
                    'references': references
                }
            })

        except Exception as e:
            self.logger.error(f"Error standardizing OpenCTI indicator: {e}")
            return None

    def _parse_stix_pattern(self, pattern: str) -> tuple:
        """
        Parse STIX pattern to extract indicator type and value

        Returns:
            (indicator_type, indicator_value)
        """
        try:
            # Remove brackets and split
            pattern = pattern.strip('[]')

            # Common STIX patterns
            if 'ipv4-addr:value' in pattern or 'ipv6-addr:value' in pattern:
                # Extract IP
                value = pattern.split("'")[1] if "'" in pattern else pattern.split('"')[1]
                return (IndicatorType.IP.value, value)

            elif 'domain-name:value' in pattern:
                # Extract domain
                value = pattern.split("'")[1] if "'" in pattern else pattern.split('"')[1]
                return (IndicatorType.DOMAIN.value, value)

            elif 'url:value' in pattern:
                # Extract URL
                value = pattern.split("'")[1] if "'" in pattern else pattern.split('"')[1]
                return (IndicatorType.URL.value, value)

            elif 'file:hashes' in pattern:
                # Extract file hash
                value = pattern.split("'")[1] if "'" in pattern else pattern.split('"')[1]
                return (IndicatorType.FILE_HASH.value, value)

            elif 'email-addr:value' in pattern or 'email-message' in pattern:
                # Extract email
                value = pattern.split("'")[1] if "'" in pattern else pattern.split('"')[1]
                return (IndicatorType.EMAIL.value, value)

        except Exception as e:
            self.logger.error(f"Error parsing STIX pattern '{pattern}': {e}")

        return (None, None)

    def _map_opencti_threat_type(self, indicator_types: List[str]) -> str:
        """Map OpenCTI indicator types to standard threat types"""
        if not indicator_types:
            return ThreatType.UNKNOWN.value

        types_lower = [t.lower() for t in indicator_types]

        if any(x in types_lower for x in ['malware', 'malicious-activity']):
            return ThreatType.MALWARE.value
        elif any(x in types_lower for x in ['phishing']):
            return ThreatType.PHISHING.value
        elif any(x in types_lower for x in ['c2', 'command-and-control']):
            return ThreatType.C2.value
        elif any(x in types_lower for x in ['exploit']):
            return ThreatType.EXPLOIT.value
        elif any(x in types_lower for x in ['ransomware']):
            return ThreatType.RANSOMWARE.value
        elif any(x in types_lower for x in ['apt', 'targeted-attack']):
            return ThreatType.APT.value
        elif any(x in types_lower for x in ['botnet']):
            return ThreatType.BOTNET.value

        return ThreatType.UNKNOWN.value

    def _calculate_severity_from_score(self, score: int) -> str:
        """Calculate severity from OpenCTI score (0-100)"""
        if score >= 75:
            return 'high'
        elif score >= 50:
            return 'medium'
        else:
            return 'low'

    async def get_indicator_context(self, indicator_value: str) -> Optional[dict]:
        """Get additional context for an indicator from OpenCTI"""
        try:
            # Search for indicator by pattern
            query = """
            query SearchIndicator($search: String) {
                indicators(search: $search, first: 1) {
                    edges {
                        node {
                            id
                            name
                            description
                            x_opencti_score
                            created_at
                            updated_at
                            observables {
                                edges {
                                    node {
                                        entity_type
                                        observable_value
                                    }
                                }
                            }
                        }
                    }
                }
            }
            """

            variables = {"search": indicator_value}

            headers = {
                'Authorization': f'Bearer {self.auth_token}',
                'Content-Type': 'application/json'
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.graphql_url,
                    json={'query': query, 'variables': variables},
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        data = await response.json()

                        if 'data' in data and 'indicators' in data['data']:
                            edges = data['data']['indicators'].get('edges', [])
                            if edges:
                                node = edges[0]['node']
                                return {
                                    'name': node.get('name'),
                                    'description': node.get('description'),
                                    'score': node.get('x_opencti_score'),
                                    'created': node.get('created_at'),
                                    'updated': node.get('updated_at'),
                                    'opencti_id': node.get('id')
                                }

        except Exception as e:
            self.logger.error(f"OpenCTI context fetch error: {e}")

        return None
