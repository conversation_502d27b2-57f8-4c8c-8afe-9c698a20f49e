import React from 'react'
import { X, Info, Clock, User, FileText } from 'lucide-react'
import { useLayoutStore } from '../../stores/navigationStore'

const RightDrawer: React.FC = () => {
  const { toggleRightDrawer } = useLayoutStore()

  return (
    <div className="w-80 bg-white border-l h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-3 border-b">
        <h3 className="font-medium">Context Panel</h3>
        <button
          onClick={toggleRightDrawer}
          className="p-1 hover:bg-gray-100 rounded"
        >
          <X size={18} />
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {/* Entity Details */}
          <div className="border rounded-lg p-3">
            <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
              <Info size={16} />
              Entity Details
            </h4>
            <div className="text-sm text-gray-600 space-y-1">
              <div>Type: IP Address</div>
              <div>Value: *************</div>
              <div>Risk Score: 75/100</div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="border rounded-lg p-3">
            <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
              <Clock size={16} />
              Recent Activity
            </h4>
            <div className="text-sm text-gray-600 space-y-2">
              <div className="flex items-center justify-between">
                <span>Login attempt</span>
                <span className="text-xs">2m ago</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Data download</span>
                <span className="text-xs">15m ago</span>
              </div>
            </div>
          </div>

          {/* Related Cases */}
          <div className="border rounded-lg p-3">
            <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
              <FileText size={16} />
              Related Cases
            </h4>
            <div className="text-sm text-gray-600 space-y-1">
              <div>CASE-123: Suspicious login</div>
              <div>CASE-456: Data exfiltration</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RightDrawer