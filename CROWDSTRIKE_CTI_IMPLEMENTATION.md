# CrowdStrike CTI Connector Implementation

## Date: October 2, 2025

## Overview

Implemented CrowdStrike CTI connector to treat CrowdStrike INTEL_READ and IOCS_READ scopes as **CTI feeds** (not EDR logs). This completes the CTI aggregation architecture by adding CrowdStrike as a fourth CTI source alongside OTX, OpenCTI, and ThreatFox.

## Problem Solved

**User's Insight**: "some applications like crowdstrike gives me cti through scopes like malware and ioc"

CrowdStrike provides two types of data:
1. **EDR Logs** (EVENT_STREAMS scope) - Device telemetry, process execution, network connections
2. **CTI Feeds** (INTEL + IOCS scopes) - Threat intelligence indicators, custom IOCs, threat actors

Previously, we only handled EDR logs. Now we properly segregate and ingest CTI from CrowdStrike's intelligence scopes.

## Architecture

```
┌──────────────────────────────────────────────────────────────┐
│                   CTI SOURCES (Ingestion)                    │
├──────────────────────────────────────────────────────────────┤
│  • OTX (AlienVault)                                          │
│  • OpenCTI                                                   │
│  • ThreatFox (abuse.ch)                                      │
│  • CrowdStrike INTEL (threat intelligence) ← NEW             │
│  • CrowdStrike IOCS (custom IOCs)          ← NEW             │
└──────────────────────────────────────────────────────────────┘
                            ↓
┌──────────────────────────────────────────────────────────────┐
│                    CTI AGGREGATOR                            │
│              (Deduplication & Merging)                       │
├──────────────────────────────────────────────────────────────┤
│                                                              │
│  Merges same IOC from multiple sources:                     │
│  - OTX: ******* (score: 80, tags: [malware])               │
│  - CrowdStrike: ******* (score: 90, tags: [apt28])         │
│  - ThreatFox: ******* (score: 85, tags: [botnet])          │
│                                                              │
│  MERGED: ******* (score: 90, tags: [malware, apt28,        │
│           botnet], sources: [otx, crowdstrike_cti,          │
│           threatfox])                                        │
└──────────────────────────────────────────────────────────────┘
                            ↓
         ┌──────────────────┴──────────────────┐
         ↓                                      ↓
┌─────────────────────┐              ┌──────────────────────┐
│  SINGLE SOURCE OF   │              │  CTI DATA ROUTER     │
│  TRUTH: entities    │              │  (Segregated)        │
│  table (PostgreSQL) │              ├──────────────────────┤
└─────────────────────┘              │  → cti.enrichment    │
         ↓                            │  → cti.rules         │
┌─────────────────────┐              │  → cti.investigation │
│  REDIS IOC CACHE    │              │  → cti.mitre         │
│  (One-way update)   │              └──────────────────────┘
└─────────────────────┘
```

## Implementation Details

### 1. CrowdStrike CTI Connector

**File**: `engines/ingestion/crowdstrike_cti_integration.py` (450 lines)

**Key Features**:
- OAuth2 authentication with token management
- Three data retrieval methods:
  - `get_intel_indicators()` - INTEL_READ scope (threat intelligence)
  - `get_custom_iocs()` - IOCS_READ scope (custom IOCs)
  - `get_threat_actors()` - Actor profiles for enrichment
- Automatic type mapping (CrowdStrike → standard IOC types)
- Threat score calculation based on confidence and attributes
- Tag extraction from multiple sources

**API Endpoints Used**:
```python
# INTEL_READ scope
/intel/combined/indicators/v1  # Threat intelligence indicators
/intel/combined/actors/v1      # Threat actor profiles

# IOCS_READ scope
/indicators/combined/iocs/v1   # Custom IOCs
```

**Indicator Normalization**:
```python
{
    'id': 'crowdstrike_unique_id',
    'type': 'ip',  # Normalized from 'ipv4'
    'value': '*******',
    'threat_score': 90,  # Calculated from confidence
    'confidence': 'high',
    'tags': ['apt28', 'malware:emotet', 'actor:fancy_bear'],
    'threat_actor': 'FANCY BEAR',
    'malware_family': 'Emotet',
    'campaign': 'phishing_campaign',
    'kill_chain_phases': ['exploitation', 'command-and-control']
}
```

### 2. CTI Manager Integration

**File**: `engines/ingestion/cti_manager.py`

**Changes**:
- Added CrowdStrike CTI connector initialization (lines 100-112)
- Added `_process_crowdstrike_cti()` method (lines 265-326)
- Configured 1-hour update interval
- Integrated with CTI Aggregator for deduplication

**Processing Flow**:
```python
async def _process_crowdstrike_cti(connector):
    # 1. Fetch INTEL indicators (threat intelligence)
    intel_indicators = await connector.get_intel_indicators(limit=100)

    # 2. Fetch custom IOCs
    custom_iocs = await connector.get_custom_iocs(limit=100)

    # 3. Fetch threat actors (for enrichment)
    threat_actors = await connector.get_threat_actors(limit=20)

    # 4. Normalize to standard CTI format
    cti_data = {
        'source': 'crowdstrike_cti',
        'indicators': intel_indicators + custom_iocs,
        'reports': threat_actors,
        'total_items': len(indicators) + len(reports)
    }

    # 5. Send to CTI Aggregator (deduplicates and stores)
    await _publish_cti_data(cti_data)
```

### 3. Configuration

**Environment Variables**:
```bash
# CrowdStrike CTI API credentials (separate from EDR)
CROWDSTRIKE_CLIENT_ID=your_client_id
CROWDSTRIKE_CLIENT_SECRET=your_client_secret
```

**Config Format**:
```python
config = {
    'crowdstrike_cti': {
        'enabled': True,
        'client_id': os.getenv('CROWDSTRIKE_CLIENT_ID'),
        'client_secret': os.getenv('CROWDSTRIKE_CLIENT_SECRET'),
        'base_url': 'https://api.crowdstrike.com'
    }
}
```

## CrowdStrike Scopes Clarification

### CTI Scopes (NEW - This Implementation)

**INTEL_READ**: Threat intelligence indicators
- Malware families
- Threat actors
- IOCs with context
- Campaign information
- **Use Case**: CTI feed for enrichment and detection

**IOCS_READ**: Custom IOCs
- User-defined indicators
- File hashes, IPs, domains
- Custom watchlists
- **Use Case**: Organization-specific threat intelligence

### EDR Scopes (Existing - Log Ingestion)

**EVENT_STREAMS_READ**: Raw EDR telemetry
- Process execution
- Network connections
- File operations
- Registry changes
- **Use Case**: Security event logs for detection and investigation

**Clear Separation**:
```
CrowdStrike INTEL/IOCS → CTI Manager → CTI Aggregator → entities table
                                                       ↓
                                              Redis cache (enrichment)
                                                       ↓
                                              CTI Router (rules, MITRE)

CrowdStrike EVENT_STREAMS → Log Collector → Contextualization → Backend
                                                               ↓
                                                    Events, not entities
```

## Data Flow Example

### Scenario: CrowdStrike identifies new APT28 infrastructure

1. **CrowdStrike Updates INTEL**:
   - New IP: `***********` with high confidence
   - Tags: `apt28`, `fancy_bear`, `c2_server`
   - Threat score: 95

2. **Ingestion Engine Fetches**:
   ```python
   connector.get_intel_indicators()
   # Returns: {type: 'ip', value: '***********', threat_score: 95, ...}
   ```

3. **CTI Manager Processes**:
   ```python
   cti_data = {
       'source': 'crowdstrike_cti',
       'indicators': [{...}]
   }
   await _publish_cti_data(cti_data)
   ```

4. **CTI Aggregator Checks**:
   - Query entities table for `ip:***********`
   - If exists from OTX (score: 80):
     - Merge: score = max(95, 80) = 95
     - Tags: union(['malware'], ['apt28', 'fancy_bear', 'c2_server'])
     - Sources: ['otx', 'crowdstrike_cti']
   - If new:
     - Create new entity with CrowdStrike data

5. **One-Way Cache Update**:
   ```python
   redis.setex('cti:ioc:ip:***********', 86400, {
       'threat_score': 95,
       'tags': ['malware', 'apt28', 'fancy_bear', 'c2_server'],
       'sources': ['otx', 'crowdstrike_cti']
   })
   ```

6. **Segregated Routing**:
   - `cti.enrichment.iocs` → Contextualization (enrich logs)
   - `cti.rules.patterns` → Backend (generate detection rules)
   - `cti.investigation.context` → Backend (investigation context)
   - `cti.mitre.mappings` → Backend (MITRE framework updates)

## Benefits Achieved

### ✅ CrowdStrike CTI Integration
- Leverages CrowdStrike's threat intelligence beyond EDR logs
- Enriches detection capabilities with premium threat intel
- Provides threat actor context for investigations

### ✅ Proper Scope Segregation
- CTI scopes (INTEL, IOCS) treated as threat feeds
- EDR scopes (EVENT_STREAMS) treated as logs
- Clean separation prevents confusion and duplication

### ✅ Multi-Source Enrichment
- Same IOC from CrowdStrike + OTX + ThreatFox merged
- Best threat score preserved
- All source data retained for provenance

### ✅ No Duplication
- CTI Aggregator deduplicates before storage
- Single entity in database per IOC
- Single Redis cache entry per IOC

## Testing

### Manual Test

```bash
# Set credentials
export CROWDSTRIKE_CLIENT_ID="your_id"
export CROWDSTRIKE_CLIENT_SECRET="your_secret"

# Run test
cd engines/ingestion
python crowdstrike_cti_integration.py
```

**Expected Output**:
```
Testing CrowdStrike CTI Integration...
--------------------------------------------------
Connection Test: Success

[Fetching INTEL Indicators]
  - ip: ******* (Score: 90)
  - domain: malicious.example.com (Score: 85)
  - sha256: abc123... (Score: 95)

[Fetching Custom IOCs]
  - ip: ******** (Severity: 85)
  - domain: bad.domain.com (Severity: 90)

[Fetching Threat Actors]
  - FANCY BEAR: Russian APT group targeting government...
  - COZY BEAR: Advanced persistent threat actor...

Total CTI items: 100+
```

### Integration Test

1. **Enable CrowdStrike CTI** in ingestion config
2. **Trigger manual update**:
   ```python
   await cti_manager.manual_update('crowdstrike_cti')
   ```
3. **Verify entities table**:
   ```sql
   SELECT entity_type, entity_value, risk_score,
          enrichment_metadata->'cti_sources' as sources
   FROM entities
   WHERE 'crowdstrike_cti' = ANY(
       ARRAY(SELECT jsonb_array_elements_text(
           enrichment_metadata->'cti_sources'
       ))
   );
   ```
4. **Verify Redis cache**:
   ```bash
   redis-cli KEYS "cti:ioc:*"
   redis-cli GET "cti:ioc:ip:*******"
   ```

## Next Steps

1. ✅ CrowdStrike CTI Connector - COMPLETE
2. ⏳ Implement periodic cache refresh scheduler (15-minute interval)
3. ⏳ Test end-to-end CTI flow with all 4 sources
4. ⏳ Monitor deduplication metrics and storage efficiency
5. ⏳ Apply same delegation pattern to other update types

## Files Summary

**Created**:
- `crowdstrike_cti_integration.py` (450 lines) - CrowdStrike INTEL + IOCS connector

**Modified**:
- `cti_manager.py` - Added CrowdStrike CTI connector and processing
- `backend/Dockerfile` - Added query_translator.py for multi-SIEM support

**Integration Points**:
- CTI Aggregator (existing) - Receives CrowdStrike CTI for deduplication
- CTI Data Router (existing) - Routes to segregated channels
- Update Scheduler (existing) - Can trigger CrowdStrike CTI updates

## Architecture Completeness

The CTI aggregation architecture is now complete with:
- ✅ 4 CTI sources (OTX, OpenCTI, ThreatFox, CrowdStrike)
- ✅ Single aggregation point (deduplication)
- ✅ Single source of truth (entities table)
- ✅ One-way cache updates (no loops)
- ✅ Segregated routing (4 channels for different purposes)
- ✅ Proper scope separation (CTI feeds vs EDR logs)

CrowdStrike CTI integration successfully implemented!
