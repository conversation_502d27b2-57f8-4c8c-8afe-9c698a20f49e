#!/usr/bin/env python3
"""
Test AI Functions in SIEMLess v2.0 Intelligence Engine
Tests consensus, crystallization, and pattern validation
"""

import asyncio
import json
import redis
import time
from datetime import datetime

class AIFunctionTester:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)

    def test_redis_connection(self):
        """Test Redis connectivity"""
        try:
            response = self.redis_client.ping()
            print(f"✅ Redis connection: {response}")
            return True
        except Exception as e:
            print(f"❌ Redis connection failed: {e}")
            return False

    def test_ai_consensus(self):
        """Test AI consensus functionality"""
        print("\n🧠 Testing AI Consensus...")

        # Send consensus request
        test_data = {
            'request_id': 'test_consensus_001',
            'pattern_data': {
                'log_sample': 'Failed login attempt from *************',
                'source': 'auth_logs'
            },
            'complexity': 'medium'
        }

        try:
            # Publish consensus request
            self.redis_client.publish('intelligence.consensus', json.dumps(test_data))
            print("   📤 Sent consensus request")

            # Subscribe to response
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe('intelligence.consensus_result')

            # Wait for response
            print("   ⏳ Waiting for AI consensus response...")
            timeout = time.time() + 10  # 10 second timeout

            for message in pubsub.listen():
                if time.time() > timeout:
                    print("   ⏰ Timeout waiting for response")
                    break

                if message['type'] == 'message':
                    response = json.loads(message['data'])
                    print(f"   ✅ Consensus result: {response}")
                    return True

            return False

        except Exception as e:
            print(f"   ❌ Consensus test failed: {e}")
            return False

    def test_pattern_crystallization(self):
        """Test pattern crystallization functionality"""
        print("\n💎 Testing Pattern Crystallization...")

        test_data = {
            'pattern_id': 'test_pattern_crystal_001',
            'ai_insights': {
                'confidence': 0.85,
                'pattern_type': 'authentication_failure',
                'responses': [
                    {'model': 'gemini-pro', 'confidence': 0.82},
                    {'model': 'claude-opus-4', 'confidence': 0.88}
                ]
            }
        }

        try:
            # Publish crystallization request
            self.redis_client.publish('intelligence.crystallize', json.dumps(test_data))
            print("   📤 Sent crystallization request")

            # Subscribe to response
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe('intelligence.pattern_crystallized')

            # Wait for response
            print("   ⏳ Waiting for crystallization response...")
            timeout = time.time() + 10

            for message in pubsub.listen():
                if time.time() > timeout:
                    print("   ⏰ Timeout waiting for response")
                    break

                if message['type'] == 'message':
                    response = json.loads(message['data'])
                    print(f"   ✅ Crystallized pattern: {response}")
                    return True

            return False

        except Exception as e:
            print(f"   ❌ Crystallization test failed: {e}")
            return False

    def test_pattern_validation(self):
        """Test pattern validation functionality"""
        print("\n🔍 Testing Pattern Validation...")

        test_data = {
            'pattern_id': 'test_validation_001',
            'pattern': {
                'regex': r'Failed login.*from (\d+\.){3}\d+',
                'description': 'Authentication failure pattern'
            },
            'type': 'general'
        }

        try:
            # Publish validation request
            self.redis_client.publish('intelligence.validate', json.dumps(test_data))
            print("   📤 Sent validation request")

            # Subscribe to response
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe('intelligence.pattern_validated')

            # Wait for response
            print("   ⏳ Waiting for validation response...")
            timeout = time.time() + 10

            for message in pubsub.listen():
                if time.time() > timeout:
                    print("   ⏰ Timeout waiting for response")
                    break

                if message['type'] == 'message':
                    response = json.loads(message['data'])
                    print(f"   ✅ Validation result: {response}")
                    return True

            return False

        except Exception as e:
            print(f"   ❌ Validation test failed: {e}")
            return False

    def test_unknown_pattern_discovery(self):
        """Test unknown pattern discovery"""
        print("\n🔎 Testing Unknown Pattern Discovery...")

        test_data = {
            'log_sample': 'SUSPICIOUS: Unusual network activity detected on port 4444',
            'source_type': 'network_monitor'
        }

        try:
            # Publish unknown pattern
            self.redis_client.publish('ingestion.unknown_patterns', json.dumps(test_data))
            print("   📤 Sent unknown pattern for analysis")

            # Subscribe to crystallization result (should trigger)
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe('intelligence.crystallize')

            # Wait for response
            print("   ⏳ Waiting for pattern discovery response...")
            timeout = time.time() + 10

            for message in pubsub.listen():
                if time.time() > timeout:
                    print("   ⏰ Timeout waiting for response")
                    break

                if message['type'] == 'message':
                    response = json.loads(message['data'])
                    print(f"   ✅ Pattern discovery triggered crystallization: {response}")
                    return True

            return False

        except Exception as e:
            print(f"   ❌ Pattern discovery test failed: {e}")
            return False

    def check_cost_metrics(self):
        """Check cost tracking functionality"""
        print("\n💰 Checking Cost Metrics...")

        try:
            # The cost metrics would be logged by the Intelligence Engine
            # We can check the logs or send a status request
            print("   📊 Cost metrics are tracked internally by Intelligence Engine")
            print("   💡 Check docker logs for cost information:")
            print("      docker-compose logs intelligence_engine | grep 'Cost metrics'")
            return True

        except Exception as e:
            print(f"   ❌ Cost metrics check failed: {e}")
            return False

    def run_all_tests(self):
        """Run all AI function tests"""
        print("Starting AI Function Tests for SIEMLess v2.0")
        print("=" * 50)

        # Test connection first
        if not self.test_redis_connection():
            print("❌ Cannot proceed without Redis connection")
            return False

        # Run all tests
        tests = [
            ("AI Consensus", self.test_ai_consensus),
            ("Pattern Crystallization", self.test_pattern_crystallization),
            ("Pattern Validation", self.test_pattern_validation),
            ("Unknown Pattern Discovery", self.test_unknown_pattern_discovery),
            ("Cost Metrics", self.check_cost_metrics)
        ]

        passed = 0
        failed = 0

        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
                failed += 1

        print("\n" + "=" * 50)
        print(f"🎯 Test Results: {passed} passed, {failed} failed")

        if failed == 0:
            print("🎉 All AI functions are working correctly!")
        else:
            print("⚠️  Some tests failed. Check Intelligence Engine logs for details.")

        return failed == 0

if __name__ == "__main__":
    tester = AIFunctionTester()
    tester.run_all_tests()