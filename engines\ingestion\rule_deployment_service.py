"""
Rule Deployment Service
Deploys approved detection rules to target SIEMs (<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Sentinel, QRadar)

ARCHITECTURAL NOTE:
This service belongs in Ingestion Engine, NOT Backend Engine.
- Ingestion Engine: External I/O boundary (internet/LAN access required)
- Backend Engine: Internal processing only (no external network access)
"""

import asyncio
import json
import base64
from typing import Dict, Any, List, Optional
from datetime import datetime
from elasticsearch import Elasticsearch
import requests
from requests.auth import HTTPBasicAuth


class RuleDeploymentService:
    """
    Deploys detection rules to external SIEM platforms

    Supports:
    - Elastic Security (Detection Engine API)
    - Splunk (via REST API)
    - Microsoft Sentinel (via Azure API)
    - IBM QRadar (via REST API)
    """

    def __init__(self, config: Dict[str, Any], logger=None):
        self.config = config
        self.logger = logger

        # Initialize Elasticsearch client for deployments
        self.elastic_cloud_id = config.get('elastic_cloud_id')
        self.elastic_api_key = config.get('elastic_api_key')
        self.elastic_username = config.get('elastic_username')
        self.elastic_password = config.get('elastic_password')

        # Initialize Elasticsearch client - use cloud_id to avoid DNS issues
        if self.elastic_cloud_id and self.elastic_api_key:
            self.elastic_client = Elasticsearch(
                cloud_id=self.elastic_cloud_id,
                api_key=self.elastic_api_key,
                request_timeout=30
            )
            if self.logger:
                self.logger.info(f"Initialized Elastic client with Cloud ID: {self.elastic_cloud_id.split(':')[0]}")
        elif self.elastic_cloud_id and self.elastic_username and self.elastic_password:
            self.elastic_client = Elasticsearch(
                cloud_id=self.elastic_cloud_id,
                basic_auth=(self.elastic_username, self.elastic_password),
                request_timeout=30
            )
        else:
            self.elastic_client = None
            if self.logger:
                self.logger.warning("No Elastic credentials configured for rule deployment")

    def _decode_cloud_id(self, cloud_id: str) -> Optional[str]:
        """
        Decode Elastic Cloud ID to extract Kibana URL

        Cloud ID format: name:base64(kibana_host$es_uuid$kibana_uuid)
        Example: Dacta_Global:YXAtc291dGhlYXN0LTEuYXdzLmZvdW5kLmlvJDY...

        Returns: https://kibana_host:9243 or https://kibana_host:443
        """
        try:
            # Split cloud ID (format: deployment_name:encoded_data)
            parts = cloud_id.split(':')
            if len(parts) != 2:
                if self.logger:
                    self.logger.error(f"Invalid Cloud ID format: {cloud_id}")
                return None

            # Decode base64 encoded part
            encoded = parts[1]
            decoded = base64.b64decode(encoded).decode('utf-8')

            # Extract components: host$es_uuid$kibana_uuid
            components = decoded.split('$')
            if len(components) < 3:
                if self.logger:
                    self.logger.error(f"Could not parse Cloud ID components: {decoded}")
                return None

            base_host = components[0]
            kibana_uuid = components[2]

            # Build Kibana URL: https://kibana_uuid.base_host:9243
            kibana_host = f"{kibana_uuid}.{base_host}"

            # Elastic Cloud uses port 9243 for Kibana
            kibana_url = f"https://{kibana_host}:9243"

            return kibana_url

        except Exception as e:
            if self.logger:
                self.logger.error(f"Error decoding Cloud ID: {e}")
            return None

    # ========================================================================
    # Elastic Security Deployment
    # ========================================================================

    async def deploy_to_elastic(self, rule: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deploy rule to Elastic Security Detection Engine

        API Endpoint: POST /api/detection_engine/rules
        Docs: https://www.elastic.co/guide/en/security/current/rules-api-create.html

        Args:
            rule: Detection rule dict with:
                - rule_id: Unique identifier
                - rule_name: Display name
                - rule_content: Elastic KQL query
                - description: Rule description
                - severity: critical|high|medium|low
                - mitre_tactics: List of MITRE tactics
                - mitre_techniques: List of MITRE techniques
                - enabled: Boolean

        Returns:
            {
                'success': bool,
                'rule_id': str,
                'elastic_rule_id': str (from Elastic),
                'error': str (if failed)
            }
        """
        try:
            # Validate Elastic client is initialized
            if not self.elastic_client:
                return {
                    'success': False,
                    'rule_id': rule.get('rule_id'),
                    'error': 'Elasticsearch client not initialized - check cloud_id and credentials'
                }

            # Build Elastic Security rule payload
            payload = self._build_elastic_rule_payload(rule)

            if not self.elastic_client:
                return {
                    'success': False,
                    'rule_id': rule.get('rule_id'),
                    'error': 'Elasticsearch client not initialized'
                }

            # Decode cloud_id to get Kibana URL (not Elasticsearch URL)
            kibana_url = self._decode_cloud_id(self.elastic_cloud_id)

            if not kibana_url:
                return {
                    'success': False,
                    'rule_id': rule.get('rule_id'),
                    'error': 'Could not decode Kibana URL from cloud_id'
                }

            if self.logger:
                self.logger.info(f"[*] Deploying to Kibana at: {kibana_url}")

            # Use requests with the resolved URL from ES client
            headers = {
                'Content-Type': 'application/json',
                'kbn-xsrf': 'true'
            }

            if self.elastic_api_key:
                headers['Authorization'] = f'ApiKey {self.elastic_api_key}'
            elif self.elastic_username and self.elastic_password:
                auth_str = f"{self.elastic_username}:{self.elastic_password}"
                headers['Authorization'] = f'Basic {base64.b64encode(auth_str.encode()).decode()}'

            url = f"{kibana_url}/api/detection_engine/rules"

            response = requests.post(
                url,
                json=payload,
                headers=headers,
                timeout=30,
                verify=True
            )

            if response.status_code in [200, 201]:
                result = response.json()

                if self.logger:
                    self.logger.info(f"[+] Successfully deployed rule '{rule.get('rule_name')}' to Elastic Security")
                    self.logger.info(f"    Elastic Rule ID: {result.get('id')}")
                    self.logger.info(f"    Enabled: {result.get('enabled', False)}")

                return {
                    'success': True,
                    'rule_id': rule.get('rule_id'),
                    'elastic_rule_id': result.get('id'),
                    'elastic_version': result.get('version', 1),
                    'enabled': result.get('enabled', False),
                    'created_at': result.get('created_at'),
                    'updated_at': result.get('updated_at')
                }
            else:
                error_msg = f"Elastic API error: {response.status_code} - {response.text}"
                if self.logger:
                    self.logger.error(f"[-] Failed to deploy rule: {error_msg}")

                return {
                    'success': False,
                    'rule_id': rule.get('rule_id'),
                    'error': error_msg
                }

        except Exception as e:
            error_msg = f"Deployment failed: {str(e)}"
            if self.logger:
                self.logger.error(f"❌ Exception deploying rule '{rule.get('rule_name')}': {error_msg}")

            return {
                'success': False,
                'rule_id': rule.get('rule_id'),
                'error': error_msg
            }

    def _build_elastic_rule_payload(self, rule: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build Elastic Security Detection Engine rule payload

        Elastic Rule Types:
        - query: KQL/Lucene query-based detection
        - eql: Event Query Language
        - threshold: Count-based detection
        - machine_learning: ML job-based
        - threat_match: Indicator matching
        """

        # Map SIEMLess severity to Elastic severity
        severity_map = {
            'critical': 'critical',
            'high': 'high',
            'medium': 'medium',
            'low': 'low',
            'info': 'low'
        }

        # Map SIEMLess severity to Elastic risk score (0-100)
        risk_score_map = {
            'critical': 99,
            'high': 75,
            'medium': 47,
            'low': 21,
            'info': 10
        }

        severity = rule.get('severity', 'medium')

        payload = {
            # Required fields
            'type': 'query',  # Query-based rule (most common)
            'name': rule.get('rule_name'),
            'description': rule.get('description', ''),
            'query': rule.get('rule_content'),  # Elastic KQL query
            'language': 'kuery',  # KQL language
            'risk_score': risk_score_map.get(severity, 47),
            'severity': severity_map.get(severity, 'medium'),

            # Optional but recommended
            # Rules are deployed DISABLED by default - user must manually enable in SIEM UI
            'enabled': False,
            'interval': '5m',  # How often to run the rule
            'from': 'now-6m',  # Look back window
            'to': 'now',
            'index': ['logs-*', 'winlogbeat-*', 'filebeat-*'],  # Target indices

            # Rule metadata
            'author': ['SIEMLess'],
            'license': 'Elastic License v2',
            'rule_id': rule.get('rule_id'),  # Our internal ID
            'references': rule.get('references', []),
            'tags': rule.get('tags', []),

            # Actions on detection
            'actions': [],  # Can add alert actions here
            'throttle': 'no_actions',  # Don't throttle alerts

            # Advanced settings
            'max_signals': 100,  # Max alerts per execution
            'meta': {
                'from': 'siemless',
                'version': '2.0',
                'cti_source': rule.get('cti_source'),
                'cti_indicator_id': rule.get('cti_indicator_id')
            }
        }

        # Add MITRE ATT&CK mapping if available
        if rule.get('mitre_techniques'):
            payload['threat'] = self._build_mitre_threat_mapping(
                rule.get('mitre_tactics', []),
                rule.get('mitre_techniques', [])
            )

        return payload

    def _build_mitre_threat_mapping(self, tactics: List[str], techniques: List[str]) -> List[Dict[str, Any]]:
        """
        Build MITRE ATT&CK threat mapping for Elastic

        Elastic expects:
        {
            'framework': 'MITRE ATT&CK',
            'tactic': {'id': 'TA0001', 'name': 'Initial Access', 'reference': '...'},
            'technique': [{'id': 'T1566', 'name': 'Phishing', 'reference': '...'}]
        }
        """

        # MITRE tactic mapping (name → ID)
        tactic_map = {
            'initial-access': {'id': 'TA0001', 'name': 'Initial Access'},
            'execution': {'id': 'TA0002', 'name': 'Execution'},
            'persistence': {'id': 'TA0003', 'name': 'Persistence'},
            'privilege-escalation': {'id': 'TA0004', 'name': 'Privilege Escalation'},
            'defense-evasion': {'id': 'TA0005', 'name': 'Defense Evasion'},
            'credential-access': {'id': 'TA0006', 'name': 'Credential Access'},
            'discovery': {'id': 'TA0007', 'name': 'Discovery'},
            'lateral-movement': {'id': 'TA0008', 'name': 'Lateral Movement'},
            'collection': {'id': 'TA0009', 'name': 'Collection'},
            'exfiltration': {'id': 'TA0010', 'name': 'Exfiltration'},
            'command-and-control': {'id': 'TA0011', 'name': 'Command and Control'},
            'impact': {'id': 'TA0040', 'name': 'Impact'}
        }

        threat_mapping = []

        for tactic in tactics:
            tactic_data = tactic_map.get(tactic.lower())
            if not tactic_data:
                continue

            threat_entry = {
                'framework': 'MITRE ATT&CK',
                'tactic': {
                    'id': tactic_data['id'],
                    'name': tactic_data['name'],
                    'reference': f"https://attack.mitre.org/tactics/{tactic_data['id']}"
                },
                'technique': []
            }

            # Add techniques for this tactic
            for technique in techniques:
                # Extract technique ID (e.g., "T1566" from "T1566.001")
                technique_id = technique.split('.')[0]

                threat_entry['technique'].append({
                    'id': technique,
                    'name': technique_id,  # Would need full name lookup
                    'reference': f"https://attack.mitre.org/techniques/{technique_id}"
                })

            threat_mapping.append(threat_entry)

        return threat_mapping

    async def update_elastic_rule(self, rule: Dict[str, Any], elastic_rule_id: str) -> Dict[str, Any]:
        """
        Update existing rule in Elastic Security

        API Endpoint: PUT /api/detection_engine/rules
        """
        try:
            payload = self._build_elastic_rule_payload(rule)
            payload['id'] = elastic_rule_id  # Use Elastic's internal ID

            headers = {
                'Content-Type': 'application/json',
                'kbn-xsrf': 'true'
            }

            auth = None
            if self.elastic_api_key:
                headers['Authorization'] = f'ApiKey {self.elastic_api_key}'
            elif self.elastic_username and self.elastic_password:
                auth = HTTPBasicAuth(self.elastic_username, self.elastic_password)

            url = f"{self.elastic_kibana_url}/api/detection_engine/rules"

            response = requests.put(
                url,
                json=payload,
                headers=headers,
                auth=auth,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()

                if self.logger:
                    self.logger.info(f"✅ Successfully updated rule '{rule.get('rule_name')}' in Elastic Security")

                return {
                    'success': True,
                    'rule_id': rule.get('rule_id'),
                    'elastic_rule_id': result.get('id'),
                    'updated_at': result.get('updated_at')
                }
            else:
                return {
                    'success': False,
                    'rule_id': rule.get('rule_id'),
                    'error': f"Update failed: {response.status_code} - {response.text}"
                }

        except Exception as e:
            return {
                'success': False,
                'rule_id': rule.get('rule_id'),
                'error': f"Update failed: {str(e)}"
            }

    async def delete_elastic_rule(self, elastic_rule_id: str) -> Dict[str, Any]:
        """
        Delete rule from Elastic Security

        API Endpoint: DELETE /api/detection_engine/rules?id={rule_id}
        """
        try:
            headers = {
                'kbn-xsrf': 'true'
            }

            auth = None
            if self.elastic_api_key:
                headers['Authorization'] = f'ApiKey {self.elastic_api_key}'
            elif self.elastic_username and self.elastic_password:
                auth = HTTPBasicAuth(self.elastic_username, self.elastic_password)

            url = f"{self.elastic_kibana_url}/api/detection_engine/rules?id={elastic_rule_id}"

            response = requests.delete(
                url,
                headers=headers,
                auth=auth,
                timeout=30
            )

            if response.status_code == 200:
                if self.logger:
                    self.logger.info(f"✅ Successfully deleted rule from Elastic Security (ID: {elastic_rule_id})")

                return {
                    'success': True,
                    'elastic_rule_id': elastic_rule_id
                }
            else:
                return {
                    'success': False,
                    'error': f"Delete failed: {response.status_code} - {response.text}"
                }

        except Exception as e:
            return {
                'success': False,
                'error': f"Delete failed: {str(e)}"
            }

    # ========================================================================
    # Splunk Deployment (Future Implementation)
    # ========================================================================

    async def deploy_to_splunk(self, rule: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deploy rule to Splunk Enterprise Security

        API Endpoint: POST /servicesNS/nobody/search/saved/searches

        TODO: Implement Splunk deployment
        """
        return {
            'success': False,
            'rule_id': rule.get('rule_id'),
            'error': 'Splunk deployment not yet implemented'
        }

    # ========================================================================
    # Microsoft Sentinel Deployment (Future Implementation)
    # ========================================================================

    async def deploy_to_sentinel(self, rule: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deploy rule to Microsoft Sentinel

        API: Azure REST API
        Endpoint: PUT /subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/
                  providers/Microsoft.OperationalInsights/workspaces/{workspaceName}/
                  providers/Microsoft.SecurityInsights/alertRules/{ruleId}

        TODO: Implement Sentinel deployment
        """
        return {
            'success': False,
            'rule_id': rule.get('rule_id'),
            'error': 'Sentinel deployment not yet implemented'
        }

    # ========================================================================
    # IBM QRadar Deployment (Future Implementation)
    # ========================================================================

    async def deploy_to_qradar(self, rule: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deploy rule to IBM QRadar

        API Endpoint: POST /api/analytics/custom_rules

        TODO: Implement QRadar deployment
        """
        return {
            'success': False,
            'rule_id': rule.get('rule_id'),
            'error': 'QRadar deployment not yet implemented'
        }

    # ========================================================================
    # Multi-SIEM Deployment
    # ========================================================================

    async def deploy_to_all_configured(self, rule: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Deploy rule to all configured SIEM platforms

        Returns:
            {
                'elastic': [result],
                'splunk': [result],
                'sentinel': [result],
                'qradar': [result]
            }
        """
        results = {}

        # Check which SIEMs are configured
        if self.elastic_kibana_url:
            results['elastic'] = await self.deploy_to_elastic(rule)

        # Add other SIEMs as they're implemented

        return results

    async def bulk_deploy(self, rules: List[Dict[str, Any]], target_siem: str = 'elastic') -> List[Dict[str, Any]]:
        """
        Deploy multiple rules in batch

        Args:
            rules: List of rule dicts
            target_siem: 'elastic' | 'splunk' | 'sentinel' | 'qradar' | 'all'

        Returns:
            List of deployment results
        """
        results = []

        for rule in rules:
            if target_siem == 'elastic':
                result = await self.deploy_to_elastic(rule)
            elif target_siem == 'splunk':
                result = await self.deploy_to_splunk(rule)
            elif target_siem == 'sentinel':
                result = await self.deploy_to_sentinel(rule)
            elif target_siem == 'qradar':
                result = await self.deploy_to_qradar(rule)
            elif target_siem == 'all':
                result = await self.deploy_to_all_configured(rule)
            else:
                result = {
                    'success': False,
                    'rule_id': rule.get('rule_id'),
                    'error': f'Unknown SIEM target: {target_siem}'
                }

            results.append(result)

        return results


# ============================================================================
# Integration Example
# ============================================================================

async def example_usage():
    """Example: Deploy rule to Elastic Security"""

    # Initialize service
    config = {
        'elastic_kibana_url': 'https://your-kibana-instance:5601',
        'elastic_api_key': 'your-api-key',
        # OR
        'elastic_username': 'elastic',
        'elastic_password': 'your-password'
    }

    deployment_service = RuleDeploymentService(config)

    # Rule to deploy
    rule = {
        'rule_id': 'rule-12345',
        'rule_name': 'Emotet C2 Communication Detection',
        'rule_content': 'destination.ip: ************** AND network.protocol: "tcp"',
        'description': 'Detects communication with known Emotet C2 server',
        'severity': 'critical',
        'mitre_tactics': ['command-and-control'],
        'mitre_techniques': ['T1071.001'],
        'enabled': True,
        'tags': ['emotet', 'malware', 'c2'],
        'cti_source': 'threatfox',
        'cti_indicator_id': 'indicator-67890'
    }

    # Deploy to Elastic
    result = await deployment_service.deploy_to_elastic(rule)

    if result['success']:
        print(f"✅ Rule deployed to Elastic!")
        print(f"   Elastic Rule ID: {result['elastic_rule_id']}")
    else:
        print(f"❌ Deployment failed: {result['error']}")


if __name__ == '__main__':
    asyncio.run(example_usage())
