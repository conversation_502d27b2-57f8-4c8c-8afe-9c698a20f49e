# Context-Aware Investigation Platform - Implementation Summary

## October 2025 - Phase 1 Foundation Complete

---

## What We Built (Phase 1)

### 1. Database Schema (`init_db_context_management.sql`)

**Tables Created:**

```sql
-- Business Context Storage
organizational_context
  - Store business information about entities (hosts, users, IPs, etc.)
  - Behavior patterns (scheduled jobs, normal times, expected traffic)
  - Business metadata (owner, business unit, criticality, security zone)
  - Audit trail (created_by, updated_by, approved_by)
  - Status workflow (active, pending_approval, archived)

-- Investigation Tracking
investigation_status_history
  - Track every status change in investigations
  - Who changed it, when, and why
  - Full audit trail for compliance

investigation_verdicts
  - Store analyst verdicts (TP, FP, Escalate, Closed)
  - Context logging for false positives
  - Compare our prediction vs. actual verdict
  - Sync with Elastic's verdict labels

-- Rule Tuning
rule_tuning_suggestions
  - Auto-generated from false positive resolutions
  - Suppression conditions extracted from FP context
  - Approval workflow (pending → approved → applied)
  - Links back to investigation that triggered it

-- Query Templates
query_templates
  - Deterministic query generation per source type
  - Deep link templates for opening in source systems
  - Metadata about what data each source provides

-- Investigation Timeline
investigation_timeline
  - Detailed event log for every action in investigation
  - Status changes, enrichment completions, comments, etc.
```

### 2. Backend API (`business_context_api.py`)

**Endpoints Implemented:**

```python
# Business Context CRUD
GET    /api/context                              # List all contexts with filtering
GET    /api/context/{context_id}                 # Get single context
GET    /api/context/entity/{type}/{value}        # Check if entity has context
POST   /api/context                              # Create new context
PUT    /api/context/{context_id}                 # Update context
DELETE /api/context/{context_id}                 # Archive context

# Rule Tuning Suggestions
GET    /api/rule-tuning/suggestions              # List suggestions
POST   /api/rule-tuning/suggestions              # Create suggestion (from FP)
PUT    /api/rule-tuning/suggestions/{id}/approve # Approve suggestion
PUT    /api/rule-tuning/suggestions/{id}/apply   # Mark as applied
```

**Features:**
- Full CRUD operations for business context
- Entity-specific context lookup
- Rule tuning suggestion workflow
- User authentication tracking (X-User-ID header)
- Soft delete (archive) instead of hard delete

### 3. Frontend Components

#### BusinessContextEditor (`BusinessContextEditor.tsx`)

**Purpose**: Allow analysts to add/edit organizational context

**Features:**
- Entity information (type, value, label, description)
- Business metadata (business unit, owner, criticality, security zone)
- Behavior patterns (scheduled jobs, normal times, expected traffic)
- Form validation
- Success/error messaging
- Create and edit modes

**User Workflow:**
1. Analyst sees unknown entity in alert
2. Clicks "Add Context" button
3. Fills out form with business information
4. Saves context
5. Future alerts with this entity show "Known Device" badge

#### BusinessContextBadge (`BusinessContextBadge.tsx`)

**Purpose**: Show badge when entity has organizational context

**Features:**
- Automatic context lookup on render
- Color-coded by criticality (red/orange/blue/gray)
- Hover tooltip with full context details
- Displays behavior patterns
- Recommendation text

**Display Logic:**
```
IF entity has organizational context:
  Show badge: "Known Device" (or "Known User", etc.)
  Color: Based on criticality
  Tooltip: Full context details + behavior pattern + recommendation
ELSE:
  Show nothing (no badge)
```

---

## Integration Points

### Backend Integration (Delivery Engine)

**File**: `engines/delivery/delivery_engine.py`

Add to initialization:

```python
from business_context_api import BusinessContextAPI

class DeliveryEngine(BaseEngine):
    def __init__(self):
        super().__init__("delivery", 8005)

        # Existing code...

        # Add business context API
        self.context_api = BusinessContextAPI(self.db_connection)

    async def setup_http_routes(self, app):
        # Existing routes...

        # Register context routes
        self.context_api.register_routes(app)
```

### Frontend Integration

**1. Add to Alert Display Components:**

```tsx
// In AlertQueue.tsx, AlertDetailView.tsx, etc.
import { BusinessContextBadge } from '../components/context/BusinessContextBadge'

// For each entity displayed:
<div className="flex items-center gap-2">
  <span>{entity.value}</span>
  <BusinessContextBadge
    entityType={entity.type}
    entityValue={entity.value}
  />
</div>
```

**2. Add "Add Context" Button:**

```tsx
import { BusinessContextEditor } from '../components/context/BusinessContextEditor'

const [showContextEditor, setShowContextEditor] = useState(false)

// In entity display:
<button onClick={() => setShowContextEditor(true)}>
  Add Business Context
</button>

{showContextEditor && (
  <BusinessContextEditor
    initialEntity={{ type: 'host', value: 'UNKNOWN-SERVER-01' }}
    onSave={async (context) => {
      await apiRequest('/context', {
        method: 'POST',
        body: JSON.stringify(context)
      })
    }}
    onClose={() => setShowContextEditor(false)}
  />
)}
```

---

## User Workflows

### Workflow 1: Add Context to Unknown Entity

```
Analyst investigating alert
  ↓
Sees host "BACKUP-SERVER-01" with suspicious activity
  ↓
Clicks "Add Business Context" button
  ↓
Fills out form:
  - Entity Type: Host
  - Entity Value: BACKUP-SERVER-01
  - Context Label: "Primary Backup Server"
  - Description: "Runs weekly backups every Sunday 2-4 AM"
  - Business Unit: "IT Operations"
  - Owner: "<EMAIL>"
  - Criticality: High
  - Security Zone: Internal
  - Behavior Pattern:
    - Scheduled Jobs: weekly_backup
    - Normal Times: Sunday 02:00-04:00
    - Expected Traffic: large_file_operations
  ↓
Saves context
  ↓
Future alerts with this host show "Known Device" badge
  ↓
Analyst hovers → sees full context → verifies activity matches normal pattern
  ↓
Marks as False Positive with reason: "Planned weekly backup - confirmed"
  ↓
System suggests rule tuning: "Suppress for BACKUP-SERVER-01 + Sunday 02:00-04:00"
```

### Workflow 2: Use Context During Investigation

```
New alert arrives: "Large file operations on BACKUP-SERVER-01"
  ↓
Alert displayed with "Known Device" badge (blue, medium priority)
  ↓
Analyst hovers badge → sees tooltip:
  "Primary Backup Server
   Business Unit: IT Operations
   Owner: <EMAIL>
   Normal Behavior:
     - Jobs: weekly_backup
     - Times: Sunday 02:00-04:00
     - Traffic: large_file_operations

   ✓ Recommendation: This is a known entity with documented behavior.
     Verify activity matches expected patterns before escalating."
  ↓
Analyst checks: Current time is Sunday 02:15 AM ✓
  ↓
Marks as False Positive
  ↓
System learns: "BACKUP-SERVER-01 + large_file_operations + Sunday 02:00-04:00 = Benign"
```

---

## Database Setup

**Run the schema:**

```bash
# Connect to database
docker-compose exec postgres psql -U siemless -d siemless_v2

# Apply schema
\i /path/to/init_db_context_management.sql
```

Or via docker:

```bash
docker-compose exec postgres psql -U siemless -d siemless_v2 -f /docker-entrypoint-initdb.d/init_db_context_management.sql
```

---

## Next Steps (Phase 2 & 3)

### Phase 2: Deterministic Query Generator

**Goal**: Generate queries ONLY for sources we have logs from

**Components to Build:**
1. Query Template Seeding
   - Create templates for Elastic, Fortinet, Palo Alto
   - Store in `query_templates` table
2. Query Generator Service
   - Detect available log sources from `ingestion_logs`
   - Generate queries only for available sources
   - Provide copy buttons and deep links
3. Investigation Guide Enhancement
   - Add "Queries to Run" section
   - Show "What to Look For" guidance
   - Display data source limitations

### Phase 3: Investigation Lifecycle Tracking

**Goal**: Track full investigation workflow with SLA monitoring

**Components to Build:**
1. Investigation Status Workflow
   - "Investigate" button creates investigation
   - Status: New → Acknowledged → Investigating → Resolved
   - Track: Who, When, How Long
2. Verdict Marking UI
   - True Positive / False Positive / Escalate / Close buttons
   - Context form for FP explanations
   - Auto-generate rule tuning suggestions
3. SLA Monitoring
   - Critical: 60 min
   - High: 4 hours
   - Medium/Low: 8 hours
   - Visual countdown timers
4. Feedback Loop
   - Compare our prediction vs. analyst verdict
   - Measure accuracy over time
   - Auto-improve investigation guides

---

## Success Metrics

**Phase 1 Goals:**
- ✅ Analysts can add business context to entities
- ✅ Context is stored and persisted
- ✅ Context is displayed on future alerts
- ✅ Context includes behavior patterns for FP reduction

**Measurable Outcomes (After Deployment):**
- % of entities with business context (target: >50% of frequently seen entities)
- Reduction in false positive rate for entities with context (target: >30% reduction)
- Analyst time saved verifying known entities (target: >2 hours/week per analyst)
- Rule tuning suggestions generated from FP context (target: >10 suggestions/month)

---

## Files Created

### Backend:
- `engines/init_db_context_management.sql` - Database schema
- `engines/delivery/business_context_api.py` - REST API for context management

### Frontend:
- `frontend/src/components/context/BusinessContextEditor.tsx` - Context editor form
- `frontend/src/components/context/BusinessContextBadge.tsx` - Context display badge

### Documentation:
- `CONTEXT_AWARE_INVESTIGATION_IMPLEMENTATION.md` - This file

---

## Deployment Checklist

- [ ] Apply database schema (`init_db_context_management.sql`)
- [ ] Integrate `BusinessContextAPI` into Delivery Engine
- [ ] Add `BusinessContextEditor` to frontend (where?)
- [ ] Add `BusinessContextBadge` to alert displays
- [ ] Test context CRUD operations
- [ ] Test context badge display
- [ ] Train analysts on adding context
- [ ] Monitor context usage metrics

---

**Phase 1 Complete!** Ready to move to Phase 2 (Query Generator) when approved.
