import React, { useState, useEffect } from 'react'
import { Play, Check, X, FileText, Shield, Zap, AlertTriangle } from 'lucide-react'

interface DetectionRule {
  id: string
  name: string
  platform: 'splunk' | 'elastic' | 'sentinel' | 'qradar'
  query: string
  description: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  mitre_tactics: string[]
  test_cases: TestCase[]
  last_modified: string
  author: string
}

interface TestCase {
  id: string
  name: string
  description: string
  input_log: string
  expected_result: boolean
  test_status?: 'passed' | 'failed' | 'pending'
}

interface TestResult {
  rule_id: string
  test_case_id: string
  status: 'passed' | 'failed'
  execution_time: number
  error_message?: string
}

export const RuleTestRunner: React.FC = () => {
  const [rules, setRules] = useState<DetectionRule[]>([])
  const [selectedRule, setSelectedRule] = useState<DetectionRule | null>(null)
  const [testResults, setTestResults] = useState<Map<string, TestResult>>(new Map())
  const [isRunning, setIsRunning] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Fetch detection rules from API
    const fetchRules = async () => {
      try {
        const response = await fetch('/api/workflows/templates')
        if (response.ok) {
          const data = await response.json()
          // Check if there are detection rule templates
          const ruleTemplates = data.templates?.filter((t: any) =>
            t.category === 'detection' || t.name.includes('rule')
          ) || []

          if (ruleTemplates.length > 0) {
            // Map templates to rules format
            const mappedRules = ruleTemplates.map((template: any, idx: number) => ({
              id: template.template_id || `rule-${idx}`,
              name: template.name,
              platform: 'splunk',
              query: template.definition?.query || '',
              description: template.definition?.description || '',
              severity: 'medium',
              mitre_tactics: [],
              test_cases: [],
              last_modified: template.created_at || new Date().toISOString(),
              author: 'System'
            }))
            setRules(mappedRules)
          } else {
            // Use mock data if no real rules
            setRules(generateMockRules())
          }
        } else {
          setRules(generateMockRules())
        }
      } catch (error) {
        console.error('Failed to fetch rules:', error)
        setRules(generateMockRules())
      } finally {
        setLoading(false)
      }
    }

    fetchRules()
  }, [])

  const generateMockRules = (): DetectionRule[] => {
    return [
      {
        id: 'rule-1',
        name: 'Suspicious PowerShell Execution',
        platform: 'splunk',
        query: 'index=windows EventCode=4688 Image="*powershell.exe" CommandLine="*-EncodedCommand*"',
        description: 'Detects suspicious PowerShell execution with encoded commands',
        severity: 'high',
        mitre_tactics: ['T1059.001', 'T1027'],
        test_cases: [
          {
            id: 'test-1',
            name: 'Encoded PowerShell Command',
            description: 'Test detection of encoded PowerShell command',
            input_log: 'EventCode=4688 Image="C:\\Windows\\System32\\powershell.exe" CommandLine="powershell.exe -EncodedCommand SGVsbG8gV29ybGQ="',
            expected_result: true
          },
          {
            id: 'test-2',
            name: 'Normal PowerShell Command',
            description: 'Should not trigger on normal PowerShell',
            input_log: 'EventCode=4688 Image="C:\\Windows\\System32\\powershell.exe" CommandLine="powershell.exe Get-Process"',
            expected_result: false
          }
        ],
        last_modified: new Date().toISOString(),
        author: 'SOC Team'
      },
      {
        id: 'rule-2',
        name: 'Multiple Failed Login Attempts',
        platform: 'elastic',
        query: 'event.category:authentication AND event.outcome:failure | stats count by source.ip | where count > 5',
        description: 'Detects multiple failed login attempts from same IP',
        severity: 'medium',
        mitre_tactics: ['T1110'],
        test_cases: [
          {
            id: 'test-3',
            name: 'Multiple Failed Logins',
            description: 'Test detection of brute force attempt',
            input_log: 'event.category:authentication event.outcome:failure source.ip:************* count:10',
            expected_result: true
          }
        ],
        last_modified: new Date().toISOString(),
        author: 'Security Team'
      },
      {
        id: 'rule-3',
        name: 'Data Exfiltration via DNS',
        platform: 'sentinel',
        query: 'DnsEvents | where QueryType == "TXT" and QuerySize > 200 | where SubType == "response"',
        description: 'Detects potential data exfiltration through DNS TXT records',
        severity: 'critical',
        mitre_tactics: ['T1048', 'T1071.004'],
        test_cases: [
          {
            id: 'test-4',
            name: 'Large DNS TXT Query',
            description: 'Test detection of suspicious DNS activity',
            input_log: 'QueryType:TXT QuerySize:500 SubType:response Domain:suspicious.com',
            expected_result: true
          }
        ],
        last_modified: new Date().toISOString(),
        author: 'Threat Intel Team'
      }
    ]
  }

  const runTest = async (rule: DetectionRule, testCase: TestCase) => {
    setIsRunning(true)

    // Simulate test execution
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 1000))

    const result: TestResult = {
      rule_id: rule.id,
      test_case_id: testCase.id,
      status: Math.random() > 0.2 ? 'passed' : 'failed',
      execution_time: Math.random() * 1000,
      error_message: Math.random() > 0.2 ? undefined : 'Query timeout exceeded'
    }

    setTestResults(prev => new Map(prev).set(`${rule.id}-${testCase.id}`, result))
    setIsRunning(false)

    return result
  }

  const runAllTests = async (rule: DetectionRule) => {
    setIsRunning(true)

    for (const testCase of rule.test_cases) {
      await runTest(rule, testCase)
    }

    setIsRunning(false)
  }

  const getPlatformColor = (platform: string) => {
    const colors = {
      splunk: 'text-green-600 bg-green-100',
      elastic: 'text-blue-600 bg-blue-100',
      sentinel: 'text-purple-600 bg-purple-100',
      qradar: 'text-orange-600 bg-orange-100'
    }
    return colors[platform as keyof typeof colors] || 'text-gray-600 bg-gray-100'
  }

  const getSeverityColor = (severity: string) => {
    const colors = {
      critical: 'text-red-600 bg-red-100',
      high: 'text-orange-600 bg-orange-100',
      medium: 'text-yellow-600 bg-yellow-100',
      low: 'text-green-600 bg-green-100'
    }
    return colors[severity as keyof typeof colors] || 'text-gray-600 bg-gray-100'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Shield className="animate-pulse text-blue-500 mx-auto mb-4" size={48} />
          <p>Loading detection rules...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-full bg-gray-50">
      {/* Rules List */}
      <div className="w-1/3 bg-white border-r overflow-y-auto">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold">Detection Rules</h2>
          <p className="text-sm text-gray-600">{rules.length} rules available</p>
        </div>

        <div className="p-4 space-y-2">
          {rules.map(rule => (
            <div
              key={rule.id}
              onClick={() => setSelectedRule(rule)}
              className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                selectedRule?.id === rule.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'hover:bg-gray-50'
              }`}
            >
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-medium">{rule.name}</h3>
                <span className={`px-2 py-1 rounded-full text-xs ${getPlatformColor(rule.platform)}`}>
                  {rule.platform}
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-2">{rule.description}</p>
              <div className="flex justify-between items-center">
                <span className={`px-2 py-1 rounded text-xs ${getSeverityColor(rule.severity)}`}>
                  {rule.severity}
                </span>
                <span className="text-xs text-gray-500">
                  {rule.test_cases.length} test cases
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Test Runner */}
      {selectedRule ? (
        <div className="flex-1 overflow-y-auto">
          <div className="p-6">
            <div className="bg-white rounded-lg shadow p-6 mb-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h2 className="text-xl font-bold mb-2">{selectedRule.name}</h2>
                  <p className="text-gray-600">{selectedRule.description}</p>
                </div>
                <button
                  onClick={() => runAllTests(selectedRule)}
                  disabled={isRunning}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
                >
                  {isRunning ? (
                    <Zap className="animate-pulse" size={20} />
                  ) : (
                    <Play size={20} />
                  )}
                  Run All Tests
                </button>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-6">
                <div>
                  <p className="text-sm text-gray-600">Platform</p>
                  <p className="font-medium capitalize">{selectedRule.platform}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Severity</p>
                  <p className="font-medium capitalize">{selectedRule.severity}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Author</p>
                  <p className="font-medium">{selectedRule.author}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">MITRE Tactics</p>
                  <p className="font-medium">{selectedRule.mitre_tactics.join(', ') || 'None'}</p>
                </div>
              </div>

              <div className="bg-gray-100 p-4 rounded-lg mb-6">
                <p className="text-sm font-medium text-gray-700 mb-2">Query</p>
                <code className="text-sm text-gray-800 whitespace-pre-wrap">
                  {selectedRule.query}
                </code>
              </div>

              <div>
                <h3 className="font-semibold mb-4">Test Cases</h3>
                <div className="space-y-4">
                  {selectedRule.test_cases.map(testCase => {
                    const result = testResults.get(`${selectedRule.id}-${testCase.id}`)

                    return (
                      <div key={testCase.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h4 className="font-medium">{testCase.name}</h4>
                            <p className="text-sm text-gray-600">{testCase.description}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            {result && (
                              <span className={`flex items-center gap-1 px-2 py-1 rounded text-sm ${
                                result.status === 'passed'
                                  ? 'bg-green-100 text-green-700'
                                  : 'bg-red-100 text-red-700'
                              }`}>
                                {result.status === 'passed' ? (
                                  <Check size={16} />
                                ) : (
                                  <X size={16} />
                                )}
                                {result.status}
                              </span>
                            )}
                            <button
                              onClick={() => runTest(selectedRule, testCase)}
                              disabled={isRunning}
                              className="p-2 text-blue-600 hover:bg-blue-50 rounded disabled:opacity-50"
                            >
                              <Play size={16} />
                            </button>
                          </div>
                        </div>

                        <div className="bg-gray-50 p-3 rounded mt-3">
                          <p className="text-xs font-medium text-gray-700 mb-1">Test Input</p>
                          <code className="text-xs text-gray-600">
                            {testCase.input_log}
                          </code>
                        </div>

                        {result && (
                          <div className="mt-3 text-sm">
                            <p className="text-gray-600">
                              Execution time: {result.execution_time.toFixed(2)}ms
                            </p>
                            {result.error_message && (
                              <p className="text-red-600 flex items-center gap-1 mt-1">
                                <AlertTriangle size={14} />
                                {result.error_message}
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <FileText size={48} className="mx-auto mb-4 text-gray-400" />
            <p>Select a rule to view test cases</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default RuleTestRunner