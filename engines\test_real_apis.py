#!/usr/bin/env python3
"""
Real API Integration Tests for SIEMLess v2.0
Tests actual API calls through Redis messaging - NO MOCKS
"""

import asyncio
import json
import redis
import requests
import time
import os
from datetime import datetime, timedelta
import base64

class RealAPITester:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6380, decode_responses=True)
        self.test_results = []

    def log_result(self, test_name, success, details):
        """Log test results"""
        result = {
            'test': test_name,
            'success': success,
            'timestamp': datetime.now().isoformat(),
            'details': details
        }
        self.test_results.append(result)
        status = "[SUCCESS]" if success else "[FAIL]"
        print(f"{status} {test_name}: {details}")

    def test_crowdstrike_api(self):
        """Test real CrowdStrike API integration"""
        print("\n=== Testing CrowdStrike API Integration ===")

        try:
            # Get credentials from environment (should be loaded from .env)
            client_id = os.getenv('CROWDSTRIKE_CLIENT_ID')
            client_secret = os.getenv('CROWDSTRIKE_CLIENT_SECRET')
            base_url = os.getenv('CROWDSTRIKE_BASE_URL')

            if not all([client_id, client_secret, base_url]):
                self.log_result("CrowdStrike Config", False, "Missing API credentials")
                return False

            # Test authentication
            auth_url = f"{base_url}/oauth2/token"
            auth_data = {
                'client_id': client_id,
                'client_secret': client_secret
            }

            print(f"   [API] Authenticating with CrowdStrike...")
            auth_response = requests.post(auth_url, data=auth_data, timeout=10)

            if auth_response.status_code == 200:
                token = auth_response.json().get('access_token')
                self.log_result("CrowdStrike Auth", True, f"Token obtained: {token[:20]}...")

                # Send ingestion request through Redis
                ingestion_request = {
                    'source': 'crowdstrike',
                    'action': 'fetch_detections',
                    'credentials': {
                        'token': token,
                        'base_url': base_url
                    },
                    'request_id': f'cs_test_{int(time.time())}'
                }

                self.redis_client.publish('ingestion.crowdstrike', json.dumps(ingestion_request))
                self.log_result("CrowdStrike Redis", True, "Ingestion request sent via Redis")
                return True
            else:
                self.log_result("CrowdStrike Auth", False, f"HTTP {auth_response.status_code}")
                return False

        except Exception as e:
            self.log_result("CrowdStrike API", False, f"Exception: {str(e)}")
            return False

    def test_opencti_api(self):
        """Test real OpenCTI API integration"""
        print("\n=== Testing OpenCTI API Integration ===")

        try:
            opencti_url = os.getenv('OPENCTI_URL')
            opencti_token = os.getenv('OPENCTI_TOKEN')

            if not all([opencti_url, opencti_token]):
                self.log_result("OpenCTI Config", False, "Missing API credentials")
                return False

            # Test OpenCTI connection
            headers = {
                'Authorization': f'Bearer {opencti_token}',
                'Content-Type': 'application/json'
            }

            # Test GraphQL endpoint
            graphql_url = f"{opencti_url.rstrip('/')}/graphql"
            query = {
                'query': '''
                    query GetIndicators {
                        indicators(first: 5) {
                            edges {
                                node {
                                    id
                                    pattern
                                    indicator_types
                                    created
                                }
                            }
                        }
                    }
                '''
            }

            print(f"   [API] Querying OpenCTI indicators...")
            response = requests.post(graphql_url, json=query, headers=headers, timeout=10)

            if response.status_code == 200:
                data = response.json()
                indicators = data.get('data', {}).get('indicators', {}).get('edges', [])

                self.log_result("OpenCTI API", True, f"Retrieved {len(indicators)} indicators")

                # Send CTI data through Redis for rule generation
                cti_request = {
                    'source': 'opencti',
                    'action': 'generate_rules',
                    'indicators': indicators[:3],  # Send first 3 for testing
                    'request_id': f'cti_test_{int(time.time())}'
                }

                self.redis_client.publish('backend.cti_update', json.dumps(cti_request))
                self.log_result("OpenCTI Redis", True, "CTI data sent for rule generation")
                return True
            else:
                self.log_result("OpenCTI API", False, f"HTTP {response.status_code}: {response.text}")
                return False

        except Exception as e:
            self.log_result("OpenCTI API", False, f"Exception: {str(e)}")
            return False

    def test_elasticsearch_api(self):
        """Test real ElasticSearch API integration"""
        print("\n=== Testing ElasticSearch API Integration ===")

        try:
            cloud_id = os.getenv('ELASTIC_CLOUD_ID')
            api_key = os.getenv('ELASTIC_API_KEY')

            if not all([cloud_id, api_key]):
                self.log_result("ElasticSearch Config", False, "Missing API credentials")
                return False

            # Decode cloud_id to get endpoint
            cloud_parts = cloud_id.split(':')
            if len(cloud_parts) >= 2:
                encoded_endpoint = cloud_parts[1]
                decoded = base64.b64decode(encoded_endpoint + '==').decode('utf-8')
                es_endpoint = f"https://{decoded.split('$')[0]}"
            else:
                self.log_result("ElasticSearch Config", False, "Invalid cloud_id format")
                return False

            headers = {
                'Authorization': f'ApiKey {api_key}',
                'Content-Type': 'application/json'
            }

            # Test connection
            print(f"   [API] Testing ElasticSearch connection...")
            response = requests.get(f"{es_endpoint}/", headers=headers, timeout=10)

            if response.status_code == 200:
                cluster_info = response.json()
                self.log_result("ElasticSearch Auth", True, f"Connected to cluster: {cluster_info.get('cluster_name')}")

                # Query for security events
                search_query = {
                    "query": {
                        "bool": {
                            "must": [
                                {"range": {"@timestamp": {"gte": "now-1h"}}},
                                {"wildcard": {"event.category": "*security*"}}
                            ]
                        }
                    },
                    "size": 10,
                    "_source": ["@timestamp", "event.*", "source.ip", "destination.ip", "user.name"]
                }

                search_response = requests.post(
                    f"{es_endpoint}/_search",
                    json=search_query,
                    headers=headers,
                    timeout=15
                )

                if search_response.status_code == 200:
                    search_data = search_response.json()
                    hits = search_data.get('hits', {}).get('hits', [])

                    self.log_result("ElasticSearch Query", True, f"Retrieved {len(hits)} security events")

                    # Send data for contextualization through Redis
                    context_request = {
                        'source': 'elasticsearch',
                        'action': 'contextualize_events',
                        'events': hits[:5],  # Send first 5 events
                        'request_id': f'es_test_{int(time.time())}'
                    }

                    self.redis_client.publish('contextualization.new_data', json.dumps(context_request))
                    self.log_result("ElasticSearch Redis", True, "Events sent for contextualization")
                    return True
                else:
                    self.log_result("ElasticSearch Query", False, f"Query failed: {search_response.status_code}")
                    return False
            else:
                self.log_result("ElasticSearch Auth", False, f"HTTP {response.status_code}")
                return False

        except Exception as e:
            self.log_result("ElasticSearch API", False, f"Exception: {str(e)}")
            return False

    def test_ai_contextualization(self):
        """Test real AI contextualization through Intelligence Engine"""
        print("\n=== Testing Real AI Contextualization ===")

        try:
            # Create sample security event for contextualization
            security_event = {
                'timestamp': datetime.now().isoformat(),
                'source_ip': '*************',
                'destination_ip': '********',
                'user': 'admin',
                'action': 'failed_login',
                'details': 'Multiple failed login attempts detected from suspicious IP'
            }

            # Send for AI analysis
            ai_request = {
                'task': 'contextualize_security_event',
                'event_data': security_event,
                'complexity': 'complex',  # Use high-quality AI models
                'request_id': f'ai_context_{int(time.time())}'
            }

            self.redis_client.publish('intelligence.consensus', json.dumps(ai_request))
            self.log_result("AI Contextualization", True, "Security event sent for AI analysis")

            # Also test pattern discovery
            pattern_request = {
                'log_sample': 'ALERT: Suspicious PowerShell execution detected - Invoke-Expression with encoded command',
                'source_type': 'endpoint_detection',
                'request_id': f'pattern_discovery_{int(time.time())}'
            }

            self.redis_client.publish('ingestion.unknown_patterns', json.dumps(pattern_request))
            self.log_result("AI Pattern Discovery", True, "Unknown pattern sent for AI analysis")
            return True

        except Exception as e:
            self.log_result("AI Contextualization", False, f"Exception: {str(e)}")
            return False

    def test_end_to_end_flow(self):
        """Test complete end-to-end data flow"""
        print("\n=== Testing End-to-End Data Flow ===")

        try:
            # Simulate a complete workflow
            workflow_data = {
                'workflow_id': f'e2e_test_{int(time.time())}',
                'source_engine': 'ingestion',
                'target_engine': 'delivery',
                'data': {
                    'alert_type': 'suspicious_activity',
                    'severity': 'high',
                    'entities': ['*************', 'admin', 'powershell.exe'],
                    'iocs': ['known_malicious_hash', 'suspicious_domain.com']
                }
            }

            # Send workflow through multiple engines
            self.redis_client.publish('workflow.start', json.dumps(workflow_data))
            self.log_result("Workflow Start", True, f"E2E workflow initiated: {workflow_data['workflow_id']}")

            # Send for intelligence processing
            self.redis_client.publish('intelligence.validate', json.dumps({
                'pattern_id': workflow_data['workflow_id'],
                'pattern': workflow_data['data'],
                'type': 'critical'
            }))

            # Send for backend processing
            self.redis_client.publish('backend.store_data', json.dumps({
                'storage_type': 'warm',
                'data': workflow_data['data'],
                'ttl': 86400  # 24 hours
            }))

            self.log_result("End-to-End Flow", True, "Complete workflow sent through all engines")
            return True

        except Exception as e:
            self.log_result("End-to-End Flow", False, f"Exception: {str(e)}")
            return False

    def monitor_redis_responses(self, duration=30):
        """Monitor Redis for responses to our API tests"""
        print(f"\n=== Monitoring Redis Responses for {duration} seconds ===")

        try:
            pubsub = self.redis_client.pubsub()

            # Subscribe to response channels
            response_channels = [
                'intelligence.consensus_result',
                'intelligence.pattern_crystallized',
                'intelligence.pattern_validated',
                'backend.rule_generated',
                'backend.data_stored',
                'contextualization.entities_extracted',
                'delivery.case_created'
            ]

            for channel in response_channels:
                pubsub.subscribe(channel)

            print(f"   [MONITOR] Subscribed to {len(response_channels)} response channels")

            start_time = time.time()
            responses_received = 0

            for message in pubsub.listen():
                if time.time() - start_time > duration:
                    break

                if message['type'] == 'message':
                    channel = message['channel']
                    data = json.loads(message['data'])
                    responses_received += 1

                    print(f"   [RESPONSE] {channel}: {json.dumps(data, indent=2)[:200]}...")

            self.log_result("Redis Monitoring", True, f"Received {responses_received} responses")
            return True

        except Exception as e:
            self.log_result("Redis Monitoring", False, f"Exception: {str(e)}")
            return False

    def run_all_tests(self):
        """Run all real API integration tests"""
        print("=== SIEMLess v2.0 Real API Integration Tests ===")
        print("Testing ACTUAL API calls - NO MOCKS")
        print("=" * 60)

        # Check Redis connection first
        try:
            self.redis_client.ping()
            self.log_result("Redis Connection", True, "Connected successfully")
        except:
            self.log_result("Redis Connection", False, "Cannot connect to Redis")
            return False

        # Run all API tests
        tests = [
            ("CrowdStrike API", self.test_crowdstrike_api),
            ("OpenCTI API", self.test_opencti_api),
            ("ElasticSearch API", self.test_elasticsearch_api),
            ("AI Contextualization", self.test_ai_contextualization),
            ("End-to-End Flow", self.test_end_to_end_flow)
        ]

        for test_name, test_func in tests:
            try:
                test_func()
            except Exception as e:
                self.log_result(test_name, False, f"Unexpected error: {str(e)}")

        # Monitor responses
        self.monitor_redis_responses(30)

        # Summary
        print("\n" + "=" * 60)
        print("TEST SUMMARY:")
        passed = sum(1 for r in self.test_results if r['success'])
        total = len(self.test_results)

        print(f"Passed: {passed}/{total}")
        print(f"Failed: {total - passed}/{total}")

        # Show failed tests
        failed_tests = [r for r in self.test_results if not r['success']]
        if failed_tests:
            print("\nFAILED TESTS:")
            for test in failed_tests:
                print(f"  - {test['test']}: {test['details']}")

        return passed == total

if __name__ == "__main__":
    tester = RealAPITester()
    success = tester.run_all_tests()

    if success:
        print("\n[SUCCESS] All real API integrations working!")
    else:
        print("\n[WARNING] Some API tests failed - check credentials and connectivity")