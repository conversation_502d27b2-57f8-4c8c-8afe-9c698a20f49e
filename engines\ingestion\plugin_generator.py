"""
AI-Powered Context Plugin Generator
Uses Intelligence Engine to automatically generate plugin code from API documentation
"""

import asyncio
import json
import os
from typing import Dict, Any, List, Optional
import redis


class PluginGenerator:
    """
    Generates context source plugins using AI

    Usage:
        generator = PluginGenerator()
        code = await generator.generate_plugin(
            source_name='Sentinel<PERSON>ne',
            api_docs_url='https://docs.sentinelone.com/api',
            categories=['asset', 'detection', 'incident']
        )
    """

    def __init__(self):
        self.redis_client = redis.Redis(
            host=os.getenv('REDIS_HOST', 'localhost'),
            port=int(os.getenv('REDIS_PORT', 6379)),
            decode_responses=True
        )

    async def generate_plugin(
        self,
        source_name: str,
        api_docs: Dict[str, Any] = None,
        api_docs_url: str = None,
        categories: List[str] = None,
        sample_responses: Dict[str, str] = None
    ) -> str:
        """
        Generate a complete plugin from API documentation

        Args:
            source_name: Name of the source (e.g., 'SentinelOne')
            api_docs: Parsed API documentation dict
            api_docs_url: URL to API docs (will be fetched if api_docs not provided)
            categories: Categories to support (e.g., ['asset', 'detection'])
            sample_responses: Sample API responses for each category

        Returns:
            Generated Python code for the plugin
        """

        # Build prompt for Intelligence Engine
        prompt = self._build_generation_prompt(
            source_name, api_docs, api_docs_url, categories, sample_responses
        )

        # Send to Intelligence Engine
        request_id = f"plugin-gen-{source_name.lower()}"

        self.redis_client.publish('intelligence.generate_code', json.dumps({
            'request_id': request_id,
            'task': 'generate_context_plugin',
            'source_name': source_name,
            'prompt': prompt,
            'response_channel': f'ingestion.plugin.{request_id}.complete'
        }))

        # Wait for response
        code = await self._wait_for_response(f'ingestion.plugin.{request_id}.complete', timeout=120)

        if code and 'plugin_code' in code:
            return code['plugin_code']
        else:
            raise Exception(f"Failed to generate plugin: {code.get('error', 'Unknown error')}")

    def _build_generation_prompt(
        self,
        source_name: str,
        api_docs: Dict[str, Any],
        api_docs_url: str,
        categories: List[str],
        sample_responses: Dict[str, str]
    ) -> str:
        """Build comprehensive prompt for AI code generation"""

        prompt = f"""
Generate a complete Context Source Plugin for {source_name}.

## Plugin Requirements

### Base Class
Must inherit from ContextSourcePlugin with these methods:
- get_source_name() -> str
- get_supported_categories() -> List[ContextCategory]
- get_supported_query_types() -> List[str]
- validate_credentials() -> bool
- query_context(query: ContextQuery) -> List[ContextResult]

### Supported Categories
{', '.join(categories) if categories else 'asset, detection, incident'}

### Query Types
Support these query types: ip, hostname, user, file_hash, domain

### Authentication
Extract authentication method from API docs and implement in __init__() and validate_credentials().

### API Endpoints
"""

        if api_docs:
            prompt += f"\n\nAPI Documentation:\n{json.dumps(api_docs, indent=2)}\n"
        elif api_docs_url:
            prompt += f"\n\nAPI Documentation URL: {api_docs_url}\n"
            prompt += "Fetch and parse the API documentation to understand available endpoints.\n"

        if sample_responses:
            prompt += "\n\n### Sample API Responses\n"
            for category, response in sample_responses.items():
                prompt += f"\n**{category} endpoint response:**\n```json\n{response}\n```\n"

        prompt += """

### Field Mapping Standards

Map vendor fields to these standard fields:

**For ASSET category:**
- hostname: Device hostname
- local_ip: Internal IP address
- os_version: Operating system
- last_login_user: Last logged-in user
- mac_address: MAC address
- machine_domain: Domain
- status: Online/offline status
- last_seen: Last activity timestamp

**For DETECTION category:**
- detection_id or alert_id: Unique ID
- severity: Critical/high/medium/low
- status: Open/closed
- created_timestamp: Detection time
- device_hostname: Affected device
- device_ip: Affected IP
- user_name: Affected user
- mitre_techniques: Array of MITRE IDs
- threat_name: Malware/threat name

**For INCIDENT category:**
- incident_id: Unique ID
- name: Incident title
- state: Open/investigating/closed
- severity: Severity score
- assigned_to: Analyst assigned
- tactics: MITRE tactics array
- techniques: MITRE techniques array
- hosts: Affected hosts array

### Code Structure

```python
import aiohttp
from datetime import datetime
from typing import Dict, Any, List
from context_source_plugin import (
    ContextSourcePlugin, ContextQuery, ContextResult, ContextCategory
)


class {source_name}ContextPlugin(ContextSourcePlugin):

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        # Extract credentials from config

    def get_source_name(self) -> str:
        return "{source_name_lower}"

    def get_supported_categories(self) -> List[ContextCategory]:
        return [...]

    def get_supported_query_types(self) -> List[str]:
        return [...]

    async def validate_credentials(self) -> bool:
        # Test API connection
        ...

    async def query_context(self, query: ContextQuery) -> List[ContextResult]:
        # Route to appropriate query method based on categories
        ...

    async def _query_assets(self, query: ContextQuery) -> List[ContextResult]:
        # Query asset API
        # Map response fields to standard format
        # Return List[ContextResult]
        ...

    async def _query_detections(self, query: ContextQuery) -> List[ContextResult]:
        # Query detections API
        # Map response fields to standard format
        # Return List[ContextResult]
        ...
```

### Requirements
1. Use aiohttp for async HTTP requests
2. Implement proper error handling (try/except with logging)
3. Map ALL vendor-specific fields to standard fields
4. Set appropriate confidence scores (0.0-1.0)
5. Handle pagination if API returns paginated results
6. Support time_range filtering when provided in query
7. Return empty list [] if no results found (don't raise exceptions)
8. Log errors using self.logger.error()

### Output Format
Return ONLY the complete Python code. No explanations, no markdown code blocks.
Start with the import statements and end with the last method.

Generate the complete, production-ready plugin code now.
"""

        return prompt

    async def _wait_for_response(self, channel: str, timeout: int = 120) -> Optional[Dict]:
        """Wait for response on Redis channel"""
        import time

        try:
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe(channel)

            start_time = time.time()
            while time.time() - start_time < timeout:
                message = pubsub.get_message(timeout=1)
                if message and message['type'] == 'message':
                    data = json.loads(message['data'])
                    pubsub.unsubscribe(channel)
                    return data.get('data', data)
                await asyncio.sleep(0.1)

            pubsub.unsubscribe(channel)
            return None

        except Exception as e:
            print(f"Error waiting for response: {e}")
            return None

    async def generate_from_swagger(
        self,
        source_name: str,
        swagger_url: str,
        categories: List[str]
    ) -> str:
        """
        Generate plugin from Swagger/OpenAPI documentation

        This fetches the Swagger spec, parses endpoints, and generates the plugin
        """
        import aiohttp

        # Fetch Swagger spec
        async with aiohttp.ClientSession() as session:
            async with session.get(swagger_url) as resp:
                if resp.status == 200:
                    swagger_spec = await resp.json()
                else:
                    raise Exception(f"Failed to fetch Swagger spec: {resp.status}")

        # Parse relevant endpoints
        relevant_endpoints = self._parse_swagger_endpoints(swagger_spec, categories)

        # Generate plugin
        return await self.generate_plugin(
            source_name=source_name,
            api_docs={'swagger_spec': swagger_spec, 'relevant_endpoints': relevant_endpoints},
            categories=categories
        )

    def _parse_swagger_endpoints(self, swagger_spec: Dict, categories: List[str]) -> Dict:
        """Extract relevant endpoints from Swagger spec based on categories"""

        relevant = {}
        paths = swagger_spec.get('paths', {})

        # Map categories to likely endpoint patterns
        category_patterns = {
            'asset': ['agent', 'device', 'host', 'endpoint', 'asset'],
            'detection': ['threat', 'alert', 'detection', 'incident', 'event'],
            'incident': ['incident', 'case', 'investigation'],
            'identity': ['user', 'account', 'identity'],
            'vulnerability': ['vulnerability', 'vuln', 'cve'],
            'network': ['network', 'flow', 'traffic', 'connection']
        }

        for category in categories:
            patterns = category_patterns.get(category, [])
            relevant[category] = []

            for path, methods in paths.items():
                path_lower = path.lower()
                if any(pattern in path_lower for pattern in patterns):
                    # This endpoint might be relevant for this category
                    for method, spec in methods.items():
                        if method.upper() == 'GET':  # Focus on read operations
                            relevant[category].append({
                                'path': path,
                                'method': method,
                                'summary': spec.get('summary', ''),
                                'parameters': spec.get('parameters', []),
                                'responses': spec.get('responses', {})
                            })

        return relevant

    def save_plugin(self, code: str, filename: str):
        """Save generated plugin to file"""
        filepath = os.path.join(os.path.dirname(__file__), filename)
        with open(filepath, 'w') as f:
            f.write(code)
        print(f"Plugin saved to: {filepath}")


async def main():
    """Example usage"""
    generator = PluginGenerator()

    # Example 1: Generate from API docs URL
    print("Generating SentinelOne plugin...")
    code = await generator.generate_plugin(
        source_name='SentinelOne',
        api_docs_url='https://usea1-partners.sentinelone.net/api-doc/overview',
        categories=['asset', 'detection', 'incident']
    )

    generator.save_plugin(code, 'sentinelone_context_plugin.py')
    print("SentinelOne plugin generated!")

    # Example 2: Generate from Swagger
    print("\nGenerating Tenable plugin from Swagger...")
    code = await generator.generate_from_swagger(
        source_name='Tenable',
        swagger_url='https://cloud.tenable.com/api-docs',
        categories=['asset', 'vulnerability']
    )

    generator.save_plugin(code, 'tenable_context_plugin.py')
    print("Tenable plugin generated!")


if __name__ == "__main__":
    print("""
    Context Plugin Generator

    Usage:
        python plugin_generator.py

    Or programmatically:
        from plugin_generator import PluginGenerator

        generator = PluginGenerator()
        code = await generator.generate_plugin(
            source_name='MySource',
            api_docs_url='https://api.mysource.com/docs',
            categories=['asset', 'detection']
        )
        generator.save_plugin(code, 'mysource_context_plugin.py')

    This will use the Intelligence Engine to automatically:
    1. Fetch and parse API documentation
    2. Identify relevant endpoints for each category
    3. Map vendor fields to standard fields
    4. Generate complete, production-ready plugin code
    5. Save to file

    The generated plugin will be ready to use immediately!
    """)

    # Uncomment to run examples
    # asyncio.run(main())
