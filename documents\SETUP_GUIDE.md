# SIEMLess v2.0 - Complete Setup Guide

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Quick Start](#quick-start)
3. [Detailed Engine Setup](#detailed-engine-setup)
4. [Configuration Files](#configuration-files)
5. [Environment Variables](#environment-variables)
6. [API Keys Setup](#api-keys-setup)
7. [Database Setup](#database-setup)
8. [Docker Configuration](#docker-configuration)
9. [Testing & Validation](#testing--validation)
10. [Troubleshooting](#troubleshooting)

---

## Prerequisites

### System Requirements
- **OS**: Linux, macOS, or Windows with WSL2
- **RAM**: Minimum 8GB (16GB recommended)
- **Storage**: 50GB available space
- **CPU**: 4+ cores recommended

### Software Requirements
```bash
# Check versions
docker --version          # Docker 20.10+
docker-compose --version   # Docker Compose 2.0+
python3 --version         # Python 3.11+
psql --version           # PostgreSQL client 14+
redis-cli --version      # Redis client 6.0+
```

### Installation Commands

#### Ubuntu/Debian
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Python and dependencies
sudo apt install python3.11 python3-pip python3-venv postgresql-client redis-tools -y
```

#### macOS
```bash
# Install Homebrew if needed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install required packages
brew install docker docker-compose python@3.11 postgresql redis
```

#### Windows (WSL2)
```powershell
# Install WSL2
wsl --install

# Inside WSL2, follow Ubuntu instructions
```

---

## Quick Start

### 1-Minute Setup
```bash
# Clone repository
git clone https://github.com/your-org/siemless-v2.git
cd siemless-v2

# Create environment file
cat > .env << EOF
POSTGRES_PASSWORD=siemless123
REDIS_PORT=6380
POSTGRES_PORT=5433
POSTGRES_DB=siemless_v2
POSTGRES_USER=siemless
EOF

# Start all services
docker-compose up -d

# Verify health (wait 30 seconds for startup)
sleep 30
for port in 8001 8002 8003 8004 8005; do
    echo "Engine on port $port: $(curl -s http://localhost:$port/health | jq -r .status)"
done

# Run tests
python test_comprehensive_suite.py
```

---

## Detailed Engine Setup

### Intelligence Engine (Port 8001)

#### Configuration
```yaml
# engines/intelligence/config.yaml
engine:
  name: intelligence
  port: 8001

ai_models:
  primary:
    - name: gemma-27b
      provider: google
      cost: 0.0  # FREE
      max_tokens: 2048

  fallback:
    - name: gemini-2.0-flash
      provider: google
      cost: 0.002
    - name: claude-3-haiku
      provider: anthropic
      cost: 0.008
    - name: gpt-4-turbo
      provider: openai
      cost: 0.020

consensus:
  threshold: 0.80  # 80% agreement required
  min_models: 3
  timeout: 30
```

#### Required API Keys
```bash
# At least one AI provider required
export GEMINI_API_KEY="your_key"      # Recommended (low cost)
export OPENAI_API_KEY="your_key"      # Optional
export ANTHROPIC_API_KEY="your_key"   # Optional
```

#### Start Individual Engine
```bash
docker-compose up -d intelligence_engine
docker-compose logs -f intelligence_engine
```

### Backend Engine (Port 8002)

#### Configuration
```yaml
# engines/backend/config.yaml
engine:
  name: backend
  port: 8002

storage:
  hot_tier:
    type: redis
    ttl: 86400  # 24 hours

  warm_tier:
    type: postgresql
    retention: 30  # days

  cold_tier:
    type: s3
    bucket: siemless-archive
    retention: 365  # days

cti:
  otx:
    enabled: true
    api_key: ${OTX_API_KEY}
    sync_interval: 3600

  misp:
    enabled: false
    url: https://misp.example.com
    api_key: ${MISP_API_KEY}
```

#### CTI Setup
```bash
# OTX (AlienVault) - FREE tier available
export OTX_API_KEY="your_otx_key"

# Optional S3 for cold storage
export AWS_ACCESS_KEY_ID="your_key"
export AWS_SECRET_ACCESS_KEY="your_secret"
export S3_BUCKET="siemless-storage"
```

### Ingestion Engine (Port 8003)

#### Configuration
```yaml
# engines/ingestion/config.yaml
engine:
  name: ingestion
  port: 8003

sources:
  syslog:
    enabled: true
    port: 514
    protocol: udp

  http:
    enabled: true
    port: 8080
    endpoint: /logs

  kafka:
    enabled: false
    brokers: ["localhost:9092"]
    topics: ["security-logs"]

github_sync:
  enabled: true
  repositories:
    - url: https://github.com/siemless/patterns
      branch: main
      path: /patterns
  sync_interval: 3600

hot_reload:
  enabled: true
  max_deploy_time_ms: 100
```

#### Pattern Sources Setup
```bash
# Create local pattern directory
mkdir -p engines/library/patterns

# Add GitHub token for private repos (optional)
export GITHUB_TOKEN="your_github_token"

# Test pattern sync
python engines/ingestion/github_pattern_sync.py
```

### Contextualization Engine (Port 8004)

#### Configuration
```yaml
# engines/contextualization/config.yaml
engine:
  name: contextualization
  port: 8004

enrichment:
  geoip:
    enabled: true
    database: /data/GeoLite2-City.mmdb

  threat_intel:
    enabled: true
    sources:
      - otx
      - abuseipdb

  asset_inventory:
    enabled: false
    source: cmdb
    api_url: ${CMDB_API_URL}

entity_extraction:
  types:
    - user
    - device
    - process
    - file
    - network

  normalization:
    enabled: true
    rules:
      - "DOMAIN\\user -> <EMAIL>"
      - "IPv6 -> IPv4 mapping"
```

#### Enrichment Data Setup
```bash
# Download GeoIP database (FREE with registration)
wget https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-City
tar -xzf GeoLite2-City.tar.gz
cp GeoLite2-City*/GeoLite2-City.mmdb engines/contextualization/data/

# Add threat intel API keys
export ABUSEIPDB_KEY="your_key"
```

### Delivery Engine (Port 8005)

#### Configuration
```yaml
# engines/delivery/config.yaml
engine:
  name: delivery
  port: 8005

api:
  enabled: true
  cors:
    origins: ["http://localhost:3000"]

  endpoints:
    - /api/cases
    - /api/alerts
    - /api/workflow
    - /api/dashboard

alerts:
  channels:
    email:
      enabled: true
      smtp_server: smtp.gmail.com
      port: 587
      username: ${SMTP_USERNAME}
      password: ${SMTP_PASSWORD}

    slack:
      enabled: true
      webhook_url: ${SLACK_WEBHOOK}

    pagerduty:
      enabled: false
      api_key: ${PAGERDUTY_KEY}

workflow:
  templates_dir: /app/workflow_templates
  max_concurrent: 10
  default_timeout: 300
```

#### Alert Channels Setup
```bash
# Email configuration
export SMTP_USERNAME="<EMAIL>"
export SMTP_PASSWORD="your_password"

# Slack webhook
export SLACK_WEBHOOK="https://hooks.slack.com/services/xxx/yyy/zzz"

# Create alert directories
mkdir -p alerts
```

---

## Configuration Files

### Main Docker Compose Configuration

```yaml
# docker-compose.yml
version: '3.8'

services:
  # Infrastructure
  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  postgres:
    image: postgres:14-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Engines
  intelligence_engine:
    build:
      context: ./engines/intelligence
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - ENGINE_NAME=intelligence
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    depends_on:
      - redis
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  backend_engine:
    build:
      context: ./engines/backend
      dockerfile: Dockerfile
    ports:
      - "8002:8002"
    environment:
      - ENGINE_NAME=backend
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - OTX_API_KEY=${OTX_API_KEY}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - S3_BUCKET=${S3_BUCKET}
    depends_on:
      - redis
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ... similar configuration for other engines

volumes:
  redis_data:
  postgres_data:

networks:
  default:
    name: siemless_network
```

### Python Requirements

```txt
# requirements.txt
# Core dependencies
redis==5.0.1
redis[hiredis]==5.0.1
psycopg2-binary==2.9.9
aiohttp==3.9.1
asyncio==3.4.3

# AI providers
openai==1.6.1
anthropic==0.8.1
google-generativeai==0.3.2

# Data processing
pandas==2.1.4
numpy==1.26.2
regex==2023.12.25

# CTI integration
OTXv2==1.5.12

# Storage
boto3==1.34.11  # AWS S3

# Monitoring
prometheus-client==0.19.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
```

---

## Environment Variables

### Complete .env Template

```bash
# .env
# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5433
POSTGRES_DB=siemless_v2
POSTGRES_USER=siemless
POSTGRES_PASSWORD=siemless123

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6380

# AI Provider Keys (at least one required)
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxx
ANTHROPIC_API_KEY=sk-ant-xxxxxxxxxxxxx
GEMINI_API_KEY=AIzaxxxxxxxxxxxxxxxxxxxxx

# CTI Integration
OTX_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Storage (optional)
AWS_ACCESS_KEY_ID=AKIAXXXXXXXXXXXXX
AWS_SECRET_ACCESS_KEY=xxxxxxxxxxxxxxxxxxxxxxxx
AWS_REGION=us-east-1
S3_BUCKET=siemless-storage

# Alert Channels (optional)
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=xxxxxxxxxxxx
SMTP_FROM=<EMAIL>

SLACK_WEBHOOK=https://hooks.slack.com/services/xxx/yyy/zzz
PAGERDUTY_KEY=xxxxxxxxxxxxxxxxxxxx

# GitHub Integration (optional)
GITHUB_TOKEN=ghp_xxxxxxxxxxxxxxxxxxxx

# Monitoring (optional)
GRAFANA_PASSWORD=admin
PROMETHEUS_RETENTION=7d

# Security
JWT_SECRET=your-secret-key-change-this
API_KEY=xxxxxxxxxxxxxxxxxxxxxxxx

# Performance
MAX_WORKERS=10
BATCH_SIZE=100
CACHE_TTL=3600
```

---

## API Keys Setup

### Getting API Keys

#### Google Gemini (Recommended - Low Cost)
1. Visit: https://makersuite.google.com/app/apikey
2. Click "Create API Key"
3. Copy the key starting with "AIza..."
4. Add to .env: `GEMINI_API_KEY=AIza...`

#### OpenAI
1. Visit: https://platform.openai.com/api-keys
2. Create new secret key
3. Copy the key starting with "sk-..."
4. Add to .env: `OPENAI_API_KEY=sk-...`

#### Anthropic Claude
1. Visit: https://console.anthropic.com/
2. Go to API Keys section
3. Create new key
4. Add to .env: `ANTHROPIC_API_KEY=sk-ant-...`

#### AlienVault OTX (FREE)
1. Register at: https://otx.alienvault.com/
2. Go to Settings → API Integration
3. Copy your OTX Key
4. Add to .env: `OTX_API_KEY=...`

---

## Database Setup

### Initialize Database

```bash
# Connect to PostgreSQL
psql -h localhost -p 5433 -U siemless -d postgres

# Create databases
CREATE DATABASE siemless_v2;
CREATE DATABASE siemless_v3;  # For future migration

# Connect to siemless_v2
\c siemless_v2

# Run initialization script
\i sql/init.sql

# Verify tables created
\dt

# Create initial data
INSERT INTO engine_coordination (engine_name, status, version) VALUES
('intelligence', 'initializing', 'v2.0'),
('backend', 'initializing', 'v2.0'),
('ingestion', 'initializing', 'v2.0'),
('contextualization', 'initializing', 'v2.0'),
('delivery', 'initializing', 'v2.0');
```

### Database Migrations

```bash
# Run migrations
python manage_db.py migrate

# Create backup
pg_dump -h localhost -p 5433 -U siemless siemless_v2 > backup_$(date +%Y%m%d).sql

# Restore from backup
psql -h localhost -p 5433 -U siemless siemless_v2 < backup_20240101.sql
```

---

## Docker Configuration

### Build Images

```bash
# Build all images
docker-compose build

# Build specific engine
docker-compose build intelligence_engine

# Build with no cache
docker-compose build --no-cache
```

### Container Management

```bash
# Start all containers
docker-compose up -d

# Start specific engines
docker-compose up -d redis postgres
docker-compose up -d intelligence_engine backend_engine

# View logs
docker-compose logs -f
docker-compose logs -f intelligence_engine

# Stop containers
docker-compose stop
docker-compose down  # Also removes containers

# Restart engine
docker-compose restart intelligence_engine

# Execute command in container
docker-compose exec intelligence_engine bash
docker-compose exec postgres psql -U siemless -d siemless_v2
```

### Resource Limits

```yaml
# docker-compose.override.yml
version: '3.8'

services:
  intelligence_engine:
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  redis:
    deploy:
      resources:
        limits:
          memory: 512M

  postgres:
    deploy:
      resources:
        limits:
          memory: 1G
```

---

## Testing & Validation

### System Health Checks

```bash
# Check all engine health
./scripts/health_check.sh

# Manual health checks
curl http://localhost:8001/health | jq
curl http://localhost:8002/health | jq
curl http://localhost:8003/health | jq
curl http://localhost:8004/health | jq
curl http://localhost:8005/health | jq

# Check Redis
redis-cli -p 6380 ping

# Check PostgreSQL
psql -h localhost -p 5433 -U siemless -d siemless_v2 -c "SELECT 1"
```

### Run Test Suites

```bash
# Comprehensive test
python test_comprehensive_suite.py

# Individual engine tests
python test_individual_engines.py

# Async pattern tests
python test_async_patterns.py

# Workflow execution test
python test_real_workflow_execution.py

# CTI integration test
python test_cti_via_redis.py

# Performance test
python test_performance.py --duration 60 --concurrent 10
```

### Validate Workflows

```bash
# Test incident response workflow
curl -X POST http://localhost:8005/api/workflow/start \
  -H "Content-Type: application/json" \
  -d '{
    "workflow_type": "incident_response",
    "context": {
      "incident_title": "Test Incident",
      "priority": "high"
    }
  }'

# Check workflow status
curl http://localhost:8005/api/workflow/{workflow_id}/status | jq
```

---

## Troubleshooting

### Common Issues

#### Engines Not Starting
```bash
# Check logs
docker-compose logs intelligence_engine | tail -50

# Common fixes:
# 1. Port conflicts
lsof -i :8001  # Check if port in use
# 2. Database not ready
docker-compose restart intelligence_engine  # After 30 seconds
# 3. Missing environment variables
env | grep -E "(REDIS|POSTGRES|API_KEY)"
```

#### Database Connection Failed
```bash
# Verify PostgreSQL is running
docker-compose ps postgres

# Test connection
psql -h localhost -p 5433 -U siemless -d siemless_v2

# Common fixes:
# 1. Wrong port (should be 5433, not 5432)
# 2. Database doesn't exist
docker-compose exec postgres psql -U siemless -c "CREATE DATABASE siemless_v2"
```

#### Redis Connection Issues
```bash
# Test Redis
redis-cli -p 6380 ping

# Monitor Redis
redis-cli -p 6380 monitor

# Flush Redis if needed (CAUTION)
redis-cli -p 6380 FLUSHALL
```

#### Health Check Failures
```bash
# Check if using correct pattern
# WRONG: python requests (not installed in container)
# RIGHT: curl (always available)

# Fix in docker-compose.yml:
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
```

#### Async/Event Loop Issues
```bash
# Symptom: Health endpoints not responding
# Cause: Synchronous Redis blocking event loop

# Solution: Ensure using redis.asyncio
# Check base_engine.py has:
import redis.asyncio as redis_async
```

### Debug Mode

```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Run engine in foreground
docker-compose up intelligence_engine  # No -d flag

# Interactive debugging
docker-compose exec intelligence_engine python
>>> from intelligence_engine import IntelligenceEngine
>>> engine = IntelligenceEngine()
>>> engine.test_connection()
```

### Reset Everything

```bash
# Stop all containers
docker-compose down

# Remove volumes (CAUTION: Deletes all data)
docker-compose down -v

# Remove all images
docker-compose down --rmi all

# Fresh start
docker-compose up --build -d
```

---

## Production Deployment

### Security Hardening

```bash
# Use secrets instead of environment variables
docker secret create postgres_password ./secrets/postgres_password.txt

# Update docker-compose.yml
services:
  postgres:
    secrets:
      - postgres_password
    environment:
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
```

### SSL/TLS Configuration

```nginx
# nginx.conf for reverse proxy
server {
    listen 443 ssl;
    server_name siemless.company.com;

    ssl_certificate /etc/ssl/certs/siemless.crt;
    ssl_certificate_key /etc/ssl/private/siemless.key;

    location /api/ {
        proxy_pass http://localhost:8005/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Monitoring Setup

```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.retention.time=7d'
      - '--storage.tsdb.retention.size=1GB'

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - ./monitoring/grafana:/etc/grafana/provisioning
      - grafana_data:/var/lib/grafana

volumes:
  prometheus_data:
  grafana_data:
```

### Backup Strategy

```bash
# Automated backup script
#!/bin/bash
# backup.sh

BACKUP_DIR="/backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# Backup PostgreSQL
docker-compose exec -T postgres pg_dumpall -U siemless > $BACKUP_DIR/postgres.sql

# Backup Redis
docker-compose exec -T redis redis-cli SAVE
docker cp $(docker-compose ps -q redis):/data/dump.rdb $BACKUP_DIR/redis.rdb

# Backup configurations
tar -czf $BACKUP_DIR/configs.tar.gz .env docker-compose.yml engines/*/config.yaml

# Upload to S3 (optional)
aws s3 sync $BACKUP_DIR s3://siemless-backups/$(date +%Y%m%d)/
```

---

## Support & Resources

### Documentation
- Project Index: `/PROJECT_INDEX.md`
- Architecture: `/ARCHITECTURE.md`
- API Documentation: `/API_DOCUMENTATION.md`
- Troubleshooting Guide: `/TROUBLESHOOTING_GUIDE.md`

### Community
- GitHub Issues: https://github.com/siemless/siemless-v2/issues
- Discord: https://discord.gg/siemless
- Slack: siemless.slack.com

### Professional Support
- Email: <EMAIL>
- Enterprise: <EMAIL>

---

*SIEMLess v2.0: Learn Expensive Once → Operate Free Forever*