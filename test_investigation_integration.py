"""
Test End-to-End Investigation Integration
Tests the complete flow: SIEM Alert → Investigation → Enrichment → Dashboard
"""

import asyncio
import json
import requests
import redis
from datetime import datetime

# Configuration
DELIVERY_URL = "http://localhost:8005"
REDIS_HOST = "localhost"
REDIS_PORT = 6380

def test_investigation_api():
    """Test investigation REST API endpoints"""
    print("\n=== Testing Investigation API ===")

    # Test 1: List investigations (should be empty initially)
    print("\n1. GET /api/v1/investigations - List all investigations")
    response = requests.get(f"{DELIVERY_URL}/api/v1/investigations")
    print(f"   Status: {response.status_code}")
    data = response.json()
    print(f"   Count: {data.get('count', 0)}")
    assert response.status_code == 200
    print("   [OK] List investigations endpoint working")

    # Test 2: Get investigation stats
    print("\n2. GET /api/v1/investigations/stats - Get statistics")
    response = requests.get(f"{DELIVERY_URL}/api/v1/investigations/stats")
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        stats = response.json()
        print(f"   Stats: {json.dumps(stats, indent=2)}")
        print("   [OK] Investigation stats endpoint working")
    else:
        print(f"   [WARN] Stats endpoint returned {response.status_code}")

    # Test 3: Create manual investigation
    print("\n3. POST /api/v1/investigations - Create manual investigation")
    investigation_data = {
        "title": "Test Investigation: Suspicious PowerShell Activity",
        "severity": "high",
        "alert_id": "test-alert-001",
        "entities": {
            "ips": ["*************", "*********"],
            "users": ["admin", "jdoe"],
            "hosts": ["WORKSTATION-01"]
        },
        "mitre_techniques": ["T1059.001", "T1055"],
        "timestamp": datetime.utcnow().isoformat()
    }

    response = requests.post(
        f"{DELIVERY_URL}/api/v1/investigations",
        json=investigation_data
    )
    print(f"   Status: {response.status_code}")

    if response.status_code == 200:
        result = response.json()
        investigation_id = result.get('investigation_id')
        print(f"   Investigation ID: {investigation_id}")
        print(f"   Risk Score: {result.get('risk_score', 'N/A')}")
        print(f"   Status: {result.get('status', 'N/A')}")
        print("   [OK] Investigation created successfully")

        # Test 4: Get investigation details
        print(f"\n4. GET /api/v1/investigations/{investigation_id} - Get details")
        response = requests.get(f"{DELIVERY_URL}/api/v1/investigations/{investigation_id}")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            inv_details = response.json()
            print(f"   Title: {inv_details.get('title')}")
            print(f"   Entities: {len(inv_details.get('entities', {}))} types")
            print(f"   Timeline Events: {len(inv_details.get('timeline', []))}")
            print("   [OK] Investigation details retrieved")

        # Test 5: Add note to investigation
        print(f"\n5. POST /api/v1/investigations/{investigation_id}/notes - Add note")
        note_data = {
            "note": "Initial triage complete - escalating to Tier 2",
            "author": "SOC Analyst"
        }
        response = requests.post(
            f"{DELIVERY_URL}/api/v1/investigations/{investigation_id}/notes",
            json=note_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   [OK] Note added successfully")

        # Test 6: Assign investigation
        print(f"\n6. POST /api/v1/investigations/{investigation_id}/assign - Assign to analyst")
        assign_data = {
            "assignee": "<EMAIL>"
        }
        response = requests.post(
            f"{DELIVERY_URL}/api/v1/investigations/{investigation_id}/assign",
            json=assign_data
        )
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("   [OK] Investigation assigned")

        return investigation_id
    else:
        print(f"   [ERROR] Failed to create investigation: {response.text}")
        return None


def test_alert_triggered_investigation():
    """Test auto-investigation from SIEM alert"""
    print("\n=== Testing Alert-Triggered Investigation ===")

    # Connect to Redis
    r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)

    # Simulate SIEM alert (high severity - should trigger investigation)
    print("\n1. Publishing high-severity alert to 'ingestion.alerts.received'")
    alert_data = {
        "alert_id": "siem-alert-12345",
        "title": "Malicious Process Execution Detected",
        "description": "Suspicious PowerShell process with encoded command detected",
        "severity": "high",  # This should trigger auto-investigation
        "source_siem": "elastic",
        "timestamp": datetime.utcnow().isoformat(),
        "entities": {
            "ips": ["************"],
            "users": ["compromised_user"],
            "hosts": ["LAPTOP-WIN10"],
            "processes": ["powershell.exe"]
        },
        "mitre_techniques": ["T1059.001", "T1027"],
        "raw_alert": {
            "rule_name": "Encoded PowerShell Command",
            "rule_id": "elastic-rule-001"
        }
    }

    r.publish('ingestion.alerts.received', json.dumps(alert_data))
    print("   [OK] Alert published to Redis")

    # Wait for investigation to be created
    print("\n2. Waiting 3 seconds for auto-investigation creation...")
    import time
    time.sleep(3)

    # Check if investigation was created
    print("\n3. Checking for new investigations")
    response = requests.get(f"{DELIVERY_URL}/api/v1/investigations")

    if response.status_code == 200:
        data = response.json()
        investigations = data.get('investigations', [])
        print(f"   Total investigations: {len(investigations)}")

        # Look for our auto-created investigation
        auto_created = [inv for inv in investigations if 'Malicious Process' in inv.get('title', '')]

        if auto_created:
            print(f"   [OK] Auto-investigation created!")
            inv = auto_created[0]
            print(f"   Investigation ID: {inv.get('investigation_id')}")
            print(f"   Title: {inv.get('title')}")
            print(f"   Risk Score: {inv.get('risk_score')}")
            print(f"   Status: {inv.get('status')}")
            return inv.get('investigation_id')
        else:
            print("   [WARN] Auto-investigation not found yet")
            print(f"   Current investigations: {[inv.get('title') for inv in investigations]}")
    else:
        print(f"   [ERROR] Failed to list investigations: {response.status_code}")

    return None


def test_medium_severity_no_investigation():
    """Test that medium severity alerts DON'T trigger auto-investigation"""
    print("\n=== Testing Medium Severity Alert (Should NOT Create Investigation) ===")

    r = redis.Redis(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)

    # Get current investigation count
    response = requests.get(f"{DELIVERY_URL}/api/v1/investigations")
    before_count = len(response.json().get('investigations', []))
    print(f"\n1. Current investigation count: {before_count}")

    # Publish medium severity alert
    print("\n2. Publishing medium-severity alert (should NOT trigger investigation)")
    alert_data = {
        "alert_id": "siem-alert-67890",
        "title": "Failed Login Attempt",
        "severity": "medium",  # Should NOT trigger auto-investigation
        "source_siem": "splunk",
        "timestamp": datetime.utcnow().isoformat(),
        "entities": {
            "users": ["test_user"],
            "ips": ["**********"]
        }
    }

    r.publish('ingestion.alerts.received', json.dumps(alert_data))
    print("   [OK] Alert published")

    # Wait and check
    import time
    time.sleep(2)

    response = requests.get(f"{DELIVERY_URL}/api/v1/investigations")
    after_count = len(response.json().get('investigations', []))
    print(f"\n3. Investigation count after medium alert: {after_count}")

    if after_count == before_count:
        print("   [OK] Medium severity alert correctly did NOT create investigation")
    else:
        print("   [WARN] Investigation count changed unexpectedly")


def test_investigation_workflow():
    """Test complete investigation lifecycle"""
    print("\n=== Testing Complete Investigation Lifecycle ===")

    # Create investigation
    investigation_data = {
        "title": "Lifecycle Test: Ransomware Activity",
        "severity": "critical",
        "entities": {
            "ips": ["*************"],
            "users": ["victim_user"],
            "hosts": ["SERVER-01"]
        },
        "mitre_techniques": ["T1486", "T1490"]
    }

    print("\n1. Creating investigation...")
    response = requests.post(
        f"{DELIVERY_URL}/api/v1/investigations",
        json=investigation_data
    )

    if response.status_code != 200:
        print(f"   [ERROR] Failed to create investigation")
        return

    inv_id = response.json().get('investigation_id')
    print(f"   [OK] Investigation created: {inv_id}")

    # Add evidence
    print("\n2. Adding evidence...")
    evidence_data = {
        "evidence_type": "file_hash",
        "value": "abc123def456",
        "description": "Malicious file hash identified",
        "source": "VirusTotal"
    }
    response = requests.post(
        f"{DELIVERY_URL}/api/v1/investigations/{inv_id}/evidence",
        json=evidence_data
    )
    print(f"   Status: {response.status_code}")

    # Update status
    print("\n3. Updating status to 'investigating'...")
    update_data = {
        "status": "investigating"
    }
    response = requests.patch(
        f"{DELIVERY_URL}/api/v1/investigations/{inv_id}",
        json=update_data
    )
    print(f"   Status: {response.status_code}")

    # Close investigation
    print("\n4. Closing investigation...")
    close_data = {
        "resolution": "Contained and remediated",
        "closed_by": "SOC Lead"
    }
    response = requests.post(
        f"{DELIVERY_URL}/api/v1/investigations/{inv_id}/close",
        json=close_data
    )
    print(f"   Status: {response.status_code}")

    # Verify closed
    response = requests.get(f"{DELIVERY_URL}/api/v1/investigations/{inv_id}")
    if response.status_code == 200:
        inv = response.json()
        print(f"   Final Status: {inv.get('status')}")
        print(f"   Resolution: {inv.get('resolution')}")
        print("   [OK] Investigation lifecycle complete")


if __name__ == "__main__":
    print("=" * 70)
    print("SIEMLess v2.0 - Investigation Integration Test")
    print("=" * 70)

    try:
        # Test REST API
        manual_inv_id = test_investigation_api()

        # Test auto-investigation from alerts
        auto_inv_id = test_alert_triggered_investigation()

        # Test that medium severity doesn't trigger
        test_medium_severity_no_investigation()

        # Test complete lifecycle
        test_investigation_workflow()

        print("\n" + "=" * 70)
        print("INTEGRATION TEST COMPLETE")
        print("=" * 70)
        print("\nSummary:")
        print(f"- Manual investigation created: {manual_inv_id}")
        print(f"- Auto investigation created: {auto_inv_id or 'Not verified'}")
        print("- API endpoints: Working")
        print("- Alert integration: Working")
        print("- Lifecycle management: Working")

    except Exception as e:
        print(f"\n[ERROR] Test failed: {e}")
        import traceback
        traceback.print_exc()
