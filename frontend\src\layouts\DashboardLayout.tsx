import React, { useRef, useCallback } from 'react'
import { Layout, Model, IJsonModel, TabNode, Action, ITabSetRenderValues } from 'flexlayout-react'
import 'flexlayout-react/style/light.css'

// Widget component factory
import { WidgetFactory } from '../widgets/WidgetFactory'

// Default layout configuration
const defaultLayoutModel: IJsonModel = {
  global: {
    tabEnableClose: true,
    tabEnableFloat: true,
    tabEnableRename: true,
    tabSetEnableMaximize: true,
    tabSetEnableDrag: true,
    tabSetEnableClose: true,
  },
  borders: [],
  layout: {
    type: "row",
    weight: 100,
    children: [
      {
        type: "row",
        weight: 70,
        children: [
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Alert Queue",
                component: "AlertQueue",
                config: { refreshInterval: 5000 }
              }
            ]
          },
          {
            type: "tabset",
            weight: 50,
            children: [
              {
                type: "tab",
                name: "Entity Graph",
                component: "EntityGraph",
                config: {}
              }
            ]
          }
        ]
      },
      {
        type: "row",
        weight: 30,
        children: [
          {
            type: "tabset",
            weight: 100,
            children: [
              {
                type: "tab",
                name: "Pattern Library",
                component: "PatternLibrary",
                config: {}
              },
              {
                type: "tab",
                name: "CTI Feeds",
                component: "CTIFeeds",
                config: {}
              }
            ]
          }
        ]
      }
    ]
  }
}

interface DashboardLayoutProps {
  initialModel?: IJsonModel
  onModelChange?: (model: Model) => void
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  initialModel = defaultLayoutModel,
  onModelChange
}) => {
  const layoutRef = useRef<Layout>(null)
  const [model] = React.useState(() => Model.fromJson(initialModel))

  // Factory function to create components
  const factory = useCallback((node: TabNode) => {
    const component = node.getComponent()
    const config = node.getConfig()

    return <WidgetFactory componentName={component} config={config} />
  }, [])

  // Handle model changes
  const onAction = useCallback((action: Action) => {
    if (onModelChange) {
      // Allow the action and notify parent
      onModelChange(model)
    }
    return action
  }, [model, onModelChange])

  // Custom tab rendering
  const onRenderTabSet = useCallback((tabSetNode: any, renderValues: ITabSetRenderValues) => {
    renderValues.stickyButtons.push(
      <button
        key="add"
        className="flexlayout__button"
        onClick={() => {
          // Add new tab logic
          const newTab = {
            type: "tab",
            name: "New Widget",
            component: "AlertQueue",
            config: {}
          }
          model.doAction(Action.addNode(newTab, tabSetNode.getId(), 0, -1))
        }}
        title="Add Widget"
      >
        +
      </button>
    )
  }, [model])

  return (
    <div className="h-screen w-full">
      <Layout
        ref={layoutRef}
        model={model}
        factory={factory}
        onAction={onAction}
        onRenderTabSet={onRenderTabSet}
      />
    </div>
  )
}

export default DashboardLayout