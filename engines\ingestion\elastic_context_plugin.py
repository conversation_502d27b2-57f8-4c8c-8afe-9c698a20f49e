"""
Elastic Security Context Source Plugin
Queries Elastic SIEM for investigation context using ECS (Elastic Common Schema)
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from elasticsearch import Elasticsearch
from context_source_plugin import (
    ContextSourcePlugin, ContextQuery, ContextResult, ContextCategory
)


class ElasticContextPlugin(ContextSourcePlugin):
    """
    Elastic Security plugin using official elasticsearch-py SDK
    Supports querying ECS-normalized logs and Elastic Security alerts
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.elastic_url = config.get('elastic_url')
        self.cloud_id = config.get('cloud_id')  # Elastic Cloud support
        self.api_key = config.get('api_key')
        self.username = config.get('username')
        self.password = config.get('password')
        self.verify_certs = config.get('verify_certs', True)
        self.client = None

    def get_source_name(self) -> str:
        return "elastic"

    def get_supported_categories(self) -> List[ContextCategory]:
        return [
            ContextCategory.ASSET,      # Host/endpoint data from logs
            ContextCategory.DETECTION,  # Elastic Security alerts
            ContextCategory.LOG,        # Raw ECS logs
            ContextCategory.NETWORK,    # Network flows
        ]

    def get_supported_query_types(self) -> List[str]:
        return ['ip', 'hostname', 'user', 'hash', 'domain', 'process']

    async def validate_credentials(self) -> bool:
        """Initialize Elasticsearch client and test connection"""
        try:
            # Initialize client - prioritize cloud_id if available
            if self.cloud_id and self.api_key:
                self.logger.info(f"Connecting to Elastic Cloud: {self.cloud_id.split(':')[0]}")
                self.client = Elasticsearch(
                    cloud_id=self.cloud_id,
                    api_key=self.api_key,
                    request_timeout=30
                )
            elif self.elastic_url and self.api_key:
                self.client = Elasticsearch(
                    [self.elastic_url],
                    api_key=self.api_key,
                    verify_certs=self.verify_certs
                )
            elif self.elastic_url:
                self.client = Elasticsearch(
                    [self.elastic_url],
                    basic_auth=(self.username, self.password),
                    verify_certs=self.verify_certs
                )
            else:
                self.logger.error("No Elastic connection info provided (need cloud_id or elastic_url)")
                return False

            # Test connection
            if self.client.ping():
                info = self.client.info()
                self.logger.info(f"Connected to Elasticsearch {info['version']['number']}")
                return True
            else:
                self.logger.error("Elasticsearch ping failed")
                return False

        except Exception as e:
            self.logger.error(f"Elasticsearch connection error: {e}")
            return False

    async def query_context(self, query: ContextQuery) -> List[ContextResult]:
        """Main query handler"""
        results = []

        for category in query.categories:
            if category == ContextCategory.ASSET:
                results.extend(await self._query_assets(query))
            elif category == ContextCategory.DETECTION:
                results.extend(await self._query_detections(query))
            elif category == ContextCategory.LOG:
                results.extend(await self._query_logs(query))
            elif category == ContextCategory.NETWORK:
                results.extend(await self._query_network(query))

        return results

    async def _query_assets(self, query: ContextQuery) -> List[ContextResult]:
        """Query for asset/host information from logs"""
        results = []

        try:
            # Build Elasticsearch query for host data
            es_query = self._build_asset_query(query)

            # Search across common log indices
            response = self.client.search(
                index=['logs-*', 'filebeat-*', 'winlogbeat-*', 'metricbeat-*'],
                body=es_query,
                size=50
            )

            # Aggregate host information
            host_data = self._aggregate_host_data(response['hits']['hits'])

            for host_id, host_info in host_data.items():
                results.append(self._format_asset_result(host_info))

        except Exception as e:
            self.logger.error(f"Error querying Elastic assets: {e}", exc_info=True)

        return results

    async def _query_detections(self, query: ContextQuery) -> List[ContextResult]:
        """Query Elastic Security alerts"""
        results = []

        try:
            # Build query for security alerts
            es_query = self._build_detection_query(query)

            # Search security alerts index
            response = self.client.search(
                index=['.alerts-security.alerts-*', 'logs-endpoint.alerts-*'],
                body=es_query,
                size=50
            )

            for hit in response['hits']['hits']:
                results.append(self._format_detection_result(hit['_source']))

        except Exception as e:
            self.logger.error(f"Error querying Elastic detections: {e}", exc_info=True)

        return results

    async def _query_logs(self, query: ContextQuery) -> List[ContextResult]:
        """Query raw ECS logs"""
        results = []

        try:
            es_query = self._build_log_query(query)

            response = self.client.search(
                index=['logs-*', 'filebeat-*'],
                body=es_query,
                size=100
            )

            for hit in response['hits']['hits']:
                results.append(self._format_log_result(hit['_source']))

        except Exception as e:
            self.logger.error(f"Error querying Elastic logs: {e}", exc_info=True)

        return results

    async def _query_network(self, query: ContextQuery) -> List[ContextResult]:
        """Query network flow data"""
        results = []

        try:
            es_query = self._build_network_query(query)

            response = self.client.search(
                index=['logs-network.*', 'packetbeat-*', 'logs-zeek.*'],
                body=es_query,
                size=50
            )

            for hit in response['hits']['hits']:
                results.append(self._format_network_result(hit['_source']))

        except Exception as e:
            self.logger.error(f"Error querying Elastic network: {e}", exc_info=True)

        return results

    def _build_asset_query(self, query: ContextQuery) -> Dict:
        """Build Elasticsearch query for asset data"""
        # Time range (default: last 7 days)
        time_range = query.time_range or {
            'start': (datetime.utcnow() - timedelta(days=7)).isoformat(),
            'end': datetime.utcnow().isoformat()
        }

        # Build query based on query type
        if query.query_type == 'hostname':
            must_clause = [{"match": {"host.name": query.query_value}}]
        elif query.query_type == 'ip':
            must_clause = [
                {"bool": {"should": [
                    {"match": {"host.ip": query.query_value}},
                    {"match": {"source.ip": query.query_value}},
                    {"match": {"destination.ip": query.query_value}}
                ]}}
            ]
        elif query.query_type == 'user':
            must_clause = [{"match": {"user.name": query.query_value}}]
        else:
            must_clause = []

        return {
            "query": {
                "bool": {
                    "must": must_clause,
                    "filter": [{
                        "range": {
                            "@timestamp": {
                                "gte": time_range['start'],
                                "lte": time_range['end']
                            }
                        }
                    }]
                }
            },
            "aggs": {
                "by_host": {
                    "terms": {"field": "host.name.keyword", "size": 50}
                }
            }
        }

    def _build_detection_query(self, query: ContextQuery) -> Dict:
        """Build query for Elastic Security alerts"""
        time_range = query.time_range or {
            'start': (datetime.utcnow() - timedelta(days=30)).isoformat(),
            'end': datetime.utcnow().isoformat()
        }

        # Build query based on entity type
        if query.query_type == 'hostname':
            must_clause = [{"match": {"host.name": query.query_value}}]
        elif query.query_type == 'ip':
            must_clause = [
                {"bool": {"should": [
                    {"match": {"source.ip": query.query_value}},
                    {"match": {"destination.ip": query.query_value}},
                    {"match": {"host.ip": query.query_value}}
                ]}}
            ]
        elif query.query_type == 'user':
            must_clause = [{"match": {"user.name": query.query_value}}]
        elif query.query_type == 'hash':
            must_clause = [
                {"bool": {"should": [
                    {"match": {"file.hash.sha256": query.query_value}},
                    {"match": {"process.hash.sha256": query.query_value}}
                ]}}
            ]
        else:
            must_clause = []

        return {
            "query": {
                "bool": {
                    "must": must_clause + [{"term": {"event.kind": "alert"}}],
                    "filter": [{
                        "range": {
                            "@timestamp": {
                                "gte": time_range['start'],
                                "lte": time_range['end']
                            }
                        }
                    }]
                }
            },
            "sort": [{"@timestamp": "desc"}]
        }

    def _build_log_query(self, query: ContextQuery) -> Dict:
        """Build query for raw logs"""
        time_range = query.time_range or {
            'start': (datetime.utcnow() - timedelta(hours=24)).isoformat(),
            'end': datetime.utcnow().isoformat()
        }

        if query.query_type == 'hostname':
            must_clause = [{"match": {"host.name": query.query_value}}]
        elif query.query_type == 'ip':
            must_clause = [
                {"bool": {"should": [
                    {"match": {"source.ip": query.query_value}},
                    {"match": {"destination.ip": query.query_value}}
                ]}}
            ]
        elif query.query_type == 'process':
            must_clause = [{"match": {"process.name": query.query_value}}]
        else:
            must_clause = []

        return {
            "query": {
                "bool": {
                    "must": must_clause,
                    "filter": [{
                        "range": {
                            "@timestamp": {
                                "gte": time_range['start'],
                                "lte": time_range['end']
                            }
                        }
                    }]
                }
            },
            "sort": [{"@timestamp": "desc"}]
        }

    def _build_network_query(self, query: ContextQuery) -> Dict:
        """Build query for network flows"""
        time_range = query.time_range or {
            'start': (datetime.utcnow() - timedelta(hours=24)).isoformat(),
            'end': datetime.utcnow().isoformat()
        }

        if query.query_type == 'ip':
            must_clause = [
                {"bool": {"should": [
                    {"match": {"source.ip": query.query_value}},
                    {"match": {"destination.ip": query.query_value}}
                ]}}
            ]
        elif query.query_type == 'domain':
            must_clause = [{"match": {"destination.domain": query.query_value}}]
        else:
            must_clause = []

        return {
            "query": {
                "bool": {
                    "must": must_clause,
                    "filter": [{
                        "range": {
                            "@timestamp": {
                                "gte": time_range['start'],
                                "lte": time_range['end']
                            }
                        }
                    }]
                }
            },
            "sort": [{"@timestamp": "desc"}]
        }

    def _aggregate_host_data(self, hits: List[Dict]) -> Dict[str, Dict]:
        """Aggregate host information from multiple log entries"""
        hosts = {}

        for hit in hits:
            source = hit.get('_source', {})
            host = source.get('host', {})
            hostname = host.get('name')

            if not hostname:
                continue

            if hostname not in hosts:
                hosts[hostname] = {
                    'hostname': hostname,
                    'ips': set(),
                    'os': host.get('os', {}).get('full'),
                    'os_version': host.get('os', {}).get('version'),
                    'mac_addresses': set(),
                    'users': set(),
                    'processes': set(),
                    'first_seen': source.get('@timestamp'),
                    'last_seen': source.get('@timestamp'),
                    'log_count': 0
                }

            # Aggregate data
            host_data = hosts[hostname]
            host_data['log_count'] += 1

            # Update last seen
            if source.get('@timestamp'):
                if source['@timestamp'] > host_data.get('last_seen', ''):
                    host_data['last_seen'] = source['@timestamp']

            # Collect IPs
            if host.get('ip'):
                if isinstance(host['ip'], list):
                    host_data['ips'].update(host['ip'])
                else:
                    host_data['ips'].add(host['ip'])

            # Collect MACs
            if host.get('mac'):
                if isinstance(host['mac'], list):
                    host_data['mac_addresses'].update(host['mac'])
                else:
                    host_data['mac_addresses'].add(host['mac'])

            # Collect users
            user = source.get('user', {})
            if user.get('name'):
                host_data['users'].add(user['name'])

            # Collect processes
            process = source.get('process', {})
            if process.get('name'):
                host_data['processes'].add(process['name'])

        # Convert sets to lists for JSON serialization
        for host_id, host_data in hosts.items():
            host_data['ips'] = list(host_data['ips'])
            host_data['mac_addresses'] = list(host_data['mac_addresses'])
            host_data['users'] = list(host_data['users'])
            host_data['processes'] = list(host_data['processes'])

        return hosts

    def _format_asset_result(self, host_data: Dict) -> ContextResult:
        """Format aggregated host data into ContextResult"""
        return ContextResult(
            source_name=self.get_source_name(),
            category=ContextCategory.ASSET,
            confidence=0.90,  # High confidence from aggregated data
            data={
                'hostname': host_data.get('hostname'),
                'local_ip': host_data.get('ips', [None])[0] if host_data.get('ips') else None,
                'all_ips': host_data.get('ips', []),
                'mac_addresses': host_data.get('mac_addresses', []),
                'os_version': host_data.get('os') or host_data.get('os_version'),
                'users': host_data.get('users', []),
                'processes': host_data.get('processes', []),
                'first_seen': host_data.get('first_seen'),
                'last_seen': host_data.get('last_seen'),
                'log_count': host_data.get('log_count', 0)
            },
            timestamp=host_data.get('last_seen'),
            metadata={'raw': host_data, 'source_type': 'aggregated_logs'}
        )

    def _format_detection_result(self, alert: Dict) -> ContextResult:
        """Format Elastic Security alert into ContextResult"""
        kibana = alert.get('kibana', {}).get('alert', {})

        return ContextResult(
            source_name=self.get_source_name(),
            category=ContextCategory.DETECTION,
            confidence=0.95,
            data={
                'alert_id': alert.get('_id'),
                'rule_name': kibana.get('rule', {}).get('name'),
                'severity': kibana.get('severity'),
                'risk_score': kibana.get('risk_score'),
                'hostname': alert.get('host', {}).get('name'),
                'user': alert.get('user', {}).get('name'),
                'source_ip': alert.get('source', {}).get('ip'),
                'destination_ip': alert.get('destination', {}).get('ip'),
                'process': alert.get('process', {}).get('name'),
                'file_hash': alert.get('file', {}).get('hash', {}).get('sha256'),
                'status': kibana.get('workflow_status'),
                'description': kibana.get('reason')
            },
            timestamp=alert.get('@timestamp'),
            metadata={'raw': alert}
        )

    def _format_log_result(self, log: Dict) -> ContextResult:
        """Format raw ECS log into ContextResult"""
        return ContextResult(
            source_name=self.get_source_name(),
            category=ContextCategory.LOG,
            confidence=0.85,
            data={
                'timestamp': log.get('@timestamp'),
                'event_action': log.get('event', {}).get('action'),
                'event_category': log.get('event', {}).get('category'),
                'hostname': log.get('host', {}).get('name'),
                'source_ip': log.get('source', {}).get('ip'),
                'destination_ip': log.get('destination', {}).get('ip'),
                'user': log.get('user', {}).get('name'),
                'process': log.get('process', {}).get('name'),
                'message': log.get('message')
            },
            timestamp=log.get('@timestamp'),
            metadata={'raw': log}
        )

    def _format_network_result(self, flow: Dict) -> ContextResult:
        """Format network flow data into ContextResult"""
        return ContextResult(
            source_name=self.get_source_name(),
            category=ContextCategory.NETWORK,
            confidence=0.90,
            data={
                'timestamp': flow.get('@timestamp'),
                'source_ip': flow.get('source', {}).get('ip'),
                'source_port': flow.get('source', {}).get('port'),
                'destination_ip': flow.get('destination', {}).get('ip'),
                'destination_port': flow.get('destination', {}).get('port'),
                'destination_domain': flow.get('destination', {}).get('domain'),
                'protocol': flow.get('network', {}).get('protocol'),
                'bytes_in': flow.get('source', {}).get('bytes'),
                'bytes_out': flow.get('destination', {}).get('bytes'),
                'hostname': flow.get('host', {}).get('name')
            },
            timestamp=flow.get('@timestamp'),
            metadata={'raw': flow}
        )
