"""
Comprehensive endpoint testing after AsyncPG migration
Tests all documented endpoints from CODEBASE_INDEX.md
"""
import requests
import json
from typing import Dict, List

# Test results tracking
results = {
    'passed': [],
    'failed': [],
    'skipped': []
}

def test_endpoint(name: str, url: str, method: str = 'GET', data: Dict = None, expected_keys: List[str] = None):
    """Test an endpoint and track results"""
    try:
        if method == 'GET':
            response = requests.get(url, timeout=5)
        elif method == 'POST':
            response = requests.post(url, json=data, timeout=5)

        # Check response
        if response.status_code >= 500:
            results['failed'].append(f"{name}: HTTP {response.status_code} - {response.text[:100]}")
            return False

        # Try to parse JSON
        try:
            json_data = response.json()

            # Check for error field
            if isinstance(json_data, dict) and 'error' in json_data:
                # Some errors are expected (e.g., empty data)
                if 'not found' in json_data['error'].lower() or 'not initialized' in json_data['error'].lower():
                    results['passed'].append(f"{name}: OK (expected empty/not initialized)")
                    return True
                else:
                    results['failed'].append(f"{name}: Error - {json_data['error'][:100]}")
                    return False

            # Check expected keys if provided
            if expected_keys and isinstance(json_data, dict):
                missing = [k for k in expected_keys if k not in json_data]
                if missing:
                    results['failed'].append(f"{name}: Missing keys - {missing}")
                    return False

            results['passed'].append(f"{name}: OK")
            return True

        except json.JSONDecodeError:
            if response.status_code == 404:
                results['skipped'].append(f"{name}: 404 Not Found")
            else:
                results['failed'].append(f"{name}: Invalid JSON response")
            return False

    except requests.RequestException as e:
        results['failed'].append(f"{name}: Request failed - {str(e)[:100]}")
        return False

print("=" * 80)
print("AsyncPG Migration - Comprehensive Endpoint Testing")
print("=" * 80)
print()

# ============================================================================
# BACKEND ENGINE (Port 8002) - Database-heavy endpoints
# ============================================================================
print("Testing Backend Engine (8002)...")
print("-" * 80)

# Graph API endpoints
test_endpoint("Graph Stats", "http://localhost:8002/api/graph/stats")
test_endpoint("Graph Centrality", "http://localhost:8002/api/graph/centrality?limit=10")

# Log Source Quality
test_endpoint("Log Sources List", "http://localhost:8002/api/log-quality/sources")
test_endpoint("Log Quality Stats", "http://localhost:8002/api/log-quality/stats")

# Detection Fidelity
test_endpoint("Detection Coverage", "http://localhost:8002/api/detection/coverage")
test_endpoint("Detection Gaps", "http://localhost:8002/api/detection/gaps")

# Correlation
test_endpoint("Correlation Requirements", "http://localhost:8002/api/correlation/requirements")

# MITRE ATT&CK
test_endpoint("MITRE Techniques", "http://localhost:8002/api/mitre/techniques")
test_endpoint("MITRE Tactics", "http://localhost:8002/api/mitre/tactics")

print()

# ============================================================================
# DELIVERY ENGINE (Port 8005) - Frontend-facing APIs
# ============================================================================
print("Testing Delivery Engine (8005)...")
print("-" * 80)

# Rules API
test_endpoint("List Rules", "http://localhost:8005/api/rules?limit=5", expected_keys=['rules', 'total'])
test_endpoint("List Rules - Quality Filter", "http://localhost:8005/api/rules?quality_label=high&limit=5")

# Patterns API
test_endpoint("List Patterns", "http://localhost:8005/api/patterns?limit=5", expected_keys=['patterns'])

# Entities API
test_endpoint("List Entities", "http://localhost:8005/api/entities?limit=5", expected_keys=['entities'])

# Cases API
test_endpoint("List Cases", "http://localhost:8005/api/cases", expected_keys=['cases'])

# Dashboard API
test_endpoint("Dashboard Data", "http://localhost:8005/api/dashboard")

# Alerts API
test_endpoint("List Alerts", "http://localhost:8005/api/alerts")

# Workflows API
test_endpoint("List Workflows", "http://localhost:8005/api/workflows")

print()

# ============================================================================
# INGESTION ENGINE (Port 8003) - Data ingestion APIs
# ============================================================================
print("Testing Ingestion Engine (8003)...")
print("-" * 80)

# Sources API
test_endpoint("List Sources", "http://localhost:8003/api/sources")
test_endpoint("Source Stats", "http://localhost:8003/api/sources/stats")

# Parsers API
test_endpoint("List Parsers", "http://localhost:8003/api/parsers")

# CTI API
test_endpoint("List CTI Sources", "http://localhost:8003/api/cti/sources")
test_endpoint("CTI Stats", "http://localhost:8003/api/cti/stats")

print()

# ============================================================================
# CONTEXTUALIZATION ENGINE (Port 8004) - Entity extraction
# ============================================================================
print("Testing Contextualization Engine (8004)...")
print("-" * 80)

# No documented API endpoints currently
results['skipped'].append("Contextualization: No public API endpoints documented")

print()

# ============================================================================
# INTELLIGENCE ENGINE (Port 8001) - AI operations
# ============================================================================
print("Testing Intelligence Engine (8001)...")
print("-" * 80)

# No documented API endpoints currently
results['skipped'].append("Intelligence: No public API endpoints documented")

print()

# ============================================================================
# RESULTS SUMMARY
# ============================================================================
print("=" * 80)
print("TEST RESULTS SUMMARY")
print("=" * 80)
print()

print(f"PASSED: {len(results['passed'])}")
for r in results['passed']:
    print(f"   {r}")
print()

if results['failed']:
    print(f"FAILED: {len(results['failed'])}")
    for r in results['failed']:
        print(f"   {r}")
    print()

if results['skipped']:
    print(f"SKIPPED: {len(results['skipped'])}")
    for r in results['skipped']:
        print(f"   {r}")
    print()

total_tests = len(results['passed']) + len(results['failed']) + len(results['skipped'])
pass_rate = (len(results['passed']) / (total_tests - len(results['skipped'])) * 100) if (total_tests - len(results['skipped'])) > 0 else 0

print("=" * 80)
print(f"Total Tests: {total_tests}")
print(f"Pass Rate: {pass_rate:.1f}%")
print("=" * 80)

# Exit with error code if there were failures
import sys
sys.exit(0 if len(results['failed']) == 0 else 1)
