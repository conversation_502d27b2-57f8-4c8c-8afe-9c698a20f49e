# API Credentials Configuration
# NEVER COMMIT THIS FILE TO GIT!
# Copy this to api_credentials.yaml and fill in your keys
# Hot-reloadable - changes take effect without restart

credentials:
  # ============================================================================
  # GOOGLE AI
  # ============================================================================
  google:
    # Primary key
    primary: ${GOOGLE_API_KEY}  # Can use environment variable
    # Optional: Secondary keys for rotation (rate limit handling)
    secondary:
      - AIza...your_backup_key_1
      - AIza...your_backup_key_2
    # API endpoint (optional override)
    endpoint: https://generativelanguage.googleapis.com

  # ============================================================================
  # ANTHROPIC (CLAUDE)
  # ============================================================================
  anthropic:
    primary: ${ANTHROPIC_API_KEY}
    secondary: []
    endpoint: https://api.anthropic.com

  # ============================================================================
  # OPENAI
  # ============================================================================
  openai:
    primary: ${OPENAI_API_KEY}
    secondary: []
    endpoint: https://api.openai.com/v1
    organization: ${OPENAI_ORG_ID}  # Optional

  # ============================================================================
  # OLLAMA (LOCAL)
  # ============================================================================
  ollama:
    primary: ""  # No key needed for local
    endpoint: http://localhost:11434

# ============================================================================
# KEY ROTATION STRATEGY
# ============================================================================

rotation:
  enabled: true
  strategy: round_robin  # Options: round_robin, failover, least_used

  # When a key hits rate limit, pause before retrying
  rate_limit_pause: 60  # seconds

  # Track usage per key to distribute load
  track_usage: true

  # Auto-rotate when approaching rate limit
  auto_rotate_threshold: 0.9  # 90% of rate limit

# ============================================================================
# KEY VALIDATION
# ============================================================================

validation:
  # Validate keys on load
  validate_on_load: true

  # Validation method
  method: simple_test_call  # Options: simple_test_call, cache_only

  # Timeout for validation calls
  timeout: 10  # seconds

  # Cache valid keys (avoid re-validating constantly)
  cache_valid_keys: true
  cache_duration: 3600  # 1 hour

# ============================================================================
# SECURITY
# ============================================================================

security:
  # Mask keys in logs (show only first 8 and last 4 chars)
  mask_in_logs: true

  # Never log full keys
  log_full_keys: false

  # Encrypt keys in memory (future enhancement)
  encrypt_in_memory: false

# ============================================================================
# HOT-RELOAD SETTINGS
# ============================================================================

hot_reload:
  enabled: true
  watch_interval: 10  # Check for changes every 10 seconds
  validate_before_reload: true
  rollback_on_invalid: true
