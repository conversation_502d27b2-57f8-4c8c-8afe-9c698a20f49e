# SIEMLess Authentication Proxy Configuration

# Redirect HTTP to HTTPS (when certificates are configured)
server {
    listen 80;
    server_name localhost;

    # Health check endpoint (no auth)
    location /health {
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # For now, proxy to services (later redirect to HTTPS)
    location / {
        proxy_pass http://delivery_engine;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Main HTTPS server (when certificates are ready)
# server {
#     listen 443 ssl http2;
#     server_name localhost;
#
#     ssl_certificate /etc/nginx/certs/siemless.crt;
#     ssl_certificate_key /etc/nginx/certs/siemless.key;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers HIGH:!aNULL:!MD5;
#
#     # Protected API endpoints
#     location /api/ {
#         # Authentication check
#         auth_request /auth;
#         auth_request_set $auth_status $upstream_status;
#         auth_request_set $auth_user $upstream_http_x_auth_user;
#         auth_request_set $auth_roles $upstream_http_x_auth_roles;
#
#         # Pass auth info to backend
#         proxy_set_header X-Auth-User $auth_user;
#         proxy_set_header X-Auth-Roles $auth_roles;
#
#         # Proxy to Delivery Engine
#         proxy_pass http://delivery_engine;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#
#         # WebSocket support
#         proxy_http_version 1.1;
#         proxy_set_header Upgrade $http_upgrade;
#         proxy_set_header Connection "upgrade";
#     }
#
#     # Authentication endpoint (internal)
#     location = /auth {
#         internal;
#         proxy_pass http://keycloak/auth/realms/siemless/protocol/openid-connect/userinfo;
#         proxy_pass_request_body off;
#         proxy_set_header Content-Length "";
#         proxy_set_header X-Original-URI $request_uri;
#     }
# }

# Keycloak admin and authentication endpoints
server {
    listen 8080;
    server_name localhost;

    location / {
        proxy_pass http://keycloak;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Required for Keycloak
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
}