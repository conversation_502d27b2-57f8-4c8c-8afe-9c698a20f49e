"""
CrowdStrike API Scope Configuration

This module defines the available CrowdStrike API scopes and their mappings
to use cases and entity types.
"""

from typing import Dict, List, Any
from dataclasses import dataclass, field
from enum import Enum


class CrowdStrikeScope(Enum):
    """Available CrowdStrike API scopes."""
    ALERTS_READ = "alerts:read"
    DETECTIONS_READ = "detections:read"
    EVENT_STREAMS_READ = "event-streams:read"
    HOSTS_READ = "hosts:read"
    INCIDENTS_READ = "incidents:read"
    INTEL_READ = "intel:read"
    IOCS_READ = "iocs:read"


@dataclass
class ScopeMapping:
    """Mapping configuration for a CrowdStrike scope."""
    scope: CrowdStrikeScope
    description: str
    api_endpoints: List[str]
    entity_types: List[str]
    use_case_types: List[str]
    data_fields: Dict[str, str] = field(default_factory=dict)
    required_for: List[str] = field(default_factory=list)


# Define scope mappings
CROWDSTRIKE_SCOPE_MAPPINGS = {
    CrowdStrikeScope.ALERTS_READ: ScopeMapping(
        scope=CrowdStrikeScope.ALERTS_READ,
        description="Read alerts and alert details",
        api_endpoints=[
            "/alerts/queries/alerts/v1",
            "/alerts/entities/alerts/v1"
        ],
        entity_types=["alert", "user", "device", "process"],
        use_case_types=["threat_detection", "alert_triage"],
        data_fields={
            "alert_id": "id",
            "severity": "severity",
            "status": "status",
            "assigned_to": "assigned_to_uid",
            "created_time": "created_timestamp"
        },
        required_for=["alert_management", "soc_operations"]
    ),

    CrowdStrikeScope.DETECTIONS_READ: ScopeMapping(
        scope=CrowdStrikeScope.DETECTIONS_READ,
        description="Read detections and detection details",
        api_endpoints=[
            "/detects/queries/detects/v1",
            "/detects/entities/summaries/GET/v1"
        ],
        entity_types=["detection", "user", "device", "process", "file"],
        use_case_types=["threat_detection", "credential_dumping", "process_execution"],
        data_fields={
            "detection_id": "detection_id",
            "severity": "max_severity",
            "tactic": "behaviors.tactic",
            "technique": "behaviors.technique",
            "user": "behaviors.user_name",
            "device": "device.hostname",
            "process": "behaviors.filename",
            "command_line": "behaviors.cmdline"
        },
        required_for=["threat_hunting", "incident_response"]
    ),

    CrowdStrikeScope.EVENT_STREAMS_READ: ScopeMapping(
        scope=CrowdStrikeScope.EVENT_STREAMS_READ,
        description="Read raw event streams for detailed telemetry",
        api_endpoints=[
            "/sensors/entities/datafeed/v2",
            "/sensors/entities/datafeed-actions/v1"
        ],
        entity_types=["event", "process", "network", "file", "registry"],
        use_case_types=["process_monitoring", "network_analysis", "file_tracking"],
        data_fields={
            "event_id": "event_id",
            "event_type": "event_simpleName",
            "timestamp": "timestamp",
            "process_name": "ImageFileName",
            "parent_process": "ParentImageFileName",
            "network_connection": "RemoteAddress",
            "file_path": "TargetFileName"
        },
        required_for=["forensics", "detailed_investigation"]
    ),

    CrowdStrikeScope.HOSTS_READ: ScopeMapping(
        scope=CrowdStrikeScope.HOSTS_READ,
        description="Read host/device information",
        api_endpoints=[
            "/devices/queries/devices-scroll/v1",
            "/devices/entities/devices/v1"
        ],
        entity_types=["device", "user", "group"],
        use_case_types=["asset_inventory", "device_compliance"],
        data_fields={
            "device_id": "device_id",
            "hostname": "hostname",
            "os_version": "os_version",
            "last_seen": "last_seen",
            "status": "status",
            "user": "last_login_user",
            "domain": "machine_domain"
        },
        required_for=["asset_management", "compliance_reporting"]
    ),

    CrowdStrikeScope.INCIDENTS_READ: ScopeMapping(
        scope=CrowdStrikeScope.INCIDENTS_READ,
        description="Read incidents and incident details",
        api_endpoints=[
            "/incidents/queries/incidents/v1",
            "/incidents/entities/incidents/GET/v1"
        ],
        entity_types=["incident", "user", "device", "detection"],
        use_case_types=["incident_management", "breach_investigation"],
        data_fields={
            "incident_id": "incident_id",
            "state": "state",
            "severity": "fine_score",
            "assigned_to": "assigned_to_name",
            "tactics": "tactics",
            "techniques": "techniques",
            "objectives": "objectives"
        },
        required_for=["incident_response", "executive_reporting"]
    ),

    CrowdStrikeScope.INTEL_READ: ScopeMapping(
        scope=CrowdStrikeScope.INTEL_READ,
        description="Read threat intelligence indicators and reports",
        api_endpoints=[
            "/intel/queries/indicators/v1",
            "/intel/entities/indicators/GET/v1",
            "/intel/queries/reports/v1"
        ],
        entity_types=["indicator", "threat_actor", "malware", "vulnerability"],
        use_case_types=["threat_intelligence", "ioc_monitoring"],
        data_fields={
            "indicator": "indicator",
            "type": "type",
            "threat_types": "threat_types",
            "malware_families": "malware_families",
            "actor": "actors"
        },
        required_for=["threat_intel_enrichment", "proactive_hunting"]
    ),

    CrowdStrikeScope.IOCS_READ: ScopeMapping(
        scope=CrowdStrikeScope.IOCS_READ,
        description="Read custom IOCs (Indicators of Compromise)",
        api_endpoints=[
            "/indicators/queries/iocs/v1",
            "/indicators/entities/iocs/v1"
        ],
        entity_types=["ioc", "file", "network", "domain"],
        use_case_types=["ioc_detection", "custom_detection"],
        data_fields={
            "ioc_value": "value",
            "ioc_type": "type",
            "severity": "severity",
            "action": "action",
            "platforms": "platforms"
        },
        required_for=["custom_detection_rules", "ioc_management"]
    )
}


class CrowdStrikeScopeManager:
    """Manages CrowdStrike API scope configurations and mappings."""

    def __init__(self, client_id: str, environment_id: str):
        self.client_id = client_id
        self.environment_id = environment_id
        self.configured_scopes: List[CrowdStrikeScope] = []

    def configure_scopes(self, scopes: List[str]) -> Dict[str, Any]:
        """
        Configure which scopes are available for this client.

        Args:
            scopes: List of scope strings (e.g., ["detections:read", "hosts:read"])

        Returns:
            Configuration summary with available features
        """
        self.configured_scopes = []
        available_features = {
            "entity_types": set(),
            "use_case_types": set(),
            "api_endpoints": set(),
            "required_for": set()
        }

        for scope_str in scopes:
            try:
                scope = CrowdStrikeScope(scope_str)
                self.configured_scopes.append(scope)

                mapping = CROWDSTRIKE_SCOPE_MAPPINGS[scope]
                available_features["entity_types"].update(mapping.entity_types)
                available_features["use_case_types"].update(mapping.use_case_types)
                available_features["api_endpoints"].update(mapping.api_endpoints)
                available_features["required_for"].update(mapping.required_for)
            except (ValueError, KeyError):
                continue

        return {
            "configured_scopes": [s.value for s in self.configured_scopes],
            "available_features": {
                k: list(v) for k, v in available_features.items()
            }
        }

    def get_field_mappings_for_scope(self, scope: str) -> Dict[str, str]:
        """Get field mappings for a specific scope."""
        try:
            scope_enum = CrowdStrikeScope(scope)
            return CROWDSTRIKE_SCOPE_MAPPINGS[scope_enum].data_fields
        except (ValueError, KeyError):
            return {}

    def get_required_scopes_for_use_case(self, use_case_type: str) -> List[str]:
        """Get required scopes for a specific use case type."""
        required_scopes = []

        for scope, mapping in CROWDSTRIKE_SCOPE_MAPPINGS.items():
            if use_case_type in mapping.use_case_types:
                required_scopes.append(scope.value)

        return required_scopes

    def validate_scope_requirements(self, required_features: List[str]) -> Dict[str, Any]:
        """
        Validate if configured scopes meet requirements.

        Args:
            required_features: List of required features

        Returns:
            Validation result with missing scopes if any
        """
        configured_features = set()
        for scope in self.configured_scopes:
            mapping = CROWDSTRIKE_SCOPE_MAPPINGS[scope]
            configured_features.update(mapping.required_for)

        missing_features = set(required_features) - configured_features
        missing_scopes = []

        for feature in missing_features:
            for scope, mapping in CROWDSTRIKE_SCOPE_MAPPINGS.items():
                if feature in mapping.required_for and scope not in self.configured_scopes:
                    missing_scopes.append(scope.value)

        return {
            "valid": len(missing_features) == 0,
            "missing_features": list(missing_features),
            "required_scopes": list(set(missing_scopes))
        }

    def generate_enhanced_use_case(self, base_use_case: Dict[str, Any]) -> Dict[str, Any]:
        """
        Enhance a use case with scope-specific field mappings.

        Args:
            base_use_case: Base use case definition

        Returns:
            Enhanced use case with proper field mappings
        """
        enhanced = base_use_case.copy()

        # Add field mappings based on configured scopes
        all_field_mappings = {}
        for scope in self.configured_scopes:
            mapping = CROWDSTRIKE_SCOPE_MAPPINGS[scope]
            all_field_mappings.update(mapping.data_fields)

        # Update entity mappings with scope-specific fields
        if "events" in enhanced:
            for event in enhanced["events"]:
                if "entity_mapping" in event:
                    # Enhance with available field mappings
                    for entity_key, field_name in event["entity_mapping"].items():
                        if field_name in all_field_mappings.values():
                            # Field is available through configured scopes
                            event["scope_validated"] = True

        enhanced["field_mappings"] = all_field_mappings
        enhanced["configured_scopes"] = [s.value for s in self.configured_scopes]

        return enhanced


# Convenience functions for API usage
def get_crowdstrike_scope_requirements(use_case_type: str) -> List[str]:
    """Get required CrowdStrike API scopes for a use case type."""
    manager = CrowdStrikeScopeManager("", "")
    return manager.get_required_scopes_for_use_case(use_case_type)


def validate_crowdstrike_config(scopes: List[str], required_features: List[str]) -> Dict[str, Any]:
    """Validate if CrowdStrike configuration meets requirements."""
    manager = CrowdStrikeScopeManager("", "")
    manager.configure_scopes(scopes)
    return manager.validate_scope_requirements(required_features)