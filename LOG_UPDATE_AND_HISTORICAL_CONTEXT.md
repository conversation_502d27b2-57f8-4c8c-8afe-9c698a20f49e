# Log Update & Historical Context System

## Date: October 2, 2025

## Overview

Comprehensive system for **regular log updates** and **historical context retrieval** with **most-recent-first priority**. Essential for investigation workflows and continuous threat detection.

## Problem Solved

Traditional SIEMs struggle with:
- ❌ Irregular log collection creating gaps in timelines
- ❌ Historical queries that prioritize old data over recent events
- ❌ No intelligent polling based on source activity
- ❌ Difficult investigation context gathering across time windows

Our solution:
- ✅ Regular, intelligent polling of all log sources
- ✅ **Most-recent-first** retrieval for all historical queries
- ✅ Dynamic interval adjustment based on activity
- ✅ Comprehensive historical context for investigations

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    LOG UPDATE SYSTEM                        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Log Sources (EDR, SIEM, Firewall, Cloud)                 │
│           ↓                                                 │
│  ┌─────────────────────────────────────┐                  │
│  │  Log Update Poller                  │                  │
│  │  - Configurable intervals per source│                  │
│  │  - Checkpoint-based continuation    │                  │
│  │  - Dynamic interval adjustment      │                  │
│  │  - Backfill for gaps               │                  │
│  └─────────────────────────────────────┘                  │
│           ↓                                                 │
│  Ingestion Pipeline                                        │
│           ↓                                                 │
│  Contextualization (entity extraction)                     │
│           ↓                                                 │
│  Backend Storage (events, relationships, sessions)         │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│              HISTORICAL CONTEXT SYSTEM                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Investigation Query (entity + time range)                 │
│           ↓                                                 │
│  ┌─────────────────────────────────────┐                  │
│  │  Historical Context Manager         │                  │
│  │  - Recent events (DESC order)       │                  │
│  │  - Relationships (last interaction) │                  │
│  │  - Sessions (latest first)          │                  │
│  │  - Timeline (chronological)         │                  │
│  │  - Risk evolution                   │                  │
│  └─────────────────────────────────────┘                  │
│           ↓                                                 │
│  Complete Historical Context                               │
│  - Entity history                                          │
│  - Activity timeline                                        │
│  - Risk evolution                                           │
│  - Summary & patterns                                       │
└─────────────────────────────────────────────────────────────┘
```

## Key Components

### 1. Log Update Poller

**File**: `engines/ingestion/log_update_poller.py` (600+ lines)

**Features**:
- **Regular Polling**: Configurable intervals per source type
- **Most-Recent-First**: Always prioritizes newest logs
- **Checkpoint System**: Never misses logs, resumes from last position
- **Dynamic Adjustment**: Changes polling frequency based on activity
- **Backfill Support**: Fills historical gaps on demand
- **Health Monitoring**: Alerts on stale sources

**Default Polling Intervals**:
```python
{
    'edr': 60,           # 1 minute (high activity - CrowdStrike, SentinelOne)
    'siem': 300,         # 5 minutes (aggregated - Splunk, Elastic)
    'firewall': 120,     # 2 minutes (moderate - Palo Alto, Fortinet)
    'cloud': 180,        # 3 minutes (AWS, Azure, GCP)
    'threat_intel': 3600 # 1 hour (CTI feeds)
}
```

**Dynamic Adjustment Logic**:
- **High activity** (>5000 logs/poll): Reduce interval by 50%
- **Low activity** (<100 logs/poll): Increase interval by 100%
- **Normal activity**: Maintain current interval

### 2. Historical Context Manager

**File**: `engines/backend/historical_context_manager.py` (700+ lines)

**Features**:
- **Entity-Centric History**: Complete timeline for any entity
- **Most-Recent-First Queries**: All queries ordered DESC
- **Multi-Dimensional Context**:
  - Recent events (last N hours)
  - Relationships (ordered by last interaction)
  - Sessions (grouped activity)
  - Timeline (chronological view)
  - Risk evolution (score changes over time)
- **Multi-Entity Context**: Intersection analysis for investigations
- **Smart Caching**: Redis caching with appropriate TTLs

## Data Flow

### Log Update Flow

```
1. Poller checks if time to poll source (interval elapsed)
   ↓
2. Calculate time range: last_checkpoint → now
   ↓
3. Query source API with recent-first ordering:
   - EDR: timestamp|desc
   - SIEM: @timestamp desc
   - Firewall: time_generated desc
   - Cloud: order desc
   ↓
4. Fetch logs (up to fetch_size limit)
   ↓
5. Send to ingestion pipeline via Redis
   ↓
6. Update checkpoint to most recent timestamp
   ↓
7. Adjust polling interval based on activity
```

### Historical Context Flow

```
1. Investigation requests entity history (e.g., IP, user, host)
   ↓
2. Check Redis cache first (5-min TTL for recent events)
   ↓
3. If miss, query database:
   a. Get entity details
   b. Get recent events (ORDER BY event_timestamp DESC)
   c. Get relationships (ORDER BY last_seen DESC)
   d. Get sessions (ORDER BY start_time DESC)
   e. Build timeline (ORDER BY timestamp DESC)
   f. Get risk evolution (ORDER BY timestamp DESC)
   ↓
4. Generate summary:
   - Activity counts
   - Risk trend (increasing/decreasing/stable)
   - Temporal patterns
   - Unusual timing detection
   ↓
5. Cache result (5-30 min TTL based on data type)
   ↓
6. Return complete historical context
```

## SQL Queries (Most-Recent-First)

### Recent Events
```sql
SELECT *
FROM events
WHERE event_timestamp >= '2025-10-01T00:00:00Z'
AND entities_involved @> '{"ip":"*******"}'::jsonb
ORDER BY event_timestamp DESC  -- MOST RECENT FIRST
LIMIT 1000;
```

### Relationships (Last Interaction)
```sql
SELECT *
FROM relationships
WHERE last_seen >= '2025-10-01T00:00:00Z'
AND (
    (from_entity_type = 'ip' AND from_entity_value = '*******')
    OR (to_entity_type = 'ip' AND to_entity_value = '*******')
)
ORDER BY last_seen DESC  -- MOST RECENT INTERACTION
LIMIT 500;
```

### Sessions (Latest First)
```sql
SELECT *
FROM sessions
WHERE start_time >= '2025-10-01T00:00:00Z'
AND entities_involved @> '{"user":"admin"}'::jsonb
ORDER BY start_time DESC  -- LATEST SESSION FIRST
LIMIT 100;
```

### Timeline (Chronological)
```sql
WITH timeline AS (
    -- Events
    SELECT 'event' as type, event_id as id, event_timestamp as ts
    FROM events
    WHERE event_timestamp >= '2025-10-01T00:00:00Z'

    UNION ALL

    -- Relationships
    SELECT 'relationship', relationship_id, last_seen
    FROM relationships
    WHERE last_seen >= '2025-10-01T00:00:00Z'

    UNION ALL

    -- Sessions
    SELECT 'session', session_id, end_time
    FROM sessions
    WHERE end_time >= '2025-10-01T00:00:00Z'
)
SELECT * FROM timeline
ORDER BY ts DESC  -- MOST RECENT FIRST
LIMIT 1000;
```

### Risk Evolution
```sql
SELECT
    timestamp,
    risk_score,
    reason,
    contributing_factors
FROM entity_risk_history
WHERE entity_type = 'ip'
AND entity_value = '*******'
AND timestamp >= '2025-10-01T00:00:00Z'
ORDER BY timestamp DESC  -- MOST RECENT CHANGES FIRST
LIMIT 100;
```

## API Integration

### Add Polling Source
```python
from log_update_poller import LogUpdatePoller

poller = LogUpdatePoller(redis_client, db_connection)

# Add CrowdStrike EDR
await poller.add_source({
    'source_id': 'crowdstrike_production',
    'source_type': 'edr',
    'poll_interval': 60,  # 1 minute
    'fetch_size': 1000,
    'time_window_minutes': 5,
    'enabled': True
})

# Start polling
await poller.start()
```

### Get Historical Context
```python
from historical_context_manager import HistoricalContextManager

context_mgr = HistoricalContextManager(db_connection, redis_client)

# Get last 24 hours for IP
context = await context_mgr.get_entity_history(
    entity_type='ip',
    entity_value='*************',
    hours_back=24,
    max_results=1000
)

print(f"Entity: {context.entity}")
print(f"Recent events: {len(context.recent_events)}")
print(f"Relationships: {len(context.relationships)}")
print(f"Risk trend: {context.summary['risk_summary']['risk_trend']}")
```

### Get Multi-Entity Investigation Context
```python
# Investigation: Suspicious user accessing multiple hosts
entities = [
    {'type': 'user', 'value': 'jdoe'},
    {'type': 'host', 'value': 'DC01'},
    {'type': 'host', 'value': 'FILE01'}
]

investigation_context = await context_mgr.get_multi_entity_context(
    entities,
    hours_back=48
)

# Check shared events (user on both hosts)
shared_events = investigation_context['intersections']['shared_events']
print(f"User accessed both hosts in {len(shared_events)} events")
```

## Polling Configuration

### Database Schema
```sql
CREATE TABLE log_polling_config (
    source_id VARCHAR(255) PRIMARY KEY,
    source_type VARCHAR(50) NOT NULL,
    poll_interval_seconds INTEGER NOT NULL,
    fetch_size INTEGER DEFAULT 1000,
    time_window_minutes INTEGER DEFAULT 5,
    enabled BOOLEAN DEFAULT TRUE,
    last_checkpoint TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE log_polling_metrics (
    metric_id SERIAL PRIMARY KEY,
    source_id VARCHAR(255) NOT NULL,
    poll_timestamp TIMESTAMP NOT NULL,
    logs_fetched INTEGER,
    time_range_start TIMESTAMP,
    time_range_end TIMESTAMP,
    duration_seconds FLOAT,
    errors JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_poll_metrics_source ON log_polling_metrics(source_id, poll_timestamp DESC);
```

## Backfill System

### Trigger Backfill
```python
# Request backfill via Redis
redis.publish('ingestion.backfill.request', json.dumps({
    'source_id': 'crowdstrike_production',
    'start_time': '2025-10-01T00:00:00Z',
    'end_time': '2025-10-02T00:00:00Z'
}))
```

**Backfill Process**:
1. Split time range into 1-hour chunks
2. Poll each chunk in **reverse chronological order** (most recent first)
3. Send logs to ingestion pipeline
4. Update checkpoint after each chunk
5. Continue until start_time reached

## Health Monitoring

### Stale Source Detection
- Monitors each source for 2x expected polling interval
- If no poll in 2x interval → ALERT
- Publishes to `ingestion.alert.stale_source`

### Dynamic Adjustment
- Calculates average logs/poll over last hour
- Adjusts interval every 10 minutes:
  - High volume: Poll more frequently
  - Low volume: Poll less frequently
  - Prevents resource waste and ensures timely updates

## Investigation Workflow Example

### Scenario: Investigate suspicious IP activity

```python
# 1. Get historical context for IP (last 24 hours)
context = await context_mgr.get_entity_history(
    entity_type='ip',
    entity_value='***********',
    hours_back=24
)

# 2. Analyze recent events (already sorted most recent first)
print(f"Most recent event: {context.recent_events[0]['event_timestamp']}")
print(f"Event type: {context.recent_events[0]['event_type']}")

# 3. Check relationships
for rel in context.relationships[:10]:  # Top 10 most recent
    print(f"Connected to: {rel['to_entity_type']}:{rel['to_entity_value']}")
    print(f"Last interaction: {rel['last_seen']}")

# 4. Check risk evolution
risk_trend = context.summary['risk_summary']['risk_trend']
print(f"Risk trend: {risk_trend}")  # increasing/decreasing/stable

if risk_trend == 'increasing':
    print(f"Peak risk: {context.summary['risk_summary']['peak_risk']}")
    print(f"High-risk events: {context.summary['risk_summary']['high_risk_events']}")

# 5. Check temporal patterns
unusual = context.summary['temporal_patterns']['unusual_time_activity']
if unusual:
    print(f"Off-hours activity detected: {len(unusual)} events")
    for event in unusual:
        print(f"  - {event['timestamp']}: {event['event_type']}")

# 6. Get related entities for expanded investigation
related_users = [rel for rel in context.relationships if rel['to_entity_type'] == 'user']
if related_users:
    # Investigate users connected to this IP
    for user_rel in related_users[:5]:
        user_context = await context_mgr.get_entity_history(
            entity_type='user',
            entity_value=user_rel['to_entity_value'],
            hours_back=24
        )
        print(f"User {user_rel['to_entity_value']} activity: {len(user_context.recent_events)} events")
```

## Performance Optimization

### Caching Strategy
```python
cache_ttl = {
    'recent_events': 300,      # 5 minutes (fast-changing)
    'relationships': 600,      # 10 minutes (moderate)
    'risk_evolution': 1800     # 30 minutes (slow-changing)
}
```

### Query Optimization
- All queries use DESC ordering on timestamp indexes
- Limits enforced at query level (not application)
- Checkpoint system prevents duplicate fetches
- Parallel polling of multiple sources

### Database Indexes
```sql
-- Critical indexes for recent-first queries
CREATE INDEX idx_events_timestamp_desc ON events(event_timestamp DESC);
CREATE INDEX idx_relationships_last_seen_desc ON relationships(last_seen DESC);
CREATE INDEX idx_sessions_start_time_desc ON sessions(start_time DESC);
CREATE INDEX idx_entity_risk_history_timestamp_desc ON entity_risk_history(timestamp DESC);

-- Entity lookup indexes
CREATE INDEX idx_events_entities_gin ON events USING gin(entities_involved);
CREATE INDEX idx_sessions_entities_gin ON sessions USING gin(entities_involved);
```

## Integration with Existing Systems

### Update Scheduler Integration
```python
# update_scheduler.py already triggers CTI updates
# Add log update polling to schedule

async def _run_scheduled_updates(self):
    schedules = {
        'log_polling': {
            'interval': timedelta(seconds=10),  # Check poller health
            'enabled': True
        },
        # ... existing schedules
    }
```

### Evidence Manager Integration
```python
# evidence_manager.py uses historical context for investigation evidence
from historical_context_manager import get_investigation_context

async def gather_evidence(investigation_id, entities, time_range):
    # Get historical context
    context = await get_investigation_context(db, entities, time_range['hours'])

    # Generate SIEM queries
    queries = SIEMQueryGenerator.generate_elastic_query(
        entities={'ips': [e['value'] for e in entities if e['type'] == 'ip']},
        time_range={'start': context.summary['entity_summary']['first_seen']}
    )

    return {
        'context': context,
        'siem_queries': queries
    }
```

## Benefits Achieved

### ✅ Regular Log Updates
- Configurable polling per source type
- Never misses logs (checkpoint system)
- Dynamic adjustment based on activity
- Backfill for historical gaps

### ✅ Most-Recent-First Priority
- All queries ordered DESC by timestamp
- Investigation always starts with latest events
- Risk evolution shows current state first
- Timeline is reverse chronological

### ✅ Complete Historical Context
- Entity-centric view of all activity
- Relationships with last interaction time
- Sessions grouped by activity
- Risk score evolution tracking

### ✅ Investigation Efficiency
- Single API call for complete context
- Multi-entity intersection analysis
- Temporal pattern detection
- Unusual activity flagging

## Next Steps

1. ✅ Historical Context Manager - COMPLETE
2. ✅ Log Update Poller - COMPLETE
3. ⏳ Integration with existing Ingestion Engine
4. ⏳ Frontend dashboard for polling metrics
5. ⏳ Alert system for stale sources
6. ⏳ Advanced temporal analysis (anomaly detection)

## Files Summary

**Created**:
- `historical_context_manager.py` (700+ lines) - Complete historical context retrieval
- `log_update_poller.py` (600+ lines) - Regular log polling with intelligence

**Integration Points**:
- Ingestion Engine - Receives polled logs
- Backend Engine - Stores historical data
- Update Scheduler - Monitors polling health
- Evidence Manager - Uses historical context

The log update and historical context system is now **complete and ready for integration**!
