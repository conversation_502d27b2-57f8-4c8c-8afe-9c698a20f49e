"""
Test script to verify investigation guide extraction
"""
import requests
import json

# Fetch one alert with investigation guide
response = requests.get('http://localhost:8005/api/alerts?status=all&limit=1')
data = response.json()

if data['alerts']:
    alert = data['alerts'][0]

    print("="*80)
    print("ALERT DETAILS")
    print("="*80)
    print(f"Title: {alert['title']}")
    print(f"Severity: {alert['severity']}")
    print(f"Timestamp: {alert['timestamp']}")
    print(f"MITRE Techniques: {', '.join(alert.get('mitre_techniques', []))}")
    print(f"Entities: {alert.get('entities', {})}")

    print("\n" + "="*80)
    print("RULE DESCRIPTION")
    print("="*80)
    print(alert.get('rule_description', 'No description available')[:300] + "...")

    print("\n" + "="*80)
    print("INVESTIGATION GUIDE")
    print("="*80)

    guide = alert.get('investigation_guide', '')

    if guide:
        print(f"Guide Length: {len(guide)} characters\n")

        # Parse sections
        sections = guide.split('**')
        section_titles = [s.split('\n')[0].strip() for s in sections if s.strip()]

        print("Available Sections:")
        for i, title in enumerate(section_titles, 1):
            print(f"  {i}. {title}")

        print("\n" + "-"*80)
        print("FULL INVESTIGATION GUIDE:")
        print("-"*80)
        print(guide)
    else:
        print("No investigation guide available for this alert")
else:
    print("No alerts found")
