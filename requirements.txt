# SIEMLess v2.0 Requirements
# Core Dependencies

# Async and networking
redis[hiredis]==5.0.1  # Has async support built-in (redis.asyncio)
asyncpg==0.29.0  # Async PostgreSQL driver
aiohttp==3.9.1
websockets==12.0

# Database
psycopg2-binary==2.9.9
sqlalchemy==2.0.23
alembic==1.13.0

# Message Queue and Caching
celery==5.3.4

# Data Processing
pandas==2.1.4
numpy==1.26.2

# API Framework
fastapi==0.108.0
uvicorn[standard]==0.25.0
pydantic==2.5.3

# Testing
pytest==8.3.5
pytest-asyncio==0.23.2
pytest-cov==7.0.0
pytest-mock==3.12.0

# Monitoring and Logging
prometheus-client==0.19.0
python-json-logger==2.0.7

# Utilities
python-dotenv==1.0.0
pyyaml==6.0.1
click==8.1.7

# Optional: System metrics
psutil==5.9.7

# Graph processing (for Graph Builder)
networkx==3.2.1
matplotlib==3.8.2

# Machine learning (for anomaly detection)
scikit-learn==1.3.2

# Authentication and security
PyJWT==2.8.0
cryptography==41.0.7

# Additional async libraries
aiofiles==23.2.1
aiohttp-cors==0.7.0

# JSON processing
jsonlines==4.0.0

# AI Provider SDKs (install as needed)
google-generativeai==0.3.2
anthropic==0.21.3
openai==1.6.1

# CTI Provider SDKs
OTXv2==1.5.12  # AlienVault OTX API client
pymisp==2.4.185  # MISP threat intelligence platform (optional)

# Additional required libraries
semantic-version==2.10.0  # For version management in pattern evolution