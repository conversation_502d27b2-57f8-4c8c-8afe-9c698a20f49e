"""
SIEMLess v2.0 - Task Coordinator
Coordinates background tasks and their lifecycle for the ingestion engine
"""

import asyncio
import os
from datetime import datetime
from typing import Dict, Any, List, Callable, Optional
import logging


class TaskCoordinator:
    """Coordinates background tasks for the ingestion engine"""

    def __init__(self, config_manager, logger: logging.Logger):
        self.config_manager = config_manager
        self.logger = logger
        self.tasks = {}
        self.task_configs = {}
        self.task_stats = {}

    def register_task(self, task_name: str, task_func: Callable, interval: int,
                     enabled: bool = True, dependencies: List[str] = None):
        """Register a background task"""
        self.task_configs[task_name] = {
            'function': task_func,
            'interval': interval,
            'enabled': enabled,
            'dependencies': dependencies or [],
            'registered_at': datetime.utcnow().isoformat()
        }

        self.task_stats[task_name] = {
            'runs': 0,
            'errors': 0,
            'last_run': None,
            'last_error': None,
            'total_runtime': 0.0,
            'avg_runtime': 0.0
        }

        self.logger.info(f"Registered task: {task_name} (interval: {interval}s)")

    def start_all_tasks(self) -> List[asyncio.Task]:
        """Start all registered background tasks"""
        started_tasks = []

        for task_name, config in self.task_configs.items():
            if config['enabled']:
                task = asyncio.create_task(self._task_wrapper(task_name))
                self.tasks[task_name] = task
                started_tasks.append(task)
                self.logger.info(f"Started background task: {task_name}")

        self.logger.info(f"Started {len(started_tasks)} background tasks")
        return started_tasks

    async def _task_wrapper(self, task_name: str):
        """Wrapper for background tasks with error handling and statistics"""
        config = self.task_configs[task_name]
        task_func = config['function']
        interval = config['interval']

        while True:
            try:
                start_time = asyncio.get_event_loop().time()

                # Check dependencies
                if not await self._check_dependencies(task_name):
                    await asyncio.sleep(10)  # Wait before retrying
                    continue

                # Run the task
                await task_func()

                # Update statistics
                end_time = asyncio.get_event_loop().time()
                runtime = end_time - start_time

                stats = self.task_stats[task_name]
                stats['runs'] += 1
                stats['last_run'] = datetime.utcnow().isoformat()
                stats['total_runtime'] += runtime
                stats['avg_runtime'] = stats['total_runtime'] / stats['runs']

                self.logger.debug(f"Task {task_name} completed in {runtime:.2f}s")

            except Exception as e:
                # Update error statistics
                stats = self.task_stats[task_name]
                stats['errors'] += 1
                stats['last_error'] = {
                    'message': str(e),
                    'timestamp': datetime.utcnow().isoformat()
                }

                self.logger.error(f"Task {task_name} failed: {e}")

                # Wait shorter interval on error to retry sooner
                error_interval = min(interval, 60)
                await asyncio.sleep(error_interval)
                continue

            # Wait for next execution
            await asyncio.sleep(interval)

    async def _check_dependencies(self, task_name: str) -> bool:
        """Check if task dependencies are met"""
        config = self.task_configs[task_name]
        dependencies = config.get('dependencies', [])

        for dep_task in dependencies:
            if dep_task not in self.task_stats:
                self.logger.warning(f"Task {task_name} depends on unknown task: {dep_task}")
                return False

            dep_stats = self.task_stats[dep_task]
            if dep_stats['runs'] == 0:
                self.logger.debug(f"Task {task_name} waiting for dependency: {dep_task}")
                return False

        return True

    def stop_task(self, task_name: str):
        """Stop a specific background task"""
        if task_name in self.tasks:
            self.tasks[task_name].cancel()
            del self.tasks[task_name]
            self.logger.info(f"Stopped task: {task_name}")

    def stop_all_tasks(self):
        """Stop all background tasks"""
        for task_name in list(self.tasks.keys()):
            self.stop_task(task_name)

    def get_task_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all tasks"""
        status = {}

        for task_name, config in self.task_configs.items():
            stats = self.task_stats[task_name]
            task_obj = self.tasks.get(task_name)

            status[task_name] = {
                'enabled': config['enabled'],
                'interval': config['interval'],
                'dependencies': config['dependencies'],
                'running': task_obj is not None and not task_obj.done(),
                'stats': stats.copy()
            }

        return status

    def enable_task(self, task_name: str):
        """Enable a task"""
        if task_name in self.task_configs:
            self.task_configs[task_name]['enabled'] = True
            self.logger.info(f"Enabled task: {task_name}")

    def disable_task(self, task_name: str):
        """Disable and stop a task"""
        if task_name in self.task_configs:
            self.task_configs[task_name]['enabled'] = False
            self.stop_task(task_name)
            self.logger.info(f"Disabled task: {task_name}")

    def update_task_interval(self, task_name: str, new_interval: int):
        """Update the interval for a task (requires restart)"""
        if task_name in self.task_configs:
            old_interval = self.task_configs[task_name]['interval']
            self.task_configs[task_name]['interval'] = new_interval
            self.logger.info(f"Updated task {task_name} interval: {old_interval}s → {new_interval}s")


class IngestionTaskFactory:
    """Factory for creating ingestion engine specific tasks"""

    def __init__(self, engine_components: Dict[str, Any], logger: logging.Logger):
        self.components = engine_components
        self.logger = logger

    def create_source_monitor_task(self, data_source_manager) -> Callable:
        """Create source monitoring task"""
        async def source_monitor_loop():
            """Monitor active sources for health and performance"""
            try:
                active_sources = data_source_manager.get_active_sources()

                for source_id, source_info in active_sources.items():
                    # Update source health check
                    data_source_manager.update_source_stats(source_id)

                    # Log health status
                    is_healthy = data_source_manager.is_source_healthy(source_id)
                    if not is_healthy:
                        self.logger.warning(f"Source {source_id} appears unhealthy")

                self.logger.debug(f"Monitored {len(active_sources)} sources")

            except Exception as e:
                self.logger.error(f"Source monitor error: {e}")

        return source_monitor_loop

    def create_ingestion_task(self, data_source_manager, log_router, stats_monitor) -> Callable:
        """Create main ingestion processing task"""
        async def ingestion_loop():
            """Main ingestion processing loop"""
            try:
                # Fetch data from all sources
                logs_batch = await data_source_manager.fetch_data_from_sources()

                if logs_batch:
                    self.logger.info(f"Fetched {len(logs_batch)} logs, routing to engines")

                    # Process and route logs
                    routing_stats = await log_router.process_log_batch(
                        logs_batch,
                        self.components.get('parser_hot_reload')
                    )

                    # Update statistics
                    stats_monitor.increment_stat('total_logs', len(logs_batch))
                    stats_monitor.update_processing_stats(routing_stats)

                else:
                    self.logger.debug("No logs fetched in this cycle")

            except Exception as e:
                self.logger.error(f"Ingestion loop error: {e}")

        return ingestion_loop

    def create_stats_reporting_task(self, stats_monitor, publisher) -> Callable:
        """Create statistics reporting task"""
        async def stats_reporting_loop():
            """Periodically report ingestion statistics"""
            try:
                # Publish statistics to monitoring
                monitoring_metrics = stats_monitor.export_metrics_for_monitoring()

                publisher('monitoring.ingestion_stats', {
                    'engine': 'ingestion',
                    'stats': stats_monitor.get_all_stats(),
                    'metrics': monitoring_metrics,
                    'timestamp': datetime.utcnow().isoformat()
                })

                self.logger.debug("Published statistics to monitoring")

            except Exception as e:
                self.logger.error(f"Stats reporting error: {e}")

        return stats_reporting_loop

    def create_github_sync_task(self, github_sync, config_manager, stats_monitor) -> Callable:
        """Create GitHub synchronization task"""
        async def github_sync_loop():
            """Periodically sync patterns from GitHub repositories"""
            try:
                github_config = config_manager.get_source_config('github')

                if github_config.get('enabled', False):
                    # Sync all configured repositories
                    results = await github_sync.sync_all_repositories()

                    # Update stats
                    for result in results:
                        if result['status'] == 'completed':
                            stats_monitor.increment_stat('github_repos')
                            self.logger.info(f"GitHub sync completed: {result['repo']} - {result['patterns_deployed']} patterns")

                else:
                    self.logger.debug("GitHub sync disabled")

            except Exception as e:
                self.logger.error(f"GitHub sync error: {e}")

        return github_sync_loop

    def create_api_doc_update_task(self, api_doc_generator) -> Callable:
        """Create API documentation update task"""
        async def api_doc_update_loop():
            """Periodically update API documentation"""
            try:
                # Generate documentation
                os.makedirs("api_docs", exist_ok=True)
                await api_doc_generator.generate_documentation()
                await api_doc_generator.export_documentation("json", "api_docs/openapi.json")

                self.logger.debug("API documentation updated")

            except Exception as e:
                self.logger.error(f"API doc update error: {e}")

        return api_doc_update_loop

    def create_component_initialization_task(self, github_sync, parser_hot_reload,
                                           api_doc_generator, config_manager, stats_monitor) -> Callable:
        """Create one-time component initialization task"""
        # Track if already initialized
        initialized = {'done': False}

        async def initialize_components():
            """Initialize components that require async setup"""
            # If already initialized, skip
            if initialized['done']:
                return

            try:
                self.logger.info("Initializing components...")

                # Initialize GitHub sync
                github_token = config_manager.get_source_config('github').get('github_token')
                await github_sync.initialize(github_token)

                # Initialize parser hot reload
                await parser_hot_reload.initialize()
                parser_status = await parser_hot_reload.get_parser_status()
                stats_monitor.update_stat('patterns_loaded', parser_status['total_parsers'])

                # Generate initial API documentation
                os.makedirs("api_docs", exist_ok=True)
                await api_doc_generator.generate_documentation()
                await api_doc_generator.export_documentation("json", "api_docs/openapi.json")

                self.logger.info("All components initialized successfully")

                # Mark as initialized
                initialized['done'] = True
                return

            except Exception as e:
                self.logger.error(f"Failed to initialize components: {e}")
                # Mark as initialized to prevent infinite retries
                initialized['done'] = True
                # Don't raise to prevent retries
                return

        return initialize_components

    def create_database_log_processor_task(self, log_router, db_connection) -> Callable:
        """Create task to process logs already in the database"""
        from log_router import DatabaseLogRouter

        db_router = DatabaseLogRouter(db_connection, log_router, self.logger)

        async def process_database_logs():
            """Process unprocessed logs from database"""
            try:
                stats = await db_router.process_database_logs(limit=100)
                if stats['processed'] > 0:
                    self.logger.info(f"Processed {stats['processed']} database logs")

            except Exception as e:
                self.logger.error(f"Database log processing error: {e}")

        return process_database_logs