# Frontend & Delivery Engine - Comprehensive Review & Implementation Plan
## User-Facing Features Implementation Strategy

---

## Executive Summary

**Current Status**: Backend 100% complete (11/11 features), Frontend 40% complete (UI scaffolding exists, API integration needed)

**Goal**: Connect React frontend to Delivery Engine APIs to create full user-facing platform

**Priority**: Enable analysts to interact with all 11 backend features through intuitive UI

---

## Part 1: Current State Analysis

### Backend (Delivery Engine) - Port 8005 ✅

**Status**: Fully operational with 9 Investigation API endpoints

**Existing APIs**:
1. `POST /api/v1/investigations` - Create investigation
2. `GET /api/v1/investigations` - List investigations
3. `GET /api/v1/investigations/{id}` - Get details
4. `PATCH /api/v1/investigations/{id}` - Update
5. `POST /api/v1/investigations/{id}/assign` - Assign
6. `POST /api/v1/investigations/{id}/close` - Close
7. `POST /api/v1/investigations/{id}/notes` - Add note
8. `POST /api/v1/investigations/{id}/evidence` - Add evidence
9. `GET /api/v1/investigations/stats` - Statistics

**Features Available**:
- Investigation management
- Case workflow orchestration
- Alert delivery system
- Dashboard data generation
- Session management
- Workflow templates

### Frontend - Port 3000 📊

**Technology Stack**:
- React 18.2 with TypeScript
- FlexLayout (advanced windowing system)
- AG-Grid Enterprise (tables)
- Recharts + D3 (visualizations)
- TanStack Query (API state management)
- Zustand (global state)
- React Hook Form + Zod (forms/validation)
- Radix UI (accessible components)
- Tailwind CSS (styling)
- Storybook (component development)
- Vitest (testing)

**Existing Pages** (18 total):
1. ✅ Dashboard.tsx
2. ✅ AlertQueue.tsx
3. ✅ ActiveCases.tsx
4. ✅ MITREOverview.tsx
5. ✅ investigation/NewInvestigation.tsx
6. ✅ investigation/EntityExplorer.tsx
7. ✅ investigation/RelationshipMapper.tsx
8. ✅ investigation/AIGuide.tsx
9. ✅ investigation/TimelineAnalysis.tsx
10. ✅ engineering/CrystallizationQueue.tsx
11. ✅ engineering/PatternLibrary.tsx
12. ✅ engineering/GitHubSync.tsx
13. ✅ engineering/RuleTesting.tsx
14. ✅ analytics/PerformanceMetrics.tsx
15. ✅ analytics/CostAnalysis.tsx
16. ✅ admin/UserManagement.tsx
17. ✅ admin/SystemSettings.tsx
18. ✅ investigation/placeholder-pages.tsx

**Existing Widgets** (19 total):
1. AlertQueue - Alert list with filtering
2. CaseTimeline - Timeline visualization
3. EntityExplorer - Entity browsing
4. EntityGraph - Entity relationships
5. RelationshipGraph - Connection visualization
6. CTIFeeds - Threat intelligence
7. AIInvestigationGuide - AI assistance
8. MITREHeatmap - ATT&CK coverage
9. PatternLibrary - Pattern browser
10. CrystallizationQueue - Pattern queue
11. MetricsDashboard - Performance metrics
12. CostSavingsTracker - Cost analysis
13. RuleTestRunner - Rule testing
14. UserManagement - User admin
15. SystemSettings - System config
16. ActionToolbar - Quick actions
17. WidgetFactory - Dynamic widgets

**Existing Stores**:
- investigationStore.ts - Investigation state
- navigationStore.ts - Navigation state
- notificationStore.ts - Notifications

**API Client**: Basic Axios setup in `src/api/client.ts`

---

## Part 2: Gap Analysis

### Missing API Integrations 🔴

**Critical (Must Have)**:
1. ❌ Investigation API integration (CRUD operations)
2. ❌ Evidence collection API integration
3. ❌ Historical context API integration
4. ❌ Alert polling/streaming integration
5. ❌ Real-time updates (WebSocket/SSE)

**Important (Should Have)**:
6. ❌ MITRE mapping API integration
7. ❌ CTI feeds API integration
8. ❌ Pattern library API integration
9. ❌ Log retention policy UI
10. ❌ Preview-before-download UI

**Nice to Have**:
11. ❌ Workflow orchestration UI
12. ❌ Cost analytics API integration
13. ❌ Performance metrics API integration

### Missing Backend Endpoints 🟡

**For Frontend to Function Fully**:
1. ❌ `GET /api/v1/alerts` - Alert feed
2. ❌ `GET /api/v1/entities` - Entity search
3. ❌ `GET /api/v1/patterns` - Pattern library
4. ❌ `GET /api/v1/mitre/coverage` - MITRE coverage
5. ❌ `GET /api/v1/cti/feeds` - CTI feeds
6. ❌ `POST /api/v1/evidence/collect` - Evidence collection
7. ❌ `GET /api/v1/retention/policies` - Retention policies
8. ❌ `GET /api/v1/updates/preview` - Update preview
9. ❌ `WebSocket /ws/notifications` - Real-time notifications
10. ❌ `WebSocket /ws/investigations/{id}` - Investigation updates

### Missing UI Components 🟠

**Essential Components**:
1. ❌ Investigation detail panel (full CRUD)
2. ❌ Evidence viewer with SIEM link-back
3. ❌ Timeline with historical context
4. ❌ Entity detail drawer
5. ❌ Alert triage interface
6. ❌ Retention policy editor
7. ❌ Update preview diff viewer

**Enhancement Components**:
8. ❌ Multi-entity search
9. ❌ Bulk actions toolbar
10. ❌ Advanced filtering
11. ❌ Export functionality
12. ❌ Report generation

---

## Part 3: Implementation Plan

### Phase 1: Core Investigation Workflow (Week 1)
**Goal**: Enable end-to-end investigation from alert to closure

#### 1.1: Backend API Extensions
**Location**: `engines/delivery/`

```python
# New endpoints to add to delivery_engine.py

@app.route('/api/v1/alerts')
async def get_alerts(request):
    """GET /api/v1/alerts?status=open&limit=50

    Returns: {
        "alerts": [
            {
                "alert_id": "uuid",
                "title": "Suspicious PowerShell",
                "severity": "high",
                "timestamp": "2025-10-02T...",
                "entities": {...},
                "mitre_techniques": ["T1059.001"],
                "status": "open"
            }
        ],
        "count": 10
    }
    """

@app.route('/api/v1/entities/search')
async def search_entities(request):
    """POST /api/v1/entities/search

    Body: {
        "entity_type": "ip" | "user" | "host",
        "value": "*************",
        "time_range_hours": 24
    }

    Returns: {
        "entity": {...},
        "recent_activity": [...],
        "relationships": [...],
        "risk_score": 75
    }
    """

@app.route('/api/v1/investigations/{id}/timeline')
async def get_investigation_timeline(request):
    """GET /api/v1/investigations/{id}/timeline?hours_back=24

    Integrates with HistoricalContextManager

    Returns: {
        "timeline": [
            {
                "timestamp": "2025-10-02T10:30:00",
                "event_type": "authentication_failure",
                "entity": {"type": "user", "value": "admin"},
                "description": "...",
                "risk_score": 75
            }
        ]
    }
    """

@app.route('/api/v1/investigations/{id}/evidence/collect')
async def collect_evidence(request):
    """POST /api/v1/investigations/{id}/evidence/collect

    Integrates with InvestigationEvidenceLogger

    Body: {
        "siem_type": "elastic",
        "filters": {...},
        "time_range_hours": 24
    }

    Returns: {
        "evidence_collected": 45,
        "siem_urls": ["https://..."],
        "avg_relevance": 0.78
    }
    """

# WebSocket endpoints
@app.websocket('/ws/notifications')
async def ws_notifications(request):
    """Real-time notifications stream"""

@app.websocket('/ws/investigations/{id}')
async def ws_investigation_updates(request):
    """Real-time investigation updates"""
```

**Files to Create**:
- `delivery_engine_http_extensions.py` - Additional HTTP endpoints
- `delivery_engine_websocket.py` - WebSocket handlers
- `alert_feed_manager.py` - Alert feed aggregation
- `entity_search_service.py` - Entity search with context

**Estimated**: 800 lines of code, 1-2 days

#### 1.2: Frontend API Client
**Location**: `frontend/src/api/`

```typescript
// investigations.ts
export const investigationsApi = {
  list: async (params: ListParams) =>
    client.get('/api/v1/investigations', { params }),

  get: async (id: string) =>
    client.get(`/api/v1/investigations/${id}`),

  create: async (data: CreateInvestigationDto) =>
    client.post('/api/v1/investigations', data),

  update: async (id: string, data: UpdateInvestigationDto) =>
    client.patch(`/api/v1/investigations/${id}`, data),

  close: async (id: string, resolution: string) =>
    client.post(`/api/v1/investigations/${id}/close`, { resolution }),

  getTimeline: async (id: string, hoursBack: number = 24) =>
    client.get(`/api/v1/investigations/${id}/timeline`, {
      params: { hours_back: hoursBack }
    }),

  collectEvidence: async (id: string, params: CollectEvidenceParams) =>
    client.post(`/api/v1/investigations/${id}/evidence/collect`, params)
};

// alerts.ts
export const alertsApi = {
  list: async (params: AlertListParams) =>
    client.get('/api/v1/alerts', { params }),

  subscribe: () => {
    const ws = new WebSocket('ws://localhost:8005/ws/notifications');
    return ws;
  }
};

// entities.ts
export const entitiesApi = {
  search: async (params: EntitySearchParams) =>
    client.post('/api/v1/entities/search', params)
};
```

**Files to Create**:
- `api/investigations.ts` - Investigation API
- `api/alerts.ts` - Alerts API
- `api/entities.ts` - Entity API
- `api/evidence.ts` - Evidence API
- `hooks/useInvestigations.ts` - React Query hooks
- `hooks/useAlerts.ts` - Alert hooks with WebSocket
- `hooks/useEntities.ts` - Entity hooks

**Estimated**: 600 lines of code, 1 day

#### 1.3: Investigation UI Components
**Location**: `frontend/src/components/investigation/`

```typescript
// InvestigationDetail.tsx
export function InvestigationDetail({ investigationId }: Props) {
  const { data: investigation } = useInvestigation(investigationId);
  const { data: timeline } = useInvestigationTimeline(investigationId);

  return (
    <div className="investigation-detail">
      <InvestigationHeader investigation={investigation} />
      <TabsContainer>
        <Tab name="Overview"><InvestigationOverview /></Tab>
        <Tab name="Timeline"><TimelineVisualization timeline={timeline} /></Tab>
        <Tab name="Entities"><EntityExplorer entities={investigation.entities} /></Tab>
        <Tab name="Evidence"><EvidenceViewer /></Tab>
        <Tab name="Notes"><NotesPanel /></Tab>
      </TabsContainer>
    </div>
  );
}

// TimelineVisualization.tsx
export function TimelineVisualization({ timeline }: Props) {
  return (
    <div className="timeline">
      {timeline.map(event => (
        <TimelineEvent
          key={event.timestamp}
          event={event}
          onClick={() => showEventDetails(event)}
        />
      ))}
    </div>
  );
}

// EvidenceViewer.tsx
export function EvidenceViewer({ investigationId }: Props) {
  const [collecting, setCollecting] = useState(false);
  const { data: evidence } = useInvestigationEvidence(investigationId);

  const collectEvidence = async () => {
    setCollecting(true);
    await investigationsApi.collectEvidence(investigationId, {
      siem_type: 'elastic',
      time_range_hours: 24
    });
    setCollecting(false);
  };

  return (
    <div>
      <Button onClick={collectEvidence} loading={collecting}>
        Collect Evidence
      </Button>
      <EvidenceTable evidence={evidence} />
    </div>
  );
}
```

**Components to Create**:
- `InvestigationDetail.tsx` - Main investigation view
- `InvestigationHeader.tsx` - Header with actions
- `InvestigationOverview.tsx` - Summary panel
- `TimelineVisualization.tsx` - Timeline with zoom/filter
- `EvidenceViewer.tsx` - Evidence browser with SIEM links
- `EntityExplorer.tsx` - Enhanced entity browser
- `NotesPanel.tsx` - Collaborative notes
- `InvestigationActions.tsx` - Assign/close/export

**Estimated**: 1200 lines of code, 2-3 days

---

### Phase 2: Alert Triage Workflow (Week 2)
**Goal**: Enable analysts to triage alerts and create investigations

#### 2.1: Alert Feed Backend
```python
# alert_feed_manager.py
class AlertFeedManager:
    async def get_alerts(self, filters: Dict) -> List[Alert]:
        """Aggregate alerts from all SIEMs"""

    async def subscribe_to_alerts(self, callback: Callable):
        """Real-time alert subscription"""

    async def triage_alert(self, alert_id: str, action: str):
        """Triage: create_investigation | dismiss | escalate"""
```

#### 2.2: Alert Triage UI
```typescript
// AlertTriagePanel.tsx
export function AlertTriagePanel() {
  const { data: alerts } = useAlerts({ status: 'open' });
  const createInvestigation = useCreateInvestigation();

  const handleTriage = async (alert: Alert, action: string) => {
    if (action === 'investigate') {
      await createInvestigation.mutate({
        title: alert.title,
        severity: alert.severity,
        entities: alert.entities,
        mitre_techniques: alert.mitre_techniques
      });
    }
  };

  return <AlertQueue alerts={alerts} onTriage={handleTriage} />;
}
```

**Estimated**: 500 lines, 1-2 days

---

### Phase 3: MITRE & CTI Integration (Week 3)
**Goal**: Surface MITRE coverage and CTI feeds in UI

#### 3.1: MITRE API Endpoints
```python
@app.route('/api/v1/mitre/coverage')
async def get_mitre_coverage(request):
    """Returns MITRE ATT&CK coverage heatmap data"""

@app.route('/api/v1/mitre/techniques/{technique_id}')
async def get_technique_details(request):
    """Returns technique details with rules and gaps"""
```

#### 3.2: MITRE Heatmap UI
```typescript
// MITREHeatmap.tsx - Enhanced
export function MITREHeatmap() {
  const { data: coverage } = useMITRECoverage();

  return (
    <HeatmapVisualization
      data={coverage}
      onTechniqueClick={showTechniqueDetails}
    />
  );
}
```

**Estimated**: 400 lines, 1 day

---

### Phase 4: Advanced Features (Week 4)

#### 4.1: Log Retention Policy UI
```typescript
// RetentionPolicyEditor.tsx
export function RetentionPolicyEditor() {
  const { data: policies } = useRetentionPolicies();

  return (
    <PolicyTable
      policies={policies}
      onEdit={editPolicy}
      onTest={testPolicy}
    />
  );
}
```

#### 4.2: Preview-Before-Download UI
```typescript
// UpdatePreviewDialog.tsx
export function UpdatePreviewDialog({ updateId }: Props) {
  const { data: preview } = useUpdatePreview(updateId);

  return (
    <Dialog>
      <DiffViewer diff={preview.diff} />
      <ChangesSummary summary={preview.summary} />
      <DialogActions>
        <Button onClick={approveUpdate}>Approve</Button>
        <Button onClick={rejectUpdate}>Reject</Button>
      </DialogActions>
    </Dialog>
  );
}
```

**Estimated**: 600 lines, 2 days

---

## Part 4: Technical Architecture

### Backend Architecture

```
Delivery Engine (Port 8005)
├── HTTP Server (aiohttp)
│   ├── Investigation APIs ✅
│   ├── Alert Feed APIs (NEW)
│   ├── Entity Search APIs (NEW)
│   ├── Evidence APIs (NEW)
│   ├── MITRE APIs (NEW)
│   └── Retention APIs (NEW)
├── WebSocket Server (NEW)
│   ├── /ws/notifications
│   └── /ws/investigations/{id}
├── Workflow Orchestrator ✅
├── Investigation Engine ✅
├── Case Management ✅
└── Dashboard Data Generator ✅
```

### Frontend Architecture

```
React Frontend (Port 3000)
├── Pages (18 existing)
│   ├── Dashboard
│   ├── Investigations
│   ├── Alerts
│   └── Admin
├── Widgets (19 existing)
├── Components (NEW)
│   ├── investigation/
│   ├── alerts/
│   ├── entities/
│   └── evidence/
├── API Layer (NEW)
│   ├── api/investigations.ts
│   ├── api/alerts.ts
│   ├── api/entities.ts
│   └── api/evidence.ts
├── Hooks (NEW)
│   ├── useInvestigations
│   ├── useAlerts
│   ├── useEntities
│   └── useWebSocket
└── Stores ✅
    ├── investigationStore
    ├── navigationStore
    └── notificationStore
```

---

## Part 5: Development Checklist

### Week 1: Core Investigation Workflow
- [ ] Add alert feed endpoints to delivery engine
- [ ] Add entity search endpoints
- [ ] Add timeline endpoints (integrate HistoricalContextManager)
- [ ] Add evidence collection endpoints (integrate InvestigationEvidenceLogger)
- [ ] Create WebSocket notification handler
- [ ] Create frontend API client (investigations, alerts, entities)
- [ ] Create React Query hooks
- [ ] Build InvestigationDetail component
- [ ] Build TimelineVisualization component
- [ ] Build EvidenceViewer component
- [ ] Test end-to-end investigation creation

### Week 2: Alert Triage
- [ ] Build AlertFeedManager backend service
- [ ] Add alert triage endpoints
- [ ] Create AlertTriagePanel component
- [ ] Add WebSocket alert subscription
- [ ] Build alert-to-investigation flow
- [ ] Test alert triage workflow

### Week 3: MITRE & CTI
- [ ] Add MITRE coverage API
- [ ] Add CTI feeds API
- [ ] Enhance MITREHeatmap component
- [ ] Build CTIFeedViewer component
- [ ] Test MITRE integration

### Week 4: Advanced Features
- [ ] Add retention policy endpoints
- [ ] Add update preview endpoints
- [ ] Build RetentionPolicyEditor
- [ ] Build UpdatePreviewDialog
- [ ] Build export/report functionality
- [ ] Performance optimization
- [ ] End-to-end testing
- [ ] Documentation

---

## Part 6: File Organization

### New Backend Files (Delivery Engine)
```
engines/delivery/
├── delivery_engine.py (existing - enhance)
├── delivery_engine_http_extensions.py (NEW)
├── delivery_engine_websocket.py (NEW)
├── alert_feed_manager.py (NEW)
├── entity_search_service.py (NEW)
├── mitre_api_handlers.py (NEW)
├── cti_api_handlers.py (NEW)
├── retention_policy_api.py (NEW)
└── update_preview_api.py (NEW)
```

### New Frontend Files
```
frontend/src/
├── api/
│   ├── investigations.ts (NEW)
│   ├── alerts.ts (NEW)
│   ├── entities.ts (NEW)
│   ├── evidence.ts (NEW)
│   ├── mitre.ts (NEW)
│   ├── cti.ts (NEW)
│   └── retention.ts (NEW)
├── hooks/
│   ├── useInvestigations.ts (NEW)
│   ├── useAlerts.ts (NEW)
│   ├── useEntities.ts (NEW)
│   ├── useWebSocket.ts (NEW)
│   └── useMITRE.ts (NEW)
├── components/
│   ├── investigation/
│   │   ├── InvestigationDetail.tsx (NEW)
│   │   ├── InvestigationHeader.tsx (NEW)
│   │   ├── TimelineVisualization.tsx (NEW)
│   │   ├── EvidenceViewer.tsx (NEW)
│   │   ├── EntityExplorer.tsx (NEW)
│   │   └── NotesPanel.tsx (NEW)
│   ├── alerts/
│   │   ├── AlertTriagePanel.tsx (NEW)
│   │   ├── AlertDetail.tsx (NEW)
│   │   └── AlertActions.tsx (NEW)
│   └── common/
│       ├── DiffViewer.tsx (NEW)
│       ├── Timeline.tsx (NEW)
│       └── EntityBadge.tsx (NEW)
└── types/
    ├── investigation.ts (NEW)
    ├── alert.ts (NEW)
    ├── entity.ts (NEW)
    └── evidence.ts (NEW)
```

---

## Part 7: API Contracts

### Investigation API
```typescript
// GET /api/v1/investigations
interface InvestigationsListResponse {
  investigations: Investigation[];
  count: number;
  total: number;
}

// GET /api/v1/investigations/{id}
interface Investigation {
  id: string;
  title: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  status: 'open' | 'investigating' | 'closed';
  created_at: string;
  updated_at: string;
  assigned_to?: string;
  entities: {
    ips: string[];
    users: string[];
    hosts: string[];
  };
  mitre_techniques: string[];
  risk_score: number;
  notes: Note[];
  evidence: Evidence[];
}

// GET /api/v1/investigations/{id}/timeline
interface TimelineResponse {
  timeline: TimelineEvent[];
  count: number;
  time_range: { start: string; end: string };
}

interface TimelineEvent {
  timestamp: string;
  event_type: string;
  entity: { type: string; value: string };
  description: string;
  risk_score: number;
  source: string;
}
```

---

## Part 8: Success Metrics

### User Experience Metrics
- [ ] Investigation creation: <10 clicks from alert
- [ ] Evidence collection: <30 seconds
- [ ] Timeline load: <2 seconds
- [ ] Real-time updates: <500ms latency
- [ ] Page transitions: <300ms

### Feature Completeness
- [ ] 100% of backend APIs accessible from UI
- [ ] All 11 features have UI representation
- [ ] Full investigation lifecycle supported
- [ ] Alert triage workflow operational
- [ ] Evidence collection functional
- [ ] Timeline visualization working

### Code Quality
- [ ] TypeScript strict mode
- [ ] 80%+ test coverage
- [ ] Storybook stories for all components
- [ ] Accessibility (WCAG 2.1 AA)
- [ ] Mobile-responsive layouts

---

## Part 9: Next Steps for New Session

1. **Start with Backend API Extensions** (Day 1)
   - Add 5 critical endpoints to delivery_engine.py
   - Test with curl/Postman

2. **Frontend API Client** (Day 2)
   - Create TypeScript API clients
   - Set up React Query hooks
   - Test API integration

3. **Core UI Components** (Days 3-4)
   - InvestigationDetail
   - TimelineVisualization
   - EvidenceViewer

4. **Integration Testing** (Day 5)
   - End-to-end workflows
   - Performance testing
   - Bug fixes

5. **Polish & Documentation** (Days 6-7)
   - UI/UX refinements
   - Documentation
   - Deployment prep

---

## Part 10: Recommended Starting Point

**Highest Priority**: Alert → Investigation → Evidence Collection workflow

**Why**: This is the core analyst workflow and demonstrates all platform capabilities.

**First Task**:
```
1. Add GET /api/v1/alerts endpoint to delivery_engine.py
2. Create frontend/src/api/alerts.ts
3. Create frontend/src/hooks/useAlerts.ts
4. Enhance frontend/src/pages/AlertQueue.tsx to use real API
5. Test: View alerts → Create investigation → Collect evidence
```

---

## Estimated Effort

**Backend Extensions**: 2,000 lines, 3-4 days
**Frontend Integration**: 3,500 lines, 6-7 days
**Testing & Polish**: 2-3 days

**Total**: 12-14 days for full user-facing platform

---

## Summary

**Current**: Backend feature-complete, frontend UI scaffolding exists
**Gap**: API integration layer between frontend and backend
**Solution**: Implement ~5,500 lines of integration code over 2 weeks
**Result**: Fully functional analyst platform with all 11 features accessible

**Ready to start building the user-facing platform!** 🚀
