import React, { useState, useEffect } from 'react'
import { Alert } from '../types/investigation'
import { AlertService } from '../services/alertService'
import {
  Search, Filter, Users, Clock, TrendingUp, AlertCircle,
  CheckCircle, XCircle, RefreshCw, Play, Eye, MoreVertical,
  FileText, Target, Shield, ChevronRight
} from 'lucide-react'
import '../styles/InvestigationWorkspace.css'

/**
 * Investigation Workspace Widget
 *
 * Purpose: Active investigations dashboard for SOC analysts
 *
 * Features:
 * - Active investigations list with status tracking
 * - Investigation assignment and claiming
 * - Progress tracking (enrichment, correlation, analysis)
 * - Time tracking and SLA monitoring
 * - Quick filters (My Investigations, Unassigned, High Priority)
 * - Investigation status workflow (New → Investigating → Complete)
 * - "Take Investigation" button for unassigned alerts
 * - Real-time updates
 */

interface Investigation extends Alert {
  assigned_to?: string;
  investigation_status: 'new' | 'enriching' | 'correlating' | 'analyzing' | 'complete';
  progress_percentage: number;
  time_since_creation: number; // minutes
  sla_status: 'ok' | 'warning' | 'breached';
}

type InvestigationFilter = 'all' | 'my' | 'unassigned' | 'critical' | 'breached_sla';

export const InvestigationWorkspace: React.FC = () => {
  const [investigations, setInvestigations] = useState<Investigation[]>([])
  const [filteredInvestigations, setFilteredInvestigations] = useState<Investigation[]>([])
  const [activeFilter, setActiveFilter] = useState<InvestigationFilter>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(true)
  const [currentAnalyst] = useState('<EMAIL>') // Would come from auth context
  const [selectedInvestigation, setSelectedInvestigation] = useState<Investigation | null>(null)

  useEffect(() => {
    fetchInvestigations()
    const interval = setInterval(fetchInvestigations, 30000) // Refresh every 30s
    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    applyFilters()
  }, [investigations, activeFilter, searchQuery])

  const fetchInvestigations = async () => {
    setLoading(true)
    try {
      const alerts = await AlertService.listAlerts({ status: 'open' })

      // Transform alerts into investigations with additional metadata
      const investigationsData: Investigation[] = alerts.map(alert => ({
        ...alert,
        assigned_to: undefined, // Would come from backend
        investigation_status: getInvestigationStatus(alert),
        progress_percentage: calculateProgress(alert),
        time_since_creation: Math.floor((Date.now() - new Date(alert.created_at).getTime()) / 60000),
        sla_status: calculateSLAStatus(alert)
      }))

      setInvestigations(investigationsData)
    } catch (error) {
      console.error('Failed to fetch investigations:', error)
    } finally {
      setLoading(false)
    }
  }

  const getInvestigationStatus = (alert: Alert): Investigation['investigation_status'] => {
    if (alert.enrichment_status?.status === 'completed' &&
        alert.correlation_status?.status === 'completed') {
      return 'complete'
    }
    if (alert.correlation_status?.status === 'in_progress') return 'correlating'
    if (alert.enrichment_status?.status === 'in_progress') return 'enriching'
    if (alert.enrichment_status?.status === 'completed') return 'analyzing'
    return 'new'
  }

  const calculateProgress = (alert: Alert): number => {
    let progress = 0
    if (alert.enrichment_status?.status === 'completed') progress += 40
    else if (alert.enrichment_status?.status === 'in_progress') progress += 20

    if (alert.correlation_status?.status === 'completed') progress += 40
    else if (alert.correlation_status?.status === 'in_progress') progress += 20

    if (alert.enrichment_status?.status === 'completed' &&
        alert.correlation_status?.status === 'completed') progress += 20

    return Math.min(progress, 100)
  }

  const calculateSLAStatus = (alert: Alert): Investigation['sla_status'] => {
    const minutesSinceCreation = Math.floor((Date.now() - new Date(alert.created_at).getTime()) / 60000)
    const slaMinutes = alert.severity === 'critical' ? 60 : alert.severity === 'high' ? 240 : 480

    if (minutesSinceCreation >= slaMinutes) return 'breached'
    if (minutesSinceCreation >= slaMinutes * 0.8) return 'warning'
    return 'ok'
  }

  const applyFilters = () => {
    let filtered = [...investigations]

    // Apply filter
    switch (activeFilter) {
      case 'my':
        filtered = filtered.filter(i => i.assigned_to === currentAnalyst)
        break
      case 'unassigned':
        filtered = filtered.filter(i => !i.assigned_to)
        break
      case 'critical':
        filtered = filtered.filter(i => i.severity === 'critical')
        break
      case 'breached_sla':
        filtered = filtered.filter(i => i.sla_status === 'breached')
        break
    }

    // Apply search
    if (searchQuery) {
      filtered = filtered.filter(i =>
        i.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        i.alert_id.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    setFilteredInvestigations(filtered)
  }

  const handleTakeInvestigation = async (investigationId: string) => {
    // Would call API to assign investigation
    setInvestigations(prev =>
      prev.map(inv =>
        inv.alert_id === investigationId
          ? { ...inv, assigned_to: currentAnalyst }
          : inv
      )
    )
  }

  const handleOpenInvestigation = (investigation: Investigation) => {
    // Would navigate to full investigation screen
    window.location.href = `/investigation/${investigation.alert_id}`
  }

  const getStatusIcon = (status: Investigation['investigation_status']) => {
    switch (status) {
      case 'new': return <AlertCircle size={16} className="status-icon-new" />
      case 'enriching': return <RefreshCw size={16} className="status-icon-enriching animate-spin" />
      case 'correlating': return <RefreshCw size={16} className="status-icon-correlating animate-spin" />
      case 'analyzing': return <Target size={16} className="status-icon-analyzing" />
      case 'complete': return <CheckCircle size={16} className="status-icon-complete" />
    }
  }

  const getStatusLabel = (status: Investigation['investigation_status']) => {
    switch (status) {
      case 'new': return 'New'
      case 'enriching': return 'Enriching Entities'
      case 'correlating': return 'Correlating Events'
      case 'analyzing': return 'Analyzing'
      case 'complete': return 'Analysis Complete'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'severity-critical'
      case 'high': return 'severity-high'
      case 'medium': return 'severity-medium'
      case 'low': return 'severity-low'
      default: return ''
    }
  }

  const getSLAColor = (sla: Investigation['sla_status']) => {
    switch (sla) {
      case 'ok': return 'sla-ok'
      case 'warning': return 'sla-warning'
      case 'breached': return 'sla-breached'
    }
  }

  const formatTime = (minutes: number) => {
    if (minutes < 60) return `${minutes}m ago`
    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours}h ago`
    const days = Math.floor(hours / 24)
    return `${days}d ago`
  }

  const stats = {
    total: investigations.length,
    my: investigations.filter(i => i.assigned_to === currentAnalyst).length,
    unassigned: investigations.filter(i => !i.assigned_to).length,
    critical: investigations.filter(i => i.severity === 'critical').length,
    breached: investigations.filter(i => i.sla_status === 'breached').length
  }

  return (
    <div className="investigation-workspace">
      {/* Header */}
      <div className="workspace-header">
        <div className="header-title-section">
          <div className="header-icon">
            <Shield size={24} />
          </div>
          <div>
            <h2 className="workspace-title">Active Investigations</h2>
            <p className="workspace-subtitle">
              Manage and track ongoing alert investigations
            </p>
          </div>
        </div>

        <div className="header-actions">
          <div className="search-bar">
            <Search size={18} />
            <input
              type="text"
              placeholder="Search investigations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
          </div>

          <button
            onClick={fetchInvestigations}
            disabled={loading}
            className="action-btn"
            title="Refresh"
          >
            <RefreshCw size={18} className={loading ? 'animate-spin' : ''} />
          </button>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="filter-tabs">
        <button
          className={`filter-tab ${activeFilter === 'all' ? 'active' : ''}`}
          onClick={() => setActiveFilter('all')}
        >
          All Investigations
          <span className="tab-count">{stats.total}</span>
        </button>

        <button
          className={`filter-tab ${activeFilter === 'my' ? 'active' : ''}`}
          onClick={() => setActiveFilter('my')}
        >
          <Users size={16} />
          My Investigations
          <span className="tab-count">{stats.my}</span>
        </button>

        <button
          className={`filter-tab ${activeFilter === 'unassigned' ? 'active' : ''}`}
          onClick={() => setActiveFilter('unassigned')}
        >
          <AlertCircle size={16} />
          Unassigned
          <span className="tab-count">{stats.unassigned}</span>
        </button>

        <button
          className={`filter-tab ${activeFilter === 'critical' ? 'active' : ''}`}
          onClick={() => setActiveFilter('critical')}
        >
          <TrendingUp size={16} />
          Critical
          <span className="tab-count critical">{stats.critical}</span>
        </button>

        <button
          className={`filter-tab ${activeFilter === 'breached_sla' ? 'active' : ''}`}
          onClick={() => setActiveFilter('breached_sla')}
        >
          <Clock size={16} />
          SLA Breached
          <span className="tab-count breached">{stats.breached}</span>
        </button>
      </div>

      {/* Investigation List */}
      <div className="investigation-list">
        {loading && filteredInvestigations.length === 0 ? (
          <div className="loading-state">
            <RefreshCw size={32} className="animate-spin" />
            <p>Loading investigations...</p>
          </div>
        ) : filteredInvestigations.length === 0 ? (
          <div className="empty-state">
            <Shield size={48} />
            <h3>No Investigations Found</h3>
            <p>
              {searchQuery
                ? 'Try adjusting your search or filters'
                : activeFilter === 'my'
                ? "You don't have any assigned investigations"
                : 'No active investigations at this time'}
            </p>
          </div>
        ) : (
          filteredInvestigations.map(investigation => (
            <div
              key={investigation.alert_id}
              className={`investigation-card ${getSeverityColor(investigation.severity)}`}
              onClick={() => setSelectedInvestigation(investigation)}
            >
              <div className="card-header">
                <div className="header-left">
                  <span className={`severity-badge ${getSeverityColor(investigation.severity)}`}>
                    {investigation.severity.toUpperCase()}
                  </span>
                  <span className={`sla-indicator ${getSLAColor(investigation.sla_status)}`}>
                    <Clock size={14} />
                    {formatTime(investigation.time_since_creation)}
                  </span>
                </div>

                <div className="header-right">
                  {investigation.assigned_to ? (
                    <div className="assigned-badge">
                      <Users size={14} />
                      <span>{investigation.assigned_to === currentAnalyst ? 'You' : investigation.assigned_to}</span>
                    </div>
                  ) : (
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleTakeInvestigation(investigation.alert_id)
                      }}
                      className="take-btn"
                    >
                      <Play size={14} />
                      Take Investigation
                    </button>
                  )}
                </div>
              </div>

              <div className="card-body">
                <h3 className="investigation-title">{investigation.title}</h3>
                <p className="investigation-id">ID: {investigation.alert_id}</p>

                <div className="investigation-meta">
                  <div className="meta-item">
                    <FileText size={14} />
                    <span>{investigation.source}</span>
                  </div>
                  {investigation.mitre_techniques && investigation.mitre_techniques.length > 0 && (
                    <div className="meta-item">
                      <Target size={14} />
                      <span>{investigation.mitre_techniques.length} MITRE Techniques</span>
                    </div>
                  )}
                </div>

                {/* Status and Progress */}
                <div className="status-section">
                  <div className="status-label">
                    {getStatusIcon(investigation.investigation_status)}
                    <span>{getStatusLabel(investigation.investigation_status)}</span>
                  </div>
                  <div className="progress-bar">
                    <div
                      className="progress-fill"
                      style={{ width: `${investigation.progress_percentage}%` }}
                    />
                  </div>
                  <span className="progress-text">{investigation.progress_percentage}%</span>
                </div>

                {/* Enrichment & Correlation Status */}
                <div className="enrichment-status">
                  <div className="status-item">
                    <span className="status-label-small">Enrichment:</span>
                    {investigation.enrichment_status ? (
                      <span className={`status-badge ${investigation.enrichment_status.status}`}>
                        {investigation.enrichment_status.status === 'completed' ? '✓' : '⏳'}{' '}
                        {investigation.enrichment_status.enriched_count}/{investigation.enrichment_status.total_entities} entities
                      </span>
                    ) : (
                      <span className="status-badge pending">Not started</span>
                    )}
                  </div>

                  <div className="status-item">
                    <span className="status-label-small">Correlation:</span>
                    {investigation.correlation_status ? (
                      <span className={`status-badge ${investigation.correlation_status.status}`}>
                        {investigation.correlation_status.status === 'completed' ? '✓' : '⏳'}{' '}
                        {investigation.correlation_status.total_related_events} events
                      </span>
                    ) : (
                      <span className="status-badge pending">Not started</span>
                    )}
                  </div>
                </div>
              </div>

              <div className="card-footer">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleOpenInvestigation(investigation)
                  }}
                  className="open-btn"
                >
                  <Eye size={16} />
                  Open Investigation
                  <ChevronRight size={16} />
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}

export default InvestigationWorkspace
