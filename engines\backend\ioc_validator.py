"""
IOC Validator - Verify IOCs are still active threats
Uses multiple sources to validate and check age of indicators
"""

import asyncio
import aiohttp
import socket
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
import hashlib
import json

logger = logging.getLogger(__name__)


class IOCValidator:
    """Validate IOCs against multiple sources to verify they're still threats"""

    def __init__(self):
        """Initialize the validator"""
        self.validation_cache = {}  # Cache results to avoid repeated lookups
        self.cache_ttl = 3600  # 1 hour cache

    async def validate_ioc(self, ioc: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate an IOC against multiple sources

        Returns validation result with:
        - is_valid: bool
        - is_active: bool (still active threat)
        - age_days: int (days since first seen)
        - reputation_score: float (0-1)
        - sources_confirmed: list of sources that confirmed it
        """
        ioc_type = ioc.get('ioc_type', '').lower()
        value = ioc.get('ioc_value', '') or ioc.get('value', '')

        if not value:
            return {
                'is_valid': False,
                'is_active': False,
                'reason': 'No IOC value'
            }

        # Check cache
        cache_key = f"{ioc_type}:{value}"
        if cache_key in self.validation_cache:
            cached = self.validation_cache[cache_key]
            if datetime.utcnow() - cached['timestamp'] < timedelta(seconds=self.cache_ttl):
                return cached['result']

        # Validate based on type
        if ioc_type in ['ip', 'ipv4', 'ipv6']:
            result = await self.validate_ip(value)
        elif ioc_type in ['domain', 'fqdn', 'hostname']:
            result = await self.validate_domain(value)
        elif ioc_type in ['hash', 'file-hash', 'filehash-sha256', 'filehash-md5']:
            result = await self.validate_hash(value)
        elif ioc_type in ['url', 'uri']:
            result = await self.validate_url(value)
        else:
            result = {
                'is_valid': True,  # Can't validate, assume valid
                'is_active': True,
                'reason': f'Unknown IOC type: {ioc_type}'
            }

        # Calculate age
        result['age_days'] = self.calculate_age(ioc)

        # Adjust validity based on age
        if result['age_days'] > 90:
            result['is_active'] = False
            result['reason'] = f"Stale IOC ({result['age_days']} days old)"

        # Cache the result
        self.validation_cache[cache_key] = {
            'timestamp': datetime.utcnow(),
            'result': result
        }

        return result

    async def validate_ip(self, ip: str) -> Dict[str, Any]:
        """Validate an IP address"""
        result = {
            'is_valid': True,
            'is_active': False,
            'reputation_score': 0.0,
            'sources_confirmed': []
        }

        # Extract IP if it has port
        if ':' in ip:
            ip = ip.split(':')[0]

        # Check if IP is reachable
        is_reachable = await self.check_ip_reachable(ip)
        if not is_reachable:
            result['is_active'] = False
            result['reason'] = 'IP not reachable'
            return result

        # Check against threat intelligence sources
        # Note: In production, you'd use actual APIs like VirusTotal, AbuseIPDB, etc.

        # 1. Check AbuseIPDB (if API key available)
        abuse_score = await self.check_abuseipdb(ip)
        if abuse_score is not None:
            result['sources_confirmed'].append('AbuseIPDB')
            result['reputation_score'] = max(result['reputation_score'], abuse_score)

        # 2. Check Talos Intelligence
        talos_result = await self.check_talos(ip)
        if talos_result:
            result['sources_confirmed'].append('Talos')
            result['reputation_score'] = max(result['reputation_score'], talos_result)

        # 3. Check if it's a Tor exit node
        is_tor = await self.check_tor_exit(ip)
        if is_tor:
            result['sources_confirmed'].append('Tor Exit')
            result['reputation_score'] = max(result['reputation_score'], 0.6)

        # Determine if active based on reputation
        result['is_active'] = result['reputation_score'] > 0.3

        return result

    async def validate_domain(self, domain: str) -> Dict[str, Any]:
        """Validate a domain"""
        result = {
            'is_valid': True,
            'is_active': False,
            'reputation_score': 0.0,
            'sources_confirmed': []
        }

        # Check if domain resolves
        is_resolving = await self.check_domain_resolves(domain)
        if not is_resolving:
            result['is_active'] = False
            result['reason'] = 'Domain not resolving'
            return result

        # Check against threat intelligence
        # 1. Check URLhaus (abuse.ch)
        urlhaus_result = await self.check_urlhaus(domain)
        if urlhaus_result:
            result['sources_confirmed'].append('URLhaus')
            result['reputation_score'] = max(result['reputation_score'], 0.8)

        # 2. Check if newly registered (suspicious)
        is_new = await self.check_newly_registered(domain)
        if is_new:
            result['sources_confirmed'].append('Newly Registered')
            result['reputation_score'] = max(result['reputation_score'], 0.5)

        result['is_active'] = result['reputation_score'] > 0.3

        return result

    async def validate_hash(self, file_hash: str) -> Dict[str, Any]:
        """Validate a file hash"""
        result = {
            'is_valid': True,
            'is_active': True,  # Hashes don't expire like IPs/domains
            'reputation_score': 0.5,  # Default medium confidence
            'sources_confirmed': []
        }

        # Validate hash format
        hash_len = len(file_hash)
        if hash_len == 32:
            result['hash_type'] = 'MD5'
        elif hash_len == 40:
            result['hash_type'] = 'SHA1'
        elif hash_len == 64:
            result['hash_type'] = 'SHA256'
        else:
            result['is_valid'] = False
            result['reason'] = 'Invalid hash length'
            return result

        # Check against malware databases
        # 1. Check MalwareBazaar (abuse.ch)
        bazaar_result = await self.check_malwarebazaar(file_hash)
        if bazaar_result:
            result['sources_confirmed'].append('MalwareBazaar')
            result['reputation_score'] = max(result['reputation_score'], 0.9)

        # Hashes are generally reliable if from trusted source
        result['is_active'] = len(result['sources_confirmed']) > 0

        return result

    async def validate_url(self, url: str) -> Dict[str, Any]:
        """Validate a URL"""
        result = {
            'is_valid': True,
            'is_active': False,
            'reputation_score': 0.0,
            'sources_confirmed': []
        }

        # Extract domain from URL
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            domain = parsed.netloc

            # First validate the domain
            domain_result = await self.validate_domain(domain)
            result.update(domain_result)

            # Check specific URL in URLhaus
            urlhaus_result = await self.check_urlhaus_url(url)
            if urlhaus_result:
                result['sources_confirmed'].append('URLhaus-URL')
                result['reputation_score'] = max(result['reputation_score'], 0.9)

        except Exception as e:
            result['is_valid'] = False
            result['reason'] = f'URL parse error: {e}'

        return result

    async def check_ip_reachable(self, ip: str) -> bool:
        """Check if an IP is reachable (basic connectivity test)"""
        try:
            # Try to connect to common ports
            for port in [80, 443, 22, 21]:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((ip, port))
                sock.close()
                if result == 0:
                    return True
            return False
        except:
            return False

    async def check_domain_resolves(self, domain: str) -> bool:
        """Check if domain resolves to an IP"""
        try:
            socket.gethostbyname(domain)
            return True
        except:
            return False

    async def check_abuseipdb(self, ip: str) -> Optional[float]:
        """Check IP reputation on AbuseIPDB (requires API key)"""
        api_key = os.getenv('ABUSEIPDB_API_KEY')
        if not api_key:
            return None

        try:
            async with aiohttp.ClientSession() as session:
                headers = {'Key': api_key, 'Accept': 'application/json'}
                params = {'ipAddress': ip, 'maxAgeInDays': '90'}

                async with session.get(
                    'https://api.abuseipdb.com/api/v2/check',
                    headers=headers,
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        abuse_score = data.get('data', {}).get('abuseConfidenceScore', 0)
                        return abuse_score / 100.0  # Convert to 0-1
        except:
            pass

        return None

    async def check_talos(self, ip: str) -> Optional[float]:
        """Check Cisco Talos reputation (web scraping fallback)"""
        # In production, use proper API
        # This is a simplified check
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f'https://talosintelligence.com/reputation_center/lookup?search={ip}',
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        content = await response.text()
                        if 'poor' in content.lower():
                            return 0.8
                        elif 'neutral' in content.lower():
                            return 0.4
                        elif 'good' in content.lower():
                            return 0.1
        except:
            pass

        return None

    async def check_tor_exit(self, ip: str) -> bool:
        """Check if IP is a Tor exit node"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    'https://check.torproject.org/torbulkexitlist',
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        exit_nodes = await response.text()
                        return ip in exit_nodes
        except:
            pass

        return False

    async def check_urlhaus(self, domain: str) -> bool:
        """Check URLhaus for malicious domains"""
        try:
            async with aiohttp.ClientSession() as session:
                data = {'host': domain}
                async with session.post(
                    'https://urlhaus-api.abuse.ch/v1/host/',
                    data=data,
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get('query_status') == 'ok':
                            return len(result.get('urls', [])) > 0
        except:
            pass

        return False

    async def check_urlhaus_url(self, url: str) -> bool:
        """Check specific URL in URLhaus"""
        try:
            async with aiohttp.ClientSession() as session:
                data = {'url': url}
                async with session.post(
                    'https://urlhaus-api.abuse.ch/v1/url/',
                    data=data,
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get('query_status') == 'ok':
                            return True
        except:
            pass

        return False

    async def check_malwarebazaar(self, file_hash: str) -> bool:
        """Check MalwareBazaar for file hash"""
        try:
            async with aiohttp.ClientSession() as session:
                data = {'query': 'get_info', 'hash': file_hash}
                async with session.post(
                    'https://mb-api.abuse.ch/api/v1/',
                    data=data,
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get('query_status') == 'ok':
                            return True
        except:
            pass

        return False

    async def check_newly_registered(self, domain: str) -> bool:
        """Check if domain was recently registered (suspicious)"""
        # In production, use WHOIS API
        # For now, return False (not implemented)
        return False

    def calculate_age(self, ioc: Dict[str, Any]) -> int:
        """Calculate age of IOC in days"""
        created = ioc.get('created_at') or ioc.get('first_seen') or ioc.get('created')

        if not created:
            return 0

        try:
            if isinstance(created, str):
                created_dt = datetime.fromisoformat(created.replace('Z', '+00:00'))
            else:
                created_dt = created

            age_days = (datetime.utcnow() - created_dt.replace(tzinfo=None)).days
            return max(0, age_days)
        except:
            return 0

    async def batch_validate(self, iocs: List[Dict[str, Any]], max_concurrent: int = 10) -> List[Dict[str, Any]]:
        """Validate multiple IOCs concurrently"""
        validated_iocs = []
        semaphore = asyncio.Semaphore(max_concurrent)

        async def validate_with_limit(ioc):
            async with semaphore:
                validation = await self.validate_ioc(ioc)
                ioc['validation'] = validation
                return ioc

        tasks = [validate_with_limit(ioc) for ioc in iocs]
        results = await asyncio.gather(*tasks)

        # Filter out invalid/inactive IOCs
        for ioc in results:
            validation = ioc.get('validation', {})
            if validation.get('is_valid') and validation.get('is_active'):
                validated_iocs.append(ioc)
            else:
                logger.debug(f"IOC failed validation: {ioc.get('ioc_value', '')[:50]} - {validation.get('reason', 'Unknown')}")

        logger.info(f"Validated {len(validated_iocs)}/{len(iocs)} IOCs as active threats")
        return validated_iocs


# Add os import for environment variables
import os


async def test_validator():
    """Test the IOC validator"""
    validator = IOCValidator()

    test_iocs = [
        {'ioc_value': '*******', 'ioc_type': 'ip'},  # Google DNS
        {'ioc_value': 'google.com', 'ioc_type': 'domain'},  # Legitimate
        {'ioc_value': 'definitely-not-real-malware-c2.com', 'ioc_type': 'domain'},  # Non-existent
        {'ioc_value': '098f6bcd4621d373cade4e832627b4f6', 'ioc_type': 'hash'},  # MD5 of "test"
        {'ioc_value': 'http://example.com/malware.exe', 'ioc_type': 'url'},
    ]

    for ioc in test_iocs:
        print(f"\nValidating: {ioc['ioc_value']}")
        result = await validator.validate_ioc(ioc)
        print(f"  Valid: {result.get('is_valid')}")
        print(f"  Active: {result.get('is_active')}")
        print(f"  Score: {result.get('reputation_score', 0):.2f}")
        print(f"  Sources: {result.get('sources_confirmed', [])}")
        if result.get('reason'):
            print(f"  Reason: {result['reason']}")


if __name__ == "__main__":
    asyncio.run(test_validator())