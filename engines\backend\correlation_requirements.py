"""
Correlation Requirements Engine
Determines required log sources for detecting various attack patterns
"""

from typing import Dict, List, Optional, Tuple
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class CorrelationRequirementsEngine:
    """Engine for determining log source requirements for correlation"""

    def __init__(self):
        self.attack_requirements = self._initialize_attack_requirements()
        self.technique_to_sources = self._initialize_technique_mapping()
        self.synergy_matrix = self._initialize_synergy_matrix()

    def _initialize_attack_requirements(self) -> Dict:
        """Initialize requirements for different attack types"""
        return {
            'lateral_movement': {
                'name': 'Lateral Movement Detection',
                'mitre_techniques': ['T1021', 'T1072', 'T1570', 'T1550'],
                'required_sources': [
                    {
                        'category': 'identity',
                        'min_tier': 'GOLD',
                        'events': ['authentication', 'privilege_escalation'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'network',
                        'min_tier': 'SILVER',
                        'events': ['rdp', 'smb', 'ssh', 'winrm'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'endpoint',
                        'min_tier': 'GOLD',
                        'events': ['process_creation', 'service_creation'],
                        'importance': 'high'
                    }
                ],
                'time_window': 300,  # 5 minutes
                'min_confidence': 75,
                'indicators': [
                    'Multiple authentication attempts from same source',
                    'RDP/SMB connections to multiple hosts',
                    'Use of administrative tools (PsExec, WMI)',
                    'Service creation on remote hosts'
                ]
            },

            'ransomware': {
                'name': 'Ransomware Detection',
                'mitre_techniques': ['T1486', 'T1490', 'T1059', 'T1047'],
                'required_sources': [
                    {
                        'category': 'endpoint',
                        'min_tier': 'GOLD',
                        'events': ['file_modification', 'process_behavior'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'file_integrity',
                        'min_tier': 'SILVER',
                        'events': ['mass_modification', 'entropy_change'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'backup',
                        'min_tier': 'BRONZE',
                        'events': ['shadow_copy_deletion', 'backup_disruption'],
                        'importance': 'high'
                    }
                ],
                'time_window': 600,  # 10 minutes
                'min_confidence': 85,
                'indicators': [
                    'Mass file encryption',
                    'Shadow copy deletion',
                    'High file entropy changes',
                    'Ransom note creation',
                    'Backup system disruption'
                ]
            },

            'data_exfiltration': {
                'name': 'Data Exfiltration Detection',
                'mitre_techniques': ['T1041', 'T1048', 'T1567', 'T1537'],
                'required_sources': [
                    {
                        'category': 'network',
                        'min_tier': 'GOLD',
                        'events': ['large_transfer', 'unusual_destination'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'dlp',
                        'min_tier': 'SILVER',
                        'events': ['sensitive_data_movement'],
                        'importance': 'high'
                    },
                    {
                        'category': 'cloud',
                        'min_tier': 'SILVER',
                        'events': ['unauthorized_share', 'bulk_download'],
                        'importance': 'medium'
                    }
                ],
                'time_window': 3600,  # 1 hour
                'min_confidence': 70,
                'indicators': [
                    'Large data transfer to external IPs',
                    'DNS tunneling activity',
                    'Cloud storage uploads',
                    'Compression before transfer',
                    'Off-hours data movement'
                ]
            },

            'privilege_escalation': {
                'name': 'Privilege Escalation Detection',
                'mitre_techniques': ['T1068', 'T1055', 'T1134', 'T1548'],
                'required_sources': [
                    {
                        'category': 'endpoint',
                        'min_tier': 'PLATINUM',
                        'events': ['process_injection', 'token_manipulation'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'identity',
                        'min_tier': 'GOLD',
                        'events': ['privilege_change', 'group_modification'],
                        'importance': 'critical'
                    }
                ],
                'time_window': 180,  # 3 minutes
                'min_confidence': 80,
                'indicators': [
                    'Process injection attempts',
                    'Token manipulation',
                    'Unusual privilege grants',
                    'Exploit execution',
                    'Service privilege modification'
                ]
            },

            'persistence': {
                'name': 'Persistence Mechanism Detection',
                'mitre_techniques': ['T1547', 'T1053', 'T1136', 'T1543'],
                'required_sources': [
                    {
                        'category': 'endpoint',
                        'min_tier': 'GOLD',
                        'events': ['registry_modification', 'scheduled_task'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'identity',
                        'min_tier': 'SILVER',
                        'events': ['account_creation', 'group_addition'],
                        'importance': 'high'
                    }
                ],
                'time_window': 1800,  # 30 minutes
                'min_confidence': 75,
                'indicators': [
                    'Registry run key modification',
                    'Scheduled task creation',
                    'Service installation',
                    'Account creation',
                    'Startup folder modification'
                ]
            },

            'credential_access': {
                'name': 'Credential Access Detection',
                'mitre_techniques': ['T1003', 'T1558', 'T1110', 'T1555'],
                'required_sources': [
                    {
                        'category': 'endpoint',
                        'min_tier': 'PLATINUM',
                        'events': ['lsass_access', 'credential_dumping'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'identity',
                        'min_tier': 'GOLD',
                        'events': ['kerberos_abuse', 'password_spray'],
                        'importance': 'critical'
                    }
                ],
                'time_window': 600,  # 10 minutes
                'min_confidence': 85,
                'indicators': [
                    'LSASS process access',
                    'Mimikatz indicators',
                    'Kerberoasting activity',
                    'Password spraying',
                    'Credential store access'
                ]
            },

            'command_and_control': {
                'name': 'Command and Control Detection',
                'mitre_techniques': ['T1071', 'T1132', 'T1573', 'T1090'],
                'required_sources': [
                    {
                        'category': 'network',
                        'min_tier': 'GOLD',
                        'events': ['beaconing', 'encrypted_channel'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'proxy',
                        'min_tier': 'SILVER',
                        'events': ['suspicious_domains', 'user_agent'],
                        'importance': 'high'
                    },
                    {
                        'category': 'dns',
                        'min_tier': 'SILVER',
                        'events': ['dga_domains', 'tunneling'],
                        'importance': 'high'
                    }
                ],
                'time_window': 7200,  # 2 hours
                'min_confidence': 70,
                'indicators': [
                    'Periodic beaconing',
                    'Encrypted channels',
                    'Domain generation algorithms',
                    'DNS tunneling',
                    'Proxy usage patterns'
                ]
            },

            'insider_threat': {
                'name': 'Insider Threat Detection',
                'mitre_techniques': ['T1078', 'T1531', 'T1567', 'T1213'],
                'required_sources': [
                    {
                        'category': 'identity',
                        'min_tier': 'PLATINUM',
                        'events': ['unusual_access', 'privilege_abuse'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'database',
                        'min_tier': 'GOLD',
                        'events': ['bulk_query', 'sensitive_access'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'file_access',
                        'min_tier': 'SILVER',
                        'events': ['mass_download', 'sensitive_file_access'],
                        'importance': 'high'
                    }
                ],
                'time_window': 86400,  # 24 hours
                'min_confidence': 80,
                'indicators': [
                    'Unusual access patterns',
                    'After-hours activity',
                    'Mass data collection',
                    'Access to unusual resources',
                    'Data staging behavior'
                ]
            },

            'supply_chain': {
                'name': 'Supply Chain Attack Detection',
                'mitre_techniques': ['T1195', 'T1072', 'T1199'],
                'required_sources': [
                    {
                        'category': 'endpoint',
                        'min_tier': 'PLATINUM',
                        'events': ['software_update', 'dll_loading'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'network',
                        'min_tier': 'GOLD',
                        'events': ['update_server_comm', 'certificate_validation'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'file_integrity',
                        'min_tier': 'GOLD',
                        'events': ['signature_validation', 'hash_mismatch'],
                        'importance': 'high'
                    }
                ],
                'time_window': 3600,  # 1 hour
                'min_confidence': 85,
                'indicators': [
                    'Unexpected software updates',
                    'Certificate validation failures',
                    'Suspicious DLL loading',
                    'Update server redirection',
                    'File signature mismatches'
                ]
            },

            'apt_campaign': {
                'name': 'APT Campaign Detection',
                'mitre_techniques': ['T1583', 'T1587', 'T1588', 'T1608'],
                'required_sources': [
                    {
                        'category': 'endpoint',
                        'min_tier': 'PLATINUM',
                        'events': ['advanced_techniques', 'zero_day'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'network',
                        'min_tier': 'PLATINUM',
                        'events': ['sophisticated_c2', 'custom_protocol'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'identity',
                        'min_tier': 'GOLD',
                        'events': ['targeted_compromise', 'long_term_access'],
                        'importance': 'critical'
                    },
                    {
                        'category': 'threat_intel',
                        'min_tier': 'GOLD',
                        'events': ['ioc_match', 'ttp_match'],
                        'importance': 'high'
                    }
                ],
                'time_window': 2592000,  # 30 days
                'min_confidence': 90,
                'indicators': [
                    'Long-term persistent access',
                    'Custom malware deployment',
                    'Zero-day exploitation',
                    'Targeted spear-phishing',
                    'Data staging and slow exfiltration'
                ]
            }
        }

    def _initialize_technique_mapping(self) -> Dict:
        """Map MITRE techniques to required log sources"""
        return {
            'T1003': ['endpoint', 'identity'],  # Credential Dumping
            'T1021': ['network', 'identity', 'endpoint'],  # Remote Services
            'T1041': ['network', 'dlp'],  # Exfiltration Over C2
            'T1047': ['endpoint', 'identity'],  # Windows Management Instrumentation
            'T1048': ['network', 'firewall'],  # Exfiltration Over Alternative Protocol
            'T1053': ['endpoint'],  # Scheduled Task/Job
            'T1055': ['endpoint'],  # Process Injection
            'T1059': ['endpoint'],  # Command and Scripting Interpreter
            'T1068': ['endpoint', 'vulnerability'],  # Exploitation for Privilege Escalation
            'T1070': ['endpoint', 'identity'],  # Indicator Removal
            'T1071': ['network', 'proxy'],  # Application Layer Protocol
            'T1078': ['identity'],  # Valid Accounts
            'T1090': ['network', 'proxy'],  # Proxy
            'T1110': ['identity', 'network'],  # Brute Force
            'T1132': ['network', 'dns'],  # Data Encoding
            'T1134': ['endpoint', 'identity'],  # Access Token Manipulation
            'T1136': ['identity'],  # Create Account
            'T1195': ['endpoint', 'network'],  # Supply Chain Compromise
            'T1199': ['network', 'identity'],  # Trusted Relationship
            'T1213': ['database', 'file_access'],  # Data from Information Repositories
            'T1486': ['endpoint', 'file_integrity'],  # Data Encrypted for Impact
            'T1490': ['endpoint', 'backup'],  # Inhibit System Recovery
            'T1531': ['identity'],  # Account Access Removal
            'T1537': ['cloud', 'network'],  # Transfer Data to Cloud Account
            'T1543': ['endpoint'],  # Create or Modify System Process
            'T1547': ['endpoint'],  # Boot or Logon Autostart Execution
            'T1548': ['endpoint', 'identity'],  # Abuse Elevation Control Mechanism
            'T1550': ['identity', 'endpoint'],  # Use Alternate Authentication Material
            'T1555': ['endpoint', 'browser'],  # Credentials from Password Stores
            'T1558': ['identity', 'network'],  # Steal or Forge Kerberos Tickets
            'T1567': ['network', 'cloud', 'dlp'],  # Exfiltration to Cloud Storage
            'T1570': ['network', 'identity'],  # Lateral Tool Transfer
            'T1573': ['network'],  # Encrypted Channel
            'T1583': ['threat_intel', 'network'],  # Acquire Infrastructure
            'T1587': ['threat_intel'],  # Develop Capabilities
            'T1588': ['threat_intel'],  # Obtain Capabilities
            'T1608': ['threat_intel', 'network'],  # Stage Capabilities
        }

    def _initialize_synergy_matrix(self) -> Dict:
        """Initialize synergy bonuses for source combinations"""
        return {
            'endpoint_network': {
                'sources': ['endpoint', 'network'],
                'multiplier': 1.5,
                'description': 'Process to network correlation'
            },
            'identity_endpoint': {
                'sources': ['identity', 'endpoint'],
                'multiplier': 1.6,
                'description': 'User to process correlation'
            },
            'network_dns': {
                'sources': ['network', 'dns'],
                'multiplier': 1.4,
                'description': 'DNS to network traffic correlation'
            },
            'endpoint_file_integrity': {
                'sources': ['endpoint', 'file_integrity'],
                'multiplier': 1.5,
                'description': 'Process to file correlation'
            },
            'full_triad': {
                'sources': ['endpoint', 'network', 'identity'],
                'multiplier': 1.8,
                'description': 'Complete visibility triad'
            },
            'full_stack': {
                'sources': ['endpoint', 'network', 'identity', 'cloud'],
                'multiplier': 2.0,
                'description': 'Full stack visibility'
            }
        }

    def get_requirements(self, attack_type: str) -> Optional[Dict]:
        """Get requirements for detecting an attack type"""
        return self.attack_requirements.get(attack_type)

    def check_requirements_met(self, attack_type: str, available_sources: List[Dict]) -> Dict:
        """Check if available sources meet requirements for detecting an attack"""
        requirements = self.get_requirements(attack_type)
        if not requirements:
            return {
                'attack_type': attack_type,
                'requirements_met': False,
                'reason': 'Unknown attack type',
                'missing_sources': [],
                'confidence': 0
            }

        # Check each required source
        missing_sources = []
        partial_sources = []
        met_sources = []

        for req_source in requirements['required_sources']:
            source_found = False
            source_adequate = False

            for available in available_sources:
                if available['category'] == req_source['category']:
                    source_found = True
                    # Check if tier meets minimum
                    if self._compare_tiers(available['tier'], req_source['min_tier']):
                        source_adequate = True
                        met_sources.append({
                            'category': req_source['category'],
                            'required_tier': req_source['min_tier'],
                            'actual_tier': available['tier'],
                            'status': 'met'
                        })
                    else:
                        partial_sources.append({
                            'category': req_source['category'],
                            'required_tier': req_source['min_tier'],
                            'actual_tier': available['tier'],
                            'status': 'insufficient_quality'
                        })
                    break

            if not source_found:
                missing_sources.append({
                    'category': req_source['category'],
                    'required_tier': req_source['min_tier'],
                    'importance': req_source['importance'],
                    'status': 'missing'
                })
            elif not source_adequate and req_source['importance'] == 'critical':
                missing_sources.append({
                    'category': req_source['category'],
                    'required_tier': req_source['min_tier'],
                    'importance': req_source['importance'],
                    'status': 'quality_too_low'
                })

        # Calculate confidence based on met requirements
        total_sources = len(requirements['required_sources'])
        critical_sources = [s for s in requirements['required_sources'] if s['importance'] == 'critical']
        critical_met = sum(1 for s in met_sources if any(
            r['category'] == s['category'] and r['importance'] == 'critical'
            for r in requirements['required_sources']
        ))

        if len(critical_sources) > 0:
            base_confidence = (critical_met / len(critical_sources)) * 70  # Critical sources worth 70%
        else:
            base_confidence = 0

        # Add confidence for non-critical sources
        other_met = len(met_sources) - critical_met
        other_total = total_sources - len(critical_sources)
        if other_total > 0:
            base_confidence += (other_met / other_total) * 30  # Other sources worth 30%

        # Apply synergy bonuses
        synergy_bonus = self._calculate_synergy_bonus([s['category'] for s in met_sources])
        final_confidence = min(base_confidence * synergy_bonus, 100)

        # Determine if requirements are met
        requirements_met = (
            len(missing_sources) == 0 or
            (final_confidence >= requirements['min_confidence'] and critical_met == len(critical_sources))
        )

        return {
            'attack_type': attack_type,
            'attack_name': requirements['name'],
            'requirements_met': requirements_met,
            'confidence': round(final_confidence, 1),
            'min_confidence_required': requirements['min_confidence'],
            'met_sources': met_sources,
            'partial_sources': partial_sources,
            'missing_sources': missing_sources,
            'indicators': requirements['indicators'],
            'time_window': requirements['time_window'],
            'synergy_bonus': synergy_bonus
        }

    def _compare_tiers(self, actual_tier: str, required_tier: str) -> bool:
        """Compare if actual tier meets or exceeds required tier"""
        tier_order = {'BRONZE': 1, 'SILVER': 2, 'GOLD': 3, 'PLATINUM': 4}
        return tier_order.get(actual_tier, 0) >= tier_order.get(required_tier, 0)

    def _calculate_synergy_bonus(self, source_categories: List[str]) -> float:
        """Calculate synergy bonus for source combination"""
        bonus = 1.0
        source_set = set(source_categories)

        for synergy_name, synergy_config in self.synergy_matrix.items():
            required_sources = set(synergy_config['sources'])
            if required_sources.issubset(source_set):
                bonus = max(bonus, synergy_config['multiplier'])

        return bonus

    def get_technique_sources(self, technique_id: str) -> List[str]:
        """Get required log sources for a MITRE technique"""
        return self.technique_to_sources.get(technique_id, [])

    def recommend_sources(self, current_sources: List[Dict], target_attacks: List[str]) -> Dict:
        """Recommend additional sources to improve detection capability"""
        recommendations = {
            'current_capability': {},
            'recommended_sources': [],
            'capability_improvement': {}
        }

        # Assess current capability for each target attack
        for attack in target_attacks:
            assessment = self.check_requirements_met(attack, current_sources)
            recommendations['current_capability'][attack] = {
                'confidence': assessment['confidence'],
                'requirements_met': assessment['requirements_met']
            }

        # Find missing critical sources across all attacks
        all_missing = {}
        for attack in target_attacks:
            requirements = self.get_requirements(attack)
            if requirements:
                assessment = self.check_requirements_met(attack, current_sources)
                for missing in assessment['missing_sources']:
                    key = f"{missing['category']}_{missing['required_tier']}"
                    if key not in all_missing:
                        all_missing[key] = {
                            'category': missing['category'],
                            'min_tier': missing['required_tier'],
                            'needed_for': [],
                            'importance_score': 0
                        }
                    all_missing[key]['needed_for'].append(attack)
                    if missing['importance'] == 'critical':
                        all_missing[key]['importance_score'] += 3
                    elif missing['importance'] == 'high':
                        all_missing[key]['importance_score'] += 2
                    else:
                        all_missing[key]['importance_score'] += 1

        # Sort recommendations by importance
        sorted_recommendations = sorted(
            all_missing.values(),
            key=lambda x: (x['importance_score'], len(x['needed_for'])),
            reverse=True
        )

        # Add top recommendations
        for rec in sorted_recommendations[:5]:  # Top 5 recommendations
            recommendations['recommended_sources'].append({
                'category': rec['category'],
                'minimum_tier': rec['min_tier'],
                'improves_detection_for': rec['needed_for'],
                'priority': 'high' if rec['importance_score'] >= 5 else 'medium'
            })

        # Calculate improvement if recommendations are implemented
        hypothetical_sources = current_sources.copy()
        for rec in recommendations['recommended_sources']:
            hypothetical_sources.append({
                'category': rec['category'],
                'tier': rec['minimum_tier']
            })

        for attack in target_attacks:
            new_assessment = self.check_requirements_met(attack, hypothetical_sources)
            recommendations['capability_improvement'][attack] = {
                'current_confidence': recommendations['current_capability'][attack]['confidence'],
                'improved_confidence': new_assessment['confidence'],
                'improvement': round(
                    new_assessment['confidence'] - recommendations['current_capability'][attack]['confidence'],
                    1
                )
            }

        return recommendations

    def generate_correlation_rule(self, attack_type: str) -> Optional[Dict]:
        """Generate a correlation rule based on attack requirements"""
        requirements = self.get_requirements(attack_type)
        if not requirements:
            return None

        rule = {
            'rule_id': f"correlation_{attack_type}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            'name': requirements['name'],
            'description': f"Correlation rule for detecting {requirements['name']}",
            'attack_type': attack_type,
            'mitre_techniques': requirements['mitre_techniques'],
            'correlation_logic': {
                'time_window': requirements['time_window'],
                'min_events': len(requirements['required_sources']),
                'event_sources': []
            },
            'detection_indicators': requirements['indicators'],
            'response_actions': []
        }

        # Build correlation logic
        for source in requirements['required_sources']:
            rule['correlation_logic']['event_sources'].append({
                'source_category': source['category'],
                'required_events': source['events'],
                'min_tier': source['min_tier'],
                'correlation_fields': self._get_correlation_fields(source['category'])
            })

        # Add response actions based on attack type
        if attack_type == 'ransomware':
            rule['response_actions'] = [
                'isolate_endpoint',
                'kill_processes',
                'backup_critical_data',
                'notify_incident_response'
            ]
        elif attack_type == 'data_exfiltration':
            rule['response_actions'] = [
                'block_destination_ip',
                'terminate_connection',
                'capture_traffic',
                'notify_data_protection_team'
            ]
        elif attack_type == 'lateral_movement':
            rule['response_actions'] = [
                'disable_account',
                'reset_credentials',
                'isolate_affected_systems',
                'increase_monitoring'
            ]
        else:
            rule['response_actions'] = [
                'create_incident',
                'notify_soc',
                'increase_logging',
                'gather_forensics'
            ]

        return rule

    def _get_correlation_fields(self, category: str) -> List[str]:
        """Get fields to use for correlation based on source category"""
        correlation_fields = {
            'endpoint': ['hostname', 'process_name', 'process_id', 'user', 'command_line'],
            'network': ['src_ip', 'dst_ip', 'dst_port', 'protocol', 'bytes_transferred'],
            'identity': ['username', 'source_ip', 'target_account', 'authentication_type'],
            'file_integrity': ['file_path', 'hash', 'file_size', 'modification_time'],
            'dns': ['domain', 'query_type', 'response_ip', 'query_count'],
            'proxy': ['url', 'user_agent', 'method', 'response_code', 'bytes'],
            'cloud': ['resource_id', 'action', 'user_identity', 'source_ip'],
            'database': ['query', 'table', 'user', 'rows_affected', 'database_name'],
            'dlp': ['data_classification', 'destination', 'volume', 'policy_violated']
        }
        return correlation_fields.get(category, ['timestamp', 'source'])