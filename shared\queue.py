"""
Message Queue System for SIEMLess v2.0
Inter-engine communication using Redis for speed and reliability
"""
import json
import asyncio
import redis.asyncio as redis
from typing import Dict, Any, Optional, List
from datetime import datetime
import os
import uuid


class MessageQueue:
    """
    Asynchronous message queue for inter-engine communication
    Uses Redis for fast, reliable message passing
    """

    def __init__(self, engine_name: str):
        """
        Initialize message queue for an engine

        Args:
            engine_name: Name of the engine using this queue
        """
        self.engine_name = engine_name
        self.queue_name = f"siemless:queue:{engine_name}"
        self.redis_client = None
        self.pubsub = None
        self.connected = False

    async def connect(self):
        """Connect to Redis"""
        if not self.connected:
            self.redis_client = await redis.Redis(
                host=os.environ.get('REDIS_HOST', 'localhost'),
                port=int(os.environ.get('REDIS_PORT', 6379)),
                decode_responses=True
            )
            self.pubsub = self.redis_client.pubsub()
            await self.pubsub.subscribe(self.queue_name)
            self.connected = True

    async def get_message(self, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
        """
        Get next message from queue

        Args:
            timeout: How long to wait for a message (seconds)

        Returns:
            Message dictionary or None if no message
        """
        if not self.connected:
            await self.connect()

        try:
            # Try to get message from queue
            message_data = await self.redis_client.blpop(
                self.queue_name,
                timeout=timeout
            )

            if message_data:
                # Parse the message
                _, message_str = message_data
                message = json.loads(message_str)

                # Add metadata
                message['_received_at'] = datetime.utcnow().isoformat()
                message['_receiver'] = self.engine_name

                return message

        except asyncio.TimeoutError:
            # No message available
            return None
        except Exception as e:
            print(f"Error getting message: {e}")
            return None

    async def send_message(self, target_engine: str, data: Dict[str, Any]) -> bool:
        """
        Send message to another engine

        Args:
            target_engine: Name of the target engine
            data: Message data to send

        Returns:
            True if message sent successfully
        """
        if not self.connected:
            await self.connect()

        try:
            # Create message with metadata
            message = {
                'id': str(uuid.uuid4()),
                'source': self.engine_name,
                'target': target_engine,
                'timestamp': datetime.utcnow().isoformat(),
                'data': data
            }

            # Send to target queue
            target_queue = f"siemless:queue:{target_engine}"
            await self.redis_client.rpush(
                target_queue,
                json.dumps(message)
            )

            # Log message sent
            await self._log_message_flow(message, 'sent')

            return True

        except Exception as e:
            print(f"Error sending message: {e}")
            return False

    async def broadcast_message(self, channel: str, data: Dict[str, Any]) -> bool:
        """
        Broadcast message to all subscribers of a channel

        Args:
            channel: Channel name to broadcast on
            data: Message data to broadcast

        Returns:
            True if broadcast successful
        """
        if not self.connected:
            await self.connect()

        try:
            # Create broadcast message
            message = {
                'id': str(uuid.uuid4()),
                'source': self.engine_name,
                'channel': channel,
                'timestamp': datetime.utcnow().isoformat(),
                'data': data
            }

            # Publish to channel
            await self.redis_client.publish(
                f"siemless:channel:{channel}",
                json.dumps(message)
            )

            return True

        except Exception as e:
            print(f"Error broadcasting message: {e}")
            return False

    async def subscribe_channel(self, channel: str) -> bool:
        """
        Subscribe to a broadcast channel

        Args:
            channel: Channel name to subscribe to

        Returns:
            True if subscription successful
        """
        if not self.connected:
            await self.connect()

        try:
            await self.pubsub.subscribe(f"siemless:channel:{channel}")
            return True
        except Exception as e:
            print(f"Error subscribing to channel: {e}")
            return False

    async def get_broadcast(self, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
        """
        Get next broadcast message from subscribed channels

        Args:
            timeout: How long to wait for a message (seconds)

        Returns:
            Broadcast message or None if no message
        """
        if not self.connected:
            await self.connect()

        try:
            # Get message from pubsub with timeout
            message = await asyncio.wait_for(
                self.pubsub.get_message(ignore_subscribe_messages=True),
                timeout=timeout
            )

            if message and message['type'] == 'message':
                return json.loads(message['data'])

        except asyncio.TimeoutError:
            return None
        except Exception as e:
            print(f"Error getting broadcast: {e}")
            return None

    async def get_queue_size(self) -> int:
        """
        Get number of messages waiting in queue

        Returns:
            Number of messages in queue
        """
        if not self.connected:
            await self.connect()

        try:
            return await self.redis_client.llen(self.queue_name)
        except Exception:
            return 0

    async def clear_queue(self) -> bool:
        """
        Clear all messages from queue

        Returns:
            True if queue cleared successfully
        """
        if not self.connected:
            await self.connect()

        try:
            await self.redis_client.delete(self.queue_name)
            return True
        except Exception:
            return False

    async def _log_message_flow(self, message: Dict[str, Any], action: str):
        """
        Log message flow for debugging and monitoring

        Args:
            message: The message being logged
            action: Action taken (sent, received, etc)
        """
        try:
            # Store message flow in Redis for monitoring
            flow_key = f"siemless:flow:{datetime.utcnow().strftime('%Y%m%d')}"
            flow_entry = {
                'timestamp': datetime.utcnow().isoformat(),
                'action': action,
                'source': message.get('source'),
                'target': message.get('target'),
                'message_id': message.get('id')
            }

            await self.redis_client.rpush(
                flow_key,
                json.dumps(flow_entry)
            )

            # Expire after 7 days
            await self.redis_client.expire(flow_key, 604800)

        except Exception:
            # Don't fail on logging errors
            pass

    async def get_message_flow_stats(self) -> Dict[str, Any]:
        """
        Get statistics about message flow

        Returns:
            Statistics about messages sent/received
        """
        if not self.connected:
            await self.connect()

        try:
            # Get today's flow key
            flow_key = f"siemless:flow:{datetime.utcnow().strftime('%Y%m%d')}"

            # Get all flow entries
            flow_entries = await self.redis_client.lrange(flow_key, 0, -1)

            stats = {
                'total_messages': len(flow_entries),
                'messages_sent': 0,
                'messages_received': 0,
                'unique_sources': set(),
                'unique_targets': set()
            }

            for entry_str in flow_entries:
                entry = json.loads(entry_str)
                if entry['action'] == 'sent':
                    stats['messages_sent'] += 1
                elif entry['action'] == 'received':
                    stats['messages_received'] += 1

                if entry.get('source'):
                    stats['unique_sources'].add(entry['source'])
                if entry.get('target'):
                    stats['unique_targets'].add(entry['target'])

            # Convert sets to counts
            stats['unique_sources'] = len(stats['unique_sources'])
            stats['unique_targets'] = len(stats['unique_targets'])

            return stats

        except Exception as e:
            print(f"Error getting flow stats: {e}")
            return {}

    async def close(self):
        """Close connections and clean up"""
        if self.connected:
            if self.pubsub:
                await self.pubsub.unsubscribe()
                await self.pubsub.close()
            if self.redis_client:
                await self.redis_client.close()
            self.connected = False


class PriorityMessageQueue(MessageQueue):
    """
    Priority-based message queue for critical operations
    """

    async def send_priority_message(self, target_engine: str, data: Dict[str, Any],
                                   priority: int = 5) -> bool:
        """
        Send message with priority

        Args:
            target_engine: Name of the target engine
            data: Message data to send
            priority: Priority level (1=highest, 10=lowest)

        Returns:
            True if message sent successfully
        """
        if not self.connected:
            await self.connect()

        try:
            # Create message with priority
            message = {
                'id': str(uuid.uuid4()),
                'source': self.engine_name,
                'target': target_engine,
                'timestamp': datetime.utcnow().isoformat(),
                'priority': priority,
                'data': data
            }

            # Use sorted set for priority queue
            target_queue = f"siemless:priority:{target_engine}"
            await self.redis_client.zadd(
                target_queue,
                {json.dumps(message): priority}
            )

            return True

        except Exception as e:
            print(f"Error sending priority message: {e}")
            return False

    async def get_priority_message(self, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
        """
        Get highest priority message from queue

        Args:
            timeout: How long to wait for a message (seconds)

        Returns:
            Highest priority message or None
        """
        if not self.connected:
            await self.connect()

        try:
            priority_queue = f"siemless:priority:{self.engine_name}"

            # Get highest priority (lowest score) message
            messages = await self.redis_client.zrange(
                priority_queue, 0, 0, withscores=False
            )

            if messages:
                message_str = messages[0]
                # Remove from queue
                await self.redis_client.zrem(priority_queue, message_str)
                return json.loads(message_str)

            # Fall back to regular queue
            return await self.get_message(timeout)

        except Exception as e:
            print(f"Error getting priority message: {e}")
            return None