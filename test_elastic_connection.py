"""
Test Elastic Connection and Explore Data Structure
This will help us understand what data exists and build proper queries
"""

from elasticsearch import Elasticsearch
import os
from dotenv import load_dotenv
import json

load_dotenv()

# Get credentials
cloud_id = os.getenv('ELASTIC_CLOUD_ID')
api_key = os.getenv('ELASTIC_API_KEY')

print("="*80)
print("ELASTIC CONNECTION TEST")
print("="*80)

# Connect
print(f"\nConnecting to: {cloud_id.split(':')[0]}")
client = Elasticsearch(
    cloud_id=cloud_id,
    api_key=api_key,
    request_timeout=30
)

# Test connection
if client.ping():
    info = client.info()
    print(f"[OK] Connected to Elasticsearch {info['version']['number']}")
else:
    print("[FAIL] Connection failed")
    exit(1)

# Get indices
print("\n" + "="*80)
print("AVAILABLE INDICES")
print("="*80)
indices = client.cat.indices(format='json')
log_indices = [idx for idx in indices if 'log' in idx['index'] or 'file' in idx['index'] or 'win' in idx['index']]

print(f"\nFound {len(log_indices)} log-related indices:")
for idx in sorted(log_indices, key=lambda x: x['index'])[:10]:
    print(f"  - {idx['index']} ({idx['docs.count']} docs)")

# Pick the index with most data
if log_indices:
    main_index = max(log_indices, key=lambda x: int(x['docs.count']))
    print(f"\nUsing index with most data: {main_index['index']} ({main_index['docs.count']} docs)")

    # Get sample documents
    print("\n" + "="*80)
    print("SAMPLE DOCUMENTS")
    print("="*80)

    sample = client.search(
        index=main_index['index'],
        body={
            "size": 3,
            "query": {"match_all": {}}
        }
    )

    for i, hit in enumerate(sample['hits']['hits'], 1):
        print(f"\n--- Document {i} ---")
        source = hit['_source']

        # Show key fields
        print(f"Index: {hit['_index']}")
        print(f"ID: {hit['_id']}")

        # Extract useful fields
        if 'host' in source:
            print(f"Host: {source['host']}")
        if 'event' in source:
            print(f"Event: {source['event']}")
        if 'source' in source and isinstance(source['source'], dict):
            print(f"Source IP: {source['source'].get('ip', 'N/A')}")
        if 'destination' in source and isinstance(source['destination'], dict):
            print(f"Dest IP: {source['destination'].get('ip', 'N/A')}")
        if 'user' in source:
            print(f"User: {source['user']}")
        if 'process' in source:
            print(f"Process: {source['process']}")

        # Show top-level keys
        print(f"Available fields: {list(source.keys())[:20]}")

    # Get field mappings
    print("\n" + "="*80)
    print("COMMON ECS FIELDS IN DATA")
    print("="*80)

    # Check for common fields
    common_fields = ['host.name', 'host.ip', 'source.ip', 'destination.ip',
                     'user.name', 'process.name', 'event.action', '@timestamp']

    for field in common_fields:
        try:
            agg_query = {
                "size": 0,
                "aggs": {
                    "field_values": {
                        "terms": {"field": f"{field}.keyword" if '.' in field else f"{field}", "size": 3}
                    }
                }
            }
            result = client.search(index=main_index['index'], body=agg_query)

            if result['aggregations']['field_values']['buckets']:
                values = [b['key'] for b in result['aggregations']['field_values']['buckets']]
                print(f"  {field}: {values[:3]}")
        except:
            # Try without .keyword
            try:
                agg_query = {
                    "size": 0,
                    "aggs": {
                        "field_values": {
                            "terms": {"field": field, "size": 3}
                        }
                    }
                }
                result = client.search(index=main_index['index'], body=agg_query)
                if result['aggregations']['field_values']['buckets']:
                    values = [b['key'] for b in result['aggregations']['field_values']['buckets']]
                    print(f"  {field}: {values[:3]}")
            except:
                pass

print("\n" + "="*80)
print("TEST COMPLETE")
print("="*80)
