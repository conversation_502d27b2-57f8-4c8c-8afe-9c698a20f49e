"""
OpenCTI Integration Module
Connects to your OpenCTI instance to pull threat intelligence
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import os
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class OpenCTIIndicator:
    """Represents an indicator from OpenCTI"""
    id: str
    pattern: str
    pattern_type: str  # stix, yara, sigma, etc
    valid_from: datetime
    valid_until: Optional[datetime]
    confidence: int
    labels: List[str] = field(default_factory=list)
    kill_chain_phases: List[str] = field(default_factory=list)
    created_by: Optional[str] = None
    external_references: List[Dict] = field(default_factory=list)


class OpenCTIConnector:
    """
    Connects to your OpenCTI instance to retrieve threat intelligence
    """

    def __init__(self, url: str = None, api_key: str = None):
        # Try both OPENCTI_TOKEN and OPENCTI_API_KEY for compatibility
        self.url = url or os.getenv('OPENCTI_URL', 'http://localhost:8080')
        # Remove trailing slash to avoid double slashes in URLs
        self.url = self.url.rstrip('/')
        self.api_key = api_key or os.getenv('OPENCTI_TOKEN') or os.getenv('OPENCTI_API_KEY', '')

        # GraphQL endpoint
        self.graphql_url = f"{self.url}/graphql"

        # Headers for OpenCTI API
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        self.session = None

    async def connect(self) -> bool:
        """Test connection to OpenCTI"""
        try:
            async with aiohttp.ClientSession() as session:
                # Simple query to test connection
                query = """
                    query TestConnection {
                        about {
                            version
                        }
                    }
                """

                async with session.post(
                    self.graphql_url,
                    headers=self.headers,
                    json={'query': query}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        version = data.get('data', {}).get('about', {}).get('version', 'Unknown')
                        logger.info(f"Connected to OpenCTI version: {version}")
                        return True
                    else:
                        logger.error(f"Failed to connect to OpenCTI: {response.status}")
                        return False

        except Exception as e:
            logger.error(f"OpenCTI connection error: {e}")
            return False

    async def get_indicators(self, limit: int = 100, indicator_types: List[str] = None) -> List[Dict]:
        """
        Fetch indicators from OpenCTI

        Args:
            limit: Maximum number of indicators to fetch
            indicator_types: Filter by types (e.g., ['IPv4-Addr', 'Domain-Name', 'File'])

        Returns:
            List of indicators from OpenCTI
        """
        try:
            # Simplified GraphQL query for OpenCTI v6.0.0
            query = """
                query GetIndicators($first: Int!) {
                    indicators(first: $first) {
                        edges {
                            node {
                                id
                                pattern
                                name
                                confidence
                                valid_from
                                valid_until
                            }
                        }
                    }
                }
            """

            # Build filters if indicator types specified
            filters = None
            if indicator_types:
                filters = {
                    "mode": "and",
                    "filters": [
                        {
                            "key": "pattern_type",
                            "values": indicator_types,
                            "operator": "eq",
                            "mode": "or"
                        }
                    ]
                }

            variables = {
                "first": limit
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.graphql_url,
                    headers=self.headers,
                    json={'query': query, 'variables': variables}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        indicators = []

                        edges = data.get('data', {}).get('indicators', {}).get('edges', [])
                        for edge in edges:
                            node = edge.get('node', {})

                            # Determine pattern type from pattern
                            pattern = node.get('pattern', '')
                            pattern_type = 'stix'  # Default to STIX
                            if 'file:hashes' in pattern:
                                pattern_type = 'file-hash'
                            elif 'network-traffic' in pattern:
                                pattern_type = 'network'
                            elif 'domain-name:value' in pattern:
                                pattern_type = 'domain'

                            indicator = {
                                'id': node.get('id'),
                                'pattern': pattern,
                                'pattern_type': pattern_type,
                                'name': node.get('name'),
                                'description': node.get('name', ''),  # Use name as description if not provided
                                'valid_from': node.get('valid_from'),
                                'valid_until': node.get('valid_until'),
                                'confidence': node.get('confidence', 0),
                                'labels': [],  # Simplified for v6.0.0
                                'kill_chain_phases': []  # Simplified for v6.0.0
                            }
                            indicators.append(indicator)

                        logger.info(f"Retrieved {len(indicators)} indicators from OpenCTI")
                        return indicators
                    else:
                        logger.error(f"Failed to fetch indicators: {response.status}")
                        text = await response.text()
                        logger.error(f"Response: {text}")
                        return []

        except Exception as e:
            logger.error(f"Error fetching indicators: {e}")
            return []

    async def get_observable(self, observable_value: str, observable_type: str = None) -> Optional[Dict]:
        """
        Search for a specific observable in OpenCTI

        Args:
            observable_value: The value to search for (IP, domain, hash, etc)
            observable_type: Optional type filter

        Returns:
            Observable data if found
        """
        try:
            query = """
                query GetObservable($search: String!) {
                    stixCyberObservables(search: $search, first: 1) {
                        edges {
                            node {
                                id
                                entity_type
                                observable_value
                                created
                                modified
                                confidence
                                indicators {
                                    edges {
                                        node {
                                            pattern
                                            confidence
                                            valid_from
                                            valid_until
                                        }
                                    }
                                }
                                labels {
                                    edges {
                                        node {
                                            value
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            """

            variables = {
                "search": observable_value
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.graphql_url,
                    headers=self.headers,
                    json={'query': query, 'variables': variables}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        edges = data.get('data', {}).get('stixCyberObservables', {}).get('edges', [])

                        if edges:
                            return edges[0].get('node')
                        return None
                    else:
                        logger.error(f"Failed to search observable: {response.status}")
                        return None

        except Exception as e:
            logger.error(f"Error searching observable: {e}")
            return None

    async def get_reports(self, limit: int = 10) -> List[Dict]:
        """
        Fetch threat reports from OpenCTI
        """
        try:
            query = """
                query GetReports($first: Int!) {
                    reports(first: $first, orderBy: published, orderMode: desc) {
                        edges {
                            node {
                                id
                                name
                                description
                                published
                                confidence
                                report_types
                                labels {
                                    edges {
                                        node {
                                            value
                                        }
                                    }
                                }
                                objects {
                                    edges {
                                        node {
                                            ... on Indicator {
                                                pattern
                                                pattern_type
                                            }
                                            ... on AttackPattern {
                                                name
                                                x_mitre_id
                                            }
                                            ... on ThreatActor {
                                                name
                                                sophistication
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            """

            variables = {
                "first": limit
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.graphql_url,
                    headers=self.headers,
                    json={'query': query, 'variables': variables}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        reports = []

                        edges = data.get('data', {}).get('reports', {}).get('edges', [])
                        for edge in edges:
                            node = edge.get('node', {})
                            reports.append(node)

                        logger.info(f"Retrieved {len(reports)} reports from OpenCTI")
                        return reports
                    else:
                        logger.error(f"Failed to fetch reports: {response.status}")
                        return []

        except Exception as e:
            logger.error(f"Error fetching reports: {e}")
            return []

    async def get_attack_patterns(self) -> List[Dict]:
        """
        Fetch MITRE ATT&CK patterns from OpenCTI
        """
        try:
            query = """
                query GetAttackPatterns {
                    attackPatterns(first: 500) {
                        edges {
                            node {
                                id
                                name
                                description
                                x_mitre_id
                                x_mitre_detection
                                x_mitre_platforms
                                killChainPhases {
                                    edges {
                                        node {
                                            phase_name
                                            kill_chain_name
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            """

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.graphql_url,
                    headers=self.headers,
                    json={'query': query}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        patterns = []

                        edges = data.get('data', {}).get('attackPatterns', {}).get('edges', [])
                        for edge in edges:
                            patterns.append(edge.get('node'))

                        logger.info(f"Retrieved {len(patterns)} attack patterns from OpenCTI")
                        return patterns
                    else:
                        logger.error(f"Failed to fetch attack patterns: {response.status}")
                        return []

        except Exception as e:
            logger.error(f"Error fetching attack patterns: {e}")
            return []

    def convert_to_sigma(self, indicator: Dict) -> Optional[str]:
        """
        Convert OpenCTI indicator to Sigma rule

        Args:
            indicator: OpenCTI indicator

        Returns:
            Sigma rule YAML or None
        """
        pattern = indicator.get('pattern', '')
        pattern_type = indicator.get('pattern_type', '')

        # If already Sigma, return as-is
        if pattern_type == 'sigma':
            return pattern

        # Convert STIX pattern to Sigma
        if pattern_type == 'stix':
            return self._stix_to_sigma(pattern, indicator)

        return None

    def _stix_to_sigma(self, stix_pattern: str, indicator: Dict) -> str:
        """
        Convert STIX pattern to Sigma rule
        """
        # Basic conversion - this would be expanded for production
        sigma_rule = f"""
title: {indicator.get('name', 'OpenCTI Indicator')}
description: {indicator.get('description', 'Automatically generated from OpenCTI')}
status: experimental
author: OpenCTI Integration
date: {datetime.now().strftime('%Y/%m/%d')}
references:
    - OpenCTI ID: {indicator.get('id', 'unknown')}
tags:
"""

        # Add labels as tags
        for label in indicator.get('labels', []):
            sigma_rule += f"    - {label}\n"

        # Parse STIX pattern and convert to detection logic
        if '[file:hashes' in stix_pattern:
            # Extract hash from pattern (MD5, SHA1, SHA256)
            import re
            # Match hash value after = sign
            hash_match = re.search(r"=\s*'([a-fA-F0-9]{32,64})'", stix_pattern)
            if hash_match:
                hash_value = hash_match.group(1)
                sigma_rule += f"""
logsource:
    category: file_event
    product: windows
detection:
    selection:
        Hashes|contains: '{hash_value}'
    condition: selection
falsepositives:
    - Unknown
level: high
"""

        elif '[network-traffic:dst_ref.value' in stix_pattern:
            # Extract IP/domain from pattern
            import re
            value_match = re.search(r"'([^']+)'", stix_pattern)
            if value_match:
                ioc_value = value_match.group(1)
                sigma_rule += f"""
logsource:
    category: network_connection
    product: windows
detection:
    selection:
        DestinationIp: '{ioc_value}'
    condition: selection
falsepositives:
    - Legitimate connections to this destination
level: medium
"""

        else:
            # Generic pattern
            sigma_rule += f"""
logsource:
    category: process_creation
    product: windows
detection:
    selection:
        # STIX Pattern: {stix_pattern}
        # TODO: Manual conversion required
        CommandLine|contains: 'suspicious'
    condition: selection
falsepositives:
    - Unknown
level: medium
"""

        return sigma_rule


class OpenCTIRuleGenerator:
    """
    Generates detection rules from OpenCTI intelligence
    """

    def __init__(self, opencti_connector: OpenCTIConnector):
        self.opencti = opencti_connector
        self.generated_rules = []

    async def generate_rules_from_indicators(self, limit: int = 50) -> List[Dict]:
        """
        Generate detection rules from OpenCTI indicators
        """
        # Fetch indicators
        indicators = await self.opencti.get_indicators(limit=limit)

        rules = []
        for indicator in indicators:
            # Convert to detection rule
            rule = await self.create_detection_rule(indicator)
            if rule:
                rules.append(rule)

        logger.info(f"Generated {len(rules)} detection rules from OpenCTI")
        return rules

    async def create_detection_rule(self, indicator: Dict) -> Optional[Dict]:
        """
        Create a detection rule from an OpenCTI indicator
        """
        pattern = indicator.get('pattern', '')
        if not pattern:
            return None

        # Generate rule ID
        import hashlib
        rule_id = f"OPENCTI_{hashlib.md5(pattern.encode()).hexdigest()[:8]}"

        # Determine confidence based on OpenCTI confidence score
        opencti_confidence = indicator.get('confidence', 0)
        if opencti_confidence >= 80:
            rule_confidence = 0.9
        elif opencti_confidence >= 60:
            rule_confidence = 0.7
        elif opencti_confidence >= 40:
            rule_confidence = 0.5
        else:
            rule_confidence = 0.3

        # Create rule structure
        rule = {
            'rule_id': rule_id,
            'name': indicator.get('name', f'OpenCTI Rule {rule_id}'),
            'description': indicator.get('description', 'Generated from OpenCTI indicator'),
            'source': 'OpenCTI',
            'confidence': rule_confidence,
            'pattern': pattern,
            'pattern_type': indicator.get('pattern_type', 'stix'),
            'labels': indicator.get('labels', []),
            'kill_chain_phases': indicator.get('kill_chain_phases', []),
            'valid_from': indicator.get('valid_from'),
            'valid_until': indicator.get('valid_until'),
            'sigma_rule': self.opencti.convert_to_sigma(indicator),
            'created_at': datetime.now().isoformat()
        }

        return rule


async def test_opencti_connection():
    """Test the OpenCTI connection and fetch some data"""

    connector = OpenCTIConnector()

    # Test connection
    connected = await connector.connect()
    if not connected:
        logger.error("Failed to connect to OpenCTI")
        return

    print("Successfully connected to OpenCTI!")

    # Get some indicators
    print("\n[Fetching Indicators]")
    indicators = await connector.get_indicators(limit=5)
    for ind in indicators:
        print(f"  - {ind.get('name', 'Unknown')}: {ind.get('pattern', '')[:50]}...")

    # Get recent reports
    print("\n[Fetching Reports]")
    reports = await connector.get_reports(limit=3)
    for report in reports:
        print(f"  - {report.get('name', 'Unknown')}: {report.get('published', '')}")

    # Generate rules
    print("\n[Generating Rules]")
    generator = OpenCTIRuleGenerator(connector)
    rules = await generator.generate_rules_from_indicators(limit=5)
    for rule in rules:
        print(f"  - {rule['name']}: Confidence {rule['confidence']}")

    return rules


if __name__ == "__main__":
    # Test the OpenCTI integration
    asyncio.run(test_opencti_connection())