/**
 * Investigation Workspace - Active Investigations Dashboard
 */

.investigation-workspace {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f9fafb;
  overflow: hidden;
}

/* ===== Header ===== */
.workspace-header {
  background: #ffffff;
  border-bottom: 2px solid #e5e7eb;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #ffffff;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.workspace-title {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.workspace-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0 0 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  min-width: 300px;
  transition: all 0.2s;
}

.search-bar:focus-within {
  background: #ffffff;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.search-bar svg {
  color: #9ca3af;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  font-size: 14px;
  color: #111827;
}

.search-input::placeholder {
  color: #9ca3af;
}

.action-btn {
  padding: 8px;
  background: #ffffff;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== Filter Tabs ===== */
.filter-tabs {
  display: flex;
  background: #ffffff;
  border-bottom: 2px solid #e5e7eb;
  padding: 0 24px;
  gap: 4px;
  overflow-x: auto;
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: transparent;
  color: #6b7280;
  border: none;
  border-bottom: 3px solid transparent;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.filter-tab:hover {
  color: #111827;
  background: #f9fafb;
}

.filter-tab.active {
  color: #10b981;
  border-bottom-color: #10b981;
  background: #ecfdf5;
  font-weight: 600;
}

.tab-count {
  padding: 2px 8px;
  background: #e5e7eb;
  color: #374151;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 700;
}

.filter-tab.active .tab-count {
  background: #10b981;
  color: #ffffff;
}

.tab-count.critical {
  background: #fecaca;
  color: #dc2626;
}

.tab-count.breached {
  background: #fef2f2;
  color: #ef4444;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* ===== Investigation List ===== */
.investigation-list {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
  gap: 20px;
  align-content: start;
}

/* ===== Loading & Empty States ===== */
.loading-state,
.empty-state {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #9ca3af;
}

.loading-state svg,
.empty-state svg {
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* ===== Investigation Card ===== */
.investigation-card {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  border-left: 4px solid #d1d5db;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s;
}

.investigation-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.investigation-card.severity-critical {
  border-left-color: #dc2626;
}

.investigation-card.severity-high {
  border-left-color: #ea580c;
}

.investigation-card.severity-medium {
  border-left-color: #d97706;
}

.investigation-card.severity-low {
  border-left-color: #65a30d;
}

/* Card Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.header-left,
.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.severity-badge {
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 700;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.severity-badge.severity-critical {
  background: #dc2626;
}

.severity-badge.severity-high {
  background: #ea580c;
}

.severity-badge.severity-medium {
  background: #d97706;
}

.severity-badge.severity-low {
  background: #65a30d;
}

.sla-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.sla-indicator.sla-ok {
  color: #059669;
  background: #d1fae5;
}

.sla-indicator.sla-warning {
  color: #d97706;
  background: #fed7aa;
}

.sla-indicator.sla-breached {
  color: #dc2626;
  background: #fecaca;
  animation: pulse 2s ease-in-out infinite;
}

.assigned-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #eff6ff;
  color: #1d4ed8;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.take-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #10b981;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.take-btn:hover {
  background: #059669;
  transform: scale(1.05);
}

/* Card Body */
.card-body {
  padding: 16px 20px;
}

.investigation-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 4px 0;
  line-height: 1.4;
}

.investigation-id {
  font-size: 12px;
  font-family: 'Courier New', monospace;
  color: #6b7280;
  margin: 0 0 12px 0;
}

.investigation-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #6b7280;
}

.meta-item svg {
  color: #9ca3af;
}

/* Status Section */
.status-section {
  margin-bottom: 16px;
}

.status-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.status-icon-new { color: #6b7280; }
.status-icon-enriching { color: #3b82f6; }
.status-icon-correlating { color: #8b5cf6; }
.status-icon-analyzing { color: #f59e0b; }
.status-icon-complete { color: #10b981; }

.progress-bar {
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 11px;
  font-weight: 600;
  color: #6b7280;
}

/* Enrichment Status */
.enrichment-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
}

.status-label-small {
  font-weight: 500;
  color: #6b7280;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
}

.status-badge.completed {
  background: #d1fae5;
  color: #059669;
}

.status-badge.in_progress {
  background: #dbeafe;
  color: #1d4ed8;
}

.status-badge.pending {
  background: #f3f4f6;
  color: #6b7280;
}

.status-badge.failed {
  background: #fecaca;
  color: #dc2626;
}

/* Card Footer */
.card-footer {
  padding: 12px 20px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.open-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  background: #ffffff;
  color: #10b981;
  border: 1px solid #10b981;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.open-btn:hover {
  background: #10b981;
  color: #ffffff;
  transform: translateX(2px);
}

.open-btn svg:last-child {
  transition: transform 0.2s;
}

.open-btn:hover svg:last-child {
  transform: translateX(4px);
}

/* ===== Responsive Design ===== */
@media (max-width: 1400px) {
  .investigation-list {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  }
}

@media (max-width: 1024px) {
  .workspace-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .search-bar {
    flex: 1;
    min-width: 0;
  }

  .investigation-list {
    grid-template-columns: 1fr;
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .workspace-header {
    padding: 16px;
  }

  .workspace-title {
    font-size: 20px;
  }

  .filter-tabs {
    padding: 0 16px;
  }

  .filter-tab {
    padding: 10px 12px;
    font-size: 13px;
  }

  .filter-tab svg {
    display: none;
  }

  .investigation-list {
    padding: 12px;
    gap: 12px;
  }

  .investigation-card {
    border-left-width: 3px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .header-right {
    width: 100%;
  }

  .take-btn,
  .assigned-badge {
    width: 100%;
    justify-content: center;
  }

  .investigation-meta {
    flex-direction: column;
    gap: 8px;
  }

  .enrichment-status {
    padding: 10px;
  }
}

/* ===== Dark Mode Support ===== */
@media (prefers-color-scheme: dark) {
  .investigation-workspace {
    background: #111827;
  }

  .workspace-header {
    background: #1f2937;
    border-bottom-color: #374151;
  }

  .workspace-title {
    color: #f9fafb;
  }

  .workspace-subtitle {
    color: #9ca3af;
  }

  .search-bar {
    background: #374151;
    border-color: #4b5563;
  }

  .search-input {
    color: #f9fafb;
  }

  .action-btn {
    background: #374151;
    color: #f9fafb;
    border-color: #4b5563;
  }

  .filter-tabs {
    background: #1f2937;
    border-bottom-color: #374151;
  }

  .filter-tab {
    color: #9ca3af;
  }

  .filter-tab:hover {
    color: #f9fafb;
    background: #374151;
  }

  .filter-tab.active {
    color: #34d399;
    background: #064e3b;
  }

  .investigation-card {
    background: #1f2937;
    border-color: #374151;
  }

  .card-header,
  .card-footer {
    background: #111827;
    border-color: #374151;
  }

  .investigation-title {
    color: #f9fafb;
  }

  .investigation-id {
    color: #9ca3af;
  }

  .enrichment-status {
    background: #111827;
  }

  .open-btn {
    background: #374151;
    color: #34d399;
    border-color: #34d399;
  }

  .open-btn:hover {
    background: #10b981;
  }
}

/* ===== Print Styles ===== */
@media print {
  .workspace-header,
  .filter-tabs,
  .card-footer {
    display: none;
  }

  .investigation-list {
    grid-template-columns: 1fr;
  }

  .investigation-card {
    page-break-inside: avoid;
    margin-bottom: 20px;
  }
}
