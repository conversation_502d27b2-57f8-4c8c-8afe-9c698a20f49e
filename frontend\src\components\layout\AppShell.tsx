import React, { useState, useEffect } from 'react'
import { Outlet } from 'react-router-dom'
import { useAuthStore } from '../../stores/authStore'
import { useNavigationStore, useLayoutStore } from '../../stores/navigationStore'
import TopBar from './TopBar'
import SideBar from './SideBar'
import RightDrawer from './RightDrawer'
import BottomPanel from './BottomPanel'
import clsx from 'clsx'

const AppShell: React.FC = () => {
  const { user } = useAuthStore()
  const { sidebarExpanded } = useNavigationStore()
  const { rightDrawerOpen, bottomPanelOpen } = useLayoutStore()

  // Track window size for responsive behavior
  const [windowWidth, setWindowWidth] = useState(window.innerWidth)

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth)
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const isMobile = windowWidth < 640
  const isTablet = windowWidth < 1024
  const isDesktop = windowWidth >= 1280

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Static Top Bar - NOT sticky/fixed */}
      <TopBar />

      {/* Main Layout Container */}
      <div className="flex flex-1 overflow-hidden">
        {/* Collapsible Sidebar */}
        {!isMobile && (
          <SideBar
            expanded={sidebarExpanded}
            user={user}
          />
        )}

        {/* Main Content Area */}
        <main
          className={clsx(
            'flex-1 flex flex-col overflow-hidden transition-all duration-200',
            {
              'ml-60': !isMobile && sidebarExpanded,
              'ml-16': !isMobile && !sidebarExpanded,
              'mr-80': rightDrawerOpen && isDesktop
            }
          )}
        >
          {/* Content Router Outlet */}
          <div className="flex-1 overflow-auto">
            <Outlet />
          </div>

          {/* Bottom Panel */}
          {bottomPanelOpen && <BottomPanel />}
        </main>

        {/* Right Drawer */}
        {rightDrawerOpen && isDesktop && <RightDrawer />}
      </div>

      {/* Mobile Navigation Overlay */}
      {isMobile && sidebarExpanded && (
        <div className="fixed inset-0 z-40 bg-black bg-opacity-50" />
      )}
    </div>
  )
}

export default AppShell