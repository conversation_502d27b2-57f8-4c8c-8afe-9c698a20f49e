# Vendor Plugin Comparison: CrowdStrike vs Elastic
**How Different Data Formats Are Handled Through Context Plugins**

## Executive Summary

**Problem**: Different security vendors use different data formats
- CrowdStrike uses **vendor-specific JSON** (device_id, local_ip, agent_version)
- Elastic uses **ECS (Elastic Common Schema)** (host.name, host.ip, agent.version)
- Both need to output **standardized ContextResult** format

**Solution**: Context plugins translate vendor formats → standard format → entity extraction

## Plugin Architecture Comparison

### CrowdStrike Plugin
**File**: `engines/ingestion/crowdstrike_context_plugin.py` (300 lines)
**SDK**: FalconPy (official CrowdStrike Python SDK)
**Data Model**: Vendor-specific API responses

#### Input Format (CrowdStrike API):
```json
{
  "device_id": "abc123",
  "hostname": "010117039050LN1",
  "local_ip": "*************",
  "external_ip": "************",
  "mac_address": "00:1B:44:11:3A:B7",
  "os_version": "Windows 10",
  "platform_name": "Windows",
  "agent_version": "7.08.16207.0",
  "status": "normal",
  "last_seen": "2025-10-02T12:00:00Z"
}
```

#### Translation to Standard Format:
```python
def _format_host_result(self, host: Dict) -> ContextResult:
    return ContextResult(
        source_name='crowdstrike',
        category=ContextCategory.ASSET,
        confidence=0.95,
        data={
            'device_id': host.get('device_id'),        # CrowdStrike field
            'hostname': host.get('hostname'),           # CrowdStrike field
            'local_ip': host.get('local_ip'),          # CrowdStrike field
            'mac_address': host.get('mac_address'),    # CrowdStrike field
            'os_version': host.get('os_version'),      # CrowdStrike field
            'status': host.get('status')               # CrowdStrike field
        },
        timestamp=host.get('last_seen'),
        metadata={'raw': host}
    )
```

### Elastic Plugin
**File**: `engines/ingestion/elastic_context_plugin.py` (500 lines)
**SDK**: elasticsearch-py (official Elastic Python SDK)
**Data Model**: ECS (Elastic Common Schema) - normalized logs from multiple vendors

#### Input Format (Elastic ECS):
```json
{
  "@timestamp": "2025-10-02T12:00:00Z",
  "host": {
    "name": "server-01",
    "ip": ["*************", "********"],
    "mac": ["00:1B:44:11:3A:B7"],
    "os": {
      "full": "Windows 10 Pro",
      "version": "10.0.19044"
    }
  },
  "user": {
    "name": "admin"
  },
  "process": {
    "name": "powershell.exe"
  }
}
```

#### Translation to Standard Format:
```python
def _format_asset_result(self, host_data: Dict) -> ContextResult:
    return ContextResult(
        source_name='elastic',
        category=ContextCategory.ASSET,
        confidence=0.90,
        data={
            'hostname': host_data.get('hostname'),              # From host.name
            'local_ip': host_data.get('ips', [None])[0],       # From host.ip[]
            'all_ips': host_data.get('ips', []),               # All IPs
            'mac_addresses': host_data.get('mac_addresses'),   # From host.mac[]
            'os_version': host_data.get('os'),                 # From host.os.full
            'users': host_data.get('users', []),               # Aggregated
            'processes': host_data.get('processes', [])        # Aggregated
        },
        timestamp=host_data.get('last_seen'),
        metadata={'raw': host_data, 'source_type': 'aggregated_logs'}
    )
```

## Key Differences

### 1. Data Source Type

**CrowdStrike**:
- Direct API calls to CrowdStrike Falcon platform
- Real-time device inventory
- Vendor-specific detections

**Elastic**:
- Queries ECS-normalized logs already in Elasticsearch
- Aggregated data from multiple sources (Windows, Linux, network devices)
- Vendor-agnostic schema

### 2. Query Patterns

**CrowdStrike** (Direct API):
```python
# Query devices with FQL filter
fql_filter = f"hostname:'{query.query_value}'"
response = self.hosts_client.query_devices_by_filter(
    filter=fql_filter,
    limit=50
)
device_ids = response['body']['resources']
details = self.hosts_client.get_device_details(ids=device_ids)
```

**Elastic** (Elasticsearch DSL):
```python
# Query with Elasticsearch query DSL
es_query = {
    "query": {
        "bool": {
            "must": [{"match": {"host.name": query.query_value}}],
            "filter": [{
                "range": {
                    "@timestamp": {
                        "gte": "2025-09-25T00:00:00Z",
                        "lte": "2025-10-02T23:59:59Z"
                    }
                }
            }]
        }
    }
}
response = self.client.search(index=['logs-*'], body=es_query)
```

### 3. Data Aggregation

**CrowdStrike**:
- Returns complete device object
- No aggregation needed
- Single authoritative source

**Elastic**:
- Aggregates data from multiple log entries
- Builds host profile over time
- Multiple sources (filebeat, winlogbeat, packetbeat, etc.)

```python
# Elastic aggregation example
def _aggregate_host_data(self, hits: List[Dict]) -> Dict:
    hosts = {}
    for hit in hits:
        hostname = hit['_source']['host']['name']
        if hostname not in hosts:
            hosts[hostname] = {
                'ips': set(),
                'users': set(),
                'processes': set(),
                'log_count': 0
            }
        # Aggregate IPs, users, processes from each log
        hosts[hostname]['log_count'] += 1
        # ...
    return hosts
```

### 4. Supported Categories

**CrowdStrike Plugin**:
```python
def get_supported_categories(self) -> List[ContextCategory]:
    return [
        ContextCategory.ASSET,      # HOSTS_READ
        ContextCategory.DETECTION,  # DETECTIONS_READ
        ContextCategory.INCIDENT,   # INCIDENTS_READ
    ]
```

**Elastic Plugin**:
```python
def get_supported_categories(self) -> List[ContextCategory]:
    return [
        ContextCategory.ASSET,      # Aggregated host data
        ContextCategory.DETECTION,  # Security alerts
        ContextCategory.LOG,        # Raw ECS logs
        ContextCategory.NETWORK,    # Network flows
    ]
```

### 5. Authentication Methods

**CrowdStrike**:
```python
# OAuth2 with client credentials (handled by FalconPy)
self.hosts_client = Hosts(
    client_id=self.client_id,
    client_secret=self.client_secret,
    base_url=self.base_url
)
```

**Elastic**:
```python
# API Key or Basic Auth
if self.api_key:
    self.client = Elasticsearch(
        [self.elastic_url],
        api_key=self.api_key
    )
else:
    self.client = Elasticsearch(
        [self.elastic_url],
        basic_auth=(self.username, self.password)
    )
```

## Output Format (Both Produce Same Result)

Both plugins output standardized **ContextResult**:

```python
@dataclass
class ContextResult:
    source_name: str         # 'crowdstrike' or 'elastic'
    category: ContextCategory
    confidence: float        # 0.0 - 1.0
    data: Dict[str, Any]     # Standardized fields
    timestamp: str
    metadata: Dict[str, Any] = None
```

### Example Output (CrowdStrike):
```python
ContextResult(
    source_name='crowdstrike',
    category=ContextCategory.ASSET,
    confidence=0.95,
    data={
        'hostname': '010117039050LN1',
        'local_ip': '*************',
        'mac_address': '00:1B:44:11:3A:B7',
        'os_version': 'Windows 10'
    },
    timestamp='2025-10-02T12:00:00Z'
)
```

### Example Output (Elastic):
```python
ContextResult(
    source_name='elastic',
    category=ContextCategory.ASSET,
    confidence=0.90,
    data={
        'hostname': 'server-01',
        'local_ip': '*************',
        'all_ips': ['*************', '********'],
        'mac_addresses': ['00:1B:44:11:3A:B7'],
        'os_version': 'Windows 10 Pro',
        'users': ['admin', 'service_account'],
        'processes': ['powershell.exe', 'chrome.exe']
    },
    timestamp='2025-10-02T12:00:00Z'
)
```

## Entity Extraction (Same for Both)

Once both plugins output **ContextResult**, the Contextualization Engine extracts entities using the **same hardcoded field mappings**:

```python
# From entity_extractor.py
ENTITY_FIELD_MAPPINGS = {
    'hostname': [
        'hostname',          # ✅ Both plugins provide this
        'host.name',
        'device.hostname'
    ],
    'ip_address': [
        'local_ip',          # ✅ Both plugins provide this
        'all_ips',           # ✅ Elastic provides this
        'source.ip',
        'device.local_ip'
    ],
    'mac_address': [
        'mac_address',       # ✅ CrowdStrike provides this
        'mac_addresses',     # ✅ Elastic provides this
        'device.mac'
    ]
}
```

**Key Point**: The hardcoded mappings check for **multiple field names** to handle different vendor outputs.

## Complete Data Flow

### CrowdStrike Flow:
```
1. Query: hostname="010117039050LN1"
2. CrowdStrike API → FalconPy SDK → Plugin
3. Plugin formats → ContextResult with 'hostname', 'local_ip', 'mac_address'
4. Contextualization Engine checks ENTITY_FIELD_MAPPINGS
5. Finds 'hostname' in data → extracts as hostname entity
6. Finds 'local_ip' in data → extracts as ip_address entity
7. Finds 'mac_address' in data → extracts as mac_address entity
8. Stores entities in PostgreSQL
```

### Elastic Flow:
```
1. Query: hostname="server-01"
2. Elasticsearch API → elasticsearch-py → Plugin
3. Plugin aggregates 100 logs → builds host profile
4. Plugin formats → ContextResult with 'hostname', 'all_ips', 'mac_addresses'
5. Contextualization Engine checks ENTITY_FIELD_MAPPINGS
6. Finds 'hostname' in data → extracts as hostname entity
7. Finds 'all_ips' in data → extracts multiple ip_address entities
8. Finds 'mac_addresses' in data → extracts multiple mac_address entities
9. Stores entities in PostgreSQL
```

## Benefits of Plugin Architecture

### 1. Vendor Independence
- Add new vendors without modifying core engine
- Each plugin handles vendor-specific quirks
- Standardized output ensures consistent processing

### 2. Format Flexibility
- CrowdStrike: Direct API responses
- Elastic: ECS-normalized logs
- Future: Any format can be translated

### 3. Easy Maintenance
- Update CrowdStrike API? → Update crowdstrike_context_plugin.py only
- Update Elastic schema? → Update elastic_context_plugin.py only
- Core extraction logic unchanged

### 4. Scalability
- Add SentinelOne → 1 new file
- Add Active Directory → 1 new file
- Add Palo Alto → 1 new file

## Configuration

### CrowdStrike (.env):
```bash
CROWDSTRIKE_CLIENT_ID=your_client_id
CROWDSTRIKE_CLIENT_SECRET=your_client_secret
CROWDSTRIKE_BASE_URL=https://api.us-2.crowdstrike.com
```

### Elastic (.env):
```bash
ELASTIC_URL=https://your-elastic.cloud:9200
ELASTIC_API_KEY=your_api_key
# OR
ELASTIC_USERNAME=elastic
ELASTIC_PASSWORD=your_password
ELASTIC_VERIFY_CERTS=true
```

## Testing Both Plugins

### Test CrowdStrike:
```bash
docker-compose exec -T redis redis-cli PUBLISH 'ingestion.pull_context' '{
  "request_id": "test-cs-001",
  "query_type": "hostname",
  "query_value": "010117039050LN1",
  "categories": ["asset"]
}'
```

### Test Elastic:
```bash
docker-compose exec -T redis redis-cli PUBLISH 'ingestion.pull_context' '{
  "request_id": "test-elastic-001",
  "query_type": "hostname",
  "query_value": "server-01",
  "categories": ["asset", "log"]
}'
```

### Expected Output (Both):
```
ingestion | [test-xxx-001] Got 1 results from crowdstrike/elastic
ingestion | [test-xxx-001] Sending 1 results to Contextualization
contextualization | [test-xxx-001] Extracted entities: hostname, IP, MAC
contextualization | [test-xxx-001] Created relationships
```

## Summary

**The Answer to "What if the format is different?"**

1. **Create a Context Plugin** - Handles vendor-specific format
2. **Translate to ContextResult** - Standardized output format
3. **Let Contextualization Extract** - Uses existing field mappings
4. **No Core Modification Needed** - Plugin system handles it all

**Currently Implemented:**
- ✅ CrowdStrike Plugin (vendor-specific API)
- ✅ Elastic Plugin (ECS-normalized logs)

**Can Be Added (~2-3 hours each):**
- SentinelOne
- Microsoft Sentinel
- Palo Alto Cortex XDR
- Active Directory
- Tenable
- Qualys
- Splunk
- QRadar
- *Any security vendor with an API*

The plugin architecture ensures **infinite scalability** while maintaining **consistent entity extraction** regardless of vendor data format.
