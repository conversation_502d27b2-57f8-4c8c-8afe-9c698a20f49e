#!/usr/bin/env python3
"""
SIEMLess v2.0 - Enhanced Ruleset Engine
Generates detection rules in multiple formats (Sigma, YARA, KQL, SPL)
+ CTI Integration and Rule Lifecycle Management
"""

import asyncio
import json
import logging
import sys
import os
import redis
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
import uuid
from dataclasses import dataclass, asdict, field
from enum import Enum

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared.base import BaseEngine
from shared.logging import log_everything

@dataclass
class RuleTemplate:
    """Template for different rule types"""
    rule_type: str
    format: str
    template: str
    required_fields: List[str]
    mitre_tactics: List[str]
    severity: str

@dataclass
class GeneratedRule:
    """Generated detection rule"""
    rule_id: str
    name: str
    description: str
    rule_type: str
    format: str
    content: str
    mitre_tactics: List[str]
    mitre_techniques: List[str]
    severity: str
    confidence: float
    created_at: datetime
    metadata: Dict[str, Any]

# === CTI and Lifecycle Management Data Classes ===

class RuleStatus(Enum):
    ACTIVE = "active"
    TESTING = "testing"
    DEPRECATED = "deprecated"
    UNDER_REVIEW = "under_review"
    NEEDS_UPDATE = "needs_update"

class ThreatContext(Enum):
    APT = "advanced_persistent_threat"
    MALWARE = "malware_family"
    TECHNIQUE = "mitre_technique"
    CAMPAIGN = "threat_campaign"
    VULNERABILITY = "vulnerability_exploit"

class UpdateTrigger(Enum):
    NEW_CTI = "new_cti_intelligence"
    PERFORMANCE_DECLINE = "performance_decline"
    FALSE_POSITIVE_SPIKE = "false_positive_spike"
    THREAT_EVOLUTION = "threat_evolution"
    MANUAL_REQUEST = "manual_request"
    SCHEDULED_REVIEW = "scheduled_review"

@dataclass
class ThreatIntelligence:
    """CTI data point for rule context"""
    source: str
    ioc_type: str
    ioc_value: str
    threat_context: ThreatContext
    confidence_score: float
    first_seen: datetime
    last_seen: datetime
    mitre_tactics: List[str] = field(default_factory=list)
    mitre_techniques: List[str] = field(default_factory=list)
    kill_chain_phase: Optional[str] = None
    threat_actor: Optional[str] = None
    malware_family: Optional[str] = None
    campaign_name: Optional[str] = None
    raw_intelligence: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RulePerformanceMetrics:
    """Rule effectiveness and performance tracking"""
    rule_id: str
    true_positives: int = 0
    false_positives: int = 0
    false_negatives: int = 0
    total_alerts: int = 0
    avg_response_time: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0
    last_triggered: Optional[datetime] = None
    performance_trend: str = "stable"  # improving, declining, stable
    analyst_feedback_score: float = 0.0
    coverage_effectiveness: float = 0.0

@dataclass
class RuleUpdate:
    """Rule update/modification record"""
    update_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    rule_id: str = ""
    update_type: str = ""  # threshold_adjustment, logic_enhancement, ioc_update, context_expansion
    trigger: UpdateTrigger = UpdateTrigger.MANUAL_REQUEST
    original_rule: str = ""
    updated_rule: str = ""
    change_description: str = ""
    impact_assessment: str = ""
    validation_results: Dict[str, Any] = field(default_factory=dict)
    applied_date: Optional[datetime] = None
    rollback_available: bool = True
    rollback_reason: Optional[str] = None
    created_date: datetime = field(default_factory=datetime.now)
    created_by: str = "enhanced_ruleset_engine"

@dataclass
class RuleImprovementSuggestion:
    """AI-generated rule improvement recommendations"""
    suggestion_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    rule_id: str = ""
    suggestion_type: str = ""  # performance_optimization, false_positive_reduction, coverage_expansion
    description: str = ""
    proposed_changes: str = ""
    confidence_score: float = 0.0
    expected_impact: str = ""
    risk_assessment: str = ""
    supporting_evidence: List[str] = field(default_factory=list)
    implementation_priority: str = "medium"  # low, medium, high, critical
    estimated_effort: str = "medium"  # low, medium, high
    created_date: datetime = field(default_factory=datetime.now)
    status: str = "pending"  # pending, approved, rejected, implemented

@dataclass
class RuleTestCase:
    """Test case for rule validation"""
    test_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    rule_id: str = ""
    test_name: str = ""
    test_type: str = ""  # positive_match, negative_match, edge_case, performance
    test_data: str = ""
    expected_result: bool = True
    actual_result: Optional[bool] = None
    test_status: str = "pending"  # pending, passed, failed, skipped
    execution_time: Optional[float] = None
    error_message: Optional[str] = None
    created_date: datetime = field(default_factory=datetime.now)
    last_run: Optional[datetime] = None

@dataclass
class EnhancedRule(GeneratedRule):
    """Enhanced rule with lifecycle management"""
    status: RuleStatus = RuleStatus.TESTING
    version: str = "1.0"
    parent_rule_id: Optional[str] = None
    last_updated: datetime = field(default_factory=datetime.now)
    next_review_date: datetime = field(default_factory=lambda: datetime.now() + timedelta(days=90))

    # CTI Integration
    source_cti: List[ThreatIntelligence] = field(default_factory=list)
    threat_context: ThreatContext = ThreatContext.TECHNIQUE
    threat_actors: List[str] = field(default_factory=list)
    malware_families: List[str] = field(default_factory=list)

    # Performance and Lifecycle
    performance_metrics: RulePerformanceMetrics = field(default_factory=lambda: RulePerformanceMetrics(""))
    update_history: List[RuleUpdate] = field(default_factory=list)

    # Deployment and Testing
    deployed_platforms: List[str] = field(default_factory=list)
    deployment_config: Dict[str, Any] = field(default_factory=dict)
    validation_tests: List[str] = field(default_factory=list)
    quality_score: float = 0.0
    analyst_notes: str = ""
    tags: List[str] = field(default_factory=list)

class RulesetEngine(BaseEngine):
    """
    Enhanced Ruleset Engine - Complete Rule Lifecycle Management

    Core Responsibilities:
    - Generate Sigma detection rules
    - Create YARA file/memory patterns
    - Build KQL queries for Sentinel
    - Generate SPL queries for Splunk
    - Create custom detection logic
    - Rule validation and testing
    - MITRE ATT&CK mapping

    NEW Lifecycle Management:
    - CTI integration and rule generation
    - Performance tracking and analytics
    - AI-powered improvement suggestions
    - Rule testing and validation framework
    - SIEM platform deployment management
    - Automated rule updates and maintenance
    """

    def __init__(self, redis_client: Optional[redis.Redis] = None):
        super().__init__('ruleset', '2.0.0')
        self.redis_client = redis_client or redis.Redis(host='localhost', port=6379, db=0)

        # Existing rule generation
        self.rule_templates = {}
        self.generated_rules = {}
        self.mitre_mappings = {}

        # NEW: Lifecycle management
        self.enhanced_rules: Dict[str, EnhancedRule] = {}
        self.cti_sources: Dict[str, Any] = {}
        self.improvement_suggestions: Dict[str, RuleImprovementSuggestion] = {}
        self.test_cases: Dict[str, List[RuleTestCase]] = {}

        # Performance tracking
        self.performance_thresholds = {
            'min_precision': 0.8,
            'min_recall': 0.7,
            'max_false_positive_rate': 0.1,
            'max_response_time': 5.0
        }

        self.load_rule_templates()
        self.load_mitre_mappings()

        self.logger.info("Enhanced Ruleset Engine initialized with lifecycle management")

    def load_rule_templates(self):
        """Load rule templates for different formats"""
        self.rule_templates = {
            'sigma_process_creation': RuleTemplate(
                rule_type='process_creation',
                format='sigma',
                template='''title: {title}
id: {rule_id}
description: {description}
status: experimental
author: SIEMLess v2.0
date: {date}
tags:
{tags}
logsource:
    category: process_creation
    product: {product}
detection:
    selection:
{selection}
    condition: selection
fields:
{fields}
falsepositives:
{falsepositives}
level: {level}''',
                required_fields=['process_name', 'command_line'],
                mitre_tactics=['execution', 'persistence', 'privilege_escalation'],
                severity='medium'
            ),

            'sigma_network_connection': RuleTemplate(
                rule_type='network_connection',
                format='sigma',
                template='''title: {title}
id: {rule_id}
description: {description}
status: experimental
author: SIEMLess v2.0
date: {date}
tags:
{tags}
logsource:
    category: network_connection
    product: {product}
detection:
    selection:
{selection}
    condition: selection
fields:
{fields}
falsepositives:
{falsepositives}
level: {level}''',
                required_fields=['destination_ip', 'destination_port'],
                mitre_tactics=['command_and_control', 'exfiltration'],
                severity='high'
            ),

            'yara_file': RuleTemplate(
                rule_type='file_detection',
                format='yara',
                template='''rule {rule_name}
{{
    meta:
        description = "{description}"
        author = "SIEMLess v2.0"
        date = "{date}"
        severity = "{severity}"
        mitre_tactics = "{mitre_tactics}"
        mitre_techniques = "{mitre_techniques}"

    strings:
{strings}

    condition:
{condition}
}}''',
                required_fields=['file_strings', 'file_condition'],
                mitre_tactics=['persistence', 'defense_evasion'],
                severity='high'
            ),

            'kql_sentinel': RuleTemplate(
                rule_type='log_analytics',
                format='kql',
                template='''// {title}
// Description: {description}
// MITRE: {mitre_techniques}
// Severity: {severity}
// Created: {date}

{table_name}
| where TimeGenerated >= ago({timeframe})
{filters}
| extend
{extensions}
| project
{projections}
| where {conditions}
| summarize
{aggregations}
''',
                required_fields=['table_name', 'filters', 'conditions'],
                mitre_tactics=['initial_access', 'execution', 'persistence'],
                severity='medium'
            ),

            'spl_splunk': RuleTemplate(
                rule_type='search_analytics',
                format='spl',
                template='''search index={index} earliest=-{timeframe}
{base_search}
| eval {evaluations}
| where {conditions}
| stats {statistics}
| eval severity="{severity}"
| eval mitre_tactics="{mitre_tactics}"
| eval mitre_techniques="{mitre_techniques}"
| eval description="{description}"''',
                required_fields=['index', 'base_search', 'conditions'],
                mitre_tactics=['initial_access', 'execution', 'persistence'],
                severity='medium'
            )
        }

    def load_mitre_mappings(self):
        """Load MITRE ATT&CK technique mappings"""
        self.mitre_mappings = {
            'T1055': {
                'name': 'Process Injection',
                'tactic': 'defense_evasion',
                'description': 'Inject code into processes'
            },
            'T1003': {
                'name': 'OS Credential Dumping',
                'tactic': 'credential_access',
                'description': 'Dump credentials from system'
            },
            'T1059.001': {
                'name': 'PowerShell',
                'tactic': 'execution',
                'description': 'PowerShell execution'
            },
            'T1059.003': {
                'name': 'Windows Command Shell',
                'tactic': 'execution',
                'description': 'Command shell execution'
            },
            'T1071.001': {
                'name': 'Web Protocols',
                'tactic': 'command_and_control',
                'description': 'C2 via web protocols'
            },
            'T1547.001': {
                'name': 'Registry Run Keys',
                'tactic': 'persistence',
                'description': 'Registry autostart execution'
            },
            'T1190': {
                'name': 'Exploit Public-Facing Application',
                'tactic': 'initial_access',
                'description': 'Exploit web applications'
            },
            'T1082': {
                'name': 'System Information Discovery',
                'tactic': 'discovery',
                'description': 'Gather system information'
            }
        }

    async def start(self):
        """Start the Ruleset Engine"""
        await super().start()
        self.log(f"🎯 Ruleset Engine v{self.version} started")
        self.log("📋 Available rule formats: Sigma, YARA, KQL, SPL")

        # Subscribe to relevant queues
        await self.subscribe_to_queue('rule_generation_requests')
        await self.subscribe_to_queue('pattern_crystallization')
        await self.subscribe_to_queue('mitre_mapping_requests')

        # Start processing loop
        await self.process_rule_requests()

    async def process_rule_requests(self):
        """Main processing loop for rule generation requests"""
        while True:
            try:
                # Check for rule generation requests
                messages = await self.get_queue_messages('rule_generation_requests')

                for message in messages:
                    await self.handle_rule_generation_request(message)

                # Check for pattern crystallization
                pattern_messages = await self.get_queue_messages('pattern_crystallization')

                for message in pattern_messages:
                    await self.handle_pattern_crystallization(message)

                await asyncio.sleep(1)

            except Exception as e:
                self.log(f"❌ Error in rule processing: {e}", level='error')
                await asyncio.sleep(5)

    async def handle_rule_generation_request(self, message: Dict[str, Any]):
        """Handle incoming rule generation requests"""
        try:
            request_id = message.get('request_id', str(uuid.uuid4()))
            rule_type = message.get('rule_type', 'sigma_process_creation')
            detection_logic = message.get('detection_logic', {})
            context_data = message.get('context_data', {})
            enrichment_data = message.get('enrichment_data', {})

            self.log(f"🎯 Generating {rule_type} rule for request {request_id}")

            # Generate rule based on type
            if rule_type.startswith('sigma'):
                rule = await self.generate_sigma_rule(detection_logic, context_data, enrichment_data)
            elif rule_type == 'yara_file':
                rule = await self.generate_yara_rule(detection_logic, context_data, enrichment_data)
            elif rule_type == 'kql_sentinel':
                rule = await self.generate_kql_rule(detection_logic, context_data, enrichment_data)
            elif rule_type == 'spl_splunk':
                rule = await self.generate_spl_rule(detection_logic, context_data, enrichment_data)
            else:
                rule = await self.generate_custom_rule(rule_type, detection_logic, context_data, enrichment_data)

            # Store generated rule
            self.generated_rules[rule.rule_id] = rule

            # Send to Query Engine for SIEM translation
            await self.send_to_queue('query_engine_requests', {
                'request_id': request_id,
                'rule_id': rule.rule_id,
                'rule_format': rule.format,
                'rule_content': rule.content,
                'target_systems': message.get('target_systems', ['splunk', 'sentinel', 'qradar']),
                'context_data': context_data,
                'enrichment_data': enrichment_data
            })

            # Log rule generation
            await log_everything(
                component='ruleset_engine',
                action='rule_generated',
                details={
                    'request_id': request_id,
                    'rule_id': rule.rule_id,
                    'rule_type': rule_type,
                    'format': rule.format,
                    'mitre_tactics': rule.mitre_tactics,
                    'mitre_techniques': rule.mitre_techniques,
                    'severity': rule.severity,
                    'confidence': rule.confidence
                }
            )

            self.log(f"✅ Generated rule {rule.rule_id} ({rule.format}) - Severity: {rule.severity}")

        except Exception as e:
            self.log(f"❌ Error generating rule: {e}", level='error')

    async def generate_sigma_rule(self, detection_logic: Dict, context_data: Dict, enrichment_data: Dict) -> GeneratedRule:
        """Generate Sigma detection rule"""
        rule_id = str(uuid.uuid4())

        # Extract detection components
        process_name = detection_logic.get('process_name', '*')
        command_line = detection_logic.get('command_line', '*')
        parent_process = detection_logic.get('parent_process', '*')
        file_path = detection_logic.get('file_path', '*')

        # Determine rule subtype
        if process_name != '*' or command_line != '*':
            template_key = 'sigma_process_creation'
        else:
            template_key = 'sigma_network_connection'

        template = self.rule_templates[template_key]

        # Build selection criteria
        selection_parts = []
        if process_name != '*':
            selection_parts.append(f"        Image|endswith: '{process_name}'")
        if command_line != '*':
            selection_parts.append(f"        CommandLine|contains: '{command_line}'")
        if parent_process != '*':
            selection_parts.append(f"        ParentImage|endswith: '{parent_process}'")

        selection = '\n'.join(selection_parts) if selection_parts else "        Image: '*'"

        # Determine MITRE techniques
        mitre_techniques = self.detect_mitre_techniques(detection_logic, context_data)

        # Build tags
        tags = [f"    - attack.{tech.lower()}" for tech in mitre_techniques]

        # Generate rule content
        rule_content = template.template.format(
            title=detection_logic.get('title', f'Suspicious {process_name} Activity'),
            rule_id=rule_id,
            description=detection_logic.get('description', f'Detected suspicious activity involving {process_name}'),
            date=datetime.now().strftime('%Y/%m/%d'),
            tags='\n'.join(tags),
            product=context_data.get('product', 'windows'),
            selection=selection,
            fields='    - Image\n    - CommandLine\n    - ParentImage',
            falsepositives='    - Administrative activity\n    - Software installations',
            level=detection_logic.get('severity', 'medium')
        )

        return GeneratedRule(
            rule_id=rule_id,
            name=detection_logic.get('title', f'Suspicious {process_name} Activity'),
            description=detection_logic.get('description', f'Sigma rule for {process_name}'),
            rule_type='sigma',
            format='sigma',
            content=rule_content,
            mitre_tactics=template.mitre_tactics,
            mitre_techniques=mitre_techniques,
            severity=detection_logic.get('severity', 'medium'),
            confidence=0.85,
            created_at=datetime.now(),
            metadata={
                'template_used': template_key,
                'detection_fields': list(detection_logic.keys()),
                'context_sources': list(context_data.keys()),
                'enrichment_sources': list(enrichment_data.keys())
            }
        )

    async def generate_yara_rule(self, detection_logic: Dict, context_data: Dict, enrichment_data: Dict) -> GeneratedRule:
        """Generate YARA file detection rule"""
        rule_id = str(uuid.uuid4())
        rule_name = f"SIEMLess_{detection_logic.get('name', 'Unknown').replace(' ', '_').replace('-', '_')}"

        # Extract file characteristics
        file_strings = detection_logic.get('file_strings', [])
        file_hashes = detection_logic.get('file_hashes', [])
        file_size = detection_logic.get('file_size', {})
        pe_characteristics = detection_logic.get('pe_characteristics', {})

        # Build strings section
        strings_section = []
        for i, string in enumerate(file_strings):
            if isinstance(string, dict):
                string_type = string.get('type', 'ascii')
                string_value = string.get('value', '')
                modifier = f" {string_type}" if string_type != 'ascii' else ""
                strings_section.append(f'        $s{i} = "{string_value}"{modifier}')
            else:
                strings_section.append(f'        $s{i} = "{string}"')

        # Add hash strings if provided
        for i, hash_value in enumerate(file_hashes):
            strings_section.append(f'        $hash{i} = "{hash_value}"')

        strings_content = '\n'.join(strings_section) if strings_section else '        $default = "suspicious"'

        # Build condition
        condition_parts = []
        if file_strings:
            condition_parts.append(f"any of ($s*)")
        if file_hashes:
            condition_parts.append(f"any of ($hash*)")
        if file_size:
            min_size = file_size.get('min', 0)
            max_size = file_size.get('max', 0)
            if min_size > 0 and max_size > 0:
                condition_parts.append(f"filesize >= {min_size} and filesize <= {max_size}")

        condition = " and ".join(condition_parts) if condition_parts else "any of them"

        # Determine MITRE techniques
        mitre_techniques = self.detect_mitre_techniques(detection_logic, context_data)

        template = self.rule_templates['yara_file']
        rule_content = template.template.format(
            rule_name=rule_name,
            description=detection_logic.get('description', f'YARA rule for {rule_name}'),
            date=datetime.now().strftime('%Y-%m-%d'),
            severity=detection_logic.get('severity', 'medium'),
            mitre_tactics=', '.join(template.mitre_tactics),
            mitre_techniques=', '.join(mitre_techniques),
            strings=strings_content,
            condition=f"        {condition}"
        )

        return GeneratedRule(
            rule_id=rule_id,
            name=rule_name,
            description=detection_logic.get('description', f'YARA rule for file detection'),
            rule_type='yara',
            format='yara',
            content=rule_content,
            mitre_tactics=template.mitre_tactics,
            mitre_techniques=mitre_techniques,
            severity=detection_logic.get('severity', 'medium'),
            confidence=0.80,
            created_at=datetime.now(),
            metadata={
                'file_strings_count': len(file_strings),
                'file_hashes_count': len(file_hashes),
                'has_size_constraints': bool(file_size),
                'has_pe_characteristics': bool(pe_characteristics)
            }
        )

    async def generate_kql_rule(self, detection_logic: Dict, context_data: Dict, enrichment_data: Dict) -> GeneratedRule:
        """Generate KQL rule for Microsoft Sentinel"""
        rule_id = str(uuid.uuid4())

        # Extract query components
        table_name = detection_logic.get('table_name', 'SecurityEvent')
        timeframe = detection_logic.get('timeframe', '1h')

        # Build filters
        filters = []
        if 'process_name' in detection_logic:
            filters.append(f"| where ProcessName endswith '{detection_logic['process_name']}'")
        if 'command_line' in detection_logic:
            filters.append(f"| where CommandLine contains '{detection_logic['command_line']}'")
        if 'event_id' in detection_logic:
            filters.append(f"| where EventID == {detection_logic['event_id']}")
        if 'account_name' in detection_logic:
            filters.append(f"| where AccountName == '{detection_logic['account_name']}'")

        filters_content = '\n'.join(filters) if filters else "| where EventID > 0"

        # Build extensions
        extensions = [
            "    Severity = case(",
            "        ProcessName in ('powershell.exe', 'cmd.exe'), 'Medium',",
            "        ProcessName contains 'mimikatz', 'High',",
            "        'Low')",
            "    , ThreatCategory = 'SuspiciousActivity'"
        ]

        # Build projections
        projections = [
            "    TimeGenerated, Computer, Account, ProcessName,",
            "    CommandLine, ParentProcessName, Severity, ThreatCategory"
        ]

        # Build conditions
        conditions = detection_logic.get('conditions', 'Severity in ("Medium", "High")')

        # Build aggregations
        aggregations = [
            "    Count = count(),",
            "    DistinctComputers = dcount(Computer),",
            "    DistinctAccounts = dcount(Account)",
            "    by ProcessName, CommandLine"
        ]

        # Determine MITRE techniques
        mitre_techniques = self.detect_mitre_techniques(detection_logic, context_data)

        template = self.rule_templates['kql_sentinel']
        rule_content = template.template.format(
            title=detection_logic.get('title', 'Suspicious Activity Detection'),
            description=detection_logic.get('description', 'KQL rule for threat detection'),
            mitre_techniques=', '.join(mitre_techniques),
            severity=detection_logic.get('severity', 'medium'),
            date=datetime.now().strftime('%Y-%m-%d'),
            table_name=table_name,
            timeframe=timeframe,
            filters=filters_content,
            extensions='\n'.join(extensions),
            projections='\n'.join(projections),
            conditions=conditions,
            aggregations='\n'.join(aggregations)
        )

        return GeneratedRule(
            rule_id=rule_id,
            name=detection_logic.get('title', 'KQL Threat Detection'),
            description=detection_logic.get('description', 'KQL rule for Microsoft Sentinel'),
            rule_type='kql',
            format='kql',
            content=rule_content,
            mitre_tactics=template.mitre_tactics,
            mitre_techniques=mitre_techniques,
            severity=detection_logic.get('severity', 'medium'),
            confidence=0.82,
            created_at=datetime.now(),
            metadata={
                'table_name': table_name,
                'timeframe': timeframe,
                'filter_count': len(filters),
                'has_aggregations': True
            }
        )

    async def generate_spl_rule(self, detection_logic: Dict, context_data: Dict, enrichment_data: Dict) -> GeneratedRule:
        """Generate SPL rule for Splunk"""
        rule_id = str(uuid.uuid4())

        # Extract search components
        index = detection_logic.get('index', 'main')
        timeframe = detection_logic.get('timeframe', '1h')

        # Build base search
        search_parts = []
        if 'process_name' in detection_logic:
            search_parts.append(f'process_name="{detection_logic["process_name"]}"')
        if 'command_line' in detection_logic:
            search_parts.append(f'command_line="*{detection_logic["command_line"]}*"')
        if 'source_ip' in detection_logic:
            search_parts.append(f'src_ip="{detection_logic["source_ip"]}"')
        if 'event_code' in detection_logic:
            search_parts.append(f'EventCode={detection_logic["event_code"]}')

        base_search = ' '.join(search_parts) if search_parts else 'sourcetype="*"'

        # Build evaluations
        evaluations = [
            'risk_score=case(',
            '    match(command_line, "(?i)powershell.*-enc.*"), 75,',
            '    match(process_name, "(?i)mimikatz|procdump|dumpert"), 90,',
            '    match(process_name, "(?i)cmd|powershell"), 50,',
            '    1==1, 25)',
            ', threat_category="suspicious_activity"'
        ]

        # Build conditions
        conditions = detection_logic.get('conditions', 'risk_score > 50')

        # Build statistics
        statistics = [
            'count as event_count,',
            'dc(host) as affected_hosts,',
            'dc(user) as affected_users,',
            'max(risk_score) as max_risk_score',
            'by process_name, command_line'
        ]

        # Determine MITRE techniques
        mitre_techniques = self.detect_mitre_techniques(detection_logic, context_data)

        template = self.rule_templates['spl_splunk']
        rule_content = template.template.format(
            index=index,
            timeframe=timeframe,
            base_search=base_search,
            evaluations=' | '.join(evaluations),
            conditions=conditions,
            statistics=' '.join(statistics),
            severity=detection_logic.get('severity', 'medium'),
            mitre_tactics=', '.join(template.mitre_tactics),
            mitre_techniques=', '.join(mitre_techniques),
            description=detection_logic.get('description', 'SPL rule for threat detection')
        )

        return GeneratedRule(
            rule_id=rule_id,
            name=detection_logic.get('title', 'SPL Threat Detection'),
            description=detection_logic.get('description', 'SPL rule for Splunk SIEM'),
            rule_type='spl',
            format='spl',
            content=rule_content,
            mitre_tactics=template.mitre_tactics,
            mitre_techniques=mitre_techniques,
            severity=detection_logic.get('severity', 'medium'),
            confidence=0.81,
            created_at=datetime.now(),
            metadata={
                'index': index,
                'timeframe': timeframe,
                'search_components': len(search_parts),
                'has_risk_scoring': True
            }
        )

    async def generate_custom_rule(self, rule_type: str, detection_logic: Dict, context_data: Dict, enrichment_data: Dict) -> GeneratedRule:
        """Generate custom rule format"""
        rule_id = str(uuid.uuid4())

        # Create generic rule structure
        rule_content = {
            "rule_id": rule_id,
            "rule_type": rule_type,
            "detection_logic": detection_logic,
            "context_data": context_data,
            "enrichment_data": enrichment_data,
            "created_at": datetime.now().isoformat(),
            "version": "2.0.0"
        }

        mitre_techniques = self.detect_mitre_techniques(detection_logic, context_data)

        return GeneratedRule(
            rule_id=rule_id,
            name=detection_logic.get('title', f'Custom {rule_type} Rule'),
            description=detection_logic.get('description', f'Custom rule for {rule_type}'),
            rule_type=rule_type,
            format='json',
            content=json.dumps(rule_content, indent=2),
            mitre_tactics=['execution', 'persistence'],
            mitre_techniques=mitre_techniques,
            severity=detection_logic.get('severity', 'medium'),
            confidence=0.75,
            created_at=datetime.now(),
            metadata={
                'custom_format': True,
                'rule_type': rule_type
            }
        )

    def detect_mitre_techniques(self, detection_logic: Dict, context_data: Dict) -> List[str]:
        """Detect MITRE ATT&CK techniques from detection logic"""
        techniques = []

        # Process-based detection
        process_name = detection_logic.get('process_name', '').lower()
        command_line = detection_logic.get('command_line', '').lower()

        if 'powershell' in process_name or 'powershell' in command_line:
            techniques.append('T1059.001')
        if 'cmd' in process_name or 'cmd.exe' in process_name:
            techniques.append('T1059.003')
        if 'mimikatz' in process_name or 'mimikatz' in command_line:
            techniques.append('T1003')
        if any(term in command_line for term in ['inject', 'hollowing', 'migration']):
            techniques.append('T1055')
        if 'reg add' in command_line or 'registry' in command_line:
            techniques.append('T1547.001')

        # Network-based detection
        if 'destination_ip' in detection_logic or 'c2_communication' in context_data:
            techniques.append('T1071.001')

        # File-based detection
        if 'file_strings' in detection_logic or 'malware' in context_data:
            techniques.append('T1055')

        # System discovery
        if any(term in command_line for term in ['systeminfo', 'whoami', 'net user']):
            techniques.append('T1082')

        return list(set(techniques))  # Remove duplicates

    async def handle_pattern_crystallization(self, message: Dict[str, Any]):
        """Handle pattern crystallization from AI discoveries"""
        try:
            pattern_id = message.get('pattern_id')
            ai_discovery = message.get('ai_discovery', {})
            confidence_score = message.get('confidence_score', 0.0)

            self.log(f"🔮 Crystallizing pattern {pattern_id} (confidence: {confidence_score:.2f})")

            # Only crystallize high-confidence patterns
            if confidence_score >= 0.75:
                # Convert AI discovery to deterministic rule
                rule_request = {
                    'rule_type': ai_discovery.get('suggested_rule_type', 'sigma_process_creation'),
                    'detection_logic': ai_discovery.get('detection_criteria', {}),
                    'context_data': ai_discovery.get('context', {}),
                    'enrichment_data': ai_discovery.get('enrichment', {}),
                    'request_id': f"crystallized_{pattern_id}"
                }

                await self.handle_rule_generation_request(rule_request)

                self.log(f"✅ Crystallized pattern {pattern_id} into deterministic rule")
            else:
                self.log(f"⚠️ Pattern {pattern_id} confidence too low for crystallization ({confidence_score:.2f})")

        except Exception as e:
            self.log(f"❌ Error in pattern crystallization: {e}", level='error')

    async def get_rule_library(self) -> Dict[str, Any]:
        """Get current rule library"""
        return {
            'total_rules': len(self.generated_rules),
            'rules_by_format': {
                'sigma': len([r for r in self.generated_rules.values() if r.format == 'sigma']),
                'yara': len([r for r in self.generated_rules.values() if r.format == 'yara']),
                'kql': len([r for r in self.generated_rules.values() if r.format == 'kql']),
                'spl': len([r for r in self.generated_rules.values() if r.format == 'spl']),
                'custom': len([r for r in self.generated_rules.values() if r.format == 'json'])
            },
            'rules_by_severity': {
                'high': len([r for r in self.generated_rules.values() if r.severity == 'high']),
                'medium': len([r for r in self.generated_rules.values() if r.severity == 'medium']),
                'low': len([r for r in self.generated_rules.values() if r.severity == 'low'])
            },
            'recent_rules': [
                {
                    'rule_id': rule.rule_id,
                    'name': rule.name,
                    'format': rule.format,
                    'severity': rule.severity,
                    'created_at': rule.created_at.isoformat()
                }
                for rule in sorted(self.generated_rules.values(),
                                 key=lambda x: x.created_at, reverse=True)[:10]
            ]
        }

    # BaseEngine abstract method implementations
    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single message from the message queue"""
        try:
            message_type = message.get('type', 'unknown')

            if message_type == 'rule_generation_request':
                await self.handle_rule_generation_request(message)
                return {'status': 'success', 'message': 'Rule generation processed'}
            elif message_type == 'pattern_crystallization':
                await self.handle_pattern_crystallization(message)
                return {'status': 'success', 'message': 'Pattern crystallization processed'}
            else:
                return {'status': 'ignored', 'message': f'Unknown message type: {message_type}'}

        except Exception as e:
            self.log(f"Error processing message: {e}", level='error')
            return {'status': 'error', 'message': str(e)}

    def get_capabilities(self) -> Dict[str, Any]:
        """Return engine capabilities for discovery"""
        return {
            'engine_name': 'ruleset',
            'version': self.version,
            'description': 'Generates detection rules in multiple formats (Sigma, YARA, KQL, SPL)',
            'supported_formats': ['sigma', 'yara', 'kql', 'spl', 'custom'],
            'rule_types': [
                'sigma_process_creation',
                'sigma_network_connection',
                'yara_file',
                'kql_sentinel',
                'spl_splunk'
            ],
            'mitre_mapping': True,
            'pattern_crystallization': True,
            'ai_integration': False,
            'endpoints': [
                '/api/v2/ruleset/generate',
                '/api/v2/ruleset/library',
                '/api/v2/ruleset/sigma',
                '/api/v2/ruleset/yara',
                '/api/v2/ruleset/kql',
                '/api/v2/ruleset/spl',
                '/api/v2/ruleset/crystallize',
                '/api/v2/ruleset/mitre'
            ],
            'message_types': [
                'rule_generation_requests',
                'pattern_crystallization',
                'mitre_mapping_requests'
            ]
        }

    # === NEW: CTI Integration and Lifecycle Management Methods ===

    async def ingest_threat_intelligence(self, cti_data: ThreatIntelligence) -> bool:
        """Ingest new CTI and trigger rule updates if relevant"""
        try:
            # Store CTI data
            cti_key = f"cti:{cti_data.source}:{cti_data.ioc_value}"
            await self._store_cti(cti_key, cti_data)

            # Find rules that might need updates based on this CTI
            affected_rules = await self._find_rules_for_cti_update(cti_data)

            # Queue update assessments for affected rules
            for rule_id in affected_rules:
                await self._queue_rule_update_assessment(rule_id, UpdateTrigger.NEW_CTI, cti_data)

            self.logger.info(f"Ingested CTI from {cti_data.source}, affected {len(affected_rules)} rules")
            return True

        except Exception as e:
            self.logger.error(f"Failed to ingest CTI: {str(e)}")
            return False

    async def generate_rules_from_cti(self, cti_batch: List[ThreatIntelligence]) -> List[EnhancedRule]:
        """Generate new detection rules from CTI intelligence"""
        generated_rules = []

        for cti in cti_batch:
            try:
                # Determine best rule format based on CTI type
                rule_format = self._determine_rule_format_for_cti(cti)

                # Create enhanced rule based on CTI context
                enhanced_rule = await self._create_enhanced_rule_from_cti(cti, rule_format)

                # Generate initial test cases
                test_cases = await self._generate_test_cases_for_rule(enhanced_rule)
                self.test_cases[enhanced_rule.rule_id] = test_cases

                # Store enhanced rule
                self.enhanced_rules[enhanced_rule.rule_id] = enhanced_rule
                generated_rules.append(enhanced_rule)

            except Exception as e:
                self.logger.error(f"Failed to generate rule from CTI {cti.ioc_value}: {str(e)}")

        self.logger.info(f"Generated {len(generated_rules)} enhanced rules from {len(cti_batch)} CTI inputs")
        return generated_rules

    async def update_rule_performance(self, rule_id: str, alert_data: Dict[str, Any]) -> bool:
        """Update rule performance metrics with new alert data"""
        if rule_id not in self.enhanced_rules:
            self.logger.warning(f"Enhanced rule {rule_id} not found for performance update")
            return False

        rule = self.enhanced_rules[rule_id]
        metrics = rule.performance_metrics

        # Update counters based on alert classification
        if alert_data.get('classification') == 'true_positive':
            metrics.true_positives += 1
        elif alert_data.get('classification') == 'false_positive':
            metrics.false_positives += 1
        elif alert_data.get('classification') == 'false_negative':
            metrics.false_negatives += 1

        metrics.total_alerts += 1
        metrics.last_triggered = datetime.now()

        # Recalculate performance scores
        await self._calculate_performance_scores(metrics)

        # Check if rule needs attention
        await self._assess_rule_health(rule)

        self.logger.info(f"Updated performance metrics for enhanced rule {rule_id}")
        return True

    async def generate_improvement_suggestions(self, rule_id: str) -> List[RuleImprovementSuggestion]:
        """Generate AI-powered improvement suggestions for a rule"""
        if rule_id not in self.enhanced_rules:
            return []

        rule = self.enhanced_rules[rule_id]
        suggestions = []

        try:
            # Analyze performance issues
            performance_suggestions = await self._analyze_performance_issues(rule)
            suggestions.extend(performance_suggestions)

            # Analyze coverage gaps
            coverage_suggestions = await self._analyze_coverage_gaps(rule)
            suggestions.extend(coverage_suggestions)

            # Analyze false positive patterns
            fp_suggestions = await self._analyze_false_positive_patterns(rule)
            suggestions.extend(fp_suggestions)

            # Store suggestions
            for suggestion in suggestions:
                self.improvement_suggestions[suggestion.suggestion_id] = suggestion

            self.logger.info(f"Generated {len(suggestions)} improvement suggestions for rule {rule_id}")

        except Exception as e:
            self.logger.error(f"Failed to generate improvements for rule {rule_id}: {str(e)}")

        return suggestions

    async def run_rule_tests(self, rule_id: str) -> Dict[str, Any]:
        """Run all test cases for a specific rule"""
        if rule_id not in self.test_cases:
            return {'status': 'no_tests', 'results': []}

        test_results = []
        passed_tests = 0
        total_tests = len(self.test_cases[rule_id])

        for test_case in self.test_cases[rule_id]:
            try:
                start_time = datetime.now()

                # Execute rule test
                result = await self._execute_rule_test(rule_id, test_case)

                execution_time = (datetime.now() - start_time).total_seconds()

                test_case.actual_result = result
                test_case.execution_time = execution_time
                test_case.last_run = datetime.now()

                if result == test_case.expected_result:
                    test_case.test_status = "passed"
                    passed_tests += 1
                else:
                    test_case.test_status = "failed"

                test_results.append({
                    'test_id': test_case.test_id,
                    'test_name': test_case.test_name,
                    'status': test_case.test_status,
                    'execution_time': execution_time,
                    'expected': test_case.expected_result,
                    'actual': result
                })

            except Exception as e:
                test_case.test_status = "failed"
                test_case.error_message = str(e)
                test_results.append({
                    'test_id': test_case.test_id,
                    'test_name': test_case.test_name,
                    'status': "failed",
                    'error': str(e)
                })

        success_rate = passed_tests / total_tests if total_tests > 0 else 0

        return {
            'status': 'completed',
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': success_rate,
            'results': test_results
        }

    async def deploy_rule_to_platform(self, rule_id: str, platform: str, config: Dict[str, Any] = None) -> bool:
        """Deploy rule to specific SIEM/security platform"""
        if rule_id not in self.enhanced_rules:
            self.logger.error(f"Enhanced rule {rule_id} not found for deployment")
            return False

        rule = self.enhanced_rules[rule_id]

        try:
            # Convert rule format if needed for platform
            platform_rule = await self._convert_rule_for_platform(rule, platform)

            # Deploy via platform-specific integration
            deployment_result = await self._deploy_to_platform(platform_rule, platform, config)

            if deployment_result['success']:
                rule.deployed_platforms.append(platform)
                rule.deployment_config[platform] = config or {}

                # Update rule status to active if first deployment
                if rule.status == RuleStatus.TESTING and len(rule.deployed_platforms) == 1:
                    rule.status = RuleStatus.ACTIVE

                self.logger.info(f"Successfully deployed enhanced rule {rule_id} to {platform}")
                return True
            else:
                self.logger.error(f"Failed to deploy rule {rule_id} to {platform}: {deployment_result['error']}")
                return False

        except Exception as e:
            self.logger.error(f"Deployment error for rule {rule_id}: {str(e)}")
            return False

    async def get_rule_performance_analytics(self) -> Dict[str, Any]:
        """Get comprehensive performance analytics across all rules"""
        if not self.enhanced_rules:
            return {
                'total_rules': 0,
                'performance_summary': {},
                'timestamp': datetime.now().isoformat()
            }

        # Calculate aggregate metrics
        total_rules = len(self.enhanced_rules)
        healthy_rules = 0
        needs_attention = 0
        total_alerts = 0
        total_tp = total_fp = total_fn = 0

        precision_scores = []
        recall_scores = []
        f1_scores = []

        for rule in self.enhanced_rules.values():
            metrics = rule.performance_metrics

            if metrics.precision >= 0.8 and metrics.recall >= 0.7:
                healthy_rules += 1
            else:
                needs_attention += 1

            total_alerts += metrics.total_alerts
            total_tp += metrics.true_positives
            total_fp += metrics.false_positives
            total_fn += metrics.false_negatives

            if metrics.precision > 0:
                precision_scores.append(metrics.precision)
            if metrics.recall > 0:
                recall_scores.append(metrics.recall)
            if metrics.f1_score > 0:
                f1_scores.append(metrics.f1_score)

        # Calculate averages
        avg_precision = sum(precision_scores) / len(precision_scores) if precision_scores else 0
        avg_recall = sum(recall_scores) / len(recall_scores) if recall_scores else 0
        avg_f1 = sum(f1_scores) / len(f1_scores) if f1_scores else 0

        # Calculate overall effectiveness
        overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
        overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0

        return {
            'total_rules': total_rules,
            'rule_health': {
                'healthy_rules': healthy_rules,
                'needs_attention': needs_attention,
                'health_percentage': (healthy_rules / total_rules * 100) if total_rules > 0 else 0
            },
            'performance_summary': {
                'total_alerts': total_alerts,
                'true_positives': total_tp,
                'false_positives': total_fp,
                'false_negatives': total_fn,
                'overall_precision': round(overall_precision, 3),
                'overall_recall': round(overall_recall, 3),
                'avg_precision': round(avg_precision, 3),
                'avg_recall': round(avg_recall, 3),
                'avg_f1_score': round(avg_f1, 3)
            },
            'timestamp': datetime.now().isoformat()
        }

    # === Helper Methods for CTI and Lifecycle Management ===

    async def _store_cti(self, key: str, cti_data: ThreatIntelligence) -> None:
        """Store CTI data in Redis"""
        cti_json = {
            'source': cti_data.source,
            'ioc_type': cti_data.ioc_type,
            'ioc_value': cti_data.ioc_value,
            'threat_context': cti_data.threat_context.value,
            'confidence_score': cti_data.confidence_score,
            'first_seen': cti_data.first_seen.isoformat(),
            'last_seen': cti_data.last_seen.isoformat(),
            'mitre_tactics': cti_data.mitre_tactics,
            'mitre_techniques': cti_data.mitre_techniques,
            'raw_intelligence': cti_data.raw_intelligence
        }

        await self.redis_client.setex(key, 86400 * 30, json.dumps(cti_json))  # 30 days TTL

    async def _find_rules_for_cti_update(self, cti_data: ThreatIntelligence) -> List[str]:
        """Identify rules that should be updated based on new CTI"""
        affected_rules = []

        for rule_id, rule in self.enhanced_rules.items():
            # Check if CTI matches rule's threat context
            if (rule.threat_context == cti_data.threat_context or
                any(tactic in rule.mitre_tactics for tactic in cti_data.mitre_tactics) or
                any(technique in rule.mitre_techniques for technique in cti_data.mitre_techniques)):

                affected_rules.append(rule_id)

        return affected_rules

    async def _queue_rule_update_assessment(self, rule_id: str, trigger: UpdateTrigger, context_data: Any = None) -> None:
        """Queue a rule for update assessment"""
        assessment_data = {
            'rule_id': rule_id,
            'trigger': trigger.value,
            'context_data': context_data,
            'queued_at': datetime.now().isoformat()
        }

        await self.redis_client.lpush('rule_update_queue', json.dumps(assessment_data, default=str))
        self.logger.info(f"Queued rule {rule_id} for update assessment (trigger: {trigger.value})")

    def _determine_rule_format_for_cti(self, cti: ThreatIntelligence) -> str:
        """Determine best rule format based on CTI type"""
        if cti.ioc_type in ['file_hash', 'file_name', 'file_path']:
            return 'yara'
        elif cti.ioc_type in ['ip_address', 'domain', 'url']:
            return 'sigma'
        elif cti.threat_context == ThreatContext.MALWARE:
            return 'yara'
        else:
            return 'sigma'  # Default to Sigma

    async def _create_enhanced_rule_from_cti(self, cti: ThreatIntelligence, rule_format: str) -> EnhancedRule:
        """Create enhanced rule from CTI data"""
        rule_id = str(uuid.uuid4())

        # Generate rule content based on format
        if rule_format == 'sigma':
            base_rule = await self.generate_sigma_rule(
                {'ioc_value': cti.ioc_value, 'ioc_type': cti.ioc_type},
                {'threat_context': cti.threat_context.value},
                {'cti_source': cti.source}
            )
        elif rule_format == 'yara':
            base_rule = await self.generate_yara_rule(
                {'ioc_value': cti.ioc_value, 'ioc_type': cti.ioc_type},
                {'threat_context': cti.threat_context.value},
                {'cti_source': cti.source}
            )
        else:
            # Fallback to Sigma
            base_rule = await self.generate_sigma_rule(
                {'ioc_value': cti.ioc_value, 'ioc_type': cti.ioc_type},
                {'threat_context': cti.threat_context.value},
                {'cti_source': cti.source}
            )

        # Create enhanced rule with lifecycle management
        enhanced_rule = EnhancedRule(
            rule_id=rule_id,
            name=f"CTI-Generated: {cti.threat_context.value.title()} Detection",
            description=f"Auto-generated rule for {cti.ioc_type} {cti.ioc_value}",
            rule_type=base_rule.rule_type,
            format=base_rule.format,
            content=base_rule.content,
            mitre_tactics=cti.mitre_tactics,
            mitre_techniques=cti.mitre_techniques,
            severity=base_rule.severity,
            confidence=cti.confidence_score,
            created_at=datetime.now(),
            metadata=base_rule.metadata,
            source_cti=[cti],
            threat_context=cti.threat_context,
            performance_metrics=RulePerformanceMetrics(rule_id)
        )

        return enhanced_rule

    async def _generate_test_cases_for_rule(self, rule: EnhancedRule) -> List[RuleTestCase]:
        """Generate test cases for a rule"""
        test_cases = []

        # Positive test case
        positive_test = RuleTestCase(
            rule_id=rule.rule_id,
            test_name=f"Positive Detection Test - {rule.name}",
            test_type="positive_match",
            test_data=f"sample_data_for_{rule.rule_type}",
            expected_result=True
        )
        test_cases.append(positive_test)

        # Negative test case
        negative_test = RuleTestCase(
            rule_id=rule.rule_id,
            test_name=f"Negative Detection Test - {rule.name}",
            test_type="negative_match",
            test_data="benign_sample_data",
            expected_result=False
        )
        test_cases.append(negative_test)

        return test_cases

    async def _calculate_performance_scores(self, metrics: RulePerformanceMetrics) -> None:
        """Calculate precision, recall, and F1 scores"""
        if metrics.total_alerts == 0:
            return

        # Precision = TP / (TP + FP)
        if (metrics.true_positives + metrics.false_positives) > 0:
            metrics.precision = metrics.true_positives / (metrics.true_positives + metrics.false_positives)

        # Recall = TP / (TP + FN)
        if (metrics.true_positives + metrics.false_negatives) > 0:
            metrics.recall = metrics.true_positives / (metrics.true_positives + metrics.false_negatives)

        # F1 Score = 2 * (precision * recall) / (precision + recall)
        if (metrics.precision + metrics.recall) > 0:
            metrics.f1_score = 2 * (metrics.precision * metrics.recall) / (metrics.precision + metrics.recall)

    async def _assess_rule_health(self, rule: EnhancedRule) -> None:
        """Assess rule health and trigger improvements if needed"""
        metrics = rule.performance_metrics

        # Check for performance decline
        needs_attention = False
        trigger_reason = None

        if metrics.precision < self.performance_thresholds['min_precision']:
            needs_attention = True
            trigger_reason = UpdateTrigger.FALSE_POSITIVE_SPIKE

        elif metrics.recall < self.performance_thresholds['min_recall']:
            needs_attention = True
            trigger_reason = UpdateTrigger.PERFORMANCE_DECLINE

        elif metrics.avg_response_time > self.performance_thresholds['max_response_time']:
            needs_attention = True
            trigger_reason = UpdateTrigger.PERFORMANCE_DECLINE

        if needs_attention:
            rule.status = RuleStatus.NEEDS_UPDATE
            await self._queue_rule_update_assessment(rule.rule_id, trigger_reason)

    async def _analyze_performance_issues(self, rule: EnhancedRule) -> List[RuleImprovementSuggestion]:
        """Analyze rule performance and suggest optimizations"""
        suggestions = []
        metrics = rule.performance_metrics

        # Low precision suggests false positive issues
        if metrics.precision < 0.8:
            suggestion = RuleImprovementSuggestion(
                rule_id=rule.rule_id,
                suggestion_type="false_positive_reduction",
                description=f"Rule precision is {metrics.precision:.2f}, below threshold of 0.8",
                proposed_changes="Add additional context filters to reduce false positives",
                confidence_score=0.85,
                expected_impact="Reduce false positive rate by 20-30%",
                implementation_priority="high"
            )
            suggestions.append(suggestion)

        # Low recall suggests coverage gaps
        if metrics.recall < 0.7:
            suggestion = RuleImprovementSuggestion(
                rule_id=rule.rule_id,
                suggestion_type="coverage_expansion",
                description=f"Rule recall is {metrics.recall:.2f}, below threshold of 0.7",
                proposed_changes="Expand detection logic to catch more attack variants",
                confidence_score=0.75,
                expected_impact="Increase detection coverage by 15-25%",
                implementation_priority="medium"
            )
            suggestions.append(suggestion)

        return suggestions

    async def _analyze_coverage_gaps(self, rule: EnhancedRule) -> List[RuleImprovementSuggestion]:
        """Analyze rule coverage gaps"""
        return []  # Placeholder for AI-powered analysis

    async def _analyze_false_positive_patterns(self, rule: EnhancedRule) -> List[RuleImprovementSuggestion]:
        """Analyze false positive patterns"""
        return []  # Placeholder for AI-powered analysis

    async def _execute_rule_test(self, rule_id: str, test_case: RuleTestCase) -> bool:
        """Execute a rule test case - simulation for now"""
        # In real implementation, this would execute the rule against test data
        # For now, simulate based on test type
        if test_case.test_type == "positive_match":
            return True  # Simulate positive detection
        elif test_case.test_type == "negative_match":
            return False  # Simulate no detection
        else:
            return test_case.expected_result  # Default to expected result

    async def _convert_rule_for_platform(self, rule: EnhancedRule, target_platform: str) -> Dict[str, Any]:
        """Convert rule to platform-specific format"""
        return {
            'rule_id': rule.rule_id,
            'name': rule.name,
            'content': rule.content,
            'format': target_platform
        }

    async def _deploy_to_platform(self, rule: Dict[str, Any], platform: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy rule to specific platform - simulation"""
        # In real implementation, this would use platform APIs
        return {'success': True, 'deployment_id': str(uuid.uuid4())}

    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate engine-specific configuration"""
        required_fields = ['ENGINE_NAME', 'REDIS_HOST', 'REDIS_PORT']

        for field in required_fields:
            if field not in config:
                self.log(f"Missing required configuration field: {field}", level='error')
                return False

        # Validate rule template availability
        if not self.rule_templates:
            self.log("No rule templates loaded", level='error')
            return False

        # Validate MITRE mappings
        if not self.mitre_mappings:
            self.log("No MITRE ATT&CK mappings loaded", level='error')
            return False

        return True

async def main():
    """Main entry point"""
    engine = RulesetEngine()
    await engine.start()

if __name__ == "__main__":
    asyncio.run(main())