# SIEMLess v2.0 - Environment Configuration
# Copy this file to .env and configure your values

# ==========================================
# DATABASE CONFIGURATION
# ==========================================
POSTGRES_DB=siemless_v3
POSTGRES_USER=siemless
POSTGRES_PASSWORD=secure_password_123

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379

# ==========================================
# AI MODEL API KEYS
# ==========================================
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_ai_api_key_here

# ==========================================
# ELASTICSEARCH INTEGRATION
# ==========================================
# Option 1: Elastic Cloud (Recommended)
ELASTIC_CLOUD_ID=your_elastic_cloud_id
ELASTIC_API_KEY=your_elastic_api_key

# Option 2: Self-hosted Elasticsearch
ELASTIC_URL=http://localhost:9200
ELASTIC_USERNAME=elastic
ELASTIC_PASSWORD=your_elastic_password

# ==========================================
# CROWDSTRIKE INTEGRATION
# ==========================================
CROWDSTRIKE_CLIENT_ID=your_crowdstrike_client_id
CROWDSTRIKE_CLIENT_SECRET=your_crowdstrike_client_secret
CROWDSTRIKE_BASE_URL=https://api.crowdstrike.com

# CrowdStrike API Scopes (comma-separated)
# Available scopes:
# - detections:read (Detection events)
# - hosts:read (Host/device information)
# - incidents:read (Incident data)
# - alerts:read (Alert management)
# - event-streams:read (Raw telemetry)
# - intel:read (Threat intelligence)
# - iocs:read (Indicators of compromise)
CROWDSTRIKE_SCOPES=detections:read,hosts:read,incidents:read,alerts:read

# ==========================================
# OTHER SIEM INTEGRATIONS (Optional)
# ==========================================
# Splunk
SPLUNK_HOST=your_splunk_instance
SPLUNK_TOKEN=your_splunk_token

# QRadar
QRADAR_HOST=your_qradar_instance
QRADAR_TOKEN=your_qradar_token

# Sentinel
SENTINEL_WORKSPACE_ID=your_workspace_id
SENTINEL_CLIENT_ID=your_client_id
SENTINEL_CLIENT_SECRET=your_client_secret
SENTINEL_TENANT_ID=your_tenant_id

# ==========================================
# CTI SOURCES (Optional)
# ==========================================
OPENCTI_URL=http://your-opencti-instance:8080
OPENCTI_TOKEN=your_opencti_api_token

MISP_URL=http://your-misp-instance
MISP_KEY=your_misp_api_key

# ==========================================
# CLOUD STORAGE (For Backend Engine)
# ==========================================
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
S3_BUCKET=siemless-storage

# ==========================================
# DEVELOPMENT/DEBUG SETTINGS
# ==========================================
LOG_LEVEL=INFO
DEBUG_MODE=false
ENVIRONMENT=development

# ==========================================
# ENGINE SPECIFIC SETTINGS
# ==========================================
# Ingestion Engine
INGESTION_BATCH_SIZE=1000
INGESTION_POLL_INTERVAL=30

# Intelligence Engine
AI_MODEL_DEFAULT=gemma-3-27b
CONSENSUS_THRESHOLD=0.80

# Contextualization Engine
ENRICHMENT_DEPTH=3
ENTITY_CACHE_TTL=3600

# Backend Engine
STORAGE_TIER_HOT_DAYS=7
STORAGE_TIER_WARM_DAYS=30
STORAGE_TIER_COLD_DAYS=365

# Delivery Engine
ALERT_CHANNELS=email,slack,webhook
DASHBOARD_REFRESH_INTERVAL=60