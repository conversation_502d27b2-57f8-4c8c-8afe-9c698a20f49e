"""
Test Parser Generation Workflow
Tests the complete parser generation pipeline
"""

import requests
import json
import time

# Test sample logs
SAMPLE_FIREWALL_LOGS = [
    '{"timestamp": "2025-10-02 14:23:45", "src": "*************", "dst": "********", "srcport": 54321, "dstport": 443, "action": "allow", "user": "john.doe", "bytes": 1024}',
    '{"timestamp": "2025-10-02 14:24:12", "src": "*************", "dst": "********", "srcport": 54322, "dstport": 443, "action": "allow", "user": "jane.smith", "bytes": 2048}',
    '{"timestamp": "2025-10-02 14:25:33", "src": "*************", "dst": "********", "srcport": 54323, "dstport": 22, "action": "deny", "user": "admin", "bytes": 0}'
]

SAMPLE_WINDOWS_LOGS = [
    '2025-10-02 10:15:23 EventID=4624 User=CORP\\\\admin Host=DC01 IP=************ Logon=Interactive Status=Success',
    '2025-10-02 10:16:45 EventID=4625 User=CORP\\\\guest Host=WKS01 IP=************ Logon=Network Status=Failure',
    '2025-10-02 10:17:12 EventID=4624 User=CORP\\\\jdoe Host=WKS02 IP=************* Logon=RemoteInteractive Status=Success'
]

def test_parser_generation():
    """Test parser generation endpoint"""

    print("=" * 80)
    print("Testing Parser Generation Workflow")
    print("=" * 80)
    print()

    # Test 1: Generate parser for firewall logs
    print("Test 1: Firewall Logs (JSON) → Elastic ECS")
    print("-" * 80)

    payload = {
        "log_samples": SAMPLE_FIREWALL_LOGS,
        "log_source": "custom_firewall",
        "vendor": "Acme Firewall",
        "target_siem": "elastic"
    }

    print(f"Sending request to http://localhost:8003/api/parsers/generate")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print()

    try:
        response = requests.post(
            'http://localhost:8003/api/parsers/generate',
            json=payload,
            timeout=120  # 2 minutes for AI processing
        )

        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        print()

        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print(f"  Parser ID: {result['parser_id']}")
            print(f"  Coverage: {result.get('coverage', 0)}%")
            print(f"  Entities: {result.get('entity_count', 0)}")
            print(f"  Relationships: {result.get('relationship_count', 0)}")
            print()
        else:
            print("❌ FAILED!")
            print()

    except requests.exceptions.Timeout:
        print("❌ Request timed out (>120s)")
        print()
    except Exception as e:
        print(f"❌ Error: {e}")
        print()

    # Test 2: Generate parser for Windows logs
    print("=" * 80)
    print("Test 2: Windows Event Logs (Key-Value) → Splunk CIM")
    print("-" * 80)

    payload2 = {
        "log_samples": SAMPLE_WINDOWS_LOGS,
        "log_source": "windows_security",
        "vendor": "Microsoft",
        "target_siem": "splunk"
    }

    print(f"Sending request to http://localhost:8003/api/parsers/generate")
    print(f"Payload: {json.dumps(payload2, indent=2)}")
    print()

    try:
        response = requests.post(
            'http://localhost:8003/api/parsers/generate',
            json=payload2,
            timeout=120
        )

        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        print()

        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print(f"  Parser ID: {result['parser_id']}")
            print(f"  Coverage: {result.get('coverage', 0)}%")
            print(f"  Entities: {result.get('entity_count', 0)}")
            print(f"  Relationships: {result.get('relationship_count', 0)}")
            print()
        else:
            print("❌ FAILED!")
            print()

    except requests.exceptions.Timeout:
        print("❌ Request timed out (>120s)")
        print()
    except Exception as e:
        print(f"❌ Error: {e}")
        print()

    # Test 3: List all parsers
    print("=" * 80)
    print("Test 3: List All Parsers")
    print("-" * 80)

    try:
        response = requests.get('http://localhost:8003/api/parsers')

        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        print()

        if response.status_code == 200:
            result = response.json()
            print(f"✅ Found {result['count']} parsers")
            print()
        else:
            print("❌ FAILED!")
            print()

    except Exception as e:
        print(f"❌ Error: {e}")
        print()


def test_availability():
    """Test if engines are available"""
    print("=" * 80)
    print("Checking Engine Availability")
    print("=" * 80)
    print()

    engines = {
        'Ingestion': 'http://localhost:8003/health',
        'Intelligence': 'http://localhost:8001/health',
        'Contextualization': 'http://localhost:8004/health',
        'Backend': 'http://localhost:8002/health'
    }

    all_available = True

    for name, url in engines.items():
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"[OK] {name:20s} - {data.get('status', 'unknown')}")
            else:
                print(f"[FAIL] {name:20s} - HTTP {response.status_code}")
                all_available = False
        except Exception as e:
            print(f"[FAIL] {name:20s} - {str(e)}")
            all_available = False

    print()

    if not all_available:
        print("WARNING: Not all engines are available!")
        print("   Parser generation may fail or timeout.")
        print()

    return all_available


if __name__ == '__main__':
    print()
    print("=" * 80)
    print(" " * 20 + "Parser Generation Test Suite")
    print("=" * 80)
    print()

    # Check engine availability first
    available = test_availability()

    if available:
        print("All engines available. Proceeding with tests...")
        print()
        test_parser_generation()
    else:
        print("WARNING: Some engines are not available.")
        print("Start all engines first:")
        print("  docker-compose up -d")
        print()

    print("=" * 80)
    print("Test Suite Complete")
    print("=" * 80)
