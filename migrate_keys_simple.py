#!/usr/bin/env python3
"""
Simple API Key Migration Script for SIEMLess v2.0

This script migrates API keys from environment variables to encrypted database storage
using synchronous database operations for compatibility.
"""

import os
import sys
import json
import secrets
import base64
from datetime import datetime
from typing import Dict, Any

# Database imports
import psycopg2
from psycopg2.extras import RealDictCursor

# Crypto imports
from cryptography.hazmat.primitives.ciphers.aead import AESGCM

class SimpleCredentialMigrator:
    """Simple credential migration utility"""

    def __init__(self):
        # Database configuration
        self.db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': os.environ.get('POSTGRES_DB', 'siemless_db'),
            'user': os.environ.get('POSTGRES_USER', 'test'),
            'password': os.environ.get('POSTGRES_PASSWORD', 'test123')
        }

        # Get master key
        self.master_key = self._get_master_key()

        print("[INIT] Simple credential migrator initialized")

    def _get_master_key(self) -> bytes:
        """Get master key from environment"""
        master_key_str = os.environ.get("SIEMLESS_MASTER_KEY")
        if master_key_str:
            try:
                return base64.b64decode(master_key_str)
            except Exception:
                try:
                    return bytes.fromhex(master_key_str)
                except ValueError:
                    raise ValueError("Invalid master key format")

        # Generate temporary key
        print("[WARNING] Generating temporary master key")
        return secrets.token_bytes(32)

    def _encrypt_credentials(self, credentials: Dict[str, Any]) -> bytes:
        """Encrypt credentials with AES-GCM"""
        aesgcm = AESGCM(self.master_key)
        nonce = secrets.token_bytes(12)
        plaintext = json.dumps(credentials).encode('utf-8')
        ciphertext = aesgcm.encrypt(nonce, plaintext, None)
        return nonce + ciphertext

    def create_schema(self, conn):
        """Create v2 schema"""
        cursor = conn.cursor()

        print("[SETUP] Creating v2 schema...")

        # Create schema
        cursor.execute("CREATE SCHEMA IF NOT EXISTS siemless_v2")

        # Create tables
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS siemless_v2.clients (
                client_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                client_name VARCHAR(255) NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                is_active BOOLEAN DEFAULT TRUE,
                metadata JSONB DEFAULT '{}'
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS siemless_v2.environments (
                environment_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                client_id UUID REFERENCES siemless_v2.clients(client_id),
                environment_name VARCHAR(255) NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                is_active BOOLEAN DEFAULT TRUE
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS siemless_v2.credentials (
                credential_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                client_id UUID REFERENCES siemless_v2.clients(client_id),
                environment_id UUID REFERENCES siemless_v2.environments(environment_id),
                provider_type VARCHAR(100) NOT NULL,
                provider_name VARCHAR(100) NOT NULL,
                credential_name VARCHAR(100) DEFAULT 'default',
                encrypted_data BYTEA NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                is_active BOOLEAN DEFAULT TRUE,
                metadata JSONB DEFAULT '{}'
            )
        """)

        conn.commit()
        print("[OK] Schema created successfully")

    def create_default_client(self, conn):
        """Create default client and environment"""
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        print("[SETUP] Creating default client...")

        # Insert client
        cursor.execute("""
            INSERT INTO siemless_v2.clients (client_name, metadata)
            VALUES ('SIEMLess Default Client', '{"description": "Default v2 client"}')
            ON CONFLICT DO NOTHING
            RETURNING client_id
        """)

        result = cursor.fetchone()
        if not result:
            cursor.execute("""
                SELECT client_id FROM siemless_v2.clients
                WHERE client_name = 'SIEMLess Default Client'
            """)
            result = cursor.fetchone()

        client_id = str(result['client_id'])

        # Insert environment
        cursor.execute("""
            INSERT INTO siemless_v2.environments (client_id, environment_name)
            VALUES (%s, 'production')
            ON CONFLICT DO NOTHING
            RETURNING environment_id
        """, (client_id,))

        result = cursor.fetchone()
        if not result:
            cursor.execute("""
                SELECT environment_id FROM siemless_v2.environments
                WHERE client_id = %s AND environment_name = 'production'
            """, (client_id,))
            result = cursor.fetchone()

        environment_id = str(result['environment_id'])

        conn.commit()
        print(f"[OK] Client ID: {client_id}")
        print(f"[OK] Environment ID: {environment_id}")

        return client_id, environment_id

    def migrate_credentials(self, conn, client_id: str, environment_id: str):
        """Migrate API credentials"""
        cursor = conn.cursor()

        print("[MIGRATE] Starting credential migration...")

        # API key mappings
        api_keys = {
            'ai_provider': {
                'anthropic': {
                    'ANTHROPIC_API_KEY': os.environ.get('ANTHROPIC_API_KEY', '')
                },
                'openai': {
                    'OPENAI_API_KEY': os.environ.get('OPENAI_API_KEY', '')
                },
                'google': {
                    'GEMINI_API_KEY': os.environ.get('GEMINI_API_KEY', '')
                }
            },
            'cti_provider': {
                'opencti': {
                    'OPENCTI_URL': os.environ.get('OPENCTI_URL', ''),
                    'OPENCTI_TOKEN': os.environ.get('OPENCTI_TOKEN', '')
                },
                'otx': {
                    'OTX_API_KEY': os.environ.get('OTX_API_KEY', '')
                }
            },
            'siem_provider': {
                'crowdstrike': {
                    'CROWDSTRIKE_CLIENT_ID': os.environ.get('CROWDSTRIKE_CLIENT_ID', ''),
                    'CROWDSTRIKE_CLIENT_SECRET': os.environ.get('CROWDSTRIKE_CLIENT_SECRET', ''),
                    'CROWDSTRIKE_BASE_URL': os.environ.get('CROWDSTRIKE_BASE_URL', '')
                },
                'elasticsearch': {
                    'ELASTIC_CLOUD_ID': os.environ.get('ELASTIC_CLOUD_ID', ''),
                    'ELASTIC_API_KEY': os.environ.get('ELASTIC_API_KEY', '')
                }
            }
        }

        migrated_count = 0

        for provider_type, providers in api_keys.items():
            for provider_name, credentials in providers.items():
                # Filter empty credentials
                filtered_creds = {k: v for k, v in credentials.items() if v}

                if not filtered_creds:
                    print(f"[SKIP] No credentials for {provider_type}/{provider_name}")
                    continue

                # Encrypt credentials
                encrypted_data = self._encrypt_credentials(filtered_creds)

                # Insert into database
                cursor.execute("""
                    INSERT INTO siemless_v2.credentials
                    (client_id, environment_id, provider_type, provider_name,
                     credential_name, encrypted_data, metadata)
                    VALUES (%s, %s, %s, %s, 'default', %s, %s)
                    ON CONFLICT DO NOTHING
                """, (client_id, environment_id, provider_type, provider_name,
                      encrypted_data, json.dumps({'migrated': True, 'key_count': len(filtered_creds)})))

                migrated_count += 1
                print(f"[OK] Migrated {len(filtered_creds)} keys for {provider_type}/{provider_name}")

        conn.commit()
        print(f"[SUCCESS] Migrated {migrated_count} credential sets")

        return migrated_count

    def verify_migration(self, conn, client_id: str, environment_id: str):
        """Verify migration results"""
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        print("[VERIFY] Checking migration...")

        cursor.execute("""
            SELECT provider_type, provider_name, credential_name,
                   created_at, metadata
            FROM siemless_v2.credentials
            WHERE client_id = %s AND environment_id = %s AND is_active = TRUE
            ORDER BY provider_type, provider_name
        """, (client_id, environment_id))

        credentials = cursor.fetchall()

        print(f"[OK] Total credentials: {len(credentials)}")
        print("\n[SUMMARY] Credential breakdown:")

        for cred in credentials:
            key_count = cred['metadata'].get('key_count', 'unknown')
            print(f"  - {cred['provider_type']}/{cred['provider_name']}: {key_count} keys")

        return len(credentials) > 0

    def run_migration(self):
        """Run the complete migration"""
        print("[START] SIEMLess v2.0 API Key Migration")
        print("=" * 50)

        try:
            # Connect to database
            print(f"[CONNECT] Connecting to {self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
            conn = psycopg2.connect(**self.db_config)

            # Create schema
            self.create_schema(conn)

            # Create default client
            client_id, environment_id = self.create_default_client(conn)

            # Migrate credentials
            migrated = self.migrate_credentials(conn, client_id, environment_id)

            # Verify results
            success = self.verify_migration(conn, client_id, environment_id)

            if success and migrated > 0:
                print("\n[SUCCESS] MIGRATION COMPLETED!")
                print(f"Client ID: {client_id}")
                print(f"Environment ID: {environment_id}")
                print(f"Credentials migrated: {migrated}")
                print("\n[SECURE] All API keys are now encrypted in the database")
                return 0
            else:
                print("\n[ERROR] Migration failed or no credentials found")
                return 1

        except Exception as e:
            print(f"\n[ERROR] Migration failed: {e}")
            import traceback
            traceback.print_exc()
            return 1

        finally:
            if 'conn' in locals():
                conn.close()

if __name__ == "__main__":
    migrator = SimpleCredentialMigrator()
    sys.exit(migrator.run_migration())