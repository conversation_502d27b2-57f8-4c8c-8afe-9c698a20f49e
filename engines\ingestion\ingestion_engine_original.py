"""
SIEMLess v2.0 - Ingestion Engine
Multi-source data ingestion with intelligent routing and normalization
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from base_engine import BaseEngine
from pattern_matcher import PatternMatcher
from github_pattern_sync import GitHubPatternSync
from parser_hot_reload import <PERSON><PERSON><PERSON><PERSON>ot<PERSON>elo<PERSON>
from api_doc_generator import APIDocumentationGenerator

class IngestionEngine(BaseEngine):
    """Ingestion Engine for SIEMLess v2.0"""

    def __init__(self):
        super().__init__("ingestion")
        self.active_sources = {}
        # Initialize pattern matcher for FREE processing
        self.pattern_matcher = PatternMatcher(
            self.db_connection,
            self.redis_client,
            self.logger
        )

        # Initialize GitHub pattern sync
        self.github_sync = GitHubPatternSync(
            self.redis_client,
            self.db_connection,
            self.logger
        )

        # Initialize parser hot reload
        self.parser_hot_reload = ParserHotReload(
            self.redis_client,
            self.db_connection,
            self.logger
        )

        # Initialize API documentation generator
        self.api_doc_generator = APIDocumentationGenerator(
            self.redis_client,
            self.db_connection,
            self.logger
        )

        self.ingestion_stats = {
            'total_logs': 0,
            'processed_logs': 0,
            'failed_logs': 0,
            'sources_active': 0,
            'patterns_loaded': 0,
            'github_repos': 0
        }
        self.source_configs = {
            'elasticsearch': {
                'enabled': True,
                'batch_size': 1000,
                'poll_interval': 30
            },
            'crowdstrike': {
                'enabled': True,
                'batch_size': 500,
                'poll_interval': 60
            },
            'palo_alto': {
                'enabled': True,
                'batch_size': 100,
                'poll_interval': 120
            },
            'github': {
                'enabled': True,
                'repositories': [],
                'sync_interval': 3600,
                'github_token': None
            }
        }

    def start_engine_tasks(self) -> List[asyncio.Task]:
        """Start ingestion-specific background tasks"""
        tasks = []

        # Initialize new components
        self.logger.info("Initializing GitHub pattern sync...")
        tasks.append(asyncio.create_task(self._initialize_components()))

        # Source monitoring task
        self.logger.info("Creating source monitor task...")
        tasks.append(asyncio.create_task(self._source_monitor_loop()))

        # Data ingestion task
        self.logger.info("Creating ingestion loop task...")
        tasks.append(asyncio.create_task(self._ingestion_loop()))

        # Statistics reporting task
        self.logger.info("Creating stats reporting task...")
        tasks.append(asyncio.create_task(self._stats_reporting_loop()))

        # GitHub sync task
        self.logger.info("Creating GitHub sync task...")
        tasks.append(asyncio.create_task(self._github_sync_loop()))

        # API documentation update task
        self.logger.info("Creating API doc update task...")
        tasks.append(asyncio.create_task(self._api_doc_update_loop()))

        self.logger.info(f"Ingestion Engine started {len(tasks)} tasks")
        return tasks

    async def _initialize_components(self):
        """Initialize new components asynchronously"""
        try:
            # Initialize GitHub sync
            github_token = self.source_configs['github'].get('github_token')
            await self.github_sync.initialize(github_token)

            # Initialize parser hot reload
            await self.parser_hot_reload.initialize()
            parser_status = await self.parser_hot_reload.get_parser_status()
            self.ingestion_stats['patterns_loaded'] = parser_status['total_parsers']

            # Generate initial API documentation
            await self.api_doc_generator.generate_documentation()
            await self.api_doc_generator.export_documentation("json", "api_docs/openapi.json")

            self.logger.info("All components initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")

    def get_subscribed_channels(self) -> List[str]:
        """Return list of Redis channels this engine subscribes to"""
        return [
            'ingestion.start_source',
            'ingestion.stop_source',
            'ingestion.configure_source',
            'ingestion.get_stats',
            'ingestion.sync_github',
            'ingestion.add_github_repo',
            'ingestion.reload_parsers',
            'ingestion.generate_api_docs'
        ]

    async def process_message(self, message: Dict[str, Any]):
        """Process incoming message from message queue"""
        try:
            data = json.loads(message['data'])
            channel = message['channel']

            self.logger.info(f"Processing message from {channel}")

            if channel == 'ingestion.start_source':
                await self._handle_start_source(data)
            elif channel == 'ingestion.stop_source':
                await self._handle_stop_source(data)
            elif channel == 'ingestion.configure_source':
                await self._handle_configure_source(data)
            elif channel == 'ingestion.get_stats':
                await self._handle_get_stats(data)
            elif channel == 'ingestion.sync_github':
                await self._handle_sync_github(data)
            elif channel == 'ingestion.add_github_repo':
                await self._handle_add_github_repo(data)
            elif channel == 'ingestion.reload_parsers':
                await self._handle_reload_parsers(data)
            elif channel == 'ingestion.generate_api_docs':
                await self._handle_generate_api_docs(data)

        except Exception as e:
            self.logger.error(f"Error processing message: {e}")

    async def _handle_start_source(self, data: Dict[str, Any]):
        """Handle source start request"""
        source_id = data.get('source_id')
        source_type = data.get('source_type', 'elasticsearch')

        if source_id and source_id not in self.active_sources:
            self.active_sources[source_id] = {
                'type': source_type,
                'status': 'active',
                'started_at': datetime.utcnow(),
                'logs_processed': 0
            }

            self.ingestion_stats['sources_active'] += 1

            # Publish confirmation
            self.publish_message('ingestion.source_started', {
                'source_id': source_id,
                'status': 'started',
                'timestamp': datetime.utcnow().isoformat()
            })

            self.logger.info(f"Started ingestion source: {source_id}")

    async def _handle_stop_source(self, data: Dict[str, Any]):
        """Handle source stop request"""
        source_id = data.get('source_id')

        if source_id in self.active_sources:
            del self.active_sources[source_id]
            self.ingestion_stats['sources_active'] -= 1

            # Publish confirmation
            self.publish_message('ingestion.source_stopped', {
                'source_id': source_id,
                'status': 'stopped',
                'timestamp': datetime.utcnow().isoformat()
            })

            self.logger.info(f"Stopped ingestion source: {source_id}")

    async def _handle_configure_source(self, data: Dict[str, Any]):
        """Handle source configuration update"""
        source_type = data.get('source_type')
        config = data.get('config', {})

        if source_type in self.source_configs:
            self.source_configs[source_type].update(config)

            # Publish confirmation
            self.publish_message('ingestion.source_configured', {
                'source_type': source_type,
                'config': self.source_configs[source_type],
                'timestamp': datetime.utcnow().isoformat()
            })

            self.logger.info(f"Configured source type: {source_type}")

    async def _handle_get_stats(self, data: Dict[str, Any]):
        """Handle statistics request"""
        stats = {
            'engine': 'ingestion',
            'stats': self.ingestion_stats,
            'active_sources': self.active_sources,
            'source_configs': self.source_configs,
            'timestamp': datetime.utcnow().isoformat()
        }

        # Publish statistics
        self.publish_message('ingestion.stats_response', stats)

    async def _source_monitor_loop(self):
        """Monitor active sources for health and performance"""
        while self.is_running:
            try:
                for source_id, source_info in self.active_sources.items():
                    # Simulate source health check
                    source_info['last_check'] = datetime.utcnow().isoformat()

                    # Update source statistics
                    source_info['logs_processed'] += 50  # Simulate processing

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                self.logger.error(f"Source monitor error: {e}")
                await asyncio.sleep(5)

    async def _ingestion_loop(self):
        """Main ingestion processing loop"""
        try:
            self.logger.info("Ingestion loop started successfully")
            while self.is_running:
                try:
                    if self.active_sources:
                        self.logger.info(f"Processing {len(self.active_sources)} active sources")
                        # Fetch real data from sources
                        logs_batch = await self._fetch_real_data()

                        if logs_batch:
                            self.logger.info(f"Fetched {len(logs_batch)} logs, routing to engines")
                            # Process and route logs
                            await self._process_log_batch(logs_batch)

                            # Update statistics
                            self.ingestion_stats['total_logs'] += len(logs_batch)
                            self.ingestion_stats['processed_logs'] += len(logs_batch)
                        else:
                            self.logger.debug("No logs fetched in this cycle")

                    await asyncio.sleep(10)  # Process every 10 seconds

                except Exception as e:
                    self.logger.error(f"Ingestion loop cycle error: {e}", exc_info=True)
                    await asyncio.sleep(5)
        except Exception as e:
            self.logger.error(f"FATAL: Ingestion loop crashed: {e}", exc_info=True)

    async def _fetch_real_data(self) -> List[Dict[str, Any]]:
        """Fetch REAL data from configured sources"""
        logs = []

        for source_id, source_info in self.active_sources.items():
            source_type = source_info['type']

            # Try to fetch real data first
            try:
                if source_type == 'elasticsearch':
                    # Import and use the v0.2 Elasticsearch implementation
                    from data_sources_v02 import UniversalElasticIngestor

                    config = {
                        'source_name': source_id,
                        'index_pattern': 'logs-*',
                        'timestamp_field': '@timestamp'
                    }

                    ingestor = UniversalElasticIngestor(config, self.logger)

                    # Fetch last 5 minutes of data
                    MAX_LOGS_PER_CYCLE = 500  # Process up to 500 logs per cycle

                    count = 0
                    for log_data in ingestor.fetch_logs():
                        log = {
                            'source_id': source_id,
                            'source_type': source_type,
                            'timestamp': datetime.utcnow().isoformat(),
                            'data': log_data,
                            'log_id': f"{source_id}_{int(time.time())}_{count}"
                        }
                        logs.append(log)
                        count += 1

                        # Yield control periodically to prevent blocking
                        if count % 10 == 0:
                            await asyncio.sleep(0)

                        if count >= MAX_LOGS_PER_CYCLE:
                            self.logger.info(f"Reached cycle limit of {MAX_LOGS_PER_CYCLE} logs")
                            break

                elif source_type == 'database':
                    # Process logs already in our database
                    cursor = self.db_connection.cursor()
                    cursor.execute("""
                        SELECT log_id, source_type, log_data, created_at
                        FROM ingestion_logs
                        WHERE processed = false
                        LIMIT 100
                    """)

                    db_logs = cursor.fetchall()
                    cursor.close()

                    for log_row in db_logs:
                        # log_row contains: log_id, source_type, log_data, created_at
                        log = {
                            'source_id': source_id,
                            'source_type': log_row[1] if log_row[1] else 'database',
                            'timestamp': log_row[3].isoformat() if len(log_row) > 3 and log_row[3] else datetime.utcnow().isoformat(),
                            'data': log_row[2] if len(log_row) > 2 else {},  # log_data
                            'log_id': str(log_row[0]) if log_row[0] else f"db_{int(time.time())}"
                        }
                        logs.append(log)

                    if db_logs:
                        self.logger.info(f"Fetched {len(db_logs)} unprocessed logs from database")

                elif source_type == 'crowdstrike':
                    # Import and use the v0.2 CrowdStrike implementation
                    from data_sources_v02 import CrowdStrikeApiIngestor

                    config = {
                        'source_type': 'detections',
                        'source_name': source_id
                    }

                    ingestor = CrowdStrikeApiIngestor(config, self.logger)

                    # Fetch recent detections
                    detections = ingestor.fetch_detections(limit=1000)  # Fetch up to 1000 detections
                    for idx, detection in enumerate(detections):
                        log = {
                            'source_id': source_id,
                            'source_type': source_type,
                            'timestamp': datetime.utcnow().isoformat(),
                            'data': detection,
                            'log_id': f"{source_id}_{int(time.time())}_{idx}"
                        }
                        logs.append(log)

                else:
                    # Fallback to simulated data for unknown types
                    batch_size = self.source_configs.get(source_type, {}).get('batch_size', 10)
                    for i in range(batch_size):  # Use full batch_size
                        log = {
                            'source_id': source_id,
                            'source_type': source_type,
                            'timestamp': datetime.utcnow().isoformat(),
                            'data': self._generate_sample_log(source_type),
                            'log_id': f"{source_id}_{int(time.time())}_{i}"
                        }
                        logs.append(log)

            except Exception as e:
                self.logger.warning(f"Failed to fetch real data from {source_type}: {e}")
                # Fallback to simulated data on error
                for i in range(20):  # Increased fallback
                    log = {
                        'source_id': source_id,
                        'source_type': source_type,
                        'timestamp': datetime.utcnow().isoformat(),
                        'data': self._generate_sample_log(source_type),
                        'log_id': f"{source_id}_{int(time.time())}_{i}"
                    }
                    logs.append(log)

        return logs

    async def _route_by_use_case(self, enriched_log: Dict, pattern_match: Dict):
        """Route logs to engines based on use case type (v0.7 pattern)"""
        pattern_type = pattern_match.get('pattern_type', 'unknown')

        # Define use case routing rules (v0.7 use case → v2.0 engines)
        USE_CASE_ROUTES = {
            # Security use cases → Intelligence + Context
            'authentication': ['backend', 'contextualization', 'intelligence'],
            'failed_authentication': ['backend', 'contextualization', 'intelligence', 'delivery'],
            'privilege_escalation': ['intelligence', 'contextualization', 'delivery'],
            'malware': ['intelligence', 'contextualization', 'backend', 'delivery'],

            # Network use cases → Context + Backend
            'network_anomaly': ['contextualization', 'backend', 'intelligence'],
            'data_exfiltration': ['intelligence', 'contextualization', 'delivery'],
            'traffic': ['contextualization', 'backend'],  # Extract entities then store

            # Compliance use cases → Backend + Delivery
            'compliance_violation': ['backend', 'delivery'],
            'audit_event': ['backend'],

            # Detection use cases → Full pipeline
            'detection': ['intelligence', 'contextualization', 'backend', 'delivery'],
            'threat_detection': ['intelligence', 'contextualization', 'backend', 'delivery'],

            # System events → Context + Backend
            'system': ['contextualization', 'backend'],
            'performance': ['contextualization', 'backend'],

            # Unknown → Default route
            'unknown': ['backend', 'contextualization']
        }

        # Get routing for this pattern type
        routes = USE_CASE_ROUTES.get(pattern_type, USE_CASE_ROUTES['unknown'])

        # Apply routing
        for route in routes:
            if route == 'backend':
                # Always store processed logs
                self.publish_message('backend.store_processed_log', enriched_log)

            elif route == 'contextualization':
                # Send log to contextualization for entity extraction and enrichment
                self.publish_message('contextualization.process_log', {
                    'log': enriched_log['log'],
                    'pattern_id': pattern_match.get('pattern_id'),
                    'pattern_type': pattern_type,
                    'entity_hints': pattern_match.get('entities', []),  # Pattern's entity suggestions
                    'log_id': enriched_log['log'].get('log_id'),
                    'source_type': enriched_log['log'].get('source_type')
                })

            elif route == 'intelligence' and pattern_type in ['authentication', 'failed_authentication', 'malware', 'threat_detection']:
                # Only send security-relevant patterns to expensive AI
                self.publish_message('intelligence.security_analysis', {
                    'log': enriched_log['log'],
                    'pattern': pattern_match,
                    'priority': 'high' if pattern_type in ['malware', 'threat_detection'] else 'medium'
                })

            elif route == 'delivery' and pattern_type in ['failed_authentication', 'privilege_escalation', 'malware', 'threat_detection', 'compliance_violation']:
                # Create cases for important events
                self.publish_message('delivery.create_case', {
                    'pattern_type': pattern_type,
                    'severity': self._get_severity(pattern_type),
                    'entities': pattern_match.get('entities', []),
                    'log_id': enriched_log['log'].get('log_id')
                })

    def _get_severity(self, pattern_type: str) -> str:
        """Map pattern types to severity levels"""
        severity_map = {
            'malware': 'critical',
            'privilege_escalation': 'high',
            'threat_detection': 'high',
            'failed_authentication': 'medium',
            'compliance_violation': 'medium',
            'network_anomaly': 'low',
            'system': 'info'
        }
        return severity_map.get(pattern_type, 'low')

    def _generate_sample_log(self, source_type: str) -> Dict[str, Any]:
        """Use REAL log samples from v0.2 instead of simulated data"""
        try:
            # Load real log samples
            import os
            sample_file = os.path.join(os.path.dirname(__file__), 'sample_logs.json')

            if os.path.exists(sample_file):
                with open(sample_file, 'r') as f:
                    real_logs = json.load(f)

                # Return a real log sample from the file
                if real_logs and len(real_logs) > 0:
                    sample_index = int(time.time()) % len(real_logs)
                    sample_log = real_logs[sample_index]

                    # Extract the _source field which contains the actual log data
                    if '_source' in sample_log:
                        return sample_log['_source']
                    else:
                        return sample_log

        except Exception as e:
            self.logger.warning(f"Failed to load real samples: {e}, falling back to simplified logs")

        # Fallback to realistic but simplified logs that match expected patterns
        base_timestamp = datetime.utcnow().isoformat()

        if source_type == 'crowdstrike':
            return {
                'observer': {'vendor': 'CrowdStrike', 'product': 'Falcon'},
                'event': {'action': 'detection', 'category': ['malware']},
                'host': {'name': f'workstation-{int(time.time()) % 100}'},
                'user': {'name': f'user{int(time.time()) % 50}'},
                'process': {'name': 'powershell.exe'},
                '@timestamp': base_timestamp
            }
        elif source_type == 'palo_alto':
            return {
                'observer': {'vendor': 'Palo Alto', 'product': 'Firewall'},
                'event': {'action': 'allow', 'category': ['network']},
                'source': {'ip': f"10.0.{int(time.time()) % 255}.{int(time.time()) % 255}"},
                'destination': {'ip': f"192.168.{int(time.time()) % 255}.{int(time.time()) % 255}", 'port': 443},
                'network': {'protocol': 'tcp'},
                '@timestamp': base_timestamp
            }
        else:  # fortinet/elasticsearch or default
            return {
                'observer': {'vendor': 'Fortinet', 'product': 'Fortigate', 'name': 'firewall'},
                'fortinet': {'firewall': {'type': 'traffic', 'action': 'allow'}},
                'source': {'ip': f"192.168.{int(time.time()) % 255}.{int(time.time()) % 255}"},
                'destination': {'ip': f"10.57.{int(time.time()) % 255}.{int(time.time()) % 255}"},
                'event': {'action': 'allow', 'category': ['network']},
                'network': {'protocol': 'tcp'},
                '@timestamp': base_timestamp
            }

    async def _process_log_batch(self, logs: List[Dict[str, Any]]):
        """Process logs: Pattern match first (FREE), AI only for unknowns"""
        try:
            pattern_matched = 0
            unknown_patterns = 0

            for log in logs:
                # STEP 1: Try pattern matching (99.9% of logs)
                # First try hot-reloaded parsers
                log = await self._process_log_with_parsers(log)

                # Then use pattern matcher
                pattern_match = self.pattern_matcher.match(log.get('parsed_data', log.get('data', log)))

                if pattern_match:
                    # Pattern matched! Process for FREE
                    pattern_matched += 1

                    # Prepare enriched log with pattern match info
                    enriched_log = {
                        'log': log,
                        'pattern_id': pattern_match['pattern_id'],
                        'pattern_type': pattern_match['pattern_type'],
                        'entities': pattern_match['entities'],  # Entity hints from pattern
                        'matched_at': datetime.utcnow().isoformat(),
                        'processing_type': 'pattern_match'
                    }

                    # Route to appropriate engines based on pattern type
                    # Contextualization engine will handle entity extraction
                    await self._route_by_use_case(enriched_log, pattern_match)

                else:
                    # Unknown pattern (0.1% of logs) - needs AI analysis
                    unknown_patterns += 1

                    # Buffer unknown patterns to avoid overwhelming AI
                    self.publish_message('intelligence.unknown_pattern', {
                        'log': log,
                        'priority': 'low',  # Don't rush, learn properly
                        'source': log.get('source_id', 'unknown')
                    })

                    # Still store raw for audit
                    self.publish_message('backend.store_raw_log', log)

            # Update stats
            self.ingestion_stats['pattern_matched'] = self.ingestion_stats.get('pattern_matched', 0) + pattern_matched
            self.ingestion_stats['unknown_patterns'] = self.ingestion_stats.get('unknown_patterns', 0) + unknown_patterns

            match_rate = (pattern_matched / len(logs)) * 100 if logs else 0
            self.logger.info(
                f"Processed {len(logs)} logs: "
                f"{pattern_matched} matched ({match_rate:.1f}%), "
                f"{unknown_patterns} unknown"
            )

            # Alert if too many unknowns (indicates we need more patterns)
            if unknown_patterns > 10:
                self.logger.warning(f"High unknown pattern rate: {unknown_patterns} in this batch")

        except Exception as e:
            self.logger.error(f"Error processing log batch: {e}")
            self.ingestion_stats['failed_logs'] += len(logs)

    async def _process_log_batch_old(self, logs: List[Dict[str, Any]]):
        """DEPRECATED - Old method that didn't use pattern matching"""
        # This shows the OLD way - everything went to every engine
        # Now we use _process_log_batch above with smart routing
        pass

    async def _stats_reporting_loop(self):
        """Periodically report ingestion statistics"""
        while self.is_running:
            try:
                # Publish statistics to monitoring
                self.publish_message('monitoring.ingestion_stats', {
                    'engine': 'ingestion',
                    'stats': self.ingestion_stats,
                    'active_sources': len(self.active_sources),
                    'timestamp': datetime.utcnow().isoformat()
                })

                await asyncio.sleep(60)  # Report every minute

            except Exception as e:
                self.logger.error(f"Stats reporting error: {e}")
                await asyncio.sleep(30)

    async def _github_sync_loop(self):
        """Periodically sync patterns from GitHub repositories"""
        while self.is_running:
            try:
                if self.source_configs['github']['enabled']:
                    # Sync all configured repositories
                    results = await self.github_sync.sync_all_repositories()

                    # Update stats
                    for result in results:
                        if result['status'] == 'completed':
                            self.ingestion_stats['github_repos'] += 1
                            self.logger.info(f"GitHub sync completed: {result['repo']} - {result['patterns_deployed']} patterns")

                    # Wait for next sync interval
                    sync_interval = self.source_configs['github']['sync_interval']
                    await asyncio.sleep(sync_interval)
                else:
                    await asyncio.sleep(3600)  # Check every hour if disabled

            except Exception as e:
                self.logger.error(f"GitHub sync error: {e}")
                await asyncio.sleep(300)  # Retry after 5 minutes

    async def _api_doc_update_loop(self):
        """Periodically update API documentation"""
        while self.is_running:
            try:
                # Update API documentation every hour
                await self.api_doc_generator.generate_documentation()
                await self.api_doc_generator.export_documentation("json", "api_docs/openapi.json")
                self.logger.info("API documentation updated")

                await asyncio.sleep(3600)  # Update every hour

            except Exception as e:
                self.logger.error(f"API doc update error: {e}")
                await asyncio.sleep(600)  # Retry after 10 minutes

    async def _handle_sync_github(self, data: Dict[str, Any]):
        """Handle manual GitHub sync request"""
        try:
            repository = data.get('repository')

            if repository:
                # Sync specific repository
                result = await self.github_sync.sync_repository(repository)
            else:
                # Sync all repositories
                results = await self.github_sync.sync_all_repositories()
                result = {'repositories': len(results), 'status': 'completed'}

            # Publish result
            self.publish_message('ingestion.sync_github_result', result)

        except Exception as e:
            self.logger.error(f"GitHub sync handler error: {e}")

    async def _handle_add_github_repo(self, data: Dict[str, Any]):
        """Handle adding a new GitHub repository"""
        try:
            repo_url = data.get('repo_url')
            branch = data.get('branch', 'main')
            sync_interval = data.get('sync_interval', 3600)

            success = await self.github_sync.add_repository(repo_url, branch, sync_interval)

            if success:
                # Update config
                if 'repositories' not in self.source_configs['github']:
                    self.source_configs['github']['repositories'] = []

                self.source_configs['github']['repositories'].append({
                    'url': repo_url,
                    'branch': branch,
                    'sync_interval': sync_interval
                })

                self.logger.info(f"Added GitHub repository: {repo_url}")

                # Publish success
                self.publish_message('ingestion.github_repo_added', {
                    'repo_url': repo_url,
                    'status': 'success'
                })
            else:
                # Publish failure
                self.publish_message('ingestion.github_repo_added', {
                    'repo_url': repo_url,
                    'status': 'failed'
                })

        except Exception as e:
            self.logger.error(f"Add GitHub repo error: {e}")

    async def _handle_reload_parsers(self, data: Dict[str, Any]):
        """Handle parser reload request"""
        try:
            parser_id = data.get('parser_id')

            if parser_id:
                # Reload specific parser
                cursor = self.db_connection.cursor()
                cursor.execute("""
                    SELECT pattern_type, pattern_data
                    FROM pattern_library
                    WHERE pattern_id = %s AND is_active = TRUE
                """, (parser_id,))

                row = cursor.fetchone()
                if row:
                    success = await self.parser_hot_reload.reload_parser(parser_id, row[0], row[1])
                    status = 'success' if success else 'failed'
                else:
                    status = 'not_found'
            else:
                # Reload all parsers
                await self.parser_hot_reload.initialize()
                status = 'success'

            # Get current parser status
            parser_status = await self.parser_hot_reload.get_parser_status()
            self.ingestion_stats['patterns_loaded'] = parser_status['total_parsers']

            # Publish result
            self.publish_message('ingestion.parsers_reloaded', {
                'status': status,
                'total_parsers': parser_status['total_parsers']
            })

        except Exception as e:
            self.logger.error(f"Parser reload error: {e}")

    async def _handle_generate_api_docs(self, data: Dict[str, Any]):
        """Handle API documentation generation request"""
        try:
            format = data.get('format', 'json')
            output_path = data.get('output_path')

            # Generate documentation
            spec = await self.api_doc_generator.generate_documentation()

            # Export if path provided
            if output_path:
                await self.api_doc_generator.export_documentation(format, output_path)

            # Generate SDKs if requested
            if data.get('generate_sdks'):
                python_sdk = await self.api_doc_generator.generate_client_sdk('python')
                js_sdk = await self.api_doc_generator.generate_client_sdk('javascript')

                # Save SDKs
                with open('siemless_client.py', 'w') as f:
                    f.write(python_sdk)
                with open('siemless_client.js', 'w') as f:
                    f.write(js_sdk)

            # Publish result
            self.publish_message('ingestion.api_docs_generated', {
                'status': 'success',
                'endpoints': len(spec.get('paths', {})),
                'schemas': len(spec.get('components', {}).get('schemas', {}))
            })

        except Exception as e:
            self.logger.error(f"API doc generation error: {e}")

    async def _process_log_with_parsers(self, log: Dict) -> Dict:
        """Process log using hot-reloaded parsers"""
        try:
            # Try to parse the log
            log_text = json.dumps(log.get('data', {})) if isinstance(log.get('data'), dict) else str(log.get('data', ''))

            # Use parser hot reload to parse
            parse_result = await self.parser_hot_reload.parse_log(log_text, log.get('source_type'))

            if parse_result['parsed']:
                # Extract entities if parsed
                entities = await self.parser_hot_reload.extract_entities(parse_result['data'])

                # Enrich log with parsed data and entities
                log['parsed_data'] = parse_result['data']
                log['entities'] = entities
                log['parser_used'] = parse_result['parser_used']

                return log
            else:
                # Fall back to pattern matcher if parser fails
                return log

        except Exception as e:
            self.logger.error(f"Parser processing error: {e}")
            return log

if __name__ == "__main__":
    async def main():
        engine = IngestionEngine()
        await engine.start()

    asyncio.run(main())