# Graph Visualization Architecture - Frontend to Backend

## Your Use Case: Alert Investigation with Entity Graph

### User Flow
1. User sees alert with suspicious IP in Dashboard
2. User clicks on IP to investigate
3. **Frontend makes direct HTTP call** to Backend Engine
4. Backend queries Apache AGE graph
5. Returns graph data (nodes/edges)
6. Frontend renders D3.js visualization
7. User clicks to expand node → repeat from step 3

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    USER BROWSER                              │
│  ┌────────────────────────────────────────────────────┐     │
│  │  Frontend (React + D3.js)                          │     │
│  │  - EntityInvestigationGraph.tsx                    │     │
│  │  - Renders nodes/edges                             │     │
│  │  - Click handlers for expand/collapse              │     │
│  └────────────────────────────────────────────────────┘     │
└──────────────────────────┬──────────────────────────────────┘
                           │
                           │ HTTP GET (Direct)
                           │ No Redis involved!
                           ↓
┌─────────────────────────────────────────────────────────────┐
│              Backend Engine (Port 8002)                      │
│  ┌────────────────────────────────────────────────────┐     │
│  │  HTTP Endpoints (aiohttp)                          │     │
│  │  GET /api/graph/explore?entity_id=X&depth=2        │     │
│  │  GET /api/graph/path?source=X&target=Y            │     │
│  │  GET /api/graph/stats                              │     │
│  └────────────────┬───────────────────────────────────┘     │
│                   │                                          │
│                   ↓                                          │
│  ┌────────────────────────────────────────────────────┐     │
│  │  AGEGraphService (Python)                          │     │
│  │  - explore_entity()                                │     │
│  │  - find_path()                                     │     │
│  │  - get_graph_stats()                               │     │
│  └────────────────┬───────────────────────────────────┘     │
└────────────────────┼────────────────────────────────────────┘
                     │
                     │ Cypher Queries
                     ↓
┌─────────────────────────────────────────────────────────────┐
│         PostgreSQL + Apache AGE (Port 5433)                  │
│  ┌────────────────────────────────────────────────────┐     │
│  │  Graph: entity_graph                               │     │
│  │  - 31 nodes (IpAddress, Hostname, Username, etc.) │     │
│  │  - Relationships (user→host, ip→domain, etc.)     │     │
│  └────────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────┘
```

## No Redis Involved for Graph Queries!

Redis is **NOT** in the path for graph visualization because:
- ✅ **Synchronous request/response** - User expects instant graph
- ✅ **Low latency critical** - 50ms vs 500ms with Redis routing
- ✅ **Simple pattern** - HTTP GET is cleaner than pub/sub
- ✅ **Streaming potential** - Can use HTTP streaming for large graphs

## When Redis IS Used (Different Flows)

### Flow 1: Alert Generation (Uses Redis)
```
Ingestion Engine → Redis pub/sub → Contextualization Engine
                → Redis pub/sub → Backend Engine (stores alert)
                → Redis pub/sub → Delivery Engine (sends to frontend)
```

### Flow 2: Graph Query (NO Redis)
```
Frontend → HTTP GET → Backend Engine → PostgreSQL AGE → Response → Frontend
```

## Frontend Implementation

### React Component (No Delivery Engine!)

```typescript
// frontend/src/pages/investigation/EntityInvestigationGraph.tsx

import React, { useState, useEffect } from 'react';
import * as d3 from 'd3';

interface EntityGraphProps {
    entityId: string;  // From alert
    alertId?: string;
}

const EntityInvestigationGraph: React.FC<EntityGraphProps> = ({ entityId, alertId }) => {
    const [graphData, setGraphData] = useState<{ nodes: any[], edges: any[] }>({
        nodes: [],
        edges: []
    });
    const [depth, setDepth] = useState(2);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        fetchGraphData();
    }, [entityId, depth]);

    const fetchGraphData = async () => {
        setLoading(true);
        try {
            // DIRECT HTTP CALL TO BACKEND ENGINE
            // No Delivery Engine, no Redis!
            const response = await fetch(
                `http://localhost:8002/api/graph/explore?entity_id=${entityId}&depth=${depth}`
            );

            const data = await response.json();
            setGraphData(data);
            renderGraph(data);
        } catch (error) {
            console.error('Failed to fetch graph:', error);
        } finally {
            setLoading(false);
        }
    };

    const renderGraph = (data: any) => {
        // D3.js force-directed graph
        const svg = d3.select('#graph-svg');
        const width = 800;
        const height = 600;

        // Clear existing
        svg.selectAll('*').remove();

        // Create force simulation
        const simulation = d3.forceSimulation(data.nodes)
            .force('link', d3.forceLink(data.edges)
                .id((d: any) => d.id)
                .distance(100))
            .force('charge', d3.forceManyBody().strength(-300))
            .force('center', d3.forceCenter(width / 2, height / 2));

        // Draw edges
        const link = svg.append('g')
            .selectAll('line')
            .data(data.edges)
            .enter().append('line')
            .attr('stroke', '#999')
            .attr('stroke-width', 2);

        // Draw nodes
        const node = svg.append('g')
            .selectAll('circle')
            .data(data.nodes)
            .enter().append('circle')
            .attr('r', 10)
            .attr('fill', (d: any) => {
                // Color by entity type
                switch(d.properties.type) {
                    case 'ip_address': return '#e74c3c';
                    case 'hostname': return '#3498db';
                    case 'username': return '#2ecc71';
                    default: return '#95a5a6';
                }
            })
            .call(d3.drag()
                .on('start', dragstarted)
                .on('drag', dragged)
                .on('end', dragended))
            .on('click', handleNodeClick);

        // Node labels
        const label = svg.append('g')
            .selectAll('text')
            .data(data.nodes)
            .enter().append('text')
            .text((d: any) => d.properties.value)
            .attr('font-size', 12)
            .attr('dx', 15)
            .attr('dy', 4);

        // Update positions on simulation tick
        simulation.on('tick', () => {
            link
                .attr('x1', (d: any) => d.source.x)
                .attr('y1', (d: any) => d.source.y)
                .attr('x2', (d: any) => d.target.x)
                .attr('y2', (d: any) => d.target.y);

            node
                .attr('cx', (d: any) => d.x)
                .attr('cy', (d: any) => d.y);

            label
                .attr('x', (d: any) => d.x)
                .attr('y', (d: any) => d.y);
        });

        function dragstarted(event: any, d: any) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event: any, d: any) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event: any, d: any) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }
    };

    const handleNodeClick = (event: any, node: any) => {
        // Expand this node - increase depth centered on it
        // Make new API call for this node
        const newEntityId = node.properties.id;

        fetch(`http://localhost:8002/api/graph/explore?entity_id=${newEntityId}&depth=1`)
            .then(res => res.json())
            .then(newData => {
                // Merge new data with existing graph
                const mergedNodes = [...graphData.nodes, ...newData.nodes];
                const mergedEdges = [...graphData.edges, ...newData.edges];

                // Remove duplicates
                const uniqueNodes = Array.from(
                    new Map(mergedNodes.map(n => [n.id, n])).values()
                );

                setGraphData({
                    nodes: uniqueNodes,
                    edges: mergedEdges
                });
                renderGraph({ nodes: uniqueNodes, edges: mergedEdges });
            });
    };

    return (
        <div className="entity-graph-container">
            <div className="controls">
                <h2>Investigating Entity: {entityId}</h2>
                <div className="depth-controls">
                    <button
                        onClick={() => setDepth(Math.max(1, depth - 1))}
                        disabled={depth <= 1}
                    >
                        Less Detail
                    </button>
                    <span>Depth: {depth} hops</span>
                    <button
                        onClick={() => setDepth(Math.min(5, depth + 1))}
                        disabled={depth >= 5}
                    >
                        More Detail
                    </button>
                </div>
                <div className="stats">
                    <span>Nodes: {graphData.nodes.length}</span>
                    <span>Edges: {graphData.edges.length}</span>
                </div>
                {loading && <div className="loading">Loading graph...</div>}
            </div>
            <svg id="graph-svg" width={800} height={600}></svg>
        </div>
    );
};

export default EntityInvestigationGraph;
```

## Role of Delivery Engine

The Delivery Engine is **NOT** involved in graph queries. Its role is different:

### What Delivery Engine DOES Do:
1. **Alert Delivery** - Push alerts to frontend via WebSocket/SSE
2. **Case Management** - CRUD operations for investigation cases
3. **Dashboard Data** - Aggregate stats, metrics, summaries
4. **User Sessions** - Authentication, authorization
5. **Notifications** - Email, Slack, webhooks for alerts

### What Delivery Engine Does NOT Do:
- ❌ Graph visualization queries (too slow, unnecessary hop)
- ❌ Real-time interactive features (latency sensitive)
- ❌ Direct database queries (Backend Engine's job)

## Complete Data Flow Example

### Scenario: User Investigates Suspicious Login Alert

**Step 1: Alert Arrives (Uses Redis)**
```
1. Log arrives → Ingestion Engine
2. Ingestion → Redis pub/sub → Contextualization Engine (extracts entities)
3. Contextualization → Redis pub/sub → Backend Engine (stores alert + entities)
4. Backend → Redis pub/sub → Delivery Engine
5. Delivery → WebSocket → Frontend (alert notification appears)
```

**Step 2: User Clicks on IP in Alert (NO Redis)**
```
1. Frontend: User clicks IP "************"
2. Frontend → HTTP GET → Backend: /api/graph/explore?entity_id=uuid
3. Backend → Cypher → PostgreSQL AGE
4. PostgreSQL → Returns graph data
5. Backend → HTTP Response → Frontend
6. Frontend: Renders D3.js graph (50ms total)
```

**Step 3: User Expands Node (NO Redis)**
```
1. Frontend: User clicks connected hostname node
2. Frontend → HTTP GET → Backend: /api/graph/explore?entity_id=uuid&depth=1
3. Backend → AGE → Returns
4. Frontend: Merges new nodes into existing graph
```

**Step 4: User Creates Investigation Case (Uses Delivery Engine)**
```
1. Frontend → HTTP POST → Delivery Engine: /api/cases/create
2. Delivery → PostgreSQL (creates case)
3. Delivery → Redis pub/sub (notifies other engines)
4. Delivery → Response → Frontend
```

## Performance Comparison

### Direct HTTP (Current Implementation)
- Frontend → Backend: **~5ms**
- Backend → AGE Query: **~50ms**
- AGE → Response: **~5ms**
- **Total: ~60ms** ✅

### If We Used Redis (DON'T DO THIS)
- Frontend → Delivery: **~5ms**
- Delivery → Redis pub: **~10ms**
- Redis → Backend: **~10ms**
- Backend → AGE Query: **~50ms**
- Backend → Redis pub: **~10ms**
- Redis → Delivery: **~10ms**
- Delivery → Frontend: **~5ms**
- **Total: ~100ms** ❌ (67% slower!)

## Summary

**For your graph visualization use case:**

✅ **DO:** Frontend → Direct HTTP → Backend Engine → AGE
❌ **DON'T:** Frontend → Delivery → Redis → Backend → AGE

**Redis is for:**
- Async workflows (log processing, CTI updates)
- Event notifications (alerts, case updates)
- Engine coordination

**Direct HTTP is for:**
- User queries (graph exploration)
- Interactive features (real-time visualization)
- Dashboard data fetching

Your graph visualization should bypass both Delivery Engine and Redis for maximum performance! 🚀
