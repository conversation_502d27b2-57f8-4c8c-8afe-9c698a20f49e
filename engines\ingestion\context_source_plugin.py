"""
Context Source Plugin System
Allows easy addition of new data sources for investigation context

Architecture:
- Base plugin class defines interface
- Each source (CrowdStrike, SentinelOne, etc.) implements plugin
- Context manager auto-discovers and uses available plugins
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ContextCategory(Enum):
    """Categories of context data"""
    CTI = "cti"                      # Threat Intelligence (INTEL_READ, IOCS_READ)
    ASSET = "asset"                  # Device/Host Information (HOSTS_READ)
    DETECTION = "detection"          # Security Detections (ALERTS_READ, DETECTIONS_READ)
    INCIDENT = "incident"            # Incidents/Cases (INCIDENTS_READ)
    LOG = "log"                      # Raw Log Events (EVENT_STREAMS_READ)
    IDENTITY = "identity"            # User/Identity info (Active Directory, Okta, etc.)
    NETWORK = "network"              # Network flows, firewall logs
    VULNERABILITY = "vulnerability"  # Vuln scan results (Tenable, Qualys, etc.)


@dataclass
class ContextQuery:
    """Standard query format for context requests"""
    query_type: str          # 'ip', 'hostname', 'user', 'file_hash', 'domain'
    query_value: str         # The actual value to search for
    categories: List[ContextCategory]  # Which categories to pull
    time_range: Optional[Dict[str, str]] = None  # {'start': 'now-1h', 'end': 'now'}
    max_results: int = 100
    metadata: Dict[str, Any] = None  # Additional query parameters


@dataclass
class ContextResult:
    """Standard result format from context sources"""
    source_name: str         # 'crowdstrike', 'sentinelone', 'elastic', etc.
    category: ContextCategory
    data: Dict[str, Any]     # The actual data returned
    confidence: float        # 0.0 - 1.0
    timestamp: str
    metadata: Dict[str, Any] = None


class ContextSourcePlugin(ABC):
    """
    Base class for context source plugins

    Each data source (CrowdStrike, SentinelOne, Elastic, etc.) implements this interface
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.get_source_name()}")
        self._enabled = config.get('enabled', False)
        self._credentials_valid = False

    @abstractmethod
    def get_source_name(self) -> str:
        """Return unique source identifier (e.g., 'crowdstrike', 'sentinelone')"""
        pass

    @abstractmethod
    def get_supported_categories(self) -> List[ContextCategory]:
        """Return which context categories this source can provide"""
        pass

    @abstractmethod
    def get_supported_query_types(self) -> List[str]:
        """Return which query types this source supports (e.g., ['ip', 'hostname'])"""
        pass

    @abstractmethod
    async def validate_credentials(self) -> bool:
        """Validate API credentials and connectivity"""
        pass

    @abstractmethod
    async def query_context(self, query: ContextQuery) -> List[ContextResult]:
        """
        Execute context query and return results

        Returns empty list if:
        - Source doesn't support this query type
        - No results found
        - API error occurred
        """
        pass

    def is_enabled(self) -> bool:
        """Check if this source is enabled"""
        return self._enabled

    def supports_query(self, query: ContextQuery) -> bool:
        """Check if this source can handle the given query"""
        if not self.is_enabled():
            return False

        # Check if we support this query type
        if query.query_type not in self.get_supported_query_types():
            return False

        # Check if we support any of the requested categories
        supported_categories = self.get_supported_categories()
        requested_categories = query.categories

        if not any(cat in supported_categories for cat in requested_categories):
            return False

        return True

    async def initialize(self) -> bool:
        """
        Initialize the plugin (validate credentials, setup connections)
        Called once at startup
        """
        if not self.is_enabled():
            self.logger.info(f"{self.get_source_name()} plugin is disabled")
            return False

        self.logger.info(f"Initializing {self.get_source_name()} plugin...")

        try:
            self._credentials_valid = await self.validate_credentials()
            if self._credentials_valid:
                self.logger.info(f"{self.get_source_name()} plugin initialized successfully")
                return True
            else:
                self.logger.warning(f"{self.get_source_name()} credentials validation failed")
                return False
        except Exception as e:
            self.logger.error(f"Failed to initialize {self.get_source_name()}: {e}")
            return False


class ContextSourceManager:
    """
    Manages all context source plugins
    Routes queries to appropriate sources and aggregates results
    """

    def __init__(self):
        self.plugins: Dict[str, ContextSourcePlugin] = {}
        self.logger = logging.getLogger(__name__)

    def register_plugin(self, plugin: ContextSourcePlugin):
        """Register a context source plugin"""
        source_name = plugin.get_source_name()
        self.plugins[source_name] = plugin
        self.logger.info(f"Registered plugin: {source_name}")

    async def initialize_all(self) -> Dict[str, bool]:
        """Initialize all registered plugins"""
        results = {}
        for source_name, plugin in self.plugins.items():
            results[source_name] = await plugin.initialize()
        return results

    def get_available_sources(self) -> List[str]:
        """Get list of enabled and initialized sources"""
        return [
            name for name, plugin in self.plugins.items()
            if plugin.is_enabled() and plugin._credentials_valid
        ]

    async def query_context(self, query: ContextQuery) -> Dict[str, List[ContextResult]]:
        """
        Query all applicable sources and return aggregated results

        Returns:
            Dict mapping source_name -> list of results
        """
        results = {}

        for source_name, plugin in self.plugins.items():
            # Check if this plugin can handle the query
            if not plugin.supports_query(query):
                continue

            try:
                self.logger.info(f"Querying {source_name} for {query.query_type}={query.query_value}")
                source_results = await plugin.query_context(query)

                if source_results:
                    results[source_name] = source_results
                    self.logger.info(f"Got {len(source_results)} results from {source_name}")
                else:
                    self.logger.debug(f"No results from {source_name}")

            except Exception as e:
                self.logger.error(f"Error querying {source_name}: {e}", exc_info=True)
                # Continue with other sources even if one fails

        return results

    def get_plugin_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all plugins for monitoring/debugging"""
        status = {}
        for source_name, plugin in self.plugins.items():
            status[source_name] = {
                'enabled': plugin.is_enabled(),
                'credentials_valid': plugin._credentials_valid,
                'supported_categories': [cat.value for cat in plugin.get_supported_categories()],
                'supported_query_types': plugin.get_supported_query_types()
            }
        return status


# Convenience function for creating queries
def create_context_query(
    query_type: str,
    query_value: str,
    categories: Optional[List[str]] = None,
    time_range: Optional[Dict[str, str]] = None,
    max_results: int = 100
) -> ContextQuery:
    """
    Helper function to create context queries

    Example:
        query = create_context_query('ip', '*************', ['asset', 'detection'])
    """
    if categories is None:
        # Default: Query all categories
        categories = [ContextCategory.ASSET, ContextCategory.DETECTION, ContextCategory.LOG]
    else:
        # Convert string categories to enums
        categories = [ContextCategory(cat) for cat in categories]

    return ContextQuery(
        query_type=query_type,
        query_value=query_value,
        categories=categories,
        time_range=time_range,
        max_results=max_results
    )
