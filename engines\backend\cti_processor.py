"""
CTI Processing and Detection Rule Generation Module
Based on v0.7's proven CTI-to-rule automation
"""
import logging
import json
import hashlib
import re
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field, asdict
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import asyncio
from collections import defaultdict

logger = logging.getLogger(__name__)


class ThreatType(Enum):
    """Types of threat indicators"""
    MALWARE = "malware"
    PHISHING = "phishing"
    C2 = "command_and_control"
    EXPLOIT = "exploit"
    BOTNET = "botnet"
    APT = "apt"
    RANSOMWARE = "ransomware"
    CRYPTOMINER = "cryptominer"


class IOCType(Enum):
    """Types of Indicators of Compromise"""
    IP_ADDRESS = "ip"
    DOMAIN = "domain"
    URL = "url"
    FILE_HASH = "hash"
    EMAIL = "email"
    FILE_NAME = "filename"
    REGISTRY = "registry"
    MUTEX = "mutex"
    USER_AGENT = "user_agent"
    CERTIFICATE = "certificate"


@dataclass
class ThreatIndicator:
    """Represents a threat indicator from CTI feed"""
    indicator_id: str
    ioc_type: IOCType
    ioc_value: str
    threat_type: ThreatType
    confidence: float  # 0.0 to 1.0
    severity: str  # low, medium, high, critical
    source: str
    first_seen: datetime
    last_seen: datetime
    tags: List[str] = field(default_factory=list)
    metadata: Dict = field(default_factory=dict)
    ttl: int = 86400  # Time to live in seconds


@dataclass
class DetectionRule:
    """Represents a detection rule generated from CTI"""
    rule_id: str
    name: str
    description: str
    severity: str
    confidence: float
    logic: str  # The actual detection logic
    platform: str  # SIEM platform
    test_cases: List[Dict] = field(default_factory=list)
    false_positive_rate: float = 0.0
    indicators: List[str] = field(default_factory=list)
    mitre_techniques: List[str] = field(default_factory=list)
    metadata: Dict = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)


class CTIProcessor:
    """
    Processes CTI feeds and generates detection rules
    Based on v0.7's proven implementation with automatic test case generation
    """

    def __init__(self):
        self.indicators: Dict[str, ThreatIndicator] = {}
        self.rules: Dict[str, DetectionRule] = {}
        self.feed_sources = {
            'misp': {'enabled': True, 'priority': 1},
            'otx': {'enabled': True, 'priority': 2},
            'abuse_ch': {'enabled': True, 'priority': 1},
            'emerging_threats': {'enabled': True, 'priority': 3},
            'custom': {'enabled': True, 'priority': 5}
        }
        self.mitre_mapping = self._load_mitre_techniques()
        self.rule_templates = self._load_rule_templates()

    def _load_mitre_techniques(self) -> Dict:
        """Load MITRE ATT&CK technique mappings"""
        return {
            'T1566': {'name': 'Phishing', 'tactics': ['Initial Access']},
            'T1059': {'name': 'Command and Scripting Interpreter', 'tactics': ['Execution']},
            'T1055': {'name': 'Process Injection', 'tactics': ['Defense Evasion', 'Privilege Escalation']},
            'T1003': {'name': 'OS Credential Dumping', 'tactics': ['Credential Access']},
            'T1021': {'name': 'Remote Services', 'tactics': ['Lateral Movement']},
            'T1071': {'name': 'Application Layer Protocol', 'tactics': ['Command and Control']},
            'T1486': {'name': 'Data Encrypted for Impact', 'tactics': ['Impact']},
            'T1190': {'name': 'Exploit Public-Facing Application', 'tactics': ['Initial Access']},
            'T1105': {'name': 'Ingress Tool Transfer', 'tactics': ['Command and Control']},
            'T1070': {'name': 'Indicator Removal', 'tactics': ['Defense Evasion']}
        }

    def _load_rule_templates(self) -> Dict:
        """Load detection rule templates for different scenarios"""
        return {
            'ip_c2': {
                'name': 'Suspicious C2 Communication to {ioc}',
                'description': 'Detected potential command and control communication to known malicious IP',
                'mitre': ['T1071', 'T1105'],
                'logic_template': 'source_ip=internal AND destination_ip={ioc} AND (bytes_out>1000 OR connection_count>10)'
            },
            'domain_dns': {
                'name': 'DNS Query to Malicious Domain {ioc}',
                'description': 'DNS resolution attempt for known malicious domain',
                'mitre': ['T1071'],
                'logic_template': 'event_type=dns AND query_name={ioc}'
            },
            'hash_execution': {
                'name': 'Execution of Malicious File {ioc}',
                'description': 'Known malicious file hash detected in process execution',
                'mitre': ['T1059', 'T1105'],
                'logic_template': 'event_type=process_creation AND file_hash={ioc}'
            },
            'phishing_url': {
                'name': 'Access to Phishing URL {ioc}',
                'description': 'User accessed known phishing URL',
                'mitre': ['T1566', 'T1190'],
                'logic_template': 'event_type=web_request AND url={ioc} AND method=GET'
            },
            'registry_persistence': {
                'name': 'Suspicious Registry Modification {ioc}',
                'description': 'Known malicious registry key modification detected',
                'mitre': ['T1547'],
                'logic_template': 'event_type=registry_modification AND registry_path={ioc}'
            }
        }

    async def process_cti_feed(self, feed_data: List[Dict], source: str) -> Dict:
        """
        Process CTI feed and extract indicators
        Returns statistics about processed indicators
        """
        stats = {
            'total': len(feed_data),
            'processed': 0,
            'new': 0,
            'updated': 0,
            'skipped': 0,
            'errors': 0
        }

        for item in feed_data:
            try:
                indicator = await self._parse_indicator(item, source)
                if indicator:
                    # Check if indicator already exists
                    existing = self.indicators.get(indicator.indicator_id)

                    if existing:
                        # Update if newer
                        if indicator.last_seen > existing.last_seen:
                            self.indicators[indicator.indicator_id] = indicator
                            stats['updated'] += 1
                        else:
                            stats['skipped'] += 1
                    else:
                        # New indicator
                        self.indicators[indicator.indicator_id] = indicator
                        stats['new'] += 1

                    stats['processed'] += 1

                    # Generate detection rule for high-confidence indicators
                    if indicator.confidence >= 0.7:
                        await self._generate_detection_rule(indicator)
            except Exception as e:
                logger.error(f"Error processing CTI item: {e}")
                stats['errors'] += 1

        return stats

    async def _parse_indicator(self, item: Dict, source: str) -> Optional[ThreatIndicator]:
        """Parse CTI feed item into ThreatIndicator"""
        try:
            # Extract IOC value and type
            ioc_value = item.get('indicator', item.get('value', ''))
            ioc_type = self._determine_ioc_type(ioc_value)

            if not ioc_type:
                return None

            # Generate unique ID
            indicator_id = hashlib.sha256(f"{source}:{ioc_value}".encode()).hexdigest()[:16]

            # Extract threat type
            threat_type = self._determine_threat_type(item)

            # Calculate confidence based on source reputation and data quality
            confidence = self._calculate_confidence(item, source)

            # Extract severity
            severity = item.get('severity', 'medium').lower()
            if severity not in ['low', 'medium', 'high', 'critical']:
                severity = 'medium'

            # Parse timestamps
            first_seen = self._parse_timestamp(item.get('first_seen', datetime.now().isoformat()))
            last_seen = self._parse_timestamp(item.get('last_seen', datetime.now().isoformat()))

            # Extract tags
            tags = item.get('tags', [])
            if isinstance(tags, str):
                tags = [tags]

            return ThreatIndicator(
                indicator_id=indicator_id,
                ioc_type=ioc_type,
                ioc_value=ioc_value,
                threat_type=threat_type,
                confidence=confidence,
                severity=severity,
                source=source,
                first_seen=first_seen,
                last_seen=last_seen,
                tags=tags,
                metadata=item.get('metadata', {})
            )

        except Exception as e:
            logger.error(f"Failed to parse indicator: {e}")
            return None

    def _determine_ioc_type(self, value: str) -> Optional[IOCType]:
        """Determine IOC type from value using regex patterns"""
        patterns = {
            IOCType.IP_ADDRESS: r'^(?:\d{1,3}\.){3}\d{1,3}$',
            IOCType.DOMAIN: r'^(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$',
            IOCType.URL: r'^https?://.*',
            IOCType.EMAIL: r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
            IOCType.FILE_HASH: r'^[a-fA-F0-9]{32,64}$',  # MD5, SHA1, SHA256
            IOCType.FILE_NAME: r'^.*\.(exe|dll|scr|bat|ps1|vbs|js)$',
            IOCType.REGISTRY: r'^(HKLM|HKCU|HKCR|HKU|HKCC)\\.*',
            IOCType.USER_AGENT: r'Mozilla|Chrome|Safari|Edge'
        }

        for ioc_type, pattern in patterns.items():
            if re.match(pattern, value, re.IGNORECASE):
                return ioc_type

        return None

    def _determine_threat_type(self, item: Dict) -> ThreatType:
        """Determine threat type from CTI data"""
        # Check explicit threat type
        threat = item.get('threat_type', '').lower()

        threat_mapping = {
            'malware': ThreatType.MALWARE,
            'phishing': ThreatType.PHISHING,
            'c2': ThreatType.C2,
            'command': ThreatType.C2,
            'exploit': ThreatType.EXPLOIT,
            'botnet': ThreatType.BOTNET,
            'apt': ThreatType.APT,
            'ransomware': ThreatType.RANSOMWARE,
            'miner': ThreatType.CRYPTOMINER
        }

        for key, threat_type in threat_mapping.items():
            if key in threat:
                return threat_type

        # Check tags for threat type hints
        tags = item.get('tags', [])
        if isinstance(tags, str):
            tags = [tags]

        for tag in tags:
            tag_lower = tag.lower()
            for key, threat_type in threat_mapping.items():
                if key in tag_lower:
                    return threat_type

        # Default to malware
        return ThreatType.MALWARE

    def _calculate_confidence(self, item: Dict, source: str) -> float:
        """Calculate confidence score based on multiple factors"""
        confidence = 0.5  # Base confidence

        # Source reputation
        source_confidence = {
            'misp': 0.9,
            'otx': 0.8,
            'abuse_ch': 0.85,
            'emerging_threats': 0.7,
            'custom': 0.6
        }
        confidence = source_confidence.get(source, 0.5)

        # Adjust based on data quality
        if item.get('verified', False):
            confidence += 0.1

        # Consider age of intelligence
        last_seen = self._parse_timestamp(item.get('last_seen', datetime.now().isoformat()))
        age_days = (datetime.now() - last_seen).days

        if age_days < 7:
            confidence += 0.1
        elif age_days > 30:
            confidence -= 0.1
        elif age_days > 90:
            confidence -= 0.2

        # Multiple sources increase confidence
        if item.get('source_count', 1) > 1:
            confidence += 0.05 * min(item.get('source_count', 1), 3)

        return max(0.0, min(1.0, confidence))

    def _parse_timestamp(self, timestamp: str) -> datetime:
        """Parse various timestamp formats"""
        formats = [
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d'
        ]

        for fmt in formats:
            try:
                return datetime.strptime(timestamp, fmt)
            except:
                continue

        return datetime.now()

    async def _generate_detection_rule(self, indicator: ThreatIndicator) -> Optional[DetectionRule]:
        """
        Generate detection rule from threat indicator
        Includes automatic test case generation (v0.7 feature)
        """
        # Select appropriate template
        template_key = self._select_rule_template(indicator)
        template = self.rule_templates.get(template_key)

        if not template:
            return None

        # Generate rule ID
        rule_id = f"CTI_{indicator.threat_type.value}_{indicator.indicator_id[:8]}"

        # Format rule name and description
        rule_name = template['name'].format(ioc=indicator.ioc_value[:30])
        description = template['description']

        # Generate detection logic
        logic = template['logic_template'].format(ioc=indicator.ioc_value)

        # Generate test cases automatically
        test_cases = self._generate_test_cases(indicator, logic)

        # Create detection rule
        rule = DetectionRule(
            rule_id=rule_id,
            name=rule_name,
            description=description,
            severity=indicator.severity,
            confidence=indicator.confidence,
            logic=logic,
            platform='generic',  # Will be translated later
            test_cases=test_cases,
            false_positive_rate=self._estimate_false_positive_rate(indicator),
            indicators=[indicator.ioc_value],
            mitre_techniques=template.get('mitre', []),
            metadata={
                'source': indicator.source,
                'threat_type': indicator.threat_type.value,
                'ioc_type': indicator.ioc_type.value
            }
        )

        # Store rule
        self.rules[rule_id] = rule

        logger.info(f"Generated detection rule: {rule_id}")
        return rule

    def _select_rule_template(self, indicator: ThreatIndicator) -> str:
        """Select appropriate rule template based on indicator type"""
        template_mapping = {
            IOCType.IP_ADDRESS: 'ip_c2' if indicator.threat_type == ThreatType.C2 else 'ip_c2',
            IOCType.DOMAIN: 'domain_dns',
            IOCType.FILE_HASH: 'hash_execution',
            IOCType.URL: 'phishing_url' if indicator.threat_type == ThreatType.PHISHING else 'phishing_url',
            IOCType.REGISTRY: 'registry_persistence'
        }

        return template_mapping.get(indicator.ioc_type, 'ip_c2')

    def _generate_test_cases(self, indicator: ThreatIndicator, logic: str) -> List[Dict]:
        """
        Generate test cases for detection rule
        This is a key v0.7 feature for rule validation
        """
        test_cases = []

        # True Positive test case
        true_positive = {
            'name': f'True Positive - Detect {indicator.ioc_type.value}',
            'type': 'true_positive',
            'input': {
                'event_type': self._get_event_type(indicator.ioc_type),
                'ioc_field': self._get_ioc_field(indicator.ioc_type),
                'ioc_value': indicator.ioc_value,
                'additional_fields': self._get_test_fields(indicator)
            },
            'expected': 'alert',
            'description': f'Should trigger on exact match of {indicator.ioc_value}'
        }
        test_cases.append(true_positive)

        # True Negative test case
        true_negative = {
            'name': f'True Negative - Different {indicator.ioc_type.value}',
            'type': 'true_negative',
            'input': {
                'event_type': self._get_event_type(indicator.ioc_type),
                'ioc_field': self._get_ioc_field(indicator.ioc_type),
                'ioc_value': self._generate_benign_value(indicator.ioc_type),
                'additional_fields': self._get_test_fields(indicator)
            },
            'expected': 'no_alert',
            'description': f'Should not trigger on benign {indicator.ioc_type.value}'
        }
        test_cases.append(true_negative)

        # Edge case test
        edge_case = {
            'name': f'Edge Case - Similar {indicator.ioc_type.value}',
            'type': 'edge_case',
            'input': {
                'event_type': self._get_event_type(indicator.ioc_type),
                'ioc_field': self._get_ioc_field(indicator.ioc_type),
                'ioc_value': self._generate_similar_value(indicator.ioc_value),
                'additional_fields': self._get_test_fields(indicator)
            },
            'expected': 'no_alert',
            'description': f'Should not trigger on similar but different {indicator.ioc_type.value}'
        }
        test_cases.append(edge_case)

        return test_cases

    def _get_event_type(self, ioc_type: IOCType) -> str:
        """Get event type for IOC type"""
        event_mapping = {
            IOCType.IP_ADDRESS: 'network_connection',
            IOCType.DOMAIN: 'dns_query',
            IOCType.FILE_HASH: 'process_creation',
            IOCType.URL: 'web_request',
            IOCType.REGISTRY: 'registry_modification'
        }
        return event_mapping.get(ioc_type, 'generic')

    def _get_ioc_field(self, ioc_type: IOCType) -> str:
        """Get field name for IOC type"""
        field_mapping = {
            IOCType.IP_ADDRESS: 'destination_ip',
            IOCType.DOMAIN: 'query_name',
            IOCType.FILE_HASH: 'file_hash',
            IOCType.URL: 'url',
            IOCType.REGISTRY: 'registry_path'
        }
        return field_mapping.get(ioc_type, 'value')

    def _get_test_fields(self, indicator: ThreatIndicator) -> Dict:
        """Get additional test fields based on threat type"""
        if indicator.threat_type == ThreatType.C2:
            return {
                'bytes_out': 5000,
                'connection_count': 15,
                'source_ip': '************'
            }
        elif indicator.threat_type == ThreatType.PHISHING:
            return {
                'method': 'GET',
                'user_agent': 'Mozilla/5.0',
                'referrer': 'https://suspicious.com'
            }
        else:
            return {}

    def _generate_benign_value(self, ioc_type: IOCType) -> str:
        """Generate benign value for testing"""
        benign_values = {
            IOCType.IP_ADDRESS: '*******',
            IOCType.DOMAIN: 'google.com',
            IOCType.FILE_HASH: 'a' * 32,
            IOCType.URL: 'https://example.com',
            IOCType.REGISTRY: 'HKLM\\Software\\Microsoft\\Windows'
        }
        return benign_values.get(ioc_type, 'benign_value')

    def _generate_similar_value(self, value: str) -> str:
        """Generate similar but different value for edge case testing"""
        if re.match(r'^(?:\d{1,3}\.){3}\d{1,3}$', value):
            # IP address - change last octet
            parts = value.split('.')
            parts[-1] = str((int(parts[-1]) + 1) % 256)
            return '.'.join(parts)
        elif '.' in value and not value.startswith('http'):
            # Domain - add subdomain
            return f'www.{value}'
        elif len(value) == 32 or len(value) == 64:
            # Hash - change last character
            return value[:-1] + ('0' if value[-1] != '0' else '1')
        else:
            return value + '_test'

    def _estimate_false_positive_rate(self, indicator: ThreatIndicator) -> float:
        """Estimate false positive rate based on indicator quality"""
        base_rate = 0.05  # 5% base rate

        # Adjust based on confidence
        if indicator.confidence > 0.9:
            base_rate *= 0.5
        elif indicator.confidence < 0.5:
            base_rate *= 2

        # Adjust based on IOC type
        ioc_type_rates = {
            IOCType.IP_ADDRESS: 1.0,  # Normal FP rate
            IOCType.DOMAIN: 0.8,  # Slightly better
            IOCType.FILE_HASH: 0.3,  # Very specific, low FP
            IOCType.URL: 1.2,  # Slightly higher
            IOCType.REGISTRY: 1.5  # Higher due to variations
        }

        rate_multiplier = ioc_type_rates.get(indicator.ioc_type, 1.0)

        return min(1.0, base_rate * rate_multiplier)

    def get_rule_performance_metrics(self, rule_id: str) -> Dict:
        """Get performance metrics for a detection rule"""
        rule = self.rules.get(rule_id)
        if not rule:
            return {}

        return {
            'rule_id': rule_id,
            'name': rule.name,
            'confidence': rule.confidence,
            'false_positive_rate': rule.false_positive_rate,
            'test_coverage': len(rule.test_cases),
            'indicators_count': len(rule.indicators),
            'mitre_coverage': len(rule.mitre_techniques),
            'severity': rule.severity,
            'age_days': (datetime.now() - rule.created_at).days,
            'platform': rule.platform
        }


async def test_cti_processor():
    """Test CTI processing and rule generation"""
    processor = CTIProcessor()

    # Sample CTI feed data
    cti_feed = [
        {
            'indicator': '*************',
            'threat_type': 'c2',
            'severity': 'high',
            'confidence': 0.85,
            'tags': ['emotet', 'banking-trojan'],
            'first_seen': '2024-01-15T10:00:00Z',
            'last_seen': '2024-01-20T15:30:00Z',
            'verified': True
        },
        {
            'value': 'malicious-domain.com',
            'threat_type': 'phishing',
            'severity': 'critical',
            'tags': ['phishing', 'credential-theft'],
            'source_count': 3
        },
        {
            'indicator': 'd41d8cd98f00b204e9800998ecf8427e',
            'threat_type': 'malware',
            'severity': 'high',
            'tags': ['ransomware', 'lockbit'],
            'verified': True
        }
    ]

    print("CTI Processing Test")
    print("=" * 60)

    # Process CTI feed
    stats = await processor.process_cti_feed(cti_feed, 'misp')

    print(f"\nProcessing Statistics:")
    print(f"  Total: {stats['total']}")
    print(f"  Processed: {stats['processed']}")
    print(f"  New: {stats['new']}")
    print(f"  Updated: {stats['updated']}")
    print(f"  Skipped: {stats['skipped']}")

    # Display generated rules
    print(f"\n\nGenerated Detection Rules:")
    print("-" * 40)

    for rule_id, rule in processor.rules.items():
        print(f"\nRule: {rule.name}")
        print(f"  ID: {rule_id}")
        print(f"  Severity: {rule.severity}")
        print(f"  Confidence: {rule.confidence:.2%}")
        print(f"  Logic: {rule.logic}")
        print(f"  Test Cases: {len(rule.test_cases)}")
        print(f"  MITRE Techniques: {', '.join(rule.mitre_techniques)}")

        # Show test cases
        print(f"  Test Cases:")
        for tc in rule.test_cases[:2]:  # Show first 2 test cases
            print(f"    - {tc['name']}: {tc['expected']}")


if __name__ == "__main__":
    asyncio.run(test_cti_processor())