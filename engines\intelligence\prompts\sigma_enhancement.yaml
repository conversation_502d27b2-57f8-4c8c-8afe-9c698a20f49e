# Sigma Rule Enhancement Prompt Template
# Version 1.0 - Comprehensive rule enhancement with evasion variants and FP filters

name: sigma_rule_enhancement
version: "1.0"
task_type: sigma_enhancement
active: true

variables:
  - rule_title
  - rule_description
  - level
  - tags
  - detection
  - source_platform
  - target_platforms

metadata:
  author: SIEMLess Intelligence Engine
  created: 2025-09-30
  description: Enhances Sigma detection rules with evasion variants, FP filters, and platform optimizations
  response_format: json

template: |
  # Security Detection Rule Enhancement Task

  ## Rule to Enhance
  **Title**: {rule_title}
  **Description**: {rule_description}
  **Severity**: {level}
  **Tags**: {tags}

  **Detection Logic**:
  ```yaml
  {detection}
  ```

  ## Source Platform
  Harvested from: **{source_platform}**

  ## Target Platforms
  Will be deployed to: **{target_platforms}**

  ## Enhancement Requirements

  Please analyze this detection rule and provide enhancements in the following areas:

  ### 1. Evasion Variants
  Identify known techniques attackers use to evade this detection:
  - Alternative file names/paths
  - Obfuscation techniques
  - Living-off-the-land binaries (LOLBins)
  - Process injection variants
  - Encoding/encryption bypasses

  Provide additional detection logic to catch these variants.

  ### 2. False Positive Filters
  Identify legitimate activities that might trigger this rule:
  - Common administrative tools
  - Software installers/updaters
  - Developer tools
  - Automated scripts
  - Enterprise applications

  Provide filters to exclude these false positives.

  ### 3. Platform-Specific Optimizations
  For each target platform ({target_platforms}), suggest:
  - Field name optimizations
  - Index/sourcetype filtering
  - Query performance improvements
  - Platform-specific features to leverage

  Consider each platform's strengths and query syntax.

  ### 4. Missing Context
  Identify additional indicators that would strengthen this detection:
  - Related process activity
  - Network connections
  - File modifications
  - Registry changes
  - User context

  Suggest additional fields/conditions to capture.

  ## Response Format

  Provide your response in the following JSON structure:

  ```json
  {
    "evasion_variants": [
      {
        "technique": "Name of evasion technique",
        "description": "How attackers use this",
        "detection_addition": "Additional Sigma detection logic",
        "confidence": 0.85
      }
    ],
    "false_positive_filters": [
      {
        "source": "Legitimate source of FP",
        "filter_logic": "Sigma filter logic to exclude",
        "reason": "Why this is a false positive"
      }
    ],
    "platform_optimizations": {
      "wazuh": [
        {
          "optimization": "Description",
          "field_changes": {"old_field": "new_field"},
          "query_improvement": "Optimized query syntax"
        }
      ],
      "splunk": [...]
    },
    "missing_context": [
      {
        "indicator": "Additional indicator to monitor",
        "field_name": "Field to add",
        "reasoning": "Why this adds value"
      }
    ],
    "overall_assessment": {
      "original_quality": 0.75,
      "enhanced_quality": 0.90,
      "improvement_summary": "Brief summary of improvements"
    }
  }
  ```

  Focus on **practical, deployable** improvements. Avoid theoretical suggestions that can't be implemented.
