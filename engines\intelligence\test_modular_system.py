"""
Test the new modular Intelligence Engine architecture
"""

import sys
import os
import logging
import asyncio

# Add paths
sys.path.append(os.path.dirname(__file__))

from core.model_registry import ModelRegistry
from core.credential_manager import CredentialManager
from providers.base_provider import ProviderFactory

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_model_registry():
    """Test Model Registry"""
    print("\n" + "=" * 80)
    print("TEST 1: Model Registry")
    print("=" * 80)

    config_path = os.path.join(os.path.dirname(__file__), 'config', 'ai_models.yaml')

    try:
        registry = ModelRegistry(config_path, logger)

        print(f"\n[OK] Registry initialized: {registry}")
        print(f"[OK] Stats: {registry.get_model_stats()}")

        # Test getting models
        print("\n--- Testing Model Selection ---")

        # Get by ID
        sonnet = registry.get_model('claude-sonnet-4')
        print(f"[OK] Got model by ID: {sonnet.name} (quality={sonnet.quality_score})")

        # Get by alias
        default = registry.get_model('default')
        print(f"[OK] Got model by alias 'default': {default.name}")

        # Get for task
        sigma_model = registry.get_model_for_task('sigma_enhancement')
        print(f"[OK] Got model for sigma_enhancement: {sigma_model.name}")

        # List models
        free_models = registry.list_available_models(tier='free')
        print(f"[OK] Found {len(free_models)} free models")

        production_models = registry.list_available_models(tier='production')
        print(f"[OK] Found {len(production_models)} production models")

        print("\n[PASS] Model Registry")
        return True

    except Exception as e:
        print(f"\n[FAIL] Model Registry - {e}")
        import traceback
        traceback.print_exc()
        return False


def test_credential_manager():
    """Test Credential Manager"""
    print("\n" + "=" * 80)
    print("TEST 2: Credential Manager")
    print("=" * 80)

    try:
        cred_mgr = CredentialManager(logger=logger)

        print(f"\n[OK] Credential Manager initialized: {cred_mgr}")
        print(f"[OK] Providers available: {cred_mgr.list_providers()}")

        # Test getting credentials
        print("\n--- Testing Credential Retrieval ---")

        for provider in cred_mgr.list_providers():
            key = cred_mgr.get_credential(provider)
            masked = cred_mgr.mask_key(key)
            print(f"[OK] {provider}: {masked}")

        # Test usage stats
        stats = cred_mgr.get_usage_stats()
        print(f"\n[OK] Usage stats: {stats}")

        print("\n[PASS] Credential Manager: PASSED")
        return True

    except Exception as e:
        print(f"\n[FAIL] Credential Manager: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integration():
    """Test integration of Model Registry + Credential Manager"""
    print("\n" + "=" * 80)
    print("TEST 3: Integration")
    print("=" * 80)

    try:
        # Initialize components
        config_path = os.path.join(os.path.dirname(__file__), 'config', 'ai_models.yaml')
        registry = ModelRegistry(config_path, logger)
        cred_mgr = CredentialManager(logger=logger)

        print(f"\n[OK] Components initialized")

        # Get model for task
        model = registry.get_model_for_task('sigma_enhancement', max_cost=0.01)
        print(f"[OK] Selected model: {model.name} (provider={model.provider})")

        # Check credentials exist for this model's provider
        has_cred = cred_mgr.has_credentials(model.provider)
        print(f"[OK] Credentials available for {model.provider}: {has_cred}")

        if has_cred:
            key = cred_mgr.get_credential(model.provider)
            masked = cred_mgr.mask_key(key)
            print(f"[OK] Credential: {masked}")

        # Test hot-reload simulation
        print("\n--- Testing Hot-Reload ---")
        print("[OK] Current model for 'default' alias:", registry.get_model('default').name)

        # Reload config
        success = registry.reload_config()
        print(f"[OK] Config reload: {'SUCCESS' if success else 'FAILED'}")

        print("\n[PASS] Integration: PASSED")
        return True

    except Exception as e:
        print(f"\n[FAIL] Integration: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("\n" + "=" * 80)
    print("TESTING MODULAR INTELLIGENCE ENGINE ARCHITECTURE")
    print("=" * 80)

    results = []

    # Run tests
    results.append(("Model Registry", test_model_registry()))
    results.append(("Credential Manager", test_credential_manager()))
    results.append(("Integration", test_integration()))

    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)

    for test_name, passed in results:
        status = "[PASS] PASS" if passed else "[FAIL] FAIL"
        print(f"{test_name:<30} {status}")

    total = len(results)
    passed = sum(1 for _, p in results if p)
    print(f"\nTotal: {passed}/{total} tests passed")

    if passed == total:
        print("\n[SUCCESS] ALL TESTS PASSED!")
        return 0
    else:
        print(f"\n[WARNING]  {total - passed} test(s) failed")
        return 1


if __name__ == "__main__":
    exit(main())
