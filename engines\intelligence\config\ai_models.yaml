# AI Model Configuration
# Hot-reloadable - changes take effect without restart
# Last Updated: 2025-09-30
# Based on Sigma Enhancement Test Results

models:
  # ============================================================================
  # GOOGLE AI MODELS
  # ============================================================================

  gemma-27b:
    provider: google
    model_name: gemma-3-27b-it
    tier: free
    cost_per_1k_tokens: 0.0
    cost_per_request: 0.0
    quality_score: 75
    speed_score: 95
    max_tokens: 8192
    capabilities:
      - text
      - json
      - reasoning
    recommended_for:
      - testing
      - development
      - simple_analysis
    test_results:
      sigma_enhancement:
        evasion_variants: 4.0
        fp_filters: 3.7
        missing_context: 4.0
        quality_improvement: 0.15
        avg_response_time: 27.27
    notes: "FREE tier model. Fastest response time. Consistent quality. Best for development."

  gemini-flash:
    provider: google
    model_name: gemini-2.5-flash
    tier: low_cost
    cost_per_1k_tokens: 0.002
    cost_per_request: 0.002
    quality_score: 60
    speed_score: 40
    max_tokens: 32768
    capabilities:
      - text
      - json
      - code
    recommended_for: []  # Not recommended
    test_results:
      sigma_enhancement:
        evasion_variants: 1.7
        fp_filters: 1.7
        missing_context: 4.0
        quality_improvement: 0.17
        avg_response_time: 125.84
    warnings:
      - unreliable_json_parsing
      - slow_response_time
      - safety_filter_issues
    notes: "UNRELIABLE - parsing errors and slowest model. Not recommended for production."

  gemini-pro:
    provider: google
    model_name: gemini-2.5-pro
    tier: high_quality
    cost_per_1k_tokens: 0.015
    cost_per_request: 0.015
    quality_score: 85
    speed_score: 70
    max_tokens: 32768
    capabilities:
      - text
      - json
      - code
      - reasoning
    recommended_for:
      - complex_analysis
    test_results:
      sigma_enhancement:
        evasion_variants: 3.7
        fp_filters: 3.0
        missing_context: 4.0
        quality_improvement: 0.25
        avg_response_time: 67.58
    notes: "Overpriced vs Claude Sonnet 4. Similar quality but almost 2x more expensive."

  # ============================================================================
  # ANTHROPIC MODELS (CLAUDE)
  # ============================================================================

  claude-sonnet-4:
    provider: anthropic
    model_name: claude-sonnet-4-20250514
    tier: production
    cost_per_1k_tokens: 0.008
    cost_per_request: 0.008
    quality_score: 92
    speed_score: 85
    max_tokens: 200000
    capabilities:
      - text
      - json
      - reasoning
      - code
      - security_analysis
    recommended_for:
      - production
      - sigma_enhancement
      - threat_analysis
      - pattern_validation
    test_results:
      sigma_enhancement:
        evasion_variants: 4.7
        fp_filters: 4.3
        missing_context: 6.3
        quality_improvement: 0.29
        avg_response_time: 46.37
    notes: "BEST OVERALL. Highest quality, most enhancements, faster than Gemini Pro, cheaper. RECOMMENDED for production."

  claude-opus-4:
    provider: anthropic
    model_name: claude-opus-4-20250514
    tier: premium
    cost_per_1k_tokens: 0.020
    cost_per_request: 0.020
    quality_score: 95
    speed_score: 60
    max_tokens: 200000
    capabilities:
      - text
      - json
      - reasoning
      - code
      - security_analysis
      - complex_reasoning
    recommended_for:
      - critical_analysis
      - complex_threat_hunting
    notes: "Premium model. Use only for critical tasks requiring highest quality."

  # ============================================================================
  # OPENAI MODELS
  # ============================================================================

  gpt-4-turbo:
    provider: openai
    model_name: gpt-4-turbo-preview
    tier: high_quality
    cost_per_1k_tokens: 0.018
    cost_per_request: 0.018
    quality_score: 90
    speed_score: 70
    max_tokens: 128000
    capabilities:
      - text
      - json
      - code
      - reasoning
    recommended_for:
      - fallback
    notes: "Fallback option if Anthropic unavailable. Good quality but expensive."

  gpt-5:
    provider: openai
    model_name: gpt-5
    tier: latest
    cost_per_1k_tokens: 0.025
    cost_per_request: 0.025
    quality_score: 97
    speed_score: 75
    max_tokens: 128000
    capabilities:
      - text
      - json
      - code
      - reasoning
      - advanced_analysis
    recommended_for:
      - experimental
    notes: "Latest OpenAI model. Very expensive. Use sparingly."

  # ============================================================================
  # LOCAL MODELS (OLLAMA)
  # ============================================================================

  llama-3-70b:
    provider: ollama
    model_name: llama3:70b
    tier: local
    cost_per_1k_tokens: 0.0
    cost_per_request: 0.0
    quality_score: 80
    speed_score: 50
    max_tokens: 8192
    capabilities:
      - text
      - json
      - code
    recommended_for:
      - offline_analysis
      - sensitive_data
    notes: "Local model. Free but requires local Ollama setup. Good for sensitive data that can't leave premises."

# ============================================================================
# MODEL ALIASES - Easy switching without changing code
# ============================================================================

aliases:
  default: claude-sonnet-4
  production: claude-sonnet-4
  development: gemma-27b
  testing: gemma-27b
  best_free: gemma-27b
  best_quality: gpt-5
  best_value: claude-sonnet-4
  offline: llama-3-70b

  # Use case specific aliases
  sigma_enhancement: claude-sonnet-4
  log_parsing: gemma-27b
  threat_analysis: claude-sonnet-4
  pattern_validation: claude-sonnet-4

# ============================================================================
# TASK-BASED MODEL SELECTION
# ============================================================================

task_requirements:
  sigma_enhancement:
    min_quality_score: 85
    max_cost_per_request: 0.01
    required_capabilities:
      - json
      - security_analysis
    preferred_models:
      - claude-sonnet-4
      - gemini-pro
    fallback_models:
      - gemma-27b

  log_parsing:
    min_quality_score: 70
    max_cost_per_request: 0.005
    required_capabilities:
      - text
      - json
    preferred_models:
      - gemma-27b
      - claude-sonnet-4

  threat_analysis:
    min_quality_score: 90
    max_cost_per_request: 0.015
    required_capabilities:
      - reasoning
      - security_analysis
    preferred_models:
      - claude-sonnet-4
      - claude-opus-4

  pattern_validation:
    min_quality_score: 85
    max_cost_per_request: 0.01
    required_capabilities:
      - reasoning
      - json
    preferred_models:
      - claude-sonnet-4
      - gpt-4-turbo

# ============================================================================
# HOT-RELOAD SETTINGS
# ============================================================================

hot_reload:
  enabled: true
  watch_interval: 5  # Check for changes every 5 seconds
  validate_on_reload: true
  rollback_on_error: true
