import React from 'react'

export const RelationshipMapper: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Relationship Mapper</h1>
      <div className="bg-white rounded-lg p-6 shadow">
        <p className="text-gray-600">Entity relationship visualization coming soon.</p>
      </div>
    </div>
  )
}

export const TimelineAnalysis: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Timeline Analysis</h1>
      <div className="bg-white rounded-lg p-6 shadow">
        <p className="text-gray-600">Event timeline analysis coming soon.</p>
      </div>
    </div>
  )
}

export const AIGuide: React.FC = () => {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">AI Investigation Guide</h1>
      <div className="bg-white rounded-lg p-6 shadow">
        <p className="text-gray-600">AI-powered investigation assistant coming soon.</p>
      </div>
    </div>
  )
}