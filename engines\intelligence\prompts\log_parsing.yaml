# Log Parsing Prompt Template
# Version 1.0 - Extract entities and relationships from unknown log formats

name: intelligent_log_parsing
version: "1.0"
task_type: log_parsing
active: true

variables:
  - log_sample
  - log_source
  - vendor

metadata:
  author: SIEMLess Intelligence Engine
  created: 2025-09-30
  description: Parses unknown log formats and extracts security-relevant entities
  response_format: json

template: |
  # Intelligent Log Parsing Task

  ## Log Sample
  **Source**: {log_source}
  **Vendor**: {vendor}

  ```
  {log_sample}
  ```

  ## Task
  Analyze this log entry and extract security-relevant information.

  ## Required Analysis

  ### 1. Log Format Identification
  - Identify the log format (JSON, CEF, Syslog, CSV, custom, etc.)
  - Identify the timestamp format
  - Identify the structure pattern

  ### 2. Entity Extraction
  Extract all security-relevant entities:
  - **IP Addresses**: Source and destination IPs
  - **Users**: Usernames, email addresses, user IDs
  - **Hosts**: Hostnames, computer names, device names
  - **Processes**: Process names, command lines, PIDs
  - **Files**: File paths, file names, hashes
  - **Network**: Ports, protocols, URLs, domains
  - **Actions**: Events, operations, methods
  - **Results**: Status codes, outcomes, errors

  ### 3. Relationship Mapping
  Identify relationships between entities:
  - user -> host (who accessed what)
  - process -> file (what accessed what)
  - ip -> port (network connections)
  - user -> action (who did what)

  ### 4. Security Context
  - Event type (authentication, network, process, file, etc.)
  - Severity/Risk level (low, medium, high, critical)
  - MITRE ATT&CK techniques (if applicable)
  - Indicators of compromise (if any)

  ### 5. Parsing Pattern
  Provide a reusable parsing pattern for similar logs from this source.

  ## Response Format

  ```json
  {
    "log_format": {
      "type": "json|cef|syslog|custom",
      "timestamp_format": "%Y-%m-%d %H:%M:%S",
      "structure": "Description of structure"
    },
    "entities": {
      "ips": [{"value": "***********", "type": "source|destination"}],
      "users": [{"value": "admin", "domain": "CORP"}],
      "hosts": [{"value": "DC01", "fqdn": "dc01.corp.local"}],
      "processes": [{"name": "cmd.exe", "path": "C:\\Windows\\System32\\cmd.exe", "pid": 1234}],
      "files": [{"path": "C:\\temp\\malware.exe", "hash": "abc123"}],
      "network": [{"port": 445, "protocol": "SMB", "direction": "outbound"}],
      "actions": [{"action": "login", "result": "success"}]
    },
    "relationships": [
      {
        "from": {"type": "user", "value": "admin"},
        "to": {"type": "host", "value": "DC01"},
        "relationship": "logged_into"
      }
    ],
    "security_context": {
      "event_type": "authentication",
      "severity": "medium",
      "mitre_techniques": ["T1078"],
      "iocs": [],
      "risk_score": 45
    },
    "parsing_pattern": {
      "regex": "Pattern to extract key fields",
      "field_mappings": {
        "src_ip": "source.ip",
        "dst_ip": "destination.ip"
      },
      "description": "How to parse similar logs"
    }
  }
  ```

  Focus on **actionable security intelligence**. Extract only meaningful entities.
