"""
HTTP API handlers for Investigation Dashboard
"""

from aiohttp import web
import json
import sys
import os
from typing import Dict

# Add parent directories to path for cross-engine imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from backend.historical_context_manager import HistoricalContextManager
    from investigation_evidence_logger import InvestigationEvidenceLogger
except ImportError:
    # Will be available in Docker container environment
    HistoricalContextManager = None
    InvestigationEvidenceLogger = None


class InvestigationHTTPHandlers:
    """REST API for investigation management"""

    def __init__(self, investigation_engine, logger, db_connection=None, redis_client=None, siem_configs=None):
        self.engine = investigation_engine
        self.logger = logger
        self.db = db_connection
        self.redis = redis_client

        # Initialize context manager and evidence logger if available
        if HistoricalContextManager and db_connection and redis_client:
            self.context_manager = HistoricalContextManager(db_connection, redis_client)
        else:
            self.context_manager = None

        if InvestigationEvidenceLogger and db_connection and redis_client and siem_configs:
            self.evidence_logger = InvestigationEvidenceLogger(db_connection, redis_client, siem_configs)
        else:
            self.evidence_logger = None

    def get_routes(self):
        """Get all HTTP routes"""
        return [
            web.post('/api/v1/investigations', self.create_investigation),
            web.get('/api/v1/investigations', self.list_investigations),
            web.get('/api/v1/investigations/{investigation_id}', self.get_investigation),
            web.patch('/api/v1/investigations/{investigation_id}', self.update_investigation),
            web.post('/api/v1/investigations/{investigation_id}/assign', self.assign_investigation),
            web.post('/api/v1/investigations/{investigation_id}/close', self.close_investigation),
            web.post('/api/v1/investigations/{investigation_id}/notes', self.add_note),
            web.post('/api/v1/investigations/{investigation_id}/evidence', self.add_evidence),
            web.get('/api/v1/investigations/stats', self.get_stats),
            # Auto-enrichment endpoints (TODO: implement these methods)
            # web.post('/api/v1/investigations/{investigation_id}/enrich', self.enrich_investigation),
            # web.get('/api/v1/investigations/{investigation_id}/timeline', self.get_timeline),
            # web.post('/api/v1/investigations/{investigation_id}/collect_evidence', self.collect_evidence),
        ]

    async def create_investigation(self, request: web.Request) -> web.Response:
        """
        POST /api/v1/investigations

        Create new investigation

        Body:
        {
            "title": "Suspicious PowerShell Activity",
            "severity": "high",
            "alert_id": "alert-123",
            "entities": {
                "ips": ["*************"],
                "users": ["admin"],
                "hosts": ["WORKSTATION-01"]
            },
            "mitre_techniques": ["T1059.001"]
        }

        Returns:
        {
            "investigation_id": "uuid",
            "title": "...",
            "status": "open",
            "risk_score": 75,
            "created_at": "2025-10-02T..."
        }
        """
        try:
            data = await request.json()

            # Convert to alert format for investigation engine
            alert_data = {
                'alert_id': data.get('alert_id', ''),
                'title': data.get('title'),
                'severity': data.get('severity', 'medium'),
                'entities': data.get('entities', {}),
                'mitre_techniques': data.get('mitre_techniques', []),
                'timestamp': data.get('timestamp'),
                'raw_alert': data.get('raw_data', {})
            }

            investigation = await self.engine.create_investigation_from_alert(alert_data)

            return web.json_response({
                'investigation_id': investigation.id,
                'title': investigation.title,
                'severity': investigation.severity,
                'status': investigation.status,
                'risk_score': investigation.risk_score,
                'created_at': investigation.created_at.isoformat()
            }, status=201)

        except Exception as e:
            self.logger.error(f"Error creating investigation: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def list_investigations(self, request: web.Request) -> web.Response:
        """
        GET /api/v1/investigations?status=open&limit=50

        List investigations

        Query params:
        - status: Filter by status (open, investigating, closed)
        - limit: Max results (default 50)

        Returns:
        {
            "investigations": [
                {
                    "id": "uuid",
                    "title": "...",
                    "severity": "high",
                    "status": "open",
                    "risk_score": 75,
                    "created_at": "...",
                    "updated_at": "..."
                }
            ],
            "count": 10
        }
        """
        try:
            status = request.query.get('status')
            limit = int(request.query.get('limit', 50))

            investigations = await self.engine.list_investigations(status, limit)

            return web.json_response({
                'investigations': investigations,
                'count': len(investigations)
            })

        except Exception as e:
            self.logger.error(f"Error listing investigations: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def get_investigation(self, request: web.Request) -> web.Response:
        """
        GET /api/v1/investigations/{investigation_id}

        Get full investigation details

        Returns:
        {
            "id": "uuid",
            "title": "...",
            "severity": "high",
            "status": "open",
            "created_at": "...",
            "updated_at": "...",
            "alert_ids": ["alert-123"],
            "entities": {...},
            "mitre_techniques": ["T1059.001"],
            "threat_intel": [...],
            "timeline": [...],
            "risk_score": 75,
            "assignee": null,
            "tags": []
        }
        """
        try:
            investigation_id = request.match_info['investigation_id']

            investigation = await self.engine.get_investigation(investigation_id)

            if not investigation:
                return web.json_response(
                    {'error': 'Investigation not found'},
                    status=404
                )

            # Convert to dict
            inv_dict = {
                'id': investigation.id,
                'title': investigation.title,
                'severity': investigation.severity,
                'status': investigation.status,
                'created_at': investigation.created_at.isoformat(),
                'updated_at': investigation.updated_at.isoformat(),
                'alert_ids': investigation.alert_ids,
                'entities': investigation.entities,
                'mitre_techniques': investigation.mitre_techniques,
                'threat_intel': investigation.threat_intel,
                'timeline': investigation.timeline,
                'risk_score': investigation.risk_score,
                'assignee': investigation.assignee,
                'tags': investigation.tags
            }

            return web.json_response(inv_dict)

        except Exception as e:
            self.logger.error(f"Error getting investigation: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def update_investigation(self, request: web.Request) -> web.Response:
        """
        PATCH /api/v1/investigations/{investigation_id}

        Update investigation

        Body:
        {
            "status": "investigating",
            "tags": ["phishing", "credential_theft"],
            "notes": "Additional analysis..."
        }

        Returns:
        {
            "success": true,
            "investigation_id": "uuid",
            "updated_at": "..."
        }
        """
        try:
            investigation_id = request.match_info['investigation_id']
            updates = await request.json()

            success = await self.engine.update_investigation(investigation_id, updates)

            if not success:
                return web.json_response(
                    {'error': 'Investigation not found or update failed'},
                    status=404
                )

            return web.json_response({
                'success': True,
                'investigation_id': investigation_id,
                'updated_at': updates.get('updated_at')
            })

        except Exception as e:
            self.logger.error(f"Error updating investigation: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def assign_investigation(self, request: web.Request) -> web.Response:
        """
        POST /api/v1/investigations/{investigation_id}/assign

        Assign investigation to analyst

        Body:
        {
            "assignee": "<EMAIL>"
        }

        Returns:
        {
            "success": true,
            "investigation_id": "uuid",
            "assignee": "<EMAIL>"
        }
        """
        try:
            investigation_id = request.match_info['investigation_id']
            data = await request.json()
            assignee = data.get('assignee')

            if not assignee:
                return web.json_response(
                    {'error': 'assignee is required'},
                    status=400
                )

            success = await self.engine.assign_investigation(investigation_id, assignee)

            if not success:
                return web.json_response(
                    {'error': 'Investigation not found'},
                    status=404
                )

            return web.json_response({
                'success': True,
                'investigation_id': investigation_id,
                'assignee': assignee
            })

        except Exception as e:
            self.logger.error(f"Error assigning investigation: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def close_investigation(self, request: web.Request) -> web.Response:
        """
        POST /api/v1/investigations/{investigation_id}/close

        Close investigation

        Body:
        {
            "resolution": "false_positive",  # or "true_positive", "benign", etc.
            "notes": "After investigation..."
        }

        Returns:
        {
            "success": true,
            "investigation_id": "uuid",
            "status": "closed"
        }
        """
        try:
            investigation_id = request.match_info['investigation_id']
            data = await request.json()
            resolution = data.get('resolution', 'closed')

            success = await self.engine.close_investigation(investigation_id, resolution)

            if not success:
                return web.json_response(
                    {'error': 'Investigation not found'},
                    status=404
                )

            return web.json_response({
                'success': True,
                'investigation_id': investigation_id,
                'status': 'closed',
                'resolution': resolution
            })

        except Exception as e:
            self.logger.error(f"Error closing investigation: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def add_note(self, request: web.Request) -> web.Response:
        """
        POST /api/v1/investigations/{investigation_id}/notes

        Add note to investigation

        Body:
        {
            "author": "<EMAIL>",
            "note": "Found additional suspicious activity..."
        }

        Returns:
        {
            "success": true,
            "note_id": 123
        }
        """
        try:
            investigation_id = request.match_info['investigation_id']
            data = await request.json()

            # This would be implemented to store in investigation_notes table
            # For now, just acknowledge

            return web.json_response({
                'success': True,
                'note_id': 1,
                'investigation_id': investigation_id
            }, status=201)

        except Exception as e:
            self.logger.error(f"Error adding note: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def add_evidence(self, request: web.Request) -> web.Response:
        """
        POST /api/v1/investigations/{investigation_id}/evidence

        Add evidence link (back to SIEM)

        Body:
        {
            "source_siem": "elastic",
            "evidence_type": "query_result",
            "siem_query": "process.name:powershell.exe AND ...",
            "siem_url": "https://elastic.company.com/...",
            "event_count": 45,
            "timestamp_start": "2025-10-01T12:00:00Z",
            "timestamp_end": "2025-10-01T13:00:00Z"
        }

        Returns:
        {
            "success": true,
            "evidence_id": 123
        }
        """
        try:
            investigation_id = request.match_info['investigation_id']
            data = await request.json()

            # This would store in investigation_evidence table
            # For now, acknowledge

            return web.json_response({
                'success': True,
                'evidence_id': 1,
                'investigation_id': investigation_id
            }, status=201)

        except Exception as e:
            self.logger.error(f"Error adding evidence: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )

    async def get_stats(self, request: web.Request) -> web.Response:
        """
        GET /api/v1/investigations/stats

        Get investigation statistics

        Returns:
        {
            "total_investigations": 150,
            "open_count": 25,
            "investigating_count": 10,
            "closed_count": 115,
            "avg_risk_score": 65.5,
            "high_risk_count": 12
        }
        """
        try:
            if not self.engine.db:
                return web.json_response(
                    {'error': 'Database not available'},
                    status=503
                )

            cursor = self.engine.db.cursor()
            cursor.execute("SELECT * FROM get_investigation_stats()")
            row = cursor.fetchone()

            if row:
                return web.json_response({
                    'total_investigations': int(row[0]),
                    'open_count': int(row[1]),
                    'investigating_count': int(row[2]),
                    'closed_count': int(row[3]),
                    'avg_risk_score': float(row[4]) if row[4] else 0.0,
                    'high_risk_count': int(row[5])
                })
            else:
                return web.json_response({
                    'total_investigations': 0,
                    'open_count': 0,
                    'investigating_count': 0,
                    'closed_count': 0,
                    'avg_risk_score': 0.0,
                    'high_risk_count': 0
                })

        except Exception as e:
            self.logger.error(f"Error getting stats: {e}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )
