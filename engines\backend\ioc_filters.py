"""
IOC Filtering and Quality Management
Filters out noise, scanners, and low-quality indicators
"""

import ipaddress
from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)


class IOCFilter:
    """Filter out noise and low-quality IOCs"""

    # Known scanner IP ranges (partial IPs for prefix matching)
    SCANNER_IP_PREFIXES = [
        # Shodan
        '162.142.125.',
        '167.94.138.',
        '167.94.145.',
        '167.94.146.',
        '71.6.135.',
        '71.6.146.',
        '71.6.158.',
        '71.6.165.',
        '71.6.167.',
        '71.6.199.',
        '71.6.232.',
        '80.82.77.',
        '80.82.70.',
        '89.248.167.',
        '89.248.172.',
        '93.120.27.',
        '94.102.49.',
        '104.131.0.',
        '104.236.198.',
        '198.20.69.',
        '198.20.70.',
        '198.20.99.',
        '208.180.20.',
        '209.126.110.',

        # Censys
        '162.142.125.',
        '167.248.133.',
        '192.35.168.',
        '141.212.122.',
        '141.212.123.',
        '185.180.143.',

        # Rapid7/Project Sonar
        '146.185.25.',
        '71.6.',  # Broad but commonly Rapid7

        # ShadowServer
        '216.218.206.',
        '74.82.47.',
        '184.105.139.',
        '184.105.247.',

        # Internet Census
        '60.191.38.',
        '61.176.222.',
        '61.177.172.',
        '222.186.34.',
        '222.186.56.',
        '222.186.58.',
        '222.187.239.',

        # Known mass scanners
        '45.83.64.',
        '45.83.65.',
        '45.83.66.',
        '45.83.67.',
        '185.165.190.',
        '185.165.191.',
        '205.210.31.',
        '198.235.24.',
        '192.241.',
        '193.163.125.',
        '193.174.89.',
    ]

    # Cloud provider IP ranges (simplified - in production, use full CIDR blocks)
    CLOUD_PREFIXES = [
        # AWS
        '3.',
        '13.',
        '18.',
        '34.',
        '35.',
        '52.',
        '54.',

        # Azure
        '20.',
        '40.',
        '51.',
        '104.40.',
        '104.41.',
        '104.42.',
        '104.43.',
        '104.44.',
        '104.45.',
        '104.46.',
        '137.116.',
        '137.117.',
        '168.61.',
        '168.62.',

        # Google Cloud
        '34.',
        '35.',
        '104.154.',
        '104.155.',
        '104.196.',
        '104.197.',
        '104.198.',
        '104.199.',
        '130.211.',
        '146.148.',
        '173.255.',
        '192.158.',
        '199.192.',
        '199.223.',
        '208.68.',

        # DigitalOcean
        '104.131.',
        '104.236.',
        '107.170.',
        '128.199.',
        '134.209.',
        '138.68.',
        '138.197.',
        '139.59.',
        '142.93.',
        '143.110.',
        '143.198.',
        '143.244.',
        '146.190.',
        '157.230.',
        '157.245.',
        '159.65.',
        '159.89.',
        '159.203.',
        '159.223.',
        '161.35.',
        '164.90.',
        '164.92.',
        '165.22.',
        '165.227.',
        '165.232.',
        '167.71.',
        '167.99.',
        '167.172.',
        '170.187.',
        '174.138.',
        '178.128.',
        '178.62.',
        '188.166.',
        '192.34.',
        '192.81.',
        '192.241.',
        '198.199.',
        '198.211.',
        '204.48.',
        '206.189.',
        '206.81.',
        '209.38.',
        '209.97.',

        # Cloudflare
        '103.21.',
        '103.22.',
        '103.31.',
        '104.16.',
        '104.17.',
        '104.18.',
        '104.19.',
        '104.20.',
        '104.21.',
        '104.22.',
        '104.23.',
        '104.24.',
        '104.25.',
        '104.26.',
        '104.27.',
        '108.162.',
        '131.0.72.',
        '141.101.',
        '162.158.',
        '162.159.',
        '172.64.',
        '172.65.',
        '172.66.',
        '172.67.',
        '172.68.',
        '172.69.',
        '172.70.',
        '172.71.',
        '173.245.',
        '188.114.',
        '190.93.',
        '197.234.',
        '198.41.',
    ]

    # Known legitimate domains to filter out
    LEGITIMATE_DOMAINS = [
        'google.com',
        'googleapis.com',
        'googleusercontent.com',
        'gstatic.com',
        'microsoft.com',
        'microsoftonline.com',
        'windows.com',
        'windowsupdate.com',
        'office.com',
        'office365.com',
        'amazon.com',
        'amazonaws.com',
        'cloudfront.net',
        'apple.com',
        'icloud.com',
        'facebook.com',
        'fbcdn.net',
        'twitter.com',
        'twimg.com',
        'linkedin.com',
        'github.com',
        'githubusercontent.com',
        'stackoverflow.com',
        'stackexchange.com',
        'cloudflare.com',
        'cloudflare.net',
        'akamai.net',
        'akamaihd.net',
        'akamaiedge.net',
        'fastly.net',
        'jsdelivr.net',
        'unpkg.com',
        'jquery.com',
        'bootstrapcdn.com',
        'wordpress.org',
        'wordpress.com',
        'wikipedia.org',
        'wikimedia.org',
        'yahoo.com',
        'yimg.com',
        'bing.com',
        'live.com',
        'outlook.com',
        'skype.com',
        'zoom.us',
        'slack.com',
        'dropbox.com',
        'box.com',
        'salesforce.com',
        'adobe.com',
        'oracle.com',
        'ibm.com',
        'intel.com',
        'cisco.com',
        'vmware.com',
        'docker.com',
        'docker.io',
        'ubuntu.com',
        'debian.org',
        'redhat.com',
        'centos.org',
        'archlinux.org',
        'pypi.org',
        'npmjs.com',
        'rubygems.org',
        'packagist.org',
        'nuget.org',
        'maven.org',
        'gradle.org',
    ]

    def __init__(self):
        """Initialize the IOC filter"""
        self.filtered_count = 0
        self.passed_count = 0

    def is_scanner_ip(self, ip: str) -> bool:
        """Check if IP belongs to known scanner"""
        for prefix in self.SCANNER_IP_PREFIXES:
            if ip.startswith(prefix):
                return True
        return False

    def is_cloud_ip(self, ip: str) -> bool:
        """Check if IP belongs to cloud provider"""
        for prefix in self.CLOUD_PREFIXES:
            if ip.startswith(prefix):
                return True
        return False

    def is_private_ip(self, ip: str) -> bool:
        """Check if IP is private/internal"""
        try:
            ip_obj = ipaddress.ip_address(ip)
            return ip_obj.is_private or ip_obj.is_loopback or ip_obj.is_reserved
        except:
            return False

    def is_legitimate_domain(self, domain: str) -> bool:
        """Check if domain is known legitimate service"""
        domain_lower = domain.lower()
        for legit in self.LEGITIMATE_DOMAINS:
            if legit in domain_lower or domain_lower.endswith(f'.{legit}'):
                return True
        return False

    def is_honeypot_data(self, ioc: Dict[str, Any]) -> bool:
        """Check if IOC is from honeypot (not real attack)"""
        name = (ioc.get('name', '') or ioc.get('title', '')).lower()
        source = ioc.get('source', '').lower()

        # Check for honeypot indicators
        honeypot_indicators = ['honeypot', 'honeyaml', 'honey-', 'dionaea', 'cowrie']
        for indicator in honeypot_indicators:
            if indicator in name:
                return True

        # OTX honeypot data is particularly noisy
        if source == 'otx' and 'honeypot' in name:
            return True

        return False

    def should_filter_ioc(self, ioc: Dict[str, Any]) -> tuple[bool, str]:
        """
        Determine if IOC should be filtered out

        Returns:
            (should_filter, reason)
        """
        # Support both ioc_type and indicator_type (CTI format)
        ioc_type = (ioc.get('ioc_type') or ioc.get('indicator_type', '')).lower()
        # Support both ioc_value and indicator_value (CTI format)
        value = ioc.get('ioc_value') or ioc.get('indicator_value') or ioc.get('value', '')

        if not value:
            return True, "No IOC value"

        # Check honeypot data
        if self.is_honeypot_data(ioc):
            return True, "Honeypot data"

        # Check IP-based IOCs
        if ioc_type in ['ip', 'ipv4', 'ipv6', 'ip:port']:
            # Extract IP if it has port
            if ':' in value:
                ip = value.split(':')[0]
            else:
                ip = value

            if self.is_private_ip(ip):
                return True, "Private/internal IP"

            if self.is_scanner_ip(ip):
                return True, "Known scanner IP"

            # Cloud IPs need more context - don't filter immediately
            # but reduce their quality score
            if self.is_cloud_ip(ip):
                # Don't filter, but mark for lower quality
                ioc['is_cloud'] = True

        # Check domain-based IOCs
        elif ioc_type in ['domain', 'fqdn', 'hostname']:
            if self.is_legitimate_domain(value):
                return True, "Legitimate domain"

        # Check URL-based IOCs
        elif ioc_type in ['url', 'uri']:
            # Extract domain from URL
            try:
                from urllib.parse import urlparse
                parsed = urlparse(value)
                domain = parsed.netloc.lower()
                if self.is_legitimate_domain(domain):
                    return True, "URL on legitimate domain"
            except:
                pass

        # Check quality score if available
        quality = ioc.get('quality_score', 1.0)
        if quality < 0.3:
            return True, f"Low quality score: {quality:.2f}"

        # Check age (if timestamp available)
        import datetime
        created = ioc.get('created_at') or ioc.get('first_seen')
        if created:
            try:
                if isinstance(created, str):
                    created_dt = datetime.datetime.fromisoformat(created.replace('Z', '+00:00'))
                else:
                    created_dt = created

                age_days = (datetime.datetime.utcnow() - created_dt.replace(tzinfo=None)).days
                if age_days > 90:
                    return True, f"Stale IOC ({age_days} days old)"
            except:
                pass

        return False, "Passed filters"

    def filter_iocs(self, iocs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter a list of IOCs"""
        filtered_iocs = []

        for ioc in iocs:
            should_filter, reason = self.should_filter_ioc(ioc)

            if should_filter:
                self.filtered_count += 1
                logger.info(f"Filtered IOC {ioc.get('indicator_value', ioc.get('ioc_value', 'unknown'))[:50]}: {reason}")
            else:
                self.passed_count += 1
                filtered_iocs.append(ioc)

        logger.info(f"Filtered {self.filtered_count} IOCs, kept {self.passed_count}")
        return filtered_iocs

    def get_filter_stats(self) -> Dict[str, int]:
        """Get filtering statistics"""
        return {
            'filtered': self.filtered_count,
            'passed': self.passed_count,
            'total': self.filtered_count + self.passed_count,
            'filter_rate': self.filtered_count / max(1, self.filtered_count + self.passed_count)
        }


def test_filters():
    """Test the IOC filters"""
    filter = IOCFilter()

    test_iocs = [
        {'ioc_value': '**************', 'ioc_type': 'ip'},  # Shodan
        {'ioc_value': '********', 'ioc_type': 'ip'},  # Private
        {'ioc_value': 'google.com', 'ioc_type': 'domain'},  # Legitimate
        {'ioc_value': 'evil-malware-c2.com', 'ioc_type': 'domain'},  # Should pass
        {'name': 'Honeypot Scanner September', 'ioc_value': '*******', 'ioc_type': 'ip'},  # Honeypot
        {'ioc_value': '**************', 'ioc_type': 'ip'},  # Should pass (Tor exit)
    ]

    for ioc in test_iocs:
        should_filter, reason = filter.should_filter_ioc(ioc)
        print(f"{ioc.get('ioc_value', 'Unknown'):30} - Filter: {should_filter:5} - Reason: {reason}")


if __name__ == "__main__":
    test_filters()