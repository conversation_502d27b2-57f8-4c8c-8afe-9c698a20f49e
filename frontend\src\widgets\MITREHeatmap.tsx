import React, { useState, useEffect, useMemo } from 'react'
import { mitreAPI } from '../api/client'
import {
  Target, TrendingUp, Calendar, Filter, Download,
  ZoomIn, ZoomOut, Info, AlertTriangle, Loader
} from 'lucide-react'

interface MITRETechnique {
  id: string
  name: string
  tactic: string
  count: number
  severity: number
  recent_cases: string[]
  description?: string
}

interface HeatmapCell {
  technique: string
  tactic: string
  value: number
  normalizedValue: number
  cases: string[]
}

interface MITREHeatmapProps {
  timeRange?: string
  onCellClick?: (technique: MITRETechnique) => void
}

export const MITREHeatmap: React.FC<MITREHeatmapProps> = ({
  timeRange = '7d',
  onCellClick
}) => {
  const [techniques, setTechniques] = useState<MITRETechnique[]>([])
  const [selectedTactic, setSelectedTactic] = useState<string | null>(null)
  const [hoveredCell, setHoveredCell] = useState<HeatmapCell | null>(null)
  const [loading, setLoading] = useState(false)
  const [zoomLevel, setZoomLevel] = useState(1)

  // MITRE ATT&CK tactics in order
  const tactics = [
    'Initial Access',
    'Execution',
    'Persistence',
    'Privilege Escalation',
    'Defense Evasion',
    'Credential Access',
    'Discovery',
    'Lateral Movement',
    'Collection',
    'Command and Control',
    'Exfiltration',
    'Impact'
  ]

  // Generate mock data
  useEffect(() => {
    setLoading(true)
    // Simulate API call
    setTimeout(() => {
      const mockTechniques: MITRETechnique[] = []

      tactics.forEach(tactic => {
        // Generate 3-8 techniques per tactic
        const techniqueCount = Math.floor(Math.random() * 6) + 3
        for (let i = 0; i < techniqueCount; i++) {
          mockTechniques.push({
            id: `T${Math.floor(Math.random() * 1000) + 1000}`,
            name: `Technique ${tactic.split(' ')[0]} ${i + 1}`,
            tactic,
            count: Math.floor(Math.random() * 100),
            severity: Math.random() * 10,
            recent_cases: [`CASE-${Math.floor(Math.random() * 1000)}`, `CASE-${Math.floor(Math.random() * 1000)}`]
          })
        }
      })

      setTechniques(mockTechniques)
      setLoading(false)
    }, 1000)
  }, [timeRange])

  // Process data into heatmap cells
  const heatmapData = useMemo(() => {
    const maxCount = Math.max(...techniques.map(t => t.count), 1)

    const data: Record<string, HeatmapCell[]> = {}

    tactics.forEach(tactic => {
      data[tactic] = techniques
        .filter(t => t.tactic === tactic)
        .map(technique => ({
          technique: technique.name,
          tactic: technique.tactic,
          value: technique.count,
          normalizedValue: technique.count / maxCount,
          cases: technique.recent_cases
        }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 10) // Limit to top 10 per tactic
    })

    return data
  }, [techniques])

  // Calculate statistics
  const stats = useMemo(() => {
    const totalEvents = techniques.reduce((sum, t) => sum + t.count, 0)
    const uniqueTechniques = techniques.length
    const avgSeverity = techniques.length > 0
      ? techniques.reduce((sum, t) => sum + t.severity, 0) / techniques.length
      : 0
    const mostActive = techniques.reduce((max, t) => t.count > (max?.count || 0) ? t : max, techniques[0])

    return {
      totalEvents,
      uniqueTechniques,
      avgSeverity,
      mostActive
    }
  }, [techniques])

  // Get color based on normalized value
  const getHeatColor = (value: number, isSelected: boolean = false) => {
    if (isSelected) return 'rgb(59, 130, 246)' // blue-500

    if (value === 0) return 'rgb(243, 244, 246)' // gray-100
    if (value < 0.2) return 'rgb(254, 243, 199)' // amber-100
    if (value < 0.4) return 'rgb(254, 215, 170)' // amber-200
    if (value < 0.6) return 'rgb(251, 191, 36)' // amber-400
    if (value < 0.8) return 'rgb(245, 158, 11)' // amber-500
    return 'rgb(220, 38, 38)' // red-600
  }

  // Export heatmap as CSV
  const exportData = () => {
    const csv = [
      ['Tactic', 'Technique', 'Count', 'Severity'],
      ...techniques.map(t => [t.tactic, t.name, t.count, t.severity.toFixed(2)])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob([csv], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `mitre-heatmap-${timeRange}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader className="animate-spin text-blue-500" size={48} />
        <span className="ml-4 text-gray-600">Loading MITRE techniques...</span>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-4">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-xl font-bold flex items-center gap-2">
              <Target size={24} />
              MITRE ATT&CK Heatmap
            </h2>
            <p className="text-indigo-100 text-sm mt-1">
              Observed adversary tactics and techniques
            </p>
          </div>
          <div className="text-right">
            <select
              className="px-3 py-1 rounded text-sm bg-white/20 text-white border border-white/30"
              value={timeRange}
              onChange={(e) => window.location.search = `?timeRange=${e.target.value}`}
            >
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-4 gap-4 mt-4">
          <div>
            <div className="text-2xl font-bold">{stats.totalEvents.toLocaleString()}</div>
            <div className="text-xs text-indigo-100">Total Events</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{stats.uniqueTechniques}</div>
            <div className="text-xs text-indigo-100">Unique Techniques</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{stats.avgSeverity.toFixed(1)}/10</div>
            <div className="text-xs text-indigo-100">Avg Severity</div>
          </div>
          <div>
            <div className="text-lg font-bold truncate">{stats.mostActive?.name || 'N/A'}</div>
            <div className="text-xs text-indigo-100">Most Active</div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="p-3 border-b bg-gray-50 flex justify-between items-center">
        <div className="flex gap-2">
          <button
            onClick={() => setZoomLevel(Math.min(zoomLevel * 1.2, 3))}
            className="p-1 border rounded hover:bg-white"
            title="Zoom In"
          >
            <ZoomIn size={18} />
          </button>
          <button
            onClick={() => setZoomLevel(Math.max(zoomLevel * 0.8, 0.5))}
            className="p-1 border rounded hover:bg-white"
            title="Zoom Out"
          >
            <ZoomOut size={18} />
          </button>
          <button
            onClick={() => setZoomLevel(1)}
            className="px-2 py-1 border rounded hover:bg-white text-sm"
          >
            Reset
          </button>
          <span className="text-sm text-gray-600 ml-2">
            Zoom: {(zoomLevel * 100).toFixed(0)}%
          </span>
        </div>
        <button
          onClick={exportData}
          className="flex items-center gap-2 px-3 py-1 border rounded hover:bg-white text-sm"
        >
          <Download size={16} />
          Export CSV
        </button>
      </div>

      {/* Heatmap Grid */}
      <div className="flex-1 overflow-auto p-4">
        <div
          className="inline-block min-w-full"
          style={{ transform: `scale(${zoomLevel})`, transformOrigin: 'top left' }}
        >
          {/* Tactic Headers */}
          <div className="grid grid-cols-12 gap-1 mb-2">
            {tactics.map(tactic => (
              <div
                key={tactic}
                className={`text-xs font-medium text-center p-2 rounded cursor-pointer ${
                  selectedTactic === tactic ? 'bg-blue-500 text-white' : 'bg-gray-100 hover:bg-gray-200'
                }`}
                onClick={() => setSelectedTactic(selectedTactic === tactic ? null : tactic)}
              >
                <div className="truncate" title={tactic}>
                  {tactic.split(' ')[0]}
                </div>
                <div className="text-xs opacity-75 mt-1">
                  {heatmapData[tactic]?.reduce((sum, cell) => sum + cell.value, 0) || 0}
                </div>
              </div>
            ))}
          </div>

          {/* Heatmap Cells */}
          <div className="grid grid-cols-12 gap-1">
            {tactics.map(tactic => (
              <div key={tactic} className="space-y-1">
                {(heatmapData[tactic] || []).map((cell, idx) => (
                  <div
                    key={`${tactic}-${idx}`}
                    className="relative group cursor-pointer transition-all duration-200 hover:scale-105"
                    style={{
                      backgroundColor: getHeatColor(
                        cell.normalizedValue,
                        selectedTactic === tactic
                      ),
                      height: '20px'
                    }}
                    onMouseEnter={() => setHoveredCell(cell)}
                    onMouseLeave={() => setHoveredCell(null)}
                    onClick={() => {
                      const technique = techniques.find(
                        t => t.name === cell.technique && t.tactic === cell.tactic
                      )
                      if (technique && onCellClick) {
                        onCellClick(technique)
                      }
                    }}
                  >
                    {/* Show value on hover */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 bg-black/50 text-white text-xs font-bold">
                      {cell.value}
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Tooltip */}
      {hoveredCell && (
        <div className="absolute bg-gray-900 text-white p-3 rounded shadow-lg text-sm z-50"
             style={{
               bottom: '20px',
               right: '20px',
               maxWidth: '300px'
             }}>
          <div className="font-bold mb-1">{hoveredCell.technique}</div>
          <div className="text-xs opacity-75 mb-2">{hoveredCell.tactic}</div>
          <div className="flex justify-between mb-1">
            <span>Events:</span>
            <span className="font-bold">{hoveredCell.value}</span>
          </div>
          <div className="text-xs opacity-75">
            Recent Cases: {hoveredCell.cases.join(', ')}
          </div>
        </div>
      )}

      {/* Legend */}
      <div className="border-t p-3 bg-gray-50">
        <div className="flex items-center gap-4">
          <span className="text-sm font-medium">Activity Level:</span>
          <div className="flex gap-2">
            {[0, 0.2, 0.4, 0.6, 0.8, 1].map(value => (
              <div key={value} className="flex items-center gap-1">
                <div
                  className="w-4 h-4 rounded"
                  style={{ backgroundColor: getHeatColor(value) }}
                />
                <span className="text-xs text-gray-600">
                  {value === 0 ? 'None' : value === 1 ? 'Critical' : ''}
                </span>
              </div>
            ))}
          </div>
          <div className="ml-auto flex items-center gap-2 text-sm text-gray-600">
            <Info size={14} />
            Click cells to view technique details
          </div>
        </div>
      </div>
    </div>
  )
}

export default MITREHeatmap