# Log Source Scoring System - Quantitative Assessment Framework

## Overview

This document provides a comprehensive scoring system for evaluating log sources based on their contribution to security detection, correlation capabilities, and operational value.

---

## Scoring Methodology

### Base Score Calculation

```python
def calculate_log_source_score(log_source):
    """
    Calculate comprehensive score for a log source (0-100)
    """

    # Component scores with weights
    visibility_score = assess_visibility(log_source) * 0.30
    quality_score = assess_data_quality(log_source) * 0.25
    coverage_score = assess_coverage(log_source) * 0.20
    timeliness_score = assess_timeliness(log_source) * 0.15
    correlation_score = assess_correlation_value(log_source) * 0.10

    # Calculate base score
    base_score = (
        visibility_score +
        quality_score +
        coverage_score +
        timeliness_score +
        correlation_score
    )

    # Apply modifiers
    multipliers = calculate_multipliers(log_source)
    final_score = base_score * multipliers['total']

    return {
        'final_score': min(final_score, 100),
        'breakdown': {
            'visibility': visibility_score,
            'quality': quality_score,
            'coverage': coverage_score,
            'timeliness': timeliness_score,
            'correlation': correlation_score
        },
        'tier': determine_tier(final_score)
    }
```

---

## Component Scoring Details

### 1. Visibility Score (30% weight)

```python
VISIBILITY_SCORING = {
    'depth_levels': {
        'kernel_level': {
            'score': 100,
            'description': 'Kernel-level system calls and operations',
            'examples': ['CrowdStrike', 'SentinelOne kernel mode']
        },
        'system_level': {
            'score': 80,
            'description': 'System-level processes and services',
            'examples': ['Sysmon', 'auditd', 'Windows Security Events']
        },
        'application_level': {
            'score': 60,
            'description': 'Application-layer activities',
            'examples': ['IIS logs', 'Apache logs', 'Database logs']
        },
        'network_level': {
            'score': 70,
            'description': 'Network communications and flows',
            'examples': ['Netflow', 'Firewall logs', 'Zeek']
        },
        'user_level': {
            'score': 50,
            'description': 'User activities and interactions',
            'examples': ['Bash history', 'PowerShell history']
        }
    },

    'granularity': {
        'command_arguments': 10,  # Bonus points
        'process_genealogy': 10,
        'memory_analysis': 15,
        'file_hash_extraction': 5,
        'network_payload': 10
    }
}
```

### 2. Data Quality Score (25% weight)

```python
DATA_QUALITY_SCORING = {
    'structure': {
        'structured_json': 25,
        'structured_xml': 20,
        'semi_structured': 15,
        'csv_delimited': 10,
        'unstructured': 5
    },

    'completeness': {
        'all_required_fields': 25,
        'most_fields_present': 20,
        'basic_fields_only': 10,
        'minimal_fields': 5
    },

    'accuracy': {
        'validated_data': 25,
        'normalized_timestamps': 20,
        'consistent_formatting': 15,
        'raw_unprocessed': 10
    },

    'enrichment': {
        'built_in_context': 25,
        'metadata_included': 20,
        'basic_enrichment': 10,
        'no_enrichment': 0
    }
}
```

### 3. Coverage Score (20% weight)

```python
COVERAGE_SCORING = {
    'attack_techniques': {
        'percentage_covered': {
            '90-100%': 100,
            '70-89%': 80,
            '50-69%': 60,
            '30-49%': 40,
            '10-29%': 20,
            '<10%': 5
        }
    },

    'asset_coverage': {
        'all_critical_assets': 50,
        'most_critical_assets': 40,
        'some_critical_assets': 25,
        'few_critical_assets': 10
    },

    'user_coverage': {
        'all_users': 50,
        'privileged_users': 40,
        'standard_users': 20,
        'service_accounts': 30
    }
}
```

### 4. Timeliness Score (15% weight)

```python
TIMELINESS_SCORING = {
    'latency': {
        'real_time': 100,        # < 1 second
        'near_real_time': 80,    # 1-60 seconds
        'low_latency': 60,       # 1-5 minutes
        'moderate_latency': 40,  # 5-30 minutes
        'high_latency': 20,      # 30+ minutes
        'batch': 5               # Daily or less frequent
    },

    'retention': {
        'long_term': {
            'days': 365,
            'score_modifier': 1.2
        },
        'medium_term': {
            'days': 90,
            'score_modifier': 1.0
        },
        'short_term': {
            'days': 30,
            'score_modifier': 0.8
        },
        'minimal': {
            'days': 7,
            'score_modifier': 0.6
        }
    }
}
```

### 5. Correlation Value Score (10% weight)

```python
CORRELATION_VALUE_SCORING = {
    'unique_identifiers': {
        'has_session_id': 25,
        'has_correlation_id': 20,
        'has_trace_id': 15,
        'has_user_id': 10,
        'has_hostname': 10,
        'has_timestamp': 20
    },

    'linkability': {
        'directly_linkable': 100,
        'indirectly_linkable': 70,
        'partially_linkable': 40,
        'isolated_events': 10
    }
}
```

---

## Log Source Categories & Scores

### Category 1: Endpoint Detection & Response (EDR)

```python
EDR_SCORING = {
    'crowdstrike_falcon': {
        'base_score': 98,
        'strengths': [
            'Kernel-level visibility',
            'Real-time streaming',
            'Built-in threat intelligence',
            'Process genealogy',
            'Behavioral detection'
        ],
        'scoring_breakdown': {
            'visibility': 30,      # Full 30%
            'quality': 24,         # 24/25%
            'coverage': 19,        # 19/20%
            'timeliness': 15,      # Full 15%
            'correlation': 10      # Full 10%
        },
        'tier': 'PLATINUM'
    },

    'sentinelone': {
        'base_score': 96,
        'tier': 'PLATINUM'
    },

    'microsoft_defender': {
        'base_score': 82,
        'tier': 'GOLD'
    },

    'carbon_black': {
        'base_score': 90,
        'tier': 'PLATINUM'
    }
}
```

### Category 2: Network Security

```python
NETWORK_SCORING = {
    'zeek': {
        'base_score': 88,
        'strengths': [
            'Deep protocol analysis',
            'Metadata extraction',
            'File extraction',
            'Scriptable'
        ],
        'scoring_breakdown': {
            'visibility': 21,      # 21/30%
            'quality': 22,         # 22/25%
            'coverage': 17,        # 17/20%
            'timeliness': 14,      # 14/15%
            'correlation': 9       # 9/10%
        },
        'tier': 'GOLD'
    },

    'palo_alto_firewall': {
        'base_score': 85,
        'tier': 'GOLD'
    },

    'cisco_firepower': {
        'base_score': 75,
        'tier': 'SILVER'
    },

    'netflow': {
        'base_score': 65,
        'tier': 'SILVER'
    }
}
```

### Category 3: Identity & Access

```python
IDENTITY_SCORING = {
    'active_directory': {
        'base_score': 92,
        'strengths': [
            'Authentication events',
            'Kerberos monitoring',
            'Group changes',
            'Privilege tracking'
        ],
        'tier': 'PLATINUM'
    },

    'azure_ad': {
        'base_score': 84,
        'tier': 'GOLD'
    },

    'okta': {
        'base_score': 86,
        'tier': 'GOLD'
    },

    'ldap': {
        'base_score': 60,
        'tier': 'BRONZE'
    }
}
```

### Category 4: Cloud Platforms

```python
CLOUD_SCORING = {
    'aws_cloudtrail': {
        'base_score': 87,
        'tier': 'GOLD'
    },

    'azure_activity_log': {
        'base_score': 85,
        'tier': 'GOLD'
    },

    'gcp_audit_log': {
        'base_score': 86,
        'tier': 'GOLD'
    },

    'kubernetes_audit': {
        'base_score': 78,
        'tier': 'GOLD'
    }
}
```

### Category 5: Operating System Logs

```python
OS_SCORING = {
    'sysmon': {
        'base_score': 78,
        'strengths': [
            'Process creation',
            'Network connections',
            'Registry changes',
            'File creation'
        ],
        'tier': 'GOLD'
    },

    'windows_security_events': {
        'base_score': 70,
        'tier': 'SILVER'
    },

    'linux_auditd': {
        'base_score': 76,
        'tier': 'GOLD'
    },

    'syslog': {
        'base_score': 45,
        'tier': 'BRONZE'
    }
}
```

---

## Composite Scoring for Multiple Sources

### Combination Scoring Algorithm

```python
def calculate_composite_score(log_sources):
    """
    Calculate overall detection capability from multiple log sources
    """
    # Group sources by category
    categories = group_by_category(log_sources)

    # Calculate category scores
    category_scores = {}
    for category, sources in categories.items():
        # Take best source in category (diminishing returns for multiple)
        best_score = max(source['score'] for source in sources)
        additional_sources = len(sources) - 1

        # Apply diminishing returns
        category_score = best_score + (additional_sources * 5)  # +5 per additional
        category_scores[category] = min(category_score, 100)

    # Calculate overall score with synergies
    base_score = sum(category_scores.values()) / len(REQUIRED_CATEGORIES)

    # Apply synergy bonuses
    synergy_bonus = calculate_synergies(categories)

    # Apply completeness multiplier
    completeness = len(categories) / len(ALL_CATEGORIES)

    final_score = base_score * (1 + synergy_bonus) * completeness

    return min(final_score, 100)
```

### Synergy Bonuses

```python
SYNERGY_BONUSES = {
    'endpoint_network': {
        'requires': ['EDR', 'Network'],
        'bonus': 0.15,
        'reason': 'Correlate process to network activity'
    },

    'identity_endpoint': {
        'requires': ['Identity', 'EDR'],
        'bonus': 0.20,
        'reason': 'Link users to processes'
    },

    'full_stack': {
        'requires': ['EDR', 'Network', 'Identity', 'Cloud'],
        'bonus': 0.30,
        'reason': 'Complete visibility'
    }
}
```

---

## Practical Scoring Examples

### Example 1: Small Business Setup

```python
SMALL_BUSINESS = {
    'log_sources': [
        {'name': 'Windows Security Events', 'score': 70},
        {'name': 'Basic Firewall', 'score': 55},
        {'name': 'Office 365', 'score': 75}
    ],

    'composite_score': 58,
    'detection_capability': 'BASIC',
    'gaps': [
        'No endpoint visibility',
        'Limited network analysis',
        'No behavioral detection'
    ],

    'improvement_priority': [
        {'add': 'Sysmon', 'score_increase': 15, 'cost': '$'},
        {'add': 'Microsoft Defender', 'score_increase': 20, 'cost': '$$'}
    ]
}
```

### Example 2: Enterprise Setup

```python
ENTERPRISE = {
    'log_sources': [
        {'name': 'CrowdStrike', 'score': 98},
        {'name': 'Palo Alto', 'score': 85},
        {'name': 'Active Directory', 'score': 92},
        {'name': 'AWS CloudTrail', 'score': 87},
        {'name': 'Splunk Enterprise Security', 'score': 80}
    ],

    'composite_score': 91,
    'detection_capability': 'ADVANCED',
    'strengths': [
        'Comprehensive endpoint visibility',
        'Strong identity monitoring',
        'Cloud security coverage'
    ],

    'optimization_opportunities': [
        {'add': 'Zeek', 'benefit': 'Deep packet inspection'},
        {'add': 'Deception tech', 'benefit': 'Early warning'}
    ]
}
```

---

## Score-Based Decision Matrix

### Investment Recommendations by Current Score

```python
IMPROVEMENT_RECOMMENDATIONS = {
    'score_0_30': {
        'classification': 'CRITICAL GAP',
        'priority_actions': [
            'Deploy Sysmon immediately',
            'Enable Windows auditing',
            'Centralize existing logs'
        ],
        'expected_improvement': '+30 points',
        'investment': '$10-20K'
    },

    'score_31_50': {
        'classification': 'SIGNIFICANT GAP',
        'priority_actions': [
            'Deploy EDR solution',
            'Add network visibility',
            'Improve identity monitoring'
        ],
        'expected_improvement': '+25 points',
        'investment': '$50-100K'
    },

    'score_51_70': {
        'classification': 'MODERATE COVERAGE',
        'priority_actions': [
            'Upgrade to premium EDR',
            'Add behavioral analytics',
            'Implement SOAR'
        ],
        'expected_improvement': '+20 points',
        'investment': '$100-250K'
    },

    'score_71_85': {
        'classification': 'GOOD COVERAGE',
        'priority_actions': [
            'Optimize existing sources',
            'Add specialized detection',
            'Implement threat hunting'
        ],
        'expected_improvement': '+10 points',
        'investment': '$50-150K'
    },

    'score_86_100': {
        'classification': 'EXCELLENT COVERAGE',
        'priority_actions': [
            'Fine-tune detection',
            'Add deception technology',
            'Focus on automation'
        ],
        'expected_improvement': '+5 points',
        'investment': 'Optimization focus'
    }
}
```

---

## ROI Calculation

### Score to Business Value Mapping

```python
def calculate_roi(current_score, target_score, investment):
    """
    Calculate ROI for improving log source scoring
    """
    # Risk reduction per point of improvement
    risk_reduction_per_point = 0.02  # 2% per point

    # Calculate risk reduction
    score_improvement = target_score - current_score
    risk_reduction = score_improvement * risk_reduction_per_point

    # Average annual loss expectancy
    average_breach_cost = 4450000  # $4.45M average

    # Calculate savings
    annual_savings = average_breach_cost * risk_reduction

    # Calculate ROI
    roi = ((annual_savings - investment) / investment) * 100

    return {
        'score_improvement': score_improvement,
        'risk_reduction': f'{risk_reduction * 100}%',
        'annual_savings': f'${annual_savings:,.0f}',
        'roi': f'{roi:.0f}%',
        'payback_period': f'{investment / annual_savings * 12:.1f} months'
    }
```

### Example ROI Calculations

```python
ROI_EXAMPLES = [
    {
        'scenario': 'Basic to Intermediate',
        'current_score': 35,
        'target_score': 65,
        'investment': 150000,
        'roi': '178%',
        'payback_period': '6.7 months'
    },
    {
        'scenario': 'Intermediate to Advanced',
        'current_score': 65,
        'target_score': 85,
        'investment': 300000,
        'roi': '98%',
        'payback_period': '12.2 months'
    },
    {
        'scenario': 'Advanced to Elite',
        'current_score': 85,
        'target_score': 95,
        'investment': 200000,
        'roi': '45%',
        'payback_period': '20.1 months'
    }
]
```

---

## Automated Scoring Tool

```python
class LogSourceScorer:
    def __init__(self):
        self.scoring_rules = load_scoring_rules()
        self.source_catalog = load_source_catalog()

    def assess_environment(self, available_sources):
        """
        Comprehensive assessment of logging environment
        """
        results = {
            'individual_scores': {},
            'composite_score': 0,
            'tier': '',
            'gaps': [],
            'recommendations': []
        }

        # Score each source
        for source in available_sources:
            score = self.calculate_source_score(source)
            results['individual_scores'][source['name']] = score

        # Calculate composite
        results['composite_score'] = self.calculate_composite_score(
            results['individual_scores']
        )

        # Determine tier
        results['tier'] = self.determine_tier(results['composite_score'])

        # Identify gaps
        results['gaps'] = self.identify_gaps(available_sources)

        # Generate recommendations
        results['recommendations'] = self.generate_recommendations(
            results['composite_score'],
            results['gaps']
        )

        return results

    def generate_report(self, assessment_results):
        """
        Generate executive report from assessment
        """
        report = {
            'executive_summary': {
                'overall_score': assessment_results['composite_score'],
                'classification': assessment_results['tier'],
                'key_strengths': self.identify_strengths(assessment_results),
                'critical_gaps': assessment_results['gaps'][:3],
                'recommended_actions': assessment_results['recommendations'][:3]
            },
            'detailed_analysis': assessment_results,
            'roadmap': self.create_improvement_roadmap(assessment_results),
            'investment_analysis': self.calculate_investment_requirements(
                assessment_results
            )
        }

        return report
```

---

## Conclusion

The Log Source Scoring System provides:

1. **Objective measurement** of log source value
2. **Clear improvement paths** based on current scores
3. **ROI justification** for security investments
4. **Gap identification** for risk assessment
5. **Optimization guidance** for existing sources

Key takeaways:
- **Quality > Quantity**: High-scoring sources provide more value
- **Synergy Matters**: Combining sources multiplies effectiveness
- **Continuous Improvement**: Regular reassessment drives progress
- **Business Alignment**: Scores translate to risk reduction
- **Investment Clarity**: Clear ROI for security spending