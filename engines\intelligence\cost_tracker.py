"""
Cost Tracker Module
Handles cost metrics, optimization, and monitoring for AI model usage
"""

import asyncio
from typing import Dict, Any, List
from datetime import datetime, timedelta
import logging

class CostTracker:
    """Manages cost tracking and optimization for AI model usage"""

    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or logging.getLogger(__name__)

        # Cost Tracking
        self.cost_metrics = {
            'total_requests': 0,
            'total_cost': 0.0,
            'free_requests': 0,
            'paid_requests': 0,
            'crystallized_patterns': 0
        }

        # Model-specific cost tracking
        self.model_costs = {}
        self.daily_costs = {}

    def track_request(self, model_tier: str, cost: float):
        """Track a single AI model request"""
        # Update global metrics
        self.cost_metrics['total_requests'] += 1
        self.cost_metrics['total_cost'] += cost

        if cost == 0:
            self.cost_metrics['free_requests'] += 1
        else:
            self.cost_metrics['paid_requests'] += 1

        # Track model-specific costs
        if model_tier not in self.model_costs:
            self.model_costs[model_tier] = {
                'requests': 0,
                'total_cost': 0.0,
                'avg_cost': 0.0
            }

        self.model_costs[model_tier]['requests'] += 1
        self.model_costs[model_tier]['total_cost'] += cost
        self.model_costs[model_tier]['avg_cost'] = (
            self.model_costs[model_tier]['total_cost'] /
            self.model_costs[model_tier]['requests']
        )

        # Track daily costs
        today = datetime.utcnow().date().isoformat()
        if today not in self.daily_costs:
            self.daily_costs[today] = {
                'requests': 0,
                'cost': 0.0,
                'models_used': set()
            }

        self.daily_costs[today]['requests'] += 1
        self.daily_costs[today]['cost'] += cost
        self.daily_costs[today]['models_used'].add(model_tier)

    def track_crystallization(self):
        """Track a pattern crystallization event"""
        self.cost_metrics['crystallized_patterns'] += 1

    def get_cost_metrics(self) -> Dict[str, Any]:
        """Get current cost metrics"""
        return self.cost_metrics.copy()

    def get_model_costs(self) -> Dict[str, Any]:
        """Get model-specific cost breakdown"""
        # Convert sets to lists for JSON serialization
        model_costs_copy = {}
        for model, data in self.model_costs.items():
            model_costs_copy[model] = data.copy()

        return model_costs_copy

    def get_daily_costs(self, days: int = 7) -> Dict[str, Any]:
        """Get daily cost breakdown for specified number of days"""
        cutoff_date = (datetime.utcnow() - timedelta(days=days)).date().isoformat()

        filtered_costs = {}
        for date, data in self.daily_costs.items():
            if date >= cutoff_date:
                # Convert set to list for JSON serialization
                data_copy = data.copy()
                data_copy['models_used'] = list(data['models_used'])
                filtered_costs[date] = data_copy

        return filtered_costs

    def calculate_savings(self) -> Dict[str, Any]:
        """Calculate cost savings from crystallization"""
        crystallized_patterns = self.cost_metrics['crystallized_patterns']
        estimated_uses_per_pattern = 1000  # Estimated future uses per pattern
        cost_per_ai_call = 0.002   # Average cost per AI call

        # Calculate potential cost without crystallization
        total_potential_cost = crystallized_patterns * estimated_uses_per_pattern * cost_per_ai_call
        actual_cost = self.cost_metrics['total_cost']

        if total_potential_cost > 0:
            savings = total_potential_cost - actual_cost
            savings_percentage = (savings / total_potential_cost) * 100
        else:
            savings = 0
            savings_percentage = 0

        return {
            'crystallized_patterns': crystallized_patterns,
            'potential_cost_without_crystallization': total_potential_cost,
            'actual_cost': actual_cost,
            'total_savings': savings,
            'savings_percentage': savings_percentage,
            'roi_ratio': savings / actual_cost if actual_cost > 0 else 0
        }

    def get_cost_efficiency_report(self) -> Dict[str, Any]:
        """Generate a comprehensive cost efficiency report"""
        savings = self.calculate_savings()
        model_costs = self.get_model_costs()

        # Calculate efficiency metrics
        free_request_percentage = (
            (self.cost_metrics['free_requests'] / self.cost_metrics['total_requests']) * 100
            if self.cost_metrics['total_requests'] > 0 else 0
        )

        avg_cost_per_request = (
            self.cost_metrics['total_cost'] / self.cost_metrics['total_requests']
            if self.cost_metrics['total_requests'] > 0 else 0
        )

        # Find most cost-effective model
        most_efficient_model = None
        best_efficiency = float('inf')

        for model, data in model_costs.items():
            if data['requests'] > 0:
                efficiency = data['total_cost'] / data['requests']
                if efficiency < best_efficiency:
                    best_efficiency = efficiency
                    most_efficient_model = model

        return {
            'overview': {
                'total_requests': self.cost_metrics['total_requests'],
                'total_cost': self.cost_metrics['total_cost'],
                'avg_cost_per_request': avg_cost_per_request,
                'free_request_percentage': free_request_percentage
            },
            'savings': savings,
            'model_efficiency': {
                'most_efficient_model': most_efficient_model,
                'best_efficiency_score': best_efficiency,
                'model_breakdown': model_costs
            },
            'recommendations': self._generate_cost_recommendations()
        }

    def _generate_cost_recommendations(self) -> List[str]:
        """Generate cost optimization recommendations"""
        recommendations = []

        # Check free model usage
        free_percentage = (
            (self.cost_metrics['free_requests'] / self.cost_metrics['total_requests']) * 100
            if self.cost_metrics['total_requests'] > 0 else 0
        )

        if free_percentage < 50:
            recommendations.append("Consider using more free models for simple tasks to reduce costs")

        # Check crystallization efficiency
        if self.cost_metrics['crystallized_patterns'] < 10:
            recommendations.append("Increase pattern crystallization to maximize long-term cost savings")

        # Check model distribution
        if len(self.model_costs) < 3:
            recommendations.append("Consider using a wider variety of models for better cost optimization")

        # Check daily costs
        recent_costs = self.get_daily_costs(3)
        if recent_costs:
            daily_costs = [data['cost'] for data in recent_costs.values()]
            if daily_costs and max(daily_costs) > 10.0:
                recommendations.append("Daily costs are high - review model selection for expensive tasks")

        if not recommendations:
            recommendations.append("Cost efficiency is optimal - continue current practices")

        return recommendations

    async def optimize_costs(self):
        """Periodic cost optimization analysis"""
        try:
            # Log current cost metrics
            self.logger.info(f"Cost metrics: {self.cost_metrics}")

            # Calculate and log savings
            savings = self.calculate_savings()
            self.logger.info(f"Cost savings: {savings['total_savings']:.2f} ({savings['savings_percentage']:.1f}%)")

            # Generate efficiency report
            report = self.get_cost_efficiency_report()

            # Log recommendations
            for recommendation in report['recommendations']:
                self.logger.info(f"Cost recommendation: {recommendation}")

        except Exception as e:
            self.logger.error(f"Cost optimization error: {e}")

    def reset_metrics(self):
        """Reset all cost metrics (use with caution)"""
        self.cost_metrics = {
            'total_requests': 0,
            'total_cost': 0.0,
            'free_requests': 0,
            'paid_requests': 0,
            'crystallized_patterns': 0
        }
        self.model_costs = {}
        self.daily_costs = {}
        self.logger.info("Cost metrics reset")

    def export_cost_data(self) -> Dict[str, Any]:
        """Export all cost data for analysis or backup"""
        return {
            'cost_metrics': self.get_cost_metrics(),
            'model_costs': self.get_model_costs(),
            'daily_costs': self.get_daily_costs(30),  # Last 30 days
            'savings_analysis': self.calculate_savings(),
            'efficiency_report': self.get_cost_efficiency_report(),
            'export_timestamp': datetime.utcnow().isoformat()
        }