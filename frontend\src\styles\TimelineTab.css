/**
 * Timeline Tab - Event Chronology Visualization
 */

.timeline-tab {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
}

/* No Timeline State */
.no-timeline {
  text-align: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.no-timeline-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.no-timeline h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
}

.no-timeline p {
  font-size: 14px;
  color: #6b7280;
  margin: 4px 0;
}

.no-timeline .hint {
  font-size: 13px;
  font-style: italic;
  color: #9ca3af;
  margin-top: 12px;
}

/* Timeline Header */
.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.timeline-summary h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.timeline-stats {
  display: flex;
  gap: 32px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #3b82f6;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.timeline-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.view-mode-toggle {
  display: flex;
  background: #f3f4f6;
  border-radius: 6px;
  padding: 2px;
}

.mode-btn {
  padding: 8px 16px;
  background: transparent;
  color: #6b7280;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.mode-btn.active {
  background: #ffffff;
  color: #111827;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.export-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.export-btn:hover {
  background: #2563eb;
}

/* Severity Filters */
.severity-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.severity-filter {
  padding: 8px 16px;
  border: 2px solid;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.severity-filter:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Chronological View */
.chronological-view {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 24px;
}

.timeline-axis {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0;
}

.timeline-event {
  display: flex;
  gap: 20px;
  position: relative;
}

.event-marker {
  position: relative;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20px;
}

.marker-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: inherit;
  border: 3px solid #ffffff;
  box-shadow: 0 0 0 2px currentColor;
  z-index: 2;
}

.marker-line {
  flex: 1;
  width: 2px;
  background: #d1d5db;
  min-height: 40px;
  margin-top: 4px;
}

.event-card {
  flex: 1;
  margin-bottom: 24px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.2s;
}

.event-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateX(4px);
}

.event-time {
  padding: 8px 16px;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  font-size: 12px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
  color: #6b7280;
}

.event-content {
  padding: 16px;
}

.event-header-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.event-icon {
  font-size: 20px;
}

.event-type {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  flex: 1;
}

.event-severity-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 700;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.event-description {
  font-size: 13px;
  color: #374151;
  line-height: 1.5;
  margin-bottom: 8px;
}

.event-source {
  font-size: 12px;
  color: #6b7280;
}

.time-gap {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 16px 0;
  padding-left: 10px;
}

.gap-line {
  width: 60px;
  height: 2px;
  background: repeating-linear-gradient(
    to right,
    #d1d5db 0px,
    #d1d5db 4px,
    transparent 4px,
    transparent 8px
  );
}

.gap-label {
  font-size: 12px;
  font-weight: 500;
  color: #9ca3af;
  font-style: italic;
}

/* Grouped View */
.grouped-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.hour-group {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.hour-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.hour-time {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.hour-count {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  padding: 4px 8px;
  background: #e5e7eb;
  border-radius: 4px;
}

.hour-events {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.grouped-event {
  padding: 12px 16px;
  background: #f9fafb;
  border-radius: 6px;
  border-left: 4px solid;
  transition: all 0.2s;
}

.grouped-event:hover {
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateX(4px);
}

.grouped-event-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.grouped-event-icon {
  font-size: 18px;
}

.grouped-event-type {
  font-size: 13px;
  font-weight: 600;
  color: #111827;
  flex: 1;
}

.grouped-severity-badge {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 700;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.grouped-event-description {
  font-size: 13px;
  color: #374151;
  margin-bottom: 6px;
  line-height: 1.4;
}

.grouped-event-meta {
  display: flex;
  gap: 16px;
  font-size: 11px;
  color: #6b7280;
}

/* Heatmap View */
.heatmap-view {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 20px;
}

.heatmap-info {
  margin-bottom: 20px;
}

.heatmap-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.heatmap-info p {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
}

.heatmap-grid {
  overflow-x: auto;
}

.heatmap-hours {
  display: grid;
  grid-template-columns: 100px repeat(24, 32px);
  gap: 2px;
  margin-bottom: 2px;
}

.hour-label {
  font-size: 10px;
  font-weight: 600;
  color: #6b7280;
  text-align: center;
  padding: 4px 0;
}

.heatmap-row {
  display: grid;
  grid-template-columns: 100px repeat(24, 32px);
  gap: 2px;
  margin-bottom: 2px;
}

.date-label {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  padding-right: 8px;
}

.heatmap-cell {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
}

.heatmap-cell.has-events:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.heatmap-cell.selected {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px #bfdbfe;
}

.cell-count {
  font-size: 10px;
  font-weight: 700;
  color: #111827;
}

.selected-hour-events {
  margin-top: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.selected-hour-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.selected-hour-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.close-btn {
  width: 28px;
  height: 28px;
  background: #ffffff;
  color: #6b7280;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #111827;
}

.selected-events-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selected-event {
  padding: 10px 12px;
  background: #ffffff;
  border-radius: 6px;
  border-left: 3px solid;
}

.selected-event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  font-weight: 600;
  color: #111827;
}

.selected-severity {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
  font-weight: 700;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.selected-event-desc {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.heatmap-legend {
  margin-top: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.legend-label {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
}

.legend-gradient {
  flex: 1;
  max-width: 300px;
}

.gradient-bar {
  height: 24px;
  border-radius: 4px;
  background: linear-gradient(to right, #fef3c7, #fbbf24, #f59e0b, #ea580c, #dc2626);
  margin-bottom: 4px;
}

.gradient-labels {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: #6b7280;
}

/* Responsive */
@media (max-width: 1200px) {
  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .timeline-controls {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .timeline-tab {
    padding: 16px;
  }

  .timeline-stats {
    gap: 16px;
  }

  .severity-filters {
    overflow-x: auto;
  }

  .event-card {
    margin-left: 0;
  }

  .view-mode-toggle {
    width: 100%;
    justify-content: space-between;
  }

  .mode-btn {
    flex: 1;
    font-size: 11px;
    padding: 8px 8px;
  }

  .heatmap-hours,
  .heatmap-row {
    grid-template-columns: 80px repeat(24, 28px);
  }

  .heatmap-cell {
    width: 28px;
    height: 28px;
  }

  .date-label {
    font-size: 10px;
  }
}

/* Print Styles */
@media print {
  .timeline-controls,
  .severity-filters,
  .export-btn,
  .close-btn {
    display: none;
  }

  .chronological-view,
  .grouped-view {
    break-inside: avoid;
  }

  .event-card,
  .grouped-event {
    break-inside: avoid;
  }
}
