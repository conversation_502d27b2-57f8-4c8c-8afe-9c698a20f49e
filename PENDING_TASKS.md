# Pending Tasks - October 3, 2025

## 🔥 HIGH PRIORITY

### 1. Fix pattern_manager.py Database Cursor Leaks
**File**: `engines/intelligence/pattern_manager.py`
**Status**: ⚠️ CRITICAL - Prevents connection pool exhaustion
**Time Estimate**: 30 minutes

**What's needed:**
1. Add database helper functions (same as consensus_engine.py):
   ```python
   def db_execute(connection, query: str, *params):
       cursor = connection.cursor()
       cursor.execute(query, params if params else None)
       cursor.close()  # CRITICAL

   def db_fetchone(connection, query: str, *params):
       cursor = connection.cursor()
       cursor.execute(query, params if params else None)
       result = cursor.fetchone()
       cursor.close()
       return result

   def db_fetchall(connection, query: str, *params):
       cursor = connection.cursor()
       cursor.execute(query, params if params else None)
       results = cursor.fetchall()
       cursor.close()
       return results
   ```

2. Replace all `cursor.execute()` calls with helper functions in:
   - `store_crystallized_pattern()` (lines 68-85)
   - `store_entity_pattern()` (lines 87-99)
   - `cleanup_patterns()` (lines 136-152)
   - `maintain_library()` (lines 154-189)
   - `get_pattern()` (lines 191-214)
   - `list_patterns()` (lines 216-239)

**Backup exists**: `engines/intelligence/pattern_manager.py.backup`

**Reference**: [consensus_engine.py](engines/intelligence/consensus_engine.py:14-36) - See how it was fixed

---

## 🧪 MEDIUM PRIORITY

### 2. Create test_intelligence_handlers.py
**File**: `test_intelligence_handlers.py` (NEW)
**Status**: ❌ NOT STARTED
**Time Estimate**: 4-6 hours

**What's needed:**
Test all 9 Intelligence Engine message handlers:

```python
# Test structure
def test_consensus_handler():
    """Test intelligence.consensus handler"""
    # Mock AI model manager
    # Send test request to handler
    # Verify consensus calculation
    # Verify database storage

def test_crystallize_handler():
    """Test intelligence.crystallize handler"""
    # Mock AI insights
    # Send crystallization request
    # Verify pattern extraction
    # Verify cost tracking

def test_validate_handler():
    """Test intelligence.validate handler"""
    pass

def test_parse_log_sample_handler():
    """Test intelligence.parse_log_sample handler"""
    # ✅ ALREADY HAS TEST: test_parser_generation.py
    pass

def test_analyze_telemetry_handler():
    """Test intelligence.analyze_telemetry handler"""
    # Test alert-only detection
    # Test full telemetry detection
    # Test partial mode detection

def test_extract_entities_ai_handler():
    """Test intelligence.extract_entities_ai handler"""
    # Test unknown vendor extraction
    # Test entity + rule extraction
    # Test JSON parsing fallback

def test_generate_log_mapping_handler():
    """Test intelligence.generate_log_mapping handler"""
    # ✅ ALREADY HAS TEST: test_schema_detection.py
    pass

def test_unknown_patterns_handler():
    """Test ingestion.unknown_patterns handler"""
    # Test confidence threshold
    # Test crystallization trigger

def test_new_entities_handler():
    """Test contextualization.new_entities handler"""
    # Test entity pattern storage
    # Test confidence threshold
```

**Reference**: [INTELLIGENCE_ENGINE_AUDIT.md](INTELLIGENCE_ENGINE_AUDIT.md) - Complete handler documentation

---

## 📋 INVESTIGATION LIFECYCLE TASKS

### 3. Phase 2: Query Generator Implementation
**Status**: 🔄 IN PROGRESS (30% complete)
**Time Estimate**: 10-15 hours (2-3 days)

**Breakdown**:
1. **Seed Query Templates** (1-2 hours)
   - [ ] Create SQL INSERT statements
   - [ ] Add Elastic, Fortinet, Palo Alto, CrowdStrike templates
   - [ ] Test variable substitution

2. **Build QueryGeneratorService** (4-6 hours)
   - [ ] Source detection from `ingestion_logs`
   - [ ] Query generation with template variables
   - [ ] Deep link generation
   - [ ] Unit tests

3. **Create API Endpoints** (2-3 hours)
   - [ ] `POST /api/investigations/generate-queries`
   - [ ] `GET /api/investigations/available-sources`
   - [ ] Integration tests

4. **Frontend Integration** (3-4 hours)
   - [ ] "Generate Queries" button
   - [ ] Query display with copy buttons
   - [ ] Deep link buttons
   - [ ] Source limitation warnings

**Reference**: [INVESTIGATION_LIFECYCLE_STATUS.md](INVESTIGATION_LIFECYCLE_STATUS.md) - Complete roadmap

---

## ✅ RECENTLY COMPLETED

### Intelligence Engine Audit (October 3, 2025)
- ✅ Audited all 9 message handlers
- ✅ Fixed consensus_engine.py cursor leaks
- ✅ Documented schema detection success (99.997% cost reduction)
- ✅ Created comprehensive audit report

**Output**: [INTELLIGENCE_ENGINE_AUDIT.md](INTELLIGENCE_ENGINE_AUDIT.md)

### Investigation Lifecycle Status Tracking (October 3, 2025)
- ✅ Phase 1 (Business Context) marked COMPLETE
- ✅ Phase 2 (Query Generator) roadmap detailed
- ✅ Phase 3-5 scoped and prioritized
- ✅ Technical debt and testing gaps documented

**Output**: [INVESTIGATION_LIFECYCLE_STATUS.md](INVESTIGATION_LIFECYCLE_STATUS.md)

---

## 🎯 Recommended Order

1. **Fix pattern_manager.py** (30 min) - HIGH PRIORITY
   - Prevents production issues
   - Low effort, high impact

2. **Seed Query Templates** (1-2 hours) - UNBLOCKS PHASE 2
   - Enables query generator development
   - Immediate value for analysts

3. **Build QueryGeneratorService** (4-6 hours) - PHASE 2 CORE
   - Main Phase 2 deliverable
   - Depends on query templates

4. **Create test_intelligence_handlers.py** (4-6 hours) - PARALLEL WORK
   - Can be done alongside query generator
   - Improves overall quality

---

## 📊 Completion Estimates

| Task | Priority | Time | Status |
|------|----------|------|--------|
| pattern_manager.py fix | HIGH | 30 min | ⚠️ Ready to start |
| Query template seeding | HIGH | 1-2 hrs | ⏳ Pending |
| QueryGeneratorService | MEDIUM | 4-6 hrs | ⏳ Pending |
| test_intelligence_handlers.py | MEDIUM | 4-6 hrs | ⏳ Pending |

**Total Remaining Work**: ~10-15 hours

---

**Last Updated**: October 3, 2025
