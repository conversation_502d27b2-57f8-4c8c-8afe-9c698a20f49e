"""
SIEMLess v2.0 - Pattern Marketplace & Community Exchange
Community-driven pattern sharing and collaboration platform
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
import asyncio
from collections import defaultdict

class MarketplaceRole(Enum):
    """User roles in the marketplace"""
    CONSUMER = "consumer"
    CONTRIBUTOR = "contributor"
    VALIDATOR = "validator"
    MODERATOR = "moderator"
    ADMIN = "admin"

class PatternQuality(Enum):
    """Pattern quality ratings"""
    EXPERIMENTAL = "experimental"
    BETA = "beta"
    STABLE = "stable"
    PRODUCTION = "production"
    CERTIFIED = "certified"

class PatternMarketplace:
    """Pattern marketplace for community sharing and collaboration"""

    def __init__(self, pattern_library, db_connection=None, redis_client=None, logger=None):
        self.pattern_library = pattern_library
        self.db_connection = db_connection
        self.redis_client = redis_client
        self.logger = logger

        # Marketplace configuration
        self.config = {
            'min_reputation_to_share': 10,
            'min_reputation_to_validate': 50,
            'min_votes_for_certification': 10,
            'certification_threshold': 0.80,
            'trending_window_days': 7
        }

        # User reputation system
        self.reputation_system = ReputationSystem()

        # Pattern quality assurance
        self.quality_assurance = QualityAssurance()

    # ==================== Pattern Publishing ====================

    async def publish_pattern(self, pattern_id: str, publisher_id: str,
                              metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Publish a pattern to the marketplace"""
        # Get pattern from library
        pattern = self.pattern_library.get_pattern(pattern_id)
        if not pattern:
            return {'success': False, 'error': 'Pattern not found'}

        # Check publisher reputation
        reputation = self.reputation_system.get_reputation(publisher_id)
        if reputation < self.config['min_reputation_to_share']:
            return {
                'success': False,
                'error': f'Insufficient reputation. Need {self.config["min_reputation_to_share"]}, have {reputation}'
            }

        # Quality check
        quality_check = await self.quality_assurance.check_pattern(pattern)
        if not quality_check['passed']:
            return {
                'success': False,
                'error': 'Pattern failed quality checks',
                'issues': quality_check['issues']
            }

        # Create marketplace listing
        listing = {
            'listing_id': self._generate_listing_id(pattern_id, publisher_id),
            'pattern_id': pattern_id,
            'publisher_id': publisher_id,
            'published_at': datetime.utcnow().isoformat(),

            'pattern_info': {
                'name': pattern['pattern_name'],
                'type': pattern['pattern_type'],
                'category': pattern['pattern_category'],
                'description': pattern['metadata'].get('description', ''),
                'tags': pattern['metadata']['tags'],
                'version': pattern['pattern_version']
            },

            'quality': {
                'level': quality_check['quality_level'],
                'score': quality_check['score'],
                'checks_passed': quality_check['checks_passed']
            },

            'metrics': {
                'downloads': 0,
                'implementations': 0,
                'success_rate': pattern['performance']['success_rate'],
                'false_positive_rate': pattern['metadata']['false_positive_rate']
            },

            'community': {
                'votes': {'up': 0, 'down': 0},
                'rating': 0.0,
                'reviews': [],
                'endorsements': []
            },

            'pricing': metadata.get('pricing', {
                'model': 'free',
                'price': 0
            })
        }

        # Store listing
        self._store_listing(listing)

        # Award reputation for sharing
        self.reputation_system.award_points(publisher_id, 'pattern_shared', 10)

        # Notify community
        await self._notify_community(listing)

        if self.logger:
            self.logger.info(f"Pattern {pattern_id} published to marketplace by {publisher_id}")

        return {
            'success': True,
            'listing_id': listing['listing_id'],
            'quality_level': listing['quality']['level']
        }

    # ==================== Pattern Discovery ====================

    def search_marketplace(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search marketplace for patterns"""
        results = []

        # Get all listings
        listings = self._get_all_listings()

        # Apply filters
        for listing in listings:
            if self._matches_marketplace_criteria(listing, criteria):
                results.append(listing)

        # Sort results
        sort_by = criteria.get('sort_by', 'downloads')
        if sort_by == 'downloads':
            results.sort(key=lambda x: x['metrics']['downloads'], reverse=True)
        elif sort_by == 'rating':
            results.sort(key=lambda x: x['community']['rating'], reverse=True)
        elif sort_by == 'newest':
            results.sort(key=lambda x: x['published_at'], reverse=True)
        elif sort_by == 'trending':
            results = self._sort_by_trending(results)

        # Pagination
        offset = criteria.get('offset', 0)
        limit = criteria.get('limit', 50)

        return results[offset:offset + limit]

    def get_trending_patterns(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get currently trending patterns"""
        window_start = datetime.utcnow() - timedelta(days=self.config['trending_window_days'])

        trending = []

        for listing in self._get_all_listings():
            # Calculate trending score
            score = self._calculate_trending_score(listing, window_start)
            trending.append((listing, score))

        # Sort by trending score
        trending.sort(key=lambda x: x[1], reverse=True)

        return [listing for listing, score in trending[:limit]]

    def get_recommended_patterns(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get personalized pattern recommendations"""
        recommendations = []

        # Get user profile
        user_profile = self._get_user_profile(user_id)

        # Get user's implementation history
        history = self._get_user_history(user_id)

        # Collaborative filtering
        similar_users = self._find_similar_users(user_id)
        for similar_user in similar_users:
            their_patterns = self._get_user_implementations(similar_user)
            for pattern_id in their_patterns:
                if pattern_id not in history:
                    listing = self._get_listing_by_pattern(pattern_id)
                    if listing:
                        recommendations.append(listing)

        # Content-based filtering
        for implemented_pattern in history[-10:]:  # Last 10 implementations
            similar_patterns = self.pattern_library.find_similar_patterns(implemented_pattern, 0.7)
            for pattern_id, similarity in similar_patterns:
                listing = self._get_listing_by_pattern(pattern_id)
                if listing and pattern_id not in history:
                    recommendations.append(listing)

        # Remove duplicates and score
        seen = set()
        unique_recommendations = []
        for listing in recommendations:
            if listing['pattern_id'] not in seen:
                seen.add(listing['pattern_id'])
                listing['recommendation_score'] = self._calculate_recommendation_score(listing, user_profile)
                unique_recommendations.append(listing)

        # Sort by recommendation score
        unique_recommendations.sort(key=lambda x: x['recommendation_score'], reverse=True)

        return unique_recommendations[:limit]

    # ==================== Pattern Implementation ====================

    async def implement_pattern(self, listing_id: str, user_id: str) -> Dict[str, Any]:
        """Implement a pattern from the marketplace"""
        listing = self._get_listing(listing_id)
        if not listing:
            return {'success': False, 'error': 'Listing not found'}

        # Check access (for paid patterns)
        if not self._check_access(user_id, listing):
            return {'success': False, 'error': 'Access denied'}

        # Get pattern from library
        pattern = self.pattern_library.get_pattern(listing['pattern_id'])

        # Adapt pattern to user's environment
        adapted_pattern = self._adapt_for_user(pattern, user_id)

        # Import to user's library
        imported_id = self.pattern_library.import_pattern(adapted_pattern)

        # Update metrics
        listing['metrics']['downloads'] += 1
        listing['metrics']['implementations'] += 1
        self._update_listing(listing)

        # Award reputation to publisher
        publisher_id = listing['publisher_id']
        self.reputation_system.award_points(publisher_id, 'pattern_implemented', 1)

        # Track implementation
        self._track_implementation(user_id, listing_id, imported_id)

        return {
            'success': True,
            'imported_pattern_id': imported_id,
            'pattern_name': pattern['pattern_name']
        }

    # ==================== Community Interaction ====================

    def vote_pattern(self, listing_id: str, user_id: str, vote_type: str) -> bool:
        """Vote on a pattern (up/down)"""
        listing = self._get_listing(listing_id)
        if not listing:
            return False

        # Check if already voted
        if self._has_voted(user_id, listing_id):
            return False

        # Record vote
        if vote_type == 'up':
            listing['community']['votes']['up'] += 1
            self.reputation_system.award_points(listing['publisher_id'], 'received_upvote', 2)
        else:
            listing['community']['votes']['down'] += 1

        # Recalculate rating
        total_votes = listing['community']['votes']['up'] + listing['community']['votes']['down']
        if total_votes > 0:
            listing['community']['rating'] = listing['community']['votes']['up'] / total_votes

        self._update_listing(listing)
        self._record_vote(user_id, listing_id, vote_type)

        # Check for certification
        self._check_for_certification(listing)

        return True

    def review_pattern(self, listing_id: str, user_id: str, review: Dict[str, Any]) -> bool:
        """Add a review to a pattern"""
        listing = self._get_listing(listing_id)
        if not listing:
            return False

        # Validate reviewer has implemented the pattern
        if not self._has_implemented(user_id, listing_id):
            return False

        # Add review
        review_data = {
            'review_id': self._generate_review_id(listing_id, user_id),
            'user_id': user_id,
            'timestamp': datetime.utcnow().isoformat(),
            'rating': review['rating'],  # 1-5 stars
            'comment': review.get('comment', ''),
            'success_metrics': review.get('metrics', {}),
            'helpful_count': 0
        }

        listing['community']['reviews'].append(review_data)
        self._update_listing(listing)

        # Award reputation
        self.reputation_system.award_points(user_id, 'review_posted', 3)

        return True

    def endorse_pattern(self, listing_id: str, endorser_id: str, endorsement: Dict[str, Any]) -> bool:
        """Expert endorsement of a pattern"""
        listing = self._get_listing(listing_id)
        if not listing:
            return False

        # Check endorser reputation
        reputation = self.reputation_system.get_reputation(endorser_id)
        if reputation < self.config['min_reputation_to_validate']:
            return False

        # Add endorsement
        endorsement_data = {
            'endorser_id': endorser_id,
            'timestamp': datetime.utcnow().isoformat(),
            'endorser_reputation': reputation,
            'comment': endorsement.get('comment', ''),
            'validated_aspects': endorsement.get('validated', [])
        }

        listing['community']['endorsements'].append(endorsement_data)
        self._update_listing(listing)

        # Award reputation
        self.reputation_system.award_points(listing['publisher_id'], 'received_endorsement', 5)
        self.reputation_system.award_points(endorser_id, 'gave_endorsement', 2)

        # Update quality level
        if len(listing['community']['endorsements']) >= 3:
            listing['quality']['level'] = PatternQuality.CERTIFIED.value

        return True

    # ==================== Pattern Certification ====================

    def _check_for_certification(self, listing: Dict):
        """Check if pattern qualifies for certification"""
        total_votes = listing['community']['votes']['up'] + listing['community']['votes']['down']

        if total_votes >= self.config['min_votes_for_certification']:
            rating = listing['community']['rating']
            if rating >= self.config['certification_threshold']:
                # Certify pattern
                listing['quality']['level'] = PatternQuality.CERTIFIED.value
                listing['quality']['certified_at'] = datetime.utcnow().isoformat()

                # Award bonus reputation
                self.reputation_system.award_points(
                    listing['publisher_id'],
                    'pattern_certified',
                    20
                )

                # Notify publisher
                self._notify_certification(listing)

    # ==================== Analytics & Insights ====================

    def get_marketplace_stats(self) -> Dict[str, Any]:
        """Get marketplace statistics"""
        listings = self._get_all_listings()

        stats = {
            'total_patterns': len(listings),
            'patterns_by_quality': defaultdict(int),
            'patterns_by_type': defaultdict(int),
            'total_downloads': 0,
            'total_implementations': 0,
            'avg_rating': 0.0,
            'top_contributors': [],
            'trending_categories': []
        }

        ratings = []
        for listing in listings:
            stats['patterns_by_quality'][listing['quality']['level']] += 1
            stats['patterns_by_type'][listing['pattern_info']['type']] += 1
            stats['total_downloads'] += listing['metrics']['downloads']
            stats['total_implementations'] += listing['metrics']['implementations']

            if listing['community']['rating'] > 0:
                ratings.append(listing['community']['rating'])

        if ratings:
            stats['avg_rating'] = sum(ratings) / len(ratings)

        # Get top contributors
        stats['top_contributors'] = self.reputation_system.get_top_contributors(10)

        return stats

    def get_pattern_analytics(self, listing_id: str) -> Dict[str, Any]:
        """Get detailed analytics for a pattern"""
        listing = self._get_listing(listing_id)
        if not listing:
            return {}

        analytics = {
            'basic_metrics': listing['metrics'],
            'community_metrics': listing['community'],

            'usage_timeline': self._get_usage_timeline(listing_id),
            'implementation_success_rate': self._calculate_implementation_success_rate(listing_id),
            'geographic_distribution': self._get_geographic_distribution(listing_id),
            'user_segments': self._analyze_user_segments(listing_id),

            'similar_patterns': self.pattern_library.find_similar_patterns(listing['pattern_id'], 0.7),
            'competing_patterns': self._find_competing_patterns(listing),

            'revenue': self._calculate_revenue(listing) if listing['pricing']['model'] != 'free' else 0
        }

        return analytics

# ==================== Supporting Classes ====================

class ReputationSystem:
    """Manage user reputation in the marketplace"""

    def __init__(self):
        self.reputations = defaultdict(int)
        self.actions = defaultdict(list)

        # Action point values
        self.point_values = {
            'pattern_shared': 10,
            'pattern_implemented': 1,
            'received_upvote': 2,
            'received_downvote': -1,
            'received_endorsement': 5,
            'pattern_certified': 20,
            'review_posted': 3,
            'gave_endorsement': 2,
            'bug_reported': 5,
            'bug_fixed': 15
        }

    def get_reputation(self, user_id: str) -> int:
        """Get user's current reputation"""
        return self.reputations[user_id]

    def award_points(self, user_id: str, action: str, multiplier: int = 1):
        """Award reputation points for an action"""
        points = self.point_values.get(action, 0) * multiplier
        self.reputations[user_id] += points

        # Track action
        self.actions[user_id].append({
            'action': action,
            'points': points,
            'timestamp': datetime.utcnow().isoformat()
        })

        # Check for level ups
        self._check_level_up(user_id)

    def get_top_contributors(self, limit: int = 10) -> List[Tuple[str, int]]:
        """Get top contributors by reputation"""
        sorted_users = sorted(self.reputations.items(), key=lambda x: x[1], reverse=True)
        return sorted_users[:limit]

    def _check_level_up(self, user_id: str):
        """Check if user reached new reputation level"""
        reputation = self.reputations[user_id]

        levels = {
            100: 'Contributor',
            500: 'Expert',
            1000: 'Master',
            5000: 'Guru',
            10000: 'Legend'
        }

        for threshold, title in levels.items():
            if reputation >= threshold:
                # Award badge/title
                pass

class QualityAssurance:
    """Quality assurance for marketplace patterns"""

    async def check_pattern(self, pattern: Dict) -> Dict[str, Any]:
        """Comprehensive quality check for patterns"""
        checks = {
            'has_description': bool(pattern['metadata'].get('description')),
            'has_test_cases': len(pattern['validation'].get('test_cases', [])) > 0,
            'has_documentation': bool(pattern.get('documentation')),
            'syntax_valid': await self._check_syntax(pattern),
            'performance_acceptable': await self._check_performance(pattern),
            'security_safe': await self._check_security(pattern),
            'no_duplicates': await self._check_duplicates(pattern)
        }

        passed = sum(checks.values())
        total = len(checks)

        # Determine quality level
        quality_level = PatternQuality.EXPERIMENTAL.value
        if passed >= total * 0.9:
            quality_level = PatternQuality.PRODUCTION.value
        elif passed >= total * 0.75:
            quality_level = PatternQuality.STABLE.value
        elif passed >= total * 0.6:
            quality_level = PatternQuality.BETA.value

        return {
            'passed': passed >= total * 0.6,
            'score': passed / total,
            'checks_passed': passed,
            'checks_total': total,
            'quality_level': quality_level,
            'checks': checks,
            'issues': [check for check, passed in checks.items() if not passed]
        }

    async def _check_syntax(self, pattern: Dict) -> bool:
        """Check pattern syntax validity"""
        # Implement syntax checking
        return True

    async def _check_performance(self, pattern: Dict) -> bool:
        """Check pattern performance"""
        # Check execution time
        if pattern['performance'].get('avg_execution_time_ms', 0) > 1000:
            return False
        return True

    async def _check_security(self, pattern: Dict) -> bool:
        """Check pattern security"""
        # Check for potential security issues
        pattern_str = json.dumps(pattern)

        # Check for dangerous patterns
        dangerous = ['eval(', 'exec(', 'os.system', '__import__']
        for danger in dangerous:
            if danger in pattern_str:
                return False

        return True

    async def _check_duplicates(self, pattern: Dict) -> bool:
        """Check if pattern is duplicate"""
        # This would check against existing patterns
        return True