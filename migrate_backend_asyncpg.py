#!/usr/bin/env python3
"""
Migrate backend_engine.py from psycopg2 (self.db_connection) to asyncpg (self.db_pool)

This script:
1. Replaces all db_connection references with db_pool
2. Converts cursor operations to asyncpg patterns
3. Updates placeholder syntax from %s to $1, $2, $3
4. Makes database functions async
"""

import re
import sys

def convert_placeholders(sql, num_params):
    """Convert %s placeholders to $1, $2, $3 format"""
    result = sql
    for i in range(num_params, 0, -1):  # Reverse order to avoid replacing $1 in $10
        result = result.replace('%s', f'${i}', 1)
    return result

def count_placeholders(sql):
    """Count number of %s placeholders in SQL"""
    return sql.count('%s')

def migrate_cursor_pattern(content):
    """Migrate psycopg2 cursor patterns to asyncpg"""

    # Pattern 1: Simple INSERT/UPDATE/DELETE with execute
    # Before: cursor.execute("INSERT ...", (param1, param2))
    # After: await conn.execute("INSERT ...", param1, param2)

    lines = content.split('\n')
    output_lines = []
    i = 0

    while i < len(lines):
        line = lines[i]

        # Check if this is a cursor creation line
        if 'cursor = self.db_connection.cursor()' in line:
            # Find the corresponding cursor.execute() and cursor.close()
            indent = len(line) - len(line.lstrip())
            block_start = i
            execute_lines = []
            j = i + 1

            # Collect all lines until cursor.close() or end of function
            while j < len(lines):
                next_line = lines[j]

                # Check for cursor.execute
                if 'cursor.execute(' in next_line:
                    execute_start = j
                    # Collect multi-line execute statement
                    paren_count = next_line.count('(') - next_line.count(')')
                    execute_block = [next_line]
                    j += 1
                    while j < len(lines) and paren_count > 0:
                        execute_block.append(lines[j])
                        paren_count += lines[j].count('(') - lines[j].count(')')
                        j += 1
                    execute_lines.append((execute_start, execute_block))
                    continue

                # Check for end of cursor block
                if 'cursor.close()' in next_line:
                    # Convert the entire block to asyncpg
                    output_lines.append(' ' * indent + 'async with self.db_pool.acquire() as conn:')

                    for exec_start, exec_block in execute_lines:
                        # Determine operation type
                        full_execute = ' '.join(exec_block)

                        if 'cursor.fetchall()' in '\n'.join(lines[j:min(j+5, len(lines))]):
                            # SELECT with fetchall -> fetch
                            output_lines.extend([' ' * (indent + 4) + l.strip() for l in exec_block])
                        elif 'cursor.fetchone()' in '\n'.join(lines[j:min(j+5, len(lines))]):
                            # SELECT with fetchone -> fetchrow
                            output_lines.extend([' ' * (indent + 4) + l.strip() for l in exec_block])
                        else:
                            # INSERT/UPDATE/DELETE -> execute
                            output_lines.extend([' ' * (indent + 4) + l.strip() for l in exec_block])

                    i = j + 1
                    break

                j += 1
            else:
                # No cursor.close() found, just output the line as is
                output_lines.append(line)
                i += 1
        else:
            output_lines.append(line)
            i += 1

    return '\n'.join(output_lines)

def main():
    input_file = 'c:\\Users\\<USER>\\Documents\\siemless_v2\\engines\\backend\\backend_engine.py'
    output_file = 'c:\\Users\\<USER>\\Documents\\siemless_v2\\engines\\backend\\backend_engine.py.new'

    print(f"Reading {input_file}...")
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()

    print("Original file size:", len(content), "bytes")

    # Step 1: Replace self.db_connection with self.db_pool (simple references)
    print("\nStep 1: Replacing simple db_connection references...")
    # Don't replace in cursor patterns yet
    content = re.sub(r'self\.db_connection(?!\.cursor\(\))', 'self.db_pool', content)

    # Step 2: Replace cursor patterns (complex transformation)
    print("\nStep 2: Converting cursor patterns to asyncpg...")
    # This is complex - let's do it manually for now

    # For now, let's just do basic replacements
    replacements = [
        # Simple db_connection references
        ('LogSourceIdentifier(self.db_connection)', 'LogSourceIdentifier(self.db_pool)'),
        ('UpdateScheduler(self.db_connection,', 'UpdateScheduler(self.db_pool,'),
        ('db_connection=self.db_connection', 'db_pool=self.db_pool'),
        ('MITREAttackMapper(\n            redis_client=self.redis_client,\n            db_connection=self.db_connection,',
         'MITREAttackMapper(\n            redis_client=self.redis_client,\n            db_pool=self.db_pool,'),
        ('MITREAIIntelligence(\n                redis_client=self.redis_client,\n                db_connection=self.db_connection,',
         'MITREAIIntelligence(\n                redis_client=self.redis_client,\n                db_pool=self.db_pool,'),
        ('LogRetentionPolicyEngine(\n            db_connection=self.db_connection,',
         'LogRetentionPolicyEngine(\n            db_pool=self.db_pool,'),

        # Commit statements - asyncpg auto-commits
        ('self.db_connection.commit()', '# asyncpg auto-commits'),
    ]

    for old, new in replacements:
        if old in content:
            content = content.replace(old, new)
            print(f"  - Replaced: {old[:50]}...")

    print(f"\nNew file size: {len(content)} bytes")
    print(f"Writing to {output_file}...")

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)

    print("\nDone! Review the .new file and compare with original.")
    print("\nNOTE: Cursor patterns need manual migration. See migration guide.")

if __name__ == '__main__':
    main()
