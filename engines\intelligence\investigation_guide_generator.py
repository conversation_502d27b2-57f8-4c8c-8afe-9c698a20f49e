"""
Investigation Guide Generator for Intelligence Engine
Automatically creates step-by-step investigation procedures for security alerts
"""

import json
from typing import Dict, List, Any, Optional
from datetime import datetime
import asyncio

class InvestigationGuideGenerator:
    """Generate human-readable investigation guides from security alerts"""

    def __init__(self, db_connection, redis_client, logger):
        self.db = db_connection
        self.redis = redis_client
        self.logger = logger

        # Investigation templates by alert type
        self.templates = {
            'credential_access': self._credential_access_template,
            'lateral_movement': self._lateral_movement_template,
            'data_exfiltration': self._data_exfiltration_template,
            'malware_detection': self._malware_detection_template,
            'privilege_escalation': self._privilege_escalation_template,
            'persistence': self._persistence_template
        }

    async def generate_guide(self, alert: Dict[str, Any]) -> Dict[str, Any]:
        """Generate investigation guide for an alert"""
        try:
            # Determine alert type
            alert_type = await self._classify_alert(alert)

            # Extract entities
            entities = await self._extract_entities(alert)

            # Generate investigation steps
            steps = await self._generate_investigation_steps(alert_type, entities)

            # Create hunt queries
            queries = await self._generate_hunt_queries(alert_type, entities)

            # Add remediation recommendations
            remediation = await self._generate_remediation(alert_type, entities)

            # Build complete guide
            guide = {
                'id': f"guide_{datetime.utcnow().timestamp()}",
                'alert_id': alert.get('id'),
                'alert_type': alert_type,
                'severity': alert.get('severity', 'medium'),
                'entities': entities,
                'investigation_steps': steps,
                'hunt_queries': queries,
                'iocs': await self._extract_iocs(alert),
                'remediation': remediation,
                'context': await self._build_context(alert, entities),
                'estimated_time': self._estimate_investigation_time(steps),
                'automation_potential': self._calculate_automation_potential(steps)
            }

            # Store guide in database
            await self._store_guide(guide)

            # Publish for delivery engine
            await self.redis.publish('intelligence.investigation_guide', json.dumps(guide))

            self.logger.info(f"Generated investigation guide: {guide['id']}")
            return guide

        except Exception as e:
            self.logger.error(f"Failed to generate investigation guide: {e}")
            raise

    async def _classify_alert(self, alert: Dict) -> str:
        """Classify alert into investigation category"""
        # Use MITRE ATT&CK mapping if available
        if 'mitre_technique' in alert:
            technique = alert['mitre_technique']
            if 'T1078' in technique or 'T1110' in technique:
                return 'credential_access'
            elif 'T1021' in technique or 'T1570' in technique:
                return 'lateral_movement'
            elif 'T1048' in technique or 'T1041' in technique:
                return 'data_exfiltration'
            elif 'T1055' in technique or 'T1543' in technique:
                return 'persistence'

        # Fallback to keyword matching
        alert_text = json.dumps(alert).lower()
        if 'credential' in alert_text or 'password' in alert_text:
            return 'credential_access'
        elif 'lateral' in alert_text or 'movement' in alert_text:
            return 'lateral_movement'
        elif 'exfil' in alert_text or 'transfer' in alert_text:
            return 'data_exfiltration'
        elif 'malware' in alert_text or 'virus' in alert_text:
            return 'malware_detection'

        return 'general_investigation'

    async def _extract_entities(self, alert: Dict) -> Dict:
        """Extract key entities from alert"""
        entities = {
            'users': [],
            'hosts': [],
            'ips': [],
            'files': [],
            'processes': [],
            'domains': []
        }

        # Extract from common fields
        if 'user' in alert:
            entities['users'].append(alert['user'])
        if 'source_ip' in alert:
            entities['ips'].append(alert['source_ip'])
        if 'destination_ip' in alert:
            entities['ips'].append(alert['destination_ip'])
        if 'hostname' in alert:
            entities['hosts'].append(alert['hostname'])
        if 'file_hash' in alert:
            entities['files'].append({'hash': alert['file_hash']})
        if 'process_name' in alert:
            entities['processes'].append(alert['process_name'])

        return entities

    async def _generate_investigation_steps(self, alert_type: str, entities: Dict) -> List[Dict]:
        """Generate step-by-step investigation procedure"""
        # Get template function for alert type
        template_func = self.templates.get(alert_type, self._general_template)
        return await template_func(entities)

    def _credential_access_template(self, entities: Dict) -> List[Dict]:
        """Investigation template for credential access alerts"""
        steps = []

        if entities['users']:
            user = entities['users'][0]
            steps.extend([
                {
                    'step': 1,
                    'action': 'Verify User Activity',
                    'description': f'Check if user {user} is currently active and this is expected behavior',
                    'query_hint': f'index=* user="{user}" | stats count by action',
                    'automation': 'partial'
                },
                {
                    'step': 2,
                    'action': 'Review Authentication History',
                    'description': f'Examine recent authentication attempts for {user}',
                    'query_hint': f'EventID=4624 OR EventID=4625 user="{user}"',
                    'automation': 'full'
                },
                {
                    'step': 3,
                    'action': 'Check Geographic Anomalies',
                    'description': 'Look for logins from unusual locations',
                    'query_hint': f'user="{user}" | iplocation src_ip',
                    'automation': 'full'
                }
            ])

        if entities['ips']:
            ip = entities['ips'][0]
            steps.append({
                'step': len(steps) + 1,
                'action': 'Investigate Source IP',
                'description': f'Check reputation and other activities from {ip}',
                'query_hint': f'src_ip="{ip}" | stats count by action, user',
                'automation': 'full'
            })

        steps.append({
            'step': len(steps) + 1,
            'action': 'Review Privileged Account Usage',
            'description': 'Check if any privileged accounts were compromised',
            'query_hint': 'EventID=4672 | stats count by user',
            'automation': 'partial'
        })

        return steps

    def _lateral_movement_template(self, entities: Dict) -> List[Dict]:
        """Investigation template for lateral movement"""
        steps = [
            {
                'step': 1,
                'action': 'Map Movement Path',
                'description': 'Identify the sequence of systems accessed',
                'query_hint': 'EventID=4624 LogonType=3 OR LogonType=10',
                'automation': 'full'
            },
            {
                'step': 2,
                'action': 'Check Remote Execution',
                'description': 'Look for remote command execution',
                'query_hint': 'EventID=4688 (wmic OR psexec OR winrm)',
                'automation': 'full'
            },
            {
                'step': 3,
                'action': 'Examine Network Connections',
                'description': 'Review unusual SMB/RDP connections',
                'query_hint': 'port=445 OR port=3389 | stats count by src_ip, dest_ip',
                'automation': 'full'
            }
        ]
        return steps

    def _data_exfiltration_template(self, entities: Dict) -> List[Dict]:
        """Investigation template for data exfiltration"""
        steps = [
            {
                'step': 1,
                'action': 'Analyze Data Transfer Volume',
                'description': 'Check for unusual outbound data transfers',
                'query_hint': 'bytes_out > 1000000 | stats sum(bytes_out) by dest_ip',
                'automation': 'full'
            },
            {
                'step': 2,
                'action': 'Identify Sensitive Data Access',
                'description': 'Review access to sensitive files/databases',
                'query_hint': 'action=read (sensitive OR confidential OR database)',
                'automation': 'partial'
            },
            {
                'step': 3,
                'action': 'Check External Destinations',
                'description': 'Investigate external IPs receiving data',
                'query_hint': 'dest_ip!=10.0.0.0/8 bytes_out>1000000',
                'automation': 'full'
            }
        ]
        return steps

    def _malware_detection_template(self, entities: Dict) -> List[Dict]:
        """Investigation template for malware detection"""
        steps = [
            {
                'step': 1,
                'action': 'Isolate Affected System',
                'description': 'Immediately isolate the infected system from network',
                'query_hint': 'N/A - Manual action required',
                'automation': 'manual'
            },
            {
                'step': 2,
                'action': 'Identify Malware Indicators',
                'description': 'Collect file hashes, registry keys, network indicators',
                'query_hint': 'EventID=4688 | regex process_name=".*\\.exe$"',
                'automation': 'full'
            },
            {
                'step': 3,
                'action': 'Check Lateral Spread',
                'description': 'Verify if malware has spread to other systems',
                'query_hint': 'file_hash=* | stats dc(hostname) by file_hash',
                'automation': 'full'
            }
        ]
        return steps

    def _privilege_escalation_template(self, entities: Dict) -> List[Dict]:
        """Investigation template for privilege escalation"""
        return [
            {
                'step': 1,
                'action': 'Review Privilege Changes',
                'description': 'Check recent privilege modifications',
                'query_hint': 'EventID=4728 OR EventID=4732 OR EventID=4756',
                'automation': 'full'
            },
            {
                'step': 2,
                'action': 'Analyze Process Creation',
                'description': 'Look for suspicious process elevation',
                'query_hint': 'EventID=4688 TokenElevationType!=%%1936',
                'automation': 'full'
            }
        ]

    def _persistence_template(self, entities: Dict) -> List[Dict]:
        """Investigation template for persistence mechanisms"""
        return [
            {
                'step': 1,
                'action': 'Check Scheduled Tasks',
                'description': 'Review newly created scheduled tasks',
                'query_hint': 'EventID=4698 OR schtasks',
                'automation': 'full'
            },
            {
                'step': 2,
                'action': 'Review Registry Modifications',
                'description': 'Check Run keys and service creation',
                'query_hint': 'EventID=4657 (CurrentVersion\\Run OR Services)',
                'automation': 'full'
            }
        ]

    def _general_template(self, entities: Dict) -> List[Dict]:
        """Generic investigation template"""
        return [
            {
                'step': 1,
                'action': 'Initial Triage',
                'description': 'Gather basic information about the alert',
                'query_hint': 'index=* | head 100',
                'automation': 'partial'
            },
            {
                'step': 2,
                'action': 'Entity Investigation',
                'description': 'Investigate all entities involved',
                'query_hint': 'index=* (user=* OR src_ip=* OR dest_ip=*)',
                'automation': 'full'
            },
            {
                'step': 3,
                'action': 'Timeline Analysis',
                'description': 'Build timeline of events',
                'query_hint': 'index=* | sort _time',
                'automation': 'full'
            }
        ]

    async def _generate_hunt_queries(self, alert_type: str, entities: Dict) -> List[Dict]:
        """Generate threat hunting queries"""
        queries = []

        # Universal queries
        queries.append({
            'name': 'Timeline View',
            'description': 'Complete timeline around the alert',
            'splunk': f'index=* earliest=-1h latest=+1h | sort _time',
            'elastic': f'* | sort @timestamp',
            'sentinel': f'* | order by TimeGenerated'
        })

        # Entity-specific queries
        for user in entities.get('users', []):
            queries.append({
                'name': f'User Activity - {user}',
                'description': f'All activity for user {user}',
                'splunk': f'index=* user="{user}" | stats count by action',
                'elastic': f'user.name:"{user}"',
                'sentinel': f'| where AccountName == "{user}"'
            })

        for ip in entities.get('ips', []):
            queries.append({
                'name': f'IP Investigation - {ip}',
                'description': f'All traffic involving {ip}',
                'splunk': f'index=* (src_ip="{ip}" OR dest_ip="{ip}")',
                'elastic': f'source.ip:"{ip}" OR destination.ip:"{ip}"',
                'sentinel': f'| where SourceIP == "{ip}" or DestinationIP == "{ip}"'
            })

        return queries

    async def _extract_iocs(self, alert: Dict) -> List[Dict]:
        """Extract Indicators of Compromise"""
        iocs = []

        # Extract IPs
        for field in ['src_ip', 'dest_ip', 'source_ip', 'destination_ip']:
            if field in alert:
                iocs.append({
                    'type': 'ip',
                    'value': alert[field],
                    'context': f'Found in {field}'
                })

        # Extract file hashes
        for field in ['file_hash', 'md5', 'sha256', 'sha1']:
            if field in alert:
                iocs.append({
                    'type': 'hash',
                    'value': alert[field],
                    'context': f'File hash from {field}'
                })

        # Extract domains
        for field in ['domain', 'url', 'hostname']:
            if field in alert:
                iocs.append({
                    'type': 'domain',
                    'value': alert[field],
                    'context': f'Domain from {field}'
                })

        return iocs

    async def _generate_remediation(self, alert_type: str, entities: Dict) -> List[str]:
        """Generate remediation recommendations"""
        remediation = []

        if alert_type == 'credential_access':
            remediation.extend([
                'Reset potentially compromised credentials',
                'Enable MFA if not already active',
                'Review and restrict account permissions',
                'Check for unauthorized privilege escalation'
            ])
        elif alert_type == 'lateral_movement':
            remediation.extend([
                'Isolate affected systems from network',
                'Disable compromised accounts',
                'Review firewall rules for unnecessary access',
                'Implement network segmentation'
            ])
        elif alert_type == 'data_exfiltration':
            remediation.extend([
                'Block identified external IPs',
                'Review DLP policies',
                'Audit access to sensitive data',
                'Implement egress filtering'
            ])
        elif alert_type == 'malware_detection':
            remediation.extend([
                'Quarantine infected files',
                'Run full system scan',
                'Update antivirus signatures',
                'Rebuild system if necessary'
            ])
        else:
            remediation.extend([
                'Document incident details',
                'Preserve evidence for analysis',
                'Review security controls',
                'Update detection rules'
            ])

        return remediation

    async def _build_context(self, alert: Dict, entities: Dict) -> Dict:
        """Build additional context for investigation"""
        context = {
            'alert_time': alert.get('timestamp', datetime.utcnow().isoformat()),
            'data_sources': alert.get('source', 'unknown'),
            'confidence': alert.get('confidence', 'medium'),
            'related_alerts': await self._find_related_alerts(alert),
            'entity_risk_scores': await self._calculate_entity_risk(entities),
            'historical_context': await self._get_historical_context(entities)
        }
        return context

    async def _find_related_alerts(self, alert: Dict) -> List[str]:
        """Find related alerts in the system"""
        # Placeholder - would query database for related alerts
        return []

    async def _calculate_entity_risk(self, entities: Dict) -> Dict:
        """Calculate risk scores for entities"""
        risks = {}
        for entity_type, entity_list in entities.items():
            for entity in entity_list:
                # Placeholder risk calculation
                risks[str(entity)] = 'medium'
        return risks

    async def _get_historical_context(self, entities: Dict) -> Dict:
        """Get historical context for entities"""
        # Placeholder - would query historical data
        return {
            'previous_incidents': 0,
            'first_seen': datetime.utcnow().isoformat(),
            'baseline_behavior': 'normal'
        }

    def _estimate_investigation_time(self, steps: List[Dict]) -> int:
        """Estimate time needed for investigation in minutes"""
        total_time = 0
        for step in steps:
            if step.get('automation') == 'full':
                total_time += 2  # Automated steps are quick
            elif step.get('automation') == 'partial':
                total_time += 5  # Partially automated
            else:
                total_time += 10  # Manual steps take longer
        return total_time

    def _calculate_automation_potential(self, steps: List[Dict]) -> float:
        """Calculate how much of the investigation can be automated"""
        if not steps:
            return 0.0

        automated_steps = sum(1 for s in steps if s.get('automation') in ['full', 'partial'])
        return (automated_steps / len(steps)) * 100

    async def _store_guide(self, guide: Dict) -> None:
        """Store investigation guide in database"""
        query = """
            INSERT INTO investigation_guides
            (alert_type, guide_content, effectiveness_score, created_at)
            VALUES (%s, %s, %s, %s)
        """
        await self.db.execute(
            query,
            (
                guide['alert_type'],
                json.dumps(guide),
                0.0,  # Initial effectiveness score
                datetime.utcnow()
            )
        )