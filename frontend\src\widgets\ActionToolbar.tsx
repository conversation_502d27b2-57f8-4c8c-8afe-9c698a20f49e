import React, { useState, useEffect } from 'react'
import { workflowAPI } from '../api/client'
import {
  Shield, Lock, Download, RefreshCw, UserX, FileSearch,
  AlertOctagon, Send, Eye, Zap, PlayCircle, StopCircle,
  CheckCircle, XCircle, Clock, Loader, ChevronRight,
  Info, AlertTriangle, Settings
} from 'lucide-react'

interface WorkflowAction {
  id: string
  name: string
  icon: React.ElementType
  category: 'response' | 'containment' | 'investigation' | 'remediation'
  description: string
  requiredRole: string
  parameters?: Record<string, any>
  confirmationRequired: boolean
  estimatedTime: string
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
}

interface WorkflowExecution {
  workflowId: string
  status: 'running' | 'completed' | 'failed' | 'stopped'
  startTime: string
  endTime?: string
  output?: any
  error?: string
  progress?: number
}

interface ActionToolbarProps {
  caseId: string
  entityId?: string
  onActionComplete?: (action: WorkflowAction, result: any) => void
}

export const ActionToolbar: React.FC<ActionToolbarProps> = ({
  caseId,
  entityId,
  onActionComplete
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [executingWorkflows, setExecutingWorkflows] = useState<Map<string, WorkflowExecution>>(new Map())
  const [showConfirmation, setShowConfirmation] = useState<WorkflowAction | null>(null)
  const [workflowParameters, setWorkflowParameters] = useState<Record<string, any>>({})
  const [expandedAction, setExpandedAction] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  // Define available workflow actions
  const workflowActions: WorkflowAction[] = [
    // Response Actions
    {
      id: 'isolate_host',
      name: 'Isolate Host',
      icon: Lock,
      category: 'response',
      description: 'Immediately isolate the affected host from network',
      requiredRole: 'analyst',
      confirmationRequired: true,
      estimatedTime: '< 1 min',
      riskLevel: 'high',
      parameters: {
        hostname: '',
        isolation_type: 'full' // full, partial, monitoring
      }
    },
    {
      id: 'disable_account',
      name: 'Disable User Account',
      icon: UserX,
      category: 'response',
      description: 'Disable compromised user account in Active Directory',
      requiredRole: 'analyst',
      confirmationRequired: true,
      estimatedTime: '< 1 min',
      riskLevel: 'medium',
      parameters: {
        username: '',
        reason: ''
      }
    },
    {
      id: 'block_ip',
      name: 'Block IP Address',
      icon: Shield,
      category: 'response',
      description: 'Add IP to firewall block list',
      requiredRole: 'analyst',
      confirmationRequired: true,
      estimatedTime: '< 1 min',
      riskLevel: 'low',
      parameters: {
        ip_address: entityId || '',
        duration: '24h'
      }
    },

    // Containment Actions
    {
      id: 'snapshot_vm',
      name: 'Snapshot VM',
      icon: Download,
      category: 'containment',
      description: 'Create forensic snapshot of virtual machine',
      requiredRole: 'analyst',
      confirmationRequired: false,
      estimatedTime: '5-10 min',
      riskLevel: 'low',
      parameters: {
        vm_name: '',
        include_memory: true
      }
    },
    {
      id: 'collect_forensics',
      name: 'Collect Forensics',
      icon: FileSearch,
      category: 'containment',
      description: 'Collect memory dump and disk artifacts',
      requiredRole: 'engineer',
      confirmationRequired: false,
      estimatedTime: '15-30 min',
      riskLevel: 'low',
      parameters: {
        target_host: '',
        collection_type: 'full' // full, memory_only, disk_only
      }
    },
    {
      id: 'reset_credentials',
      name: 'Force Password Reset',
      icon: RefreshCw,
      category: 'containment',
      description: 'Force password reset for affected accounts',
      requiredRole: 'analyst',
      confirmationRequired: true,
      estimatedTime: '< 1 min',
      riskLevel: 'medium',
      parameters: {
        user_list: [],
        notify_users: true
      }
    },

    // Investigation Actions
    {
      id: 'threat_hunt',
      name: 'Initiate Threat Hunt',
      icon: Eye,
      category: 'investigation',
      description: 'Launch automated threat hunting across environment',
      requiredRole: 'analyst',
      confirmationRequired: false,
      estimatedTime: '30-60 min',
      riskLevel: 'low',
      parameters: {
        ioc_list: [],
        scope: 'enterprise' // enterprise, subnet, host_group
      }
    },
    {
      id: 'expand_timeline',
      name: 'Expand Timeline',
      icon: Clock,
      category: 'investigation',
      description: 'Gather extended timeline data for entity',
      requiredRole: 'viewer',
      confirmationRequired: false,
      estimatedTime: '5-10 min',
      riskLevel: 'low',
      parameters: {
        time_range: '24h',
        entity: entityId || ''
      }
    },

    // Remediation Actions
    {
      id: 'patch_system',
      name: 'Deploy Patch',
      icon: Settings,
      category: 'remediation',
      description: 'Deploy security patch to vulnerable systems',
      requiredRole: 'engineer',
      confirmationRequired: true,
      estimatedTime: '10-20 min',
      riskLevel: 'medium',
      parameters: {
        patch_id: '',
        target_systems: [],
        reboot_required: false
      }
    },
    {
      id: 'update_rules',
      name: 'Update Detection Rules',
      icon: Zap,
      category: 'remediation',
      description: 'Deploy new detection rules to SIEM',
      requiredRole: 'engineer',
      confirmationRequired: false,
      estimatedTime: '< 5 min',
      riskLevel: 'low',
      parameters: {
        rule_package: '',
        siem_targets: ['splunk', 'elastic']
      }
    }
  ]

  // Execute workflow
  const executeWorkflow = async (action: WorkflowAction) => {
    if (action.confirmationRequired && !showConfirmation) {
      setShowConfirmation(action)
      return
    }

    setLoading(true)
    const execution: WorkflowExecution = {
      workflowId: `${action.id}_${Date.now()}`,
      status: 'running',
      startTime: new Date().toISOString(),
      progress: 0
    }

    setExecutingWorkflows(prev => new Map(prev).set(action.id, execution))

    try {
      // Call API to start workflow
      const response = await workflowAPI.startWorkflow(action.id, {
        case_id: caseId,
        entity_id: entityId,
        ...workflowParameters[action.id]
      })

      // Simulate progress updates
      let progress = 0
      const progressInterval = setInterval(() => {
        progress += 10
        if (progress <= 90) {
          setExecutingWorkflows(prev => {
            const updated = new Map(prev)
            const exec = updated.get(action.id)
            if (exec) {
              exec.progress = progress
            }
            return updated
          })
        }
      }, 1000)

      // Wait for completion
      setTimeout(() => {
        clearInterval(progressInterval)
        const completedExecution: WorkflowExecution = {
          ...execution,
          status: 'completed',
          endTime: new Date().toISOString(),
          progress: 100,
          output: response.data
        }

        setExecutingWorkflows(prev => {
          const updated = new Map(prev)
          updated.set(action.id, completedExecution)
          return updated
        })

        if (onActionComplete) {
          onActionComplete(action, response.data)
        }

        // Clear execution after 5 seconds
        setTimeout(() => {
          setExecutingWorkflows(prev => {
            const updated = new Map(prev)
            updated.delete(action.id)
            return updated
          })
        }, 5000)
      }, 5000)

    } catch (error: any) {
      const failedExecution: WorkflowExecution = {
        ...execution,
        status: 'failed',
        endTime: new Date().toISOString(),
        error: error.message
      }

      setExecutingWorkflows(prev => {
        const updated = new Map(prev)
        updated.set(action.id, failedExecution)
        return updated
      })
    } finally {
      setLoading(false)
      setShowConfirmation(null)
    }
  }

  // Stop workflow
  const stopWorkflow = async (actionId: string) => {
    const execution = executingWorkflows.get(actionId)
    if (execution && execution.status === 'running') {
      try {
        await workflowAPI.stopWorkflow(execution.workflowId)
        setExecutingWorkflows(prev => {
          const updated = new Map(prev)
          const exec = updated.get(actionId)
          if (exec) {
            exec.status = 'stopped'
            exec.endTime = new Date().toISOString()
          }
          return updated
        })
      } catch (error) {
        console.error('Failed to stop workflow:', error)
      }
    }
  }

  // Filter actions by category
  const filteredActions = workflowActions.filter(
    action => selectedCategory === 'all' || action.category === selectedCategory
  )

  // Get risk color
  const getRiskColor = (risk: string) => {
    const colors = {
      low: 'text-green-600 bg-green-100',
      medium: 'text-yellow-600 bg-yellow-100',
      high: 'text-orange-600 bg-orange-100',
      critical: 'text-red-600 bg-red-100'
    }
    return colors[risk as keyof typeof colors] || colors.low
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Loader className="animate-spin text-blue-500" size={16} />
      case 'completed':
        return <CheckCircle className="text-green-500" size={16} />
      case 'failed':
        return <XCircle className="text-red-500" size={16} />
      case 'stopped':
        return <StopCircle className="text-gray-500" size={16} />
      default:
        return null
    }
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header */}
      <div className="bg-gradient-to-r from-red-500 to-orange-500 text-white p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-bold flex items-center gap-2">
              <AlertOctagon size={24} />
              Response Actions
            </h2>
            <p className="text-red-100 text-sm mt-1">
              Case: {caseId} {entityId && `| Entity: ${entityId}`}
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-red-100">Active Workflows</div>
            <div className="text-2xl font-bold">
              {Array.from(executingWorkflows.values()).filter(e => e.status === 'running').length}
            </div>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="p-3 border-b bg-gray-50">
        <div className="flex gap-2">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`px-3 py-1 rounded text-sm ${
              selectedCategory === 'all' ? 'bg-blue-500 text-white' : 'bg-white border'
            }`}
          >
            All Actions
          </button>
          <button
            onClick={() => setSelectedCategory('response')}
            className={`px-3 py-1 rounded text-sm ${
              selectedCategory === 'response' ? 'bg-blue-500 text-white' : 'bg-white border'
            }`}
          >
            Response
          </button>
          <button
            onClick={() => setSelectedCategory('containment')}
            className={`px-3 py-1 rounded text-sm ${
              selectedCategory === 'containment' ? 'bg-blue-500 text-white' : 'bg-white border'
            }`}
          >
            Containment
          </button>
          <button
            onClick={() => setSelectedCategory('investigation')}
            className={`px-3 py-1 rounded text-sm ${
              selectedCategory === 'investigation' ? 'bg-blue-500 text-white' : 'bg-white border'
            }`}
          >
            Investigation
          </button>
          <button
            onClick={() => setSelectedCategory('remediation')}
            className={`px-3 py-1 rounded text-sm ${
              selectedCategory === 'remediation' ? 'bg-blue-500 text-white' : 'bg-white border'
            }`}
          >
            Remediation
          </button>
        </div>
      </div>

      {/* Actions Grid */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {filteredActions.map(action => {
            const Icon = action.icon
            const execution = executingWorkflows.get(action.id)
            const isExpanded = expandedAction === action.id

            return (
              <div
                key={action.id}
                className={`border rounded-lg ${
                  execution ? 'border-blue-300 bg-blue-50' : ''
                }`}
              >
                {/* Action Card */}
                <div className="p-3">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-gray-100 rounded">
                        <Icon size={20} />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium">{action.name}</h3>
                        <p className="text-sm text-gray-600 mt-1">
                          {action.description}
                        </p>
                      </div>
                    </div>
                    {execution && (
                      <div className="ml-2">
                        {getStatusIcon(execution.status)}
                      </div>
                    )}
                  </div>

                  {/* Metadata */}
                  <div className="flex gap-3 text-xs mb-3">
                    <span className={`px-2 py-1 rounded ${getRiskColor(action.riskLevel)}`}>
                      {action.riskLevel} risk
                    </span>
                    <span className="flex items-center gap-1 text-gray-600">
                      <Clock size={12} />
                      {action.estimatedTime}
                    </span>
                    <span className="flex items-center gap-1 text-gray-600">
                      <Shield size={12} />
                      {action.requiredRole}
                    </span>
                  </div>

                  {/* Progress Bar */}
                  {execution && execution.status === 'running' && execution.progress !== undefined && (
                    <div className="mb-3">
                      <div className="flex justify-between text-xs text-gray-600 mb-1">
                        <span>Progress</span>
                        <span>{execution.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${execution.progress}%` }}
                        />
                      </div>
                    </div>
                  )}

                  {/* Error Message */}
                  {execution && execution.status === 'failed' && (
                    <div className="mb-3 p-2 bg-red-100 border border-red-300 rounded text-sm text-red-700">
                      <AlertTriangle size={14} className="inline mr-1" />
                      {execution.error || 'Workflow failed'}
                    </div>
                  )}

                  {/* Success Message */}
                  {execution && execution.status === 'completed' && (
                    <div className="mb-3 p-2 bg-green-100 border border-green-300 rounded text-sm text-green-700">
                      <CheckCircle size={14} className="inline mr-1" />
                      Workflow completed successfully
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    {!execution || execution.status === 'failed' || execution.status === 'stopped' ? (
                      <>
                        <button
                          onClick={() => executeWorkflow(action)}
                          className="flex-1 px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 flex items-center justify-center gap-2"
                          disabled={loading}
                        >
                          <PlayCircle size={16} />
                          Execute
                        </button>
                        {action.parameters && (
                          <button
                            onClick={() => setExpandedAction(isExpanded ? null : action.id)}
                            className="px-3 py-2 border rounded text-sm hover:bg-gray-50"
                          >
                            <Settings size={16} />
                          </button>
                        )}
                      </>
                    ) : execution.status === 'running' ? (
                      <button
                        onClick={() => stopWorkflow(action.id)}
                        className="flex-1 px-3 py-2 bg-red-500 text-white rounded text-sm hover:bg-red-600 flex items-center justify-center gap-2"
                      >
                        <StopCircle size={16} />
                        Stop
                      </button>
                    ) : null}
                  </div>

                  {/* Parameters Form (Expanded) */}
                  {isExpanded && action.parameters && (
                    <div className="mt-3 p-3 bg-gray-50 rounded space-y-2">
                      <h4 className="text-sm font-medium mb-2">Parameters</h4>
                      {Object.entries(action.parameters).map(([key, defaultValue]) => (
                        <div key={key}>
                          <label className="block text-xs text-gray-600 mb-1">
                            {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </label>
                          {typeof defaultValue === 'boolean' ? (
                            <input
                              type="checkbox"
                              checked={workflowParameters[action.id]?.[key] ?? defaultValue}
                              onChange={(e) => setWorkflowParameters(prev => ({
                                ...prev,
                                [action.id]: {
                                  ...prev[action.id],
                                  [key]: e.target.checked
                                }
                              }))}
                            />
                          ) : Array.isArray(defaultValue) ? (
                            <input
                              type="text"
                              className="w-full px-2 py-1 border rounded text-sm"
                              placeholder={`Comma-separated ${key}`}
                              value={workflowParameters[action.id]?.[key]?.join(',') || ''}
                              onChange={(e) => setWorkflowParameters(prev => ({
                                ...prev,
                                [action.id]: {
                                  ...prev[action.id],
                                  [key]: e.target.value.split(',').filter(Boolean)
                                }
                              }))}
                            />
                          ) : (
                            <input
                              type="text"
                              className="w-full px-2 py-1 border rounded text-sm"
                              value={workflowParameters[action.id]?.[key] ?? defaultValue}
                              onChange={(e) => setWorkflowParameters(prev => ({
                                ...prev,
                                [action.id]: {
                                  ...prev[action.id],
                                  [key]: e.target.value
                                }
                              }))}
                            />
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <AlertTriangle className="text-orange-500" />
              Confirm Action
            </h3>
            <p className="mb-4">
              Are you sure you want to execute <strong>{showConfirmation.name}</strong>?
            </p>
            <div className="p-3 bg-orange-50 border border-orange-200 rounded mb-4">
              <p className="text-sm text-orange-700">
                <strong>Risk Level:</strong> {showConfirmation.riskLevel.toUpperCase()}
              </p>
              <p className="text-sm text-orange-700 mt-1">
                This action may have significant impact on system operations.
              </p>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => {
                  executeWorkflow(showConfirmation)
                }}
                className="flex-1 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
              >
                Confirm & Execute
              </button>
              <button
                onClick={() => setShowConfirmation(null)}
                className="flex-1 px-4 py-2 border rounded hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ActionToolbar