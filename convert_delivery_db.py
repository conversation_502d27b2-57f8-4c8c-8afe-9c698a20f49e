#!/usr/bin/env python3
"""
Batch convert delivery engine files from psycopg2 to asyncpg
"""

import re

files_to_convert = [
    'engines/delivery/business_context_manager.py',
    'engines/delivery/investigation_evidence_logger.py'
]

def convert_file(filepath):
    """Convert a single file from psycopg2 to asyncpg"""
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()

    original_content = content

    # 1. Remove psycopg2 imports and cursor factory
    content = re.sub(r'from psycopg2\.extras import RealDictCursor\n', '', content)
    content = re.sub(r', cursor_factory=RealDictCursor', '', content)

    # 2. Replace cursor patterns with asyncpg patterns
    # Pattern: cursor = self.db.cursor()
    content = re.sub(
        r'cursor = self\.db\.cursor\(\)\s*\n',
        '',
        content
    )

    # Pattern: cursor.execute(...) followed by cursor.close()
    # This is more complex - need to wrap in async with

    # Pattern: self.db.commit()
    content = re.sub(r'\s+self\.db\.commit\(\)\n', '\n', content)

    # Pattern: self.db.rollback()
    content = re.sub(r'\s+self\.db\.rollback\(\)\n', '', content)

    # Pattern: cursor.close()
    content = re.sub(r'\s+cursor\.close\(\)\n', '', content)

    # 3. Replace %s with $1, $2, etc. in SQL queries
    # This is tricky - we need context to number them properly
    # For now, flag these for manual review

    if content != original_content:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Converted {filepath}")
        print(f"⚠️  Manual review needed: Replace %s with $1, $2, etc.")
        print(f"⚠️  Manual review needed: Wrap cursor.execute() with async with self.db_pool.acquire()")
    else:
        print(f"ℹ️  No changes needed for {filepath}")

if __name__ == '__main__':
    for filepath in files_to_convert:
        convert_file(filepath)
        print()
