#!/usr/bin/env python3
"""
SIEMLess v2.0 - Query Engine
Translates detection rules into SIEM-specific queries and deployment formats
"""

import asyncio
import json
import logging
import sys
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
import uuid
from dataclasses import dataclass, asdict
import re

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from shared.base import BaseEngine
from shared.logging import log_everything

@dataclass
class SIEMTarget:
    """SIEM deployment target configuration"""
    siem_type: str
    name: str
    endpoint: str
    auth_config: Dict[str, Any]
    query_language: str
    deployment_method: str
    field_mappings: Dict[str, str]
    capabilities: List[str]

@dataclass
class TranslatedQuery:
    """Translated query for specific SIEM"""
    query_id: str
    original_rule_id: str
    siem_type: str
    query_language: str
    query_content: str
    deployment_format: str
    field_mappings: Dict[str, str]
    enrichment_data: Dict[str, Any]
    context_data: Dict[str, Any]
    confidence: float
    created_at: datetime
    metadata: Dict[str, Any]

class QueryEngine(BaseEngine):
    """
    Query Engine - Translates detection rules into SIEM-specific queries

    Core Responsibilities:
    - Translate Sigma rules to SIEM queries
    - Convert YARA rules to endpoint detection
    - Transform KQL to various SIEM languages
    - Deploy rules to target SIEM systems
    - Pull enrichment data for context
    - Apply field mappings and normalization
    - Handle SIEM-specific deployment formats
    """

    def __init__(self):
        super().__init__('query', '2.0.0')
        self.siem_targets = {}
        self.translated_queries = {}
        self.field_mappings = {}
        self.enrichment_cache = {}
        self.load_siem_configurations()
        self.load_field_mappings()

    def load_siem_configurations(self):
        """Load SIEM target configurations"""
        self.siem_targets = {
            'splunk': SIEMTarget(
                siem_type='splunk',
                name='Splunk Enterprise',
                endpoint='https://splunk.example.com:8089',
                auth_config={'username': 'admin', 'password': 'changeme'},
                query_language='spl',
                deployment_method='rest_api',
                field_mappings={
                    'process_name': 'Image',
                    'command_line': 'CommandLine',
                    'parent_process': 'ParentImage',
                    'source_ip': 'src_ip',
                    'destination_ip': 'dest_ip',
                    'event_time': '_time',
                    'hostname': 'host',
                    'user': 'user'
                },
                capabilities=['search', 'alerts', 'correlation', 'dashboards']
            ),

            'sentinel': SIEMTarget(
                siem_type='sentinel',
                name='Microsoft Sentinel',
                endpoint='https://management.azure.com',
                auth_config={'tenant_id': 'tenant', 'client_id': 'client', 'client_secret': 'secret'},
                query_language='kql',
                deployment_method='arm_template',
                field_mappings={
                    'process_name': 'ProcessName',
                    'command_line': 'CommandLine',
                    'parent_process': 'ParentProcessName',
                    'source_ip': 'SourceIP',
                    'destination_ip': 'DestinationIP',
                    'event_time': 'TimeGenerated',
                    'hostname': 'Computer',
                    'user': 'AccountName'
                },
                capabilities=['hunting', 'analytics', 'workbooks', 'automation']
            ),

            'qradar': SIEMTarget(
                siem_type='qradar',
                name='IBM QRadar',
                endpoint='https://qradar.example.com',
                auth_config={'api_token': 'token'},
                query_language='aql',
                deployment_method='custom_rule',
                field_mappings={
                    'process_name': 'Process Name',
                    'command_line': 'Process Command Line',
                    'parent_process': 'Parent Process',
                    'source_ip': 'sourceip',
                    'destination_ip': 'destinationip',
                    'event_time': 'starttime',
                    'hostname': 'hostname',
                    'user': 'username'
                },
                capabilities=['rules', 'offenses', 'searches', 'reports']
            ),

            'elastic_siem': SIEMTarget(
                siem_type='elastic_siem',
                name='Elastic Security',
                endpoint='https://elastic.example.com:9200',
                auth_config={'api_key': 'key'},
                query_language='eql',
                deployment_method='kibana_api',
                field_mappings={
                    'process_name': 'process.name',
                    'command_line': 'process.command_line',
                    'parent_process': 'process.parent.name',
                    'source_ip': 'source.ip',
                    'destination_ip': 'destination.ip',
                    'event_time': '@timestamp',
                    'hostname': 'host.hostname',
                    'user': 'user.name'
                },
                capabilities=['detection_rules', 'timelines', 'cases', 'ml_jobs']
            ),

            'chronicle': SIEMTarget(
                siem_type='chronicle',
                name='Google Chronicle',
                endpoint='https://chronicle.googleapis.com',
                auth_config={'service_account': 'path/to/key.json'},
                query_language='yara_l',
                deployment_method='api',
                field_mappings={
                    'process_name': 'target.process.file.full_path',
                    'command_line': 'target.process.command_line',
                    'parent_process': 'principal.process.file.full_path',
                    'source_ip': 'principal.ip',
                    'destination_ip': 'target.ip',
                    'event_time': 'metadata.event_timestamp',
                    'hostname': 'principal.hostname',
                    'user': 'principal.user.userid'
                },
                capabilities=['hunting', 'investigations', 'iocs', 'rules']
            )
        }

    def load_field_mappings(self):
        """Load common field mappings and transformations"""
        self.field_mappings = {
            'common_to_splunk': {
                'process.name': 'Image',
                'process.command_line': 'CommandLine',
                'process.parent.name': 'ParentImage',
                'source.ip': 'src_ip',
                'destination.ip': 'dest_ip',
                'user.name': 'user',
                'host.hostname': 'host',
                '@timestamp': '_time'
            },
            'common_to_sentinel': {
                'process.name': 'ProcessName',
                'process.command_line': 'CommandLine',
                'process.parent.name': 'ParentProcessName',
                'source.ip': 'SourceIP',
                'destination.ip': 'DestinationIP',
                'user.name': 'AccountName',
                'host.hostname': 'Computer',
                '@timestamp': 'TimeGenerated'
            },
            'common_to_qradar': {
                'process.name': '"Process Name"',
                'process.command_line': '"Process Command Line"',
                'process.parent.name': '"Parent Process"',
                'source.ip': 'sourceip',
                'destination.ip': 'destinationip',
                'user.name': 'username',
                'host.hostname': 'hostname',
                '@timestamp': 'starttime'
            }
        }

    async def start(self):
        """Start the Query Engine"""
        await super().start()
        self.log(f"🔍 Query Engine v{self.version} started")
        self.log(f"🎯 Configured SIEM targets: {list(self.siem_targets.keys())}")

        # Subscribe to relevant queues
        await self.subscribe_to_queue('query_engine_requests')
        await self.subscribe_to_queue('siem_deployment_requests')
        await self.subscribe_to_queue('field_mapping_requests')

        # Start processing loop
        await self.process_query_requests()

    async def process_query_requests(self):
        """Main processing loop for query translation requests"""
        while True:
            try:
                # Check for query translation requests
                messages = await self.get_queue_messages('query_engine_requests')

                for message in messages:
                    await self.handle_query_translation_request(message)

                # Check for deployment requests
                deployment_messages = await self.get_queue_messages('siem_deployment_requests')

                for message in deployment_messages:
                    await self.handle_deployment_request(message)

                await asyncio.sleep(1)

            except Exception as e:
                self.log(f"❌ Error in query processing: {e}", level='error')
                await asyncio.sleep(5)

    async def handle_query_translation_request(self, message: Dict[str, Any]):
        """Handle incoming query translation requests"""
        try:
            request_id = message.get('request_id')
            rule_id = message.get('rule_id')
            rule_format = message.get('rule_format')
            rule_content = message.get('rule_content')
            target_systems = message.get('target_systems', ['splunk'])
            context_data = message.get('context_data', {})
            enrichment_data = message.get('enrichment_data', {})

            self.log(f"🔍 Translating rule {rule_id} for {len(target_systems)} SIEM systems")

            # Get additional enrichment data
            enhanced_enrichment = await self.get_enrichment_data(rule_content, context_data)
            enrichment_data.update(enhanced_enrichment)

            # Get additional context data
            enhanced_context = await self.get_context_data(rule_content, enrichment_data)
            context_data.update(enhanced_context)

            translated_queries = []

            # Translate for each target SIEM
            for siem_type in target_systems:
                if siem_type in self.siem_targets:
                    query = await self.translate_rule_to_siem(
                        rule_id, rule_format, rule_content, siem_type,
                        context_data, enrichment_data
                    )
                    if query:
                        translated_queries.append(query)
                        self.translated_queries[query.query_id] = query

            # Send deployment notifications
            for query in translated_queries:
                await self.send_to_queue('siem_deployment_requests', {
                    'query_id': query.query_id,
                    'siem_type': query.siem_type,
                    'deployment_format': query.deployment_format,
                    'auto_deploy': message.get('auto_deploy', False)
                })

            # Log translation results
            await log_everything(
                component='query_engine',
                action='rules_translated',
                details={
                    'request_id': request_id,
                    'rule_id': rule_id,
                    'rule_format': rule_format,
                    'target_systems': target_systems,
                    'translated_queries': len(translated_queries),
                    'query_ids': [q.query_id for q in translated_queries]
                }
            )

            self.log(f"✅ Translated rule {rule_id} to {len(translated_queries)} SIEM queries")

        except Exception as e:
            self.log(f"❌ Error translating query: {e}", level='error')

    async def get_enrichment_data(self, rule_content: str, context_data: Dict) -> Dict[str, Any]:
        """Get enrichment data from enrichment engine"""
        try:
            # Send request to enrichment engine
            enrichment_request = {
                'request_id': str(uuid.uuid4()),
                'rule_content': rule_content,
                'context_data': context_data,
                'enrichment_types': ['threat_intel', 'asset_info', 'user_context', 'network_topology']
            }

            await self.send_to_queue('enrichment_requests', enrichment_request)

            # For now, return simulated enrichment data
            return {
                'threat_intel': {
                    'ioc_matches': 0,
                    'threat_actor_associations': [],
                    'malware_families': []
                },
                'asset_info': {
                    'criticality_level': 'medium',
                    'asset_category': 'workstation',
                    'security_groups': ['standard_users']
                },
                'user_context': {
                    'risk_score': 'low',
                    'access_level': 'standard',
                    'recent_activities': []
                },
                'network_topology': {
                    'network_segment': 'corporate',
                    'trust_level': 'internal',
                    'communication_patterns': []
                }
            }

        except Exception as e:
            self.log(f"⚠️ Error getting enrichment data: {e}", level='warning')
            return {}

    async def get_context_data(self, rule_content: str, enrichment_data: Dict) -> Dict[str, Any]:
        """Get context data from context engine"""
        try:
            # Send request to context engine
            context_request = {
                'request_id': str(uuid.uuid4()),
                'rule_content': rule_content,
                'enrichment_data': enrichment_data,
                'context_types': ['environmental', 'behavioral', 'temporal', 'relationship']
            }

            await self.send_to_queue('context_requests', context_request)

            # For now, return simulated context data
            return {
                'environmental': {
                    'deployment_environment': 'production',
                    'security_controls': ['edr', 'firewall', 'proxy'],
                    'monitoring_coverage': 'high'
                },
                'behavioral': {
                    'baseline_established': True,
                    'anomaly_detection': 'enabled',
                    'learning_period': 30
                },
                'temporal': {
                    'time_patterns': ['business_hours', 'weekdays'],
                    'seasonal_factors': [],
                    'trend_analysis': 'stable'
                },
                'relationship': {
                    'entity_relationships': [],
                    'dependency_mapping': [],
                    'trust_relationships': []
                }
            }

        except Exception as e:
            self.log(f"⚠️ Error getting context data: {e}", level='warning')
            return {}

    async def translate_rule_to_siem(self, rule_id: str, rule_format: str, rule_content: str,
                                   siem_type: str, context_data: Dict, enrichment_data: Dict) -> Optional[TranslatedQuery]:
        """Translate rule to specific SIEM format"""
        try:
            siem_target = self.siem_targets.get(siem_type)
            if not siem_target:
                self.log(f"❌ Unknown SIEM type: {siem_type}")
                return None

            query_id = str(uuid.uuid4())

            # Route to appropriate translation method
            if rule_format == 'sigma':
                translated_content = await self.translate_sigma_to_siem(rule_content, siem_target, context_data, enrichment_data)
            elif rule_format == 'yara':
                translated_content = await self.translate_yara_to_siem(rule_content, siem_target, context_data, enrichment_data)
            elif rule_format == 'kql':
                translated_content = await self.translate_kql_to_siem(rule_content, siem_target, context_data, enrichment_data)
            elif rule_format == 'spl':
                translated_content = await self.translate_spl_to_siem(rule_content, siem_target, context_data, enrichment_data)
            else:
                translated_content = await self.translate_custom_to_siem(rule_content, siem_target, context_data, enrichment_data)

            # Determine deployment format
            deployment_format = self.get_deployment_format(siem_target, rule_format)

            return TranslatedQuery(
                query_id=query_id,
                original_rule_id=rule_id,
                siem_type=siem_type,
                query_language=siem_target.query_language,
                query_content=translated_content,
                deployment_format=deployment_format,
                field_mappings=siem_target.field_mappings,
                enrichment_data=enrichment_data,
                context_data=context_data,
                confidence=0.85,
                created_at=datetime.now(),
                metadata={
                    'original_format': rule_format,
                    'translation_method': f'translate_{rule_format}_to_siem',
                    'siem_capabilities': siem_target.capabilities
                }
            )

        except Exception as e:
            self.log(f"❌ Error translating rule to {siem_type}: {e}", level='error')
            return None

    async def translate_sigma_to_siem(self, sigma_rule: str, siem_target: SIEMTarget,
                                    context_data: Dict, enrichment_data: Dict) -> str:
        """Translate Sigma rule to SIEM-specific query"""
        if siem_target.siem_type == 'splunk':
            return await self.translate_sigma_to_splunk(sigma_rule, siem_target, context_data, enrichment_data)
        elif siem_target.siem_type == 'sentinel':
            return await self.translate_sigma_to_sentinel(sigma_rule, siem_target, context_data, enrichment_data)
        elif siem_target.siem_type == 'qradar':
            return await self.translate_sigma_to_qradar(sigma_rule, siem_target, context_data, enrichment_data)
        elif siem_target.siem_type == 'elastic_siem':
            return await self.translate_sigma_to_elastic(sigma_rule, siem_target, context_data, enrichment_data)
        else:
            return await self.translate_sigma_to_generic(sigma_rule, siem_target, context_data, enrichment_data)

    async def translate_sigma_to_splunk(self, sigma_rule: str, siem_target: SIEMTarget,
                                      context_data: Dict, enrichment_data: Dict) -> str:
        """Translate Sigma rule to Splunk SPL"""
        # Parse Sigma rule (simplified parsing)
        lines = sigma_rule.split('\n')
        title = ""
        detection_fields = {}

        # Extract key fields from Sigma rule
        for line in lines:
            line = line.strip()
            if line.startswith('title:'):
                title = line.replace('title:', '').strip()
            elif 'Image|endswith:' in line:
                process_name = line.split("'")[1] if "'" in line else "*"
                detection_fields['process_name'] = process_name
            elif 'CommandLine|contains:' in line:
                command_line = line.split("'")[1] if "'" in line else "*"
                detection_fields['command_line'] = command_line

        # Build Splunk search
        search_parts = ['search index=main earliest=-1h']

        if 'process_name' in detection_fields:
            search_parts.append(f'Image="*{detection_fields["process_name"]}"')
        if 'command_line' in detection_fields:
            search_parts.append(f'CommandLine="*{detection_fields["command_line"]}*"')

        # Add enrichment-based filters
        asset_info = enrichment_data.get('asset_info', {})
        if asset_info.get('criticality_level') == 'high':
            search_parts.append('| eval priority="high"')

        # Add context-based filters
        env_data = context_data.get('environmental', {})
        if 'edr' in env_data.get('security_controls', []):
            search_parts.append('| eval data_source="edr"')

        spl_query = ' '.join(search_parts)
        spl_query += '''
| eval rule_name="''' + title + '''"
| eval detection_time=now()
| eval confidence=0.85
| stats count by host, user, Image, CommandLine, rule_name
| where count > 0'''

        return spl_query

    async def translate_sigma_to_sentinel(self, sigma_rule: str, siem_target: SIEMTarget,
                                        context_data: Dict, enrichment_data: Dict) -> str:
        """Translate Sigma rule to Microsoft Sentinel KQL"""
        # Parse Sigma rule
        lines = sigma_rule.split('\n')
        title = ""
        detection_fields = {}

        for line in lines:
            line = line.strip()
            if line.startswith('title:'):
                title = line.replace('title:', '').strip()
            elif 'Image|endswith:' in line:
                process_name = line.split("'")[1] if "'" in line else "*"
                detection_fields['process_name'] = process_name
            elif 'CommandLine|contains:' in line:
                command_line = line.split("'")[1] if "'" in line else "*"
                detection_fields['command_line'] = command_line

        # Build KQL query
        kql_query = f'''// {title}
// Translated from Sigma rule by SIEMLess v2.0 Query Engine
SecurityEvent
| where TimeGenerated >= ago(1h)'''

        if 'process_name' in detection_fields:
            kql_query += f'\n| where ProcessName endswith "{detection_fields["process_name"]}"'
        if 'command_line' in detection_fields:
            kql_query += f'\n| where CommandLine contains "{detection_fields["command_line"]}"'

        # Add enrichment-based logic
        threat_intel = enrichment_data.get('threat_intel', {})
        if threat_intel.get('ioc_matches', 0) > 0:
            kql_query += '\n| extend ThreatIntelMatch = "true"'

        # Add context-based logic
        behavioral = context_data.get('behavioral', {})
        if behavioral.get('anomaly_detection') == 'enabled':
            kql_query += '\n| extend AnomalyDetectionEnabled = "true"'

        kql_query += f'''
| extend RuleName = "{title}"
| extend DetectionTime = now()
| extend Confidence = 0.85
| project TimeGenerated, Computer, AccountName, ProcessName, CommandLine, RuleName, Confidence
| summarize Count = count() by Computer, AccountName, ProcessName, CommandLine, RuleName
| where Count > 0'''

        return kql_query

    async def translate_sigma_to_qradar(self, sigma_rule: str, siem_target: SIEMTarget,
                                      context_data: Dict, enrichment_data: Dict) -> str:
        """Translate Sigma rule to IBM QRadar AQL"""
        # Parse Sigma rule
        lines = sigma_rule.split('\n')
        title = ""
        detection_fields = {}

        for line in lines:
            line = line.strip()
            if line.startswith('title:'):
                title = line.replace('title:', '').strip()
            elif 'Image|endswith:' in line:
                process_name = line.split("'")[1] if "'" in line else "*"
                detection_fields['process_name'] = process_name
            elif 'CommandLine|contains:' in line:
                command_line = line.split("'")[1] if "'" in line else "*"
                detection_fields['command_line'] = command_line

        # Build AQL query
        aql_query = f'''SELECT sourceip, destinationip, username, "Process Name", "Process Command Line",
    '{title}' as "Rule Name",
    CURRENT_TIMESTAMP as "Detection Time"
FROM events
WHERE starttime > LAST 1 HOURS'''

        if 'process_name' in detection_fields:
            aql_query += f'\n    AND "Process Name" LIKE \'%{detection_fields["process_name"]}\''
        if 'command_line' in detection_fields:
            aql_query += f'\n    AND "Process Command Line" LIKE \'%{detection_fields["command_line"]}%\''

        # Add enrichment-based conditions
        asset_info = enrichment_data.get('asset_info', {})
        if asset_info.get('criticality_level') == 'high':
            aql_query += '\n    AND "Asset Criticality" = \'High\''

        aql_query += '''
GROUP BY sourceip, destinationip, username, "Process Name", "Process Command Line"
HAVING COUNT(*) > 0'''

        return aql_query

    async def translate_sigma_to_elastic(self, sigma_rule: str, siem_target: SIEMTarget,
                                       context_data: Dict, enrichment_data: Dict) -> str:
        """Translate Sigma rule to Elastic Security EQL"""
        # Parse Sigma rule
        lines = sigma_rule.split('\n')
        title = ""
        detection_fields = {}

        for line in lines:
            line = line.strip()
            if line.startswith('title:'):
                title = line.replace('title:', '').strip()
            elif 'Image|endswith:' in line:
                process_name = line.split("'")[1] if "'" in line else "*"
                detection_fields['process_name'] = process_name
            elif 'CommandLine|contains:' in line:
                command_line = line.split("'")[1] if "'" in line else "*"
                detection_fields['command_line'] = command_line

        # Build EQL query
        eql_query = f'''// {title}
// Translated from Sigma rule by SIEMLess v2.0 Query Engine

process where @timestamp >= "now-1h"'''

        conditions = []
        if 'process_name' in detection_fields:
            conditions.append(f'endswith(process.name, "{detection_fields["process_name"]}")')
        if 'command_line' in detection_fields:
            conditions.append(f'stringContains(process.command_line, "{detection_fields["command_line"]}")')

        if conditions:
            eql_query += ' and ' + ' and '.join(conditions)

        return eql_query

    async def translate_yara_to_siem(self, yara_rule: str, siem_target: SIEMTarget,
                                   context_data: Dict, enrichment_data: Dict) -> str:
        """Translate YARA rule to SIEM file detection query"""
        if siem_target.siem_type == 'splunk':
            return f'''search index=main earliest=-24h sourcetype=file_monitoring
| regex filename="{re.escape(yara_rule.split('$')[0] if '$' in yara_rule else '*')}"
| eval rule_type="yara_detection"
| eval yara_rule="{yara_rule.split('rule ')[1].split('{')[0].strip() if 'rule ' in yara_rule else 'unknown'}"
| stats count by host, filename, file_hash, yara_rule'''

        elif siem_target.siem_type == 'sentinel':
            return f'''DeviceFileEvents
| where TimeGenerated >= ago(24h)
| where FileName matches regex @"{re.escape(yara_rule.split('$')[0] if '$' in yara_rule else '.*')}"
| extend YaraRule = "{yara_rule.split('rule ')[1].split('{')[0].strip() if 'rule ' in yara_rule else 'unknown'}"
| project TimeGenerated, DeviceName, FileName, SHA256, YaraRule'''

        else:
            return f"# YARA rule translation for {siem_target.siem_type} not yet implemented"

    async def translate_kql_to_siem(self, kql_rule: str, siem_target: SIEMTarget,
                                  context_data: Dict, enrichment_data: Dict) -> str:
        """Translate KQL rule to other SIEM formats"""
        if siem_target.siem_type == 'splunk':
            # Convert KQL to SPL (simplified conversion)
            spl_query = kql_rule.replace('SecurityEvent', 'search index=main sourcetype=WinEventLog:Security')
            spl_query = spl_query.replace('| where ', '| search ')
            spl_query = spl_query.replace('TimeGenerated', '_time')
            spl_query = spl_query.replace('Computer', 'host')
            return spl_query

        elif siem_target.siem_type == 'qradar':
            # Convert KQL to AQL (simplified conversion)
            aql_query = kql_rule.replace('SecurityEvent', 'SELECT * FROM events')
            aql_query = aql_query.replace('| where ', 'WHERE ')
            aql_query = aql_query.replace('TimeGenerated >= ago(1h)', 'starttime > LAST 1 HOURS')
            return aql_query

        else:
            return kql_rule  # Return as-is for Sentinel and compatible systems

    async def translate_spl_to_siem(self, spl_rule: str, siem_target: SIEMTarget,
                                  context_data: Dict, enrichment_data: Dict) -> str:
        """Translate SPL rule to other SIEM formats"""
        if siem_target.siem_type == 'sentinel':
            # Convert SPL to KQL (simplified conversion)
            kql_query = spl_rule.replace('search index=main', 'SecurityEvent | where')
            kql_query = kql_query.replace('earliest=-1h', 'TimeGenerated >= ago(1h)')
            kql_query = kql_query.replace('host', 'Computer')
            kql_query = kql_query.replace('user', 'AccountName')
            return kql_query

        else:
            return spl_rule  # Return as-is for Splunk and compatible systems

    async def translate_custom_to_siem(self, custom_rule: str, siem_target: SIEMTarget,
                                     context_data: Dict, enrichment_data: Dict) -> str:
        """Translate custom JSON rule to SIEM format"""
        try:
            rule_data = json.loads(custom_rule)
            detection_logic = rule_data.get('detection_logic', {})

            # Generate basic query based on SIEM type
            if siem_target.siem_type == 'splunk':
                return f'''search index=main earliest=-1h
| search {' '.join([f'{k}="{v}"' for k, v in detection_logic.items()])}
| eval rule_type="custom"
| stats count by host, user'''

            elif siem_target.siem_type == 'sentinel':
                conditions = ' and '.join([f'{k} == "{v}"' for k, v in detection_logic.items()])
                return f'''SecurityEvent
| where TimeGenerated >= ago(1h) and {conditions}
| extend RuleType = "custom"
| project TimeGenerated, Computer, AccountName'''

            else:
                return f"# Custom rule translation for {siem_target.siem_type}: {custom_rule}"

        except json.JSONDecodeError:
            return f"# Invalid JSON in custom rule: {custom_rule}"

    def get_deployment_format(self, siem_target: SIEMTarget, rule_format: str) -> str:
        """Determine deployment format for SIEM"""
        if siem_target.siem_type == 'splunk':
            return 'savedsearch'
        elif siem_target.siem_type == 'sentinel':
            return 'analytics_rule'
        elif siem_target.siem_type == 'qradar':
            return 'custom_rule'
        elif siem_target.siem_type == 'elastic_siem':
            return 'detection_rule'
        else:
            return 'generic_query'

    async def handle_deployment_request(self, message: Dict[str, Any]):
        """Handle SIEM deployment requests"""
        try:
            query_id = message.get('query_id')
            siem_type = message.get('siem_type')
            auto_deploy = message.get('auto_deploy', False)

            query = self.translated_queries.get(query_id)
            if not query:
                self.log(f"❌ Query {query_id} not found for deployment")
                return

            self.log(f"🚀 Preparing deployment of query {query_id} to {siem_type}")

            if auto_deploy:
                deployment_result = await self.deploy_to_siem(query)
                if deployment_result:
                    self.log(f"✅ Successfully deployed query {query_id} to {siem_type}")
                else:
                    self.log(f"❌ Failed to deploy query {query_id} to {siem_type}")
            else:
                self.log(f"📋 Query {query_id} ready for manual deployment to {siem_type}")

        except Exception as e:
            self.log(f"❌ Error in deployment: {e}", level='error')

    async def deploy_to_siem(self, query: TranslatedQuery) -> bool:
        """Deploy query to actual SIEM system"""
        try:
            # This would contain actual SIEM API calls
            # For now, we'll simulate the deployment

            deployment_config = {
                'query_id': query.query_id,
                'siem_type': query.siem_type,
                'query_content': query.query_content,
                'deployment_format': query.deployment_format,
                'timestamp': datetime.now().isoformat()
            }

            # Log deployment simulation
            await log_everything(
                component='query_engine',
                action='siem_deployment_simulated',
                details=deployment_config
            )

            return True

        except Exception as e:
            self.log(f"❌ Deployment error: {e}", level='error')
            return False

    async def get_translation_statistics(self) -> Dict[str, Any]:
        """Get translation statistics"""
        return {
            'total_translations': len(self.translated_queries),
            'translations_by_siem': {
                siem: len([q for q in self.translated_queries.values() if q.siem_type == siem])
                for siem in self.siem_targets.keys()
            },
            'translations_by_language': {
                lang: len([q for q in self.translated_queries.values() if q.query_language == lang])
                for lang in ['spl', 'kql', 'aql', 'eql', 'yara_l']
            },
            'recent_translations': [
                {
                    'query_id': query.query_id,
                    'siem_type': query.siem_type,
                    'query_language': query.query_language,
                    'confidence': query.confidence,
                    'created_at': query.created_at.isoformat()
                }
                for query in sorted(self.translated_queries.values(),
                                  key=lambda x: x.created_at, reverse=True)[:10]
            ]
        }

    # BaseEngine abstract method implementations
    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Process a single message from the message queue"""
        try:
            message_type = message.get('type', 'unknown')

            if message_type == 'query_engine_request':
                await self.handle_query_translation_request(message)
                return {'status': 'success', 'message': 'Query translation processed'}
            elif message_type == 'siem_deployment_request':
                await self.handle_deployment_request(message)
                return {'status': 'success', 'message': 'SIEM deployment processed'}
            else:
                return {'status': 'ignored', 'message': f'Unknown message type: {message_type}'}

        except Exception as e:
            self.log(f"Error processing message: {e}", level='error')
            return {'status': 'error', 'message': str(e)}

    def get_capabilities(self) -> Dict[str, Any]:
        """Return engine capabilities for discovery"""
        return {
            'engine_name': 'query',
            'version': self.version,
            'description': 'Translates detection rules into SIEM-specific queries and deployment formats',
            'supported_siems': list(self.siem_targets.keys()),
            'query_languages': ['spl', 'kql', 'aql', 'eql', 'yara_l'],
            'translation_types': [
                'sigma_to_siem',
                'yara_to_siem',
                'kql_to_siem',
                'spl_to_siem',
                'custom_to_siem'
            ],
            'enrichment_integration': True,
            'context_integration': True,
            'deployment_methods': ['rest_api', 'arm_template', 'custom_rule', 'kibana_api'],
            'endpoints': [
                '/api/v2/query/translate',
                '/api/v2/query/deploy',
                '/api/v2/query/siems',
                '/api/v2/query/test',
                '/api/v2/query/stats',
                '/api/v2/query/mappings',
                '/api/v2/query/enrichment',
                '/api/v2/query/context'
            ],
            'message_types': [
                'query_engine_requests',
                'siem_deployment_requests',
                'field_mapping_requests'
            ]
        }

    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate engine-specific configuration"""
        required_fields = ['ENGINE_NAME', 'REDIS_HOST', 'REDIS_PORT']

        for field in required_fields:
            if field not in config:
                self.log(f"Missing required configuration field: {field}", level='error')
                return False

        # Validate SIEM target configurations
        if not self.siem_targets:
            self.log("No SIEM targets configured", level='error')
            return False

        # Validate field mappings
        if not self.field_mappings:
            self.log("No field mappings loaded", level='error')
            return False

        return True

async def main():
    """Main entry point"""
    engine = QueryEngine()
    await engine.start()

if __name__ == "__main__":
    asyncio.run(main())