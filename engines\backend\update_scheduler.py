"""
Update Scheduler
Automates the checking and application of source updates
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import json

from source_update_manager import SourceUpdateManager

logger = logging.getLogger(__name__)


class UpdateScheduler:
    """
    Schedules and manages automatic updates for log source definitions,
    correlation rules, and quality scores
    """

    def __init__(self, db_pool=None, redis_client=None):
        self.db_pool = db_pool
        self.redis_client = redis_client
        self.update_manager = SourceUpdateManager(db_pool, redis_client)
        self.is_running = False

        # Schedule configuration
        self.schedules = {
            'community_updates': {
                'interval': timedelta(hours=24),
                'last_run': None,
                'auto_apply': False,
                'enabled': True
            },
            'cti_updates': {
                'interval': timedelta(hours=6),
                'last_run': None,
                'auto_apply': True,  # CTI updates are trusted
                'enabled': True
            },
            'learning_analysis': {
                'interval': timedelta(hours=12),
                'last_run': None,
                'auto_apply': False,  # Learned patterns need review
                'enabled': True
            },
            'cve_monitoring': {
                'interval': timedelta(hours=4),
                'last_run': None,
                'auto_apply': True,  # Security updates are critical
                'enabled': True
            },
            'performance_review': {
                'interval': timedelta(days=1),
                'last_run': None,
                'auto_apply': False,
                'enabled': True
            }
        }

    async def start(self):
        """Start the update scheduler"""
        self.is_running = True
        logger.info("Update Scheduler started")

        # Start all scheduled tasks
        tasks = [
            asyncio.create_task(self._run_scheduled_updates()),
            asyncio.create_task(self._monitor_manual_triggers()),
            asyncio.create_task(self._process_approval_queue()),
            asyncio.create_task(self._cleanup_old_data())
        ]

        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Scheduler error: {e}")
        finally:
            self.is_running = False

    async def stop(self):
        """Stop the update scheduler"""
        self.is_running = False
        logger.info("Update Scheduler stopped")

    async def _run_scheduled_updates(self):
        """Main loop for running scheduled updates"""
        while self.is_running:
            try:
                current_time = datetime.utcnow()

                for schedule_name, config in self.schedules.items():
                    if not config['enabled']:
                        continue

                    # Check if it's time to run this schedule
                    if config['last_run'] is None or \
                       (current_time - config['last_run']) >= config['interval']:

                        logger.info(f"Running scheduled update: {schedule_name}")

                        # Run the appropriate update check
                        if schedule_name == 'community_updates':
                            await self._check_community_updates(config['auto_apply'])
                        elif schedule_name == 'cti_updates':
                            await self._check_cti_updates(config['auto_apply'])
                        elif schedule_name == 'learning_analysis':
                            await self._analyze_learned_patterns(config['auto_apply'])
                        elif schedule_name == 'cve_monitoring':
                            await self._monitor_cve_impacts(config['auto_apply'])
                        elif schedule_name == 'performance_review':
                            await self._review_pattern_performance()

                        # Update last run time
                        config['last_run'] = current_time

                        # Store schedule state
                        await self._save_schedule_state()

            except Exception as e:
                logger.error(f"Error in scheduled updates: {e}")

            # Wait before next check
            await asyncio.sleep(60)  # Check every minute

    async def _check_community_updates(self, auto_apply: bool):
        """Check for community-contributed updates"""
        try:
            logger.info("Checking community updates...")

            # Get updates from community sources
            updates = await self.update_manager._check_github_updates()

            if updates:
                logger.info(f"Found {len(updates)} community updates")

                # Apply updates (with approval if needed)
                results = await self.update_manager.apply_updates(updates, auto_apply)

                # Log results
                await self._log_update_results('community', results)

                # Notify administrators
                if results['pending']:
                    await self._send_approval_notification(results['pending'])

        except Exception as e:
            logger.error(f"Community update check failed: {e}")

    async def _check_cti_updates(self, auto_apply: bool):
        """Trigger CTI update check (delegates to Ingestion Engine)"""
        try:
            logger.info("Triggering CTI update check...")

            # Delegate to Ingestion Engine via Redis
            if self.redis_client:
                self.redis_client.publish('ingestion.cti.update', json.dumps({
                    'sources': ['otx', 'opencti', 'threatfox'],
                    'auto_apply': auto_apply,
                    'triggered_by': 'scheduler',
                    'timestamp': datetime.utcnow().isoformat()
                }))
                logger.info("CTI update triggered - Ingestion Engine will fetch")
            else:
                logger.warning("Redis not available - CTI update skipped")

        except Exception as e:
            logger.error(f"CTI update trigger failed: {e}")

    async def _analyze_learned_patterns(self, auto_apply: bool):
        """Analyze patterns learned from unidentified logs"""
        try:
            logger.info("Analyzing learned patterns...")

            # Get learned pattern updates
            updates = await self.update_manager._check_learned_patterns()

            if updates:
                logger.info(f"Found {len(updates)} learned patterns")

                # Validate learned patterns before applying
                validated_updates = await self._validate_learned_patterns(updates)

                # Apply validated updates
                if validated_updates:
                    results = await self.update_manager.apply_updates(
                        validated_updates,
                        auto_apply
                    )

                    # Log results
                    await self._log_update_results('learned', results)

                # Store patterns that need more samples
                await self._store_learning_progress(updates)

        except Exception as e:
            logger.error(f"Learned pattern analysis failed: {e}")

    async def _monitor_cve_impacts(self, auto_apply: bool):
        """Monitor CVE impacts on product quality scores"""
        try:
            logger.info("Monitoring CVE impacts...")

            # Check for CVE-related updates
            updates = await self.update_manager._check_cve_impacts()

            if updates:
                logger.info(f"Found {len(updates)} CVE impacts")

                # Apply quality score adjustments
                results = await self.update_manager.apply_updates(updates, auto_apply)

                # Alert on critical vulnerabilities
                await self._alert_critical_cves(updates)

                # Log results
                await self._log_update_results('cve', results)

        except Exception as e:
            logger.error(f"CVE monitoring failed: {e}")

    async def _review_pattern_performance(self):
        """Review pattern performance and adjust confidence scores"""
        try:
            if not self.db_pool:
                return

            logger.info("Reviewing pattern performance...")

            async with self.db_pool.acquire() as conn:
                # Get underperforming patterns
                underperforming = await conn.fetch("""
                    SELECT
                        p.pattern_id,
                        p.pattern_name,
                        p.identifies_vendor,
                        p.identifies_product,
                        m.accuracy_rate,
                        m.false_positive_rate
                    FROM source_identification_patterns p
                    JOIN pattern_performance_metrics m ON p.pattern_id = m.pattern_id
                    WHERE m.metric_date = CURRENT_DATE - 1
                    AND (m.accuracy_rate < 0.7 OR m.false_positive_rate > 0.3)
                """)

                for pattern in underperforming:
                    # Reduce confidence score for underperforming patterns
                    await conn.execute("""
                        UPDATE source_identification_patterns
                        SET confidence_score = confidence_score * 0.9
                        WHERE pattern_id = $1
                    """, pattern['pattern_id'])

                    logger.warning(
                        f"Reduced confidence for pattern {pattern['pattern_name']} "
                        f"(accuracy: {pattern['accuracy_rate']:.2f})"
                    )

                # Get high-performing patterns for confidence boost
                high_performing = await conn.fetch("""
                    SELECT
                        p.pattern_id,
                        p.pattern_name,
                        m.accuracy_rate
                    FROM source_identification_patterns p
                    JOIN pattern_performance_metrics m ON p.pattern_id = m.pattern_id
                    WHERE m.metric_date = CURRENT_DATE - 1
                    AND m.accuracy_rate > 0.95
                    AND p.confidence_score < 0.9
                """)

                for pattern in high_performing:
                    # Increase confidence for high-performing patterns
                    await conn.execute("""
                        UPDATE source_identification_patterns
                        SET confidence_score = LEAST(confidence_score * 1.1, 1.0)
                        WHERE pattern_id = $1
                    """, pattern['pattern_id'])

                    logger.info(
                        f"Increased confidence for pattern {pattern['pattern_name']} "
                        f"(accuracy: {pattern['accuracy_rate']:.2f})"
                    )

        except Exception as e:
            logger.error(f"Performance review failed: {e}")

    async def _monitor_manual_triggers(self):
        """Monitor for manual update triggers via Redis"""
        if not self.redis_client:
            return

        try:
            pubsub = self.redis_client.pubsub()
            pubsub.subscribe('backend.trigger_update')

            while self.is_running:
                message = pubsub.get_message(timeout=1.0)
                if message and message['type'] == 'message':
                    data = json.loads(message['data'])
                    update_type = data.get('update_type', 'all')

                    logger.info(f"Manual update trigger received: {update_type}")

                    if update_type == 'all':
                        # Check all update sources
                        updates = await self.update_manager.check_for_updates()
                        await self.update_manager.apply_updates(updates, auto_apply=False)
                    elif update_type == 'community':
                        await self._check_community_updates(auto_apply=False)
                    elif update_type == 'cti':
                        await self._check_cti_updates(auto_apply=True)
                    elif update_type == 'learned':
                        await self._analyze_learned_patterns(auto_apply=False)

                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"Manual trigger monitoring failed: {e}")

    async def _process_approval_queue(self):
        """Process updates in the approval queue"""
        while self.is_running:
            try:
                if not self.db_pool:
                    await asyncio.sleep(60)
                    continue

                async with self.db_pool.acquire() as conn:
                    # Get approved updates
                    approved = await conn.fetch("""
                        SELECT * FROM update_approval_queue
                        WHERE status = 'approved'
                        ORDER BY created_at ASC
                        LIMIT 10
                    """)

                    for item in approved:
                        update_data = item['update_data']

                        # Apply the update
                        success = await conn.fetchval("""
                            SELECT apply_source_update($1::JSONB)
                        """, json.dumps(update_data))

                        if success:
                            # Mark as processed
                            await conn.execute("""
                                DELETE FROM update_approval_queue
                                WHERE queue_id = $1
                            """, item['queue_id'])

                            logger.info(f"Applied approved update for {item['vendor']} {item['product']}")
                        else:
                            # Mark as failed
                            await conn.execute("""
                                UPDATE update_approval_queue
                                SET status = 'failed'
                                WHERE queue_id = $1
                            """, item['queue_id'])

            except Exception as e:
                logger.error(f"Approval queue processing failed: {e}")

            await asyncio.sleep(30)  # Check every 30 seconds

    async def _cleanup_old_data(self):
        """Clean up old update history and metrics"""
        while self.is_running:
            try:
                if not self.db_pool:
                    await asyncio.sleep(3600)
                    continue

                async with self.db_pool.acquire() as conn:
                    # Clean up old update history (keep 90 days)
                    await conn.execute("""
                        DELETE FROM source_update_history
                        WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days'
                    """)

                    # Clean up old performance metrics (keep 30 days)
                    await conn.execute("""
                        DELETE FROM pattern_performance_metrics
                        WHERE metric_date < CURRENT_DATE - 30
                    """)

                    # Clean up processed approval queue items
                    await conn.execute("""
                        DELETE FROM update_approval_queue
                        WHERE status IN ('applied', 'rejected')
                        AND reviewed_at < CURRENT_TIMESTAMP - INTERVAL '7 days'
                    """)

                logger.info("Completed cleanup of old update data")

            except Exception as e:
                logger.error(f"Cleanup failed: {e}")

            await asyncio.sleep(3600)  # Run hourly

    async def _validate_learned_patterns(self, updates: list) -> list:
        """Validate learned patterns before applying"""
        validated = []

        for update in updates:
            # Check confidence threshold
            if update.confidence < 0.7:
                continue

            # Check for conflicts with existing patterns
            if not await self._check_pattern_conflicts(update):
                continue

            # Validate against known good samples if available
            if await self._validate_against_samples(update):
                validated.append(update)

        return validated

    async def _check_pattern_conflicts(self, update) -> bool:
        """Check if new pattern conflicts with existing ones"""
        # Implementation would check for pattern overlap
        return True

    async def _validate_against_samples(self, update) -> bool:
        """Validate pattern against known good samples"""
        # Implementation would test pattern accuracy
        return True

    async def _update_correlation_rules_from_cti(self, updates: list):
        """Update correlation rules based on CTI updates"""
        if not self.db_pool:
            return

        async with self.db_pool.acquire() as conn:
            for update in updates:
                if update.mitre_coverage:
                    # Create or update correlation rules for new techniques
                    for technique in update.mitre_coverage:
                        await conn.execute("""
                            INSERT INTO correlation_rules (
                                rule_name, rule_type, use_case,
                                mitre_techniques, required_sources
                            ) VALUES (
                                $1, 'simple', 'cti_auto',
                                ARRAY[$2], ARRAY[$3]
                            )
                            ON CONFLICT (rule_name) DO UPDATE
                            SET mitre_techniques = array_append(
                                correlation_rules.mitre_techniques, $2
                            )
                        """,
                            f"CTI_{technique}_{update.vendor}",
                            technique,
                            update.vendor.lower()
                        )

    async def _store_learning_progress(self, updates: list):
        """Store learning progress for patterns that need more samples"""
        if not self.db_pool:
            return

        async with self.db_pool.acquire() as conn:
            for update in updates:
                if update.source == 'learned' and update.confidence < 0.7:
                    await conn.execute("""
                        INSERT INTO learned_patterns (
                            suspected_vendor, suspected_product,
                            confidence_score, status
                        ) VALUES ($1, $2, $3, 'learning')
                        ON CONFLICT DO NOTHING
                    """,
                        update.vendor,
                        update.product,
                        update.confidence
                    )

    async def _log_update_results(self, source: str, results: dict):
        """Log update results for monitoring"""
        logger.info(
            f"Update results from {source}: "
            f"Applied: {len(results['applied'])}, "
            f"Pending: {len(results['pending'])}, "
            f"Failed: {len(results['failed'])}"
        )

        # Publish metrics to Redis for monitoring
        if self.redis_client:
            self.redis_client.publish(
                'backend.update_metrics',
                json.dumps({
                    'source': source,
                    'timestamp': datetime.utcnow().isoformat(),
                    'applied': len(results['applied']),
                    'pending': len(results['pending']),
                    'failed': len(results['failed'])
                })
            )

    async def _send_approval_notification(self, pending_updates: list):
        """Send notification about updates needing approval"""
        if self.redis_client:
            self.redis_client.publish(
                'backend.approval_needed',
                json.dumps({
                    'count': len(pending_updates),
                    'updates': [
                        {
                            'vendor': u.vendor,
                            'product': u.product,
                            'type': u.update_type,
                            'source': u.source
                        }
                        for u in pending_updates[:5]  # First 5 for preview
                    ]
                })
            )

    async def _alert_critical_cves(self, updates: list):
        """Alert on critical CVE impacts"""
        critical = [u for u in updates if u.update_type == 'cve' and u.quality_score <= 3]

        if critical and self.redis_client:
            self.redis_client.publish(
                'backend.critical_cve_alert',
                json.dumps({
                    'affected_products': [
                        f"{u.vendor} {u.product}" for u in critical
                    ],
                    'timestamp': datetime.utcnow().isoformat()
                })
            )

    async def _save_schedule_state(self):
        """Save schedule state to database"""
        if not self.db_pool:
            return

        try:
            async with self.db_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO scheduler_state (
                        scheduler_name, state_data, updated_at
                    ) VALUES ('source_updates', $1, NOW())
                    ON CONFLICT (scheduler_name) DO UPDATE
                    SET state_data = EXCLUDED.state_data,
                        updated_at = NOW()
                """, json.dumps({
                    schedule: {
                        'last_run': config['last_run'].isoformat() if config['last_run'] else None,
                        'enabled': config['enabled']
                    }
                    for schedule, config in self.schedules.items()
                }))
        except Exception as e:
            logger.error(f"Failed to save schedule state: {e}")

    async def load_schedule_state(self):
        """Load schedule state from database"""
        if not self.db_pool:
            return

        try:
            async with self.db_pool.acquire() as conn:
                state = await conn.fetchval("""
                    SELECT state_data FROM scheduler_state
                    WHERE scheduler_name = 'source_updates'
                """)

                if state:
                    state_data = json.loads(state)
                    for schedule, config in state_data.items():
                        if schedule in self.schedules:
                            if config['last_run']:
                                self.schedules[schedule]['last_run'] = datetime.fromisoformat(config['last_run'])
                            self.schedules[schedule]['enabled'] = config['enabled']

        except Exception as e:
            logger.error(f"Failed to load schedule state: {e}")