"""
Discover All Security Vendor Data Sources in Elastic
Analyzes indices to find <PERSON><PERSON><PERSON>Point, ThreatLocker, and other security vendors
"""

import os
from elasticsearch import Elasticsearch
from dotenv import load_dotenv
from collections import defaultdict
import json

load_dotenv()

ELASTIC_CLOUD_ID = os.getenv('ELASTIC_CLOUD_ID')
ELASTIC_API_KEY = os.getenv('ELASTIC_API_KEY')

print("="*80)
print("ELASTIC SECURITY VENDOR DISCOVERY")
print("="*80)

# Connect
print("\n[1] Connecting to Elastic Cloud...")
client = Elasticsearch(
    cloud_id=ELASTIC_CLOUD_ID,
    api_key=ELASTIC_API_KEY,
    request_timeout=30
)

if not client.ping():
    print("[FAIL] Connection failed")
    exit(1)

info = client.info()
print(f"[OK] Connected to Elasticsearch {info['version']['number']}")

# Get all indices with data
print("\n[2] Scanning all indices...")

# Search for common security vendor patterns in index names
vendor_patterns = [
    'tipping', 'threat', 'crowdstrike', 'palo', 'fortinet', 'cisco',
    'checkpoint', 'zscaler', 'proofpoint', 'sentinel', 'defender',
    'sophos', 'trend', 'mcafee', 'symantec', 'carbon', 'cybereason',
    'endpoint', 'edr', 'firewall', 'ids', 'ips', 'network', 'web'
]

query = {
    "query": {"match_all": {}},
    "size": 0,
    "aggs": {
        "indices": {
            "terms": {
                "field": "_index",
                "size": 1000
            }
        }
    }
}

try:
    response = client.search(index='*', body=query)

    indices = []
    for bucket in response['aggregations']['indices']['buckets']:
        index_name = bucket['key']
        doc_count = bucket['doc_count']
        indices.append({'name': index_name, 'count': doc_count})

    print(f"[OK] Found {len(indices)} indices with data")

    # Categorize by vendor
    vendors = defaultdict(list)
    other_indices = []

    for idx in indices:
        name = idx['name'].lower()
        matched = False

        for pattern in vendor_patterns:
            if pattern in name:
                vendors[pattern].append(idx)
                matched = True
                break

        if not matched and 'logs-' in name:
            other_indices.append(idx)

    # Display results
    print("\n" + "="*80)
    print("SECURITY VENDORS FOUND")
    print("="*80)

    for vendor, vendor_indices in sorted(vendors.items()):
        total_docs = sum(idx['count'] for idx in vendor_indices)
        print(f"\n[{vendor.upper()}]")
        print(f"  Indices: {len(vendor_indices)}")
        print(f"  Total Documents: {total_docs:,}")

        # Show top 5 indices
        sorted_indices = sorted(vendor_indices, key=lambda x: x['count'], reverse=True)
        for idx in sorted_indices[:5]:
            print(f"    - {idx['name']}: {idx['count']:,} docs")
        if len(sorted_indices) > 5:
            print(f"    ... and {len(sorted_indices) - 5} more indices")

    # Show other log indices
    if other_indices:
        print(f"\n[OTHER LOG SOURCES]")
        print(f"  Indices: {len(other_indices)}")
        sorted_other = sorted(other_indices, key=lambda x: x['count'], reverse=True)
        for idx in sorted_other[:10]:
            print(f"    - {idx['name']}: {idx['count']:,} docs")

    # Sample data from key indices
    print("\n" + "="*80)
    print("SAMPLE DATA ANALYSIS")
    print("="*80)

    # Look for TippingPoint specifically
    tippingpoint_indices = [idx for idx in indices if 'tipping' in idx['name'].lower()]
    if tippingpoint_indices:
        print("\n[TIPPINGPOINT DETECTED]")
        for idx in tippingpoint_indices:
            print(f"\n  Index: {idx['name']}")
            print(f"  Documents: {idx['count']:,}")

            # Get sample
            sample = client.search(index=idx['name'], body={"size": 1})
            if sample['hits']['hits']:
                doc = sample['hits']['hits'][0]['_source']
                print(f"  Sample fields: {list(doc.keys())[:15]}")

                # Show vendor-specific fields
                if 'tippingpoint' in doc:
                    print(f"  TippingPoint fields: {list(doc['tippingpoint'].keys())}")

    # Look for ThreatLocker
    threatlocker_indices = [idx for idx in indices if 'threat' in idx['name'].lower() and 'locker' in idx['name'].lower()]
    if threatlocker_indices:
        print("\n[THREATLOCKER DETECTED]")
        for idx in threatlocker_indices:
            print(f"\n  Index: {idx['name']}")
            print(f"  Documents: {idx['count']:,}")

            sample = client.search(index=idx['name'], body={"size": 1})
            if sample['hits']['hits']:
                doc = sample['hits']['hits'][0]['_source']
                print(f"  Sample fields: {list(doc.keys())[:15]}")

    # Check for common ECS fields across all logs
    print("\n" + "="*80)
    print("COMMON ECS FIELDS AVAILABLE")
    print("="*80)

    # Sample from logs-* to check field availability
    sample_query = {
        "query": {"range": {"@timestamp": {"gte": "now-24h"}}},
        "size": 100
    }

    sample_logs = client.search(index='logs-*', body=sample_query)

    # Collect all field names
    all_fields = set()
    vendor_fields = defaultdict(set)

    for hit in sample_logs['hits']['hits']:
        source = hit['_source']
        index = hit['_index']

        # Extract vendor from index name
        vendor = 'unknown'
        for pattern in vendor_patterns:
            if pattern in index.lower():
                vendor = pattern
                break

        # Collect fields
        for key in source.keys():
            all_fields.add(key)
            vendor_fields[vendor].add(key)

            # If nested, add subfields
            if isinstance(source[key], dict):
                for subkey in source[key].keys():
                    all_fields.add(f"{key}.{subkey}")
                    vendor_fields[vendor].add(f"{key}.{subkey}")

    print(f"\n[Field Coverage Across Vendors]")

    key_fields = {
        'host': ['host.name', 'host.ip', 'host.mac', 'host.os.name'],
        'network': ['source.ip', 'destination.ip', 'network.protocol'],
        'user': ['user.name', 'user.domain'],
        'process': ['process.name', 'process.executable', 'process.pid'],
        'threat': ['threat.indicator', 'threat.technique'],
        'event': ['event.action', 'event.category', 'event.outcome']
    }

    for category, fields in key_fields.items():
        print(f"\n  [{category.upper()}]")
        for field in fields:
            has_field = field in all_fields
            status = "[OK]" if has_field else "[--]"
            print(f"    {status} {field}")

            # Show which vendors have this field
            if has_field:
                vendors_with_field = [v for v, fields_set in vendor_fields.items() if field in fields_set]
                if vendors_with_field:
                    print(f"         Available in: {', '.join(vendors_with_field[:5])}")

except Exception as e:
    print(f"[FAIL] Error: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "="*80)
print("DISCOVERY COMPLETE")
print("="*80)
