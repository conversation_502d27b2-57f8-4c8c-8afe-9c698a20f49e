"""
The Librarian Engine - Central Knowledge Authority for SIEMLess v2.0
Manages all patterns, validates discoveries, and crystallizes knowledge
"""
import asyncio
import json
import hashlib
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pathlib import Path

# Add v2 to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from shared.base import BaseEngine
from shared.logging import UniversalLogger


class LibrarianEngine(BaseEngine):
    """
    The Librarian: Central knowledge authority that manages all patterns

    Core responsibilities:
    1. Accept pattern submissions from all engines
    2. Validate patterns through multi-AI consensus
    3. Crystallize validated patterns into deterministic rules
    4. Serve patterns to requesting engines
    5. Update pattern library regularly
    """

    def __init__(self):
        super().__init__('librarian', '2.0.0')

        # Pattern storage
        self.pattern_library = {}
        self.pending_validation = []
        self.validation_history = {}

        # Pattern categories
        self.pattern_categories = {
            'parser': ['cisco_asa', 'palo_alto', 'fortinet', 'checkpoint'],
            'entity': ['user', 'device', 'process', 'network', 'file'],
            'relationship': ['accesses', 'executes', 'communicates', 'modifies'],
            'detection': ['anomaly', 'threat', 'compliance', 'operational'],
            'enrichment': ['geolocation', 'reputation', 'context', 'history']
        }

        # Validation thresholds
        self.consensus_threshold = 0.8  # 80% agreement for crystallization
        self.confidence_threshold = 0.7  # 70% confidence minimum

        # Load existing patterns
        self._load_pattern_library()

    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process incoming messages about patterns

        Message types:
        - pattern_submission: New pattern discovered by an engine
        - pattern_request: Engine requesting a pattern
        - validation_result: AI validation result for a pattern
        - library_update: Update to pattern library
        """
        message_type = message.get('type')
        result = {'success': False, 'message': 'Unknown message type'}

        self.logger.start_operation(f"process_{message_type}")

        try:
            if message_type == 'pattern_submission':
                result = await self._handle_pattern_submission(message)

            elif message_type == 'pattern_request':
                result = await self._handle_pattern_request(message)

            elif message_type == 'validation_result':
                result = await self._handle_validation_result(message)

            elif message_type == 'library_update':
                result = await self._handle_library_update(message)

            else:
                self.logger.log('unknown_message_type', {
                    'type': message_type,
                    'message': message
                }, 'WARNING')

        except Exception as e:
            self.logger.log_error(e, {'message': message})
            result = {'success': False, 'error': str(e)}

        finally:
            self.logger.end_operation(f"process_{message_type}")

        return result

    async def _handle_pattern_submission(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle new pattern submission from an engine
        """
        pattern = message.get('pattern', {})
        source_engine = message.get('source_engine')

        # Generate pattern ID
        pattern_id = self._generate_pattern_id(pattern)

        # Check if pattern already exists
        if pattern_id in self.pattern_library:
            # Update existing pattern with new evidence
            existing = self.pattern_library[pattern_id]
            existing['evidence_count'] += 1
            existing['last_seen'] = datetime.utcnow().isoformat()

            self.logger.log('pattern_reinforced', {
                'pattern_id': pattern_id,
                'evidence_count': existing['evidence_count'],
                'source': source_engine
            })

            return {
                'success': True,
                'pattern_id': pattern_id,
                'status': 'reinforced',
                'confidence': existing['confidence']
            }

        # New pattern - add to validation queue
        validation_entry = {
            'pattern_id': pattern_id,
            'pattern': pattern,
            'source_engine': source_engine,
            'submitted_at': datetime.utcnow().isoformat(),
            'validation_status': 'pending',
            'ai_validations': []
        }

        self.pending_validation.append(validation_entry)

        # Trigger validation process
        asyncio.create_task(self._validate_pattern(validation_entry))

        self.logger.log('pattern_submitted', {
            'pattern_id': pattern_id,
            'source': source_engine,
            'category': pattern.get('category', 'unknown')
        })

        return {
            'success': True,
            'pattern_id': pattern_id,
            'status': 'validating',
            'message': 'Pattern submitted for validation'
        }

    async def _validate_pattern(self, validation_entry: Dict[str, Any]):
        """
        Validate pattern through multi-AI consensus
        """
        pattern_id = validation_entry['pattern_id']
        pattern = validation_entry['pattern']

        self.logger.log('validation_started', {
            'pattern_id': pattern_id,
            'pattern_type': pattern.get('type')
        })

        # Request validation from multiple AI engines
        validation_requests = [
            {
                'target': 'ai_consensus',
                'data': {
                    'type': 'pattern_validation',
                    'pattern': pattern,
                    'validation_type': 'gemini'
                }
            },
            {
                'target': 'ai_consensus',
                'data': {
                    'type': 'pattern_validation',
                    'pattern': pattern,
                    'validation_type': 'claude'
                }
            },
            {
                'target': 'ai_consensus',
                'data': {
                    'type': 'pattern_validation',
                    'pattern': pattern,
                    'validation_type': 'gpt4'
                }
            }
        ]

        # Send validation requests
        for request in validation_requests:
            await self.message_queue.send_message(
                request['target'],
                request['data']
            )

        # Mark as awaiting validation
        validation_entry['validation_status'] = 'validating'

    async def _handle_validation_result(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle validation result from AI consensus engine
        """
        pattern_id = message.get('pattern_id')
        validation = message.get('validation', {})

        # Find the validation entry
        entry = None
        for v in self.pending_validation:
            if v['pattern_id'] == pattern_id:
                entry = v
                break

        if not entry:
            return {'success': False, 'error': 'Pattern not found in validation queue'}

        # Add validation result
        entry['ai_validations'].append(validation)

        # Check if we have enough validations for consensus
        if len(entry['ai_validations']) >= 3:
            # Calculate consensus
            confidence_scores = [v.get('confidence', 0) for v in entry['ai_validations']]
            avg_confidence = sum(confidence_scores) / len(confidence_scores)

            agreements = [v.get('valid', False) for v in entry['ai_validations']]
            consensus = sum(agreements) / len(agreements)

            # Check if pattern should be crystallized
            if consensus >= self.consensus_threshold and avg_confidence >= self.confidence_threshold:
                # Crystallize the pattern
                await self._crystallize_pattern(entry, avg_confidence)

                # Remove from validation queue
                self.pending_validation.remove(entry)

                return {
                    'success': True,
                    'pattern_id': pattern_id,
                    'status': 'crystallized',
                    'confidence': avg_confidence
                }
            else:
                # Pattern rejected
                self.logger.log('pattern_rejected', {
                    'pattern_id': pattern_id,
                    'consensus': consensus,
                    'confidence': avg_confidence
                })

                # Remove from validation queue
                self.pending_validation.remove(entry)

                return {
                    'success': True,
                    'pattern_id': pattern_id,
                    'status': 'rejected',
                    'reason': f'Insufficient consensus ({consensus:.2f}) or confidence ({avg_confidence:.2f})'
                }

        return {
            'success': True,
            'pattern_id': pattern_id,
            'status': 'validating',
            'validations_received': len(entry['ai_validations'])
        }

    async def _crystallize_pattern(self, validation_entry: Dict[str, Any], confidence: float):
        """
        Crystallize validated pattern into deterministic rule
        """
        pattern_id = validation_entry['pattern_id']
        pattern = validation_entry['pattern']

        # Create crystallized pattern entry
        crystallized = {
            'pattern_id': pattern_id,
            'pattern': pattern,
            'confidence': confidence,
            'source_engine': validation_entry['source_engine'],
            'crystallized_at': datetime.utcnow().isoformat(),
            'evidence_count': 1,
            'last_seen': datetime.utcnow().isoformat(),
            'category': pattern.get('category', 'general'),
            'type': pattern.get('type', 'unknown'),
            'validations': validation_entry['ai_validations'],
            'active': True
        }

        # Add to pattern library
        self.pattern_library[pattern_id] = crystallized

        # Persist to database
        await self._persist_pattern(crystallized)

        # Log crystallization
        self.logger.log_decision(
            'pattern_crystallized',
            validation_entry,
            crystallized,
            reasoning='Pattern validated through multi-AI consensus',
            confidence=confidence
        )

        # Notify interested engines
        await self.message_queue.broadcast_message('pattern_crystallized', {
            'pattern_id': pattern_id,
            'category': crystallized['category'],
            'type': crystallized['type'],
            'confidence': confidence
        })

    async def _handle_pattern_request(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle pattern request from an engine
        """
        pattern_type = message.get('pattern_type')
        category = message.get('category')
        requesting_engine = message.get('source')

        # Find matching patterns
        matching_patterns = []

        for pattern_id, pattern_data in self.pattern_library.items():
            if pattern_data['active']:
                if pattern_type and pattern_data.get('type') == pattern_type:
                    matching_patterns.append(pattern_data)
                elif category and pattern_data.get('category') == category:
                    matching_patterns.append(pattern_data)

        # Sort by confidence and recency
        matching_patterns.sort(
            key=lambda p: (p['confidence'], p['last_seen']),
            reverse=True
        )

        # Log pattern retrieval
        self.logger.log('pattern_served', {
            'requesting_engine': requesting_engine,
            'pattern_type': pattern_type,
            'category': category,
            'patterns_found': len(matching_patterns)
        })

        if matching_patterns:
            # Return best match
            best_pattern = matching_patterns[0]
            return {
                'success': True,
                'pattern': best_pattern['pattern'],
                'pattern_id': best_pattern['pattern_id'],
                'confidence': best_pattern['confidence'],
                'alternatives': len(matching_patterns) - 1
            }
        else:
            return {
                'success': False,
                'message': 'No matching patterns found',
                'pattern_type': pattern_type,
                'category': category
            }

    async def _handle_library_update(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle updates to pattern library
        """
        action = message.get('action')

        if action == 'reload':
            # Reload pattern library from database
            self._load_pattern_library()
            return {'success': True, 'patterns_loaded': len(self.pattern_library)}

        elif action == 'prune':
            # Remove old/unused patterns
            pruned = await self._prune_patterns()
            return {'success': True, 'patterns_pruned': pruned}

        elif action == 'export':
            # Export patterns for backup
            export_path = await self._export_patterns()
            return {'success': True, 'export_path': export_path}

        else:
            return {'success': False, 'error': f'Unknown action: {action}'}

    async def _prune_patterns(self) -> int:
        """
        Remove old or low-confidence patterns
        """
        pruned = 0
        cutoff_date = datetime.utcnow() - timedelta(days=30)

        patterns_to_remove = []

        for pattern_id, pattern_data in self.pattern_library.items():
            # Check if pattern is old and has low confidence
            last_seen = datetime.fromisoformat(pattern_data['last_seen'].replace('Z', '+00:00'))

            if last_seen < cutoff_date and pattern_data['confidence'] < 0.5:
                patterns_to_remove.append(pattern_id)
                pruned += 1

        # Remove pruned patterns
        for pattern_id in patterns_to_remove:
            del self.pattern_library[pattern_id]

        self.logger.log('patterns_pruned', {
            'count': pruned,
            'remaining': len(self.pattern_library)
        })

        return pruned

    async def _export_patterns(self) -> str:
        """
        Export pattern library to file
        """
        export_dir = Path('v2/patterns/exports')
        export_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        export_file = export_dir / f'pattern_library_{timestamp}.json'

        with open(export_file, 'w') as f:
            json.dump(self.pattern_library, f, indent=2, default=str)

        self.logger.log('patterns_exported', {
            'file': str(export_file),
            'pattern_count': len(self.pattern_library)
        })

        return str(export_file)

    async def _persist_pattern(self, pattern: Dict[str, Any]):
        """
        Persist pattern to database
        """
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO pattern_library
                (pattern_id, pattern_type, pattern_data, confidence, active, last_updated)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (pattern_id)
                DO UPDATE SET
                    pattern_data = EXCLUDED.pattern_data,
                    confidence = EXCLUDED.confidence,
                    last_updated = EXCLUDED.last_updated
            """, (
                pattern['pattern_id'],
                pattern['type'],
                json.dumps(pattern['pattern']),
                pattern['confidence'],
                pattern['active'],
                datetime.utcnow()
            ))

            conn.commit()
            cursor.close()

        except Exception as e:
            self.logger.log_error(e, {'context': 'persist_pattern'})

    def _generate_pattern_id(self, pattern: Dict[str, Any]) -> str:
        """
        Generate unique ID for pattern
        """
        # Create hash from pattern content
        pattern_str = json.dumps(pattern, sort_keys=True)
        return hashlib.sha256(pattern_str.encode()).hexdigest()[:16]

    def _load_pattern_library(self):
        """
        Load pattern library from database or files
        """
        try:
            # Try loading from database first
            conn = self.get_db_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT pattern_id, pattern_type, pattern_data, confidence, last_updated
                FROM pattern_library
                WHERE active = true
            """)

            for row in cursor.fetchall():
                self.pattern_library[row['pattern_id']] = {
                    'pattern_id': row['pattern_id'],
                    'type': row['pattern_type'],
                    'pattern': row['pattern_data'],
                    'confidence': row['confidence'],
                    'last_seen': row['last_updated'].isoformat(),
                    'active': True,
                    'evidence_count': 1
                }

            cursor.close()

            self.logger.log('pattern_library_loaded', {
                'source': 'database',
                'patterns': len(self.pattern_library)
            })

        except Exception:
            # Fall back to loading from files
            self._load_patterns_from_files()

    def _load_patterns_from_files(self):
        """
        Load initial patterns from files (bootstrap)
        """
        pattern_dir = Path('v2/patterns')
        pattern_dir.mkdir(parents=True, exist_ok=True)

        # Load any existing pattern files
        for pattern_file in pattern_dir.glob('*.json'):
            try:
                with open(pattern_file, 'r') as f:
                    patterns = json.load(f)

                    if isinstance(patterns, dict):
                        for pattern_id, pattern_data in patterns.items():
                            self.pattern_library[pattern_id] = pattern_data
                    elif isinstance(patterns, list):
                        for pattern_data in patterns:
                            pattern_id = self._generate_pattern_id(pattern_data)
                            self.pattern_library[pattern_id] = {
                                'pattern_id': pattern_id,
                                'pattern': pattern_data,
                                'confidence': 1.0,
                                'active': True,
                                'last_seen': datetime.utcnow().isoformat()
                            }

                self.logger.log('patterns_loaded_from_file', {
                    'file': pattern_file.name,
                    'patterns': len(patterns) if isinstance(patterns, (dict, list)) else 0
                })

            except Exception as e:
                self.logger.log_error(e, {'file': str(pattern_file)})

    def get_capabilities(self) -> Dict[str, Any]:
        """
        Return Librarian capabilities
        """
        return {
            'engine': 'librarian',
            'version': self.version,
            'capabilities': [
                'pattern_validation',
                'pattern_crystallization',
                'pattern_serving',
                'multi_ai_consensus',
                'knowledge_management'
            ],
            'pattern_categories': self.pattern_categories,
            'library_size': len(self.pattern_library),
            'pending_validations': len(self.pending_validation),
            'consensus_threshold': self.consensus_threshold,
            'confidence_threshold': self.confidence_threshold
        }

    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """
        Validate Librarian configuration
        """
        required_fields = ['consensus_threshold', 'confidence_threshold']

        for field in required_fields:
            if field not in config:
                return False

        # Validate thresholds
        if not (0.0 <= config['consensus_threshold'] <= 1.0):
            return False
        if not (0.0 <= config['confidence_threshold'] <= 1.0):
            return False

        return True


async def main():
    """
    Main entry point for Librarian Engine
    """
    engine = LibrarianEngine()

    # Log capabilities
    capabilities = engine.get_capabilities()
    engine.logger.log('engine_capabilities', capabilities)

    # Start processing
    await engine.start()


if __name__ == '__main__':
    asyncio.run(main())