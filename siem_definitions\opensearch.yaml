# OpenSearch SIEM Configuration
# Query Language: OpenSearch Query DSL / Lucene

platform:
  name: opensearch
  display_name: OpenSearch
  query_language: opensearch-dsl
  description: Open source search and analytics engine (AWS fork of Elasticsearch)
  vendor: Amazon Web Services / OpenSearch Community
  version: "1.0"
  active: true

# Field mappings: generic_field -> OpenSearch field (ECS-compatible)
field_mappings:
  source_ip: source.ip
  destination_ip: destination.ip
  username: user.name
  process_name: process.name
  file_hash: file.hash.sha256
  event_id: event.code
  hostname: host.name
  port: destination.port
  source_port: source.port
  destination_port: destination.port
  domain: dns.question.name
  url: url.full
  file_name: file.name
  file_path: file.path
  registry_path: registry.path
  command_line: process.command_line
  parent_process: process.parent.name
  network_protocol: network.protocol
  http_method: http.request.method
  user_agent: user_agent.original
  email_sender: email.from.address
  email_recipient: email.to.address
  dns_query: dns.question.name
  service_name: service.name
  account_name: user.name
  process_id: process.pid
  parent_process_id: process.parent.pid
  user_domain: user.domain
  host_os: host.os.name
  event_action: event.action
  event_outcome: event.outcome
  event_category: event.category
  event_type: event.type

# Operator mappings: generic_operator -> OpenSearch DSL
operator_mappings:
  equals: match
  not_equals: must_not
  contains: wildcard
  not_contains: must_not_wildcard
  regex: regexp
  greater_than: gt
  less_than: lt
  greater_equal: gte
  less_equal: lte
  in_list: terms
  not_in_list: must_not_terms
  exists: exists
  not_exists: must_not_exists
  range: range
  prefix: prefix
  fuzzy: fuzzy

# Time field for temporal queries
time_field: "@timestamp"

# Query syntax specifics
syntax:
  comment: "//"
  string_quote: "\""
  escape_char: "\\"
  wildcard: "*"
  field_separator: "."
  logical_and: AND
  logical_or: OR
  logical_not: NOT
  query_dsl_format: json
  lucene_syntax_supported: true
  case_sensitive: false
  fuzzy_operator: "~"
  boost_operator: "^"

# Platform-specific features
metadata:
  supports_regex: true
  supports_wildcards: true
  supports_aggregations: true
  supports_scripting: true
  supports_painless: true
  supports_sql: true  # SQL query language
  supports_ppl: true  # Piped Processing Language
  max_result_window: 10000
  default_time_range: "15m"

  # Common index patterns
  index_patterns:
    - logs-*
    - security-*
    - filebeat-*
    - winlogbeat-*
    - packetbeat-*
    - auditbeat-*
    - heartbeat-*
    - metricbeat-*

  # OpenSearch Dashboards (formerly Kibana)
  dashboards_supported: true
  visualizations_supported: true

  # Query DSL template (JSON format)
  rule_template: |
    {
      "query": {
        "bool": {
          "must": [
            {"match": {"{field}": "{value}"}},
            {"range": {"@timestamp": {"gte": "now-{time_range}"}}}
          ]
        }
      },
      "sort": [{"@timestamp": {"order": "desc"}}],
      "size": 100
    }

  # Lucene query template (alternative simple syntax)
  lucene_template: |
    {field}:"{value}" AND @timestamp:[now-{time_range} TO now]

  # SQL query template
  sql_template: |
    SELECT * FROM {index}
    WHERE {field} = '{value}'
    AND @timestamp > NOW() - INTERVAL '{time_range}'

  # PPL query template (Piped Processing Language)
  ppl_template: |
    source={index} | where {field}='{value}' | where @timestamp > now() - {time_range}

  # Aggregation types
  aggregation_types:
    - terms  # Bucket aggregation
    - date_histogram  # Time-based buckets
    - range  # Numeric ranges
    - histogram  # Numeric histogram
    - stats  # Statistics (min, max, avg, sum)
    - cardinality  # Unique values
    - percentiles  # Percentile ranks
    - geo_distance  # Geographic distance
    - significant_terms  # Unusual terms
    - top_hits  # Top matching documents

  # Alerting (OpenSearch Alerting plugin)
  alerting_supported: true
  alert_types:
    - Per query monitor  # Query-based alerts
    - Per bucket monitor  # Aggregation-based alerts
    - Per document monitor  # Document-level alerts
    - Composite monitor  # Combined monitors

  # Alert destinations
  alert_destinations:
    - Email
    - Slack
    - Amazon SNS
    - Amazon Chime
    - Custom webhook
    - Microsoft Teams (via webhook)
    - PagerDuty (via webhook)

  # Security Analytics (built-in SIEM features)
  security_analytics:
    - Threat detection
    - Log correlation
    - Sigma rule support  # Native Sigma rules!
    - Pre-packaged detections
    - Custom detection rules
    - Alert correlation
    - Threat intelligence integration

  # Sigma rule support (MAJOR ADVANTAGE)
  supports_sigma_rules: true
  sigma_rule_categories:
    - network
    - process_creation
    - file_event
    - registry_event
    - image_load
    - dns_query
    - pipe_created
    - raw_access_thread

  # Security Analytics rule template
  security_rule_template: |
    name: {rule_name}
    description: {description}
    references:
      - {reference}
    tags:
      - attack.{tactic}
      - attack.{technique}
    log_source:
      category: {category}
      product: {product}
    detection:
      selection:
        {field}: {value}
      condition: selection
    level: {severity}

  # Index State Management
  supports_ism: true
  ism_policies:
    - hot-warm-cold  # Tiered storage
    - snapshot  # Automated backups
    - delete  # Automated deletion
    - rollover  # Index rollover
    - read_only  # Make read-only
    - shrink  # Reduce shards
    - force_merge  # Merge segments

  # Machine Learning (OpenSearch ML)
  supports_ml: true
  ml_capabilities:
    - Anomaly detection
    - k-NN search  # Vector search
    - Learning to rank
    - RCF (Random Cut Forest)
    - AD (Anomaly Detection)

  # Performance optimization
  performance_features:
    - Index templates
    - Index aliases
    - Shard allocation
    - Refresh intervals
    - Segment merging
    - Query caching
    - Request caching

  # Data formats
  supported_formats:
    - JSON
    - NDJSON (newline-delimited)
    - CSV (via Logstash)
    - Syslog
    - CEF
    - LEEF

  # Integration capabilities
  integrations:
    - Logstash  # Data ingestion
    - Beats  # Lightweight shippers
    - Fluent Bit  # Log forwarder
    - Data Prepper  # Trace analytics
    - AWS services (S3, Lambda, CloudWatch)
    - SIEM tools
    - Threat intelligence feeds

  # API support
  rest_api: true
  bulk_api: true
  search_api: true
  index_api: true
  document_api: true

  # Cross-cluster search
  supports_cross_cluster_search: true

  # Snapshot and restore
  supports_snapshots: true
  snapshot_repositories:
    - S3
    - Azure Blob
    - Google Cloud Storage
    - Filesystem
    - HDFS
