# Production Scaling for High-Volume Log Ingestion

## Current Implementation
- Fetches 5-10 logs per cycle (for testing)
- Processes every 10 seconds
- Routes ALL logs to ALL engines

## Handling >10,000 Logs/Second

### 1. Batch Processing Strategy
```python
# In ingestion_engine.py - modify _fetch_real_data()
async def _fetch_real_data(self) -> List[Dict[str, Any]]:
    logs = []
    for source_id, source_info in self.active_sources.items():
        if source_type == 'elasticsearch':
            config = {
                'source_name': source_id,
                'index_pattern': 'logs-*',
                'timestamp_field': '@timestamp'
            }

            ingestor = UniversalElasticIngestor(config, self.logger)

            # PRODUCTION SETTINGS
            BATCH_SIZE = 100        # Process 100 at a time
            MAX_PER_CYCLE = 500     # Max 500 per 10-second cycle

            count = 0
            for log_data in ingestor.fetch_logs(batch_size=BATCH_SIZE):
                logs.append({...})
                count += 1

                # Yield control periodically
                if count % 10 == 0:
                    await asyncio.sleep(0)

                if count >= MAX_PER_CYCLE:
                    break
```

### 2. Intelligent Routing (Don't Send Everything Everywhere)
```python
async def _route_log(self, log: Dict, source_info: Dict):
    """Route based on log type and content"""

    # Only send security events to Intelligence Engine
    if self._is_security_event(log):
        self.publish_message('intelligence.analyze_log', log)

    # Only send logs with IPs/users to Contextualization
    if self._has_entities(log):
        self.publish_message('contextualization.enrich_log', log)

    # Always store raw logs
    self.publish_message('backend.store_raw', log)
```

### 3. Backpressure Handling
```python
class IngestionEngine(BaseEngine):
    def __init__(self):
        super().__init__("ingestion")
        self.pending_queue = asyncio.Queue(maxsize=1000)  # Buffer
        self.processing_semaphore = asyncio.Semaphore(10)  # Max 10 concurrent
```

### 4. Database Batching
```python
async def _store_batch_to_db(self, logs: List[Dict]):
    """Batch insert for efficiency"""
    if len(logs) >= 100:  # Batch threshold
        query = """
            INSERT INTO ingestion_logs (source_id, log_data, timestamp)
            VALUES %s
            ON CONFLICT DO NOTHING
        """
        # Use execute_values for batch insert
        psycopg2.extras.execute_values(
            self.db_cursor, query, logs, template=None, page_size=100
        )
```

### 5. Rate Limiting Per Source
```python
class RateLimiter:
    def __init__(self, max_per_second: int = 100):
        self.max_per_second = max_per_second
        self.tokens = max_per_second
        self.last_update = time.time()

    async def acquire(self, count: int = 1):
        """Token bucket algorithm"""
        now = time.time()
        elapsed = now - self.last_update
        self.tokens = min(
            self.max_per_second,
            self.tokens + elapsed * self.max_per_second
        )
        self.last_update = now

        if self.tokens >= count:
            self.tokens -= count
            return True

        # Wait for tokens to refill
        wait_time = (count - self.tokens) / self.max_per_second
        await asyncio.sleep(wait_time)
        self.tokens = 0
        return True
```

### 6. Monitoring & Metrics
```python
self.metrics = {
    'logs_per_second': 0,
    'dropped_logs': 0,
    'queue_depth': 0,
    'processing_lag': 0,
    'memory_usage': 0
}

async def _monitor_performance(self):
    """Alert if falling behind"""
    if self.pending_queue.qsize() > 800:  # 80% full
        self.logger.warning("Queue backing up! Consider scaling")

    if self.metrics['processing_lag'] > 30:  # 30 seconds behind
        self.logger.error("Critical lag! Dropping non-essential logs")
        self.drop_non_critical = True
```

### 7. Horizontal Scaling Strategy

For >10k logs/second, run multiple Ingestion Engine instances:

```yaml
# docker-compose.yml
ingestion_engine_1:
  # ... config
  environment:
    - INSTANCE_ID=1
    - SHARD_RANGE=0-33  # Handle 33% of sources

ingestion_engine_2:
  # ... config
  environment:
    - INSTANCE_ID=2
    - SHARD_RANGE=34-66

ingestion_engine_3:
  # ... config
  environment:
    - INSTANCE_ID=3
    - SHARD_RANGE=67-100
```

### 8. Configuration for Different Scales

#### Small (100-1000 logs/sec)
```python
BATCH_SIZE = 50
MAX_PER_CYCLE = 200
PROCESS_INTERVAL = 5  # seconds
```

#### Medium (1000-5000 logs/sec)
```python
BATCH_SIZE = 100
MAX_PER_CYCLE = 500
PROCESS_INTERVAL = 2
ENABLE_SAMPLING = True  # Process 10% for AI
```

#### Large (5000-50000 logs/sec)
```python
BATCH_SIZE = 500
MAX_PER_CYCLE = 2000
PROCESS_INTERVAL = 1
ENABLE_SAMPLING = True  # Process 1% for AI
USE_KAFKA = True  # Switch from Redis to Kafka
```

## Critical Optimizations

1. **Sampling for AI**: Don't send ALL logs to expensive AI analysis
   ```python
   if random.random() < 0.01:  # 1% sample
       self.publish_message('intelligence.analyze_log', log)
   ```

2. **Prioritization**: Process security events first
   ```python
   if log.get('severity') == 'critical':
       await self.priority_queue.put(log)
   else:
       await self.normal_queue.put(log)
   ```

3. **Circuit Breaker**: Stop fetching if downstream is overwhelmed
   ```python
   if self.downstream_errors > 10:
       self.logger.error("Circuit breaker activated")
       await asyncio.sleep(60)  # Back off for 1 minute
   ```

## Warning Signs to Watch

1. **Redis Memory > 80%** - Queue backing up
2. **Ingestion lag > 1 minute** - Can't keep up
3. **Dropped logs > 0** - Losing data
4. **CPU > 90%** - Need more instances
5. **Network I/O saturated** - Need batching

## Quick Fixes for Overload

1. **Emergency**: Drop all non-security logs
2. **Temporary**: Increase batch sizes, reduce frequency
3. **Quick Scale**: `docker-compose scale ingestion_engine=3`
4. **Last Resort**: Enable write-ahead log, process offline

---

Remember: It's better to sample intelligently than to crash trying to process everything!