#!/usr/bin/env python3
"""
SIEMLess v2.0 Investigation Engine

Advanced threat hunting, digital forensics, and security investigation capabilities.
Provides comprehensive investigation workflows with AI-assisted analysis.

Author: SIEMLess Development Team
Version: 2.0.0
"""

import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, asdict
import logging
import hashlib
import re

from shared.base_engine import BaseEngine
from shared.queue.queue_manager import QueueManager
from shared.logging.siemless_logger import SIEMLessLogger

class InvestigationType(Enum):
    """Types of security investigations"""
    THREAT_HUNT = "threat_hunt"
    DIGITAL_FORENSICS = "digital_forensics"
    INCIDENT_RESPONSE = "incident_response"
    MALWARE_ANALYSIS = "malware_analysis"
    BREACH_INVESTIGATION = "breach_investigation"
    INSIDER_THREAT = "insider_threat"
    VULNERABILITY_ASSESSMENT = "vulnerability_assessment"
    COMPLIANCE_AUDIT = "compliance_audit"

class InvestigationStatus(Enum):
    """Investigation status enumeration"""
    INITIATED = "initiated"
    DATA_COLLECTION = "data_collection"
    ANALYSIS = "analysis"
    CORRELATION = "correlation"
    HYPOTHESIS_TESTING = "hypothesis_testing"
    EVIDENCE_REVIEW = "evidence_review"
    COMPLETED = "completed"
    SUSPENDED = "suspended"

class EvidenceQuality(Enum):
    """Digital evidence quality levels"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    CORRUPTED = "corrupted"
    INCONCLUSIVE = "inconclusive"

class AnalysisConfidence(Enum):
    """Confidence levels for analysis results"""
    VERY_HIGH = "very_high"  # 90-100%
    HIGH = "high"           # 70-89%
    MEDIUM = "medium"       # 50-69%
    LOW = "low"            # 30-49%
    VERY_LOW = "very_low"  # 0-29%

@dataclass
class InvestigationHypothesis:
    """Investigation hypothesis with testing results"""
    hypothesis_id: str
    description: str
    created_by: str
    created_at: datetime
    supporting_evidence: List[str]
    contradicting_evidence: List[str]
    confidence_score: float
    status: str  # "testing", "confirmed", "rejected", "inconclusive"
    mitre_techniques: List[str]
    test_results: List[Dict[str, Any]]

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        return data

@dataclass
class DigitalEvidence:
    """Digital evidence with forensic metadata"""
    evidence_id: str
    investigation_id: str
    source_system: str
    evidence_type: str
    file_path: Optional[str]
    file_hash: Optional[str]
    file_size: Optional[int]
    collected_at: datetime
    collected_by: str
    acquisition_method: str
    chain_of_custody: List[Dict[str, Any]]
    integrity_verified: bool
    quality: EvidenceQuality
    metadata: Dict[str, Any]
    analysis_results: List[Dict[str, Any]]

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['collected_at'] = self.collected_at.isoformat()
        data['quality'] = self.quality.value
        return data

@dataclass
class ThreatIndicator:
    """Threat indicator with context and relationships"""
    indicator_id: str
    indicator_type: str  # ip, domain, hash, email, etc.
    indicator_value: str
    first_seen: datetime
    last_seen: datetime
    confidence: AnalysisConfidence
    threat_level: str
    source: str
    context: Dict[str, Any]
    related_indicators: List[str]
    mitre_techniques: List[str]
    campaigns: List[str]

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['first_seen'] = self.first_seen.isoformat()
        data['last_seen'] = self.last_seen.isoformat()
        data['confidence'] = self.confidence.value
        return data

@dataclass
class InvestigationTimeline:
    """Investigation timeline event"""
    event_id: str
    investigation_id: str
    timestamp: datetime
    event_type: str
    description: str
    source_system: str
    entities_involved: List[str]
    evidence_references: List[str]
    confidence: AnalysisConfidence
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['confidence'] = self.confidence.value
        return data

@dataclass
class SecurityInvestigation:
    """Core investigation entity"""
    investigation_id: str
    title: str
    description: str
    investigation_type: InvestigationType
    status: InvestigationStatus
    priority: str
    lead_investigator: str
    team_members: List[str]
    created_at: datetime
    updated_at: datetime
    target_completion: Optional[datetime]

    # Investigation scope
    target_systems: List[str]
    target_timeframe: Dict[str, datetime]
    investigation_scope: str

    # Hypotheses and theories
    hypotheses: List[InvestigationHypothesis]
    current_theory: Optional[str]

    # Evidence and artifacts
    evidence: List[DigitalEvidence]
    indicators: List[ThreatIndicator]
    timeline: List[InvestigationTimeline]

    # AI Analysis
    ai_insights: List[Dict[str, Any]]
    pattern_matches: List[Dict[str, Any]]
    anomalies_detected: List[Dict[str, Any]]

    # MITRE ATT&CK mapping
    attack_patterns: List[str]
    techniques_observed: List[str]
    tactics_identified: List[str]

    # Results and conclusions
    findings: List[Dict[str, Any]]
    recommendations: List[str]
    lessons_learned: List[str]

    # Integration data
    related_cases: List[str]
    external_references: List[str]
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['investigation_type'] = self.investigation_type.value
        data['status'] = self.status.value
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        if self.target_completion:
            data['target_completion'] = self.target_completion.isoformat()
        data['target_timeframe'] = {k: v.isoformat() for k, v in self.target_timeframe.items()}
        data['hypotheses'] = [h.to_dict() for h in self.hypotheses]
        data['evidence'] = [e.to_dict() for e in self.evidence]
        data['indicators'] = [i.to_dict() for i in self.indicators]
        data['timeline'] = [t.to_dict() for t in self.timeline]
        return data

class InvestigationEngine(BaseEngine):
    """
    Investigation Engine for SIEMLess v2.0

    Provides advanced threat hunting, digital forensics, and security investigation
    capabilities with AI-assisted analysis and pattern recognition.
    """

    def __init__(self):
        super().__init__("investigation", "2.0.0")
        self.investigations: Dict[str, SecurityInvestigation] = {}
        self.hunt_templates: Dict[str, Dict[str, Any]] = {}
        self.analysis_patterns: Dict[str, Dict[str, Any]] = {}
        self.threat_intel_cache: Dict[str, Dict[str, Any]] = {}

        # Initialize templates and patterns
        self._initialize_hunt_templates()
        self._initialize_analysis_patterns()

    async def initialize(self) -> bool:
        """Initialize the Investigation Engine"""
        try:
            self.logger.info("Initializing Investigation Engine v2.0")

            # Register message handlers
            await self.queue_manager.register_handler(
                "investigation.create", self._handle_create_investigation
            )
            await self.queue_manager.register_handler(
                "investigation.add_evidence", self._handle_add_evidence
            )
            await self.queue_manager.register_handler(
                "investigation.analyze", self._handle_analyze_request
            )
            await self.queue_manager.register_handler(
                "investigation.hunt", self._handle_threat_hunt
            )
            await self.queue_manager.register_handler(
                "detection.new_alert", self._handle_new_alert
            )

            # Start background tasks
            asyncio.create_task(self._continuous_analysis())
            asyncio.create_task(self._threat_intel_enrichment())
            asyncio.create_task(self._pattern_discovery())

            self.logger.info("Investigation Engine initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Investigation Engine: {e}")
            return False

    def _initialize_hunt_templates(self):
        """Initialize threat hunting templates"""
        self.hunt_templates = {
            "apt_lateral_movement": {
                "name": "APT Lateral Movement Hunt",
                "description": "Hunt for APT lateral movement techniques",
                "indicators": [
                    {"type": "process", "patterns": ["psexec", "wmic", "powershell"]},
                    {"type": "network", "patterns": ["smb", "rdp", "winrm"]},
                    {"type": "authentication", "patterns": ["pass_the_hash", "golden_ticket"]}
                ],
                "mitre_techniques": ["T1021", "T1550", "T1078"],
                "search_timeframe": "7d",
                "confidence_threshold": 0.7
            },
            "data_exfiltration": {
                "name": "Data Exfiltration Hunt",
                "description": "Hunt for data exfiltration activities",
                "indicators": [
                    {"type": "network", "patterns": ["large_upload", "dns_tunneling", "uncommon_ports"]},
                    {"type": "file", "patterns": ["compression", "encryption", "staging"]},
                    {"type": "process", "patterns": ["archiving_tools", "ftp_clients"]}
                ],
                "mitre_techniques": ["T1041", "T1048", "T1020"],
                "search_timeframe": "30d",
                "confidence_threshold": 0.8
            },
            "insider_threat": {
                "name": "Insider Threat Hunt",
                "description": "Hunt for insider threat indicators",
                "indicators": [
                    {"type": "user", "patterns": ["off_hours_access", "privilege_escalation", "bulk_access"]},
                    {"type": "data", "patterns": ["sensitive_data_access", "unusual_queries"]},
                    {"type": "behavior", "patterns": ["policy_violations", "suspicious_downloads"]}
                ],
                "mitre_techniques": ["T1078", "T1087", "T1083"],
                "search_timeframe": "90d",
                "confidence_threshold": 0.6
            },
            "malware_persistence": {
                "name": "Malware Persistence Hunt",
                "description": "Hunt for malware persistence mechanisms",
                "indicators": [
                    {"type": "registry", "patterns": ["run_keys", "services", "scheduled_tasks"]},
                    {"type": "file", "patterns": ["startup_folders", "dll_hijacking"]},
                    {"type": "wmi", "patterns": ["event_subscriptions", "permanent_subscriptions"]}
                ],
                "mitre_techniques": ["T1053", "T1547", "T1546"],
                "search_timeframe": "14d",
                "confidence_threshold": 0.75
            }
        }

    def _initialize_analysis_patterns(self):
        """Initialize analysis patterns for automated detection"""
        self.analysis_patterns = {
            "command_and_control": {
                "pattern_type": "network_behavior",
                "indicators": [
                    "regular_beaconing",
                    "domain_generation_algorithm",
                    "encrypted_c2_traffic",
                    "dns_over_https"
                ],
                "detection_rules": [
                    {"metric": "connection_frequency", "threshold": 0.9, "window": "1h"},
                    {"metric": "domain_entropy", "threshold": 4.5, "window": "24h"},
                    {"metric": "ssl_cert_anomaly", "threshold": 0.8, "window": "1h"}
                ]
            },
            "privilege_escalation": {
                "pattern_type": "process_behavior",
                "indicators": [
                    "token_manipulation",
                    "process_injection",
                    "dll_hijacking",
                    "kernel_exploitation"
                ],
                "detection_rules": [
                    {"metric": "privilege_change", "threshold": 1.0, "window": "5m"},
                    {"metric": "process_hollowing", "threshold": 0.9, "window": "1m"},
                    {"metric": "dll_load_anomaly", "threshold": 0.8, "window": "10m"}
                ]
            },
            "credential_access": {
                "pattern_type": "authentication",
                "indicators": [
                    "credential_dumping",
                    "kerberoasting",
                    "password_spraying",
                    "brute_force"
                ],
                "detection_rules": [
                    {"metric": "failed_login_rate", "threshold": 10, "window": "5m"},
                    {"metric": "lsass_access", "threshold": 1.0, "window": "1m"},
                    {"metric": "kerberos_anomaly", "threshold": 0.7, "window": "15m"}
                ]
            }
        }

    async def create_investigation(self, investigation_data: Dict[str, Any]) -> SecurityInvestigation:
        """Create a new security investigation"""
        try:
            investigation_id = str(uuid.uuid4())
            current_time = datetime.utcnow()

            # Parse timeframe
            timeframe_start = datetime.fromisoformat(investigation_data.get('timeframe_start', current_time.isoformat()))
            timeframe_end = datetime.fromisoformat(investigation_data.get('timeframe_end', current_time.isoformat()))

            investigation = SecurityInvestigation(
                investigation_id=investigation_id,
                title=investigation_data['title'],
                description=investigation_data['description'],
                investigation_type=InvestigationType(investigation_data.get('investigation_type', InvestigationType.THREAT_HUNT.value)),
                status=InvestigationStatus.INITIATED,
                priority=investigation_data.get('priority', 'medium'),
                lead_investigator=investigation_data['lead_investigator'],
                team_members=investigation_data.get('team_members', []),
                created_at=current_time,
                updated_at=current_time,
                target_completion=datetime.fromisoformat(investigation_data['target_completion']) if investigation_data.get('target_completion') else None,
                target_systems=investigation_data.get('target_systems', []),
                target_timeframe={'start': timeframe_start, 'end': timeframe_end},
                investigation_scope=investigation_data.get('investigation_scope', ''),
                hypotheses=[],
                current_theory=None,
                evidence=[],
                indicators=[],
                timeline=[],
                ai_insights=[],
                pattern_matches=[],
                anomalies_detected=[],
                attack_patterns=investigation_data.get('attack_patterns', []),
                techniques_observed=[],
                tactics_identified=[],
                findings=[],
                recommendations=[],
                lessons_learned=[],
                related_cases=investigation_data.get('related_cases', []),
                external_references=investigation_data.get('external_references', []),
                metadata=investigation_data.get('metadata', {})
            )

            # Store the investigation
            self.investigations[investigation_id] = investigation

            # Auto-generate initial hypothesis if template provided
            template_name = investigation_data.get('hunt_template')
            if template_name and template_name in self.hunt_templates:
                await self._generate_template_hypothesis(investigation, template_name)

            # Broadcast investigation creation
            await self.queue_manager.publish("investigation.created", {
                "investigation_id": investigation_id,
                "investigation_data": investigation.to_dict()
            })

            self.logger.info(f"Created investigation {investigation_id}: {investigation.title}")
            return investigation

        except Exception as e:
            self.logger.error(f"Failed to create investigation: {e}")
            raise

    async def add_evidence(self, investigation_id: str, evidence_data: Dict[str, Any]) -> Optional[DigitalEvidence]:
        """Add digital evidence to an investigation"""
        try:
            if investigation_id not in self.investigations:
                self.logger.warning(f"Investigation {investigation_id} not found")
                return None

            investigation = self.investigations[investigation_id]
            evidence_id = str(uuid.uuid4())

            evidence = DigitalEvidence(
                evidence_id=evidence_id,
                investigation_id=investigation_id,
                source_system=evidence_data['source_system'],
                evidence_type=evidence_data['evidence_type'],
                file_path=evidence_data.get('file_path'),
                file_hash=evidence_data.get('file_hash'),
                file_size=evidence_data.get('file_size'),
                collected_at=datetime.utcnow(),
                collected_by=evidence_data['collected_by'],
                acquisition_method=evidence_data.get('acquisition_method', 'manual'),
                chain_of_custody=[{
                    "action": "collected",
                    "by": evidence_data['collected_by'],
                    "at": datetime.utcnow().isoformat(),
                    "method": evidence_data.get('acquisition_method', 'manual'),
                    "details": evidence_data.get('collection_details', {})
                }],
                integrity_verified=evidence_data.get('integrity_verified', False),
                quality=EvidenceQuality(evidence_data.get('quality', EvidenceQuality.MEDIUM.value)),
                metadata=evidence_data.get('metadata', {}),
                analysis_results=[]
            )

            investigation.evidence.append(evidence)
            investigation.updated_at = datetime.utcnow()

            # Auto-analyze evidence if possible
            if evidence.file_hash:
                await self._analyze_evidence(investigation, evidence)

            # Broadcast evidence addition
            await self.queue_manager.publish("investigation.evidence_added", {
                "investigation_id": investigation_id,
                "evidence_id": evidence_id,
                "evidence_data": evidence.to_dict()
            })

            self.logger.info(f"Added evidence {evidence_id} to investigation {investigation_id}")
            return evidence

        except Exception as e:
            self.logger.error(f"Failed to add evidence to investigation {investigation_id}: {e}")
            return None

    async def add_hypothesis(self, investigation_id: str, hypothesis_data: Dict[str, Any]) -> Optional[InvestigationHypothesis]:
        """Add a hypothesis to an investigation"""
        try:
            if investigation_id not in self.investigations:
                return None

            investigation = self.investigations[investigation_id]
            hypothesis_id = str(uuid.uuid4())

            hypothesis = InvestigationHypothesis(
                hypothesis_id=hypothesis_id,
                description=hypothesis_data['description'],
                created_by=hypothesis_data['created_by'],
                created_at=datetime.utcnow(),
                supporting_evidence=hypothesis_data.get('supporting_evidence', []),
                contradicting_evidence=[],
                confidence_score=hypothesis_data.get('confidence_score', 0.5),
                status="testing",
                mitre_techniques=hypothesis_data.get('mitre_techniques', []),
                test_results=[]
            )

            investigation.hypotheses.append(hypothesis)
            investigation.updated_at = datetime.utcnow()

            # Start hypothesis testing
            await self._test_hypothesis(investigation, hypothesis)

            self.logger.info(f"Added hypothesis {hypothesis_id} to investigation {investigation_id}")
            return hypothesis

        except Exception as e:
            self.logger.error(f"Failed to add hypothesis: {e}")
            return None

    async def conduct_threat_hunt(self, investigation_id: str, hunt_params: Dict[str, Any]) -> Dict[str, Any]:
        """Conduct a threat hunt as part of an investigation"""
        try:
            if investigation_id not in self.investigations:
                return {"success": False, "error": "Investigation not found"}

            investigation = self.investigations[investigation_id]
            hunt_results = {
                "hunt_id": str(uuid.uuid4()),
                "investigation_id": investigation_id,
                "started_at": datetime.utcnow().isoformat(),
                "hunt_type": hunt_params.get('hunt_type', 'custom'),
                "indicators_searched": [],
                "matches_found": [],
                "anomalies_detected": [],
                "confidence_scores": {},
                "recommendations": []
            }

            # Use template if specified
            template_name = hunt_params.get('template')
            if template_name and template_name in self.hunt_templates:
                template = self.hunt_templates[template_name]
                hunt_params = {**template, **hunt_params}

            # Search for indicators
            indicators = hunt_params.get('indicators', [])
            for indicator in indicators:
                matches = await self._search_indicator(indicator, hunt_params)
                if matches:
                    hunt_results["matches_found"].extend(matches)
                    hunt_results["indicators_searched"].append(indicator)

            # Analyze patterns
            pattern_analysis = await self._analyze_hunt_patterns(hunt_results["matches_found"])
            hunt_results["anomalies_detected"] = pattern_analysis.get("anomalies", [])
            hunt_results["confidence_scores"] = pattern_analysis.get("confidence_scores", {})

            # Generate recommendations
            recommendations = await self._generate_hunt_recommendations(hunt_results)
            hunt_results["recommendations"] = recommendations

            # Update investigation with hunt results
            investigation.pattern_matches.append(hunt_results)
            investigation.updated_at = datetime.utcnow()

            # Create timeline entries for significant findings
            for match in hunt_results["matches_found"]:
                if match.get("confidence", 0) > 0.7:
                    await self._add_timeline_event(investigation, {
                        "event_type": "threat_hunt_finding",
                        "description": f"Hunt finding: {match.get('description', 'Unknown')}",
                        "source_system": "investigation_engine",
                        "timestamp": match.get("timestamp", datetime.utcnow().isoformat()),
                        "confidence": AnalysisConfidence.HIGH,
                        "metadata": match
                    })

            hunt_results["completed_at"] = datetime.utcnow().isoformat()
            self.logger.info(f"Completed threat hunt for investigation {investigation_id}")

            return {"success": True, "hunt_results": hunt_results}

        except Exception as e:
            self.logger.error(f"Failed to conduct threat hunt: {e}")
            return {"success": False, "error": str(e)}

    async def _search_indicator(self, indicator: Dict[str, Any], hunt_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search for specific indicators in available data sources"""
        matches = []

        # This would integrate with various data sources
        # For now, return simulated matches based on indicator type
        indicator_type = indicator.get("type")
        patterns = indicator.get("patterns", [])

        for pattern in patterns:
            # Simulate search results
            if indicator_type == "process":
                matches.append({
                    "indicator_type": indicator_type,
                    "pattern": pattern,
                    "matches": f"Found {pattern} in process logs",
                    "timestamp": datetime.utcnow().isoformat(),
                    "confidence": 0.8,
                    "source": "process_logs",
                    "details": {"process_name": pattern, "frequency": 5}
                })
            elif indicator_type == "network":
                matches.append({
                    "indicator_type": indicator_type,
                    "pattern": pattern,
                    "matches": f"Found {pattern} in network logs",
                    "timestamp": datetime.utcnow().isoformat(),
                    "confidence": 0.75,
                    "source": "network_logs",
                    "details": {"protocol": pattern, "connections": 12}
                })

        return matches

    async def _analyze_hunt_patterns(self, matches: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze patterns in hunt results"""
        analysis = {
            "anomalies": [],
            "confidence_scores": {},
            "correlations": []
        }

        if not matches:
            return analysis

        # Group matches by type
        matches_by_type = {}
        for match in matches:
            indicator_type = match.get("indicator_type", "unknown")
            if indicator_type not in matches_by_type:
                matches_by_type[indicator_type] = []
            matches_by_type[indicator_type].append(match)

        # Detect anomalies
        for indicator_type, type_matches in matches_by_type.items():
            if len(type_matches) > 5:  # High frequency
                analysis["anomalies"].append({
                    "type": "high_frequency",
                    "indicator_type": indicator_type,
                    "count": len(type_matches),
                    "confidence": 0.8,
                    "description": f"High frequency of {indicator_type} indicators"
                })

            # Calculate confidence scores
            avg_confidence = sum(m.get("confidence", 0) for m in type_matches) / len(type_matches)
            analysis["confidence_scores"][indicator_type] = avg_confidence

        return analysis

    async def _generate_hunt_recommendations(self, hunt_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on hunt results"""
        recommendations = []

        matches_count = len(hunt_results.get("matches_found", []))
        anomalies_count = len(hunt_results.get("anomalies_detected", []))

        if matches_count > 10:
            recommendations.append("High number of matches detected - consider expanding investigation scope")

        if anomalies_count > 3:
            recommendations.append("Multiple anomalies detected - recommend immediate incident response")

        # Check confidence scores
        confidence_scores = hunt_results.get("confidence_scores", {})
        high_confidence_types = [t for t, c in confidence_scores.items() if c > 0.8]

        if high_confidence_types:
            recommendations.append(f"High confidence indicators for: {', '.join(high_confidence_types)}")

        if not recommendations:
            recommendations.append("No significant findings - consider adjusting hunt parameters")

        return recommendations

    async def _analyze_evidence(self, investigation: SecurityInvestigation, evidence: DigitalEvidence):
        """Perform automated analysis on digital evidence"""
        try:
            analysis_result = {
                "analysis_id": str(uuid.uuid4()),
                "analyzed_at": datetime.utcnow().isoformat(),
                "analysis_type": "automated",
                "findings": [],
                "confidence": AnalysisConfidence.MEDIUM,
                "tools_used": ["static_analysis", "hash_lookup", "pattern_matching"]
            }

            # Hash-based analysis
            if evidence.file_hash:
                # Simulate threat intel lookup
                threat_intel = await self._lookup_threat_intel(evidence.file_hash)
                if threat_intel:
                    analysis_result["findings"].append({
                        "type": "threat_intel_match",
                        "description": f"Hash matches known malware: {threat_intel.get('malware_family')}",
                        "confidence": 0.9,
                        "source": "threat_intel"
                    })
                    analysis_result["confidence"] = AnalysisConfidence.HIGH

            # File type analysis
            if evidence.file_path:
                file_ext = evidence.file_path.split('.')[-1].lower()
                if file_ext in ['exe', 'dll', 'scr', 'bat', 'ps1']:
                    analysis_result["findings"].append({
                        "type": "suspicious_file_type",
                        "description": f"Potentially suspicious file type: {file_ext}",
                        "confidence": 0.6,
                        "source": "file_analysis"
                    })

            evidence.analysis_results.append(analysis_result)

            # Generate indicators from analysis
            for finding in analysis_result["findings"]:
                if finding.get("confidence", 0) > 0.7:
                    await self._create_threat_indicator(investigation, evidence, finding)

        except Exception as e:
            self.logger.error(f"Failed to analyze evidence {evidence.evidence_id}: {e}")

    async def _lookup_threat_intel(self, file_hash: str) -> Optional[Dict[str, Any]]:
        """Lookup threat intelligence for a hash"""
        # Simulate threat intel lookup
        # In real implementation, this would query threat intel services

        if file_hash in self.threat_intel_cache:
            return self.threat_intel_cache[file_hash]

        # Simulate some known bad hashes
        known_bad = {
            "d41d8cd98f00b204e9800998ecf8427e": {
                "malware_family": "TestMalware",
                "threat_level": "high",
                "first_seen": "2024-01-01",
                "campaigns": ["APT-TEST"]
            }
        }

        result = known_bad.get(file_hash)
        if result:
            self.threat_intel_cache[file_hash] = result

        return result

    async def _create_threat_indicator(self, investigation: SecurityInvestigation, evidence: DigitalEvidence, finding: Dict[str, Any]):
        """Create a threat indicator from analysis findings"""
        try:
            indicator_id = str(uuid.uuid4())
            current_time = datetime.utcnow()

            indicator = ThreatIndicator(
                indicator_id=indicator_id,
                indicator_type=finding.get("type", "unknown"),
                indicator_value=evidence.file_hash or evidence.file_path or "unknown",
                first_seen=current_time,
                last_seen=current_time,
                confidence=AnalysisConfidence.HIGH if finding.get("confidence", 0) > 0.8 else AnalysisConfidence.MEDIUM,
                threat_level=finding.get("threat_level", "medium"),
                source=finding.get("source", "investigation_engine"),
                context={"evidence_id": evidence.evidence_id, "finding": finding},
                related_indicators=[],
                mitre_techniques=[],
                campaigns=[]
            )

            investigation.indicators.append(indicator)
            self.logger.info(f"Created threat indicator {indicator_id} for investigation {investigation.investigation_id}")

        except Exception as e:
            self.logger.error(f"Failed to create threat indicator: {e}")

    async def _add_timeline_event(self, investigation: SecurityInvestigation, event_data: Dict[str, Any]):
        """Add an event to the investigation timeline"""
        try:
            event_id = str(uuid.uuid4())

            timeline_event = InvestigationTimeline(
                event_id=event_id,
                investigation_id=investigation.investigation_id,
                timestamp=datetime.fromisoformat(event_data.get("timestamp", datetime.utcnow().isoformat())),
                event_type=event_data["event_type"],
                description=event_data["description"],
                source_system=event_data["source_system"],
                entities_involved=event_data.get("entities_involved", []),
                evidence_references=event_data.get("evidence_references", []),
                confidence=event_data.get("confidence", AnalysisConfidence.MEDIUM),
                metadata=event_data.get("metadata", {})
            )

            investigation.timeline.append(timeline_event)
            # Sort timeline by timestamp
            investigation.timeline.sort(key=lambda x: x.timestamp)

        except Exception as e:
            self.logger.error(f"Failed to add timeline event: {e}")

    async def _generate_template_hypothesis(self, investigation: SecurityInvestigation, template_name: str):
        """Generate initial hypothesis from hunt template"""
        try:
            template = self.hunt_templates[template_name]

            hypothesis_data = {
                "description": f"Investigating {template['description'].lower()}",
                "created_by": "investigation_engine",
                "mitre_techniques": template.get("mitre_techniques", []),
                "confidence_score": 0.5
            }

            await self.add_hypothesis(investigation.investigation_id, hypothesis_data)

        except Exception as e:
            self.logger.error(f"Failed to generate template hypothesis: {e}")

    async def _test_hypothesis(self, investigation: SecurityInvestigation, hypothesis: InvestigationHypothesis):
        """Test an investigation hypothesis"""
        try:
            test_result = {
                "test_id": str(uuid.uuid4()),
                "tested_at": datetime.utcnow().isoformat(),
                "test_type": "automated",
                "evidence_analyzed": len(investigation.evidence),
                "supporting_count": 0,
                "contradicting_count": 0,
                "confidence_adjustment": 0.0
            }

            # Simple hypothesis testing based on evidence
            for evidence in investigation.evidence:
                for analysis in evidence.analysis_results:
                    for finding in analysis.get("findings", []):
                        if any(tech in hypothesis.mitre_techniques for tech in finding.get("mitre_techniques", [])):
                            test_result["supporting_count"] += 1
                            hypothesis.supporting_evidence.append(evidence.evidence_id)

            # Adjust confidence based on supporting evidence
            if test_result["supporting_count"] > 0:
                confidence_boost = min(0.3, test_result["supporting_count"] * 0.1)
                hypothesis.confidence_score = min(1.0, hypothesis.confidence_score + confidence_boost)
                test_result["confidence_adjustment"] = confidence_boost

            hypothesis.test_results.append(test_result)

            # Update hypothesis status
            if hypothesis.confidence_score > 0.8:
                hypothesis.status = "confirmed"
            elif hypothesis.confidence_score < 0.3:
                hypothesis.status = "rejected"
            else:
                hypothesis.status = "testing"

        except Exception as e:
            self.logger.error(f"Failed to test hypothesis: {e}")

    async def _handle_create_investigation(self, message: Dict[str, Any]):
        """Handle investigation creation requests"""
        try:
            investigation_data = message.get('investigation_data', {})
            investigation = await self.create_investigation(investigation_data)

            if 'reply_to' in message:
                await self.queue_manager.publish(message['reply_to'], {
                    "success": True,
                    "investigation_id": investigation.investigation_id,
                    "investigation_data": investigation.to_dict()
                })

        except Exception as e:
            self.logger.error(f"Error handling investigation creation: {e}")
            if 'reply_to' in message:
                await self.queue_manager.publish(message['reply_to'], {
                    "success": False,
                    "error": str(e)
                })

    async def _handle_add_evidence(self, message: Dict[str, Any]):
        """Handle evidence addition requests"""
        try:
            investigation_id = message.get('investigation_id')
            evidence_data = message.get('evidence_data', {})

            evidence = await self.add_evidence(investigation_id, evidence_data)

            if 'reply_to' in message:
                await self.queue_manager.publish(message['reply_to'], {
                    "success": evidence is not None,
                    "evidence_data": evidence.to_dict() if evidence else None
                })

        except Exception as e:
            self.logger.error(f"Error handling evidence addition: {e}")

    async def _handle_analyze_request(self, message: Dict[str, Any]):
        """Handle analysis requests"""
        try:
            investigation_id = message.get('investigation_id')
            analysis_type = message.get('analysis_type', 'full')

            if investigation_id not in self.investigations:
                return

            investigation = self.investigations[investigation_id]

            if analysis_type == 'evidence':
                # Re-analyze all evidence
                for evidence in investigation.evidence:
                    await self._analyze_evidence(investigation, evidence)
            elif analysis_type == 'patterns':
                # Run pattern analysis
                await self._run_pattern_analysis(investigation)

        except Exception as e:
            self.logger.error(f"Error handling analysis request: {e}")

    async def _handle_threat_hunt(self, message: Dict[str, Any]):
        """Handle threat hunt requests"""
        try:
            investigation_id = message.get('investigation_id')
            hunt_params = message.get('hunt_params', {})

            hunt_results = await self.conduct_threat_hunt(investigation_id, hunt_params)

            if 'reply_to' in message:
                await self.queue_manager.publish(message['reply_to'], hunt_results)

        except Exception as e:
            self.logger.error(f"Error handling threat hunt: {e}")

    async def _handle_new_alert(self, message: Dict[str, Any]):
        """Handle new detection alerts for potential investigation"""
        try:
            alert_data = message.get('alert_data', {})

            # Check if alert warrants investigation
            if alert_data.get('severity') in ['high', 'critical']:
                # Auto-create investigation for high-severity alerts
                investigation_data = {
                    'title': f"Investigation: {alert_data.get('rule_name', 'Unknown Alert')}",
                    'description': f"Automated investigation created for alert: {alert_data.get('description', 'N/A')}",
                    'investigation_type': InvestigationType.INCIDENT_RESPONSE.value,
                    'lead_investigator': 'auto_investigator',
                    'priority': 'high' if alert_data.get('severity') == 'high' else 'critical',
                    'target_systems': alert_data.get('affected_systems', []),
                    'timeframe_start': (datetime.utcnow() - timedelta(hours=24)).isoformat(),
                    'timeframe_end': datetime.utcnow().isoformat(),
                    'metadata': {'source_alert': alert_data}
                }

                investigation = await self.create_investigation(investigation_data)
                self.logger.info(f"Auto-created investigation {investigation.investigation_id} from alert")

        except Exception as e:
            self.logger.error(f"Error handling new alert: {e}")

    async def _continuous_analysis(self):
        """Continuous analysis of ongoing investigations"""
        while True:
            try:
                for investigation_id, investigation in self.investigations.items():
                    if investigation.status in [InvestigationStatus.DATA_COLLECTION, InvestigationStatus.ANALYSIS]:
                        # Run periodic analysis
                        await self._run_pattern_analysis(investigation)

                # Sleep for 30 minutes before next analysis cycle
                await asyncio.sleep(1800)

            except Exception as e:
                self.logger.error(f"Error in continuous analysis: {e}")
                await asyncio.sleep(300)

    async def _threat_intel_enrichment(self):
        """Periodic threat intelligence enrichment"""
        while True:
            try:
                for investigation_id, investigation in self.investigations.items():
                    # Enrich indicators with latest threat intel
                    for indicator in investigation.indicators:
                        if indicator.indicator_type == "hash":
                            threat_intel = await self._lookup_threat_intel(indicator.indicator_value)
                            if threat_intel:
                                indicator.context.update(threat_intel)

                # Sleep for 1 hour before next enrichment
                await asyncio.sleep(3600)

            except Exception as e:
                self.logger.error(f"Error in threat intel enrichment: {e}")
                await asyncio.sleep(600)

    async def _pattern_discovery(self):
        """Pattern discovery across investigations"""
        while True:
            try:
                # Analyze patterns across all investigations
                all_indicators = []
                for investigation in self.investigations.values():
                    all_indicators.extend(investigation.indicators)

                # Find common patterns
                indicator_patterns = {}
                for indicator in all_indicators:
                    key = f"{indicator.indicator_type}:{indicator.threat_level}"
                    if key not in indicator_patterns:
                        indicator_patterns[key] = []
                    indicator_patterns[key].append(indicator)

                # Identify significant patterns
                for pattern, indicators in indicator_patterns.items():
                    if len(indicators) > 5:  # Pattern appears in multiple investigations
                        self.logger.info(f"Discovered pattern: {pattern} with {len(indicators)} instances")

                # Sleep for 2 hours before next pattern discovery
                await asyncio.sleep(7200)

            except Exception as e:
                self.logger.error(f"Error in pattern discovery: {e}")
                await asyncio.sleep(1800)

    async def _run_pattern_analysis(self, investigation: SecurityInvestigation):
        """Run pattern analysis on an investigation"""
        try:
            # Analyze collected evidence for patterns
            patterns_found = []

            # Check for known attack patterns
            for pattern_name, pattern_config in self.analysis_patterns.items():
                pattern_match = await self._check_pattern_match(investigation, pattern_config)
                if pattern_match:
                    patterns_found.append({
                        "pattern_name": pattern_name,
                        "confidence": pattern_match["confidence"],
                        "evidence": pattern_match["evidence"],
                        "techniques": pattern_match.get("techniques", [])
                    })

            if patterns_found:
                investigation.pattern_matches.extend(patterns_found)
                investigation.updated_at = datetime.utcnow()

                # Broadcast pattern discovery
                await self.queue_manager.publish("investigation.patterns_found", {
                    "investigation_id": investigation.investigation_id,
                    "patterns": patterns_found
                })

        except Exception as e:
            self.logger.error(f"Error in pattern analysis: {e}")

    async def _check_pattern_match(self, investigation: SecurityInvestigation, pattern_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Check if investigation matches a specific pattern"""
        try:
            indicators = pattern_config.get("indicators", [])
            matched_indicators = []

            # Check evidence for pattern indicators
            for evidence in investigation.evidence:
                for analysis in evidence.analysis_results:
                    for finding in analysis.get("findings", []):
                        if finding.get("type") in indicators:
                            matched_indicators.append(finding)

            # Calculate confidence based on matched indicators
            if matched_indicators:
                confidence = min(1.0, len(matched_indicators) / len(indicators))
                if confidence > 0.5:  # Threshold for pattern match
                    return {
                        "confidence": confidence,
                        "evidence": [e.evidence_id for e in investigation.evidence],
                        "matched_indicators": matched_indicators,
                        "techniques": pattern_config.get("mitre_techniques", [])
                    }

            return None

        except Exception as e:
            self.logger.error(f"Error checking pattern match: {e}")
            return None

    async def get_investigation(self, investigation_id: str) -> Optional[SecurityInvestigation]:
        """Get an investigation by ID"""
        return self.investigations.get(investigation_id)

    async def list_investigations(self, filters: Dict[str, Any] = None) -> List[SecurityInvestigation]:
        """List investigations with optional filters"""
        investigations = list(self.investigations.values())

        if filters:
            if 'status' in filters:
                investigations = [i for i in investigations if i.status.value == filters['status']]
            if 'investigation_type' in filters:
                investigations = [i for i in investigations if i.investigation_type.value == filters['investigation_type']]
            if 'lead_investigator' in filters:
                investigations = [i for i in investigations if i.lead_investigator == filters['lead_investigator']]

        # Sort by priority and creation date
        priority_order = {'critical': 0, 'high': 1, 'medium': 2, 'low': 3}
        investigations.sort(key=lambda i: (priority_order.get(i.priority, 4), i.created_at), reverse=True)

        return investigations

    async def get_investigation_metrics(self) -> Dict[str, Any]:
        """Get investigation metrics and analytics"""
        total_investigations = len(self.investigations)
        if total_investigations == 0:
            return {"total_investigations": 0}

        # Status distribution
        status_dist = {}
        for investigation in self.investigations.values():
            status = investigation.status.value
            status_dist[status] = status_dist.get(status, 0) + 1

        # Type distribution
        type_dist = {}
        for investigation in self.investigations.values():
            inv_type = investigation.investigation_type.value
            type_dist[inv_type] = type_dist.get(inv_type, 0) + 1

        # Evidence metrics
        total_evidence = sum(len(inv.evidence) for inv in self.investigations.values())
        total_indicators = sum(len(inv.indicators) for inv in self.investigations.values())
        total_hypotheses = sum(len(inv.hypotheses) for inv in self.investigations.values())

        # Active investigations
        active_investigations = sum(1 for inv in self.investigations.values()
                                 if inv.status not in [InvestigationStatus.COMPLETED, InvestigationStatus.SUSPENDED])

        return {
            "total_investigations": total_investigations,
            "active_investigations": active_investigations,
            "status_distribution": status_dist,
            "type_distribution": type_dist,
            "total_evidence": total_evidence,
            "total_indicators": total_indicators,
            "total_hypotheses": total_hypotheses,
            "avg_evidence_per_investigation": round(total_evidence / total_investigations, 2),
            "avg_indicators_per_investigation": round(total_indicators / total_investigations, 2)
        }

async def main():
    """Main entry point for Investigation Engine"""
    engine = InvestigationEngine()

    if await engine.initialize():
        print("Investigation Engine v2.0 started successfully")
        try:
            await asyncio.sleep(float('inf'))
        except KeyboardInterrupt:
            print("Shutting down Investigation Engine...")
    else:
        print("Failed to start Investigation Engine")
        return 1

    return 0

if __name__ == "__main__":
    asyncio.run(main())