# Graph Builder Engine Dockerfile
FROM siemless-v2-base:latest

# Copy Graph Builder Engine specific code
COPY engines/graph_builder/ /app/engines/graph_builder/



# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD redis-cli -h $REDIS_HOST ping || exit 1

# Set the command to run Graph Builder Engine
CMD ["python", "engines/graph_builder/graph_builder.py"]
