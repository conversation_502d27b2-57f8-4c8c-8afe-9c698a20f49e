"""
Message Handlers Module
Handles channel-specific message processing for Intelligence Engine
"""

import json
import uuid
from typing import Dict, Any, Callable, List
import logging

class MessageHandlers:
    """Handles different types of messages for the Intelligence Engine"""

    def __init__(self, ai_model_manager, pattern_manager, consensus_engine,
                 cost_tracker, publish_callback: Callable, logger: logging.Logger = None):
        self.ai_model_manager = ai_model_manager
        self.pattern_manager = pattern_manager
        self.consensus_engine = consensus_engine
        self.cost_tracker = cost_tracker
        self.publish_callback = publish_callback
        self.logger = logger or logging.getLogger(__name__)

        # Message handler mapping
        self.handlers = {
            'intelligence.consensus': self.handle_consensus_request,
            'intelligence.crystallize': self.handle_crystallization_request,
            'intelligence.validate': self.handle_validation_request,
            'ingestion.unknown_patterns': self.handle_unknown_pattern,
            'contextualization.new_entities': self.handle_new_entity_pattern,
            'intelligence.parse_log_sample': self.handle_parse_log_sample,
            'intelligence.analyze_telemetry': self.handle_analyze_telemetry,  # Telemetry mode detection
            'intelligence.extract_entities_ai': self.handle_extract_entities_ai,  # AI entity extraction
            'intelligence.generate_log_mapping': self.handle_generate_log_mapping  # NEW: Schema mapping generation
        }

    async def process_message(self, channel: str, data: Dict[str, Any]):
        """Route message to appropriate handler"""
        if channel in self.handlers:
            await self.handlers[channel](data)
        else:
            self.logger.warning(f"No handler for channel: {channel}")

    async def handle_consensus_request(self, data: Dict[str, Any]):
        """Handle AI consensus validation request"""
        try:
            pattern_data = data.get('pattern_data')
            task_complexity = data.get('complexity', 'medium')

            # Select appropriate AI models based on complexity
            models = self.ai_model_manager.select_models_for_task(task_complexity)

            # Get consensus from multiple models
            consensus_result = await self.consensus_engine.get_ai_consensus(pattern_data, models)

            # Store result and publish response
            result_id = str(uuid.uuid4())
            self.consensus_engine.store_consensus_result(result_id, consensus_result)

            self.publish_callback('intelligence.consensus_result', {
                'result_id': result_id,
                'consensus': consensus_result,
                'source_request': data.get('request_id')
            })

            self.logger.info(f"Consensus request processed: {result_id}")

        except Exception as e:
            self.logger.error(f"Consensus request error: {e}")

    async def handle_crystallization_request(self, data: Dict[str, Any]):
        """Handle pattern crystallization request"""
        try:
            pattern_id = data.get('pattern_id')
            ai_insights = data.get('ai_insights')

            # Crystallize AI insights into deterministic pattern
            crystallized_pattern = await self.pattern_manager.crystallize_pattern(ai_insights)

            # Store crystallized pattern
            self.pattern_manager.store_crystallized_pattern(pattern_id, crystallized_pattern)

            # Update cost metrics
            self.cost_tracker.track_crystallization()

            # Calculate cost savings
            cost_savings = self.pattern_manager.calculate_cost_savings(crystallized_pattern)

            self.publish_callback('intelligence.pattern_crystallized', {
                'pattern_id': pattern_id,
                'crystallized_pattern': crystallized_pattern,
                'cost_savings': cost_savings
            })

            self.logger.info(f"Pattern crystallized: {pattern_id}")

        except Exception as e:
            self.logger.error(f"Crystallization error: {e}")

    async def handle_validation_request(self, data: Dict[str, Any]):
        """Handle pattern validation request"""
        try:
            pattern = data.get('pattern')
            validation_type = data.get('type', 'general')

            # Validate pattern using appropriate model
            validation_result = await self.pattern_manager.validate_pattern(pattern, validation_type)

            self.publish_callback('intelligence.pattern_validated', {
                'pattern_id': data.get('pattern_id'),
                'validation_result': validation_result,
                'confidence': validation_result.get('confidence', 0)
            })

            self.logger.info(f"Pattern validated: {data.get('pattern_id')}")

        except Exception as e:
            self.logger.error(f"Validation error: {e}")

    async def handle_unknown_pattern(self, data: Dict[str, Any]):
        """Handle unknown pattern from ingestion"""
        try:
            log_sample = data.get('log_sample')
            source_type = data.get('source_type')

            # Analyze unknown pattern using AI
            analysis_result = await self.pattern_manager.analyze_unknown_pattern(log_sample, source_type)

            confidence_threshold = 0.70  # Should be configurable
            if analysis_result['confidence'] > confidence_threshold:
                # Request crystallization
                self.publish_callback('intelligence.crystallize', {
                    'pattern_id': str(uuid.uuid4()),
                    'ai_insights': analysis_result,
                    'source_data': data
                })

                self.logger.info(f"Unknown pattern analysis triggered crystallization: {analysis_result['confidence']}")
            else:
                self.logger.info(f"Unknown pattern confidence too low for crystallization: {analysis_result['confidence']}")

        except Exception as e:
            self.logger.error(f"Unknown pattern error: {e}")

    async def handle_new_entity_pattern(self, data: Dict[str, Any]):
        """Handle new entity pattern from contextualization"""
        try:
            entity_pattern = data.get('entity_pattern')
            confidence = data.get('confidence', 0)

            confidence_threshold = 0.70  # Should be configurable
            if confidence > confidence_threshold:
                # Add to pattern library for future use
                pattern_id = self.pattern_manager.generate_pattern_id(entity_pattern)
                self.pattern_manager.store_entity_pattern(pattern_id, entity_pattern)

                self.logger.info(f"Stored new entity pattern: {pattern_id}")

                # Notify other engines of new pattern
                self.publish_callback('intelligence.new_entity_pattern_stored', {
                    'pattern_id': pattern_id,
                    'entity_pattern': entity_pattern,
                    'confidence': confidence
                })
            else:
                self.logger.debug(f"Entity pattern confidence too low: {confidence}")

        except Exception as e:
            self.logger.error(f"Entity pattern error: {e}")

    async def handle_health_check(self, data: Dict[str, Any]):
        """Handle health check requests"""
        try:
            # Gather health information from all modules
            health_status = {
                'status': 'healthy',
                'timestamp': data.get('timestamp'),
                'modules': {
                    'ai_models': self._check_ai_models_health(),
                    'pattern_manager': self._check_pattern_manager_health(),
                    'consensus_engine': self._check_consensus_engine_health(),
                    'cost_tracker': self._check_cost_tracker_health()
                },
                'metrics': self.cost_tracker.get_cost_metrics()
            }

            self.publish_callback('intelligence.health_response', health_status)

        except Exception as e:
            self.logger.error(f"Health check error: {e}")
            self.publish_callback('intelligence.health_response', {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': data.get('timestamp')
            })

    def _check_ai_models_health(self) -> Dict[str, Any]:
        """Check AI models health"""
        return {
            'status': 'healthy',
            'available_models': len(self.ai_model_manager.ai_models),
            'model_tiers': list(self.ai_model_manager.ai_models.keys())
        }

    def _check_pattern_manager_health(self) -> Dict[str, Any]:
        """Check pattern manager health"""
        return {
            'status': 'healthy',
            'cached_patterns': len(self.pattern_manager.pattern_library),
            'confidence_threshold': self.pattern_manager.confidence_threshold
        }

    def _check_consensus_engine_health(self) -> Dict[str, Any]:
        """Check consensus engine health"""
        return {
            'status': 'healthy',
            'consensus_threshold': self.consensus_engine.consensus_threshold
        }

    def _check_cost_tracker_health(self) -> Dict[str, Any]:
        """Check cost tracker health"""
        metrics = self.cost_tracker.get_cost_metrics()
        return {
            'status': 'healthy',
            'total_requests': metrics['total_requests'],
            'total_cost': metrics['total_cost']
        }

    async def handle_pattern_search(self, data: Dict[str, Any]):
        """Handle pattern search requests"""
        try:
            search_criteria = data.get('criteria', {})
            pattern_type = search_criteria.get('type')
            limit = search_criteria.get('limit', 100)

            # Search patterns
            patterns = self.pattern_manager.list_patterns(pattern_type)

            # Limit results if specified
            if limit and len(patterns) > limit:
                patterns = patterns[:limit]

            self.publish_callback('intelligence.pattern_search_results', {
                'request_id': data.get('request_id'),
                'patterns': patterns,
                'total_found': len(patterns),
                'search_criteria': search_criteria
            })

            self.logger.info(f"Pattern search completed: {len(patterns)} results")

        except Exception as e:
            self.logger.error(f"Pattern search error: {e}")

    async def handle_cost_report_request(self, data: Dict[str, Any]):
        """Handle cost report requests"""
        try:
            report_type = data.get('report_type', 'efficiency')

            if report_type == 'efficiency':
                report = self.cost_tracker.get_cost_efficiency_report()
            elif report_type == 'daily':
                days = data.get('days', 7)
                report = self.cost_tracker.get_daily_costs(days)
            elif report_type == 'models':
                report = self.cost_tracker.get_model_costs()
            else:
                report = self.cost_tracker.export_cost_data()

            self.publish_callback('intelligence.cost_report', {
                'request_id': data.get('request_id'),
                'report_type': report_type,
                'report_data': report
            })

            self.logger.info(f"Cost report generated: {report_type}")

        except Exception as e:
            self.logger.error(f"Cost report error: {e}")

    def add_handler(self, channel: str, handler: Callable):
        """Add a custom message handler"""
        self.handlers[channel] = handler
        self.logger.info(f"Added handler for channel: {channel}")

    def remove_handler(self, channel: str):
        """Remove a message handler"""
        if channel in self.handlers:
            del self.handlers[channel]
            self.logger.info(f"Removed handler for channel: {channel}")

    def get_supported_channels(self) -> list:
        """Get list of supported channels"""
        return list(self.handlers.keys())

    async def handle_parse_log_sample(self, data: Dict[str, Any]):
        """
        Handle parser generation request from Ingestion Engine
        Analyzes log samples and generates parsing logic
        """
        try:
            # Extract actual data from message envelope
            message_data = data.get('data', data)

            parser_id = message_data.get('parser_id')
            log_samples = message_data.get('log_samples', [])
            log_source = message_data.get('log_source', 'unknown')
            vendor = message_data.get('vendor', 'unknown')
            target_siem = message_data.get('target_siem', 'generic')
            response_channel = message_data.get('response_channel')

            if not log_samples or not response_channel:
                self.logger.error(f"Missing log_samples or response_channel in parse request")
                self.logger.error(f"Data received: {data}")
                return

            self.logger.info(f"Parser generation request for {log_source} ({vendor}) -> {target_siem}")
            self.logger.info(f"Analyzing {len(log_samples)} log samples")

            # Build AI prompt for log parsing
            prompt = self._build_parser_generation_prompt(
                log_samples, log_source, vendor, target_siem
            )

            # Use Gemma-3-27b FREE tier for parser generation
            # Cost: $0.00 per parser (FREE!), creates reusable asset
            model_tier = 'free'  # gemma-3-27b-it (FREE)
            self.logger.info(f"Using {model_tier} model (FREE) for parser generation")

            # Call AI model to generate parser
            ai_response = await self.ai_model_manager.call_ai_model(
                model_tier,
                {'prompt': prompt}
            )

            # Extract parser logic from AI response
            parser_logic = self._extract_parser_from_response(ai_response['result'])

            # Get model info for response
            model_info = self.ai_model_manager.get_model_info(model_tier)

            # Build response
            response_data = {
                'parser_id': parser_id,
                'parser': parser_logic,
                'confidence': ai_response.get('confidence', 0.85),
                'model_used': model_info['name'],
                'cost': model_info['cost_per_request'],
                'field_mappings': parser_logic.get('field_mappings', {}),
                'entity_types': parser_logic.get('entity_types', [])
            }

            # Publish response to specified channel
            self.publish_callback(response_channel, response_data)

            self.logger.info(f"Parser generated successfully: {parser_id}")
            self.logger.info(f"Extracted {len(parser_logic.get('field_mappings', {}))} fields, "
                           f"{len(parser_logic.get('entity_types', []))} entity types")

        except Exception as e:
            self.logger.error(f"Parser generation error: {e}")
            # Send error response if possible
            if response_channel:
                self.publish_callback(response_channel, {
                    'parser_id': data.get('parser_id'),
                    'error': str(e),
                    'status': 'failed'
                })

    def _build_parser_generation_prompt(self, log_samples: list, log_source: str,
                                       vendor: str, target_siem: str) -> str:
        """Build AI prompt for parser generation"""
        samples_text = "\n".join([f"{i+1}. {sample}" for i, sample in enumerate(log_samples[:5])])

        return f"""You are a log parsing expert. Analyze these {vendor} {log_source} log samples and generate a parser.

LOG SAMPLES:
{samples_text}

TASK:
1. Identify the log format (JSON, key-value, syslog, CEF, etc.)
2. Extract all field names and their data types
3. Identify security-relevant entities (IPs, users, hosts, processes, files, etc.)
4. Create field mappings for {target_siem} SIEM
5. Generate regex/grok patterns for parsing

RESPONSE FORMAT (JSON):
{{
    "format_type": "json|key-value|syslog|cef|custom",
    "field_mappings": {{
        "source_field": "target_field",
        "timestamp": "event.timestamp",
        "source_ip": "source.ip",
        ...
    }},
    "entity_types": ["ip", "user", "host", "process", "file", ...],
    "regex_patterns": {{
        "timestamp": "regex_here",
        "ip_address": "regex_here",
        ...
    }},
    "grok_pattern": "grok_pattern_if_needed",
    "sample_extraction": {{
        "log1": {{"field": "value", ...}},
        ...
    }},
    "notes": "any parsing challenges or recommendations"
}}

Provide ONLY valid JSON in your response."""

    def _extract_parser_from_response(self, ai_response: str) -> Dict[str, Any]:
        """Extract parser logic from AI response"""
        try:
            # Try to parse as JSON
            # Remove markdown code blocks if present
            response = ai_response.strip()
            if response.startswith('```'):
                # Extract JSON from code block
                lines = response.split('\n')
                json_lines = []
                in_json = False
                for line in lines:
                    if line.startswith('```'):
                        in_json = not in_json
                        continue
                    if in_json:
                        json_lines.append(line)
                response = '\n'.join(json_lines)

            parser_logic = json.loads(response)

            # Validate required fields
            required_fields = ['format_type', 'field_mappings', 'entity_types']
            for field in required_fields:
                if field not in parser_logic:
                    self.logger.warning(f"Missing {field} in parser logic, adding default")
                    if field == 'format_type':
                        parser_logic['format_type'] = 'custom'
                    elif field == 'field_mappings':
                        parser_logic['field_mappings'] = {}
                    elif field == 'entity_types':
                        parser_logic['entity_types'] = []

            return parser_logic

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse AI response as JSON: {e}")
            # Return basic parser structure
            return {
                'format_type': 'unknown',
                'field_mappings': {},
                'entity_types': [],
                'error': f'Failed to parse AI response: {str(e)}',
                'raw_response': ai_response[:500]  # First 500 chars for debugging
            }

    # ============================================================
    # TELEMETRY MODE DETECTION HANDLER
    # ============================================================

    async def handle_analyze_telemetry(self, data: Dict[str, Any]):
        """
        Analyze log samples to determine if source is in alert-only or full telemetry mode

        This handler receives requests from Backend Engine's log source quality system
        and uses AI (Gemma-3 FREE) to intelligently detect telemetry mode.

        WHY THIS MATTERS:
        - Alert-only sources can drop from PLATINUM (98 points) to BRONZE (48 points)
        - Detection confidence drops by 50-60%
        - Can't write custom SIEM rules
        - Can't baseline normal behavior
        - This helps users identify misconfigured sources BEFORE they affect detection

        WORKFLOW:
        1. Backend detects uncertain telemetry mode (deterministic check fails)
        2. Backend publishes to 'intelligence.analyze_telemetry'
        3. This handler receives the request
        4. AI (Gemma-3 FREE) analyzes log samples
        5. Responds with mode, confidence, reasoning, and recommendations
        6. Backend crystallizes the pattern for future FREE use

        Args:
            data: {
                'request_id': str,
                'source_name': str,
                'sample_logs': List[Dict],
                'response_channel': str
            }
        """

        try:
            # Extract data from message envelope
            message_data = data.get('data', data)

            request_id = message_data.get('request_id')
            source_name = message_data.get('source_name')
            sample_logs = message_data.get('sample_logs', [])
            response_channel = message_data.get('response_channel')

            if not sample_logs or not response_channel:
                self.logger.error(f"Missing sample_logs or response_channel in telemetry request")
                return

            self.logger.info(f"Telemetry analysis request for {source_name} ({len(sample_logs)} samples)")

            # Build AI prompt for telemetry mode detection
            prompt = self._build_telemetry_detection_prompt(source_name, sample_logs)

            # Use Gemma-3 (FREE tier) for this analysis
            # This is perfect for Gemma-3: pattern recognition, no complex reasoning needed
            model_tier = 'free'  # gemma-3-27b-it (FREE)
            self.logger.info(f"Using {model_tier} model (FREE) for telemetry detection")

            # Call AI model
            ai_response = await self.ai_model_manager.call_ai_model(
                model_tier,
                {'prompt': prompt}
            )

            # Extract telemetry analysis from AI response
            telemetry_analysis = self._extract_telemetry_analysis(ai_response['result'])

            # Add metadata
            telemetry_analysis['source'] = 'ai'
            telemetry_analysis['model_used'] = 'gemma-3-27b'
            telemetry_analysis['cost'] = 0.0
            telemetry_analysis['request_id'] = request_id

            # Publish response
            self.publish_callback(response_channel, telemetry_analysis)

            self.logger.info(
                f"Telemetry analysis complete: {source_name} = {telemetry_analysis.get('mode')} "
                f"(confidence: {telemetry_analysis.get('confidence', 0):.2f})"
            )

        except Exception as e:
            self.logger.error(f"Telemetry analysis error: {e}")
            # Send error response if possible
            if response_channel:
                self.publish_callback(response_channel, {
                    'request_id': request_id,
                    'mode': 'unknown',
                    'confidence': 0.0,
                    'reasoning': f'AI analysis failed: {str(e)}',
                    'error': str(e),
                    'status': 'failed'
                })

    def _build_telemetry_detection_prompt(self, source_name: str, sample_logs: List[Dict]) -> str:
        """
        Build AI prompt for telemetry mode detection

        The prompt is designed to make it easy for Gemma-3 (FREE model) to detect:
        - Alert-only mode: Pre-filtered alerts/detections only
        - Full telemetry: Raw events with rich metadata
        - Partial mode: Some event types enabled but missing others
        """

        # Convert logs to readable format
        samples_text = "\n".join([
            f"{i+1}. {json.dumps(log, indent=2)}"
            for i, log in enumerate(sample_logs[:5])  # First 5 samples
        ])

        return f"""You are a security telemetry expert. Analyze these {source_name} log samples and determine the telemetry mode.

LOG SAMPLES:
{samples_text}

TELEMETRY MODES:

1. ALERT-ONLY MODE:
   - Contains only pre-filtered alerts/detections
   - Keywords: "alert", "detection", "incident", "severity", "threat"
   - Few fields (~5-15): alert_id, severity, description, timestamp
   - Missing: CommandLine, ParentProcess, network metadata, file operations
   - Few event types (2-5): Detection, Alert, Incident

2. FULL TELEMETRY MODE:
   - Contains raw events with rich metadata
   - Many fields (30-70): CommandLine, ParentProcess, SHA256, DestinationIP, etc.
   - Many event types (15-30): ProcessRollup2, NetworkConnect, FileWrite, DnsRequest
   - Continuous stream of normal + suspicious events

3. PARTIAL MODE:
   - Some event types enabled, others missing
   - Medium field count (15-30)
   - Mixed: some alerts + some raw events

TASK:
Determine which mode these logs represent and explain why.

RESPONSE FORMAT (JSON only):
{{
  "mode": "alert_only|full_telemetry|partial",
  "confidence": 0.0-1.0,
  "reasoning": "Brief explanation of why you chose this mode",
  "indicators": ["list", "of", "key", "indicators"],
  "missing_for_full_telemetry": ["what's", "missing", "if", "not", "full"],
  "recommendation": "How to enable full telemetry (if needed)"
}}

Respond with ONLY the JSON, no other text."""

    def _extract_telemetry_analysis(self, ai_response: str) -> Dict[str, Any]:
        """
        Extract telemetry analysis from AI response

        AI should return JSON like:
        {
          "mode": "alert_only",
          "confidence": 0.95,
          "reasoning": "All logs contain only Detection/Alert fields...",
          "indicators": ["high severity keywords", "few fields", "no raw process data"],
          "missing_for_full_telemetry": ["ProcessRollup2", "NetworkConnect", "FileWrite"],
          "recommendation": "Enable Data Replicator → Stream All Events"
        }
        """

        try:
            # Try to parse as JSON
            analysis = json.loads(ai_response)

            # Validate required fields
            if 'mode' not in analysis:
                analysis['mode'] = 'unknown'
            if 'confidence' not in analysis:
                analysis['confidence'] = 0.5
            if 'reasoning' not in analysis:
                analysis['reasoning'] = 'AI analysis provided no reasoning'

            # Ensure confidence is float
            analysis['confidence'] = float(analysis['confidence'])

            return analysis

        except json.JSONDecodeError:
            # AI didn't return valid JSON - try to extract from text
            self.logger.warning("AI response not valid JSON, attempting text extraction")

            # Simple pattern matching
            mode = 'unknown'
            confidence = 0.5
            reasoning = ai_response[:200]  # First 200 chars

            if 'alert' in ai_response.lower() and 'only' in ai_response.lower():
                mode = 'alert_only'
                confidence = 0.7
            elif 'full' in ai_response.lower() and 'telemetry' in ai_response.lower():
                mode = 'full_telemetry'
                confidence = 0.7
            elif 'partial' in ai_response.lower():
                mode = 'partial'
                confidence = 0.6

            return {
                'mode': mode,
                'confidence': confidence,
                'reasoning': reasoning,
                'indicators': [],
                'missing_for_full_telemetry': [],
                'recommendation': 'Enable full telemetry mode in source configuration',
                'note': 'Extracted from non-JSON AI response'
            }

    async def handle_extract_entities_ai(self, data: Dict[str, Any]):
        """
        Handle AI-powered entity extraction request from adaptive extractor

        This is called when adaptive_entity_extractor encounters unknown log formats
        and needs AI to analyze the log structure and extract entities
        """
        try:
            request_id = data.get('request_id')
            vendor = data.get('vendor', 'unknown')
            prompt = data.get('prompt')
            log_sample = data.get('log_sample', {})

            self.logger.info(f"[{request_id}] AI entity extraction requested for vendor: {vendor}")

            # Select appropriate AI model (use low-cost model for entity extraction)
            models = self.ai_model_manager.select_models_for_task('low')

            if not models:
                self.logger.error(f"[{request_id}] No AI models available")
                self.publish_callback(f'intelligence.extraction_response.{request_id}', {
                    'status': 'error',
                    'error': 'No AI models available'
                })
                return

            # Use first available model
            model_name = models[0]

            self.logger.info(f"[{request_id}] Using model: {model_name}")

            # Get AI response
            ai_response = await self.ai_model_manager.call_model(model_name, prompt)

            # Track cost
            await self.cost_tracker.track_ai_call(
                model=model_name,
                task='entity_extraction',
                input_tokens=len(prompt) // 4,  # Rough estimate
                output_tokens=len(ai_response) // 4
            )

            # Parse AI response (expecting JSON)
            try:
                result = json.loads(ai_response)

                # Validate required fields
                if 'entities' not in result:
                    result['entities'] = []
                if 'extraction_rules' not in result:
                    result['extraction_rules'] = {}
                if 'confidence' not in result:
                    result['confidence'] = 0.7

                self.logger.info(
                    f"[{request_id}] AI extracted {len(result['entities'])} entities "
                    f"with {len(result['extraction_rules'])} rules"
                )

                # Publish success response
                self.publish_callback(f'intelligence.extraction_response.{request_id}', {
                    'status': 'success',
                    'entities': result['entities'],
                    'extraction_rules': result['extraction_rules'],
                    'confidence': result['confidence'],
                    'vendor': vendor,
                    'model_used': model_name
                })

            except json.JSONDecodeError as e:
                self.logger.error(f"[{request_id}] Failed to parse AI response as JSON: {e}")
                self.logger.debug(f"[{request_id}] AI response: {ai_response[:500]}")

                # Fallback: extract what we can from text response
                entities = []
                extraction_rules = {}

                # Simple pattern extraction from text
                if 'ip' in ai_response.lower():
                    extraction_rules['ip_address'] = ['source.ip', 'destination.ip']
                if 'hostname' in ai_response.lower() or 'host' in ai_response.lower():
                    extraction_rules['hostname'] = ['host.name', 'hostname']
                if 'user' in ai_response.lower():
                    extraction_rules['username'] = ['user.name', 'username']

                self.publish_callback(f'intelligence.extraction_response.{request_id}', {
                    'status': 'success',
                    'entities': entities,
                    'extraction_rules': extraction_rules,
                    'confidence': 0.5,
                    'vendor': vendor,
                    'model_used': model_name,
                    'note': 'Parsed from non-JSON response'
                })

        except Exception as e:
            self.logger.error(f"[{request_id}] AI entity extraction error: {e}", exc_info=True)
            self.publish_callback(f'intelligence.extraction_response.{request_id}', {
                'status': 'error',
                'error': str(e)
            })

    async def handle_generate_log_mapping(self, data: Dict[str, Any]):
        """
        Handle log schema mapping generation request
        Uses AI consensus (Gemma + Sonnet) to create entity extraction mappings
        Cost: $0.008 per schema (one-time)
        """
        try:
            from mapping_generator import MappingGenerator

            self.logger.info("Generating log mapping with AI consensus")

            # Extract request data
            sample_log = data.get("sample_log")
            request_id = data.get("request_id")
            schema_hash = data.get("schema_hash")
            models = data.get("models", ["free", "mid_quality"])  # Gemma + Sonnet
            response_channel = data.get("response_channel")

            if not sample_log:
                raise Exception("No sample log provided")

            # Initialize mapping generator
            generator = MappingGenerator(
                self.ai_model_manager,
                self.consensus_engine,
                self.logger
            )

            # Generate mapping with AI consensus
            self.logger.info(f"Calling AI models: {models}")
            result = await generator.generate_mapping(sample_log, models)

            # Add metadata
            result["schema_hash"] = schema_hash
            result["generated_at"] = str(uuid.uuid4())

            # Publish result to response channel
            if response_channel:
                self.publish_callback(response_channel, result)
                schema_name = result.get("schema_name", "unknown")
                self.logger.info(f"Mapping generated and published: {schema_name}")
            else:
                self.logger.warning("No response channel specified")

            # Track cost
            cost = 0.008 if "mid_quality" in models or "high_quality" in models else 0.0
            self.cost_tracker.track_request("schema_mapping", cost)

            schema_name_final = result.get("schema_name", "unknown")
            self.logger.info(f"Schema mapping generated: {schema_name_final} (cost: ${cost})")

        except Exception as e:
            self.logger.error(f"Log mapping generation failed: {e}")
            import traceback
            self.logger.error(traceback.format_exc())

            # Publish error if response channel provided
            if data.get("response_channel"):
                self.publish_callback(data["response_channel"], {
                    "error": str(e),
                    "schema_hash": data.get("schema_hash")
                })

