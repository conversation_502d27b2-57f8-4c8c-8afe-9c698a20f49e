"""
MITRE ATT&CK HTTP API Handlers
Provides REST endpoints for MITRE mapping and coverage analysis
"""

import json
from aiohttp import web
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)


class MITREHTTPHandlers:
    """HTTP handlers for MITRE ATT&CK integration"""

    def __init__(self, mitre_mapper, logger=None):
        self.mapper = mitre_mapper
        self.logger = logger or logging.getLogger(__name__)

    def get_routes(self):
        """Return list of routes for this handler"""
        return [
            web.get('/api/v1/mitre/coverage', self.get_coverage),
            web.get('/api/v1/mitre/heatmap', self.get_heatmap),
            web.get('/api/v1/mitre/gaps', self.get_gaps),
            web.get('/api/v1/mitre/overlaps', self.get_overlaps),
            web.get('/api/v1/mitre/technique/{technique_id}', self.get_technique_details),
            web.get('/api/v1/mitre/tactic/{tactic}', self.get_tactic_techniques),
            web.post('/api/v1/mitre/map_rule', self.map_rule),
            web.post('/api/v1/mitre/analyze_sources', self.analyze_log_sources),
            web.post('/api/v1/mitre/update_framework', self.update_framework),
        ]

    async def get_coverage(self, request):
        """
        GET /api/v1/mitre/coverage
        Get overall MITRE ATT&CK coverage analysis
        """
        try:
            analysis = await self.mapper.calculate_coverage()

            return web.json_response({
                'status': 'success',
                'coverage': {
                    'total_techniques': analysis.total_techniques,
                    'covered_techniques': analysis.covered_techniques,
                    'coverage_percentage': round(analysis.coverage_percentage, 2),
                    'gaps_count': len(analysis.gaps),
                    'overlaps_count': len(analysis.overlaps)
                },
                'by_tactic': analysis.by_tactic
            })

        except Exception as e:
            self.logger.error(f"Coverage analysis failed: {e}")
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=500)

    async def get_heatmap(self, request):
        """
        GET /api/v1/mitre/heatmap
        Get coverage heatmap data for visualization
        """
        try:
            rule_mappings = list(self.mapper.rule_mappings.values())
            heatmap = await self.mapper.get_coverage_heatmap(rule_mappings)

            return web.json_response({
                'status': 'success',
                'heatmap': heatmap
            })

        except Exception as e:
            self.logger.error(f"Heatmap generation failed: {e}")
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=500)

    async def get_gaps(self, request):
        """
        GET /api/v1/mitre/gaps
        Get uncovered techniques (detection gaps)
        """
        try:
            analysis = await self.mapper.calculate_coverage()

            # Enrich gaps with technique details
            gap_details = []
            for tech_id in analysis.gaps[:50]:  # Limit to top 50
                technique = await self.mapper.get_technique_details(tech_id)
                if technique:
                    gap_details.append({
                        'technique_id': tech_id,
                        'name': technique.name,
                        'tactics': technique.tactics,
                        'data_sources': technique.data_sources,
                        'platforms': technique.platforms,
                        'url': technique.url
                    })

            return web.json_response({
                'status': 'success',
                'total_gaps': len(analysis.gaps),
                'gaps': gap_details
            })

        except Exception as e:
            self.logger.error(f"Gap analysis failed: {e}")
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=500)

    async def get_overlaps(self, request):
        """
        GET /api/v1/mitre/overlaps
        Get techniques with multiple rule coverage (overlaps)
        """
        try:
            analysis = await self.mapper.calculate_coverage()

            # Enrich overlaps with technique details
            overlap_details = []
            for tech_id, rule_ids in analysis.overlaps.items():
                technique = await self.mapper.get_technique_details(tech_id)
                if technique:
                    overlap_details.append({
                        'technique_id': tech_id,
                        'name': technique.name,
                        'rule_count': len(rule_ids),
                        'rule_ids': rule_ids,
                        'tactics': technique.tactics
                    })

            # Sort by rule count (most overlap first)
            overlap_details.sort(key=lambda x: x['rule_count'], reverse=True)

            return web.json_response({
                'status': 'success',
                'total_overlaps': len(analysis.overlaps),
                'overlaps': overlap_details[:100]  # Top 100
            })

        except Exception as e:
            self.logger.error(f"Overlap analysis failed: {e}")
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=500)

    async def get_technique_details(self, request):
        """
        GET /api/v1/mitre/technique/{technique_id}
        Get detailed information about a specific technique
        """
        technique_id = request.match_info['technique_id']

        try:
            technique = await self.mapper.get_technique_details(technique_id)

            if not technique:
                return web.json_response({
                    'status': 'error',
                    'message': f'Technique {technique_id} not found'
                }, status=404)

            return web.json_response({
                'status': 'success',
                'technique': {
                    'id': technique.technique_id,
                    'name': technique.name,
                    'tactics': technique.tactics,
                    'sub_techniques': technique.sub_techniques,
                    'data_sources': technique.data_sources,
                    'platforms': technique.platforms,
                    'description': technique.description,
                    'url': technique.url,
                    'deprecated': technique.deprecated
                }
            })

        except Exception as e:
            self.logger.error(f"Failed to get technique details: {e}")
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=500)

    async def get_tactic_techniques(self, request):
        """
        GET /api/v1/mitre/tactic/{tactic}
        Get all techniques for a specific tactic
        """
        tactic = request.match_info['tactic']

        try:
            techniques = await self.mapper.get_techniques_by_tactic(tactic)

            technique_list = [
                {
                    'id': tech.technique_id,
                    'name': tech.name,
                    'data_sources': tech.data_sources,
                    'platforms': tech.platforms,
                    'url': tech.url
                }
                for tech in techniques
                if not tech.deprecated
            ]

            return web.json_response({
                'status': 'success',
                'tactic': tactic,
                'technique_count': len(technique_list),
                'techniques': technique_list
            })

        except Exception as e:
            self.logger.error(f"Failed to get tactic techniques: {e}")
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=500)

    async def map_rule(self, request):
        """
        POST /api/v1/mitre/map_rule
        Map a detection rule to MITRE techniques

        Body: {
            "rule_id": "...",
            "name": "...",
            "description": "...",
            "logsource": {...},
            "detection": {...}
        }
        """
        try:
            rule_data = await request.json()

            mapping = await self.mapper.map_rule_to_mitre(rule_data)

            return web.json_response({
                'status': 'success',
                'mapping': {
                    'rule_id': mapping.rule_id,
                    'rule_name': mapping.rule_name,
                    'techniques': mapping.techniques,
                    'tactics': mapping.tactics,
                    'confidence': mapping.confidence,
                    'mapping_method': mapping.mapping_method,
                    'coverage_score': mapping.coverage_score
                }
            })

        except Exception as e:
            self.logger.error(f"Rule mapping failed: {e}")
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=500)

    async def analyze_log_sources(self, request):
        """
        POST /api/v1/mitre/analyze_sources
        Analyze log source overlap and coverage

        Body: {
            "sources": [
                {"name": "CrowdStrike", "type": "edr", "quality_score": 98},
                {"name": "Palo Alto", "type": "firewall", "quality_score": 85}
            ]
        }
        """
        try:
            data = await request.json()
            sources = data.get('sources', [])

            if not sources:
                return web.json_response({
                    'status': 'error',
                    'message': 'No sources provided'
                }, status=400)

            analysis = await self.mapper.analyze_log_source_overlap(sources)

            return web.json_response({
                'status': 'success',
                'analysis': analysis
            })

        except Exception as e:
            self.logger.error(f"Source analysis failed: {e}")
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=500)

    async def update_framework(self, request):
        """
        POST /api/v1/mitre/update_framework
        Update MITRE ATT&CK framework from GitHub

        Body: {
            "force": true/false  (optional)
        }
        """
        try:
            data = await request.json()
            force_update = data.get('force', False)

            result = await self.mapper.fetch_attack_framework(force_update=force_update)

            return web.json_response({
                'status': 'success',
                'result': result
            })

        except Exception as e:
            self.logger.error(f"Framework update failed: {e}")
            return web.json_response({
                'status': 'error',
                'message': str(e)
            }, status=500)
