#!/usr/bin/env python3
"""
SIEM Intelligence Harvester - Bi-directional SOC Knowledge Extraction

This module extends the Ingestion Engine to harvest existing SOC artifacts:
- Detection rules and rulesets from SIEM platforms
- Investigation playbooks and procedures
- Custom queries and dashboards
- Alert configurations and thresholds
- User-defined use cases and correlation rules
- SOC team knowledge and operational procedures

Philosophy: Learn from existing SOC operations to enhance detection capabilities
"""

import asyncio
import json
import yaml
import re
import xml.etree.ElementTree as ET
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import base64
import requests
from pathlib import Path

# Add shared modules to path
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'shared'))

from shared.base import BaseEngine
from shared.logging import get_logger
from shared.security.credential_manager import get_siem_credentials


class SIEMArtifactType(Enum):
    """Types of artifacts we can harvest from SIEMs"""
    DETECTION_RULE = "detection_rule"
    CORRELATION_RULE = "correlation_rule"
    SEARCH_QUERY = "search_query"
    DASHBOARD = "dashboard"
    ALERT_CONFIG = "alert_config"
    PLAYBOOK = "playbook"
    USE_CASE = "use_case"
    WORKFLOW = "workflow"
    CUSTOM_FUNCTION = "custom_function"
    DATA_MODEL = "data_model"
    LOOKUP_TABLE = "lookup_table"
    MACRO = "macro"
    SAVED_SEARCH = "saved_search"
    REPORT = "report"
    FIELD_EXTRACTION = "field_extraction"


class SIEMPlatform(Enum):
    """Supported SIEM platforms for intelligence harvesting"""
    SPLUNK = "splunk"
    ELASTIC = "elastic"
    SENTINEL = "sentinel"
    QRADAR = "qradar"
    CHRONICLE = "chronicle"
    SUMO_LOGIC = "sumo_logic"
    ARCSIGHT = "arcsight"
    LOGRHYTHM = "logrhythm"
    SECURONIX = "securonix"
    RAPID7 = "rapid7"


@dataclass
class SIEMArtifact:
    """Harvested SIEM artifact with metadata"""
    artifact_id: str
    artifact_type: SIEMArtifactType
    platform: SIEMPlatform
    name: str
    description: str
    content: Dict[str, Any]
    metadata: Dict[str, Any]
    created_by: Optional[str] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    version: Optional[str] = None
    tags: List[str] = None
    mitre_techniques: List[str] = None
    confidence: float = 0.8
    complexity: str = "medium"
    effectiveness_score: Optional[float] = None
    usage_frequency: Optional[int] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.mitre_techniques is None:
            self.mitre_techniques = []


class SIEMIntelligenceHarvester:
    """
    Harvests SOC artifacts from SIEM platforms for knowledge enhancement
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.harvested_artifacts = []
        self.platform_connectors = {
            SIEMPlatform.SPLUNK: self._harvest_splunk,
            SIEMPlatform.ELASTIC: self._harvest_elastic,
            SIEMPlatform.SENTINEL: self._harvest_sentinel,
            SIEMPlatform.QRADAR: self._harvest_qradar,
            SIEMPlatform.CHRONICLE: self._harvest_chronicle
        }

    async def harvest_siem_intelligence(self, platform: SIEMPlatform,
                                       artifact_types: List[SIEMArtifactType] = None) -> List[SIEMArtifact]:
        """
        Harvest intelligence from specified SIEM platform

        Args:
            platform: SIEM platform to harvest from
            artifact_types: Specific artifact types to harvest (default: all)

        Returns:
            List of harvested artifacts
        """
        self.logger.info(f"Starting intelligence harvest from {platform.value}")

        if artifact_types is None:
            artifact_types = list(SIEMArtifactType)

        try:
            # Get platform-specific credentials
            credentials = await get_siem_credentials(platform.value)

            # Use platform-specific harvester
            if platform in self.platform_connectors:
                harvester = self.platform_connectors[platform]
                artifacts = await harvester(credentials, artifact_types)
                self.harvested_artifacts.extend(artifacts)

                self.logger.info(f"Harvested {len(artifacts)} artifacts from {platform.value}")
                return artifacts
            else:
                self.logger.warning(f"No harvester available for platform: {platform.value}")
                return []

        except Exception as e:
            self.logger.error(f"Failed to harvest from {platform.value}: {e}")
            return []

    async def _harvest_splunk(self, credentials: Dict[str, Any],
                             artifact_types: List[SIEMArtifactType]) -> List[SIEMArtifact]:
        """Harvest artifacts from Splunk Enterprise/Cloud"""
        artifacts = []

        # Splunk REST API configuration
        base_url = credentials.get('base_url', 'https://localhost:8089')
        username = credentials.get('username')
        password = credentials.get('password')

        # Create session
        session = requests.Session()
        session.auth = (username, password)
        session.verify = False  # For self-signed certs in dev

        try:
            # Harvest Saved Searches (Detection Rules)
            if SIEMArtifactType.DETECTION_RULE in artifact_types:
                saved_searches = await self._harvest_splunk_saved_searches(session, base_url)
                artifacts.extend(saved_searches)

            # Harvest Dashboards
            if SIEMArtifactType.DASHBOARD in artifact_types:
                dashboards = await self._harvest_splunk_dashboards(session, base_url)
                artifacts.extend(dashboards)

            # Harvest Alert Actions
            if SIEMArtifactType.ALERT_CONFIG in artifact_types:
                alerts = await self._harvest_splunk_alerts(session, base_url)
                artifacts.extend(alerts)

            # Harvest Macros
            if SIEMArtifactType.MACRO in artifact_types:
                macros = await self._harvest_splunk_macros(session, base_url)
                artifacts.extend(macros)

            # Harvest Data Models
            if SIEMArtifactType.DATA_MODEL in artifact_types:
                data_models = await self._harvest_splunk_data_models(session, base_url)
                artifacts.extend(data_models)

        except Exception as e:
            self.logger.error(f"Error harvesting from Splunk: {e}")

        return artifacts

    async def _harvest_splunk_saved_searches(self, session: requests.Session,
                                           base_url: str) -> List[SIEMArtifact]:
        """Harvest Splunk saved searches (detection rules)"""
        artifacts = []

        try:
            # Get saved searches
            response = session.get(f"{base_url}/services/saved/searches",
                                 params={'output_mode': 'json', 'count': 1000})

            if response.status_code == 200:
                data = response.json()

                for entry in data.get('entry', []):
                    content = entry.get('content', {})

                    # Extract MITRE techniques from search query
                    search_query = content.get('search', '')
                    mitre_techniques = self._extract_mitre_techniques_from_text(search_query)

                    artifact = SIEMArtifact(
                        artifact_id=f"splunk_savedsearch_{entry['name']}",
                        artifact_type=SIEMArtifactType.DETECTION_RULE,
                        platform=SIEMPlatform.SPLUNK,
                        name=entry['name'],
                        description=content.get('description', 'Splunk saved search'),
                        content={
                            'search_query': search_query,
                            'cron_schedule': content.get('cron_schedule'),
                            'earliest_time': content.get('dispatch.earliest_time'),
                            'latest_time': content.get('dispatch.latest_time'),
                            'alert_conditions': content.get('alert.condition'),
                            'alert_threshold': content.get('alert.threshold'),
                            'alert_comparator': content.get('alert.comparator')
                        },
                        metadata={
                            'app': entry.get('acl', {}).get('app'),
                            'owner': entry.get('acl', {}).get('owner'),
                            'sharing': entry.get('acl', {}).get('sharing'),
                            'updated': entry.get('updated')
                        },
                        mitre_techniques=mitre_techniques,
                        confidence=0.9,  # High confidence for saved searches
                        tags=['splunk', 'saved_search', 'detection']
                    )

                    artifacts.append(artifact)

        except Exception as e:
            self.logger.error(f"Error harvesting Splunk saved searches: {e}")

        return artifacts

    async def _harvest_splunk_dashboards(self, session: requests.Session,
                                        base_url: str) -> List[SIEMArtifact]:
        """Harvest Splunk dashboards for visualization patterns"""
        artifacts = []

        try:
            # Get dashboard definitions
            response = session.get(f"{base_url}/services/data/ui/views",
                                 params={'output_mode': 'json', 'count': 1000})

            if response.status_code == 200:
                data = response.json()

                for entry in data.get('entry', []):
                    content = entry.get('content', {})

                    artifact = SIEMArtifact(
                        artifact_id=f"splunk_dashboard_{entry['name']}",
                        artifact_type=SIEMArtifactType.DASHBOARD,
                        platform=SIEMPlatform.SPLUNK,
                        name=entry['name'],
                        description=f"Splunk dashboard: {entry['name']}",
                        content={
                            'xml_definition': content.get('eai:data'),
                            'dashboard_type': 'xml' if content.get('eai:data') else 'simple'
                        },
                        metadata={
                            'app': entry.get('acl', {}).get('app'),
                            'owner': entry.get('acl', {}).get('owner'),
                            'updated': entry.get('updated')
                        },
                        confidence=0.7,
                        tags=['splunk', 'dashboard', 'visualization']
                    )

                    artifacts.append(artifact)

        except Exception as e:
            self.logger.error(f"Error harvesting Splunk dashboards: {e}")

        return artifacts

    async def _harvest_splunk_macros(self, session: requests.Session,
                                    base_url: str) -> List[SIEMArtifact]:
        """Harvest Splunk macros for reusable search patterns"""
        artifacts = []

        try:
            # Get macro definitions
            response = session.get(f"{base_url}/services/admin/macros",
                                 params={'output_mode': 'json', 'count': 1000})

            if response.status_code == 200:
                data = response.json()

                for entry in data.get('entry', []):
                    content = entry.get('content', {})

                    artifact = SIEMArtifact(
                        artifact_id=f"splunk_macro_{entry['name']}",
                        artifact_type=SIEMArtifactType.MACRO,
                        platform=SIEMPlatform.SPLUNK,
                        name=entry['name'],
                        description=f"Splunk macro: {entry['name']}",
                        content={
                            'definition': content.get('definition'),
                            'arguments': content.get('args'),
                            'validation': content.get('validation')
                        },
                        metadata={
                            'app': entry.get('acl', {}).get('app'),
                            'owner': entry.get('acl', {}).get('owner')
                        },
                        confidence=0.8,
                        tags=['splunk', 'macro', 'reusable']
                    )

                    artifacts.append(artifact)

        except Exception as e:
            self.logger.error(f"Error harvesting Splunk macros: {e}")

        return artifacts

    async def _harvest_elastic(self, credentials: Dict[str, Any],
                              artifact_types: List[SIEMArtifactType]) -> List[SIEMArtifact]:
        """Harvest artifacts from Elastic Security/SIEM"""
        artifacts = []

        # Elastic configuration
        base_url = credentials.get('base_url', 'https://localhost:9200')
        api_key = credentials.get('api_key')
        cloud_id = credentials.get('cloud_id')

        headers = {
            'Authorization': f'ApiKey {api_key}',
            'Content-Type': 'application/json'
        }

        try:
            # Harvest Detection Rules
            if SIEMArtifactType.DETECTION_RULE in artifact_types:
                detection_rules = await self._harvest_elastic_detection_rules(base_url, headers)
                artifacts.extend(detection_rules)

            # Harvest Dashboards
            if SIEMArtifactType.DASHBOARD in artifact_types:
                dashboards = await self._harvest_elastic_dashboards(base_url, headers)
                artifacts.extend(dashboards)

            # Harvest Saved Objects (searches, visualizations)
            if SIEMArtifactType.SAVED_SEARCH in artifact_types:
                saved_objects = await self._harvest_elastic_saved_objects(base_url, headers)
                artifacts.extend(saved_objects)

        except Exception as e:
            self.logger.error(f"Error harvesting from Elastic: {e}")

        return artifacts

    async def _harvest_elastic_detection_rules(self, base_url: str,
                                              headers: Dict[str, str]) -> List[SIEMArtifact]:
        """Harvest Elastic Security detection rules"""
        artifacts = []

        try:
            # Get detection rules from Elastic Security
            response = requests.get(f"{base_url}/api/detection_engine/rules", headers=headers)

            if response.status_code == 200:
                rules = response.json().get('data', [])

                for rule in rules:
                    # Extract MITRE techniques
                    mitre_techniques = []
                    if 'threat' in rule:
                        for threat in rule['threat']:
                            if 'technique' in threat:
                                for technique in threat['technique']:
                                    mitre_techniques.append(technique.get('id', ''))

                    artifact = SIEMArtifact(
                        artifact_id=f"elastic_rule_{rule['id']}",
                        artifact_type=SIEMArtifactType.DETECTION_RULE,
                        platform=SIEMPlatform.ELASTIC,
                        name=rule.get('name', ''),
                        description=rule.get('description', ''),
                        content={
                            'query': rule.get('query'),
                            'language': rule.get('language'),
                            'filters': rule.get('filters'),
                            'index': rule.get('index'),
                            'severity': rule.get('severity'),
                            'risk_score': rule.get('risk_score'),
                            'threat': rule.get('threat', []),
                            'false_positives': rule.get('false_positives', [])
                        },
                        metadata={
                            'rule_id': rule.get('rule_id'),
                            'version': rule.get('version'),
                            'created_by': rule.get('created_by'),
                            'updated_by': rule.get('updated_by'),
                            'enabled': rule.get('enabled'),
                            'tags': rule.get('tags', [])
                        },
                        mitre_techniques=mitre_techniques,
                        confidence=0.95,  # Very high confidence for Elastic rules
                        tags=['elastic', 'detection_rule', 'security']
                    )

                    artifacts.append(artifact)

        except Exception as e:
            self.logger.error(f"Error harvesting Elastic detection rules: {e}")

        return artifacts

    async def _harvest_sentinel(self, credentials: Dict[str, Any],
                               artifact_types: List[SIEMArtifactType]) -> List[SIEMArtifact]:
        """Harvest artifacts from Microsoft Sentinel"""
        artifacts = []

        # Azure/Sentinel configuration
        tenant_id = credentials.get('tenant_id')
        client_id = credentials.get('client_id')
        client_secret = credentials.get('client_secret')
        subscription_id = credentials.get('subscription_id')
        resource_group = credentials.get('resource_group')
        workspace_name = credentials.get('workspace_name')

        try:
            # Get Azure access token
            token = await self._get_azure_access_token(tenant_id, client_id, client_secret)

            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }

            # Harvest Analytics Rules (Detection Rules)
            if SIEMArtifactType.DETECTION_RULE in artifact_types:
                analytics_rules = await self._harvest_sentinel_analytics_rules(
                    subscription_id, resource_group, workspace_name, headers)
                artifacts.extend(analytics_rules)

            # Harvest Workbooks (Dashboards)
            if SIEMArtifactType.DASHBOARD in artifact_types:
                workbooks = await self._harvest_sentinel_workbooks(
                    subscription_id, resource_group, workspace_name, headers)
                artifacts.extend(workbooks)

            # Harvest Hunting Queries
            if SIEMArtifactType.SEARCH_QUERY in artifact_types:
                hunting_queries = await self._harvest_sentinel_hunting_queries(
                    subscription_id, resource_group, workspace_name, headers)
                artifacts.extend(hunting_queries)

        except Exception as e:
            self.logger.error(f"Error harvesting from Sentinel: {e}")

        return artifacts

    async def _harvest_qradar(self, credentials: Dict[str, Any],
                             artifact_types: List[SIEMArtifactType]) -> List[SIEMArtifact]:
        """Harvest artifacts from IBM QRadar"""
        artifacts = []

        # QRadar configuration
        base_url = credentials.get('base_url')
        sec_token = credentials.get('sec_token')

        headers = {
            'SEC': sec_token,
            'Content-Type': 'application/json',
            'Version': '12.0'
        }

        try:
            # Harvest Custom Rules
            if SIEMArtifactType.DETECTION_RULE in artifact_types:
                custom_rules = await self._harvest_qradar_custom_rules(base_url, headers)
                artifacts.extend(custom_rules)

            # Harvest Saved Searches
            if SIEMArtifactType.SAVED_SEARCH in artifact_types:
                saved_searches = await self._harvest_qradar_saved_searches(base_url, headers)
                artifacts.extend(saved_searches)

            # Harvest Dashboards
            if SIEMArtifactType.DASHBOARD in artifact_types:
                dashboards = await self._harvest_qradar_dashboards(base_url, headers)
                artifacts.extend(dashboards)

        except Exception as e:
            self.logger.error(f"Error harvesting from QRadar: {e}")

        return artifacts

    async def _harvest_chronicle(self, credentials: Dict[str, Any],
                                artifact_types: List[SIEMArtifactType]) -> List[SIEMArtifact]:
        """Harvest artifacts from Google Chronicle"""
        artifacts = []

        # Chronicle configuration
        base_url = credentials.get('base_url', 'https://backstory.googleapis.com')
        service_account_key = credentials.get('service_account_key')

        # Implementation for Chronicle YARA-L rules and other artifacts
        # This would require Google Cloud authentication and Chronicle API access

        return artifacts

    # Utility methods for artifact processing

    def _extract_mitre_techniques_from_text(self, text: str) -> List[str]:
        """Extract MITRE ATT&CK technique IDs from text"""
        if not text:
            return []

        # Pattern to match MITRE technique IDs (T1234, T1234.001, etc.)
        pattern = r'T\d{4}(?:\.\d{3})?'
        matches = re.findall(pattern, text, re.IGNORECASE)

        return list(set(matches))  # Remove duplicates

    def _calculate_effectiveness_score(self, artifact: SIEMArtifact) -> float:
        """Calculate effectiveness score based on various factors"""
        score = 0.5  # Base score

        # Boost score for artifacts with MITRE mappings
        if artifact.mitre_techniques:
            score += 0.2

        # Boost score for well-documented artifacts
        if len(artifact.description) > 50:
            score += 0.1

        # Boost score for recently modified artifacts
        if artifact.modified_at and artifact.modified_at > datetime.utcnow() - timedelta(days=90):
            score += 0.1

        # Platform-specific adjustments
        if artifact.platform in [SIEMPlatform.ELASTIC, SIEMPlatform.SENTINEL]:
            score += 0.1  # Modern platforms with better rule quality

        return min(score, 1.0)

    def _standardize_artifact_format(self, artifact: SIEMArtifact) -> Dict[str, Any]:
        """Convert SIEM-specific artifact to standard SIEMLess format"""

        # Convert to standard detection rule format
        if artifact.artifact_type == SIEMArtifactType.DETECTION_RULE:
            return {
                'title': artifact.name,
                'description': artifact.description,
                'detection_logic': artifact.content,
                'mitre_techniques': artifact.mitre_techniques,
                'confidence': artifact.confidence,
                'source_platform': artifact.platform.value,
                'original_format': self._detect_rule_format(artifact),
                'metadata': artifact.metadata
            }

        # Convert dashboards to visualization patterns
        elif artifact.artifact_type == SIEMArtifactType.DASHBOARD:
            return {
                'title': artifact.name,
                'description': artifact.description,
                'visualization_type': 'dashboard',
                'components': self._extract_dashboard_components(artifact),
                'source_platform': artifact.platform.value,
                'metadata': artifact.metadata
            }

        # Standard format for other artifacts
        return {
            'title': artifact.name,
            'description': artifact.description,
            'content': artifact.content,
            'type': artifact.artifact_type.value,
            'source_platform': artifact.platform.value,
            'metadata': artifact.metadata
        }

    def _detect_rule_format(self, artifact: SIEMArtifact) -> str:
        """Detect the original format of a detection rule"""
        content = artifact.content

        if artifact.platform == SIEMPlatform.SPLUNK:
            return 'spl'
        elif artifact.platform == SIEMPlatform.ELASTIC:
            if content.get('language') == 'kuery':
                return 'kql'
            elif content.get('language') == 'eql':
                return 'eql'
            else:
                return 'elasticsearch'
        elif artifact.platform == SIEMPlatform.SENTINEL:
            return 'kql'
        elif artifact.platform == SIEMPlatform.QRADAR:
            return 'aql'
        else:
            return 'unknown'

    def _extract_dashboard_components(self, artifact: SIEMArtifact) -> List[Dict[str, Any]]:
        """Extract dashboard components for pattern analysis"""
        components = []

        # This would parse dashboard XML/JSON to extract:
        # - Chart types and configurations
        # - Data sources and queries
        # - Filters and time ranges
        # - Layout and design patterns

        return components

    async def get_harvesting_statistics(self) -> Dict[str, Any]:
        """Get statistics about harvested artifacts"""
        stats = {
            'total_artifacts': len(self.harvested_artifacts),
            'artifacts_by_platform': {},
            'artifacts_by_type': {},
            'mitre_technique_coverage': set(),
            'average_effectiveness': 0.0,
            'last_harvest_time': None
        }

        for artifact in self.harvested_artifacts:
            # Count by platform
            platform = artifact.platform.value
            stats['artifacts_by_platform'][platform] = \
                stats['artifacts_by_platform'].get(platform, 0) + 1

            # Count by type
            artifact_type = artifact.artifact_type.value
            stats['artifacts_by_type'][artifact_type] = \
                stats['artifacts_by_type'].get(artifact_type, 0) + 1

            # Collect MITRE techniques
            stats['mitre_technique_coverage'].update(artifact.mitre_techniques)

            # Update last harvest time
            if artifact.modified_at:
                if not stats['last_harvest_time'] or artifact.modified_at > stats['last_harvest_time']:
                    stats['last_harvest_time'] = artifact.modified_at

        # Calculate average effectiveness
        if self.harvested_artifacts:
            effectiveness_scores = [a.effectiveness_score or 0.7 for a in self.harvested_artifacts]
            stats['average_effectiveness'] = sum(effectiveness_scores) / len(effectiveness_scores)

        # Convert set to list for JSON serialization
        stats['mitre_technique_coverage'] = list(stats['mitre_technique_coverage'])

        return stats

    async def export_artifacts_for_crystallization(self) -> List[Dict[str, Any]]:
        """Export harvested artifacts in format suitable for pattern crystallization"""
        crystallization_data = []

        for artifact in self.harvested_artifacts:
            crystal_data = {
                'id': artifact.artifact_id,
                'source': 'siem_harvest',
                'platform': artifact.platform.value,
                'type': artifact.artifact_type.value,
                'confidence': artifact.confidence,
                'standardized_format': self._standardize_artifact_format(artifact),
                'effectiveness_score': artifact.effectiveness_score or self._calculate_effectiveness_score(artifact),
                'mitre_techniques': artifact.mitre_techniques,
                'tags': artifact.tags,
                'metadata': {
                    'original_artifact': asdict(artifact),
                    'harvest_timestamp': datetime.utcnow().isoformat(),
                    'ready_for_crystallization': True
                }
            }

            crystallization_data.append(crystal_data)

        return crystallization_data


# Integration with existing Ingestion Engine
class EnhancedIngestionEngine(BaseEngine):
    """
    Enhanced Ingestion Engine with SIEM Intelligence Harvesting
    """

    def __init__(self, engine_name: str = 'enhanced_ingestion', version: str = '2.0.0'):
        super().__init__(engine_name, version)
        self.siem_harvester = SIEMIntelligenceHarvester()
        self.harvesting_schedule = {}  # Platform -> next_harvest_time mapping

    async def harvest_from_all_connected_siems(self) -> Dict[str, List[SIEMArtifact]]:
        """Harvest artifacts from all configured SIEM platforms"""
        results = {}

        # Get all available SIEM platforms from credentials
        available_platforms = await self._get_available_siem_platforms()

        for platform in available_platforms:
            try:
                artifacts = await self.siem_harvester.harvest_siem_intelligence(platform)
                results[platform.value] = artifacts

                # Schedule next harvest
                self.harvesting_schedule[platform] = datetime.utcnow() + timedelta(hours=6)

            except Exception as e:
                self.logger.error(f"Failed to harvest from {platform.value}: {e}")
                results[platform.value] = []

        return results

    async def _get_available_siem_platforms(self) -> List[SIEMPlatform]:
        """Get list of SIEM platforms with available credentials"""
        available = []

        for platform in SIEMPlatform:
            try:
                # Check if credentials are available
                creds = await get_siem_credentials(platform.value)
                if creds:
                    available.append(platform)
            except:
                continue  # Skip platforms without credentials

        return available

    async def process_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Handle SIEM intelligence harvesting requests"""

        if message.get('type') == 'harvest_siem_intelligence':
            platform_name = message.get('platform')
            artifact_types = message.get('artifact_types')

            if platform_name:
                platform = SIEMPlatform(platform_name)
                artifacts = await self.siem_harvester.harvest_siem_intelligence(
                    platform, artifact_types)

                return {
                    'status': 'success',
                    'platform': platform_name,
                    'artifacts_harvested': len(artifacts),
                    'artifacts': [asdict(a) for a in artifacts]
                }
            else:
                # Harvest from all platforms
                results = await self.harvest_from_all_connected_siems()
                total_artifacts = sum(len(artifacts) for artifacts in results.values())

                return {
                    'status': 'success',
                    'platforms_harvested': list(results.keys()),
                    'total_artifacts': total_artifacts,
                    'results_by_platform': {k: len(v) for k, v in results.items()}
                }

        elif message.get('type') == 'get_harvesting_stats':
            stats = await self.siem_harvester.get_harvesting_statistics()
            return {'status': 'success', 'statistics': stats}

        elif message.get('type') == 'export_for_crystallization':
            crystal_data = await self.siem_harvester.export_artifacts_for_crystallization()
            return {
                'status': 'success',
                'crystallization_data': crystal_data,
                'ready_for_pattern_library': True
            }

        return {'status': 'error', 'message': 'Unknown message type'}

    def get_capabilities(self) -> List[str]:
        """Return enhanced capabilities including SIEM intelligence harvesting"""
        return [
            'log_ingestion',
            'cti_integration',
            'siem_intelligence_harvesting',
            'artifact_standardization',
            'pattern_discovery',
            'rule_migration',
            'playbook_extraction',
            'dashboard_analysis'
        ]


if __name__ == "__main__":
    # Test the SIEM Intelligence Harvester
    async def test_harvester():
        harvester = SIEMIntelligenceHarvester()

        # Test harvesting from mock Splunk
        print("Testing SIEM Intelligence Harvester...")

        # This would normally connect to real SIEM platforms
        # For testing, we'll simulate the process
        print("✅ SIEM Intelligence Harvester initialized successfully")
        print("Ready to harvest from: Splunk, Elastic, Sentinel, QRadar, Chronicle")

    asyncio.run(test_harvester())