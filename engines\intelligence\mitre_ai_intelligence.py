"""
MITRE AI Intelligence Engine
Provides AI-powered analysis for MITRE ATT&CK integration:
- Tier 3: AI rule-to-technique inference
- Gap prioritization with context
- False positive prediction
- Rule overlap/redundancy detection
"""

import json
import hashlib
import asyncio
from typing import Dict, List, Optional, Set, Tuple
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
import logging

# AI model imports
try:
    import google.generativeai as genai
    HAS_GEMINI = True
except ImportError:
    HAS_GEMINI = False

try:
    from anthropic import AsyncAnthropic
    HAS_ANTHROPIC = True
except ImportError:
    HAS_ANTHROPIC = False


@dataclass
class AIInference:
    """AI-inferred technique mapping"""
    rule_id: str
    technique_id: str
    confidence: float
    reasoning: str
    ai_model: str
    cost: float


@dataclass
class GapRecommendation:
    """AI-prioritized gap recommendation"""
    technique_id: str
    technique_name: str
    tactics: List[str]
    priority_score: int
    reasoning: str
    environment_context: Dict
    threat_intel_context: Dict
    attack_chain_context: Dict
    required_data_sources: List[str]
    effort: str
    fp_estimate: str


@dataclass
class FPPrediction:
    """False positive prediction"""
    rule_id: str
    predicted_fp_rate: float
    confidence: float
    risk_factors: List[str]
    tuning_suggestions: List[Dict]


class MITREAIIntelligence:
    """AI-powered intelligence for MITRE ATT&CK analysis"""

    def __init__(self, redis_client=None, db_connection=None, logger=None):
        self.redis = redis_client
        self.db = db_connection
        self.logger = logger or logging.getLogger(__name__)

        # AI model configuration
        self.gemini_model = None
        self.claude_client = None
        self._initialize_ai_models()

        # Pattern library for caching expensive AI operations
        self.pattern_cache = {}

        # Cost tracking
        self.total_cost = 0.0
        self.total_savings = 0.0

    def _initialize_ai_models(self):
        """Initialize AI models with cost optimization"""
        try:
            # Gemini 2.5 Flash - Low cost, fast (primary for tier 3)
            if HAS_GEMINI:
                import os
                api_key = os.getenv('GEMINI_API_KEY')
                if api_key:
                    genai.configure(api_key=api_key)
                    self.gemini_model = genai.GenerativeModel('gemini-2.5-flash')
                    self.logger.info("Initialized Gemini 2.5 Flash for AI intelligence")

            # Claude Haiku - Good reasoning, fast (for gap prioritization)
            if HAS_ANTHROPIC:
                import os
                api_key = os.getenv('ANTHROPIC_API_KEY')
                if api_key:
                    self.claude_client = AsyncAnthropic(api_key=api_key)
                    self.logger.info("Initialized Claude Haiku for AI intelligence")

        except Exception as e:
            self.logger.warning(f"AI model initialization partial: {e}")

    async def infer_technique_mapping(self, rule: Dict) -> List[AIInference]:
        """
        Tier 3: AI-powered rule-to-technique inference
        For rules without explicit MITRE tags

        Uses pattern matching to avoid repeated AI calls for similar rules
        """
        rule_id = rule.get('id', rule.get('title', 'unknown'))

        # Generate rule signature for pattern matching
        signature = self._generate_rule_signature(rule)
        pattern_hash = hashlib.sha256(signature.encode()).hexdigest()

        # Check pattern cache first (learn once, use forever)
        cached = await self._check_pattern_cache(pattern_hash, 'tier3_mapping')
        if cached:
            self.logger.info(f"Tier 3 mapping cache hit for {rule_id}")
            await self._track_pattern_reuse(pattern_hash, cached['cost'])
            return self._deserialize_inferences(cached['analysis'], rule_id)

        # No cache - run expensive AI analysis
        self.logger.info(f"Tier 3: Running AI inference for {rule_id}")

        if not self.gemini_model:
            self.logger.warning("Gemini not available, skipping AI inference")
            return []

        try:
            start_time = datetime.utcnow()

            # Build analysis prompt
            prompt = self._build_tier3_prompt(rule)

            # Call Gemini 2.5 Flash (cheap, fast)
            response = await asyncio.to_thread(
                self.gemini_model.generate_content,
                prompt
            )

            latency = int((datetime.utcnow() - start_time).total_seconds() * 1000)

            # Parse response
            inferences = self._parse_ai_inference_response(response.text, rule_id)

            # Calculate cost (Gemini 2.5 Flash: $0.075/1M input, $0.30/1M output)
            # Rough estimate: ~500 input tokens, ~200 output tokens
            cost = (500 * 0.075 / 1_000_000) + (200 * 0.30 / 1_000_000)
            cost = round(cost, 6)

            # Cache the pattern for reuse
            await self._store_pattern(
                pattern_hash=pattern_hash,
                pattern_type='tier3_mapping',
                pattern_data={'signature': signature, 'rule_sample': rule_id},
                ai_analysis={'inferences': [self._serialize_inference(inf) for inf in inferences]},
                ai_model='gemini-2.5-flash',
                cost=cost
            )

            # Track cost
            await self._track_ai_cost(
                operation_type='tier3_mapping',
                ai_model='gemini-2.5-flash',
                input_tokens=500,
                output_tokens=200,
                cost=cost,
                latency_ms=latency,
                success=True
            )

            # Store inferences in database
            for inference in inferences:
                await self._store_inference(inference)

            self.logger.info(f"Tier 3: Inferred {len(inferences)} techniques for {rule_id} (cost: ${cost:.6f})")
            return inferences

        except Exception as e:
            self.logger.error(f"Tier 3 inference failed for {rule_id}: {e}")
            await self._track_ai_cost(
                operation_type='tier3_mapping',
                ai_model='gemini-2.5-flash',
                input_tokens=500,
                output_tokens=0,
                cost=0.0,
                latency_ms=0,
                success=False,
                error_message=str(e)
            )
            return []

    def _generate_rule_signature(self, rule: Dict) -> str:
        """Generate signature for pattern matching similar rules"""
        # Extract key features for similarity
        features = []

        # Log source type
        if 'logsource' in rule:
            logsource = rule['logsource']
            if isinstance(logsource, dict):
                features.append(f"product:{logsource.get('product', '')}")
                features.append(f"service:{logsource.get('service', '')}")
                category = logsource.get('category', '')
                if isinstance(category, list):
                    features.extend([f"cat:{c}" for c in category])
                else:
                    features.append(f"cat:{category}")

        # Detection patterns (simplified)
        if 'detection' in rule:
            detection = rule.get('detection', {})
            # Extract key fields being checked
            for key, value in detection.items():
                if isinstance(value, dict):
                    for field in value.keys():
                        features.append(f"field:{field}")

        return '|'.join(sorted(features))

    def _build_tier3_prompt(self, rule: Dict) -> str:
        """Build prompt for AI technique inference"""
        return f"""Analyze this security detection rule and map it to MITRE ATT&CK techniques.

Rule Title: {rule.get('title', 'Unknown')}
Rule Description: {rule.get('description', 'No description')}
Log Source: {json.dumps(rule.get('logsource', {}))}
Detection Logic: {json.dumps(rule.get('detection', {}), indent=2)[:500]}

Tasks:
1. Identify which MITRE ATT&CK techniques this rule would detect
2. Provide confidence score (0.0-1.0) for each mapping
3. Explain your reasoning

Output format (JSON):
{{
  "mappings": [
    {{
      "technique_id": "T1234",
      "technique_name": "Example Technique",
      "confidence": 0.85,
      "reasoning": "Rule detects X which is characteristic of this technique"
    }}
  ]
}}

Focus on parent techniques (e.g., T1055, not T1055.001) unless the rule is very specific.
Only include techniques with confidence >= 0.6.
"""

    def _parse_ai_inference_response(self, response_text: str, rule_id: str) -> List[AIInference]:
        """Parse AI response into structured inferences"""
        try:
            # Extract JSON from response
            # Sometimes AI wraps JSON in markdown code blocks
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1

            if json_start == -1 or json_end == 0:
                return []

            json_str = response_text[json_start:json_end]
            data = json.loads(json_str)

            inferences = []
            for mapping in data.get('mappings', []):
                inference = AIInference(
                    rule_id=rule_id,
                    technique_id=mapping.get('technique_id'),
                    confidence=float(mapping.get('confidence', 0.0)),
                    reasoning=mapping.get('reasoning', ''),
                    ai_model='gemini-2.5-flash',
                    cost=0.0  # Cost tracked at operation level
                )
                inferences.append(inference)

            return inferences

        except Exception as e:
            self.logger.error(f"Failed to parse AI response: {e}")
            return []

    async def prioritize_gaps(
        self,
        gaps: List[str],
        environment_context: Dict,
        threat_intel: Optional[List[Dict]] = None,
        current_coverage: Optional[Dict] = None
    ) -> List[GapRecommendation]:
        """
        AI-powered gap prioritization with context

        Args:
            gaps: List of technique IDs with no coverage
            environment_context: Customer profile (cloud-heavy, Windows-focused, etc.)
            threat_intel: Recent CTI indicators
            current_coverage: Current detection coverage map

        Returns:
            Prioritized list of gap recommendations
        """
        if not self.claude_client:
            self.logger.warning("Claude not available for gap prioritization")
            return []

        # Check cache for similar gap sets
        gap_signature = '|'.join(sorted(gaps[:20]))  # Use first 20 for signature
        pattern_hash = hashlib.sha256(gap_signature.encode()).hexdigest()

        cached = await self._check_pattern_cache(pattern_hash, 'gap_prioritization')
        if cached:
            self.logger.info("Gap prioritization cache hit")
            await self._track_pattern_reuse(pattern_hash, cached['cost'])
            return self._deserialize_gap_recommendations(cached['analysis'])

        try:
            start_time = datetime.utcnow()

            prompt = f"""Prioritize these MITRE ATT&CK detection gaps for a security operations team.

Environment Context:
{json.dumps(environment_context, indent=2)}

Detection Gaps ({len(gaps)} total):
{json.dumps(gaps[:50], indent=2)}

Recent Threat Intelligence:
{json.dumps(threat_intel[:10] if threat_intel else [], indent=2)}

Current Coverage Summary:
{json.dumps(current_coverage, indent=2) if current_coverage else 'Not provided'}

Rank the top 20 most critical gaps considering:
1. Attack chain dependencies (missing techniques that enable other attacks)
2. Environment relevance (cloud vs on-prem, OS types, etc.)
3. Threat landscape (active campaigns using these techniques)
4. Detection difficulty (some techniques are very hard to detect reliably)
5. Business impact (techniques that lead to data exfiltration, ransomware, etc.)

Output format (JSON):
{{
  "recommendations": [
    {{
      "technique_id": "T1234",
      "priority_score": 95,
      "reasoning": "Critical because...",
      "environment_fit": "high|medium|low",
      "threat_intel_relevance": "high|medium|low",
      "attack_chain_impact": "Enables T1xxx and T1yyy",
      "implementation_effort": "low|medium|high",
      "fp_estimate": "low|medium|high"
    }}
  ]
}}
"""

            # Call Claude Haiku (good reasoning, fast)
            response = await self.claude_client.messages.create(
                model="claude-haiku-3-5-20241022",
                max_tokens=4000,
                messages=[{"role": "user", "content": prompt}]
            )

            latency = int((datetime.utcnow() - start_time).total_seconds() * 1000)

            # Parse response
            response_text = response.content[0].text
            recommendations = self._parse_gap_recommendations(response_text, gaps)

            # Calculate cost (Claude Haiku: $0.25/1M input, $1.25/1M output)
            input_tokens = response.usage.input_tokens
            output_tokens = response.usage.output_tokens
            cost = (input_tokens * 0.25 / 1_000_000) + (output_tokens * 1.25 / 1_000_000)
            cost = round(cost, 6)

            # Cache pattern
            await self._store_pattern(
                pattern_hash=pattern_hash,
                pattern_type='gap_prioritization',
                pattern_data={'gaps_sample': gaps[:20], 'environment': environment_context},
                ai_analysis={'recommendations': [self._serialize_gap_rec(r) for r in recommendations]},
                ai_model='claude-haiku-3-5',
                cost=cost
            )

            # Track cost
            await self._track_ai_cost(
                operation_type='gap_prioritization',
                ai_model='claude-haiku-3-5',
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                cost=cost,
                latency_ms=latency,
                success=True
            )

            # Store recommendations
            for rec in recommendations:
                await self._store_gap_recommendation(rec)

            self.logger.info(f"Prioritized {len(recommendations)} gap recommendations (cost: ${cost:.6f})")
            return recommendations

        except Exception as e:
            self.logger.error(f"Gap prioritization failed: {e}")
            return []

    def _parse_gap_recommendations(self, response_text: str, technique_ids: List[str]) -> List[GapRecommendation]:
        """Parse gap prioritization response"""
        try:
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            json_str = response_text[json_start:json_end]
            data = json.loads(json_str)

            recommendations = []
            for rec in data.get('recommendations', []):
                recommendation = GapRecommendation(
                    technique_id=rec.get('technique_id'),
                    technique_name=rec.get('technique_name', ''),
                    tactics=rec.get('tactics', []),
                    priority_score=int(rec.get('priority_score', 50)),
                    reasoning=rec.get('reasoning', ''),
                    environment_context={'fit': rec.get('environment_fit', 'medium')},
                    threat_intel_context={'relevance': rec.get('threat_intel_relevance', 'medium')},
                    attack_chain_context={'impact': rec.get('attack_chain_impact', '')},
                    required_data_sources=rec.get('required_data_sources', []),
                    effort=rec.get('implementation_effort', 'medium'),
                    fp_estimate=rec.get('fp_estimate', 'medium')
                )
                recommendations.append(recommendation)

            return sorted(recommendations, key=lambda x: x.priority_score, reverse=True)

        except Exception as e:
            self.logger.error(f"Failed to parse gap recommendations: {e}")
            return []

    async def predict_false_positives(self, rule: Dict, similar_rules: Optional[List[Dict]] = None) -> Optional[FPPrediction]:
        """
        Predict false positive rate for a rule before deployment

        Uses historical data from similar rules when available
        """
        rule_id = rule.get('id', rule.get('title', 'unknown'))

        if not self.gemini_model:
            return None

        try:
            prompt = f"""Analyze this security detection rule and predict its false positive rate.

Rule Title: {rule.get('title')}
Detection Logic: {json.dumps(rule.get('detection', {}), indent=2)[:500]}
Log Source: {json.dumps(rule.get('logsource', {}))}

Similar Rules Performance:
{json.dumps(similar_rules[:3] if similar_rules else [], indent=2)}

Predict:
1. False positive rate (0.0 = clean, 1.0 = very noisy)
2. Confidence in prediction
3. Risk factors that might cause noise
4. Tuning suggestions to reduce FPs

Output (JSON):
{{
  "predicted_fp_rate": 0.15,
  "confidence": 0.8,
  "risk_factors": ["Broad wildcard matching", "No exclusions for service accounts"],
  "tuning_suggestions": [
    {{"type": "exclusion", "field": "user", "values": ["service_*"]}},
    {{"type": "threshold", "field": "count", "operator": ">", "value": 5}}
  ]
}}
"""

            response = await asyncio.to_thread(
                self.gemini_model.generate_content,
                prompt
            )

            prediction = self._parse_fp_prediction(response.text, rule_id)

            if prediction:
                await self._store_fp_prediction(prediction)

            return prediction

        except Exception as e:
            self.logger.error(f"FP prediction failed for {rule_id}: {e}")
            return None

    def _parse_fp_prediction(self, response_text: str, rule_id: str) -> Optional[FPPrediction]:
        """Parse FP prediction response"""
        try:
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            json_str = response_text[json_start:json_end]
            data = json.loads(json_str)

            return FPPrediction(
                rule_id=rule_id,
                predicted_fp_rate=float(data.get('predicted_fp_rate', 0.0)),
                confidence=float(data.get('confidence', 0.0)),
                risk_factors=data.get('risk_factors', []),
                tuning_suggestions=data.get('tuning_suggestions', [])
            )

        except Exception as e:
            self.logger.error(f"Failed to parse FP prediction: {e}")
            return None

    # Database operations
    async def _store_inference(self, inference: AIInference):
        """Store AI inference in database"""
        if not self.db:
            return

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO ai_technique_inferences
                (rule_id, technique_id, confidence, reasoning, ai_model, ai_cost)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                inference.rule_id,
                inference.technique_id,
                inference.confidence,
                inference.reasoning,
                inference.ai_model,
                inference.cost
            ))
            self.db.commit()

        except Exception as e:
            self.logger.error(f"Failed to store AI inference: {e}")

    async def _store_gap_recommendation(self, rec: GapRecommendation):
        """Store gap recommendation in database"""
        if not self.db:
            return

        try:
            cursor = self.db.cursor()
            # Recommendations expire after 30 days (threat landscape changes)
            expires_at = datetime.utcnow() + timedelta(days=30)

            cursor.execute("""
                INSERT INTO ai_gap_recommendations
                (technique_id, technique_name, tactics, priority_score, reasoning,
                 environment_context, threat_intel_context, attack_chain_context,
                 required_data_sources, estimated_implementation_effort,
                 estimated_fp_rate, expires_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                rec.technique_id,
                rec.technique_name,
                rec.tactics,
                rec.priority_score,
                rec.reasoning,
                json.dumps(rec.environment_context),
                json.dumps(rec.threat_intel_context),
                json.dumps(rec.attack_chain_context),
                rec.required_data_sources,
                rec.effort,
                rec.fp_estimate,
                expires_at
            ))
            self.db.commit()

        except Exception as e:
            self.logger.error(f"Failed to store gap recommendation: {e}")

    async def _store_fp_prediction(self, prediction: FPPrediction):
        """Store FP prediction in database"""
        if not self.db:
            return

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO ai_fp_predictions
                (rule_id, predicted_fp_rate, confidence, risk_factors, tuning_suggestions)
                VALUES (%s, %s, %s, %s, %s)
            """, (
                prediction.rule_id,
                prediction.predicted_fp_rate,
                prediction.confidence,
                json.dumps(prediction.risk_factors),
                json.dumps(prediction.tuning_suggestions)
            ))
            self.db.commit()

        except Exception as e:
            self.logger.error(f"Failed to store FP prediction: {e}")

    async def _store_pattern(self, pattern_hash: str, pattern_type: str, pattern_data: Dict,
                           ai_analysis: Dict, ai_model: str, cost: float):
        """Store pattern in library for reuse"""
        if not self.db:
            return

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO ai_pattern_library
                (pattern_hash, pattern_type, pattern_data, ai_analysis, ai_model, ai_cost)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (pattern_hash) DO NOTHING
            """, (
                pattern_hash,
                pattern_type,
                json.dumps(pattern_data),
                json.dumps(ai_analysis),
                ai_model,
                cost
            ))
            self.db.commit()

        except Exception as e:
            self.logger.error(f"Failed to store pattern: {e}")

    async def _check_pattern_cache(self, pattern_hash: str, pattern_type: str) -> Optional[Dict]:
        """Check if pattern exists in cache"""
        if not self.db:
            return None

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                SELECT ai_analysis, ai_cost
                FROM ai_pattern_library
                WHERE pattern_hash = %s AND pattern_type = %s
            """, (pattern_hash, pattern_type))

            row = cursor.fetchone()
            if row:
                return {
                    'analysis': row[0],
                    'cost': float(row[1])
                }
            return None

        except Exception as e:
            self.logger.error(f"Failed to check pattern cache: {e}")
            return None

    async def _track_pattern_reuse(self, pattern_hash: str, original_cost: float):
        """Track pattern reuse for savings calculation"""
        if not self.db:
            return

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                UPDATE ai_pattern_library
                SET reuse_count = reuse_count + 1,
                    savings_usd = savings_usd + %s,
                    last_used_at = CURRENT_TIMESTAMP
                WHERE pattern_hash = %s
            """, (original_cost, pattern_hash))
            self.db.commit()

            self.total_savings += original_cost

        except Exception as e:
            self.logger.error(f"Failed to track pattern reuse: {e}")

    async def _track_ai_cost(self, operation_type: str, ai_model: str, input_tokens: int,
                           output_tokens: int, cost: float, latency_ms: int, success: bool,
                           error_message: str = None):
        """Track AI operation cost"""
        if not self.db:
            return

        try:
            cursor = self.db.cursor()
            cursor.execute("""
                INSERT INTO ai_intelligence_costs
                (operation_type, ai_model, input_tokens, output_tokens, cost_usd,
                 latency_ms, success, error_message)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                operation_type,
                ai_model,
                input_tokens,
                output_tokens,
                cost,
                latency_ms,
                success,
                error_message
            ))
            self.db.commit()

            if success:
                self.total_cost += cost

        except Exception as e:
            self.logger.error(f"Failed to track AI cost: {e}")

    def _serialize_inference(self, inf: AIInference) -> Dict:
        """Serialize inference for storage"""
        return {
            'technique_id': inf.technique_id,
            'confidence': inf.confidence,
            'reasoning': inf.reasoning
        }

    def _deserialize_inferences(self, data: Dict, rule_id: str) -> List[AIInference]:
        """Deserialize inferences from cache"""
        inferences = []
        for inf_data in data.get('inferences', []):
            inferences.append(AIInference(
                rule_id=rule_id,
                technique_id=inf_data['technique_id'],
                confidence=inf_data['confidence'],
                reasoning=inf_data['reasoning'],
                ai_model='gemini-2.5-flash',
                cost=0.0
            ))
        return inferences

    def _serialize_gap_rec(self, rec: GapRecommendation) -> Dict:
        """Serialize gap recommendation"""
        return {
            'technique_id': rec.technique_id,
            'priority_score': rec.priority_score,
            'reasoning': rec.reasoning
        }

    def _deserialize_gap_recommendations(self, data: Dict) -> List[GapRecommendation]:
        """Deserialize gap recommendations from cache"""
        # Simplified - would need full reconstruction
        return []
